<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. If D : E = 3 : 2 and D - E = 9 then Find the value of D + E ?</p>",
                    question_hi: "<p>1. यदि D : E = 3 : 2 और D - E = 9 है, तो D + E का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>40</p>", "<p>45</p>", 
                                "<p>47</p>", "<p>44</p>"],
                    options_hi: ["<p>40</p>", "<p>45</p>",
                                "<p>47</p>", "<p>44</p>"],
                    solution_en: "<p>1.(b) Let the D and E be 3<math display=\"inline\"><mi>x</mi></math> and 2x respectively,<br>According to the question,<br>D - E = 9<br><math display=\"inline\"><mo>&#8658;</mo></math> 3x - 2x = 9<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 9<br>So, D = 27 and E = 18<br>Now, <br>D + E = 27 + 18 = 45</p>",
                    solution_hi: "<p>1.(b) माना कि D और E क्रमशः 3<math display=\"inline\"><mi>x</mi></math> और 2x हैं,<br>प्रश्न के अनुसार,<br>D - E = 9<br><math display=\"inline\"><mo>&#8658;</mo></math> 3x - 2x = 9<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 9<br>तो, D = 27 और E = 18<br>अब,<br>D + E = 27 + 18 = 45</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "2.  The ratio of three numbers is 5 : 6 : 7. If the sum of the three numbers is 540, then which is the smallest number among the three numbers ?",
                    question_hi: "2.  तीन संख्याओं का अनुपात 5 : 6 : 7 है। यदि तीनों संख्याओं का योग 540 है, तो तीनों संख्याओं में से सबसे छोटी संख्या कौन-सी है ?",
                    options_en: [" 540", " 150", 
                                " 180", " 210"],
                    options_hi: [" 540", " 150",
                                " 180", " 210"],
                    solution_en: "2.(b) Let the numbers be 5<math display=\"inline\"><mi>x</mi></math>, 6x, and 7x<br />According to question,<br /><math display=\"inline\"><mo>⇒</mo></math> 5x + 6x + 7x = 540<br /><math display=\"inline\"><mo>⇒</mo></math> 18x = 540<br /><math display=\"inline\"><mo>⇒</mo></math> x = 30<br />Now, the smallest number = 5<math display=\"inline\"><mi>x</mi></math> = 5 × 30 = 150",
                    solution_hi: "2.(b) माना संख्याएँ 5<math display=\"inline\"><mi>x</mi></math>, 6x,और 7x हैं<br />प्रश्न के अनुसार,<br /><math display=\"inline\"><mo>⇒</mo></math> 5x + 6x + 7x = 540<br /><math display=\"inline\"><mo>⇒</mo></math> 18x = 540<br /><math display=\"inline\"><mo>⇒</mo></math> x = 30<br />अब, सबसे छोटी संख्या = 5<math display=\"inline\"><mi>x</mi></math> = 5 × 30 = 150",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. If the sum of two quantities is equal to six times their difference, then find the ratio of the two quantities.</p>",
                    question_hi: "<p>3. यदि दो मात्राओं का योग उनके बीच के अंतर के छः गुना के बराबर है, तो दोनों मात्राओं का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>7 : 5</p>", "<p>3 : 7</p>", 
                                "<p>4 : 9</p>", "<p>5 : 2</p>"],
                    options_hi: ["<p>7 : 5</p>", "<p>3 : 7</p>",
                                "<p>4 : 9</p>", "<p>5 : 2</p>"],
                    solution_en: "<p>3.(a)<br>Let two quantity be <math display=\"inline\"><mi>x</mi></math> and y<br>According to the question,<br><math display=\"inline\"><mi>x</mi><mo>+</mo><mi>y</mi></math> = (x - y) &times; 6<br><math display=\"inline\"><mi>x</mi><mo>+</mo><mi>y</mi></math> = 6x - 6y<br>5<math display=\"inline\"><mi>x</mi></math> = 7y &rArr; x : y = 7 : 5</p>",
                    solution_hi: "<p>3.(a)<br>माना दो मात्राएँ <math display=\"inline\"><mi>x</mi></math> और y हैं<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mi>x</mi><mo>+</mo><mi>y</mi></math> = (x - y) &times; 6<br><math display=\"inline\"><mi>x</mi><mo>+</mo><mi>y</mi></math> = 6x - 6y<br>5<math display=\"inline\"><mi>x</mi></math> = 7y &rArr; x : y = 7 : 5</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "4. Four numbers are in the ratio of 7 : 8 : 9 : 10. Sum of these number is 68. What will be the sum of the first and fourth number?",
                    question_hi: "4. चार संख्याएं 7 : 8 : 9 : 10 के अनुपात में हैं। इन संख्याओं का योग 68 है। पहली ओर चौथी संख्या का योग कितना होगा?",
                    options_en: [" 68 ", " 38 ", 
                                " 34 ", " 36"],
                    options_hi: [" 68 ", " 38 ",
                                " 34 ", " 36"],
                    solution_en: "4.(c)<br />Let four numbers be 7<math display=\"inline\"><mi>x</mi></math> , 8x , 9x and 10x respectively<br />According to the question,<br />7<math display=\"inline\"><mi>x</mi></math> + 8x +  9x +10x = 68<br />34<math display=\"inline\"><mi>x</mi></math> = 68       ⇒ x = 2<br />Hence, required sum = (7<math display=\"inline\"><mi>x</mi></math> + 10x) = (17x) = 34",
                    solution_hi: "4.(c)<br />माना कि चार संख्याएँ क्रमशः  7<math display=\"inline\"><mi>x</mi></math> , 8x , 9x और 10x  हैं<br />प्रश्न के अनुसार,<br />7<math display=\"inline\"><mi>x</mi></math> + 8x +  9x +10x = 68<br />34<math display=\"inline\"><mi>x</mi></math> = 68       ⇒ x = 2<br />अत: अभीष्ट योग = (7<math display=\"inline\"><mi>x</mi></math> + 10x) = (17x) = 34",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5.  If 3T = 10U and 5U = 7V, then what is the value of T : U : V ?",
                    question_hi: "<p>5. यदि 3T = 10U और 5U = 7V है , तो T : U : V का मान कितना है ?</p>",
                    options_en: [" 70 : 31 : 14", " 70 : 21 : 15", 
                                " 10 : 3 : 2", " 21 : 15 : 70 "],
                    options_hi: [" 70 : 31 : 14", " 70 : 21 : 15",
                                " 10 : 3 : 2", " 21 : 15 : 70 "],
                    solution_en: "<p>5.(b) <br>3T = 10U and 5U = 7V<br>Then, T : U = 10 : 3 and U : V = 7 : 5<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> T&nbsp; &nbsp;:&nbsp; U&nbsp; &nbsp;:&nbsp; V<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10&nbsp; :&nbsp; 3&nbsp; &nbsp;:&nbsp; 3<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 7&nbsp; &nbsp;:&nbsp; 7&nbsp; &nbsp;:&nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&mdash;----------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;70&nbsp; :&nbsp; 21 :&nbsp; 15</p>",
                    solution_hi: "<p>5.(b) <br>3T = 10U और 5U = 7V<br>फिर, T : U = 10 : 3 और U : V = 7 : 5<br>T : U : V = 70 : 21 : 15<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> T&nbsp; &nbsp;:&nbsp; U&nbsp; &nbsp;:&nbsp; V<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;10&nbsp; :&nbsp; 3&nbsp; &nbsp; :&nbsp; 3<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 7&nbsp; &nbsp;:&nbsp; 7&nbsp; &nbsp; :&nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &mdash;----------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;70&nbsp; :&nbsp; 21&nbsp; :&nbsp; 15</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If 4T = 3V = 12S, then what is the value of T : V : S ?</p>",
                    question_hi: "6. यदि 4T = 3V = 12S है, तो T : V : S का मान कितना है ?",
                    options_en: [" 5 : 3 : 1 ", " 3 : 4 : 1", 
                                " 4 : 3 : 1 ", " 3 : 1 : 4"],
                    options_hi: [" 5 : 3 : 1 ", " 3 : 4 : 1",
                                " 4 : 3 : 1 ", " 3 : 1 : 4"],
                    solution_en: "<p>6.(b) 4T = 3V = 12S<br>Let 4T = 3V = 12S = k<br>Then, T = <math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math> ,&nbsp;V = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mn>3</mn></mfrac></math>,&nbsp; and S = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mn>12</mn></mfrac><mo>&#160;</mo></math><br>Hence, <br>T : V : S = <math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mn>12</mn></mfrac><mo>&#160;</mo></math> = 3 : 4 : 1</p>",
                    solution_hi: "<p>6.(b) 4T = 3V = 12S<br>माना, 4T = 3V = 12S = k<br>फिर, T = <math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math> ,&nbsp;V = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mn>3</mn></mfrac></math>, और S = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mn>12</mn></mfrac><mo>&#160;</mo></math><br>इसलिए, <br>T : V : S = <math display=\"inline\"><mfrac><mrow><mi>k</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mn>3</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>k</mi><mn>12</mn></mfrac><mo>&#160;</mo></math> = 3 : 4 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The ratio of two numbers is 3 : 1. The sum of the squares of those numbers is 810. What will be the sum of the numbers ?</p>",
                    question_hi: "<p>7. दो संख्याओं का अनुपात 3 : 1 है। उन संख्याओं के वर्गों का योग 810 है। संख्याओं का योग कितना होगा ?</p>",
                    options_en: ["<p>60</p>", "<p>27</p>", 
                                "<p>24</p>", "<p>36</p>"],
                    options_hi: ["<p>60</p>", "<p>27</p>",
                                "<p>24</p>", "<p>36</p>"],
                    solution_en: "<p>7.(d)<br>Let the numbers be 3a and a.<br>According to question,<br>(3a)&sup2; + a&sup2; = 810<br>10a&sup2; = 810<br>a&sup2; = 81<br>a = 9<br>Then the numbers are (3 &times; 9) = 27 and 9.<br>Then, required sum = 27 + 9 = 36.</p>",
                    solution_hi: "<p>7.(d)<br>माना संख्याएँ 3a और a हैं।<br>प्रश्न के अनुसार,<br>(3a)&sup2; + a&sup2; = 810<br>10a&sup2; = 810<br>a&sup2; = 81<br>a = 9<br>तो संख्याएँ (3 &times; 9) = 27 और 9 हैं।<br>तब, अभीष्ट योग = 27 + 9 = 36.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "8. The ratio of P, Q and R is 3 : 5 : 8 respectively. If their sum is 400, then what is the difference between P and R ?  ",
                    question_hi: "8. P, Q और R का अनुपात क्रमशः 3 : 5 : 8 है। यदि उनका योग 400 है, तो P और R के बीच कितना अंतर है?",
                    options_en: [" 50  ", " 125 ", 
                                " 100 ", " 75"],
                    options_hi: [" 50  ", " 125 ",
                                " 100 ", " 75"],
                    solution_en: "8.(b)<br />According to question,<br />(3 + 5 + 8) units = 400<br />16 unit = 400<br />Difference between P and R = (8 - 3) unit = 5 unit<br />Therefore, required difference = <math display=\"inline\"><mfrac><mrow><mn>400</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>5</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = 125.",
                    solution_hi: "8.(b)<br />प्रश्न के अनुसार,<br />(3 + 5 + 8) इकाई = 400<br />16 इकाई = 400<br />P और R के बीच अंतर = (8 - 3) इकाई = 5 इकाई<br />अत: अभीष्ट अंतर = <math display=\"inline\"><mfrac><mrow><mn>400</mn><mi>&nbsp;</mi><mo>×</mo><mi>&nbsp;</mi><mn>5</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> = 125",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. If a : b : c = 2 : 5 : 3 and c : d = 5 : 2, then what is the value of a : b : c : d ?</p>",
                    question_hi: "<p>9. यदि a : b : c = 2 : 5 : 3 और c : d = 5 : 2 है, तो a : b : c : d का मान कितना है ?</p>",
                    options_en: ["<p>9 : 25 : 15 : 4</p>", "<p>10 : 25 : 15 : 6</p>", 
                                "<p>10 : 25 : 5 : 2</p>", "<p>10 : 25 : 5 : 3</p>"],
                    options_hi: ["<p>9 : 25 : 15 : 4</p>", "<p>10 : 25 : 15 : 6</p>",
                                "<p>10 : 25 : 5 : 2</p>", "<p>10 : 25 : 5 : 3</p>"],
                    solution_en: "<p>9.(b)<br>Ratio - a&nbsp; :&nbsp; b&nbsp; :&nbsp; c&nbsp; :&nbsp; d<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; :&nbsp; 5&nbsp; :&nbsp; 3&nbsp; :&nbsp; 3&nbsp;<br><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; :&nbsp; 5</strong>&nbsp; :&nbsp; 5&nbsp; :&nbsp; 2<br>-----------------------------------<br>Final - 10 : 25 : 15 : 6</p>",
                    solution_hi: "<p>9.(b)<br>अनुपात - a&nbsp; :&nbsp; b&nbsp; :&nbsp; c&nbsp; :&nbsp; d<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; :&nbsp; 5&nbsp; :&nbsp; 3&nbsp; :&nbsp; 3&nbsp;<br><strong>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5&nbsp; :&nbsp; 5</strong>&nbsp; :&nbsp; 5&nbsp; :&nbsp; 2<br>-----------------------------------<br>अंतिम -&nbsp; 10 : 25 : 15&nbsp; :&nbsp; 6</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If p : q : r = 5 : 3 : 2, then what is the value of 2p : 4q : 3r ?</p>",
                    question_hi: "<p>10. यदि p : q : r = 5 : 3 : 2 है, तो 2p : 4q : 3r का मान कितना है ?</p>",
                    options_en: ["<p>3 : 2 : 4</p>", "<p>6 : 2 : 1</p>", 
                                "<p>5 : 6 : 4</p>", "<p>5 : 6 : 3</p>"],
                    options_hi: ["<p>3 : 2 : 4</p>", "<p>6 : 2 : 1</p>",
                                "<p>5 : 6 : 4</p>", "<p>5 : 6 : 3</p>"],
                    solution_en: "<p>10.(d)<br>p : q : r = 5 : 3 : 2<br>2p : 4q : 3r = 2 &times; 5 : 4 &times; 3 : 3 &times; 2 <br>= 10 : 12 : 6 = 5 : 6 : 3</p>",
                    solution_hi: "<p>10.(d)<br>p : q : r = 5 : 3 : 2<br>2p : 4q : 3r = 2 &times; 5 : 4 &times; 3 : 3 &times; 2 <br>= 10 : 12 : 6 = 5 : 6 : 3</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "11. If x : y : z = 3 : 6 : 7 and 5y - x + 2z = 123, then what is the value of y?",
                    question_hi: "11. यदि x : y : z = 3 : 6 : 7 और 5y - x + 2z = 123 है, तो y का मान कितना है?",
                    options_en: [" 21 ", " 9 ", 
                                " 18", " 84"],
                    options_hi: [" 21 ", " 9 ",
                                " 18", " 84"],
                    solution_en: "11.(c) <br />x : y : z = 3 : 6 : 7<br />Let x , y and z be 3a , 6a  and  7a respectively,<br />Now, 5y - x + 2z = 123<br />   5 × 6a - 3a + 2 × 7a = 123<br /><math display=\"inline\"><mo>⇒</mo></math>         30a - 3a + 14a = 123<br /><math display=\"inline\"><mo>⇒</mo></math>                            41a = 123<br /><math display=\"inline\"><mo>⇒</mo></math>                                 a = 3<br />      so,                 y = 6a = 18",
                    solution_hi: "11.(c) <br />x : y : z = 3 : 6 : 7<br />माना x , y और z क्रमशः 3a , 6a और 7a हैं,<br />अब, 5y - x + 2z = 123<br /> 5 × 6a - 3a + 2 × 7a = 123<br /><math display=\"inline\"><mo>⇒</mo></math>         30a - 3a + 14a = 123<br /><math display=\"inline\"><mo>⇒</mo></math>                            41a = 123<br /><math display=\"inline\"><mo>⇒</mo></math>                                 a = 3<br />    तो,                    y = 6a = 18",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The ratio of three numbers is 6 : 7 : 8. If the sum of the three numbers is 420, then what is the smallest number among the three numbers ?</p>",
                    question_hi: "<p>12. तीन संख्याओं का अनुपात 6 : 7 : 8 है। यदि तीनों संख्याओं का योग 420 है, तो तीनों संख्याओं में सबसे छोटी संख्या कौन-सी है ?</p>",
                    options_en: ["<p>190</p>", "<p>180</p>", 
                                "<p>120</p>", "<p>140</p>"],
                    options_hi: ["<p>190</p>", "<p>180</p>",
                                "<p>120</p>", "<p>140</p>"],
                    solution_en: "<p>12.(c)<br>Let the 3 numbers be 6<math display=\"inline\"><mi>x</mi></math>, 7x and 8x<br>According to the question,<br>6<math display=\"inline\"><mi>x</mi></math> + 7x + 8x = 420<br>21<math display=\"inline\"><mi>x</mi></math> = 420, x = 20<br>Smallest number (6<math display=\"inline\"><mi>x</mi></math>) = 120</p>",
                    solution_hi: "<p>12.(c)<br>माना , तीन संख्याएँ = 6<math display=\"inline\"><mi>x</mi></math>, 7x और 8x <br>प्रश्न के अनुसार,<br>6<math display=\"inline\"><mi>x</mi></math> + 7x + 8x = 420<br>21<math display=\"inline\"><mi>x</mi></math> = 420, x = 20<br>सबसे छोटी संख्या (6<math display=\"inline\"><mi>x</mi></math>) = 120</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Four numbers are in the ratio of 7 : 5 : 3 : 2. Sum of these number is 68. What will be the sum of the first and fourth number ?</p>",
                    question_hi: "<p>13. चार संख्याएं 7 : 5 : 3 : 2 के अनुपात में हैं। इन संख्याओं का योग 68 है। पहली और चौथी संख्या का योग कितना होगा ?</p>",
                    options_en: ["<p>36</p>", "<p>24</p>", 
                                "<p>48</p>", "<p>32</p>"],
                    options_hi: ["<p>36</p>", "<p>24</p>",
                                "<p>48</p>", "<p>32</p>"],
                    solution_en: "<p>13.(a)<br>Let the numbers be 7<math display=\"inline\"><mi>x</mi></math>, 5x, 3x and 2x <br>According to the question,<br>7<math display=\"inline\"><mi>x</mi></math> + 5x + 3x + 2x = 68<br>17<math display=\"inline\"><mi>x</mi></math> = 68, x = 4<br>Sum of 1st and 4th number (7<math display=\"inline\"><mi>x</mi></math> + 2x) = 9 &times; 4 = 36</p>",
                    solution_hi: "<p>13.(a)<br>माना , तीन संख्याएँ = 7<math display=\"inline\"><mi>x</mi></math>, 5x, 3x और 2x <br>प्रश्न के अनुसार,<br>7<math display=\"inline\"><mi>x</mi></math> + 5x + 3x + 2x = 68<br>17<math display=\"inline\"><mi>x</mi></math> = 68, x = 4<br>पहली और चौथी संख्या का योग (7<math display=\"inline\"><mi>x</mi></math> + 2x) = 9 &times; 4 = 36</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "14. The ratio of incomes of Manoj and Paras is 12 : 7. If Paras gets Rs. 4000 less than Manoj, then what is the total income of Manoj and Paras ?",
                    question_hi: "14. मनोज और पारस की आय का अनुपात 12 : 7 है। यदि पारस की आय, मनोज की आय से 4000 रुपए कम है, तो मनोज और पारस की कुल आय कितनी है ?",
                    options_en: [" Rs. 9600", " Rs. 4000", 
                                " Rs. 5600", " Rs. 15200"],
                    options_hi: [" 9600 रुपए", " 4000 रुपए",
                                " 5600 रुपए", " 15200 रुपए"],
                    solution_en: "14.(d)<br />Let the income of Manoj and Paras 12<math display=\"inline\"><mi>x</mi></math> and 7x respectively.<br />Difference between their income = 12<math display=\"inline\"><mi>x</mi></math> - 7x = 5x<br />5<math display=\"inline\"><mi>x</mi></math> = 4000, x = 800<br />Total income of Manoj and Paras (12<math display=\"inline\"><mi>x</mi></math> + 7x)  =  19 × 800 = Rs. 15200",
                    solution_hi: "14.(d)<br />माना कि मनोज और पारस की आय क्रमशः 12<math display=\"inline\"><mi>x</mi></math> और 7x है।<br />उनकी आय के बीच का अंतर = 12<math display=\"inline\"><mi>x</mi></math> - 7x = 5x<br />5<math display=\"inline\"><mi>x</mi></math> = 4000, x = 800<br />मनोज और पारस की कुल आय (12<math display=\"inline\"><mi>x</mi></math> + 7x)  =  19 × 800 = Rs. 15200",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. If g : h : j = 5 : 2 : 1, then what is the value of g : 2h : j ?</p>",
                    question_hi: "<p>15. यदि g : h : j = 5 : 2 : 1 है, तो g : 2h : j का मान कितना है ?</p>",
                    options_en: [" 5 : 4 : 1", " 5 : 4 : 2", 
                                " 5 : 1 : 3", " 3 : 5 : 2"],
                    options_hi: [" 5 : 4 : 1", " 5 : 4 : 2",
                                " 5 : 1 : 3", " 3 : 5 : 2"],
                    solution_en: "<p>15.(a) if g : h : j = 5 : 2 : 1<br>Then , g : 2h : j = 5 : 2<math display=\"inline\"><mo>&#215;</mo></math>2 : 1 = 5 : 4 : 1</p>",
                    solution_hi: "<p>15.(a) यदि g : h : j = 5 : 2 : 1<br>अतः , g : 2h : j = 5 : 2<math display=\"inline\"><mo>&#215;</mo></math>2 : 1 = 5 : 4 : 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>