<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">Study the given graph and answer the question that follows.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image1.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">During 2016 to 2021, for which year is the production of rice highest?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2366;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2366;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image1.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">2016 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2021 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>2020</p>\n", "<p>2018</p>\n", 
                                "<p>2019</p>\n", "<p>2021</p>\n"],
                    options_hi: ["<p>2020</p>\n", "<p>2018</p>\n",
                                "<p>2019</p>\n", "<p>2021</p>\n"],
                    solution_en: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Total production of rice in 2020 = 60 + 50 + 45 = 155</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total pr</span><span style=\"font-family: Cambria Math;\">oduction of rice in 2018 = 55 + 50 + 60 = 165</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total production of rice in 2019 = 45 + 55 + 60 = 160</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total production of rice in 2021 = 50 + 55 + 40 = 145</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Clearly, we can see that </span><span style=\"font-family: Cambria Math;\">the production of rice is highest in 2018.</span></p>\n",
                    solution_hi: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">2020 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 60 + 50 + 45 = 155</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2018 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 55 + 50 + 60 = 165</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2019 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 45 + 55 + 60 = 160</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2021 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> = 50 + 55 + 40 = 145</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> 2018 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">Study the given table and answer the question that follows.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The table shows the production (in thousands) of four different types of tractors by a company during five years.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_85649001331702269383861.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Which type of tractor has same production in the years 2010 and 2013?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2381;&#2352;&#2376;&#2325;&#2381;&#2335;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2361;&#2332;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image2.png\" width=\"374\" height=\"155\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2010 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2013 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2381;&#2352;&#2376;&#2325;&#2381;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Type - B</p>\n", "<p>Type - C</p>\n", 
                                "<p>Type - A</p>\n", "<p>Type - D</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;</span><span style=\"font-family: Cambria Math;\">&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> - B</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> - C </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> - A </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> - D</span></p>\n"],
                    solution_en: "<p>2.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">On observing the table, clearly we can see that Type C tractor has the same production in the years 2010 and 2013.</span></p>\n",
                    solution_hi: "<p>2.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2010 </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2013 </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2366;&#2311;&#2346;</span><span style=\"font-family: Cambria Math;\"> C </span><span style=\"font-family: Cambria Math;\">&#2335;&#2381;&#2352;&#2376;&#2325;&#2381;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">3.</span><span style=\"font-family:Cambria Math\">The following figure shows the expenditures incurred by Rohan in different heads. Rohan has a monthly income of 50,000 out of which he spen</span><span style=\"font-family:Cambria Math\">ds 60% on different expenditures. What is the total expenditure (in) incurred on Health, Education and Transportation together? </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image3.png\"/></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">3. </span><span style=\"font-family:Cambria Math\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आरेख</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">रोहन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मदों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दर</span><span style=\"font-family:Cambria Math\">्शाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">रोहन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मासिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आय</span><span style=\"font-family:Cambria Math\"> ₹50,000 </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">जिसमें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वह</span><span style=\"font-family:Cambria Math\"> 60% </span><span style=\"font-family:Cambria Math\">विभिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">खर्ची</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">व्यय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">स्वास्थ्य</span><span style=\"font-family:Cambria Math\"> (Health), </span><span style=\"font-family:Cambria Math\">शिक्षा</span><span style=\"font-family:Cambria Math\"> (Education) </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">परिवहन</span><span style=\"font-family:Cambria Math\"> (Transportation) </span><span style=\"font-family:Cambria Math\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">साथ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">व्यय</span><span style=\"font-family:Cambria Math\"> (2 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Cambria Math\">कितना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> ?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image3.png\"/></p>",
                    options_en: [" <p> Rs.16,800</span></p>", " <p> Rs.15,800</span></p>", 
                                " <p> Rs.28,000</span></p>", " <p> Rs.18,000</span></p>"],
                    options_hi: [" <p> Rs.16,800</span></p>", " <p> Rs.15,800</span></p>",
                                " <p> Rs.28,000</span></p>", " <p> Rs.1</span><span style=\"font-family:Cambria Math\">8,000</span></p>"],
                    solution_en: " <p>3.(a)</span></p> <p><span style=\"font-family:Cambria Math\">Total expenditure of Rohan = 50,000×</span><span style=\"font-family:Cambria Math\"> = ₹30,000</span></p> <p><span style=\"font-family:Cambria Math\">Total expenditure incurred on Health, Education and Transportation together = (24+18+14)% of 30,000 = 56% of 30,000 = ₹16,800</span></p>",
                    solution_hi: " <p>3.(a)</span></p> <p><span style=\"font-family:Cambria Math\">रोहन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">व्यय</span><span style=\"font-family:Cambria Math\">  = 50,000×</span><span style=\"font-family:Cambria Math\"> = ₹30,000</span></p> <p><span style=\"font-family:Cambria Math\">स्वास्थ्य</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">शिक्षा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">परिवहन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">व्यय</span><span style=\"font-family:Cambria Math\">r = (24+18+14)% </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\">  30,000 = 56% of 30,000 = ₹16,800</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">4.</span><span style=\"font-family:Cambria Math\"> The following table gives the  numbers of patients visiting a dental clinic for the issues related to their oral health during the months September , October, November and December 2018. Study the carefully and answer the question that follows.</span></p> <p><span style=\"font-family:Cambria Math\">Months(20</span><span style=\"font-family:Cambria Math\">18)</span></p> <p><span style=\"font-family:Cambria Math\">Filling </span></p> <p><span style=\"font-family:Cambria Math\">Root canal </span></p> <p><span style=\"font-family:Cambria Math\">Tooth Extraction </span></p> <p><span style=\"font-family:Cambria Math\">Implants</span></p> <p><span style=\"font-family:Cambria Math\">September</span></p> <p><span style=\"font-family:Cambria Math\">180</span></p> <p><span style=\"font-family:Cambria Math\">480</span></p> <p><span style=\"font-family:Cambria Math\">570</span></p> <p><span style=\"font-family:Cambria Math\">260</span></p> <p><span style=\"font-family:Cambria Math\">October</span></p> <p><span style=\"font-family:Cambria Math\">560</span></p> <p><span style=\"font-family:Cambria Math\">770</span></p> <p><span style=\"font-family:Cambria Math\">620</span></p> <p><span style=\"font-family:Cambria Math\">470</span></p> <p><span style=\"font-family:Cambria Math\">November</span></p> <p><span style=\"font-family:Cambria Math\">940</span></p> <p><span style=\"font-family:Cambria Math\">1080</span></p> <p><span style=\"font-family:Cambria Math\">890</span></p> <p><span style=\"font-family:Cambria Math\">220</span></p> <p><span style=\"font-family:Cambria Math\">December</span></p> <p><span style=\"font-family:Cambria Math\">820</span></p> <p><span style=\"font-family:Cambria Math\">590</span></p> <p><span style=\"font-family:Cambria Math\">530</span></p> <p><span style=\"font-family:Cambria Math\">1040</span></p> <p><span style=\"font-family:Cambria Math\">What is the ratio of the total number of patients in September to the total number of patients in December in 2018?</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">4. </span><span style=\"font-family:Cambria Math\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">डेटा</span><span style=\"font-family:Cambria Math\"> 2018 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सितंबर</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">अक्टूबर</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">नवंबर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दिसंबर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">महीनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दौरान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अपने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मौखिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">स्वास्थ्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मुद्दों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दंत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">चिकित्सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">क्लिनिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वाले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">रोगियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दर्शाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">डेटा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ध्यानपूर्वक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अध्ययन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नीचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दे</span><span style=\"font-family:Cambria Math\">ं।</span></p> <p><span style=\"font-family:Cambria Math\">महीना</span><span style=\"font-family:Cambria Math\">  (2018)</span></p> <p><span style=\"font-family:Cambria Math\">फिलिंग</span></p> <p><span style=\"font-family:Cambria Math\">रुट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कनाल</span></p> <p><span style=\"font-family:Cambria Math\">टूथ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक्सट्रैक्शन</span><span style=\"font-family:Cambria Math\">  </span></p> <p><span style=\"font-family:Cambria Math\">इम्प्लेंट</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">सितंबर</span></p> <p><span style=\"font-family:Cambria Math\">180</span></p> <p><span style=\"font-family:Cambria Math\">480</span></p> <p><span style=\"font-family:Cambria Math\">570</span></p> <p><span style=\"font-family:Cambria Math\">260</span></p> <p><span style=\"font-family:Cambria Math\">अक्टूबर</span></p> <p><span style=\"font-family:Cambria Math\">560</span></p> <p><span style=\"font-family:Cambria Math\">770</span></p> <p><span style=\"font-family:Cambria Math\">620</span></p> <p><span style=\"font-family:Cambria Math\">470</span></p> <p><span style=\"font-family:Cambria Math\">नवंबर</span></p> <p><span style=\"font-family:Cambria Math\">940</span></p> <p><span style=\"font-family:Cambria Math\">1080</span></p> <p><span style=\"font-family:Cambria Math\">890</span></p> <p><span style=\"font-family:Cambria Math\">220</span></p> <p><span style=\"font-family:Cambria Math\">दिसंबर</span></p> <p><span style=\"font-family:Cambria Math\">820</span></p> <p><span style=\"font-family:Cambria Math\">590</span></p> <p><span style=\"font-family:Cambria Math\">530</span></p> <p><span style=\"font-family:Cambria Math\">1040</span></p> <p><span style=\"font-family:Cambria Math\">2018 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सितंबर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">रोगियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दिसंबर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">रोगियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अनुपात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">क्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p>  3 : 4</span></p>", " <p>  1 : 2</span></p>", 
                                " <p> 2 : 1 </span></p>", " <p> 4 : 3</span></p>"],
                    options_hi: [" <p>  3 : 4</span></p>", " <p>  1 : 2</span></p>",
                                " <p> 2 : 1 </span></p>", " <p> 4 : 3</span></p>"],
                    solution_en: " <p>4.(b)</span><span style=\"font-family:Cambria Math\"> Total number of patients in September = 180+480+570+260 = 1490</span></p> <p><span style=\"font-family:Cambria Math\">Total number of patients in Dec</span><span style=\"font-family:Cambria Math\">ember = 820+590+530+1040 = 2980</span></p> <p><span style=\"font-family:Cambria Math\">So, required ratio = 1490 : 2980</span></p> <p><span style=\"font-family:Cambria Math\">= 1 :2</span></p>",
                    solution_hi: " <p>4.(b)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सितंबर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मरीज</span><span style=\"font-family:Cambria Math\"> = 180+480+570+260 = 1490</span></p> <p><span style=\"font-family:Cambria Math\">दिसंबर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मरीज</span><span style=\"font-family:Cambria Math\"> = 820+590+530+1040 = 2980</span></p> <p><span style=\"font-family:Cambria Math\">इसलिए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">वांछित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अनुपात</span><span style=\"font-family:Cambria Math\">  = 1490 : 2980</span></p> <p><span style=\"font-family:Cambria Math\">= 1 :2</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">5.</span><span style=\"font-family:Cambria Math\"> The following table shows the number of boys and the number of girls in 5 different classes, C1 to C5.</span></p> <p><span style=\"font-family:Cambria Math\">Class</span></p> <p><span style=\"font-family:Cambria Math\">Boys</span></p> <p><span style=\"font-family:Cambria Math\">Girls</span></p> <p><span style=\"font-family:Cambria Math\">C1</span></p> <p><span style=\"font-family:Cambria Math\">25</span></p> <p><span style=\"font-family:Cambria Math\">25</span></p> <p><span style=\"font-family:Cambria Math\">C2</span></p> <p><span style=\"font-family:Cambria Math\">30</span></p> <p><span style=\"font-family:Cambria Math\">20</span></p> <p><span style=\"font-family:Cambria Math\">C3</span></p> <p><span style=\"font-family:Cambria Math\">15</span></p> <p><span style=\"font-family:Cambria Math\">25</span></p> <p><span style=\"font-family:Cambria Math\">C4</span></p> <p><span style=\"font-family:Cambria Math\">28</span></p> <p><span style=\"font-family:Cambria Math\">32</span></p> <p><span style=\"font-family:Cambria Math\">C5</span></p> <p><span style=\"font-family:Cambria Math\">26</span></p> <p><span style=\"font-family:Cambria Math\">24</span></p> <p><span style=\"font-family:Cambria Math\">The number of boys in C5 is approximately what percent of the average  number of girls per class?</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">5.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">निम्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तालिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">स्कूल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> 5 </span><span style=\"font-family:Cambria Math\">विभिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्गों</span><span style=\"font-family:Cambria Math\">, C1 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> C5 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लड़कों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लड़कियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दर्शाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p> <p><span style=\"font-family:Cambria Math\">वर्ग</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">लड़के</span></p> <p><span style=\"font-family:Cambria Math\">लड़कियाँ</span></p> <p><span style=\"font-family:Cambria Math\">C1</span></p> <p><span style=\"font-family:Cambria Math\">25</span></p> <p><span style=\"font-family:Cambria Math\">25</span></p> <p><span style=\"font-family:Cambria Math\">C2</span></p> <p><span style=\"font-family:Cambria Math\">30</span></p> <p><span style=\"font-family:Cambria Math\">20</span></p> <p><span style=\"font-family:Cambria Math\">C3</span></p> <p><span style=\"font-family:Cambria Math\">15</span></p> <p><span style=\"font-family:Cambria Math\">25</span></p> <p><span style=\"font-family:Cambria Math\">C4</span></p> <p><span style=\"font-family:Cambria Math\">28</span></p> <p><span style=\"font-family:Cambria Math\">32</span></p> <p><span style=\"font-family:Cambria Math\">C5</span></p> <p><span style=\"font-family:Cambria Math\">26</span></p> <p><span style=\"font-family:Cambria Math\">24</span></p> <p><span style=\"font-family:Cambria Math\">C5 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लड़कों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कक्षा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लड़कियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">औसत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लगभग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कितना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रतिशत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p>  111.23%</span></p>", " <p>  91.86%</span></p>", 
                                " <p>  98.67%</span></p>", " <p> 103.17%</span></p>"],
                    options_hi: [" <p>  111.23%</span></p>", " <p>  91.86%</span></p>",
                                " <p>  98.67%</span></p>", " <p> 103.17%</span></p>"],
                    solution_en: " <p>5.(d) </span><span style=\"font-family:Cambria Math\">No of boys in C5 = 26</span></p> <p><span style=\"font-family:Cambria Math\">Average no. of girls = </span><span style=\"font-family:Cambria Math\"> = </span><span style=\"font-family:Cambria Math\"> = 25.2</span></p> <p><span style=\"font-family:Cambria Math\">Required percentage = </span><span style=\"font-family:Cambria Math\"> × 100 = 103.17%</span></p>",
                    solution_hi: " <p>5.(d) </span><span style=\"font-family:Cambria Math\">C5 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लड़को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> = 26</span></p> <p><span style=\"font-family:Cambria Math\">लड़कियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">औसत</span><span style=\"font-family:Cambria Math\">  = </span><span style=\"font-family:Cambria Math\"> = </span><span style=\"font-family:Cambria Math\"> = 25.2</span></p> <p><span style=\"font-family:Cambria Math\">वांछित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रतिशत</span><span style=\"font-family:Cambria Math\">  = </span><span style=\"font-family:Cambria Math\"> × 100 = 103.17%</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">6. Study the given table and answer the question that follows.</span></p> <p><span style=\"font-family:Cambria Math\">The table shows the table numbers of batteries sold by a company (in lakhs) over the years.</span></p> <p><span style=\"font-family:Cambria Math\">          </span></p> <p><span style=\"font-family:Cambria Math\">Year</span></p> <p><span style=\"font-family:Cambria Math\"> Types of batteries</span></p> <p><span style=\"font-family:Cambria Math\">4AH</span></p> <p><span style=\"font-family:Cambria Math\">7AH</span></p> <p><span style=\"font-family:Cambria Math\">32AH</span></p> <p><span style=\"font-family:Cambria Math\">35AH</span></p> <p><span style=\"font-family:Cambria Math\">55AH</span></p> <p><span style=\"font-family:Cambria Math\">Total</span></p> <p><span style=\"font-family:Cambria Math\">1992</span></p> <p><span style=\"font-family:Cambria Math\"> 75</span></p> <p><span style=\"font-family:Cambria Math\">144</span></p> <p><span style=\"font-family:Cambria Math\">114</span></p> <p><span style=\"font-family:Cambria Math\">102</span></p> <p><span style=\"font-family:Cambria Math\">108</span></p> <p><span style=\"font-family:Cambria Math\">543</span></p> <p><span style=\"font-family:Cambria Math\">1993</span></p> <p><span style=\"font-family:Cambria Math\">90</span></p> <p><span style=\"font-family:Cambria Math\">126</span></p> <p><span style=\"font-family:Cambria Math\">102</span></p> <p><span style=\"font-family:Cambria Math\">84</span></p> <p><span style=\"font-family:Cambria Math\">126</span></p> <p><span style=\"font-family:Cambria Math\">528</span></p> <p><span style=\"font-family:Cambria Math\">1994</span></p> <p><span style=\"font-family:Cambria Math\">96</span></p> <p><span style=\"font-family:Cambria Math\">114</span></p> <p><span style=\"font-family:Cambria Math\">75</span></p> <p><span style=\"font-family:Cambria Math\">105</span></p> <p><span style=\"font-family:Cambria Math\">135</span></p> <p><span style=\"font-family:Cambria Math\">525</span></p> <p><span style=\"font-family:Cambria Math\">1995</span></p> <p><span style=\"font-family:Cambria Math\">105</span></p> <p><span style=\"font-family:Cambria Math\">90</span></p> <p><span style=\"font-family:Cambria Math\">150</span></p> <p><span style=\"font-family:Cambria Math\">90</span></p> <p><span style=\"font-family:Cambria Math\">75</span></p> <p><span style=\"font-family:Cambria Math\">510</span></p> <p><span style=\"font-family:Cambria Math\">1996</span></p> <p><span style=\"font-family:Cambria Math\">90</span></p> <p><span style=\"font-family:Cambria Math\">75</span></p> <p><span style=\"font-family:Cambria Math\">135</span></p> <p><span style=\"font-family:Cambria Math\">75</span></p> <p><span style=\"font-family:Cambria Math\">90</span></p> <p><span style=\"font-family:Cambria Math\">465</span></p> <p><span style=\"font-family:Cambria Math\">1997</span></p> <p><span style=\"font-family:Cambria Math\">105</span></p> <p><span style=\"font-family:Cambria Math\">60</span></p> <p><span style=\"font-family:Cambria Math\">165</span></p> <p><span style=\"font-family:Cambria Math\">45</span></p> <p><span style=\"font-family:Cambria Math\">120</span></p> <p><span style=\"font-family:Cambria Math\">495</span></p> <p><span style=\"font-family:Cambria Math\">1998</span></p> <p><span style=\"font-family:Cambria Math\">115</span></p> <p><span style=\"font-family:Cambria Math\">85</span></p> <p><span style=\"font-family:Cambria Math\">160</span></p> <p><span style=\"font-family:Cambria Math\">100</span></p> <p><span style=\"font-family:Cambria Math\">145</span></p> <p><span style=\"font-family:Cambria Math\">605</span></p> <p><span style=\"font-family:Cambria Math\">From 1992 to 1997, which battery saw a steady decline in sales?</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">6. </span><span style=\"font-family:Cambria Math\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तालिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अध्ययन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नीचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दीजिए।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तालिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्षों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंपनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बेची</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बैटरियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Cambria Math\">लाखों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Cambria Math\">दर्शाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p> <p><span style=\"font-family:Cambria Math\">Year</span></p> <p><span style=\"font-family:Cambria Math\"> Types of batteries</span></p> <p><span style=\"font-family:Cambria Math\">4AH</span></p> <p><span style=\"font-family:Cambria Math\">7AH</span></p> <p><span style=\"font-family:Cambria Math\">32AH</span></p> <p><span style=\"font-family:Cambria Math\">35AH</span></p> <p><span style=\"font-family:Cambria Math\">55AH</span></p> <p><span style=\"font-family:Cambria Math\">Total</span></p> <p><span style=\"font-family:Cambria Math\">1992</span></p> <p><span style=\"font-family:Cambria Math\"> 75</span></p> <p><span style=\"font-family:Cambria Math\">144</span></p> <p><span style=\"font-family:Cambria Math\">114</span></p> <p><span style=\"font-family:Cambria Math\">102</span></p> <p><span style=\"font-family:Cambria Math\">108</span></p> <p><span style=\"font-family:Cambria Math\">543</span></p> <p><span style=\"font-family:Cambria Math\">1993</span></p> <p><span style=\"font-family:Cambria Math\">90</span></p> <p><span style=\"font-family:Cambria Math\">126</span></p> <p><span style=\"font-family:Cambria Math\">102</span></p> <p><span style=\"font-family:Cambria Math\">84</span></p> <p><span style=\"font-family:Cambria Math\">126</span></p> <p><span style=\"font-family:Cambria Math\">528</span></p> <p><span style=\"font-family:Cambria Math\">1994</span></p> <p><span style=\"font-family:Cambria Math\">96</span></p> <p><span style=\"font-family:Cambria Math\">114</span></p> <p><span style=\"font-family:Cambria Math\">75</span></p> <p><span style=\"font-family:Cambria Math\">105</span></p> <p><span style=\"font-family:Cambria Math\">135</span></p> <p><span style=\"font-family:Cambria Math\">525</span></p> <p><span style=\"font-family:Cambria Math\">1995</span></p> <p><span style=\"font-family:Cambria Math\">105</span></p> <p><span style=\"font-family:Cambria Math\">90</span></p> <p><span style=\"font-family:Cambria Math\">150</span></p> <p><span style=\"font-family:Cambria Math\">90</span></p> <p><span style=\"font-family:Cambria Math\">75</span></p> <p><span style=\"font-family:Cambria Math\">510</span></p> <p><span style=\"font-family:Cambria Math\">1996</span></p> <p><span style=\"font-family:Cambria Math\">90</span></p> <p><span style=\"font-family:Cambria Math\">75</span></p> <p><span style=\"font-family:Cambria Math\">135</span></p> <p><span style=\"font-family:Cambria Math\">75</span></p> <p><span style=\"font-family:Cambria Math\">90</span></p> <p><span style=\"font-family:Cambria Math\">465</span></p> <p><span style=\"font-family:Cambria Math\">1997</span></p> <p><span style=\"font-family:Cambria Math\">105</span></p> <p><span style=\"font-family:Cambria Math\">60</span></p> <p><span style=\"font-family:Cambria Math\">165</span></p> <p><span style=\"font-family:Cambria Math\">45</span></p> <p><span style=\"font-family:Cambria Math\">120</span></p> <p><span style=\"font-family:Cambria Math\">495</span></p> <p><span style=\"font-family:Cambria Math\">1998</span></p> <p><span style=\"font-family:Cambria Math\">115</span></p> <p><span style=\"font-family:Cambria Math\">85</span></p> <p><span style=\"font-family:Cambria Math\">160</span></p> <p><span style=\"font-family:Cambria Math\">100</span></p> <p><span style=\"font-family:Cambria Math\">145</span></p> <p><span style=\"font-family:Cambria Math\">605</span></p> <p><span style=\"font-family:Cambria Math\">1992 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> 1997 </span><span style=\"font-family:Cambria Math\">तक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बैटरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बिक्री</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लगातार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गिरावट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">देखी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गई</span><span style=\"font-family:Cambria Math\"> ?</span></p>",
                    options_en: [" <p>  35 AH</span></p>", " <p> 4 AH </span></p>", 
                                " <p> 32AH</span></p>", " <p> 7AH</span></p>"],
                    options_hi: [" <p>  35 AH</span></p>", " <p> 4 AH </span></p>",
                                " <p> 32AH</span></p>", " <p> 7AH</span></p>"],
                    solution_en: " <p>6.(d)</span><span style=\"font-family:Cambria Math\"> From</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">the above table it is clearly concluded that from 1992 to 1997, 7AH battery showed a steady decline in sales.</span></p> <p> </span></p>",
                    solution_hi: " <p>6.(d)</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उपरोक्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तालिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">स्पष्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">निकाला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सकता</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कि</span><span style=\"font-family:Cambria Math\"> 1992 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> 1997 </span><span style=\"font-family:Cambria Math\">तक</span><span style=\"font-family:Cambria Math\">, 7AH </span><span style=\"font-family:Cambria Math\">बैटरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बिक्री</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लगातार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गिरावट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">देखी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गई।</span></p> <p> </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">7.Study the given table and answer the question that follows.</span></p> <p><span style=\"font-family:Cambria Math\">The table shows the production of five types of cars by a company for the years 2018 to 2021.</span></p> <p><span style=\"font-family:Cambria Math\">Year </span></p> <p><span style=\"font-family:Cambria Math\">2018</span></p> <p><span style=\"font-family:Cambria Math\">2019</span></p> <p><span style=\"font-family:Cambria Math\">2020</span></p> <p><span style=\"font-family:Cambria Math\">2021</span></p> <p><span style=\"font-family:Cambria Math\">Total</span></p> <p><span style=\"font-family:Cambria Math\">Car </span></p> <p><span style=\"font-family:Cambria Math\">10</span></p> <p><span style=\"font-family:Cambria Math\">20</span></p> <p><span style=\"font-family:Cambria Math\">19</span></p> <p><span style=\"font-family:Cambria Math\">6</span></p> <p><span style=\"font-family:Cambria Math\">55</span></p> <p><span style=\"font-family:Cambria Math\">14</span></p> <p><span style=\"font-family:Cambria Math\">15</span></p> <p><span style=\"font-family:Cambria Math\">16</span></p> <p><span style=\"font-family:Cambria Math\">14</span></p> <p><span style=\"font-family:Cambria Math\">59</span></p> <p><span style=\"font-family:Cambria Math\">21</span></p> <p><span style=\"font-family:Cambria Math\">16</span></p> <p><span style=\"font-family:Cambria Math\">13</span></p> <p><span style=\"font-family:Cambria Math\">14</span></p> <p><span style=\"font-family:Cambria Math\">64</span></p> <p><span style=\"font-family:Cambria Math\">7</span></p> <p><span style=\"font-family:Cambria Math\">10</span></p> <p><span style=\"font-family:Cambria Math\">9</span></p> <p><span style=\"font-family:Cambria Math\">25</span></p> <p><span style=\"font-family:Cambria Math\">51</span></p> <p><span style=\"font-family:Cambria Math\">20</span></p> <p><span style=\"font-family:Cambria Math\">16</span></p> <p><span style=\"font-family:Cambria Math\">19</span></p> <p><span style=\"font-family:Cambria Math\">16</span></p> <p><span style=\"font-family:Cambria Math\">71</span></p> <p><span style=\"font-family:Cambria Math\">Total</span></p> <p><span style=\"font-family:Cambria Math\">72</span></p> <p><span style=\"font-family:Cambria Math\">77</span></p> <p><span style=\"font-family:Cambria Math\">76</span></p> <p><span style=\"font-family:Cambria Math\">75</span></p> <p><span style=\"font-family:Cambria Math\">300</span></p> <p><span style=\"font-family:Cambria Math\">In which year was the production of cars of all types taken together equal to the average of total production during the period?</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">7.</span><span style=\"font-family:Cambria Math\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तालिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अध्ययन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नीचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दें।</span></p> <p><span style=\"font-family:Cambria Math\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तालिका</span><span style=\"font-family:Cambria Math\"> 2018 </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> 2021 </span><span style=\"font-family:Cambria Math\">तक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंपनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पांच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कारों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्पादन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दर्शाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p> <p><span style=\"font-family:Cambria Math\">वर्ष</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">2018</span></p> <p><span style=\"font-family:Cambria Math\">2019</span></p> <p><span style=\"font-family:Cambria Math\">2020</span></p> <p><span style=\"font-family:Cambria Math\">2021</span></p> <p><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">कार</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">10</span></p> <p><span style=\"font-family:Cambria Math\">20</span></p> <p><span style=\"font-family:Cambria Math\">19</span></p> <p><span style=\"font-family:Cambria Math\">6</span></p> <p><span style=\"font-family:Cambria Math\">55</span></p> <p><span style=\"font-family:Cambria Math\">14</span></p> <p><span style=\"font-family:Cambria Math\">15</span></p> <p><span style=\"font-family:Cambria Math\">16</span></p> <p><span style=\"font-family:Cambria Math\">14</span></p> <p><span style=\"font-family:Cambria Math\">59</span></p> <p><span style=\"font-family:Cambria Math\">21</span></p> <p><span style=\"font-family:Cambria Math\">16</span></p> <p><span style=\"font-family:Cambria Math\">13</span></p> <p><span style=\"font-family:Cambria Math\">14</span></p> <p><span style=\"font-family:Cambria Math\">64</span></p> <p><span style=\"font-family:Cambria Math\">7</span></p> <p><span style=\"font-family:Cambria Math\">10</span></p> <p><span style=\"font-family:Cambria Math\">9</span></p> <p><span style=\"font-family:Cambria Math\">25</span></p> <p><span style=\"font-family:Cambria Math\">51</span></p> <p><span style=\"font-family:Cambria Math\">20</span></p> <p><span style=\"font-family:Cambria Math\">16</span></p> <p><span style=\"font-family:Cambria Math\">19</span></p> <p><span style=\"font-family:Cambria Math\">16</span></p> <p><span style=\"font-family:Cambria Math\">71</span></p> <p><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">72</span></p> <p><span style=\"font-family:Cambria Math\">77</span></p> <p><span style=\"font-family:Cambria Math\">76</span></p> <p><span style=\"font-family:Cambria Math\">75</span></p> <p><span style=\"font-family:Cambria Math\">300</span></p> <p><span style=\"font-family:Cambria Math\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्ष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कारों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्पादन</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">पूरी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अवधि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दौरान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्पादन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">औसत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बराबर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">था</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p> 2018 </span></p>", " <p> 2020</span></p>", 
                                " <p> 2019</span></p>", " <p> 2021</span></p>"],
                    options_hi: [" <p> 2018 </span></p>", " <p> 2020</span></p>",
                                " <p> 2019</span></p>", " <p> 2021</span></p>"],
                    solution_en: " <p>7.(d) </span><span style=\"font-family:Cambria Math\">Average of production of car of all types = </span><span style=\"font-family:Cambria Math\"> = 75</span></p> <p><span style=\"font-family:Cambria Math\">In the year 2021, T</span><span style=\"font-family:Cambria Math\">otal production of cars is 75 which is equal to average production of cars of all types.</span></p>",
                    solution_hi: " <p>7.(d) </span><span style=\"font-family:Cambria Math\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्पादन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">औसत</span><span style=\"font-family:Cambria Math\"> = </span><span style=\"font-family:Cambria Math\"> = 75</span></p> <p><span style=\"font-family:Cambria Math\">वर्ष</span><span style=\"font-family:Cambria Math\"> 2021 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कारों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्पादन</span><span style=\"font-family:Cambria Math\"> 75 </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कारों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">औसत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्पादन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बराबर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">8.The giv</span><span style=\"font-family:Cambria Math\">en table shows the number of visitors to the Christmas party and the New Year party organised by Bhide\'s Cafe and Mehta\'s Café.</span></p> <p><span style=\"font-family:Cambria Math\">Name of cafe</span></p> <p><span style=\"font-family:Cambria Math\">Visitors(Christmas Party)</span></p> <p><span style=\"font-family:Cambria Math\">Visitors(New Year Party)</span></p> <p><span style=\"font-family:Cambria Math\">  Bhide’s cafe</span></p> <p><span style=\"font-family:Cambria Math\">559</span></p> <p><span style=\"font-family:Cambria Math\">1118</span></p> <p><span style=\"font-family:Cambria Math\">Mehta’s cafe</span></p> <p><span style=\"font-family:Cambria Math\">666</span></p> <p><span style=\"font-family:Cambria Math\">1024</span></p> <p><span style=\"font-family:Cambria Math\">What percentage more of visitors visited Mehta\'s Cafe to Bhide\'s Café (correct to two decimals places)?</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">8. </span><span style=\"font-family:Cambria Math\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तालिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">भिडे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कैफे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मेहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कैफे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आयोजित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">क्रिसमस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पार्टी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">साल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पार्टी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बालों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आगंतुको</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दर्शाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p> <p><span style=\"font-family:Cambria Math\">कैफे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नाम</span></p> <p><span style=\"font-family:Cambria Math\">आगंतुक</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Cambria Math\">क्रिसमस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पार्टी</span><span style=\"font-family:Cambria Math\">)</span></p> <p><span style=\"font-family:Cambria Math\">आगंतुक</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Cambria Math\">नए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">साल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पार्टी</span><span style=\"font-family:Cambria Math\">)</span></p> <p><span style=\"font-family:Cambria Math\">भिडे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कैफे</span></p> <p><span style=\"font-family:Cambria Math\">559</span></p> <p><span style=\"font-family:Cambria Math\">1118</span></p> <p><span style=\"font-family:Cambria Math\">मेहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कैफे</span></p> <p><span style=\"font-family:Cambria Math\">666</span></p> <p><span style=\"font-family:Cambria Math\">1024</span></p> <p><span style=\"font-family:Cambria Math\">भिड़े</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कैफे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तुलना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मेहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कैफे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कितने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रतिशत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अधिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आगंतुक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आए</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Cambria Math\">दो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दशमलब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">स्थानों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सही</span><span style=\"font-family:Cambria Math\"> ) ?</span></p>",
                    options_en: [" <p> 0.67% </span></p>", " <p> 0.57% </span></p>", 
                                " <p> 0.78% </span></p>", " <p> 0.87%</span></p>"],
                    options_hi: [" <p> 0.67% </span></p>", " <p> 0.57% </span></p>",
                                " <p> 0.78% </span></p>", " <p> 0.87%</span></p>"],
                    solution_en: " <p>8.(c) </span><span style=\"font-family:Cambria Math\">No. of visitors at Mehta’s cafe = 666 + 1024 = 1690</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">No. of visitors at Bhide’s cafe = 559+ 1118 = 1677</span></p> <p><span style=\"font-family:Cambria Math\">Required % = </span><span style=\"font-family:Cambria Math\"> × 100 =  0.78%</span></p>",
                    solution_hi: " <p>8.(c) </span><span style=\"font-family:Cambria Math\">मेहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कैफे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आगंतुकों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> = 666 + 1024 = 1690</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">भिड़े</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कैफे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आगंतुकों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> = 559+ 1118 = 1677</span></p> <p><span style=\"font-family:Cambria Math\">वांछित</span><span style=\"font-family:Cambria Math\"> % = </span><span style=\"font-family:Cambria Math\"> × 100 =  0.78%</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">9.Study the given double bar-graph and answer the question that follows. </span></p> <p><span style=\"font-family:Cambria Math\">The double bar-graph represents a summary of match results for the football team of country P against different countries.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image4.png\"/></p> <p><span style=\"font-family:Cambria Math\">For which country is the differenc</span><span style=\"font-family:Cambria Math\">e between the number of matches won and lost against country P the highest?</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">9.</span><span style=\"font-family:Cambria Math\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दोहरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दंड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आलेख</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अध्ययन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नीचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दीजिए।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दोहरा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दंड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आलेख</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\"> P </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">फुटबॉल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">टीम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">देशों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">खिलाफ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मैच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">परिणामों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सारांश</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">निरूपण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image5.png\"/></p> <p><span style=\"font-family:Cambria Math\">किस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\"> P </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">खिलाफ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जीते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मैचों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बीच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंतर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सबसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अधिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p>  Country D</span></p>", " <p>  Country A</span></p>", 
                                " <p>  Country B</span></p>", " <p> Country C</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\">  D</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\">  A</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\">  B</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\">  C</span></p>"],
                    solution_en: " <p>9.(d)  </span><span style=\"font-family:Cambria Math\">For country D, total no. of matches = 19</span></p> <p><span style=\"font-family:Cambria Math\">No. of matches won by D = 13</span></p> <p><span style=\"font-family:Cambria Math\">No. of matches lost by D = 19 - 13 = 6</span></p> <p><span style=\"font-family:Cambria Math\">Difference between the no. of matches won and lost = 13 -6 = 7</span></p> <p><span style=\"font-family:Cambria Math\">Similarly, For Country C,  total no. of matches = 35</span></p> <p><span style=\"font-family:Cambria Math\">No. of matches won by C = 12</span></p> <p><span style=\"font-family:Cambria Math\">No</span><span style=\"font-family:Cambria Math\">. of matches lost by C = 35 - 12 = 23</span></p> <p><span style=\"font-family:Cambria Math\">Difference between the no. of matches won and lost = 23 -12 = 11</span></p> <p><span style=\"font-family:Cambria Math\"> So, Country C has the highest difference between the no of matches won and lost against country P i.e. 11</span></p>",
                    solution_hi: " <p>9.(d)  </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\"> D </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मैचों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्य</span><span style=\"font-family:Cambria Math\">ा</span><span style=\"font-family:Cambria Math\">  = 19</span></p> <p><span style=\"font-family:Cambria Math\">D </span><span style=\"font-family:Cambria Math\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जीते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मैचों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> = 13</span></p> <p><span style=\"font-family:Cambria Math\">D </span><span style=\"font-family:Cambria Math\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मैचों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> = 19 - 13 = 6</span></p> <p><span style=\"font-family:Cambria Math\">जीते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हुए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हुए</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Cambria Math\">मैच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंतर</span><span style=\"font-family:Cambria Math\">  = 13 -6 = 7</span></p> <p><span style=\"font-family:Cambria Math\">इसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रकार</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\"> C </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मैचों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\">  = 35</span></p> <p><span style=\"font-family:Cambria Math\">C </span><span style=\"font-family:Cambria Math\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जीते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मैचों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> = 12</span></p> <p><span style=\"font-family:Cambria Math\">C </span><span style=\"font-family:Cambria Math\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ग</span><span style=\"font-family:Cambria Math\">ए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मैचों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> = 35 - 12 = 23</span></p> <p><span style=\"font-family:Cambria Math\">मैच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जीते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बीच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंतर</span><span style=\"font-family:Cambria Math\">  = 23 -12 = 11</span></p> <p><span style=\"font-family:Cambria Math\">इसलिए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\"> C </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">देश</span><span style=\"font-family:Cambria Math\"> P </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">खिलाफ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जीते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मैचों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बीच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सबसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अधिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंतर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">यानी</span><span style=\"font-family:Cambria Math\"> 11</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">10.  </span><span style=\"font-family:Cambria Math\">The following bar graph  displays the information about the per</span><span style=\"font-family:Cambria Math\">centage marks of a students in six  different subjects in the High school Examination . The maximum marks of all the subject are equal. Study the bar graph carefully and answer the question that follows.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image6.png\"/></p> <p><span style=\"font-family:Cambria Math\">What is the percentage score of all the six subjects combined ?</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">10.  </span><span style=\"font-family:Cambria Math\">निम्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ग्राफ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">स्कूल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">परीक्षा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">छह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अलग</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Cambria Math\">अलग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विषयों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">छात्र</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रतिशत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंकों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जानकारी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रदर्शित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विषयों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अधिकतम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">समान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ग्राफ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ध्यानपूर्वक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अध्ययन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">नीचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्तर</span><span style=\"font-family:Cambria Math\">   </span><span style=\"font-family:Cambria Math\">दें।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image7.png\"/></p> <p><span style=\"font-family:Cambria Math\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">छह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विषयों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रतिशत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कितने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हैं</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p>  90.6 percent</span></p>", " <p>  84.4 percent</span></p>", 
                                " <p>  86.9 percent</span></p>", " <p> 88.5 percent</span></p>"],
                    options_hi: [" <p>  90.6 </span><span style=\"font-family:Cambria Math\">प्रतिशत</span></p>", " <p>  84.4</span><span style=\"font-family:Cambria Math\">प्रतिशत</span></p>",
                                " <p>  86.9 </span><span style=\"font-family:Cambria Math\">प्रतिशत</span></p>", " <p> 88.5 </span><span style=\"font-family:Cambria Math\">प्रतिशत</span></p>"],
                    solution_en: " <p>10.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Percentage score of all the six subjects combined = </span><span style=\"font-family:Cambria Math\">×100 = </span><span style=\"font-family:Cambria Math\"> = 88.5%</span></p>",
                    solution_hi: " <p>10.(d)</span></p> <p><span style=\"font-family:Cambria Math\">संयुक्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">छह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विषयों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रति</span><span style=\"font-family:Cambria Math\">शत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">स्कोर</span><span style=\"font-family:Cambria Math\"> = </span><span style=\"font-family:Cambria Math\">×100 = </span><span style=\"font-family:Cambria Math\"> = 88.5%</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">11.</span><span style=\"font-family:Cambria Math\"> The following table shows the number of students in different streams in 5 departments.  Study the table carefully and answer the question based on the table.</span></p> <p><span style=\"font-family:Cambria Math\">Stream</span></p> <p><span style=\"font-family:Cambria Math\">Department</span></p> <p><span style=\"font-family:Cambria Math\">A</span></p> <p><span style=\"font-family:Cambria Math\">B</span></p> <p><span style=\"font-family:Cambria Math\">C</span></p> <p><span style=\"font-family:Cambria Math\">D</span></p> <p><span style=\"font-family:Cambria Math\">E</span></p> <p><span style=\"font-family:Cambria Math\">Physics</span></p> <p><span style=\"font-family:Cambria Math\">400</span></p> <p><span style=\"font-family:Cambria Math\">500</span></p> <p><span style=\"font-family:Cambria Math\">350</span></p> <p><span style=\"font-family:Cambria Math\">300</span></p> <p><span style=\"font-family:Cambria Math\">600</span></p> <p><span style=\"font-family:Cambria Math\">Chemistry</span></p> <p><span style=\"font-family:Cambria Math\">750</span></p> <p><span style=\"font-family:Cambria Math\">600</span></p> <p><span style=\"font-family:Cambria Math\">700</span></p> <p><span style=\"font-family:Cambria Math\">500</span></p> <p><span style=\"font-family:Cambria Math\">450</span></p> <p><span style=\"font-family:Cambria Math\">Mathematics</span></p> <p><span style=\"font-family:Cambria Math\">350</span></p> <p><span style=\"font-family:Cambria Math\">400</span></p> <p><span style=\"font-family:Cambria Math\">450</span></p> <p><span style=\"font-family:Cambria Math\">500</span></p> <p><span style=\"font-family:Cambria Math\">650</span></p> <p><span style=\"font-family:Cambria Math\"> Computer Science</span></p> <p><span style=\"font-family:Cambria Math\">250</span></p> <p><span style=\"font-family:Cambria Math\">300</span></p> <p><span style=\"font-family:Cambria Math\">380</span></p> <p><span style=\"font-family:Cambria Math\">550</span></p> <p><span style=\"font-family:Cambria Math\">750</span></p> <p><span style=\"font-family:Cambria Math\">Find the ratio of students in departments A and B In Physics , Chemistry and Mathematics streams.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">11.</span><span style=\"font-family:Cambria Math\">   </span><span style=\"font-family:Cambria Math\">निम्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सारणी</span><span style=\"font-family:Cambria Math\"> 5 </span><span style=\"font-family:Cambria Math\">विभागों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्गों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">छात्रों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दर्शाती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सारणी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ध्यानपूर्वक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अध्ययन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">इस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सारणी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आधार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">।</span></p> <p><span style=\"font-family:Cambria Math\">       </span></p> <p><span style=\"font-family:Cambria Math\">वर्ग</span></p> <p><span style=\"font-family:Cambria Math\">विभाग</span></p> <p><span style=\"font-family:Cambria Math\">A</span></p> <p><span style=\"font-family:Cambria Math\">B</span></p> <p><span style=\"font-family:Cambria Math\">C</span></p> <p><span style=\"font-family:Cambria Math\">D</span></p> <p><span style=\"font-family:Cambria Math\">E</span></p> <p><span style=\"font-family:Cambria Math\">भौतिकी</span></p> <p><span style=\"font-family:Cambria Math\">400</span></p> <p><span style=\"font-family:Cambria Math\">500</span></p> <p><span style=\"font-family:Cambria Math\">350</span></p> <p><span style=\"font-family:Cambria Math\">300</span></p> <p><span style=\"font-family:Cambria Math\">600</span></p> <p><span style=\"font-family:Cambria Math\">रसायन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विज्ञान</span></p> <p><span style=\"font-family:Cambria Math\">750</span></p> <p><span style=\"font-family:Cambria Math\">600</span></p> <p><span style=\"font-family:Cambria Math\">700</span></p> <p><span style=\"font-family:Cambria Math\">500</span></p> <p><span style=\"font-family:Cambria Math\">450</span></p> <p><span style=\"font-family:Cambria Math\">गणित</span></p> <p><span style=\"font-family:Cambria Math\">350</span></p> <p><span style=\"font-family:Cambria Math\">400</span></p> <p><span style=\"font-family:Cambria Math\">450</span></p> <p><span style=\"font-family:Cambria Math\">500</span></p> <p><span style=\"font-family:Cambria Math\">650</span></p> <p><span style=\"font-family:Cambria Math\">कम्प्यूटर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विज्ञान</span></p> <p><span style=\"font-family:Cambria Math\">250</span></p> <p><span style=\"font-family:Cambria Math\">300</span></p> <p><span style=\"font-family:Cambria Math\">380</span></p> <p><span style=\"font-family:Cambria Math\">550</span></p> <p><span style=\"font-family:Cambria Math\">750</span></p> <p><span style=\"font-family:Cambria Math\">भौतिकी</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">रसायन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विज्ञान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गणित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्गों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाग</span><span style=\"font-family:Cambria Math\"> A </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> B </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">छात्रों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अनुपात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कीजिए।</span></p>",
                    options_en: [" <p>  1 : 3</span></p>", " <p>  3 : 1</span></p>", 
                                " <p>  1 : 1</span></p>", " <p>  2 : 1</span></p>"],
                    options_hi: [" <p>  1 : 3</span></p>", " <p>  3 : 1</span></p>",
                                " <p>  1 : 1</span></p>", " <p>  2 : 1</span></p>"],
                    solution_en: " <p>11.(c)</span></p> <p><span style=\"font-family:Cambria Math\">No. of students in department A in Physics , Chemistry and Mathematics = 400+750+350 = 1500</span></p> <p><span style=\"font-family:Cambria Math\">No. of students in department B in Physics , Chemistry and Mathematics = 500+600+400 = 1500</span></p> <p><span style=\"font-family:Cambria Math\">Required ratio = 1500 : 1500 = 1 : 1</span></p>",
                    solution_hi: " <p>11.(c)</span></p> <p><span style=\"font-family:Cambria Math\">भौतिकी</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">रसायन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विज्ञान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गणित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाग</span><span style=\"font-family:Cambria Math\"> A </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">छात्रों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> = 400+750+350 = 1500</span></p> <p><span style=\"font-family:Cambria Math\">भौतिकी</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">रसायन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विज्ञान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गणित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभाग</span><span style=\"font-family:Cambria Math\"> B </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">छात्रों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> = 500+600+400 = 1500</span></p> <p><span style=\"font-family:Cambria Math\">वांछित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अनुपात</span><span style=\"font-family:Cambria Math\">  = 1500 : 1500 = 1 : 1</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">12. A table showing the percentage of the tot</span><span style=\"font-family:Cambria Math\">al population of a state by age groups for the year 2019  is given.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image8.png\"/></p> <p><span style=\"font-family:Cambria Math\">Age group in years</span></p> <p><span style=\"font-family:Cambria Math\">UP to 16</span></p> <p><span style=\"font-family:Cambria Math\">17-24</span></p> <p><span style=\"font-family:Cambria Math\">25-32</span></p> <p><span style=\"font-family:Cambria Math\">33-40</span></p> <p><span style=\"font-family:Cambria Math\">41-48</span></p> <p><span style=\"font-family:Cambria Math\">49-56</span></p> <p><span style=\"font-family:Cambria Math\">57 and above</span></p> <p><span style=\"font-family:Cambria Math\">Percentage</span></p> <p><span style=\"font-family:Cambria Math\">28.00</span></p> <p><span style=\"font-family:Cambria Math\">16.35</span></p> <p><span style=\"font-family:Cambria Math\">17.20</span></p> <p><span style=\"font-family:Cambria Math\">14.45</span></p> <p><span style=\"font-family:Cambria Math\">15.21</span></p> <p><span style=\"font-family:Cambria Math\">7.15</span></p> <p><span style=\"font-family:Cambria Math\">1.64</span></p> <p><span style=\"font-family:Cambria Math\">Out of every 3,800 persons what is the no of persons below 25 yrs (correct to closest integer value)</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">12. </span><span style=\"font-family:Cambria Math\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तालिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्ष</span><span style=\"font-family:Cambria Math\"> 2019 </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आयु</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">समूहों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">द्वारा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">राज्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">जनसंख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रतिशत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दर्शाया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image9.png\"/></p> <p><span style=\"font-family:Cambria Math\">आयु</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्षों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span></p> <p><span style=\"font-family:Cambria Math\">16 </span><span style=\"font-family:Cambria Math\">तक</span></p> <p><span style=\"font-family:Cambria Math\">17-24</span></p> <p><span style=\"font-family:Cambria Math\">25-32</span></p> <p><span style=\"font-family:Cambria Math\">33-40</span></p> <p><span style=\"font-family:Cambria Math\">41-48</span></p> <p><span style=\"font-family:Cambria Math\">49-56</span></p> <p><span style=\"font-family:Cambria Math\">57 </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अधिक</span></p> <p><span style=\"font-family:Cambria Math\">प्रतिशत</span></p> <p><span style=\"font-family:Cambria Math\">28.00</span></p> <p><span style=\"font-family:Cambria Math\">16.35</span></p> <p><span style=\"font-family:Cambria Math\">17.20</span></p> <p><span style=\"font-family:Cambria Math\">14.45</span></p> <p><span style=\"font-family:Cambria Math\">15.21</span></p> <p><span style=\"font-family:Cambria Math\">7.15</span></p> <p><span style=\"font-family:Cambria Math\">1.64</span></p> <p><span style=\"font-family:Cambria Math\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> 3,800 </span><span style=\"font-family:Cambria Math\">व्यक्तियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> 25 </span><span style=\"font-family:Cambria Math\">वर्ष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आयु</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">व्यक्तियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कितनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">? (</span><span style=\"font-family:Cambria Math\">निकटतम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पूर्णांक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">तक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">सही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दीजिए</span><span style=\"font-family:Cambria Math\">)</span></p>",
                    options_en: [" <p>  1,264 approx</span></p>", " <p>  1,432 approx</span></p>", 
                                " <p>  1,685 approx</span></p>", " <p> 1,543 approx</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">लगभग</span><span style=\"font-family:Cambria Math\"> 1,264</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">लगभग</span><span style=\"font-family:Cambria Math\"> 1,432</span></p>",
                                " <p>  </span><span style=\"font-family:Cambria Math\">लगभग</span><span style=\"font-family:Cambria Math\"> 1,685</span></p>", " <p>  </span><span style=\"font-family:Cambria Math\">लगभग</span><span style=\"font-family:Cambria Math\"> 1,543</span></p>"],
                    solution_en: " <p>12.(c)</span></p> <p><span style=\"font-family:Cambria Math\">The no. of persons below 25 yrs = (28+16.35)% of 3800 = 44.35% of 3800 </span><span style=\"font-family:Cambria Math\"> 1,685</span></p>",
                    solution_hi: " <p>12.(c)</span></p> <p><span style=\"font-family:Cambria Math\">25 </span><span style=\"font-family:Cambria Math\">वर्ष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">व्यक्तियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> = (28+16.35)% of 3800 = 44.35% of 3800 </span><span style=\"font-family:Cambria Math\"> 1,685</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: " <p><span style=\"font-family:Cambria Math\">13.  </span><span style=\"font-family:Cambria Math\">The following pie-chart represents a total expenditure of ₹2,28,000 on d</span><span style=\"font-family:Cambria Math\">ifferent items in constructing a house in a town. </span></p> <p><span style=\"font-family:Cambria Math\">Total Expenditure is ₹ 2,28,000.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image10.png\"/></p> <p><span style=\"font-family:Cambria Math\">What is the total expenditure done on Steel, Cement and Tiles?</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">13. </span><span style=\"font-family:Cambria Math\">निम्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">चार्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">शहर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">घर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">निर्माण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">विभिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">मदों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गए</span><span style=\"font-family:Cambria Math\"> ₹2,28,000 </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">व्यय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">निरूपित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">      </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">व्यय</span><span style=\"font-family:Cambria Math\"> ₹2,28,000 </span><span style=\"font-family:Cambria Math\">है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image11.png\"/></p> <p><span style=\"font-family:Cambria Math\">स्टील</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">सीमेंट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">टाइल्स</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">व्यय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कितना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p>  Rs.1,06,560</span></p>", " <p>  Rs.1,04,880</span></p>", 
                                " <p>  Rs.1,24,520</span></p>", " <p> Rs.1,34,560</span></p>"],
                    options_hi: [" <p>  Rs.1,06,560</span></p>", " <p>  Rs.1,04,880</span></p>",
                                " <p>  Rs.1,24,520</span></p>", " <p> Rs.1,34,560</span></p>"],
                    solution_en: " <p>13.(b)</span></p> <p><span style=\"font-family:Cambria Math\">Total expenditure done on steel, cement and tiles = (18+19+9)% of 2,28,000 = 46% of 2,28,000 = ₹1,04,880</span></p>",
                    solution_hi: " <p>13.(b)</span></p> <p><span style=\"font-family:Cambria Math\">स्टील</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">सीमेंट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">टाइल्स</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">खर्च</span><span style=\"font-family:Cambria Math\"> = (18+19+9)% of 2,28,000 = 46% of 2,28,000 = ₹1,04,880</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: " <p>14.</span><span style=\"font-family:Cambria Math\"> The following bar graph shows the sales (in thousand numbers) of scooters from six different companies in 2011 and 2012.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image12.png\"/></p> <p><span style=\"font-family:Cambria Math\">The total sales of company B for both the years are what percentage of the total sales of company E for both the years?</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">14.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दण्ड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आरेख</span><span style=\"font-family:Cambria Math\"> 2011 </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> 2012 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">छह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अलग</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Cambria Math\">अलग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंपनियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">स्कूटरों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बिक्री</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Cambria Math\">हजार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\">) </span><span style=\"font-family:Cambria Math\">दर्शाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image13.png\"/></p> <p><span style=\"font-family:Cambria Math\">दोनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्षों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंपनी</span><span style=\"font-family:Cambria Math\"> B </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बिक्री</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Cambria Math\">दोनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्षों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंपनी</span><span style=\"font-family:Cambria Math\"> E </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बिक्री</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कितना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प</span><span style=\"font-family:Cambria Math\">्रतिशत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    options_en: [" <p>  </span><span style=\"font-family:Cambria Math\">64.90%</span></p>", " <p>  61.80%</span></p>", 
                                " <p>  62.50%</span></p>", " <p> 65.12%</span></p>"],
                    options_hi: [" <p>  64.90%</span></p>", " <p>  61.80%</span></p>",
                                " <p>  62.50%</span></p>", " <p> 65.12%</span></p>"],
                    solution_en: " <p>14.(d)</span></p> <p><span style=\"font-family:Cambria Math\">The total sales of company B for both the years = 10+18 = 28</span></p> <p><span style=\"font-family:Cambria Math\">The total sales of company E for both the years = 18+25 = 43</span></p> <p><span style=\"font-family:Cambria Math\">Required % = </span><span style=\"font-family:Cambria Math\">×100 = </span><span style=\"font-family:Cambria Math\"> = 65.12%</span></p>",
                    solution_hi: " <p>14.(d)</span></p> <p><span style=\"font-family:Cambria Math\">दोनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्षों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंपनी</span><span style=\"font-family:Cambria Math\"> B </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बिक्री</span><span style=\"font-family:Cambria Math\"> = 10+18 = 28</span></p> <p><span style=\"font-family:Cambria Math\">दोनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">वर्षों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंपनी</span><span style=\"font-family:Cambria Math\"> E </span><span style=\"font-family:Cambria Math\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बिक्री</span><span style=\"font-family:Cambria Math\"> = 18+25 = 43</span></p> <p><span style=\"font-family:Cambria Math\">वांछित</span><span style=\"font-family:Cambria Math\"> % = </span><span style=\"font-family:Cambria Math\">×100 = </span><span style=\"font-family:Cambria Math\"> = 65.12%</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: " <p>15.</span><span style=\"font-family:Cambria Math\"> The following graph shows the income and expenditure of a company. Study the graph carefully and answer the question based on the graph.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image14.png\"/></p> <p><span style=\"font-family:Cambria Math\">The difference between the profits in 2015 and 2016 is:</span></p>",
                    question_hi: " <p>15. </span><span style=\"font-family:Cambria Math\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ग्राफ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंपनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">व्यय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दर्शाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ग्राफ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ध्यानपूर्वक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अध्ययन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ग्राफ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">आधारित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">उत्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">दीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/**********/word/media/image15.png\"/></p> <p><span style=\"font-family:Cambria Math\">2015 </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> 2016 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">हुए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लाभों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बीच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंतर</span><span style=\"font-family:Cambria Math\"> _________ </span><span style=\"font-family:Cambria Math\">है।</span></p>",
                    options_en: [" <p>  Rs. 1 lac</span></p>", " <p>  Rs.20 lacs</span></p>", 
                                " <p>  Rs.15 lacs</span></p>", " <p> Rs.10</span><span style=\"font-family:Cambria Math\"> lacs</span></p>"],
                    options_hi: [" <p> Rs.1 </span><span style=\"font-family:Cambria Math\">लाख</span></p>", " <p> Rs.20 </span><span style=\"font-family:Cambria Math\">लाख</span></p>",
                                " <p> Rs.15 </span><span style=\"font-family:Cambria Math\">लाख</span></p>", " <p> Rs.10 </span><span style=\"font-family:Cambria Math\">लाख</span></p>"],
                    solution_en: " <p>15.(d)</span></p> <p><span style=\"font-family:Cambria Math\">Profit of a company in 2015 = 40 - 30 = 10 lacs</span><span style=\"font-family:Cambria Math\">Profit of a company in 2016 =  50 - 30 = 20 lacs</span></p> <p><span style=\"font-family:Cambria Math\">The difference between the profits in 2015 and 2016 = 20 - 10 = 10 lacs</span></p>",
                    solution_hi: " <p>15.(d)</span></p> <p><span style=\"font-family:Cambria Math\">2015 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंपनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लाभ</span><span style=\"font-family:Cambria Math\"> = 40 - 30 = </span><span style=\"font-family:Cambria Math\">रु</span><span style=\"font-family:Cambria Math\">.10 </span><span style=\"font-family:Cambria Math\">लाख</span><span style=\"font-family:Cambria Math\">2015 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">कंपनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लाभ</span><span style=\"font-family:Cambria Math\"> =  50 - 30 = </span><span style=\"font-family:Cambria Math\">रु</span><span style=\"font-family:Cambria Math\">.20 </span><span style=\"font-family:Cambria Math\">लाख</span></p> <p><span style=\"font-family:Cambria Math\">2015 </span><span style=\"font-family:Cambria Math\">और</span><span style=\"font-family:Cambria Math\"> 2016 </span><span style=\"font-family:Cambria Math\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">लाभ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">बीच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">अंतर</span><span style=\"font-family:Cambria Math\"> = 20 - 10 = </span><span style=\"font-family:Cambria Math\">रु</span><span style=\"font-family:Cambria Math\">.10 </span><span style=\"font-family:Cambria Math\">लाख</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>