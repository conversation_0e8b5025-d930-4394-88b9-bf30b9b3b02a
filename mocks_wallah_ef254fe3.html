<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A thief is spotted by a policeman from a distance of 400 metres. When the policeman starts to chase the thief also starts running. If the speed of the thief is 18 km/h and that of the policeman 20 km/h, then how far would the thief have run before he is overtaken ?</p>",
                    question_hi: "<p>1. एक पुलिसकर्मी एक चोर को 400 m की दूरी से देखता है। जब पुलिसकर्मी उसका पीछा करना शुरू करता है, तो चोर भी भागने लगता है। यदि चोर की चाल 18 km/h है और पुलिसकर्मी की चाल 20 km/h है, तो पकड़े जाने से पहले चोर कितनी दूरी तय कर चुका होगा ?</p>",
                    options_en: ["<p>1.8 km</p>", "<p>3.6 km</p>", 
                                "<p>9 km</p>", "<p>2.5 km</p>"],
                    options_hi: ["<p>1.8 km</p>", "<p>3.6 km</p>",
                                "<p>9 km</p>", "<p>2.5 km</p>"],
                    solution_en: "<p>1.(b) Speed &prop; distance <br>Ratio&nbsp; &nbsp; &nbsp; &rarr;&nbsp; Thief&nbsp; :&nbsp; Policeman <br>Speed&nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp;18&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;20 or 9 : 10<br>Distance &rarr;&nbsp; &nbsp; &nbsp; 9&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;10<br>Now,<br>1 unit = 400 m<br>9 unit = 9 &times; 400 = 3600 m = 3.6 km <br>Hence, By the thief cover the distance is 3.6 km</p>",
                    solution_hi: "<p>1.(b) गति &prop; दूरी <br>अनुपात &rarr; चोर : पुलिसकर्मी <br>गति&nbsp; &nbsp; &nbsp; &rarr;&nbsp; 18 :&nbsp; &nbsp; &nbsp; 20 या 9 : 10<br>दूरी&nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp;9&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;10<br>अब,<br>1 इकाई = 400 m<br>9 इकाई = 9 &times; 400 = 3600 m = 3.6 km <br>अतः, चोर द्वारा तय की गई दूरी 3.6 km</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Two bicycle riders started at a speed of 7 m/sec and 8 m/sec on a circular path in opposite directions at the same point. If the circumference of the circle is 300 m, then in what time will they first meet ?</p>",
                    question_hi: "<p>2. दो साइकिल सवार एक ही स्&zwj;थान से विपरीत दिशाओं में एक वृत्ताकार पथ पर 7 m/sec और 8 m/sec की चाल से चलना शुरू करते हैं। यदि वृत्त की परिधि 300 m है, तो वे कितने समय में पहली बार मिलेंगे ?</p>",
                    options_en: ["<p>24 sec</p>", "<p>12 sec</p>", 
                                "<p>20 sec</p>", "<p>30 sec</p>"],
                    options_hi: ["<p>24 sec</p>", "<p>12 sec</p>",
                                "<p>20 sec</p>", "<p>30 sec</p>"],
                    solution_en: "<p>2.(c) Relative speed = 8 + 7 = 15 m/s<br>Time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>D</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>s</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>15</mn></mfrac></math> = 20 sec<br>Hence, they meet first time in 20 sec</p>",
                    solution_hi: "<p>2.(c) सापेक्ष गति = 8 + 7 = 15 m/s<br>समय = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2342;&#2370;&#2352;&#2368;</mi><mi>&#2327;&#2340;&#2367;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>15</mn></mfrac></math> = 20 सेकंड<br>अतः, वे 20 सेकंड में पहली बार मिलते हैं।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. A and B start running simultaneously, around a circular track of 1200 m, in the same direction, at a speed of 8 m/s and 12 m/s, respectively. A and B will meet at the starting point for the first time after:</p>",
                    question_hi: "<p>3. A और B, 1200 m के एक वृत्ताकार ट्रैक के चारों ओर एक ही दिशा में क्रमशः 8 m/s और 12 m/s की चाल से एक साथ दौड़ना आरम्&zwj;भ करते हैं। A और B प्रारंभिक बिंदु पर पहली बार ______बाद मिलेंगे।</p>",
                    options_en: ["<p>80 s</p>", "<p>150 s</p>", 
                                "<p>300 s</p>", "<p>100 s</p>"],
                    options_hi: ["<p>80 s</p>", "<p>150 s</p>",
                                "<p>300 s</p>", "<p>100 s</p>"],
                    solution_en: "<p>3.(c) If they are moving in same direction then the relative speed = 12 - 8 = 4m/s<br>Time after A and B meet starting point for first time = <math display=\"inline\"><mfrac><mrow><mn>1200</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 300 seconds</p>",
                    solution_hi: "<p>3.(c) यदि वे एक ही दिशा में आगे बढ़ रहे हैं तो सापेक्ष गति = 12 - 8 = 4m/s<br>A और B पहली बार प्रारंभिक बिंदु पर मिलते हैं = <math display=\"inline\"><mfrac><mrow><mn>1200</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 300 s बाद</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A thief is running away on a straight road on a bike moving at the speed of 28 m/s. A policeman chases him in a car moving at the speed of 30 m/s. if the instantaneous separation of the bike from the car is 400 m, how long will it take for the policeman to catch the thief ?</p>",
                    question_hi: "<p>4. एक चोर एक सीधी सड़क पर 28 मीटर/सेकेंड की चाल से बाइक चलाते हुए भाग रहा है। एक पुलिसकर्मी 30 मीटर/सेकेंड की चाल से एक कार को चलाते हुए उसका पीछा करता है। यदि कार से बाइक की तात्कालिक दूरी (instantaneous separation) 400 मीटर है, तो पुलिसकर्मी को चोर को पकड़ने में कितना समय लगेगा ?</p>",
                    options_en: ["<p>200 seconds</p>", "<p>100 seconds</p>", 
                                "<p>150 seconds</p>", "<p>300 seconds</p>"],
                    options_hi: ["<p>200 सेकंड</p>", "<p>100 सेकंड</p>",
                                "<p>150 सेकंड</p>", "<p>300 सेकंड</p>"],
                    solution_en: "<p>4.(a)<br>Distance between policeman and thief = 400 m<br>Relative speed = 30 - 28 = 2 m/sec<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>400</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 200 seconds</p>",
                    solution_hi: "<p>4.(a)<br>पुलिसकर्मी और चोर के बीच की दूरी = 400 मीटर<br>सापेक्ष गति = 30 - 28 = 2 मीटर/सेकंड<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>400</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 200 सेकंड</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. How many seconds will a boy take to run one complete round around a square field of side 38 metres, if he runs at a speed of 6 km/h ?</p>",
                    question_hi: "<p>5. एक लड़का 38 मीटर भुजा वाले एक वर्गाकार मैदान के चारों ओर एक पूरा चक्कर लगाने में कितने सेकंड का समय लेगा, यदि वह 6 km/h की चाल से दौड़ता है ?</p>",
                    options_en: ["<p>50.1</p>", "<p>71.2</p>", 
                                "<p>61.2</p>", "<p>91.2</p>"],
                    options_hi: ["<p>50.1</p>", "<p>71.2</p>",
                                "<p>61.2</p>", "<p>91.2</p>"],
                    solution_en: "<p>5.(d) Perimeter of the square field = 4 &times; 38 = 152m<br>Time taken to complete round around the square field = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>152</mn><mrow><mn>6</mn><mo>&#215;</mo><mfrac><mn>5</mn><mrow><mn>18</mn><mi>&#160;</mi></mrow></mfrac></mrow></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>152</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>5</mn></mfrac></math> = 91.2 sec</p>",
                    solution_hi: "<p>5.(d) वर्गाकार मैदान का परिमाप = 4 &times; 38 = 152m<br>वर्गाकार मैदान के चारों ओर चक्कर पूरा करने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>152</mn><mrow><mn>6</mn><mo>&#215;</mo><mfrac><mn>5</mn><mrow><mn>18</mn><mi>&#160;</mi></mrow></mfrac></mrow></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>152</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>5</mn></mfrac></math> = 91.2 सेकंड</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A person walks along a rectangular field, whose dimensions are 6 km long and 3 km wide. For the first 1 hour, he walks at a speed of 2 km/hr. After 1 hour, he increases his speed to 4 km/hr. Next 1 hour, his speed is decreased to 2 km/hr. For the next 1 hour, he increases his speed to 4 km/hr. Find his average speed (in km/hr).</p>",
                    question_hi: "<p>6. एक व्यक्ति एक आयताकार मैदान के अनुदिश चलता है, जिसकी लंबाई 6 km और चौड़ाई 3 km है। पहले 1 घंटे के लिए, वह 2 km/hr की चाल से चलता है। 1 घंटे के बाद, वह अपनी चाल बढ़ाकर 4 km/hr कर देता है। अगले 1 घंटे में उसकी चाल घटकर 2 km/hr हो जाती है। अगले 1 घंटे के लिए, वह अपनी चाल बढ़ाकर 4 km/hr कर देता है। उसकी औसत चाल (km/hr में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>5</p>", "<p>4</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>5</p>", "<p>4</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>6.(c) <strong>In 1<sup>st</sup> </strong>hour, distance covered by person at the speed 2km/h = 2 km<br>In 2nd hour , distance covered by person at the speed of 4 km/h = 4 km<br>In 3rd hour ,distance covered by person at the speed of 2km/h = 2 km<br>In 4th hour , distance covered by person at the speed of 4km/h = 4 km<br>Total distance covered = 2 + 4 + 2 + 4 = 12 km<br>Total time taken = 1 + 1 + 1 + 1 = 4 hour <br>Average speed = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>4</mn></mfrac></math> = 3 km/h</p>",
                    solution_hi: "<p>6.(c) पहले घंटे में, व्यक्ति द्वारा 2 किमी/घंटा की गति से तय की गई दूरी = 2 km<br>दूसरे घंटे में व्यक्ति द्वारा 4 किमी/घंटा की गति से तय की गई दूरी = 4 km<br>तीसरे घंटे में व्यक्ति द्वारा 2 किमी/घंटा की गति से तय की गई दूरी = 2 km<br>चौथे घंटे में व्यक्ति द्वारा 4 किमी/घंटा की गति से तय की गई दूरी = 4 km<br>तय की गई कुल दूरी = 2 + 4 + 2 + 4 = 12 km<br>कुल लिया गया समय = 1 + 1 + 1 + 1 = 4 घंटा<br>औसत गति = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mi>&#160;</mi><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>4</mn></mfrac></math> = 3 km/h</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. One train is moving at twice the speed of a second train. The lengths of the trains are equal. The ratio of the time taken by these trains to cross each other when moving in the same direction and in opposite directions, respectively, is:</p>",
                    question_hi: "<p>7. एक ट्रेन दूसरी ट्रेन की दुगुनी चाल से चल रही है। ट्रेनों की लंबाई बराबर है। एक ही दिशा में और विपरीत दिशाओं में चलते हुए इन ट्रेनों द्वारा एक दूसरे को पार करने में लगने वाले समय का अनुपात क्रमशः कितना है ?</p>",
                    options_en: ["<p>1 : 3</p>", "<p>3 : 2</p>", 
                                "<p>3 : 1</p>", "<p>2 : 3</p>"],
                    options_hi: ["<p>1 : 3</p>", "<p>3 : 2</p>",
                                "<p>3 : 1</p>", "<p>2 : 3</p>"],
                    solution_en: "<p>7.(c) Let the two train speed be A and B<br>Ratio&nbsp; &nbsp; &nbsp; &rarr; Train A : Train B<br>Speed&nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 1<br>Distance &rarr;&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 1 <br>Now,<br>Required ratio &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>1</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>3</mn></mfrac></math> or 3 : 1</p>",
                    solution_hi: "<p>7.(c) माना कि दोनों ट्रेनों की गति A और B है<br>अनुपात &rarr; ट्रेन A : ट्रेन B<br>गति&nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; 2&nbsp; &nbsp;:&nbsp; &nbsp; 1<br>दूरी&nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; 2&nbsp; &nbsp;:&nbsp; &nbsp; 1 <br>अब,<br>आवश्यक अनुपात &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>1</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>3</mn></mfrac></math> या 3 : 1</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Distance between A and B is 1200 km. If they start moving towards each other at same time, they will meet in 24 hrs. If A starts 10 hrs after B, then they will meet after another 20 hrs. What are their respective speeds ?</p>",
                    question_hi: "<p>8. A और B के बीच की दूरी 1200 किलोमीटर है। यदि वे एक ही समय पर एक-दूसरे की ओर बढ़ना शुरू करते है, तो वे 24 घंटे में मिलेंगे। यदि A, B के 10 घंटे बाद प्रारंभ करता है, तो वे 20 घंटे और अधिक बाद में मिलेंगे। उनकी क्रमशः चाल क्या हैं ?</p>",
                    options_en: ["<p>30 km/h, 20 km/h</p>", "<p>25 km/h, 35 km/h</p>", 
                                "<p>35 km/h, 25 km/h</p>", "<p>40 km/h, 25km/h</p>"],
                    options_hi: ["<p>30 किलोमीटर/घंटा, 20 किलोमीटर/घंटा,</p>", "<p>25 किलोमीटर/घंटा, 35 किलोमीटर/घंटा</p>",
                                "<p>35 किलोमीटर/घंटा, 25 किलोमीटर/घंटा</p>", "<p>40 किलोमीटर/घंटा, 25 किलोमीटर/घंटा,</p>"],
                    solution_en: "<p>8.(a)<br>Let speed of A and B be <math display=\"inline\"><mi>x</mi></math> and y respectively,<br>Relative speed = (<math display=\"inline\"><mi>x</mi></math> + y) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1200</mn><mn>24</mn></mfrac></math> = 50<br><math display=\"inline\"><mi>x</mi></math> + y = 50 ---------(i)<br>when B starts 10 hour before A then,<br>Distance covered by B in 10 hours = 10y<br>Now, they are travelling towards each other and time taken 20 hours<br>So, 10y + 20<math display=\"inline\"><mi>x</mi></math> + 20y = 1200<br>20<math display=\"inline\"><mi>x</mi></math> + 30y = 1200<br>2<math display=\"inline\"><mi>x</mi></math> + 3y = 120---------(ii)<br>By the equation (ii) - 2 &times; (i) we get;<br>2<math display=\"inline\"><mi>x</mi></math> + 3y - 2(x + y) = 120 - 2 &times; 50<br>y = 20 km/h<br>Put y = 20 km/h in eq .(i)<br><math display=\"inline\"><mi>x</mi></math> = 30 km/h<br>Hence, speed of A and B be 30 km/h and 20 km/h respectively,</p>",
                    solution_hi: "<p>8.(a)<br>माना A और B की गति क्रमशः x और y है,<br>सापेक्ष गति = (<math display=\"inline\"><mi>x</mi></math> + y) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1200</mn><mn>24</mn></mfrac></math> = 50<br><math display=\"inline\"><mi>x</mi></math> + y = 50 ---------(i)<br>जब B, A से 10 घंटे पहले शुरू करता है,<br>B द्वारा 10 घंटे में तय की गई दूरी = 10y<br>अब, वे एक-दूसरे की ओर यात्रा कर रहे हैं और समय 20 घंटे लग रहे हैं<br>तो, 10y + 20<math display=\"inline\"><mi>x</mi></math> + 20y = 1200<br>20<math display=\"inline\"><mi>x</mi></math> + 30y = 1200<br>2<math display=\"inline\"><mi>x</mi></math> + 3y = 120---------(ii)<br>समीकरण (ii) - 2 &times; (i) से हम पाते हैं;<br>2<math display=\"inline\"><mi>x</mi></math> + 3y - 2(x + y) = 120 - 2 &times; 50<br>y = 20 किमी/घंटा<br>समीकरण (i) में y = 20 किमी/घंटा रखने पर <br><math display=\"inline\"><mi>x</mi></math> = 30 किमी/घंटा<br>अतः, A और B की गति क्रमश: 30 किमी/घंटा और 20 किमी/घंटा है</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Raghav covers a distance of 45 km in 1 hour 40 minutes. Assuming he maintains a constant speed throughout his journey, what is Raghav&rsquo;s speed (in m/s) ?</p>",
                    question_hi: "<p>9. राघव 1 घंटा 40 मिनट में 45 km की दूरी तय करता है। यह मानते हुए कि वह अपनी पूरी यात्रा में एक समान चाल से चलता है, राघव की चाल (m/s में) क्या है ?</p>",
                    options_en: ["<p>9</p>", "<p>6</p>", 
                                "<p>7.5</p>", "<p>7.2</p>"],
                    options_hi: ["<p>9</p>", "<p>6</p>",
                                "<p>7.5</p>", "<p>7.2</p>"],
                    solution_en: "<p>9.(c)<br>1 hr 40 min. = 1 + <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> hour<br>Distance = 45 km<br>Speed = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = 45 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 27 km/h<br>= 27 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 3 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> = 7.5 m/sec</p>",
                    solution_hi: "<p>9.(c)<br>1 घंटा 40 मिनट = 1 + <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> घंटे<br>दूरी = 45 किमी<br>गति = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mfrac><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = 45 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 27 किमी/घंटा<br>= 27 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 3 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> = 7.5 मीटर/सेकंड</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Geeta went to her home town at a distance of 270 km from her house. She started her journey at 10:00 a.m. and reached the destination at 02:00 p.m. by travelling 20 km by cab at a speed of 60 km/h and 240 km by train running at a speed of 80 km/h. She covered the remaining distance by an auto to reach her place. What was the speed of the auto ?</p>",
                    question_hi: "<p>10. गीता अपने घर से 270 km दूर अपने गृहनगर गई। उसने अपनी यात्रा 10:00 a.m. पर शुरू की और 02:00 p.m. पर वह अपने गंतव्य स्थान पर पहुंच गई, जिसमें 20 km की यात्रा उसने 60 km/h की चाल से कैब से और 240 km की यात्रा 80 km/h की चाल से ट्रेन से तय की। अपने गंतव्य स्थान तक की शेष दूरी उसने ऑटो से तय की। ऑटो की चाल ज्ञात कीजिए।</p>",
                    options_en: ["<p>15 km/h</p>", "<p>4 km/h</p>", 
                                "<p>20 km/h</p>", "<p>18 km/h</p>"],
                    options_hi: ["<p>15 km/h</p>", "<p>4 km/h</p>",
                                "<p>20 km/h</p>", "<p>18 km/h</p>"],
                    solution_en: "<p>10.(a) Total distance covered by Geeta = 270 km&nbsp;<br>Total time taken by Geeta to reach the destination = 4hr (10:00a.m to 02:00p.m.) <br>Let speed of auto = xkm/h<br>According to question , <br>Speed of auto = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>80</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mi>x</mi></mfrac></math> = 4hr <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + 3 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mi>x</mi></mfrac></math> = 4<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mi>x</mi></mfrac></math> = 4 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math> <br>&rArr; x = 15 km/h</p>",
                    solution_hi: "<p>10.(a) गीता द्वारा तय की गई कुल दूरी = 270 km&nbsp;<br>गीता को गंतव्य तक पहुंचने में लगा कुल समय = 4hr (10:00a.m to 02:00p.m.) <br>माना ऑटो की गति = xkm/h<br>प्रश्न के अनुसार, <br>ऑटो की गति = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>80</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mi>x</mi></mfrac></math> = 4hr <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> + 3 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mi>x</mi></mfrac></math> = 4<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mi>x</mi></mfrac></math> = 4 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math> <br>&rArr; x = 15 km/h</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. During the evening patrolling, a policeman noticed a thief snatching a lady&rsquo;s mobile phone at a distance of 200 m. The thief starts running at a speed of 15 km/hr. If the policeman is chasing him at a speed of 16 km/hr, then the time taken to catch the thief is :</p>",
                    question_hi: "<p>11. शाम की गश्त के दौरान, एक पुलिसकर्मी ने 200 m की दूरी पर एक चोर को एक महिला का मोबाइल फोन छीनते हुए देखा। चोर 15 km/hr की चाल से दौड़ना शुरू करता है। यदि पुलिसकर्मी 16 km/hr की चाल से उसका पीछा कर रहा है, तो चोर को पकड़ने में उसे कितना समय लगेगा ?</p>",
                    options_en: ["<p>18 minutes</p>", "<p>12 minutes</p>", 
                                "<p>24 minutes</p>", "<p>30 minutes</p>"],
                    options_hi: ["<p>18 मिनट</p>", "<p>12 मिनट</p>",
                                "<p>24 मिनट</p>", "<p>30 मिनट</p>"],
                    solution_en: "<p>11.(b)<br>Distance between them = 200 m<br>Relative distance = 16 - 15 = 1 km/h<br>= 1 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> m/sec<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></mrow></mfrac></math> = 720 second <br>= <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 12 minutes.</p>",
                    solution_hi: "<p>11.(b)<br>उनके बीच की दूरी = 200 मीटर<br>सापेक्ष दूरी = 16 - 15 = 1 किमी/घंटा<br>= 1 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> मीटर/सेकंड<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></mrow></mfrac></math> = 720 सेकंड <br>= <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 12 मिनट</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. A person has to cover a distance of 8 km in 1 hour. If he covers one-fourth of the distance in one-third of the total time, then what should his speed (in km/h) be to cover the remaining distance in the remaining time so that the person reaches the destination exactly on time ?</p>",
                    question_hi: "<p>12. एक व्यक्ति को 1 घंटे में 8 km की दूरी तय करनी है। यदि वह कुल समय के एक-तिहाई समय में एक-चौथाई दूरी तय करता है, तो शेष दूरी को शेष समय में तय करने के लिए उसकी चाल (km/h में) कितनी होनी चाहिए ताकि वह व्यक्ति ठीक समय पर गंतव्य पर पहुंच जाए ?</p>",
                    options_en: ["<p>8</p>", "<p>7</p>", 
                                "<p>9</p>", "<p>6</p>"],
                    options_hi: ["<p>8</p>", "<p>7</p>",
                                "<p>9</p>", "<p>6</p>"],
                    solution_en: "<p>12.(c) Speed of a person = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 8 km/h<br>According to question,<br>Remaining time = 1 &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours&nbsp;<br>Remaining distance = 8 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 6 km<br>So, required speed = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = 9 km/h</p>",
                    solution_hi: "<p>12.(c) व्यक्ति की गति = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 8 km/h<br>प्रश्न के अनुसार,<br>शेष समय = 1 &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours <br>शेष दूरी = 8 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 6 km<br>तो, आवश्यक गति = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = 9 km/h</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Walking at a speed of 5.4 km/h, Rohit takes 40 minutes to reach his school from his home. What time will he take if he walks 8 km/h ?</p>",
                    question_hi: "<p>13. 5.4 km/h की चाल से चलते हुए, रोहित को अपने घर से स्कूल पहुँचने में 40 मिनट लगते हैं। यदि वह 8 km/h की चाल से चलता है तो उसे कितना समय लगेगा ?</p>",
                    options_en: ["<p>25 minutes</p>", "<p>24 minutes</p>", 
                                "<p>23 minutes</p>", "<p>27 minutes</p>"],
                    options_hi: ["<p>25 मिनट</p>", "<p>24 मिनट</p>",
                                "<p>23 मिनट</p>", "<p>27 मिनट</p>"],
                    solution_en: "<p>13.(d) Distance from the school = 5.4 &times; <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 3.6 km<br>So,<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>6</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>20</mn></mfrac></math> hours <br>= <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 60 = 27 min</p>",
                    solution_hi: "<p>13.(d) विद्यालय से दूरी = 5.4 &times; <math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 3.6 km<br>इसलिए,<br>अभीष्ट समय = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>6</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> घंटे&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 60 = 27 मिनट</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A train 315 m long is running at a speed of 54 km/h. What time will it take to cross a pole on its route ?</p>",
                    question_hi: "<p>14. 315 m लंबी एक रेलगाड़ी 54 km/h की चाल से चल रही है। अपने मार्ग पर एक खम्भे को पार करने में उसे कितना समय लगेगा ?</p>",
                    options_en: ["<p>18 s</p>", "<p>19 s</p>", 
                                "<p>21 s</p>", "<p>20 s</p>"],
                    options_hi: ["<p>18 s</p>", "<p>19 s</p>",
                                "<p>21 s</p>", "<p>20 s</p>"],
                    solution_en: "<p>14.(c)<br>Speed of train = 54 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 15 m/sec<br>Length of train = 315 m<br>So, time taken by train to cross the pole = <math display=\"inline\"><mfrac><mrow><mn>315</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 21 second.</p>",
                    solution_hi: "<p>14.(c)<br>ट्रेन की गति = 54 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 15 m/sec<br>रेलगाड़ी की लम्बाई = 315 m<br>तो, ट्रेन को खम्भे को पार करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>315</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 21 सेकंड।</p>",
                    correct: " c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. A train moving at the same speed passes two bridges of length 900 m and 486 m in 110 seconds and 70 seconds, respectively. The length of the train (in m) is:</p>",
                    question_hi: "<p>15. समान चाल से चल रही एक रेलगाड़ी 900 m और 486 m लंबे दो पुलों को क्रमशः 110 सेकंड और 70 सेकंड में पार करती है। रेलगाड़ी की लंबाई (m में) कितनी है ?</p>",
                    options_en: ["<p>283.5</p>", "<p>225.7</p>", 
                                "<p>253.8</p>", "<p>238.5</p>"],
                    options_hi: ["<p>283.5</p>", "<p>225.7</p>",
                                "<p>253.8</p>", "<p>238.5</p>"],
                    solution_en: "<p>15.(d) Let length of train = <math display=\"inline\"><mi>x</mi></math> m<br>According to question, <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>900</mn><mo>+</mo><mi>x</mi></mrow><mn>110</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>486</mn><mo>+</mo><mi>x</mi></mrow><mn>70</mn></mfrac></math><br>&rArr; 6300 + 7x = 5346 + 11x <br>&rArr; 4x = 954 &rArr; x = 238.5<br>&there4; Length of the train = 238.5m</p>",
                    solution_hi: "<p>15.(d) माना ट्रेन की लंबाई = <math display=\"inline\"><mi>x</mi></math> m<br>प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>900</mn><mo>+</mo><mi>x</mi></mrow><mn>110</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>486</mn><mo>+</mo><mi>x</mi></mrow><mn>70</mn></mfrac></math><br>&rArr; 6300 + 7x = 5346 + 11x <br>&rArr; 4x = 954 &rArr; x = 238.5<br>&there4; ट्रेन की लंबाई = 238.5 m</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>