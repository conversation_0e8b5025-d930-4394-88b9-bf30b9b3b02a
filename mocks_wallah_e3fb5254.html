<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Which of the following places is the origin of river Indus?</span></p>\\n",
                    question_hi: "<p>1. <span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2367;&#2306;&#2343;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2342;&#2381;&#2327;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Bokhar Chu</p>\\n", "<p>Gangotri Glacier</p>\\n", 
                                "<p>Chemayungdung Glacier</p>\\n", "<p>Zemu Glacier</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2348;&#2379;&#2326;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2369;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2327;&#2306;&#2327;&#2379;&#2340;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2381;&#2354;&#2375;&#2358;&#2367;&#2351;&#2352;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2330;&#2375;&#2350;&#2366;&#2351;&#2369;&#2306;&#2327;&#2337;&#2369;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2381;&#2354;&#2375;&#2358;&#2367;&#2351;&#2352;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2332;&#2364;&#2375;&#2350;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2381;&#2354;&#2375;&#2358;&#2367;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    solution_en: "<p>1.(a) <strong>Bokhar Chu </strong><span style=\"font-family: Cambria Math;\">near Mansarovar lake situated in Great Himalayas. </span><span style=\"font-family: Cambria Math;\"><strong>Rivers &amp; their Origins</strong>:</span><span style=\"font-family: Cambria Math;\"> Ganga (Gangotri Glacier), Godavari (Trimbakeshwar near Nashik), Brahmaputra (Kailash ranges of Himalayas), Yamuna (Yamunotri Glacier), Kaveri (Talakaveri, Brahmagiri Range), Narmada (Amarkantak), Tapi (Satpura Range), Krishna (Mahabaleshwar), Pennar (Nandi Hills), Luni (Pushkar, Aravalli Hills). </span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p>1.(a) <strong>&#2348;&#2379;&#2326;&#2352; &#2330;&#2370;</strong><span style=\"font-weight: 400;\"> &#2350;&#2366;&#2344;&#2360;&#2352;&#2379;&#2357;&#2352; &#2333;&#2368;&#2354; &#2325;&#2375; &#2346;&#2366;&#2360; &#2350;&#2361;&#2366;&#2344; &#2361;&#2367;&#2350;&#2366;&#2354;&#2351; &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2376;&#2404;</span><strong> &#2344;&#2342;&#2367;&#2351;&#2366;&#2305; &#2324;&#2352; &#2313;&#2344;&#2325;&#2375; &#2313;&#2342;&#2381;&#2327;&#2350; &#2360;&#2381;&#2341;&#2354;:</strong><span style=\"font-weight: 400;\"> &#2327;&#2306;&#2327;&#2366; (&#2327;&#2306;&#2327;&#2379;&#2340;&#2381;&#2352;&#2368; &#2327;&#2381;&#2354;&#2375;&#2358;&#2367;&#2351;&#2352;), &#2327;&#2379;&#2342;&#2366;&#2357;&#2352;&#2368; (&#2344;&#2366;&#2360;&#2367;&#2325; &#2325;&#2375; &#2346;&#2366;&#2360; &#2340;&#2381;&#2352;&#2381;&#2351;&#2306;&#2348;&#2325;&#2375;&#2358;&#2381;&#2357;&#2352;), &#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2346;&#2369;&#2340;&#2381;&#2352; (&#2361;&#2367;&#2350;&#2366;&#2354;&#2351; &#2325;&#2368; &#2325;&#2376;&#2354;&#2366;&#2358; &#2346;&#2352;&#2381;&#2357;&#2340;&#2350;&#2366;&#2354;&#2366;), &#2351;&#2350;&#2369;&#2344;&#2366; (&#2351;&#2350;&#2369;&#2344;&#2379;&#2340;&#2381;&#2352;&#2368; &#2327;&#2381;&#2354;&#2375;&#2358;&#2367;&#2351;&#2352;), &#2325;&#2366;&#2357;&#2375;&#2352;&#2368; (&#2340;&#2366;&#2354;&#2325;&#2366;&#2357;&#2375;&#2352;&#2368;, &#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2327;&#2367;&#2352;&#2367; &#2346;&#2352;&#2381;&#2357;&#2340;&#2350;&#2366;&#2354;&#2366;), &#2344;&#2352;&#2381;&#2350;&#2342;&#2366; (&#2309;&#2350;&#2352;&#2325;&#2306;&#2335;&#2325;), &#2340;&#2366;&#2346;&#2368; (&#2360;&#2340;&#2346;&#2369;&#2337;&#2364;&#2366; &#2346;&#2352;&#2381;&#2357;&#2340;&#2358;&#2381;&#2352;&#2375;&#2339;&#2368;), &#2325;&#2371;&#2359;&#2381;&#2339;&#2366; (&#2350;&#2361;&#2366;&#2348;&#2354;&#2375;&#2358;&#2381;&#2357;&#2352;), &#2346;&#2375;&#2344;&#2381;&#2344;&#2366;&#2352; (&#2344;&#2306;&#2342;&#2368; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305;), &#2354;&#2370;&#2344;&#2368; (&#2346;&#2369;&#2359;&#2381;&#2325;&#2352;, &#2309;&#2352;&#2366;&#2357;&#2354;&#2368; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2366;&#2305;)&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Who among the following Indian dancers was awarded the Sangeet Natak Akademi Award 2020 for contribution to Odissi dance?</span></p>\\n",
                    question_hi: "<p>2. <span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2352;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2323;&#2337;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2371;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2327;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2327;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2366;&#2335;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2366;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2020 </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2381;&#2350;&#2366;&#2344;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    options_en: ["<p>Brajendra Kumar Pattnaik</p>\\n", "<p>Balkar Sidhu</p>\\n", 
                                "<p>Rabindra Atibudhi</p>\\n", "<p>Pasumarthy Vithal</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2348;&#2381;&#2352;&#2332;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2350;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2335;&#2344;&#2366;&#2351;&#2325;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2348;&#2354;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2367;&#2342;&#2381;&#2343;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2352;&#2357;&#2368;&#2344;&#2381;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2340;&#2367;&#2348;&#2369;&#2342;&#2381;&#2343;&#2367;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2346;&#2360;&#2369;&#2350;&#2352;&#2381;&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2335;&#2381;&#2336;&#2354;</span></p>\\n"],
                    solution_en: "<p>2.(c) <span style=\"font-family: Cambria Math;\"><strong>Rabindra Atibudhi. Odissi dance:</strong><span style=\"font-weight: 400;\"> Indian classical dance form and it originates from the state of Odisha. </span><strong>Proponents of Odissi dance</strong><span style=\"font-weight: 400;\">: Kelucharan Mohapatra, Raghunath Dutta, Deba Prasad Das, Pankaj Charan Das, Gangadhar Pradhan, Sonal Mansingh, Jhelum Paranjape, Mayadhar Raut, Ileana Citaristi.</span></span></p>\\n",
                    solution_hi: "<p>2.(c) <strong>&#2352;&#2357;&#2368;&#2344;&#2381;&#2342;&#2381;&#2352; &#2309;&#2340;&#2367;&#2348;&#2369;&#2342;&#2381;&#2343;&#2367;&#2404; &#2323;&#2337;&#2367;&#2360;&#2368; &#2344;&#2371;&#2340;&#2381;&#2351;:</strong><span style=\"font-weight: 400;\"> &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351; &#2344;&#2371;&#2340;&#2381;&#2351; &#2358;&#2376;&#2354;&#2368; &#2324;&#2352; &#2311;&#2360;&#2325;&#2366; &#2313;&#2342;&#2381;&#2349;&#2357; &#2323;&#2337;&#2367;&#2358;&#2366; &#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2375; &#2361;&#2369;&#2310; &#2361;&#2376;&#2404;</span><strong> &#2323;&#2337;&#2367;&#2360;&#2368; &#2344;&#2371;&#2340;&#2381;&#2351; &#2325;&#2375; &#2346;&#2381;&#2352;&#2340;&#2367;&#2346;&#2366;&#2342;&#2325; </strong><span style=\"font-weight: 400;\">: &#2325;&#2375;&#2354;&#2369;&#2330;&#2352;&#2339; &#2350;&#2361;&#2366;&#2346;&#2366;&#2340;&#2381;&#2352;, &#2352;&#2328;&#2369;&#2344;&#2366;&#2341; &#2342;&#2340;&#2381;&#2340;&#2366;, &#2342;&#2375;&#2348;&#2366; &#2346;&#2381;&#2352;&#2360;&#2366;&#2342; &#2342;&#2366;&#2360;, &#2346;&#2306;&#2325;&#2332; &#2330;&#2352;&#2339; &#2342;&#2366;&#2360;, &#2327;&#2306;&#2327;&#2366;&#2343;&#2352; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;, &#2360;&#2379;&#2344;&#2354; &#2350;&#2366;&#2344;&#2360;&#2367;&#2306;&#2361;, &#2333;&#2375;&#2354;&#2350; &#2346;&#2352;&#2366;&#2306;&#2332;&#2346;&#2375;, &#2350;&#2366;&#2351;&#2366;&#2343;&#2352; &#2352;&#2366;&#2313;&#2340;, &#2311;&#2354;&#2367;&#2351;&#2366;&#2344;&#2366; &#2360;&#2367;&#2335;&#2366;&#2352;&#2367;&#2360;&#2381;&#2335;&#2368;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">The correct electronic configuration of Cu is : </span></p>\\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">Cu </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2344;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>[Ar]<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>9</mn></msup><mn>4</mn><msup><mi>s</mi><mn>2</mn></msup></math></p>\\n", "<p>[Kr]<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>9</mn></msup><mn>4</mn><msup><mi>s</mi><mn>2</mn></msup></math></p>\\n", 
                                "<p>[Kr]<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>10</mn></msup><mn>4</mn><msup><mi>s</mi><mn>1</mn></msup></math></p>\\n", "<p>[Ar]<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>10</mn></msup><mn>4</mn><msup><mi>s</mi><mn>1</mn></msup></math></p>\\n"],
                    options_hi: ["<p>[Ar]<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>9</mn></msup><mn>4</mn><msup><mi>s</mi><mn>2</mn></msup></math></p>\\n", "<p>[Kr]<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>9</mn></msup><mn>4</mn><msup><mi>s</mi><mn>2</mn></msup></math></p>\\n",
                                "<p>[Kr]<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>10</mn></msup><mn>4</mn><msup><mi>s</mi><mn>1</mn></msup></math></p>\\n", "<p>[Ar]<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>10</mn></msup><mn>4</mn><msup><mi>s</mi><mn>1</mn></msup></math></p>\\n"],
                    solution_en: "<p>3.(d) [Ar]<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>10</mn></msup><mn>4</mn><msup><mi>s</mi><mn>1</mn></msup></math><span style=\"font-family: Cambria Math;\">. <strong>Electronic Configuration</strong></span><span style=\"font-family: Cambria Math;\">: The Distribution of electrons in various shells, subshells and orbitals of an atom. It is represented by the formula 2n&sup2;</span><span style=\"font-family: Cambria Math;\">, where &lsquo;n&rsquo; is the shell number.</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">Copper</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(Cu) - Atomic number (29), Electronic configuration - 1s&sup2; 2s&sup2; 2p&#8310; 3s&sup2; 3p&#8310; 4s&sup1; 3d&sup1;&#8304;</span><span style=\"font-family: Cambria Math;\">&nbsp;or [Ar]</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>10</mn></msup><mn>4</mn><msup><mi>s</mi><mn>1</mn></msup></math><span style=\"font-family: Cambria Math;\">. </span></p>\\n",
                    solution_hi: "<p>3.(d) [Ar]<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>10</mn></msup><mn>4</mn><msup><mi>s</mi><mn>1</mn></msup></math><span style=\"font-family: Kokila;\">&#2404;</span><strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2344;&#2381;&#2351;&#2366;&#2360;</span></strong><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2350;&#2366;&#2339;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2358;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2313;&#2346;&#2325;&#2379;&#2358;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2325;&#2381;&#2359;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2340;&#2352;&#2339;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2n&sup2;</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2332;&#2361;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> \'n\' </span><span style=\"font-family: Kokila;\">&#2325;&#2379;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2377;&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">(Cu) - </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2350;&#2366;&#2339;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2350;&#2366;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> (29), </span><span style=\"font-family: Kokila;\">&#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2344;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">1s&sup2; 2s&sup2; 2p&#8310; 3s&sup2; 3p&#8310; 4s&sup1; 3d&sup1;&#8304; or [Ar]</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><msup><mi>d</mi><mn>10</mn></msup><mn>4</mn><msup><mi>s</mi><mn>1</mn></msup><mo>&nbsp;</mo></math><span style=\"font-family: Kokila;\">&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Which of the following festivals is mainly associated with Manipur ?</span></p>\\n",
                    question_hi: "<p>4. <span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2351;&#2380;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Yaoshang</p>\\n", "<p>Hareli</p>\\n", 
                                "<p>Baisakhi</p>\\n", "<p>Wangala</p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2351;&#2366;&#2323;&#2358;&#2366;&#2306;&#2327; (Yaoshang)</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2361;&#2352;&#2375;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> (Hareli) </span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2348;&#2376;&#2360;&#2366;&#2326;&#2368;</span><span style=\"font-family: Cambria Math;\"> (Baisakhi) </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2357;&#2306;&#2327;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> (Wangala) </span></p>\\n"],
                    solution_en: "<p>4.(a) <span style=\"font-family: Cambria Math;\">&nbsp;<strong>Yaoshang</strong><span style=\"font-weight: 400;\"> - A five-day event celebrated on the full moon day of the month of Lamda (February-March). It is indigenous tradition of the Meitei people. </span><strong>Manipur Festivals</strong><span style=\"font-weight: 400;\">: Lai Haraoba, Kang Festival, Heirku Hindongba, Chavang Kut, Sangai, Ningol Chakouba, Chumpha, Lui Ngai Ni.</span></span></p>\\n",
                    solution_hi: "<p>4.(a) <strong><span style=\"font-family: Kokila;\">&#2351;&#2366;&#2323;&#2358;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Kokila;\">&#2354;&#2376;&#2350;&#2381;&#2337;&#2366;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2347;&#2352;&#2357;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Kokila;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2367;&#2350;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2357;&#2360;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2381;&#2352;&#2350;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2376;&#2340;&#2375;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2357;&#2342;&#2375;&#2358;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2306;&#2346;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2381;&#2351;&#2380;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2352;&#2366;&#2323;&#2348;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2361;&#2379;&#2340;&#2381;&#2360;&#2357;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2352;&#2325;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2306;&#2337;&#2379;&#2306;&#2327;&#2348;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2330;&#2357;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2335;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2327;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2306;&#2327;&#2379;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2325;&#2379;&#2348;&#2366;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Kokila;\">&#2330;&#2369;&#2350;&#2381;&#2347;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2354;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">&#2344;&#2327;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">&#2344;&#2368;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Which of the following statements is correct about the Directive Principles of State Policy of the Indian Constitution?</span></p>\\n",
                    question_hi: "<p>5. <span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2342;&#2375;&#2358;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Consists of Art. 12 to 32</p>\\n", "<p>It has been borrowed from the Soviet Union</p>\\n", 
                                "<p>They are enumerated in Part II of the Constitution</p>\\n", "<p>Is described as &lsquo;novel feature of the Constitution&rsquo;</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2375;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> 32 </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2379;&#2357;&#2367;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2328;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> II </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2352;&#2381;&#2342;&#2367;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p>\'<span style=\"font-family: Kokila;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2357;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2339;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span></p>\\n"],
                    solution_en: "<p>5.(d) <span style=\"font-family: Cambria Math;\"><strong>Directive Principles of State Policy </strong><span style=\"font-weight: 400;\">(DPSP) - Part IV of the Constitution (Article 36-51). They are borrowed from the Constitution of Ireland</span><strong> </strong><span style=\"font-weight: 400;\">and are non-Justiciable Rights. </span><strong>Important provisions of the DPSP: </strong><span style=\"font-weight: 400;\">Equal justice and free legal aid (Article 39A), Village panchayats (Article 40), Uniform Civil Code (Article 44), Protection of forests and wildlife (Article 48A), Promotion of International Peace and Security (Article 51).</span></span></p>\\n",
                    solution_hi: "<p>5.(d) <strong>&#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2375; &#2344;&#2368;&#2340;&#2367; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2325; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340;</strong><span style=\"font-weight: 400;\"> (DPSP) - &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2366; &#2349;&#2366;&#2327; IV (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 36-51)&#2404; &#2311;&#2344;&#2381;&#2361;&#2375;&#2306; &#2310;&#2351;&#2352;&#2354;&#2376;&#2306;&#2337; &#2325;&#2375; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2360;&#2375; &#2354;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2324;&#2352; &#2351;&#2375; &#2327;&#2376;&#2352;-&#2344;&#2381;&#2351;&#2366;&#2351;&#2360;&#2306;&#2327;&#2340; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; &#2361;&#2376;&#2306;&#2404; </span><strong>DPSP &#2325;&#2375; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2346;&#2381;&#2352;&#2366;&#2357;&#2343;&#2366;&#2344;: </strong><span style=\"font-weight: 400;\">&#2360;&#2350;&#2366;&#2344; &#2344;&#2381;&#2351;&#2366;&#2351; &#2324;&#2352; &#2350;&#2369;&#2347;&#2381;&#2340; &#2325;&#2366;&#2344;&#2370;&#2344;&#2368; &#2360;&#2361;&#2366;&#2351;&#2340;&#2366; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 39A), &#2327;&#2381;&#2352;&#2366;&#2350; &#2346;&#2306;&#2330;&#2366;&#2351;&#2340;&#2375;&#2306; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 40), &#2360;&#2350;&#2366;&#2344; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325; &#2360;&#2306;&#2361;&#2367;&#2340;&#2366; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 44), &#2357;&#2344;&#2379;&#2306; &#2324;&#2352; &#2357;&#2344;&#2381;&#2351;&#2332;&#2368;&#2357;&#2379;&#2306; &#2325;&#2368; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 48A), &#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2358;&#2366;&#2306;&#2340;&#2367; &#2324;&#2352; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; &#2325;&#2379; &#2348;&#2338;&#2364;&#2366;&#2357;&#2366; (&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 51)&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">What is the state of matter that exhibits behaviour uniformity?</span></p>\\n",
                    question_hi: "<p>6. <span style=\"font-family: Kokila;\">&#2346;&#2342;&#2366;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;&#2360;&#2350;&#2366;&#2344;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2369;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Snow</p>\\n", "<p>Solid</p>\\n", 
                                "<p>Liquid</p>\\n", "<p>Gas</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2336;&#2379;&#2360;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2352;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2327;&#2376;&#2360;</span></p>\\n"],
                    solution_en: "<p>6.(d) <span style=\"font-family: Cambria Math;\"><strong>Gas. </strong><span style=\"font-weight: 400;\">The four main states of matter- Solids, Liquids, Gases and Plasma. </span><strong>Solid</strong><span style=\"font-weight: 400;\"> has definite shape and size. Example: Solid ice, Sugar, Rock, Wood, etc. </span><strong>Liquid</strong><span style=\"font-weight: 400;\">: Have no fixed shape but have a fixed volume. Example: Water, Milk, Blood, Urine, Gasoline. </span><strong>Gas</strong><span style=\"font-weight: 400;\">: Particles are far apart, fast-moving and not organised in any particular way. Example: Air. </span><strong>Plasma</strong><span style=\"font-weight: 400;\">: Electrically charged gas. Example: Auroras, lightning, and welding arcs. Fifth State of Matter - Bose-Einstein Condensate (only exists at near absolute zero temperatures and is very fragile and unstable).</span></span></p>\\n",
                    solution_hi: "<p>6.(d) <strong>&#2327;&#2376;&#2360;</strong><span style=\"font-weight: 400;\">&#2404;</span><strong> </strong><span style=\"font-weight: 400;\">&#2346;&#2342;&#2366;&#2352;&#2381;&#2341; &#2325;&#2368; &#2330;&#2366;&#2352; &#2350;&#2369;&#2326;&#2381;&#2351; &#2309;&#2357;&#2360;&#2381;&#2341;&#2366;&#2319;&#2305;- &#2336;&#2379;&#2360;, &#2342;&#2381;&#2352;&#2357; , &#2327;&#2376;&#2360; &#2324;&#2352; &#2346;&#2381;&#2354;&#2366;&#2332;&#2381;&#2350;&#2366;&#2404; </span><strong>&#2336;&#2379;&#2360; </strong><span style=\"font-weight: 400;\">&#2325;&#2366; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2310;&#2325;&#2366;&#2352; &#2319;&#2357;&#2306; &#2310;&#2351;&#2340;&#2344; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339;: &#2336;&#2379;&#2360; &#2348;&#2352;&#2381;&#2347;, &#2358;&#2352;&#2381;&#2325;&#2352;&#2366; , &#2330;&#2335;&#2381;&#2335;&#2366;&#2344;, &#2354;&#2325;&#2337;&#2364;&#2368;, &#2310;&#2342;&#2367;&#2404; </span><strong>&#2342;&#2381;&#2352;&#2357; </strong><span style=\"font-weight: 400;\">: &#2311;&#2360;&#2325;&#2366; &#2325;&#2379;&#2312; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2310;&#2325;&#2366;&#2352; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376; &#2354;&#2375;&#2325;&#2367;&#2344; &#2311;&#2360;&#2325;&#2366; &#2310;&#2351;&#2340;&#2344; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339;: &#2332;&#2354;, &#2342;&#2370;&#2343;, &#2352;&#2325;&#2381;&#2340;, &#2350;&#2370;&#2340;&#2381;&#2352;, &#2327;&#2376;&#2360;&#2379;&#2354;&#2368;&#2344;&#2404; </span><strong>&#2327;&#2376;&#2360;</strong><span style=\"font-weight: 400;\">: &#2325;&#2339; &#2342;&#2370;&#2352;-&#2342;&#2370;&#2352; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2340;&#2375;&#2332;&#2368; &#2360;&#2375; &#2348;&#2397;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2325;&#2367;&#2360;&#2368; &#2357;&#2367;&#2358;&#2375;&#2359; &#2340;&#2352;&#2368;&#2325;&#2375; &#2360;&#2375; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340; &#2344;&#2361;&#2368;&#2306; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339;: &#2357;&#2366;&#2351;&#2369;&#2404; </span><strong>&#2346;&#2381;&#2354;&#2366;&#2332;&#2381;&#2350;&#2366;</strong><span style=\"font-weight: 400;\">: &#2357;&#2367;&#2342;&#2381;&#2351;&#2369;&#2340; &#2310;&#2357;&#2375;&#2358;&#2367;&#2340; &#2327;&#2376;&#2360;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339;: &#2309;&#2352;&#2379;&#2352;&#2366;, &#2348;&#2367;&#2332;&#2354;&#2368; , &#2324;&#2352; &#2357;&#2375;&#2354;&#2381;&#2337;&#2367;&#2306;&#2327; &#2310;&#2352;&#2381;&#2325;&#2404; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; &#2325;&#2368; &#2346;&#2366;&#2306;&#2330;&#2357;&#2368;&#2306; &#2309;&#2357;&#2360;&#2381;&#2341;&#2366; - &#2348;&#2379;&#2360;-&#2310;&#2311;&#2306;&#2360;&#2381;&#2335;&#2368;&#2344; &#2325;&#2306;&#2337;&#2375;&#2344;&#2360;&#2375;&#2335; (&#2325;&#2375;&#2357;&#2354; &#2346;&#2370;&#2352;&#2381;&#2339; &#2358;&#2370;&#2344;&#2381;&#2351; &#2340;&#2366;&#2346;&#2350;&#2366;&#2344; &#2346;&#2352; &#2350;&#2380;&#2332;&#2370;&#2342; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2348;&#2361;&#2369;&#2340; &#2344;&#2366;&#2332;&#2369;&#2325; &#2324;&#2352; &#2309;&#2360;&#2381;&#2341;&#2367;&#2352; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;)&#2404;</span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">At what temperature (in degree celsius) is milk heated for 15 to 30 seconds to kill microbes in the pasteurization method ?</span></p>\\n",
                    question_hi: "<p>7. <span style=\"font-family: Kokila;\">&#2346;&#2366;&#2358;&#2381;&#2330;&#2369;&#2352;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2370;&#2325;&#2381;&#2359;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2368;&#2357;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2366;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2366;&#2346;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2337;&#2367;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;&#2354;&#2381;&#2360;&#2367;&#2351;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 15 </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 30 </span><span style=\"font-family: Kokila;\">&#2360;&#2375;&#2325;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>45</p>\\n", "<p>100</p>\\n", 
                                "<p>70</p>\\n", "<p>20</p>\\n"],
                    options_hi: ["<p>45</p>\\n", "<p>100</p>\\n",
                                "<p>70</p>\\n", "<p>20</p>\\n"],
                    solution_en: "<p>7.(c) <strong>70&#8451;. Pasteurization</strong><span style=\"font-family: Cambria Math;\">: Process by which milk is heated to a specific temperature for a set period of time to kill harmful bacteria that can lead to diseases like Listeriosis, Typhoid fever, Tuberculosis, Diphtheria and Brucellosis. This process was discovered by Louis Pasteur. </span></p>\\n",
                    solution_hi: "<p>7.(c) <strong>70&#8451; <span style=\"font-family: Kokila;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2358;&#2381;&#2330;&#2369;&#2352;&#2368;&#2325;&#2352;&#2339;</span></strong><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2357;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2366;&#2346;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2340;&#2366;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2344;&#2367;&#2325;&#2366;&#2352;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2376;&#2325;&#2381;&#2335;&#2368;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2360;&#2381;&#2335;&#2375;&#2352;&#2367;&#2351;&#2379;&#2360;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2335;&#2366;&#2311;&#2347;&#2366;&#2311;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2369;&#2326;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2340;&#2346;&#2375;&#2342;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2337;&#2367;&#2346;&#2381;&#2341;&#2368;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2381;&#2352;&#2369;&#2360;&#2375;&#2354;&#2379;&#2360;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2376;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2350;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2379;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2369;&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2358;&#2381;&#2330;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2368;&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Which state&rsquo;s governor approved the Prohibition of Online Gaming and Regulation of Online Games Bill 2022 ?</span></p>\\n",
                    question_hi: "<p>8. <span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2381;&#2351;&#2346;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2321;&#2344;&#2354;&#2366;&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2375;&#2350;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2359;&#2375;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2321;&#2344;&#2354;&#2366;&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2375;&#2350;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2306;&#2332;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Tamil Nadu</p>\\n", "<p>Andhra Pradesh</p>\\n", 
                                "<p>Gujarat</p>\\n", "<p>Punjab</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2310;&#2306;&#2343;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2327;&#2369;&#2332;&#2352;&#2366;&#2340;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2346;&#2306;&#2332;&#2366;&#2348;</span></p>\\n"],
                    solution_en: "<p>8.(a) <strong>Tamil Nadu</strong>.<span style=\"font-family: Cambria Math;\"> The Ministry of Electronics and Information Technology (MeitY) has released the Draft Rules for Online Gaming. The proposed rules have been introduced as an amendment to the Information Technology (Intermediary Guidelines and Digital Media Ethics Code) Rules, 2021.</span></p>\\n",
                    solution_hi: "<p>8.(a) <span style=\"font-family: Kokila;\"><strong>&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;</strong>&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2370;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2380;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> (MeitY) </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2321;&#2344;&#2354;&#2366;&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2375;&#2350;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2337;&#2381;&#2352;&#2366;&#2347;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2351;&#2350;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2370;&#2330;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2380;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2350;&#2343;&#2381;&#2351;&#2357;&#2352;&#2381;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2358;&#2366;&#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2337;&#2367;&#2332;&#2367;&#2335;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2368;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2361;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\">, 2021 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2358;&#2379;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">In the Indian constitution the concept of &lsquo;equality before law&rsquo; is borrowed from the _____ constitution. </span></p>\\n",
                    question_hi: "<p>9. <span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> \'</span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">\' </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2357;&#2343;&#2366;&#2352;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> _____ </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    options_en: ["<p>British</p>\\n", "<p>Canadian</p>\\n", 
                                "<p>US</p>\\n", "<p>Irish</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2348;&#2381;&#2352;&#2367;&#2335;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2325;&#2344;&#2366;&#2337;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2309;&#2350;&#2375;&#2352;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2310;&#2351;&#2352;&#2354;&#2376;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    solution_en: "<p>9.(a) <span style=\"font-family: Cambria Math;\">&nbsp;<strong>British. Article 14</strong><span style=\"font-weight: 400;\">: Equality before the law. Features of the Indian Constitution taken from other countries:</span><strong> Britain </strong><span style=\"font-weight: 400;\">- Parliamentary government, Rule of Law, Single citizenship, Parliamentary privileges, Bicameralism, etc. </span><strong>Ireland</strong><span style=\"font-weight: 400;\"> - Directive Principles of State Policy,&nbsp; Method of Election of the president, Members nomination to the Rajya Sabha by the President, etc. </span><strong>United States of America </strong><span style=\"font-weight: 400;\">- Impeachment of the president, Fundamental Rights, Judicial review, Preamble of the constitution, etc. </span><strong>Canada </strong><span style=\"font-weight: 400;\">- Residuary powers vest with the centre, Centre appoints the Governors at the states, etc.&nbsp; </span></span></p>\\n",
                    solution_hi: "<p>9.(a) <strong>&#2348;&#2381;&#2352;&#2367;&#2335;&#2375;&#2344; &#2404; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 14: </strong><span style=\"font-weight: 400;\">&#2357;&#2367;&#2343;&#2367; &#2325;&#2375; &#2360;&#2350;&#2325;&#2381;&#2359; &#2360;&#2350;&#2366;&#2344;&#2340;&#2366;&#2404; &#2309;&#2344;&#2381;&#2351; &#2342;&#2375;&#2358;&#2379; &#2360;&#2375; &#2354;&#2368; &#2327;&#2351;&#2368; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2368; &#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366;&#2319;&#2305;: </span><strong>&#2348;&#2381;&#2352;&#2367;&#2335;&#2375;&#2344; </strong><span style=\"font-weight: 400;\">- &#2360;&#2306;&#2360;&#2342;&#2368;&#2351; &#2360;&#2352;&#2325;&#2366;&#2352;, &#2357;&#2367;&#2343;&#2367; &#2325;&#2366; &#2358;&#2366;&#2360;&#2344;, &#2319;&#2325;&#2354; &#2344;&#2366;&#2327;&#2352;&#2367;&#2325;&#2340;&#2366;, &#2360;&#2306;&#2360;&#2342;&#2368;&#2351; &#2357;&#2367;&#2358;&#2375;&#2359;&#2366;&#2343;&#2367;&#2325;&#2366;&#2352;, &#2342;&#2381;&#2357;&#2367;&#2360;&#2342;&#2344;&#2368;&#2351;&#2340;&#2366;, &#2310;&#2342;&#2367;&#2404; </span><strong>&#2310;&#2351;&#2352;&#2354;&#2376;&#2306;&#2337; </strong><span style=\"font-weight: 400;\">- &#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2375; &#2344;&#2368;&#2340;&#2367; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2325; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340;, &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2357;&#2366;&#2330;&#2344; &#2325;&#2368; &#2357;&#2367;&#2343;&#2367;, &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2352;&#2366;&#2332;&#2381;&#2351;&#2360;&#2349;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; &#2360;&#2342;&#2360;&#2381;&#2351;&#2379;&#2306; &#2325;&#2366; &#2350;&#2344;&#2379;&#2344;&#2351;&#2344;, &#2310;&#2342;&#2367;&#2404; </span><strong>&#2360;&#2306;&#2351;&#2369;&#2325;&#2381;&#2340; &#2352;&#2366;&#2332;&#2381;&#2351; &#2309;&#2350;&#2375;&#2352;&#2367;&#2325;&#2366; - </strong><span style=\"font-weight: 400;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2346;&#2340;&#2367; &#2346;&#2352; &#2350;&#2361;&#2366;&#2349;&#2367;&#2351;&#2379;&#2327;, &#2350;&#2380;&#2354;&#2367;&#2325; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;, &#2344;&#2381;&#2351;&#2366;&#2351;&#2367;&#2325; &#2360;&#2350;&#2368;&#2325;&#2381;&#2359;&#2366;, &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2368; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;&#2344;&#2366;, &#2310;&#2342;&#2367;&#2404; </span><strong>&#2325;&#2344;&#2366;&#2337;&#2366; </strong><span style=\"font-weight: 400;\">- &#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2325;&#2375; &#2309;&#2343;&#2368;&#2344; &#2309;&#2357;&#2358;&#2367;&#2359;&#2381;&#2335; &#2358;&#2325;&#2381;&#2340;&#2367;&#2351;&#2366;&#2306;, &#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2375; &#2352;&#2366;&#2332;&#2381;&#2351;&#2346;&#2366;&#2354; &#2325;&#2368; &#2344;&#2367;&#2351;&#2369;&#2325;&#2381;&#2340;&#2367; &#2310;&#2342;&#2367;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">Gross primary deficit is equal to ________.</span></p>\\n",
                    question_hi: "<p>10. <span style=\"font-family: Kokila;\">&#2360;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2341;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2366;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> ________ </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    options_en: ["<p>difference between gross fiscal deficit and interest payments</p>\\n", "<p>difference between total expenditure and total receipts</p>\\n", 
                                "<p>difference between net borrowings and net capital receipts</p>\\n", "<p>difference between revenue deficit and capital expenditure</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2325;&#2379;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2366;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2315;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2381;&#2352;&#2361;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2306;&#2332;&#2368;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2366;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2306;&#2332;&#2368;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.(a) <strong>Gross Primary Deficit</strong> </span><span style=\"font-family: Cambria Math;\">is</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Gross Fiscal Deficit less interest payments. Fiscal deficit is the difference between the government&rsquo;s total expenditure and its total receipts excluding borrowing.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Effective Revenue Deficit: Revenue deficit as reduced by grants for the creation of capital assets.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\">.(a) </span><strong><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2341;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2366;&#2335;&#2366;</span></strong><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2325;&#2379;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2366;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2325;&#2379;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2366;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2379;&#2337;&#2364;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2366;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2306;&#2332;&#2368;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;&#2367;&#2360;&#2306;&#2346;&#2340;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2366;&#2335;&#2366;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">Who among the following Indian shuttlers has won the Silver medal in the U -17 women&rsquo;s singles at Badminton Asia Junior Championship 2022 ? </span></p>\\n",
                    question_hi: "<p>11. <span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2335;&#2354;&#2352;</span><span style=\"font-family: Cambria Math;\"> (shuttlers) </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2358;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2370;&#2344;&#2367;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2376;&#2350;&#2381;&#2346;&#2367;&#2351;&#2344;&#2358;&#2367;&#2346;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2337;&#2352;</span><span style=\"font-family: Cambria Math;\">-17 </span><span style=\"font-family: Kokila;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2332;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2368;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Amar Verma</p>\\n", "<p>Aryan Shah</p>\\n", 
                                "<p>Unnati Hooda</p>\\n", "<p>Abhishek Kiran</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2309;&#2350;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2350;&#2366;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2310;&#2352;&#2381;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2361;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2313;&#2344;&#2381;&#2344;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2369;&#2337;&#2366;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2309;&#2349;&#2367;&#2359;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2352;&#2339;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">.(c) <strong>Unnati Hooda. Badminton Asia Junior Championships: </strong><span style=\"font-weight: 400;\">Organized by the Badminton Asia governing body to select the best junior badminton players (under -19) in Asia. </span><strong>Badminton Asia Junior Championships 2022: </strong><span style=\"font-weight: 400;\">Nonthaburi, Thailand. </span><strong>2023:&nbsp; </strong><span style=\"font-weight: 400;\">Yogyakarta, Indonesia. </span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11</span><span style=\"font-family: Cambria Math;\">.(c) <strong>&#2313;&#2344;&#2381;&#2344;&#2340;&#2367; &#2361;&#2369;&#2337;&#2366;&#2404; &#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344; &#2319;&#2358;&#2367;&#2351;&#2366; &#2332;&#2370;&#2344;&#2367;&#2351;&#2352; &#2330;&#2376;&#2306;&#2346;&#2367;&#2351;&#2344;&#2358;&#2367;&#2346;:</strong><span style=\"font-weight: 400;\"> &#2319;&#2358;&#2367;&#2351;&#2366; &#2350;&#2375;&#2306; &#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336; &#2332;&#2370;&#2344;&#2367;&#2351;&#2352; &#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; (&#2309;&#2306;&#2337;&#2352; -19) &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344; &#2319;&#2358;&#2367;&#2351;&#2366; &#2358;&#2366;&#2360;&#2368; &#2344;&#2367;&#2325;&#2366;&#2351; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2310;&#2351;&#2379;&#2332;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2348;&#2376;&#2337;&#2350;&#2367;&#2306;&#2335;&#2344; &#2319;&#2358;&#2367;&#2351;&#2366; &#2332;&#2370;&#2344;&#2367;&#2351;&#2352; &#2330;&#2376;&#2306;&#2346;&#2367;&#2351;&#2344;&#2358;&#2367;&#2346; 2022:</strong><span style=\"font-weight: 400;\"> &#2344;&#2379;&#2306;&#2341;&#2348;&#2369;&#2352;&#2368;, &#2341;&#2366;&#2312;&#2354;&#2376;&#2306;&#2337;&#2404; 2023: &#2351;&#2379;&#2327;&#2381;&#2351;&#2325;&#2366;&#2352;&#2381;&#2340;&#2366;, &#2311;&#2306;&#2337;&#2379;&#2344;&#2375;&#2358;&#2367;&#2351;&#2366;&#2404;</span></span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">Which of the following was the capital of the Hindu Shahi Kingdom ?</span></p>\\n",
                    question_hi: "<p>12. <span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2367;&#2306;&#2342;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2343;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Srinagar</p>\\n", "<p>Lahore</p>\\n", 
                                "<p>Waihind</p>\\n", "<p>Multan</p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2358;&#2381;&#2352;&#2368;&#2344;&#2327;&#2352; (Srinagar)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2354;&#2366;&#2361;&#2380;&#2352; (Lahore)</span></p>\\n",
                                "<p><span style=\"font-weight: 400;\">&#2357;&#2376;&#2361;&#2367;&#2344;&#2381;&#2342; (Waihind)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2369;&#2354;&#2381;&#2340;&#2366;&#2344; (Multan)</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">.(c) <strong>Waihind. Hindu Shahi kingdom</strong><span style=\"font-weight: 400;\"> was founded by Kallar. It ruled over Kabul and the old province of Gandhara from the decline of the Kushan empire. This kingdom was known as the Kabul Shahi dynasty. </span><strong>Ancient Dynasties with their Capital: </strong><span style=\"font-weight: 400;\">Haryanka Dynasty - Rajgriha/Pataliputra, Shishunaga Dynasty - Rajgir,&nbsp; Nanda - Pataliputra, Satavahana - Pratishthana/Amaravati.</span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12</span><span style=\"font-family: Cambria Math;\">.(c) <strong>&#2357;&#2376;&#2361;&#2367;&#2344;&#2381;&#2342;&#2404; &#2361;&#2367;&#2306;&#2342;&#2370; &#2358;&#2366;&#2361;&#2368; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351; </strong><span style=\"font-weight: 400;\">&#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; &#2325;&#2354;&#2381;&#2354;&#2366;&#2352; &#2344;&#2375; &#2325;&#2368; &#2341;&#2368;&#2404; &#2311;&#2360;&#2344;&#2375; &#2325;&#2369;&#2359;&#2366;&#2339; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2375; &#2346;&#2340;&#2344; &#2325;&#2375; &#2348;&#2366;&#2342; &#2325;&#2366;&#2348;&#2369;&#2354; &#2324;&#2352; &#2327;&#2366;&#2306;&#2343;&#2366;&#2352; &#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2330;&#2368;&#2344; &#2346;&#2381;&#2352;&#2366;&#2306;&#2340; &#2346;&#2352; &#2358;&#2366;&#2360;&#2344; &#2325;&#2367;&#2351;&#2366;&#2404; &#2351;&#2361; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2366;&#2348;&#2369;&#2354; &#2358;&#2366;&#2361;&#2368; &#2352;&#2366;&#2332;&#2357;&#2306;&#2358; &#2325;&#2375; &#2344;&#2366;&#2350; &#2360;&#2375; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2341;&#2366;&#2404;</span><strong> &#2346;&#2381;&#2352;&#2366;&#2330;&#2368;&#2344; &#2357;&#2306;&#2358; &#2319;&#2357;&#2306; &#2313;&#2344;&#2325;&#2368; &#2352;&#2366;&#2332;&#2343;&#2366;&#2344;&#2368;: </strong><span style=\"font-weight: 400;\">&#2361;&#2352;&#2381;&#2351;&#2325; &#2357;&#2306;&#2358; - &#2352;&#2366;&#2332;&#2327;&#2371;&#2361;/&#2346;&#2366;&#2335;&#2354;&#2367;&#2346;&#2369;&#2340;&#2381;&#2352;, &#2358;&#2367;&#2358;&#2369;&#2344;&#2366;&#2327; &#2357;&#2306;&#2358; - &#2352;&#2366;&#2332;&#2327;&#2368;&#2352;, &#2344;&#2306;&#2342; &#2357;&#2306;&#2358; - &#2346;&#2366;&#2335;&#2354;&#2367;&#2346;&#2369;&#2340;&#2381;&#2352;, &#2360;&#2366;&#2340;&#2357;&#2366;&#2361;&#2344; &#2357;&#2306;&#2358; - &#2346;&#2381;&#2352;&#2340;&#2367;&#2359;&#2381;&#2336;&#2366;&#2344;/&#2309;&#2350;&#2352;&#2366;&#2357;&#2340;&#2368;&#2404;</span></span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">Pandavani Folk singer_______ is a Padma Bhushan Awardee of 2003.</span></p>\\n",
                    question_hi: "<p>13. <span style=\"font-family: Kokila;\">&#2346;&#2306;&#2337;&#2357;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2379;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2366;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> _______ 2003 </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2342;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2370;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2332;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span></p>\\n",
                    options_en: ["<p>Sulakshana Pandit</p>\\n", "<p>Teejan Bai</p>\\n", 
                                "<p>Mamta Chandrekar</p>\\n", "<p>Ila Arun</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2360;&#2369;&#2354;&#2325;&#2381;&#2359;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2306;&#2337;&#2367;&#2340;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2340;&#2368;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2312;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2350;&#2350;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2306;&#2342;&#2381;&#2352;&#2366;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2311;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2352;&#2369;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">.(b) <strong>Teejan Bai </strong><span style=\"font-weight: 400;\">(Chhattisgarh)</span><strong>. </strong><span style=\"font-weight: 400;\">She has been awarded Padma Vibhushan (2019),&nbsp; Padma Shri (1988), Sangeet Natak Academy award (1995). </span><strong>Pandavani: </strong><span style=\"font-weight: 400;\">Folk singing style that involves the narration of tales from the Mahabharata. </span><strong>Proponents associated with Pandavani</strong><span style=\"font-weight: 400;\">: Punaram Nishad, Chetan Dewangan, and Jhaduram Dewangan, Ritu Verma, etc. </span></span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">.(b) <strong>&#2340;&#2368;&#2332;&#2344; &#2348;&#2366;&#2312; </strong><span style=\"font-weight: 400;\">(&#2331;&#2340;&#2381;&#2340;&#2368;&#2360;&#2327;&#2338;&#2364;)&#2404; &#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2346;&#2342;&#2381;&#2350; &#2357;&#2367;&#2349;&#2370;&#2359;&#2339; (2019), &#2346;&#2342;&#2381;&#2350; &#2358;&#2381;&#2352;&#2368; (1988), &#2360;&#2306;&#2327;&#2368;&#2340; &#2344;&#2366;&#2335;&#2325; &#2309;&#2325;&#2366;&#2342;&#2350;&#2368; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; (1995) &#2360;&#2375; &#2360;&#2350;&#2381;&#2350;&#2366;&#2344;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2346;&#2306;&#2337;&#2357;&#2366;&#2344;&#2368;</strong><span style=\"font-weight: 400;\">: &#2354;&#2379;&#2325; &#2327;&#2366;&#2351;&#2344; &#2358;&#2376;&#2354;&#2368; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2350;&#2361;&#2366;&#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2325;&#2361;&#2366;&#2344;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2357;&#2352;&#2381;&#2339;&#2344; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2404; </span><strong>&#2346;&#2306;&#2337;&#2357;&#2366;&#2344;&#2368; &#2360;&#2375; &#2332;&#2369;&#2337;&#2364;&#2375; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;&#2325;:</strong><span style=\"font-weight: 400;\"> &#2346;&#2369;&#2344;&#2366;&#2352;&#2366;&#2350; &#2344;&#2367;&#2359;&#2366;&#2342;, &#2330;&#2375;&#2340;&#2344; &#2342;&#2375;&#2357;&#2366;&#2306;&#2327;&#2344; &#2324;&#2352; &#2333;&#2366;&#2337;&#2370;&#2352;&#2366;&#2350; &#2342;&#2375;&#2357;&#2366;&#2306;&#2327;&#2344;, &#2352;&#2367;&#2340;&#2369; &#2357;&#2352;&#2381;&#2350;&#2366; &#2310;&#2342;&#2367;&#2404;</span></span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Which of the following players won Silver at the Tokyo Olympics 2020 ?</span></p>\\n",
                    question_hi: "<p>14. <span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2367;&#2354;&#2366;&#2337;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2335;&#2379;&#2325;&#2381;&#2351;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2323;&#2354;&#2306;&#2346;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> 2020 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2332;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2368;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Mirabai Chanu and Sushil Kumar</p>\\n", "<p>PV Sindhu and Sushil Kumar</p>\\n", 
                                "<p>Ravi Kumar Dahiya and Mirabai Chanu</p>\\n", "<p>Ravi Kumar Dahiya and Sushil Kumar</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2350;&#2368;&#2352;&#2366;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2366;&#2344;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2369;&#2358;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2350;&#2366;&#2352;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2346;&#2368;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2367;&#2306;&#2343;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2369;&#2358;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2350;&#2366;&#2352;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2352;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2350;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2361;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2368;&#2352;&#2366;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2366;&#2344;&#2370;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2352;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2350;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2361;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2369;&#2358;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2350;&#2366;&#2352;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">.(c) </span><span style=\"font-family: Cambria Math;\">Ravi Kumar Dahiya: Indian freestyle wrestler in 57 Kg category. He became the second Indian wrestler to clinch an Olympic silver. Mirabai Chanu</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">(49 kg weightlifting category)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">first weightlifter to win silver at the Tokyo Olympics 2020. Tokyo Olympics 2020: India won seven medals - 1 Gold, 2 Silver and 4 Bronze. Olympics 2024:</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Paris (France). </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">14</span><span style=\"font-family: Cambria Math;\">.(c) </span><span style=\"font-family: Kokila;\">&#2352;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2369;&#2350;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2361;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">: 57 </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2354;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2347;&#2381;&#2352;&#2368;&#2360;&#2381;&#2335;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2354;&#2357;&#2366;&#2344;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2323;&#2354;&#2306;&#2346;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2332;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2368;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2354;&#2357;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2344;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2368;&#2352;&#2366;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2366;&#2344;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">(49 </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2327;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2379;&#2340;&#2381;&#2340;&#2379;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Kokila;\">&#2335;&#2379;&#2325;&#2381;&#2351;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2323;&#2354;&#2306;&#2346;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> 2020 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2332;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2368;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2379;&#2340;&#2381;&#2340;&#2379;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2335;&#2379;&#2325;&#2381;&#2351;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2323;&#2354;&#2306;&#2346;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> 2020: </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2368;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> - 1 </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2357;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\">, 2 </span><span style=\"font-family: Kokila;\">&#2352;&#2332;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2306;&#2360;&#2381;&#2351;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2323;&#2354;&#2306;&#2346;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> 2024: </span><span style=\"font-family: Kokila;\">&#2346;&#2375;&#2352;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2347;&#2381;&#2352;&#2366;&#2306;&#2360;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Kokila;\">&#2404;</span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">In which session of the Indian National Congress was the resolution declaring Poorna Swaraj passed ?</span></p>\\n",
                    question_hi: "<p>15. <span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2343;&#2367;&#2357;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2357;&#2352;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2379;&#2359;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    options_en: ["<p>Calcutta</p>\\n", "<p>Bombay</p>\\n", 
                                "<p>Madras</p>\\n", "<p>Lahore</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2325;&#2354;&#2325;&#2340;&#2381;&#2340;&#2366;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2348;&#2377;&#2350;&#2381;&#2348;&#2375;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2350;&#2342;&#2381;&#2352;&#2366;&#2360;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2361;&#2380;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">.(d) <strong>Lahore (1929). </strong><span style=\"font-weight: 400;\">Pandit Jawahar Lal Nehru presided over the Lahore session of congress and hoisted the tricolor flag of India. </span><strong>Congress Session and President: </strong><span style=\"font-weight: 400;\">Lucknow session - Ambica Charan Mazumdar (1916), Tripuri - Subhash Chandra Bose (1939), Gaya - Chittaranjan Das (1922), Kanpur - Sarojini Naidu (1925), Karachi - Vallabhbhai Patel (1931), Ramgarh - Maulana Abul Kalam Azad (1940).</span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15</span><span style=\"font-family: Cambria Math;\">.(d) <strong>&#2354;&#2366;&#2361;&#2380;&#2352; (1929)&#2404; </strong><span style=\"font-weight: 400;\">&#2346;&#2306;&#2337;&#2367;&#2340; &#2332;&#2357;&#2366;&#2361;&#2352; &#2354;&#2366;&#2354; &#2344;&#2375;&#2361;&#2352;&#2370; &#2344;&#2375; &#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360; &#2325;&#2375; &#2354;&#2366;&#2361;&#2380;&#2352; &#2309;&#2343;&#2367;&#2357;&#2375;&#2358;&#2344; &#2325;&#2368; &#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359;&#2340;&#2366; &#2325;&#2368; &#2324;&#2352; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2340;&#2367;&#2352;&#2306;&#2327;&#2366; &#2333;&#2306;&#2337;&#2366; &#2347;&#2361;&#2352;&#2366;&#2351;&#2366;&#2404;</span><strong> &#2325;&#2366;&#2306;&#2327;&#2381;&#2352;&#2375;&#2360; &#2309;&#2343;&#2367;&#2357;&#2375;&#2358;&#2344; &#2324;&#2352; &#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359;: </strong><span style=\"font-weight: 400;\">&#2354;&#2326;&#2344;&#2314; &#2309;&#2343;&#2367;&#2357;&#2375;&#2358;&#2344; - &#2309;&#2306;&#2348;&#2367;&#2325;&#2366; &#2330;&#2352;&#2339; &#2350;&#2332;&#2370;&#2350;&#2342;&#2366;&#2352; (1916), &#2340;&#2381;&#2352;&#2367;&#2346;&#2369;&#2352;&#2368; - &#2360;&#2369;&#2349;&#2366;&#2359; &#2330;&#2306;&#2342;&#2381;&#2352; &#2348;&#2379;&#2360; (1939), &#2327;&#2351;&#2366; - &#2330;&#2367;&#2340;&#2352;&#2306;&#2332;&#2344; &#2342;&#2366;&#2360; (1922), &#2325;&#2366;&#2344;&#2346;&#2369;&#2352; - &#2360;&#2352;&#2379;&#2332;&#2367;&#2344;&#2368; &#2344;&#2366;&#2351;&#2337;&#2370; (1925), &#2325;&#2352;&#2366;&#2330;&#2368; - &#2357;&#2354;&#2381;&#2354;&#2349;&#2349;&#2366;&#2312; &#2346;&#2335;&#2375;&#2354; (1931), &#2352;&#2366;&#2350;&#2327;&#2338;&#2364; - &#2350;&#2380;&#2354;&#2366;&#2344;&#2366; &#2309;&#2348;&#2369;&#2354; &#2325;&#2354;&#2366;&#2350; &#2310;&#2332;&#2364;&#2366;&#2342; (1940)&#2404;</span></span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">With reference to Manipuri classical dance, which of the following statements is correct?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">a) Lai Haraoba is the earliest form of Manipuri dance.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">b) Ras, Sankirtana and Thang-Ta are the most popular forms of this dance.</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">c) Manipuri dance has its origin in the Jain traditional dance form. </span></p>\\n",
                    question_hi: "<p>16. <span style=\"font-family: Kokila;\">&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2371;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2342;&#2352;&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2352;&#2366;&#2323;&#2348;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2371;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2352;&#2366;&#2339;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2376;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2325;&#2367;&#2352;&#2381;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kokila;\">&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2371;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2379;&#2325;&#2346;&#2381;&#2352;&#2367;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2376;&#2354;&#2368;&#2351;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Kokila;\">&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2371;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2346;&#2340;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2376;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2352;&#2306;&#2346;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2371;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2376;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2404;</span></p>\\n",
                    options_en: ["<p>a, b, c</p>\\n", "<p>Both a and c</p>\\n", 
                                "<p>Both a and b</p>\\n", "<p>Both b and c</p>\\n"],
                    options_hi: ["<p>a, b, c</p>\\n", "<p>a <span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> c </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\\n",
                                "<p>a <span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> b </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\\n", "<p>b <span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> c </span><span style=\"font-family: Kokila;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16</span><span style=\"font-family: Cambria Math;\">.(c) <strong>Both a and b. Manipuri Dance:</strong><span style=\"font-weight: 400;\"> Classical dance forms of India and noted for themes based on Vaishnavism and spectacular execution of &lsquo;Ras Lila&rsquo; (dance dramas based on love between Radha and Krishna). </span><strong>Guru Bipin Singh: </strong><span style=\"font-weight: 400;\">Regarded as the \"Father of the Manipuri Dance&rdquo;. </span><strong>Famous dancers</strong><span style=\"font-weight: 400;\"> - Nirmala Mehta, Charu Mathur, Darshana Jhaveri, Devyani Chalia.</span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16</span><span style=\"font-family: Cambria Math;\">.(c) <strong>a &#2324;&#2352; b &#2342;&#2379;&#2344;&#2379;&#2306;&#2404; &#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368; &#2344;&#2371;&#2340;&#2381;&#2351;: </strong><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351; &#2344;&#2371;&#2340;&#2381;&#2351; &#2352;&#2370;&#2346; &#2324;&#2352; &#2357;&#2376;&#2359;&#2381;&#2339;&#2357;&#2357;&#2366;&#2342; &#2346;&#2352; &#2310;&#2343;&#2366;&#2352;&#2367;&#2340; &#2357;&#2367;&#2359;&#2351;&#2379;&#2306; &#2324;&#2352; \'&#2352;&#2366;&#2360; &#2354;&#2368;&#2354;&#2366;\' (&#2352;&#2366;&#2343;&#2366; &#2324;&#2352; &#2325;&#2371;&#2359;&#2381;&#2339; &#2325;&#2375; &#2348;&#2368;&#2330; &#2346;&#2381;&#2352;&#2375;&#2350; &#2346;&#2352; &#2310;&#2343;&#2366;&#2352;&#2367;&#2340; &#2344;&#2371;&#2340;&#2381;&#2351; &#2344;&#2366;&#2335;&#2325;) &#2325;&#2375; &#2358;&#2366;&#2344;&#2342;&#2366;&#2352; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2327;&#2369;&#2352;&#2369; &#2348;&#2367;&#2346;&#2367;&#2344; &#2360;&#2367;&#2306;&#2361;</strong><span style=\"font-weight: 400;\">: \"&#2350;&#2339;&#2367;&#2346;&#2369;&#2352;&#2368; &#2344;&#2371;&#2340;&#2381;&#2351; &#2325;&#2375; &#2332;&#2344;&#2325;\" &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2332;&#2366;&#2344;&#2375; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343; &#2344;&#2352;&#2381;&#2340;&#2325; - </strong><span style=\"font-weight: 400;\">&#2344;&#2367;&#2352;&#2381;&#2350;&#2354;&#2366; &#2350;&#2375;&#2361;&#2340;&#2366;, &#2330;&#2366;&#2352;&#2369; &#2350;&#2366;&#2341;&#2369;&#2352;, &#2342;&#2352;&#2381;&#2358;&#2344; &#2333;&#2366;&#2357;&#2375;&#2352;&#2368;, &#2342;&#2375;&#2357;&#2351;&#2366;&#2344;&#2368; &#2330;&#2366;&#2354;&#2367;&#2351;&#2366;&#2404;</span></span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">According to David Ricardo, during a high deficit situation taxation and borrowings are _____ means of spending. </span></p>\\n",
                    question_hi: "<p>17. <span style=\"font-family: Kokila;\">&#2337;&#2375;&#2357;&#2367;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2367;&#2325;&#2366;&#2352;&#2381;&#2337;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2313;&#2330;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2328;&#2366;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2366;&#2352;&#2379;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2315;&#2339;</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Kokila;\">&#2327;&#2381;&#2352;&#2361;&#2339;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\">_____ </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;&#2306;&#2404;</span></p>\\n",
                    options_en: ["<p>prudent</p>\\n", "<p>differentiated</p>\\n", 
                                "<p>equivalent</p>\\n", "<p>subsidised</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2357;&#2375;&#2325;&#2346;&#2370;&#2352;&#2381;&#2339;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2349;&#2375;&#2342;&#2367;&#2340;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2360;&#2350;&#2340;&#2369;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2352;&#2367;&#2351;&#2366;&#2351;&#2340;&#2368;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17</span><span style=\"font-family: Cambria Math;\">.(c) <strong>Equivalent.</strong><span style=\"font-weight: 400;\"> Ricardian equivalence is an economic theory that says that financing government spending out of current taxes or future taxes (and current deficits) will have equivalent effects on the overall economy. He developed the comparative advantage theory, Labor theory of value and theory of rents. </span><strong>Books of David Ricardo -</strong><span style=\"font-weight: 400;\"> \"On the Principles of Political Economy and Taxation\", &ldquo;Economic Essays&rdquo;, &ldquo;The High Price of Bullion&rdquo;. </span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17</span><span style=\"font-family: Cambria Math;\">.(c) <strong>&nbsp;&#2360;&#2350;&#2340;&#2369;&#2354;&#2381;&#2351;&#2404; </strong><span style=\"font-weight: 400;\">&#2352;&#2367;&#2325;&#2366;&#2352;&#2381;&#2337;&#2367;&#2351;&#2344; &#2340;&#2369;&#2354;&#2381;&#2351;&#2340;&#2366; &#2319;&#2325; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340; &#2361;&#2376; &#2332;&#2379; &#2325;&#2361;&#2340;&#2366; &#2361;&#2376; &#2325;&#2367; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2325;&#2352;&#2379;&#2306; &#2351;&#2366; &#2349;&#2357;&#2367;&#2359;&#2381;&#2351; &#2325;&#2375; &#2325;&#2352;&#2379;&#2306; (&#2324;&#2352; &#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344; &#2328;&#2366;&#2335;&#2375;) &#2360;&#2375; &#2360;&#2352;&#2325;&#2366;&#2352;&#2368; &#2326;&#2352;&#2381;&#2330; &#2325;&#2366; &#2357;&#2367;&#2340;&#2381;&#2340;&#2346;&#2379;&#2359;&#2339; &#2360;&#2350;&#2327;&#2381;&#2352; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2346;&#2352; &#2360;&#2350;&#2366;&#2344; &#2346;&#2381;&#2352;&#2349;&#2366;&#2357; &#2337;&#2366;&#2354;&#2375;&#2327;&#2366;&#2404; &#2313;&#2344;&#2381;&#2361;&#2379;&#2306;&#2344;&#2375; &#2340;&#2369;&#2354;&#2344;&#2366;&#2340;&#2381;&#2350;&#2325; &#2354;&#2366;&#2349; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340;, &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2366; &#2358;&#2381;&#2352;&#2350; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340; &#2324;&#2352; &#2354;&#2327;&#2366;&#2344; &#2325;&#2366; &#2360;&#2367;&#2342;&#2381;&#2343;&#2366;&#2306;&#2340; &#2357;&#2367;&#2325;&#2360;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366;&#2404;</span><strong> &#2337;&#2375;&#2357;&#2367;&#2337; &#2352;&#2367;&#2325;&#2366;&#2352;&#2381;&#2337;&#2379; &#2325;&#2368; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325;&#2375;&#2306; </strong><span style=\"font-weight: 400;\">- \"&#2321;&#2344; &#2342; &#2346;&#2381;&#2352;&#2367;&#2306;&#2360;&#2367;&#2346;&#2354;&#2381;&#2360; &#2321;&#2398; &#2346;&#2379;&#2354;&#2367;&#2335;&#2367;&#2325;&#2354; &#2311;&#2325;&#2377;&#2344;&#2350;&#2368; &#2319;&#2306;&#2337; &#2335;&#2376;&#2325;&#2381;&#2360;&#2375;&#2358;&#2344;\", \"&#2311;&#2325;&#2377;&#2344;&#2350;&#2368;&nbsp; &#2319;&#2360;&#2375;&#2332;\", \"&#2342; &#2361;&#2366;&#2312; &#2346;&#2381;&#2352;&#2366;&#2311;&#2360; &#2321;&#2398; &#2348;&#2369;&#2354;&#2367;&#2351;&#2344;\"&#2404;</span></span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. <span style=\"font-family: Cambria Math;\">According to the Ministry of Social Justice and Empowerment, Government of India, What was the percentage of population living below the poverty line in Chhattisgarh in the year 2004-05 ?</span></p>\\n",
                    question_hi: "<p>18. <span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2350;&#2366;&#2332;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2381;&#2351;&#2366;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2004-05 </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2331;&#2340;&#2381;&#2340;&#2368;&#2360;&#2327;&#2338;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2352;&#2368;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2368;&#2357;&#2344;&#2351;&#2366;&#2346;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2344;&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>35.9%</p>\\n", "<p>40.9%</p>\\n", 
                                "<p>32%</p>\\n", "<p>49%</p>\\n"],
                    options_hi: ["<p>35.9%</p>\\n", "<p>40.9%</p>\\n",
                                "<p>32%</p>\\n", "<p>49%</p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18</span><span style=\"font-family: Cambria Math;\">.(b) <strong>40.9%. Poverty line </strong><span style=\"font-weight: 400;\">refers to that line which expresses per capita average monthly expenditure that is essentially required by the people to satisfy their minimum needs. As per </span><strong>the Tendulkar committee</strong><span style=\"font-weight: 400;\">,</span><strong> </strong><span style=\"font-weight: 400;\">the poverty line is estimated on a monthly basis as Rs. 816 in rural areas and Rs. 1000 in urban areas. People who are not able to earn even such an amount in a month are considered below the poverty line. </span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18</span><span style=\"font-family: Cambria Math;\">.(b) <strong>40.9%&#2404; &#2327;&#2352;&#2368;&#2348;&#2368; &#2352;&#2375;&#2326;&#2366;</strong><span style=\"font-weight: 400;\"> &#2313;&#2360; &#2352;&#2375;&#2326;&#2366; &#2325;&#2379; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349;&#2367;&#2340; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376; &#2332;&#2379; &#2346;&#2381;&#2352;&#2340;&#2367; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2324;&#2360;&#2340; &#2350;&#2366;&#2360;&#2367;&#2325; &#2357;&#2381;&#2351;&#2351; &#2325;&#2379; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376; &#2332;&#2379; &#2354;&#2379;&#2327;&#2379;&#2306; &#2325;&#2379; &#2313;&#2344;&#2325;&#2368; &#2344;&#2381;&#2351;&#2370;&#2344;&#2340;&#2350; &#2332;&#2352;&#2370;&#2352;&#2340;&#2379;&#2306; &#2325;&#2379; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2309;&#2344;&#2367;&#2357;&#2366;&#2352;&#2381;&#2351; &#2352;&#2370;&#2346; &#2360;&#2375; &#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2361;&#2376;&#2404; </span><strong>&#2340;&#2375;&#2306;&#2342;&#2369;&#2354;&#2325;&#2352; &#2360;&#2350;&#2367;&#2340;&#2367;</strong><span style=\"font-weight: 400;\"> &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;, &#2350;&#2366;&#2360;&#2367;&#2325; &#2310;&#2343;&#2366;&#2352; &#2346;&#2352; &#2327;&#2352;&#2368;&#2348;&#2368; &#2352;&#2375;&#2326;&#2366; &#2327;&#2381;&#2352;&#2366;&#2350;&#2368;&#2339; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; 816 &#2352;&#2369;&#2346;&#2351;&#2375; &#2324;&#2352; &#2358;&#2361;&#2352;&#2368; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; 1000 &#2352;&#2369;&#2346;&#2351;&#2375; &#2310;&#2306;&#2325;&#2368; &#2327;&#2312; &#2361;&#2376;&#2404; &#2332;&#2379; &#2354;&#2379;&#2327; &#2319;&#2325; &#2350;&#2361;&#2368;&#2344;&#2375; &#2350;&#2375;&#2306; &#2311;&#2340;&#2344;&#2368; &#2343;&#2344;&#2352;&#2366;&#2358;&#2367; &#2344;&#2361;&#2368;&#2306; &#2309;&#2352;&#2381;&#2332;&#2367;&#2340; &#2325;&#2352; &#2346;&#2366;&#2340;&#2375; &#2361;&#2376; , &#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2327;&#2352;&#2368;&#2348;&#2368; &#2352;&#2375;&#2326;&#2366; &#2360;&#2375; &#2344;&#2368;&#2330;&#2375; &#2350;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. <span style=\"font-family: Cambria Math;\">At the Sportstar Aces Awards in 2023, which state received the prize for the best state for sports promotion ?</span></p>\\n",
                    question_hi: "<p>19. <span style=\"font-family: Cambria Math;\">2023 </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2346;&#2379;&#2352;&#2381;&#2335;&#2360;&#2381;&#2335;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2360;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2357;&#2366;&#2352;&#2381;&#2337;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> (Sportstar Aces Awards) </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2397;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Punjab</p>\\n", "<p>Odisha</p>\\n", 
                                "<p>Madhya Pradesh</p>\\n", "<p>Haryana</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2346;&#2306;&#2332;&#2366;&#2348;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2323;&#2337;&#2367;&#2358;&#2366;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2361;&#2352;&#2367;&#2351;&#2366;&#2339;&#2366;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\">.(b) <strong>Odisha. Sportstar Aces Awards 2023</strong>:</span><span style=\"font-family: Cambria Math;\"> Conferred in Mumbai. It was the fifth edition. </span><span style=\"font-family: Cambria Math;\">Sportsman of the year (Cricket): Hardik Pandya, Sportswoman of the year (Cricket): Harmanpreet Kaur. Best PSU for the formation of sports: ONGC (Oil and Natural Gas Corporation), Sportstar of the year (male) : Neeraj Chopra, Sportstar of the year (Female) : Mirabai Chanu, Life achievement award : Shyama Thapa, Inspirational icon - Sania Mirza.</span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19</span><span style=\"font-family: Cambria Math;\">.(b) </span><strong><span style=\"font-family: Kokila;\">&#2323;&#2337;&#2367;&#2358;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2346;&#2379;&#2352;&#2381;&#2335;&#2360;&#2381;&#2335;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2360;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2357;&#2366;&#2352;&#2381;&#2337;&#2381;&#2360;</span></strong><span style=\"font-family: Cambria Math;\"><strong> 2023</strong>:</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2369;&#2306;&#2348;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2366;&#2305;&#2330;&#2357;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2346;&#2379;&#2352;&#2381;&#2335;&#2381;&#2360;&#2350;&#2376;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2321;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2312;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\">): </span><span style=\"font-family: Kokila;\">&#2361;&#2366;&#2352;&#2381;&#2342;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2306;&#2337;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2346;&#2379;&#2352;&#2381;&#2335;&#2381;&#2360;&#2357;&#2369;&#2350;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2321;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2312;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2352;&#2367;&#2325;&#2375;&#2335;</span><span style=\"font-family: Cambria Math;\">): </span><span style=\"font-family: Kokila;\">&#2361;&#2352;&#2350;&#2344;&#2346;&#2381;&#2352;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2380;&#2352;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2326;&#2375;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">PSU </span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Cambria Math;\">ONGC </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Kokila;\">&#2340;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2376;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2327;&#2350;</span><span style=\"font-family: Cambria Math;\">), </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2346;&#2379;&#2352;&#2381;&#2335;&#2381;&#2360;&#2360;&#2381;&#2335;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2321;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2312;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2352;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\">): </span><span style=\"font-family: Kokila;\">&#2344;&#2368;&#2352;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2379;&#2346;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2346;&#2379;&#2352;&#2381;&#2335;&#2360;&#2381;&#2335;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2321;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2312;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kokila;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\">): </span><span style=\"font-family: Kokila;\">&#2350;&#2368;&#2352;&#2366;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2330;&#2366;&#2344;&#2370;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2354;&#2366;&#2311;&#2347;&#2335;&#2366;&#2311;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2330;&#2368;&#2357;&#2350;&#2375;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Kokila;\">&#2358;&#2381;&#2351;&#2366;&#2350;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;&#2346;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2311;&#2306;&#2360;&#2381;&#2346;&#2367;&#2352;&#2375;&#2358;&#2344;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2311;&#2325;&#2344;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2344;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2367;&#2352;&#2381;&#2332;&#2366;&#2404;</span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20.<span style=\"font-family: Cambria Math;\"> After which five year plan there was not much emphasis on the public sector?</span></p>\\n",
                    question_hi: "<p>20. <span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2366;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2361;&#2369;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2379;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Seventh plan</p>\\n", "<p>Ninth plan</p>\\n", 
                                "<p>Eighth plan</p>\\n", "<p>Eleventh plan</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2360;&#2366;&#2340;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2344;&#2380;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2310;&#2336;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2327;&#2381;&#2351;&#2366;&#2352;&#2361;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20</span><span style=\"font-family: Cambria Math;\">.(b) <strong>Ninth Plan (1997 - 2002)</strong><span style=\"font-weight: 400;\">: This Plan focussed on &ldquo;Growth With Social Justice &amp; Equality&ldquo;. </span><strong>Eight Five Year Plan (1992 - 97): </strong><span style=\"font-weight: 400;\">Introduction of fiscal &amp; economic reforms including liberalization under the Prime Minister ship of Shri P V Narasimha Rao. </span><strong>Eleventh Plan (2007 - 2012): </strong><span style=\"font-weight: 400;\">Aimed &ldquo;Towards Faster &amp; More Inclusive Growth &ldquo;. </span><strong>Seventh Plan (1985 - 90): </strong><span style=\"font-weight: 400;\">Focus on &ldquo;food,work &amp; productivity&rdquo;.</span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20</span><span style=\"font-family: Cambria Math;\">.(b) <strong>&#2344;&#2380;&#2357;&#2368;&#2306; &#2351;&#2379;&#2332;&#2344;&#2366;</strong><span style=\"font-weight: 400;\"> </span><strong>(1997 - 2002)</strong><span style=\"font-weight: 400;\">: &#2351;&#2361; &#2351;&#2379;&#2332;&#2344;&#2366; \"&#2360;&#2366;&#2350;&#2366;&#2332;&#2367;&#2325; &#2344;&#2381;&#2351;&#2366;&#2351; &#2324;&#2352; &#2360;&#2350;&#2366;&#2344;&#2340;&#2366; &#2325;&#2375; &#2360;&#2366;&#2341; &#2357;&#2367;&#2325;&#2366;&#2360;\" &#2346;&#2352; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2367;&#2340; &#2341;&#2368;&#2404; </span><strong>&#2310;&#2336;&#2357;&#2368;&#2306; &#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351; &#2351;&#2379;&#2332;&#2344;&#2366; (1992-97)</strong><span style=\"font-weight: 400;\">: &#2358;&#2381;&#2352;&#2368; &#2346;&#2368;.&#2357;&#2368;. &#2344;&#2352;&#2360;&#2367;&#2350;&#2381;&#2361;&#2366; &#2352;&#2366;&#2357; &#2325;&#2375; &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340;&#2381;&#2357; &#2325;&#2366;&#2354; &#2350;&#2375;&#2306; &#2313;&#2342;&#2366;&#2352;&#2368;&#2325;&#2352;&#2339; &#2360;&#2361;&#2367;&#2340; &#2357;&#2367;&#2340;&#2381;&#2340;&#2368;&#2351; &#2324;&#2352; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2360;&#2369;&#2343;&#2366;&#2352;&#2379;&#2306; &#2325;&#2368; &#2358;&#2369;&#2352;&#2370;&#2310;&#2340;&#2404; </span><strong>&#2327;&#2381;&#2351;&#2366;&#2352;&#2361;&#2357;&#2368;&#2306; &#2351;&#2379;&#2332;&#2344;&#2366;</strong><span style=\"font-weight: 400;\"> </span><strong>(2007 - 2012)</strong><span style=\"font-weight: 400;\">: &#2354;&#2325;&#2381;&#2359;&#2381;&#2351; \"&#2340;&#2375;&#2332;&#2364; &#2324;&#2352; &#2309;&#2343;&#2367;&#2325; &#2360;&#2350;&#2366;&#2357;&#2375;&#2358;&#2368; &#2357;&#2367;&#2325;&#2366;&#2360; &#2325;&#2368; &#2323;&#2352;\"&#2404; </span><strong>&#2360;&#2366;&#2340;&#2357;&#2368;&#2306; &#2351;&#2379;&#2332;&#2344;&#2366; (1985 - 90)</strong><span style=\"font-weight: 400;\">: \"&#2349;&#2379;&#2332;&#2344;, &#2325;&#2366;&#2352;&#2381;&#2351; &#2324;&#2352; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325;&#2340;&#2366;\" &#2346;&#2352; &#2343;&#2381;&#2351;&#2366;&#2344; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404;</span></span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. <span style=\"font-family: Cambria Math;\">Which of the following is an objective of the Government Budget ? </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A) GDP Growth </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">B) Reallocation of Resources </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C) Balanced Regional Growth </span></p>\\n",
                    question_hi: "<p>21. <span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2352;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2332;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">A) </span><span style=\"font-family: Kokila;\">&#2332;&#2368;&#2337;&#2368;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> (GDP) </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">B) </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2360;&#2366;&#2343;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2369;&#2344;&#2352;&#2381;&#2310;&#2357;&#2306;&#2335;&#2344;</span></p>\\r\\n<p><span style=\"font-family: Cambria Math;\">C) </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2366;&#2360;</span></p>\\n",
                    options_en: ["<p>Only A</p>\\n", "<p>All - A, B and C</p>\\n", 
                                "<p>Only A and C</p>\\n", "<p>Only B</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> A</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> A, B </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> C </span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> C</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> B</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">.(b) <strong>All - A, B and C. Government budget: </strong><span style=\"font-weight: 400;\">Country\'s document or yearly financial statement that shows estimated revenue and expenditure of every item during the course of that year. It is mentioned in Article 112 of the Indian Constitution. </span><strong>Three types of Budget</strong><span style=\"font-weight: 400;\">: Balanced Budget (Revenue = Expenditure), Surplus Budget (Revenue &gt; Expenditure) and Deficit Budget (Revenue &lt; Expenditure). </span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21</span><span style=\"font-family: Cambria Math;\">.(b) <strong>&#2360;&#2349;&#2368; </strong><span style=\"font-weight: 400;\">- </span><strong>A</strong><span style=\"font-weight: 400;\">, </span><strong>B &#2324;&#2352; C</strong><span style=\"font-weight: 400;\">&#2404;</span><strong> &#2360;&#2352;&#2325;&#2366;&#2352;&#2368; &#2348;&#2332;&#2335;:</strong><span style=\"font-weight: 400;\"> &#2342;&#2375;&#2358; &#2325;&#2366; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2364; &#2351;&#2366; &#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325; &#2357;&#2367;&#2340;&#2381;&#2340;&#2368;&#2351; &#2357;&#2367;&#2357;&#2352;&#2339; &#2332;&#2379; &#2313;&#2360; &#2357;&#2352;&#2381;&#2359; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2350;&#2342; &#2325;&#2375; &#2309;&#2344;&#2369;&#2350;&#2366;&#2344;&#2367;&#2340; &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2324;&#2352; &#2357;&#2381;&#2351;&#2351; &#2325;&#2379; &#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 112 &#2350;&#2375;&#2306; &#2311;&#2360;&#2325;&#2366; &#2313;&#2354;&#2381;&#2354;&#2375;&#2326; &#2361;&#2376;&#2404; </span><strong>&#2348;&#2332;&#2335; &#2340;&#2368;&#2344; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;: </strong><span style=\"font-weight: 400;\">&#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340; &#2348;&#2332;&#2335; (&#2352;&#2366;&#2332;&#2360;&#2381;&#2357; = &#2357;&#2381;&#2351;&#2351;), &#2309;&#2343;&#2367;&#2358;&#2375;&#2359; &#2348;&#2332;&#2335; (&#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &gt; &#2357;&#2381;&#2351;&#2351;) &#2324;&#2352; &#2348;&#2332;&#2335; &#2328;&#2366;&#2335;&#2366;&nbsp; (&#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &lt; &#2357;&#2381;&#2351;&#2351;)&#2404;</span></span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. <span style=\"font-family: Cambria Math;\">Which organisation formerly known as the Imperial Council of Agricultural Research and founded on July 16, 1929, is in charge of Agricultural Research and Education in India ? </span></p>\\n",
                    question_hi: "<p>22. <span style=\"font-family: Cambria Math;\">16 </span><span style=\"font-family: Kokila;\">&#2332;&#2369;&#2354;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">, 1929 </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2327;&#2336;&#2344;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2311;&#2306;&#2346;&#2368;&#2352;&#2367;&#2351;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2313;&#2306;&#2360;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2321;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2327;&#2381;&#2352;&#2368;&#2325;&#2354;&#2381;&#2330;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2367;&#2360;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kokila;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2371;&#2359;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2367;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\\n",
                    options_en: ["<p>Food Corporation of India (FCI)</p>\\n", "<p><span style=\"font-weight: 400;\">Agricultural and Processed Food Products Export Development Authority (APEDA)</span></p>\\n", 
                                "<p><span style=\"font-weight: 400;\">National Bank for Agricultural and Rural Development (NABARD)</span></p>\\n", "<p>Indian Council of Agricultural Research (ICAR)</p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2326;&#2366;&#2342;&#2381;&#2351; &#2344;&#2367;&#2327;&#2350; (FCI)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2371;&#2359;&#2367; &#2324;&#2352; &#2346;&#2381;&#2352;&#2360;&#2306;&#2360;&#2381;&#2325;&#2371;&#2340; &#2326;&#2366;&#2342;&#2381;&#2351; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342; &#2344;&#2367;&#2352;&#2381;&#2351;&#2366;&#2340; &#2357;&#2367;&#2325;&#2366;&#2360; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; (APEDA)</span></p>\\n",
                                "<p><span style=\"font-weight: 400;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2325;&#2371;&#2359;&#2367; &#2324;&#2352; &#2327;&#2381;&#2352;&#2366;&#2350;&#2368;&#2339; &#2357;&#2367;&#2325;&#2366;&#2360; &#2348;&#2376;&#2306;&#2325; (NABARD)</span></p>\\n", "<p><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2371;&#2359;&#2367; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2346;&#2352;&#2367;&#2359;&#2342; (ICAR)</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22</span><span style=\"font-family: Cambria Math;\">.(d) <strong>Indian Council of Agricultural Research (ICAR): </strong><span style=\"font-weight: 400;\">Autonomous organization under the Department of Agricultural Research and Education (DARE), Ministry of Agriculture and Farmers Welfare, Government of India. </span><strong>Headquarter</strong><span style=\"font-weight: 400;\">: New Delhi. </span><strong>Agriculture Organization in India: </strong><span style=\"font-weight: 400;\">Central Food Technological Research Institute</span><strong> </strong><span style=\"font-weight: 400;\">(CFTRI) - Mysore (Karnataka), Central Institute for Cotton Research (CICR) - Nagpur (Maharashtra), Central Rice Research Institute (CRRI) - Cuttack (Odisha), etc. </span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22</span><span style=\"font-family: Cambria Math;\">.(d) <strong>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2325;&#2371;&#2359;&#2367; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2346;&#2352;&#2367;&#2359;&#2342; </strong><span style=\"font-weight: 400;\">(</span><strong>ICAR</strong><span style=\"font-weight: 400;\">): &#2349;&#2366;&#2352;&#2340; &#2360;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2325;&#2371;&#2359;&#2367; &#2319;&#2357;&#2306; &#2325;&#2367;&#2360;&#2366;&#2344; &#2325;&#2354;&#2381;&#2351;&#2366;&#2339; &#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351; &#2325;&#2375; &#2325;&#2371;&#2359;&#2367; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2324;&#2352; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366; &#2357;&#2367;&#2349;&#2366;&#2327; (DARE) &#2325;&#2375; &#2340;&#2361;&#2340; &#2360;&#2381;&#2357;&#2366;&#2351;&#2340;&#2381;&#2340; &#2360;&#2306;&#2327;&#2336;&#2344;&#2404; </span><strong>&#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351;</strong><span style=\"font-weight: 400;\">: &#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2325;&#2371;&#2359;&#2367; &#2360;&#2306;&#2327;&#2336;&#2344;: </strong><span style=\"font-weight: 400;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2326;&#2366;&#2342;&#2381;&#2351; &#2346;&#2381;&#2352;&#2380;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;&#2368; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2344; (CFTRI) - &#2350;&#2376;&#2360;&#2370;&#2352; (&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;), &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2325;&#2346;&#2366;&#2360; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2344; (CICR) - &#2344;&#2366;&#2327;&#2346;&#2369;&#2352; (&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;), &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2330;&#2366;&#2357;&#2354; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2344; (CRRI) - &#2325;&#2335;&#2325; (&#2323;&#2337;&#2367;&#2358;&#2366;), &#2310;&#2342;&#2367;&#2404;</span></span></p>\\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. <span style=\"font-family: Cambria Math;\">Which of the following states has the National Academy of Sciences ?</span></p>\\n",
                    question_hi: "<p>23. <span style=\"font-family: Kokila;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2325;&#2366;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\\n",
                    options_en: ["<p>Uttarakhand</p>\\n", "<p>Telangana</p>\\n", 
                                "<p>Uttar Pradesh</p>\\n", "<p>Karnataka</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2326;&#2306;&#2337;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2340;&#2375;&#2354;&#2306;&#2327;&#2366;&#2344;&#2366;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\">.(c) <strong>Uttar Pradesh. The National Academy of Sciences (</strong><span style=\"font-weight: 400;\">Established in 1930) is the oldest science academy in India. </span><strong>Organizations and their Headquarters: </strong><span style=\"font-weight: 400;\">National Council for Educational Research and Training (NCERT) - New Delhi, Inland Waterways Authority of India (IWAI) - Noida, Central Statistical Organization (CSO) - New Delhi, Press Trust of India (PTI) - New Delhi, etc.</span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23</span><span style=\"font-family: Cambria Math;\">.(c) <strong>&#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;&#2404; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344; &#2309;&#2325;&#2366;&#2342;&#2350;&#2368; (</strong><span style=\"font-weight: 400;\">1930 &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;) &#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2348;&#2360;&#2375; &#2346;&#2369;&#2352;&#2366;&#2344;&#2368; &#2357;&#2367;&#2332;&#2381;&#2334;&#2366;&#2344; &#2309;&#2325;&#2366;&#2342;&#2350;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2360;&#2306;&#2327;&#2336;&#2344; &#2324;&#2352; &#2313;&#2344;&#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351;: </strong><span style=\"font-weight: 400;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2358;&#2376;&#2325;&#2381;&#2359;&#2367;&#2325; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; &#2319;&#2357;&#2306; &#2346;&#2381;&#2352;&#2358;&#2367;&#2325;&#2381;&#2359;&#2339; &#2346;&#2352;&#2367;&#2359;&#2342; (NCERT) - &#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;, &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2309;&#2306;&#2340;&#2352;&#2381;&#2342;&#2375;&#2358;&#2368;&#2351; &#2332;&#2354;&#2350;&#2366;&#2352;&#2381;&#2327; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; (IWAI) - &#2344;&#2379;&#2319;&#2337;&#2366;, &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351; &#2360;&#2366;&#2306;&#2326;&#2381;&#2351;&#2367;&#2325;&#2368; &#2360;&#2306;&#2327;&#2336;&#2344; (CSO) - &#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;, &#2346;&#2381;&#2352;&#2375;&#2360; &#2335;&#2381;&#2352;&#2360;&#2381;&#2335; &#2321;&#2347; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366; (PTI) - &#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; , &#2310;&#2342;&#2367;&#2404;</span></span></p>\\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. <span style=\"font-family: Cambria Math;\">Name an oval-shaped range of hills sculpted by glacial flow.</span></p>\\n",
                    question_hi: "<p>24. <span style=\"font-family: Kokila;\">&#2361;&#2367;&#2350;&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2357;&#2366;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2313;&#2340;&#2381;&#2325;&#2368;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2309;&#2306;&#2337;&#2366;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2310;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2348;&#2340;&#2366;&#2311;&#2319;&#2404;</span></p>\\n",
                    options_en: ["<p>Sandur</p>\\n", "<p>Drumlins</p>\\n", 
                                "<p>Moraines</p>\\n", "<p>Esker</p>\\n"],
                    options_hi: ["<p><span style=\"font-family: Kokila;\">&#2360;&#2306;&#2342;&#2369;&#2352;</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2337;&#2381;&#2352;&#2350;&#2354;&#2367;&#2344;&#2381;&#2360;</span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2350;&#2379;&#2352;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2319;&#2360;&#2381;&#2325;&#2352;</span></p>\\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.(b) <strong>Drumlins - </strong><span style=\"font-weight: 400;\">It is formed by the filling of heavy rocky debris in the glacial crevices and its stay under the ice. One end of the drumlins facing the glacier called </span><strong>the stoss end</strong><span style=\"font-weight: 400;\"> and the other end called </span><strong>tail</strong><span style=\"font-weight: 400;\">. </span><strong>Eskers: </strong><span style=\"font-weight: 400;\">When glaciers melt in summer, the water flows on the surface of the ice. These waters accumulate beneath the glacier and flow like streams in a channel beneath the ice. </span><strong>Moraine: </strong><span style=\"font-weight: 400;\">Materials (Soil and Rock) left behind by a moving glacier.</span></span></p>\\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24</span><span style=\"font-family: Cambria Math;\">.(b) <strong>&#2337;&#2381;&#2352;&#2350;&#2354;&#2367;&#2344;&#2381;&#2360; </strong><span style=\"font-weight: 400;\">- &#2311;&#2360;&#2325;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2361;&#2367;&#2350;&#2344;&#2342; &#2342;&#2352;&#2366;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2368; &#2330;&#2335;&#2381;&#2335;&#2366;&#2344;&#2368; &#2350;&#2354;&#2348;&#2375; &#2325;&#2375; &#2349;&#2352;&#2344;&#2375; &#2357; &#2313;&#2360;&#2325;&#2375; &#2348;&#2352;&#2381;&#2347; &#2325;&#2375; &#2344;&#2368;&#2330;&#2375; &#2352;&#2361;&#2344;&#2375; &#2360;&#2375; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2361;&#2367;&#2350;&#2344;&#2342; &#2325;&#2375; &#2360;&#2366;&#2350;&#2344;&#2375; &#2337;&#2381;&#2352;&#2350;&#2354;&#2367;&#2344;&#2381;&#2360; &#2325;&#2375; &#2319;&#2325; &#2360;&#2367;&#2352;&#2375; &#2325;&#2379; </span><strong>&#2360;&#2381;&#2335;&#2377;&#2360; &#2319;&#2306;&#2337;</strong><span style=\"font-weight: 400;\"> &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2342;&#2370;&#2360;&#2352;&#2375; &#2360;&#2367;&#2352;&#2375; &#2325;&#2379; &#2335;&#2375;&#2354; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2319;&#2360;&#2381;&#2325;&#2352; </strong><span style=\"font-weight: 400;\">: &#2332;&#2348; &#2327;&#2352;&#2381;&#2350;&#2367;&#2351;&#2379;&#2306; &#2350;&#2375;&#2306; &#2327;&#2381;&#2354;&#2375;&#2358;&#2367;&#2351;&#2352; &#2346;&#2367;&#2328;&#2354;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2340;&#2379; &#2346;&#2366;&#2344;&#2368; &#2348;&#2352;&#2381;&#2347; &#2325;&#2368; &#2360;&#2340;&#2361; &#2346;&#2352; &#2348;&#2361;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2375; &#2346;&#2366;&#2344;&#2368; &#2327;&#2381;&#2354;&#2375;&#2358;&#2367;&#2351;&#2352; &#2325;&#2375; &#2344;&#2368;&#2330;&#2375; &#2332;&#2350;&#2366; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2348;&#2352;&#2381;&#2347; &#2325;&#2375; &#2344;&#2368;&#2330;&#2375; &#2319;&#2325; &#2330;&#2376;&#2344;&#2354; &#2350;&#2375;&#2306; &#2343;&#2366;&#2352;&#2366;&#2323;&#2306; &#2325;&#2368; &#2340;&#2352;&#2361; &#2348;&#2361;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2350;&#2379;&#2352;&#2375;&#2344;</strong><span style=\"font-weight: 400;\">: &#2319;&#2325; &#2330;&#2354;&#2340;&#2375; &#2361;&#2369;&#2319; &#2361;&#2367;&#2350;&#2344;&#2342; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2331;&#2379;&#2396;&#2375; &#2327;&#2319; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; (&#2350;&#2367;&#2335;&#2381;&#2335;&#2368; &#2324;&#2352; &#2330;&#2335;&#2381;&#2335;&#2366;&#2344;) &#2361;&#2376;&#2404;</span></span></p>\\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <span style=\"font-family: Cambria Math;\">Identify the different option from the following.</span></p>\\n",
                    question_hi: "<p>25. <span style=\"font-family: Kokila;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2361;&#2330;&#2366;&#2344;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\\n",
                    options_en: ["<p>Intrinsic value</p>\\n", "<p>Guarantee of issuing authority</p>\\n", 
                                "<p>Fiat money</p>\\n", "<p>Legal tender</p>\\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2310;&#2306;&#2340;&#2352;&#2367;&#2325; &#2350;&#2370;&#2354;&#2381;&#2351; (Intrinsic value)</span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2332;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2327;&#2366;&#2352;&#2306;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\"> (Guarantee of issuing authority) </span></p>\\n",
                                "<p><span style=\"font-family: Kokila;\">&#2325;&#2366;&#2327;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2369;&#2342;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> (Fiat money) </span></p>\\n", "<p><span style=\"font-family: Kokila;\">&#2357;&#2376;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kokila;\">&#2350;&#2369;&#2342;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> (Legal tender) </span></p>\\n"],
                    solution_en: "<p>25.(a) <span style=\"font-family: Cambria Math;\"><strong>Intrinsic value </strong><span style=\"font-weight: 400;\">- The true value of the product regardless of market value. </span><strong>Guarantee of Issuing Institution: </strong><span style=\"font-weight: 400;\">Provides the respective partner banks / banking networks with a global guarantee amount</span><strong>. Fiat money - </strong><span style=\"font-weight: 400;\">Money that has been issued by the Government Authority, and has no intrinsic value. </span><strong>Legal tender </strong><span style=\"font-weight: 400;\">- Legal tender means banknotes and coins which are offered in payment of debts and which must be accepted.</span></span></p>\\n",
                    solution_hi: "<p>25.(a) <strong>&#2310;&#2306;&#2340;&#2352;&#2367;&#2325; &#2350;&#2370;&#2354;&#2381;&#2351; </strong><span style=\"font-weight: 400;\">- &#2348;&#2366;&#2332;&#2364;&#2366;&#2352; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; &#2357;&#2367;&#2330;&#2366;&#2352; &#2325;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342; &#2325;&#2366; &#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325; &#2350;&#2370;&#2354;&#2381;&#2351;&#2404;</span><strong> &#2332;&#2366;&#2352;&#2368;&#2325;&#2352;&#2381;&#2340;&#2366; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2344; &#2325;&#2368; &#2327;&#2366;&#2352;&#2306;&#2335;&#2368;: </strong><span style=\"font-weight: 400;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2349;&#2366;&#2327;&#2368;&#2342;&#2366;&#2352; &#2348;&#2376;&#2306;&#2325;&#2379;&#2306;/&#2348;&#2376;&#2306;&#2325;&#2367;&#2306;&#2327; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2379; &#2357;&#2376;&#2358;&#2381;&#2357;&#2367;&#2325; &#2327;&#2366;&#2352;&#2306;&#2335;&#2368; &#2352;&#2366;&#2358;&#2367; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong>&#2347;&#2367;&#2319;&#2335; &#2350;&#2344;&#2368; -</strong><span style=\"font-weight: 400;\"> &#2357;&#2361; &#2343;&#2344; &#2332;&#2379; &#2360;&#2352;&#2325;&#2366;&#2352;&#2368; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2332;&#2366;&#2352;&#2368; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;, &#2324;&#2352; &#2332;&#2367;&#2360;&#2325;&#2366; &#2325;&#2379;&#2312; &#2310;&#2306;&#2340;&#2352;&#2367;&#2325; &#2350;&#2370;&#2354;&#2381;&#2351; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;</span><strong> &#2325;&#2366;&#2344;&#2370;&#2344;&#2368; &#2344;&#2367;&#2357;&#2367;&#2342;&#2366; </strong><span style=\"font-weight: 400;\">- &#2325;&#2366;&#2344;&#2370;&#2344;&#2368; &#2344;&#2367;&#2357;&#2367;&#2342;&#2366; &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; &#2348;&#2376;&#2306;&#2325;&#2344;&#2379;&#2335; &#2324;&#2352; &#2360;&#2367;&#2325;&#2381;&#2325;&#2375; &#2332;&#2379; &#2315;&#2339; &#2325;&#2375; &#2349;&#2369;&#2327;&#2340;&#2366;&#2344; &#2350;&#2375;&#2306; &#2346;&#2375;&#2358; &#2325;&#2367;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2332;&#2367;&#2344;&#2381;&#2361;&#2375;&#2306; &#2360;&#2381;&#2357;&#2368;&#2325;&#2366;&#2352; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2344;&#2366; &#2330;&#2366;&#2361;&#2367;&#2319;&#2404;</span></p>\\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>