<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 21</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">21</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 20,
                end: 20
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. A room where medicines are prepared and provided.</p>\r\n<p>SSC CGL Tier II, 12/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>infirmary</p>", "<p>dispensary</p>", 
                                "<p>surgery</p>", "<p>hospital</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>1. b.<strong>dispensary </strong>- a room where medicines are prepared and provided.</p>\r\n<p><strong>infirmary</strong>- -a hospital.</p>\r\n<p><strong>surgery</strong>- the treatment of injuries or disorders of the body</p>",
                    solution_hi: "<p>1. b.<strong>dispensary </strong>- a room where medicines are prepared and provided.</p>\r\n<p><strong>infirmary</strong>- -a hospital.</p>\r\n<p><strong>surgery</strong>- the treatment of injuries or disorders of the body</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. The science dealing with X-rays and other high-energy radiation, especially for the diagnosis and treatment of disease.</p>\r\n<p>SSC CGL Tier II, 12/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>nephrology</p>", "<p>neurology</p>", 
                                "<p>pathology</p>", "<p>radiology</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>2. d. <strong>radiology </strong>- the science dealing with X-rays and other high-energy radiation</p>\r\n<p><strong>nephrology</strong>- the branch of medicine that deals with the physiology and diseases of the kidneys.</p>\r\n<p><strong>neurology</strong>- (related to nerves and the nervous system.)</p>\r\n<p><strong>pathology</strong>- (the science of the causes and effects of diseases)</p>",
                    solution_hi: "<p>2. d. <strong>radiology </strong>- the science dealing with X-rays and other high-energy radiation</p>\r\n<p><strong>nephrology</strong>- the branch of medicine that deals with the physiology and diseases of the kidneys.</p>\r\n<p><strong>neurology</strong>- (related to nerves and the nervous system.)</p>\r\n<p><strong>pathology</strong>- (the science of the causes and effects of diseases)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3.Fear of heights.</p>\r\n<p>SSC CGL Tier II, 12/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>autophobia</p>", "<p>zoophobia</p>", 
                                "<p>xenophobia</p>", "<p>Acrophobia</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>3. d. <strong>AcrophobiaExtreme </strong>or irrational fear of heights</p>\r\n<p><strong>autophobia</strong>- is the specific phobia of isolation</p>\r\n<p><strong>zoophobia</strong>-animal phobia is a class of specific phobias to particular animals</p>\r\n<p><strong>xenophobia </strong>-(dislike of or prejudice against people from other countries.)</p>",
                    solution_hi: "<p>3. d. <strong>AcrophobiaExtreme </strong>or irrational fear of heights</p>\r\n<p><strong>autophobia</strong>- is the specific phobia of isolation</p>\r\n<p><strong>zoophobia</strong>-animal phobia is a class of specific phobias to particular animals</p>\r\n<p><strong>xenophobia </strong>-(dislike of or prejudice against people from other countries.)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. The quality of being honest and having strong moral principles.</p>\r\n<p>SSC CGL Tier II, 12/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>insatiability</p>", "<p>integrity</p>", 
                                "<p>insidiousness</p>", "<p>intellect</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>4. b.<strong>integrity</strong>- quality of being honest and having strong moral principles</p>\r\n<p><strong>insatiability </strong>-Impossible to satiate or satisfy</p>\r\n<p><strong>insidiousness</strong>- Intended to entrap/harmful; alluring:</p>\r\n<p><strong>intellect </strong>-a person\'s mental powers.</p>",
                    solution_hi: "<p>4. b.<strong>integrity</strong>- quality of being honest and having strong moral principles</p>\r\n<p><strong>insatiability </strong>-Impossible to satiate or satisfy</p>\r\n<p><strong>insidiousness</strong>- Intended to entrap/harmful; alluring:</p>\r\n<p><strong>intellect </strong>-a person\'s mental powers.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Causing no harm.</p>\r\n<p>SSC CGL Tier II, 12/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>insolvent</p>", "<p>inorganic</p>", 
                                "<p>integral</p>", "<p>innocuous</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>5.d. <strong>innocuous</strong> -causing no harm</p>\r\n<p><strong>insolvent </strong>- unable to pay debts owed.</p>\r\n<p><strong>inorganic</strong>-not consisting of or deriving from living matter.</p>\r\n<p><strong>integral</strong>- important</p>",
                    solution_hi: "<p>5.d. <strong>innocuous</strong> -causing no harm</p>\r\n<p><strong>insolvent </strong>- unable to pay debts owed.</p>\r\n<p><strong>inorganic</strong>-not consisting of or deriving from living matter.</p>\r\n<p><strong>integral</strong>- important</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6.The customary code of polite behaviour in society.</p>\r\n<p>SSC CGL Tier II, 12/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>norm</p>", "<p>majesty</p>", 
                                "<p>estimation</p>", "<p>etiquette</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>6. d. <strong>etiquette </strong>-the customary code of polite behaviour in society</p>\r\n<p><strong>norm </strong>- something that is usual, typical, or standard.</p>\r\n<p><strong>majesty </strong>- royal power/ impressive beauty, scale, or stateliness.</p>\r\n<p><strong>estimation </strong>-a rough calculation of the value, number, quantity, or extent of something.</p>",
                    solution_hi: "<p>6. d. <strong>etiquette </strong>-the customary code of polite behaviour in society</p>\r\n<p><strong>norm </strong>- something that is usual, typical, or standard.</p>\r\n<p><strong>majesty </strong>- royal power/ impressive beauty, scale, or stateliness.</p>\r\n<p><strong>estimation </strong>-a rough calculation of the value, number, quantity, or extent of something.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7.A decision on which one cannot go back.</p>\r\n<p>SSC CGL Tier II, 12/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>intractable</p>", "<p>invincible</p>", 
                                "<p>invulnerable</p>", "<p>irrevocable</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>7. d. <strong>irrevocable </strong>-a decision on which one cannot go back</p>\r\n<p><strong>intractable</strong>- hard to control or deal with</p>\r\n<p><strong>invincible </strong>-too powerful to be defeated or overcome.</p>\r\n<p><strong>invulnerable </strong>-impossible to harm or damage.</p>",
                    solution_hi: "<p>7. d. <strong>irrevocable </strong>-a decision on which one cannot go back</p>\r\n<p><strong>intractable</strong>- hard to control or deal with</p>\r\n<p><strong>invincible </strong>-too powerful to be defeated or overcome.</p>\r\n<p><strong>invulnerable </strong>-impossible to harm or damage.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. The height of an object or point in relation to sea level or ground level.</p>\r\n<p>SSC CGL Tier II, 12/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>rectitude</p>", "<p>longitude</p>", 
                                "<p>latitude</p>", "<p>altitude</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>8. d. <strong>altitude</strong>-The height of an object or point in relation to sea level or ground level.(Correct)</p>\r\n<p>a. <strong>rectitude</strong>-morally correct behaviour or thinking; righteousness.</p>\r\n<p>b. <strong>longitude </strong>-the angular distance of a place east or west of the Greenwich meridian, or west of the standard meridian of a celestial object, usually expressed in degrees and minutes.</p>\r\n<p>c. <strong>latitude</strong>- the angular distance of a place north or south of the earth\'s equator, or of the equator of a celestial object, usually expressed in degrees and minutes.</p>",
                    solution_hi: "<p>8. d. <strong>altitude</strong>-The height of an object or point in relation to sea level or ground level.(Correct)</p>\r\n<p>a. <strong>rectitude</strong>-morally correct behaviour or thinking; righteousness.</p>\r\n<p>b. <strong>longitude </strong>-the angular distance of a place east or west of the Greenwich meridian, or west of the standard meridian of a celestial object, usually expressed in degrees and minutes.</p>\r\n<p>c. <strong>latitude</strong>- the angular distance of a place north or south of the earth\'s equator, or of the equator of a celestial object, usually expressed in degrees and minutes.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. The feeling of being upset or annoyed as a result of being unable to change or achieve something.</p>\r\n<p>SSC CGL Tier II, 12/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>astonishment</p>", "<p>fascination</p>", 
                                "<p>frustration</p>", "<p>anticipation</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>9.c. <strong>frustration</strong>- The feeling of being upset or annoyed as a result of being unable to change or achieve something.(Correct)</p>\r\n<p><strong>astonishment</strong>- great surprise.</p>\r\n<p><strong>fascination</strong>-the power to fascinate someone; the quality of being fascinating.</p>\r\n<p><strong>anticipation</strong>- the action of anticipating something; expectation or prediction.</p>",
                    solution_hi: "<p>9.c. <strong>frustration</strong>- The feeling of being upset or annoyed as a result of being unable to change or achieve something.(Correct)</p>\r\n<p><strong>astonishment</strong>- great surprise.</p>\r\n<p><strong>fascination</strong>-the power to fascinate someone; the quality of being fascinating.</p>\r\n<p><strong>anticipation</strong>- the action of anticipating something; expectation or prediction.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10.exercising a compelling charm</p>\r\n<p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>dismissive</p>", "<p>vapid</p>", 
                                "<p>charismatic</p>", "<p>uninspiring</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>10. c.<strong>charismatic </strong>- exercising a compelling charm</p>\r\n<p><strong>dismissive </strong>-Something is unworthy of consideration.</p>\r\n<p><strong>vapid</strong>- offering nothing that is stimulating or challenging/bland.</p>\r\n<p><strong>uninspiring</strong>- not inspiring</p>",
                    solution_hi: "<p>10. c.<strong>charismatic </strong>- exercising a compelling charm</p>\r\n<p><strong>dismissive </strong>-Something is unworthy of consideration.</p>\r\n<p><strong>vapid</strong>- offering nothing that is stimulating or challenging/bland.</p>\r\n<p><strong>uninspiring</strong>- not inspiring</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: " <p>11. Diverse in character or event. </p> <p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: [" <p> homogenous</p>", " <p> assiduous</p>", 
                                " <p> heterogeneous </p>", " <p> horrendous</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>11. c.heterogeneous -diverse in character or event</p> <p>homogenous-of the same kind; alike.</p> <p>assiduous -showing great care and perseverance.</p> <p>horrendous -extremely unpleasant, horrifying, or terrible.</p>",
                    solution_hi: " <p>11. c.heterogeneous -diverse in character or event</p> <p>homogenous-of the same kind; alike.</p> <p>assiduous -showing great care and perseverance.</p> <p>horrendous -extremely unpleasant, horrifying, or terrible.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12.The act of compelling or forcing authority on others.</p>\r\n<p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>aspersion</p>", "<p>volition</p>", 
                                "<p>coercion</p>", "<p>Scion</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>12. c.<strong>coercion </strong>-The act of compelling or forcing authority on others.</p>\r\n<p><strong>aspersion</strong>- an attack on the reputation or integrity of someone or something.</p>\r\n<p><strong>volition </strong>-the power of using one\'s will.</p>\r\n<p><strong>Scion</strong>-a descendant of a notable family.a young shoot or twig of a plant</p>",
                    solution_hi: "<p>12. c.<strong>coercion </strong>-The act of compelling or forcing authority on others.</p>\r\n<p><strong>aspersion</strong>- an attack on the reputation or integrity of someone or something.</p>\r\n<p><strong>volition </strong>-the power of using one\'s will.</p>\r\n<p><strong>Scion</strong>-a descendant of a notable family.a young shoot or twig of a plant</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Found everywhere.</p>\r\n<p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>scarce</p>", "<p>rare</p>", 
                                "<p>unusual</p>", "<p>Omnipresent</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>13. d. <strong>Omnipresent</strong>-found everywhere-God</p>\r\n<p><strong>scarce </strong>-insufficient for the demand.</p>\r\n<p><strong>rare </strong>not occurring very often.</p>\r\n<p><strong>unusual </strong>- not habitually or commonly occurring or done.</p>",
                    solution_hi: "<p>13. d. <strong>Omnipresent</strong>-found everywhere-God</p>\r\n<p><strong>scarce </strong>-insufficient for the demand.</p>\r\n<p><strong>rare </strong>not occurring very often.</p>\r\n<p><strong>unusual </strong>- not habitually or commonly occurring or done.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p>14.The belief that events are predetermined and therefore cannot be changed. </p> <p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: [" <p> fatalism</p>", " <p> chasm</p>", 
                                " <p> autism</p>", " <p> prism</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>14. a. fatalism - the belief that all events are predetermined and therefore inevitable.</p> <p>chasm - a profound difference between people, viewpoints, feelings, etc.</p> <p>autism - a developm -ental mental disorder </p> <p>prisma glass or other transparent object to experiment the division of white light into a spectrum of colours.</p>",
                    solution_hi: " <p>14. a. fatalism - the belief that all events are predetermined and therefore inevitable.</p> <p>chasm - a profound difference between people, viewpoints, feelings, etc.</p> <p>autism - a developm -ental mental disorder </p> <p>prisma glass or other transparent object to experiment the division of white light into a spectrum of colours.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. The exact opposite.</p>\r\n<p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: ["<p>prosthesis</p>", "<p>aesthete</p>", 
                                "<p>analogous</p>", "<p>antithesis</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: "<p>15. d. <strong>antithesis</strong> a person or thing that is the direct opposite of someone or something else.</p>\r\n<p><strong>prosthesis </strong>-an artificial body part, such as a limb, a heart implant.</p>\r\n<p><strong>aesthete</strong>- a person who is appreciative of and sensitive to art and beauty.</p>\r\n<p><strong>analogous</strong>- comparable in certain respects</p>",
                    solution_hi: "<p>15. d. <strong>antithesis</strong> a person or thing that is the direct opposite of someone or something else.</p>\r\n<p><strong>prosthesis </strong>-an artificial body part, such as a limb, a heart implant.</p>\r\n<p><strong>aesthete</strong>- a person who is appreciative of and sensitive to art and beauty.</p>\r\n<p><strong>analogous</strong>- comparable in certain respects</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: " <p>16.Touching along the side or boundary. </p> <p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: [" <p>  inconspicuous</p>", " <p> unpretentious</p>", 
                                " <p> harmonious</p>", " <p> contiguous</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>16. d. contiguous -Touching along the side or boundary</p> <p>inconspicuous- not clearly visible or attracting attention. </p> <p>unpretentious - pleasantly simple and functional; modest.</p> <p>harmonious -  tuneful /free from disagreement or dissent.</p>",
                    solution_hi: " <p>16. d. contiguous -Touching along the side or boundary</p> <p>inconspicuous- not clearly visible or attracting attention. </p> <p>unpretentious - pleasantly simple and functional; modest.</p> <p>harmonious -  tuneful /free from disagreement or dissent.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: " <p>17.One who studies human societies and their culture. </p> <p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: [" <p> pathologist</p>", " <p> astrobiologist</p>", 
                                " <p> pharmacologist</p>", " <p> anthropologist</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>17. d. anthropologist -One who studies human societies and their culture</p> <p>pathologist - a scientist who studies the causes and effects of diseases</p> <p>astrobiologist-An astrobiologist is a person who studies the possibility of life beyond Earth.</p> <p>pharmacologist- who create, develop, and test new medications.</p>",
                    solution_hi: " <p>17. d. anthropologist -One who studies human societies and their culture</p> <p>pathologist - a scientist who studies the causes and effects of diseases</p> <p>astrobiologist-An astrobiologist is a person who studies the possibility of life beyond Earth.</p> <p>pharmacologist- who create, develop, and test new medications.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: " <p>18.Consisting of many things or parts.</p> <p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: [" <p> few</p>", " <p> singular</p>", 
                                " <p> numeral</p>", " <p> multitudinous</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>18. d. multitudinous-Consisting of many things or parts</p> <p>numeral - a figure, symbol, or group of figures denoting a number.</p>",
                    solution_hi: " <p>18. d. multitudinous-Consisting of many things or parts</p> <p>numeral - a figure, symbol, or group of figures denoting a number.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: " <p>19. Making a show of being morally superior to others</p> <p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: [" <p> approving</p>", " <p> sanctum</p>", 
                                " <p> profligate</p>", " <p> sanctimonious</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>19. d. sanctimonious-Making a show of being morally superior to others</p> <p>approving - showing or feeling approval of someone or something.</p> <p>sanctum - a sacred place, especially a shrine within a temple or church.</p> <p>profligate - recklessly extravagant or wasteful in the use of resources.</p>",
                    solution_hi: " <p>19. d. sanctimonious-Making a show of being morally superior to others</p> <p>approving - showing or feeling approval of someone or something.</p> <p>sanctum - a sacred place, especially a shrine within a temple or church.</p> <p>profligate - recklessly extravagant or wasteful in the use of resources.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: " <p>20. a private conversation between two people </p> <p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: [" <p> monologue</p>", " <p> tête-à-tête</p>", 
                                " <p> dialogue</p>", " <p> sermon</p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>20. b.tête-à-tête a private conversation between two people</p> <p>a. monologue -a long, tedious speech by one person during a conversation.</p> <p>c. dialogue -a conversation between two or more people as a feature of a book, play, or film.</p> <p>d. sermon-a talk on a religious or moral subject,</p>",
                    solution_hi: " <p>20. b.tête-à-tête a private conversation between two people</p> <p>a. monologue -a long, tedious speech by one person during a conversation.</p> <p>c. dialogue -a conversation between two or more people as a feature of a book, play, or film.</p> <p>d. sermon-a talk on a religious or moral subject,</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "misc",
                    question_en: " <p>21. proceeding in a gradual way and secretly intending to cause harm</p> <p>SSC CGL Tier II, 13/9/2019</p>",
                    question_hi: "",
                    options_en: [" <p> benevolent</p>", " <p> malevolent</p>", 
                                " <p> solvent</p>", " <p> insidious      </p>"],
                    options_hi: ["", "",
                                "", ""],
                    solution_en: " <p>21. d. insidious - proceeding in a gradual way and secretly intending to cause harm</p> <p>a. benevolent -well meaning and kindly.</p> <p>b.malevolent - having or showing a wish to do evil to others.</p> <p>c.Solvent -able to dissol]ve other substances.</p>",
                    solution_hi: " <p>21. d. insidious - proceeding in a gradual way and secretly intending to cause harm</p> <p>a. benevolent -well meaning and kindly.</p> <p>b.malevolent - having or showing a wish to do evil to others.</p> <p>c.Solvent -able to dissol]ve other substances.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>