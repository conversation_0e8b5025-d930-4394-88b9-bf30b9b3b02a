<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the option that can be used as a one-word substitute for the given group of words.<br>One who eats human flesh.</p>",
                    question_hi: "<p>1. Select the option that can be used as a one-word substitute for the given group of words.<br>One who eats human flesh.</p>",
                    options_en: ["<p>Scavengers</p>", "<p>Herbivores</p>", 
                                "<p>Cannibal</p>", "<p>Omnivores</p>"],
                    options_hi: ["<p>Scavengers</p>", "<p>Herbivores</p>",
                                "<p>Cannibal</p>", "<p>Omnivores</p>"],
                    solution_en: "<p>1.(c) <strong>Cannibal-</strong> one who eats human flesh.<br><strong>Scavengers-</strong> animals or birds that eat dead animals that have been killed by another animal.<br><strong>Herbivores-</strong> an animal that eats plants.<br><strong>Omnivores-</strong> an organism that eats both plant and animal matter.</p>",
                    solution_hi: "<p>1.(c) <strong>Cannibal</strong> (नरभक्षी)- one who eats human flesh.<br><strong>Scavengers</strong> (मुर्दाखोर)- animals or birds that eat dead animals that have been killed by another animal.<br><strong>Herbivores</strong> (शाकाहारी)- an animal that eats plants.<br><strong>Omnivores</strong> (सर्वाहारी)- an organism that eats both plant and animal matter.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate option to substitute the underlined part of the following sentence.<br>My brother always received <span style=\"text-decoration: underline;\">the lion\'s share</span> of every meal that we had.</p>",
                    question_hi: "<p>2. Select the most appropriate option to substitute the underlined part of the following sentence.<br>My brother always received <span style=\"text-decoration: underline;\">the lion\'s share</span> of every meal that we had.</p>",
                    options_en: ["<p>a very small part</p>", "<p>the pet\'s share</p>", 
                                "<p>the last bite</p>", "<p>the major portion</p>"],
                    options_hi: ["<p>a very small part</p>", "<p>the pet\'s share</p>",
                                "<p>the last bite</p>", "<p>the major portion</p>"],
                    solution_en: "<p>2.(d) the major portion<br>&ldquo;The lion&rsquo;s share&rdquo; is an idiom which means the major portion. Hence, option (d) is the most appropriate answer.</p>",
                    solution_hi: "<p>2.(d) the major portion<br>&ldquo;The lion&rsquo;s share&rdquo; एक idiom है, जिसका अर्थ है बड़ा हिस्सा। अतः, option (d) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />He and his friends / does not / want to / attend the meeting.",
                    question_hi: "3. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br />He and his friends / does not / want to / attend the meeting.",
                    options_en: [" does not", " want to", 
                                " attend the meeting", " He and his friends"],
                    options_hi: [" does not", " want to",
                                " attend the meeting", " He and his friends"],
                    solution_en: "3.(a) does not<br />According to the “Subject-Verb Agreement Rule”, a plural subject always takes a plural verb. In the given sentence, ‘He and his friends’ is a plural subject that will take ‘do’ as a plural verb. Hence, ‘do not’ is the most appropriate answer.",
                    solution_hi: "3.(a) does not<br />“Subject-Verb Agreement Rule” के अनुसार, plural subject के साथ हमेशा plural verb का प्रयोग होता है। दिए गए sentence में, ‘He and his friends’,  एक plural subject है, जिसके साथ plural verb ‘do’ का प्रयोग होगा। अतः, \'do not’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate option that can substitute the underlined part in the following sentence.<br>We all have to <span style=\"text-decoration: underline;\">pull our socks up</span> for the upcoming Board exam, if we want to get outstanding grades.</p>",
                    question_hi: "<p>4. Select the most appropriate option that can substitute the underlined part in the following sentence.<br>We all have to <span style=\"text-decoration: underline;\">pull our socks up</span> for the upcoming Board exam, if we want to get outstanding grades.</p>",
                    options_en: ["<p>act in a proper manner</p>", "<p>work harder than before</p>", 
                                "<p>calm down impudent contempt</p>", "<p>revive interest in old matters</p>"],
                    options_hi: ["<p>act in a proper manner</p>", "<p>work harder than before</p>",
                                "<p>calm down impudent contempt</p>", "<p>revive interest in old matters</p>"],
                    solution_en: "<p>4.(b) work harder than before<br>&lsquo;Pull our socks up&rsquo; is an idiom which means to work harder than before. Hence, option (b) is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(b) work harder than before<br>&lsquo;Pull our socks up&rsquo; एक idiom है, जिसका अर्थ है पहले से अधिक मेहनत करना। अतः, option (b) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "5. The following sentence has been divided into four segments. Identify the segment in which the article has been used INCORRECTLY.<br />In the recent past, the IT industry has emerged / as a major contributor to an industry revenue / as well as employment / opportunity provider in the country.",
                    question_hi: "5. The following sentence has been divided into four segments. Identify the segment in which the article has been used INCORRECTLY.<br />In the recent past, the IT industry has emerged / as a major contributor to an industry revenue / as well as employment / opportunity provider in the country.",
                    options_en: [" opportunity provider in the country. ", " In the recent past, the IT industry has emerged  ", 
                                " as a major contributor to an industry revenue", " as well as employment<br /> "],
                    options_hi: [" opportunity provider in the country. ", " In the recent past, the IT industry has emerged  ",
                                " as a major contributor to an industry revenue", " as well as employment<br /> "],
                    solution_en: "5.(c) as a major contributor to an industry revenue<br />‘Industry’ mentioned in the sentence is specific and we generally use  the definite article ‘the’ with a specific or particular noun. Hence, ‘as a major contributor to the industry revenue’ is the most appropriate answer.",
                    solution_hi: "5.(c) as a major contributor to an industry revenue<br />‘Industry’ जो sentence में उल्लेखित है, वह specific है और हम generally किसी specific या particular noun के साथ definite article ‘the’  का प्रयोग करते हैं। अतः, ‘as a major contributor to the industry revenue’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the most appropriate ANTONYM of the given word.<br>Nominal</p>",
                    question_hi: "<p>6. Select the most appropriate ANTONYM of the given word.<br>Nominal</p>",
                    options_en: ["<p>Nasty</p>", "<p>Sound</p>", 
                                "<p>Constant</p>", "<p>Significant</p>"],
                    options_hi: ["<p>Nasty</p>", "<p>Sound</p>",
                                "<p>Constant</p>", "<p>Significant</p>"],
                    solution_en: "<p>6.(d) <strong>Significant-</strong> sufficiently great or important to be worthy of attention.<br><strong>Nominal-</strong> existing in name only.<br><strong>Nasty-</strong> very unpleasant, offensive, or harmful.<br><strong>Sound-</strong> in good condition.<br><strong>Constant-</strong> occurring continuously or repeatedly.</p>",
                    solution_hi: "<p>6.(d) <strong>Significant</strong> (महत्त्वपूर्ण)- sufficiently great or important to be worthy of attention.<br><strong>Nominal</strong> (नाममात्र का)- existing in name only.<br><strong>Nasty</strong> (घृणित)- very unpleasant, offensive, or harmful.<br><strong>Sound</strong> (अच्छाखासा)- in good condition.<br><strong>Constant</strong> (निरंतर)- occurring continuously or repeatedly.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "7. Select the sentence that does NOT have a spelling error.",
                    question_hi: "7. Select the sentence that does NOT have a spelling error.",
                    options_en: [" There were no intermittent showers in the city, as was predicted during the whether forecast. ", " She is very particular about pronounciation in her classes. ", 
                                " The movie was very successful in the urban areas, but not among rural audiences. ", " There wedding was during the summer, which is why we didn\'t attend it."],
                    options_hi: [" There were no intermittent showers in the city, as was predicted during the whether forecast. ", " She is very particular about pronounciation in her classes. ",
                                " The movie was very successful in the urban areas, but not among rural audiences. ", " There wedding was during the summer, which is why we didn\'t attend it."],
                    solution_en: "7.(c) The movie was very successful in the urban areas, but not among rural audiences.<br />‘Weather’, ‘pronunciation’ and ‘their’ are the correct spellings.",
                    solution_hi: "7.(c) The movie was very successful in the urban areas, but not among rural audiences.<br />‘Weather’, ‘pronunciation’ और ‘their’ सही spellings हैं। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the option that expresses the following sentence in passive voice.<br>He had cleaned the bathroom.</p>",
                    question_hi: "<p>8. Select the option that expresses the following sentence in passive voice.<br>He had cleaned the bathroom.</p>",
                    options_en: ["<p>The bathroom has cleaned by him.</p>", "<p>The bathroom had cleaned by him.</p>", 
                                "<p>The bathroom had been cleaned by him.</p>", "<p>The bathroom has been cleaned by him.</p>"],
                    options_hi: ["<p>The bathroom has cleaned by him.</p>", "<p>The bathroom had cleaned by him.</p>",
                                "<p>The bathroom had been cleaned by him.</p>", "<p>The bathroom has been cleaned by him.</p>"],
                    solution_en: "<p>8.(c) The bathroom had been cleaned by him. (Correct)<br>(a) The bathroom has cleaned by him. (Incorrect Sentence Structure) <br>(b) The bathroom <span style=\"text-decoration: underline;\">had</span> cleaned by him. (Incorrect Helping Verb)<br>(d) The bathroom <span style=\"text-decoration: underline;\">has been cleaned</span> by him. (Incorrect Tense)</p>",
                    solution_hi: "<p>8.(c) The bathroom had been cleaned by him. (Correct)<br>(a) The bathroom has cleaned by him. (गलत Sentence Structure) <br>(b) The bathroom <span style=\"text-decoration: underline;\">had</span> cleaned by him. (गलत Helping Verb)<br>(d) The bathroom <span style=\"text-decoration: underline;\">has been cleaned</span> by him. (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Ashoka was the greatest <span style=\"text-decoration: underline;\">than all the Mauryan Emperors</span> .</p>",
                    question_hi: "<p>9. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Ashoka was the greatest <span style=\"text-decoration: underline;\">than all the Mauryan Emperors</span> .</p>",
                    options_en: ["<p>all Mauryan Emperors</p>", "<p>of the Mauryan Emperors</p>", 
                                "<p>to all another Mauryan Emperors</p>", "<p>to other Mauryan Emperors</p>"],
                    options_hi: ["<p>all Mauryan Emperors</p>", "<p>of the Mauryan Emperors</p>",
                                "<p>to all another Mauryan Emperors</p>", "<p>to other Mauryan Emperors</p>"],
                    solution_en: "<p>9.(b) of the Mauryan Emperors<br>&lsquo;Than&rsquo; is used with comparative degree while &lsquo;of&rsquo; is used after the superlative degree. &lsquo;The + superlative adjective + of + plural noun/pronoun&rsquo; is the correct structure. Hence, \'of the Mauryan Emperors\' is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(b) of the Mauryan Emperors<br>&lsquo;Than&rsquo; का प्रयोग comparative degree के साथ किया जाता है जबकि &lsquo;of&rsquo; का प्रयोग superlative degree के बाद किया जाता है। &lsquo;The + superlative adjective + of + plural noun/pronoun&rsquo; सही structure है। अतः, \'of the Mauryan Emperors\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Select the most appropriate ANTONYM of the given word.<br />Concede",
                    question_hi: "10. Select the most appropriate ANTONYM of the given word.<br />Concede",
                    options_en: [" Surrender ", " Battle ", 
                                " Avow ", " Deny"],
                    options_hi: [" Surrender ", " Battle ",
                                " Avow ", " Deny"],
                    solution_en: "<p>10.(d) <strong>Deny-</strong> to refuse to admit the truth or existence of something.<br><strong>Concede-</strong> to acknowledge or admit something, often reluctantly.<br><strong>Surrender-</strong> to give up or yield to another\'s power or control.<br><strong>Battle-</strong> a fight or struggle between opposing forces.<br><strong>Avow-</strong> to declare or affirm openly.</p>",
                    solution_hi: "<p>10.(d) <strong>Deny</strong> (ग़लत ठहराना)- to refuse to admit the truth or existence of something.<br><strong>Concede</strong> (स्वीकार करना)- to acknowledge or admit something, often reluctantly.<br><strong>Surrender</strong> (आत्मसमर्पण करना)- to give up or yield to another\'s power or control.<br><strong>Battle</strong> (युद्ध)- a fight or struggle between opposing forces.<br><strong>Avow</strong> (स्वीकार करना/खुलकर कहना)- to declare or affirm openly.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "11. Identify the INCORRECTLY spelt word in the following sentence.<br />Parul is known as a rigteous and honest lady.",
                    question_hi: "11. Identify the INCORRECTLY spelt word in the following sentence.<br />Parul is known as a rigteous and honest lady.",
                    options_en: [" rigteous ", " known ", 
                                " lady", " honest"],
                    options_hi: [" rigteous ", " known ",
                                " lady", " honest"],
                    solution_en: "11.(a) rigteous<br />\'Righteous\' is the correct spelling. ",
                    solution_hi: "11.(a) rigteous<br />\'Righteous\' सही spelling है।  ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "12. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />P-for a<br />Q- exercise daily<br />R- one must<br />S- good body",
                    question_hi: "12. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />P-for a<br />Q- exercise daily<br />R- one must<br />S- good body",
                    options_en: [" PRQS ", " PRSQ", 
                                " RQPS", " PQRS"],
                    options_hi: [" PRQS ", " PRSQ",
                                " RQPS", " PQRS"],
                    solution_en: "12.(c) RQPS<br />The correct sentence is ‘One must exercise daily for a good body’.",
                    solution_hi: "12.(c) RQPS<br />‘One must exercise daily for a good body’ सही sentence है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate ANTONYM of the given word.<br>Bombastic</p>",
                    question_hi: "<p>13. Select the most appropriate ANTONYM of the given word.<br>Bombastic</p>",
                    options_en: ["<p>Simple</p>", "<p>Wicked</p>", 
                                "<p>Elevated</p>", "<p>Pastoral</p>"],
                    options_hi: ["<p>Simple</p>", "<p>Wicked</p>",
                                "<p>Elevated</p>", "<p>Pastoral</p>"],
                    solution_en: "<p>13.(a) <strong>Simple-</strong> easy to understand or uncomplicated.<br><strong>Bombastic-</strong> high-sounding but with little meaning.<br><strong>Wicked-</strong> morally wrong or bad.<br><strong>Elevated-</strong> raised to a higher position or level.<br><strong>Pastoral-</strong> related to the countryside or rural life.</p>",
                    solution_hi: "<p>13.(a) <strong>Simple</strong> (साधारण)- easy to understand or uncomplicated.<br><strong>Bombastic</strong> (आडंबरपूर्ण)- high-sounding but with little meaning.<br><strong>Wicked</strong> (दुष्ट)- morally wrong or bad.<br><strong>Elevated</strong> (पदोन्नत करना/ऊपर उठाना)- raised to a higher position or level.<br><strong>Pastoral</strong> (ग्रामीण)- related to the countryside or rural life.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate ANTONYM of the given word.<br>Forgive</p>",
                    question_hi: "<p>14. Select the most appropriate ANTONYM of the given word.<br>Forgive</p>",
                    options_en: ["<p>Unburden</p>", "<p>Requite</p>", 
                                "<p>Clear</p>", "<p>Loathe</p>"],
                    options_hi: ["<p>Unburden</p>", "<p>Requite</p>",
                                "<p>Clear</p>", "<p>Loathe</p>"],
                    solution_en: "<p>14.(d) <strong>Loathe-</strong> to feel intense dislike or disgust for something.<br><strong>Forgive-</strong> to stop feeling angry or resentful towards someone for an offense.<br><strong>Unburden-</strong> to relieve oneself of a burden or responsibility.<br><strong>Requite-</strong> to return a favor or respond to a kindness.<br><strong>Clear-</strong> to remove or get rid of something.</p>",
                    solution_hi: "<p>14.(d) <strong>Loathe</strong> (घृणा करना)- to feel intense dislike or disgust for something.<br><strong>Forgive</strong> (क्षमा करना)- to stop feeling angry or resentful towards someone for an offense.<br><strong>Unburden</strong> (भारमुक्त होना)- to relieve oneself of a burden or responsibility.<br><strong>Requite</strong> (प्रतिकार करना/लौटाना)- to return a favor or respond to a kindness.<br><strong>Clear</strong> (साफ करना/छुटकारा पाना)- to remove or get rid of something.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate synonym of the word in brackets to fill in the blank.<br>He said that he lacks the __________ to stand in a queue. (patience)</p>",
                    question_hi: "<p>15. Select the most appropriate synonym of the word in brackets to fill in the blank.<br>He said that he lacks the __________ to stand in a queue. (patience)</p>",
                    options_en: ["<p>relevance</p>", "<p>elegance</p>", 
                                "<p>endurance</p>", "<p>acceptance</p>"],
                    options_hi: ["<p>relevance</p>", "<p>elegance</p>",
                                "<p>endurance</p>", "<p>acceptance</p>"],
                    solution_en: "<p>15.(c) <strong>Endurance-</strong> the ability to withstand hardship or stress over time.<br><strong>Patience-</strong> the ability to wait calmly for something without getting annoyed.<br><strong>Relevance-</strong> the quality of being closely connected or appropriate to the matter at hand.<br><strong>Elegance-</strong> the quality of being graceful and stylish in appearance or manner.<br><strong>Acceptance-</strong> the process of being received or welcomed.</p>",
                    solution_hi: "<p>15.(c) <strong>Endurance</strong> (सहनशीलता)- the ability to withstand hardship or stress over time.<br><strong>Patience</strong> (धैर्य)- the ability to wait calmly for something without getting annoyed.<br><strong>Relevance</strong> (प्रासंगिकता)- the quality of being closely connected or appropriate to the matter at hand.<br><strong>Elegance</strong> (शालीनता)- the quality of being graceful and stylish in appearance or manner.<br><strong>Acceptance</strong> (स्वीकृति)- the process of being received or welcomed.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(a) the Industrial Revolution denotes<br>(b) and continued until the middle of the 19<sup>th</sup> century<br>(c) a significant period of social, economic and technological revolution<br>(d) that commenced in the latter part of the 18<sup>th</sup> century</p>",
                    question_hi: "<p>16. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>(a) the Industrial Revolution denotes<br>(b) and continued until the middle of the 19<sup>th</sup> century<br>(c) a significant period of social, economic and technological revolution<br>(d) that commenced in the latter part of the 18<sup>th</sup> century</p>",
                    options_en: ["<p>dcba</p>", "<p>acdb</p>", 
                                "<p>abcd</p>", "<p>cadb</p>"],
                    options_hi: ["<p>dcba</p>", "<p>acdb</p>",
                                "<p>abcd</p>", "<p>cadb</p>"],
                    solution_en: "<p>16.(b) acdb<br>The given sentence starts with Part (a) as it introduces the main subject of the sentence, i.e. the Industrial Revolution. Part (a) will be followed by Part (c) as it tells us the meaning of the Industrial Revolution. Further, Part (d) states it commenced in the latter part of the 18<sup>th</sup> century &amp; Part (b) states that it continued until the middle of the 19<sup>th</sup> century. So, (b) will follow (d). Going through the options, option &lsquo;b&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>16.(b) acdb<br>दिया गया sentence, Part (a) से प्रारम्भ होगा क्योंकि यह sentence के मुख्य विचार &lsquo;the Industrial Revolution&rsquo; का परिचय देता है। Part (a) के बाद Part (c) आएगा क्योंकि यह हमें Industrial Revolution का अर्थ बताता है। इसके अलावा, Part (d) बताता है कि यह 18<sup>th</sup> century के अंतिम भाग में प्रारम्भ हुआ और Part (b) बताता है कि यह 19<sup>th</sup> century के मध्य तक जारी रहा। इसलिए, (d) के बाद (b) आएगा। अतः options के माध्यम से जाने पर option &lsquo;b&rsquo; में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>The ship launched out on <span style=\"text-decoration: underline;\">a journey by water or sea</span> of discovery.</p>",
                    question_hi: "<p>17. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>The ship launched out on <span style=\"text-decoration: underline;\">a journey by water or sea</span> of discovery.</p>",
                    options_en: ["<p>voyage</p>", "<p>rendezvous</p>", 
                                "<p>verso</p>", "<p>vespers</p>"],
                    options_hi: ["<p>voyage</p>", "<p>rendezvous</p>",
                                "<p>verso</p>", "<p>vespers</p>"],
                    solution_en: "<p>17.(a) <strong>Voyage-</strong> journey by water or sea.<br><strong>Rendezvous-</strong> a meeting at an agreed time and place.<br><strong>Verso-</strong> the reverse of something such as a coin or painting.<br><strong>Vespers-</strong> a Christian evening prayer service that takes place in the late afternoon or evening.</p>",
                    solution_hi: "<p>17.(a) <strong>Voyage</strong> (समुद्री यात्रा)- journey by water or sea.<br><strong>Rendezvous</strong> (मिलन स्थल)- a meeting at an agreed time and place.<br><strong>Verso</strong> (उलटी तरफ़)- the reverse of something such as a coin or painting.<br><strong>Vespers</strong> (संध्याकालीन प्रार्थना)- a Christian evening prayer service that takes place in the late afternoon or evening.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>The pool was too _________ at this end for kids to play. [SHALLOW]</p>",
                    question_hi: "<p>18. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>The pool was too _________ at this end for kids to play. [SHALLOW]</p>",
                    options_en: ["<p>green</p>", "<p>blue</p>", 
                                "<p>deep</p>", "<p>cold</p>"],
                    options_hi: ["<p>green</p>", "<p>blue</p>",
                                "<p>deep</p>", "<p>cold</p>"],
                    solution_en: "<p>18.(c) <strong>Deep-</strong> extending far down from the top or surface.<br><strong>Shallow-</strong> having little depth.<br><strong>Green-</strong> a color often associated with nature and freshness.<br><strong>Blue-</strong> a color resembling that of the clear sky or ocean.<br><strong>Cold-</strong> having a low temperature.</p>",
                    solution_hi: "<p>18.(c) <strong>Deep</strong> (गहरा)- extending far down from the top or surface.<br><strong>Shallow</strong> (उथला)- having little depth.<br><strong>Green</strong> (हरा)- a color often associated with nature and freshness.<br><strong>Blue</strong> (नीला)- a color resembling that of the clear sky or ocean.<br><strong>Cold</strong> (ठंडा)- having a low temperature.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "19. Select the option that can substitute/replace the bracketed word correctly and complete the sentence.<br />I said I (don\'t) understand.",
                    question_hi: "19. Select the option that can substitute/replace the bracketed word correctly and complete the sentence.<br />I said I (don\'t) understand.",
                    options_en: [" doesn\'t", " am doing", 
                                " didn\'t", " done"],
                    options_hi: [" doesn\'t", " am doing",
                                " didn\'t", " done"],
                    solution_en: "19.(c) didn\'t<br />The given sentence is in the past tense, so it must have the verb of past tense. ‘Did’ is a helping verb of simple past tense. Hence, didn’t is the most appropriate answer.",
                    solution_hi: "19.(c) didn\'t<br />दिया गया sentence, past tense में है, इसलिए इसमें past tense की verb होनी चाहिए। ‘Did’ simple past tense की helping verb है। अतः, didn’t सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the option that correctly expresses the given sentence in passive voice.<br>Why didn\'t she bring her son to my house?</p>",
                    question_hi: "<p>20. Select the option that correctly expresses the given sentence in passive voice.<br>Why didn\'t she bring her son to my house?</p>",
                    options_en: ["<p>Why wasn\'t her son brought by her to my house?</p>", "<p>Why isn\'t her son brought to my house?</p>", 
                                "<p>Why wasn\'t her son brought to my house?</p>", "<p>Why isn\'t her son brought by you to my house?</p>"],
                    options_hi: ["<p>Why wasn\'t her son brought by her to my house?</p>", "<p>Why isn\'t her son brought to my house?</p>",
                                "<p>Why wasn\'t her son brought to my house?</p>", "<p>Why isn\'t her son brought by you to my house?</p>"],
                    solution_en: "<p>20.(a) Why wasn\'t her son brought by her to my house? (Correct)<br>(b) Why <span style=\"text-decoration: underline;\">is</span>n\'t her son brought to my house? (Incorrect Tense)<br>(c) Why wasn\'t her son brought to my house? (Doer is missing)<br>(d) Why <span style=\"text-decoration: underline;\">is</span>n\'t her son brought by <span style=\"text-decoration: underline;\">you</span> to my house? (Incorrect Tense and Pronoun)</p>",
                    solution_hi: "<p>20.(a) Why wasn\'t her son brought by her to my house? (Correct)<br>(b) Why <span style=\"text-decoration: underline;\">is</span>n\'t her son brought to my house? (गलत Tense)<br>(c) Why wasn\'t her son brought to my house? (Doer, missing है )<br>(d) Why <span style=\"text-decoration: underline;\">is</span>n\'t her son brought by <span style=\"text-decoration: underline;\">you</span> to my house? (गलत Tense और Pronoun)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong><br>We naturally seek the company of those whose (21) __________ are the same as our own. Fools do not like the (22) _________ of wise men, and wise men avoid fools. Serious-minded people do not find much in common with (23) ___________ folk, and vice versa. Sinners feel uncomfortable with (24) _________ and do not seek their company. We can, therefore, as a rule, judge a man\'s (25) _________ by the company he keeps.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze Test:</strong><br>We naturally seek the company of those whose (21) __________ are the same as our own. Fools do not like the (22) _________ of wise men, and wise men avoid fools. Serious-minded people do not find much in common with (23) ___________ folk, and vice versa. Sinners feel uncomfortable with (24) _________ and do not seek their company. We can, therefore, as a rule, judge a man\'s (25) _________ by the company he keeps.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>organisations</p>", "<p>institutes</p>", 
                                "<p>apathy</p>", "<p>tastes</p>"],
                    options_hi: ["<p>organisations</p>", "<p>institutes</p>",
                                "<p>apathy</p>", "<p>tastes</p>"],
                    solution_en: "<p>21.(d) tastes<br>&lsquo;Taste&rsquo; means the fact of liking or enjoying something. The given passage states that we naturally seek the company of those whose tastes are the same as our own. Hence, \'tastes\' is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(d) tastes<br>&lsquo;Taste&rsquo; का अर्थ है किसी चीज़ को पसंद करना या उसका आनंद लेना। दिए गए passage में कहा गया है कि हम स्वाभाविक रूप से उन लोगों की company चाहते हैं जिनकी पसंद(taste) हमारे जैसा ही हो। अतः, \'tastes\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong><br>We naturally seek the company of those whose (21) __________ are the same as our own. Fools do not like the (22) _________ of wise men, and wise men avoid fools. Serious-minded people do not find much in common with (23) ___________ folk, and vice versa. Sinners feel uncomfortable with (24) _________ and do not seek their company. We can, therefore, as a rule, judge a man\'s (25) _________ by the company he keeps.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong><br>We naturally seek the company of those whose (21) __________ are the same as our own. Fools do not like the (22) _________ of wise men, and wise men avoid fools. Serious-minded people do not find much in common with (23) ___________ folk, and vice versa. Sinners feel uncomfortable with (24) _________ and do not seek their company. We can, therefore, as a rule, judge a man\'s (25) _________ by the company he keeps.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>company</p>", "<p>culture</p>", 
                                "<p>serious</p>", "<p>antagonist</p>"],
                    options_hi: ["<p>company</p>", "<p>culture</p>",
                                "<p>serious</p>", "<p>antagonist</p>"],
                    solution_en: "<p>22.(a) company<br>&lsquo;Company&rsquo; means the fact of being with a person. The given passage states that fools do not like the company of wise men and wise men avoid fools. Hence, \'company\' is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) company<br>&lsquo;Company&rsquo; का अर्थ है किसी व्यक्ति के साथ होने। दिए गए passage में कहा गया है कि मूर्ख लोग बुद्धिमान लोगों की संगति पसंद नहीं करते और बुद्धिमान लोग मूर्खों से दूर रहते हैं। अतः, \'company\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong><br>We naturally seek the company of those whose (21) __________ are the same as our own. Fools do not like the (22) _________ of wise men, and wise men avoid fools. Serious-minded people do not find much in common with (23) ___________ folk, and vice versa. Sinners feel uncomfortable with (24) _________ and do not seek their company. We can, therefore, as a rule, judge a man\'s (25) _________ by the company he keeps.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze Test:</strong><br>We naturally seek the company of those whose (21) __________ are the same as our own. Fools do not like the (22) _________ of wise men, and wise men avoid fools. Serious-minded people do not find much in common with (23) ___________ folk, and vice versa. Sinners feel uncomfortable with (24) _________ and do not seek their company. We can, therefore, as a rule, judge a man\'s (25) _________ by the company he keeps.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>solemn</p>", "<p>thoughtful</p>", 
                                "<p>grave</p>", "<p>frivolous</p>"],
                    options_hi: ["<p>solemn</p>", "<p>thoughtful</p>",
                                "<p>grave</p>", "<p>frivolous</p>"],
                    solution_en: "<p>23.(d) frivolous<br>&lsquo;Frivolous&rsquo; means not having any serious purpose or value. The given passage states that serious-minded people do not find much in common with frivolous folk. Hence, \'frivolous\' is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(d) frivolous<br>&lsquo;Frivolous&rsquo; का अर्थ है कोई गंभीर उद्देश्य या मूल्य न होना। दिए गए passage में कहा गया है कि serious-minded people और तुच्छ(frivolous) लोगों में ज्यादा मिलाप नहीं होता है। अतः, \'frivolous\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong><br>We naturally seek the company of those whose (21) __________ are the same as our own. Fools do not like the (22) _________ of wise men, and wise men avoid fools. Serious-minded people do not find much in common with (23) ___________ folk, and vice versa. Sinners feel uncomfortable with (24) _________ and do not seek their company. We can, therefore, as a rule, judge a man\'s (25) _________ by the company he keeps.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong><br>We naturally seek the company of those whose (21) __________ are the same as our own. Fools do not like the (22) _________ of wise men, and wise men avoid fools. Serious-minded people do not find much in common with (23) ___________ folk, and vice versa. Sinners feel uncomfortable with (24) _________ and do not seek their company. We can, therefore, as a rule, judge a man\'s (25) _________ by the company he keeps.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>knave</p>", "<p>rowdy</p>", 
                                "<p>saints</p>", "<p>commoners</p>"],
                    options_hi: ["<p>knave</p>", "<p>rowdy</p>",
                                "<p>saints</p>", "<p>commoners</p>"],
                    solution_en: "<p>24.(c) saints<br>&lsquo;Saint&rsquo; means a very virtuous, kind, or patient person. The given passage states that sinners feel uncomfortable with saints and do not seek their company. Hence, \'saints\' is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(c) saints<br>&lsquo;Saint&rsquo; का अर्थ है बहुत ही गुणी, दयालु या धैर्यवान व्यक्ति। दिए गए passage में कहा गया है कि पापी लोग (sinners) संतों(saints) के साथ असहज महसूस करते हैं और उनका साथ नहीं चाहते। अतः, \'saints\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong><br>We naturally seek the company of those whose (21) __________ are the same as our own. Fools do not like the (22) _________ of wise men, and wise men avoid fools. Serious-minded people do not find much in common with (23) ___________ folk, and vice versa. Sinners feel uncomfortable with (24) _________ and do not seek their company. We can, therefore, as a rule, judge a man\'s (25) _________ by the company he keeps.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25. <strong>Cloze Test:</strong><br>We naturally seek the company of those whose (21) __________ are the same as our own. Fools do not like the (22) _________ of wise men, and wise men avoid fools. Serious-minded people do not find much in common with (23) ___________ folk, and vice versa. Sinners feel uncomfortable with (24) _________ and do not seek their company. We can, therefore, as a rule, judge a man\'s (25) _________ by the company he keeps.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>futurities</p>", "<p>character</p>", 
                                "<p>heirloom</p>", "<p>legacy</p>"],
                    options_hi: ["<p>futurities</p>", "<p>character</p>",
                                "<p>heirloom</p>", "<p>legacy</p>"],
                    solution_en: "<p>25.(b) character<br>&lsquo;Character&rsquo; means the mental and moral qualities distinctive to an individual. The given passage states that we can, therefore, as a rule, judge a man&rsquo;s character by the company he keeps. Hence, \'character\' is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(b) character<br>&lsquo;Character&rsquo; का अर्थ है किसी व्यक्ति के विशिष्ट मानसिक और नैतिक गुण। दिए गए passage में कहा गया है कि हम, एक rule के रूप में, किसी व्यक्ति के character का आकलन उसकी संगति(company) के आधार पर सकते हैं। अतः, \'character\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>