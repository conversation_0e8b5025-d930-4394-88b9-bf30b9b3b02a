<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the combination of letters that when sequentially placed in the blanks of the given series will logically complete the series.<br>QUA_TO_ANT_UAN_KU_NT</p>",
                    question_hi: "<p>1. अक्षरों के उस संयोजन का चयन कीजिए, जिसे दी गई श्रृंखला के रिक्त स्थानों में क्रमिक रूप से रखे जाने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br>QUA_TO_ANT_UAN_KU_NT</p>",
                    options_en: [
                        "<p>NUMTA</p>",
                        "<p>NUTMA</p>",
                        "<p>NUTAM</p>",
                        "<p>NULTA</p>"
                    ],
                    options_hi: [
                        "<p>NUMTA</p>",
                        "<p>NUTMA</p>",
                        "<p>NUTAM</p>",
                        "<p>NULTA</p>"
                    ],
                    solution_en: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495621121.png\" alt=\"rId4\" width=\"350\" height=\"58\"></p>",
                    solution_hi: "<p>1.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495621121.png\" alt=\"rId4\" width=\"350\" height=\"58\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. 129 is related to 387 following a certain logic. Following the same logic, 164 is related to 492. To which of the following is 148 related, following the same logic ?</p>",
                    question_hi: "<p>2. एक निश्चित तर्क के अनुसार 129 का संबंध 387 से है। उसी तर्क का अनुसरण करते हुए, 164 का संबंध 492 से है। उसी तर्क का अनुसरण करते हुए, 148 का संबंध निम्नलिखित में से किससे है ?</p>",
                    options_en: [
                        "<p>444</p>",
                        "<p>352</p>",
                        "<p>468</p>",
                        "<p>362</p>"
                    ],
                    options_hi: [
                        "<p>444</p>",
                        "<p>352</p>",
                        "<p>468</p>",
                        "<p>362</p>"
                    ],
                    solution_en: "<p>2.(a) <strong>Logic :-</strong> (1st number) &times; 3 = (2nd number)<br>(129, 387) :- (129) &times; 3 = 387<br>(164 , 492) :- (164) &times; 3 = 492<br>Similarly,<br>(148 , ?) :- (148) &times; 3 = 444</p>",
                    solution_hi: "<p>2.(a) <strong>तर्क :-</strong> (पहली संख्या) &times; 3 = (दूसरी संख्या)<br>(129, 387) :- (129) &times; 3 = 387<br>(164 , 492) :- (164) &times; 3 = 492<br>इसी प्रकार,<br>(148 , ?) :- (148) &times; 3 = 444</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "3. Three of the following four letter-clusters are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?<br />(Note : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)<br />1) EFINU<br />2) BCFKR<br />3) JKNSZ<br />4) HIMQX",
                    question_hi: "3. निम्नलिखित चार अक्षर-समूह में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है?<br />(ध्यान दें : असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)<br />1) EFINU<br />2) BCFKR<br />3) JKNSZ<br />4) HIMQX",
                    options_en: [
                        " BCFKR ",
                        " JKNSZ ",
                        " EFINU  ",
                        " HIMQX"
                    ],
                    options_hi: [
                        " BCFKR ",
                        " JKNSZ ",
                        " EFINU  ",
                        " HIMQX"
                    ],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495621235.png\" alt=\"rId5\" width=\"180\" height=\"69\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495621425.png\" alt=\"rId6\" width=\"180\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495621952.png\" alt=\"rId7\" width=\"180\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622166.png\" alt=\"rId8\" width=\"180\"></p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495621235.png\" alt=\"rId5\" width=\"180\" height=\"69\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495621425.png\" alt=\"rId6\" width=\"180\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495621952.png\" alt=\"rId7\" width=\"180\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622166.png\" alt=\"rId8\" width=\"180\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "4. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br />(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g., 13 – Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br />(3, 12, 26) <br />(7, 9, 53) ",
                    question_hi: "4. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं। <br />(ध्यान दें : संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br />(3, 12, 26) <br />(7, 9, 53) ",
                    options_en: [
                        " (5, 15, 45)  ",
                        " (12, 4, 114) ",
                        " (7, 5, 25) ",
                        " (4, 9, 16) "
                    ],
                    options_hi: [
                        " (5, 15, 45)  ",
                        " (12, 4, 114) ",
                        " (7, 5, 25) ",
                        " (4, 9, 16)"
                    ],
                    solution_en: "<p>4.(c) <strong>Logic :-</strong> (1st number &times; 2nd number) - 10 = 3rd number<br>(3, 12, 26) :- (3 &times; 12) - 10 &rArr; (36) - 10 = 26<br>(7, 9, 53) :- (7 &times; 9) - 10 &rArr; (63) - 10 = 53<br>Similarly,<br>(7, 5, 25) :- (7 &times; 5) - 10 &rArr; (35) - 10 = 25</p>",
                    solution_hi: "<p>4.(c) <strong>तर्क :-</strong> (पहली संख्या &times; दूसरी संख्या) - 10 = तीसरी संख्या<br>(3, 12, 26) :- (3 &times; 12) - 10 &rArr; (36) - 10 = 26<br>(7, 9, 53) :- (7 &times; 9) - 10 &rArr; (63) - 10 = 53<br>इसी प्रकार,<br>(7, 5, 25) :- (7 &times; 5) - 10 &rArr; (35) - 10 = 25</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622350.png\" alt=\"rId9\" width=\"420\" height=\"97\"></p>",
                    question_hi: "<p>5. विकल्पों में दी गई उस आकृति की पहचान कीजिए, जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622350.png\" alt=\"rId9\" width=\"420\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622448.png\" alt=\"rId10\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622662.png\" alt=\"rId11\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622762.png\" alt=\"rId12\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622886.png\" alt=\"rId13\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622448.png\" alt=\"rId10\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622662.png\" alt=\"rId11\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622762.png\" alt=\"rId12\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622886.png\" alt=\"rId13\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622762.png\" alt=\"rId12\" width=\"90\"></p>",
                    solution_hi: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495622762.png\" alt=\"rId12\" width=\"90\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Read the given statements and conclusions carefully. You have to take the given statements to be true even if they seem to be at variance from commonly known facts. You have to decide which conclusion/s logically follow/s from the given statements.<br><strong>Statements :</strong><br>All apples are mangoes.<br>All mangoes are lemons.<br>All lemons are kiwis.<br><strong>Conclusions :</strong><br>(I) All apples are lemons.<br>(II) All mangoes are kiwis.</p>",
                    question_hi: "<p>6. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। आपको दिए गए कथनों को सत्य मानना है, भले ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों। आपको यह तय करना है कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>सभी सेब, आम हैं।<br>सभी आम, नींबू हैं।<br>सभी नींबू , कीवी हैं।<br><strong>निष्कर्ष :</strong><br>(I) सभी सेब, नींबू हैं।<br>(II) सभी आम, कीवी हैं।</p>",
                    options_en: [
                        " Neither conclusion I nor II follow.",
                        " Only conclusion I follows.",
                        " Only conclusion II follows.",
                        " Both conclusions I and II follow."
                    ],
                    options_hi: [
                        " न तो निष्कर्ष I अनुसरण करता है और न ही II अनुसरण करता है।",
                        " केवल निष्कर्ष I अनुसरण करता है।",
                        " केवल निष्कर्ष II अनुसरण करता है।",
                        "<p>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>"
                    ],
                    solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495623277.png\" alt=\"rId14\" width=\"250\" height=\"98\"><br>Both conclusion I and II follows.</p>",
                    solution_hi: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495623812.png\" alt=\"rId15\" width=\"250\"><br>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. 30 is related to 216 by certain logic. Following the same logic, 40 is related to 226. To which of the following is 50 related, following the same logic? <br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>7. 30 का संबंध एक निश्चित तर्क द्वारा 216 से है। उसी तर्क का अनुसरण करते हुए, 40 का संबंध 226 से है। उसी तर्क का अनुसरण करते हुए, 50 का संबंध निम्नलिखित में से किससे है ? <br>(ध्यान दें : संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>236</p>",
                        "<p>250</p>",
                        "<p>238</p>",
                        "<p>248</p>"
                    ],
                    options_hi: [
                        "<p>236</p>",
                        "<p>250</p>",
                        "<p>238</p>",
                        "<p>248</p>"
                    ],
                    solution_en: "<p>7.(a) <strong>Logic :- </strong>(2nd number - 1st number) = 186<br>(30 , 216) :- 216 - 30 = 186<br>(40 , 226) :- 226 - 40 = 186<br>Similarly,<br>(50 , ?) :- ? - 50 = 186<br>? = 186 + 50 = 236</p>",
                    solution_hi: "<p>7.(a) <strong>तर्क :- </strong>(दूसरी संख्या - पहली संख्या) = 186<br>(30 , 216) :- 216 - 30 = 186<br>(40 , 226) :- 226 - 40 = 186<br>इसीप्रकार,<br>(50 , ?) :- ? - 50 = 186<br>? = 186 + 50 = 236</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)<br>Pig : Piglets :: Deer : ?</p>",
                    question_hi: "<p>8. उस विकल्प का चयन करें, जो तीसरे शब्द से उसी प्रकार संबंधित है, जिस प्रकार दूसरा शब्द पहले शब्द से संबंधित है। (शब्दों को सार्थक अँग्रेजी/ हिंदी शब्द माना जाना चाहिए और इन्हें शब्द में अक्षरों की संख्या/व्यंजनों की संख्या/स्वरों की संख्या के आधार पर एक-दूसरे से संबद्ध नहीं किया जाना चाहिए।)<br>सुअर : पिगलेट :: हिरण : ?</p>",
                    options_en: [
                        "<p>Pup</p>",
                        "<p>Foal</p>",
                        "<p>Fawn</p>",
                        "<p>Calf</p>"
                    ],
                    options_hi: [
                        "<p>पिल्ला (Pup)</p>",
                        "<p>फॉल ( Foal)</p>",
                        "<p>फॉन (Fawn)</p>",
                        "<p>बछड़ा (Calf)</p>"
                    ],
                    solution_en: "<p>8.(c)&nbsp;As &lsquo;Piglets&rsquo; are &lsquo;baby pigs\' similarly &lsquo;Fawn&rsquo; are the &lsquo;baby deer&rsquo;.</p>",
                    solution_hi: "<p>8.(c)&nbsp;जैसे \'पिगलेट\' \'सूअर के बच्चे\' हैं उसी प्रकार \'फ़ॉन\' \'हिरण के बच्चे\' हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group ?<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is nor allowed.)</p>",
                    question_hi: "<p>9. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है ?<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>82 &ndash; 46 &ndash; 10</p>",
                        "<p>90 &ndash; 99 &ndash; 108</p>",
                        "<p>67 &ndash; 45 &ndash; 23</p>",
                        "<p>90 &ndash; 51 &ndash; 13</p>"
                    ],
                    options_hi: [
                        "<p>82 &ndash; 46 &ndash; 10</p>",
                        "<p>90 &ndash; 99 &ndash; 108</p>",
                        "<p>67 &ndash; 45 &ndash; 23</p>",
                        "<p>90 &ndash; 51 &ndash; 13</p>"
                    ],
                    solution_en: "<p>9.(d) <strong>Logic:-</strong> equal difference between numbers.<br>(82 - 46 - 10) :- (<math display=\"inline\"><mn>82</mn><mo>-</mo><mn>46</mn></math>) = 36 and 46 - 10 = 36<br>(90 - 99 - 108) :- (<math display=\"inline\"><mn>99</mn><mo>-</mo><mn>90</mn></math>) = 9 and 108 - 99 = 9<br>(67 - 45 - 23) :- (<math display=\"inline\"><mn>67</mn><mo>-</mo><mn>45</mn></math>) = 22 and 45 - 23 = 22 <br>But<br>(90 - 51 - 13) :- (<math display=\"inline\"><mn>90</mn><mo>-</mo><mn>51</mn></math>) = 39 and 51 - 13 = 38</p>",
                    solution_hi: "<p>9.(d) <strong>तर्क:- </strong>संख्याओं के बीच समान अंतर।<br>(82 - 46 - 10) :- (<math display=\"inline\"><mn>82</mn><mo>-</mo><mn>46</mn></math>) = 36 और 46 - 10 = 36<br>(90 - 99 - 108) :- (<math display=\"inline\"><mn>99</mn><mo>-</mo><mn>90</mn></math>) = 9 और 108 - 99 = 9<br>(67 - 45 - 23) :- (<math display=\"inline\"><mn>67</mn><mo>-</mo><mn>45</mn></math>) = 22 और 45 - 23 = 22&nbsp;<br>लेकिन <br>(90 - 51 - 13) :- (<math display=\"inline\"><mn>90</mn><mo>-</mo><mn>51</mn></math>) = 39 और 51 - 13 = 38</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10.  In a certain code language, \'sun and sand\' is written as \'zf jc dt\', and \'the hot sun\' is coded as \'km dt wo\'. How will \'sun\' be coded in that language?",
                    question_hi: "10.  एक निश्चित कूट भाषा में \'sun and sand\' को \'zf jc dt\' के रूप में कूटबद्ध किया जाता है और \'the hot sun\' को \'km dt wo\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'sun\' को किस प्रकार कूटबद्ध किया जाएगा?",
                    options_en: [
                        "  km",
                        "  zf",
                        "  dt",
                        " wo"
                    ],
                    options_hi: [
                        "  km",
                        "  zf",
                        "  dt",
                        " wo"
                    ],
                    solution_en: "10.(c) sun and sand → zf  jc  dt……… (i)<br />                 the  hot  sun → km  dt  wo…….. (ii)<br />From (i) and (ii) ‘sun’ and ‘dt’ are common. The code of ‘sun’ = ‘dt’.",
                    solution_hi: "10.(c) sun and sand → zf  jc  dt……… (i)<br />                 the  hot  sun → km  dt  wo…….. (ii)<br />(i) और (ii) से \'sun\' और \'dt\' उभयनिष्ठ हैं। \'sun\' का कूट = \'dt\'.",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. What should come in place of the question mark (?) in the given series based on the English alphabetical order?<br>BRK, XUH, TXE, PAB, ?</p>",
                    question_hi: "<p>11. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए? <br>BRK, XUH, TXE, PAB, ?</p>",
                    options_en: [
                        "<p>LDY</p>",
                        "<p>NCX</p>",
                        "<p>MDX</p>",
                        "<p>MCZ</p>"
                    ],
                    options_hi: [
                        "<p>LDY</p>",
                        "<p>NCX</p>",
                        "<p>MDX</p>",
                        "<p>MCZ</p>"
                    ],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495624167.png\" alt=\"rId16\" width=\"300\" height=\"92\"></p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495624167.png\" alt=\"rId16\" width=\"300\" height=\"92\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. At a wedding reception, a man pointed towards the groom and claims the groom to be the brother of the son of his grandfather\'s son. Who is the groom to the man ?</p>",
                    question_hi: "<p>12. एक शादी के रिसेप्शन में, एक व्यक्ति ने दूल्हे की ओर इशारा किया और दूल्हे को अपने दादा के पुत्र के पुत्र का भाई बताया। दूल्हा उस आदमी का क्या लगता है?</p>",
                    options_en: [
                        "<p>Brother</p>",
                        "<p>Brother-in-law</p>",
                        "<p>Uncle</p>",
                        "<p>Father</p>"
                    ],
                    options_hi: [
                        "<p>भाई</p>",
                        "<p>साला/जीजा</p>",
                        "<p>चाचा</p>",
                        "<p>पिता</p>"
                    ],
                    solution_en: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495624402.png\" alt=\"rId17\" width=\"150\" height=\"139\"><br>Groom is the brother of man.</p>",
                    solution_hi: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495624524.png\" alt=\"rId18\" width=\"150\"><br>दूल्हा उस व्यक्ति का भाई है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Kiara starts from Point A, which is 5 km towards the north of Z. From Point A, she turns towards the east and drives 8 km. She then takes a right turn and drives 9 km. Finally, she takes a right turn, drives for 15 km and stops at Point B. In which direction is Point B with respect to Point Z ?</p>",
                    question_hi: "<p>13. कियारा बिंदु A से ड्राइव करना आरंभ करती है, जो बिंदु Z के उत्तर में 5 km पर है। बिंदु A से, वह पूर्व की ओर मुड़ती है और 8 km ड्राइव करती है। फिर वह दाएं मुड़ती है और 9 km ड्राइव करती है। अंत में, वह दाएं मुड़ती है और 15 km ड्राइव करके बिंदु B पर रुक जाती है। बिंदु Z के संदर्भ में बिंदु B किस दिशा में है?</p>",
                    options_en: [
                        "<p>South-West</p>",
                        "<p>North-East</p>",
                        "<p>North-West</p>",
                        "<p>South</p>"
                    ],
                    options_hi: [
                        "<p>दक्षिण-पश्चिम</p>",
                        "<p>उत्तर-पूर्व</p>",
                        "<p>उत्तर-पश्चिम</p>",
                        "<p>दक्षिण</p>"
                    ],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495624709.png\" alt=\"rId19\" width=\"200\"><br>Point B is in the South-West direction with respect to Z.</p>",
                    solution_hi: "<p>13.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495624912.png\" alt=\"rId20\" width=\"200\"><br>बिंदु B, Z के संबंध में दक्षिण-पश्चिम दिशा में है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code, BANK is written as AZMJ and TALE is written as SZKD. What is the code for LAND ?</p>",
                    question_hi: "14. एक निश्चित कूट भाषा में BANK को AZMJ और TALE को SZKD लिखा जाता है। LAND के लिए कूट क्‍या है?",
                    options_en: [
                        " NDLA ",
                        " MBOE ",
                        " JYLB",
                        " KZMC"
                    ],
                    options_hi: [
                        " NDLA ",
                        " MBOE ",
                        " JYLB",
                        " KZMC"
                    ],
                    solution_en: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625014.png\" alt=\"rId21\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625105.png\" alt=\"rId22\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625203.png\" alt=\"rId23\"></p>",
                    solution_hi: "<p>14.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625014.png\" alt=\"rId21\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625105.png\" alt=\"rId22\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625203.png\" alt=\"rId23\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the figure from among the given option that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625316.png\" alt=\"rId24\" width=\"430\" height=\"100\"></p>",
                    question_hi: "<p>15. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो निम्नलिखित शृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625316.png\" alt=\"rId24\" width=\"430\" height=\"100\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625458.png\" alt=\"rId25\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625593.png\" alt=\"rId26\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625690.png\" alt=\"rId27\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625834.png\" alt=\"rId28\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625458.png\" alt=\"rId25\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625593.png\" alt=\"rId26\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625690.png\" alt=\"rId27\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625834.png\" alt=\"rId28\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625834.png\" alt=\"rId28\" width=\"90\"></p>",
                    solution_hi: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625834.png\" alt=\"rId28\" width=\"90\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. What should come in place of ? in the given series ?<br>131 141 161 191 231 ?</p>",
                    question_hi: "<p>16. दी गई श्रृंखला में प्रश्न चिह्न \'?\' के स्थान पर क्या आना चाहिए ?<br>131 141 161 191 231 ?</p>",
                    options_en: [
                        "<p>271</p>",
                        "<p>261</p>",
                        "<p>281</p>",
                        "<p>291</p>"
                    ],
                    options_hi: [
                        "<p>271</p>",
                        "<p>261</p>",
                        "<p>281</p>",
                        "<p>291</p>"
                    ],
                    solution_en: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625955.png\" alt=\"rId29\" width=\"250\" height=\"47\"></p>",
                    solution_hi: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495625955.png\" alt=\"rId29\" width=\"250\" height=\"47\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement.<br><strong>Statements :</strong><br>All houses are trees.<br>All trees are stones.<br>All stones are rocks.<br><strong>Conclusion (I) :</strong> Some stones are trees.<br><strong>Conclusion (II) : </strong>All houses are rocks.</p>",
                    question_hi: "<p>17. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong><br>सभी घर, पेड़ हैं।<br>सभी पेड़, पत्थर हैं।<br>सभी पत्थर, चट्टानें हैं।<br><strong>निष्कर्ष (I) :</strong> कुछ पत्थर, पेड़ हैं।<br><strong>निष्कर्ष (II) :</strong> सभी घर, चट्टानें हैं।</p>",
                    options_en: [
                        "<p>Both conclusions (I) and (II) follow.</p>",
                        "<p>Only conclusion (II) follows.</p>",
                        "<p>Only conclusion (I) follows.</p>",
                        "<p>Neither conclusion (I) nor (II) follows.</p>"
                    ],
                    options_hi: [
                        "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>",
                        "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                        "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                        "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>"
                    ],
                    solution_en: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495626075.png\" alt=\"rId30\" width=\"250\"><br>Both conclusion I and II follow.</p>",
                    solution_hi: "<p>17.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495626261.png\" alt=\"rId31\" width=\"250\"><br>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;;<br>&lsquo;A &minus; B&rsquo; means &lsquo;A is the brother of B&rsquo;;<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo; and<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the father of B&rsquo;.<br>Based on the above, how is S related to P if &lsquo;P &times; Q &divide; R &minus; S &times; A&rsquo;?</p>",
                    question_hi: "<p>18. एक निश्चित कूट भाषा में,<br>\'A + B\' का अर्थ है \'A, B की माता है\';<br>\'A &minus; B\' का अर्थ है \'A, B का भाई है\';<br>\'A &times; B\' का अर्थ है \'A, B की पत्नी है\' और<br>\'A &divide; B\' का अर्थ है \'A, B का पिता है\'।<br>उपर्युक्त के आधार पर, यदि \'P &times; Q &divide; R &minus; S &times; A\' है, तो S, P से किस प्रकार संबंधित है?</p>",
                    options_en: [
                        "<p>Mother</p>",
                        "<p>Daughter</p>",
                        "<p>Son</p>",
                        "<p>Father</p>"
                    ],
                    options_hi: [
                        "<p>माता</p>",
                        "<p>पुत्री</p>",
                        "<p>पुत्र</p>",
                        "<p>पिता</p>"
                    ],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495626442.png\" alt=\"rId32\" width=\"180\"><br>&lsquo;S&rsquo; is the daughter of &lsquo;P&rsquo;.</p>",
                    solution_hi: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495626442.png\" alt=\"rId32\" width=\"180\"><br>\'S\', \'P\' की पुत्री है ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19.Select the option that can replace the question mark (?) in the following series. <br>NW17, PY19, RA21, TC23, ?</p>",
                    question_hi: "<p>19. उस विकल्प का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आ सकता है। <br>NW17, PY19, RA21, TC23, ?</p>",
                    options_en: [
                        "<p>VE25</p>",
                        "<p>WD24</p>",
                        "<p>XD24</p>",
                        "<p>WF23</p>"
                    ],
                    options_hi: [
                        "<p>VE25</p>",
                        "<p>WD24</p>",
                        "<p>XD24</p>",
                        "<p>WF23</p>"
                    ],
                    solution_en: "<p>19.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495626575.png\" alt=\"rId33\" width=\"300\" height=\"99\"></p>",
                    solution_hi: "<p>19.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495626575.png\" alt=\"rId33\" width=\"300\" height=\"99\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. How many rectangles are there in the following figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495626733.png\" alt=\"rId34\" width=\"250\" height=\"85\"></p>",
                    question_hi: "<p>20. दी गई आकृति में कितने आयत हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495626733.png\" alt=\"rId34\" width=\"250\"></p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>10</p>",
                        "<p>11</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495626899.png\" alt=\"rId35\" width=\"280\"><br><strong>10 rectangles are : </strong>ABCD, IJKL, QRWX, QSVX, QTUX, RSVW, RTUW, STUV, EFGH, MNOP</p>",
                    solution_hi: "<p>20.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495626899.png\" alt=\"rId35\" width=\"280\"><br><strong>10 आयत हैं:</strong> ABCD, IJKL, QRWX, QSVX, QTUX, RSVW, RTUW, STUV, EFGH, MNOP</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. What will come in the place of the question mark (?) in the following equation, if &lsquo;+\' and \'<math display=\"inline\"><mo>&#247;</mo></math>&rsquo; are interchanged and &lsquo;-&rsquo; and \'&times;\' are interchanged ?<br>44 &divide;&nbsp;78 + 6 - 3 &times; 19 = ?</p>",
                    question_hi: "<p>21. यदि \'+\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदल दिया जाए तथा &lsquo;-&rsquo; और \'&times;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>44 &divide;&nbsp;78 + 6 - 3 &times; 19 = ?</p>",
                    options_en: [
                        " 74",
                        " 64",
                        " 78 ",
                        " 68"
                    ],
                    options_hi: [
                        " 74",
                        " 64",
                        " 78 ",
                        " 68 "
                    ],
                    solution_en: "<p>21.(b) <strong>Given :-</strong> 44 &divide;&nbsp;78 + 6 - 3 &times; 19<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;&divide;&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; we get<br>44 + 78 &divide; 6 &times; 3 - 19<br>44 + 13 &times; 3 - 19<br>44 + 39 - 19<br>44 + 20 = 64</p>",
                    solution_hi: "<p>21.(b) <strong>दिया गया :-</strong> 44 &divide; 78 + 6 - 3 &times; 19<br>दिए गए निर्देश के अनुसार \'+\'और \'&divide;\' और \'-\' और \'&times;\' को आपस में बदलने के बाद हमें प्राप्त होता है<br>44 + 78 &divide; 6 &times; 3 - 19<br>44 + 13 &times; 3 - 19<br>44 + 39 - 19<br>44 + 20 = 64</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. A, B, C, D, E, F and G are sitting around a circular table facing the centre (but not necessarily in the same order). Only three people sit between E and F when counted from the left of E. Only three people sit between G and D when counted from the right of D. A sits to the immediate right of G. C is an immediate neighbour of D as well as F.&nbsp;Who sits second to the left of B ?</p>",
                    question_hi: "<p>22. A, B, C, D, E, F और G एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वेइसी क्रम में बैठे हों)। E के बाएँ से गिनने पर, E और F के बीच में केवल तीन व्यक्ति बैठे हैं। D के दाएँ से गिनने पर, G और D के बीच में केवल तीन व्यक्ति बैठे हैं। A, G के ठीक दाएँ बैठा है। D और F दोनों का निकटतम पड़ोसी C है।&nbsp;B के बाएँ से दूसरे स्थान पर कौन बैठा है ?</p>",
                    options_en: [
                        "<p>G</p>",
                        "<p>C</p>",
                        "<p>A</p>",
                        "<p>E</p>"
                    ],
                    options_hi: [
                        "<p>G</p>",
                        "<p>C</p>",
                        "<p>A</p>",
                        "<p>E</p>"
                    ],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495627136.png\" alt=\"rId36\" width=\"130\" height=\"133\"></p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495627136.png\" alt=\"rId36\" width=\"130\" height=\"133\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. What will come in place of the question mark (?) in the following equation if &lsquo;+&rsquo; means &lsquo;&times;&rsquo;, &lsquo;&minus;&rsquo; means &lsquo;&divide;&rsquo;, &lsquo;&times;&rsquo; means &lsquo;+&rsquo; and &lsquo;&divide;&rsquo; means &lsquo;&minus;&rsquo; ?&nbsp;<br>(18 + 10 &times; 20) &minus; 8 &divide; 6 = ?</p>",
                    question_hi: "<p>23. निम्नलिखित समीकरण में यदि \'+\' का अर्थ \'&times;\' है, \'&minus;\' का अर्थ \'&divide;\' है, \'&times;\' का अर्थ \'+\' है और \'&divide;\' का अर्थ \'&minus;\' है, तो प्रश्न-चिह्न (?) के स्थान पर क्या आएगा ?<br>(18 + 10 &times; 20) &minus; 8 &divide; 6 = ?</p>",
                    options_en: [
                        "<p>19</p>",
                        "<p>20</p>",
                        "<p>18</p>",
                        "<p>22</p>"
                    ],
                    options_hi: [
                        "<p>19</p>",
                        "<p>20</p>",
                        "<p>18</p>",
                        "<p>22</p>"
                    ],
                    solution_en: "<p>23.(a) <strong>Given :-</strong> (18 + 10 &times; 20) - 8 &divide;&nbsp;6<br>As per given instruction after interchanging the sign we get,<br>(18 &times; 10 + 20) &divide;&nbsp;8 - 6<br>(180 + 20) &divide;&nbsp;8 - 6<br>(25) - 6 = 19</p>",
                    solution_hi: "<p>23.(a) <strong>दिया गया :-</strong> (18 + 10 &times; 20) - 8 &divide;&nbsp;6<br>दिए गए निर्देश के अनुसार चिह्न को आपस में बदलने पर हमें मिलता है,<br>(18 &times; 10 + 20) &divide;&nbsp;8 - 6<br>(180 + 20) &divide;&nbsp;8 - 6<br>(25) - 6 = 19</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. The position(s) of how many letters will remain unchanged if all the letters in the word &lsquo;ASTEROID&rsquo; are arranged in English alphabetical order ?</p>",
                    question_hi: "<p>24. यदि &lsquo;ASTEROID&rsquo; शब्द के सभी अक्षरों को अंग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति में कोई बदलाव नहीं होगा?</p>",
                    options_en: [
                        "<p>Three</p>",
                        "<p>None</p>",
                        "<p>Two</p>",
                        "<p>One</p>"
                    ],
                    options_hi: [
                        "<p>तीन</p>",
                        "<p>एक भी नहीं</p>",
                        "<p>दो</p>",
                        "<p>एक</p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495627297.png\" alt=\"rId37\" width=\"130\" height=\"72\"><br>Hence, position of only one letter remain unchanged i.e. A.</p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495627297.png\" alt=\"rId37\" width=\"130\" height=\"72\"><br>इसलिए, केवल एक अक्षर की स्थिति अपरिवर्तित है i.e. A.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Ghanshyam is thrice as old as Akash. 10 years ago, Ghanshyam was four times as old as Akash. What is the difference between their current ages (in years) ?</p>",
                    question_hi: "<p>25. घनश्याम की आयु, आकाश की आयु की तीन गुना है। 10 वर्ष पहले, घनश्याम की आयु, आकाश की आयु की चार गुना थी। उनकी वर्तमान आयु (वर्ष में) के बीच कितना अंतर है?</p>",
                    options_en: [
                        "<p>65</p>",
                        "<p>50</p>",
                        "<p>55</p>",
                        "<p>60</p>"
                    ],
                    options_hi: [
                        "<p>65</p>",
                        "<p>50</p>",
                        "<p>55</p>",
                        "<p>60</p>"
                    ],
                    solution_en: "<p>25.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495627395.png\" alt=\"rId38\" width=\"280\" height=\"230\"><br>&there4; Difference between their current age = 90 - 30 = 60yr</p>",
                    solution_hi: "<p>25.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495627517.png\" alt=\"rId39\" width=\"280\" height=\"224\"><br>&there4; उनकी वर्तमान आयु के बीच का अंतर = 90 - 30 = 60 वर्ष</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. How many states are covered in the World Bank report titled &lsquo;Jobs at Your Doorstep : A Jobs Diagnostics for Young People&rsquo;?</p>",
                    question_hi: "26. विश्व बैंक की \'जॉब्स एट योर डोरस्टेप: ए जॉब्स डायग्नोस्टिक्स फॉर यंग पीपल\' शीर्षक वाली रिपोर्ट में कितने राज्य शामिल हैं ? ",
                    options_en: [
                        " 4 ",
                        " 5 ",
                        " 6           ",
                        " 7       "
                    ],
                    options_hi: [
                        " 4            ",
                        " 5           ",
                        " 6          ",
                        " 7     "
                    ],
                    solution_en: "<p>26.(c) <strong>6.</strong> The World Bank&rsquo;s report titled &lsquo;Jobs at Your Doorstep: A Jobs&nbsp;Diagnostics for Young People&rsquo; covers six states: Himachal Pradesh, Kerala, Madhya Pradesh, Maharashtra, Odisha, and Rajasthan. Union Minister for Education, Shri Dharmendra Pradhan, along with Minister of Labour and Employment and Youth Affairs and Sports Dr. Mansukh Mandaviya launched this report in New Delhi.</p>",
                    solution_hi: "<p>26.(c) <strong>6.</strong> विश्व बैंक की रिपोर्ट &lsquo;जॉब्स एट योर डोरस्टेप: ए जॉब्स डायग्नोस्टिक्स फॉर यंग पीपल&rsquo; में छह राज्यों को शामिल किया गया है: हिमाचल प्रदेश, केरल, मध्य प्रदेश, महाराष्ट्र, ओडिशा और राजस्थान। केंद्रीय शिक्षा मंत्री श्री धर्मेंद्र प्रधान ने श्रम एवं रोजगार तथा युवा मामले एवं खेल मंत्री डॉ. मनसुख मंडाविया के साथ मिलकर नई दिल्ली में इस रिपोर्ट को लॉन्च किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which team won the Syed Mushtaq Ali Trophy (SMAT) 2024 ?</p>",
                    question_hi: "<p>27. सैयद मुश्ताक अली ट्रॉफी (SMAT) 2024 किस टीम ने जीती?</p>",
                    options_en: [
                        "<p>Madhya Pradesh</p>",
                        "<p>Mumbai</p>",
                        "<p>Tamil Nadu</p>",
                        "<p>Delhi</p>"
                    ],
                    options_hi: [
                        "<p>मध्य प्रदेश</p>",
                        "<p>मुंबई</p>",
                        "<p>तमिलनाडु</p>",
                        "<p>दिल्ली</p>"
                    ],
                    solution_en: "<p>27.(b) <strong>Mumbai</strong>. Mumbai defeated Madhya Pradesh by five wickets to win the Syed Mushtaq Ali Trophy 2024.</p>",
                    solution_hi: "<p>27.(b) <strong>मुंबई।</strong> मुंबई ने मध्य प्रदेश को पांच विकेट से हराकर सैयद मुश्ताक अली ट्रॉफी 2024 का खिताब जीता।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The 10th International Forest Fair took place in _________, Madhya Pradesh.</p>",
                    question_hi: "<p>28. 10वां अंतर्राष्ट्रीय वन मेला मध्य प्रदेश के __________ में हुआ।</p>",
                    options_en: [
                        "<p>Indore</p>",
                        "<p>Gwalior</p>",
                        "<p>Jabalpur</p>",
                        "<p>Bhopal</p>"
                    ],
                    options_hi: [
                        "<p>इंदौर</p>",
                        "<p>ग्वालियर</p>",
                        "<p>जबलपुर</p>",
                        "<p>भोपाल</p>"
                    ],
                    solution_en: "<p>28.(d) <strong>Bhopal</strong>. This festival started on December 17, 2024, and concluded on December 23. The fair aims to promote sustainable forest practices, empower local communities, and encourage collaboration among stakeholders in the forestry sector.</p>",
                    solution_hi: "<p>28.(d) <strong>भोपाल</strong>। यह मेला 17 दिसंबर, 2024 को शुरू हुआ और 23 दिसंबर को समाप्त हुआ। मेले का उद्देश्य सतत वन प्रथाओं को बढ़ावा देना, स्थानीय समुदायों को सशक्त बनाना और वानिकी क्षेत्र में हितधारकों के बीच सहयोग को प्रोत्साहित करना है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. In which of the following festivals is Kolam drawn ?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन-से त्यौहार में कोलम बनाया जाता है?</p>",
                    options_en: [
                        "<p>Karma</p>",
                        "<p>Bhogali Bihu</p>",
                        "<p>Hareli</p>",
                        "<p>Pongal</p>"
                    ],
                    options_hi: [
                        "<p>करमा</p>",
                        "<p>भोगाली बिहु</p>",
                        "<p>हरेली</p>",
                        "<p>पोंगल</p>"
                    ],
                    solution_en: "<p>29.(d) <strong>Pongal</strong>. It is a harvest festival celebrated by the Tamil community. It is primarily celebrated in Tamil Nadu, Andhra Pradesh, Karnataka and Puducherry. Other harvesting festivals: Karma (Jharkhand), Bhogali Bihu (Assam), Hareli (Chhattisgarh), Onam (Kerala), Nuakhai (Odisha), Gudi Padwa (Maharashtra).</p>",
                    solution_hi: "<p>29.(d) <strong>पोंगल</strong>। यह तमिल समुदाय द्वारा मनाया जाने वाला एक फसल उत्सव है। यह मुख्य रूप से तमिलनाडु, आंध्र प्रदेश, कर्नाटक और पुडुचेरी में मनाया जाता है। अन्य फसल उत्सव: करमा (झारखंड), भोगली बिहू (असम), हरेली (छत्तीसगढ़), ओणम (केरल), नुआखाई (ओडिशा), गुड़ी पड़वा (महाराष्ट्र) इत्यादि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. From which of the following epics did the Garadi folk dance of Puducherry originate ?</p>",
                    question_hi: "<p>30.पुडुचेरी का गराडी लोक नृत्य निम्नलिखित में से किस महाकाव्य से उत्पन्न हुआ?</p>",
                    options_en: [
                        "<p>Ramayana</p>",
                        "<p>Panchatantra</p>",
                        "<p>Mahabharata</p>",
                        "<p>Hitopadesha</p>"
                    ],
                    options_hi: [
                        "<p>रामायण</p>",
                        "<p>पंचतंत्र</p>",
                        "<p>महाभारत</p>",
                        "<p>हितोपदेश</p>"
                    ],
                    solution_en: "<p>30.(a) <strong>Ramayana</strong>. Garadi dance is mainly performed at the time of the car festival of Lord Vishnu. Indian States and folk Dances: Kerala - Chavittukali, Kolkali or Kolkkali, Kummattikkali. Goa - Dashavatara, Dekhni, Dhalo, Dhangar, Fugdi, Ghodemodni. Karnataka - Dollu Kunitha, Bayalata, Kamsale, Veeragase. Maharashtra - Dhangari Gaja, Lavani, Povada, and Koli dance.</p>",
                    solution_hi: "<p>30.(a) <strong>रामायण</strong>। गराडी नृत्य मुख्य रूप से भगवान विष्णु के रथ महोत्सव के समय किया जाता है। भारतीय राज्य एवं लोक नृत्य: केरल - चविट्टुकली, कोलकली या कोलक्कली, कुम्मट्टिक्ली। गोवा - दशावतार, देखनी, धालो, धनगर, फुगड़ी, घोडेमोदनी। कर्नाटक - डोल्लू कुनिथा, बैलाटा, कमसाले, वीरगासे। महाराष्ट्र - धनगरी गजा, लावणी, पोवाड़ा और कोली नृत्य।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who among the following is a distinguished and experienced scion of the famous Kavungal family associated with \'Kathakali\' for six generations ?</p>",
                    question_hi: "<p>31. छह पीढ़ियों से \'कथकली\' से जुड़े प्रसिद्ध कवंगल परिवार के प्रतिष्ठित और अनुभवी वंशज निम्नलिखित में से कौन है?</p>",
                    options_en: [
                        "<p>Padma Subramanyam</p>",
                        "<p>Ammannur Madhava Chakyar</p>",
                        "<p>Chathunni Panicker</p>",
                        "<p>Meenakshi Sundaram Pillai</p>"
                    ],
                    options_hi: [
                        "<p>पद्मा सुब्रमण्यम</p>",
                        "<p>अम्मानूर माधव चक्यार</p>",
                        "<p>चतुन्नी पणिक्कर</p>",
                        "<p>मीनाक्षी सुंदरम पिल्लई</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>Chathunni Panicker: </strong>Awards - Padma Shri (2006), Sangeet Natak Akademi Award (1973). Other exponents of Kathakali - Kanak Rele, Kottakkal Sivaraman, Guru Kunchu Kurup. Padma Subramanyam (Bharatanatyam): Awards - Padma Vibhushan (2024), Padma Bhushan (2003), Padma shri (1981). Guru Ammannur Madhava Chakyar (Kutiyattam): Award - Padma shri (1982).</p>",
                    solution_hi: "<p>31.(c) <strong>चतुन्नी पणिक्कर</strong>। पुरस्कार - पद्म श्री (2006), संगीत नाटक अकादमी पुरस्कार (1973)। कथकली के अन्य प्रतिपादक - कनक रेले, कोट्टक्कल शिवरामन, गुरु कुंचू कुरुप। पद्म सुब्रमण्यम (भरतनाट्यम): पुरस्कार - पद्म विभूषण (2024), पद्म भूषण (2003), पद्म श्री (1981)। गुरु अम्मनूर माधव चक्यार (कुटियाट्टम): पुरस्कार - पद्म श्री (1982)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Who among the following wrote the book &lsquo;Chanakya&rsquo;s Chant&rsquo; ?</p>",
                    question_hi: "32. चाणक्याज चैंट (Chanakya’s Chant) पुस्तक निम्नलिखित में से किसने लिखी?",
                    options_en: [
                        " Novoneel Chakraborty ",
                        " Samit Basu ",
                        " Amish Tripathi ",
                        " Ashwin Sanghi "
                    ],
                    options_hi: [
                        " नोवोनील चक्रवर्ती",
                        " समित बसु",
                        " अमीश त्रिपाठी",
                        " अश्विन सांघी"
                    ],
                    solution_en: "<p>32.(d) <strong>Ashwin Sanghi.</strong> His other books: &lsquo;The Rozabal Line&rsquo;, &lsquo;The Krishna Key&rsquo;, &lsquo;The Sialkot Saga&rsquo;. Novoneel Chakraborty - &lsquo;Stranger Trilogy&rsquo;, &lsquo;Forever is True&rsquo;, &lsquo;Half Torn Hearts&rsquo;. Samit Basu - &lsquo;The Simoqin Prophecies&rsquo;, &lsquo;The City Inside&rsquo;, &lsquo;Chosen Spirits&rsquo;. Amish Tripathi - &lsquo;The Immortals of Meluha&rsquo;, &lsquo;The Secret of the Nagas&rsquo;, &lsquo;The Shiva Trilogy&rsquo;.</p>",
                    solution_hi: "<p>32.(d) <strong>अश्विन सांघी।</strong> उनकी अन्य पुस्तकें: &lsquo;द रोज़ाबल लाइन&rsquo;, &lsquo;द कृष्णा की&rsquo;, &lsquo;द सियालकोट सागा&rsquo;। नोवोनील चक्रवर्ती - &lsquo;स्ट्रेंजर ट्रिलॉजी&rsquo;, &lsquo;फॉरएवर इज़ ट्रू&rsquo;, &lsquo;हाफ़ टॉर्न हार्ट्स&rsquo;। समित बसु - &lsquo;द सिमोकिन प्रोफेसीज़&rsquo;, &lsquo;द सिटी इनसाइड&rsquo;, &lsquo;चॉसन स्पिरिट्स&rsquo;। अमीश त्रिपाठी - &lsquo;द इम्मॉर्टल्स ऑफ़ मेलुहा&rsquo;, &lsquo;द सीक्रेट ऑफ़ द नागाज़&rsquo;, &lsquo;द शिवा ट्रिलॉजी&rsquo;।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. ______ was the first fast bowler who was the Indian test team captain.</p>",
                    question_hi: "<p>33.______ पहले तेज गेंदबाज थे, जो भारतीय टेस्ट टीम के कप्तान थे।</p>",
                    options_en: [
                        "<p>Javagal Srinath</p>",
                        "<p>Jaspreet Bumrah</p>",
                        "<p>Zaheer Khan</p>",
                        "<p>Kapil Dev</p>"
                    ],
                    options_hi: [
                        "<p>जवागल श्रीनाथ</p>",
                        "<p>जसप्रीत बुमराह</p>",
                        "<p>जहीर खान</p>",
                        "<p>कपिल देव</p>"
                    ],
                    solution_en: "<p>33.(d) <strong>Kapil Dev.</strong> He is the only player in the history of cricket to have taken more than 400 wickets (434 wickets) and scored more than 5,000 runs in Test. He captained the Indian cricket team that won the 1983 Cricket World Cup, and in the process became the first Indian captain to win the Cricket World Cup.</p>",
                    solution_hi: "<p>33.(d) <strong>कपिल देव</strong>। वे क्रिकेट के इतिहास में एकमात्र ऐसे खिलाड़ी हैं जिन्होंने टेस्ट में 400 से ज़्यादा विकेट (434 विकेट) लिए हैं और 5,000 से ज़्यादा रन बनाए हैं। उन्होंने 1983 में क्रिकेट विश्व कप जीतने वाली भारतीय क्रिकेट टीम की कप्तानी की और इस प्रक्रिया में क्रिकेट विश्व कप जीतने वाले पहले भारतीय कप्तान बने।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. While estimating Gross Domestic Product, the monetary value of only final goods are considered because ___________.<br>a. its immobility, the active economic flow is ended.<br>b. of the issues of double counting<br>c. the value added at each stage of the production process is included</p>",
                    question_hi: "<p>34. सकल घरेलू उत्पाद (Gross Domestic Product) का प्राक्कलन करते समय, केवल अंतिम वस्तुओं के मौद्रिक मूल्य पर विचार किया जाता है क्योंकि ______।<br>a. इसकी गतिहीनता (immobility) से सक्रिय आर्थिक प्रवाह समाप्त हो जाता है<br>b. इसमें दोहरी गणना (double counting) की समस्या होती है<br>c. उत्पादन प्रक्रिया के प्रत्येक चरण में मूल्य वर्धित (value added) शामिल किया जाता है</p>",
                    options_en: [
                        " Only b",
                        " Only a",
                        " All a, b and c",
                        " Only b and c"
                    ],
                    options_hi: [
                        " केवल b",
                        " केवल a",
                        " सभी a, b और c",
                        " केवल b और c"
                    ],
                    solution_en: "<p>34.(a) <strong>Only b.</strong> Gross Domestic Product (GDP) - Aggregate value of goods and services produced within the domestic territory of a country. While estimating GDP, the monetary value of only final goods is considered to avoid the problem of double counting. If intermediate goods were included, they would be counted multiple times, leading to an inflated GDP figure.</p>",
                    solution_hi: "<p>34.(a) <strong>केवल b.</strong> सकल घरेलू उत्पाद (GDP) - किसी देश के घरेलू क्षेत्र में उत्पादित वस्तुओं और सेवाओं का कुल मूल्य। GDP का अनुमान लगाते समय, केवल अंतिम वस्तुओं के मौद्रिक मूल्य को ही माना जाता है ताकि दोहरी गणना की समस्या से बचा जा सके। यदि मध्यवर्ती वस्तुओं को शामिल किया जाता है, तो उन्हें कई बार गिना जाएगा, जिससे GDP का आंकड़ा बढ़ जाएगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. The institute established by Kaka Hathrasi in 1932 is:</p>",
                    question_hi: "<p>35. 1932 में काका हाथरसी (Kaka Hathrasi) द्वारा स्थापित संस्थान कौन सा है?</p>",
                    options_en: [
                        "<p>Sangeet Karyalaya</p>",
                        "<p>Music Art Centre</p>",
                        "<p>Music Art Kendra</p>",
                        "<p>Swar Sangam Kendra</p>"
                    ],
                    options_hi: [
                        "<p>संगीत कार्यालय</p>",
                        "<p>म्यूजिक आर्ट सेंटर</p>",
                        "<p>म्यूजिक आर्ट केंद्र</p>",
                        "<p>स्वर संगम केंद्र</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Sangeet Karyalaya:</strong> Located - Hathras (Uttar Pradesh). The institute was founded with the aim of promoting Indian classical music and preserving the cultural heritage of India. Kaka Hathrasi published the monthly magazine \"SANGEET\" in 1935.</p>",
                    solution_hi: "<p>35.(a) <strong>संगीत कार्यालय:</strong> स्थित - हाथरस (उत्तर प्रदेश)। इस संस्थान की स्थापना भारतीय शास्त्रीय संगीत को बढ़ावा देने और भारत की सांस्कृतिक विरासत को संरक्षित करने के उद्देश्य से की गई थी। काका हाथरसी ने 1935 में मासिक पत्रिका \"संगीत\" प्रकाशित की।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which language is known as &lsquo;Italian of the East&rsquo; ?</p>",
                    question_hi: "<p>36. किस भाषा को \'पूर्व का इतालवी\' कहा जाता है ?</p>",
                    options_en: [
                        " Telugu",
                        " Malayalam",
                        " Marathi",
                        " Kannada"
                    ],
                    options_hi: [
                        " तेलुगु",
                        " मलयालम",
                        " मराठी",
                        " कन्नड़"
                    ],
                    solution_en: "<p>36.(a) <strong>Telugu</strong>. Telugu Language Day is celebrated every year on August 29. In 2008, Telugu was granted the status of a classical language. Nicolo di Conti, who visited the Vijayanagara Empire, found that the words in the Telugu language end with vowels, just like those in Italian, and hence referred to it as \"The Italian of the East&rdquo;.</p>",
                    solution_hi: "<p>36.(a) <strong>तेलुगु</strong>। तेलुगु भाषा दिवस प्रत्येक वर्ष 29 अगस्त को मनाया जाता है। 2008 में तेलुगु को शास्त्रीय भाषा का दर्जा प्रदान किया गया था। विजयनगर साम्राज्य का दौरा करने वाले निकोलो डी कोंटी ने पाया कि तेलुगु भाषा में शब्द इटालियन की तरह ही स्वरों (vowels) के साथ समाप्त होते हैं, इसलिए इसे \"पूर्व का इटालियन\" कहा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. The ______ were tributary chiefs of the Gupta dynasty. They established an independent kingdom in western India. Dhruvasena II was the most important ruler of this kingdom.</p>",
                    question_hi: "<p>37. ______ गुप्तों के सहायक प्रमुख थे। उन्होंने पश्चिमी भारत में एक स्वतंत्र राज्य की स्थापना की थी। ध्रुवसेन द्वितीय इस राजवंश का सर्वाधिक महत्वपूर्ण शासक था ?</p>",
                    options_en: [
                        "<p>Maitrakas</p>",
                        "<p>Maukharis</p>",
                        "<p>Pushyabhutis</p>",
                        "<p>Gurjara Pratiharas</p>"
                    ],
                    options_hi: [
                        "<p>मैत्रक</p>",
                        "<p>मौखरि</p>",
                        "<p>पुष्यभूति</p>",
                        "<p>गुर्जर-प्रतिहार</p>"
                    ],
                    solution_en: "<p>37.(a) <strong>Maitrakas </strong>established Valabhi as their capital and ruled until the middle of the eighth century. Dhruvasena II was a contemporary of Harshavardhana and was married to his daughter. Hsuan Tsang tells us that Dhruvasena II attended Harsha&rsquo;s assembly at Prayaga (Allahabad). Maukharies ruled over Kanauj, a city in western Uttar Pradesh, they were also the subordinate rulers of the Guptas and used the title of samanta.</p>",
                    solution_hi: "<p>37.(a) <strong>मैत्रक </strong>ने वल्लभी को अपनी राजधानी बनाया और आठवीं शताब्दी के मध्य तक शासन किया। ध्रुवसेन द्वितीय, हर्षवर्धन के समकालीन थे और उनकी शादी उनकी पुत्री से हुई थी। ह्वेन त्सांग बताता है कि ध्रुवसेन द्वितीय ने प्रयाग (इलाहाबाद) में हर्ष की सभा में भाग लिया था। मौखरियों ने पश्चिमी उत्तर प्रदेश के एक शहर कन्नौज पर शासन किया, वे गुप्तों के अधीनस्थ शासक भी थे और सामंत की उपाधि का उपयोग करते थे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following types of emergencies is declared under Article 360 of the Indian Constitution ?</p>",
                    question_hi: "38. भारतीय संविधान के अनुच्छेद 360 के तहत निम्नलिखित में से किस प्रकार के आपातकाल की घोषणा की जाती है?",
                    options_en: [
                        " State Emergency",
                        " Financial Emergency",
                        " National Emergency",
                        " Constitutional Emergency"
                    ],
                    options_hi: [
                        " राज्य आपातकाल",
                        " वित्तीय आपातकाल ",
                        " राष्ट्रीय आपातकाल",
                        " संवैधानिक आपातकाल"
                    ],
                    solution_en: "<p>38.(b) <strong>Financial Emergency.</strong> Article 360 in Part XVIII of the Indian Constitution empowers the President of India to proclaim a Financial Emergency if he/she is satisfied that a situation has arisen due to which the financial stability or credit of India or any part of its territory is threatened. Other Emergency types: National Emergency (Article 352): Declared during war, external aggression, or armed rebellion. State Emergency/President\'s Rule (Article 356): When state machinery fails or state government cannot be carried on according to the Constitution.</p>",
                    solution_hi: "<p>38.(b) <strong>वित्तीय आपातकाल।</strong> भारतीय संविधान के भाग XVIII में अनुच्छेद 360 भारत के राष्ट्रपति को वित्तीय आपातकाल की घोषणा करने का अधिकार प्रदान करता है, यदि उन्हे लगता है की ऐसी स्थिति उत्पन्न हो गई है जिसके कारण भारत की वित्तीय स्थिरता या ऋण या उसके किसी भाग को खतरा है। अन्य प्रकार के आपातकाल: राष्ट्रीय आपातकाल (अनुच्छेद 352): युद्ध, बाह्य आक्रमण या सशस्त्र विद्रोह के दौरान घोषित किया जाता है। राज्य आपातकाल/राष्ट्रपति शासन (अनुच्छेद 356): जब राज्य का संवैधानिक तंत्र विफल हो जाता है या राज्य सरकार संविधान के अनुसार नहीं चल रही होती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. In which year was the Gandhi-Irwin Pact signed ?</p>",
                    question_hi: "<p>39. गांधी-इरविन समझौते पर किस वर्ष हस्ताक्षर किए गए थे ?</p>",
                    options_en: [
                        "<p>1935</p>",
                        "<p>1929</p>",
                        "<p>1931</p>",
                        "<p>1941</p>"
                    ],
                    options_hi: [
                        "<p>1935</p>",
                        "<p>1929</p>",
                        "<p>1931</p>",
                        "<p>1941</p>"
                    ],
                    solution_en: "<p>39.(c) <strong>1931</strong>. The Gandhi-Irwin Pact was signed on March 5, 1931, between Mahatma Gandhi and Lord Irwin, the Viceroy of India (1926-1931). The pact ended the Civil Disobedience Movement, initiated by Gandhi with the Salt March, and was signed before the Second Round Table Conference in London.</p>",
                    solution_hi: "<p>39.(c) <strong>1931</strong>. गांधी-इरविन समझौता 5 मार्च, 1931 को महात्मा गांधी और भारत के वायसराय लॉर्ड इरविन (1926-1931) के बीच हुआ था। इस समझौते ने सविनय अवज्ञा आंदोलन को समाप्त कर दिया, जिसकी शुरुआत गांधीजी ने नमक मार्च के साथ की थी, तथा इस पर लंदन में द्वितीय गोलमेज सम्मेलन से पहले हस्ताक्षर किये गये थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Who among the following deciphered the Brahmi Script of the Mauryan period ?</p>",
                    question_hi: "<p>40. निम्नलिखित में से किसने मौर्य काल की ब्राह्मी लिपि का कूटवाचन (deciphered) किया ?</p>",
                    options_en: [
                        " James Mill   ",
                        " James Prinsep ",
                        " Rakhal Das Banerji ",
                        "<p>Herbert Hope Risley</p>"
                    ],
                    options_hi: [
                        "<p>जेम्स मिल (James Mill)</p>",
                        "<p>जेम्स प्रिंसेप (James Prinsep)</p>",
                        "<p>राखल दास बनर्जी (Rakhal Das Banerji)</p>",
                        "<p>हर्बर्ट होपरिस्ली (Herbert Hope Risley)</p>"
                    ],
                    solution_en: "<p>40.(b) <strong>James Prinsep</strong> deciphered the Brahmi and Kharoshthi scripts. The Brahmi script, one of the oldest writing styles in ancient India, is best exemplified by the rock-cut edicts of Ashoka, which were deciphered by Prinsep, an archaeologist with the East India Company.</p>",
                    solution_hi: "<p>40.(b) <strong>जेम्स प्रिंसेप</strong> ने ब्राह्मी और खरोष्ठी लिपियों का अर्थ निकाला। प्राचीन भारत की सबसे पुरानी लेखन शैलियों में से एक ब्राह्मी लिपि का सबसे अच्छा उदाहरण अशोक के चट्टानों पर खुदे शिलालेख हैं, जिन्हें ईस्ट इंडिया कंपनी के पुरातत्वविद् प्रिंसेप ने पढ़ा था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "41. The digestive enzymes inside lysosomes are made by:",
                    question_hi: "41. लाइसोसोम (lysosomes) के भीतर पाचन एंजाइम _____ द्वारा बनाए जाते हैं।",
                    options_en: [
                        " rough endoplasmic reticulum  ",
                        " plastids themselves   ",
                        " Golgi apparatus ",
                        " mitochondria"
                    ],
                    options_hi: [
                        " रूक्ष अंतःप्रद्रव्य जालिका (rough endoplasmic reticulum) ",
                        " स्वयं प्लास्टिड (plastids themselves)  ",
                        " गॉल्जी उपकरण (Golgi apparatus) ",
                        " सूत्रकणिका (mitochondria)"
                    ],
                    solution_en: "<p>41.(a) <strong>rough endoplasmic reticulum.</strong> Digestive enzymes are proteins that break down food so our body can absorb nutrients. Plastids are double membrane bound organelles found in the cytoplasm of plant cells and some protists such as Euglena. Golgi apparatus is a central membrane organelle for trafficking and post-translational modification of protein and lipid in the cell. Mitochondria are the \"powerhouses\" of the cell, breaking down fuel molecules and capturing energy in cellular respiration.</p>",
                    solution_hi: "41.(a) रूक्ष अंतःप्रद्रव्य जालिका (rough endoplasmic reticulum)। पाचन एंजाइम प्रोटीन होते हैं जो भोजन को तोड़ते हैं जिससे हमारा शरीर पोषक तत्वों को अवशोषित कर सके। प्लास्टिड पादप की कोशिकाओं और यूग्लीना जैसे कुछ प्रोटिस्ट के कोशिका द्रव्य में पाए जाने वाले दोहरे झिल्ली से बंधे अंग हैं। गॉल्जी उपकरण कोशिका में प्रोटीन और लिपिड के यातायात और अनुवादोत्तर संशोधन के लिए एक केंद्रीय झिल्ली अंग है। माइटोकॉन्ड्रिया कोशिका का \"पावरहाउस\" हैं, जो ईंधन के अणुओं को तोड़ते हैं और कोशिकीय श्वसन में ऊर्जा को संरक्षित करते हैं।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Kaal Baisakhi belongs to which state from the following ?</p>",
                    question_hi: "42. काल बैसाखी निम्नलिखित में से किस राज्य से संबंधित है?",
                    options_en: [
                        " Punjab",
                        " Rajasthan",
                        " West Bengal",
                        " Bihar"
                    ],
                    options_hi: [
                        " पंजाब",
                        " राजस्थान ",
                        " पश्चिम बंगाल",
                        " बिहार"
                    ],
                    solution_en: "<p>42.(c) <strong>West Bengal. </strong>Kal Baisakhi, also known as Nor\'westers, is a pre-monsoon thunderstorm characteristic of West Bengal, typically occurring in the Bengali month of Baisakh (April-May). Related meteorological terms: Cherry Blossoms/Coffee Showers - It is the local humid wind of Karnataka that is helpful for the coffee plantation and flows in the hot weather season. Mango- shower - It is pre-monsoon rainfall and occurs especially in parts of Kerala, and Karnataka. It helps in the early ripening of mangoes.</p>",
                    solution_hi: "<p>42.(c) पश्चिम बंगाल। काल बैसाखी, जिसे नॉरवेस्टर के नाम से भी जाना जाता है, यह पश्चिम बंगाल की विशेषता वाला मानसून-पूर्व तूफ़ान है, जो आमतौर पर बंगाली महीने बैसाख (अप्रैल-मई) में आता है। मौसम संबंधी शब्द: चेरी ब्लॉसम/कॉफ़ी शावर - यह कर्नाटक की स्थानीय आर्द्र वायु है जो कॉफ़ी के बागानों के लिए उपयोगी है और गर्म मौसम के दौरान चलती है। मैंगो - शावर :- यह मानसून-पूर्व वर्षा है और विशेष रूप से केरल और कर्नाटक के कुछ हिस्सों में होती है। यह आमों को जल्दी पकने में मदद करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. The zone where the earthquake waves are not observed is called:</p>",
                    question_hi: "<p>43. वह क्षेत्र क्या कहलाता है जहाँ कोई भी भूकंपीय तरंग अभिलेखित (observed) नहीं होती?</p>",
                    options_en: [
                        "<p>Benthic Zone</p>",
                        "<p>Hypocentre</p>",
                        "<p>Shadow Zone</p>",
                        "<p>Epicentre</p>"
                    ],
                    options_hi: [
                        "<p>नितलस्थ क्षेत्र (Benthic Zone)</p>",
                        "<p>अवकेंद्र (Hypocentre)</p>",
                        "<p>छाया क्षेत्र (Shadow Zone)</p>",
                        "<p>अधिकेंद्र (Epicentre)</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>Shadow Zone.</strong> The shadow zone is the area between 104 to 140 degrees from an earthquake where no direct P waves are received. It occurs because S waves are stopped by the liquid core, and P waves are refracted. The benthic zone is the lowest ecological region of a body of water, such as a lake or ocean. The hypocenter is the point inside the Earth where an earthquake begins, while the epicenter is the point on the surface directly above it.</p>",
                    solution_hi: "<p>43.(c) <strong>छाया क्षेत्र </strong>(<strong>Shadow Zone</strong>)। छाया क्षेत्र वह क्षेत्र है जहाँ भूकंप के 104 से 140 डिग्री के बीच कोई सीधी P तरंगें प्राप्त नहीं होतीं। यह इसलिए होता है क्योंकि S तरंगें द्रव कोर द्वारा रोक दी जाती हैं, और P तरंगें अपवर्तित हो जाती हैं। बेन्थिक ज़ोन किसी जल निकाय, जैसे कि झील या महासागर का सबसे निचला पारिस्थितिक क्षेत्र है। हाइपोसेंटर पृथ्वी के अंदर वह बिंदु है जहाँ भूकंप शुरू होता है, जबकि उपरिकेंद्र सतह पर सीधे उसके ऊपर स्थित बिंदु होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of following is NOT a device of parliamentary proceedings ?</p>",
                    question_hi: "<p>44. निम्नलिखित में से कौन-सा विकल्प संसदीय कार्यवाही की विधि नहीं है?</p>",
                    options_en: [
                        "<p>Half-an-hour discussion</p>",
                        "<p>Zero-hour discussion</p>",
                        "<p>Two-hour (short) discussion</p>",
                        "<p>Full-day discussion</p>"
                    ],
                    options_hi: [
                        "<p>आधे घंटे की चर्चा</p>",
                        "<p>शून्य काल चर्चा</p>",
                        "<p>दो घंटे की (लघु) चर्चा</p>",
                        "<p>पूरे दिन की चर्चा</p>"
                    ],
                    solution_en: "<p>44.(d) <strong>Full-day discussion. </strong>Devices of Parliamentary Proceedings refers to the various procedural tools, mechanisms, and practices employed within a parliamentary system to facilitate the conduct of the business of the legislature. Half-an-Hour Discussion - It is meant for discussing a matter of sufficient public importance, which has been subjected to a lot of debate and the answer to which needs elucidation on a matter of fact. Zero-Hour Discussion - This occurs immediately after Question Hour, allowing members to raise urgent matters without prior notice. Short Duration Discussion - It is also known as two-hour discussion as the time allotted for such a discussion should not exceed two hours.</p>",
                    solution_hi: "<p>44.(d) <strong>पूरे दिन की चर्चा।</strong> संसदीय कार्यवाही के उपकरण संसदीय प्रणाली के भीतर विधानमंडल के व्यवसाय के संचालन को सुविधाजनक बनाने के लिए नियोजित विभिन्न प्रक्रियात्मक उपकरणों, तंत्रों और प्रथाओं को संदर्भित करते हैं। आधे घंटे की चर्चा - इसका उद्देश्य पर्याप्त सार्वजनिक महत्व के विषय पर चर्चा करना है, जिस पर बहुत बहस हुई है और जिसका उत्तर तथ्यात्मक मामले पर स्पष्टीकरण की आवश्यकता है। शून्य-काल चर्चा - यह प्रश्नकाल के तुरंत बाद होती है, जिससे सदस्यों को बिना किसी पूर्व सूचना के तत्काल मामलों को उठाने की अनुमति मिलती है। लघु अवधि की चर्चा - इसे दो घंटे की चर्चा के रूप में भी जाना जाता है क्योंकि इस तरह की चर्चा के लिए आवंटित समय दो घंटे से अधिक नहीं होना चाहिए।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Air and National Defence are examples of which of the following goods ?</p>",
                    question_hi: "<p>45. वायु और राष्ट्रीय रक्षा निम्नलिखित में से किन वस्तुओं के उदाहरण हैं?</p>",
                    options_en: [
                        "<p>Public goods</p>",
                        "<p>Common goods</p>",
                        "<p>Club goods</p>",
                        "<p>Private goods</p>"
                    ],
                    options_hi: [
                        "<p>सार्वजनिक वस्तुओं</p>",
                        "<p>सामान्&zwj;य वस्तुओं</p>",
                        "<p>क्लब वस्तुओं</p>",
                        "<p>निजी वस्तुओं</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Public goods </strong>are resources that are available to everyone, and can be used without reducing their availability to others. Public goods are characterized by non-excludability (no one can be excluded from using them) and non-rivalry (one person\'s use does not reduce availability to others). Air (as a clean resource) and National Defence are examples of public goods because they are available for everyone to use, and their consumption by one individual does not diminish their availability to others.</p>",
                    solution_hi: "<p>45.(a) <strong>सार्वजनिक वस्तुएँ </strong>वे संसाधन हैं जो सभी के लिए उपलब्ध हैं, और दूसरों के लिए उनकी उपलब्धता को कम किए बिना उनका उपयोग किया जा सकता है। सार्वजनिक वस्तुओं की विशेषता गैर-बहिष्कारिता (किसी को भी उनके उपयोग से बाहर नहीं रखा जा सकता) और गैर-प्रतिद्वंद्विता (एक व्यक्ति के उपयोग से दूसरों के लिए उनकी उपलब्धता कम नहीं होती) है। वायु (एक स्वच्छ संसाधन के रूप में) और राष्ट्रीय रक्षा सार्वजनिक वस्तुओं के उदाहरण हैं क्योंकि वे सभी के उपयोग के लिए उपलब्ध हैं, और एक व्यक्ति द्वारा उनके उपभोग से दूसरों के लिए उनकी उपलब्धता कम नहीं होती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which Law of Newton states that for every action (force) in nature there is an equal and opposite reaction ?</p>",
                    question_hi: "<p>46. न्यूटन के किस नियम के अनुसार प्रकृति में प्रत्येक क्रिया (बल) के लिए एक समान और विपरीत प्रतिक्रिया होती है ?</p>",
                    options_en: [
                        "<p>2nd</p>",
                        "<p>1st</p>",
                        "<p>4th</p>",
                        "<p>3rd</p>"
                    ],
                    options_hi: [
                        "<p>दूसरे</p>",
                        "<p>पहले</p>",
                        "<p>चौथे</p>",
                        "<p>तीसरे</p>"
                    ],
                    solution_en: "<p>46.(d) <strong>3rd.</strong> Newton\'s first law states that every object will remain at rest or in uniform motion in a straight line unless compelled to change its state by the action of an external force. Newton\'s second law of motion states that F = ma, or net force is equal to mass times acceleration.</p>",
                    solution_hi: "<p>46.(d) <strong>तीसरे</strong>। न्यूटन का पहला नियम कहता है कि प्रत्येक वस्तु स्थिर अवस्था में या सीधी रेखा में एकसमान गति में रहेगी जब तक कि किसी बाहरी बल की क्रिया द्वारा उसे अपनी अवस्था बदलने के लिए मजबूर न किया जाए। न्यूटन के गति के दूसरे नियम में कहा गया है कि F = ma, या कुल बल, द्रव्यमान गुणा त्वरण के बराबर होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. What is the chemical formula for sodium bicarbonate ?</p>",
                    question_hi: "<p>47. सोडियम बाइकार्बोनेट (sodium bicarbonate) का रासायनिक सूत्र क्या है?</p>",
                    options_en: [
                        "<p>NaCl</p>",
                        "<p>Na<sub>2</sub>CO<sub>3</sub></p>",
                        "<p>NaHCO<sub>3</sub></p>",
                        "<p>NaOH</p>"
                    ],
                    options_hi: [
                        "<p>NaCl</p>",
                        "<p>Na<sub>2</sub>CO<sub>3</sub></p>",
                        "<p>NaHCO<sub>3</sub></p>",
                        "<p>NaOH</p>"
                    ],
                    solution_en: "<p>47.(c) <strong>NaHCO<sub>3</sub>. </strong>Sodium bicarbonate (NaHCO<sub>3</sub>), or baking soda, is used in baking, cleaning, and as an antacid. It reacts with acids to release carbon dioxide, helping dough rise. Sodium chloride (NaCl) is common table salt, while sodium carbonate (Na<sub>2</sub>CO<sub>3</sub>) is soda ash, and sodium hydroxide (NaOH) is a strong base known as caustic soda.</p>",
                    solution_hi: "<p>47.(c) <strong>NaHCO<sub>3</sub></strong>. सोडियम बाइकार्बोनेट (NaHCO<sub>3</sub>), या बेकिंग सोडा, बेकिंग, सफाई और एंटासिड के रूप में उपयोग किया जाता है। यह अम्ल के साथ अभिक्रिया करके कार्बन डाइऑक्साइड मुक्त करता है, जिससे आटा फूलने में मदद मिलती है। सोडियम क्लोराइड (NaCl) सामान्य टेबल साल्ट है, जबकि सोडियम कार्बोनेट (Na<sub>2</sub>CO<sub>3</sub>) सोडा ऐश है, और सोडियम हाइड्रॉक्साइड (NaOH) एक प्रबल क्षार है जिसे कास्टिक सोडा के रूप में जाना जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "48. The iodine value of oil and fat indicates:",
                    question_hi: "48. तेल और वसा का आयोडीन मान _______ दर्शाता है।",
                    options_en: [
                        " degree of unsaturation ",
                        " degree of saturation  ",
                        " number of iodine atoms present ",
                        " number of C atoms present"
                    ],
                    options_hi: [
                        " असंतृप्ति कोटि (degree of unsaturation)",
                        " संतृप्ति कोटि (degree of saturation) ",
                        " उपस्थित आयोडीन परमाणुओं की संख्या (number of iodine atoms present) ",
                        " उपस्थित C परमाणुओं की संख्या (number of C atoms present)"
                    ],
                    solution_en: "<p>48.(a) <strong>degree of unsaturation.</strong> Iodine value or number is the number of grams of iodine consumed by 100g of fat. A higher iodine value indicates a higher degree of unsaturation.</p>",
                    solution_hi: "<p>48.(a) <strong>असंतृप्ति कोटि</strong> (degree of unsaturation)। आयोडीन मान या संख्या 100 ग्राम वसा द्वारा प्रयुक्त आयोडीन के ग्रामो की संख्या है। उच्च आयोडीन मान असंतृप्ति की उच्च कोटि को इंगित करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Identify the device used to change the resistance in an electric circuit.</p>",
                    question_hi: "<p>49. विद्युत परिपथ में प्रतिरोध को बदलने के लिए प्रयुक्त उपकरण की पहचान कीजिए।</p>",
                    options_en: [
                        "<p>Ammeter</p>",
                        "<p>Rheostat</p>",
                        "<p>Conductor</p>",
                        "<p>Voltmeter</p>"
                    ],
                    options_hi: [
                        "<p>ऐमीटर (Ammeter)</p>",
                        "<p>धारा नियंत्रक (Rheostat)</p>",
                        "<p>विद्युत चालक (Conductor)</p>",
                        "<p>वाल्टमीटर (Voltmeter)</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>Rheostat</strong>. Ammeter is a device used to measure either alternating or direct current. Conductors are those substances through which electricity can flow are called conductors. Insulators are those substances through which electricity cannot flow easily. The voltmeter is a measurement device used to measure voltage in electrical circuits or electronic devices.</p>",
                    solution_hi: "<p>49.(b) <strong>धारा नियंत्रक</strong>। एमीटर एक उपकरण है जिसका उपयोग प्रत्यावर्ती या दिष्ट धारा को मापने के लिए किया जाता है। चालक वे पदार्थ होते हैं जिनके माध्यम से विद्युत प्रवाहित हो सकती है, उन्हें चालक कहते हैं। इन्सुलेटर वे पदार्थ होते हैं जिनके माध्यम से विद्युत आसानी से प्रवाहित नहीं हो सकती। वोल्टमीटर एक मापक उपकरण है जिसका उपयोग विद्युत परिपथों या इलेक्ट्रॉनिक उपकरणों में वोल्टेज मापने के लिए किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. What is the chemical reaction that occurs when sugar is heated and turns into a caramel syrup?</p>",
                    question_hi: "<p>50. शर्करा को गर्म करने पर उसका कैरामेल सिरप (caramel syrup) में बदल जाना, कौन-सी रासायनिक अभिक्रिया है ?</p>",
                    options_en: [
                        "<p>Maillard reaction</p>",
                        "<p>Precipitation</p>",
                        "<p>Caramelisation</p>",
                        "<p>Neutralisation</p>"
                    ],
                    options_hi: [
                        "<p>मेलार्ड अभिक्रिया (Maillard reaction)</p>",
                        "<p>अवक्षेपण (Precipitation)</p>",
                        "<p>शर्करादहन (Caramelisation)</p>",
                        "<p>उदासीनीकरण (Neutralisation)</p>"
                    ],
                    solution_en: "<p>50.(c) <strong>Caramelisation</strong>. Maillard Reaction: A chemical reaction between amino acids and reducing sugars. It occurs during cooking, responsible for browning of food and creating complex flavors. Precipitation is any liquid or frozen water that forms in the atmosphere and falls back to the earth. It comes in many forms, like rain, sleet, and snow. A neutralization reaction is when an acid and a base react to form water and a salt.</p>",
                    solution_hi: "<p>50.(c) <strong>शर्करादहन </strong>(Caramelisation)। मेलार्ड अभिक्रिया: अमीनो अम्ल और अवशोषक शर्करा के बीच एक रासायनिक अभिक्रिया है। यह खाना पकाने के दौरान होती है, जो खाद्य पदार्थ को भूरा करने और जटिल स्वाद बनाने के निर्माण के लिए जिम्मेदार होती है। वर्षा कोई द्रव या जमे हुए जल है जो वायुमंडल में बनता है और वापस धरती पर गिरता है। यह कई रूपों में आता है, जैसे बारिश, ओले और बर्फ। एक उदासीनीकरण अभिक्रिया तब होती है जब एक अम्ल और एक क्षार अभिक्रिया करके जल एवं लवण बनाते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A class of 30 students appeared in a test. The average score of 12 students is 62, and that of the rest is 74. What is the average score of the class ?</p>",
                    question_hi: "<p>51. 30 छात्रों वाली एक कक्षा के सभी छात्रों ने एक टेस्ट दिया। 12 छात्रों का औसत स्कोर 62 है और बाकी छात्रों का औसत स्कोर 74 है। कक्षा का औसत स्कोर ज्ञात करें।</p>",
                    options_en: [
                        "<p>70.2</p>",
                        "<p>69.2</p>",
                        "<p>68.2</p>",
                        "<p>67.2</p>"
                    ],
                    options_hi: [
                        "<p>70.2</p>",
                        "<p>69.2</p>",
                        "<p>68.2</p>",
                        "<p>67.2</p>"
                    ],
                    solution_en: "<p>51.(b) <br>No of students&nbsp; &nbsp;12&nbsp; :&nbsp; 18 = 2&nbsp; &nbsp;:&nbsp; &nbsp;3<br>Average score&nbsp; &nbsp;62&nbsp; &nbsp; &nbsp; 62+12<br>Average score of the class = 62 + <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 62 + 7.2 = 69.2</p>",
                    solution_hi: "<p>51.(b) <br>छात्रों की संख्या&nbsp; &nbsp; 12&nbsp; :&nbsp; 18 = 2&nbsp; &nbsp;:&nbsp; &nbsp;3<br>औसत स्कोर&nbsp; &nbsp; &nbsp; &nbsp; 62&nbsp; &nbsp; &nbsp; 62 +12<br>कक्षा का औसत स्कोर = 62 + <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 62 + 7.2 = 69.2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. If tan4&theta;. tan6&theta; = 1, where 6&theta; is an acute angle, then find the value of Cot5&theta;.</p>",
                    question_hi: "<p>52. यदि tan4&theta;. tan6&theta; = 1 है, जहाँ 6&theta; एक न्यूनकोण है, तो Cot5&theta; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mo>-</mo><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mo>-</mo><mn>1</mn></math></p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mo>-</mo><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mo>-</mo><mn>1</mn></math></p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>52.(d) <strong>Given:-</strong> tan 4&theta;. tan6&theta; = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>tan</mi><mn>6</mn><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = cot 6&theta;<br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = tan(90&deg; - 6&theta;)<br><math display=\"inline\"><mo>&#8658;</mo></math> 4&theta; = 90&deg; - 6&theta; <br><math display=\"inline\"><mo>&#8658;</mo></math> 10&theta; = 90&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &theta; = 9&deg;<br>Put the value of &theta; in Cot5&theta;&nbsp;<br>cot 45&deg; = 1</p>",
                    solution_hi: "<p>52.(d)<strong> दिया गया है:- </strong>tan 4&theta;. tan6&theta; = 1<br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>tan</mi><mn>6</mn><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = cot 6&theta;<br><math display=\"inline\"><mo>&#8658;</mo></math> tan 4&theta; = tan(90&deg; - 6&theta;)<br><math display=\"inline\"><mo>&#8658;</mo></math> 4&theta; = 90&deg; - 6&theta; <br><math display=\"inline\"><mo>&#8658;</mo></math> 10&theta; = 90&deg;<br><math display=\"inline\"><mo>&#8658;</mo></math> &theta; = 9&deg;<br>&theta; का मान Cot5&theta; रखने पर <br>cot 45&deg; = 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. A seller decreased the selling price of each item from ₹5,000 to ₹4,680, by which his loss percentage increased by 4%. If he has to get 4% profit, then the selling price of the item should be:</p>",
                    question_hi: "<p>53. एक विक्रेता ने अपनी प्रत्येक वस्तु का विक्रय मूल्य ₹5,000 से घटाकर ₹4,680 कर दिया, जिससे उसके हानि प्रतिशत में 4% की वृद्धि हुई। यदि उसे 4% लाभ अर्जित करना है, तो वस्तु का विक्रय मूल्य क्या होना चाहिए?</p>",
                    options_en: [
                        "<p>₹8,840</p>",
                        "<p>₹8,320</p>",
                        "<p>₹7,800</p>",
                        "<p>₹7,280</p>"
                    ],
                    options_hi: [
                        "<p>₹8,840</p>",
                        "<p>₹8,320</p>",
                        "<p>₹7,800</p>",
                        "<p>₹7,280</p>"
                    ],
                    solution_en: "<p>53.(b)&nbsp;According to the question,<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mo>-</mo><mn>5000</mn></mrow><mi>CP</mi></mfrac></math>) &times; 100 + 4 = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mo>-</mo><mn>4680</mn></mrow><mi>CP</mi></mfrac></math>) &times; 100&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi>CP</mi><mo>-</mo><mn>500000</mn><mo>+</mo><mn>4</mn><mi>CP</mi></mrow><mi>CP</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi>CP</mi><mo>-</mo><mn>468000</mn></mrow><mi>CP</mi></mfrac></math><br>100CP - 500000 + 4CP = 100 CP - 468000 <br>4CP = 32000 <br>CP = ₹8000<br>Now, if he get 4% profit ,then SP = 8000 &times; <math display=\"inline\"><mfrac><mrow><mn>104</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹8320<br><strong>Short trick :- </strong>4% = (5000 - 4680) = ₹320<br>100% = ₹8000<br>Then, required SP = 8000 &times; <math display=\"inline\"><mfrac><mrow><mn>104</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹8320</p>",
                    solution_hi: "<p>53.(b)&nbsp;प्रश्न के अनुसार,<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mo>-</mo><mn>5000</mn></mrow><mi>CP</mi></mfrac></math>) &times; 100 + 4 = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>CP</mi><mo>-</mo><mn>4680</mn></mrow><mi>CP</mi></mfrac></math>) &times; 100&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi>CP</mi><mo>-</mo><mn>500000</mn><mo>+</mo><mn>4</mn><mi>CP</mi></mrow><mi>CP</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi>CP</mi><mo>-</mo><mn>468000</mn></mrow><mi>CP</mi></mfrac></math><br>100CP - 500000 + 4CP = 100 CP - 468000 <br>4CP = 32000 <br>CP = ₹8000<br>अब, यदि उसे 4% लाभ मिलता है, तो SP = 8000 &times; <math display=\"inline\"><mfrac><mrow><mn>104</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹8320<br><strong>शॉर्ट ट्रिक :- </strong>4% = (5000 - 4680) = ₹320<br>100% = ₹8000<br>तब, आवश्यक विक्रय मूल्य = 8000 &times; <math display=\"inline\"><mfrac><mrow><mn>104</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹8320</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. A&rsquo;s income is equal to 125% of the income of B, while B&rsquo;s savings are 120% of the savings of A. If the expenditure of B is 75% of the expenditure of A, then how much are B&rsquo;s savings as a percentage of her expenditure?</p>",
                    question_hi: "<p>54. A की आय B की आय के 125% के बराबर है, जब कि B की बचत A की बचत का 120% है। यदि B का&nbsp;व्यय A के व्यय का 75% है, तो B की बचत उसके व्यय के प्रतिशत के रूप में कितनी है?</p>",
                    options_en: [
                        "<p>20%</p>",
                        "<p>22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                        "<p>16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>18<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>20%</p>",
                        "<p>22<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>%</p>",
                        "<p>16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                        "<p>18<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>54.(a)&nbsp;Ratio -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;A&nbsp; &nbsp; :&nbsp; &nbsp;B<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Expendi. - 100y : 75y<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Savings&nbsp; - 100<math display=\"inline\"><mi>x</mi></math> : 120x<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;-------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Income&nbsp; -&nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; :&nbsp; 4<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>100</mn><mi mathvariant=\"normal\">y</mi></mrow><mrow><mn>120</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>75</mn><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> <br>400x&nbsp;+ 400y = 600x + 375y<br>25y = 200x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>200</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math> <br>B&rsquo;s savings = 120x&nbsp;= 120 &times; 1 = 120<br>B&rsquo;s expenditure = 75y&nbsp;= 75 &times; 8 = 600<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>600</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>54.(a)&nbsp;अनुपात -&nbsp; &nbsp; A&nbsp; &nbsp; :&nbsp; &nbsp;B<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;व्यय - 100y :&nbsp; 75y<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; बचत - 100x&nbsp;: 120x<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ----------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; आय -&nbsp; &nbsp; &nbsp; 5&nbsp; :&nbsp; &nbsp; 4<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>100</mn><mi mathvariant=\"normal\">y</mi></mrow><mrow><mn>120</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>75</mn><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>400<math display=\"inline\"><mi>x</mi></math> + 400y = 600x + 375y<br>25y = 200x<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>200</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>8</mn></mfrac></math><br>B की बचत = 120x&nbsp;= 120 &times; 1 = 120<br>B का व्यय = 75y&nbsp;= 75 &times; 8 = 600<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>600</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55.Which of the following numbers will completely divide 4<sup>12</sup> + 4<sup>13</sup> + 4<sup>14</sup> + 4<sup>15</sup> ?</p>",
                    question_hi: "<p>55. निम्नलिखित में से कौन-सी संख्या 4<sup>12</sup> + 4<sup>13</sup> + 4<sup>14</sup> + 4<sup>15</sup> को पूर्णत: विभाजित करेगी?</p>",
                    options_en: [
                        "<p>17</p>",
                        "<p>11</p>",
                        "<p>3</p>",
                        "<p>7</p>"
                    ],
                    options_hi: [
                        "<p>17</p>",
                        "<p>11</p>",
                        "<p>3</p>",
                        "<p>7</p>"
                    ],
                    solution_en: "<p>55.(a)&nbsp;4<sup>12</sup> + 4<sup>13</sup> + 4<sup>14</sup> + 4<sup>15</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> 4<sup>12</sup> ( 1 + 4<sup>1</sup> + 4<sup>2</sup> + 4<sup>3</sup> )<br><math display=\"inline\"><mo>&#8658;</mo></math> 4<sup>12 </sup>( 1 + 4 + 16 + 64)<br><math display=\"inline\"><mo>&#8658;</mo></math> 4<sup>12</sup> (85)<br>85 is divisible by 17 and hence 17 is the answer.</p>",
                    solution_hi: "<p>55.(a) 4<sup>12</sup> + 4<sup>13</sup> + 4<sup>14</sup> + 4<sup>15</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> 4<sup>12</sup> ( 1 + 4<sup>1</sup> + 4<sup>2</sup> + 4<sup>3</sup> )<br><math display=\"inline\"><mo>&#8658;</mo></math> 4<sup>12 </sup>( 1 + 4 + 16 + 64)<br><math display=\"inline\"><mo>&#8658;</mo></math> 4<sup>12</sup> (85)<br>85, 17 से विभाज्य है और इसलिए उत्तर 17 है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. The monthly wages (in ₹) of three mess workers, Rahul, Sunil and Vipin, of a boy&rsquo;s hostel for three months are given in the following table. Study the table carefully and answer the question given below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495627714.png\" alt=\"rId40\" width=\"300\" height=\"86\"> <br>In November, the wages of Sunil is what percentage of the wages of Rahul ? (Correct up to two decimal places.)</p>",
                    question_hi: "<p>56. निम्नलिखित तालिका में, लड़कों के एक छात्रावास के भोजनालय के तीन कर्मचारियों, राहुल, सुनील और विपिन का तीन महीने का मासिक वेतन (₹ में) दिया गया है। तालिका का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495627814.png\" alt=\"rId41\" width=\"250\" height=\"106\"> <br>नवंबर में सुनील का वेतन, राहुल के वेतन का कितने प्रतिशत है ? (दो दशमलव स्थान तक पूर्णांकित)</p>",
                    options_en: [
                        "<p>90.23%</p>",
                        "<p>89.30%</p>",
                        "<p>88.23%</p>",
                        "<p>91.30%</p>"
                    ],
                    options_hi: [
                        "<p>90.23%</p>",
                        "<p>89.30%</p>",
                        "<p>88.23%</p>",
                        "<p>91.30%</p>"
                    ],
                    solution_en: "<p>56.(d) <br>Required % = <math display=\"inline\"><mfrac><mrow><mn>10500</mn></mrow><mrow><mn>11500</mn></mrow></mfrac></math> &times; 100 = 91.30%</p>",
                    solution_hi: "<p>56.(d) <br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>10500</mn></mrow><mrow><mn>11500</mn></mrow></mfrac></math> &times; 100 = 91.30%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The value of 29 - [(48 &divide; 12) + 2 &times; (8 &divide; 4 + 4)] is equal to:</p>",
                    question_hi: "<p>57. 29 - [(48 &divide; 12) + 2 &times; (8 &divide; 4 + 4)] का मान निम्न में से किसके बराबर होगा ?</p>",
                    options_en: [
                        "<p>13</p>",
                        "<p>15</p>",
                        "<p>21</p>",
                        "<p>19</p>"
                    ],
                    options_hi: [
                        "<p>13</p>",
                        "<p>15</p>",
                        "<p>21</p>",
                        "<p>19</p>"
                    ],
                    solution_en: "<p>57.(a)<br>29 - [(48 &divide; 12) + 2 &times; (8 &divide; 4 + 4)]<br>29 - [4 + 2 &times; (2 + 4)]<br>29 - [4 + 12] = 13</p>",
                    solution_hi: "<p>57.(a)<br>29 - [(48 &divide; 12) + 2 &times; (8 &divide; 4 + 4)]<br>29 - [4 + 2 &times; (2 + 4)]<br>29 - [4 + 12] = 13</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. The average of the squares of the first 46 natural numbers is</p>",
                    question_hi: "<p>58. प्रथम 46 प्राकृतिक संख्याओं के वर्गों का औसत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>729.5</p>",
                        "<p>728.5</p>",
                        "<p>727.5</p>",
                        "<p>730.5</p>"
                    ],
                    options_hi: [
                        "<p>729.5</p>",
                        "<p>728.5</p>",
                        "<p>727.5</p>",
                        "<p>730.5</p>"
                    ],
                    solution_en: "<p>58.(b) <br>Sum of square of n natural numbers = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">n</mi><mo>(</mo><mi mathvariant=\"normal\">n</mi><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>6</mn></mfrac></math><br>Sum of square of 46 natural numbers = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>46</mn><mo>(</mo><mn>46</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>46</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>6</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>46</mn><mo>&#215;</mo><mn>47</mn><mo>&#215;</mo><mn>93</mn></mrow><mn>6</mn></mfrac></math> = 33511<br>Required average = <math display=\"inline\"><mfrac><mrow><mn>33511</mn></mrow><mrow><mn>46</mn></mrow></mfrac></math> = 728.5</p>",
                    solution_hi: "<p>58.(b) <br>n प्राकृत संख्याओं के वर्ग का योग = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">n</mi><mo>(</mo><mi mathvariant=\"normal\">n</mi><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>6</mn></mfrac></math><br>46 प्राकृत संख्याओं के वर्ग का योग = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>46</mn><mo>(</mo><mn>46</mn><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>46</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>6</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>46</mn><mo>&#215;</mo><mn>47</mn><mo>&#215;</mo><mn>93</mn></mrow><mn>6</mn></mfrac></math>&nbsp;= 33511<br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>33511</mn></mrow><mrow><mn>46</mn></mrow></mfrac></math>&nbsp;= 728.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A drink is made by mixing water and juice in the ratio 9 : 7. If x litres of water and 2x litres of juice are mixed in 160 litres of drink, then the new ratio becomes 13 : 15. The quantity of drink (in litres) is:</p>",
                    question_hi: "<p>59. पानी और जूस को 9 : 7 के अनुपात में मिलाकर एक पेय बनाया जाता है। यदि 160 लीटर पेय में x लीटर पानी और 2x लीटर जूस मिलाया जाता है, तो नया अनुपात 13 : 15 हो जाता है। पेय की मात्रा (लीटर में) ज्ञात करें।</p>",
                    options_en: [
                        "<p>240</p>",
                        "<p>280</p>",
                        "<p>300</p>",
                        "<p>260</p>"
                    ],
                    options_hi: [
                        "<p>240</p>",
                        "<p>280</p>",
                        "<p>300</p>",
                        "<p>260</p>"
                    ],
                    solution_en: "<p>59.(b)<br>Mixture <math display=\"inline\"><mo>&#8594;</mo></math> Water : Juice<br>160 liter <math display=\"inline\"><mo>&#8594;</mo></math> 90 lit. : 70 lit.<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8595;</mo></math>+x lit. : &darr;+2x lit.<br>&nbsp; &nbsp; Final <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;13&nbsp; &nbsp; :&nbsp; &nbsp; 15<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>70</mn><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>15</mn></mfrac></math><br>1350 + 15x&nbsp;= 910 + 26x<br>11x&nbsp;= 440<br>x = 40<br>Water + juice added = x + 2x&nbsp;= 40 + 80 = 120 litres<br>Total quantity of drink = 160 + 120 = 280 litres</p>",
                    solution_hi: "<p>59.(b)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; मिश्रण जल :&nbsp; &nbsp;रस<br>160 लीटर <math display=\"inline\"><mo>&#8594;</mo></math> 90 लीटर : 70 लीटर <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; <math display=\"inline\"><mo>&#8595;</mo></math>+x लीटर : &darr;+2x लीटर <br>&nbsp; &nbsp; &nbsp; अंतिम <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 13&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;15<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>+</mo><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>70</mn><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>15</mn></mfrac></math><br>1350 + 15x&nbsp;= 910 + 26x<br>11x&nbsp;= 440<br>x = 40<br>मिलाया गया पानी + रस = x + 2x = 40 + 80 = 120 लीटर <br>पेय की कुल मात्रा = 160 + 120 = 280 लीटर</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. if 7 tan&theta; = 3, and &theta; is an acute angle , then <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>sin&#952;</mi><mo>-</mo><mi>cos&#952;</mi></mrow><mrow><mn>5</mn><mi>sin&#952;</mi><mo>+</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math> is equal to :</p>",
                    question_hi: "<p>60. यदि 7tan&theta; = 3 और &theta; एक न्यून कोण है, तो&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>sin&#952;</mi><mo>-</mo><mi>cos&#952;</mi></mrow><mrow><mn>5</mn><mi>sin&#952;</mi><mo>+</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math>का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>60.(a) <br>7tan &theta; = 3 &rArr; tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math><br>Now, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>sin&#952;</mi><mo>-</mo><mi>cos&#952;</mi></mrow><mrow><mn>5</mn><mi>sin&#952;</mi><mo>+</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>tan&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mn>5</mn><mi>tan&#952;</mi><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>7</mn></mfrac></mstyle><mo>-</mo><mn>1</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><mn>7</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>29</mn><mn>7</mn></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>29</mn></mfrac></math></p>",
                    solution_hi: "<p>60.(a) <br>7 tan&theta; = 3 &rArr; tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math><br>अब , <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>sin&#952;</mi><mo>-</mo><mi>cos&#952;</mi></mrow><mrow><mn>5</mn><mi>sin&#952;</mi><mo>+</mo><mn>2</mn><mi>cos&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>tan&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mn>5</mn><mi>tan&#952;</mi><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>7</mn></mfrac></mstyle><mo>-</mo><mn>1</mn></mrow><mrow><mn>5</mn><mo>&#215;</mo><mfrac><mn>3</mn><mn>7</mn></mfrac><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><mn>7</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>29</mn><mn>7</mn></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>29</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A solid metallic cylinder of base radius 6 cm and height 10 cm is melted to form small cylinders, each of height 2.5 cm and base radius 2 cm. Find the number of small cylinders.</p>",
                    question_hi: "<p>61. 6 cm आधार त्रिज्या और 10 cm ऊंचाई वाले धातु के एक ठोस बेलन को पिघलाकर छोटे बेलन बनाए जाते हैं, जिनमें से प्रत्येक की ऊंचाई 2.5 cm और आधार त्रिज्या 2 cm है। छोटे बेलनों की संख्या ज्ञात करें।</p>",
                    options_en: [
                        "<p>36</p>",
                        "<p>32</p>",
                        "<p>80</p>",
                        "<p>360</p>"
                    ],
                    options_hi: [
                        "<p>36</p>",
                        "<p>32</p>",
                        "<p>80</p>",
                        "<p>360</p>"
                    ],
                    solution_en: "<p>61.(a)&nbsp;Let the number of small cylinder = n<br>Volume of the cylinder = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;r</mi><mn>2</mn></msup><mi mathvariant=\"normal\">h</mi></math><br>Volume of bigger cylinder = n &times; volume of small cylinder <br>6 &times; 6 &times; 10 = n &times; 2 &times; 2 &times; 2.5<br>n = 36</p>",
                    solution_hi: "<p>61.(a)&nbsp;माना छोटे बेलनों की संख्या = n<br>बेलन का आयतन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#960;r</mi><mn>2</mn></msup><mi mathvariant=\"normal\">h</mi></math><br>बडे बेलन का आयतन = n &times; छोटे बेलन का आयतन<br>6 &times; 6 &times; 10 = n &times; 2 &times; 2 &times; 2.5<br>n = 36</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Find the value of the following expression.<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin&#952;</mi></mrow></mfrac></msqrt></math></p>",
                    question_hi: "<p>62. निम्नलिखित व्यंजक का मान ज्ञात कीजिए।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin&#952;</mi></mrow></mfrac></msqrt></math></p>",
                    options_en: [
                        "<p>sec&theta; + tan&theta;</p>",
                        "<p>cosec&theta; + tan&theta;</p>",
                        "<p>cosec&theta; + cot&theta;</p>",
                        "<p>sec&theta; + cot&theta;</p>"
                    ],
                    options_hi: [
                        "<p>sec&theta; + tan&theta;</p>",
                        "<p>cosec&theta; + tan&theta;</p>",
                        "<p>cosec&theta; + cot&theta;</p>",
                        "<p>sec&theta; + cot&theta;</p>"
                    ],
                    solution_en: "<p>62.(a) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin&#952;</mi></mrow></mfrac></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mstyle displaystyle=\"true\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi>&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></mstyle><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi>&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></mstyle></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi>&#952;</mi></mrow></mfenced><mn>2</mn></msup><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></msqrt></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow><mi>cos&#952;</mi></mfrac></math> = sec&theta; + tan&theta;</p>",
                    solution_hi: "<p>62.(a) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin&#952;</mi></mrow></mfrac></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mstyle displaystyle=\"true\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi>&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></mstyle><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi>&#952;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin</mi><mi>&#952;</mi></mrow></mfrac></mstyle></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><msup><mfenced><mrow><mn>1</mn><mo>+</mo><mi>sin</mi><mi>&#952;</mi></mrow></mfenced><mn>2</mn></msup><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></msqrt></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#952;</mi></mrow><mi>cos&#952;</mi></mfrac></math> = sec&theta; + tan&theta;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If m = a secA and y = b tan A then find the value of b<sup>2</sup>m<sup>2</sup> - a<sup>2</sup>y<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow></mfrac></math> + cos<sup>2</sup>A.</p>",
                    question_hi: "<p>63. यदि m = a secA और y = b tanA है, तो b<sup>2</sup>m<sup>2</sup> - a<sup>2</sup>y<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow></mfrac></math> + cos<sup>2</sup>A का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>a<sup>2</sup>b<sup>2</sup></p>",
                        "<p>1 - a<sup>2</sup>b<sup>2</sup></p>",
                        "<p>a<sup>2</sup>b<sup>2</sup> + 2</p>",
                        "<p>a<sup>2</sup>b<sup>2</sup> + 1</p>"
                    ],
                    options_hi: [
                        "<p>a<sup>2</sup>b<sup>2</sup></p>",
                        "<p>1 - a<sup>2</sup>b<sup>2</sup></p>",
                        "<p>a<sup>2</sup>b<sup>2</sup>&nbsp;+ 2</p>",
                        "<p>a<sup>2</sup>b<sup>2</sup> + 1</p>"
                    ],
                    solution_en: "<p>63.(d) <strong>Given:</strong> m = a secA and y = b tan A<br>b<sup>2</sup>m<sup>2</sup> - a<sup>2</sup>y<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow></mfrac></math> + cos<sup>2</sup>A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></math> - a<sup>2</sup>b<sup>2</sup>tan<sup>2</sup>A + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + cos<sup>2</sup> A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math> (sec<sup>2</sup>A - tan<sup>2</sup>A ) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + cos<sup>2</sup>A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math>(1) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>Sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&#215;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + cos<sup>2</sup>A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math>&nbsp;+ 1</p>",
                    solution_hi: "<p>63.(d)&nbsp;<strong>दिया गया: </strong>m = a secA और y = b tan A<br>b<sup>2</sup>m<sup>2</sup> - a<sup>2</sup>y<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">y</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">m</mi><mn>2</mn></msup></mrow></mfrac></math> + cos<sup>2</sup>A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></math> - a<sup>2</sup>b<sup>2</sup>tan<sup>2</sup>A + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + cos<sup>2</sup> A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math> (sec<sup>2</sup>A - tan<sup>2</sup>A ) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + cos<sup>2</sup>A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math>(1) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>Sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi><mo>&#215;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + cos<sup>2</sup>A<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">b</mi><mn>2</mn></msup></math> + 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A car covers the distance from Delhi to Gurugram at 60 km/h and returns at a uniform speed of 40 km/h. The average speed of the car during the whole journey is:</p>",
                    question_hi: "<p>64. एक कार दिल्ली से गुरुग्राम की दूरी 60 km/h की चाल से तय करती है और 40 km/h की समान चाल से वापस आती है। पूरी यात्रा के दौरान कार की औसत चाल क्&zwj;या है ?</p>",
                    options_en: [
                        "<p>40 km/h</p>",
                        "<p>60 km/h</p>",
                        "<p>50 km/h</p>",
                        "<p>48 km/h</p>"
                    ],
                    options_hi: [
                        "<p>40 km/h</p>",
                        "<p>60 km/h</p>",
                        "<p>50 km/h</p>",
                        "<p>48 km/h</p>"
                    ],
                    solution_en: "<p>64.(d)&nbsp;Let the distance be 120 km i.e. LCM of (60, 40)<br>average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mfrac><mn>120</mn><mn>60</mn></mfrac><mo>+</mo><mfrac><mn>120</mn><mn>40</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>5</mn></mfrac></math> = 48 km/hr</p>",
                    solution_hi: "<p>64.(d)&nbsp;मान लीजिए दूरी 120 किमी ,यानी (60, 40) का LCM <br>औसत चाल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>120</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mfrac><mn>120</mn><mn>60</mn></mfrac><mo>+</mo><mfrac><mn>120</mn><mn>40</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>240</mn><mn>5</mn></mfrac></math>&nbsp;= 48 km/hr</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. The radius of a roller is 49 cm and its length is 200 cm. It takes 700 complete revolutions to move once over to level a playground. Find the area of the playground (Use <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>.)</p>",
                    question_hi: "<p>65. एक रोलर की त्रिज्या 49 cm है और इसकी लंबाई 200 cm है। एक खेल के मैदान को समतल करने के लिए एक बार चलने के लिए 700 पूर्ण चक्कर लगाने पड़ते हैं। खेल के मैदान का क्षेत्रफल ज्ञात कीजिए। ( <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> का प्रयोग कीजिए)</p>",
                    options_en: [
                        "<p>4312 &times;&nbsp;10<sup>4</sup> cm&sup2;</p>",
                        "<p>4312 &times;&nbsp;10<sup>2</sup> cm&sup2;</p>",
                        "<p>4322 &times;&nbsp;10<sup>4</sup> cm&sup2;</p>",
                        "<p>4312 &times;&nbsp;10<sup>3</sup> cm&sup2;</p>"
                    ],
                    options_hi: [
                        "<p>4312 &times;&nbsp;10<sup>4</sup> cm&sup2;</p>",
                        "<p>4312 &times;&nbsp;10<sup>2</sup> cm&sup2;</p>",
                        "<p>4322 &times;&nbsp;10<sup>4</sup> cm&sup2;</p>",
                        "<p>4312 <math display=\"inline\"><mo>&#215;</mo></math> 103 cm&sup2;</p>"
                    ],
                    solution_en: "<p>65.(a)&nbsp;Area of the playground = 700 &times; CSA of cylinder<br>= 700 &times; 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;rh</mi></math><br>= 700 &times; 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 49 &times; 200<br>= 4312 <math display=\"inline\"><mo>&#215;</mo></math> 10<sup>4</sup> cm&sup2;</p>",
                    solution_hi: "<p>65.(a)&nbsp;खेल के मैदान का क्षेत्रफल = 700 &times; बेलन का वक्रपृष्ठीय क्षेत्रफल<br>= 700 &times; 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#960;rh</mi></math><br>= 700 &times; 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 49 &times; 200<br>= 4312 <math display=\"inline\"><mo>&#215;</mo></math> 10<sup>4</sup> cm&sup2;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If p = 106, q = 105 and r = 104, then value of p&sup2; + q&sup2; + r&sup2; - pq - qr - rp is:</p>",
                    question_hi: "<p>66. यदि p = 106, q = 105 और r = 104 हो, तो p&sup2; + q&sup2; + r&sup2; - pq - qr - rp का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>66.(d) <strong>Formula used :</strong>&nbsp;a<sup>2</sup> + b<sup>2 </sup>+ c<sup>2 </sup>- ab - bc - ca = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>[(a - b)<sup>2 </sup>+ (b - c)<sup>2 </sup>+ (c - a)<sup>2</sup>]<br>Then, p&sup2; + q&sup2; + r&sup2; - pq - qr - rp = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mfenced open=\"[\" close=\"]\" separators=\"|\"><mrow><mo>(</mo><mn>106</mn><mo>-</mo><mn>105</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>105</mn><mo>-</mo><mn>104</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>106</mn><mo>-</mo><mn>104</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfenced></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mfenced open=\"[\" close=\"]\" separators=\"|\"><mrow><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfenced></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 6 = 3</p>",
                    solution_hi: "<p>66.(d) <strong>फार्मूला</strong><strong>:</strong> a<sup>2</sup> + b<sup>2 </sup>+ c<sup>2 </sup>- ab - bc - ca = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>[(a - b)<sup>2 </sup>+ (b - c)<sup>2 </sup>+ (c - a)<sup>2</sup>]<br>तो , p&sup2; + q&sup2; + r&sup2; - pq - qr - rp = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mfenced open=\"[\" close=\"]\" separators=\"|\"><mrow><mo>(</mo><mn>106</mn><mo>-</mo><mn>105</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>105</mn><mo>-</mo><mn>104</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>106</mn><mo>-</mo><mn>104</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfenced></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mfenced open=\"[\" close=\"]\" separators=\"|\"><mrow><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfenced></math> = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 6 = 3</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Pradeep sells two fans at ₹480 each and by doing so he gains 20% on one fan and loses 20% on the other. His loss on the whole (in ₹) is:</p>",
                    question_hi: "<p>67. प्रदीप प्रत्येक ₹480 के मूल्य से दो पंखे बेचता है और ऐसा करके वह एक पंखे पर 20% का लाभ कमाता है और दूसरे पर उसे 20% की हानि होती है। कुल मिलाकर उसे कितनी हानि (₹ में) हुई ?</p>",
                    options_en: [
                        "<p>10</p>",
                        "<p>40</p>",
                        "<p>20</p>",
                        "<p>30</p>"
                    ],
                    options_hi: [
                        "<p>10</p>",
                        "<p>40</p>",
                        "<p>20</p>",
                        "<p>30</p>"
                    ],
                    solution_en: "<p>67.(b)<br>Ratio&nbsp; &nbsp; - CP : SP <br>1st fan&nbsp; -&nbsp; 5&nbsp; :&nbsp; 6 ) &times; 2<br>2nd fan -&nbsp; 5&nbsp; :&nbsp; 4 ) &times; 3<br>------------------------------<br>Final - (10 + 15) : (12 + 12) = 25 : 24<br>Loss = 25 - 24 = 1 units<br>SP of each fan = 12 units<br>12 units = ₹ 480<br>(loss) 1 units = ₹ 40</p>",
                    solution_hi: "<p>67.(b)<br>अनुपात&nbsp; &nbsp; &nbsp; - क्रय मूल्य : विक्रय मूल्य <br>पहला पंखा -&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;6 ) &times; 2<br>दूसरा पंखा -&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;4 ) &times; 3<br>---------------------------------------<br>अंतिम - (10 + 15) : (12 + 12) = 25 : 24<br>हानि = 25 - 24 = 1 इकाई<br>प्रत्येक पंखे का विक्रय मूल्य = 12 इकाई<br>12 इकाई = ₹ 480<br>(हानि) 1 इकाई = ₹ 40</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. In what ratio must a grocer mix two varieties of pulses costing ₹60 and ₹75 per kg, respectively, so as to get a mixture worth ₹65 per kg ?</p>",
                    question_hi: "<p>68. एक पंसारी को क्रमशः ₹60 और ₹75 प्रति किलोग्राम मूल्य वाली दो प्रकार की दालों को किस अनुपात में मिलाना चाहिए, ताकि ₹65 प्रति किलोग्राम मूल्य का मिश्रण प्राप्त हो ?</p>",
                    options_en: [
                        "<p>3 : 4</p>",
                        "<p>2 : 1</p>",
                        "<p>2 : 3</p>",
                        "<p>1 : 2</p>"
                    ],
                    options_hi: [
                        "<p>3 : 4</p>",
                        "<p>2 : 1</p>",
                        "<p>2 : 3</p>",
                        "<p>1 : 2</p>"
                    ],
                    solution_en: "<p>68.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495627981.png\" alt=\"rId42\" width=\"230\" height=\"146\"><br>Required ratio = 2 : 1</p>",
                    solution_hi: "<p>68.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495628123.png\" alt=\"rId43\" width=\"230\" height=\"143\"><br>अभीष्ट अनुपात = 2 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. 30 men working 5 hours a day can do work in 18 days. In how many days will 20 men working 7 hours a day do the same work ?</p>",
                    question_hi: "<p>69. 30 आदमी प्रतिदिन 5 घंटे काम करके एक काम को 18 दिनों में पूरा कर सकते हैं। 20 आदमी प्रतिदिन 7 घंटे काम करके उसी काम को कितने दिनों में पूरा करेंगे?</p>",
                    options_en: [
                        "<p>19<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>16 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>19<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>13<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>16 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p>11<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>69.(a)<br>M<sub>1</sub>D<sub>1</sub>H<sub>1</sub>&nbsp;= M<sub>2</sub>D<sub>2</sub>H<sub>2</sub><br>30 &times; 18 &times; 5 = 20 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub></math> &times; 7<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>18</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>20</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>135</mn><mn>7</mn></mfrac></math> = 19<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> Days</p>",
                    solution_hi: "<p>69.(a)<br>M<sub>1</sub>D<sub>1</sub>H<sub>1</sub>&nbsp;= M<sub>2</sub>D<sub>2</sub>H<sub>2</sub><br>30 &times; 18 &times; 5 = 20 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub></math> &times; 7<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">D</mi><mn>2</mn></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>18</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>20</mn><mo>&#215;</mo><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>135</mn><mn>7</mn></mfrac></math> = 19<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math> दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The marked price of an item is 1.4 times the cost price. In order to earn 23.2% profit, what percent discount can a shopkeeper allow ?</p>",
                    question_hi: "<p>70. किसी वस्तु का अंकित मूल्य, उसके क्रय मूल्य का 1.4 गुना है। 23.2% का लाभ अर्जित करने के लिए एक दुकानदार कितने प्रतिशत की छूट दे सकता है ?</p>",
                    options_en: [
                        "<p>15%</p>",
                        "<p>17%</p>",
                        "<p>13%</p>",
                        "<p>12%</p>"
                    ],
                    options_hi: [
                        "<p>15%</p>",
                        "<p>17%</p>",
                        "<p>13%</p>",
                        "<p>12%</p>"
                    ],
                    solution_en: "<p>70.(d)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MP</mi><mi>CP</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">P</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>-</mo><mi mathvariant=\"normal\">D</mi><mo>%</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>23</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>100</mn><mo>-</mo><mi mathvariant=\"normal\">D</mi><mo>%</mo></mrow></mfrac></math><br>100 - D% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math> &times; 123.2<br>100 - D% = 88<br>D% = 12%</p>",
                    solution_hi: "<p>70.(d)<br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mi>&#2354;&#2366;&#2349;</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>-</mo><mi>&#2331;&#2370;&#2335;</mi><mo>%</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mn>23</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>100</mn><mo>-</mo><mi>&#2331;&#2370;&#2335;</mi><mo>%</mo></mrow></mfrac></math><br>100 - छूट% =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>7</mn></mfrac></math> &times; 123.2<br>100 - छूट% = 88<br>छूट% = 12%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Ramu used to spend 72% of his income. His income has increased by 12%, and he increases his e<math display=\"inline\"><mi>x</mi></math>penditure by 5%. If Ramu earlier saved y and after the increases he now saves x, then what is the value of (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)% ?</p>",
                    question_hi: "<p>71. रामू अपनी आय का 72% खर्च करता है। उसकी आय में 12% की वृद्धि होती है, और वह अपने खर्च में 5% की वृद्धि करता है। यदि रामू पहले \'y बचत करता था और वृद्धि के बाद अब वह \'<math display=\"inline\"><mi>x</mi></math> बचत करता है, तो (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)% का मान क्या है?</p>",
                    options_en: [
                        "<p>25%</p>",
                        "<p>22%</p>",
                        "<p>27%</p>",
                        "<p>30%</p>"
                    ],
                    options_hi: [
                        "<p>25%</p>",
                        "<p>22%</p>",
                        "<p>27%</p>",
                        "<p>30%</p>"
                    ],
                    solution_en: "<p>71.(d)&nbsp;Let the income of the Ramu is 100 units<br>According to the question,<br>Ratio&nbsp; &nbsp; -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;before : after<br>Income -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100 : 112<br>Expenditure -&nbsp; &nbsp; &nbsp; &nbsp; 72 : 75.6<br>Saving -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 28 : 36.4<br>Required value = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)% = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>.</mo><mn>4</mn><mo>-</mo><mn>28</mn></mrow><mn>28</mn></mfrac></math> &times; 100)% = 30%</p>",
                    solution_hi: "<p>71.(d) <br>माना रामू की आय 100 इकाई है<br>प्रश्न के अनुसार,<br>अनुपात - पहले : बाद में<br>आय&nbsp; &nbsp; &nbsp;-&nbsp; 100 : 112<br>व्यय&nbsp; &nbsp; &nbsp;-&nbsp; &nbsp; 72 : 75.6<br>बचत&nbsp; &nbsp; -&nbsp; &nbsp; 28 : 36.4<br>आवश्यक मान = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow><mi mathvariant=\"normal\">y</mi></mfrac></math> &times; 100)% = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36</mn><mo>.</mo><mn>4</mn><mo>-</mo><mn>28</mn></mrow><mn>28</mn></mfrac></math> &times; 100)% = 30%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. What will be the amount due on ₹12,000 in 2 years when the rate of simple interest on&nbsp;successive years is 9% and 10%, respectively ?</p>",
                    question_hi: "<p>72. 2 वर्षों में ₹12,000 पर देय धनराशि क्या होगी जब क्रमिक वर्षों में साधारण ब्याज की दर क्रमशः 9% और 10% है ?</p>",
                    options_en: [
                        "<p>₹14,250</p>",
                        "<p>₹14,350</p>",
                        "<p>₹14,150</p>",
                        "<p>₹14,280</p>"
                    ],
                    options_hi: [
                        "<p>₹14,250</p>",
                        "<p>₹14,350</p>",
                        "<p>₹14,150</p>",
                        "<p>₹14,280</p>"
                    ],
                    solution_en: "<p>72.(d)<br>Amount due = 12000 &times; <math display=\"inline\"><mfrac><mrow><mn>119</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹14,280</p>",
                    solution_hi: "<p>72.(d)<br>देय राशि = 12000 &times; <math display=\"inline\"><mfrac><mrow><mn>119</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹14,280</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A man bought 4 books for ₹50 each, 5 books for ₹60 each and 6 books for ₹70 each. The average cost of a book for the man is (round up to two decimal places):</p>",
                    question_hi: "<p>73. एक व्यक्ति ने ₹50 प्रति पुस्तक के मूल्य से 4 पुस्तकें, ₹60 प्रति पुस्तक के मूल्य से 5 पुस्तकें और ₹70 प्रति पुस्तक के मूल्य से 6 पुस्तकें खरीदीं। व्यक्ति लिए एक पुस्तक का औसत मूल्य क्या है (दशमलव के दो स्थानों तक सन्निकटित)?</p>",
                    options_en: [
                        "<p>₹61.33</p>",
                        "<p>₹50.33</p>",
                        "<p>₹60.33</p>",
                        "<p>₹36.33</p>"
                    ],
                    options_hi: [
                        "<p>₹61.33</p>",
                        "<p>₹50.33</p>",
                        "<p>₹60.33</p>",
                        "<p>₹36.33</p>"
                    ],
                    solution_en: "<p>73.(a)<br>Cost of 4 books = 4 &times; 50 = 200 <br>Cost of 5 books = 5 &times; 60 = 300<br>Cost of 6 books = 6 &times; 70 = 420<br>Average cost = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>+</mo><mn>300</mn><mo>+</mo><mn>420</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>6</mn></mrow></mfrac></math> = 61.33</p>",
                    solution_hi: "<p>73.(a)<br>4 पुस्तकों का मूल्य = 4 &times; 50 = 200 <br>5 पुस्तकों का मूल्य = 5 &times; 60 = 300<br>6 पुस्तकों का मूल्य = 6 &times; 70 = 420<br>औसत लागत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>+</mo><mn>300</mn><mo>+</mo><mn>420</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>6</mn></mrow></mfrac></math>&nbsp;= 61.33</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The given chart shows the production of different types of cars in the year 2021.<br>Study the given chart and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495628297.png\" alt=\"rId44\" width=\"250\" height=\"229\"> <br>If the total production in the year 2021 was 2,00,00,000, then what will be the central angle of the sector representing the production of type D cars in 2021?</p>",
                    question_hi: "<p>74. दिया गया चार्ट वर्ष 2021 में विभिन्न प्रकार की कारों के उत्पादन को दर्शाता है।दिए गए चार्ट का अध्ययन कीजिए और दिए गए प्रश्न का उत्तर दीजिए। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743495628461.png\" alt=\"rId45\" width=\"250\" height=\"228\"> <br>यदि वर्ष 2021 में कुल उत्पादन 2,00,00,000 था, तो 2021 में D किस्म की कारों के उत्पादन को निरूपित करने वाले क्षेत्र का केंद्रीय कोण क्या होगा?</p>",
                    options_en: [
                        "<p>50&deg;</p>",
                        "<p>36&deg;</p>",
                        "<p>42&deg;</p>",
                        "<p>72&deg;</p>"
                    ],
                    options_hi: [
                        "<p>50&deg;</p>",
                        "<p>36&deg;</p>",
                        "<p>42&deg;</p>",
                        "<p>72&deg;</p>"
                    ],
                    solution_en: "<p>74.(d)&nbsp;As we know, 1% = 3.6&deg;<br>Then, Production of type D cars in 2021 = 20% = 20 &times; 3.6&deg; = 72&deg;</p>",
                    solution_hi: "<p>74.(d)&nbsp;जैसा कि हम जानते हैं, 1% = 3.6&deg;<br>फिर, 2021 में टाइप D कारों का उत्पादन = 20% = 20 &times; 3.6&deg; = 72&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. On a 200-m-long circular track, two people start from the same point, at the same time, at speeds of 12 km/h and 15 km/h, respectively. After how much time will they meet again for the first time if they are running in the same direction ?</p>",
                    question_hi: "<p>75. 200-m लंबे एक वृत्ताकार पथ पर, दो व्यक्ति एक ही बिंदु से एक ही समय पर क्रमश: 12 km/h और 15 km/h की चाल से दौड़ना शुरू करते हैं। यदि वे एक ही दिशा में दौड़ रहे हैं तो कितने समय बाद वे पहली बार दोबारा मिलेंगे?</p>",
                    options_en: [
                        "<p>180 seconds</p>",
                        "<p>200 seconds</p>",
                        "<p>240 seconds</p>",
                        "<p>220 seconds</p>"
                    ],
                    options_hi: [
                        "<p>180 सेकंड</p>",
                        "<p>200 सेकंड</p>",
                        "<p>240 सेकंड</p>",
                        "<p>220 सेकंड</p>"
                    ],
                    solution_en: "<p>75.(c) Time taken to meet again for the first time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mrow><mn>3</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>18</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>5</mn></mfrac></math> = 240sec</p>",
                    solution_hi: "<p>75.(c) पहली बार दोबारा मिलने में समय लगा = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>200</mn><mrow><mn>3</mn><mo>&#215;</mo><mfrac><mn>5</mn><mn>18</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>200</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>5</mn></mfrac></math>&nbsp;= 240sec</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Parts of the following sentence have been given as options. Select the option that contains an error. <br>Solutions to the country&rsquo;s interior problems are still a long way away.</p>",
                    question_hi: "<p>76. Parts of the following sentence have been given as options. Select the option that contains an error. <br>Solutions to the country&rsquo;s interior problems are still a long way away.</p>",
                    options_en: [
                        "<p>problems are still</p>",
                        "<p>a long way away.</p>",
                        "<p>Solutions to</p>",
                        "<p>the country&rsquo;s interior</p>"
                    ],
                    options_hi: [
                        "<p>problems are still</p>",
                        "<p>a long way away.</p>",
                        "<p>Solutions to</p>",
                        "<p>the country&rsquo;s interior</p>"
                    ],
                    solution_en: "<p>76.(d) the country&rsquo;s interior<br>The correct word to use in this sentence is &lsquo;internal&rsquo;, not &lsquo;interior&rsquo;. &lsquo;Internal&rsquo; means occurring inside a country. Hence, <span style=\"text-decoration: underline;\"><em>the country&rsquo;s internal</em></span> is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(d) the country&rsquo;s interior<br>इस sentence में प्रयोग करने के लिए सही शब्द &lsquo;interior&rsquo; नहीं बल्कि &lsquo;internal&rsquo; है। &lsquo;Internal&rsquo; का अर्थ है देश के अंदर घटित हो रहे। अतः,&nbsp;<span style=\"text-decoration: underline;\"><em>the country&rsquo;s internal</em></span> सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate meaning of the idiom ( in the context). <br>A man of straw</p>",
                    question_hi: "<p>77. Select the most appropriate meaning of the idiom ( in the context). <br>A man of straw</p>",
                    options_en: [
                        "<p>A man of no substance</p>",
                        "<p>A very active person</p>",
                        "<p>A worthy fellow</p>",
                        "<p>An unreasonable person</p>"
                    ],
                    options_hi: [
                        "<p>A man of no substance</p>",
                        "<p>A very active person</p>",
                        "<p>A worthy fellow</p>",
                        "<p>An unreasonable person</p>"
                    ],
                    solution_en: "<p>77.(a) A man of no substance<br>Eg- As he is a man of straw people do not trust him easily.</p>",
                    solution_hi: "<p>77.(a) A man of no substance/ बिना सत्यनिष्ठा का आदमी<br>Eg- As he is a man of straw people do not trust him easily.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>No sooner the teacher entered the class than the students stopped talking.</p>",
                    question_hi: "<p>78. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>No sooner the teacher entered the class than the students stopped talking.</p>",
                    options_en: [
                        "<p>entered the class</p>",
                        "<p>than the students</p>",
                        "<p>stopped talking.</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>entered the class</p>",
                        "<p>than the students</p>",
                        "<p>stopped talking.</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>78.(a) entered the class. <br>&ldquo;No sooner + Verb + Subject&rdquo; is the correct grammatical structure for the given sentence. Hence, &lsquo;No sooner had(Verb) the teacher(Subject)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a) entered the class. <br>दिए गए वाक्य के लिए &ldquo;No sooner + Verb + Subject&rdquo; सही grammatical structure है। इसलिए, &lsquo;No sooner had(Verb) the teacher(Subject)&rsquo; उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>Though Mr. Swami <strong><span style=\"text-decoration: underline;\">ran in</span></strong> debt, he did not stop wasting money.</p>",
                    question_hi: "<p>79. Select the most appropriate option to substitute the underlined segment in the given sentence. <br>Though Mr. Swami <span style=\"text-decoration: underline;\"><strong>ran in</strong></span> debt, he did not stop wasting money.</p>",
                    options_en: [
                        "<p>ran along</p>",
                        "<p>ran into</p>",
                        "<p>ran to</p>",
                        "<p>ran over</p>"
                    ],
                    options_hi: [
                        "<p>ran along</p>",
                        "<p>ran into</p>",
                        "<p>ran to</p>",
                        "<p>ran over</p>"
                    ],
                    solution_en: "<p>79.(b) To \'run into\' means to&nbsp;experience a difficult situation or a problem. In the given question Mr Swami ran into a debt</p>",
                    solution_hi: "<p>79.(b) \'Run into\' का अर्थ है to experience a difficult situation or a problem.(किसी कठिन परिस्थिति या समस्या का अनुभव करना), दिए गए प्रश्न में Mr Swami ran into a debt सबसे उपयुक्त है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>The manager said, &ldquo;Well, what can I do for you ?&rdquo;</p>",
                    question_hi: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>The manager said, &ldquo;Well, what can I do for you ?&rdquo;</p>",
                    options_en: [
                        "<p>The manager wanted to know what he could do for him.</p>",
                        "<p>The manager wanted to know that what he could do for him.</p>",
                        "<p>The manager asked what he can do for him.</p>",
                        "<p>The manager wanted to know what he can do for him.</p>"
                    ],
                    options_hi: [
                        "<p>The manager wanted to know what he could do for him.</p>",
                        "<p>The manager wanted to know that what he could do for him.</p>",
                        "<p>The manager asked what he can do for him.</p>",
                        "<p>The manager wanted to know what he can do for him.</p>"
                    ],
                    solution_en: "<p>80.(a) The manager wanted to know what he could do for him. <br>(b) The manager wanted to know <strong>that</strong> what he could do for him. (Incorrect connecting word)<br>(c) The manager asked <strong>that </strong>what he can do for him. (Incorrect connecting word)<br>(d) The manager wanted to know what he <strong>can </strong>do for him. (Incorrect change of tense)</p>",
                    solution_hi: "<p>80.(a) The manager wanted to know what he could do for him. <br>(b) The manager wanted to know <strong>that </strong>what he could do for him. (गलत connecting word)<br>(c) The manager asked <strong>that </strong>what he can do for him. (गलत connecting word)<br>(d) The manager wanted to know what he <strong>can </strong>do for him. (Tense का गलत परिवर्तन)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that expresses the given sentence in passive voice.<br>I remember my mother taking me to the stadium.</p>",
                    question_hi: "<p>81. Select the option that expresses the given sentence in passive voice.<br>I remember my mother taking me to the stadium.</p>",
                    options_en: [
                        "<p>I remember being taken to the stadium by my mother.</p>",
                        "<p>I was taken to the stadium by mother that I remembered.</p>",
                        "<p>My mother remembered me taken to the stadium.</p>",
                        "<p>I was remembered being taken to the stadium by my mother.</p>"
                    ],
                    options_hi: [
                        "<p>I remember being taken to the stadium by my mother.</p>",
                        "<p>I was taken to the stadium by mother that I remembered.</p>",
                        "<p>My mother remembered me taken to the stadium.</p>",
                        "<p>I was remembered being taken to the stadium by my mother.</p>"
                    ],
                    solution_en: "<p>81.(a) I remember being taken to the stadium by my mother. (Correct)<br>(b) I was taken to the stadium by mother that I remembered. (Incorrect Sentence Structure) <br>(c) My mother remembered me taken to the stadium. (Incorrect Sentence Structure) <br>(d) I was remembered being taken to the stadium by my mother. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>81.(a) I remember being taken to the stadium by my mother. (Correct)<br>(b) I was taken to the stadium by mother that I remembered. (गलत Sentence Structure) <br>(c) My mother remembered me taken to the stadium. (गलत Sentence Structure) <br>(d) I was remembered being taken to the stadium by my mother. (गलत Sentence Structure) </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Find a word that is the synonym of<br>Renounce</p>",
                    question_hi: "<p>82. Find a word that is the synonym of<br>Renounce</p>",
                    options_en: [
                        "<p>Embrace</p>",
                        "<p>Ignore</p>",
                        "<p>Overlook</p>",
                        "<p>Abjure</p>"
                    ],
                    options_hi: [
                        "<p>Embrace</p>",
                        "<p>Ignore</p>",
                        "<p>Overlook</p>",
                        "<p>Abjure</p>"
                    ],
                    solution_en: "<p>82.(d) Abjure. <br><strong>Renounce </strong>means to give up something. Abjure means to give up a belief, claim etc. So option &lsquo;d&rsquo; abjure is the most appropriate answer.</p>",
                    solution_hi: "<p>82.(d) Abjure. <br><strong>Renounce </strong>का अर्थ कुछ त्याग करना।. Abjure का अर्थ है किसी विश्वास, दावे आदि को छोड़ देना।. इसलिए option &lsquo;d&rsquo; abjure सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) So Golu set out for the river Limpopo.<br>(B) One day Golu met the mynah bird sitting in the middle of a bush.<br>(C) He asked her, &ldquo;What does the crocodile have for dinner?<br>(D) The mynah said, &ldquo;Go to the banks of the great, grassy Limpopo river and find out.</p>",
                    question_hi: "<p>83. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) So Golu set out for the river Limpopo.<br>(B) One day Golu met the mynah bird sitting in the middle of a bush.<br>(C) He asked her, &ldquo;What does the crocodile have for dinner?<br>(D) The mynah said, &ldquo;Go to the banks of the great, grassy Limpopo river and find out.</p>",
                    options_en: [
                        "<p>DCBA</p>",
                        "<p>BCDA</p>",
                        "<p>BACD</p>",
                        "<p>ACBD</p>"
                    ],
                    options_hi: [
                        "<p>DCBA</p>",
                        "<p>BCDA</p>",
                        "<p>BACD</p>",
                        "<p>ACBD</p>"
                    ],
                    solution_en: "<p>83.(b) BCDA<br>Sentence B will be the starting line as it contains the main idea of the parajumble i.e. Once Golu met the Mynah bird. Sentence C states the conversation between them i.e. He asked her what the crocodile has for dinner . So, C will follow B. Further, Sentence D states what the bird replied to him. It told him to go to the bank of the Limpopo river and Sentence A states that he went there . So, A will follow D. Going through the options, option (b) has the correct sequence.</p>",
                    solution_hi: "<p>83.(b) BCDA<br>Sentence B प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार है यानी एक बार गोलू , मैना पक्षी से मिला था। Sentence C उनके बीच की बातचीत को बताता है यानी उसने मैना से पूछा कि मगरमच्छ रात के खाने में क्या खाता है । इसलिए, B के बाद C आएगा । आगे, वाक्य D बताता है कि पक्षी ने उसे क्या उत्तर दिया। इसने उसे लिम्पोपो नदी के तट पर जाने के लिए कहा और Sentence A में कहा गया है कि वह वहां गया था। इसलिए, D के बाद A आएगा । विकल्पों के माध्यम से, option (b) में सही क्रम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Pick a word opposite in meaning to<br>Frivolous</p>",
                    question_hi: "<p>84. Pick a word opposite in meaning to<br>Frivolous</p>",
                    options_en: [
                        "<p>Popular</p>",
                        "<p>Reputed</p>",
                        "<p>Nervous</p>",
                        "<p>Serious</p>"
                    ],
                    options_hi: [
                        "<p>Popular</p>",
                        "<p>Reputed</p>",
                        "<p>Nervous</p>",
                        "<p>Serious</p>"
                    ],
                    solution_en: "<p>84.(d) Serious. <br>Frivolous means not having any serious purpose or value. So option d serious is the most appropriate answer.</p>",
                    solution_hi: "<p>84.(d) Serious. <br>Frivolous अर्थ है कोई गंभीर उद्देश्य या मूल्य का न होना। अतः विकल्प d serious सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>85. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>dictionary</p>",
                        "<p>chancellary</p>",
                        "<p>directory</p>",
                        "<p>granary</p>"
                    ],
                    options_hi: [
                        "<p>dictionary</p>",
                        "<p>chancellary</p>",
                        "<p>directory</p>",
                        "<p>granary</p>"
                    ],
                    solution_en: "<p>85.(b) chancellary <br>&lsquo;Chancellery&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>85.(b) chancellary <br>&lsquo;Chancellery&rsquo; सही spelling है।<br><br></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the word which means the same as the group of words given.<br>That which cannot be defeated</p>",
                    question_hi: "<p>86. Select the word which means the same as the group of words given.<br>That which cannot be defeated</p>",
                    options_en: [
                        "<p>Invincible</p>",
                        "<p>Invulnerable</p>",
                        "<p>Infallible</p>",
                        "<p>Indictable</p>"
                    ],
                    options_hi: [
                        "<p>Invincible</p>",
                        "<p>Invulnerable</p>",
                        "<p>Infallible</p>",
                        "<p>Indictable</p>"
                    ],
                    solution_en: "<p>86.(a) Invincible,&nbsp;<br><strong>Invincible&nbsp;</strong>- Too powerful to be defeated or overcome.<br><strong>Invulnerable </strong>- Impossible to harm or damage.<br><strong>Infallible </strong>- Incapable of making mistakes or being wrong.<br><strong>Indictable </strong>- an indictable crime is one that someone can be accused of in a court of law.</p>",
                    solution_hi: "<p>86.(a) Invincible.<br><strong>Invincible </strong>- पराजित करने या पराजित होने के लिए बहुत शक्तिशाली।<br><strong>Invulnerable </strong>- नुकसान या क्षति के लिए असंभव।<br><strong>Infallible </strong>- गलतियाँ करने या गलत होने में असमर्थ।<br><strong>Indictable </strong>- एक Indictable अपराध वह है जिस पर किसी व्यक्ति पर अदालत में आरोप लगाया जा सकता है (अभ्यारोप्य)। </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Heartfelt prayers to God will always <span style=\"text-decoration: underline;\"><strong>have expected results</strong></span>.</p>",
                    question_hi: "<p>87. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Heartfelt prayers to God will always <span style=\"text-decoration: underline;\">have expected results</span>.</p>",
                    options_en: [
                        "<p>Have desired results</p>",
                        "<p>Have good results</p>",
                        "<p>Have amazing results</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>Have desired results</p>",
                        "<p>Have good results</p>",
                        "<p>Have amazing results</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>87.(a) Have desired results<br>&ldquo;Desired results&rdquo; are those which the person praying wishes for.</p>",
                    solution_hi: "<p>87.(a) Have desired results<br>&ldquo;Desired results&rdquo;- मनोवांछित फल वे होते हैं जिनकी प्रार्थना करने वाला व्यक्ति कामना करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>Your remarks during the discussion <span style=\"text-decoration: underline;\"><strong>added fuel to the fire</strong></span>.</p>",
                    question_hi: "<p>88. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>Your remarks during the discussion <span style=\"text-decoration: underline;\"><strong>added fuel to the fire</strong></span>.</p>",
                    options_en: [
                        "<p>got other angry</p>",
                        "<p>ignited the fireplace</p>",
                        "<p>worsened matters</p>",
                        "<p>created warmth all around</p>"
                    ],
                    options_hi: [
                        "<p>got other angry</p>",
                        "<p>ignited the fireplace</p>",
                        "<p>worsened matters</p>",
                        "<p>created warmth all around</p>"
                    ],
                    solution_en: "<p>88.(c) worsened matters.<br>Example- The CBI chief said he wouldn\'t add fuel to the fire by letting out the report before the Press.</p>",
                    solution_hi: "<p>88.(c) worsened matters./ स्थिति बिगड़ना <br>Example- The CBI chief said he wouldn\'t add fuel to the fire by letting out the report before the Press.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the word which means the same as the group of words given.<br>A new word.</p>",
                    question_hi: "<p>89. Select the word which means the same as the group of words given.<br>A new word.</p>",
                    options_en: [
                        "<p>neologism</p>",
                        "<p>niche</p>",
                        "<p>nausea</p>",
                        "<p>nephrology</p>"
                    ],
                    options_hi: [
                        "<p>neologism</p>",
                        "<p>niche</p>",
                        "<p>nausea</p>",
                        "<p>nephrology</p>"
                    ],
                    solution_en: "<p>89.(a) neologism<br><strong>Neologism </strong>- A newly coined word<br><strong>Niche </strong>- A niche is a place or position that\'s particularly appropriate for someone or something, especially due to being very specific and different from others.<br><strong>Nausea </strong>- Vomiting tendency<br><strong>Nephrology </strong>- The study of normal kidney function and related diseases.</p>",
                    solution_hi: "<p>89.(a) neologism<br><strong>Neologism </strong>- एक नया गढ़ा हुआ शब्द<br><strong>Niche</strong>- एक जगह है जो विशेष रूप से किसी के लिए या कुछ के लिए उपयुक्त है, विशेष रूप से बहुत विशिष्ट और दूसरों से अलग होने के कारण।<br><strong>Nausea </strong>- उल्टी की प्रवृत्ति<br><strong>Nephrology</strong>- किडनी के सामान्य कार्य और संबंधित रोगों का अध्ययन।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Goats in hilly regions hate being tied to trees or poles<br>(B) Poor Abbu Khan was a little unlucky in the matter of his goats. <br>(C) Very often at night one of the goats would pull and pull at the string till it broke loose.<br>(D) Then it would disappear into the hills beyond.</p>",
                    question_hi: "<p>90. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Goats in hilly regions hate being tied to trees or poles<br>(B) Poor Abbu Khan was a little unlucky in the matter of his goats. <br>(C) Very often at night one of the goats would pull and pull at the string till it broke loose.<br>(D) Then it would disappear into the hills beyond.</p>",
                    options_en: [
                        "<p>ACBD</p>",
                        "<p>ABCD</p>",
                        "<p>BDCA</p>",
                        "<p>BCDA</p>"
                    ],
                    options_hi: [
                        "<p>ACBD</p>",
                        "<p>ABCD</p>",
                        "<p>BDCA</p>",
                        "<p>BCDA</p>"
                    ],
                    solution_en: "<p>90.(d) BCDA<br>Sentence B will be the starting line as it contains the main idea of the parajumble i.e.Poor Abbu Khan was unlucky in the matter of goats. . Sentence C states the incident that one night one goat tried to break its string . So, C will follow B . Further, Sentence D states that after the string had broken, it went to the hills and Sentence A states that in the hills, goats don&rsquo;t like to be tied . So A will follow D. Going through the options, option (d) BCDA has the correct sequence.</p>",
                    solution_hi: "<p>90.(d) BCDA<br>Sentence B प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार है यानी गरीब अब्बू खान बकरियों के मामले में बदकिस्मत थे। Sentence C उस घटना को बताता है कि एक रात एक बकरी ने अपनी रस्सी तोड़ने की कोशिश की। इसके अलावा, Sentence D बताता है कि रस्सी टूटने के बाद, यह पहाड़ियों पर चली गई और Sentence A कहता है कि पहाड़ों में बकरियों को बंधना पसंद नहीं है। तो D के बाद A आएगा । विकल्पों के माध्यम से, option (d) BCDA में सही क्रम है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Find a word that is the synonym of the given word in the sentance .<br>His strategy is to <span style=\"text-decoration: underline;\"><strong>protract</strong></span> negotiations until the enemy are exhausted while assembling the forces necessary to crush them.</p>",
                    question_hi: "<p>91. Find a word that is the synonym of the given word in the sentance .<br>His strategy is to <span style=\"text-decoration: underline;\"><strong>protract</strong></span> negotiations until the enemy are exhausted while assembling the forces necessary to crush them.</p>",
                    options_en: [
                        "<p>Control</p>",
                        "<p>Prolong</p>",
                        "<p>Accept</p>",
                        "<p>Cease</p>"
                    ],
                    options_hi: [
                        "<p>Control</p>",
                        "<p>Prolong</p>",
                        "<p>Accept</p>",
                        "<p>Cease</p>"
                    ],
                    solution_en: "<p>91.(b) <strong>Prolong</strong>- to make something last longer<br><strong>Protract</strong>- to make something last for a long time<br><strong>Control</strong>- power and ability to make somebody/something do what you want<br><strong>Accept</strong>- to agree to take something that somebody offers you<br><strong>Cease</strong>- to stop or end</p>",
                    solution_hi: "<p>91.(b) <strong>Prolong </strong>- किसी वस्तु को अधिक समय तक बनाए रखना<br><strong>Protract </strong>- किसी वस्तु को अधिक समय तक बनाए रखना<br><strong>Control </strong>- किसी से/किसी चीज़ से जो आप चाहते हैं वह करवाने की क्षमता और शक्ति <br><strong>Accept </strong>- कोई ऐसी चीज लेने के लिए राजी होना जो कोई आपको देता है<br><strong>Cease </strong>- रुकना या समाप्त होना</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate option to fill in the blank. <br>In the Second World War, the British _____ thousands of so-called interceptors &ndash; mostly women &ndash; whose job it was to tune in every day and night to the radio _____ of the various divisions of the German military.</p>",
                    question_hi: "<p>92. Select the most appropriate option to fill in the blank.<br>In the Second World War, the British_____ thousands of so-called interceptors &ndash; mostly women &ndash; whose job it was to tune in every day and night to the radio _____ of the various divisions of the German military.</p>",
                    options_en: [
                        "<p>averted, broadbills</p>",
                        "<p>assented, broadsides</p>",
                        "<p>assembled, broadcasts</p>",
                        "<p>adhered, broadlooms</p>"
                    ],
                    options_hi: [
                        "<p>averted, broadbills</p>",
                        "<p>assented, broadsides</p>",
                        "<p>assembled, broadcasts</p>",
                        "<p>adhered, broadlooms</p>"
                    ],
                    solution_en: "<p>92.(c) assembled, broadcasts<br>Broadcast is a verb it means to transmit (a programme or some information) by radio or television. And broadcast is available only in option c. Also when people get together at a certain place the verb used is assemble. Hence option (c) is the most appropriate answer.</p>",
                    solution_hi: "<p>92.(c) assembled, broadcasts<br>Broadcast एक verb है जिसका अर्थ है रेडियो या टेलीविजन द्वारा प्रसारित एक कार्यक्रम या कुछ जानकारी। और broadcast केवल विकल्प c में उपलब्ध है। साथ ही जब लोग एक निश्चित स्थान पर एक साथ मिलते हैं तो प्रयुक्त क्रिया assemble होती है। अतः विकल्प (c) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Pick a word opposite in the meaning to<br>Efficacious</p>",
                    question_hi: "<p>93. Pick a word opposite in the meaning to<br>Efficacious</p>",
                    options_en: [
                        "<p>Productive</p>",
                        "<p>Ineffective</p>",
                        "<p>Improper</p>",
                        "<p>Urgent</p>"
                    ],
                    options_hi: [
                        "<p>Productive</p>",
                        "<p>Ineffective</p>",
                        "<p>Improper</p>",
                        "<p>Urgent</p>"
                    ],
                    solution_en: "<p>93.(b) Efficacious (<strong>Adjective</strong>) .It means productive, <strong>effective</strong>; producing the result. Ineffective .It means not achieving what you want to achieve. <br><strong>Look at the sentence:</strong> They hope the new drug will prove especially efficacious in the relief of pain. The law proved ineffective in dealing with the problem.</p>",
                    solution_hi: "<p>93.(b) Efficacious (<strong>Adjective</strong>) .इसका अर्थ है उत्पादक, प्रभावी; परिणाम उत्पन्न करना. Ineffective - इसका मतलब है कि आप जो हासिल करना चाहते हैं उसे हासिल नहीं कर पाना।<br><strong>Look at the sentence</strong>:/वाक्य को देखें:<br>They hope the new drug will prove especially efficacious in the relief of pain./उन्हें उम्मीद है कि नई दवा दर्द से राहत दिलाने में विशेष रूप से कारगर साबित होगी।<br>The law proved ineffective in dealing with the problem./समस्या से निपटने में कानून बेअसर साबित हुआ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate option to fill in the blank.<br>The first thing they did was to _____ the extent of damage done by the rains.</p>",
                    question_hi: "<p>94. Select the most appropriate option to fill in the blank.<br>The first thing they did was to _____ the extent of damage done by the rains.</p>",
                    options_en: [
                        "<p>circulate</p>",
                        "<p>ascertain</p>",
                        "<p>repair</p>",
                        "<p>search</p>"
                    ],
                    options_hi: [
                        "<p>circulate</p>",
                        "<p>ascertain</p>",
                        "<p>repair</p>",
                        "<p>search</p>"
                    ],
                    solution_en: "<p>94.(b) <strong>Ascertain </strong>- Make sure of<br><strong>Circulate </strong>- pass from one person to another, spread<br><strong>Repair </strong>- Restore to a good condition<br><strong>Search </strong>- To look for something</p>",
                    solution_hi: "<p>94.(b) <strong>Ascertain </strong>- पता लगाना - Make sure of<br><strong>Circulate </strong>- प्रसारित - pass from one person to another, spread<br><strong>Repair </strong>- मरम्मत - Restore to a good condition<br><strong>Search </strong>- खोज - To look for something</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the option that expresses the given sentence in passive voice. <br>The flight is carrying relief material.</p>",
                    question_hi: "<p>95. Select the option that expresses the given sentence in passive voice. <br>The flight is carrying relief material.</p>",
                    options_en: [
                        "<p>Relief material is being carrying by the flight.</p>",
                        "<p>Relief material is being carried by the flight.</p>",
                        "<p>Relief material is carried by the flight.</p>",
                        "<p>Relief material is carrying by the flight.</p>"
                    ],
                    options_hi: [
                        "<p>Relief material is being carrying by the flight.</p>",
                        "<p>Relief material is being carried by the flight.</p>",
                        "<p>Relief material is carried by the flight.</p>",
                        "<p>Relief material is carrying by the flight.</p>"
                    ],
                    solution_en: "<p>95.(b) Relief material is being carried by the flight. (Correct)<br>(a) Relief material is being <span style=\"text-decoration: underline;\">carrying</span> by the flight. (Incorrect form of Verb )<br>(c) Relief material <span style=\"text-decoration: underline;\">is carried</span> by the flight. (&lsquo;Being&rsquo; is missing)<br>(d) Relief material <span style=\"text-decoration: underline;\">is carrying</span> by the flight. (Incorrect verb)</p>",
                    solution_hi: "<p>59.(b) Relief material is being carried by the flight. (Correct)<br>(a) Relief material is being <span style=\"text-decoration: underline;\">carrying</span> by the flight. (Verb की गलत form)<br>(c) Relief material <span style=\"text-decoration: underline;\">is carried</span> by the flight. (&lsquo;Being&rsquo; missing है)<br>(d) Relief material <span style=\"text-decoration: underline;\">is carrying</span> by the flight. (गलत verb)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :</strong> <br>Everything (96) _____ us makes our environment. Living and non-living things form the (97) _____ of our surroundings. Plants, animals and microbes are parts of our environment. Plants, animals and microbes have affected the environment in multiple (98) ______ like clearing of forests, building houses and farms, making roads, etc. This is the root (99) _____ of air, water and land pollution on the planet. Ecologists across the globe are making efforts to educate mankind about the (100) _____ of nature, the crisis we face and the methods of managing these crises.<br>Select the most appropriate option to fill in the blank No. 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test :</strong> <br>Everything (96) _____ us makes our environment. Living and non-living things form the (97) _____ of our surroundings. Plants, animals and microbes are parts of our environment. Plants, animals and microbes have affected the environment in multiple (98) ______ like clearing of forests, building houses and farms, making roads, etc. This is the root (99) _____ of air, water and land pollution on the planet. Ecologists across the globe are making efforts to educate mankind about the (100) _____ of nature, the crisis we face and the methods of managing these crises.<br>Select the most appropriate option to fill in the blank No. 96.</p>",
                    options_en: [
                        "<p>over</p>",
                        "<p>beyond</p>",
                        "<p>across</p>",
                        "<p>around</p>"
                    ],
                    options_hi: [
                        "<p>over</p>",
                        "<p>beyond</p>",
                        "<p>across</p>",
                        "<p>around</p>"
                    ],
                    solution_en: "<p>96.(d) Around means situated on every side. So everything situated on every side makes our environment.</p>",
                    solution_hi: "<p>96.(d) Around का अर्थ है- हर तरफ। तो हमारे हर तरफ स्थित चीज हमारे पर्यावरण को बनाती है। इसलिए विकल्प (d) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :</strong> <br>Everything (96) _____ us makes our environment. Living and non-living things form the (97) _____ of our surroundings. Plants, animals and microbes are parts of our environment. Plants, animals and microbes have affected the environment in multiple (98) ______ like clearing of forests, building houses and farms, making roads, etc. This is the root (99) _____ of air, water and land pollution on the planet. Ecologists across the globe are making efforts to educate mankind about the (100) _____ of nature, the crisis we face and the methods of managing these crises.<br>Select the most appropriate option to fill in the blank No. 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test :</strong> <br>Everything (96) _____ us makes our environment. Living and non-living things form the (97) _____ of our surroundings. Plants, animals and microbes are parts of our environment. Plants, animals and microbes have affected the environment in multiple (98) ______ like clearing of forests, building houses and farms, making roads, etc. This is the root (99) _____ of air, water and land pollution on the planet. Ecologists across the globe are making efforts to educate mankind about the (100) _____ of nature, the crisis we face and the methods of managing these crises.<br>Select the most appropriate option to fill in the blank No. 97.</p>",
                    options_en: [
                        "<p>fractions</p>",
                        "<p>ingredients</p>",
                        "<p>components</p>",
                        "<p>fragments</p>"
                    ],
                    options_hi: [
                        "<p>fractions</p>",
                        "<p>ingredients</p>",
                        "<p>components</p>",
                        "<p>fragments</p>"
                    ],
                    solution_en: "<p>97.(c) Components means something which is part of a larger whole.</p>",
                    solution_hi: "<p>97.(c) Components का अर्थ है- कुछ ऐसा जो एक बड़ी चीज का हिस्सा है । इसलिए विकल्प(a) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :</strong> <br>Everything (96) _____ us makes our environment. Living and non-living things form the (97) _____ of our surroundings. Plants, animals and microbes are parts of our environment. Plants, animals and microbes have affected the environment in multiple (98) ______ like clearing of forests, building houses and farms, making roads, etc. This is the root (99) _____ of air, water and land pollution on the planet. Ecologists across the globe are making efforts to educate mankind about the (100) _____ of nature, the crisis we face and the methods of managing these crises.<br>Select the most appropriate option to fill in the blank No. 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test :</strong> <br>Everything (96) _____ us makes our environment. Living and non-living things form the (97) _____ of our surroundings. Plants, animals and microbes are parts of our environment. Plants, animals and microbes have affected the environment in multiple (98) ______ like clearing of forests, building houses and farms, making roads, etc. This is the root (99) _____ of air, water and land pollution on the planet. Ecologists across the globe are making efforts to educate mankind about the (100) _____ of nature, the crisis we face and the methods of managing these crises.<br>Select the most appropriate option to fill in the blank No. 98.</p>",
                    options_en: [
                        "<p>plans</p>",
                        "<p>courses</p>",
                        "<p>means</p>",
                        "<p>ways</p>"
                    ],
                    options_hi: [
                        "<p>plans</p>",
                        "<p>courses</p>",
                        "<p>means</p>",
                        "<p>ways</p>"
                    ],
                    solution_en: "<p>98.(d) Humans have affected the environment in multiple &ldquo;ways&rdquo; like clearing of forests, building houses and farms, making roads, etc.</p>",
                    solution_hi: "<p>98.(d) मनुष्यों ने पर्यावरण को कई \"तरीकों (ways)\" से प्रभावित किया है जैसे जंगलों को साफ करना, घर और खेत बनाना, सड़क बनाना आदि। इसलिए विकल्प (d) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :</strong> <br>Everything (96) _____ us makes our environment. Living and non-living things form the (97) _____ of our surroundings. Plants, animals and microbes are parts of our environment. Plants, animals and microbes have affected the environment in multiple (98) ______ like clearing of forests, building houses and farms, making roads, etc. This is the root (99) _____ of air, water and land pollution on the planet. Ecologists across the globe are making efforts to educate mankind about the (100) _____ of nature, the crisis we face and the methods of managing these crises.<br>Select the most appropriate option to fill in the blank No. 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test :</strong> <br>Everything (96) _____ us makes our environment. Living and non-living things form the (97) _____ of our surroundings. Plants, animals and microbes are parts of our environment. Plants, animals and microbes have affected the environment in multiple (98) ______ like clearing of forests, building houses and farms, making roads, etc. This is the root (99) _____ of air, water and land pollution on the planet. Ecologists across the globe are making efforts to educate mankind about the (100) _____ of nature, the crisis we face and the methods of managing these crises.<br>Select the most appropriate option to fill in the blank No. 99.</p>",
                    options_en: [
                        "<p>cause</p>",
                        "<p>source</p>",
                        "<p>origin</p>",
                        "<p>motive</p>"
                    ],
                    options_hi: [
                        "<p>cause</p>",
                        "<p>source</p>",
                        "<p>origin</p>",
                        "<p>motive</p>"
                    ],
                    solution_en: "<p>99.(a) A cause is something that gives rise to an action, phenomenon, or condition</p>",
                    solution_hi: "<p>99.(a) Cause एक ऐसी चीज है जो किसी action, phenomenon या condition को जन्म देती है, इसलिए विकल्प (a) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :</strong> <br>Everything (96) _____ us makes our environment. Living and non-living things form the (97) _____ of our surroundings. Plants, animals and microbes are parts of our environment. Plants, animals and microbes have affected the environment in multiple (98) ______ like clearing of forests, building houses and farms, making roads, etc. This is the root (99) _____ of air, water and land pollution on the planet. Ecologists across the globe are making efforts to educate mankind about the (100) _____ of nature, the crisis we face and the methods of managing these crises.<br>Select the most appropriate option to fill in the blank No. 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test :</strong> <br>Everything (96) _____ us makes our environment. Living and non-living things form the (97) _____ of our surroundings. Plants, animals and microbes are parts of our environment. Plants, animals and microbes have affected the environment in multiple (98) ______ like clearing of forests, building houses and farms, making roads, etc. This is the root (99) _____ of air, water and land pollution on the planet. Ecologists across the globe are making efforts to educate mankind about the (100) _____ of nature, the crisis we face and the methods of managing these crises.<br>Select the most appropriate option to fill in the blank No. 100.</p>",
                    options_en: [
                        "<p>surprise</p>",
                        "<p>wonders</p>",
                        "<p>compassion</p>",
                        "<p>sensation</p>"
                    ],
                    options_hi: [
                        "<p>surprise</p>",
                        "<p>wonders</p>",
                        "<p>compassion</p>",
                        "<p>sensation</p>"
                    ],
                    solution_en: "<p>100.(b) Wonders of nature in the passage means all those things that are supplied to us by nature.</p>",
                    solution_hi: "<p>100.(b) Passage में प्रकृति के चमत्कारों (wonders) का अर्थ उन सभी चीजों से है जो हमें प्रकृति द्वारा दी जाती हैं। इसलिए विकल्प (b) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>