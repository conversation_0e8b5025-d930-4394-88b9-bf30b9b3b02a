<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. The following sentence has been divided into four segments. Identify the segment that&nbsp;contains a grammatical error.<br>Don&rsquo;t go / outside / because it is / raining heavy.</p>",
                    question_hi: "<p>1. The following sentence has been divided into four segments. Identify the segment that&nbsp;contains a grammatical error.<br>Don&rsquo;t go / outside / because it is / raining heavy.</p>",
                    options_en: ["<p>raining heavy.</p>", "<p>Don&rsquo;t go</p>", 
                                "<p>outside</p>", "<p>because it is</p>"],
                    options_hi: ["<p>raining heavy.</p>", "<p>Don&rsquo;t go</p>",
                                "<p>outside</p>", "<p>because it is</p>"],
                    solution_en: "<p>1.(a) <strong>raining heavy</strong><br>The given sentence needs an adverb (heavily) to modify the verb &lsquo;raining&rsquo;, not the adjective &lsquo;heavy&rsquo;. Hence, &lsquo;raining heavily&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(a) <strong>raining heavy</strong><br>दिए गए sentence में verb &lsquo;raining&rsquo; को modify करने के लिए adverb (heavily) की आवश्यकता है, न कि adjective &lsquo;heavy&rsquo; की। अतः, &lsquo;raining heavily&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Parts of the following sentence have been given as options. Select the option that&nbsp;contains an error.<br>Today she said to her friend, &ldquo;You&rsquo;re the sweetest person I have met ever&rdquo;.</p>",
                    question_hi: "<p>2. Parts of the following sentence have been given as options. Select the option that&nbsp;contains an error.<br>Today she said to her friend, &ldquo;You&rsquo;re the sweetest person I have met ever&rdquo;.</p>",
                    options_en: ["<p>I have met ever&rdquo;.</p>", "<p>her friend, &ldquo;You&rsquo;re</p>", 
                                "<p>Today she said to</p>", "<p>the sweetest person</p>"],
                    options_hi: ["<p>I have met ever&rdquo;.</p>", "<p>her friend, &ldquo;You&rsquo;re</p>",
                                "<p>Today she said to</p>", "<p>the sweetest person</p>"],
                    solution_en: "<p>2.(a)<strong> I have met ever</strong><br>&lsquo;Ever&rsquo; is an adverb of frequency. An adverb of frequency is generally placed before the main verb and after the helping verb. Therefore, &lsquo;ever&rsquo; will be placed before the main verb &lsquo;met&rsquo;. Hence &lsquo;I have ever met&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>2.(a) <strong>I have met ever</strong><br>&lsquo;Ever&rsquo; एक adverb of frequency है। Adverb of frequency का प्रयोग सामान्यतः main verb से पहले और helping verb के बाद किया जाता है। इसलिए, &lsquo;ever&rsquo;, का प्रयोग main verb &lsquo;met&rsquo; से पहले किया जाएगा। अतः &lsquo;I have ever met&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. The given sentence is divided into four segments. Select the option that has the segment with a grammatical error.<br>The class / teacher was / wounded in / a leg.</p>",
                    question_hi: "<p>3. The given sentence is divided into four segments. Select the option that has the segment with a grammatical error.<br>The class / teacher was / wounded in / a leg.</p>",
                    options_en: ["<p>The class</p>", "<p>wounded in</p>", 
                                "<p>teacher was</p>", "<p>a leg.</p>"],
                    options_hi: ["<p>The class</p>", "<p>wounded in</p>",
                                "<p>teacher was</p>", "<p>a leg.</p>"],
                    solution_en: "<p>3.(d) <strong>a leg</strong><br>&lsquo;Leg&rsquo; mentioned in the given sentence is specific and we generally use the definite article &lsquo;the&rsquo; before any specific or particular noun. Hence, &lsquo;in the leg&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>3.(d) <strong>a leg</strong><br>दिए गए sentence में उल्लिखित (mentioned) &lsquo;Leg&rsquo; specific है और हम सामान्यतः किसी specific या particular noun से पहले definite article &lsquo;the&rsquo; का प्रयोग करते हैं। अतः, &lsquo;in the leg&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. The following sentence has been split into four segments. Identify the segment that contains an error.<br>I had seen / her with her mother / in the cinema hall / yesterday.</p>",
                    question_hi: "<p>4. The following sentence has been split into four segments. Identify the segment that contains an error.<br>I had seen / her with her mother / in the cinema hall / yesterday.</p>",
                    options_en: ["<p>her with her mother</p>", "<p>yesterday</p>", 
                                "<p>in the cinema hall</p>", "<p>I had seen</p>"],
                    options_hi: ["<p>her with her mother</p>", "<p>yesterday</p>",
                                "<p>in the cinema hall</p>", "<p>I had seen</p>"],
                    solution_en: "<p>4.(d) <strong>I had seen</strong><br>&lsquo;Had seen&rsquo; must be replaced with \'saw\'. We generally use &lsquo;simple past tense(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>2</mn></msub></math>)&rsquo; to describe actions, events, or states that occurred and were completed in the past. Similarly, in the sentence, the action was completed yesterday. Hence, &lsquo;I saw&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>4.(d)<strong> I had seen</strong><br>&lsquo;Had seen&rsquo; के स्थान पर \'saw\' का प्रयोग होगा। हम सामान्यतः past में घटित और पूर्ण हुई कार्य (actions), घटनाओं (events) या स्थितियों (states) को describe करने के लिए &lsquo;simple past tense (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>2</mn></msub></math>)&rsquo; का प्रयोग करते हैं। इसी तरह, sentence में, action yesterday को complete हुआ था। अतः, &lsquo;I saw&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. The following sentence has been divided into three segments. One of them may contain an adverbial usage error. Select the option that has the segment with the error. If you don&rsquo;t find any error, select &lsquo;No error&rsquo; as your answer.<br>Mr. Narayanan / is a rather / lazy man.</p>",
                    question_hi: "<p>5. The following sentence has been divided into three segments. One of them may contain an adverbial usage error. Select the option that has the segment with the error. If you don&rsquo;t find any error, select &lsquo;No error&rsquo; as your answer.<br>Mr. Narayanan / is a rather / lazy man.</p>",
                    options_en: ["<p>No error</p>", "<p>Mr. Narayanan</p>", 
                                "<p>is a rather</p>", "<p>lazy man.</p>"],
                    options_hi: ["<p>No error</p>", "<p>Mr. Narayanan</p>",
                                "<p>is a rather</p>", "<p>lazy man.</p>"],
                    solution_en: "<p>5.(a) <strong>No error</strong><br>The given sentence is grammatically correct.</p>",
                    solution_hi: "<p>5.(a) <strong>No error</strong><br>दिया गया वाक्य grammatically सही है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>I am very much / obliged of my friend / for the help / he rendered to me.</p>",
                    question_hi: "<p>6. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>I am very much / obliged of my friend / for the help / he rendered to me.</p>",
                    options_en: ["<p>I am very much</p>", "<p>he rendered to me</p>", 
                                "<p>for the help</p>", "<p>obliged of my friend</p>"],
                    options_hi: ["<p>I am very much</p>", "<p>he rendered to me</p>",
                                "<p>for the help</p>", "<p>obliged of my friend</p>"],
                    solution_en: "<p>6.(d) <strong>obliged of my friend</strong><br>&lsquo;To&rsquo; is a fixed preposition used after &lsquo;obliged&rsquo;. Hence &lsquo;obliged to my friend&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(d) <strong>obliged of my friend</strong><br>&lsquo;To&rsquo; एक fixed preposition है जिसका प्रयोग &lsquo;obliged&rsquo; के बाद किया गया है। अतः &lsquo;obliged to my friend&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. The given sentence is divided into four segments. Select the option that has the segment with a grammatical error.<br>I am sure / that he will praying / for a good crop / this year.</p>",
                    question_hi: "<p>7. The given sentence is divided into four segments. Select the option that has the segment with a grammatical error.<br>I am sure / that he will praying / for a good crop / this year.</p>",
                    options_en: ["<p>this year.</p>", "<p>I am sure</p>", 
                                "<p>for a good crop</p>", "<p>that he will praying</p>"],
                    options_hi: ["<p>this year.</p>", "<p>I am sure</p>",
                                "<p>for a good crop</p>", "<p>that he will praying</p>"],
                    solution_en: "<p>7.(d) <strong>that he will praying</strong><br>&lsquo;Modal verbs&rsquo; are auxiliary verbs (also called helping verbs) like can, will, could, shall, must, would, might, and should. However, after a modal verb, we generally use the first form of the verb (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>1</mn></msub></math>). Hence, &lsquo;that he will pray(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>1</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(d) <strong>that he will praying</strong><br>&lsquo;Modal verbs&rsquo; auxiliary verbs (helping verbs) हैं, जैसे can, will, could, shall, must, would, might, and should. हालाँकि, हम आमतौर पर एक modal verb के बाद, verb की 1st form (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>1</mn></msub></math>) का प्रयोग करते हैं। अतः, &lsquo;that he will pray (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>1</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.<br>My students are / waiting for / their flights / at the gate no. 6.</p>",
                    question_hi: "<p>8. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.<br>My students are / waiting for / their flights / at the gate no. 6.</p>",
                    options_en: ["<p>at the gate no. 6.</p>", "<p>their flights</p>", 
                                "<p>waiting for</p>", "<p>My students are</p>"],
                    options_hi: ["<p>at the gate no. 6.</p>", "<p>their flights</p>",
                                "<p>waiting for</p>", "<p>My students are</p>"],
                    solution_en: "<p>8.(a) <strong>at the gate no.6</strong><br>Use of definite article &lsquo;the&rsquo; is incorrect. We generally do not use definite article &lsquo;the&rsquo; when referring to specific numbered gates, rooms or sections. Hence &lsquo;at gate no. 6&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>8.(a) <strong>at the gate no.6</strong><br>दिए गए वाक्य में, definite article &lsquo;the&rsquo; का प्रयोग गलत है। हम आमतौर पर किसी specific numbered gates, rooms या sections का उल्लेख करते समय definite article &lsquo;the&rsquo; का प्रयोग नहीं करते हैं। अतः, &lsquo;at gate no. 6&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Parts of the following sentence have been underlined and given as options. Select the option that contains an error.<br>She <span style=\"text-decoration: underline;\">will being studying</span> for her <span style=\"text-decoration: underline;\">exams tomorrow.</span></p>",
                    question_hi: "<p>9. Parts of the following sentence have been underlined and given as options. Select the option that contains an error.<br>She <span style=\"text-decoration: underline;\">will being studying</span> for her <span style=\"text-decoration: underline;\">exams tomorrow.</span></p>",
                    options_en: ["<p>studying</p>", "<p>exams</p>", 
                                "<p>tomorrow</p>", "<p>will being</p>"],
                    options_hi: ["<p>studying</p>", "<p>exams</p>",
                                "<p>tomorrow</p>", "<p>will being</p>"],
                    solution_en: "<p>9.(d) <strong>will being</strong><br>&lsquo;Will be + V-ing&rsquo; is the correct grammatical structure for the sentence which is in the future continuous tense. Hence, &lsquo;will be&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>9.(d) <strong>will being</strong><br>दिए गए वाक्य, जो future continuous tense में है, के लिए &lsquo;Will be + V-ing&rsquo;, सही grammatical structure है। अतः, &lsquo;will be&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the grammatically correct sentence.</p>",
                    question_hi: "<p>10. Select the grammatically correct sentence.</p>",
                    options_en: ["<p>Ragini plays the guitar melodiously.</p>", "<p>Ragini plays the guitar with melodiousness.</p>", 
                                "<p>Ragini plays the guitar melodious.</p>", "<p>Ragini plays the guitar with a melody.</p>"],
                    options_hi: ["<p>Ragini plays the guitar melodiously.</p>", "<p>Ragini plays the guitar with melodiousness.</p>",
                                "<p>Ragini plays the guitar melodious.</p>", "<p>Ragini plays the guitar with a melody.</p>"],
                    solution_en: "<p>10.(a) <strong>Ragini plays the guitar melodiously.</strong> <br>The given sentence needs an adverb to modify the meaning of the verb &lsquo;play&rsquo;. &lsquo;Melodiously&rsquo; is the correct adverb form. Hence, the sentence given in option (a) is grammatically correct.</p>",
                    solution_hi: "<p>10.(a) <strong>Ragini plays the guitar melodiously.</strong> <br>दिए गए sentence में verb &lsquo;play&rsquo; के अर्थ को modify करने के लिए एक adverb की आवश्यकता है। &lsquo;Melodiously&rsquo; सही adverb form है। अतः, option (a) में दिया गया sentence, grammatically सही है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. The following sentence has been divided into four parts. Identify the part that contains an error. <br>(A) I am hearing / (B) that the heat wave / (C) is going to be / (D) worse this year.</p>",
                    question_hi: "<p>11. The following sentence has been divided into four parts. Identify the part that contains an error. <br>(A) I am hearing / (B) that the heat wave / (C) is going to be / (D) worse this year.</p>",
                    options_en: ["<p>B</p>", "<p>D</p>", 
                                "<p>A</p>", "<p>C</p>"],
                    options_hi: ["<p>B</p>", "<p>D</p>",
                                "<p>A</p>", "<p>C</p>"],
                    solution_en: "<p>11.(c) <strong>A</strong><br>&lsquo;Hear&rsquo; is a stative verb. Stative verbs are generally not used in continuous tenses. Rather, we use simple present tense. The first form of the verb is used with pronoun &lsquo;I&rsquo;. Hence, &lsquo;hear(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>1</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>11.(c)<strong> A</strong><br>&lsquo;Hear&rsquo; एक stative verb है। Stative verb का प्रयोग सामान्यतः continuous tense में नहीं किया जाता है। इसके बजाय, हम simple present tense का प्रयोग करते हैं। pronoun &lsquo;I&rsquo; के साथ verb की 1st form का प्रयोग किया जाता है। अतः, &lsquo;hear(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>1</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. The following sentence has been split into four segments. Identify the segment that contains an INCORRECT preposition.<br>I am writing to invite you / to give a presentation / for our Jobs and Careers Conference / for engineering graduates.</p>",
                    question_hi: "<p>12. The following sentence has been split into four segments. Identify the segment that contains an INCORRECT preposition.<br>I am writing to invite you / to give a presentation / for our Jobs and Careers Conference / for engineering graduates.</p>",
                    options_en: ["<p>for engineering graduates</p>", "<p>I am writing to invite you</p>", 
                                "<p>for our Jobs and Careers Conference</p>", "<p>to give a presentation</p>"],
                    options_hi: ["<p>for engineering graduates</p>", "<p>I am writing to invite you</p>",
                                "<p>for our Jobs and Careers Conference</p>", "<p>to give a presentation</p>"],
                    solution_en: "<p>12.(c) <strong>for our Jobs and Careers Conference</strong><br>Preposition &lsquo;at&rsquo; is used to indicate a specific occasion or event. Similarly, in the given sentence, &lsquo;Jobs and Careers Conference&rsquo; is a specific event. Hence, &lsquo;at our jobs and careers conference&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(c) <strong>for our Jobs and Careers Conference</strong><br>Preposition &lsquo;at&rsquo; का प्रयोग किसी specific अवसर (occasion) या घटना (event) को इंगित करने के लिए किया जाता है। इसी तरह, दिए गए sentence में, &lsquo;Jobs and Careers Conference&rsquo; एक specific event है। अतः, &lsquo;at our jobs and careers conference&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. The following sentence has been divided into four parts. Identify the part that contains an error. <br>Pratigya arrived / to the / railway station / five hours early.</p>",
                    question_hi: "<p>13. The following sentence has been divided into four parts. Identify the part that contains an error. <br>Pratigya arrived / to the / railway station / five hours early.</p>",
                    options_en: ["<p>to the</p>", "<p>five hours early.</p>", 
                                "<p>Pratigya arrived</p>", "<p>railway station</p>"],
                    options_hi: ["<p>to the</p>", "<p>five hours early.</p>",
                                "<p>Pratigya arrived</p>", "<p>railway station</p>"],
                    solution_en: "<p>13.(a)<strong> to the</strong> <br>&lsquo;To&rsquo; must be replaced with &lsquo;at&rsquo; . We generally use &lsquo;at&rsquo; to describe specific location or position. Hence &lsquo;at the&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>13.(a) <strong>to the</strong> <br>&lsquo;To&rsquo; के स्थान पर &lsquo;at&rsquo; का प्रयोग होगा। हम सामान्यतः किसी विशेष स्थान (specific location) या स्थिति (position) को describe करने के लिए &lsquo;at&rsquo; का प्रयोग करते हैं। अतः &lsquo;at the&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>The companies were / giving in the prizes / to attract customers / during the festive season.</p>",
                    question_hi: "<p>14. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>The companies were / giving in the prizes / to attract customers / during the festive season.</p>",
                    options_en: ["<p>to attract customers</p>", "<p>The companies were</p>", 
                                "<p>giving in the prizes</p>", "<p>during the festive season</p>"],
                    options_hi: ["<p>to attract customers</p>", "<p>The companies were</p>",
                                "<p>giving in the prizes</p>", "<p>during the festive season</p>"],
                    solution_en: "<p>14.(c) <strong>giving in the prizes</strong><br>&lsquo;Giving in&rsquo; must be replaced with &lsquo;giving out&rsquo; . The phrasal verb &lsquo;giving out&rsquo; means to distribute or handing over something. While &lsquo;giving in&rsquo; means to surrender. The given sentence talks about distributing the prizes. Hence &lsquo;giving out the prizes&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>14.(c) <strong>giving in the prizes</strong><br>&lsquo;Giving in&rsquo; के स्थान पर &lsquo;Giving out&rsquo; का प्रयोग होगा। Phrasal verb &lsquo;giving out&rsquo; का अर्थ है किसी चीज़ को वितरित (distribute) करना या सौंपना (handing over)। जबकि &lsquo;giving in&rsquo; का अर्थ है आत्मसमर्पण (surrender) करना। दिया गया sentence, पुरस्कार(prize) Distribute करने के बारे में बात करता है। अतः &lsquo;giving out the prizes&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>This artwork / are a wonderful example / of the period\'s vogue / for realistic style.</p>",
                    question_hi: "<p>15. The following sentence has been split into four segments. Identify the segment that contains a grammatical error. <br>This artwork / are a wonderful example / of the period\'s vogue / for realistic style.</p>",
                    options_en: ["<p>are a wonderful example</p>", "<p>This artwork</p>", 
                                "<p>for realistic style</p>", "<p>of the period\'s vogue</p>"],
                    options_hi: ["<p>are a wonderful example</p>", "<p>This artwork</p>",
                                "<p>for realistic style</p>", "<p>of the period\'s vogue</p>"],
                    solution_en: "<p>15.(a)<strong> are a wonderful example</strong><br>According to the &ldquo;Subject-Verb Agreement Rule&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, &lsquo;This artwork&rsquo; is a singular subject that will take &lsquo;is&rsquo; as a singular verb. Hence, &lsquo;is a wonderful example&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>15.(a) <strong>are a wonderful example</strong><br>&ldquo;Subject-Verb Agreement Rule&rdquo; के अनुसार, एक singular subject के साथ हमेशा singular verb का तथा एक plural subject के साथ हमेशा plural verb का प्रयोग होता है। दिए गए sentence में, &lsquo;This artwork&rsquo; एक singular subject है जिसके साथ &lsquo;is&rsquo; singular verb का प्रयोग होगा। अतः, &lsquo;is a wonderful example&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>