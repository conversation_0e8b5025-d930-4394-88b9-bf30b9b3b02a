<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159216696.png\" alt=\"rId4\" width=\"123\" height=\"125\"></p>",
                    question_hi: "<p>1. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159216696.png\" alt=\"rId4\" width=\"123\" height=\"125\"></p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>11</p>",
                        "<p>13</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>11</p>",
                        "<p>13</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159216815.png\" alt=\"rId5\" width=\"182\" height=\"206\"><br>Total number of triangles = 10 + ABC + ABD + BCD = 13</p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159216815.png\" alt=\"rId5\" width=\"182\" height=\"206\"><br>त्रिभुजों की कुल संख्या = 10 + ABC + ABD + BCD = 13</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. If \'W\' stands for \'&divide;\', \'Y\' stands for \'&times;\', \'Z\' stands for \'+\' and \'A\' stands for &lsquo;-&rsquo;, what will come in place of the question mark (?) in the following equation ?<br>11 W 11 Y 11 Z 11 A 11 = ?</p>",
                    question_hi: "<p>2. यदि \'W का अर्थ \'&divide;\', \'Y\' का अर्थ \'&times;\', \'Z\' का अर्थ \'+&rsquo; और \'A\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण मे प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>11 W 11 Y 11 Z 11 A 11 = ?</p>",
                    options_en: [
                        "<p>21</p>",
                        "<p>11</p>",
                        "<p>42</p>",
                        "<p>22</p>"
                    ],
                    options_hi: [
                        "<p>21</p>",
                        "<p>11</p>",
                        "<p>42</p>",
                        "<p>22</p>"
                    ],
                    solution_en: "<p>2.(b) <strong>Given :-</strong> 11 W 11 Y 11 Z 11 A 11 <br>As per given instruction after interchanging the letter with sign we get<br>11 &divide;&nbsp;11 &times; 11 + 11 - 11<br>1 &times; 11 + 0 = 11</p>",
                    solution_hi: "<p>2.(b) <strong>दिया गया :- </strong>11 W 11 Y 11 Z 11 A 11<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>11 &divide; 11 &times; 11 + 11 - 11<br>1 &times; 11 + 0 = 11</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the pair which follows the same pattern as that followed by the two set of pairs given below. Both pairs follow the same pattern.<br>DFG : IKL<br>MOP : RTU</p>",
                    question_hi: "<p>3. उस युग्म को चुनिए, जो उसी पैटर्न का अनुसरण करता है जो नीचे दिए गए युग्मों के दो समुच्चय द्वारा किया जाता है। दोनों युग्म समान पैटर्न का अनुसरण करते हैं।<br>DFG : IKL<br>MOP : RTU</p>",
                    options_en: [
                        "<p>TVW : YAB</p>",
                        "<p>FHI : EGH</p>",
                        "<p>WYZ : GIJ</p>",
                        "<p>PST : KHG</p>"
                    ],
                    options_hi: [
                        "<p>TVW : YAB</p>",
                        "<p>FHI : EGH</p>",
                        "<p>WYZ : GIJ</p>",
                        "<p>PST : KHG</p>"
                    ],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159216945.png\" alt=\"rId6\" width=\"296\" height=\"115\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217055.png\" alt=\"rId7\" width=\"135\" height=\"106\"></p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159216945.png\" alt=\"rId6\" width=\"296\" height=\"115\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217055.png\" alt=\"rId7\" width=\"135\" height=\"106\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In this question, three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>Some rings are gold.<br>All gold is hot.<br>Some hot are red.<br><strong>Conclusions :</strong><br>I. Some gold are rings.<br>II.Some hot are gold.<br>III.Some red are hot.</p>",
                    question_hi: "<p>4. इस प्रश्न में, तीन कथन और उसके बाद तीन निष्कर्ष क्रमांक I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं।<br><strong>कथन :</strong><br>कुछ अंगूठियां, सोना हैं।<br>सभी सोना, गर्म है।<br>कुछ गर्म, लाल हैं।<br><strong>निष्कर्ष :</strong><br>I.कुछ सोना, अंगूठियां हैं।<br>II.कुछ गर्म, सोना हैं।<br>III.कुछ लाल, गर्म हैं।</p>",
                    options_en: [
                        "<p>All the conclusions follow.</p>",
                        "<p>Only conclusion III follows.</p>",
                        "<p>Only conclusion I follows.</p>",
                        "<p>Only conclusion II follows.</p>"
                    ],
                    options_hi: [
                        "<p>सभी निष्कर्ष अनुसरण करते हैं।</p>",
                        "<p>केवल निष्कर्ष III अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217175.png\" alt=\"rId8\" width=\"321\" height=\"111\"><br>All the conclusion follow</p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217306.png\" alt=\"rId9\" width=\"330\" height=\"108\"><br>सभी निष्कर्ष अनुसरण करते हैं</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>30 &minus; 10 &divide; 63 &times; 9 + 15 = ?</p>",
                    question_hi: "<p>5. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&lsquo; को आपस में बदल दिया जाए तथा &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में \'?\' के स्थान पर क्या आएगा ?<br>30 &minus; 10 &divide; 63 &times; 9 + 15 = ?</p>",
                    options_en: [
                        "<p>80</p>",
                        "<p>85</p>",
                        "<p>90</p>",
                        "<p>87</p>"
                    ],
                    options_hi: [
                        "<p>80</p>",
                        "<p>85</p>",
                        "<p>90</p>",
                        "<p>87</p>"
                    ],
                    solution_en: "<p>5.(b) <strong>Given :- </strong>30 - 10 &divide; 63 &times; 9 + 15<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get<br>30 + 10 &times; 63 &divide; 9 - 15<br>30 + 70 - 15<br>100 - 15 = 85</p>",
                    solution_hi: "<p>5.(b) <strong>दिया गया :- </strong>30 - 10 &divide; 63 &times; 9 + 15<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>30 + 10 &times; 63 &divide; 9 - 15<br>30 + 70 - 15<br>100 - 15 = 85</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. The position of how many letters will remain unchanged if each of the letters in the word WANDERLUST is arranged from left to right in alphabetical order ?</p>",
                    question_hi: "<p>6. यदि शब्द WANDERLUST के प्रत्येक अक्षर को बाएं से दाएं वर्णमाला क्रम में व्यवस्थित किया जाता है, तो कितने अक्षरों का स्थान परिवर्तित नहीं होगा ?</p>",
                    options_en: [
                        "<p>Three</p>",
                        "<p>One</p>",
                        "<p>None</p>",
                        "<p>Two</p>"
                    ],
                    options_hi: [
                        "<p>तीन</p>",
                        "<p>एक</p>",
                        "<p>किसी का भी नहीं</p>",
                        "<p>दो</p>"
                    ],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217434.png\" alt=\"rId10\" width=\"274\" height=\"83\"><br>The position of only one letter remains unchanged.</p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217434.png\" alt=\"rId10\" width=\"274\" height=\"83\"><br>केवल एक अक्षर का स्थान अपरिवर्तित रहता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What should come in place of the question mark (?) in the given series ?<br>499, 498, 490, 463, 399, ?</p>",
                    question_hi: "<p>7. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>499, 498, 490, 463, 399, ?</p>",
                    options_en: [
                        "<p>273</p>",
                        "<p>274</p>",
                        "<p>276</p>",
                        "<p>275</p>"
                    ],
                    options_hi: [
                        "<p>273</p>",
                        "<p>274</p>",
                        "<p>276</p>",
                        "<p>275</p>"
                    ],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217604.png\" alt=\"rId11\" width=\"245\" height=\"89\"></p>",
                    solution_hi: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217604.png\" alt=\"rId11\" width=\"245\" height=\"89\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Which of the following letter numeric clusters will replace the question mark (?) in the given series to make it logically complete ?<br>JX 26, KY 31, ?, PD 47, TH58</p>",
                    question_hi: "<p>8. निम्नलिखित में से कौन-सा अक्षर-संख्या समूह दी गई श्रृंखला को तार्किक रूप से पूरा करने के लिए प्रश्न चिह्न (?) के स्थान पर आएगा ?<br>JX 26, KY 31, ?, PD 47, TH58</p>",
                    options_en: [
                        "<p>&Mu;&Alpha; 38</p>",
                        "<p>NA 38</p>",
                        "<p>NB 37</p>",
                        "<p>MB 37</p>"
                    ],
                    options_hi: [
                        "<p>&Mu;&Alpha; 38</p>",
                        "<p>NA 38</p>",
                        "<p>NB 37</p>",
                        "<p>MB 37</p>"
                    ],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217763.png\" alt=\"rId12\" width=\"331\" height=\"101\"></p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217763.png\" alt=\"rId12\" width=\"331\" height=\"101\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements. <br><strong>Statements:</strong> <br>All eggs are chickens. <br>No chicken is a hen. <br><strong>Conclusions:</strong> <br>(I) All chickens are eggs. <br>(II) No egg is a hen.</p>",
                    question_hi: "<p>9. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, चाहे वह सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होती हो और निर्णय लीजिए कि दिए गए निष्कर्षों में से कौन-सा/कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन:</strong> <br>सभी अंडे, चूजे हैं। <br>कोई चूजा, मुर्गी नहीं है। <br><strong>निष्कर्ष:</strong> <br>(I) सभी चूजे, अंडे हैं। <br>(II) कोई अंडा, मुर्गी नहीं है।</p>",
                    options_en: [
                        "<p>Only conclusion II follows</p>",
                        "<p>Neither conclusion I nor II follows</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>Both conclusions I and II follow</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष II अनुसरण करता है।</p>",
                        "<p>न तो निष्कर्ष I और न ही निष्कर्ष II अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>",
                        "<p>निष्कर्ष I और निष्कर्ष II दोनों अनुसरण करते है।</p>"
                    ],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159217892.png\" alt=\"rId13\" width=\"221\" height=\"82\"><br>Only conclusion II follow.</p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218010.png\" alt=\"rId14\" width=\"212\" height=\"79\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the figure from among the given options that can replace the question mark (?) in the following series and complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218165.png\" alt=\"rId15\" width=\"372\" height=\"78\"></p>",
                    question_hi: "<p>10. दिए गए विकल्पों में से उस आकृति को चुनिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आ सकती है और पैटर्न को पूरा कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218165.png\" alt=\"rId15\" width=\"372\" height=\"78\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218340.png\" alt=\"rId16\" width=\"90\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218471.png\" alt=\"rId17\" width=\"90\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218641.png\" alt=\"rId18\" width=\"90\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218770.png\" alt=\"rId19\" width=\"90\" height=\"93\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218340.png\" alt=\"rId16\" width=\"91\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218471.png\" alt=\"rId17\" width=\"90\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218641.png\" alt=\"rId18\" width=\"90\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218770.png\" alt=\"rId19\" width=\"91\" height=\"94\"></p>"
                    ],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218471.png\" alt=\"rId17\" width=\"91\" height=\"93\"></p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218471.png\" alt=\"rId17\" width=\"91\" height=\"93\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a code language, \'bus car bike\' is written as \'az bz ez\', \'bicycle tyre car\' is written as \'bz fz pz\', and \'scooter bus tyre\' is written as \'az pz sz\'. What is the code for the word \'bicycle\' in that language ?</p>",
                    question_hi: "<p>11. एक कूट भाषा में, \'bus car bike\' को \'az bz ez\' लिखा जाता है, \'bicycle tyre car\' को \'bz fz pz\' लिखा जाता है और \'scooter bus tyre\' को \'az pz sz\' लिखा जाता है। उस भाषा में \'bicycle\' शब्द के लिए क्या कूट है ?</p>",
                    options_en: [
                        "<p>pz</p>",
                        "<p>sz</p>",
                        "<p>ez</p>",
                        "<p>fz</p>"
                    ],
                    options_hi: [
                        "<p>pz</p>",
                        "<p>sz</p>",
                        "<p>ez</p>",
                        "<p>fz</p>"
                    ],
                    solution_en: "<p>11.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218886.png\" alt=\"rId20\" width=\"287\" height=\"115\"><br>The code of &lsquo;bicycle&rsquo; = &lsquo;fz&rsquo;</p>",
                    solution_hi: "<p>11.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159218886.png\" alt=\"rId20\" width=\"287\" height=\"115\"><br>&lsquo;bicycle&rsquo; का कोड = \'fz\'</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br>Carpenter : Furniture</p>",
                    question_hi: "<p>12. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>बढ़ई : फर्नीचर</p>",
                    options_en: [
                        "<p>Book : Author</p>",
                        "<p>Cook : Soup</p>",
                        "<p>Dam : Engineer</p>",
                        "<p>Magazine : Editor</p>"
                    ],
                    options_hi: [
                        "<p>पुस्तक : लेखक</p>",
                        "<p>रसोइया : सूप</p>",
                        "<p>बांध : इंजीनियर</p>",
                        "<p>पत्रिका : संपादक</p>"
                    ],
                    solution_en: "<p>12.(b) As Carpenter makes Furniture similarly Cook makes Soup.</p>",
                    solution_hi: "<p>12.(b) जैसे बढ़ई फर्नीचर बनाता है वैसे ही रसोइया सूप बनाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language, \'HONOR\' is coded as 16-30-28-30-36 and \'PLEAD\' is coded as 32-24-10-2-8. How will \'LOCK\' be coded in the same language ?</p>",
                    question_hi: "<p>13. एक निश्चित कूट भाषा में, &lsquo;HONOR&rsquo; को 16-30-28-30-36 के रूप में कूटबद्ध किया जाता है और &lsquo;PLEAD&rsquo; को 32-24-10-2-8 के रूप में कूटबद्ध किया जाता है। उसी भाषा में &lsquo;LOCK&rsquo; को किस प्रकार कूटबद्ध किया जाएगा ?</p>",
                    options_en: [
                        " 24-30-6-22 ",
                        " 24-32-6-22 ",
                        " 26-30-8-22 ",
                        " 26-32-8-20<br /> "
                    ],
                    options_hi: [
                        " 24-30-6-22 ",
                        " 24-32-6-22 ",
                        " 26-30-8-22 ",
                        " 26-32-8-20"
                    ],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219024.png\" alt=\"rId21\" width=\"156\" height=\"104\">&nbsp; &nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219151.png\" alt=\"rId22\" width=\"149\" height=\"102\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219257.png\" alt=\"rId23\" width=\"127\" height=\"106\"></p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219024.png\" alt=\"rId21\" width=\"156\" height=\"104\">&nbsp; &nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219151.png\" alt=\"rId22\" width=\"149\" height=\"102\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219257.png\" alt=\"rId23\" width=\"127\" height=\"106\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the option that represents the letters that, when sequentially placed from left to right in the blanks below, will complete the letter series.<br>_ D R G _ Z D R _ B Z D _ G B Z _ R G B</p>",
                    question_hi: "<p>14. उस विकल्प का चयन कीजिए जो उन अक्षरों को दर्शाता है, जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ क्रमिक रूप से रखने पर अक्षर श्रृंखला पूरी हो जाएगी।<br>_ D R G _ Z D R _ B Z D _ G B Z _ R G B</p>",
                    options_en: [
                        "<p>ZBDRD</p>",
                        "<p>BBGRD</p>",
                        "<p>ZBGRB</p>",
                        "<p>ZBGRD</p>"
                    ],
                    options_hi: [
                        "<p>ZBDRD</p>",
                        "<p>BBGRD</p>",
                        "<p>ZBGRB</p>",
                        "<p>ZBGRD</p>"
                    ],
                    solution_en: "<p>14.(d)<br><span style=\"text-decoration: underline;\"><strong>Z</strong></span> D R G <span style=\"text-decoration: underline;\"><strong>B</strong></span>/ Z D R <span style=\"text-decoration: underline;\"><strong>G</strong></span> B/ Z D <span style=\"text-decoration: underline;\"><strong>R</strong></span> G B/ Z <span style=\"text-decoration: underline;\"><strong>D</strong></span> R G B</p>",
                    solution_hi: "<p>14.(d)<br><span style=\"text-decoration: underline;\"><strong>Z</strong></span> D R G <span style=\"text-decoration: underline;\"><strong>B</strong></span>/ Z D R <span style=\"text-decoration: underline;\"><strong>G</strong></span> B/ Z D <span style=\"text-decoration: underline;\"><strong>R</strong></span> G B/ Z <span style=\"text-decoration: underline;\"><strong>D</strong></span> R G B</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. The sequence of folding a piece of paper and the manner in which the folded paper has been cut is shown below. Choose the figure which would most closely resemble the unfolded form of the paper.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219368.png\" alt=\"rId24\" width=\"318\" height=\"92\"></p>",
                    question_hi: "<p>15. कागज़ के एक टुकड़े को मोड़ने का क्रम और मोड़े गए कागज़ को काटने का तरीका नीचे दर्शाया गया है। उस आकृति का चयन कीजिए जो कागज़ के खुले हुए रूप से सबसे अधिक मिलती-जुलती हो।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219368.png\" alt=\"rId24\" width=\"318\" height=\"92\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219535.png\" alt=\"rId25\" width=\"90\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219634.png\" alt=\"rId26\" width=\"94\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219734.png\" alt=\"rId27\" width=\"91\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219837.png\" alt=\"rId28\" width=\"90\" height=\"95\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219535.png\" alt=\"rId25\" width=\"90\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219634.png\" alt=\"rId26\" width=\"91\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219734.png\" alt=\"rId27\" width=\"92\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219837.png\" alt=\"rId28\" width=\"91\" height=\"96\"></p>"
                    ],
                    solution_en: "<p>15.(d)<br>&nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219837.png\" alt=\"rId28\" width=\"93\" height=\"98\"></p>",
                    solution_hi: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219837.png\" alt=\"rId28\" width=\"93\" height=\"98\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219940.png\" alt=\"rId29\" width=\"107\" height=\"112\"></p>",
                    question_hi: "<p>16. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159219940.png\" alt=\"rId29\" width=\"107\" height=\"112\"></p>",
                    options_en: [
                        "<p><strong id=\"docs-internal-guid-fedfb233-7fff-3f53-a8a0-e126518fcdd0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfsJMpRb68DmmATweeBasqns6CmpY4eYwiM1y2JCW7e69CIoiwazjAFYIsHHVbFxb0JsvHpbqDcVbuhCuaoHBY1QCS7o0BwFldBgxvq9LVgsuNPS0ejaqoRHab7OEETDCmZaEYYgQ?key=kHaBaDqF3lVOuQ7-3zU-aZaE\" width=\"104\" height=\"19\"></strong></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220168.png\" alt=\"rId31\" width=\"100\" height=\"18\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220267.png\" alt=\"rId32\" width=\"104\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220353.png\" alt=\"rId33\" width=\"103\" height=\"17\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220044.png\" alt=\"rId30\" width=\"104\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220168.png\" alt=\"rId31\" width=\"105\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220267.png\" alt=\"rId32\" width=\"104\" height=\"19\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220353.png\" alt=\"rId33\" width=\"103\" height=\"17\"></p>"
                    ],
                    solution_en: "<p>16.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220044.png\" alt=\"rId30\" width=\"110\" height=\"20\"></p>",
                    solution_hi: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220044.png\" alt=\"rId30\" width=\"110\" height=\"20\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?&nbsp;<br>YAU, DFZ, IKE, NPJ, ?</p>",
                    question_hi: "<p>17. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न चिह्न &lsquo;?\' के स्थान पर क्या आना चाहिए ?<br>YAU, DFZ, IKE, NPJ, ?</p>",
                    options_en: [
                        "<p>RUS</p>",
                        "<p>SOU</p>",
                        "<p>SRU</p>",
                        "<p>SUO</p>"
                    ],
                    options_hi: [
                        "<p>RUS</p>",
                        "<p>SOU</p>",
                        "<p>SRU</p>",
                        "<p>SUO</p>"
                    ],
                    solution_en: "<p>17.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220486.png\" alt=\"rId34\" width=\"412\" height=\"118\"></p>",
                    solution_hi: "<p>17.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220486.png\" alt=\"rId34\" width=\"412\" height=\"118\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent&nbsp;digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(108, 27, 9)<br>(132, 33, 11)</p>",
                    question_hi: "<p>18. उस समुच्चय का चयन करें जिसकी संख्याएं उसी तरह से संबंधित हैं जिस तरह निम्नलिखित समुच्चयों की संख्याएं संबंधित हैं।<br>(<strong>नोट : </strong>संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)<br>(108, 27, 9)<br>(132, 33, 11)</p>",
                    options_en: [
                        "<p>(180, 45, 10)</p>",
                        "<p>(180, 55, 15)</p>",
                        "<p>(180, 45, 15)</p>",
                        "<p>(190, 45, 15)</p>"
                    ],
                    options_hi: [
                        "<p>(180, 45, 10)</p>",
                        "<p>(180, 55, 15)</p>",
                        "<p>(180, 45, 15)</p>",
                        "<p>(190, 45, 15)</p>"
                    ],
                    solution_en: "<p>18.(c) <strong>Logic :- </strong>(2nd number + 3rd number) &times; 3 = 1st number<br>(108, 27, 9) :- (27 + 9) &times; 3 &rArr; (36) &times; 3 = 108<br>(132, 33,11) :- (33 + 11) &times; 3 &rArr; (44) &times; 3 = 132<br>Similarly,<br>(180, 45, 15) :- (45 + 15) &times; 3 &rArr; (60) &times; 3 = 180</p>",
                    solution_hi: "<p>18.(c) <strong>तर्क :- </strong>(दूसरी संख्या + तीसरी संख्या) &times; 3 = पहली संख्या<br>(108, 27, 9) :- (27 + 9) &times; 3 &rArr; (36) &times; 3 = 108<br>(132, 33,11) :- (33 + 11) &times; 3 &rArr; (44)&times; 3 = 132<br>इसी प्रकार,<br>(180, 45, 15) :- (45 + 15) &times; 3 &rArr; (60) &times; 3 = 180</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the set in which the numbers are related in the same way as are the numbers of&nbsp;the following sets.<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking&nbsp;down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as&nbsp;adding /subtracting /multiplying etc. to 13 can be performed. Breaking down 13 into 1&nbsp;and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(21, 34, 76)<br>(39, 26, 104)</p>",
                    question_hi: "<p>19. उस समुच्य का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्यों की संख्याएं संबंधित हैं।<br>(<strong>निर्देश :</strong>) संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13- संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संख्याएं करने की अनुमति नहीं है।)<br>(21, 34, 76)<br>(39, 26, 104)</p>",
                    options_en: [
                        "<p>(28, 23, 79)</p>",
                        "<p>(34, 25, 102)</p>",
                        "<p>(25, 31, 108)</p>",
                        "<p>(37, 43, 127)</p>"
                    ],
                    options_hi: [
                        "<p>(28, 23, 79)</p>",
                        "<p>(34, 25, 102)</p>",
                        "<p>(25, 31, 108)</p>",
                        "<p>(37, 43, 127)</p>"
                    ],
                    solution_en: "<p>19.(a) <strong>Logic :- </strong>(3rd number - 2nd number) &divide;&nbsp;2 = 1st number<br>(21, 34, 76) :- (76 - 34) &divide; 2 &rArr; (42) &divide; 2 = 21<br>(39, 26, 104) :- (104 - 26) &divide; 2 &rArr; (78) &divide; 2 = 39<br>Similarly,<br>(28, 23, 79) :- (79 -23) &divide; 2 &rArr; (56) &divide; 2 = 28</p>",
                    solution_hi: "<p>19.(a) <strong>तर्क :- </strong>(तीसरी संख्या - दूसरी संख्या) &divide; 2 = पहली संख्या<br>(21, 34, 76) :- (76 - 34) &divide; 2 &rArr; (42) &divide; 2 = 21<br>(39, 26, 104) :- (104 - 26) &divide; 2 &rArr; (78) &divide; 2 = 39<br>इसी प्रकार,<br>(28, 23, 79) :- (79 -23) &divide; 2 &rArr; (56) &divide; 2 = 28</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the figure that will come next in the following figure series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220594.png\" alt=\"rId35\" width=\"284\" height=\"67\"></p>",
                    question_hi: "<p>20. उस आकृति का चयन कीजिए, जो निम्नलिखित आकृति श्रृंखला में आगे आएगी। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220594.png\" alt=\"rId35\" width=\"284\" height=\"67\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220698.png\" alt=\"rId36\" width=\"84\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220824.png\" alt=\"rId37\" width=\"84\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220912.png\" alt=\"rId38\" width=\"84\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221004.png\" alt=\"rId39\" width=\"85\" height=\"78\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220698.png\" alt=\"rId36\" width=\"84\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220824.png\" alt=\"rId37\" width=\"84\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159220912.png\" alt=\"rId38\" width=\"84\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221004.png\" alt=\"rId39\" width=\"84\" height=\"77\"></p>"
                    ],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221004.png\" alt=\"rId39\" width=\"84\" height=\"77\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221004.png\" alt=\"rId39\" width=\"84\" height=\"77\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the correct mirror image of the given combination when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221105.png\" alt=\"rId40\" width=\"139\" height=\"124\"></p>",
                    question_hi: "<p>21. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221105.png\" alt=\"rId40\" width=\"139\" height=\"124\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221205.png\" alt=\"rId41\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221330.png\" alt=\"rId42\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221455.png\" alt=\"rId43\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221573.png\" alt=\"rId44\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221205.png\" alt=\"rId41\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221330.png\" alt=\"rId42\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221455.png\" alt=\"rId43\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221573.png\" alt=\"rId44\"></p>"
                    ],
                    solution_en: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221205.png\" alt=\"rId41\"></p>",
                    solution_hi: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221205.png\" alt=\"rId41\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option that is related to the fifth number in the same way as the second number is related to the first number and the fourth number is related to the third number. <br>45 : 56 :: 62 : 73 :: 19 : ? <br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>22. उस विकल्प का चयन करें जो पांचवीं संख्या से उसी प्रकार संबंधित हो जिस प्रकार दूसरी संख्या पहली संख्या से संबंधित है और चौथी संख्या तीसरी संख्या से संबंधित है।<br>45 : 56 :: 62 : 73 :: 19 : ?<br>नोट: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: [
                        "<p>31</p>",
                        "<p>30</p>",
                        "<p>27</p>",
                        "<p>28</p>"
                    ],
                    options_hi: [
                        "<p>31</p>",
                        "<p>30</p>",
                        "<p>27</p>",
                        "<p>28</p>"
                    ],
                    solution_en: "<p>22.(b)<br><strong>Logic:- </strong>1<sup>st</sup> no&nbsp;<strong>+ 11 =</strong> 2<sup>nd</sup>no.<br>(45 : 56) :- 45 <strong>+ 11 =</strong> 56<br>(62 : 73) :- 62 <strong>+ 11 =</strong> 73<br>Similarly <br>(19, ?) :- 19 <strong>+ 11 =</strong> 30</p>",
                    solution_hi: "<p>22.(b)<br><strong>तर्क:- </strong>पहली संख्या <strong>+ 11 =</strong> दूसरी संख्या<br>(45 : 56) :- 45 <strong>+ 11 =</strong> 56<br>(62 : 73) :- 62 <strong>+ 11 =</strong> 73<br>इसी प्रकार <br>(19, ?) :- 19 <strong>+ 11 =</strong> 30</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. A square sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would the paper look when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221698.png\" alt=\"rId45\" width=\"254\" height=\"86\"></p>",
                    question_hi: "<p>23. कागज की एक वर्गाकार शीट को दर्शाई गई दिशाओ में बिंदीदार रेखा पर अनुक्रमशः मोड़ा जाता है और आखिर में उसमें छिद्र किया जाता है। खोलने पर यह कागज कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221698.png\" alt=\"rId45\" width=\"254\" height=\"86\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221852.png\" alt=\"rId46\" width=\"101\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221953.png\" alt=\"rId47\" width=\"103\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222087.png\" alt=\"rId48\" width=\"103\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222192.png\" alt=\"rId49\" width=\"102\" height=\"99\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221852.png\" alt=\"rId46\" width=\"102\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221953.png\" alt=\"rId47\" width=\"103\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222087.png\" alt=\"rId48\" width=\"104\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222192.png\" alt=\"rId49\" width=\"102\" height=\"99\"></p>"
                    ],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221852.png\" alt=\"rId46\" width=\"103\" height=\"93\"></p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159221852.png\" alt=\"rId46\" width=\"103\" height=\"93\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222298.png\" alt=\"rId50\" width=\"117\" height=\"139\"></p>",
                    question_hi: "<p>24. उस विकल्प आकृति का चयन कीजिए जिसमें, दी गई आकृति (X), उसके एक भाग के रूप में सन्निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222298.png\" alt=\"rId50\" width=\"117\" height=\"139\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222425.png\" alt=\"rId51\" width=\"100\" height=\"101\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222528.png\" alt=\"rId52\" width=\"100\" height=\"101\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222662.png\" alt=\"rId53\" width=\"100\" height=\"101\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222759.png\" alt=\"rId54\" width=\"100\" height=\"101\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222425.png\" alt=\"rId51\" width=\"100\" height=\"101\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222528.png\" alt=\"rId52\" width=\"100\" height=\"101\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222662.png\" alt=\"rId53\" width=\"100\" height=\"101\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222759.png\" alt=\"rId54\" width=\"100\" height=\"101\"></p>"
                    ],
                    solution_en: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222871.png\" alt=\"rId55\" width=\"101\" height=\"98\"></p>",
                    solution_hi: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222871.png\" alt=\"rId55\" width=\"101\" height=\"98\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In a certain code language,<br>M &amp; N means &lsquo;M is the husband of N&rsquo;,<br>M @ N means &lsquo;M is the brother of N&rsquo;,<br>M $ N means &lsquo;M is the father of N&rsquo;,<br>M # N means &lsquo;M is the mother of N&rsquo;.<br>Based on the above, how is F related to J if &lsquo;F &amp; G # H @ I $ J&rsquo; ?</p>",
                    question_hi: "<p>25. एक निश्चित कूट भाषा में,<br>M &amp; N का अर्थ है &lsquo;M, N का पति है&rsquo;,<br>M @ N का अर्थ है &lsquo;M, N का भाई है&rsquo;,<br>M $ N का अर्थ है &lsquo;M, N का पिता है&rsquo;<br>M # N का अर्थ है \' M, N की माता है&rsquo;।<br>उपरोक्त के आधार पर, यदि &lsquo;F &amp; G # H @ I $ J&rsquo; है तो F, J से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        "<p>Mother&rsquo;s father</p>",
                        "<p>Father&rsquo;s father</p>",
                        "<p>Mother</p>",
                        "<p>Sister</p>"
                    ],
                    options_hi: [
                        "<p>नाना</p>",
                        "<p>दादा</p>",
                        "<p>माता</p>",
                        "<p>बहन</p>"
                    ],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222980.png\" alt=\"rId56\" width=\"178\" height=\"209\"><br>F is the father&rsquo;s father of J.</p>",
                    solution_hi: "<p>25.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159222980.png\" alt=\"rId56\" width=\"178\" height=\"209\"><br>F, J के दादा है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Identify the crop which is grown in Rabi season in North India from the following.</p>",
                    question_hi: "<p>26. निम्नलिखित में से उत्तर भारत में रबी मौसम में उगाई जाने वाली फसल की पहचान करें।</p>",
                    options_en: [
                        "<p>Rice</p>",
                        "<p>Cotton</p>",
                        "<p>Gram</p>",
                        "<p>Bajra</p>"
                    ],
                    options_hi: [
                        "<p>चावल</p>",
                        "<p>कपास</p>",
                        "<p>चना</p>",
                        "<p>बाजरा</p>"
                    ],
                    solution_en: "<p>26.(c) <strong>Gram.</strong> Cropping Season in India: Rabi (sown in mid-November and harvested in April/May) - Barley, Gram, Rapeseed, Mustard, Wheat. Kharif (sown in June and Harvested in October) Bajra, Jowar, Maize (corn), jute, Rice, Soybean. Zaid (sown and harvested between Rabi and Kharif) - Seasonal fruits, vegetables, fodder, Cucumber.</p>",
                    solution_hi: "<p>26.(c) <strong>चना। </strong>भारत की फसल ऋतुएँ : रबी (नवंबर के मध्य में बोया जाता है और अप्रैल/मई में काटा जाता है) - जौ, चना, रेपसीड, सरसों, गेहूं। ख़रीफ़ (जून में बोया जाता है और अक्टूबर में काटा जाता है) बाजरा, ज्वार, मक्का, जूट, चावल, सोयाबीन। जायद (रबी और ख़रीफ़ के बीच बोया और काटा जाता है) - मौसमी फल, सब्जियाँ, चारा, खीरा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. When is Maha Shivaratri celebrated according to the Drik Panchang ?</p>",
                    question_hi: "<p>27. द्रिक पंचांग के अनुसार महाशिवरात्रि कब मनाई जाती है ?</p>",
                    options_en: [
                        "<p>Magha, Krishna Paksha, Chaturdashi</p>",
                        "<p>Ashadh, Krishna Paksha, Trayodashi</p>",
                        "<p>Phalguna, Shukla Paksha, Ekadashi</p>",
                        "<p>Chaitra, Shukla Paksha, Ekam</p>"
                    ],
                    options_hi: [
                        "<p>माघ, कृष्ण पक्ष, चतुर्दशी</p>",
                        "<p>आषाढ़, कृष्ण पक्ष, त्रयोदशी</p>",
                        "<p>फाल्गुन, शुक्ल पक्ष, एकादशी</p>",
                        "<p>चैत्र , शुक्ल पक्ष, एकम</p>"
                    ],
                    solution_en: "<p>27.(a) <strong>Magha, Krishna Paksha, Chaturdashi. </strong>Maha Shivaratri is a Hindu festival celebrated annually in honour of the God Shiva. It is held on the 13th night (waning moon) and the 14th day of the month Phalguna. In the Gregorian calendar, the day falls in either February or March.</p>",
                    solution_hi: "<p>27.(a) <strong>माघ, कृष्ण पक्ष, चतुर्दशी</strong>। महा शिवरात्रि, भगवान शिव के सम्मान में प्रतिवर्ष मनाया जाने वाला एक हिंदू त्योहार है। यह 13वें रात (ढलते चंद्रमा) के और फाल्गुन महीने के 14वें दिन मनाया जाता है। ग्रेगोरियन कैलेंडर में, यह दिन फरवरी या मार्च में पड़ता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. In the Right-Hand Thumb Rule, the thumb is directed towards the direction of :</p>",
                    question_hi: "<p>28. दाहिने हाथ के अँगूठे के नियम में, अंगूठे को निम्नलिखित में से किसकी दिशा की ओर निर्देशित किया जाता है ?</p>",
                    options_en: [
                        "<p>current</p>",
                        "<p>electric field</p>",
                        "<p>motion of the conductor</p>",
                        "<p>magnetic field</p>"
                    ],
                    options_hi: [
                        "<p>विद्युत धारा</p>",
                        "<p>विद्युत क्षेत्र</p>",
                        "<p>चालक की गति</p>",
                        "<p>चुंबकीय क्षेत्र</p>"
                    ],
                    solution_en: "<p>28.(a) <strong>current. </strong>The right-hand thumb rule states that if you hold a current-carrying wire with your right hand, with your thumb pointing in the direction of the electric current, the direction your fingers curl indicates the direction of the magnetic field lines. This rule helps visualize the relationship between current and magnetic fields.</p>",
                    solution_hi: "<p>28.(a) <strong>विद्युत धारा।</strong> दाएं हाथ के अंगूठे का नियम कहता है कि यदि आप अपने दाएं हाथ से धारा प्रवाहित होने वाले तार को पकड़ते हैं, तो आपका अंगूठा विद्युत धारा की दिशा को इंगित करता है, आपकी उंगलियों की दिशा चुंबकीय क्षेत्र रेखाओं की दिशा को इंगित करती है। यह नियम विद्युत धारा और चुंबकीय क्षेत्रों के बीच के संबंध को समझने में मदद करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who was honored with the Jamsetji Tata Award by the Indian Society for Quality (ISQ) in December 2024 ?<strong id=\"docs-internal-guid-118c1db5-7fff-a1f2-525e-8fb1f8ac702b\"><br></strong></p>",
                    question_hi: "<p>29. दिसंबर 2024 में भारतीय समाज गुणवत्ता (ISQ) द्वारा जमशेदजी टाटा पुरस्कार से किसे सम्मानित किया गया ?</p>",
                    options_en: [
                        "<p>Ratan Tata</p>",
                        "<p>Kiran Mazumdar-Shaw</p>",
                        "<p>Mukesh Ambani<strong id=\"docs-internal-guid-dbb3c0c0-7fff-0fdc-b424-1adf8d642e1a\"><br></strong></p>",
                        "<p dir=\"ltr\">Sundar Pichai</p>"
                    ],
                    options_hi: [
                        "<p>रतन टाटा</p>",
                        "<p>&nbsp;किरण मजूमदार-शॉ<strong id=\"docs-internal-guid-8693c573-7fff-156c-d374-e6dcd9548bb3\"><br></strong></p>",
                        "<p>मुकेश अंबानी</p>",
                        "<p>सुंदर पिचाई</p>"
                    ],
                    solution_en: "<p>29.(b) <strong>Kiran Mazumdar-Shaw.</strong> Biocon Limited, founded in 1978 by Kiran Mazumdar-Shaw in Bengaluru, Karnataka, is a leading biopharmaceutical company. As India&rsquo;s first female brewer, she transformed it into a global biotechnology leader.</p>",
                    solution_hi: "<p>29.(b) <strong>किरण मजूमदार-शॉ।</strong> बायोकॉन लिमिटेड, जिसे 1978 में किरण मजूमदार-शॉ ने बेंगलुरु, कर्नाटक में स्थापित किया था, एक प्रमुख बायोफार्मास्युटिकल कंपनी है। भारत की पहली महिला ब्रूअर के रूप में, उन्होंने इसे एक वैश्विक बायोटेक्नोलॉजी लीडर में बदल दिया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. What is the function of cytochrome P450 ?</p>",
                    question_hi: "<p>30. साइटोक्रोम P450 का कार्य क्या है ?</p>",
                    options_en: [
                        "<p>Detoxification of xenobiotics</p>",
                        "<p>Hydrolysis of glucose</p>",
                        "<p>ATP synthesis</p>",
                        "<p>Assimilation of medicines and xenobiotics</p>"
                    ],
                    options_hi: [
                        "<p>जेनोबायोटिक्स का विषहरण</p>",
                        "<p>ग्लूकोज का हाइड्रोलिसिस</p>",
                        "<p>ATP संश्लेषण</p>",
                        "<p>दवाओं और जेनोबायोटिक्स का आत्मसात</p>"
                    ],
                    solution_en: "<p>30.(a) <strong>Detoxification of xenobiotics.</strong> Cytochrome P450 is a family of heme-containing enzymes that are important for many physiological processes. Chemical energy produced by the mitochondria is stored in a small molecule called adenosine triphosphate (ATP). Carbohydrate hydrolysis is a process by which large sugar molecules such as starch, glycogen and cellulose are broken down into simple sugars such as glucose and fructose.</p>",
                    solution_hi: "<p>30.(a) <strong>ज़ेनोबायोटिक्स का विषहरण।</strong> साइटोक्रोम P450 हीम-युक्त एंजाइमों का एक फ़ैमिली है जो कई शारीरिक प्रक्रियाओं के लिए महत्वपूर्ण है। माइटोकॉन्ड्रिया द्वारा उत्पादित रासायनिक ऊर्जा एडेनोसिन ट्राइफॉस्फेट (ATP) नामक एक छोटे अणु में संग्रहीत होती है। कार्बोहाइड्रेट हाइड्रोलिसिस एक ऐसी प्रक्रिया है जिसके द्वारा स्टार्च, ग्लाइकोजन और सेल्यूलोज जैसे बड़े शर्करा अणुओं को ग्लूकोज और फ्रुक्टोज जैसे सरल शर्करा में तोड़ा जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Through which of the following Acts does the government check restrictive trade ?</p>",
                    question_hi: "<p>31. निम्नलिखित में से किस अधिनियम के द्वारा सरकार प्रतिबंधात्मक व्यापार पर रोक लगाती है ?</p>",
                    options_en: [
                        "<p>FEMA Act</p>",
                        "<p>Industrial Policy Act 1991</p>",
                        "<p>MRTP Act</p>",
                        "<p>Foreign Trade Policy</p>"
                    ],
                    options_hi: [
                        "<p>फेमा (FEMA) अधिनियम</p>",
                        "<p>औद्योगिक नीति अधिनियम 1991</p>",
                        "<p>एम.आर.टी.पी. (MRTP) अधिनियम</p>",
                        "<p>विदेश व्यापार नीति</p>"
                    ],
                    solution_en: "<p>31.(c)<strong> MRTP Act.</strong> Monopolies and Restrictive Trade Practices Act 1969 was repealed and replaced by the Competition Act, 2002, on the recommendations of the Raghavan committee. Competition Commission of India (CCI) is a statutory body established in 2009 to enforce the Competition Act, 2002. Objective - Ensure fair and healthy competition in economic activities, Consumer welfare, Implement competition policies, etc.</p>",
                    solution_hi: "<p>31.(c) <strong>MRTP अधिनियम।</strong> राघवन समिति की सिफारिशों पर एकाधिकार और प्रतिबंधात्मक व्यापार व्यवहार अधिनियम 1969 को निरस्त कर दिया गया और उसके स्थान पर प्रतिस्पर्धा अधिनियम, 2002 लाया गया। भारतीय प्रतिस्पर्धा आयोग (CCI) प्रतिस्पर्धा अधिनियम, 2002 को लागू करने के लिए 2009 में स्थापित एक वैधानिक निकाय है। उद्देश्य - आर्थिक गतिविधियों, उपभोक्ता कल्याण, प्रतिस्पर्धा नीतियों को लागू करने आदि में निष्पक्ष और स्वस्थ प्रतिस्पर्धा सुनिश्चित करना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Anantha R Krishnan is a famous ______ player.</p>",
                    question_hi: "<p>32. अनंत आर. कृष्णन एक प्रसिद्ध ______वादक हैं।</p>",
                    options_en: [
                        "<p>flute</p>",
                        "<p>mridangam</p>",
                        "<p>santoor</p>",
                        "<p>sitar</p>"
                    ],
                    options_hi: [
                        "<p>बाँसुरी</p>",
                        "<p>मृदंगम</p>",
                        "<p>संतूर</p>",
                        "<p>सितार</p>"
                    ],
                    solution_en: "<p>32.(b) <strong>mridangam. </strong>Instruments and Players: Mridangam - KV Prasad, SV Rajarao, Umalayapuram Sivaraman, Palghat Mani Iyer. Flute - Hari Prasad Chaurasia, Pannalal Ghosh. Santoor - Pt Shiv Kumar Sharma, Bhajan Sopori. Sitar- Pt Ravi Shankar, Shahid Parvez Khan, Budhaditya Mukherjee.</p>",
                    solution_hi: "<p>32.(b) <strong>मृदंगम। </strong>वाद्ययंत्र एवं वादक: मृदंगम - के.वी.प्रसाद, एस.वी.राजाराव, उमालयपुरम शिवरामन, पालघाट मणि अय्यर। बांसुरी - हरि प्रसाद चौरसिया, पन्नालाल घोष। संतूर - पं. शिव कुमार शर्मा, भजन सोपोरी। सितार- पं. रविशंकर, शाहिद परवेज़ खान, बुधादित्य मुखर्जी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Vijayadashami is also known as _______.</p>",
                    question_hi: "<p>33. विजयादशमी को ______के नाम से भी जाना जाता है।</p>",
                    options_en: [
                        "<p>Dussehra</p>",
                        "<p>Holi</p>",
                        "<p>Diwali</p>",
                        "<p>Ganesh Chaturthi</p>"
                    ],
                    options_hi: [
                        "<p>दशहरा</p>",
                        "<p>होली</p>",
                        "<p>दिवाली</p>",
                        "<p>गणेश चतुर्थी</p>"
                    ],
                    solution_en: "<p>33.(a) <strong>Dussehra.</strong> Vijayadashami which means \"the tenth day of victory.\" Dussehra is observed on Shukla Paksha Dashmi during the month of Ashwin as per Hindu lunar calendar and a day after Maha Navami. States and their festivals: Kerala- Onam, Vishu. Mizoram- Chapcharkut Festival. Sikkim - Losar, Saga Dawa. Tripura- Durga Puja. Goa- Sunburn festival. Assam - Ambubachi, Bhogali Bihu, Baishagu, Dehing Patkai.</p>",
                    solution_hi: "<p>33.(a) <strong>दशहरा । </strong>विजयादशमी जिसका अर्थ है \"जीत का दसवां दिन।\" दशहरा हिंदू चंद्र कैलेंडर के अनुसार अश्विन महीने के दौरान शुक्ल पक्ष दशमी को और महानवमी के एक दिन बाद मनाया जाता है। राज्य और उनके त्यौहार: केरल- ओणम, विशु। मिजोरम- चपचारकुट महोत्सव। सिक्किम - लोसार, सागा दावा। त्रिपुरा- खर्ची पूजा. गोवा- सनबर्न उत्सव। असम - अंबुबाची, भोगाली बिहू, बैशागु, देहिंग पटकाई।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following is a \'s\' block element ?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन-सा तत्व एक s-ब्लॉक का तत्व है ?</p>",
                    options_en: [
                        "<p>Scandium</p>",
                        "<p>Vanadium</p>",
                        "<p>Titanium</p>",
                        "<p>Rubidium</p>"
                    ],
                    options_hi: [
                        "<p>स्कैंडियम (Scandium)</p>",
                        "<p>वैनेडियम (Vanadium)</p>",
                        "<p>टाइटेनियम (Titanium)</p>",
                        "<p>रूबीडियम (Rubidium)</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>Rubidium. </strong>The s-block contain 14 elements, which include hydrogen (H), helium (He), lithium (Li), beryllium (Be), sodium (Na), magnesium (Mg), potassium (K), calcium (Ca), rubidium (Rb), strontium (Sr), cesium (Cs), barium (Ba), francium (Fr) and radium (Ra).</p>",
                    solution_hi: "<p>34.(d) <strong>रुबिडियम। </strong>s-ब्लॉक में 14 तत्व होते हैं, जिनमें हाइड्रोजन (H), हीलियम (He), लिथियम (Li), बेरिलियम (Be), सोडियम (Na), मैग्नीशियम (Mg), पोटेशियम (K), कैल्शियम (Ca), रुबिडियम (Rb), स्ट्रोंटियम (Sr), सीज़ियम (Cs), बेरियम (Ba), फ़्रैन्शियम (Fr) और रेडियम (Ra) शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. When is the International Day for Tolerance observed ?</p>",
                    question_hi: "<p>35. अंतर्राष्ट्रीय सहिष्णुता दिवस कब मनाया जाता है ?</p>",
                    options_en: [
                        "<p>November 10</p>",
                        "<p>November 16</p>",
                        "<p>November 20</p>",
                        "<p>November 25</p>"
                    ],
                    options_hi: [
                        "<p>10 नवंबर</p>",
                        "<p>16 नवंबर</p>",
                        "<p>20 नवंबर</p>",
                        "<p>25 नवंबर</p>"
                    ],
                    solution_en: "<p>35.(b) <strong>November 16.</strong> It is observed every year to promote mutual understanding among cultures and encourage tolerance among individuals.</p>",
                    solution_hi: "<p>35.(b) <strong>16 नवंबर। </strong>यह हर साल संस्कृतियों के बीच आपसी समझ को बढ़ावा देने और व्यक्तियों के बीच सहिष्णुता को प्रोत्साहित करने के लिए मनाया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Suryakumar Yadav is associated with which of the following games ?</p>",
                    question_hi: "<p>36. सूर्यकुमार यादव निम्नलिखित में से किस खेल से संबंधित हैं ?</p>",
                    options_en: [
                        "<p>Boxing</p>",
                        "<p>Football</p>",
                        "<p>Cricket</p>",
                        "<p>Wrestling</p>"
                    ],
                    options_hi: [
                        "<p>मुक्केबाज़ी</p>",
                        "<p>फुटबॉल</p>",
                        "<p>क्रिकेट</p>",
                        "<p>कुश्ती</p>"
                    ],
                    solution_en: "<p>36.(c) <strong>Cricket. </strong>Suryakumar yadav is an Indian international cricketer. He plays as a right-handed middle-order batter. Indian Sport personalities: Manpreet Singh (Hockey), Sunil Chhetri (Football), Anup Kumar (Kabaddi), PV Sindhu (Badminton), Prajnesh Gunneswaran (Tennis), Bajrang Punia (Wrestler), Amit Panghal (Boxer), Narain Kartikeyan (Racer), and Raspreet Sidhu (Basketball).</p>",
                    solution_hi: "<p>36.(c) <strong>क्रिकेट। </strong>सूर्यकुमार यादव एक भारतीय अंतर्राष्ट्रीय क्रिकेटर हैं। ये दाएं हाथ के मध्यक्रम बल्लेबाज के रूप में खेलते हैं। भारतीय खेल हस्तियाँ: मनप्रीत सिंह (हॉकी), सुनील छेत्री (फुटबॉल), अनुप कुमार (कबड्डी), पीवी सिंधु (बैडमिंटन), प्रजनेश गुणेश्वरन (टेनिस), बजरंग पुनिया (पहलवान), अमित पंघाल (मुक्केबाज), नारायण कार्तिकेयन (धावक) और रसप्रीत सिद्धू (बास्केटबॉल)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. What was the capital of North-western province under Ashoka ?</p>",
                    question_hi: "<p>37. अशोक के अधीन उत्तर-पश्चिमी प्रांत की राजधानी क्या थी ?</p>",
                    options_en: [
                        "<p>Ujjayini</p>",
                        "<p>Tosali</p>",
                        "<p>Taxila</p>",
                        "<p>Suvarnagiri</p>"
                    ],
                    options_hi: [
                        "<p>उज्जयिनी</p>",
                        "<p>तोसली</p>",
                        "<p>तक्षशिला</p>",
                        "<p>सुवर्णगिरी</p>"
                    ],
                    solution_en: "<p>37.(c) <strong>Taxila.</strong> In the Mauryan Empire, there were five major political centers &ndash; the capital Pataliputra and the provincial centers of Taxila (North West province), Ujjayini (Western province), Tosali (Kalinga) and Suvarnagiri. These centers were carefully chosen, both Taxila and Ujjayini being situated on important long-distance trade routes, while Suvarnagiri (literally, the golden mountain) was possibly important for tapping the gold mines of Karnataka.</p>",
                    solution_hi: "<p>37.(c) <strong>तक्षशिला । </strong>मौर्य साम्राज्य में, पाँच प्रमुख राजनीतिक केंद्र थे - राजधानी पाटलिपुत्र और प्रांतीय केंद्र तक्षशिला (उत्तर पश्चिम प्रांत), उज्जयिनी (पश्चिमी प्रांत), तोसाली (कलिंग) और सुवर्णगिरि। इन केंद्रों को सावधानीपूर्वक चुना गया था, तक्षशिला और उज्जयिनी दोनों महत्वपूर्ण लंबी दूरी के व्यापार मार्गों पर स्थित थे, जबकि सुवर्णगिरि (शाब्दिक रूप से, स्वर्ण पर्वत) संभवतः कर्नाटक की सोने की खदानों के दोहन के लिए महत्वपूर्ण था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Wangala is a popular harvest festival of which Indian state ?</p>",
                    question_hi: "<p>38. वांगला किस भारतीय राज्य का एक लोकप्रिय फसल उत्सव है ?</p>",
                    options_en: [
                        "<p>Uttar Pradesh</p>",
                        "<p>Bihar</p>",
                        "<p>Meghalaya</p>",
                        "<p>West Bengal</p>"
                    ],
                    options_hi: [
                        "<p>उत्तर प्रदेश</p>",
                        "<p>बिहार</p>",
                        "<p>मेघालय</p>",
                        "<p>पश्चिम बंगाल</p>"
                    ],
                    solution_en: "<p>38.(c) <strong>Meghalaya.</strong> Wangala Festival: It is celebrated by the people of the Garo tribe and is a harvest festival held in honour of Saljong, the Sun-god of fertility. It is also known as The 100 Drums Festival of Meghalaya and signifies the onset of winter. Other Festivals : Bihar - Chhath puja, Salhesh. West Bengal - Poush Parbon, Durga Puja, Poila Baisakh. Uttar Pradesh - Bateshwar Mela, Ramlila, and Barsana Holi.</p>",
                    solution_hi: "<p>38.(c) <strong>मेघालय। </strong>वांगला महोत्सव: यह गारो जनजाति के लोगों द्वारा मनाया जाता है और उर्वरता के सूर्य देवता सालजोंग के सम्मान में आयोजित एक फसल उत्सव है। इसे मेघालय के 100 ड्रम महोत्सव के रूप में भी जाना जाता है और यह सर्दियों की शुरुआत का प्रतीक है। अन्य त्यौहार: बिहार - छठ पूजा, सल्हेश। पश्चिम बंगाल - पौष पारबोन, दुर्गा पूजा, पोइला बैसाख। उत्तर प्रदेश - बटेश्वर मेला, रामलीला और बरसाना होली।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which is the highest court of appeal in India ?</p>",
                    question_hi: "<p>39. भारत में अपील का सर्वोच्च न्यायालय कौन-सा है ?</p>",
                    options_en: [
                        "<p>Lok Adalat</p>",
                        "<p>District Court</p>",
                        "<p>High Court</p>",
                        "<p>Supreme Court of India</p>"
                    ],
                    options_hi: [
                        "<p>लोक अदालत</p>",
                        "<p>जिला न्यायालय</p>",
                        "<p>उच्च न्यायालय</p>",
                        "<p>भारत का उच्चतम न्यायालय</p>"
                    ],
                    solution_en: "<p>39.(d) <strong>Supreme Court of India -</strong> came into existence on 26th January, 1950 and inaugurated on January 28, 1950. It is located on Tilak Marg, New Delhi. Motto - Yato Dharmastato Jayah. First Chief Justice of India - Harilal J. Kania.</p>",
                    solution_hi: "<p>39.(d) <strong>भारत का उच्चतम न्यायालय - </strong>26 जनवरी, 1950 को अस्तित्व में आया और 28 जनवरी, 1950 को इसका उद्घाटन हुआ। यह नई दिल्ली के तिलक मार्ग पर स्थित है। आदर्श वाक्य- यतो धर्मस्ततो जयः। भारत के प्रथम मुख्य न्यायाधीश - हरिलाल जे. कानिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Foods like pizza, burger are rich in:</p>",
                    question_hi: "<p>40. पिज्जा, बर्गर जैसे खाद्य पदार्थ ______से भरपूर होते हैं।</p>",
                    options_en: [
                        "<p>Proteins</p>",
                        "<p>Vitamins</p>",
                        "<p>Carbohydrates</p>",
                        "<p>Minerals</p>"
                    ],
                    options_hi: [
                        "<p>प्रोटीन</p>",
                        "<p>विटामिन</p>",
                        "<p>कार्बोहाइड्रेट</p>",
                        "<p>खनिज पदार्थ</p>"
                    ],
                    solution_en: "<p>40.(c) <strong>Carbohydrates. </strong>This is primarily due to the presence of ingredients like bread (pizza crust, burger buns), which are made from refined flour. Carbohydrates are a major energy source and are abundant in such fast foods. In addition to carbohydrates, these foods may also contain fats (from cheese, oils, and meats) and some proteins (from meat, cheese, or toppings), but carbohydrates are the dominant nutrient in these items.</p>",
                    solution_hi: "<p>40.(c) <strong>कार्बोहाइड्रेट।</strong> यह मुख्य रूप से ब्रेड (पिज्जा क्रस्ट, बर्गर बन्स) जैसी सामग्री की उपस्थिति के कारण होता है, जो परिष्कृत आटे से बने होते हैं। कार्बोहाइड्रेट एक प्रमुख ऊर्जा स्रोत हैं और ऐसे फास्ट फूड में प्रचुर मात्रा में होते हैं। कार्बोहाइड्रेट के अलावा, इन खाद्य पदार्थों में वसा (पनीर, तेल और मांस) और कुछ प्रोटीन (मांस, पनीर या टॉपिंग) भी हो सकते हैं, लेकिन इन वस्तुओं में कार्बोहाइड्रेट प्रमुख पोषक तत्व हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. At the end of which of the following Five-year plans of India, five IITs were set up in the country ?</p>",
                    question_hi: "<p>41. भारत की निम्नलिखित में से किस पंचवर्षीय योजना के अंत में देश में पांच आईआईटी (IIT) स्थापित किए गए ?</p>",
                    options_en: [
                        "<p>Third</p>",
                        "<p>Fourth</p>",
                        "<p>First</p>",
                        "<p>Second</p>"
                    ],
                    options_hi: [
                        "<p>तीसरी</p>",
                        "<p>चौथी</p>",
                        "<p>पहली</p>",
                        "<p>दूसरी</p>"
                    ],
                    solution_en: "<p>41.(c) <strong>First. </strong>First Five-Year Plan (1951-56) - It was based on the Harrod Domar Model and emphasized increasing savings. It mainly addressed the agrarian sector, including investment in dams and irrigation. Example - Huge allocations were made for Bhakra Nangal Dam. The target growth rate was 2.1% and the achieved growth rate was 3.6%. The Five Year Plans were formulated, implemented and regulated by a body known as the Planning Commission. The Planning Commission was replaced by a think tank called NITI AAYOG in 2015.</p>",
                    solution_hi: "<p>41.(c)<strong> पहली। </strong>प्रथम पंचवर्षीय योजना (1951-56) - यह हैरोड डोमर मॉडल पर आधारित थी और इसमें बचत बढ़ाने पर जोर दिया गया था। इसमें मुख्य रूप से कृषि क्षेत्र पर ध्यान दिया गया, जिसमें बांधों और सिंचाई में निवेश भी शामिल था। उदाहरण - भाखड़ा नांगल बांध के लिए भारी आवंटन किया गया था। लक्ष्य वृद्धि दर 2.1% थी और प्राप्त वृद्धि दर 3.6% थी। पंचवर्षीय योजनाओं का निर्माण, क्रियान्वयन और विनियमन योजना आयोग नामक संस्था द्वारा किया जाता था। 2015 में योजना आयोग के स्थान पर नीति आयोग नामक थिंक टैंक का गठन किया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. When did India win its last gold medal in Hockey in the Olympics ?</p>",
                    question_hi: "<p>42. भारत ने ओलंपिक में हॉकी में अपना आखिरी स्वर्ण पदक कब जीता था ?</p>",
                    options_en: [
                        "<p>1972</p>",
                        "<p>1976</p>",
                        "<p>1968</p>",
                        "<p>1980</p>"
                    ],
                    options_hi: [
                        "<p>1972 में</p>",
                        "<p>1976 में</p>",
                        "<p>1968 में</p>",
                        "<p>1980 में</p>"
                    ],
                    solution_en: "<p>42.(d) <strong>1980.</strong> The Indian hockey men&rsquo;s team has won 8 Olympic gold medals in all, six of them in a row (from 1928-1956) and added two more at Tokyo 1964 and Moscow 1980 (Captain: Vasudevan Baskaran). India won their first Olympic hockey gold in the 1928 Olympic Games at Amsterdam (Captain - Jaipal Singh Munda).</p>",
                    solution_hi: "<p>42.(d) <strong>1980 ।</strong> भारतीय हॉकी पुरुष टीम ने कुल मिलाकर 8 ओलंपिक स्वर्ण पदक जीते हैं, उनमें से छह लगातार (1928-1956 तक) और टोक्यो 1964 तथा मॉस्को 1980 (कप्तान: वासुदेवन भास्करन) में दो और स्वर्ण पदक जीते। भारत ने अपना पहला ओलंपिक हॉकी स्वर्ण पदक 1928 के एम्स्टर्डम ओलंपिक खेलों (कप्तान - जयपाल सिंह मुंडा) में जीता था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. According to which physical law, if the volume and number of molecules of a gas remain constant, then the pressure and temperature of the gas are directly proportional ?</p>",
                    question_hi: "<p>43. किस भौतिक नियम के अनुसार, यदि किसी गैस के अणुओं की मात्रा और संख्या स्थिर रहे, तो गैस का दाब और ताप समानुपाती होते हैं ?</p>",
                    options_en: [
                        "<p>Boyle\'s law</p>",
                        "<p>Avogadro\'s law</p>",
                        "<p>Gay-Lussac\'s law</p>",
                        "<p>Charles\'s law</p>"
                    ],
                    options_hi: [
                        "<p>बॉयल का नियम</p>",
                        "<p>आवोगाद्रो का नियम</p>",
                        "<p>गै-लुसैक का नियम</p>",
                        "<p>चार्ल्स का नियम</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>Gay-Lussac\'s law.</strong> Boyle\'s law: At constant temperature, the pressure of a gas is inversely proportional to its volume. Avogadro\'s law: Equal volumes of gases at the same temperature and pressure have the same number of molecules. Charles\'s law: At constant pressure, the volume of a gas is directly proportional to its absolute temperature.</p>",
                    solution_hi: "<p>43.(c) <strong>गै-लुसैक का नियम।</strong> बॉयल का नियम: स्थिर तापमान पर, गैस का दाब उसके आयतन के व्युत्क्रमानुपाती होता है। एवोगैड्रो का नियम: समान तापमान और दाब पर समान आयतन वाली गैसों में अणुओं की संख्या समान होती है। चार्ल्स का नियम: स्थिर दाब पर, गैस का आयतन उसके परम तापमान के सीधे आनुपातिक होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. The Kailash Temple of Ellora and the RathaTemple of Mahabalipuram are examples of which type of temple architecture ?</p>",
                    question_hi: "<p>44. एलोरा का कैलाश मंदिर और महाबलीपुरम का रथ मंदिर किस प्रकार की मंदिर वास्तुकला के उदाहरण हैं ?</p>",
                    options_en: [
                        "<p>Giant</p>",
                        "<p>Panchayatan</p>",
                        "<p>low base</p>",
                        "<p>Rock-cut</p>"
                    ],
                    options_hi: [
                        "<p>विशालकाय</p>",
                        "<p>पंचायतन</p>",
                        "<p>निम्न आधार</p>",
                        "<p>रॉक-कट</p>"
                    ],
                    solution_en: "<p>44.(d) <strong>Rock-cut.</strong> Famous Rock cut architecture in India: Badami Cave Temples (Karnataka), Ajanta Caves (Maharashtra), Varaha Cave Temples (Tamil Nadu), Masroor Cave Temples (Himachal Pradesh), Udayagiri and Khandagiri Caves (Odisha).</p>",
                    solution_hi: "<p>44.(d) <strong>रॉक-कट। </strong>भारत में प्रसिद्ध रॉक-कट वास्तुकला: बादामी गुफा मंदिर (कर्नाटक), अजंता गुफाएं (महाराष्ट्र), वराह गुफा मंदिर (तमिलनाडु), मसरूर गुफा मंदिर (हिमाचल प्रदेश), उदयगिरि और खंडगिरि गुफाएं (ओडिशा)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Who was recognised for his services in the discovery of inert gaseous elements in air and in the determination of their place in the periodic system ?</p>",
                    question_hi: "<p>45. वायु में निष्क्रिय गैसीय तत्वों की खोज और आवर्त सारणी में उनके स्थान के निर्धारण में उनकी सेवाओं के लिए किसे मान्यता दी गई ?</p>",
                    options_en: [
                        " Henry Cavendish ",
                        " Sir William Ramsay ",
                        " Lord Rayleigh",
                        " Henry Miers"
                    ],
                    options_hi: [
                        " हेनरी कैवेंडिश ",
                        " सर विलियम रैम्जे ",
                        " लॉर्ड रेले ",
                        " हेनरी मियर्स"
                    ],
                    solution_en: "<p>45.(b) <strong>Sir William Ramsay </strong>was a Scottish chemist and he received the Nobel Prize in Chemistry in 1904 for this discovery. Henry Cavendish reported measurements of the gravitational constant, along with the mass and density of the Earth, in June 1798. Lord Rayleigh and Sir William Ramsay discovered the Argon.</p>",
                    solution_hi: "<p>45.(b) <strong>सर विलियम रैम्जे </strong>एक स्कॉटिश रसायनज्ञ थे और उन्हें इस खोज के लिए 1904 में रसायन विज्ञान में नोबेल पुरस्कार मिला था। हेनरी कैवेंडिश ने जून 1798 में पृथ्वी के द्रव्यमान और घनत्व के साथ-साथ गुरुत्वाकर्षण स्थिरांक के माप की रिपोर्ट दी। लॉर्ड रेले और सर विलियम रैम्जे ने आर्गन की खोज की थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which Article of the Constitution of India mentions that the State shall, in particular, direct its policy towards securing equal pay for equal work for both men and women ?</p>",
                    question_hi: "<p>46. भारतीय संविधान के किस अनुच्छेद में उल्लेख किया गया है कि राज्य, विशेष रूप से, पुरुषों और महिलाओं दोनों के लिए समान कार्य के लिए समान वेतन सुनिश्चित करने की दिशा में अपनी नीति निर्देशित करेगा ?</p>",
                    options_en: [
                        "<p>Article 30</p>",
                        "<p>Article 37</p>",
                        "<p>Article 39</p>",
                        "<p>Article 33</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 30</p>",
                        "<p>अनुच्छेद 37</p>",
                        "<p>अनुच्छेद 39</p>",
                        "<p>अनुच्छेद 33</p>"
                    ],
                    solution_en: "<p>46.(c) <strong>Article 39.</strong> Part IV of the Constitution of India (Article 36&ndash;51) deals with Directive Principles of State Policy (DPSP). Article 37 - Application of the principles contained in Part IV. Article 30: Right of minorities to establish and administer educational institutions. Article 33: Power of Parliament to modify the rights conferred by Part IV in their application to Forces, etc.</p>",
                    solution_hi: "<p>46.(c) <strong>अनुच्छेद 39 ।</strong> भारत के संविधान का भाग IV (अनुच्छेद 36-51) राज्य के नीति निदेशक सिद्धांतों (DPSP) से संबंधित है। अनुच्छेद 37 - भाग IV में निहित सिद्धांतों का अनुप्रयोग। अनुच्छेद 30: शैक्षणिक संस्थानों की स्थापना और प्रशासन करने का अल्पसंख्यकों का अधिकार। अनुच्छेद 33: भाग IV द्वारा प्रदत्त अधिकारों को बलों आदि पर लागू करने में संशोधन करने की संसद की शक्ति।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which sportsperson has become the first person in the world to amass 1 billion followers across various social media platforms ?</p>",
                    question_hi: "<p>47. कौन सा खिलाड़ी विभिन्न सोशल मीडिया प्लेटफॉर्म पर 1 बिलियन फॉलोअर्स जुटाने वाला दुनिया का प्रथम व्यक्ति बन गया है ?</p>",
                    options_en: [
                        "<p>Lionel Messi</p>",
                        "<p>Virat Kohli</p>",
                        "<p>Cristiano Ronaldo</p>",
                        "<p>LeBron James</p>"
                    ],
                    options_hi: [
                        "<p>लियोनेल मेस्सी</p>",
                        "<p>विराट कोहली</p>",
                        "<p>क्रिस्टियानो रोनाल्डो</p>",
                        "<p>लेब्रोन जेम्स</p>"
                    ],
                    solution_en: "<p>47.(c) Cristiano Ronaldo.</p>",
                    solution_hi: "<p>47.(c) क्रिस्टियानो रोनाल्डो।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Who coined the term \'Evergreen Revolution\' to increase agriculture production in India ?</p>",
                    question_hi: "<p>48. भारत में कृषि उत्पादन बढ़ाने के लिए \'सदाबहार क्रांति (Evergreen Revolution)\' शब्द किसने गढ़ा ?</p>",
                    options_en: [
                        "<p>Mahatma Gandhi</p>",
                        "<p>Ram Manohar Lohiya</p>",
                        "<p>R.V. Rao</p>",
                        "<p>M.S.Swaminathan</p>"
                    ],
                    options_hi: [
                        "<p>महात्मा गांधी</p>",
                        "<p>राम मनोहर लोहिया</p>",
                        "<p>आर. वी. राव</p>",
                        "<p>एम.एस. स्वामीनाथन</p>"
                    ],
                    solution_en: "<p>48.(d) <strong>M.S.Swaminathan</strong> (father of the green revolution in India). He led the Green Revolution, significantly boosting food grain production (especially wheat and rice). The Green Revolution in India transformed agriculture into an industrial system through the adoption of modern methods, including HYV seeds, tractors, irrigation, pesticides, and fertilizers.</p>",
                    solution_hi: "<p>48.(d) <strong>एम.एस. स्वामीनाथन</strong> (भारत में हरित क्रांति के जनक)। उन्होंने हरित क्रांति का नेतृत्व किया, जिससे खाद्यान्न उत्पादन (विशेषकर गेहूं और चावल) में उल्लेखनीय वृद्धि हुई। भारत में हरित क्रांति ने HYV बीज, ट्रैक्टर, सिंचाई, कीटनाशक और उर्वरकों सहित आधुनिक तरीकों को अपनाकर कृषि को एक औद्योगिक प्रणाली में बदल दिया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. The great poet and humanist writer Rabindra Nath Thakur returned his knighthood in protest of which incident ?</p>",
                    question_hi: "<p>49. महान कवि और मानवतावादी लेखक रवीन्द्र नाथ ठाकुर ने किस घटना के विरोध में अपनी नाइटहुड की उपाधि लौटा दी थी ?</p>",
                    options_en: [
                        "<p>Bengal partition, 1905</p>",
                        "<p>Pitt\'s India Act of 1784</p>",
                        "<p>Kamagatamaru case, 1914</p>",
                        "<p>Jallianwala Bagh Massacre, 1919</p>"
                    ],
                    options_hi: [
                        "<p>बंगाल विभाजन, 1905</p>",
                        "<p>पिट्स इंडिया एक्ट, 1784</p>",
                        "<p>कामागाटामारू कांड, 1914</p>",
                        "<p>जलियांवाला बाग हत्याकांड, 1919</p>"
                    ],
                    solution_en: "<p>49.(d) <strong>Jallianwala Bagh Massacre, 1919. </strong>The people were protesting peacefully against the Rowlatt Act (1919). Demand - To release two nationalist leaders Saifuddin Kitchlew and Dr. Satyapal, who were arrested under this Act. General Dyer ordered opened fire on the unarmed crowd, causing the killing of more than 1000 people on the day of Baisakhi (13 April).</p>",
                    solution_hi: "<p>49.(d) <strong>जलियांवाला बाग हत्याकांड, 1919।</strong> लोग रौलेट एक्ट (1919) के खिलाफ शांतिपूर्वक विरोध प्रदर्शन कर रहे थे। माँग - इस अधिनियम के तहत गिरफ्तार किए गए दो राष्ट्रवादी नेताओं सैफुद्दीन किचलू और डॉ. सत्यपाल को रिहा किया जाए। बैसाखी के दिन (13 अप्रैल) जनरल डायर ने निहत्थे भीड़ पर गोली चलाने का आदेश दिया, जिससे 1000 से अधिक लोग मारे गये।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which of the following Indian Olympians has written the autobiography titled \'Playing to Win\' ?</p>",
                    question_hi: "<p>50. निम्नलिखित में से किस भारतीय ओलंपियन ने \'प्लेइंग टू विन\' (Playing to Win) शीर्षक की आत्मकथा लिखी है ?</p>",
                    options_en: [
                        "<p>Abhinav Bindra</p>",
                        "<p>Saina Nehwal</p>",
                        "<p>Lovlina Borgohain</p>",
                        "<p>Karnam Malleswari</p>"
                    ],
                    options_hi: [
                        "<p>अभिनव बिंद्रा</p>",
                        "<p>साइना नेहवाल</p>",
                        "<p>लवलीन बोरगोहेन</p>",
                        "<p>कर्णम मल्लेश्वरी</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>Saina Nehwal.</strong> Autobiography of a Sports person: &lsquo;Playing It My Way&rsquo; - Sachin Tendulkar, &lsquo;Ace Against Odds&rsquo; - Sania Mirza, &lsquo;Unbreakable: An Autobiography&rsquo; - Mary Kom, &lsquo;Golden girl&rsquo; - PT Usha, &lsquo;Unstoppable: My Life So Far&rsquo; - Maria Sharapova, &lsquo;A Shot at history&rsquo; - Abhinav Bindra.</p>",
                    solution_hi: "<p>50.(b) <strong>साइना नेहवाल। </strong>खिलाड़ी और उनकी आत्मकथा: \'प्लेइंग इट माई वे\' - सचिन तेंदुलकर, \'ऐस अगेंस्ट ऑड्स\' - सानिया मिर्जा, \'अनब्रेकेबल: एन ऑटोबायोग्राफी\' - मैरी कॉम, \'गोल्डन गर्ल\' - पीटी उषा, \'अनस्टॉपेबल: माई लाइफ सो फार\' - मारिया शारापोवा, \'ए शॉट एट हिस्ट्री\' - अभिनव बिंद्रा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A grocer purchased 10 kg rice for ₹700. He spends some amount on transportation and then sells it for ₹1,400. If the percentage of profit made by the grocer is 30%, then what is the amount (in₹)he spends on transportation ? (Round off to the nearest integer)</p>",
                    question_hi: "<p>51. एक पंसारी ने ₹700 में 10 kg चावल खरीदा। वह परिवहन पर कुछ राशि व्यय करता है और फिर इसे ₹1,400 में बेच देता है। यदि पंसारी द्वारा अर्जित लाभ प्रतिशत 30% है, तो वह परिवहन पर कितनी राशि (₹ में) व्यय करता है ? (निकटतम पूर्णांक तक पूर्णांकित कीजिए)</p>",
                    options_en: [
                        "<p>350</p>",
                        "<p>380</p>",
                        "<p>300</p>",
                        "<p>377</p>"
                    ],
                    options_hi: [
                        "<p>350</p>",
                        "<p>380</p>",
                        "<p>300</p>",
                        "<p>377</p>"
                    ],
                    solution_en: "<p>51.(d)<br>Selling price = 1400<br>Cost price &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>10</mn></mfrac></math> = 1400<br>Cost price = <math display=\"inline\"><mfrac><mrow><mn>14000</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1076.92 &sim; 1077<br>Amount spend on transportation = 1077 - 700 = 377</p>",
                    solution_hi: "<p>51.(d)<br>विक्रय मूल्य = 1400<br>क्रय मूल्य &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>10</mn></mfrac></math> = 1400<br>क्रय मूल्य = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14000</mn><mn>13</mn></mfrac></math> = 1076.92 &sim; 1077<br>परिवहन पर व्यय राशि = 1077 - 700 = 377</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. A drainage tile is a cylindrical shell 42 cm long. The inside and outside diameters are 8 cm and 14 cm, respectively. What is the volume (in cm<sup>3</sup>) of clay required for&nbsp;the tile ? (Use <math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)&nbsp;</p>",
                    question_hi: "<p>52. एक जल निकासी टाइल 42 cm लंबा एक बेलनाकार खोल है। आंतरिक और बाह्य व्यास क्रमशः 8 cm और 14 cm है। टाइल के लिए आवश्यक मिट्टी का आयतन (cm<sup>3</sup> में) कितना है ? (<math display=\"inline\"><mi>&#960;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> का उपयोग कीजिए।)</p>",
                    options_en: [
                        "<p>5241</p>",
                        "<p>4356</p>",
                        "<p>4881</p>",
                        "<p>4125</p>"
                    ],
                    options_hi: [
                        "<p>5241</p>",
                        "<p>4356</p>",
                        "<p>4881</p>",
                        "<p>4125</p>"
                    ],
                    solution_en: "<p>52.(b) <br>Volume of cylindrical shell =<strong> </strong>&pi;(R<sup>2 </sup>- r<sup>2</sup>)h<strong id=\"docs-internal-guid-6fc07bc9-7fff-62bf-5316-768789ee7ca6\"> </strong><br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; (7<sup>2</sup> - 4<sup>2</sup>) &times; 42 <br>= 22 &times; (49 - 16) &times; 6 <br>= 22 &times; 33 &times; 6 = 4356 cm<sup>3</sup></p>",
                    solution_hi: "<p>52.(b) <br>बेलनाकार खोल का आयतन =<strong> </strong>&pi;(R<sup>2 </sup>- r<sup>2</sup>)h<strong id=\"docs-internal-guid-6fc07bc9-7fff-62bf-5316-768789ee7ca6\"> </strong><br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; (7<sup>2</sup> - 4<sup>2</sup>) &times; 42 <br>= 22 &times; (49 - 16) &times; 6 <br>= 22 &times; 33 &times; 6 = 4356 cm<sup>3</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. There are 12 workers in an office and the average age of all the workers is 35<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> years. A new worker joins the office, the average becomes 37 years. The current age of the newly joined worker is:</p>",
                    question_hi: "<p>53. एक कार्यालय में 12 कर्मचारी हैं और सभी कर्मचारियों की औसत आयु 35<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष है। एक नया कर्मचारी कार्यालय में शामिल होता है, औसत 37 वर्ष हो जाता है। नए शामिल हुए कर्मचारी की वर्तमान आयु क्या है ?</p>",
                    options_en: [
                        "<p>57 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> years</p>",
                        "<p>47 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> years</p>",
                        "<p>55 years</p>",
                        "<p>55 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> years</p>"
                    ],
                    options_hi: [
                        "<p>57 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष</p>",
                        "<p>47 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष</p>",
                        "<p>55 वर्ष</p>",
                        "<p>55 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष</p>"
                    ],
                    solution_en: "<p>53.(c)<br>Age of newly joined worker = 35<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> + (37 - 35<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>) &times; 13 = 55 year</p>",
                    solution_hi: "<p>53.(c)<br>नए शामिल हुए कर्मचारी की आयु = 35<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> + (37 - 35<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>) &times; 13 = 55 वर्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Find the cost of painting a ball that is in the shape of a sphere with a radius of 14 cm. The painting cost of the ball is ₹5 per square centimetre (take &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>).</p>",
                    question_hi: "<p>54. 14 cm त्रिज्या वाले गोले के आकार की एक गेंद को पेंट करने की लागत ज्ञात कीजिए, यदि गेंद की पेंटिंग की लागत ₹5 प्रति वर्ग सेंटीमीटर है ( &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> लें)।</p>",
                    options_en: [
                        "<p>₹12,230</p>",
                        "<p>₹12,320</p>",
                        "<p>₹13,022</p>",
                        "<p>₹13,220</p>"
                    ],
                    options_hi: [
                        "<p>₹12,230</p>",
                        "<p>₹12,320</p>",
                        "<p>₹13,022</p>",
                        "<p>₹13,220</p>"
                    ],
                    solution_en: "<p>54.(b)<br>Area of sphere = 4&pi;r<sup>2</sup><br>= 4 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 14 &times; 14 = 2464 cm<sup>2</sup><br>Cost of painting a ball = 2464 &times; 5 = ₹12,320</p>",
                    solution_hi: "<p>54.(b)<br>गोले का क्षेत्रफल = 4&pi;r<sup>2</sup><br>= 4 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 14 &times; 14 = 2464 cm<sup>2</sup><br>एक गेंद को पेंट करने की लागत = 2464 &times; 5 = ₹12,320</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Find the value of 15% of 1.5 + 0.15 &divide; 0.1 + 0.01 of 1.1 &times; 0.5 + 2.5 &times; 25.</p>",
                    question_hi: "<p>55. 1.5 का 15% + 0.15 &divide; 0.1 + 1.1 का 0.01 &times; 0.5 + 2.5 &times; 25 का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>77.2305</p>",
                        "<p>64.2305</p>",
                        "<p>62.2305</p>",
                        "<p>6.42305</p>"
                    ],
                    options_hi: [
                        "<p>77.2305</p>",
                        "<p>64.2305</p>",
                        "<p>62.2305</p>",
                        "<p>6.42305</p>"
                    ],
                    solution_en: "<p>55.(b)<br>15% of 1.5 + 0.15 &divide; 0.1 + 0.01 of 1.1 &times; 0.5 + 2.5 &times; 25.<br>0.225 + 1.5 + 0.0055 + 62.5<br>0.225 + 1.5 + 0.0055 + 62.5 = 64.2305</p>",
                    solution_hi: "<p>55.(b)<br>1.5 का 15% + 0.15 &divide; 0.1 + 1.1 का 0.01 &times; 0.5 + 2.5 &times; 25<br>0.225 + 1.5 + 0.0055 + 62.5<br>0.225 + 1.5 + 0.0055 + 62.5 = 64.2305</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Evaluate <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>9</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math> , if 3 tan &theta; = 4.</p>",
                    question_hi: "<p>56. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>9</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mo>(</mo><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math>&nbsp; का मान निकालिए, यदि 3 tan &theta; = 4 है।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>56.(d)<br>3 tan&theta; = 4<br>tan&theta; = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>9</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>9</mn><mo>&#215;</mo><mn>4</mn><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>56.(d)<br>3 tan&theta; = 4<br>tan&theta; = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>9</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>2</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi><mo>)</mo></mrow><mrow><mn>9</mn><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>2</mn><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>&#952;</mi><mo>)</mo></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>4</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>9</mn><mo>&#215;</mo><mn>4</mn><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. In &Delta;ABC ,D and E are points on sides AB and AC, such that DE∥BC. If AD = <math display=\"inline\"><mi>x</mi></math>, DB = x - 3, AE = x + 3 and EC = x - 2, then the value of x is:</p>",
                    question_hi: "<p>57. &Delta;ABC में, D और E भुजाओं AB और AC पर बिंदु इस प्रकार हैं कि DE∥BC है, यदि AD = <math display=\"inline\"><mi>x</mi></math>, DB = x - 3, AE = x + 3 और EC = x - 2, है, तो x का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>5.2</p>",
                        "<p>4.2</p>",
                        "<p>4.0</p>",
                        "<p>4.5</p>"
                    ],
                    options_hi: [
                        "<p>5.2</p>",
                        "<p>4.2</p>",
                        "<p>4.0</p>",
                        "<p>4.5</p>"
                    ],
                    solution_en: "<p>57.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159223163.png\" alt=\"rId57\" width=\"171\" height=\"154\"><br>By Thale&rsquo;s theorem <br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">A</mi><mi mathvariant=\"bold-italic\">D</mi></mrow><mrow><mi mathvariant=\"bold-italic\">D</mi><mi mathvariant=\"bold-italic\">B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>E</mi></mrow><mrow><mi>E</mi><mi>C</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mrow><mi>x</mi><mo>-</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow><mrow><mi>x</mi><mo>-</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; x<sup>2 </sup>- 2x = (x - 3)(x + 3)<br>&rArr; x<sup>2 </sup>- 2x = (x)<sup>2</sup> - (3)<sup>2</sup><br>&rArr; - x = -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math> &rArr; x = <strong>4.5</strong></p>",
                    solution_hi: "<p>57.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159223163.png\" alt=\"rId57\" width=\"171\" height=\"154\"><br>थेलस के प्रमेय द्वारा<br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">A</mi><mi mathvariant=\"bold-italic\">D</mi></mrow><mrow><mi mathvariant=\"bold-italic\">D</mi><mi mathvariant=\"bold-italic\">B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>E</mi></mrow><mrow><mi>E</mi><mi>C</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mrow><mi>x</mi><mo>-</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>3</mn></mrow><mrow><mi>x</mi><mo>-</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; x<sup>2 </sup>- 2x = (x - 3)(x + 3)<br>&rArr; x<sup>2 </sup>- 2x = (x)<sup>2</sup> - (3)<sup>2</sup><br>&rArr; - x = -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math> &rArr; x = <strong>4.5</strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Aarav owns a watch worth ₹10,000. He sells it to Bhavin at a profit of 15%. After some days, Bhavin sells it back to Aarav at 15% loss. Find the percentage profit or loss faced by Aarav.</p>",
                    question_hi: "<p>58. आरव के पास ₹10,000 की कीमत की एक घड़ी है। वह इसे 15% के लाभ पर भाविन को बेचता है। कुछ दिनों के बाद, भाविन इसे 15% हानि पर आरव को वापस बेच देता है। आरव को होने वाला प्रतिशत लाभ या हानि ज्ञात करें।</p>",
                    options_en: [
                        "<p>17.25% loss</p>",
                        "<p>17.25% profit</p>",
                        "<p>18.25% profit</p>",
                        "<p>18.75% loss</p>"
                    ],
                    options_hi: [
                        "<p>17.25% हानि</p>",
                        "<p>17.25% लाभ</p>",
                        "<p>18.25% लाभ</p>",
                        "<p>18.75% हानि</p>"
                    ],
                    solution_en: "<p>58.(b)<br>Let the price of watch be ₹100<br>Buying price of watch for Bhavin = 100 &times; 115% = ₹115<br>Price at which Aarav gets his watch back = 115 &times; 85% = ₹97.75<br>Total profit of Aarav = (15 + 2.25) = ₹17.25<br>So, the profit% of Aarav = 17.25%</p>",
                    solution_hi: "<p>58.(b)<br>माना कि घड़ी की कीमत ₹100 है। <br>भाविन के लिए घड़ी का क्रय मूल्य = 100 &times; 115% = ₹115<br>जिस कीमत पर आरव को उसकी घड़ी वापस मिली = 115 &times; 85% = ₹97.75<br>आरव का कुल लाभ = (15 + 2.25) = ₹17.25<br>तो, आरव का लाभ% = 17.25%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. If <math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>tan</mi><mi>&#952;</mi><mo>+</mo><msqrt><mn>13</mn></msqrt><mi>sin</mi><mi>&#952;</mi></mrow><mrow><msqrt><mn>13</mn></msqrt><mi>cos</mi><mi>&#952;</mi><mo>-</mo><mn>3</mn><mi>tan</mi><mi>&#952;</mi></mrow></mfrac></math> is :</p>",
                    question_hi: "<p>59. यदि <math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2 है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>tan</mi><mi>&#952;</mi><mo>+</mo><msqrt><mn>13</mn></msqrt><mi>sin</mi><mi>&#952;</mi></mrow><mrow><msqrt><mn>13</mn></msqrt><mi>cos</mi><mi>&#952;</mi><mo>-</mo><mn>3</mn><mi>tan</mi><mi>&#952;</mi></mrow></mfrac></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>0</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>0</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>59.(d)<br><math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2<br>sin<math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>13</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>h</mi></mfrac></math><br>b = <math display=\"inline\"><msqrt><msup><mrow><mfenced separators=\"|\"><mrow><msqrt><mn>13</mn></msqrt></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>2</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = 3<br>According to the question,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle><mo>+</mo><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow><mrow><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>-</mo><mn>3</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>2</mn></mrow><mrow><mn>3</mn><mo>-</mo><mn>2</mn></mrow></mfrac></math> = 4</p>",
                    solution_hi: "<p>59.(d)<br><math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> sin&theta; = 2<br>sin<math display=\"inline\"><mi>&#952;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><msqrt><mn>13</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>h</mi></mfrac></math><br>b = <math display=\"inline\"><msqrt><msup><mrow><mfenced separators=\"|\"><mrow><msqrt><mn>13</mn></msqrt></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mfenced separators=\"|\"><mrow><mn>2</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = 3<br>प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle><mo>+</mo><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle></mrow><mrow><msqrt><mn>13</mn></msqrt><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><msqrt><mn>13</mn></msqrt></mfrac></mstyle><mo>-</mo><mn>3</mn><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mn>2</mn><mn>3</mn></mfrac></mstyle></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>2</mn></mrow><mrow><mn>3</mn><mo>-</mo><mn>2</mn></mrow></mfrac></math> = 4</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The following pie chart gives the distribution of constituents in the human body.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159223298.png\" alt=\"rId58\" width=\"277\" height=\"228\"> <br>What is the central angle of the sector showing the distribution of protein and other constituents ?</p>",
                    question_hi: "<p>60. निम्नलिखित पाई चार्ट मानव शरीर में अवयवों के वितरण को दर्शाता है:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159223419.png\" alt=\"rId59\" width=\"264\" height=\"236\"> <br>प्रोटीन और अन्य अवयवों के वितरण को दर्शाने वाले त्रिज्यखंड (sector) का केंद्रीय कोण क्या है ?</p>",
                    options_en: [
                        "<p>45&deg;</p>",
                        "<p>108&deg;</p>",
                        "<p>34&deg;</p>",
                        "<p>120&deg;</p>"
                    ],
                    options_hi: [
                        "<p>45&deg;</p>",
                        "<p>108&deg;</p>",
                        "<p>34&deg;</p>",
                        "<p>120&deg;</p>"
                    ],
                    solution_en: "<p>60.(b)<br>1% = 3.6&deg;<br>percentage of the sector showing the distribution of protein and other constituents = (16 + 14) = 30%<br>So, required central angle = 30 &times; 3.6 = 108&deg;</p>",
                    solution_hi: "<p>60.(b)<br>1% = 3.6&deg;<br>प्रोटीन और अन्य अव्यय के वितरण को दर्शाने वाले क्षेत्र का प्रतिशत = (16 + 14) = 30%<br>अतः, अभीष्ट केन्द्रीय कोण = 30 &times; 3.6 = 108&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The hypotenuse of a right triangle is 6m more than twice the shortest side. If the third side is 2 m less than the hypotenuse, find the area(in m<sup>2</sup>) of the triangle.</p>",
                    question_hi: "<p>61. एक समकोण त्रिभुज का कर्ण सबसे छोटी भुजा के दोगुने से 6 m अधिक है। यदि तीसरी भुजा, कर्ण से 2 m छोटी है, तो त्रिभुज का क्षेत्रफल (m<sup>2</sup> में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>110</p>",
                        "<p>100</p>",
                        "<p>120</p>",
                        "<p>130</p>"
                    ],
                    options_hi: [
                        "<p>110</p>",
                        "<p>100</p>",
                        "<p>120</p>",
                        "<p>130</p>"
                    ],
                    solution_en: "<p>61.(c) <br>Smallest side = x<br>Then, hypotenuse = 2x&nbsp;+ 6<br>And, third side = 2x&nbsp;+ 4<br>Take x&nbsp;= 10 m<br>Then, sides of right angle triangle = 10, 24 and 26 m (pythagorean triplet)<br>So, area of triangle = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 10 &times; 24 = 120 m</p>",
                    solution_hi: "<p>61.(c) <br>सबसे छोटी भुजा = x<br>तब, कर्ण = 2x&nbsp;+ 6<br>और, तीसरी भुजा = 2x&nbsp;+ 4<br>x = 10 मीटर लेने पर <br>फिर, समकोण त्रिभुज की भुजाएँ = 10, 24 और 26 मीटर (पायथागॉरियन त्रिक)<br>अत: त्रिभुज का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 10 &times; 24 = 120 मीटर</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The given table shows the percentage of marks obtained by 5 students in different subjects. Study the table and answer the question that follows. (Maximum marks are given beside subject)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159223565.png\" alt=\"rId60\" width=\"476\" height=\"149\"> <br>The marks obtained by Ram in Botany are less /more than Karn\'s marks in Botany by how much ?</p>",
                    question_hi: "<p>62. दी गई तालिका 5 छात्रों द्वारा विभिन्न विषयों में प्राप्त अंकों का प्रतिशत दर्शाती है। तालिका का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें। (अधिकतम अंक विषय के बगल में दिए गए हैं)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159223762.png\" alt=\"rId61\" width=\"525\" height=\"172\"> <br>राम द्वारा वनस्पति विज्ञान में प्राप्त अंक वनस्पति विज्ञान में कर्ण के अंकों से कितने कम/अधिक हैं ?</p>",
                    options_en: [
                        "<p>30 less</p>",
                        "<p>30 more</p>",
                        "<p>25 less</p>",
                        "<p>25 more</p>"
                    ],
                    options_hi: [
                        "<p>30 कम</p>",
                        "<p>30 अधिक</p>",
                        "<p>25 कम</p>",
                        "<p>25 अधिक</p>"
                    ],
                    solution_en: "<p>62.(b)<br>Marks obtained by Ram in Botany = 200 &times; 95% = 190<br>Marks obtained by Karn in Botany = 200 &times; 80% = 160<br>Hence, Ram obtained 30 marks more than Karn in Botany.</p>",
                    solution_hi: "<p>62.(b)<br>राम द्वारा वनस्पति विज्ञान में प्राप्त अंक = 200 &times; 95% = 190<br>कर्ण द्वारा वनस्पति विज्ञान में प्राप्त अंक = 200 &times; 80% = 160<br>अतः, राम को वनस्पति विज्ञान में कर्ण से 30 अंक अधिक प्राप्त हुए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. A person saves 45% of his income. Next year, his income increases by 200%. He increases his savings by five times the previous year&rsquo;s savings. By what percentage (correct to one place of decimal) has he increased/decreased his expenditure ?</p>",
                    question_hi: "<p>63. एक व्यक्ति अपनी आय का 45% बचाता है। अगले वर्ष, उसकी आय में 200% की वृद्धि होती है। वह अपनी बचत को पिछले वर्ष की बचत के मुक़ाबले पाँच गुना बढ़ा देता है। उसने अपने व्यय में कितने प्रतिशत (दशमलव के एक स्थान तक सही) की वृद्धि /कमी की है ?</p>",
                    options_en: [
                        "<p>Increased, 63.4%</p>",
                        "<p>Increased, 36.4%</p>",
                        "<p>Decreased, 63.4%</p>",
                        "<p>Decreased, 36.4%</p>"
                    ],
                    options_hi: [
                        "<p>वृद्धि, 63.4%</p>",
                        "<p>वृद्धि, 36.4%</p>",
                        "<p>कमी, 63.4%</p>",
                        "<p>कमी, 36.4%</p>"
                    ],
                    solution_en: "<p>63.(b) <br><strong>Ratio&nbsp; &nbsp;-&nbsp; &nbsp; initial&nbsp; :&nbsp; new</strong><br><strong>Income -&nbsp;</strong> &nbsp; 100&nbsp; &nbsp; :&nbsp; &nbsp;300<br><strong>Expendi. -</strong>&nbsp; &nbsp;55&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 75<br>----------------------------------<br><strong>Savings -&nbsp; &nbsp; </strong>45&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;225<br>Desired change in expenditure = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>55</mn></mrow></mfrac></math> &times; 100 = 36.4% (increased)</p>",
                    solution_hi: "<p>63.(b) <br><strong>अनुपात -&nbsp; प्रारंभिक&nbsp; :&nbsp; नया</strong><br><strong>आय&nbsp; &nbsp; &nbsp; -</strong>&nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp;:&nbsp; 300<br><strong>व्यय&nbsp; &nbsp; &nbsp; -&nbsp; &nbsp; &nbsp; </strong>55&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 75<br>----------------------------------<br><strong>बचत&nbsp; &nbsp; -&nbsp; &nbsp; &nbsp; &nbsp;</strong>45&nbsp; &nbsp; &nbsp; :&nbsp; 225<br>व्यय में वांछित परिवर्तन = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>55</mn></mrow></mfrac></math> &times; 100 = 36.4% (वृद्धि)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A natural number n divides 732 and leaves 12 as a remainder. How many values of n are possible ?</p>",
                    question_hi: "<p>64. एक प्राकृत संख्या n, 732 को विभाजित करती है और शेषफल 12 देती है। n के कितने मान संभव हैं ?</p>",
                    options_en: [
                        "<p>20</p>",
                        "<p>16</p>",
                        "<p>18</p>",
                        "<p>22</p>"
                    ],
                    options_hi: [
                        "<p>20</p>",
                        "<p>16</p>",
                        "<p>18</p>",
                        "<p>22</p>"
                    ],
                    solution_en: "<p>64.(a)<br>n divides 732 and leaves 12 as a remainder<br>Then, number n divides (732 - 12) = 720 completely<br>Now, factor of 720 = 2<sup>4</sup>&nbsp;&times; 3<sup>2</sup> &times; 5<sup>1</sup><br>Number of factor = (4 + 1) &times; (2 + 1) &times; (1 + 1) = 30<br>Since, remainder is 12 then, value of n must be greater than 12<br>Number of factors of 720 which are less or equal to 12 <br>= (1, 2, 3, 4, 5, 6, 8, 9, 10, 12) = 10 factor<br>So the possible number of n = 30 - 10 = 20</p>",
                    solution_hi: "<p>64.(a)<br>n 732 को विभाजित करता है और 12 शेषफल के रूप में छोड़ता है<br>फिर, संख्या n (732 - 12) = 720 को पूर्णतः विभाजित करती है<br>अब, 720 का गुणनखंड = 2<sup>4</sup>&nbsp;&times; 3<sup>2</sup> &times; 5<sup>1</sup><br>गुणनखंड की संख्या = (4 + 1) &times; (2 + 1) &times; (1 + 1) = 30<br>चूँकि, शेषफल 12 है, तो n का मान 12 से अधिक होना चाहिए<br>720 के गुणनखंड की संख्याएँ जो 12 से कम या उसके बराबर हैं <br>= (1, 2, 3, 4, 5, 6, 8, 9, 10, 12) = 10 गुणनखंड<br>अतः 20 के आवश्यक मानो की संख्या = 30 - 10 = 20</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A class of 25 students took and English test. 15 students had an average score of 80. The other students had and average score of 60. What is the average score of the whole class ?</p>",
                    question_hi: "<p>65. 25 छात्रों की एक कक्षा ने अंग्रेजी की परीक्षा दी। 15 छात्रों के औसत प्राप्तांक 80 थे। अन्य छात्रों के औसत प्राप्तांक 60 थे। पूरी कक्षा का औसत प्राप्तांक क्या है ?</p>",
                    options_en: [
                        "<p>80</p>",
                        "<p>72</p>",
                        "<p>76</p>",
                        "<p>74</p>"
                    ],
                    options_hi: [
                        "<p>80</p>",
                        "<p>72</p>",
                        "<p>76</p>",
                        "<p>74</p>"
                    ],
                    solution_en: "<p>65.(b) <br>Average of class = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>80</mn><mo>+</mo><mn>10</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>25</mn></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1800</mn><mn>25</mn></mfrac></math> = 72</p>",
                    solution_hi: "<p>65.(b) <br>कक्षा का औसत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>&#215;</mo><mn>80</mn><mo>+</mo><mn>10</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>25</mn></mfrac></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1800</mn><mn>25</mn></mfrac></math> = 72</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. PQ is the diameter of a circle with centre O. PT is a tangent touching the circle at P. The secant QT intersects the circle at S. O and S are joined. If &ang; SOP = 104&deg;, what is the measure (in degrees) of &ang;PTQ ?</p>",
                    question_hi: "<p>66. केंद्र O वाले एक वृत्त का व्यास PQ है। PT वृत्त को P पर स्पर्श करने वाली एक स्पर्श रेखा है। छेदक रेखा QT वृत्त को S पर काटती है। O और S जुड़े हुए हैं। यदि &ang; SOP = 104&deg; है, तो &ang;PTQ का माप (डिग्री में) क्या है ?</p>",
                    options_en: [
                        "<p>45</p>",
                        "<p>62</p>",
                        "<p>76</p>",
                        "<p>38</p>"
                    ],
                    options_hi: [
                        "<p>45</p>",
                        "<p>62</p>",
                        "<p>76</p>",
                        "<p>38</p>"
                    ],
                    solution_en: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159223925.png\" alt=\"rId62\" width=\"128\" height=\"169\"><br>In <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>SOQ<br>&ang;QSO + &ang;SQO = 104&deg;&nbsp; &nbsp; [exterior angle is equal to sum of opposite interior angle]<br>2&ang;SQO = 104&deg;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; [opposite angle of same side (OS = OQ = Radius)]<br>&ang;SQO = 52&deg;<br>Now, in right angle <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>TPQ<br>52&deg; + 90&deg; + &ang;PTQ = 180&deg;<br>&ang;PTQ = 38&deg;</p>",
                    solution_hi: "<p>66.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159223925.png\" alt=\"rId62\" width=\"128\" height=\"169\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>SOQ में<br>&ang;QSO + &ang;SQO = 104&deg;&nbsp; &nbsp; &nbsp;[बाहरी कोण विपरीत आंतरिक कोण के योग के बराबर है]<br>2&ang;SQO = 104&deg;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;[समान भुजा का विपरीत कोण (OS = OQ = त्रिज्या)]<br>&ang;SQO = 52&deg;<br>अब, समकोण <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>TPQ में<br>52&deg; + 90&deg; + &ang;PTQ = 180&deg;<br>&ang;PTQ = 38&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. If K is the mean of 2, 3, 4, K, then the mode is:</p>",
                    question_hi: "<p>67. यदि K, 2, 3, 4, K का माध्य है, तो बहुलक है:</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>67.(c)<br>K = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>4</mn><mo>+</mo><mi>k</mi></mrow><mn>4</mn></mfrac></math><br>4k = 9 + k<br>K = 3<br>Numbers = 2, 3, 4 and 3<br>Mode = 3</p>",
                    solution_hi: "<p>67.(c)<br>K = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>4</mn><mo>+</mo><mi>k</mi></mrow><mn>4</mn></mfrac></math><br>4k = 9 + k<br>K = 3<br>संख्या = 2, 3, 4 और 3<br>बहुलक = 3</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. A car covers a certain distance in 85 minutes, if it runs at a speed of 36 km/hr. The speed at which the car must run to reduce the time of journey to 51 minutes will be:</p>",
                    question_hi: "<p>68. यदि एक कार 36 किमी/घंटा की चाल से चलती है तो वह 85 मिनट में एक निश्चित दूरी तय करती है। यात्रा के समय को 51 मिनट तक कम करने के लिए कार को किस चाल से चलाना होगा ?</p>",
                    options_en: [
                        "<p>50 km/hr</p>",
                        "<p>60 km/hr</p>",
                        "<p>70 km/hr</p>",
                        "<p>80 km/hr</p>"
                    ],
                    options_hi: [
                        "<p>50 किमी/घंटा</p>",
                        "<p>60 किमी/घंटा</p>",
                        "<p>70 किमी/घंटा</p>",
                        "<p>80 किमी/घंटा</p>"
                    ],
                    solution_en: "<p>68.(b)<br>Distance = 36 &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 51 km<br>Now, car covers 51 km in 51 minute<br>Hence, required speed = <math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>51</mn></mrow></mfrac></math> &times; 60 = 60 km/h</p>",
                    solution_hi: "<p>68.(b)<br>दूरी = 36 &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 51 किमी<br>अब, कार 51 मिनट में 51 किमी की दूरी तय करती है<br>अतः, आवश्यक गति = <math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>51</mn></mrow></mfrac></math> &times; 60 = 60 किमी/घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A commodity is made of two metals, A and B, which cost ₹35 per kg and ₹40 per kg, respectively, in the ratio of 2 : 3. What is the cost (in ₹) of 12 kilograms of this commodity ?</p>",
                    question_hi: "<p>69. एक वस्तु दो धातुओं A और B से मिलकर बनी है, जिनका मूल्य क्रमशः ₹35 प्रति किलोग्राम और ₹40 प्रति किलोग्राम है और जो क्रमशः 2 : 3 के अनुपात में हैं। इस वस्तु के 12 किलोग्राम का मूल्य (₹ में) क्या होगा ?</p>",
                    options_en: [
                        "<p>228</p>",
                        "<p>152</p>",
                        "<p>456</p>",
                        "<p>38</p>"
                    ],
                    options_hi: [
                        "<p>228</p>",
                        "<p>152</p>",
                        "<p>456</p>",
                        "<p>38</p>"
                    ],
                    solution_en: "<p>69.(c)<br>Per kg cost of metals = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>40</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>190</mn><mn>5</mn></mfrac></math> = ₹38<br>So, cost of 12kg of commodity = 12 &times; 38 = ₹456</p>",
                    solution_hi: "<p>69.(c)<br>धातुओं की प्रति किग्रा लागत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>40</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>190</mn><mn>5</mn></mfrac></math> = ₹38<br>तो, 12 किलो वस्तु की लागत = 12 &times; 38 = ₹456</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. An amount of ₹8,000 was invested for 2 years, partly in scheme 1 at the rate of 5% simple interest per annum and the rest in scheme 2 at the rate of 4% simple interest per annum. The total interest received at the end was ₹720. The amount of money invested in scheme 1 is:</p>",
                    question_hi: "<p>70. ₹8,000 की धनराशि का आंशिक रूप से 2 वर्ष के लिए योजना 1 में 5% साधारण ब्याज की दर से और शेष धनराशि का योजना 2 में 4% साधारण ब्याज की दर से निवेश किया जाता हैं। अवधि के अंत में प्राप्त कुल ब्याज ₹720 था। योजना 1 में निवेश की गई धनराशि कितनी है ?</p>",
                    options_en: [
                        "<p>₹7,200</p>",
                        "<p>₹4,400</p>",
                        "<p>₹3,640</p>",
                        "<p>₹4,000</p>"
                    ],
                    options_hi: [
                        "<p>₹7,200</p>",
                        "<p>₹4,400</p>",
                        "<p>₹3,640</p>",
                        "<p>₹4,000</p>"
                    ],
                    solution_en: "<p>70.(d)<br>Overall rate = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 9%<br>Rate on scheme 1st = 5 &times; 2 = 10%<br>Rate on scheme 2nd = 4 &times; 2 = 8%<br>By alligation method :-&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159224088.png\" alt=\"rId63\" width=\"263\" height=\"138\"><br>So the amount in both schemes is equal.<br>Hence, the amount of money invested in scheme 1st is 4000.</p>",
                    solution_hi: "<p>70.(d)<br>कुल दर = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math> &times; 100 = 9%<br>योजना 1 पर दर = 5 &times; 2 = 10%<br>योजना 2 पर दर = 4 &times; 2 = 8%<br>पृथकीकरण विधि द्वारा :- <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159224212.png\" alt=\"rId64\" width=\"204\" height=\"137\"><br>इसलिए दोनों योजनाओं में धनराशि बराबर है.<br>अतः, योजना 1 में निवेश की गई धनराशि 4000 है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A robber steals a bag from a man at 5 p.m. and starts running at 10 km/h. A policeman is informed about the robber at 5:12 p.m. and he chases him on a cycle at 15 km/h. At what time is the robber caught by the policeman ?</p>",
                    question_hi: "<p>71. एक लुटेरा 5 p.m. पर एक आदमी का बैग चुरा कर 10 km/h की चाल से दौड़ना शुरू करता है। एक पुलिसकर्मी को 5:12 p.m. पर लुटेरे के बारे में सूचित किया जाता है और वह 15 km/h की चाल से साइकिल पर चोर का पीछा करता है। किस समय पुलिसकर्मी लुटेरे को पकड़ेगा ?</p>",
                    options_en: [
                        "<p>5 : 36 p.m.</p>",
                        "<p>5 : 48 p.m</p>",
                        "<p>5 : 40 p.m.</p>",
                        "<p>5 : 12 p.m.</p>"
                    ],
                    options_hi: [
                        "<p>5 : 36 p.m.</p>",
                        "<p>5 : 48 p.m</p>",
                        "<p>5 : 40 p.m.</p>",
                        "<p>5 : 12 p.m.</p>"
                    ],
                    solution_en: "<p>71.(a)<br>Total distance covered by robber in 12 min. = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> &times; 10 = 2 km<br>Relative speed of police and robber = (15 - 10) km/h = 5 km/h<br>So, time taken by police to catch the robber = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 60 = 24 min.<br>Hence, required time = 5:12 p.m. + 24 min. = 5:36 p.m.</p>",
                    solution_hi: "<p>71.(a)<br>लुटेरे द्वारा 12 मिनट में तय की गई कुल दूरी = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> &times; 10 = 2 किमी<br>पुलिस और लुटेरे की सापेक्ष गति = (15 - 10) किमी/घंटा = 5 किमी/घंटा<br>अतः पुलिस द्वारा लुटेरे को पकड़ने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 60 = 24 मिनट<br>अतः, आवश्यक समय = 5:12 p.m. + 24 मिनट = 5:36 p.m.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. A tap can fill a cistern in 10 minutes and another tap can empty it in 12 minutes. If both the taps are open, the time (in hours) taken to fill the tank will be:</p>",
                    question_hi: "<p>72. एक नल एक टंकी को 10 मिनट में भर सकता है और दूसरा नल इसे 12 मिनट में खाली कर सकता है। यदि दोनों नल खुले हैं, तो टंकी भरने में लगने वाला समय (घंटों में) कितना होगा ?</p>",
                    options_en: [
                        "<p>1.5 hours</p>",
                        "<p>2.5 hours</p>",
                        "<p>2 hours</p>",
                        "<p>1 hour</p>"
                    ],
                    options_hi: [
                        "<p>1.5 घंटे</p>",
                        "<p>2.5 घंटे</p>",
                        "<p>2 घंटे</p>",
                        "<p>1 घंटा</p>"
                    ],
                    solution_en: "<p>72.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159224333.png\" alt=\"rId65\" width=\"287\" height=\"164\"><br>Time taken to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>6</mn><mo>-</mo><mn>5</mn></mrow></mfrac></math> = 60 min. or 1 hours</p>",
                    solution_hi: "<p>72.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159224455.png\" alt=\"rId66\" width=\"276\" height=\"162\"><br>टंकी भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>6</mn><mo>-</mo><mn>5</mn></mrow></mfrac></math> = 60 मिनट या 1 घंटा</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. LCM and HCF of x and y are 4 times and 2 times respectively of the HCF of 42 and 120. If x is 36, then what is the value of y ?</p>",
                    question_hi: "<p>73. x और y के LCM तथा HCF क्रमशः 42 और 120 के HCF का 4 गुना और 2 गुना हैं। यदि x का मान 36 है, तो y का मान क्या होगा ?</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>12</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>6</p>",
                        "<p>8</p>",
                        "<p>12</p>"
                    ],
                    solution_en: "<p>73.(c)<br>HCF of 42 and 120 = 6<br>LCM of x and y = 4 &times; 6 = 24<br>HCF of x and y = 2 &times; 6 = 12<br>Now, 36 &times; y = 24 &times; 12<br>y = 8.</p>",
                    solution_hi: "<p>73.(c)<br>42 और 120 का H.C.F. = 6<br>x और y का L.C.M. = 4 &times; 6 = 24<br>x और y का H.C.F.= 2 &times; 6 = 12<br>अब, 36 &times; y = 24 &times; 12<br>y = 8.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The bar graph given below shows the sales of ball pens (in thousand numbers) from six shops S1, S2, S3, S4, S5, and S6, during two consecutive years, 2011 and 2012.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159224537.png\" alt=\"rId67\" width=\"424\" height=\"274\"> <br>What\'s is the ratio of the total sales of shop S2 for both years to the total sales of Shop S5 for both years ?</p>",
                    question_hi: "<p>74. नीचे दिया गया दंड आलेख दो क्रमिक वर्षों, 2011 और 2012 के दौरान छः दुकानों S1, S2, S3, S4, S5 ओर S6 से बॉल पेनों की बिक्री (हजार संख्या में) को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739159224669.png\" alt=\"rId68\" width=\"408\" height=\"264\"> <br>दोनों वर्षों में दुकान S2 की कुल बिक्री और दोनों वर्षों में दुकान S5 की कुल बिक्री का अनुपात क्या है ?</p>",
                    options_en: [
                        "<p>8 : 7</p>",
                        "<p>7 : 5</p>",
                        "<p>5 : 8</p>",
                        "<p>3 : 7</p>"
                    ],
                    options_hi: [
                        "<p>8 : 7</p>",
                        "<p>7 : 5</p>",
                        "<p>5 : 8</p>",
                        "<p>3 : 7</p>"
                    ],
                    solution_en: "<p>74.(a) Total sales of shop S2 for both the years = 70 + 90 = 160<br>Total sales of shop S5 for both the years = 80 + 60 = 140 <br>Required ratio : 160 : 140 = 8 : 7</p>",
                    solution_hi: "<p>74.(a) दोनों वर्षों के लिए दुकान S2 की कुल बिक्री = 70 + 90 = 160<br>दोनों वर्षों में दुकान S5 की कुल बिक्री = 80 + 60 = 140 <br>आवश्यक अनुपात : 160 : 140 = 8 : 7</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The average monthly expenditure of a family was ₹2,500 during the first 4 months, ₹2,750 during the next 5 months and ₹3,550 during the last 3 months of a year. If the total saving during the year was ₹5,500, what was the average monthly income of the family ?</p>",
                    question_hi: "<p>75. एक परिवार का औसत मासिक खर्च पहले 4 माह के दौरान ₹2,500, अगले 5 माह के दौरान ₹2,750 और वर्ष के अंतिम 3 माह के दौरान ₹3,550 था। यदि वर्ष के दौरान कुल बचत ₹5,500 थी, तो परिवार की औसत मासिक आय कितनी थी ?</p>",
                    options_en: [
                        "<p>₹5,790</p>",
                        "<p>₹7,355</p>",
                        "<p>₹4,375</p>",
                        "<p>₹3,325</p>"
                    ],
                    options_hi: [
                        "<p>₹5,790</p>",
                        "<p>₹7,355</p>",
                        "<p>₹4,375</p>",
                        "<p>₹3,325</p>"
                    ],
                    solution_en: "<p>75.(d)<br>Total expenditure of the family = 2500 &times; 4 + 2750 &times; 5 + 3550 &times; 3 = 34400<br>Total saving of family = 5500<br>Now, income = expenditure + savings <br>income = 34400 + 5500 = Rs. 39900<br>Hence, average monthly income of family = <math display=\"inline\"><mfrac><mrow><mn>39900</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = Rs. 3325</p>",
                    solution_hi: "<p>75.(d)<br>परिवार का कुल खर्च = 2500 &times; 4 + 2750 &times; 5 + 3550 &times; 3 = 34400<br>परिवार की कुल बचत = 5500<br>अब, आय = व्यय + बचत <br>आय = 34400 + 5500 = रु. 39900<br>अतः, परिवार की औसत मासिक आय = <math display=\"inline\"><mfrac><mrow><mn>39900</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = रु. 3325</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate synonym of the given word. <br>Rout</p>",
                    question_hi: "<p>76. Select the most appropriate synonym of the given word. <br>Rout</p>",
                    options_en: [
                        "<p>death</p>",
                        "<p>defeat</p>",
                        "<p>loss</p>",
                        "<p>crash</p>"
                    ],
                    options_hi: [
                        "<p>death</p>",
                        "<p>defeat</p>",
                        "<p>loss</p>",
                        "<p>crash</p>"
                    ],
                    solution_en: "<p>76.(b)<strong> Defeat- </strong>overcome or beat.<br><strong>Rout-</strong> defeat or cause to retreat in disorder<br><strong>Death-</strong> Demise, passing away<br><strong>Loss-</strong> losing someone or something<br><strong>Crash-</strong> move or cause to move with force</p>",
                    solution_hi: "<p>76.(b) <strong>Defeat-</strong> मात देना ।<br><strong>Rout-</strong> हार या अव्यवस्था में पीछे हटने का कारण<br><strong>Death-</strong> मरना, मृत्यु<br><strong>Loss-</strong> खोना<br><strong>Crash-</strong> टकराना</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the correct passive form of the given sentence.<br>Grow more trees.</p>",
                    question_hi: "<p>77. Select the correct passive form of the given sentence.<br>Grow more trees.</p>",
                    options_en: [
                        "<p>Let more trees be grown.</p>",
                        "<p>More trees should be growing.</p>",
                        "<p>More trees should grow.</p>",
                        "<p>You should grow more trees.</p>"
                    ],
                    options_hi: [
                        "<p>Let more trees be grown.</p>",
                        "<p>More trees should be growing.</p>",
                        "<p>More trees should grow.</p>",
                        "<p>You should grow more trees.</p>"
                    ],
                    solution_en: "<p>77.(a) Let more trees be grown.(Correct)<br>(b) More trees should be <span style=\"text-decoration: underline;\">growing</span>.(Incorrect form of the verb)<br>(c) More trees should grow.(Incorrect Sentence Structure)<br>(d) You should grow more trees.(Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>77.(a) Let more trees be grown.(Correct)<br>(b) More trees should be <span style=\"text-decoration: underline;\">growing</span>.(Verb का गलत form)<br>(c) More trees should grow.(गलत Sentence Structure)<br>(d) You should grow more trees.(गलत Sentence Structure)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. The following sentence has been divided into parts. One of them may contain an error.<br>Select the part that contains the error from the given options. If you don&rsquo;t find any error, mark &lsquo;No error&rsquo; as your answer.<br>He has been (1) watered the plants (2) for one hour. (3) No error</p>",
                    question_hi: "<p>78. The following sentence has been divided into parts. One of them may contain an error.<br>Select the part that contains the error from the given options. If you don&rsquo;t find any error, mark &lsquo;No error&rsquo; as your answer.<br>He has been (1) watered the plants (2) for one hour. (3) No error</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>2</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>2</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>78.(c) watered the plants<br>&lsquo;Watered&rsquo; must be replaced with &lsquo;watering&rsquo; as &lsquo;Singular Sub + has been + V<sub>ing</sub>&rsquo; is the correct grammatical structure for the sentence which is in present perfect continuous tense. The subject &lsquo;he&rsquo; is singular. Hence, &lsquo;watering the plants&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(c) watered the plants<br>&lsquo;Watered&rsquo; के स्थान पर &lsquo;watering&rsquo; का प्रयोग होगा, क्योंकि &lsquo;Singular Sub + has been + V<sub>ing</sub>&rsquo; sentence के लिए सही grammatical structure है, जो present perfect continuous tense में है। Subject &lsquo;he&rsquo; singular है। अतः, &lsquo;watering the plants&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>To have an axe to grind</p>",
                    question_hi: "<p>79. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>To have an axe to grind</p>",
                    options_en: [
                        "<p>To have a selfish motive in doing something</p>",
                        "<p>To have an indomitable task to accomplish</p>",
                        "<p>To have adequate me of subsistence</p>",
                        "<p>To have access to top levels of authority</p>"
                    ],
                    options_hi: [
                        "<p>To have a selfish motive in doing something</p>",
                        "<p>To have an indomitable task to accomplish</p>",
                        "<p>To have adequate me of subsistence</p>",
                        "<p>To have access to top levels of authority</p>"
                    ],
                    solution_en: "<p>79.(a) To have a selfish motive in doing something<br>To have an axe to grind- To have a selfish motive in doing something<br>E.g.- I think Neha had an axe to grind that is why she voted against me in the referendum.</p>",
                    solution_hi: "<p>79.(a) To have a selfish motive in doing something<br>To have an axe to grind- To have a selfish motive in doing something (स्वार्थ से भरा उदेश्य)<br>E.g.- I think Neha had an axe to grind that is why she voted against me in the referendum.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>She faced a <span style=\"text-decoration: underline;\">session of abuse</span> after insisting that she was too beautiful to be liked.</p>",
                    question_hi: "<p>80. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>She faced a <span style=\"text-decoration: underline;\">session of abuse</span> after insisting that she was too beautiful to be liked.</p>",
                    options_en: [
                        "<p>recital of abuse</p>",
                        "<p>tirade of abuse</p>",
                        "<p>declamation of abuse</p>",
                        "<p>recitation of abuse</p>"
                    ],
                    options_hi: [
                        "<p>recital of abuse</p>",
                        "<p>tirade of abuse</p>",
                        "<p>declamation of abuse</p>",
                        "<p>recitation of abuse</p>"
                    ],
                    solution_en: "<p>80.(b) tirade of abuse<br>&lsquo;Tirade of abuse&rsquo; is the correct phrase to use here. It means a long angry speech in which someone criticies a person. The given sentence states that she faced a tirade of abuse after insisting that she was too beautiful to be liked. Hence, &lsquo;tirade of abuse&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>80.(b) tirade of abuse<br>&lsquo;Tirade of abuse&rsquo; यहाँ प्रयोग के लिए सही phrase है। इसका अर्थ है एक लंबा क्रोधपूर्ण भाषण जिसमें कोई व्यक्ति किसी की आलोचना करता है। दिए गए sentence में कहा गया है कि इस बात पर ज़ोर देने के बाद कि वह इतनी सुंदर है कि उसे पसंद नहीं किया जा सकता, उसे काफी दुर्व्यवहार(tirade of abuse) का सामना करना पड़ा। अतः, &lsquo;tirade of abuse&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>A written declaration of government or a political party.</p>",
                    question_hi: "<p>81. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>A written declaration of government or a political party.</p>",
                    options_en: [
                        "<p>manifesto</p>",
                        "<p>affidavit</p>",
                        "<p>dossier</p>",
                        "<p>document</p>"
                    ],
                    options_hi: [
                        "<p>manifesto</p>",
                        "<p>affidavit</p>",
                        "<p>dossier</p>",
                        "<p>document</p>"
                    ],
                    solution_en: "<p>81.(a) manifesto<br><strong>Manifesto </strong>- a public declaration of policy and aims, especially one issued before an election by a political party or candidate.<br><strong>Affidavit</strong> - a written statement confirmed by oath or affirmation, for use as evidence in court.<br><strong>Dossier</strong> - a collection of documents about a particular person, event, or subject.<br><strong>Document</strong> - a piece of written, printed, or electronic matter that provides information or evidence or that serves as an official record.</p>",
                    solution_hi: "<p>81.(a) manifesto<br><strong>Manifesto </strong>- विशेष रूप से एक राजनीतिक दल या उम्मीदवार द्वारा नीति और उद्देश्यों की एक सार्वजनिक घोषणा, चुनाव से पहले जारी की गई।<br><strong>Affidavit</strong> - अदालत में सबूत के रूप में शपथ या प्रतिज्ञान द्वारा पुष्टि किया गया एक लिखित बयान <br><strong>Dossier</strong> -किसी विशेष व्यक्ति, घटना या विषय के बारे में दस्तावेजों का संग्रह।<br><strong>Document</strong> - लिखित, मुद्रित या इलेक्ट्रॉनिक सामग्री का एक टुकड़ा जो सूचना या साक्ष्य प्रदान करता है या जो आधिकारिक रिकॉर्ड के रूप में कार्य करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the option that expresses the given sentence in direct speech.<br>He said that he might change that format.</p>",
                    question_hi: "<p>82. Select the option that expresses the given sentence in direct speech.<br>He said that he might change that format.</p>",
                    options_en: [
                        "<p>He told, &ldquo;He may change that format&rdquo;</p>",
                        "<p>He said, &ldquo;I may change this format&rdquo;</p>",
                        "<p>He said, &ldquo;I will change that format&rdquo;.</p>",
                        "<p>He says, &ldquo;He might change that format</p>"
                    ],
                    options_hi: [
                        "<p>He told, &ldquo;He may change that format&rdquo;</p>",
                        "<p>He said, &ldquo;I may change this format&rdquo;</p>",
                        "<p>He said, &ldquo;I will change that format&rdquo;.</p>",
                        "<p>He says, &ldquo;He might change that format</p>"
                    ],
                    solution_en: "<p>82.(b) He said, &ldquo;I may change this format&rdquo; (Correct)<br>(a) He told, &ldquo;<span style=\"text-decoration: underline;\">He</span> may change that format&rdquo; (Incorrect Pronoun)<br>(b) He said, &ldquo;I <span style=\"text-decoration: underline;\">will</span> change that format&rdquo;. (Incorrect Modal)<br>(c) He says, &ldquo;<span style=\"text-decoration: underline;\">He</span> <span style=\"text-decoration: underline;\">might</span> change that format. (Incorrect Pronoun and Modal)</p>",
                    solution_hi: "<p>82.(b) He said, &ldquo;I may change this format&rdquo; (Correct)<br>(a) He told, &ldquo;<span style=\"text-decoration: underline;\">He</span> may change that format&rdquo; (गलत Pronoun)<br>(b) He said, &ldquo;I <span style=\"text-decoration: underline;\">will</span> change that format&rdquo;. (गलत Modal)<br>(c) He says, &ldquo;<span style=\"text-decoration: underline;\">He</span> <span style=\"text-decoration: underline;\">might</span> change that format. (गलत Pronoun एवं Modal)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "83. Identify the word that is correctly spelt.",
                    question_hi: "83. Identify the word that is correctly spelt.",
                    options_en: [
                        " Hypochondria",
                        " Hyppochondria",
                        " Hypochondrea",
                        " Hyppochondrea"
                    ],
                    options_hi: [
                        " Hypochondria",
                        " Hyppochondria",
                        " Hypochondrea",
                        " Hyppochondrea"
                    ],
                    solution_en: "83.(a) Hypochondria. ",
                    solution_hi: "83.(a) Hypochondria.                   ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84.&nbsp;Select the correct active form of the given sentence.</p>\n<p><strong id=\"docs-internal-guid-1432eed0-7fff-37d3-1538-c0c8065ee2f2\"></strong>&nbsp;The workers will not be satisfied with empty promises.</p>",
                    question_hi: "<p>84. Select the correct active form of the given sentence.&nbsp;</p>\n<p>The workers will not be satisfied with empty promises.</p>",
                    options_en: [
                        "<p>Empty promises will not satisfy the workers.</p>",
                        "<p>Empty promises would not satisfy the workers.</p>",
                        "<p>Empty promises do not satisfy the workers.</p>",
                        "<p>Empty promises cannot satisfy the workers.</p>"
                    ],
                    options_hi: [
                        "<p>Empty promises will not satisfy the workers.</p>",
                        "<p>Empty promises would not satisfy the workers.</p>",
                        "<p>Empty promises do not satisfy the workers.</p>",
                        "<p>Empty promises cannot satisfy the workers.</p>"
                    ],
                    solution_en: "<p>84.(a) Empty promises will not satisfy the workers.<br>(b) Empty promises <span style=\"text-decoration: underline;\">would</span> not satisfy the workers.(Incorrect Modal)<br>(c) Empty promises <span style=\"text-decoration: underline;\">do not satisfy</span> the workers.(Incorrect Tense)<br>(d) Empty promises <span style=\"text-decoration: underline;\">cannot</span> satisfy the workers.(Incorrect Modal)</p>",
                    solution_hi: "<p>84.(a) Empty promises will not satisfy the workers.<br>(b) Empty promises <span style=\"text-decoration: underline;\">would</span> not satisfy the workers.(गलत Modal)<br>(c) Empty promises <span style=\"text-decoration: underline;\">do not satisfy</span> the workers.(गलत Tense)<br>(d) Empty promises <span style=\"text-decoration: underline;\">cannot</span> satisfy the workers.(गलत Modal)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Given below are four jumbled sentences. Find the correct order: <br>P : There is no link between the amount of money spent and beneficent exercise, else the poor would have creaking bodies forever.<br>Q : While some cost you nothing, others may require the investment of some amount of money.<br>R : However, it is important to remember that exercises should not be overdone.<br>S : It can take any form from sedentary ones like waiting to vigorous work &ndash; outs like a game of squash.</p>",
                    question_hi: "<p>85. Given below are four jumbled sentences. Find the correct order: <br>P : There is no link between the amount of money spent and beneficent exercise, else the poor would have creaking bodies forever.<br>Q : While some cost you nothing, others may require the investment of some amount of money.<br>R : However, it is important to remember that exercises should not be overdone.<br>S : It can take any form from sedentary ones like waiting to vigorous work &ndash; outs like a game of squash.</p>",
                    options_en: [
                        "<p>PQSR</p>",
                        "<p>QPRS</p>",
                        "<p>RSQP</p>",
                        "<p>SQPR</p>"
                    ],
                    options_hi: [
                        "<p>PQSR</p>",
                        "<p>QPRS</p>",
                        "<p>RSQP</p>",
                        "<p>SQPR</p>"
                    ],
                    solution_en: "<p>85.(a) PQSR <br>P will be the first sentence and it is immediately followed by sentence Q which tells that there is no relation between the benefits of an exercise with the amount of money spent. However, sentence Q will be followed by sentence S which defines the types of exercises. Further, sentence R will come, which tells exercises should not be overdone but done in moderate amounts. Going through the options, option (a) PQSR has the correct sequence.</p>",
                    solution_hi: "<p>85.(a) PQSR <br>P पहला वाक्य होगा और उसके ठीक बाद Q वाक्य होगा जो बताता है कि व्यायाम के लाभों और व्यय की गई राशि के बीच कोई संबंध नहीं है। हालाँकि, वाक्य Q के बाद वाक्य S होगा जो व्यायाम के प्रकारों को परिभाषित करता है। इसके अलावा, वाक्य R आएगा, जो बताता है कि व्यायाम को अधिक नहीं बल्कि नियमित मात्रा में किया जाना चाहिए। विकल्पों के माध्यम से , विकल्प (a) PQSR सही क्रम है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate antonym of the given word.<br>Niggardly</p>",
                    question_hi: "<p>86. Select the most appropriate antonym of the given word.<br>Niggardly</p>",
                    options_en: [
                        "<p>Hastily</p>",
                        "<p>lavishly</p>",
                        "<p>likely</p>",
                        "<p>gorgeously</p>"
                    ],
                    options_hi: [
                        "<p>Hastily</p>",
                        "<p>lavishly</p>",
                        "<p>likely</p>",
                        "<p>gorgeously</p>"
                    ],
                    solution_en: "<p>86.(b) <strong>Lavishly</strong>- in a sumptuously rich, elaborate, or luxurious manner.<br><strong>Niggardly-</strong> ungenerous with money, time, etc. <br><strong>Hastily-</strong> said or done in a hurry<br><strong>Likely-</strong> something probable, possible, plausible<br><strong>Gorgeously-</strong> in a good looking manner, attractively</p>",
                    solution_hi: "<p>86.(b) <strong>Lavishly</strong>- एक समृद्ध, विस्तृत या शानदार तरीके से।<br><strong>Niggardly-</strong> धन, समय आदि के प्रति उदार नहीं।<br><strong>Hastily-</strong> जल्दी में कही गई बात<br><strong>Likely-</strong> कुछ संभावित, संभव, प्रशंसनीय<br><strong>Gorgeously-</strong> सुन्दर ढंग से, आकर्षक रूप में</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Parts of the following sentence have been given as options. Select the option that contains an error.<br>The toast was raised in honour of the chairman yesterday at hall.</p>",
                    question_hi: "<p>87. Parts of the following sentence have been given as options. Select the option that contains an error.<br>The toast was raised in honour of the chairman yesterday at hall.</p>",
                    options_en: [
                        "<p>raised in honour</p>",
                        "<p>yesterday at hall.</p>",
                        "<p>The toast was</p>",
                        "<p>of the chairman</p>"
                    ],
                    options_hi: [
                        "<p>raised in honour</p>",
                        "<p>yesterday at hall.</p>",
                        "<p>The toast was</p>",
                        "<p>of the chairman</p>"
                    ],
                    solution_en: "<p>87.(b) yesterday at hall<br>&lsquo;Hall&rsquo; given in the sentence is specific and we use the definite article &lsquo;the&rsquo; before specific or particular nouns. Hence, &lsquo;yesterday at the hall&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>87.(b) yesterday at hall<br>दिए गए वाक्य में &lsquo;Hall&rsquo; specific है और हम specific या particular noun से पहले definite article &lsquo;the&rsquo; का प्रयोग करते हैं। अतः, &lsquo;yesterday at the hall&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>88. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>Psychometric</p>",
                        "<p>Phisiology</p>",
                        "<p>Psychiatry</p>",
                        "<p>Psychology</p>"
                    ],
                    options_hi: [
                        "<p>Psychometric</p>",
                        "<p>Phisiology</p>",
                        "<p>Psychiatry</p>",
                        "<p>Psychology</p>"
                    ],
                    solution_en: "<p>88.(b) Phisiology<strong id=\"docs-internal-guid-c5c0532e-7fff-9fb7-56b8-d04cb7fdbf07\"> </strong><br>&lsquo;Physiology&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>88.(b) Phisiology<strong id=\"docs-internal-guid-c5c0532e-7fff-9fb7-56b8-d04cb7fdbf07\"></strong><br>&lsquo;Physiology&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>I can finish the work by myself but she always tries to be <strong><span style=\"text-decoration: underline;\">a backseat driver</span>.</strong></p>",
                    question_hi: "<p>89. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>I can finish the work by myself but she always tries to be <span style=\"text-decoration: underline;\"><strong>a backseat driver</strong></span><strong>.</strong></p>",
                    options_en: [
                        "<p>person who misjudge others</p>",
                        "<p>person who wants to do things by herself</p>",
                        "<p>person who falsely accuses others</p>",
                        "<p>person who gives unwanted advice</p>"
                    ],
                    options_hi: [
                        "<p>person who misjudge others</p>",
                        "<p>person who wants to do things by herself</p>",
                        "<p>person who falsely accuses others</p>",
                        "<p>person who gives unwanted advice</p>"
                    ],
                    solution_en: "<p>89.(d) Person who gives unwanted advice. <br>Example- His old grandmother always tries to be a backseat driver.</p>",
                    solution_hi: "<p>89.(d) Person who gives unwanted advice / अवांछित सलाह देने वाला व्यक्ति। <br>उदाहरण - His old grandmother always tries to be a backseat driver. / उसकी बूढ़ी दादी हमेशा एक backseat driver बनने की कोशिश करती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>People at a religious gathering.</p>",
                    question_hi: "<p>90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>People at a religious gathering.</p>",
                    options_en: [
                        "<p>Rabble</p>",
                        "<p>Mob</p>",
                        "<p>Congregation</p>",
                        "<p>Crowd</p>"
                    ],
                    options_hi: [
                        "<p>Rabble</p>",
                        "<p>Mob</p>",
                        "<p>Congregation</p>",
                        "<p>Crowd</p>"
                    ],
                    solution_en: "<p>90.(c) congregation<br><strong>Rabble </strong>- a disorderly crowd; a mob.<br><strong>Mob</strong> - a large crowd of people, especially one that is disorderly and intent on causing trouble or violence.<br><strong>Congregation</strong> - a group of people assembled for religious worship.<br><strong>Crowd</strong> - a large number of people gathered together in a disorganized or unruly way.</p>",
                    solution_hi: "<p>90.(c) congregation<br><strong>Rabble</strong> - एक अव्यवस्थित भीड़।<br><strong>Mob</strong> - लोगों की एक बड़ी भीड़, विशेष रूप से वह जो अव्यवस्थित है और परेशानी या हिंसा पैदा करने का इरादा रखती है।<br><strong>Congregation -</strong> धार्मिक पूजा के लिए इकट्ठे लोगों का एक समूह।<br><strong id=\"docs-internal-guid-f6a1f708-7fff-13f6-45eb-06c72516a0db\"></strong><strong>Crowd</strong> - असंगठित या अनियंत्रित तरीके से बड़ी संख्या में एक साथ इकट्ठा हुए लोग।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the correct option to form a meaningful sentence.<br>Did / offences/ know / you / that men / four times / commit / driving / as many / women do? / as<br><strong>(a)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(b)&nbsp; &nbsp; &nbsp; &nbsp; (c)&nbsp; &nbsp; &nbsp; (d)&nbsp; &nbsp; &nbsp; &nbsp; (e)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (f)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (g)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(h)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(i)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (j)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(k)</strong></p>",
                    question_hi: "<p>91. Select the correct option to form a meaningful sentence.<br>Did / offences/ know / you / that men / four times / commit / driving / as many / women do? / as<br><strong>(a)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(b)&nbsp; &nbsp; &nbsp; &nbsp; (c)&nbsp; &nbsp; &nbsp; (d)&nbsp; &nbsp; &nbsp; &nbsp; (e)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (f)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (g)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(h)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(i)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (j)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (k)</strong></p>",
                    options_en: [
                        "<p>a, d, c, e, g, f, i, h, b, k, j</p>",
                        "<p>a, d, c, e, f, i, h, g, k, j, b</p>",
                        "<p>a, d, b, e, f, i, h, c, g, k, j</p>",
                        "<p>a, d, e, k, f, i, h, b, g, j, c</p>"
                    ],
                    options_hi: [
                        "<p>a, d, c, e, g, f, i, h, b, k, j</p>",
                        "<p>a, d, c, e, f, i, h, g, k, j, b</p>",
                        "<p>a, d, b, e, f, i, h, c, g, k, j</p>",
                        "<p>a, d, e, k, f, i, h, b, g, j, c</p>"
                    ],
                    solution_en: "<p>91.(a) a, d, c, e, g, f, i, h, b, k, j<br>The correct sentence is : &ldquo;Did you know that men commit four times as many driving offences as women do?&rdquo;</p>",
                    solution_hi: "<p>91.(a) a, d, c, e, g, f, i, h, b, k, j<br>&ldquo;Did you know that men commit four times as many driving offences as women do?&rdquo; सही sentence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Find a word that is the synonym of <br>Abandon</p>",
                    question_hi: "<p>92. Find a word that is the synonym of <br>Abandon</p>",
                    options_en: [
                        "<p>excuse</p>",
                        "<p>forsake</p>",
                        "<p>urge</p>",
                        "<p>risk</p>"
                    ],
                    options_hi: [
                        "<p>excuse</p>",
                        "<p>forsake</p>",
                        "<p>urge</p>",
                        "<p>risk</p>"
                    ],
                    solution_en: "<p>92.(b) <strong>forsake.</strong> <br><strong>Abandon</strong> - cease to support or look after (someone); desert.<br><strong>Forsake</strong> - abandon or leave</p>",
                    solution_hi: "<p>92.(b) <strong>forsake.</strong> <br><strong>Abandon</strong> - त्यागना<br><strong>Forsake</strong> - छोड़ना</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the correct option to substitute the underlined segment in the given sentence.&nbsp;<br>This secret will remain <span style=\"text-decoration: underline;\">between you and I</span>.</p>",
                    question_hi: "<p>93. Select the correct option to substitute the underlined segment in the given sentence.&nbsp;<br>This secret will remain <span style=\"text-decoration: underline;\">between you and I</span>.</p>",
                    options_en: [
                        "<p>between you</p>",
                        "<p>among you and I</p>",
                        "<p>between I and you</p>",
                        "<p>between you and me</p>"
                    ],
                    options_hi: [
                        "<p>between you</p>",
                        "<p>among you and I</p>",
                        "<p>between I and you</p>",
                        "<p>between you and me</p>"
                    ],
                    solution_en: "<p>93.(d) between you and me<br>A preposition takes an objective case pronoun after it. &lsquo;Between&rsquo; is a preposition that will take &lsquo;me&rsquo; instead of &lsquo;I&rsquo;. Hence, &lsquo;between you and me&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>93.(d) between you and me<br>किसी Preposition के बाद pronoun के objective case का प्रयोग होता है। &lsquo;Between&rsquo; एक preposition है जिसके साथ &lsquo;me&rsquo; का प्रयोग होगा &lsquo;I&rsquo; का नहीं । अतः, &lsquo;between you and me&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "94. Choose the word opposite in meaning to the  given word.<br />Alight",
                    question_hi: "94. Choose the word opposite in meaning to the  given word.<br />Alight",
                    options_en: [
                        " disembark",
                        " embark",
                        " embalm",
                        " align"
                    ],
                    options_hi: [
                        " disembark",
                        " embark",
                        " embalm",
                        " align"
                    ],
                    solution_en: "94.(b) embark.<br />Alight means to get off a bus, train etc. Embark means to get on a ship.",
                    solution_hi: "94.(b) embark.<br />Alight का अर्थ है बस, ट्रेन आदि से उतरना। Embark का अर्थ है जहाज पर चढ़ना।.",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>His________tastes and habits explain why he is always in debt.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>His_______ tastes and habits explain why he is always in debt.</p>",
                    options_en: [
                        " thrifty",
                        " extravagant",
                        " moderate ",
                        " judicious"
                    ],
                    options_hi: [
                        " thrifty",
                        " extravagant",
                        " moderate ",
                        " judicious"
                    ],
                    solution_en: "<p>95.(b) extravagant<br><strong>Extravagant </strong>- spending a lot more money. <br><strong>Thrifty</strong> - careful about spending money.</p>",
                    solution_hi: "<p>95.(b) extravagant<br><strong>Extravagant</strong> - बहुत अधिक पैसा खर्च करना। <br><strong>Thrifty</strong> - धन खर्च करने में सावधानी बरतना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :-</strong><br>Children working in hazardous industries are _____(96) to debilitating diseases which can cripple them for life. By sitting in cramped, damp, unhygienic spaces, their limbs become deformed for life. Inside the matchstick, fireworks and glass industries, they are______(97) of bronchial diseases and TB. Their mental and physical development is permanently______(98) by long hours of work. Once______(99), they can\'t get out of the vicious circle of poverty. They______(100) uneducated and powerless.<br>Select the most appropriate option for blank 96:</p>",
                    question_hi: "<p>96. <strong>Cloze Test :-</strong><br>Children working in hazardous industries are _____(96) to debilitating diseases which can cripple them for life. By sitting in cramped, damp, unhygienic spaces, their limbs become deformed for life. Inside the matchstick, fireworks and glass industries, they are______(97) of bronchial diseases and TB. Their mental and physical development is permanently______(98) by long hours of work. Once______(99), they can\'t get out of the vicious circle of poverty. They______(100) uneducated and powerless.<br>Select the most appropriate option for blank 96:</p>",
                    options_en: [
                        "<p>Questionable</p>",
                        "<p>Determined</p>",
                        "<p>Prone</p>",
                        "<p>Elevated</p>"
                    ],
                    options_hi: [
                        "<p>Questionable</p>",
                        "<p>Determined</p>",
                        "<p>Prone</p>",
                        "<p>Elevated</p>"
                    ],
                    solution_en: "<p>96.(c) <strong>Prone</strong><br>&lsquo;Prone&rsquo; means likely to be affected by something. The given passage states that children working in hazardous industries are prone to debilitating diseases which can cripple them for life. Hence, &lsquo;prone&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) <strong>Prone</strong><br>&lsquo;Prone&rsquo; का अर्थ है किसी चीज़ से प्रभावित होने की संभावना। दिए गए passage में कहा गया है कि खतरनाक उद्योगों (hazardous industries) में काम करने वाले बच्चों को दुर्बल करने वाली बीमारियों (debilitating diseases) का खतरा होता है जो उन्हें जीवन भर के लिए अपंग (cripple) बना सकती हैं। अतः, &lsquo;prone&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :-</strong><br>Children working in hazardous industries are _____(96) to debilitating diseases which can cripple them for life. By sitting in cramped, damp, unhygienic spaces, their limbs become deformed for life. Inside the matchstick, fireworks and glass industries, they are______(97) of bronchial diseases and TB. Their mental and physical development is permanently______(98) by long hours of work. Once______(99), they can\'t get out of the vicious circle of poverty. They______(100) uneducated and powerless.<br>Select the most appropriate option for blank 97:</p>",
                    question_hi: "<p>97. <strong>Cloze Test :-</strong><br>Children working in hazardous industries are _____(96) to debilitating diseases which can cripple them for life. By sitting in cramped, damp, unhygienic spaces, their limbs become deformed for life. Inside the matchstick, fireworks and glass industries, they are______(97) of bronchial diseases and TB. Their mental and physical development is permanently______(98) by long hours of work. Once______(99), they can\'t get out of the vicious circle of poverty. They______(100) uneducated and powerless.<br>Select the most appropriate option for blank 97:</p>",
                    options_en: [
                        "<p>Creators</p>",
                        "<p>Producers</p>",
                        "<p>Admirers</p>",
                        "<p>Victims</p>"
                    ],
                    options_hi: [
                        "<p>Creators</p>",
                        "<p>Producers</p>",
                        "<p>Admirers</p>",
                        "<p>Victims</p>"
                    ],
                    solution_en: "<p>97.(d) <strong>Victims</strong><br>&lsquo;Victim&rsquo; means a person who has suffered the effects of illness. The given passage states that inside the matchstick, fireworks and glass industries, they are victims of bronchial diseases and TB. Hence, &lsquo;victims&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(d) <strong>Victims</strong><br>&lsquo;Victim&rsquo; का अर्थ है वह व्यक्ति जो बीमारी (illness) के प्रभाव से पीड़ित है। दिए गए passage में कहा गया है कि माचिस (matchstick), आतिशबाजी (fireworks) और कांच उद्योगों में वे श्वसन संबंधी बीमारियों (bronchial diseases) और टीबी (TB) के शिकार होते हैं। अतः, &lsquo;victims&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :-</strong><br>Children working in hazardous industries are _____(96) to debilitating diseases which can cripple them for life. By sitting in cramped, damp, unhygienic spaces, their limbs become deformed for life. Inside the matchstick, fireworks and glass industries, they are______(97) of bronchial diseases and TB. Their mental and physical development is permanently______(98) by long hours of work. Once______(99), they can\'t get out of the vicious circle of poverty. They______(100) uneducated and powerless.<br>Select the most appropriate option for blank 98:</p>",
                    question_hi: "<p>98. <strong>Cloze Test :-</strong><br>Children working in hazardous industries are _____(96) to debilitating diseases which can cripple them for life. By sitting in cramped, damp, unhygienic spaces, their limbs become deformed for life. Inside the matchstick, fireworks and glass industries, they are______(97) of bronchial diseases and TB. Their mental and physical development is permanently______(98) by long hours of work. Once______(99), they can\'t get out of the vicious circle of poverty. They______(100) uneducated and powerless.<br>Select the most appropriate option for blank 98:</p>",
                    options_en: [
                        "<p>Examined</p>",
                        "<p>Impaired</p>",
                        "<p>Included</p>",
                        "<p>Established</p>"
                    ],
                    options_hi: [
                        "<p>Examined</p>",
                        "<p>Impaired</p>",
                        "<p>Included</p>",
                        "<p>Established</p>"
                    ],
                    solution_en: "<p>98.(b) <strong>Impaired</strong><br>&lsquo;Impaired&rsquo; means a reduced ability to function properly due to injury. The given passage states that their mental and physical development is permanently impaired by long hours of work. Hence, &lsquo;impaired&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(b) <strong>Impaired</strong><br>&lsquo;Impaired&rsquo; का अर्थ है चोट (injury) के कारण ठीक से काम करने की क्षमता में कमी। दिए गए passage में कहा गया है कि लंबे समय तक काम करने से उनका मानसिक (mental) एवं शारीरिक (physical) विकास स्थायी रूप से बाधित हो जाता है। अतः, &lsquo;impaired&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :-</strong><br>Children working in hazardous industries are _____(96) to debilitating diseases which can cripple them for life. By sitting in cramped, damp, unhygienic spaces, their limbs become deformed for life. Inside the matchstick, fireworks and glass industries, they are______(97) of bronchial diseases and TB. Their mental and physical development is permanently______(98) by long hours of work. Once______(99), they can\'t get out of the vicious circle of poverty. They______(100) uneducated and powerless.<br>Select the most appropriate option for blank 99:</p>",
                    question_hi: "<p>99. <strong>Cloze Test :-</strong><br>Children working in hazardous industries are _____(96) to debilitating diseases which can cripple them for life. By sitting in cramped, damp, unhygienic spaces, their limbs become deformed for life. Inside the matchstick, fireworks and glass industries, they are______(97) of bronchial diseases and TB. Their mental and physical development is permanently______(98) by long hours of work. Once______(99), they can\'t get out of the vicious circle of poverty. They______(100) uneducated and powerless.<br>Select the most appropriate option for blank 99:</p>",
                    options_en: [
                        "<p>Determined</p>",
                        "<p>Confirmed</p>",
                        "<p>Trapped</p>",
                        "<p>Adopted</p>"
                    ],
                    options_hi: [
                        "<p>Determined</p>",
                        "<p>Confirmed</p>",
                        "<p>Trapped</p>",
                        "<p>Adopted</p>"
                    ],
                    solution_en: "<p>99.(c) <strong>Trapped</strong><br>&lsquo;Trapped&rsquo; means unable to escape from a place or situation. The given passage states that once trapped, they can\'t get out of the vicious circle of poverty. Hence, &lsquo;trapped&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(c) <strong>Trapped</strong><br>&lsquo;Trapped&rsquo; का अर्थ है किसी place या situation से बचकर निकलने में असमर्थ होना। दिए गए passage में कहा गया है कि एक बार फंस जाने के बाद, वे गरीबी के दुष्चक्र (vicious circle) से बाहर नहीं निकल पाते। अतः, &lsquo;trapped&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :-</strong><br>Children working in hazardous industries are _____(96) to debilitating diseases which can cripple them for life. By sitting in cramped, damp, unhygienic spaces, their limbs become deformed for life. Inside the matchstick, fireworks and glass industries, they are______(97) of bronchial diseases and TB. Their mental and physical development is permanently______(98) by long hours of work. Once______(99), they can\'t get out of the vicious circle of poverty. They______(100) uneducated and powerless.<br>Select the most appropriate option for blank 100:</p>",
                    question_hi: "<p>100. <strong>Cloze Test :-</strong><br>Children working in hazardous industries are _____(96) to debilitating diseases which can cripple them for life. By sitting in cramped, damp, unhygienic spaces, their limbs become deformed for life. Inside the matchstick, fireworks and glass industries, they are______(97) of bronchial diseases and TB. Their mental and physical development is permanently______(98) by long hours of work. Once______(99), they can\'t get out of the vicious circle of poverty. They______(100) uneducated and powerless.<br>Select the most appropriate option for blank 100:</p>",
                    options_en: [
                        "<p>Remain</p>",
                        "<p>Choose</p>",
                        "<p>Compete</p>",
                        "<p>Accuse</p>"
                    ],
                    options_hi: [
                        "<p>Remain</p>",
                        "<p>Choose</p>",
                        "<p>Compete</p>",
                        "<p>Accuse</p>"
                    ],
                    solution_en: "<p>100.(a) <strong>Remain</strong><br>&lsquo;Remain&rsquo; means to stay in the same condition. The given passage states that they remain uneducated and powerless. Hence, &lsquo;remain&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(a) <strong>Remain</strong><br>&lsquo;Remain&rsquo; का अर्थ है उसी condition में बने रहना। दिए गए passage में कहा गया है कि वे अशिक्षित (uneducated) एवं शक्तिहीन (powerless) बने रहते हैं। अतः, &lsquo;remain&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>