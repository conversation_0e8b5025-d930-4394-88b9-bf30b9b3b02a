<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the option that can be used as a one-word substitute for the given group of words.<br>An utter state of confusion</p>",
                    question_hi: "<p>1. Select the option that can be used as a one-word substitute for the given group of words.<br>An utter state of confusion</p>",
                    options_en: ["<p>Pathos</p>", "<p>Engross</p>", 
                                "<p>Innuendos</p>", "<p>Chaos</p>"],
                    options_hi: ["<p>Pathos</p>", "<p>Engross</p>",
                                "<p>Innuendos</p>", "<p>Chaos</p>"],
                    solution_en: "<p>1.(d) <strong>Chaos</strong>- an utter state of confusion.<br><strong>Pathos</strong>- a quality that evokes pity or sadness.<br><strong>Engross</strong>- absorb all the attention or interest of.<br><strong>Innuendos</strong>- a remark or remarks that suggest something sexual or something unpleasant but do not refer to it directly.</p>",
                    solution_hi: "<p>1.(d) <strong>Chaos </strong>(अव्यवस्था)- an utter state of confusion.<br><strong>Pathos </strong>(मनोभाव)- a quality that evokes pity or sadness.<br><strong>Engross</strong> (ध्यान खींचना)- absorb all the attention or interest of.<br><strong>Innuendos </strong>(व्यंग्योक्ति)- a remark or remarks that suggest something sexual or something unpleasant but do not refer to it directly.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>After reflecting on the matter regarding his boss&rsquo;s selfish attitude, Raju decided to <span style=\"text-decoration: underline;\">force himself to perform an unpleasant action in a difficult situation in order to resolve the matter.</span></p>",
                    question_hi: "<p>2. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>After reflecting on the matter regarding his boss&rsquo;s selfish attitude, Raju decided to <span style=\"text-decoration: underline;\">force himself to perform an unpleasant action in a difficult situation in order to resolve the matter.</span>.</p>",
                    options_en: ["<p>bite the bullet</p>", "<p>dodge the bullet</p>", 
                                "<p>shoot the bullet</p>", "<p>ignore the bullet</p>"],
                    options_hi: ["<p>bite the bullet</p>", "<p>dodge the bullet</p>",
                                "<p>shoot the bullet</p>", "<p>ignore the bullet</p>"],
                    solution_en: "<p>2.(a) <strong>Bite the bullet-</strong> force himself to perform an unpleasant action in a difficult situation in order to resolve the matter.</p>",
                    solution_hi: "<p>2.(a) <strong>Bite the bullet-</strong> force himself to perform an unpleasant action in a difficult situation in order to resolve the matter./किसी कठिन परिस्थिति में मामले को सुलझाने के लिए स्वयं को कोई अप्रिय कार्य करने के लिए बाध्य करना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3. Identify the INCORRECTLY spelt word in the following sentence and select its correct spelling from the given options.<br />The occurence of this phenomenon is quite rare.",
                    question_hi: "3.  Identify the INCORRECTLY spelt word in the following sentence and select its correct spelling from the given options.<br />The occurence of this phenomenon is quite rare.",
                    options_en: [" occurrance", " occerence", 
                                " occurrence", " occurance"],
                    options_hi: [" occurrance", " occerence",
                                " occurrence", " occurance"],
                    solution_en: "3.(c) occurrence<br />\'Occurrence\' is the correct spelling. ",
                    solution_hi: "3.(c) occurrence<br />\'Occurrence\' सही spelling है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the option that expresses the given sentence in passive voice.<br>Meenakshi could have sent a reply.</p>",
                    question_hi: "<p>4. Select the option that expresses the given sentence in passive voice.<br>Meenakshi could have sent a reply.</p>",
                    options_en: ["<p>A reply could have be sent by Meenakshi.</p>", "<p>A reply could have been sent by Meenakshi.</p>", 
                                "<p>A reply could have sent by Meenakshi.</p>", "<p>A reply could have been send by Meenakshi.</p>"],
                    options_hi: ["<p>A reply could have be sent by Meenakshi.</p>", "<p>A reply could have been sent by Meenakshi.</p>",
                                "<p>A reply could have sent by Meenakshi.</p>", "<p>A reply could have been send by Meenakshi.</p>"],
                    solution_en: "<p>4.(b) A reply could have been sent by Meenakshi. (Correct)<br>(a) A reply could have <span style=\"text-decoration: underline;\">be</span> sent by Meenakshi. (Incorrect Verb)<br>(c) A reply could have sent by Meenakshi. (&lsquo;Been&rsquo; is missing)<br>(d) A reply could have been <span style=\"text-decoration: underline;\">send</span> by Meenakshi. (Incorrect form of the verb)</p>",
                    solution_hi: "<p>4.(b) A reply could have been sent by Meenakshi. (Correct)<br>(a) A reply could have <span style=\"text-decoration: underline;\">be</span> sent by Meenakshi. (गलत Verb)<br>(c) A reply could have sent by Meenakshi. (&lsquo;Been&rsquo; missing है)<br>(d) A reply could have been <span style=\"text-decoration: underline;\">send</span> by Meenakshi. (verb का गलत form)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the option that can be used as a one-word substitute for the given group of words.<br>Lack of interest, enthusiasm or concern</p>",
                    question_hi: "<p>5. Select the option that can be used as a one-word substitute for the given group of words.<br>Lack of interest, enthusiasm or concern</p>",
                    options_en: ["<p>Sympathy</p>", "<p>Apathy</p>", 
                                "<p>Empathy</p>", "<p>Antipathy</p>"],
                    options_hi: ["<p>Sympathy</p>", "<p>Apathy</p>",
                                "<p>Empathy</p>", "<p>Antipathy</p>"],
                    solution_en: "<p>5.(b) <strong>Apathy</strong>- lack of interest, enthusiasm or concern.<br><strong>Sympathy</strong>- a feeling of sincere concern for someone who is experiencing something difficult or painful.<br><strong>Empathy</strong>- the ability to understand and share the feelings of another.<br><strong>Antipathy</strong>- a strong feeling of dislike.</p>",
                    solution_hi: "<p>5.(b) <strong>Apathy </strong>(उदासीनता)- lack of interest, enthusiasm or concern.<br><strong>Sympathy </strong>(सहानुभूति)- a feeling of sincere concern for someone who is experiencing something difficult or painful.<br><strong>Empathy </strong>(समवेदना/हमदर्दी )- the ability to understand and share the feelings of another.<br><strong>Antipathy </strong>(घृणा/विद्वेष)- a strong feeling of dislike.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the most appropriate synonym of the given word.<br>Beseech</p>",
                    question_hi: "<p>6. Select the most appropriate synonym of the given word.<br>Beseech</p>",
                    options_en: ["<p>Pamper</p>", "<p>Implore</p>", 
                                "<p>Impart</p>", "<p>Bequeath</p>"],
                    options_hi: ["<p>Pamper</p>", "<p>Implore</p>",
                                "<p>Impart</p>", "<p>Bequeath</p>"],
                    solution_en: "<p>6.(b) <strong>Implore</strong>- to beg someone earnestly or desperately to do something.<br><strong>Beseech</strong>- to ask someone urgently and fervently.<br><strong>Pamper</strong>- to indulge with every attention, comfort, and kindness.<br><strong>Impart</strong>- to communicate or make information known.<br><strong>Bequeath</strong>- to leave something to someone through a will.</p>",
                    solution_hi: "<p>6.(b) <strong>Implore</strong> (प्रार्थना/याचना करना)- to beg someone earnestly or desperately to do something.<br><strong>Beseech </strong>(विनती करना)- to ask someone urgently and fervently.<br><strong>Pamper </strong>(दुलार)- to indulge with every attention, comfort, and kindness.<br><strong>Impart </strong>(बताना/सूचना देना)- to communicate or make information known.<br><strong>Bequeath </strong>(वसीयत करना)- to leave something to someone through a will.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the option that corrects the error in the given sentence.<br>She sings good.</p>",
                    question_hi: "<p>7. Select the option that corrects the error in the given sentence.<br>She sings good.</p>",
                    options_en: ["<p>better</p>", "<p>best</p>", 
                                "<p>goodly</p>", "<p>well</p>"],
                    options_hi: ["<p>better</p>", "<p>best</p>",
                                "<p>goodly</p>", "<p>well</p>"],
                    solution_en: "<p>7.(d) well<br>The given sentence needs an adverb(well) to modify the verb (sings) and not an adjective(good). Hence, &lsquo;Rahul sings well&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(d) well<br>दिए गए sentence में verb (sings) को संशोधित करने के लिए adverb(well) की आवश्यकता है, न कि adjective(good) की। अतः , &lsquo;Rahul sings well&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "8. Select the INCORRECTLY spelt word.",
                    question_hi: "8.  Select the INCORRECTLY spelt word.",
                    options_en: [" interrupt", " Occurrence", 
                                " Beggining", " Cemetery<br /> "],
                    options_hi: [" interrupt", " Occurrence",
                                " Beggining", " Cemetery"],
                    solution_en: "8.(c) Beggining<br />\'Beginning\' is the correct spelling. ",
                    solution_hi: "8.(c) Beggining<br />\'Beginning\' सही spelling है।  ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "9. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct sequence to form a meaningful sentence.<br />(P) society benefits from diversity<br />(Q) enriching cultures and<br />(O) by fostering understanding and empathy<br />(R) perspectives from around the world",
                    question_hi: "9.  Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct sequence to form a meaningful sentence.<br />(P) society benefits from diversity<br />(Q) enriching cultures and<br />(O) by fostering understanding and empathy<br />(R) perspectives from around the world",
                    options_en: [" QPRO", " POQR", 
                                " RPOQ", " ROQP<br />  "],
                    options_hi: [" QPRO", " POQR",
                                " RPOQ", " ROQP"],
                    solution_en: "9.(b) POQR<br />The given sentence starts with Part P as it introduces the main idea of the sentence, i.e. society benefits from diversity. Part P will be followed by Part O as it tells us how society benefits from diversity. Further, Part Q states that fostering understanding and empathy enriches cultures & Part R states that it also enriches perspectives from around the world. So, R will follow Q. Going through the options, option ‘b’ has the correct sequence.",
                    solution_hi: "9.(b) POQR<br />दिया गया sentence, Part P से प्रारंभ होगा क्योंकि यह वाक्य के मुख्य विचार ‘society benefits from diversity’ का परिचय देता है। Part P के बाद Part O आएगा क्योंकि यह हमें बताता है कि society को diversity से कैसे लाभ होता है। इसके अलावा, Part Q बताता है कि understanding और empathy को बढ़ावा देने से culture समृद्ध होती हैं और Part R बताता है कि इससे दुनिया भर के दृष्टिकोण(perspectives) भी समृद्ध होते हैं। इसलिए, Q के बाद R आएगा। अतः options के माध्यम से जाने पर option ‘b’ में सही sequence है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br />No sooner did Mr. Piyahri Mishra finish his chemistry lecture that the students began to leave.",
                    question_hi: "10. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br />No sooner did Mr. Piyahri Mishra finish his chemistry lecture that the students began to leave.",
                    options_en: [" so the students began to leave.", " then the students began to leave.", 
                                " and the students began to leave.", " than the students began to leave."],
                    options_hi: [" so the students began to leave.", " then the students began to leave.",
                                " and the students began to leave.", " than the students began to leave."],
                    solution_en: "10.(d) than the students began to leave.<br />‘No sooner …than’ is a fixed pair of conjunctions. Therefore, ‘that’ will be replaced with ‘than’. Hence, ‘than the students began to leave’ is the most appropriate answer.",
                    solution_hi: "10.(d) than the students began to leave.<br />‘No sooner …….. than’ एक  fixed conjunction pair है। इसलिए, ‘that’ के स्थान पर ‘than’ का प्रयोग होगा। अतः, ‘than the students began to leave’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the option that can be used as a one-word substitute for the given group of words.<br>The study of ancient things such as art, graves, ruins etc.</p>",
                    question_hi: "<p>11. Select the option that can be used as a one-word substitute for the given group of words.<br>The study of ancient things such as art, graves, ruins etc.</p>",
                    options_en: ["<p>Astrology</p>", "<p>Archaeology</p>", 
                                "<p>Geology</p>", "<p>Meteorology</p>"],
                    options_hi: ["<p>Astrology</p>", "<p>Archaeology</p>",
                                "<p>Geology</p>", "<p>Meteorology</p>"],
                    solution_en: "<p>11.(b) <strong>Archaeology</strong>- the study of ancient things such as art, graves, ruins etc.<br><strong>Astrology</strong>- the study of the movements and relative positions of celestial bodies interpreted as having an influence on human affairs and the natural world.<br><strong>Geology</strong>- the science that deals with the earth\'s physical structure and substance, its history, and the processes that act on it.<br><strong>Meteorology</strong>- the branch of science concerned with the processes and phenomena of the atmosphere, especially as a means of forecasting the weather.</p>",
                    solution_hi: "<p>11.(b) <strong>Archaeology </strong>(पुरातत्त्व विज्ञान) - the study of ancient things such as art, graves, ruins etc.<br><strong>Astrology </strong>(ज्योतिष शास्त्र) - the study of the movements and relative positions of celestial bodies interpreted as having an influence on human affairs and the natural world.<br><strong>Geology </strong>(भू-गर्भ शास्त्र) - the science that deals with the earth\'s physical structure and substance, its history, and the processes that act on it.<br><strong>Meteorology </strong>(मौसम विज्ञान) - the branch of science concerned with the processes and phenomena of the atmosphere, especially as a means of forecasting the weather.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the term which means the same as the given group of words.<br>A continuing unpleasant situation, created when one problem causes another problem that then makes the first problem worse</p>",
                    question_hi: "<p>12. Select the term which means the same as the given group of words.<br>A continuing unpleasant situation, created when one problem causes another problem that then makes the first problem worse</p>",
                    options_en: ["<p>Vicious cycle</p>", "<p>Round cycle</p>", 
                                "<p>Repetitive cycle</p>", "<p>Enclosed cycle</p>"],
                    options_hi: ["<p>Vicious cycle</p>", "<p>Round cycle</p>",
                                "<p>Repetitive cycle</p>", "<p>Enclosed cycle</p>"],
                    solution_en: "<p>12.(a) <strong>Vicious cycle</strong>- a continuing unpleasant situation, created when one problem causes another problem that then makes the first problem worse.</p>",
                    solution_hi: "<p>12.(a) <strong>Vicious cycle- </strong>a continuing unpleasant situation, created when one problem causes another problem that then makes the first problem worse./एक निरंतर अप्रिय स्थिति, जो तब उत्पन्न होती है जब एक समस्या दूसरी समस्या को जन्म देती है जो पहली समस्या को और बदतर बना देती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate synonym of the given word.<br>Miraculous</p>",
                    question_hi: "<p>13. Select the most appropriate synonym of the given word.<br>Miraculous</p>",
                    options_en: ["<p>Draining</p>", "<p>Average</p>", 
                                "<p>Expected</p>", "<p>Incredible</p>"],
                    options_hi: ["<p>Draining</p>", "<p>Average</p>",
                                "<p>Expected</p>", "<p>Incredible</p>"],
                    solution_en: "<p>13.(d) <strong>Incredible </strong>- difficult to believe; extraordinary.<br><strong>Miraculous</strong>- extraordinary and bringing about wonder or awe.<br><strong>Draining</strong>- causing someone to lose energy or strength.<br><strong>Average</strong>- not special or extraordinary.<br><strong>Expected</strong>- regarded as likely to happen.</p>",
                    solution_hi: "<p>13.(d) <strong>Incredible </strong>(अविश्वसनीय/अतुल्य)- difficult to believe; extraordinary.<br><strong>Miraculous </strong>(चमत्कारिक)- extraordinary and bringing about wonder or awe.<br><strong>Draining </strong>(अपवाहन/समाप्त होना)- causing someone to lose energy or strength.<br><strong>Average </strong>(औसत)- not special or extraordinary.<br><strong>Expected</strong> (अपेक्षित)- regarded as likely to happen.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the option that expresses the given sentence in passive voice.<br>The school will hire new teachers.</p>",
                    question_hi: "<p>14. Select the option that expresses the given sentence in passive voice.<br>The school will hire new teachers.</p>",
                    options_en: ["<p>New teachers will join the school.</p>", "<p>The school will be hiring new teachers.</p>", 
                                "<p>New teachers are hired by the school.</p>", "<p>New teachers will be hired by the school.</p>"],
                    options_hi: ["<p>New teachers will join the school.</p>", "<p>The school will be hiring new teachers.</p>",
                                "<p>New teachers are hired by the school.</p>", "<p>New teachers will be hired by the school.</p>"],
                    solution_en: "<p>14.(d) New teachers will be hired by the school. (Correct)<br>(a) New teachers <span style=\"text-decoration: underline;\">will join</span> the school. (Incorrect Verb)<br>(b) The school will be hiring new teachers. (Incorrect Sentence Structure) <br>(c) New teachers <span style=\"text-decoration: underline;\">are hired</span> by the school. (Incorrect Tense)</p>",
                    solution_hi: "<p>14.(d) New teachers will be hired by the school. (Correct)<br>(a) New teachers <span style=\"text-decoration: underline;\">will join</span> the school. (गलत Verb)<br>(b) The school will be hiring new teachers. (गलत Sentence Structure) <br>(c) New teachers <span style=\"text-decoration: underline;\">are hired</span> by the school. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate ANTONYM of the given word.<br>Absolve</p>",
                    question_hi: "<p>15. Select the most appropriate ANTONYM of the given word.<br>Absolve</p>",
                    options_en: ["<p>Commend</p>", "<p>Accuse</p>", 
                                "<p>Excuse</p>", "<p>Appeal</p>"],
                    options_hi: ["<p>Commend</p>", "<p>Accuse</p>",
                                "<p>Excuse</p>", "<p>Appeal</p>"],
                    solution_en: "<p>15.(b) <strong>Accuse</strong>- to charge someone with wrongdoing.<br><strong>Absolve</strong>- to free someone from guilt or responsibility.<br><strong>Commend</strong>- to praise formally or officially.<br><strong>Excuse</strong>- to forgive someone for a fault or offense.<br><strong>Appeal</strong>- to make a serious or urgent request.</p>",
                    solution_hi: "<p>15.(b) <strong>Accuse </strong>(दोषारोपण)- to charge someone with wrongdoing.<br><strong>Absolve </strong>(दोषमुक्त करना)- to free someone from guilt or responsibility.<br><strong>Commend</strong> (सराहना/प्रशंसा)- to praise formally or officially.<br><strong>Excuse </strong>(क्षमा करना)- to forgive someone for a fault or offense.<br><strong>Appeal </strong>(अपील/याचना)- to make a serious or urgent request.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who has supreme power or authority</p>",
                    question_hi: "<p>16. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who has supreme power or authority</p>",
                    options_en: ["<p>Secular</p>", "<p>Sovereign</p>", 
                                "<p>Diversity</p>", "<p>Foreign</p>"],
                    options_hi: ["<p>Secular</p>", "<p>Sovereign</p>",
                                "<p>Diversity</p>", "<p>Foreign</p>"],
                    solution_en: "<p>16.(b) <strong>Sovereign</strong>- a person who has supreme power or authority.<br><strong>Secular</strong>- not having any connection with religion.<br><strong>Diversity</strong>- the fact of there being many different things existing together in a group.<br><strong>Foreign</strong>- strange and unfamiliar.</p>",
                    solution_hi: "<p>16.(b) <strong>Sovereign </strong>(संप्रभु)- a person who has supreme power or authority.<br><strong>Secular</strong> (धर्मनिरपेक्ष)- not having any connection with religion.<br><strong>Diversity </strong>(विविधता)- the fact of there being many different things existing together in a group.<br><strong>Foreign </strong>(विदेशी/बाहरी)- strange and unfamiliar.</p>",
                    correct: "b ",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the most appropriate ANTONYM of the given word.<br>Appreciate</p>",
                    question_hi: "<p>17. Select the most appropriate ANTONYM of the given word.<br>Appreciate</p>",
                    options_en: ["<p>Depict</p>", "<p>Acknowledge</p>", 
                                "<p>Esteem</p>", "<p>Depreciate</p>"],
                    options_hi: ["<p>Depict</p>", "<p>Acknowledge</p>",
                                "<p>Esteem</p>", "<p>Depreciate</p>"],
                    solution_en: "<p>17.(d) <strong>Depreciate</strong>- to reduce in value over time.<br><strong>Appreciate</strong>- to increase in value.<br><strong>Depict</strong>- to represent or show something through art or description.<br><strong>Acknowledge</strong>- to accept or admit the existence or truth of something.<br><strong>Esteem</strong>- to regard with respect and admiration.</p>",
                    solution_hi: "<p>17.(d) <strong>Depreciate </strong>(मूल्यह्रास)- to reduce in value over time.<br><strong>Appreciate</strong> (भाव बढ़ जाना)- to increase in value.<br><strong>Depict </strong>(चित्रण/वर्णन करना)- to represent or show something through art or description.<br><strong>Acknowledge </strong>(स्वीकार करना)- to accept or admit the existence or truth of something.<br><strong>Esteem </strong>(सम्मान देना)- to regard with respect and admiration.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "18.  Parts of the following sentence have been given as options. Select the option that contains an error.<br />In some countries, you are not able to drink until you are 21.",
                    question_hi: "18. Parts of the following sentence have been given as options. Select the option that contains an error.<br />In some countries, you are not able to drink until you are 21.",
                    options_en: [" In some countries,", " able to drink", 
                                " until you are 21.", " you are not"],
                    options_hi: [" In some countries,", " able to drink",
                                " until you are 21.", " you are not"],
                    solution_en: "18.(b) able to drink<br />The correct word to use here is ‘allowed’ instead of ‘able’. The given sentence states that you are not allowed to drink until you are 21. Hence, ‘allowed to drink’ is the most appropriate answer.",
                    solution_hi: "18.(b) able to drink<br />यहाँ प्रयोग करने के लिए सही शब्द ‘able’ के बजाय ‘allowed’ है। दिए गए sentence में कहा गया है कि आपको 21 वर्ष की आयु तक शराब पीने की अनुमति नहीं है। अतः, ‘allowed to drink’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "19. Select the most appropriate option to fill in the blank.<br />The US ________ building was built in the year 1800.",
                    question_hi: "19. Select the most appropriate option to fill in the blank.<br />The US ________ building was built in the year 1800.",
                    options_en: [" capital", " cannibal", 
                                " captain", " capitol<br /> "],
                    options_hi: [" capital", " cannibal",
                                " captain", " capitol"],
                    solution_en: "19.(d) capitol<br />‘Capitol’ means the building in which the US Congress meets. The given sentence states that the US capitol building was built in the year 1800. Hence, \'capitol\' is the most appropriate answer. ",
                    solution_hi: "19.(d) capitol<br />‘Capitol’ का अर्थ है वह इमारत जिसमें अमेरिकी कांग्रेस की बैठक होती है। दिए गए sentence में कहा गया है कि US capitol building का निर्माण वर्ष 1800 में हुआ था। अतः, \'capitol\' सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the most appropriate synonym of the given word.<br>Magnificent</p>",
                    question_hi: "<p>20. Select the most appropriate synonym of the given word.<br>Magnificent</p>",
                    options_en: ["<p>Slow</p>", "<p>Sacred</p>", 
                                "<p>Scared</p>", "<p>Splendid</p>"],
                    options_hi: ["<p>Slow</p>", "<p>Sacred</p>",
                                "<p>Scared</p>", "<p>Splendid</p>"],
                    solution_en: "<p>20.(d) <strong>Splendid</strong>- very impressive or excellent.<br><strong>Magnificent</strong>- impressively beautiful or elaborate.<br><strong>Slow</strong>- moving or operating at a low speed.<br><strong>Sacred</strong>- regarded with great respect and reverence.<br><strong>Scared</strong>- feeling fear or anxiety.</p>",
                    solution_hi: "<p>20.(d) <strong>Splendid </strong>(उत्कृष्ट)- very impressive or excellent.<br><strong>Magnificent </strong>(भव्य/शानदार)- impressively beautiful or elaborate.<br><strong>Slow </strong>(मन्दगति)- moving or operating at a low speed.<br><strong>Sacred </strong>(सम्मानजनक)- regarded with great respect and reverence.<br><strong>Scared </strong>(डरा हुआ)- feeling fear or anxiety.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong><br>Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a (21)________ one. In augmented reality, a virtual environment coexists (22)________ the actual world, to be instructive and offer more data about the real world that a user can access without having to search. When a handset is pointed at a piece of malfunctioning (23)________, for example, industrial AR apps might provide rapid troubleshooting information. Virtual reality is a (24)________ environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. For example, VR may provide a user with a box containing a cartoon (25)________ of Mike Tyson in a virtual boxing ring.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21.<strong> Cloze Test:</strong><br>Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a (21)________ one. In augmented reality, a virtual environment coexists (22)________ the actual world, to be instructive and offer more data about the real world that a user can access without having to search. When a handset is pointed at a piece of malfunctioning (23)________, for example, industrial AR apps might provide rapid troubleshooting information. Virtual reality is a (24)________ environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. For example, VR may provide a user with a box containing a cartoon (25)________ of Mike Tyson in a virtual boxing ring.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>simulated</p>", "<p>catered</p>", 
                                "<p>revealed</p>", "<p>suffocated</p>"],
                    options_hi: ["<p>simulated</p>", "<p>catered</p>",
                                "<p>revealed</p>", "<p>suffocated</p>"],
                    solution_en: "<p>21.(a) simulated <br>&lsquo;Simulated&rsquo; means ​not real, but made to look or feel like the real thing. The given passage states that Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a simulated one. Hence, &lsquo;simulated&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(a) simulated <br>&lsquo;Simulated&rsquo; का अर्थ है वास्तविक नहीं, बल्कि वास्तविक चीज़ जैसा दिखने या महसूस करने के लिए बनाया गया। दिए गए passage में बताया गया है कि Augmented reality और virtual reality दो प्रकार की reality technologies हैं जो real-world environment का या तो विस्तार करती हैं या simulated environment में बदल देती हैं। अतः, &lsquo;simulated&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong><br>Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a (21)________ one. In augmented reality, a virtual environment coexists (22)________ the actual world, to be instructive and offer more data about the real world that a user can access without having to search. When a handset is pointed at a piece of malfunctioning (23)________, for example, industrial AR apps might provide rapid troubleshooting information. Virtual reality is a (24)________ environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. For example, VR may provide a user with a box containing a cartoon (25)________ of Mike Tyson in a virtual boxing ring.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong><br>Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a (21)________ one. In augmented reality, a virtual environment coexists (22)________ the actual world, to be instructive and offer more data about the real world that a user can access without having to search. When a handset is pointed at a piece of malfunctioning (23)________, for example, industrial AR apps might provide rapid troubleshooting information. Virtual reality is a (24)________ environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. For example, VR may provide a user with a box containing a cartoon (25)________ of Mike Tyson in a virtual boxing ring.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>with</p>", "<p>behind</p>", 
                                "<p>along</p>", "<p>beyond</p>"],
                    options_hi: ["<p>with</p>", "<p>behind</p>",
                                "<p>along</p>", "<p>beyond</p>"],
                    solution_en: "<p>22.(a) with <br>&lsquo;With&rsquo; is a fixed preposition used with the verb &lsquo;coexist&rsquo;. Hence, \'with\' is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) with <br>&lsquo;With&rsquo; एक fixed preposition है जिसका प्रयोग verb &lsquo;coexist&rsquo; के साथ किया जाता है। अतः, \'with\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong><br>Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a (21)________ one. In augmented reality, a virtual environment coexists (22)________ the actual world, to be instructive and offer more data about the real world that a user can access without having to search. When a handset is pointed at a piece of malfunctioning (23)________, for example, industrial AR apps might provide rapid troubleshooting information. Virtual reality is a (24)________ environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. For example, VR may provide a user with a box containing a cartoon (25)________ of Mike Tyson in a virtual boxing ring.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze Test:</strong><br>Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a (21)________ one. In augmented reality, a virtual environment coexists (22)________ the actual world, to be instructive and offer more data about the real world that a user can access without having to search. When a handset is pointed at a piece of malfunctioning (23)________, for example, industrial AR apps might provide rapid troubleshooting information. Virtual reality is a (24)________ environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. For example, VR may provide a user with a box containing a cartoon (25)________ of Mike Tyson in a virtual boxing ring.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>equipment</p>", "<p>arsenal</p>", 
                                "<p>outfit</p>", "<p>baggage</p>"],
                    options_hi: ["<p>equipment</p>", "<p>arsenal</p>",
                                "<p>outfit</p>", "<p>baggage</p>"],
                    solution_en: "<p>23.(a) equipment<br>&lsquo;Equipment&rsquo; means the necessary items for a particular purpose. The given passage states that when a handset is pointed at a piece of malfunctioning equipment, for example, industrial AR apps might provide rapid troubleshooting information. Hence, \'equipment\' is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(a) equipment <br>&lsquo;Equipment&rsquo; का अर्थ है किसी विशेष उद्देश्य के लिए आवश्यक वस्तुएँ। दिए गए passage में कहा गया है कि उदाहरण के लिए, जब handset को एक खराब equipment की ओर point किया जाता है, औद्योगिक AR apps तेजी से समस्या निवारण (troubleshooting) जानकारी प्रदान कर सकते हैं। अतः, \'equipment\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong><br>Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a (21)________ one. In augmented reality, a virtual environment coexists (22)________ the actual world, to be instructive and offer more data about the real world that a user can access without having to search. When a handset is pointed at a piece of malfunctioning (23)________, for example, industrial AR apps might provide rapid troubleshooting information. Virtual reality is a (24)________ environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. For example, VR may provide a user with a box containing a cartoon (25)________ of Mike Tyson in a virtual boxing ring.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong><br>Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a (21)________ one. In augmented reality, a virtual environment coexists (22)________ the actual world, to be instructive and offer more data about the real world that a user can access without having to search. When a handset is pointed at a piece of malfunctioning (23)________, for example, industrial AR apps might provide rapid troubleshooting information. Virtual reality is a (24)________ environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. For example, VR may provide a user with a box containing a cartoon (25)________ of Mike Tyson in a virtual boxing ring.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>comprehensive</p>", "<p>conclusive</p>", 
                                "<p>transgressive</p>", "<p>reprehensive</p>"],
                    options_hi: ["<p>comprehensive</p>", "<p>conclusive</p>",
                                "<p>transgressive</p>", "<p>reprehensive</p>"],
                    solution_en: "<p>24.(a) comprehensive <br>&lsquo;Comprehensive&rsquo; means including all or nearly all elements or aspects of something. The given passage states that virtual reality is a comprehensive environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. Hence, \'comprehensive\' is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(a) comprehensive <br>&lsquo;Comprehensive&rsquo; का अर्थ है किसी चीज़ के सभी या लगभग सभी तत्वों या पहलुओं को शामिल करना। दिए गए passage में कहा गया है कि virtual reality एक व्यापक पर्यावरणीय अनुकरण (environmental simulation) है जो user के surrounding को पूरी तरह से virtual world में बदल देता है। अतः, \'comprehensive\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong><br>Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a (21)________ one. In augmented reality, a virtual environment coexists (22)________ the actual world, to be instructive and offer more data about the real world that a user can access without having to search. When a handset is pointed at a piece of malfunctioning (23)________, for example, industrial AR apps might provide rapid troubleshooting information. Virtual reality is a (24)________ environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. For example, VR may provide a user with a box containing a cartoon (25)________ of Mike Tyson in a virtual boxing ring.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25.<strong> Cloze Test:</strong><br>Augmented reality and virtual reality are two types of reality technologies that either augment or replace a real-world environment with a (21)________ one. In augmented reality, a virtual environment coexists (22)________ the actual world, to be instructive and offer more data about the real world that a user can access without having to search. When a handset is pointed at a piece of malfunctioning (23)________, for example, industrial AR apps might provide rapid troubleshooting information. Virtual reality is a (24)________ environmental simulation that completely replaces the user\'s surroundings with a totally virtual world. For example, VR may provide a user with a box containing a cartoon (25)________ of Mike Tyson in a virtual boxing ring.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>glossary</p>", "<p>network</p>", 
                                "<p>rendition</p>", "<p>resistance</p>"],
                    options_hi: ["<p>glossary</p>", "<p>network</p>",
                                "<p>rendition</p>", "<p>resistance</p>"],
                    solution_en: "<p>25.(c) rendition<br>&lsquo;Rendition&rsquo; means a particular way of performing a song, piece of music, or poem. The given passage states that VR may provide a user with a box containing a cartoon rendition of Mike Tyson in a virtual boxing ring. Hence, \'rendition\' is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(c) rendition<br>&lsquo;Rendition&rsquo; का अर्थ है किसी गीत, संगीत या कविता को प्रस्तुत करने का एक विशेष तरीका। दिए गए passage में कहा गया है कि VR users को एक box उपलब्ध करा सकता है जिसमें virtual boxing ring में Mike Tyson का cartoon चित्रण होगा। अतः, \'rendition\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>