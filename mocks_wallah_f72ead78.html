<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which type of soil covers most of the Deccan Plateau ?</p>",
                    question_hi: "<p>1. दक्कन के पठार के अधिकांश भाग में किस प्रकार की मिट्टी पाई जाती है ?</p>",
                    options_en: ["<p>Alluvial soil</p>", "<p>Yellow soil</p>", 
                                "<p>Black soil</p>", "<p>Red soil</p>"],
                    options_hi: ["<p>जलोढ़ मृदा</p>", "<p>पीली मृदा</p>",
                                "<p>काली मृदा</p>", "<p>लाल मृदा</p>"],
                    solution_en: "<p>1.(c) Black soil or Regur soil covers most of the Deccan Plateau. Black soil is much required for growing cotton. Black soils are derivatives of trap lava and are spread mostly across interior Gujarat, Maharashtra, Karnataka, and Madhya Pradesh on the Deccan lava plateau and the Malwa Plateau.</p>",
                    solution_hi: "<p>1.(c) काली मिट्टी या रेगुर मिट्टी दक्कन के अधिकांश पठार को कवर करती है। कपास उगाने के लिए काली मिट्टी की बहुत आवश्यकता होती है। काली मिट्टी ट्रैप लावा के व्युत्पन्न हैं और ज्यादातर आंतरिक गुजरात, महाराष्ट्र, कर्नाटक और मध्य प्रदेश में दक्कन लावा पठार और मालवा पठार पर फैली हुई हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Where was the three Days World Sustainable Development Summit, 2019 organized ?</p>",
                    question_hi: "<p>2. तीन दिवसीय विश्व सतत विकास सम्मेलन 2019 का आयोजन कहाँ हुआ ?</p>",
                    options_en: ["<p>Hanoi, Vietnam</p>", "<p>New Delhi, India</p>", 
                                "<p>Kathmandu, Nepal</p>", "<p>Port Moresby, Papua New Guinea</p>"],
                    options_hi: ["<p>हनोई, वियतनाम</p>", "<p>नई दिल्ली, भारत</p>",
                                "<p>काठमांडू, नेपाल</p>", "<p>पोर्ट मोरेस्बी, पापुआ न्यू गिनी</p>"],
                    solution_en: "<p>2.(b) New Delhi hosted the Three Days World Sustainable Development Summit, 2019. Prime Minister Shri Narendra Modi inaugurated World Sustainable Development Summit 2021 via video conferencing. The theme of the Summit was \'Redefining our common future: Safe and secure environment for all\'.</p>",
                    solution_hi: "<p>2.(b) नई दिल्ली ने तीन दिवसीय विश्व सतत विकास शिखर सम्मेलन, 2019 की मेजबानी की। प्रधान मंत्री श्री नरेंद्र मोदी ने वीडियो कॉन्फ्रेंसिंग के माध्यम से विश्व सतत विकास शिखर सम्मेलन 2021 का उद्घाटन किया। शिखर सम्मेलन का विषय \'हमारे सामान्य भविष्य को फिर से परिभाषित करना: सभी के लिए सुरक्षित और सुरक्षित वातावरण\' था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "3. What was the rank of India in the list of world‘s most competitive economies in the IMD World Competitiveness Rankings 2019?",
                    question_hi: "3. IMD विश्व प्रतिस्पर्धात्मकता रैंकिंग 2019 में दुनिया की सबसे अधिक प्रतिस्पर्धी अर्थव्यवस्थाओं की सूची में भारत का स्थान क्या था?",
                    options_en: [" 49th", " 51st", 
                                " 43rd", " 50th"],
                    options_hi: [" 49th", " 51st",
                                " 43rd", " 50th"],
                    solution_en: "3.(c) According to the Institute for Management Development annual World Competitiveness Index 2019, India ranked 43rd in the list of world‘s most competitive economies. India maintained its rank at 43rd position in the IMD World Competitiveness Rankings 2021 as well.",
                    solution_hi: "3.(c) Institute for Management Development annual World Competitiveness Index 2019 के अनुसार, भारत दुनिया की सबसे अधिक प्रतिस्पर्धी अर्थव्यवस्थाओं की सूची में 43 वें स्थान पर है।<br />भारत ने IMD World Competitiveness Rankings 2021 में भी 43वें स्थान पर अपनी रैंक बनाए रखी।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The Rate constant of a reaction doubles with a_______ rise in temperature.</p>",
                    question_hi: "<p>4. तापमान में______वृद्धि के साथ अभिक्रिया का दर स्थिरांक दोगुना हो जाता है।</p>",
                    options_en: ["<p>40 degrees</p>", "<p>10 degrees</p>", 
                                "<p>30 degrees</p>", "<p>20 degrees</p>"],
                    options_hi: ["<p>40 डिग्री</p>", "<p>10 डिग्री</p>",
                                "<p>30 डिग्री</p>", "<p>20 डिग्री</p>"],
                    solution_en: "<p>4.(b) The rate constant of a reaction doubles with a 10 degree rise in temperature. This is due to an increase in the number of particles that have the minimum energy required.</p>",
                    solution_hi: "<p>4.(b) तापमान में 10 डिग्री की वृद्धि के साथ प्रतिक्रिया की दर स्थिरांक दोगुनी हो जाती है। यह न्यूनतम आवश्यक ऊर्जा वाले कणों की संख्या में वृद्धि के कारण है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which kingdom was called \"a cherry\" by Governor General Lord Dalhousie ?</p>",
                    question_hi: "<p>5. गवर्नर जनरल लॉर्ड डलहौजी ने किस राज्य को \"एक चेरी\" कहा था ?</p>",
                    options_en: ["<p>Kingdom of Jhansi</p>", "<p>Kingdom of Gwalior</p>", 
                                "<p>Kingdom of Awadh</p>", "<p>Kingdom of Vijayanagar</p>"],
                    options_hi: ["<p>झांसी का साम्राज्य</p>", "<p>ग्वालियर का साम्राज्य</p>",
                                "<p>अवध का साम्राज्य</p>", "<p>विजयनगर साम्राज्य</p>"],
                    solution_en: "<p>5.(c) Kingdom of Awadh was called \"a cherry\" by Governor General Lord Dalhousie in 1856 because the soil of Awadh was good for producing indigo and cotton.</p>",
                    solution_hi: "<p>5.(c) 1856 में गवर्नर जनरल लॉर्ड डलहौजी द्वारा अवध साम्राज्य को \"एक चेरी\" कहा जाता था क्योंकि अवध की मिट्टी नील और कपास के उत्पादन के लिए अच्छी थी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. As per the report of the Ministry of Statistics and Programme Implementation released in Jan, 2020, for FY 2018-19, which of the following states of India has the highest per capita income among all Indian states and union territories ?</p>",
                    question_hi: "<p>6. वित्त वर्ष 2018-19 के लिए जनवरी, 2020 में जारी सांख्यिकी और कार्यक्रम कार्यान्वयन मंत्रालय की रिपोर्ट के अनुसार भारत के निम्नलिखित में से किस राज्य की प्रति व्यक्ति आय सभी भारतीय राज्यों और केंद्र शासित प्रदेशों में सबसे अधिक है ?</p>",
                    options_en: ["<p>Haryana</p>", "<p>Kerala</p>", 
                                "<p>Punjab</p>", "<p>Goa</p>"],
                    options_hi: ["<p>हरियाणा</p>", "<p>केरल</p>",
                                "<p>पंजाब</p>", "<p>गोआ</p>"],
                    solution_en: "<p>6.(d) As per the report of the Ministry of Statistics and Programme Implementation released in Jan, 2020, for FY 2018-19, Goa has the highest per capita income among all Indian states and union territories.This is followed by Sikkim and Delhi.</p>",
                    solution_hi: "<p>6.(d) वित्त वर्ष 2018-19 के लिए जनवरी, 2020 में जारी सांख्यिकी और कार्यक्रम कार्यान्वयन मंत्रालय की रिपोर्ट के अनुसार, गोवा की प्रति व्यक्ति आय सभी भारतीय राज्यों और केंद्र शासित प्रदेशों में सबसे अधिक है। इसके बाद सिक्किम और दिल्ली का स्थान है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following is needed to access the Internet ?</p>",
                    question_hi: "<p>7. इंटरनेट उपयोग करने के लिए निम्नलिखित में से किसकी आवश्यकता होती है ?</p>",
                    options_en: ["<p>Unique IP Address</p>", "<p>EmaiL</p>", 
                                "<p>TV</p>", "<p>Hacker</p>"],
                    options_hi: ["<p>यूनिक IP एड्रेस</p>", "<p>ईमेल</p>",
                                "<p>टीवी</p>", "<p>हैकर</p>"],
                    solution_en: "<p>7.(a) Unique IP Address is needed to access the Internet. It is a unique address on the internet dedicated completely to one hosting account.</p>",
                    solution_hi: "<p>7.(a) इंटरनेट एक्सेस करने के लिए यूनिक आईपी एड्रेस की जरूरत होती है। यह इंटरनेट पर एक अनूठा पता है जो पूरी तरह से एक होस्टिंग खाते को समर्पित है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. Which is the state that leads in the list of monuments of national importance in South India declared by the Archaeological Survey of India (ASI)?",
                    question_hi: "8. भारतीय पुरातत्व सर्वेक्षण (ASI) द्वारा घोषित दक्षिण भारत में राष्ट्रीय महत्व के स्मारकों की सूची में कौन सा राज्य सबसे आगे है?",
                    options_en: [" Kerala", " Karnataka", 
                                " Andhra Pradesh", " Tamil Nadu"],
                    options_hi: [" केरल", " कर्नाटक",
                                " आंध्र प्रदेश", " तमिलनाडु"],
                    solution_en: "8.(b) With 506 monuments, Karnataka leads in the list of monuments of national importance in South India declared by the Archaeological Survey of India (ASI). ASI was founded in 1861. <br />Founder- Alexander Cunningham.",
                    solution_hi: "8.(b) 506 स्मारकों के साथ कर्नाटक भारतीय पुरातत्व सर्वेक्षण (ASI) द्वारा घोषित दक्षिण भारत में राष्ट्रीय महत्व के स्मारकों की सूची में सबसे आगे है। एएसआई की स्थापना 1861 में हुई थी। <br />संस्थापक- अलेक्जेंडर कनिंघम।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Who led the railway strike in 1974 ?</p>",
                    question_hi: "<p>9. 1974 में रेलवे हड़ताल का नेतृत्व किसने किया ?</p>",
                    options_en: ["<p>J P Narayan</p>", "<p>S P Mukherjee</p>", 
                                "<p>George Fernandes</p>", "<p>Morarji Desai</p>"],
                    options_hi: ["<p>जे पी नारायण</p>", "<p>एस पी मुखर्जी</p>",
                                "<p>जॉर्ज फर्नांडीस</p>", "<p>मोरारजी देसाई</p>"],
                    solution_en: "<p>9.(c) George Fernandes led the railway strike in 1974. George Fernandes is the awardee of Padma Vibhushan in 2020.</p>",
                    solution_hi: "<p>9.(c) जॉर्ज फर्नांडीस ने 1974 में रेलवे हड़ताल का नेतृत्व किया। जॉर्ज फर्नांडीस 2020 में पद्म विभूषण से सम्मानित हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. Who among the following freedom ﬁghters of India is known as ‘‘Frontier Gandhi ?",
                    question_hi: "10. निम्नलिखित में से भारत के स्वतंत्रता सेनानियों में से किसे \'सीमान्त गांधी\' के नाम से जाना जाता है?",
                    options_en: [" Maulana Abul Kalam Azad", " Khan Abdul Ghaffar Khan", 
                                " Badruddin Tyabji", " Mukhtar Ahmed Ansari"],
                    options_hi: [" मौलाना अबुल कलाम आज़ाद", " खान अब्दुल गफ्फार खान",
                                " बदरुद्दीन तैयबजी", " मुख्तार अहमद अंसारी"],
                    solution_en: "10.(b) Khan Abdul Ghaffar Khan is known as “Frontier Gandhi”, also known as Badshah Khan or Bacha Khan. He founded the Khudai Khidmatgar (“Servants of God”), commonly known as the “Red Shirts”, during the 1920s.",
                    solution_hi: "10.(b) खान अब्दुल गफ्फार खान को \"फ्रंटियर गांधी\" के रूप में जाना जाता है, जिन्हें बादशाह खान या बच्चा खान के नाम से भी जाना जाता है। उन्होंने 1920 के दशक के दौरान खुदाई खिदमतगार (\"भगवान के सेवक\") की स्थापना की, जिसे आमतौर पर \"लाल शर्ट\" के रूप में जाना जाता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. The United Nations General Assembly commemorates 21st June as",
                    question_hi: "11. संयुक्त राष्ट्र महासभा 21 जून को किस रूप में मनाती है ?",
                    options_en: [" International Education Day", " International Babies Day", 
                                " International Day of Yoga", " International Health Day"],
                    options_hi: [" अंतर्राष्ट्रीय शिक्षा दिवस", " अंतर्राष्ट्रीय शिशु दिवस",
                                " अंतर्राष्ट्रीय योग दिवस", " अंतर्राष्ट्रीय स्वास्थ्य दिवस"],
                    solution_en: "11.(c) The United Nations General Assembly commemorates 21st June as the International Day of Yoga. ‘Yoga for wellness’ is the theme for International Yoga Day 2021.",
                    solution_hi: "11.(c) संयुक्त राष्ट्र महासभा 21 जून को अंतर्राष्ट्रीय योग दिवस के रूप में मनाती है। ‘योग फॉर वेलनेस’ अंतर्राष्ट्रीय योग दिवस 2021 का विषय है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which of the following is an antivirus software ?</p>",
                    question_hi: "<p>12. निम्न में से कौन सा एंटीवायरस सॉफ़्टवेयर है ?</p>",
                    options_en: ["<p>Google</p>", "<p>XML</p>", 
                                "<p>Norton</p>", "<p>Microsoft</p>"],
                    options_hi: ["<p>गूगल</p>", "<p>एक्सएमएल</p>",
                                "<p>नॉर्टन</p>", "<p>माइक्रोसॉफ्ट</p>"],
                    solution_en: "<p>12.(c) Norton is an antivirus software. Kaspersky, Avira, Avast, are examples of antivirus software.</p>",
                    solution_hi: "<p>12.(c) नॉर्टन एक एंटीवायरस सॉफ्टवेयर है। Kaspersky, Avira, Avast, एंटीवायरस सॉफ़्टवेयर के उदाहरण हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. Which among the following hills has Indian desert to its western margin?",
                    question_hi: "13. निम्नलिखित पहाड़ियों में से किसकी पश्चिमी सीमा पर भारतीय रेगिस्तान है?",
                    options_en: [" Palani", " Kanchenjunga", 
                                " Aravali", " Dhupgarh"],
                    options_hi: [" पलानी", " कंचनजंगा",
                                " अरावली", " धूपगढ़"],
                    solution_en: "13.(c) Aravali hills have an Indian desert to its western margin. The Palani hills are a mountain range in the southern Indian states of Kerala and Tamil Nadu. Kanchenjunga is a mountain of the Himalayas range, located in eastern part of Nepal on the border of Sikkim and about 20 kilometers south of Tibet. Dhupgarh is the highest peak of Madhya Pradesh.",
                    solution_hi: "13.(c) अरावली पहाड़ियों के पश्चिमी किनारे पर एक भारतीय रेगिस्तान है। पलानी हिल्स दक्षिणी भारतीय राज्यों केरल और तमिलनाडु में एक पर्वत श्रृंखला है। कंचनजंगा हिमालय श्रेणी का एक पर्वत है, जो नेपाल के पूर्वी भाग में सिक्किम की सीमा पर और तिब्बत से लगभग 20 किलोमीटर दक्षिण में स्थित है। मध्य प्रदेश की सबसे ऊँची चोटी धूपगढ़ है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. In which river is India&lsquo;s largest river island, Majuli, located ?</p>",
                    question_hi: "<p>14. भारत का सबसे बड़ा नदी द्वीप माजुली किस नदी में स्थित है ?</p>",
                    options_en: ["<p>Narmada</p>", "<p>Beas</p>", 
                                "<p>Indus</p>", "<p>Brahmaputra</p>"],
                    options_hi: ["<p>नर्मदा</p>", "<p>व्यास</p>",
                                "<p>सिंधु</p>", "<p>ब्रह्मपुत्र</p>"],
                    solution_en: "<p>14.(d) Majuli, India&rsquo;s largest river island is located on the river Brahmaputra. Majuli has been the cultural capital of the Assamese civilization. It is a picturesque, lush green and pollution-free river island in the mighty river Brahmaputra.</p>",
                    solution_hi: "<p>14.(d) माजुली भारत का सबसे बड़ा नदी द्वीप ब्रह्मपुत्र नदी पर स्थित है। माजुली असमिया सभ्यता की सांस्कृतिक राजधानी रही है। यह शक्तिशाली ब्रह्मपुत्र नदी में एक सुरम्य, हरे-भरे और प्रदूषण मुक्त नदी द्वीप है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Who among the following did NOT attend the first Round Table Conference in London ?</p>",
                    question_hi: "<p>15. निम्नलिखित में से कौन लंदन में पहले गोलमेज सम्मेलन में शामिल नहीं हुआ था ?</p>",
                    options_en: ["<p>Muslim League</p>", "<p>Hindu Mahasabha</p>", 
                                "<p>Indian Princes</p>", "<p>Indian National Congress</p>"],
                    options_hi: ["<p>मुस्लिम लीग</p>", "<p>हिंदू महासभा</p>",
                                "<p>भारतीय रियासतें</p>", "<p>भारतीय राष्ट्रीय कांग्रेस</p>"],
                    solution_en: "<p>15.(d) Indian National Congress did not attend the first Round Table Conference. First round table conference was held in 1930 in London. The three Round Table Conferences of 1930&ndash;1932 were a series of peace conferences organized by the British Government and Indian political personalities to discuss constitutional reforms in India.</p>",
                    solution_hi: "<p>15.(d) भारतीय राष्ट्रीय कांग्रेस पहले गोलमेज सम्मेलन में शामिल नहीं हुई थी। पहला गोलमेज सम्मेलन 1930 में लंदन में आयोजित किया गया था। 1930-1932 के तीन गोलमेज सम्मेलन भारत में संवैधानिक सुधारों पर चर्चा करने के लिए ब्रिटिश सरकार और भारतीय राजनीतिक हस्तियों द्वारा आयोजित शांति सम्मेलनों की एक श्रृंखला थी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. In which country is the headquarters of Asian Development Bank (ADB) situated ?</p>",
                    question_hi: "<p>16. एशियाई विकास बैंक (ADB) का मुख्यालय किस देश में स्थित है ?</p>",
                    options_en: [" Japan", " Philippines", 
                                " South Korea", " China"],
                    options_hi: [" जापान", " फिलीपिंस ",
                                " दक्षिण कोरिया", " चीन"],
                    solution_en: "16.(b) The headquarters of Asian Development Bank (ADB) are situated at Manila, Philippines. It was established on 19 december 1966. <br />Membership- 68 countries. <br />President (2021)- Masatsugu Asakawa.",
                    solution_hi: "16.(b) मनीला, फिलीपींस में स्थित एशियाई विकास बैंक (ADB) का मुख्यालय, 19 दिसंबर 1966 को स्थापित किया गया। <br />सदस्यता- 68 देश। <br />राष्ट्रपति (2021)- मासत्सुगु असाकावा।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. What helps in food digestion ?</p>",
                    question_hi: "<p>17. भोजन के पाचन में क्या मदद करता है ?</p>",
                    options_en: [" Oxygen", " Carbon", 
                                " Helium", " Saliva"],
                    options_hi: [" ऑक्सीजन", " कार्बन",
                                " हीलियम", " लार"],
                    solution_en: "17.(d) Saliva helps in food digestion. Saliva contains special enzymes amylase which breaks down starch into sugars. It also contains an enzyme called lingual lipase, which breaks down fats. Saliva is composed of a variety of electrolytes, including sodium, potassium, calcium, magnesium, bicarbonate, and phosphates.",
                    solution_hi: "17.(d) लार भोजन को पचाने में मदद करती है। लार में विशेष एंजाइम एमाइलेज होता है जो स्टार्च को शर्करा में तोड़ देता है। लार में लिंगुअल लाइपेस नामक एक एंजाइम भी होता है, जो वसा को तोड़ता है। लार सोडियम, पोटेशियम, कैल्शियम, मैग्नीशियम, बाइकार्बोनेट और फॉस्फेट सहित विभिन्न प्रकार के इलेक्ट्रोलाइट्स से बना है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Which of the following is the external oversight body of the United Nations system ?</p>",
                    question_hi: "<p>18. निम्नलिखित में से कौन संयुक्त राष्ट्र प्रणाली का बाहरी निरीक्षण निकाय है ?</p>",
                    options_en: ["<p>Joint Inspection Unit</p>", "<p>Joint Environment Unit</p>", 
                                "<p>Joint Unit</p>", "<p>Joint Review Unit</p>"],
                    options_hi: ["<p>संयुक्त निरीक्षण इकाई</p>", "<p>संयुक्त पर्यावरण इकाई</p>",
                                "<p>संयुक्त इकाई</p>", "<p>संयुक्त समीक्षा इकाई</p>"],
                    solution_en: "<p>18.(a) Joint Inspection Unit is the external oversight body of the United Nation System. It is mandated to conduct evaluations, inspections and investigations system-wide. It is based in Geneva, Switzerland.</p>",
                    solution_hi: "<p>18.(a) संयुक्त निरीक्षण इकाई संयुक्त राष्ट्र प्रणाली का बाहरी निरीक्षण निकाय है। यह प्रणाली-व्यापी मूल्यांकन, निरीक्षण और जांच करने के लिए अनिवार्य है। यह जिनेवा, स्विट्जरलैंड में स्थित है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. When was the Goods and Services Tax (GST) implemented in India ?</p>",
                    question_hi: "<p>19. भारत में वस्तु एवं सेवा कर (GST) कब लागू किया गया था ?</p>",
                    options_en: ["<p>1st April 2018</p>", "<p>1st July 2017</p>", 
                                "<p>1st July 2018</p>", "<p>1st April 2017</p>"],
                    options_hi: ["<p>1 अप्रैल 2018</p>", "<p>1 जुलाई 2017</p>",
                                "<p>1 जुलाई 2018</p>", "<p>1 अप्रैल 2017</p>"],
                    solution_en: "<p>19.(b) The Goods and Services Tax (GST) was implemented in India on 1st July 2017. It was first implemented in France. GST in India was made on the recommendation of Vijay Kelkar Committee. India\'s GST is based on the Canadian model. The first state which implemented the GST was Assam. GST has been implemented by the 101st Constitution Amendment act 2016.</p>",
                    solution_hi: "<p>19.(b) 1 जुलाई 2017 को भारत में माल और सेवा कर (GST) लागू किया गया। यह पहली बार फ्रांस में लागू किया गया था भारत में GSTविजय केलकर समिति की सिफारिश पर बनाया गया था। भारत का GST कनाडाई मॉडल पर आधारित है। जीएसटी लागू करने वाला पहला राज्य असम था। जीएसटी को 101वें संविधान संशोधन अधिनियम 2016 द्वारा लागू किया गया है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. What is the name of the satellite launched by ISRO which gives alert messages to fishermen regarding bad weather and high waves and cautious them when they approach international maritime boundary line ?</p>",
                    question_hi: "<p>20. ISRO द्वारा लॉन्च किए गए उपग्रह का नाम क्या है जो मछुआरों को खराब मौसम और ऊंची लहरों के बारे में सतर्क संदेश देता है और अंतरराष्ट्रीय समुद्री सीमा रेखा पर पहुंचने पर उन्हें सतर्क करता है ?</p>",
                    options_en: [" IRNSS-I", " IRNSS-II", 
                                " IRNSS", " IRNSS-10"],
                    options_hi: [" IRNSS-I", " IRNSS-II",
                                " IRNSS", " IRNSS-10"],
                    solution_en: "20.(b) PSLV C39 / IRNSS-1H launched by ISRO, gives alert messages to fishermen regarding bad weather and high waves and cautious them when they approach international maritime boundary line.",
                    solution_hi: "20.(b) ISRO द्वारा लॉन्च किया गया PSLV C39 / IRNSS-1H मछुआरों को खराब मौसम और ऊंची लहरों के बारे में सतर्क संदेश देता है और जब वे अंतरराष्ट्रीय समुद्री सीमा रेखा पर पहुंचते हैं तो उन्हें सतर्क करते हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. In which city was the 11th BRICS summit held in 2019 ?",
                    question_hi: "21. 2019 में 11वां BRICS शिखर सम्मेलन किस शहर में आयोजित किया गया था ?",
                    options_en: [" Tokyo", " New York", 
                                " Brasilia", " London "],
                    options_hi: [" टोक्यो", " न्यूयॉर्क",
                                " ब्रासीलिया", " लन्दन "],
                    solution_en: "21.(c) BRICS (Brazil, Russia, India, China, and South Africa). <br />The 11th BRICS summit was held in 2019 in Brasilia. The 12th BRICS summit was held in 2020 in Russia. The 13th BRICS summit will be held in 2021 in New Delhi. It was founded in June 2006.",
                    solution_hi: "21.(c) BRICS (ब्राजील, रूस, भारत, चीन और दक्षिण अफ्रीका)। <br />11 वां ब्रिक्स शिखर सम्मेलन 2019 में ब्रासीलिया में आयोजित किया गया। 12 वां ब्रिक्स शिखर सम्मेलन 2020 में रूस में आयोजित किया गया। 13वां ब्रिक्स शिखर सम्मेलन 2021 में नई दिल्ली में होगा। इसकी स्थापना जून 2006 में हुई थी।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. How many atoms of oxygen are present in one molecule of Ozone ?",
                    question_hi: "22. ओजोन के एक अणु में ऑक्सीजन के कितने परमाणु होते हैं ?",
                    options_en: [" 3", " 1", 
                                " 2", " 4"],
                    options_hi: [" 3", " 1",
                                " 2", " 4"],
                    solution_en: "22.(a) Three atoms of oxygen are present in one molecule of ozone, denoted chemically as <math display=\"inline\"><msub><mrow><mi>O</mi></mrow><mrow><mn>3</mn></mrow></msub></math>. It is a pale blue gas with a distinctively pungent smell. It is an allotrope of oxygen.",
                    solution_hi: "22.(a) ओजोन के एक अणु में ऑक्सीजन के तीन परमाणु मौजूद होते हैं जिन्हें रासायनिक रूप से <math display=\"inline\"><msub><mrow><mi>O</mi></mrow><mrow><mn>3</mn></mrow></msub></math> कहा जाता है। यह एक विशिष्ट तीखी गंध वाली हल्की नीली गैस है। यह ऑक्सीजन का अपरूप है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "23. The Coromandel coast is home to the following two states",
                    question_hi: "23. कोरोमंडल तट निम्नलिखित दो राज्यों का घर है ?",
                    options_en: [" Maharashtra and Gujarat", " Punjab and NCR", 
                                " West Bengal and Jharkhand", " Tamil Nadu and Andhra Pradesh"],
                    options_hi: [" महाराष्ट्र और गुजरात", " पंजाब और NCR ",
                                " पश्चिम बंगाल और झारखंड", " तमिलनाडु और आंध्र प्रदेश"],
                    solution_en: "23.(d) The Coromandel coast is home to Tamil Nadu and Andhra Pradesh. It also has been known since ancient times as the “land of temples” for the many temples that are located along the coast. Nine coastal states of India are – Gujarat, Maharashtra, Goa, Karnataka, Kerala, Tamil Nadu, Andhra Pradesh, Odisha, West Bengal.",
                    solution_hi: "23.(d) कोरोमंडल तट तमिलनाडु और आंध्र प्रदेश का घर है। यह प्राचीन काल से तट के किनारे स्थित कई मंदिरों के लिए \"मंदिरों की भूमि\" के रूप में भी जाना जाता है। भारत के नौ तटीय राज्य हैं- गुजरात, महाराष्ट्र, गोवा, कर्नाटक, केरल, तमिलनाडु, आंध्र प्रदेश, ओडिशा, पश्चिम बंगाल।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Where was the 5th India Nuclear New Build Congress conference organized in May 2019 ?</p>",
                    question_hi: "<p>24. मई 2019 में 5वां भारत न्यूक्लियर न्यू बिल्ड कांग्रेस सम्मेलन कब आयोजित किया गया था ?</p>",
                    options_en: ["<p>Kolkata</p>", "<p>Mumbai</p>", 
                                "<p>Delhi</p>", "<p>Bangalore</p>"],
                    options_hi: ["<p>कोलकाता</p>", "<p>मुंबई</p>",
                                "<p>दिल्ली</p>", "<p>बैंगलोर</p>"],
                    solution_en: "<p>24.(b) The fifth India Nuclear New Build Congress conference was organised at Mumbai in May 2019.</p>",
                    solution_hi: "<p>24.(b) पांचवां इंडिया न्यूक्लियर न्यू बिल्ड कांग्रेस सम्मेलन मई 2019 में मुंबई में आयोजित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. What is the full form of ADSL in the context of telephony ?",
                    question_hi: "25. टेलीफोनी के सन्दर्भ में ADSL का पूर्ण रूप क्या है ?",
                    options_en: [" Asymmetrical Digital Subscriber Line", " Asymmetrical Digital Software Line ", 
                                " Aligned Digital Software Line ", " Aligned Digital Subscriber Line "],
                    options_hi: [" Asymmetrical Digital Subscriber Line", " Asymmetrical Digital Software Line ",
                                " Aligned Digital Software Line ", " Aligned Digital Subscriber Line "],
                    solution_en: "25.(a) The full form of ADSL is Asymmetric Digital Subscriber Line. It is a technology that provides high transmission speeds for video and voice to homes over an ordinary copper telephone wire.",
                    solution_hi: "25.(a) ADSL का फुल फॉर्म एसिमेट्रिक डिजिटल सब्सक्राइबर लाइन है। यह एक ऐसी तकनीक है जो एक साधारण तांबे के टेलीफोन तार पर घरों में वीडियो और आवाज के लिए उच्च संचरण गति प्रदान करती है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Which is the largest Indian public sector bank ?",
                    question_hi: "26. भारत का सबसे बड़ा सार्वजनिक क्षेत्र का बैंक कौन सा है ?",
                    options_en: [" Bank of Baroda", " State Bank of India", 
                                " Central Bank of India", " Bank of India"],
                    options_hi: [" बैंक ऑफ बड़ौदा", " भारतीय स्टेट बैंक",
                                " सेंट्रल बैंक ऑफ इंडिया", " बैंक ऑफ इंडिया"],
                    solution_en: "26.(b) The largest Indian public sector bank is State Bank of India and it was established on 1st July 1955. It was before known as Imperial Bank of India which was founded in 1921. Dinesh Kumar Khara is the present (2021) chairman. Headquarter is at Mumbai.",
                    solution_hi: "26.(b) सबसे बड़ा भारतीय सार्वजनिक क्षेत्र का बैंक भारतीय स्टेट बैंक है और इसकी स्थापना 1 जुलाई 1955 को हुई थी। इसे पहले इंपीरियल बैंक ऑफ इंडिया के नाम से जाना जाता था जिसकी स्थापना 1921 में हुई थी। दिनेश कुमार खारा वर्तमान (2021) अध्यक्ष हैं। मुख्यालय मुंबई में है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of the following is the main tissue that transports water in a plant from its roots to its leaves ?</p>",
                    question_hi: "<p>27. निम्नलिखित में से कौन-सा मुख्य ऊतक है जो पौधे में पानी को उसकी जड़ों से पत्तियों तक पहुँचाता है ?</p>",
                    options_en: ["<p>Parenchyma</p>", "<p>Collenchyma</p>", 
                                "<p>Xylem</p>", "<p>Phloem</p>"],
                    options_hi: ["<p>जीवितक</p>", "<p>कोलेनकाइमा</p>",
                                "<p>जाइलम</p>", "<p>फ्लोएम</p>"],
                    solution_en: "<p>27.(c) Xylem is the main tissue that transports water in a plant from its roots to its leaves. Phloem transports food downward from the leaves to the roots. Parenchyma is the functional tissue of an organ as distinguished from the connective and supporting tissue. Collenchyma tissue strengthened by the thickening of cell walls, as in young shoots.</p>",
                    solution_hi: "<p>27.(c) जाइलम मुख्य ऊतक है जो पौधे में पानी को उसकी जड़ों से पत्तियों तक पहुंचाता है। फ्लोएम भोजन को पत्तियों से जड़ों तक नीचे की ओर ले जाता है। पैरेन्काइमा एक अंग का कार्यात्मक ऊतक है जो संयोजी और सहायक ऊतक से अलग होता है। कोलेन्काइमा ऊतक कोशिका भित्ति के मोटे होने से मजबूत होता है, जैसा कि युवा प्ररोहों में होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The Pongal Bird Count is an initiative of which of the following states ?</p>",
                    question_hi: "<p>28. पोंगल बर्ड काउंट निम्नलिखित राज्यों में से किसकी पहल है ?</p>",
                    options_en: ["<p>Madhya Pradesh</p>", "<p>Andhra Pradesh</p>", 
                                "<p>Tamil Nadu</p>", "<p>Rajasthan</p>"],
                    options_hi: ["<p>मध्य प्रदेश</p>", "<p>आंध्र प्रदेश</p>",
                                "<p>तमिलनाडु</p>", "<p>राजस्थान</p>"],
                    solution_en: "<p>28.(c) The Pongal Bird Count is an initiative of Tamil Nadu.</p>",
                    solution_hi: "<p>28.(c) पोंगल बर्ड गणना तमिलनाडु की एक पहल है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "29. Name the process by which a single cell divides itself into 2 halves ?",
                    question_hi: "29. उस प्रक्रिया का नाम बताइए जिसके द्वारा एक कोशिका स्वयं को 2 भागों में विभाजित करती है ?",
                    options_en: [" Singular fission", " Dual fission", 
                                " Multiple fission", " Binary fission"],
                    options_hi: [" एकल विखंडन", " द्विक  विखंडन",
                                " एकाधिक विखंडन", " द्विअंगी विखण्डन"],
                    solution_en: "29.(d) Binary fission is the process by which a single cell divides itself into 2 halves.<br />Examples of binary fission are observed in bacteria, amoeba and in several eukaryotic cell organelles. Examples of multiple fission are sporozoans and algae.",
                    solution_hi: "29.(d) बाइनरी विखंडन वह प्रक्रिया है जिसके द्वारा एक एकल कोशिका स्वयं को 2 भागों में विभाजित करती है। बाइनरी विखंडन के उदाहरण बैक्टीरिया, अमीबा और कई यूकेरियोटिक सेल ऑर्गेनेल में देखे जाते हैं। एकाधिक विखंडन के उदाहरण स्पोरोज़ोअन और शैवाल हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. According to the Ancient Monument and Archeological Sites and Remains Act 1958, any structure, sculpture, inscription, etc., which are in existence for no less than ______ years is termed as ancient.</p>",
                    question_hi: "<p>30. प्राचीन स्मारक और पुरातत्व स्थल और अवशेष अधिनियम 1958 के अनुसार कोई भी संरचना, मूर्तिकला, शिलालेख आदि, जो कम से कम ______वर्षों से अस्तित्व में हैं, प्राचीन कहलाते हैं।</p>",
                    options_en: ["<p>100</p>", "<p>50</p>", 
                                "<p>200</p>", "<p>150</p>"],
                    options_hi: ["<p>100</p>", "<p>50</p>",
                                "<p>200</p>", "<p>150</p>"],
                    solution_en: "<p>30.(a) According to the Ancient Monument and Archeological Sites and Remains Act 1958, any structure, sculpture, inscription, etc., which are in existence for no less than 100 years is termed as ancient. Another act is The Ancient Monuments Preservation Act, 1904, which was passed on 18, March 1904 by British India during the times of Lord Curzon.</p>",
                    solution_hi: "<p>30.(a) प्राचीन स्मारक और पुरातत्व स्थल और अवशेष अधिनियम 1958 के अनुसार, कोई भी संरचना, मूर्तिकला, शिलालेख आदि, जो कम से कम 100 वर्षों से अस्तित्व में हैं, प्राचीन कहलाते हैं। एक अन्य अधिनियम प्राचीन स्मारक संरक्षण अधिनियम, 1904 था। लॉर्ड कर्जन के समय में ब्रिटिश भारत द्वारा 18 मार्च 1904 को पारित किया गया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following kings convened the fourth Buddhist Council in Kashmir ?</p>",
                    question_hi: "<p>31. निम्नलिखित में से किस राजा ने कश्मीर में चौथी बौद्ध संगीति का आयोजन किया ?</p>",
                    options_en: ["<p>Kanishka</p>", "<p>Bimbisara</p>", 
                                "<p>Ajatashatru</p>", "<p>Chandragupta Maurya</p>"],
                    options_hi: ["<p>कनिष्क</p>", "<p>बिंबिसार</p>",
                                "<p>अजातशत्रु</p>", "<p>चंद्रगुप्त मौर्य</p>"],
                    solution_en: "<p>31.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945573016.png\" alt=\"rId4\" width=\"290\" height=\"118\"></p>",
                    solution_hi: "<p>31.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727945573133.png\" alt=\"rId5\" width=\"246\" height=\"132\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. Government of India Act, 1935 introduced",
                    question_hi: "32. भारत सरकार अधिनियम, 1935 ने ______पेश किया गया।",
                    options_en: [" Dyrarchy", " Provincial autonomy", 
                                " Self- autonomy", " Union territories"],
                    options_hi: [" द्विशासन", " प्रांतीय स्वायत्तता",
                                " आत्म-स्वायत्तता", " केंद्र शासित प्रदेश"],
                    solution_en: "32.(b) Government of India Act, 1935 introduced Provincial autonomy. Salient features of the Government of India Act 1935 were as follows: <br />Abolition of provincial dyarchy and introduction of dyarchy at centre. Abolition of Indian council and introduction of an advisory body in its place. Provision for an All India Federation with British India territories and princely states.",
                    solution_hi: "32.(b) भारत सरकार अधिनियम, 1935 ने प्रांतीय स्वायत्तता की शुरुआत की। भारत सरकार अधिनियम 1935 की मुख्य विशेषताएं इस प्रकार थीं: <br />प्रांतीय द्वैध शासन का उन्मूलन और केंद्र में द्वैध शासन की शुरूआत। भारतीय परिषद का उन्मूलन और उसके स्थान पर एक सलाहकार निकाय की शुरूआत। ब्रिटिश भारत क्षेत्रों और रियासतों के साथ एक अखिल भारतीय संघ का प्रावधान।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Famous &ldquo;Sengal Festival &rdquo; is an annual cultural festival organised by which state of North- India ?</p>",
                    question_hi: "<p>33. प्रसिद्ध \"सेंगल उत्सव\" उत्तर-भारत के किस राज्य द्वारा आयोजित एक वार्षिक सांस्कृतिक उत्सव है ?</p>",
                    options_en: ["<p>Tripura</p>", "<p>Meghalaya</p>", 
                                "<p>Nagaland</p>", "<p>Manipur</p>"],
                    options_hi: ["<p>त्रिपुरा</p>", "<p>मेघालय</p>",
                                "<p>नगालैंड</p>", "<p>मणिपुर</p>"],
                    solution_en: "<p>33.(d) Senegal festival is celebrated for 10 days every year in the fall of November in Imphal, the capital city of Manipur, with the finest aspiration to uphold Manipur as a world-class tourism destination staging the best of its art and culture.</p>",
                    solution_hi: "<p>33.(d) मणिपुर की राजधानी इम्फाल में हर साल 10 दिनों के लिए सेनेगल उत्सव मनाया जाता है, जिसमें मणिपुर को एक विश्व स्तरीय पर्यटन स्थल के रूप में बनाए रखने की बेहतरीन आकांक्षा के साथ अपनी कला और संस्कृति का सबसे अच्छा मंचन किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. Where are the Elephanta caves situated ?",
                    question_hi: "34. एलीफेंटा की गुफाएं कहाँ स्थित हैं ?",
                    options_en: [" Gharapuri Island", " Majuli Island", 
                                " Havelock Island", " Munroe Island"],
                    options_hi: [" घारपुरी द्वीप", " माजुली द्वीप",
                                " हैवलॉक द्वीप", " मुनरो द्वीप"],
                    solution_en: "34.(a) The Elephanta caves are situated on Gharapuri Island. These caves were built between the 5th-7th century AD by Hindu Kings. It is the perfect art of Dravidian and Nagara styles of hindu architecture.",
                    solution_hi: "34.(a) एलीफेंटा की गुफाएं घारपुरी द्वीप पर स्थित हैं। इन गुफाओं का निर्माण 5वीं-7वीं शताब्दी ईस्वी के बीच हिंदू राजाओं द्वारा किया गया था। यह हिंदू वास्तुकला की द्रविड़ और नागर शैलियों की उत्तम कला है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of the following is a cold winter desert ?</p>",
                    question_hi: "<p>35. निम्नलिखित में से कौन-सा शीत मरुस्थल है ?</p>",
                    options_en: ["<p>Arabian</p>", "<p>Arctic</p>", 
                                "<p>Patagonian</p>", "<p>Sahara</p>"],
                    options_hi: ["<p>रबी</p>", "<p>आर्कटिक</p>",
                                "<p>पेंटागोनिया</p>", "<p>सहारा</p>"],
                    solution_en: "<p>35.(c) The Patagonian Desert (Argentina) is the largest of the 40 degrees parallel and is a large cold winter desert, where the temperature rarely exceeds 12&deg;C and averages just 3&deg;C.</p>",
                    solution_hi: "<p>35.(c) पेटागोनियन रेगिस्तान (अर्जेंटीना) 40 डिग्री समानांतर में सबसे बड़ा है और एक बड़ा ठंडा सर्दियों का रेगिस्तान है, जहां तापमान शायद ही कभी 12 डिग्री सेल्सियस से अधिक होता है और औसत केवल 3 डिग्री सेल्सियस होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. In India, commercial banks have the highest share in the disbursement of credit to____ and allied activities.",
                    question_hi: "36. भारत में वाणिज्यिक बैंकों के पास ऋण और संबद्ध गतिविधियों के संवितरण में सबसे अधिक हिस्सेदारी है।",
                    options_en: [" Microfinance", " Money lenders", 
                                " Agriculture", " PSUs "],
                    options_hi: [" सूक्ष्म वित्त", " साहूकार",
                                " कृषि", " PSUs "],
                    solution_en: "36.(c) In India, commercial banks have the highest share in the disbursement of credit to agriculture and allied activities. The commercial banks disburse around 60% credit followed by cooperative banks around 30% and RRB and others.",
                    solution_hi: "36.(c) भारत में, कृषि और संबद्ध गतिविधियों के लिए ऋण के वितरण में वाणिज्यिक बैंकों की हिस्सेदारी सबसे अधिक है। वाणिज्यिक बैंक लगभग 60% ऋण वितरित करते हैं, इसके बाद सहकारी बैंक लगभग 30% और RRB और अन्य हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. The first passenger railway line was opened between Bombay and Thane in",
                    question_hi: "37. पहली यात्री रेलवे लाइन बॉम्बे और ठाणे के बीच कब बनाई गई थी ?",
                    options_en: [" 1858", " 1854", 
                                " 1857", " 1853"],
                    options_hi: [" 1858", " 1854",
                                " 1857", " 1853"],
                    solution_en: "37.(d) The first passenger railway line was opened between Bombay and Thane in 1853, a 32 km line between Bombay and Thane.",
                    solution_hi: "37.(d) 1853 में बॉम्बे और ठाणे के बीच पहली यात्री रेलवे लाइन (32 किमी की लाइन ) खोली गई थी।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Which launch vehicle was used by Indian Space Research Organisation (ISRO) for its lunar exploration mission Chandrayaan-2 ?",
                    question_hi: "38. भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO) ने अपने विस्फोट मिशन चंद्रयान-2 के लिए किस प्रक्षेपण यान का उपयोग किया था ?",
                    options_en: [" PSLV-C 21", " PSLV-D 2", 
                                " ASLV-D 4", " GSLV MK - III"],
                    options_hi: [" PSLV-C 21", " PSLV-D 2",
                                " ASLV-D 4", " GSLV MK - III"],
                    solution_en: "38.(d) GSLV MK -III launch vehicle was used by Indian Space Research Organisation (ISRO) for its lunar exploration mission Chandrayaan-2. PSLV C11 launch vehicle was used for Chandrayaan1.",
                    solution_hi: "38.(d) GSLV MK-III लॉन्च व्हीकल का इस्तेमाल भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO) ने अपने चंद्र अन्वेषण मिशन चंद्रयान -2 के लिए किया था। चंद्रयान1 के लिए पीएसएलवी सी11 लॉन्च व्हीकल का इस्तेमाल किया गया था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "39. Name the minister who resigned during the India-China war in 1962 ?",
                    question_hi: "39. 1962 में भारत-चीन युद्ध के दौरान इस्तीफा देने वाले मंत्री का नाम बताइए ?",
                    options_en: [" Lal Bahadur Shastri", " Morarji Desai", 
                                " V K Krishna Menon", " Sardar Patel"],
                    options_hi: [" लाल बहादुर शास्त्री", " मोरारजी देसाई",
                                " वी के कृष्णा मेनन", " सरदार पटेल"],
                    solution_en: "39.(c) V K Krishna Menon resigned during the India China war in 1962.",
                    solution_hi: "39.(c) वी के कृष्ण मेनन ने 1962 में भारत चीन युद्ध के दौरान इस्तीफा दे दिया।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "<p>40. &lsquo;Garibi hatao&rsquo; was the slogan of which political leader ?</p>",
                    question_hi: "<p>40. \'गरीबी हटाओ\' किस राजनीतिक नेता का नारा था ?</p>",
                    options_en: ["<p>Indira Gandhi</p>", "<p>K Kamraj</p>", 
                                "<p>Morarji Desai</p>", "<p>Karpoori Thakur</p>"],
                    options_hi: ["<p>इंदिरा गांधी</p>", "<p>के कामराज</p>",
                                "<p>मोरारजी देसाई</p>", "<p>कर्पूरी ठाकुर</p>"],
                    solution_en: "<p>40.(a) &lsquo;Garibi hatao&rsquo; slogan was given by Indira Gandhi. It was part of the 5th Five-Year Plan. The Fifth Five Year Plan India was chalked out for the period spanning 1974 to 1979 with the objectives of increasing the employment level, reducing poverty, and attaining self-reliance.</p>",
                    solution_hi: "<p>40.(a) \'गरीबी हटाओ\' का नारा इंदिरा गांधी ने दिया था। यह 5वीं पंचवर्षीय योजना का हिस्सा था। भारत की पांचवी पंचवर्षीय योजना 1974 से 1979 की अवधि के लिए रोजगार स्तर बढ़ाने, गरीबी कम करने और आत्मनिर्भरता प्राप्त करने के उद्देश्यों के साथ तैयार की गई थी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>