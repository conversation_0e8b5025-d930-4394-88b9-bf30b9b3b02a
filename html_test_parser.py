#!/usr/bin/env python3
"""
HTML Test Parser - Parses different types of HTML test/exam files
Supports multiple formats and extracts comprehensive test data to JSON
"""

import os
import re
import json
import argparse
from bs4 import BeautifulSoup
from typing import Dict, List, Any, Optional, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HTMLTestParser:
    def __init__(self):
        self.supported_types = ['bootstrap_embedded', 'javascript_dynamic', 'simple_html']
    
    def detect_html_type(self, soup: BeautifulSoup, file_path: str) -> str:
        """Detect the type of HTML test file"""
        
        # Check for JavaScript-based tests with questions array
        scripts = soup.find_all('script')
        for script in scripts:
            if script.string:
                if 'const questions' in script.string or 'questions:' in script.string:
                    return 'javascript_dynamic'
        
        # Check for Bootstrap-based embedded questions
        if soup.find_all('div', class_=re.compile(r'question-block|card.*question')):
            return 'bootstrap_embedded'
        
        # Check for simple HTML with question divs
        if soup.find_all('div', class_='question'):
            return 'simple_html'
        
        # Default fallback
        return 'simple_html'
    
    def parse_bootstrap_embedded(self, soup: BeautifulSoup, file_path: str) -> Dict[str, Any]:
        """Parse Bootstrap-based embedded HTML tests"""
        logger.info(f"Parsing Bootstrap embedded test: {file_path}")
        
        # Extract test name from title or header
        test_name = soup.find('title')
        if test_name:
            test_name = test_name.get_text().strip()
        else:
            test_name = os.path.basename(file_path).replace('.html', '')
        
        # Find sections
        sections = []
        section_tabs = soup.find_all('li', class_='nav-item')
        
        if not section_tabs:
            # Single section test
            sections.append({
                'name': 'Test',
                'questions': []
            })
        else:
            for tab in section_tabs:
                section_name = tab.get_text().strip()
                sections.append({
                    'name': section_name,
                    'questions': []
                })
        
        # Extract questions
        question_blocks = soup.find_all('div', class_=re.compile(r'question-block|card.*question'))
        
        for i, block in enumerate(question_blocks):
            question_data = self._extract_bootstrap_question(block, i + 1)
            if question_data:
                # Assign to first section if only one section
                if len(sections) == 1:
                    sections[0]['questions'].append(question_data)
                else:
                    # Try to determine section based on context or assign to first
                    sections[0]['questions'].append(question_data)
        
        return {
            'test_name': test_name,
            'test_type': 'bootstrap_embedded',
            'total_questions': len(question_blocks),
            'sections': sections
        }
    
    def _extract_bootstrap_question(self, block: BeautifulSoup, question_num: int) -> Optional[Dict[str, Any]]:
        """Extract question data from Bootstrap question block"""
        try:
            # Extract question ID
            question_id = block.get('id', f'q{question_num}')
            
            # Extract marks
            pos_marks = float(block.get('data-pos-marks', 1))
            neg_marks = float(block.get('data-neg-marks', 0))
            correct_answer_index = int(block.get('data-correct', 0))
            
            # Extract question text
            question_text_elem = block.find('div', class_='question-text')
            question_text = question_text_elem.get_text().strip() if question_text_elem else ""
            
            # Extract options
            options = []
            option_elements = block.find_all('div', class_='option')
            
            for opt_elem in option_elements:
                option_text_elem = opt_elem.find('div', class_='option-text')
                if option_text_elem:
                    options.append(option_text_elem.get_text().strip())
            
            # Extract solution
            solution_elem = block.find('div', class_='solution')
            solution = ""
            if solution_elem:
                solution = solution_elem.get_text().strip()
            
            return {
                'id': question_id,
                'question_number': question_num,
                'question_text': question_text,
                'options': options,
                'correct_answer_index': correct_answer_index,
                'correct_answer': options[correct_answer_index] if correct_answer_index < len(options) else "",
                'positive_marks': pos_marks,
                'negative_marks': neg_marks,
                'solution': solution
            }
        
        except Exception as e:
            logger.warning(f"Error extracting question {question_num}: {e}")
            return None
    
    def parse_javascript_dynamic(self, soup: BeautifulSoup, file_path: str) -> Dict[str, Any]:
        """Parse JavaScript-based dynamic HTML tests"""
        logger.info(f"Parsing JavaScript dynamic test: {file_path}")
        
        # Extract test name from title
        test_name = soup.find('title')
        if test_name:
            test_name = test_name.get_text().strip()
        else:
            test_name = os.path.basename(file_path).replace('.html', '')
        
        # Find and parse JavaScript questions
        scripts = soup.find_all('script')
        questions_data = []
        
        for script in scripts:
            if script.string:
                questions_data = self._extract_js_questions(script.string)
                if questions_data:
                    break
        
        # Create single section for JS-based tests
        sections = [{
            'name': 'Test',
            'questions': questions_data
        }]
        
        return {
            'test_name': test_name,
            'test_type': 'javascript_dynamic',
            'total_questions': len(questions_data),
            'sections': sections
        }
    
    def _extract_js_questions(self, script_content: str) -> List[Dict[str, Any]]:
        """Extract questions from JavaScript content"""
        questions = []

        try:
            # Find the start and end of the questions array
            start_pattern = r'questions:\s*\[\s*\{'
            end_pattern = r'\}\s*\]\s*\}'

            start_match = re.search(start_pattern, script_content)
            if not start_match:
                return questions

            start_pos = start_match.start()

            # Find the questions section
            questions_section = script_content[start_pos:]

            # Split by question objects (look for },{)
            question_parts = re.split(r'\},\s*\{', questions_section)

            for i, part in enumerate(question_parts):
                if 'id:' not in part:
                    continue

                try:
                    # Clean up the part
                    if not part.strip().startswith('{'):
                        part = '{' + part
                    if not part.strip().endswith('}'):
                        part = part + '}'

                    # Extract individual fields using regex
                    id_match = re.search(r'id:\s*(\d+)', part)
                    text_match = re.search(r'text:\s*"([^"]*)"', part)
                    options_match = re.search(r'options:\s*\[(.*?)\]', part, re.DOTALL)
                    correct_match = re.search(r'correctIndex:\s*(\d+)', part)
                    explanation_match = re.search(r'explanation:\s*"([^"]*)"', part, re.DOTALL)

                    if not all([id_match, text_match, options_match, correct_match]):
                        continue

                    question_id = id_match.group(1)
                    text = text_match.group(1)
                    options_str = options_match.group(1)
                    correct_index = int(correct_match.group(1))
                    explanation = explanation_match.group(1) if explanation_match else ""

                    # Parse options
                    options = []
                    option_matches = re.findall(r'"([^"]*)"', options_str)
                    options = option_matches

                    if len(options) == 0:
                        continue

                    question_data = {
                        'id': f'q{question_id}',
                        'question_number': i + 1,
                        'question_text': text,
                        'options': options,
                        'correct_answer_index': correct_index,
                        'correct_answer': options[correct_index] if correct_index < len(options) else "",
                        'positive_marks': 1,
                        'negative_marks': 0.25,
                        'solution': explanation
                    }

                    questions.append(question_data)

                except Exception as e:
                    logger.warning(f"Error parsing question {i}: {e}")
                    continue

        except Exception as e:
            logger.warning(f"Error extracting JS questions: {e}")

        return questions

    def _extract_js_questions_json_fallback(self, script_content: str) -> List[Dict[str, Any]]:
        """Fallback method to extract questions using JSON parsing"""
        questions = []

        try:
            # Pattern for questions array in different formats
            patterns = [
                r'const\s+questions\s*=\s*(\[.*?\]);',
                r'questions:\s*(\[.*?\])',
                r'const\s+quizData\s*=\s*{\s*questions:\s*(\[.*?\])'
            ]

            questions_match = None
            for pattern in patterns:
                match = re.search(pattern, script_content, re.DOTALL)
                if match:
                    questions_match = match.group(1)
                    break

            if questions_match:
                # Clean up the JavaScript array to make it JSON-parseable
                cleaned_js = self._clean_js_array(questions_match)

                try:
                    questions_array = json.loads(cleaned_js)

                    for i, q in enumerate(questions_array):
                        question_data = self._normalize_js_question(q, i + 1)
                        if question_data:
                            questions.append(question_data)

                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to parse questions JSON: {e}")

        except Exception as e:
            logger.warning(f"Error in JSON fallback: {e}")

        return questions
    
    def _clean_js_array(self, js_array: str) -> str:
        """Clean JavaScript array to make it JSON-parseable"""
        # Remove JavaScript comments
        js_array = re.sub(r'//.*?\n', '\n', js_array)
        js_array = re.sub(r'/\*.*?\*/', '', js_array, flags=re.DOTALL)

        # Fix unquoted keys (be more careful with this)
        js_array = re.sub(r'(\w+)(\s*):', r'"\1"\2:', js_array)

        # Fix single quotes to double quotes (but be careful with apostrophes)
        # First, protect apostrophes in contractions
        js_array = re.sub(r"(\w)'(\w)", r"\1APOSTROPHE\2", js_array)

        # Now fix single quotes
        js_array = re.sub(r"'([^']*)'", r'"\1"', js_array)

        # Restore apostrophes
        js_array = re.sub(r"APOSTROPHE", "'", js_array)

        # Escape unescaped quotes in strings
        js_array = re.sub(r'(?<!\\)"([^"]*)"([^"]*)"([^"]*)"', r'"\1\"\2\"\3"', js_array)

        # Remove trailing commas
        js_array = re.sub(r',(\s*[}\]])', r'\1', js_array)

        return js_array
    
    def _normalize_js_question(self, q_data: Dict, question_num: int) -> Optional[Dict[str, Any]]:
        """Normalize JavaScript question data to standard format"""
        try:
            # Handle different field names
            question_text = q_data.get('text', q_data.get('question', q_data.get('question_en', '')))
            options = q_data.get('options', [])
            correct_index = q_data.get('correctIndex', q_data.get('correct', 0))
            explanation = q_data.get('explanation', q_data.get('solution', ''))
            
            return {
                'id': q_data.get('id', f'q{question_num}'),
                'question_number': question_num,
                'question_text': question_text,
                'options': options,
                'correct_answer_index': correct_index,
                'correct_answer': options[correct_index] if correct_index < len(options) else "",
                'positive_marks': 1,  # Default values for JS tests
                'negative_marks': 0.25,
                'solution': explanation
            }
        except Exception as e:
            logger.warning(f"Error normalizing JS question {question_num}: {e}")
            return None

    def parse_simple_html(self, soup: BeautifulSoup, file_path: str) -> Dict[str, Any]:
        """Parse simple HTML tests with inline questions"""
        logger.info(f"Parsing simple HTML test: {file_path}")

        # Extract test name from title or h1/h2
        test_name = soup.find('title')
        if test_name:
            test_name = test_name.get_text().strip()
        else:
            header = soup.find(['h1', 'h2'])
            if header:
                test_name = header.get_text().strip()
            else:
                test_name = os.path.basename(file_path).replace('.html', '')

        # Find questions
        question_divs = soup.find_all('div', class_='question')
        questions_data = []

        for i, q_div in enumerate(question_divs):
            question_data = self._extract_simple_question(q_div, i + 1)
            if question_data:
                questions_data.append(question_data)

        # Create single section
        sections = [{
            'name': 'Test',
            'questions': questions_data
        }]

        return {
            'test_name': test_name,
            'test_type': 'simple_html',
            'total_questions': len(questions_data),
            'sections': sections
        }

    def _extract_simple_question(self, q_div: BeautifulSoup, question_num: int) -> Optional[Dict[str, Any]]:
        """Extract question data from simple HTML question div"""
        try:
            # Extract question text
            question_text = q_div.get_text().strip()

            # Find options div (next sibling)
            options_div = q_div.find_next_sibling('div', class_='options')
            options = []
            correct_index = 0

            if options_div:
                option_buttons = options_div.find_all('button')
                for i, btn in enumerate(option_buttons):
                    option_text = btn.get_text().strip()
                    options.append(option_text)

                    # Check onclick for correct answer
                    onclick = btn.get('onclick', '')
                    if 'true' in onclick:
                        correct_index = i

            # Find explanation (next ai-response)
            explanation_elem = q_div.find_next_sibling('p', class_='ai-response')
            explanation = ""
            if explanation_elem:
                explanation = explanation_elem.get_text().strip()

            return {
                'id': f'q{question_num}',
                'question_number': question_num,
                'question_text': question_text,
                'options': options,
                'correct_answer_index': correct_index,
                'correct_answer': options[correct_index] if correct_index < len(options) else "",
                'positive_marks': 1,
                'negative_marks': 0,
                'solution': explanation
            }

        except Exception as e:
            logger.warning(f"Error extracting simple question {question_num}: {e}")
            return None

    def parse_html_file(self, file_path: str) -> Dict[str, Any]:
        """Main method to parse any HTML test file"""
        logger.info(f"Parsing file: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            soup = BeautifulSoup(content, 'html.parser')

            # Detect HTML type
            html_type = self.detect_html_type(soup, file_path)
            logger.info(f"Detected HTML type: {html_type}")

            # Parse based on type
            if html_type == 'bootstrap_embedded':
                return self.parse_bootstrap_embedded(soup, file_path)
            elif html_type == 'javascript_dynamic':
                return self.parse_javascript_dynamic(soup, file_path)
            elif html_type == 'simple_html':
                return self.parse_simple_html(soup, file_path)
            else:
                raise ValueError(f"Unsupported HTML type: {html_type}")

        except Exception as e:
            logger.error(f"Error parsing file {file_path}: {e}")
            return {
                'test_name': os.path.basename(file_path),
                'test_type': 'error',
                'error': str(e),
                'total_questions': 0,
                'sections': []
            }

    def save_to_json(self, test_data: Dict[str, Any], output_path: str):
        """Save parsed test data to JSON file"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved test data to: {output_path}")
        except Exception as e:
            logger.error(f"Error saving to JSON: {e}")


def main():
    parser = argparse.ArgumentParser(description='Parse HTML test files and convert to JSON')
    parser.add_argument('input_files', nargs='+', help='HTML files to parse')
    parser.add_argument('--output-dir', '-o', default='parsed_tests',
                       help='Output directory for JSON files')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Initialize parser
    html_parser = HTMLTestParser()

    # Process each file
    for input_file in args.input_files:
        if not os.path.exists(input_file):
            logger.error(f"File not found: {input_file}")
            continue

        # Parse the file
        test_data = html_parser.parse_html_file(input_file)

        # Generate output filename
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = os.path.join(args.output_dir, f"{base_name}.json")

        # Save to JSON
        html_parser.save_to_json(test_data, output_file)

        # Print summary
        print(f"\nParsed: {input_file}")
        print(f"Test Name: {test_data['test_name']}")
        print(f"Type: {test_data['test_type']}")
        print(f"Total Questions: {test_data['total_questions']}")
        print(f"Sections: {len(test_data['sections'])}")
        print(f"Output: {output_file}")


if __name__ == "__main__":
    main()
