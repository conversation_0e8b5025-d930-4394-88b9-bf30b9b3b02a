<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. If 23 January 2002 was Wednesday, then what was the day of the week on 26 January 2007 ?</p>",
                    question_hi: "<p>1. यदि 23 जनवरी 2002 को बुधवार था, तो 26 जनवरी 2007 को सप्ताह का कौन-सा दिन रहा होगा?</p>",
                    options_en: ["<p>Saturday</p>", "<p>Sunday</p>", 
                                "<p>Friday</p>", "<p>Monday</p>"],
                    options_hi: ["<p>शनिवार</p>", "<p>रविवार</p>",
                                "<p>शुक्रवार</p>", "<p>सोमवार</p>"],
                    solution_en: "<p>1.(c) 23 January 2002 was Wednesday. On moving to 2007 the number of odd days <br>= +1 + 2 + 1 + 1 + 1 = 6 , <br>We have reached till 23 January 2007, but we have to go to 26 January, the number of days between = 3. Total number of days = 9. On dividing by 7 remainder = 2 <math display=\"inline\"><mo>&#8658;</mo></math> Wednesday + 2 = Friday.</p>",
                    solution_hi: "<p>1.(c) 23 जनवरी 2002 को बुधवार था। 2007 में जाने पर विषम दिनों की संख्या<br>= +1 + 2 + 1 + 1 + 1 = 6 , <br>हम 23 जनवरी 2007 तक पहुँच चुके हैं, लेकिन हमें 26 जनवरी तक जाना है, बीच के दिनों की संख्या = 3. <br>कुल दिनों की संख्या = 9, 7 से भाग देने पर शेषफल = 2 <math display=\"inline\"><mo>&#8658;</mo></math> बुधवार + 2 = शुक्रवार.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. If \'A\' stands for \'<math display=\"inline\"><mo>&#247;</mo></math>\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for &lsquo;-\', what will come in place of the question mark (?) in the following equation?<br>16 B 8 D 51 A 3 C 57 = ?</p>",
                    question_hi: "<p>2. यदि \'A\' का अर्थ \'<math display=\"inline\"><mo>&#247;</mo></math>\', \'B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>16 B 8 D 51 A 3 C 57 = ?</p>",
                    options_en: ["<p>168</p>", "<p>188</p>", 
                                "<p>148</p>", "<p>128</p>"],
                    options_hi: ["<p>168</p>", "<p>188</p>",
                                "<p>148</p>", "<p>128</p>"],
                    solution_en: "<p>2.(a) <strong>Given :-</strong> 16 B 8 D 51 A 3 C 57<br>As per given instruction after interchanging the letter with sign we get<br>16 &times; 8 - 51 <math display=\"inline\"><mo>&#247;</mo></math> 3 + 57<br>128 - 17 + 57 = 168</p>",
                    solution_hi: "<p>2.(a) <strong>दिया गया :- </strong>16 B 8 D 51 A 3 C 57<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>16 &times; 8 - 51 <math display=\"inline\"><mo>&#247;</mo></math> 3 + 57<br>128 - 17 + 57 = 168</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?</p>\n<p>(Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>3. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है? <br>(ध्यान दें : असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: ["<p>RUX</p>", "<p>KMP</p>", 
                                "<p>NQT</p>", "<p>BEH</p>"],
                    options_hi: ["<p>RUX</p>", "<p>KMP</p>",
                                "<p>NQT</p>", "<p>BEH</p>"],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071766585.png\" alt=\"rId6\" width=\"170\" height=\"100\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071766765.png\" alt=\"rId7\" height=\"100\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071767020.png\" alt=\"rId8\" height=\"100\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071767283.png\" alt=\"rId9\" height=\"100\"></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071766585.png\" alt=\"rId6\" height=\"100\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071766765.png\" alt=\"rId7\" height=\"100\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071767020.png\" alt=\"rId8\" height=\"100\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071767283.png\" alt=\"rId9\" height=\"100\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, \'hands are dirty\' is written as \'ca bx vp\' and \'dirty things flies\' is written as \'by vp cb\'. How is \'dirty\' written in the given language?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में, \'hands are dirty\' को \'ca bx vp\' लिखा जाता है और \'dirty things flies\' को \'by vp cb\' लिखा जाता है। उसी कूट भाषा में \'dirty\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>vp</p>", "<p>by</p>", 
                                "<p>cb</p>", "<p>bx</p>"],
                    options_hi: ["<p>vp</p>", "<p>by</p>",
                                "<p>cb</p>", "<p>bx</p>"],
                    solution_en: "<p>4.(a) hands are dirty &rarr; ca bx vp&hellip;&hellip;(i)<br>&nbsp; &nbsp; &nbsp; &nbsp; dirty things flies &rarr; by vp cb&hellip;&hellip;(ii)<br>From (i) and (ii) &lsquo;dirty&rsquo; and &lsquo;vp&rsquo; are common. The code of &lsquo;dirty&rsquo; = &lsquo;vp&rsquo;.</p>",
                    solution_hi: "<p>4.(a) hands are dirty &rarr; ca bx vp&hellip;&hellip;(i)<br>&nbsp; &nbsp; &nbsp; &nbsp; dirty things flies &rarr; by vp cb&hellip;&hellip;(ii)<br>(i) और (ii) से \'dirty\' और \'vp\' उभय-निष्ठ हैं। \'dirty\' का कूट = \'vp\'</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. 11 is related to 121 by certain logic. Following the same logic, 21 is related to 441. To which of the following is 31 related, following the same logic? <br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding/ subtracting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>5. 11 का संबंध एक निश्चित तर्क से 121 से है। उसी तर्क का अनुसरण करते हुए, 21 का संबंध 441 से है। उसी तर्क का अनुसरण करते हुए, 31 का संबंध निम्नलिखित में से किससे है? <br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>428</p>", "<p>961</p>", 
                                "<p>356</p>", "<p>960</p>"],
                    options_hi: ["<p>428</p>", "<p>961</p>",
                                "<p>356</p>", "<p>960</p>"],
                    solution_en: "<p>5.(b) <strong>Logic :-</strong> (1st number)<sup>2</sup> = 2nd number<br>(11 , 121) :- (11)<sup>2</sup> = 121<br>(21 , 441) :- (21)<sup>2</sup> = 441<br>Similarly,<br>(31, ?) :- (31)<sup>2</sup> = 961</p>",
                    solution_hi: "<p>5.(b) <strong>तर्क :-</strong> (पहली संख्या)<sup>2</sup> = दूसरी संख्या<br>(11 , 121) :- (11)<sup>2</sup> = 121<br>(21 , 441) :- (21)<sup>2</sup> = 441<br>इसी प्रकार,<br>(31, ?) :- (31)<sup>2</sup> = 961</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. If 26 November 2013 was Tuesday, then what was the day of the week on 29 November 2018?</p>",
                    question_hi: "<p>6. यदि 26 नवंबर 2013 को मंगलवार था, तो 29 नवंबर 2018 को सप्ताह का कौन-सा दिन रहा होगा?</p>",
                    options_en: ["<p>Saturday</p>", "<p>Friday</p>", 
                                "<p>Wednesday</p>", "<p>Thursday</p>"],
                    options_hi: ["<p>शनिवार</p>", "<p>शुक्रवार</p>",
                                "<p>बुधवार</p>", "<p>गुरुवार</p>"],
                    solution_en: "<p>6.(d) 26 November 2013 was Tuesday. On moving to 2018 the number of odd days <br>= +1 + 1 + 2 + 1 + 1 = 6. <br>We have reached till 26 November, but we have to reach till 29 November. <br>The number of days in between = 3. Total number of days = 6 + 3 = 9. <br>On dividing 9 by 7, the remainder is 2 <math display=\"inline\"><mo>&#8658;</mo></math> Tuesday + 2 = Thursday.</p>",
                    solution_hi: "<p>6.(d) 26 नवंबर 2013 को मंगलवार था. 2018 में जाने पर विषम दिनों की संख्या <br>= +1 + 1 + 2 + 1 + 1 = 6. <br>हम 26 नवंबर तक पहुंच गए हैं, लेकिन हमें 29 नवंबर तक पहुंचना है. बीच में दिनों की संख्या = 3. <br>दिनों की कुल संख्या = 6 + 3 = 9, 9 को 7 से विभाजित करने पर शेषफल 2 आता है। <math display=\"inline\"><mo>&#8658;</mo></math> मंगलवार + 2 = गुरुवार।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What should come in place of the question mark (?) in the given series based on the English alphabetical order? <br>MKH, PNK, SQN, VTQ, ?</p>",
                    question_hi: "<p>7.अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न चिह्न &lsquo;?&rsquo; के स्थान पर क्या आना चाहिए?<br>MKH, PNK, SQN, VTQ, ?</p>",
                    options_en: ["<p>WYT</p>", "<p>WTY</p>", 
                                "<p>YTW</p>", "<p>YWT</p>"],
                    options_hi: ["<p>WYT</p>", "<p>WTY</p>",
                                "<p>YTW</p>", "<p>YWT</p>"],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071767764.png\" alt=\"rId10\" width=\"438\" height=\"140\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071767764.png\" alt=\"rId10\" height=\"140\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement. <br><strong>Statements :</strong> <br>All space is wind. <br>Some wind is earth. <br>Some fire is earth. <br><strong>Conclusion (I) : </strong>All earth is space. <br><strong>Conclusion (II) :</strong> Some wind is fire</p>",
                    question_hi: "<p>8. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong> <br>सभी अंतरिक्ष , पवन हैं। <br>कुछ पवन, पृथ्वी हैं। <br>कुछ अग्नि , पृथ्वी हैं। <br><strong>निष्कर्ष (I) :</strong> सभी पृथ्वी , अंतरिक्ष है। <br><strong>निष्कर्ष (II) : </strong>कुछ पवन, अग्नि हैं।</p>",
                    options_en: ["<p>Only conclusion (I) follows.</p>", "<p>Only conclusion (II) follows</p>", 
                                "<p>Both conclusions (I) and (II) follow.</p>", "<p>Neither conclusion (I) nor (II) follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>", "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                                "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>", "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>"],
                    solution_en: "<p>8.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071768127.png\" alt=\"rId11\" width=\"391\" height=\"110\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>8.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071768516.png\" alt=\"rId12\" height=\"110\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. In a certain code language <br>A + B means &lsquo;A is the mother of B&rsquo; <br>A &ndash; B means &lsquo;A is the sister of B&rsquo; <br>A &times; B means &lsquo;A is the wife of B&rsquo; <br>A &divide; B means &lsquo;A is the son of B&rsquo; <br>Based on the above, how is Z related to W if &lsquo;Z &times; Y &divide; X &minus; V + W&rsquo;?</p>",
                    question_hi: "<p>9. एक निश्चित कूट भाषा में<br>A + B का अर्थ A, B की मां है <br>A &ndash; B का अर्थ A, B की बहन है <br>A &times; B का अर्थ A, B की पत्नी है <br>A &divide; B का अर्थ A, B का बेटा है <br>उपरोक्त के आधार पर, यदि Z &times; Y &divide; X - V + W है, तो Z का W से क्या संबंध है?</p>",
                    options_en: ["<p>Mother&rsquo;s brother&rsquo;s son&rsquo;s wife</p>", "<p>Mother&rsquo;s sister&rsquo;s daughter</p>", 
                                "<p>Mother&rsquo;s sister&rsquo;s son&rsquo;s wife</p>", "<p>Mother&rsquo;s sister&rsquo;s son&rsquo;s daughter</p>"],
                    options_hi: ["<p>मां के भाई के बेटे की पत्नी</p>", "<p>मां की बहन की बेटी</p>",
                                "<p>मां की बहन के बेटे की पत्नी</p>", "<p>मां की बहन के बेटे की बेटी</p>"],
                    solution_en: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071768698.png\" alt=\"rId13\" width=\"280\" height=\"150\"><br>Z is the wife of W&rsquo;s mother&rsquo;s sister&rsquo;s son.</p>",
                    solution_hi: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071768698.png\" alt=\"rId13\" height=\"150\"><br>Z, W की माँ की बहन के बेटे की पत्नी है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071768815.png\" alt=\"rId14\" width=\"166\" height=\"150\"></p>",
                    question_hi: "<p>10. दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071768815.png\" alt=\"rId14\" height=\"150\"></p>",
                    options_en: ["<p>14</p>", "<p>12</p>", 
                                "<p>9</p>", "<p>16</p>"],
                    options_hi: ["<p>14</p>", "<p>12</p>",
                                "<p>9</p>", "<p>16</p>"],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071769078.png\" alt=\"rId15\" height=\"185\"><br>There are 16 triangle<br>ABD, ACD, DEF, CDE, ADE, GJH, JHI, HMF, BHF, BDH, DHF, HIG, MLN, LNO, NOQ, PQO.</p>",
                    solution_hi: "<p>10.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071769078.png\" alt=\"rId15\" height=\"185\"><br>16 त्रिभुज हैं<br>ABD, ACD, DEF, CDE, ADE, GJH, JHI, HMF, BHF, BDH, DHF, HIG, MLN, LNO, NOQ, PQO.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. AQLZ is related to SIDR in a certain way based on the English alphabetical order. In the same way, CPDV is related to UHVN. To which of the following is EBRY related, following the same logic?</p>",
                    question_hi: "<p>11. अंग्रेजी वर्णमाला क्रम के आधार पर AQLZ, SIDR से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, CPDV, UHVN से संबंधित है। समान तर्क का अनुसरण करते हुए EBRY निम्नलिखित में से किससे संबंधित है?</p>",
                    options_en: ["<p>WTJQ</p>", "<p>WQTJ</p>", 
                                "<p>WQJT</p>", "<p>WTQJ</p>"],
                    options_hi: ["<p>WTJQ</p>", "<p>WQTJ</p>",
                                "<p>WQJT</p>", "<p>WTQJ</p>"],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071769245.png\" alt=\"rId16\" width=\"145\" height=\"115\">&nbsp;,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071769426.png\" alt=\"rId17\" height=\"115\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071769609.png\" alt=\"rId18\" height=\"115\"></p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071769245.png\" alt=\"rId16\" height=\"115\"> ,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071769426.png\" alt=\"rId17\" height=\"115\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071769609.png\" alt=\"rId18\" height=\"115\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Which of the following numbers will replace the question mark (?) in the given series?<br>93, 111, 139, 157, 185, 203, ?</p>",
                    question_hi: "<p>12. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी?<br>93, 111, 139, 157, 185, 203, ?</p>",
                    options_en: ["<p>211</p>", "<p>215</p>", 
                                "<p>227</p>", "<p>231</p>"],
                    options_hi: ["<p>211</p>", "<p>215</p>",
                                "<p>227</p>", "<p>231</p>"],
                    solution_en: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071769938.png\" alt=\"rId19\" width=\"571\" height=\"95\"></p>",
                    solution_hi: "<p>12.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071769938.png\" alt=\"rId19\" height=\"95\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Which figure should replace the question mark (?) if the following figure series were to be continued ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071770594.png\" alt=\"rId20\" width=\"452\" height=\"100\"></p>",
                    question_hi: "<p>13. यदि निम्नलिखित आकृति श्रृंखला को जारी रखना हो तो कौन-सी आकृति प्रश्न चिन्ह (?) के स्थान पर आनी चाहिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071770594.png\" alt=\"rId20\" height=\"100\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071770718.png\" alt=\"rId21\" height=\"115\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071770857.png\" alt=\"rId22\" height=\"115\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771141.png\" alt=\"rId23\" height=\"115\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771243.png\" alt=\"rId24\" height=\"115\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071770718.png\" alt=\"rId21\" height=\"115\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071770857.png\" alt=\"rId22\" height=\"115\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771141.png\" alt=\"rId23\" height=\"115\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771243.png\" alt=\"rId24\" width=\"131\" height=\"115\"></p>"],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071770857.png\" alt=\"rId22\" height=\"120\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071770857.png\" alt=\"rId22\" height=\"120\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g., 13 &ndash; Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>(3, 12, 26) <br>(7, 9, 53)</p>",
                    question_hi: "<p>14. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुचयों की संख्याएं संबंधित हैं। <br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(3, 12, 26) <br>(7, 9, 53)</p>",
                    options_en: ["<p>(5, 15, 45)</p>", "<p>(12, 4, 114)</p>", 
                                "<p>(7, 5, 25)</p>", "<p>(4, 9, 16)</p>"],
                    options_hi: ["<p>(5, 15, 45)</p>", "<p>(12, 4, 114)</p>",
                                "<p>(7, 5, 25)</p>", "<p>(4, 9, 16)</p>"],
                    solution_en: "<p>14.(c) <strong>Logic :-</strong> (1st number &times; 2nd number) - 10 = 3rd number<br>(3, 12, 26) :- (3 &times; 12) - 10 &rArr; (36) - 10 = 26<br>(7, 9, 53) :- (7 &times; 9) - 10 &rArr; (63) - 10 = 53<br>Similarly,<br>(7, 5, 25) :- (7 &times; 5)- 10 &rArr; (35) - 10 = 25</p>",
                    solution_hi: "<p>14.(c) <strong>तर्क :-</strong> (पहली संख्या &times; दूसरी संख्या) - 10 = तीसरी संख्या<br>(3, 12, 26) :- (3 &times; 12) - 10 &rArr; (36) - 10 = 26<br>(7, 9, 53) :- (7 &times; 9) - 10 &rArr; (63) - 10 = 53<br>इसी प्रकार,<br>(7, 5, 25) :- (7 &times; 5)- 10 &rArr; (35) - 10 = 25</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. What should come in place of the question mark (?) in the given series ?<br>77, 79, 83, 89, 97, ?</p>",
                    question_hi: "<p>15. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>77, 79, 83, 89, 97, ?</p>",
                    options_en: ["<p>107</p>", "<p>108</p>", 
                                "<p>105</p>", "<p>106</p>"],
                    options_hi: ["<p>107</p>", "<p>108</p>",
                                "<p>105</p>", "<p>106</p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771470.png\" alt=\"rId25\" width=\"507\" height=\"100\"></p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771470.png\" alt=\"rId25\" height=\"100\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the option in which the given figure is embedded (rotation is not allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771692.png\" alt=\"rId26\" width=\"143\" height=\"125\"></p>",
                    question_hi: "<p>16. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771692.png\" alt=\"rId26\" height=\"125\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771791.png\" alt=\"rId27\" height=\"120\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771932.png\" alt=\"rId28\" height=\"120\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772043.png\" alt=\"rId29\" height=\"120\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772147.png\" alt=\"rId30\" height=\"120\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771791.png\" alt=\"rId27\" height=\"120\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771932.png\" alt=\"rId28\" height=\"120\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772043.png\" alt=\"rId29\" height=\"120\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772147.png\" alt=\"rId30\" height=\"120\"></p>"],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771932.png\" alt=\"rId28\" height=\"130\"></p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071771932.png\" alt=\"rId28\" height=\"130\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the option in which the given figure is embedded. (Rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772241.png\" alt=\"rId31\" width=\"172\" height=\"90\"></p>",
                    question_hi: "<p>17. उस विकल्प का चयन करें, जिसमें दी गई आकृति सन्निहित है। (घूर्णन की अनुमति नहीं है। )<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772241.png\" alt=\"rId31\" height=\"90\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772366.png\" alt=\"rId32\" width=\"177\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772497.png\" alt=\"rId33\" height=\"85\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772608.png\" alt=\"rId34\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772713.png\" alt=\"rId35\" height=\"85\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772366.png\" alt=\"rId32\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772497.png\" alt=\"rId33\" height=\"85\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772608.png\" alt=\"rId34\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772713.png\" alt=\"rId35\" height=\"85\"></p>"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772497.png\" alt=\"rId33\" height=\"90\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772497.png\" alt=\"rId33\" height=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the pair which follows the same pattern as that followed by the two set of pairs given below. Both pairs follow the same pattern.<br>FJK : KOP<br>MQR : RVW</p>",
                    question_hi: "<p>18. उस युग्म को चुनिए, जो उसी पैटर्न का अनुसरण करता है जो नीचे दिए गए युग्मों के दो समुच्चय द्वारा किया जाता है। दोनों युग्म समान पैटर्न का अनुसरण करते हैं।<br>FJK : KOP<br>MQR : RVW</p>",
                    options_en: ["<p>ADE : ZWV</p>", "<p>JMP : ORU</p>", 
                                "<p>PRS : SRP</p>", "<p>GMN : FLM</p>"],
                    options_hi: ["<p>ADE : ZWV</p>", "<p>JMP : ORU</p>",
                                "<p>PRS : SRP</p>", "<p>GMN : FLM</p>"],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772930.png\" alt=\"rId36\" width=\"178\" height=\"110\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773146.png\" alt=\"rId37\" height=\"110\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773334.png\" alt=\"rId38\" height=\"110\"></p>",
                    solution_hi: "<p>18.(b)<br>, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071772930.png\" alt=\"rId36\" height=\"110\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773146.png\" alt=\"rId37\" height=\"110\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773334.png\" alt=\"rId38\" height=\"110\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the option figure in which the given figure is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773482.png\" alt=\"rId39\" width=\"130\" height=\"100\"></p>",
                    question_hi: "<p>19. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति उसके भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773482.png\" alt=\"rId39\" height=\"110\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773621.png\" alt=\"rId40\" width=\"112\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773727.png\" alt=\"rId41\" height=\"110\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773858.png\" alt=\"rId42\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773969.png\" alt=\"rId43\" height=\"110\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773621.png\" alt=\"rId40\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773727.png\" alt=\"rId41\" height=\"110\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773858.png\" alt=\"rId42\" height=\"110\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773969.png\" alt=\"rId43\" height=\"110\"></p>"],
                    solution_en: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773969.png\" alt=\"rId43\" width=\"119\" height=\"125\"></p>",
                    solution_hi: "<p>19.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071773969.png\" alt=\"rId43\" height=\"125\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. A paper is folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774088.png\" alt=\"rId44\" width=\"324\" height=\"110\"></p>",
                    question_hi: "<p>20. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774088.png\" alt=\"rId44\" height=\"110\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774374.png\" alt=\"rId45\" width=\"129\" height=\"110\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774564.png\" alt=\"rId46\" height=\"110\"></p>", 
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774695.png\" alt=\"rId47\" height=\"110\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774820.png\" alt=\"rId48\" height=\"110\"></p>"],
                    options_hi: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774374.png\" alt=\"rId45\" height=\"110\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774564.png\" alt=\"rId46\" height=\"110\"></p>",
                                "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774695.png\" alt=\"rId47\" height=\"110\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774820.png\" alt=\"rId48\" height=\"110\"></p>"],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774820.png\" alt=\"rId48\" height=\"115\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071774820.png\" alt=\"rId48\" height=\"115\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Which two numbers should be interchanged to make the given equation correct? <br>17 &times; 7 + (21 &divide; 3) &times; 5 &ndash; 28 + 34 = 72 <br>(<strong>NOTE :</strong> Numbers must be interchanged and not the constituent digits e.g. if 2 and 3 are to be interchanged in the equation 43 &times; 3 + 4 &divide; 2, then interchanged equation is 43 &times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>21. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए? <br>17 &times; 7 + (21 &divide; 3) &times; 5 &ndash; 28 + 34 = 72 <br>(<strong>ध्यान दें : </strong>संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए। उदा. यदि समीकरण 43 &times; 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43 &times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: ["<p>21 and 28</p>", "<p>5 and 7</p>", 
                                "<p>17 and 28</p>", "<p>7 and 3</p>"],
                    options_hi: ["<p>21 और 28</p>", "<p>5 और 7</p>",
                                "<p>17 और 28</p>", "<p>7 और 3</p>"],
                    solution_en: "<p>21.(d) <strong>Given :-</strong> 17 &times; 7 + (21 <math display=\"inline\"><mo>&#247;</mo></math> 3) &times; 5 - 28 + 34 = 72<br>After going through all the options, option (d) satisfies. After interchanging 7 and 3 we get<br>17 &times; 3 + (21 <math display=\"inline\"><mo>&#247;</mo></math> 7) &times; 5 - 28 + 34<br>51 + 3 &times; 5 + 6<br>51 + 15 + 6 = 72<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>21.(d) <strong>दिया गया :- </strong>17 &times; 7 + (21 <math display=\"inline\"><mo>&#247;</mo></math> 3) &times; 5 - 28 + 34 = 72<br>सभी विकल्पों की जांच करने पर विकल्प (d) संतुष्ट करता है। 7 और 3 को आपस में बदलने पर हमें प्राप्त होता है<br>17 &times; 3 + (21 <math display=\"inline\"><mo>&#247;</mo></math> 7) &times; 5 - 28 + 34<br>51 + 3 &times; 5 + 6<br>51 + 15 + 6 = 72<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Which letter-cluster will replace the question mark (?) to complete the given series? UTHS, VRKO, ?, XNQG, YLTC</p>",
                    question_hi: "<p>22. दी गई शृंखला को पूरा करने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सा अक्षर-समूह आएगा?<br>UTHS, VRKO, ?, XNQG, YLTC</p>",
                    options_en: ["<p>TONK</p>", "<p>WONJ</p>", 
                                "<p>WPNK</p>", "<p>WPMN</p>"],
                    options_hi: ["<p>TONK</p>", "<p>WONJ</p>",
                                "<p>WPNK</p>", "<p>WPMN</p>"],
                    solution_en: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071776117.png\" alt=\"rId49\" width=\"422\" height=\"120\"></p>",
                    solution_hi: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071776117.png\" alt=\"rId49\" height=\"120\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. What should come in place of the question mark (?) in the given series ?<br>77, 88, 101, 116, ?</p>",
                    question_hi: "<p>23. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>77, 88, 101, 116, ?</p>",
                    options_en: ["<p>133</p>", "<p>131</p>", 
                                "<p>313</p>", "<p>331</p>"],
                    options_hi: ["<p>133</p>", "<p>131</p>",
                                "<p>313</p>", "<p>331</p>"],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071776399.png\" alt=\"rId50\" width=\"334\" height=\"120\"></p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071776399.png\" alt=\"rId50\" height=\"110\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. If &lsquo;E&rsquo; stands for &lsquo;&divide;&rsquo;, &lsquo;U&rsquo; stands for &lsquo;&times;&rsquo;, &lsquo;A&rsquo; stands for &lsquo;+&rsquo; and &lsquo;C&rsquo; stands for &lsquo;&ndash;&rsquo;, what will come in place of the question mark (?) in the following equation? <br>16 A 24 C 30 E 15 U 9 = ?</p>",
                    question_hi: "<p>24. यदि \'E\' का अर्थ \'&divide;\', \'U\' का अर्थ \'&times;\', \'A\' का अर्थ \'+\' और \'C\' का अर्थ \'&ndash;\' है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>16 A 24 C 30 E 15 U 9 = ?</p>",
                    options_en: ["<p>78</p>", "<p>32</p>", 
                                "<p>22</p>", "<p>66</p>"],
                    options_hi: ["<p>78</p>", "<p>32</p>",
                                "<p>22</p>", "<p>66</p>"],
                    solution_en: "<p>24.(c) <strong>Given :-</strong> 16 A 24 C 30 E 15 U 9<br>As per given instruction after interchanging the letter with sign we get<br>16 + 24 - 30 <math display=\"inline\"><mo>&#247;</mo></math> 15 &times; 9<br>40 - 2 &times; 9 = 22</p>",
                    solution_hi: "<p>24.(c) <strong>दिया गया :-</strong> 16 A 24 C 30 E 15 U 9<br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>16 + 24 - 30 <math display=\"inline\"><mo>&#247;</mo></math> 15 &times; 9<br>40 - 2 &times; 9 = 22</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Select the option figure in which the given figure is embedded as its part (rotation is NOT allowed)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071776809.png\" alt=\"rId51\" width=\"116\" height=\"120\"></p>",
                    question_hi: "<p>25. उस विकल्प आकृति का चयन करें जिसमें दी गई आकृति इसके भाग के रूप में अंतर्निहित है&nbsp;(घुमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071776809.png\" alt=\"rId51\" height=\"120\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071776926.png\" alt=\"rId52\" width=\"103\" height=\"120\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071777026.png\" alt=\"rId53\" height=\"120\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071777133.png\" alt=\"rId54\" height=\"120\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071777307.png\" alt=\"rId55\" height=\"120\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071776926.png\" alt=\"rId52\" height=\"120\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071777026.png\" alt=\"rId53\" height=\"120\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071777133.png\" alt=\"rId54\" height=\"120\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071777307.png\" alt=\"rId55\" height=\"120\"></p>"],
                    solution_en: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071777133.png\" alt=\"rId54\" width=\"101\" height=\"130\"></p>",
                    solution_hi: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734071777133.png\" alt=\"rId54\" height=\"130\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>