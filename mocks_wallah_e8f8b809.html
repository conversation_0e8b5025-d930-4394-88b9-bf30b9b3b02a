<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> In a certain code language, \'APPROACH\' is written as \'116161815138\' and &lsquo;APPROVAL&rsquo; </span><span style=\"font-family: Cambria Math;\">is written as &lsquo;11616181522112&rsquo;. How will \'POOL\' be written in that language?</span></p>\n",
                    question_hi: " <p>1. </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निश्चित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कोड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भाषा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\">, \'APPROACH\' </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> \'116161815138\' </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> \'APPROVAL\' </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> \'11616181522112\' </span><span style=\"font-family:Nirmala UI\">लिखा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भाषा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> \'POOL\' </span><span style=\"font-family:Nirmala UI\">कैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिखा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाएगा</span><span style=\"font-family:Cambria Math\"> ?</span></p>",
                    options_en: ["<p>16141412</p>\n", "<p>16171712</p>\n", 
                                "<p>16151512</p>\n", "<p>16131312</p>\n"],
                    options_hi: [" <p>  16141412</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p>  16171712</span></p>",
                                " <p>  16151512</span></p>", " <p>  16131312</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :</span><span style=\"font-family: Cambria Math;\"> Each letter is replaced by its place value in the alphabet.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">APPROACH </span><span style=\"font-family: Cambria Math;\"> 1-16-16-18-15-1-3-8 = 116161815138</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">APPROVAL </span><span style=\"font-family: Cambria Math;\"> 1-16-16-18-15-22-1-12 = 11616181522112</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, POOL </span><span style=\"font-family: Cambria Math;\"> 16-15-15-12 = 16151512</span></p>\n",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">1.(</span><span style=\"font-family:Cambria Math\">c) </span><span style=\"font-family:Nirmala UI\">तर्क</span><span style=\"font-family:Cambria Math\"> :</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वर्णमाला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">स्थानीय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिखा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\">  </span></p> <p><span style=\"font-family:Cambria Math\">APPROACH </span><span style=\"font-family:Cambria Math\"> 1 - 16 - 16 - 18 - 15 - 1 - 3 - </span><span style=\"font-family:Cambria Math\">8  =</span><span style=\"font-family:Cambria Math\"> 116161815138</span></p> <p><span style=\"font-family:Cambria Math\">APPROVAL </span><span style=\"font-family:Cambria Math\"> 1 - 16 - 16 - 18 - 15 - 22 - 1 - </span><span style=\"font-family:Cambria Math\">12  =</span><span style=\"font-family:Cambria Math\"> 11616181522112</span></p> <p><span style=\"font-family:Nirmala UI\">इसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तरह</span><span style=\"font-family:Cambria Math\">, POOL </span><span style=\"font-family:Cambria Math\"> 16 - 15 - 15 - 12 = 16151512</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Select the correct combination of mathematical signs to replace the * signs and to</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">balance the given equation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14 * 5 *41 * 76 * 35</span></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> * </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 14 * 5 * 41 * 76 * 35</span></p>\n",
                    options_en: ["<p>&times;<span style=\"font-family: Cambria Math;\">,&minus;,=,+</span></p>\n", "<p>&times;<span style=\"font-family: Cambria Math;\">,+,&minus;,=</span></p>\n", 
                                "<p>&minus;<span style=\"font-family: Cambria Math;\">,+,=,&times;</span></p>\n", "<p>+<span style=\"font-family: Cambria Math;\">,&times;,=,&minus;</span></p>\n"],
                    options_hi: ["<p>&times;<span style=\"font-family: Cambria Math;\"> , &minus; , = , +</span></p>\n", "<p>&times;<span style=\"font-family: Cambria Math;\"> , + , &minus; , =</span></p>\n",
                                "<p>&minus;<span style=\"font-family: Cambria Math;\"> , + , = , &times;</span></p>\n", "<p>+<span style=\"font-family: Cambria Math;\"> , &times; , =,&minus;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In this type of question, we will check by putting options one by one and doing so option (b) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14 * 5 * 41 * 76 * 35</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting value of option (b) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14&times;</span><span style=\"font-family: Cambria Math;\">&nbsp;5 + 41 - 76 = 35</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 14 &times; </span><span style=\"font-family: Cambria Math;\">5 + 41 - 76 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 70 + 41 - 76 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 111 - 76</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 35 = RHS</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (b) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14 * 5 * 41 * 76 * 35</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (b) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">14&times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 + 41 - 76 = 35</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 14&times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 + 41 - </span><span style=\"font-family: Cambria Math;\">76 =</span><span style=\"font-family: Cambria Math;\"> 70 + 41 - 76 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 111 - 76 = 35 = RHS</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Select the correct combination of mathematical signs to replace the * signs and to </span><span style=\"font-family: Cambria Math;\">balance the given equation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">78*3*12*5*59*17*44</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> * </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">78 * 3 * 12 * 5 * 59 * 17 * 44</span></p>\n",
                    options_en: ["<p>&divide;<span style=\"font-family: Cambria Math;\">,+, &times;,&minus;,+,=</span></p>\n", "<p>+<span style=\"font-family: Cambria Math;\">,=,&minus;,+,&times;,&divide;</span></p>\n", 
                                "<p>&divide;<span style=\"font-family: Cambria Math;\">,&minus;,=,+,&times;,&minus;</span></p>\n", "<p>=<span style=\"font-family: Cambria Math;\">,&minus;,&times;,+,&minus;,&times;</span></p>\n"],
                    options_hi: ["<p>&divide;<span style=\"font-family: Cambria Math;\">,+, &times;,&minus;,+,= </span></p>\n", "<p>+<span style=\"font-family: Cambria Math;\">,=,&minus;,+,&times;,&divide;</span></p>\n",
                                "<p>&divide;<span style=\"font-family: Cambria Math;\">,&minus;,=,+,&times;,&minus;</span></p>\n", "<p>=<span style=\"font-family: Cambria Math;\">,&minus;,&times;,+,&minus;,&times;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In this type of question, we will check by putting options one by one and doing so option (a) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">78*3*12*5*59*17*44</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting value of option (a) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">78 &divide;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 3 + 12 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 - 59 + 17 = 44</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 78 &divide;</span><span style=\"font-family: Cambria Math;\">3 + 12 &times; </span><span style=\"font-family: Cambria Math;\">5 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 26 + 12 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 26 + 60 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 103 - 59 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 44 = RHS</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (a) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">78*3*12*5*59*17*44</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (a) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">78 &divide;</span><span style=\"font-family: Cambria Math;\">3 + 12 &times;</span><span style=\"font-family: Cambria Math;\">5 - 59 + 17 = 44</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 78&divide;</span><span style=\"font-family: Cambria Math;\"> 3 + 12&times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 5 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 26 + 12&times;</span><span style=\"font-family: Cambria Math;\">&nbsp;5 - 59 + 17 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 26 + 60 - 59 + 17 = 103 - 59 = 44 = RHS</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: " <p>4. </span><span style=\"font-family:Cambria Math\">Which of the following numbers will replace the question mark (?) in the given series?</span></p> <p><span style=\"font-family:Cambria Math\">78, 112, 64</span><span style=\"font-family:Cambria Math\">, ?</span><span style=\"font-family:Cambria Math\">, 50, 140</span></p>",
                    question_hi: " <p>4. </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">श्रृंखला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चिह्न</span><span style=\"font-family:Cambria Math\"> (?) </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">स्थान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आएगी</span><span style=\"font-family:Cambria Math\"> ?</span></p> <p><span style=\"font-family:Cambria Math\">78 ,</span><span style=\"font-family:Cambria Math\"> 112 , 64 , ? , 50 , 140</span></p>",
                    options_en: [" <p>  134</span></p>", " <p>  126</span></p>", 
                                " <p>  108</span></p>", " <p>  116</span></p>"],
                    options_hi: [" <p>  134</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p>  126</span></p>",
                                " <p>  108</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p>  116</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">4.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image1.png\"/></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">4.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image1.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">In a certain code language, &ldquo;ARE&rdquo; is coded as &ldquo;87&rdquo;, and &ldquo;NOT&rdquo; is coded as &ldquo;4197&rdquo;. </span><span style=\"font-family: Cambria Math;\">How will &ldquo;CAT&rdquo; be coded in that language?</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, \"ARE\" </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \"87\" </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2337;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \"NOT\" </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \"4197\" </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2337;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> &ldquo;CAT&rdquo; </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2337;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>54</p>\n", "<p>60</p>\n", 
                                "<p>57</p>\n", "<p>63</p>\n"],
                    options_hi: ["<p>54</p>\n", "<p>60</p>\n",
                                "<p>57</p>\n", "<p>63</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :</span><span style=\"font-family: Cambria Math;\"> Product of numerical position of letters in alphabet less number of letters in given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ARE&rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 1 &times; </span><span style=\"font-family: Cambria Math;\">18 &times; </span><span style=\"font-family: Cambria Math;\">5 = 90 &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 90 - 3 = 87</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">NOT &rArr;</span><span style=\"font-family: Cambria Math;\"> 14&times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 15 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 20 = 4200 &rArr;</span><span style=\"font-family: Cambria Math;\"> 4200 - 3 = 4197</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, CAT &rArr;</span><span style=\"font-family: Cambria Math;\"> 3 &times;</span><span style=\"font-family: Cambria Math;\">1&nbsp; &times;</span><span style=\"font-family: Cambria Math;\"> 20 = 60 &rArr;</span><span style=\"font-family: Cambria Math;\"> 60 - 3 = 57</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> : ( </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2339;&#2344;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> ) - </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ARE &nbsp;&rArr; </span><span style=\"font-family: Cambria Math;\">1 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 18 &times; </span><span style=\"font-family: Cambria Math;\">5&nbsp; = 90 &nbsp;&rArr;</span><span style=\"font-family: Cambria Math;\"> 90 - 3 = 87</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">NOT &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 14 &times;</span><span style=\"font-family: Cambria Math;\">15 &times; </span><span style=\"font-family: Cambria Math;\">20 = 4200 &rArr; </span><span style=\"font-family: \'Cambria Math\';\">4200 - 3 = 4197</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\">, CAT &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 3 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 1 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 20 = 60 &nbsp;&rArr; </span><span style=\"font-family: Cambria Math;\">60 - 3 = 57</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Select the correct combination of mathematical signs to replace the * signs and to </span><span style=\"font-family: Cambria Math;\">balance the given equation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 * 5 * 3 * 3 * 2</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> * </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 * 5 * 3 * 3 * 2</span></p>\n",
                    options_en: ["<p>&ndash;<span style=\"font-family: Cambria Math;\">, =, +, &ndash;</span></p>\n", "<p>=<span style=\"font-family: Cambria Math;\">, +, +, &times;</span></p>\n", 
                                "<p>&times;<span style=\"font-family: Cambria Math;\">, =, &times;, &times;</span></p>\n", "<p>+<span style=\"font-family: Cambria Math;\">, =, &times;, &times;</span></p>\n"],
                    options_hi: ["<p>&ndash;<span style=\"font-family: Cambria Math;\">, =, +, &ndash;</span></p>\n", "<p>=<span style=\"font-family: Cambria Math;\">, +, +, &times;</span></p>\n",
                                "<p>&times;<span style=\"font-family: Cambria Math;\">, =, &times;, &times;</span></p>\n", "<p>+<span style=\"font-family: Cambria Math;\">, =, &times;, &times;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In this type of question, we will check by putting options one by one and doing so option (d) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 * 5 * 3 * 3 * 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting value of option (d) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 + 5 = 3 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 3 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS = 13 + 5 = 18</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">RHS = 3 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 3&times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2 = 18 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, LHS = RHS</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2305;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (d) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 * 5 * 3 * 3 * 2</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (d) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">13 + 5 = 3 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 3 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 13 + 5 = 18</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">RHS = 3 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 3 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 2 = 18 </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> LHS = RHS</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> Select the option that is related to the third word in the same way as the second word </span><span style=\"font-family: Cambria Math;\">is related to the first word. (The words must be considered as meaningful English </span><span style=\"font-family: Cambria Math;\">words and must NOT be related to each other based on the number of </span><span style=\"font-family: Cambria Math;\">letters/number </span><span style=\"font-family: Cambria Math;\">of consonants/vowels in the word)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Courage :</span><span style=\"font-family: Cambria Math;\"> Bravery :: Sinister : ?</span></p>\n",
                    question_hi: " <p>7. </span><span style=\"font-family:Nirmala UI\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> (</span><span style=\"font-family:Nirmala UI\">शब्दों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अर्थपूर्ण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अंग्रेजी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्दों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चाहिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षरों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">/</span><span style=\"font-family:Nirmala UI\">व्यंजनों</span><span style=\"font-family:Cambria Math\">/</span><span style=\"font-family:Nirmala UI\">स्वरों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आधार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चाहिए</span><span style=\"font-family:Cambria Math\">)</span></p> <p><span style=\"font-family:Nirmala UI\">साहस</span><span style=\"font-family:Cambria Math\"> :</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शौर्य</span><span style=\"font-family:Cambria Math\"> :: </span><span style=\"font-family:Nirmala UI\">अशुभ</span><span style=\"font-family:Cambria Math\"> : ?</span></p>",
                    options_en: ["<p>Auspicious</p>\n", "<p>Benevolent</p>\n", 
                                "<p>Ominous</p>\n", "<p>Kind</p>\n"],
                    options_hi: [" <p>  </span><span style=\"font-family:Nirmala UI\">शुभ</span></p>", " <p>  </span><span style=\"font-family:Nirmala UI\">परोपकारी</span></p>",
                                " <p>  </span><span style=\"font-family:Nirmala UI\">मनहूस</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p>  </span><span style=\"font-family:Nirmala UI\">दयालु</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Synonym of Courage is Bravery. Similarly, the synonym of Sinister is Ominous.</span></p>\n",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">7.(</span><span style=\"font-family:Cambria Math\">c) </span><span style=\"font-family:Nirmala UI\">साहस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर्यायवाची</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शौर्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अशुभ</span><span style=\"font-family:Cambria Math\"> (Sinister) </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर्यायवाची</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मनहूस</span><span style=\"font-family:Cambria Math\"> (Ominous) </span><span style=\"font-family:Nirmala UI\">होता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: " <p>8.</span><span style=\"font-family:Cambria Math\"> A paper is folded and cut as shown. How will it appear when unfolded?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image2.png\"/></p>",
                    question_hi: " <p>8. </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कागज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मोड़ा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिखाए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुसार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">काटा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रकट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कैसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिखाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">देगा</span><span style=\"font-family:Cambria Math\">?</span></p> <p><span style=\"font-family:Cambria Math\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image2.png\"/></p>",
                    options_en: [" <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image3.png\"/></p>", " <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image4.png\"/></p>", 
                                " <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image5.png\"/></p>", " <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image6.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image3.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image4.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image5.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image6.png\"/></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">8.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image4.png\"/></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">8.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><span style=\"font-family:Cambria Math\">   </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image4.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Which of the following numbers will replace the question mark (?) in the given number </span><span style=\"font-family: Cambria Math;\">series?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">458, 462, 446, 482</span><span style=\"font-family: Cambria Math;\">, ?</span><span style=\"font-family: Cambria Math;\">, 518</span></p>\n",
                    question_hi: " <p>9. </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">श्रृंखला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रश्नवाचक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चिह्न</span><span style=\"font-family:Cambria Math\"> (?) </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">स्थान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लेगी</span><span style=\"font-family:Cambria Math\">?</span></p> <p><span style=\"font-family:Cambria Math\">458 ,</span><span style=\"font-family:Cambria Math\"> 462 , 446 , 482 , ? , 518</span></p>",
                    options_en: ["<p>428</p>\n", "<p>432</p>\n", 
                                "<p>546</p>\n", "<p>418</p>\n"],
                    options_hi: [" <p>  428</span></p>", " <p>  432</span><span style=\"font-family:Cambria Math\"> </span></p>",
                                " <p>  546</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p>  418</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image7.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">9.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">      </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image7.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Select the option that represents the correct order of the given words as they would </span><span style=\"font-family: Cambria Math;\">appear in an English dictionary.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.Potential&nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">2.Posthumous</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.Postbox&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">4.Polymer</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5.Popular</span></p>\n",
                    question_hi: "<p>10. <span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;&#2325;&#2379;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.Potential&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">2.Posthumous</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.Postbox&nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">4.Polymer</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5.Popular</span></p>\n",
                    options_en: ["<p>5,4,3,2,1</p>\n", "<p>4,5,2,3,1</p>\n", 
                                "<p>4,5,3,2,1</p>\n", "<p>1,2,3,4,5</p>\n"],
                    options_hi: ["<p>5,4,3,2,1</p>\n", "<p>4,5,2,3,1</p>\n",
                                "<p>4,5,3,2,1</p>\n", "<p>1,2,3,4,5</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Correct order will </span><span style=\"font-family: Cambria Math;\">be :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Polymer &nbsp;&rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Popular &rarr; </span><span style=\"font-family: Cambria Math;\">Postbox &rarr; </span><span style=\"font-family: Cambria Math;\">Posthumous &rarr; </span><span style=\"font-family: Cambria Math;\">Potential </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">c) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">: Polymer &rarr;</span><span style=\"font-family: Cambria Math;\"> Popular &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Postbox&nbsp; &rarr;</span><span style=\"font-family: Cambria Math;\"> Posthumous &nbsp;&rarr;&nbsp; </span><span style=\"font-family: Cambria Math;\">Potential</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: " <p>11. </span><span style=\"font-family:Cambria Math\">Select the figure from among the given options that can replace the question mark (?) in the following series.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image8.png\"/></p>",
                    question_hi: " <p>11.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विकल्पों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आकृति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">श्रृंखला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रश्नवाचक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चिन्ह</span><span style=\"font-family:Cambria Math\"> (?) </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतिस्थापित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image8.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image9.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image10.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image11.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image12.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image9.png\"/><span style=\"font-family:Cambria Math\">   </span></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image10.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image11.png\"/><span style=\"font-family:Cambria Math\">  </span></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image12.png\"/></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">11.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image12.png\"/><span style=\"font-family:Cambria Math\"> </span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">11.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image12.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> Select the option that represents the correct order of the given words as they would</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">appear in an English dictionary.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.Sourced&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">2.Sorrow</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.Soulful&nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">4.Soaking</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5.Somewhere</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;&#2325;&#2379;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1.Sourced&nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">2.Sorrow</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3.Soulful&nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">4.Soaking</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5.Somewhere</span></p>\n",
                    options_en: ["<p>5<span style=\"font-family: Cambria Math;\">,4,3,2,1</span></p>\n", "<p>4<span style=\"font-family: Cambria Math;\">,5,2,3,1</span></p>\n", 
                                "<p>5<span style=\"font-family: Cambria Math;\">,4,1,2,3</span></p>\n", "<p>4<span style=\"font-family: Cambria Math;\">,5,3,2,1</span></p>\n"],
                    options_hi: ["<p>5,4,3,2,1</p>\n", "<p>4,5,2,3,1</p>\n",
                                "<p>5,4,1,2,3</p>\n", "<p>4,5,3,2,1</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Correct order will </span><span style=\"font-family: Cambria Math;\">be :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Soaking &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Somewhere&nbsp; &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Sorrow &nbsp; &rarr; </span><span style=\"font-family: Cambria Math;\">Soulful &nbsp; &rarr; </span><span style=\"font-family: Cambria Math;\">Sourced</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">b) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">: Soaking &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Somewhere &nbsp;&rarr;</span><span style=\"font-family: Cambria Math;\">Sorrow &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Soulful &rarr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> Sourced</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> Select the correct combination of mathematical signs to sequentially replace the * </span><span style=\"font-family: Cambria Math;\">signs and to balance the given equation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57*35*44*11*13*40</span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> * </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2350;&#2366;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57 * 35 * 44 * 11* 13 * 40</span></p>\n",
                    options_en: ["<p>+<span style=\"font-family: Cambria Math;\">,&ndash;, &times;,&divide;, =</span></p>\n", "<p>&times;<span style=\"font-family: Cambria Math;\">, +, &ndash;,&divide;, =</span></p>\n", 
                                "<p>&ndash;<span style=\"font-family: Cambria Math;\">,+,&times;,&divide;, =</span></p>\n", "<p>+<span style=\"font-family: Cambria Math;\">, &ndash;, &divide;,&times;, =</span></p>\n"],
                    options_hi: ["<p>+<span style=\"font-family: Cambria Math;\">,&ndash;, &times;,&divide;, =</span></p>\n", "<p>&times;<span style=\"font-family: Cambria Math;\">, +, &ndash;,&divide;, =</span></p>\n",
                                "<p>&ndash;<span style=\"font-family: Cambria Math;\">,+,&times;,&divide;, =</span></p>\n", "<p>+<span style=\"font-family: Cambria Math;\">, &ndash;, &divide;,&times;, =</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">In this type of question, we will check by putting options one by one and doing so option (d) gets satisfied.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57*35*44*11*13*40</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Putting value of option (d) in above expression, we get</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57 + 35 - 44&nbsp; &divide;</span><span style=\"font-family: Cambria Math;\"> 11 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 13 = 40</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 57 + 35 - 44 &divide; </span><span style=\"font-family: Cambria Math;\">11 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 13 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 57 + 35 - 4 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 13 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 57 + 35 - 52</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 92 - 52 &nbsp;&rArr;</span><span style=\"font-family: Cambria Math;\">&nbsp;40 = RHS </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">d) </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2305;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2320;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (d) </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2340;&#2369;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57*35*44*11*13*40</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2306;&#2332;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (d) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">57 + 35 - 44 &divide;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 11 &times; </span><span style=\"font-family: Cambria Math;\">13 = 40</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">LHS </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 57 + 35 - 44 &divide;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 11 &times; </span><span style=\"font-family: Cambria Math;\">13 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 57 + 35 - 4 &times;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 13 = 57 + 35 - 52 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 92 - 52 = 40 = RHS </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Two statements are given followed by two conclusions numbered I and II. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Statements:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Some potatoes are onions.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Some onions are tomatoes.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Conclusions:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. Some tomatoes are potatoes.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">II. No tomato is a potato.</span></p>\n",
                    question_hi: " <p>14.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कथनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> I </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> II </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कथनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सत्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मानते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हुए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">भले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सामान्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तथ्यों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतीत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हों</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">निर्णय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कथनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तार्किक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span></p> <p><span style=\"font-family:Nirmala UI\">कथन</span><span style=\"font-family:Cambria Math\">:</span></p> <p><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पोटैटो</span><span style=\"font-family:Cambria Math\"> ,</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनियन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Nirmala UI\">।</span></p> <p><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनियन</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">टोमेटो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Nirmala UI\">।</span></p> <p><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\">:</span></p> <p><span style=\"font-family:Cambria Math\">I. </span><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">टोमेटो</span><span style=\"font-family:Cambria Math\">,  </span><span style=\"font-family:Nirmala UI\">पोटैटो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Nirmala UI\">।</span></p> <p><span style=\"font-family:Cambria Math\">II. </span><span style=\"font-family:Nirmala UI\">कोई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">टोमेटो</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">पोटैटो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span></p>",
                    options_en: ["<p>Only conclusion II follows</p>\n", "<p>Only conclusion I follows</p>\n", 
                                "<p>Either conclusion I or II follows</p>\n", "<p>Both conclusions I and II follow</p>\n"],
                    options_hi: [" <p> </span><span style=\"font-family:Nirmala UI\">केवल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> II </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span></p>", " <p> </span><span style=\"font-family:Nirmala UI\">केवल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> I </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span></p>",
                                " <p> </span><span style=\"font-family:Nirmala UI\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> I </span><span style=\"font-family:Nirmala UI\">या</span><span style=\"font-family:Cambria Math\"> II </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span></p>", " <p> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> I </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> II </span><span style=\"font-family:Nirmala UI\">दोनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span></p>"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image13.png\"><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Clearly, we can see that either conclusion 1 or 2 follows.</span></p>\n",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image13.png\"/></p> <p><span style=\"font-family:Nirmala UI\">स्पष्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">हम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">देख</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सकते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निष्कर्ष</span><span style=\"font-family:Cambria Math\"> 1 </span><span style=\"font-family:Nirmala UI\">या</span><span style=\"font-family:Cambria Math\"> 2 </span><span style=\"font-family:Nirmala UI\">अनुसरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: " <p>15.</span><span style=\"font-family:Cambria Math\"> Select the correct mirror image of the given combination when the mirror is placed at MN as shown.</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image14.png\"/></p>",
                    question_hi: " <p>15. </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संयोजन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दर्पण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतिबिंब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करें</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">जब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दर्पण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> MN </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिखाया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image14.png\"/></p>",
                    options_en: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image15.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image16.png\"/></p>", 
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image17.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image18.png\"/></p>"],
                    options_hi: [" <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image15.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image16.png\"/></p>",
                                " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image17.png\"/></p>", " <p> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image18.png\"/></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">15.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image18.png\"/></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">15.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image18.png\"/></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16.<span style=\"font-family: Cambria Math;\"> Which of the following letter-clusters will replace the question mark (?) in the given </span><span style=\"font-family: Cambria Math;\">series to make it logically complete?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">XUR, WTQ, VSP, URO</span><span style=\"font-family: Cambria Math;\">, ?</span></p>\n",
                    question_hi: "<p>16.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2370;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">XUR ,</span><span style=\"font-family: Cambria Math;\"> WTQ , VSP , URO , ?</span></p>\n",
                    options_en: ["<p>TRP</p>\n", "<p>TQN</p>\n", 
                                "<p>SQO</p>\n", "<p>SPM</p>\n"],
                    options_hi: ["<p>TRP</p>\n", "<p>TQN</p>\n",
                                "<p>SQO</p>\n", "<p>SPM</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For first letter of each </span><span style=\"font-family: Cambria Math;\">word :</span><span style=\"font-family: Cambria Math;\"> X - 1 = W, W - 1 = V, V - 1 = U, U - 1 = </span><span style=\"font-family: Cambria Math;\">T</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For second letter of each </span><span style=\"font-family: Cambria Math;\">word :</span><span style=\"font-family: Cambria Math;\"> U - 1 = T, T - 1 = S, S - 1 = R, R - 1 = </span><span style=\"font-family: Cambria Math;\">Q</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For third letter of each </span><span style=\"font-family: Cambria Math;\">word ;</span><span style=\"font-family: Cambria Math;\"> R - 1 = Q, Q - 1 = P, P - 1 = O, O - 1 = </span><span style=\"font-family: Cambria Math;\">N</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Hence, we get TQN.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">: X - 1 = </span><span style=\"font-family: Cambria Math;\">W ,</span><span style=\"font-family: Cambria Math;\"> W - 1 = V , V - 1 = U , U - 1 = </span><span style=\"font-family: Cambria Math;\">T</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> U -1 = T , T - 1 = S , S-1 = R , R - 1 = </span><span style=\"font-family: Cambria Math;\">Q</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2348;&#2381;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">; R - 1 = </span><span style=\"font-family: Cambria Math;\">Q ,</span><span style=\"font-family: Cambria Math;\"> Q - 1 = P , P - 1 = O , O - 1 = </span><span style=\"font-family: Cambria Math;\">N</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> TQN </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17.<span style=\"font-family: Cambria Math;\"> Select the option that is related to the third number in the same way as the second number is related to the first number and the sixth number is related to the fifth number.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 :</span><span style=\"font-family: Cambria Math;\"> 0 :: 12 : ? :: 16 : 3</span></p>\n",
                    question_hi: "<p>17.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2336;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2306;&#2330;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 :</span><span style=\"font-family: Cambria Math;\"> 0 :: 12 : ? :: 16 : 3</span></p>\n",
                    options_en: ["<p>4</p>\n", "<p>5</p>\n", 
                                "<p>3</p>\n", "<p>1</p>\n"],
                    options_hi: ["<p>4</p>\n", "<p>5</p>\n",
                                "<p>3</p>\n", "<p>1</p>\n"],
                    solution_en: "<p>17.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&times;</mo><mn>0</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>0</mn><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mfrac><mrow><mn>1</mn><mo>&times;</mo><mn>6</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>3</mn></math></p>\r\n<p>similarly&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&times;</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math> = 1</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">d) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>&times;</mo><mn>0</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>0</mn><mo>&nbsp;</mo><mo>,</mo><mfrac><mrow><mn>1</mn><mo>&times;</mo><mn>6</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>3</mn><mspace linebreak=\"newline\"></mspace><mi>&#2311;&#2360;&#2368;</mi><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>1</mn></math></span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: " <p>18. </span><span style=\"font-family:Cambria Math\">Select the option figure that will replace the question mark (?) in the figure given below to complete the pattern.</span></p> <p><span style=\"font-family:Cambria Math\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image19.png\"/><span style=\"font-family:Cambria Math\"> </span></p>",
                    question_hi: " <p>18.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विकल्प</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पैटर्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पूरा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नीचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आकृति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चिह्न</span><span style=\"font-family:Cambria Math\"> (?) </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतिस्थापित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करेगा</span><span style=\"font-family:Nirmala UI\">।</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image19.png\"/></p>",
                    options_en: [" <p>   </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image20.png\"/><span style=\"font-family:Cambria Math\"> </span></p>", " <p>   </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image21.png\"/><span style=\"font-family:Cambria Math\"> </span></p>", 
                                " <p>   </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image22.png\"/><span style=\"font-family:Cambria Math\"> </span></p>", " <p>   </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image23.png\"/><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image20.png\"/></p>", " <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image21.png\"/></p>",
                                " <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image22.png\"/></p>", " <p>  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image23.png\"/></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">18.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><span style=\"font-family:Cambria Math\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image21.png\"/><span style=\"font-family:Cambria Math\"> </span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">18.(</span><span style=\"font-family:Cambria Math\">b)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image21.png\"/></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. <span style=\"font-family: Cambria Math;\">Select the correct mirror image of the given combination when the mirror is placed at MN as shown.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image24.png\"></p>\n",
                    question_hi: "<p>19. <span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2379;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2346;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> MN </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image24.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image25.png\"></p>\n", "<p><span style=\"font-family: Cambria Math;\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image26.png\"></p>\n", 
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_43359463511669609649859.jpg\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image28.png\"></p>\n"],
                    options_hi: ["<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image25.png\"><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image26.png\"></p>\n",
                                "<p><img src=\"https://ssccglpinnacle.com/images/mceu_43359463511669609649859.jpg\"></p>\n", "<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image28.png\"></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_43359463511669609649859.jpg\"></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">44.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_43359463511669609649859.jpg\"></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: " <p>20.</span><span style=\"font-family:Cambria Math\"> Which of the following numbers will replace the question mark (?) in the given series?</span></p> <p><span style=\"font-family:Cambria Math\">11, 20, 43, 94, 187, 336, 555</span><span style=\"font-family:Cambria Math\">, ?</span></p>",
                    question_hi: " <p>20. </span><span style=\"font-family:Nirmala UI\">निम्नलिखित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">श्रृंखला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चिह्न</span><span style=\"font-family:Cambria Math\"> (?) </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">स्थान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लेगी</span><span style=\"font-family:Cambria Math\">?</span></p> <p><span style=\"font-family:Cambria Math\">11 ,</span><span style=\"font-family:Cambria Math\"> 20 , 43 , 94 , 187 , 336 , 555 , ?</span></p>",
                    options_en: [" <p>  777</span></p>", " <p>  888</span></p>", 
                                " <p>  858</span></p>", " <p>  758</span></p>"],
                    options_hi: [" <p>  777</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p>  888</span></p>",
                                " <p>  858</span></p>", " <p>  758</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">20.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\"> </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image29.png\"/><span style=\"font-family:Cambria Math\"> </span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">20.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image29.png\"/></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21.<span style=\"font-family: Cambria Math;\"> Study the given pattern carefully and select the number that can replace the question mark (?) in it.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">First row:&nbsp; &nbsp; 73, 52, 75</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Second row:64, 41, 63</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Third row:&nbsp; &nbsp;68, 47</span><span style=\"font-family: Cambria Math;\">,&nbsp; &nbsp;?</span></p>\n",
                    question_hi: "<p>21.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2376;&#2335;&#2352;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2381;&#2351;&#2366;&#2344;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2357;&#2366;&#2330;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> (?) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2375;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">73 52</span><span style=\"font-family: Cambria Math;\"> 75</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">64 41</span><span style=\"font-family: Cambria Math;\"> 63</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">68 47</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>71</p>\n", "<p>69</p>\n", 
                                "<p>63</p>\n", "<p>65</p>\n"],
                    options_hi: ["<p>71</p>\n", "<p>69</p>\n",
                                "<p>63</p>\n", "<p>65</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>L</mi><mi>o</mi><mi>g</mi><mi>i</mi><mi>c</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>I</mi><mi>n</mi><mo>&nbsp;</mo><mi>r</mi><mi>o</mi><mi>w</mi><mo>,</mo><mo>&nbsp;</mo><mfrac><mrow><mo>[</mo><mi>I</mi><mi>s</mi><mi>t</mi><mo>&nbsp;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo><mo>]</mo></mrow><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mi>r</mi><mi>d</mi><mo>&nbsp;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&nbsp;</mo></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For 1st Row = 73 + 52 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 25 = 25 &times; </span><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Cambria Math;\">= 75</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For 2nd Row = 64 + 41 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 21= 21 &times;</span><span style=\"font-family: Cambria Math;\"> 3</span><span style=\"font-family: Cambria Math;\"> = 63</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">For 3</span><span style=\"font-family: Cambria Math;\">rd Row</span><span style=\"font-family: Cambria Math;\"> = 68 + 47 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 23 = 23 &times;</span><span style=\"font-family: Cambria Math;\"> 3</span><span style=\"font-family: Cambria Math;\"> = 69</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mi>&#2340;&#2352;&#2381;&#2325;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mo>[</mo><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2366;</mi><mo>&nbsp;</mo><mi>&#2344;&#2306;&#2348;&#2352;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>&#2342;&#2370;&#2360;&#2352;&#2366;</mi><mo>&nbsp;</mo><mi>&#2344;&#2306;&#2348;&#2352;</mi><mo>&nbsp;</mo></mrow><mn>5</mn></mfrac><mo>]</mo><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mi>r</mi><mi>d</mi><mo>&nbsp;</mo><mi>&#2344;&#2306;&#2348;&#2352;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo></math></span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> = 73 + 52 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 25 = 25 &times; </span><span style=\"font-family: Cambria Math;\">3 </span><span style=\"font-family: Cambria Math;\">= 75</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> = 64 + 41 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>105</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 21= 21 &times;</span><span style=\"font-family: Cambria Math;\"> 3</span><span style=\"font-family: Cambria Math;\"> = 63</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> = 68 + </span><span style=\"font-family: Cambria Math;\">47 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>115</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 23 = 23 &times;</span><span style=\"font-family: Cambria Math;\"> 3</span><span style=\"font-family: Cambria Math;\"> = 69</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: " <p>22.</span><span style=\"font-family:Cambria Math\"> Which letter cluster will replace the question mark (?) to complete the given series?</span></p> <p><span style=\"font-family:Cambria Math\">ANGE, DSBB, GXWY</span><span style=\"font-family:Cambria Math\">, ?</span></p>",
                    question_hi: " <p>22.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">श्रृंखला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पूरा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">समूह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चिह्न</span><span style=\"font-family:Cambria Math\"> (?) </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतिस्थापित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करेगा</span><span style=\"font-family:Cambria Math\">?</span></p> <p><span style=\"font-family:Cambria Math\">ANGE ,</span><span style=\"font-family:Cambria Math\"> DSBB , GXWY , ?</span></p>",
                    options_en: [" <p>  JBRV</span></p>", " <p>  JCSV</span></p>", 
                                " <p>  JBSV</span></p>", " <p>  JCRV</span></p>"],
                    options_hi: [" <p>  JBRV</span><span style=\"font-family:Cambria Math\">   </span></p>", " <p>  JCSV</span><span style=\"font-family:Cambria Math\">   </span></p>",
                                " <p>  JBSV</span><span style=\"font-family:Cambria Math\">   </span></p>", " <p>  JCRV</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">22.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">For first letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> A + 3 = D, D + 3 = G, G + 3 = </span><span style=\"font-family:Cambria Math\">J</span></p> <p><span style=\"font-family:Cambria Math\">For second letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> N + 5 = S, S + 5 = X, X + 5 = </span><span style=\"font-family:Cambria Math\">C</span></p> <p><span style=\"font-family:Cambria Math\">For third letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> G - 5 = B, B - 5 = W, W - 5 = </span><span style=\"font-family:Cambria Math\">R</span></p> <p><span style=\"font-family:Cambria Math\">For fourth letter of each </span><span style=\"font-family:Cambria Math\">word :</span><span style=\"font-family:Cambria Math\"> E - 3 = B, B - 3 = Y, Y - 3 = </span><span style=\"font-family:Cambria Math\">V</span></p> <p><span style=\"font-family:Cambria Math\">Hence, we get JCRV.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">22.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\">: A + 3 = </span><span style=\"font-family:Cambria Math\">D ,</span><span style=\"font-family:Cambria Math\"> D + 3 = G , G + 3 = </span><span style=\"font-family:Cambria Math\">J</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दूसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> :</span><span style=\"font-family:Cambria Math\">  N + 5 = S , S + 5 = X , X + 5 = </span><span style=\"font-family:Cambria Math\">C</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीसरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> :</span><span style=\"font-family:Cambria Math\"> G - 5 = B , B - 5 = W , W - 5 = </span><span style=\"font-family:Cambria Math\">R</span></p> <p><span style=\"font-family:Nirmala UI\">प्रत्येक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शब्द</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चौथे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अक्षर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> :</span><span style=\"font-family:Cambria Math\"> E - 3 = B , B - 3 = Y , Y - 3 = </span><span style=\"font-family:Cambria Math\">V</span></p> <p><span style=\"font-family:Nirmala UI\">इसलिए</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">हमें</span><span style=\"font-family:Cambria Math\"> JCRV </span><span style=\"font-family:Nirmala UI\">मिलता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. <span style=\"font-family: Cambria Math;\">In a certain code language, \'RAIN\' is written as \'182915\' and \'SUN\' is written as \'192214\'. How will \'MOON\' be written in that language?</span></p>\n",
                    question_hi: "<p>23.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, \'RAIN\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'182915\' </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> \'SUN\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> \'192214\' </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> \'MOON\' </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>13161415</p>\n", "<p>13161616</p>\n", 
                                "<p>13161515</p>\n", "<p>13161414</p>\n"],
                    options_hi: ["<p>13161415</p>\n", "<p>13161616</p>\n",
                                "<p>13161515</p>\n", "<p>13161414</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Logic :</span><span style=\"font-family: Cambria Math;\"> Letters at odd places are replaced by their place value in alphabet while letters at even places are replaced by their place value in alphabet plus one.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">RAIN </span><span style=\"font-family: Cambria Math;\"> (18</span><span style=\"font-family: Cambria Math;\">) ,</span><span style=\"font-family: Cambria Math;\"> (1+</span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">) , (9) , (14+</span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">) &rArr;</span><span style=\"font-family: Cambria Math;\"> 18-2-9-15</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SUN </span><span style=\"font-family: Cambria Math;\"> (19</span><span style=\"font-family: Cambria Math;\">) ,</span><span style=\"font-family: Cambria Math;\"> (21+</span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">) , (14) &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 19-22-14</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Similarly, MOON &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> (13</span><span style=\"font-family: Cambria Math;\">) ,</span><span style=\"font-family: Cambria Math;\"> (15+</span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">) , (15) , (14+</span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">)&rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 13-16-15-15 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\">:-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2359;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2339;&#2350;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2359;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2339;&#2350;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> +1 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">RAIN </span><span style=\"font-family: Cambria Math;\"> (18</span><span style=\"font-family: Cambria Math;\">) ,</span><span style=\"font-family: Cambria Math;\"> (1 + </span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">) , (9) , (14 + </span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">)&rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 18 - 2 - 9 - 15</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SUN </span><span style=\"font-family: Cambria Math;\"> (19</span><span style=\"font-family: Cambria Math;\">) ,</span><span style=\"font-family: Cambria Math;\"> (21+</span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">) , (14) &rArr;&nbsp;</span><span style=\"font-family: Cambria Math;\"> 19 - 22 - 14</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, MOON </span><span style=\"font-family: Cambria Math;\"> (13</span><span style=\"font-family: Cambria Math;\">) ,</span><span style=\"font-family: Cambria Math;\"> (15 + </span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">) , (15) , (14 + </span><span style=\"font-family: Cambria Math;\">1</span><span style=\"font-family: Cambria Math;\">) &rArr;</span><span style=\"font-family: Cambria Math;\"> 13 - 16 - 15 - 15</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: " <p>24. </span><span style=\"font-family:Cambria Math\">The second number in the given number pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) are followed in all the number pairs, EXCEPT one. Find that odd number pair.</span></p>",
                    question_hi: "<p>24.<span style=\"font-family: Cambria Math;\"> <span style=\"font-weight: 400;\">&#2342;&#2368; &#2327;&#2312; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2351;&#2369;&#2327;&#2381;&#2350;&#2379;&#2306; &#2350;&#2375;&#2306; &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2346;&#2361;&#2354;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2346;&#2352; &#2325;&#2369;&#2331; &#2327;&#2339;&#2367;&#2340;&#2368;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2305; &#2325;&#2352;&#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404; &#2319;&#2325; &#2325;&#2379; &#2331;&#2379;&#2337;&#2364;&#2325;&#2352; &#2360;&#2349;&#2368; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2351;&#2369;&#2327;&#2381;&#2350;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2350;&#2366;&#2344; &#2360;&#2306;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366; &#2325;&#2366; &#2309;&#2344;&#2369;&#2360;&#2352;&#2339; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2357;&#2361; &#2357;&#2367;&#2359;&#2350; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2351;&#2369;&#2327;&#2381;&#2350; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></span></p>\n",
                    options_en: [" <p>  21</span><span style=\"font-family:Cambria Math\"> : 431</span></p>", " <p>  15</span><span style=\"font-family:Cambria Math\"> : 225</span></p>", 
                                " <p>  13</span><span style=\"font-family:Cambria Math\"> : 169</span></p>", " <p>  12</span><span style=\"font-family:Cambria Math\"> : 144</span></p>"],
                    options_hi: ["<p>21<span style=\"font-family: Cambria Math;\"> : 431</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">15 :</span><span style=\"font-family: Cambria Math;\"> 225</span></p>\n",
                                "<p>13<span style=\"font-family: Cambria Math;\"> : 169</span></p>\n", "<p>12<span style=\"font-family: Cambria Math;\"> : 144</span></p>\n"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">24.(</span><span style=\"font-family:Cambria Math\">a)</span></p> <p><span style=\"font-family:Cambria Math\">Logic :</span><span style=\"font-family:Cambria Math\"> Except option (a), in all other options the second number is the square of the first number.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">a) </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\">:</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> (a) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2379;&#2337;&#2364;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25. </span><span style=\"font-family:Cambria Math\">Two different positions of the same dice are shown, the six faces of which are numbered from 1 to 6. Select the number that will be on the top if the dice is resting on ‘6’.</span></p> <p><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">  </span><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image30.png\"/><span style=\"font-family:Cambria Math\"> </span></p>",
                    question_hi: " <p>25.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पासे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अलग</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">अलग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">स्थान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">स्थिति</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिखाए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">जिसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">फलक</span><span style=\"font-family:Cambria Math\"> 1 </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> 6 </span><span style=\"font-family:Nirmala UI\">तक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्यांकित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यदि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पासा</span><span style=\"font-family:Cambria Math\"> \'6\' </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">टिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हुआ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चयन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पासे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शीर्ष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> ?</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669284859/word/media/image30.png\"/></p>",
                    options_en: [" <p>  3</span></p>", " <p>  2</span></p>", 
                                " <p>  1</span></p>", " <p>  4</span></p>"],
                    options_hi: [" <p>  3</span></p>", " <p>  2</span></p>",
                                " <p>  1</span></p>", " <p>  4</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">25.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">From both the dice given in question, option (a), (b) and (d) get eliminated, so the number that will be on the top if the dice is resting on ‘6’ is ‘1’.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">25.(</span><span style=\"font-family:Cambria Math\">c) </span><span style=\"font-family:Nirmala UI\">प्रश्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दोनों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पासों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विकल्प</span><span style=\"font-family:Cambria Math\"> (a) , (b) </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> (d)  </span><span style=\"font-family:Nirmala UI\">छट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> , </span><span style=\"font-family:Nirmala UI\">इसलिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यदि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पासा</span><span style=\"font-family:Cambria Math\"> \'6\' </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">टिका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हुआ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सबसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ऊपर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> \'1\' </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
           // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${question.question_en}</div>
                        <div class="hi" style="display:none">${question.question_hi}</div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                        <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>