<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">60:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the set in which the numbers are related in the same way as are the numbers of the following set.<br>(79, 58, 62)<br>(54, 33, 37)<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding / deleting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>1. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित समुच्चयों की संख्याएँ संबंधित हैं।<br>(79, 58, 62)<br>(54, 33, 37)<br>(<strong>नोट : </strong>पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>(59, 33, 43)</p>",
                        "<p>(47, 36, 41)</p>",
                        "<p>(66, 45, 49)</p>",
                        "<p>(43, 25, 30)</p>"
                    ],
                    options_hi: [
                        "<p>(59, 33, 43)</p>",
                        "<p>(47, 36, 41)</p>",
                        "<p>(66, 45, 49)</p>",
                        "<p>(43, 25, 30)</p>"
                    ],
                    solution_en: "<p>1.(c) <strong>Logic :- </strong>(1st number - 2nd number) = 21, (3rd number - 2nd number) = 4<br>(79, 58, 62) :- (79 - 58) = 21, (62 - 58) = 4<br>(54, 33, 37) :- (54 - 33) = 21, (37 - 33) = 4<br>Similarly,<br>(66, 45, 49) :- (66 - 45) = 21, (49 - 45) = 4</p>",
                    solution_hi: "<p>1.(c) <strong>तर्क :-</strong> (पहली संख्या - दूसरी संख्या) = 21, (तीसरी संख्या - दूसरी संख्या) = 4<br>(79, 58, 62) :- (79 - 58) = 21, (62 - 58) = 4<br>(54, 33, 37) :- (54 - 33) = 21, (37 - 33) = 4<br>इसी प्रकार,<br>(66, 45, 49) :- (66 - 45) = 21, (49 - 45) = 4</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Which letter cluster will replace the question mark (?) to complete the given series ?<br>YTUQ, XVRU, ?, VZLC, UBIG</p>",
                    question_hi: "<p>2. विकल्पों में से कौन सा अक्षर-समूह प्रश्नवाचक चिह्न (?) को प्रतिस्थापित करके दी गई श्रेणी को पूरा करेगा ?<br>YTUQ, XVRU, ?, VZLC, UBIG</p>",
                    options_en: [
                        "<p>WXPY</p>",
                        "<p>WYOX</p>",
                        "<p>WXOY</p>",
                        "<p>WYOY</p>"
                    ],
                    options_hi: [
                        "<p>WXPY</p>",
                        "<p>WYOX</p>",
                        "<p>WXOY</p>",
                        "<p>WYOY</p>"
                    ],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782934082.png\" alt=\"rId4\" width=\"320\" height=\"104\"></p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782934082.png\" alt=\"rId4\" width=\"320\" height=\"104\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, \'SAND\' is coded as \'2468\' and \'WEST\' is coded as \'3574\'. What is the code for \'S\' in the given code language ?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में, \'SAND\' को \'2468\' के रूप में कूटबद्ध किया जाता है और \'WEST\' को \'3574\' के रूप में कूटबद्ध किया जाता है। दी गई कूट भाषा में \'S\' के लिए कूट क्या है ?</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>8</p>",
                        "<p>7</p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>8</p>",
                        "<p>7</p>"
                    ],
                    solution_en: "<p>3.(a)<br>\'SAND\' &rarr;&nbsp;\'2468\' &hellip;&hellip;.. (i)<br>\'WEST\' &rarr; \'3574\' &hellip;&hellip;.. (ii)<br>From (i) and (ii) &lsquo;S&rsquo; and &lsquo;4&rsquo; is common.<br>So, the code of &lsquo;S&rsquo; is &lsquo;4&rsquo;.</p>",
                    solution_hi: "<p>3.(a)<br>\'SAND\' &rarr; \'2468\' &hellip;&hellip;.. (i)<br>\'WEST\' &rarr; \'3574\' &hellip;&hellip;.. (ii)<br>(i) और (ii) से \'S\' और \'4\' उभयनिष्ठ है।<br>तो, \'S\' का कोड \'4\' है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. What will come in place of the question mark (?) in the following equation if \'+\' and \'&times;&rsquo; are interchanged and \'-\' and \'&divide;\' are interchanged ?&nbsp;<br>5 + 3 &times; 2 &divide; 10 - 5 = ?</p>",
                    question_hi: "<p>4. निम्नलिखित समीकरण में यदि \'+\' और \'&times;&rsquo; को परस्पर बदल दिया जाए और \'-\' और \'&divide;\' को परस्पर बदल दिया जाए, तो प्रश्न-चिह्न (?) के स्थान पर क्या आएगा ?<br>5 + 3 &times; 2 &divide; 10 - 5 = ?</p>",
                    options_en: [
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>13</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>12</p>",
                        "<p>10</p>",
                        "<p>13</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>4.(d)<br>5 + 3 &times; 2 &divide; 10 - 5&nbsp;<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;&times;&rsquo; and &lsquo;-&rsquo; and &lsquo;&divide;&rsquo; we get<br>5 &times; 3 + 2 - 10 &divide; 5<br>= 15 + 2 - 2 = 15</p>",
                    solution_hi: "<p>4.(d)<br>5 + 3 &times; 2 &divide; 10 - 5&nbsp;<br>दिए गए निर्देश के अनुसार \'+\' और \'&lsquo;&times;&rsquo;\' तथा \'-\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>5 &times; 3 + 2 - 10 &divide; 5<br>= 15 + 2 - 2 = 15</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. A and C are sisters. A is the daughter of D. D is the mother of B. How is A related to B ?</p>",
                    question_hi: "<p>5. A और C बहनें हैं। A, D की पुत्री है। D, B की माता है। A का B से क्या रिश्&zwj;ता है ?</p>",
                    options_en: [
                        "<p>Aunt</p>",
                        "<p>Sister</p>",
                        "<p>Mother</p>",
                        "<p>Grandmother</p>"
                    ],
                    options_hi: [
                        "<p>चाची</p>",
                        "<p>बहन</p>",
                        "<p>माता</p>",
                        "<p>दादी/नानी</p>"
                    ],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782934206.png\" alt=\"rId5\" width=\"176\" height=\"107\"><br>A is the sister of B.</p>",
                    solution_hi: "<p>5.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782934206.png\" alt=\"rId5\" width=\"176\" height=\"107\"><br>A, B की बहन है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Which two signs should be interchanged to make the given equation correct ?<br>5 + 156 &minus; 12 &times; 15 &divide; 15 = 185</p>",
                    question_hi: "<p>6. दिए गए समीकरण को सही बनाने के लिए कौन से दो चिह्नों को परस्&zwj;पर बदलना चाहिए ?<br>5 + 156 &minus; 12 &times; 15 &divide; 15 = 185</p>",
                    options_en: [
                        "<p>+ and &times;</p>",
                        "<p>&minus; and +</p>",
                        "<p>&times; and &minus;</p>",
                        "<p>&divide; and &minus;</p>"
                    ],
                    options_hi: [
                        "<p>+ और &times;</p>",
                        "<p>&minus; और +</p>",
                        "<p>&times; और &minus;</p>",
                        "<p>&divide; और &minus;</p>"
                    ],
                    solution_en: "<p>6.(d)<strong> Given :-</strong> 5 + 156 - 12 &times; 15 &divide; 15 = 185<br>After going through all the options, option d satisfied. After interchanging &divide; and - we get,<br>5 + 156 &divide; 12 &times; 15 - 15<br>5 + 13 &times; 15 - 15<br>5 + 195 - 15<br>5 + 180 = 185<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>6.(d) <strong>दिया गया :- </strong>5 + 156 - 12 &times; 15 &divide; 15 = 185<br>सभी विकल्पों की जांच करने पर विकल्प d संतुष्ट करता है । &divide; तथा - को आपस में बदलने के बाद हमें प्राप्त होता है,<br>5 + 156 &divide; 12 &times; 15 - 15<br>5 + 13 &times; 15 - 15<br>5 + 195 - 15<br>5 + 180 = 185<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Read the given statements and conclusions carefully. You have to take the given statements to be true even if they seem to be at variance from commonly known facts. You have to decide which conclusion(s) logically follow(s) from the given statements<br><strong>Statements :</strong><br>No river is a mountain.<br>Some rivers are animals.<br>Some birds are mountains.<br><strong>Conclusions :</strong><br>I: No animal is a mountain.<br>II: No river is a bird.</p>",
                    question_hi: "<p>7. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। आपको दिए गए कथनों को सत्य मानना है, भले ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों। आपको तय करना है कि कौन-सा/कौन-से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>कोई भी नदी, पहाड़ नहीं है।<br>कुछ नदियाँ, जानवर हैं।<br>कुछ पक्षी, पहाड़ हैं।<br><strong>निष्कर्ष :</strong><br>I: कोई भी जानवर, पहाड़ नहीं है।<br>II: कोई भी नदी, पक्षी नहीं है।</p>",
                    options_en: [
                        "<p>Only conclusion (II) follows</p>",
                        "<p>Neither conclusion (I) nor (II) follows</p>",
                        "<p>Both conclusions (I) and (II) follow</p>",
                        "<p>Only conclusion (I) follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>",
                        "<p>न तो निष्कर्ष (I) और न ही (II) अनुसरण करता है।</p>",
                        "<p>दोनों निष्कर्ष (I) और (II) अनुसरण करते हैं।</p>",
                        "<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782934377.png\" alt=\"rId6\" width=\"330\" height=\"51\"><br>Neither conclusion (I) nor (II) follows</p>",
                    solution_hi: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782934550.png\" alt=\"rId7\" width=\"343\" height=\"53\"><br>न तो निष्कर्ष (I) और न ही (II) अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option that represents the correct order of the given words as they would appear in an English dictionary.<br>1. Dinnerware<br>2. Dingiest<br>3. Dinosaurs<br>4. Dinucleotide<br>5. Dinkier<br>6. Dingling</p>",
                    question_hi: "<p>8. उस विकल्प का चयन करें, जो दिए गए शब्दों के उस सही क्रम को दर्शाता है, जिसमें वे अंग्रेज़ी शब्दकोश में दिखाई देंगे।<br>1. Dinnerware<br>2. Dingiest<br>3. Dinosaurs<br>4. Dinucleotide<br>5. Dinkier<br>6. Dingling</p>",
                    options_en: [
                        "<p>5, 2, 3, 6, 1, 4</p>",
                        "<p>2, 6, 5, 1, 3, 4</p>",
                        "<p>2, 6, 3, 5, 1, 4</p>",
                        "<p>5, 2, 6, 3, 1, 4</p>"
                    ],
                    options_hi: [
                        "<p>5, 2, 3, 6, 1, 4</p>",
                        "<p>2, 6, 5, 1, 3, 4</p>",
                        "<p>2, 6, 3, 5, 1, 4</p>",
                        "<p>5, 2, 6, 3, 1, 4</p>"
                    ],
                    solution_en: "<p>8.(b) <strong>The correct order is :-</strong> <br>Dingiest(2) &rarr; Dingling(6) &rarr; Dinkier(5) &rarr; Dinnerware(1) &rarr; Dinosaurs(3) &rarr; Dinucleotide(4)</p>",
                    solution_hi: "<p>8.(b) <strong>सही क्रम है:-</strong><br>Dingiest(2) &rarr; Dingling(6) &rarr; Dinkier(5) &rarr; Dinnerware(1) &rarr; Dinosaurs(3) &rarr; Dinucleotide(4)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter- cluster that is different.<br>Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>9. चार अक्षर-समूह दिए गए हैं जिनमें से तीन किसी न किसी रूप में एक समान हैं, और एक उनसे असंगत है।उस असंगत अक्षर-समूह का चयन कीजिए।<br>नोट: अक्षर समूह में, असंगत व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: [
                        "<p>MOP</p>",
                        "<p>NPR</p>",
                        "<p>HJL</p>",
                        "<p>TVX</p>"
                    ],
                    options_hi: [
                        "<p>MOP</p>",
                        "<p>NPR</p>",
                        "<p>HJL</p>",
                        "<p>TVX</p>"
                    ],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782934734.png\" alt=\"rId8\" width=\"151\" height=\"53\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782934917.png\" alt=\"rId9\" width=\"157\" height=\"49\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935022.png\" alt=\"rId10\" width=\"148\" height=\"45\"><br>but</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935122.png\" alt=\"rId11\" width=\"159\" height=\"49\"></p>",
                    solution_hi: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782934734.png\" alt=\"rId8\" width=\"151\" height=\"53\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782934917.png\" alt=\"rId9\" width=\"157\" height=\"49\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935022.png\" alt=\"rId10\" width=\"148\" height=\"45\"></p>\n<p>लेकिन</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935122.png\" alt=\"rId11\" width=\"159\" height=\"49\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Which of the following letter-clusters will replace the question mark (?) in the given series to make it logically complete ?<br>WIX, VHW, UGV, TFU, ?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन-सा अक्षर-समूह दी गई श्रृंखला को तार्किक रूप से पूर्ण बनाने के लिए इसमें प्रश्न चिह्न (?) को प्रतिस्थापित करेगा ?<br>WIX, VHW, UGV, TFU, ?</p>",
                    options_en: [
                        "<p>TEU</p>",
                        "<p>SEU</p>",
                        "<p>SET</p>",
                        "<p>SFT</p>"
                    ],
                    options_hi: [
                        "<p>TEU</p>",
                        "<p>SEU</p>",
                        "<p>SET</p>",
                        "<p>SFT</p>"
                    ],
                    solution_en: "<p>10.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935239.png\" alt=\"rId12\" width=\"251\" height=\"93\"></p>",
                    solution_hi: "<p>10.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935239.png\" alt=\"rId12\" width=\"251\" height=\"93\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group ?<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>11. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है ?<br>(<strong>ध्यान दें:</strong> संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>86 &ndash; 41 &ndash; 38</p>",
                        "<p>118 &ndash; 93 &ndash; 161</p>",
                        "<p>33 &ndash; 15 &ndash; 12</p>",
                        "<p>52 &ndash; 36 &ndash; 56</p>"
                    ],
                    options_hi: [
                        "<p>86 &ndash; 41 &ndash; 38</p>",
                        "<p>118 &ndash; 93 &ndash; 161</p>",
                        "<p>33 &ndash; 15 &ndash; 12</p>",
                        "<p>52 &ndash; 36 &ndash; 56</p>"
                    ],
                    solution_en: "<p>11.(a) <strong>logic:-</strong> (first number + third number) &divide; 3 = second number<br>(b) (118 + 161) &divide; 3 = 93<br>(c) (33 + 12) &divide; 3 = 15<br>(d) (52 + 56) &divide; 3 = 36<br>But,<br>(a) (86 + 38) &divide; 3 &ne; 41</p>",
                    solution_hi: "<p>11.(a) <strong>तर्क:-</strong> (पहली संख्या + तीसरी संख्या) &divide; 3 = दूसरी संख्या<br>(b) (118 + 161) &divide; 3 = 93<br>(c) (33 + 12) &divide; 3 = 15<br>(d) (52 + 56) &divide; 3 = 36<br>लेकिन,<br>(a) (86 + 38) &divide; 3 &ne; 41</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "12. Select the option that represents the letters that, when sequentially placed from left to right in the blanks below, will complete the letter series.<br />R F _ G T R _V G _ R F V _ T _ F V G T",
                    question_hi: "12.  उस विकल्प का चयन कीजिए जो उन अक्षरों को दर्शाता है, जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ क्रमिक रूप से रखने पर अक्षर श्रृंखला पूरी हो जाएगी। <br />R F _ G T R _V G _ R F V _ T _ F V G T",
                    options_en: [
                        " VFTGR ",
                        " VFTGG ",
                        " VTTGR",
                        " VFVGR"
                    ],
                    options_hi: [
                        " VFTGR ",
                        " VFTGG ",
                        " VTTGR",
                        " VFVGR"
                    ],
                    solution_en: "<p>12.(a)<br>R F <span style=\"text-decoration: underline;\"><strong>V</strong></span> G T/ R <span style=\"text-decoration: underline;\"><strong>F</strong></span>V G <span style=\"text-decoration: underline;\"><strong>T</strong></span> / R F V <span style=\"text-decoration: underline;\"><strong>G</strong></span> T/ <span style=\"text-decoration: underline;\"><strong>R</strong></span> F V G T</p>",
                    solution_hi: "<p>12.(a)<br>R F <span style=\"text-decoration: underline;\"><strong>V</strong></span> G T/ R <span style=\"text-decoration: underline;\"><strong>F</strong></span>V G <span style=\"text-decoration: underline;\"><strong>T</strong></span> / R F V <span style=\"text-decoration: underline;\"><strong>G</strong></span> T/ <span style=\"text-decoration: underline;\"><strong>R</strong></span> F V G T</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option that is embedded in the given figure (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935410.png\" alt=\"rId13\" width=\"141\" height=\"136\"></p>",
                    question_hi: "<p>13. उस विकल्प का चयन कीजिए जो दी गई आकृति में सन्निहित है (घुमाने की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935410.png\" alt=\"rId13\" width=\"141\" height=\"136\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935525.png\" alt=\"rId14\" width=\"118\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935659.png\" alt=\"rId15\" width=\"159\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935804.png\" alt=\"rId16\" width=\"64\" height=\"82\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935907.png\" alt=\"rId17\" width=\"60\" height=\"57\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935525.png\" alt=\"rId14\" width=\"118\" height=\"79\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935659.png\" alt=\"rId15\" width=\"159\" height=\"60\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935804.png\" alt=\"rId16\" width=\"64\" height=\"82\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782935907.png\" alt=\"rId17\" width=\"60\" height=\"57\"></p>"
                    ],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936025.png\" alt=\"rId18\" width=\"119\" height=\"114\"></p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936025.png\" alt=\"rId18\" width=\"119\" height=\"114\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Which of the following numbers will replace the question mark (?) in the given series ?<br><strong>13 , 29 , 61 , 125 , 253 , 509 , ?</strong></p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न-चिह्न (?) का स्थान लेगी ?<br><strong>13 , 29 , 61 , 125 , 253 , 509 , ?</strong></p>",
                    options_en: [
                        "  1099",
                        "  1029",
                        "  1021",
                        "  1089"
                    ],
                    options_hi: [
                        "  1099",
                        "  1029",
                        "  1021",
                        "  1089"
                    ],
                    solution_en: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936192.png\" alt=\"rId19\" width=\"267\" height=\"44\"></p>",
                    solution_hi: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936192.png\" alt=\"rId19\" width=\"267\" height=\"44\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>RATE : SCWI : : HIGH : IKJL : : OPEN : ?</p>",
                    question_hi: "<p>15. उस विकल्प का चयन करें जो पांचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से और चौथा अक्षर- समूह तीसरे अक्षर-समूह से संबंधित है।<br>RATE : SCWI : : HIGH : IKJL : : OPEN : ?</p>",
                    options_en: [
                        "<p>PRIS</p>",
                        "<p>PQHR</p>",
                        "<p>PSIR</p>",
                        "<p>PRHR</p>"
                    ],
                    options_hi: [
                        "<p>PRIS</p>",
                        "<p>PQHR</p>",
                        "<p>PSIR</p>",
                        "<p>PRHR</p>"
                    ],
                    solution_en: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936298.png\" alt=\"rId20\" width=\"169\" height=\"112\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936412.png\" alt=\"rId21\" width=\"171\" height=\"117\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936524.png\" alt=\"rId22\" width=\"168\" height=\"125\"></p>",
                    solution_hi: "<p>15.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936298.png\" alt=\"rId20\" width=\"169\" height=\"112\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936412.png\" alt=\"rId21\" width=\"171\" height=\"117\"></p>\n<p>इसी प्रकार ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936524.png\" alt=\"rId22\" width=\"168\" height=\"125\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936634.png\" alt=\"rId23\" width=\"174\" height=\"61\"></p>",
                    question_hi: "<p>16. नीचे दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936634.png\" alt=\"rId23\" width=\"174\" height=\"61\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936770.png\" alt=\"rId24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936865.png\" alt=\"rId25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936969.png\" alt=\"rId26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937077.png\" alt=\"rId27\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936770.png\" alt=\"rId24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936865.png\" alt=\"rId25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936969.png\" alt=\"rId26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937077.png\" alt=\"rId27\"></p>"
                    ],
                    solution_en: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936770.png\" alt=\"rId24\"></p>",
                    solution_hi: "<p>16.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782936770.png\" alt=\"rId24\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Identify the figure given in the options that when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937188.png\" alt=\"rId28\" width=\"293\" height=\"71\"></p>",
                    question_hi: "<p>17. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937188.png\" alt=\"rId28\" width=\"293\" height=\"71\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937286.png\" alt=\"rId29\" width=\"72\" height=\"73\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937389.png\" alt=\"rId30\" width=\"72\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937517.png\" alt=\"rId31\" width=\"72\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937623.png\" alt=\"rId32\" width=\"72\" height=\"75\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937286.png\" alt=\"rId29\" width=\"72\" height=\"73\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937389.png\" alt=\"rId30\" width=\"72\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937517.png\" alt=\"rId31\" width=\"72\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937623.png\" alt=\"rId32\" width=\"72\" height=\"75\"></p>"
                    ],
                    solution_en: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937286.png\" alt=\"rId29\" width=\"72\" height=\"73\"></p>",
                    solution_hi: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937286.png\" alt=\"rId29\" width=\"72\" height=\"73\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. 8 is related to 512 following a certain logic. Following the same logic, 12 is related to 1728. To which of the following is 15 related, following the same logic ?<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>18. एक निश्चित तर्क का अनुसरण करते हुए 8, 512 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 12, 1728 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 15 निम्नलिखित में से किससे संबंधित है ?<br>(<strong>नोट :</strong> संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: [
                        "<p>2275</p>",
                        "<p>2725</p>",
                        "<p>3375</p>",
                        "<p>3735</p>"
                    ],
                    options_hi: [
                        "<p>2275</p>",
                        "<p>2725</p>",
                        "<p>3375</p>",
                        "<p>3735</p>"
                    ],
                    solution_en: "<p>18.(c) <strong>Logic:</strong> (1st number)<sup>3</sup> = 2nd number<br>8 : 512 :- (8)<sup>3</sup> = 512<br>12 : 1728 :- (12)<sup>3</sup> = 1728<br>Similarly<br>15 : x&nbsp;:- (15)<sup>3</sup> = 3375</p>",
                    solution_hi: "<p>18.(c) <strong>तर्क: </strong>(पहली संख्या)<sup>3</sup> = दूसरी संख्या<br>8 : 512 :- (8)<sup>3</sup> = 512<br>12 : 1728 :- (12)<sup>3</sup> = 1728<br>इसी प्रकार<br>15 : x&nbsp;:- (15)<sup>3</sup> = 3375</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Identify the figure given in the options that when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937753.png\" alt=\"rId33\" width=\"364\" height=\"74\"></p>",
                    question_hi: "<p>19. विकल्पों में दी गई उस आकृति को पहचानिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937753.png\" alt=\"rId33\" width=\"364\" height=\"74\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937886.png\" alt=\"rId34\" width=\"77\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937979.png\" alt=\"rId35\" width=\"77\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782938080.png\" alt=\"rId36\" width=\"78\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782938215.png\" alt=\"rId37\" width=\"77\" height=\"76\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937886.png\" alt=\"rId34\" width=\"77\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782937979.png\" alt=\"rId35\" width=\"77\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782938080.png\" alt=\"rId36\" width=\"78\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782938215.png\" alt=\"rId37\" width=\"77\" height=\"76\"></p>"
                    ],
                    solution_en: "<p>19.(a)<br><strong id=\"docs-internal-guid-8a26941d-7fff-7eb3-a4ec-42049d7afb61\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfTzoHyNUIsEhlomQjOmxt1To56uUS6_zwzZOexLMuFMLaqYewwFUlTur9K9CXXnZoFZB739vmGHeHfyXAUcmLyVGQq3fb8EKs6s0xky_lcmFh6LTOyAOhb61psdfLsaGvvpVIw5A?key=EH0NNm_3HoVWMuAlg9nYjmC9\" width=\"79\" height=\"79\"></strong></p>",
                    solution_hi: "<p>19.(a)<br><strong id=\"docs-internal-guid-8a26941d-7fff-7eb3-a4ec-42049d7afb61\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfTzoHyNUIsEhlomQjOmxt1To56uUS6_zwzZOexLMuFMLaqYewwFUlTur9K9CXXnZoFZB739vmGHeHfyXAUcmLyVGQq3fb8EKs6s0xky_lcmFh6LTOyAOhb61psdfLsaGvvpVIw5A?key=EH0NNm_3HoVWMuAlg9nYjmC9\" width=\"79\" height=\"79\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. In a certain code language, &lsquo;will you go&rsquo; is written as &lsquo;ht dt gt&rsquo; and &lsquo;you can go&rsquo; is written as &lsquo;gt st dt&rsquo;. How will &lsquo;can&rsquo; be written in the given code language ?</p>",
                    question_hi: "<p>20. एक निश्चित कूट भाषा में &lsquo;will you go&rsquo; को &lsquo;ht dt gt&rsquo; लिखा जाता है और &lsquo;you can go&rsquo; को &lsquo;gt st dt&rsquo; लिखा जाता है। दी गई कूट भाषा में &lsquo;can&rsquo; को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>st</p>",
                        "<p>gt</p>",
                        "<p>dt</p>",
                        "<p>ht</p>"
                    ],
                    options_hi: [
                        "<p>st</p>",
                        "<p>gt</p>",
                        "<p>dt</p>",
                        "<p>ht</p>"
                    ],
                    solution_en: "<p>20.(a)<br>&lsquo;will you go&rsquo; &rarr;&nbsp;&lsquo;ht dt gt&rsquo; &hellip;&hellip;.. (i)<br>&lsquo;you can go&rsquo; &rarr; &lsquo;gt st dt&rsquo; &hellip;&hellip;&hellip; (ii)<br>From (i) and (ii) &lsquo;you, go&rsquo; and &lsquo;dt, gt&rsquo; are common.<br>So, the code of &lsquo;can&rsquo; is &lsquo;st&rsquo;.</p>",
                    solution_hi: "<p>20.(a)<br>&lsquo;will you go&rsquo; &rarr; &lsquo;ht dt gt&rsquo; &hellip;&hellip;.. (i)<br>&lsquo;you can go&rsquo; &rarr; &lsquo;gt st dt&rsquo; &hellip;&hellip;&hellip; (ii)<br>(i) और (ii) से \'you, go\' और \'dt, gt\' उभयनिष्ठ हैं।<br>तो, \'can\' का कोड \'st\' है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. In a certain code language,<br>&lsquo;M $ N&rsquo; means &lsquo;M is the son of N&rsquo;,<br>&lsquo;M + N&rsquo; means &lsquo;M is the daughter-in-law of N&rsquo;,<br>&lsquo;M &divide; N&rsquo; means &lsquo;M is the husband of N&rsquo;,<br>&lsquo;M &amp; N&rsquo; means &lsquo;M is the wife of N&rsquo;,<br>&lsquo;M = N&rsquo; means &lsquo;M is the brother of N&rsquo;.<br>If &lsquo;G $ C = A &divide; B + D &amp; E&rsquo;, then how is E related to C ?</p>",
                    question_hi: "<p>21. एक निश्चित कूट भाषा में<br>\'M $ N\' का अर्थ है कि \'M, N का पुत्र है\', <br>\'M + N\' का अर्थ है कि \'M, N की बहू है\', <br>\'M <math display=\"inline\"><mo>&#247;</mo></math> N\' का अर्थ है कि \'M, N का पति है\', <br>\'M &amp; N\' का अर्थ है कि \'M, N की पत्नी है\', <br>\'M = N\' का अर्थ है कि \'M, N का भाई है\', <br>यदि \'G $ C = A <math display=\"inline\"><mo>&#247;</mo></math> B + D &amp; E\' है, तो E का C से क्या संबंध है ?</p>",
                    options_en: [
                        "<p>Brother</p>",
                        "<p>Father&rsquo;s brother</p>",
                        "<p>Son</p>",
                        "<p>Father</p>"
                    ],
                    options_hi: [
                        "<p>भाई</p>",
                        "<p>चाचा</p>",
                        "<p>पुत्र</p>",
                        "<p>पिता</p>"
                    ],
                    solution_en: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782938647.png\" alt=\"rId39\" width=\"131\" height=\"138\"><br>E is the father of C.</p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782938647.png\" alt=\"rId39\" width=\"131\" height=\"138\"><br>E, C का पिता है.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782938903.png\" alt=\"rId40\" width=\"106\" height=\"97\"></p>",
                    question_hi: "<p>22. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782938903.png\" alt=\"rId40\" width=\"106\" height=\"97\"></p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782939270.png\" alt=\"rId41\" width=\"126\" height=\"120\"><br>There are 5 triangle<br>ABC , BCD, BED, ABD, AED</p>",
                    solution_hi: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782939270.png\" alt=\"rId41\" width=\"126\" height=\"120\"><br>5 त्रिभुज हैं<br>ABC , BCD, BED, ABD, AED</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782939475.png\" alt=\"rId42\" width=\"100\" height=\"90\"></p>",
                    question_hi: "<p>23. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782939475.png\" alt=\"rId42\" width=\"100\" height=\"90\"></p>",
                    options_en: [
                        "<p><strong id=\"docs-internal-guid-f7ab74f9-7fff-db1b-51b4-d7a281549128\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfOD_genUYCEWPxvn6BXPDWFf64fSn5O4qVLd8QLfLs-T3Ug1Ainkr4VoVxF5tYNAzGRzpEpNB-NXKYcjZMGYE7SFAmNRYtcl5z04LJloiQdgoNOzRMhF3fQcXIqgtTu8TaIUfT?key=EH0NNm_3HoVWMuAlg9nYjmC9\" width=\"101\" height=\"16\"></strong></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940142.png\" alt=\"rId44\" width=\"103\" height=\"15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940294.png\" alt=\"rId45\" width=\"100\" height=\"15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940417.png\" alt=\"rId46\" width=\"101\" height=\"15\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940016.png\" alt=\"rId43\" width=\"101\" height=\"16\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940142.png\" alt=\"rId44\" width=\"103\" height=\"15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940294.png\" alt=\"rId45\" width=\"100\" height=\"15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940417.png\" alt=\"rId46\" width=\"101\" height=\"15\"></p>"
                    ],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940142.png\" alt=\"rId44\" width=\"103\" height=\"15\"></p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940142.png\" alt=\"rId44\" width=\"103\" height=\"15\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Which of the following numbers will replace the question mark (?) in the given series ? <br>5, 14, 41, 122, 365, ?</p>",
                    question_hi: "<p>24. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी ?<br>5, 14, 41, 122, 365, ?</p>",
                    options_en: [
                        "<p>1049</p>",
                        "<p>1490</p>",
                        "<p>1940</p>",
                        "<p>1094</p>"
                    ],
                    options_hi: [
                        "<p>1049</p>",
                        "<p>1490</p>",
                        "<p>1940</p>",
                        "<p>1094</p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940569.png\" alt=\"rId47\" width=\"243\" height=\"48\"></p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940569.png\" alt=\"rId47\" width=\"243\" height=\"48\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. &lsquo;Lavish&rsquo; is related to &lsquo;Austere&rsquo; in the same way as &lsquo;Prejudiced&rsquo; is related to &lsquo;_____&rsquo;.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants /vowels in the word.)</p>",
                    question_hi: "<p>25. \'अतिव्&zwj;ययी\' (Lavish), \'मिताहारी\' (Austere) से उसी प्रकार संबंधित है, जिस प्रकार \'पक्षपातपूर्ण\' (Prejudiced) \' _____\' से संबंधित है।<br>(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्&zwj;दों को अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)</p>",
                    options_en: [
                        "<p>Impartial</p>",
                        "<p>Compromised</p>",
                        "<p>Thoughtful</p>",
                        "<p>Careful</p>"
                    ],
                    options_hi: [
                        "<p>निष्&zwj;पक्ष (Impartial)</p>",
                        "<p>समझौताकारी (Compromised)</p>",
                        "<p>विचारपूर्ण (Thoughtful)</p>",
                        "<p>सावधान (Careful)</p>"
                    ],
                    solution_en: "<p>25.(a) As &lsquo;Lavish&rsquo; and &lsquo;Austere&rsquo; are antonyms of each other similarly, &lsquo;Prejudiced&rsquo; and &lsquo;Impartial&rsquo; are antonyms of each other.</p>",
                    solution_hi: "<p>25.(a) जिस प्रकार \'अतिव्&zwj;ययी\' (Lavish)\' और \'मिताहारी\' (Austere) एक दूसरे के विपरीतार्थक हैं उसी प्रकार \'पक्षपातपूर्ण\' (Prejudiced) और निष्&zwj;पक्ष (Impartial) एक दूसरे के विपरीतार्थक हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. The Vedangas are Hindu auxiliary disciplines that originated in ancient times and are linked to the study of the Vedas. These are _____ in number.</p>",
                    question_hi: "<p>26. वेदांग हिंदू धर्म के सहायक विषय हैं जिनकी उत्पत्ति प्राचीन काल में हुई थी और वे वेदों के अध्ययन से जुड़े हुए हैं। इनकी संख्या ______ है।</p>",
                    options_en: [
                        "<p>five</p>",
                        "<p>seven</p>",
                        "<p>four</p>",
                        "<p>six</p>"
                    ],
                    options_hi: [
                        "<p>पाँच</p>",
                        "<p>सात</p>",
                        "<p>चार</p>",
                        "<p>छह</p>"
                    ],
                    solution_en: "<p>26.(d) <strong>Six. </strong>Vedanga literally means \"limbs of the Vedas,\" and refers to the six disciplines connected with studying the Vedas, the ancient Indian spiritual writings. The six Vedangas are as follows: Shiksha (phonetics), Kalpa (ritual), Vyakarana (grammar), Nirukta (etymology), Chandas (meter), and Jyotisha (astronomy).</p>",
                    solution_hi: "<p>26.(d) <strong>छह। </strong>वेदांग का शाब्दिक अर्थ है \"वेदों के अंग\", और यह वेदों, प्राचीन भारतीय आध्यात्मिक लेखन के अध्ययन से जुड़े छह विषयों को संदर्भित करता है। छह वेदांग इस प्रकार हैं: शिक्षा (phonetics), कल्प (ritual), व्याकरण (grammar), निरुक्त (etymology), छंद (meter), और ज्योतिष (astronomy)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. How many Gallantry awards were approved by the President for Armed Forces personnel on the eve of Republic Day 2025 ?</p>",
                    question_hi: "<p>27. गणतंत्र दिवस 2025 की पूर्व संध्या पर राष्ट्रपति द्वारा सशस्त्र बल कर्मियों के लिए कितने वीरता पुरस्कारों को मंजूरी दी गई ?</p>",
                    options_en: [
                        "<p>85</p>",
                        "<p>93</p>",
                        "<p>100</p>",
                        "<p>120</p>"
                    ],
                    options_hi: [
                        "<p>85</p>",
                        "<p>93</p>",
                        "<p>100</p>",
                        "<p>120</p>"
                    ],
                    solution_en: "<p>27.(b) <strong>93. </strong>President Smt Droupadi Murmu has approved Gallantry awards to 93 Armed Forces and Central Armed Police Forces personnel, on the eve of 76th Republic Day. The honours include 2 Kirti Chakras, 14 Shaurya Chakras, 1 Bar to Sena Medal (Gallantry), 66 Sena Medals, 2 Nao Sena Medals, and 8 Vayu Sena Medals. The President also approved 305 defence decorations to the Armed Forces and other personnel. These include 30 Param Vishisht Seva Medals; five Uttam Yudh Seva Medals; 57 Ati Vishisht Seva Medals; 10 Yudh Seva Medals; one Bar to Sena Medals (Devotion to Duty); 43 Sena Medals (Devotion to Duty); eight Nao Sena Medals (Devotion to Duty); 15 Vayu Sena Medals (Devotion to Duty); four Bar to Vishisht Seva Medal and 132 Vishisht Seva Medals.</p>",
                    solution_hi: "<p>27.(b) <strong>93 ।</strong> राष्ट्रपति श्रीमती द्रौपदी मुर्मू ने 76वें गणतंत्र दिवस की पूर्व संध्या पर 93 सशस्त्र बलों और केंद्रीय सशस्त्र पुलिस बलों के कर्मियों को वीरता पुरस्कारों को मंजूरी दी है। सम्मानों में 2 कीर्ति चक्र, 14 शौर्य चक्र, 1 बार टू सेना पदक (वीरता), 66 सेना पदक, 2 नौसेना पदक और 8 वायु सेना पदक शामिल हैं। राष्ट्रपति ने सशस्त्र बलों और अन्य कर्मियों के लिए 305 रक्षा अलंकरणों को भी मंजूरी दी। इनमें 30 परम विशिष्ट सेवा पदक, पांच उत्तम युद्ध सेवा पदक, 57 अति विशिष्ट सेवा पदक, 10 युद्ध सेवा पदक शामिल सेना पदक (कर्तव्य के प्रति समर्पण) के लिए एक बार; 43 सेना पदक (कर्तव्य के प्रति समर्पण); आठ नाव सेना पदक (कर्तव्य के प्रति समर्पण); 15 वायु सेना पदक (कर्तव्य के प्रति समर्पण); चार बार विशिष्ट सेवा पदक और 132 विशिष्ट सेवा पदक।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The book &lsquo;Poverty and Un-British Rule in India&rsquo; was written by ______.</p>",
                    question_hi: "<p>28. \'पॉवर्टी एंड अन-ब्रिटिश रूल इन इंडिया\' पुस्तक की रचना _____ के द्वारा की गई थी।</p>",
                    options_en: [
                        "<p>MN Roy</p>",
                        "<p>Dadabhai Naoroji</p>",
                        "<p>Bipin Chandra Pal</p>",
                        "<p>Lala Lajpat Rai</p>"
                    ],
                    options_hi: [
                        "<p>एम.एन. रॉय</p>",
                        "<p>दादाभाई नौरोजी</p>",
                        "<p>बिपिन चंद्र पाल</p>",
                        "<p>लाला लाजपत राय</p>"
                    ],
                    solution_en: "<p>28.(b) <strong>Dadabhai Naoroji. </strong>He is known as the \"Grand Old Man of India\". He served as the President of the Indian National Congress three times: first in 1886, then in 1893, and finally in 1906. He was the first Indian who was elected to the British parliament. In pre-independent India, Dadabhai Naoroji was the first to discuss the concept of a poverty line.</p>",
                    solution_hi: "<p>28.(b) <strong>दादाभाई नौरोजी। </strong>उन्हें \"भारत के ग्रैंड ओल्ड मैन\" के रूप में जाना जाता है। उन्होंने तीन बार भारतीय राष्ट्रीय कांग्रेस के अध्यक्ष के रूप में कार्य किया: पहली बार 1886 में, फिर 1893 में और अंत में 1906 में। वे पहले भारतीय थे जो ब्रिटिश संसद के लिए चुने गए थे। स्वतंत्रता-पूर्व भारत में, दादाभाई नौरोजी गरीबी रेखा की अवधारणा पर चर्चा करने वाले पहले व्यक्ति थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. In which of the following places did Mahatma Gandhi, along with other Indians, establish the Natal Congress to fight against racial discrimination ?</p>",
                    question_hi: "<p>29. निम्नलिखित में से किस स्थान पर महात्मा गांधी ने अन्य भारतीयों के साथ मिलकर नस्लीय भेदभाव के खिलाफ लड़ने के लिए नेटाल कांग्रेस (Natal Congress) की स्थापना की थी ?</p>",
                    options_en: [
                        "<p>South Africa</p>",
                        "<p>India</p>",
                        "<p>England</p>",
                        "<p>France</p>"
                    ],
                    options_hi: [
                        "<p>दक्षिण अफ्रीका</p>",
                        "<p>भारत</p>",
                        "<p>इंगलैंड</p>",
                        "<p>फ्रांस</p>"
                    ],
                    solution_en: "<p>29.(a) <strong>South Africa.</strong> The Natal Indian Congress, founded by Mahatma Gandhi in 1894, aimed to combat discrimination against Indians in South Africa. Gandhiji, born on 2 October 1869, returned to India on 9 January 1915 and led several key movements in India\'s independence struggle: the Non-Cooperation Movement (1920), Civil Disobedience Movement (1930), Quit India Movement (1942), Champaran Satyagraha (1917), Kheda Satyagraha (1918), Rowlatt Satyagraha (1919), Ahmedabad Mill Strike (1918).</p>",
                    solution_hi: "<p>29.(a) <strong>दक्षिण अफ्रीका।</strong> महात्मा गांधी द्वारा 1894 में स्थापित नेटाल इंडियन कांग्रेस का उद्देश्य दक्षिण अफ्रीका में भारतीयों के खिलाफ भेदभाव का मुकाबला करना था। 2 अक्टूबर 1869 को जन्मे गांधीजी 9 जनवरी 1915 को भारत लौटे और भारत के स्वतंत्रता संग्राम में कई प्रमुख आंदोलनों का नेतृत्व किया: असहयोग आंदोलन (1920), सविनय अवज्ञा आंदोलन (1930), भारत छोड़ो आंदोलन (1942), चंपारण सत्याग्रह (1917), खेड़ा सत्याग्रह (1918), रौलट सत्याग्रह (1919), अहमदाबाद मिल हड़ताल (1918)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which type of well-managed industry was established in India at the time of independence ?</p>",
                    question_hi: "<p>30. स्वतंत्रता के समय भारत में किस प्रकार का सुप्रबंधित उद्योग स्थापित किया गया था ?</p>",
                    options_en: [
                        "<p>Electronics and Electricals</p>",
                        "<p>Steel and Electronics</p>",
                        "<p>Steel and Cotton</p>",
                        "<p>Cement and Sugar</p>"
                    ],
                    options_hi: [
                        "<p>इलेक्ट्रॉनिक्स और इलेक्ट्रिकल्स</p>",
                        "<p>इस्पात और इलेक्ट्रॉनिक्स</p>",
                        "<p>इस्पात और सूती वस्&zwj;त्र</p>",
                        "<p>सीमेंट और चीनी</p>"
                    ],
                    solution_en: "<p>30.(c) <strong>Steel and Cotton. </strong>The first iron and steel plant in India was the Tata Iron and Steel Company (TISCO), which was established in 1907 by Jamsetji Tata and Dorabji Tata in Jamshedpur, Jharkhand. The plant began production in 1911. The first cotton mill in India was the Bombay Spinning and Weaving Company, which opened in Bombay (now Mumbai) on July 7, 1854.</p>",
                    solution_hi: "<p>30.(c)<strong> इस्पात और सूती वस्&zwj;त्र । </strong>भारत में प्रथम आयरन और इस्पात संयंत्र टाटा आयरन एंड स्टील कंपनी (TISCO) था, जिसे 1907 में जमशेदजी टाटा और दोराबजी टाटा ने झारखंड के जमशेदपुर में स्थापित किया था। संयंत्र ने 1911 में उत्पादन शुरू किया था। भारत में प्रथम कपास मिल बॉम्बे स्पिनिंग एंड वीविंग कंपनी थी, जो 7 जुलाई, 1854 को बॉम्बे (अब मुंबई) में स्थापित की गई थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following options best describes the primary cause of the per capita income decline ?</p>",
                    question_hi: "<p>31. निम्नलिखित में से कौन-सा विकल्प प्रति व्यक्ति आय में गिरावट के प्राथमिक कारण का सबसे अच्छा वर्णन करता है ?</p>",
                    options_en: [
                        "<p>Equitable distribution of income</p>",
                        "<p>Increase in the population growth</p>",
                        "<p>Decline in the population growth</p>",
                        "<p>Decline in the gross domestic product</p>"
                    ],
                    options_hi: [
                        "<p>आय का समान वितरण</p>",
                        "<p>जनसंख्या वृद्धि में बढ़ोत्तरी</p>",
                        "<p>जनसंख्या वृद्धि में गिरावट</p>",
                        "<p>सकल घरेलू उत्पाद में गिरावट</p>"
                    ],
                    solution_en: "<p>31.(b) <strong>Increase in the population growth. </strong>Per capita income is calculated by dividing a country\'s total income (GDP) by its population. If the population grows rapidly while the economy (GDP) doesn\'t grow at the same pace, the per capita income decreases because the same amount of wealth is distributed among a larger number of people. Decline in population growth would likely increase per capita income. Decline in gross domestic product could also decrease per capita income.</p>",
                    solution_hi: "<p>31.(b) <strong>जनसंख्या वृद्धि में बढ़ोत्तरी ।</strong> प्रति व्यक्ति आय की गणना किसी देश की कुल आय (GDP) को उसकी जनसंख्या से विभाजित करके की जाती है। यदि जनसंख्या तेज़ी से बढ़ती है जबकि अर्थव्यवस्था (GDP) उसी गति से नहीं बढ़ती है, तो प्रति व्यक्ति आय घट जाती है क्योंकि समान मात्रा में धन अधिक लोगों में वितरित किया जाता है। जनसंख्या वृद्धि में कमी से प्रति व्यक्ति आय में वृद्धि होने की संभावना है। सकल घरेलू उत्पाद में कमी से प्रति व्यक्ति आय में भी कमी आ सकती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. The Jhumur dance performance was organized ahead of which significant event in Assam ?</p>",
                    question_hi: "<p>32. झूमर नृत्य प्रदर्शन असम में किस महत्वपूर्ण कार्यक्रम से पहले आयोजित किया गया था ?</p>",
                    options_en: [
                        "<p>Bihu Festival</p>",
                        "<p>Advantage Assam 2.0 Investment Summit</p>",
                        "<p>Assam Tea Festival</p>",
                        "<p>Kaziranga Wildlife Festival</p>"
                    ],
                    options_hi: [
                        "<p>बिहू उत्सव</p>",
                        "<p>एडवांटेज असम 2.0 इन्वेस्टमेंट समिट</p>",
                        "<p>असम चाय महोत्सव</p>",
                        "<p>काज़ीरंगा वन्यजीव महोत्सव</p>"
                    ],
                    solution_en: "<p>32.(b) The performance aimed to set a Guinness World Record for the largest Jhumur dance. Chief Minister Himanta Biswa Sarma announced the event. The event was held in Guwahati, Assam.</p>",
                    solution_hi: "<p>32.(b) इस प्रदर्शन का उद्देश्य सबसे बड़े झूमर नृत्य के लिए गिनीज वर्ल्ड रिकॉर्ड स्थापित करना था। मुख्यमंत्री हिमंत बिस्वा सरमा ने इस आयोजन की घोषणा की। यह आयोजन गुवाहाटी, असम में आयोजित किया गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Pushyabhuti dynasty, after Prabhakarvardhana, strengthened its position in the regions of Punjab and Haryana after defeating which of the following dynasties ?</p>",
                    question_hi: "<p>33. प्रभाकरवर्धन के बाद, पुष्यभूति राजवंश ने निम्नलिखित में से किस राजवंश को हराकर पंजाब और हरियाणा के क्षेत्रों में अपनी स्थिति मजबूत कर ली ?</p>",
                    options_en: [
                        "<p>Maukharis</p>",
                        "<p>Hunas</p>",
                        "<p>Chalukyas</p>",
                        "<p>Maitrakas</p>"
                    ],
                    options_hi: [
                        "<p>मौखरि</p>",
                        "<p>हूण</p>",
                        "<p>चालुक्य</p>",
                        "<p>मैत्रक</p>"
                    ],
                    solution_en: "<p>33.(b) <strong>Hunas. </strong>The Pushyabhuti dynasty (Vardhana dynasty) ruled northern India during the 6th and 7th centuries, emerging after the Gupta Empire\'s decline. Its core territory was in present-day Haryana, with Sthanishvara (Thaneshwar) as the capital. The dynasty reached its peak under Harsha Vardhana, who reigned from 606 to 647 AD. His empire, with its capital at Kannauj, spanned much of north and northwestern India, extending from Punjab to northern Orissa and from the Himalayas to the Narmada River.</p>",
                    solution_hi: "<p>33.(b) <strong>हूण। </strong>पुष्यभूति वंश (वर्धन वंश) ने 6वीं और 7वीं शताब्दी के दौरान उत्तर भारत पर शासन किया, जिसका विकास गुप्त साम्राज्य के पतन के बाद हुआ। इसका मुख्य क्षेत्र वर्तमान हरियाणा में था, जिसकी राजधानी स्थानेश्वर (थानेश्वर) थी। हर्षवर्धन के शासनकाल में राजवंश अपने चरम पर पहुंच गया, जिन्होंने 606 से 647 ई. तक शासन किया। कन्नौज में अपनी राजधानी के साथ उनका साम्राज्य उत्तर और उत्तर-पश्चिमी भारत के अधिकांश हिस्सों में फैला हुआ था, जो पंजाब से उत्तरी उड़ीसा और हिमालय से नर्मदा नदी तक फैला हुआ था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. UPSC is a Constitutional Body to conduct examinations for appointments to the services of the Union and the services of the State, respectively, under Article ______</p>",
                    question_hi: "<p>34. यूपीएससी (UPSC) एक संवैधानिक निकाय है जो अनुच्छेद _____ के तहत क्रमशः संघ की सेवाओं और राज्य की सेवाओं हेतु नियुक्&zwj;तियों के लिए परीक्षा आयोजित करता है।</p>",
                    options_en: [
                        "<p>320</p>",
                        "<p>324</p>",
                        "<p>328</p>",
                        "<p>332</p>"
                    ],
                    options_hi: [
                        "<p>320</p>",
                        "<p>324</p>",
                        "<p>328</p>",
                        "<p>332</p>"
                    ],
                    solution_en: "<p>34.(a) <strong>Article 320.</strong> Article 324 establishes a single Commission to oversee elections to the Central Legislature, including both the Upper and Lower Houses. Article 328 grants state legislatures the authority to create provisions for elections to their respective legislatures, including the preparation of electoral rolls, delimitation of constituencies, and other arrangements for the constitution of the legislature. Article 332 reserves seats in state legislative assemblies for Scheduled Castes and Scheduled Tribes.</p>",
                    solution_hi: "<p>34.(a) <strong>अनुच्छेद 320. </strong>अनुच्छेद 324 केंद्रीय विधानमंडल के चुनावों की देखरेख के लिए एक एकल आयोग की स्थापना करता है, जिसमें ऊपरी और निचले दोनों सदन शामिल हैं। अनुच्छेद 328 राज्य विधानमंडलों को अपने संबंधित विधानमंडलों के चुनावों के लिए प्रावधान बनाने का अधिकार देता है, जिसमें मतदाता सूची तैयार करना, निर्वाचन क्षेत्रों का परिसीमन और विधानमंडल के गठन के लिए अन्य व्यवस्थाएँ शामिल हैं। अनुच्छेद 332 अनुसूचित जातियों और अनुसूचित जनजातियों के लिए राज्य विधान सभाओं में सीटें आरक्षित करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. The Tripartite struggle happened among the Palas, the Pratiharas and the _______.</p>",
                    question_hi: "<p>35. पालों, प्रतिहारों और ______ के बीच त्रिपक्षीय संघर्ष हुआ।</p>",
                    options_en: [
                        "<p>Chandelas</p>",
                        "<p>Maukharis</p>",
                        "<p>Rashtrakutas</p>",
                        "<p>Chalukyas</p>"
                    ],
                    options_hi: [
                        "<p>चंदेलों</p>",
                        "<p>मौखरियों</p>",
                        "<p>राष्ट्रकूटों</p>",
                        "<p>चालुक्यों</p>"
                    ],
                    solution_en: "<p>35.(c) <strong>Rashtrakutas. </strong>The tripartite struggle in northern India from the 8th to 10th century involved the Rashtrakutas from the south, the Pala dynasty of Bengal, and the Gurjara Pratihara dynasty ruling from Malwa. This prolonged conflict over control of Kannauj between the three dynasties is known as the tripartite struggle. Lasting nearly two hundred years, it ultimately concluded with Gurjara-Pratihara ruler Nagabhata II, who established Kannauj as the capital of his kingdom.</p>",
                    solution_hi: "<p>35.(c)<strong> राष्ट्रकूट। </strong>8वीं से 10वीं शताब्दी तक उत्तर भारत में त्रिपक्षीय संघर्ष में दक्षिण के राष्ट्रकूट, बंगाल के पाल वंश और मालवा से शासन करने वाले गुर्जर प्रतिहार वंश शामिल थे। कन्नौज पर नियंत्रण को लेकर तीन राजवंशों के बीच लंबे समय तक चले इस संघर्ष को त्रिपक्षीय संघर्ष के रूप में जाना जाता है। लगभग दो सौ वर्षों तक चलने वाला यह संघर्ष अंततः गुर्जर-प्रतिहार शासक नागभट्ट द्वितीय के साथ समाप्त हुआ, जिन्होंने कन्नौज को अपने राज्य की राजधानी के रूप में स्थापित किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. What is the unit of specific resistance ?</p>",
                    question_hi: "<p>36. विशिष्ट प्रतिरोध का मात्रक क्या होता है ?</p>",
                    options_en: [
                        "<p>Farad</p>",
                        "<p>Ampere</p>",
                        "<p>Coulomb</p>",
                        "<p>Ohm meter</p>"
                    ],
                    options_hi: [
                        "<p>फैरड</p>",
                        "<p>एंपियर</p>",
                        "<p>कूलॉम</p>",
                        "<p>ओम मीटर</p>"
                    ],
                    solution_en: "<p>36.(d) <strong>Ohm meter. </strong>Specific resistance is the resistance per unit length and cross-sectional area when a known voltage is applied. Farad is the unit of capacitance, and a capacitor has 1 F when 1 coulomb (C) of charge changes the potential between its plates by 1 volt (V). The ampere (A) is the SI unit of electric current. Coulomb (C) is the standard unit of electric charge in the International System of Units (SI).</p>",
                    solution_hi: "<p>36.(d) <strong>ओम मीटर।</strong> विशिष्ट प्रतिरोध एक ज्ञात वोल्टेज लागू होने पर प्रति इकाई लंबाई और क्रॉस-सेक्शनल क्षेत्र का प्रतिरोध है। फैराड धारिता की इकाई है, और एक संधारित्र में 1 F होता है जब 1 कूलम्ब (C) आवेश इसकी प्लेटों के बीच के विभव को 1 वोल्ट (V) से बदल देता है। एम्पीयर (A) विद्युत धारा की SI इकाई है। कूलम्ब (C) अंतर्राष्ट्रीय इकाइयों की प्रणाली (SI) में विद्युत आवेश की मानक इकाई है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Subansiri, Jia Bharali, Dhansiri and Puthimari are the major tributaries of which river ?</p>",
                    question_hi: "<p>37. सुबनसिरी, जिया भराली, धनसीरी और पुठिमारी किस नदी की प्रमुख सहायक नदियाँ हैं ?</p>",
                    options_en: [
                        "<p>Godavari</p>",
                        "<p>Mahanadi</p>",
                        "<p>Brahmaputra</p>",
                        "<p>Bhagirathi</p>"
                    ],
                    options_hi: [
                        "<p>गोदावरी</p>",
                        "<p>महानदी</p>",
                        "<p>ब्रह्मपुत्र</p>",
                        "<p>भागीरथी</p>"
                    ],
                    solution_en: "<p>37.(c) <strong>Brahmaputra. </strong>The principal tributaries of the rivers are as follows: Godavari: Pravara, Purna, Manjra, Penganga, Wardha, Wainganga, Pranhita (the combined flow of Wainganga, Penganga, and Wardha), Indravati, Maner, and Sabri. Mahanadi: Seonath, Hasdeo, Mand, and Ib join Mahanadi from the left, while Ong, Tel, and Jonk join from the right. Bhagirathi: Bhilangna River, Kedar Ganga, Jadh Ganga, etc.</p>",
                    solution_hi: "<p>37.(c)<strong> ब्रह्मपुत्र। </strong>नदियों की प्रमुख सहायक नदियाँ इस प्रकार हैं: गोदावरी: प्रवरा, पूर्णा, मांजरा, पेंगंगा, वर्धा, वैनगंगा, प्राणहिता (वेनगंगा, पेंगंगा और वर्धा का संयुक्त प्रवाह), इंद्रावती, मनेर और साबरी। महानदी: सियोनाथ, हसदेव, मांड और इब बाईं ओर से महानदी में मिलती हैं, जबकि ओंग, तेल और जोंक दाईं ओर से मिलती हैं। भागीरथी: भिलंगना नदी, केदार गंगा, जाध गंगा, आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. The concept of &lsquo;Independence of judiciary&rsquo; in the Indian Constitution is taken from the Constitution of:</p>",
                    question_hi: "<p>38. भारतीय संविधान में \'न्यायपालिका की स्वतंत्रता\' की अवधारणा _____के संविधान से ली गई है|</p>",
                    options_en: [
                        "<p>Ireland</p>",
                        "<p>France</p>",
                        "<p>The USA</p>",
                        "<p>Britain</p>"
                    ],
                    options_hi: [
                        "<p>आयरलैंड</p>",
                        "<p>फ्रांस</p>",
                        "<p>संयुक्त राज्य अमेरिका</p>",
                        "<p>ब्रिटेन</p>"
                    ],
                    solution_en: "<p>38.(c) <strong>The USA. </strong>Some features of the Indian Constitution borrowed - From USA : Fundamental Rights, President&rsquo;s impeachment process, Office of the Vice-President. From Britain: Parliamentary system, Single citizenship, Bicameralism. From Ireland: Directive Principles of State Policy, Election method for the President, Rajya Sabha nominations. From France: Ideals of Liberty, Equality, and Fraternity in the Preamble, Concept of a Republic.</p>",
                    solution_hi: "<p>38.(c) <strong>संयुक्त राज्य अमेरिका। </strong>भारतीय संविधान द्वारा अपनाई गई विशेषताएँ - अमेरिका : मौलिक अधिकार, राष्ट्रपति की महाभियोग प्रक्रिया, उपराष्ट्रपति का कार्यालय। ब्रिटेन: संसदीय प्रणाली, एकल नागरिकता, द्विसदनीयता। आयरलैंड: राज्य के नीति निर्देशक सिद्धांत, राष्ट्रपति के लिए चुनाव पद्धति, राज्यसभा नामांकन। फ्रांस: प्रस्तावना में स्वतंत्रता, समानता और बंधुत्व के आदर्श, गणतंत्र की अवधारणा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. What is the recent achievement of India regarding the Eurodrone Programme in January 2025 ?</p>",
                    question_hi: "<p>39. जनवरी 2025 में यूरोड्रोन कार्यक्रम के संबंध में भारत की हालिया उपलब्धि क्या है ?</p>",
                    options_en: [
                        "<p>Signed as a full member</p>",
                        "<p>Joined as an observer state</p>",
                        "<p>Partnered for manufacturing drones</p>",
                        "<p>Hosted the Eurodrone summit</p>"
                    ],
                    options_hi: [
                        "<p>पूर्ण सदस्य के रूप में हस्ताक्षर किए</p>",
                        "<p>पर्यवेक्षक राज्य के रूप में शामिल हुए</p>",
                        "<p>ड्रोन निर्माण के लिए भागीदारी की</p>",
                        "<p>यूरोड्रोन शिखर सम्मेलन की मेज़बानी की</p>"
                    ],
                    solution_en: "<p>39.(b) <strong>Joined as an observer state. </strong>India has joined the Medium Altitude Long Endurance Remotely Piloted Aircraft System (MALE RPAS) or Eurodrone programme as an Observer State, becoming the second Asia-Pacific country after Japan to do so. This status grants access to technical data and procurement opportunities but excludes India from decision-making on design, development, or workshare distribution.</p>",
                    solution_hi: "<p>39.(b) <strong>पर्यवेक्षक राज्य के रूप में शामिल हुए।</strong> भारत ने मीडियम एल्टीट्यूड लॉन्ग एंड्योरेंस रिमोटली पायलटेड एयरक्राफ्ट सिस्टम (MALE RPAS) या यूरोड्रोन कार्यक्रम में पर्यवेक्षक राज्य (Observer State) के रूप में शामिल होकर जापान के बाद ऐसा करने वाला दूसरा एशिया-प्रशांत देश बन गया है। इस स्थिति के तहत, भारत को तकनीकी डेटा और खरीद अवसरों तक पहुंच मिलेगी, लेकिन डिजाइन, विकास या कार्य विभाजन से जुड़े निर्णय लेने में भागीदारी नहीं होगी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Match the points under Column A with those under Column B.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940744.png\" alt=\"rId48\" width=\"221\" height=\"140\"></p>",
                    question_hi: "<p>40. कॉलम A में दिए गए बिंदुओं का कॉलम B के बिंदुओं से मिलान कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782940918.png\" alt=\"rId49\" width=\"332\" height=\"157\"></p>",
                    options_en: [
                        "<p>i-b, ii-c, iii-a, iv-d</p>",
                        "<p>i-a, ii-b, iii-c, iv-d</p>",
                        "<p>i-d, ii-c, iii-b, iv-a</p>",
                        "<p>i-b, ii-a, iii-d, iv-c</p>"
                    ],
                    options_hi: [
                        "<p>i-b, ii-c, iii-a, iv-d</p>",
                        "<p>i-a, ii-b, iii-c, iv-d</p>",
                        "<p>i-d, ii-c, iii-b, iv-a</p>",
                        "<p>i-b, ii-a, iii-d, iv-c</p>"
                    ],
                    solution_en: "<p>40.(d) <strong>i-b, ii-a, iii-d, iv-c. </strong>Hexapods, the largest clade of arthropods, include most existing arthropod species. Crustaceans, part of the subphylum Crustacea, are invertebrates with about 45,000 species worldwide. Myriapods, from the subphylum Myriapoda, consist of terrestrial arthropods like millipedes and centipedes, with around 13,000 species. Chelicerata, another arthropod subphylum, includes both terrestrial and marine species.</p>",
                    solution_hi: "<p>40.(d)<strong> i-b, ii-a, iii-d, iv-c. </strong>हेक्सापोड्स, आर्थ्रोपोड्स का सबसे बड़ा समूह है, जिसमें अधिकांश मौजूदा आर्थ्रोपोड प्रजातियां शामिल हैं। क्रस्टेशियन, उपसंघ क्रस्टेशिया का हिस्सा हैं, जो अकशेरुकी हैं जिनकी विश्व भर में लगभग 45,000 प्रजातियां हैं। माइरियापोडा उपसंघ से संबंधित माइरियापोड्स में मिलीपीड और सेंटीपीड जैसे स्थलीय आर्थ्रोपोड शामिल हैं, जिनकी लगभग 13,000 प्रजातियां हैं। चेलिसेराटा, एक अन्य आर्थ्रोपोड उपसंघ है, जिसमें स्थलीय और समुद्री दोनों प्रजातियां शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. In the context of vernier calliper, an internal jaw is used to measure:</p>",
                    question_hi: "<p>41. वर्नियर कैलीपर के संदर्भ में आंतरिक जबड़े (जॉ) का उपयोग निम्न के मापन के लिए किया जाता है:</p>",
                    options_en: [
                        "<p>the length correct up to 1 mm</p>",
                        "<p>the depth of a beaker</p>",
                        "<p>the length of a rod and diameter of a sphere</p>",
                        "<p>the internal diameter of a hollow cylinder and pipes</p>"
                    ],
                    options_hi: [
                        "<p>लंबाई, जो 1 mm तक सटीक हो</p>",
                        "<p>बीकर की गहराई</p>",
                        "<p>एक छड़ की लंबाई और एक गोले का व्यास</p>",
                        "<p>एक खोखले बेलन और पाइप का आंतरिक व्यास</p>"
                    ],
                    solution_en: "<p>41.(d) A Vernier Caliper is a precision instrument used for accurate measurements in various applications. Key parts include- Depth Probe: Used for measuring the depth of objects or holes. Main Scale: Provides measurements in millimeters. Vernier Scale: Offers measurements with an accuracy of up to one decimal place in millimeters.</p>",
                    solution_hi: "<p>41.(d) वर्नियर कैलिपर एक सटीक उपकरण है जिसका उपयोग विभिन्न अनुप्रयोगों में सटीक माप के लिए किया जाता है। मुख्य भागों में शामिल हैं- गहराई की जांच: वस्तुओं या छिद्रों की गहराई मापने के लिए उपयोग किया जाता है। मुख्य पैमाना: मिलीमीटर में माप प्रदान करता है। वर्नियर स्केल: मिलीमीटर में एक दशमलव स्थान तक की सटीकता के साथ माप प्रदान करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which part of the Indian Constitution deals with the election of the Vice President ?</p>",
                    question_hi: "<p>42. भारतीय संविधान का कौन-सा भाग उपराष्ट्रपति के निर्वाचन से संबंधित है ?</p>",
                    options_en: [
                        "<p>Part IV</p>",
                        "<p>Part III</p>",
                        "<p>Part VI</p>",
                        "<p>Part V</p>"
                    ],
                    options_hi: [
                        "<p>भाग IV</p>",
                        "<p>भाग III</p>",
                        "<p>भाग VI</p>",
                        "<p>भाग V</p>"
                    ],
                    solution_en: "<p>42.(d) <strong>Part V</strong> (Article 52- 151). Article 66 - Election of Vice President. The Vice-President shall be elected by the members of an electoral college consisting of the members of both Houses of Parliament in accordance with the system of proportional representation by means of the single transferable vote and the voting at such election shall be by secret ballot. Part IV (Articles 36&ndash;51) - Contains the Directive Principles of State Policy (DPSP). Part III (Articles 12 to 35) - Fundamental Rights. Part VI (Article 152 - 237) - Addresses the functioning of states, including the executive, legislature, and judiciary.</p>",
                    solution_hi: "<p>42.(d) <strong>भाग V </strong>(अनुच्छेद 52-151)। अनुच्छेद 66 - उपराष्ट्रपति का चुनाव। उपराष्ट्रपति का चुनाव एकल संक्रमणीय मत के माध्यम से आनुपातिक प्रतिनिधित्व प्रणाली के अनुसार संसद के दोनों सदनों के सदस्यों से मिलकर बने निर्वाचक मंडल के सदस्यों द्वारा किया जाता है और ऐसे चुनाव में मतदान गुप्त मतदान द्वारा होता है। भाग IV (अनुच्छेद 36-51) - राज्य नीति के निर्देशक सिद्धांत (DPSP) शामिल हैं। भाग III (अनुच्छेद 12 से 35) - मौलिक अधिकार। भाग VI (अनुच्छेद 152 - 237) - कार्यपालिका, विधायिका और न्यायपालिका सहित राज्यों के कामकाज को संबोधित करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which of the following statements is NOT correct ?</p>",
                    question_hi: "<p>43. निम्नलिखित में से कौन-सा कथन सही नहीं है ?</p>",
                    options_en: [
                        "<p>Light year is the unit of distance.</p>",
                        "<p>Light year is the distance travelled by light in one year.</p>",
                        "<p>Light year is the unit of time.</p>",
                        "<p>Angstrom is unit of length.</p>"
                    ],
                    options_hi: [
                        "<p>प्रकाशवर्ष दूरी की इकाई है।</p>",
                        "<p>प्रकाशवर्ष एक वर्ष में प्रकाश द्वारा तय की गई दूरी है।</p>",
                        "<p>प्रकाशवर्ष समय की इकाई है।</p>",
                        "<p>एंगस्ट्रम लंबाई की इकाई</p>"
                    ],
                    solution_en: "<p>43.(c) A light year is the distance light travels in one year and serves as a convenient unit for measuring large astronomical distances, approximately 9 trillion kilometers (9 &times; 10&sup1;&sup2; km). A parsec (parallactic second) is the largest practical distance unit in astronomy, defined as the distance at which an arc of 1 astronomical unit subtends an angle of 1 second of arc, equating to 3.08 &times; 10&sup1;⁶ meters. An astronomical unit (AU) is the mean distance from the Earth to the Sun, used to measure distances to planets, and is equal to 1.496 &times; 10&sup1;&sup1; meters.</p>",
                    solution_hi: "<p>43.(c) एक प्रकाश वर्ष वह दूरी है जो प्रकाश एक वर्ष में तय करता है और बड़ी खगोलीय दूरियों को मापने के लिए एक सुविधाजनक इकाई के रूप में कार्य करता है, लगभग 9 ट्रिलियन किलोमीटर (9 &times; 10&sup1;&sup2; किमी)। एक पारसेक (पैरालैक्टिक सेकंड) खगोल विज्ञान में सबसे बड़ी व्यावहारिक दूरी इकाई है, जिसे उस दूरी के रूप में परिभाषित किया जाता है जिस पर 1 खगोलीय इकाई का चाप 1 सेकंड के चाप के कोण को घटाता है, जो 3.08 &times; 10&sup1;⁶ मीटर के बराबर होता है। एक खगोलीय इकाई (AU) पृथ्वी से सूर्य तक की औसत दूरी है, जिसका उपयोग ग्रहों की दूरी मापने के लिए किया जाता है, और यह 1.496 &times; 10&sup1;&sup1; मीटर के बराबर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. What is the economic justification for subsidies in the agriculture sector ?</p>",
                    question_hi: "<p>44. कृषि क्षेत्र में सब्सिडी का आर्थिक औचित्य क्या है ?</p>",
                    options_en: [
                        "<p>It is mostly used to provide benefits for rich farmers.</p>",
                        "<p>It is a part of government expenditure in the service sector.</p>",
                        "<p>It is an incentive to provide the benefits of advanced technology and decrease the cost of production.</p>",
                        "<p>It is used to provide benefits for capital goods producers in agriculture.</p>"
                    ],
                    options_hi: [
                        "<p>इसका उपयोग अधिकतर अमीर किसानों को लाभ प्रदान करने के लिए किया जाता है।</p>",
                        "<p>यह सेवा क्षेत्र में सरकारी व्यय का एक भाग है।</p>",
                        "<p>यह उन्नत प्रौद्योगिकी का लाभ प्रदान करने और उत्पादन की लागत को कम करने के लिए एक प्रोत्साहन है।</p>",
                        "<p>इसका उपयोग कृषि में पूंजीगत माल के उत्पादकों को लाभ प्रदान करने के लिए किया जाता है।</p>"
                    ],
                    solution_en: "<p>44.(c) Agricultural subsidies are economically justified to enhance food security and agricultural development. They help farmers boost production, leading to food security, rural development, sustainable farming, and equity. Agricultural input subsidies make inputs available below market costs to incentivize adoption, increase productivity and profitability, improve food availability and access, and ultimately reduce poverty while stimulating economic growth.</p>",
                    solution_hi: "<p>44.(c) खाद्य सुरक्षा और कृषि विकास को बढ़ाने के लिए कृषि सब्सिडी आर्थिक रूप से उचित है। वे किसानों को उत्पादन बढ़ाने में मदद करते हैं, जिससे खाद्य सुरक्षा, ग्रामीण विकास, टिकाऊ खेती और समानता को बढ़ावा मिलता है। कृषि इनपुट सब्सिडी अपनाने को प्रोत्साहित करने, उत्पादकता और लाभप्रदता बढ़ाने, खाद्य उपलब्धता और पहुंच में सुधार करने और अंततः आर्थिक विकास को प्रोत्साहित करते हुए गरीबी को कम करने के लिए बाजार लागत से कम इनपुट उपलब्ध कराती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following sectors in India faces seasonal unemployment ?</p>",
                    question_hi: "<p>45. भारत में निम्नलिखित में से कौन-सा क्षेत्र मौसमी बेरोजगारी का सामना करता है ?</p>",
                    options_en: [
                        "<p>Agriculture</p>",
                        "<p>Manufacturing</p>",
                        "<p>Medical</p>",
                        "<p>Information technology</p>"
                    ],
                    options_hi: [
                        "<p>कृषि</p>",
                        "<p>विनिर्माण</p>",
                        "<p>चिकित्सा</p>",
                        "<p>सूचना प्रौद्योगिकी</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Agriculture. </strong>Seasonal unemployment occurs when people are jobless during specific seasons when labor demand is low. It arises from a mismatch between the skills of the workforce and those required for available jobs. Unemployment is a situation in which individuals are ready and willing to work at the prevailing rate of wages but cannot get the work.</p>",
                    solution_hi: "<p>45.(a)<strong> कृषि। </strong>मौसमी बेरोज़गारी तब होती है जब लोग विशिष्ट मौसमों के दौरान बेरोज़गार होते हैं जब श्रम की मांग कम होती है। यह कार्यबल के कौशल और उपलब्ध नौकरियों के लिए आवश्यक कौशल के बीच बेमेल से उत्पन्न होता है। बेरोज़गारी एक ऐसी स्थिति है जिसमें व्यक्ति मौजूदा मज़दूरी दर पर काम करने के लिए तैयार और इच्छुक होते हैं, लेकिन उन्हें काम नहीं मिल पाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. To promote agriculture, Muhammad Tughlaq founded which of the following new ministries ?</p>",
                    question_hi: "<p>46. कृषि को बढ़ावा देने के लिए मुहम्मद तुग़लक ने निम्नलिखित में से कौन-से नए मंत्रालय की स्थापना की थी ?</p>",
                    options_en: [
                        "<p>Diwan-i-Arz</p>",
                        "<p>Diwan-i Amir-i kohi</p>",
                        "<p>Diwan-i-Insha</p>",
                        "<p>Diwan-i-Risalat</p>"
                    ],
                    options_hi: [
                        "<p>दीवान-ए-अर्ज़</p>",
                        "<p>दीवान-ए अमीर-ए कोही</p>",
                        "<p>दीवान-ए-इंशा</p>",
                        "<p>दीवान-ए-रिसालत</p>"
                    ],
                    solution_en: "<p>46.(b) <strong>Diwan-i Amir-i kohi. </strong>The Diwan-i-Kohi aimed to increase land under cultivation. Muhammad Tughlaq also promoted agriculture by abolishing or reducing agrarian cesses and introducing agricultural loans called Sondhar. The Diwan-i-Arz, established by Ghiyas ud din Balban, was the military department of the Delhi Sultanate. The Diwan-i-Insha managed royal correspondence, decrees, letters, and official documents. The Diwan-i-Risalat handled foreign affairs and religious matters, overseeing diplomacy, protocol, and sending ambassadors.</p>",
                    solution_hi: "<p>46.(b) <strong>दीवान-ए अमीर-ए कोही।</strong> दीवान-ए-कोही का उद्देश्य खेती के तहत भूमि को बढ़ाना था। मुहम्मद तुगलक ने कृषि उपकरों को समाप्त करके या कम करके तथा सोंधर नामक कृषि ऋण शुरू करके कृषि को बढ़ावा दिया। ग़यासुद्दीन बलबन द्वारा स्थापित दीवान-ए-अर्ज़, दिल्ली सल्तनत का सैन्य विभाग था। दीवान-ए-इंशा शाही पत्राचार, फरमान, पत्र और आधिकारिक दस्तावेजों का प्रबंधन करता था। दीवान-ए-रिसालत विदेशी मामलों और धार्मिक मामलों को संभालता था, कूटनीति, प्रोटोकॉल की देखरेख करता था और राजदूत भेजता था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. In the context of cell division, which chromosomal behaviour takes place at the leptotene stage ?</p>",
                    question_hi: "<p>47. कोशिका विभाजन के संदर्भ में तनुपट्ट (लिप्टोटीन) अवस्था में कौन-सा गुणसूत्र संबंधी व्यवहार होता है ?</p>",
                    options_en: [
                        "<p>Chromosomes are un-synapsed</p>",
                        "<p>Synapsis is complete</p>",
                        "<p>Homologous chromosomes pair</p>",
                        "<p>Chromosomes begin to condense</p>"
                    ],
                    options_hi: [
                        "<p>गुणसूत्रों का सूत्रयुग्मन रहित होना</p>",
                        "<p>सूत्रयुग्मन का पूर्ण हो जाना</p>",
                        "<p>समजात गुणसूत्र जोड़े बनना</p>",
                        "<p>गुणसूत्र का संघनित होना</p>"
                    ],
                    solution_en: "<p>47.(d) <strong>Chromosomes begin to condense. </strong>The leptotene stage is the first of five substages in prophase I of meiosis, a specialized cell division process that reduces the number of chromosomes by half. During this stage, chromosomes manifest as long, thin threads. Meiosis produces haploid gametes in sexually reproducing organisms, contrasting with mitosis, where a single cell divides into two identical daughter cells. Mitosis comprises several stages: Prophase, Metaphase, Anaphase, and Telophase.</p>",
                    solution_hi: "<p>47.(d) <strong>गुणसूत्र का संघनित होना।</strong> लेप्टोटीन चरण अर्धसूत्रीविभाजन के प्रोफ़ेज़ में पाँच उप-चरणों में से पहला है, एक विशेष कोशिका विभाजन प्रक्रिया जो गुणसूत्रों की संख्या को आधे से कम कर देती है। इस चरण के दौरान, गुणसूत्र लंबे, पतले धागों के रूप में व्यवस्थित होते हैं। अर्धसूत्रीविभाजन यौन प्रजनन करने वाले जीवों में अगुणित युग्मक उत्पन्न करता है, जो माइटोसिस के विपरीत है, जहाँ एक एकल कोशिका दो समान संतति कोशिकाओं में विभाजित होती है। माइटोसिस में कई चरण शामिल हैं: प्रोफ़ेज़, मेटाफ़ेज़, एनाफ़ेज़ और टेलोफ़ेज़।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. What is the name of the satellite internet company that has been granted permission to operate in India ?</p>",
                    question_hi: "<p>48. उस सैटेलाइट इंटरनेट कंपनी का नाम क्या है जिसे भारत में संचालन की अनुमति दी गई है ?</p>",
                    options_en: [
                        "<p>OneWeb</p>",
                        "<p>Starlink</p>",
                        "<p>Viasat</p>",
                        "<p>Amazon Kuiper</p>"
                    ],
                    options_hi: [
                        "<p>वनवेब</p>",
                        "<p>स्टारलिंक</p>",
                        "<p>वायसैट</p>",
                        "<p>अमेज़न क्यूपर</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>Starlink.</strong><br>Starlink, a division of SpaceX, has received regulatory approval to offer satellite-based internet services in India, especially in rural and remote areas. Elon Musk is the founder and CEO of SpaceX, which launched Starlink to deliver high-speed internet globally through satellite technology.</p>",
                    solution_hi: "<p>48.(b) <strong>स्टारलिंक।</strong><br>स्टारलिंक, जो स्पेसएक्स का एक डिवीजन है, को भारत में सैटेलाइट-आधारित इंटरनेट सेवाएं प्रदान करने के लिए नियामक मंजूरी मिल गई है, खासकर ग्रामीण और दूरदराज के क्षेत्रों में। एलन मस्क स्पेसएक्स के संस्थापक और सीईओ हैं, जिन्होंने वैश्विक स्तर पर उच्च गति इंटरनेट प्रदान करने के लिए स्टारलिंक की शुरुआत की थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. How many carbon dioxide and water molecules will be there in the product side, if the following equation is made balanced ?<br>C<sub>2</sub>H<sub>5</sub>OH + O<sub>2</sub> &rarr; CO<sub>2</sub> + H<sub>2</sub>O</p>",
                    question_hi: "<p>49. यदि निम्नलिखित समीकरण को संतुलित किया जाए, तो उत्पाद पक्ष में कार्बन डाइऑक्साइड और पानी के अणुओं की संख्या कितनी होगी ?<br>C<sub>2</sub>H<sub>5</sub>OH + O<sub>2</sub> &rarr; CO<sub>2</sub> + H<sub>2</sub>O</p>",
                    options_en: [
                        "<p>3 and 6, respectively</p>",
                        "<p>1 and 1, respectively</p>",
                        "<p>2 and 3, respectively</p>",
                        "<p>3 and 2, respectively</p>"
                    ],
                    options_hi: [
                        "<p>क्रमशः 3 और 6</p>",
                        "<p>क्रमशः 1 और 1</p>",
                        "<p>क्रमशः 2 और 3</p>",
                        "<p>क्रमशः 3 और 2</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>2 and 3, respectively.</strong> A chemical equation represents a chemical reaction, with reactants on the left and products on the right, using their respective chemical symbols. An equation is considered balanced when the number of each atom is equal on both sides, adhering to the law of conservation of mass. This law states that atoms of the reactants are rearranged to form products, while the total mass remains constant throughout the reaction.</p>",
                    solution_hi: "<p>49.(c) <strong>क्रमशः 2 और 3. </strong>रासायनिक समीकरण रासायनिक अभिक्रिया को दर्शाता है, जिसमें अभिकारक बायी ओर और उत्पाद दायी ओर होते हैं, जो उनके संबंधित रासायनिक प्रतीकों का उपयोग करते हैं। एक समीकरण को संतुलित माना जाता है जब द्रव्यमान के संरक्षण के नियम का पालन करते हुए, प्रत्येक परमाणु की संख्या दोनों ओर बराबर होती है। यह नियम बताता है कि अभिकारकों के परमाणुओं को उत्पाद बनाने के लिए पुनर्व्यवस्थित किया जाता है, जबकि कुल द्रव्यमान पूर्ण अभिक्रिया के दौरान स्थिर रहता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Nirupama Sanjeev is a famous ______ player of India ?</p>",
                    question_hi: "<p>50. निरुपमा संजीव भारत की प्रसिद्ध ______ खिलाड़ी हैं ?</p>",
                    options_en: [
                        "<p>badminton</p>",
                        "<p>swimming</p>",
                        "<p>tennis</p>",
                        "<p>chess</p>"
                    ],
                    options_hi: [
                        "<p>बैडमिंटन</p>",
                        "<p>तैराकी</p>",
                        "<p>टेनिस</p>",
                        "<p>शतरंज</p>"
                    ],
                    solution_en: "<p>50.(c) <strong>Tennis. </strong>In the 1998 Australian Open, Nirupama Sanjeev became the second Indian woman (after Nirupama Mankad) in the Open era to compete in a Grand Slam main draw, the first in singles, and the first to win a match, defeating Gloria Pizzichini. Notable Indian tennis players include Leander Paes, Sania Mirza, Mahesh Bhupathi, Rohan Bopanna, Sumit Nagal, and Kriish Tyagi.</p>",
                    solution_hi: "<p>50.(c) <strong>टेनिस। </strong>1998 के ऑस्ट्रेलियन ओपन में, निरुपमा संजीव ओपन युग में ग्रैंड स्लैम मुख्य ड्रॉ में प्रतिस्पर्धा करने वाली दूसरी भारतीय महिला (निरुपमा मांकड़ के बाद) बनीं, एकल में पहली और ग्लोरिया पिज़िचिनी को हराकर मैच जीतने वाली प्रथम महिला बनी। प्रसिद्ध भारतीय टेनिस खिलाड़ियों में लिएंडर पेस, सानिया मिर्ज़ा, महेश भूपति, रोहन बोपन्ना, सुमित नागल और कृष त्यागी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. A merchant sells 195 kg of wheat for ₹10,260 at a profit of ₹4.50 per kg of wheat. What is the cost price&nbsp;(in ₹, to the nearest integer) of 15 kg of wheat ?</p>",
                    question_hi: "<p>51. एक व्यापारी 195 kg गेहूं ₹4.50 प्रति किलोग्राम गेहूं के लाभ पर ₹10,260 में बेचता है। 15 kg गेहूं का क्रय मूल्य (₹ में, निकटतम पूर्णांक तक) क्या है ?</p>",
                    options_en: [
                        "<p>682</p>",
                        "<p>750</p>",
                        "<p>722</p>",
                        "<p>595</p>"
                    ],
                    options_hi: [
                        "<p>682</p>",
                        "<p>750</p>",
                        "<p>722</p>",
                        "<p>595</p>"
                    ],
                    solution_en: "<p>51.(c)<br>SP of 1 kg wheat = <math display=\"inline\"><mfrac><mrow><mn>10260</mn></mrow><mrow><mn>195</mn></mrow></mfrac></math> = ₹52.6<br>Then, CP of 1 kg wheat = 52.6 - 4.5 = ₹48.1<br>So, CP of 15 kg wheat = 15 &times; 48.1 = 721.5 <math display=\"inline\"><mo>&#8776;</mo></math> ₹722</p>",
                    solution_hi: "<p>51.(c)<br>1 किलो गेहूं का विक्रय मूल्य = <math display=\"inline\"><mfrac><mrow><mn>10260</mn></mrow><mrow><mn>195</mn></mrow></mfrac></math> = ₹52.6<br>फिर, 1 किलो गेहूं का क्रय मूल्य = 52.6 - 4.5 = ₹48.1<br>तो, 15 किलो गेहूं का क्रय मूल्य= 15 &times; 48.1 = 721.5 <math display=\"inline\"><mo>&#8776;</mo></math> ₹722</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. 12 sin<sup>2</sup> A + 2 cos<sup>2</sup> A = 7, where 0&deg; &lt; A &lt; 180&deg;. Then cot A is:</p>",
                    question_hi: "<p>52. 12 sin<sup>2</sup> A + 2 cos<sup>2</sup> A = 7 है, जहाँ 0 &lt; A &lt; 180&deg; है। तो cot A का मान क्या है ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mo>&#177;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></p>",
                        "<p><math display=\"inline\"><mo>&#177;</mo></math> 1</p>",
                        "<p><math display=\"inline\"><mo>&#177;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>",
                        "<p><math display=\"inline\"><mo>&#177;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mo>&#177;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math></p>",
                        "<p><math display=\"inline\"><mo>&#177;</mo></math> 1</p>",
                        "<p><math display=\"inline\"><mo>&#177;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math></p>",
                        "<p><math display=\"inline\"><mo>&#177;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>"
                    ],
                    solution_en: "<p>52.(b)<br>12 sin<sup>2</sup> A + 2 cos<sup>2</sup> A = 7-------(i)<br>12 (1 - cos<sup>2</sup> A) + 2 cos<sup>2</sup> A = 7<br>12 - 12 cos<sup>2</sup> A + 2 cos<sup>2</sup> A = 7<br>-10cos<sup>2</sup> A = - 5<br>cos<sup>2</sup> A = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> --------(ii)<br>cosA = <math display=\"inline\"><mo>&#177;</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>From equation (i) and (ii)<br>12 sin<sup>2</sup> A + 2 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 7<br>12 sin<sup>2</sup>A = 6<br>sin<sup>2</sup> A = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &rArr; sin A = <math display=\"inline\"><mo>&#177;</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>Now, cot A = <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#177;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle></mrow><mrow><mo>&#177;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle></mrow></mfrac></math> = &plusmn; 1</p>",
                    solution_hi: "<p>52.(b)<br>12 sin<sup>2</sup> A + 2 cos<sup>2</sup> A = 7-------(i)<br>12 (1 - cos<sup>2</sup> A) + 2 cos<sup>2</sup> A = 7<br>12 - 12 cos<sup>2</sup> A + 2 cos<sup>2</sup> A = 7<br>-10cos<sup>2</sup> A = - 5<br>cos<sup>2</sup> A = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> --------(ii)<br>cosA = <math display=\"inline\"><mo>&#177;</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>समीकरण (i) और (ii) से<br>12 sin<sup>2</sup> A + 2 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 7<br>12 sin<sup>2</sup>A = 6<br>sin<sup>2</sup> A = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &rArr; sin A = <math display=\"inline\"><mo>&#177;</mo><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>अब, cot A = <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#177;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle></mrow><mrow><mo>&#177;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></mstyle></mrow></mfrac></math> = &plusmn; 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. In nine games, A wins five games with score differences of 12, 3, 8, 4 and 1, while in the remaining four games, he loses with score differences of -4, -1, -2 and -3. The average score difference of A in all the nine games is:</p>",
                    question_hi: "<p>53. नौ गेमों में, A ने 12, 3, 8, 4 और 1 के स्कोर अंतर के साथ पाँच गेम जीते, जबकि शेष चार गेमों में वह -4, -1, -2 और -3 के स्कोर अंतर के साथ हार गया। सभी नौ गेमों में A का औसत स्कोर अंतर क्या है ?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>4</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>53.(a)<br>The average score difference of A in all the nine games = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>8</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>1</mn><mo>-</mo><mn>4</mn><mo>-</mo><mn>1</mn><mo>-</mo><mn>2</mn><mo>-</mo><mn>3</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>9</mn></mfrac></math> = 2</p>",
                    solution_hi: "<p>53.(a)<br>सभी नौ गेमों में A का औसत स्कोर अंतर = <math display=\"inline\"><mfrac><mrow><mn>12</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>8</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>1</mn><mo>-</mo><mn>4</mn><mo>-</mo><mn>1</mn><mo>-</mo><mn>2</mn><mo>-</mo><mn>3</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>9</mn></mfrac></math> = 2</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Three candidates X, Y and Z participated in an election. X got 30% more votes than Y, whereas Z got 25% more votes than Y. X also overtook Z by 5000 votes. If 71% of the voters voted and no invalid votes were cast, then what was the total number of voters in the voting list ?</p>",
                    question_hi: "<p>54. तीन उम्मीदवारों X, Y और Z ने एक चुनाव में भाग लिया। X को Y से 30% अधिक वोट मिले, जबकि Z को Y से 25% अधिक वोट मिले। X ने Z को भी 5000 वोटों से पीछे छोड़ दिया। यदि 71% मतदाताओं ने मतदान किया और कोई अवैध वोट नहीं डाला गया, तो मतदान सूची में मतदाताओं की कुल संख्या क्या थी ?</p>",
                    options_en: [
                        "<p>5,00,000</p>",
                        "<p>4,05,000</p>",
                        "<p>3,55,000</p>",
                        "<p>4,50,000</p>"
                    ],
                    options_hi: [
                        "<p>5,00,000</p>",
                        "<p>4,05,000</p>",
                        "<p>3,55,000</p>",
                        "<p>4,50,000</p>"
                    ],
                    solution_en: "<p>54.(a) Let the total no. of votes be 100%<br>Casted votes = 71%<br>then,<br>&rArr; x = y &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>130</mn><mn>100</mn></mfrac></math> &rArr; x : y = 13 : 10<br>&rArr; z = y &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac></math> &rArr; z : y = 5 : 4<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; x&nbsp; &nbsp;:&nbsp; y&nbsp; &nbsp;:&nbsp; z<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;13 :&nbsp; 10 :&nbsp; 10<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; :&nbsp; 4&nbsp; &nbsp;:&nbsp; 5<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; ------------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;52 : 40 : 50 or 26 : 20 : 25<br>Now,<br>(26 - 25) % = 1 % = 5000<br>100 % = 500000</p>",
                    solution_hi: "<p>54.(a) माना कि वोटों की कुल संख्या = 100% <br>डाले गए वोट = 71%<br>तब,<br>&rArr; x = y &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>130</mn><mn>100</mn></mfrac></math> &rArr; x : y = 13 : 10<br>&rArr; z = y &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>100</mn></mfrac></math> &rArr; z : y = 5 : 4<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;x&nbsp; &nbsp;:&nbsp; &nbsp;y&nbsp; &nbsp; :&nbsp; z<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;13&nbsp; :&nbsp; 10&nbsp; :&nbsp; 10<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 4&nbsp; &nbsp;:&nbsp; &nbsp;4&nbsp; &nbsp;:&nbsp; 5<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;------------------------------------<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;52&nbsp; :&nbsp; 40&nbsp; : 50 या 26 : 20 : 25<br>अब,<br>(26 - 25) % = 1 % = 5000<br>100 % = 500000</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. PQ is a chord of a circle. The tangent XR at point X on the circle intersects the extension of PQ at point R. Given that XR = 12 cm, PQ = x cm, and QR = (x - 2) cm, find the value of x.</p>",
                    question_hi: "<p>55. PQ एक वृत्त की जीवा है। इस वृत्त पर बिंदु X पर स्पर्श रेखा XR, बिंदु R पर PQ के विस्तार को प्रतिच्छेदित करती है। दिया गया है कि XR = 12 cm, PQ = x cm, और QR = (x - 2) cm है, तो x का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>10 cm</p>",
                        "<p>9 cm</p>",
                        "<p>8 cm</p>",
                        "<p>11 cm</p>"
                    ],
                    options_hi: [
                        "<p>10 cm</p>",
                        "<p>9 cm</p>",
                        "<p>8 cm</p>",
                        "<p>11 cm</p>"
                    ],
                    solution_en: "<p>55.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782941106.png\" alt=\"rId50\" width=\"254\" height=\"137\"><br><strong>Concept used :-</strong> (RX)<sup>2</sup> = RQ &times; RP<br>(12)<sup>2</sup> = (x - 2) &times; (x - 2 + x)<br>144 = (x&nbsp;- 2)(2x - 2)<br>With the help of option put the value of x&nbsp;= 10 cm<br>144 = 8 &times; 18<br>144 = 144 (satisfied)<br>So the value of x&nbsp;will be 10 cm</p>",
                    solution_hi: "<p>55.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782941106.png\" alt=\"rId50\" width=\"254\" height=\"137\"><br><strong>प्रयुक्त अवधारणा :-</strong> (RX)<sup>2</sup> = RQ &times; RP<br>(12)<sup>2</sup> = (x - 2) &times; (x - 2 + x)<br>144 = (x - 2)(2x - 2)<br>विकल्प की सहायता से <math display=\"inline\"><mi>x</mi></math> का मान 10 रखें ।<br>144 = 8 &times; 18<br>144 = 144 (संतुष्ट)<br>अतः x&nbsp;का मान 10 सेमी होगा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Study the given graph and answer the question that follows. The graph represent the depreciation of a car from 2001 to 2007.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782941326.png\" alt=\"rId51\" width=\"314\" height=\"278\"> <br>What is the depreciation of the car from 2001 to 2007 in percentage ?</p>",
                    question_hi: "<p>56. निम्नलिखित ग्राफ का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। <br>ग्राफ में वर्ष 2001 से वर्ष 2007 तक एक कार के मूल्यह्रास को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782941513.png\" alt=\"rId52\" width=\"318\" height=\"273\"> <br>वर्ष 2001 से वर्ष 2007 तक कार का कितने प्रतिशत मूल्यह्रास हुआ था ?</p>",
                    options_en: [
                        "<p>81.32%</p>",
                        "<p>62%</p>",
                        "<p>58%</p>",
                        "<p>75.83%</p>"
                    ],
                    options_hi: [
                        "<p>81.32%</p>",
                        "<p>62%</p>",
                        "<p>58%</p>",
                        "<p>75.83%</p>"
                    ],
                    solution_en: "<p>56.(d)<br>Change in % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24000</mn><mo>-</mo><mn>5800</mn></mrow><mn>24000</mn></mfrac></math> &times; 100 = 75.83%</p>",
                    solution_hi: "<p>56.(d)<br>% में परिवर्तन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24000</mn><mo>-</mo><mn>5800</mn></mrow><mn>24000</mn></mfrac></math> &times; 100 = 75.83%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If tea that costs ₹7.50 per kg is mixed with tea that costs ₹10.50 per kg in the proportion of 2 : 1 then, what is the cost (in ₹) of the resultant mixture per kg ?</p>",
                    question_hi: "<p>57. यदि ₹7.50 प्रति किलोग्राम कीमत वाली चाय को ₹10.50 प्रति किलोग्राम कीमत वाली चाय के साथ 2 : 1 के अनुपात में मिलाया जाता है, तो परिणामी मिश्रण की प्रति किलोग्राम कीमत (₹ में) क्या होगी ?</p>",
                    options_en: [
                        "<p>7.00</p>",
                        "<p>8.50</p>",
                        "<p>7.50</p>",
                        "<p>9.00</p>"
                    ],
                    options_hi: [
                        "<p>7.00</p>",
                        "<p>8.50</p>",
                        "<p>7.50</p>",
                        "<p>9.00</p>"
                    ],
                    solution_en: "<p>57.(b) According to question,<br>The cost of resultant mixture = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>50</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>10</mn><mo>.</mo><mn>50</mn><mo>&#215;</mo><mn>1</mn></mrow><mn>3</mn></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>+</mo><mn>10</mn><mo>.</mo><mn>50</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>.</mo><mn>50</mn></mrow><mn>3</mn></mfrac></math> = 8.50 kg</p>",
                    solution_hi: "<p>57.(b) प्रश्न के अनुसार,<br>अंतिम मिश्रण की लागत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>.</mo><mn>50</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>10</mn><mo>.</mo><mn>50</mn><mo>&#215;</mo><mn>1</mn></mrow><mn>3</mn></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>+</mo><mn>10</mn><mo>.</mo><mn>50</mn></mrow><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>.</mo><mn>50</mn></mrow><mn>3</mn></mfrac></math> = 8.50 kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Which of the following schemes will yield minimum discount ?<br>1) 2 successive discounts of 5% and 5%<br>2) Single discount of 10%<br>3) 2 successive discounts of 8% and 2%</p>",
                    question_hi: "<p>58. निम्नलिखित में से किस योजना में सबसे कम छूट मिलेगी ?<br>1) 5% और 5% की 2 क्रमिक छूट<br>2) 10% की एकल छूट<br>3) 8% और 2% की 2 क्रमिक छूट</p>",
                    options_en: [
                        "<p>Scheme 2 only</p>",
                        "<p>Both Schemes 1 and 3</p>",
                        "<p>Scheme 1 only</p>",
                        "<p>Scheme 3 only</p>"
                    ],
                    options_hi: [
                        "<p>केवल योजना 2</p>",
                        "<p>योजना 1 और 3, दोनों</p>",
                        "<p>केवल योजना 1</p>",
                        "<p>केवल योजना 3</p>"
                    ],
                    solution_en: "<p>58.(c)<br><strong>Scheme 1 :</strong> 2 successive discounts of 5% and 5%<br>Single discount of 5% and 5% = (5 + 5 - <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 10 - 0.25 = 9.75%<br><strong>Scheme 2 :</strong> Single discount of 10%<br><strong>Scheme 3 : </strong>2 successive discounts of 8% and 2%<br>Single discount of 8% and 2% = (8 + 2 - <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 10 - 0.16 = 9.84%<br>Clearly, we can see that only scheme 1 will yield minimum discount.</p>",
                    solution_hi: "<p>58.(c)<br><strong>योजना 1: </strong>5% और 5% की 2 क्रमिक छूट<br>5% और 5% की एकल छूट = (5 + 5 - <math display=\"inline\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 10 - 0.25 = 9.75%<br><strong>योजना 2: </strong>10% की एकल छूट<br><strong>योजना 3: </strong>8% और 2% की 2 क्रमिक छूटें<br>8% और 2% की एकल छूट = (8 + 2 - <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 10 - 0.16 = 9.84%<br>स्पष्ट रूप से, हम देख सकते हैं कि केवल स्कीम 1 ही न्यूनतम छूट देगी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A customer pays ₹975 in instalments. The payment is done each month ₹5 less than the previous month. If the first instalment is ₹100, how much time will be taken to pay the entire amount ?</p>",
                    question_hi: "<p>59. एक ग्राहक ₹975 का भुगतान किस्तों में करता है। हर महीने, पिछले महीने की तुलना में ₹5 कम का भुगतान किया जाता है। यदि पहली किस्त ₹100 है, तो पूरी राशि का भुगतान करने में कितना समय लगेगा ?</p>",
                    options_en: [
                        "<p>14 months</p>",
                        "<p>15 months</p>",
                        "<p>27 months</p>",
                        "<p>26 months</p>"
                    ],
                    options_hi: [
                        "<p>14 महीने</p>",
                        "<p>15 महीने</p>",
                        "<p>27 महीने</p>",
                        "<p>26 महीने</p>"
                    ],
                    solution_en: "<p>59.(b) According to the question,<br>100, 95, 90, 85, &hellip;&hellip;..nth term <br>s<sub>n</sub> = 975, a = 100 and d = -5<br>&rArr; s<sub>n </sub>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac></math>[2a + (n -1)d]<br>&rArr; 975 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac></math>[2 &times; 100 + (n -1) - 5]<br>&rArr; 1950 = 200n - 5n<sup>2</sup> + 5n<br>&rArr; 5n<sup>2 </sup>- 205n + 1950 = 0<br>&rArr; n<sup>2 </sup>- 41n + 390 = 0<br>On solving above equation, we get<br>&rArr; n = 15 and 26<br>Now, if n = 26, the number of installments will become negative (after 20 installments), hence it is not possible. <br>Total installments = 15 months</p>",
                    solution_hi: "<p>59.(b) प्रश्न के अनुसार,<br>100, 95, 90, 85, &hellip;&hellip;..n वां पद <br>s<sub>n</sub> = 975, a = 100 और d = -5<br>&rArr; s<sub>n </sub>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac></math>[2a + (n -1)d]<br>&rArr; 975 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">n</mi><mn>2</mn></mfrac></math>[2 &times; 100 + (n -1) - 5]<br>&rArr; 1950 = 200n - 5n<sup>2</sup> + 5n<br>&rArr; 5n<sup>2 </sup>- 205n + 1950 = 0<br>&rArr; n<sup>2 </sup>- 41n + 390 = 0<br>उपरोक्त समीकरण को हल करने पर, हमें प्राप्त होता है<br>&rArr; n = 15 और 26<br>अब, यदि n = 26 है, तो किस्तों की संख्या ऋणात्मक हो जाएगी (20 किश्तों के बाद), इसलिए यह संभव नहीं है। <br>कुल किश्तें = 15 माह</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The difference between the two perpendicular sides of a right-angled triangle is 2 cm and its area is 24 cm<sup>2</sup>. What is the perimeter (in cm) of the triangle ?</p>",
                    question_hi: "<p>60. एक समकोण त्रिभुज की दो लंबवत भुजाओं के बीच का अंतर 2 cm है और इसका क्षेत्रफल 24 cm&sup2; है। त्रिभुज का परिमाप (cm में) क्या है ?</p>",
                    options_en: [
                        "<p>24</p>",
                        "<p>14</p>",
                        "<p>16</p>",
                        "<p>18</p>"
                    ],
                    options_hi: [
                        "<p>24</p>",
                        "<p>14</p>",
                        "<p>16</p>",
                        "<p>18</p>"
                    ],
                    solution_en: "<p>60.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782942752.png\" alt=\"rId53\" width=\"141\" height=\"128\"><br>a - b = 2 &hellip; (given)<br>a = 2 + b &hellip;(i)<br>Area of triangle = 24 <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; base &times; height = 24<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; b &times; a = 24<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; b &times; (2 + b) = 24 &hellip; from (i)<br>b<sup>2</sup> + 2b - 48 = 0<br>b<sup>2</sup> + 8b - 6b - 48 = 0<br>b (b + 8) - 6(b + 8) = 0<br>(b&nbsp;+ 8) (b - 6) = 0<br>b = - 8 and 6<br>b = 6 &hellip;(side cannot be -ve)<br>a = 2 + b = 8<br>c = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>8</mn><mn>2</mn></msup><mo>+</mo><msup><mn>6</mn><mn>2</mn></msup></msqrt></math> = 10<br>Perimeter = a&nbsp;+ b + c = 8 + 6 + 10 = 24</p>",
                    solution_hi: "<p>60.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782942752.png\" alt=\"rId53\" width=\"141\" height=\"128\"><br>a - b = 2 &hellip; (दिया गया)<br>a = 2 + b &hellip;(i)<br>त्रिभुज का क्षेत्रफल = 24 <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; आधार &times; ऊंचाई = 24<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; b &times; a = 24<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> &times; b &times; (2 + b) = 24 &hellip; (i) से<br>b<sup>2</sup> + 2b - 48 = 0<br>b<sup>2</sup> + 8b - 6b - 48 = 0<br>b (b + 8) - 6(b + 8) = 0<br>(b&nbsp;+ 8) (b - 6) = 0<br>b = - 8 and 6<br>b = 6 &hellip;(भुजा -ve नहीं हो सकती)<br><math display=\"inline\"><mi>a</mi></math> = 2 + b = 8<br>c = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>8</mn><mn>2</mn></msup><mo>+</mo><msup><mn>6</mn><mn>2</mn></msup></msqrt></math> = 10<br>परिमाप = <math display=\"inline\"><mi>a</mi></math> + b + c = 8 + 6 + 10 = 24</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The university has 750 faculty (male and female only) out of which the females are 60%. The average height of females is 162 cm and that of males is 168 cm. What is the average height of the faculty (in cm) of the university ?</p>",
                    question_hi: "<p>61. एक विश्वविद्यालय में 750 संकाय (केवल पुरुष और महिला) हैं जिनमें से 60% महिलाएँ हैं। महिलाओं की औसत ऊँचाई 162 cm और पुरुषों की औसत ऊँचाई 168 cm है। विश्वविद्यालय के सभी संकायों की औसत ऊँचाई (cm में) ज्ञात करें।</p>",
                    options_en: [
                        "<p>161.4</p>",
                        "<p>166.4</p>",
                        "<p>163.4</p>",
                        "<p>164.4</p>"
                    ],
                    options_hi: [
                        "<p>161.4</p>",
                        "<p>166.4</p>",
                        "<p>163.4</p>",
                        "<p>164.4</p>"
                    ],
                    solution_en: "<p>61.(d)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Male : Female<br>No of students &rarr;&nbsp; &nbsp;40&nbsp; :&nbsp; &nbsp; &nbsp;60 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 3<br>Average height &rarr; (162 + 6) : 162<br>Average height of the faculty of the university = 162 + <math display=\"inline\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> <br>= 162 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math> = 162 + 2.4 = 164.4 cm</p>",
                    solution_hi: "<p>61.(d)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; पुरुष : महिला<br>विद्यार्थियों की संख्या &rarr;&nbsp; 40&nbsp; :&nbsp; &nbsp; 60 <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;2&nbsp; &nbsp;:&nbsp; &nbsp; 3<br>औसत ऊंचाई &rarr; (162 + 6) : 162<br>विश्वविद्यालय के संकाय की औसत ऊंचाई = 162 + <math display=\"inline\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>2</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> <br>= 162 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>5</mn></mfrac></math> = 162 + 2.4 = 164.4 सेमी</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. In a room, there are some chairs and some people. If on each chair, only one person is seated, then there is no chair for exactly one person. If on each chair, two persons sit, then there is one vacant chair. What is the number of chairs in the room ?</p>",
                    question_hi: "<p>62. एक कमरे में कुछ कुर्सियाँ और कुछ व्यक्ति हैं। यदि प्रत्येक कुर्सी पर केवल एक व्यक्ति बैठता है, तो ठीक एक व्यक्ति के लिए कुर्सी नहीं बचती है। यदि प्रत्येक कुर्सी पर दो व्यक्ति बैठते हैं, तो एक कुर्सी खाली रहती है। कमरे में कुर्सियों की संख्या कितनी है ?</p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>62.(c)<br>Let number of chairs = x<br>When one person is seated on each chair then<br>Total number of men = x&nbsp;+ 1 &hellip; (i)<br>When two person is seated on each chair then<br>Total number of men = 2x&nbsp;- 2 &hellip;(ii)<br>From (i) and (ii)<br>x + 1 = 2x - 2<br>x = 3<br>Hence number of chairs = 3</p>",
                    solution_hi: "<p>62.(c)<br>माना कुर्सियों की संख्या = x<br>जब प्रत्येक कुर्सी पर एक व्यक्ति बैठा हो<br>पुरुषों की कुल संख्या = x&nbsp;+ 1 &hellip; (i)<br>जब प्रत्येक कुर्सी पर दो व्यक्ति बैठे हों<br>पुरुषों की कुल संख्या = 2x&nbsp;- 2 &hellip;(ii)<br>(i) और (ii) से<br>x + 1 = 2x - 2<br>x = 3<br>अतः कुर्सियों की संख्या = 3</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If the simple interest on ₹7,200 in 3 years at the rate of 16% per annum equals the simple interest on ₹9,600 at the rate of <math display=\"inline\"><mi>x</mi></math>% per annum in 4 years. The value of x is equal to:</p>",
                    question_hi: "<p>63. यदि ₹7,200 पर 16% प्रति वर्ष की दर से 3 वर्षों का साधारण ब्याज, ₹9,600 पर <math display=\"inline\"><mi>x</mi></math>% प्रति वर्ष की दर से 4 वर्षों के साधारण ब्याज के बराबर है। तो x का मान किसके बराबर होगा ?</p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>11</p>",
                        "<p>8</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>11</p>",
                        "<p>8</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>63.(a) SI = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>r</mi><mi>i</mi><mi>n</mi><mi>c</mi><mi>i</mi><mi>p</mi><mi>a</mi><mi>l</mi><mo>&#215;</mo><mi>r</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>&#215;</mo><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow><mn>100</mn></mfrac></math><br>According to the question,</p>\n<p>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7200</mn><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9600</mn><mo>&#215;</mo><mi>x</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math></p>\n<p>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>72</mn><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>96</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = x</p>\n<p>&rArr; x = 9</p>",
                    solution_hi: "<p>63.(a) साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;</mi><mi>&#2352;</mi><mo>&#215;</mo><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi></mrow><mn>100</mn></mfrac></math><br>प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7200</mn><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9600</mn><mo>&#215;</mo><mi>x</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math></p>\n<p>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>72</mn><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>96</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac></math> = x</p>\n<p>&rArr; x = 9</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If product of two numbers is 108 and the HCF between them is 3. What is the LCM of the two numbers ?</p>",
                    question_hi: "<p>64. यदि दो संख्याओं का गुणनफल 108 है और उनके बीच का महत्तम समापवर्तक 3 है, तो दोनों संख्याओं का लघुत्तम समापवर्त्य कितना है ?</p>",
                    options_en: [
                        "<p>42</p>",
                        "<p>25</p>",
                        "<p>32</p>",
                        "<p>36</p>"
                    ],
                    options_hi: [
                        "<p>42</p>",
                        "<p>25</p>",
                        "<p>32</p>",
                        "<p>36</p>"
                    ],
                    solution_en: "<p>64.(d) <br>L.C.M &times; H.C.F = 1st no. &times; 2nd no.<br>L.C.M. = <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 36</p>",
                    solution_hi: "<p>64.(d) <br>L.C.M &times; H.C.F = पहली संख्या &times; दूसरी संख्या<br>L.C.M. = <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 36</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. If the sum of three numbers is 18 and the sum of their squares is 36, then find the difference between the sum of their cubes and three times of their product.</p>",
                    question_hi: "<p>65. यदि तीन संख्याओं का योग 18 है और उनके वर्गों का योग 36 है, तो उनके घनों के योग और उनके गुणनफल के तीन गुना के बीच अंतर ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>1449</p>",
                        "<p>&minus;1944</p>",
                        "<p>&minus;1494</p>",
                        "<p>4149</p>"
                    ],
                    options_hi: [
                        "<p>1449</p>",
                        "<p>&minus;1944</p>",
                        "<p>&minus;1494</p>",
                        "<p>4149</p>"
                    ],
                    solution_en: "<p>65.(b) <br>Given that, a + b + c = 18 &amp; a<sup>2</sup>&nbsp;+ b<sup>2</sup> + c<sup>2</sup> = 36<br>Then, a<sup>3 </sup>+ b<sup>3 </sup>+ c<sup>3</sup> - 3abc = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>(a + b + c) [3(a<sup>2 </sup>+ b<sup>2 </sup>+ c<sup>2</sup>) - (a + b + c)<sup>2</sup>]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 18[3 &times; 36 - 18<sup>2</sup>]<br>= 9[108 - 324] = 9 &times; (-216) = -1944</p>",
                    solution_hi: "<p>65.(b) <br>मान लें कि, a + b + c = 18 और a<sup>2</sup>&nbsp;+ b<sup>2</sup> + c<sup>2</sup> = 36<br>तब, a<sup>3 </sup>+ b<sup>3 </sup>+ c<sup>3</sup> - 3abc = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>(a + b + c) [3(a<sup>2 </sup>+ b<sup>2 </sup>+ c<sup>2</sup>) - (a + b + c)<sup>2</sup>]<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 18[3 &times; 36 - 18<sup>2</sup>]<br>= 9[108 - 324] = 9 &times; (-216) = -1944</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A boat can travel at a speed of 28 km/h in still water. If the speed of the stream is 4 km/h, what is the average speed (in km/h) of the boat if it travels 192 km downstream, stops there for one hour and then returns to the starting point ?</p>",
                    question_hi: "<p>66. एक नाव स्थिर जल में 28 km/h की चाल से यात्रा कर सकती है। यदि धारा की चाल 4 km/h है और यदि नाव धारा के अनुकूल 192 km की यात्रा करती है, वहां एक घंटे के लिए रुकती है और फिर प्रारंभिक बिंदु पर वापस आती है, तो नाव की औसत चाल (km/h में) क्या है ?</p>",
                    options_en: [
                        "<p>25.6</p>",
                        "<p>24.8</p>",
                        "<p>36.5</p>",
                        "<p>28.5</p>"
                    ],
                    options_hi: [
                        "<p>25.6</p>",
                        "<p>24.8</p>",
                        "<p>36.5</p>",
                        "<p>28.5</p>"
                    ],
                    solution_en: "<p>66.(a)<br>Speed of boat in downstream = (x + y) = (28 + 4) = 32 km/h<br>Time taken by boat to go downstream = <math display=\"inline\"><mfrac><mrow><mn>192</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> = 6 hour<br>Speed of boat in upstream = (x - y) = (28 - 4) = 24 km/h<br>Time taken by boat to go upstream = <math display=\"inline\"><mfrac><mrow><mn>192</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 8 hour<br>Total distance (both ways) = 2 &times; 192 = 384 km<br>Total time taken (both ways) = (6 + 8 + 1) = 15 hour<br>Now, average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>Distance</mi></mrow><mrow><mi>Total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>time</mi><mi mathvariant=\"normal\">&#160;</mi><mi>taken</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>384</mn><mn>15</mn></mfrac></math> = 25.6 km/h</p>",
                    solution_hi: "<p>66.(a)<br>धारा के अनुकूल नाव की गति = (x + y) = (28 + 4) = 32 किमी/घंटा<br>नाव द्वारा धारा के अनुकूल जाने में लगा समय =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>192</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math> = 6 घंटे <br>धारा के प्रतिकूल नाव की गति = (x - y) = (28 - 4) = 24 किमी/घंटा<br>नाव द्वारा धारा के प्रतिकूल जाने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>192</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 8 घंटे<br>कुल दूरी (दोनों तरफ) = 2 &times; 192 = 384 किमी<br>कुल लिया गया समय (दोनों तरफ) = (6 + 8 + 1) = 15 घंटे<br>अब, औसत गति = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2354;&#2367;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>384</mn><mn>15</mn></mfrac></math> = 25.6 किमी/घंटा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The value of 1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>1</mn><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mn>1</mn><mn>3</mn></mfrac></mrow></mfrac></mrow></mfrac></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>67. 1 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></mrow></mfrac></mrow></mfrac></math> का मान कितना होगा ?</p>",
                    options_en: [
                        "<p>1<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>",
                        "<p>1<math display=\"inline\"><mfrac><mrow><mn>38</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>1<math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>",
                        "<p>1<math display=\"inline\"><mfrac><mrow><mn>38</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>67.(d)<br>1 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></mrow></mfrac></mrow></mfrac></math></p>\n<p>= 1 +&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><mrow><mi>&#160;</mi><mn>5</mn><mo>+</mo><mfrac><mn>3</mn><mn>7</mn></mfrac></mrow></mfrac></mrow></mfrac></math></p>\n<p>= 1 +&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>7</mn><mn>38</mn></mfrac></mrow></mfrac></math></p>\n<p>= 1 +&nbsp;<math display=\"inline\"><mfrac><mrow><mn>38</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>\n<p>= 1 +&nbsp;<math display=\"inline\"><mfrac><mrow><mn>38</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> = 1<math display=\"inline\"><mfrac><mrow><mn>38</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>67.(d)<br>1 + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></mrow></mfrac></mrow></mfrac></math></p>\n<p>= 1 +&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>1</mn><mrow><mi>&#160;</mi><mn>5</mn><mo>+</mo><mfrac><mn>3</mn><mn>7</mn></mfrac></mrow></mfrac></mrow></mfrac></math></p>\n<p>= 1 +&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mfrac><mn>7</mn><mn>38</mn></mfrac></mrow></mfrac></math></p>\n<p>= 1 +&nbsp;<math display=\"inline\"><mfrac><mrow><mn>38</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>\n<p>= 1 +&nbsp;<math display=\"inline\"><mfrac><mrow><mn>38</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> = 1<math display=\"inline\"><mfrac><mrow><mn>38</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Study the given graph carefully and answer the question that follows. The graph shows the demand and production of different companies.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782942933.png\" alt=\"rId54\" width=\"381\" height=\"197\"> <br>The ratio between the companies having more production than demand and more demand than the production is:</p>",
                    question_hi: "<p>68. दिए गए ग्राफ़ का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए। ग्राफ़ विभिन्न कंपनियों की मांग और उत्पादन को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782943118.png\" alt=\"rId55\" width=\"359\" height=\"198\"> <br>मांग से अधिक उत्पादन और उत्पादन से अधिक मांग वाली कंपनियों के बीच का अनुपात क्या है?</p>",
                    options_en: [
                        "<p>4 : 1</p>",
                        "<p>2 : 1</p>",
                        "<p>1 : 2</p>",
                        "<p>1 : 4</p>"
                    ],
                    options_hi: [
                        "<p>4 : 1</p>",
                        "<p>2 : 1</p>",
                        "<p>1 : 2</p>",
                        "<p>1 : 4</p>"
                    ],
                    solution_en: "<p>68.(b) <br>companies having more production than demand = 4<br>companies having more demand than the production = 2<br>Required ratio = 4 : 2 = 2 : 1</p>",
                    solution_hi: "<p>68.(b) <br>मांग से अधिक उत्पादन वाली कंपनियाँ = 4<br>उत्पादन से अधिक मांग वाली कंपनियाँ = 2<br>आवश्यक अनुपात = 4 : 2 = 2 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. If 28.9 : x&nbsp;:: x : 36.1, and x &gt; 0, then find the value of x.</p>",
                    question_hi: "<p>69. यदि 28.9 : x&nbsp;:: x : 36.1, और x &gt; 0 है, तो x का मान ज्ञात करें।</p>",
                    options_en: [
                        " 38.3",
                        " 32.3",
                        " 30.4",
                        " 35"
                    ],
                    options_hi: [
                        " 38.3",
                        " 32.3",
                        " 30.4",
                        " 35"
                    ],
                    solution_en: "<p>69.(b)<br>28.9 : x&nbsp;:: x : 36.1<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>28</mn><mo>.</mo><mn>9</mn></mrow><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mrow><mn>36</mn><mo>.</mo><mn>1</mn></mrow></mfrac></math><br>&rArr; x<sup>2</sup> = 28.9 &times; 36.1<br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>&#215;</mo><mn>19</mn></mrow><mn>10</mn></mfrac></math> = 32.3</p>",
                    solution_hi: "<p>69.(b)<br>28.9 : x&nbsp;:: x : 36.1<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>28</mn><mo>.</mo><mn>9</mn></mrow><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mrow><mn>36</mn><mo>.</mo><mn>1</mn></mrow></mfrac></math><br>&rArr; x<sup>2</sup> = 28.9 &times; 36.1<br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>17</mn><mo>&#215;</mo><mn>19</mn></mrow><mn>10</mn></mfrac></math> = 32.3</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Tanvi can do a work in 25 days, and Tai can complete the same work in 30 days. They started the work together for 5 days. How much work is left ?</p>",
                    question_hi: "<p>70. तन्वी एक काम को 25 दिनों में पूरा कर सकती है, और ताई उसी काम को 30 दिनों में पूरा कर सकती है। उन्होंने एक साथ मिलकर 5 दिनों तक काम किया। काम का कितना अंश शेष है ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>19</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>70.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782943233.png\" alt=\"rId56\" width=\"183\" height=\"137\"><br>Work done by both in 5 days = 5 &times; (6 + 5) = 55 unit<br>Required fraction = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>-</mo><mn>55</mn></mrow><mn>150</mn></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>30</mn></mfrac></math>unit</p>",
                    solution_hi: "<p>70.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782943368.png\" alt=\"rId57\" width=\"172\" height=\"148\"><br>दोनों द्वारा 5 दिनों में किया गया कार्य = 5 &times; (6 + 5) = 55 इकाई<br>अभीष्ट भिन्न = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>-</mo><mn>55</mn></mrow><mn>150</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>30</mn></mfrac></math> इकाई</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Two parallel chords of length 5 units and 8 units are on opposite sides of the center of a circle of radius 7 units. What is the distance between the chords ? (round your answer to two decimal places)</p>",
                    question_hi: "<p>71. 5 इकाई और 8 इकाई लंबाई की दो समानांतर जीवाएं 7 इकाई त्रिज्या वाले एक वृत्त के केंद्र की सम्मुख दिशाओं (opposite sides) में हैं। जीवाओं के बीच की दूरी कितनी है ? (अपने उत्तर को दशमलव के दो स्थानों तक सन्नि कटित करें)</p>",
                    options_en: [
                        "<p>12.82 units</p>",
                        "<p>11.28 units</p>",
                        "<p>11.82 units</p>",
                        "<p>12.28 units</p>"
                    ],
                    options_hi: [
                        "<p>12.82 इकाई</p>",
                        "<p>11.28 इकाई</p>",
                        "<p>11.82 इकाई</p>",
                        "<p>12.28 इकाई</p>"
                    ],
                    solution_en: "<p>71.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782943534.png\" alt=\"rId58\" width=\"139\" height=\"127\"><br>Length of AO = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>7</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mo>-</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>49</mn><mo>-</mo><mn>6</mn><mo>.</mo><mn>25</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>42</mn><mo>.</mo><mn>75</mn></msqrt></math><br>= 6.54<br>Length of CO = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>7</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mo>-</mo><mo>(</mo><mn>4</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>49</mn><mo>-</mo><mn>16</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>33</mn></msqrt></math><br>= 5.7<br>So the length of AC = 6.54 + 5.7 = 12.27 (approx 12.28 units)</p>",
                    solution_hi: "<p>71.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744782943534.png\" alt=\"rId58\" width=\"139\" height=\"127\"><br>AO की लंबाई = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>7</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mo>-</mo><mo>(</mo><mn>2</mn><mo>.</mo><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>49</mn><mo>-</mo><mn>6</mn><mo>.</mo><mn>25</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>42</mn><mo>.</mo><mn>75</mn></msqrt></math><br>= 6.54<br>CO की लंबाई = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>7</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mo>-</mo><mo>(</mo><mn>4</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>49</mn><mo>-</mo><mn>16</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>33</mn></msqrt></math><br>= 5.7<br>अतः AC की लंबाई = 6.54 + 5.7 = 12.24 (लगभग 12.28 इकाई)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Which of the following is the correct empirical formula ?</p>",
                    question_hi: "<p>72. निम्न में से कौन सा सही मूलानुपाती सूत्र है ?</p>",
                    options_en: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Mode</mi><mo>-</mo><mi>Mean</mi></mrow><mn>3</mn></mfrac></math> = Median - Mean</p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Mode</mi><mo>-</mo><mi>Mean</mi></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">x</mi><mn>2</mn></mrow></mfrac></math> = Median - Mean</p>",
                        "<p>3 (mode - mean) = median - mean</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> (Mode - Mean) = Median - Mean</p>"
                    ],
                    options_hi: [
                        "<p><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2348;&#2361;&#2369;&#2354;&#2325;</mi><mo>-</mo><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi></mrow><mn>3</mn></mfrac></math> = माध्यिका - माध्य</p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2348;&#2361;&#2369;&#2354;&#2325;</mi><mo>-</mo><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi></mrow><mrow><mn>3</mn><mi>x</mi><mn>2</mn></mrow></mfrac></math> = माध्यिका &ndash; माध्य</p>",
                        "<p>3 (बहुलक - माध्य) = माध्यिका - माध्य</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> (बहुलक - माध्य) = माध्यिका - माध्य</p>"
                    ],
                    solution_en: "<p>72.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Mode</mi><mo>-</mo><mi>Mean</mi></mrow><mn>3</mn></mfrac></math> = Median - Mean<br>&rArr; Mode - Mean = 3 Median - 3 Mean<br>&rArr; Mode = 3 Median - 3 Mean + Mean<br>&rArr; Mode = 3 Median - 2 Mean</p>",
                    solution_hi: "<p>72.(a)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2348;&#2361;&#2369;&#2354;&#2325;</mi><mo>-</mo><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi></mrow><mn>3</mn></mfrac></math> = माध्यिका - माध्य <br>&rArr; बहुलक - माध्य = 3माध्यिका - 3माध्य<br>&rArr; बहुलक = 3माध्यिका - 3माध्य + माध्य<br>&rArr; बहुलक = 3माध्यिका - 2माध्य</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. In a right angle &Delta;ABC, &ang;B = 90&deg;, if tanA = 1, then 4 sinA cosA = _____.</p>",
                    question_hi: "<p>73. एक समकोण &Delta;ABC में &ang;B = 90&deg; है, यदि tanA = 1 है, तो 4 sinA cos A = _____ होगा।</p>",
                    options_en: [
                        " 1",
                        " 2",
                        " <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>",
                        " 4"
                    ],
                    options_hi: [
                        " 1",
                        " 2",
                        " <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>",
                        " 4"
                    ],
                    solution_en: "<p>73.(b) In right &Delta;ABC, &ang;B = 90&deg;<br>&rArr; tan A = 1<br>&rArr; tan A = tan 45&deg;<br>&rArr; A = 45&deg;<br>Now,<br>4sinA.cosA<br>= 4 &times; sin45&deg; &times; cos45&deg;<br>= 4 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 2</p>",
                    solution_hi: "<p>73.(b) समकोण &Delta;ABC में , &ang;B = 90&deg;<br>&rArr; tan A = 1<br>&rArr; tan A = tan 45&deg;<br>&rArr; A = 45&deg;<br>अब,<br>4sinA.cosA<br>= 4 &times; sin45&deg; &times; cos45&deg;<br>= 4 &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. In a circular race of 1 km, two persons X and Y start with the speed of 9 km/h and 18 km/h, respectively, at the same time from the same point. When will they meet for the first time at the starting point when running in the same direction ?</p>",
                    question_hi: "<p>74. 1 km की एक गोलाकार दौड़ में, दो व्यक्ति X और Y एक ही बिंदु से एक ही समय पर क्रमशः 9 km/h और 18 km/h की चाल से दौड़ना शुरू करते हैं। एक ही दिशा में दौड़ते हुए वे पहली बार शुरुआती बिंदु पर कब मिलेंगे ?</p>",
                    options_en: [
                        "<p>400 sec</p>",
                        "<p>360 sec</p>",
                        "<p>180 sec</p>",
                        "<p>240 sec</p>"
                    ],
                    options_hi: [
                        "<p>400 sec</p>",
                        "<p>360 sec</p>",
                        "<p>180 sec</p>",
                        "<p>240 sec</p>"
                    ],
                    solution_en: "<p>74.(a) Time taken to meet again for the first time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mrow><mo>(</mo><mn>18</mn><mo>-</mo><mn>9</mn><mo>)</mo><mo>&#215;</mo><mfrac><mn>5</mn><mn>18</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>5</mn></mfrac></math> = 400sec</p>",
                    solution_hi: "<p>74.(a) पहली बार दोबारा मिलने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1000</mn><mrow><mo>(</mo><mn>18</mn><mo>-</mo><mn>9</mn><mo>)</mo><mo>&#215;</mo><mfrac><mn>5</mn><mn>18</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>5</mn></mfrac></math> = 400sec</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Two trains of equal length take 10 s and 20 s, respectively, to cross a pole. If these trains are moving in the same direction, then how long will they take to cross each other ?</p>",
                    question_hi: "<p>75. समान लंबाई की दो रेलगाड़ियों को एक खम्भे को पार करने में क्रमशः 10 सेकंड और 20 सेकंड का समय लगता है। यदि ये रेलगाड़ियाँ एक ही दिशा में चल रही हैं, तो एक-दूसरे को पार करने में उन्&zwj;हें कितना समय लगेगा ?</p>",
                    options_en: [
                        "<p>10 sec</p>",
                        "<p>15 sec</p>",
                        "<p>40 sec</p>",
                        "<p>30 sec</p>"
                    ],
                    options_hi: [
                        "<p>10 सेकंड</p>",
                        "<p>15 सेकंड</p>",
                        "<p>40 सेकंड</p>",
                        "<p>30 सेकंड</p>"
                    ],
                    solution_en: "<p>75.(c)<br>Let the length of each train be x&nbsp;meter<br>Speed of train 1 = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>10</mn></mrow></mfrac></math> m/sec<br>Speed of train 2 = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math> m/sec<br>Relative speed of train when moving in same direction = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>10</mn></mrow></mfrac></math> - <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>According to question<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; t = 2x<br>&rArr; t = 40 sec<br>Hence, they take 40 sec to cross each other.</p>",
                    solution_hi: "<p>75.(c)<br>माना प्रत्येक ट्रेन की लंबाई = x&nbsp;मीटर <br>पहली ट्रेन की गति= <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>10</mn></mrow></mfrac></math> m/sec<br>दूसरी ट्रेन की गति = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math> m/sec<br>एक ही दिशा में चलने पर ट्रेन की सापेक्ष गति = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>10</mn></mrow></mfrac></math> - <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math>=&nbsp;<math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math><br>प्रश्न के अनुसार ,<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; t = 2x<br>&rArr; t = 40 सेकंड<br>इसलिए, उन्हें एक दूसरे को पार करने में 40 सेकंड का समय लगेगा ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence, which contains the error.<br>With this heat wave on, we are having a terrible weather.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence, which contains the error.<br>With this heat wave on, we are having a terrible weather.</p>",
                    options_en: [
                        "<p>heat wave on</p>",
                        "<p>we are having a</p>",
                        "<p>with this</p>",
                        "<p>terrible weather</p>"
                    ],
                    options_hi: [
                        "<p>heat wave on</p>",
                        "<p>we are having a</p>",
                        "<p>with this</p>",
                        "<p>terrible weather</p>"
                    ],
                    solution_en: "<p>76.(b) &ldquo;a&rdquo; should be removed<br>Weather is an uncountable Noun so, we should remove the article &ldquo;a&rdquo; before it. Usage of &ldquo;are having&rdquo; (Present continuous tense) is also incorrect. So the correct sentence will be - With this heat wave on, we have terrible weather.</p>",
                    solution_hi: "<p>76.(b) \"a\" को हटा देना चाहिए। <br>Weather एक uncountable Noun है, इसलिए हमें इसके पहले article &ldquo;a&rdquo; को हटा देना चाहिए। &ldquo;are have&rdquo; (Present continuous tense) का प्रयोग भी गलत है। तो सही वाक्य होगा - With this heat wave on, we have terrible weather.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>77. Select the INCORRECTLY spelt word.</p>",
                    options_en: [
                        "<p>Obtain</p>",
                        "<p>Encraoch</p>",
                        "<p>Delegate</p>",
                        "<p>Illusion</p>"
                    ],
                    options_hi: [
                        "<p>Obtain</p>",
                        "<p>Encraoch</p>",
                        "<p>Delegate</p>",
                        "<p>Illusion</p>"
                    ],
                    solution_en: "<p>77.(b) <strong>Encraoch</strong><br>&lsquo;Encroach&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>77.(b) <strong>Encraoch</strong><br>&lsquo;Encroach&rsquo; सही spelling है। </p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>The skunk had a <span style=\"text-decoration: underline;\">conspicuous</span> spinal stripe.</p>",
                    question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>The skunk had a <span style=\"text-decoration: underline;\">conspicuous</span> spinal stripe.</p>",
                    options_en: [
                        "<p>faint</p>",
                        "<p>charming</p>",
                        "<p>obscure</p>",
                        "<p>noticeable</p>"
                    ],
                    options_hi: [
                        "<p>faint</p>",
                        "<p>charming</p>",
                        "<p>obscure</p>",
                        "<p>noticeable</p>"
                    ],
                    solution_en: "<p>78.(d) <strong>noticeable</strong><br>&lsquo;Noticeable&rsquo; means easily seen or noticed. The given sentence states that the skunk had a conspicuous spinal stripe. Hence, \'noticeable\' is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(d) <strong>noticeable</strong><br>&lsquo;Noticeable&rsquo; का अर्थ है आसानी से seen या notice किया जा सकने वाला। दिए गए sentence में बताया गया है कि skunk की रीढ़ (spinal) की हड्डी पर एक स्पष्ट स्ट्रिप थी। अतः, \'noticeable\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>A group of girls</p>",
                    question_hi: "<p>79. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>A group of girls</p>",
                    options_en: [
                        "<p>Bevy</p>",
                        "<p>Covey</p>",
                        "<p>Troupe</p>",
                        "<p>Coterie</p>"
                    ],
                    options_hi: [
                        "<p>Bevy</p>",
                        "<p>Covey</p>",
                        "<p>Troupe</p>",
                        "<p>Coterie</p>"
                    ],
                    solution_en: "<p>79.(a) <strong>Bevy</strong> - a large group of people or things of a particular kind.<br><strong>Covey</strong> - a small flock of birds, especially partridge<br><strong>Troupe</strong> - a group of dancers, actors, or other entertainers who tour to different venues.<br><strong>Coterie</strong> - a small group of people with shared interests or tastes, especially one that is exclusive of other people</p>",
                    solution_hi: "<p>79.(a) <strong>Bevy</strong> - लोगों का एक बड़ा समूह । <br><strong>Covey</strong> - पक्षियों का एक छोटा झुंड, विशेषकर तीतर।<br><strong>Troupe</strong> - नर्तकियों, अभिनेताओं, या अन्य मनोरंजन करने वालों का एक समूह जो विभिन्न स्थानों पर भ्रमण करता है।<br><strong>Coterie</strong> - साझा हितों वाले लोगों का एक छोटा समूह, विशेष रूप से वह जो अन्य लोगों से अलग हो।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br><span style=\"text-decoration: underline;\">This is best book</span> for children to read.</p>",
                    question_hi: "<p>80. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br><span style=\"text-decoration: underline;\">This is best book</span> for children to read.</p>",
                    options_en: [
                        "<p>This is the best book</p>",
                        "<p>This is very best book</p>",
                        "<p>This is good book</p>",
                        "<p>This is very good book</p>"
                    ],
                    options_hi: [
                        "<p>This is the best book</p>",
                        "<p>This is very best book</p>",
                        "<p>This is good book</p>",
                        "<p>This is very good book</p>"
                    ],
                    solution_en: "<p>80.(a) <strong>This is the best book</strong><br>We always place definite article &lsquo;the&rsquo; before any superlative degree adjective. Similarly, in the given sentence, &lsquo;best&rsquo; is the superlative degree of &lsquo;good&rsquo;. Hence &lsquo;This is the best book&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>80.(a) <strong>This is the best book</strong><br>हम हमेशा किसी भी superlative degree adjective से पहले definite article &lsquo;the&rsquo; का प्रयोग करते हैं। इसी तरह, दिए गए sentence में, &lsquo;best&rsquo;, &lsquo;good&rsquo; की superlative degree है। अतः &lsquo;This is the best book&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that correctly expresses the given sentence in passive voice.<br>The teachers of our school have already taken this training.</p>",
                    question_hi: "<p>81. Select the option that correctly expresses the given sentence in passive voice.<br>The teachers of our school have already taken this training.</p>",
                    options_en: [
                        "<p>This training has already been taken by the teachers of our school.</p>",
                        "<p>This training has been taken by the teachers of our school.</p>",
                        "<p>This training has already been taken by the our school teachers.</p>",
                        "<p>This training was already taken by the teachers of our school.</p>"
                    ],
                    options_hi: [
                        "<p>This training has already been taken by the teachers of our school.</p>",
                        "<p>This training has been taken by the teachers of our school.</p>",
                        "<p>This training has already been taken by the our school teachers.</p>",
                        "<p>This training was already taken by the teachers of our school.</p>"
                    ],
                    solution_en: "<p>81.(a) This training has already been taken by the teachers of our school. (Correct)<br>(b) This training has been taken by the teachers of our school. (&lsquo;Already&rsquo; is missing)<br>(c) This training has already been taken by the our school teachers. (Incorrect Sentence Structure)<br>(d) This training <span style=\"text-decoration: underline;\">was already taken</span> by the teachers of our school. (Incorrect Tense)</p>",
                    solution_hi: "<p>81.(a) This training has already been taken by the teachers of our school. (Correct)<br>(b) This training has been taken by the teachers of our school. (&lsquo;Already&rsquo; missing है)<br>(c) This training has already been taken by the our school teachers. (गलत Sentence Structure)<br>(d) This training <span style=\"text-decoration: underline;\">was already taken</span> by the teachers of our school. (गलत Tense)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the most appropriate ANTONYM of the given word.<br>Beautiful</p>",
                    question_hi: "<p>82. Select the most appropriate ANTONYM of the given word.<br>Beautiful</p>",
                    options_en: [
                        "<p>Pungent</p>",
                        "<p>Ugly</p>",
                        "<p>Slimy</p>",
                        "<p>Stingy</p>"
                    ],
                    options_hi: [
                        "<p>Pungent</p>",
                        "<p>Ugly</p>",
                        "<p>Slimy</p>",
                        "<p>Stingy</p>"
                    ],
                    solution_en: "<p>82.(b) <strong>Ugly</strong> - unpleasant to look at.<br><strong>Beautiful-</strong> good-looking.<br><strong>Pungent-</strong> having a sharply strong taste or smell.<br><strong>Slimy-</strong> covered by or resembling slime.<br><strong>Stingy-</strong> one who spends little money.</p>",
                    solution_hi: "<p>82.(b) <strong>Ugly</strong> (भद्दा/कुरूप) - unpleasant to look at.<br><strong>Beautiful</strong> (सुंदर) - good-looking.<br><strong>Pungent</strong> (तीखा) - having a sharply strong taste or smell.<br><strong>Slimy</strong> (कीचड़ से लथपथ) - covered by or resembling slime.<br><strong>Stingy</strong> (कंजूस) - one who spends little money.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>O. went<br>P. yesterday<br>Q. we<br>R. to the<br>S. park</p>",
                    question_hi: "<p>83. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>O. went<br>P. yesterday<br>Q. we<br>R. to the<br>S. park</p>",
                    options_en: [
                        "<p>RQOSP</p>",
                        "<p>OPQRS</p>",
                        "<p>PSROQ</p>",
                        "<p>QORSP</p>"
                    ],
                    options_hi: [
                        "<p>RQOSP</p>",
                        "<p>OPQRS</p>",
                        "<p>PSROQ</p>",
                        "<p>QORSP</p>"
                    ],
                    solution_en: "<p>83.(d) QORSP<br>The given sentence starts with Part Q as it introduces the subject of the sentence, i.e. &lsquo;We&rsquo;. Part O contains the main verb and Part R has the preposition &lsquo;to&rsquo; that will indicate the place where he went. So, R will follow O. Further, Part S states the place (park) and Part P states the time. So, P will follow S. Going through the options, option &lsquo;d&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>83.(d) QORSP<br>दिया गया sentence, Part Q से प्रारंभ होता है क्योंकि इसमें sentence का subject, &lsquo;We\' शामिल है। Part O में main verb है और Part R में preposition \'to\' है जो उस स्थान को इंगित करेगा जहाँ वह गया था। इसलिए, O के बाद R आएगा। इसके अलावा, Part S स्थान (park) तथा Part P समय बताता है। इसलिए, S के बाद P आएगा। अतः options के माध्यम से जाने पर, option &lsquo;d&rsquo; में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Find the correctly spelt word</p>",
                    question_hi: "<p>84. Find the correctly spelt word</p>",
                    options_en: [
                        "<p>Grotesque</p>",
                        "<p>Burlesue</p>",
                        "<p>Picturesue</p>",
                        "<p>Picarsque</p>"
                    ],
                    options_hi: [
                        "<p>Grotesque</p>",
                        "<p>Burlesue</p>",
                        "<p>Picturesue</p>",
                        "<p>Picarsque</p>"
                    ],
                    solution_en: "<p>84.(a) Grotesque <br>Other words- Burlesque, Picturesque, Picaresque</p>",
                    solution_hi: "<p>84.(a) Grotesque <br>Other words- Burlesque, Picturesque, Picaresque</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85.Select the correct option to form a meaningful sentence.<br>I / that/the chef/ to work/ in/ the new/ heard/ restaurant/ is going/ across the road.<br>(a) (b)&nbsp; &nbsp; &nbsp;(c)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (d)&nbsp; &nbsp; &nbsp;(e)&nbsp; &nbsp; &nbsp; (f)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(g)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(h)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(i)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(j)</p>",
                    question_hi: "<p>85.Select the correct option to form a meaningful sentence.<br>I / that/the chef/ to work/ in/ the new/ heard/ restaurant/ is going/ across the road.<br>(a) (b)&nbsp; &nbsp; &nbsp;(c)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (d)&nbsp; &nbsp; &nbsp;(e)&nbsp; &nbsp; &nbsp; (f)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(g)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(h)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(i)&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(j)</p>",
                    options_en: [
                        "<p>a, c, g, b, h, i, f, e, d, j</p>",
                        "<p>a, b, d, c, e, f, h, g, i, j</p>",
                        "<p>a, b, d, c, g, e, f, h, i, j</p>",
                        "<p>a, g, b, c, i, d, e, f, h, j</p>"
                    ],
                    options_hi: [
                        "<p>a, c, g, b, h, i, f, e, d, j</p>",
                        "<p>a, b, d, c, e, f, h, g, i, j</p>",
                        "<p>a, b, d, c, g, e, f, h, i, j</p>",
                        "<p>a, g, b, c, i, d, e, f, h, j</p>"
                    ],
                    solution_en: "<p>85.(d) a, g, b, c, i, d, e, f, h, j<br>The correct sentence is: &ldquo;I heard that the chef is going to work in the new restaurant across the road.&rdquo;</p>",
                    solution_hi: "<p>85.(d) a, g, b, c, i, d, e, f, h, j<br>&ldquo;I heard that the chef is going to work in the new restaurant across the road&rdquo; सही sentence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>To take with a grain of salt</p>",
                    question_hi: "<p>86. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below.<br>To take with a grain of salt</p>",
                    options_en: [
                        "<p>to make more palatable</p>",
                        "<p>to take a small quantity of</p>",
                        "<p>to make something meaningful</p>",
                        "<p>to accept with misgiving</p>"
                    ],
                    options_hi: [
                        "<p>to make more palatable</p>",
                        "<p>to take a small quantity of</p>",
                        "<p>to make something meaningful</p>",
                        "<p>to accept with misgiving</p>"
                    ],
                    solution_en: "<p>86.(d) To accept with misgiving<br>Example - Mother told me to take everything Madhu says with a grain of salt, because she tends to exaggerate.</p>",
                    solution_hi: "<p>86.(d) To accept with misgiving / गलतफहमी के साथ स्वीकार करना<br>उदाहरण - Mother told me to take everything Madhu says with a grain of salt, because she tends to exaggerate./ माँ ने मुझसे कहा था कि मधू जो कहती है उसको संदेह के साथ स्वीकार करो क्योंकि वह बढ़ा-चढ़ाकर पेश करती है। </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the idiom <br>Go to the wall</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the idiom <br>Go to the wall</p>",
                    options_en: [
                        "<p>To become strong</p>",
                        "<p>To obtain protection</p>",
                        "<p>To fail</p>",
                        "<p>To start</p>"
                    ],
                    options_hi: [
                        "<p>To become strong</p>",
                        "<p>To obtain protection</p>",
                        "<p>To fail</p>",
                        "<p>To start</p>"
                    ],
                    solution_en: "<p>87.(c) Go to the wall- to fail<br>E.g.- My friend&rsquo;s business goes to the wall because of drug addiction.</p>",
                    solution_hi: "<p>87.(c) Go to the wall - to fail (विफल होना)<br>E.g.- My friend&rsquo;s business went to the wall because of drug addiction.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "88. Identify the segment in the sentence which contains grammatical error. Mark ‘no error’ in case the sentence given is correct. <br />My guide was blind  but he bring  me home safely. ",
                    question_hi: "88. Identify the segment in the sentence which contains grammatical error. Mark ‘no error’ in case the sentence given is correct. <br />My guide was blind  but he bring me home safely. ",
                    options_en: [
                        " me home safely            ",
                        " My guide ",
                        " was blind              ",
                        " but he bring "
                    ],
                    options_hi: [
                        " me home safely            ",
                        " My guide ",
                        " was blind              ",
                        " but he bring "
                    ],
                    solution_en: "88.(d)  but he bring <br />The given sentence is in the simple past tense so the verb must be used in its simple past form(brought) and not in the present form(bring). Hence, ‘but he brought’ is the most appropriate structure.",
                    solution_hi: "88.(d) but he bring <br />दिया गया sentence, simple past tense में है। इसलिए  verb भी simple past form(brought) में होगी, न कि present form(bring) में । इसलिए, ‘but he brought’ सबसे उपयुक्त उत्तर है।<br />                               ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. In the following questions, out of the four alternatives, choose the one which can be substituted for the underlined part of the sentence.<br>Raman had always been attracted to <span style=\"text-decoration: underline;\"><strong>the scientific study of sound</strong></span> and decided to take up a course in sound engineering to pursue his dream career.</p>",
                    question_hi: "<p>89. In the following questions, out of the four alternatives, choose the one which can be substituted for the underlined part of the sentence.<br>Raman had always been attracted to <span style=\"text-decoration: underline;\"><strong>the scientific study of sound</strong></span> and decided to take up a course in sound engineering to pursue his dream career.</p>",
                    options_en: [
                        "<p>Acoustics</p>",
                        "<p>Agronomy</p>",
                        "<p>Alchemy</p>",
                        "<p>Aesthetics</p>"
                    ],
                    options_hi: [
                        "<p>Acoustics</p>",
                        "<p>Agronomy</p>",
                        "<p>Alchemy</p>",
                        "<p>Aesthetics</p>"
                    ],
                    solution_en: "<p>89.(a) <strong>Acoustics-</strong> to the scientific study of sound<br><strong>Agronomy-</strong> a branch of agriculture dealing with field-crop production and soil management.<br><strong>Alchemy-</strong> a form of chemistry in the Middle Ages that involved trying to discover how to change ordinary metals into gold<br><strong>Aesthetics-</strong> the study of beauty, especially in art</p>",
                    solution_hi: "<p>89.(a) <strong>Acoustics(ध्वनि-विज्ञान)</strong> - to the scientific study of sound<br><strong>Agronomy(कृषिविज्ञान)</strong> - a branch of agriculture dealing with field-crop production and soil management.<br><strong>Alchemy(रस-विधा)</strong> - a form of chemistry in the Middle Ages that involved trying to discover how to change ordinary metals into gold<br><strong>Aesthetics(सौंदर्यशास्र)</strong> - the study of beauty, especially in art</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate synonym of the given word.<br>Fasten</p>",
                    question_hi: "<p>90. Select the most appropriate synonym of the given word.<br>Fasten</p>",
                    options_en: [
                        "<p>Undo</p>",
                        "<p>Loosen</p>",
                        "<p>Bolt</p>",
                        "<p>Detach</p>"
                    ],
                    options_hi: [
                        "<p>Undo</p>",
                        "<p>Loosen</p>",
                        "<p>Bolt</p>",
                        "<p>Detach</p>"
                    ],
                    solution_en: "<p>90.(c) <strong>Bolt-</strong> fasten a door or window with a bar that slides into a socket.<br><strong>Fasten-</strong> to fix or tie something to something.<br><strong>Undo-</strong> to open something that is tied or fastened. <br><strong>Loosen-</strong> to become or make something less tight.<br><strong>Detach-</strong> separate or disconnected.</p>",
                    solution_hi: "<p>90.(c) <strong>Bolt</strong> (पेंच) - fasten a door or window with a bar that slides into a socket.<br><strong>Fasten</strong> (बांधना) - to fix or tie something to something.<br><strong>Undo</strong> (पूर्ववत करना) - to open something that is tied or fastened. <br><strong>Loosen</strong> (ढीला करना) - to become or make something less tight.<br><strong>Detach</strong> (अलग करना) - separate or disconnected.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym of the given word.<br>Wonderful</p>",
                    question_hi: "<p>91. Select the most appropriate synonym of the given word.<br>Wonderful</p>",
                    options_en: [
                        "<p>Synthetic</p>",
                        "<p>Amazing</p>",
                        "<p>Awful</p>",
                        "<p>Upright</p>"
                    ],
                    options_hi: [
                        "<p>Synthetic</p>",
                        "<p>Amazing</p>",
                        "<p>Awful</p>",
                        "<p>Upright</p>"
                    ],
                    solution_en: "<p>91.(b) <strong>Wonderful-</strong> extremely good.<br><strong>Amazing-</strong> Impressive or extraordinary.<br><strong>Synthetic-</strong> something that is made through the chemical process.<br><strong>Awful-</strong> very bad or unpleasant.<br><strong>Upright-</strong> strictly honest.</p>",
                    solution_hi: "<p>91.(b) <strong>Wonderful</strong> (अद्भुत) - extremely good.<br><strong>Amazing</strong> (आश्चर्यजनक) - Impressive or extraordinary.<br><strong>Synthetic</strong> (सिंथेटिक) - something that is made through the chemical process.<br><strong>Awful</strong> (अप्रिय) - very bad or unpleasant.<br><strong>Upright</strong> (ईमानदार) - strictly honest.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate connotation to fill in the blank.<br>I always love the _______ of my mother&rsquo;s cooking.</p>",
                    question_hi: "<p>92. Select the most appropriate connotation to fill in the blank.<br>I always love the _______ of my mother&rsquo;s cooking.</p>",
                    options_en: [
                        "<p>perfume</p>",
                        "<p>stench</p>",
                        "<p>aroma</p>",
                        "<p>scent</p>"
                    ],
                    options_hi: [
                        "<p>perfume</p>",
                        "<p>stench</p>",
                        "<p>aroma</p>",
                        "<p>scent</p>"
                    ],
                    solution_en: "<p>92.(c) <strong>aroma</strong><br>&lsquo;Aroma&rsquo; means a pleasant smell often associated with food, drinks or natural scents. The given sentence states that I always love the aroma of my mother&rsquo;s cooking. Hence &lsquo;aroma&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>92.(c) <strong>aroma</strong><br>&lsquo;Aroma&rsquo; का अर्थ है एक सुखद गंध (pleasant smell) जो अक्सर भोजन, पेय या प्राकृतिक सुगंधों से जुड़ी होती है। दिए गए sentence में कहा गया है कि मुझे हमेशा अपनी माँ के हाथ के खाने की सुगंध पसंद आती है। अतः &lsquo;aroma&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "93. Select the option that expresses the following sentence in passive voice.<br />Ravi will play the match.",
                    question_hi: "93. Select the option that expresses the following sentence in passive voice.<br />Ravi will play the match.",
                    options_en: [
                        " The match is played by Ravi.",
                        " The match will have been played by Ravi. ",
                        " The match will be playing by Ravi.",
                        " The match will be played by Ravi."
                    ],
                    options_hi: [
                        " The match is played by Ravi.",
                        " The match will have been played by Ravi. ",
                        " The match will be playing by Ravi.",
                        " The match will be played by Ravi."
                    ],
                    solution_en: "<p>93.(d) The match will be played by Ravi. (Correct)<br>(a) The match <span style=\"text-decoration: underline;\">is played</span> by Ravi. (Incorrect Tense)<br>(b) The match <span style=\"text-decoration: underline;\">will have been</span> played by Ravi. (Incorrect Helping Verb)<br>(c) The match will be <span style=\"text-decoration: underline;\">playing</span> by Ravi. (Incorrect form of the Verb)</p>",
                    solution_hi: "<p>93.(d) The match will be played by Ravi. (Correct)<br>(a) The match <span style=\"text-decoration: underline;\">is played</span> by Ravi. (गलत Tense)<br>(b) The match <span style=\"text-decoration: underline;\">will have been</span> played by Ravi. (गलत Helping Verb)<br>(c) The match will be <span style=\"text-decoration: underline;\">playing</span> by Ravi. (Verb की गलत form)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate ANTONYM of the underlined word.<br>Language <span style=\"text-decoration: underline;\">interacts</span> with all aspects of human life and society.</p>",
                    question_hi: "<p>94. Select the most appropriate ANTONYM of the underlined word.<br>Language <span style=\"text-decoration: underline;\">interacts</span> with all aspects of human life and society.</p>",
                    options_en: [
                        "<p>interrelates</p>",
                        "<p>co-operates</p>",
                        "<p>disconnects</p>",
                        "<p>intermingles</p>"
                    ],
                    options_hi: [
                        "<p>interrelates</p>",
                        "<p>co-operates</p>",
                        "<p>disconnects</p>",
                        "<p>intermingles</p>"
                    ],
                    solution_en: "<p>94.(c) <strong>Disconnects-</strong> to separate two things that are joined.<br><strong>Interacts-</strong> to talk and do things with other people.<br><strong>Interrelates</strong> -to be connected in such a way that each thing has an effect on or depends on the other.<br><strong>Co-operates-</strong> assist someone or comply with their requests.<br><strong>Intermingles-</strong> to become mixed together.</p>",
                    solution_hi: "<p>94.(c) <strong>Disconnects</strong> (पृथक करना) - to separate two things that are joined.<br><strong>Interacts</strong> (बातचीत करना) - to talk and do things with other people.<br><strong>Interrelates</strong> (अंतर्संबंध) -to be connected in such a way that each thing has an effect on or depends on the other.<br><strong>Co-operates</strong> (सहयोग करना) - assist someone or comply with their requests.<br><strong>Intermingles</strong> (घुलमिल जाना) - to become mixed together.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>The CEO\'s decision to cut benefits was a ______ move.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>The CEO\'s decision to cut benefits was a _______move.</p>",
                    options_en: [
                        "<p>rigorous</p>",
                        "<p>laborious</p>",
                        "<p>humorous</p>",
                        "<p>harsh</p>"
                    ],
                    options_hi: [
                        "<p>rigorous</p>",
                        "<p>laborious</p>",
                        "<p>humorous</p>",
                        "<p>harsh</p>"
                    ],
                    solution_en: "<p>95.(d) harsh<br>&lsquo;Harsh&rsquo; means cruel or severe. The given sentence states that the CEO&rsquo;s decision to cut benefits was a harsh move. Hence, &lsquo;harsh&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(d) harsh<br>&lsquo;Harsh&rsquo; का अर्थ है क्रूर या सख्त। दिए गए sentence में बताया गया है कि CEO के लाभ में कटौती करने का निर्णय एक सख्त कदम था। अतः, &lsquo;harsh&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:-&nbsp;</strong><br>Communication is a connection between people sharing information with (96)_____other. It is important in everyday life, (97)_____work and nearly any time you interact with other (98)_____. Communication issues don&rsquo;t always happen (99)_____ of your English level. The truth is, you can know how to speak English (100)____ knowing how to communicate in English.&nbsp;<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:-&nbsp;</strong><br>Communication is a connection between people sharing information with (96)_____other. It is important in everyday life, (97)_____work and nearly any time you interact with other (98)_____. Communication issues don&rsquo;t always happen (99)_____ of your English level. The truth is, you can know how to speak English (100)____ knowing how to communicate in English.&nbsp;<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    options_en: [
                        "<p>every</p>",
                        "<p>each</p>",
                        "<p>some</p>",
                        "<p>one</p>"
                    ],
                    options_hi: [
                        "<p>every</p>",
                        "<p>each</p>",
                        "<p>some</p>",
                        "<p>one</p>"
                    ],
                    solution_en: "<p>96.(b) Each. <br>The phrase &lsquo;each other&rsquo; is used for saying that A does the same thing to B as B does to A. The given passage states that communication is a connection between people sharing information with each other.</p>",
                    solution_hi: "<p>96.(b) Each. <br>Phrase &lsquo;each other&rsquo; का उपयोग यह कहने के लिए किया जाता है कि A, B के लिए वही कार्य करता है जो B, A के लिए करता है। दिए गए passage में कहा गया है कि संचार(communication) एक दूसरे के साथ जानकारी साझा करने वाले लोगों के बीच एक संबंध है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:-&nbsp;</strong><br>Communication is a connection between people sharing information with (96)_____other. It is important in everyday life, (97)_____work and nearly any time you interact with other (98)_____. Communication issues don&rsquo;t always happen (99)_____ of your English level. The truth is, you can know how to speak English (100)____ knowing how to communicate in English.&nbsp;<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:-&nbsp;</strong><br>Communication is a connection between people sharing information with (96)_____other. It is important in everyday life, (97)_____work and nearly any time you interact with other (98)_____. Communication issues don&rsquo;t always happen (99)_____ of your English level. The truth is, you can know how to speak English (100)____ knowing how to communicate in English.&nbsp;<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    options_en: [
                        "<p>from</p>",
                        "<p>on</p>",
                        "<p>at</p>",
                        "<p>while</p>"
                    ],
                    options_hi: [
                        "<p>from</p>",
                        "<p>on</p>",
                        "<p>at</p>",
                        "<p>while</p>"
                    ],
                    solution_en: "<p>97.(c) at.<br>The phrase &lsquo;at work&rsquo; means someone engaged in work. The given passage states that communication is important in everyday life or when we are engaged in our work.</p>",
                    solution_hi: "<p>97.(c) at.<br>At work&rsquo; वाक्यांश (phrase) का अर्थ है कोई व्यक्ति काम में लगा हुआ है। दिया गया passage बताता है कि दैनिक जीवन में या जब हम अपने काम में लगे होते हैं तब संचार महत्वपूर्ण होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-&nbsp;</strong><br>Communication is a connection between people sharing information with (96)_____other. It is important in everyday life, (97)_____work and nearly any time you interact with other (98)_____. Communication issues don&rsquo;t always happen (99)_____ of your English level. The truth is, you can know how to speak English (100)____ knowing how to communicate in English.&nbsp;<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-&nbsp;</strong><br>Communication is a connection between people sharing information with (96)_____other. It is important in everyday life, (97)_____work and nearly any time you interact with other (98)_____. Communication issues don&rsquo;t always happen (99)_____ of your English level. The truth is, you can know how to speak English (100)____ knowing how to communicate in English.&nbsp;<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    options_en: [
                        "<p>creatures</p>",
                        "<p>people</p>",
                        "<p>ones</p>",
                        "<p>animals</p>"
                    ],
                    options_hi: [
                        "<p>creatures</p>",
                        "<p>people</p>",
                        "<p>ones</p>",
                        "<p>animals</p>"
                    ],
                    solution_en: "<p>98.(b) people<br>We generally communicate with people. However, the given passage states that communication is important when we interact with other people.</p>",
                    solution_hi: "<p>98.(b) people<br>हम आम तौर पर लोगों के साथ संवाद करते हैं। हालाँकि, दिया गया passage बताता है कि जब हम अन्य लोगों के साथ बातचीत करते हैं तो संचार महत्वपूर्ण होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:-&nbsp;</strong><br>Communication is a connection between people sharing information with (96)_____other. It is important in everyday life, (97)_____work and nearly any time you interact with other (98)_____. Communication issues don&rsquo;t always happen (99)_____ of your English level. The truth is, you can know how to speak English (100)____ knowing how to communicate in English.&nbsp;<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:-&nbsp;</strong><br>Communication is a connection between people sharing information with (96)_____other. It is important in everyday life, (97)_____work and nearly any time you interact with other (98)_____. Communication issues don&rsquo;t always happen (99)_____ of your English level. The truth is, you can know how to speak English (100)____ knowing how to communicate in English.&nbsp;<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    options_en: [
                        "<p>beside</p>",
                        "<p>since</p>",
                        "<p>because</p>",
                        "<p>about</p>"
                    ],
                    options_hi: [
                        "<p>beside</p>",
                        "<p>since</p>",
                        "<p>because</p>",
                        "<p>about</p>"
                    ],
                    solution_en: "<p>99.(c) because <br>The conjunction &lsquo;because&rsquo; means &lsquo;for the reason that&rsquo; &amp; &lsquo;due to the fact that&rsquo;. The given passage states that communication issues don&rsquo;t always happen due to your English level. Hence, &lsquo;because&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(c) because <br>Conjunction &lsquo;because&rsquo; का अर्थ है \'इस कारण से\'(&lsquo;for the reason that&rsquo;) और \'इस तथ्य के कारण\'( &lsquo;due to the fact that&rsquo;)। दिए गए passage में कहा गया है कि (Communication issues) हमेशा आपके अंग्रेजी के स्तर के कारण नहीं होती हैं। इसलिए, &lsquo;because&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-&nbsp;</strong><br>Communication is a connection between people sharing information with (96)_____other. It is important in everyday life, (97)_____work and nearly any time you interact with other (98)_____. Communication issues don&rsquo;t always happen (99)_____ of your English level. The truth is, you can know how to speak English (100)____ knowing how to communicate in English.&nbsp;<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:-&nbsp;</strong><br>Communication is a connection between people sharing information with (96)_____other. It is important in everyday life, (97)_____work and nearly any time you interact with other (98)_____. Communication issues don&rsquo;t always happen (99)_____ of your English level. The truth is, you can know how to speak English (100)____ knowing how to communicate in English.&nbsp;<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    options_en: [
                        "<p>lacking</p>",
                        "<p>without</p>",
                        "<p>minus</p>",
                        "<p>devoid</p>"
                    ],
                    options_hi: [
                        "<p>lacking</p>",
                        "<p>without</p>",
                        "<p>minus</p>",
                        "<p>devoid</p>"
                    ],
                    solution_en: "<p>100.(b) without<br>Without is used with a verb in the (-ing form) which means &lsquo;not&rsquo;. The given passage states that you can know how to speak English without knowing(-ing) how to communicate in English. Hence, &lsquo;without&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) without<br>Without एक क्रिया(-ing के रूप में) के साथ प्रयोग किया जाता है जिसका अर्थ है &lsquo;not&rsquo;। दिए गए passage में कहा गया है कि आप यह जान सकते हैं कि अंग्रेजी में कैसे संवाद करना है ये जाने बिना कि अंग्रेजी कैसे बोलें। इसलिए, &lsquo;without&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>