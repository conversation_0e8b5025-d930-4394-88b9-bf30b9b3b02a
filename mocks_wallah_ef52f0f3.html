<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">The mean of 36 numbers was found to be 42. Later, it was discovered that a number 47 was misread as 41. Find the correct mean of the given numbers (rounded off to two decimal places).</span></p>\n",
                    question_hi: "<p>1<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> 36 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 42 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> 47 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2354;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 41 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2338;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Kohinoor Devanagari;\">&#2404;</span></p>\n",
                    options_en: ["<p>43.62</p>\n", "<p>42.17</p>\n", 
                                "<p>43.74</p>\n", "<p>42.83</p>\n"],
                    options_hi: ["<p>43.62</p>\n", "<p>42.17</p>\n",
                                "<p>43.74</p>\n", "<p>42.83</p>\n"],
                    solution_en: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">The mean of 36 numbers = 42</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The sum of the all 36 numbers = 36 &times; 42 = 1512</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">But, 47 was misread as 41; So, original average =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1512</mn><mo>+</mo><mn>6</mn></mrow><mn>36</mn></mfrac><mo>=</mo><mn>42</mn><mo>.</mo><mn>17</mn><mo>%</mo></math></span></p>\n",
                    solution_hi: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">36 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 42</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> 36 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> = 36 &times; 42 = 1512</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\">, 47 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 41 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2338;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">; </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1512</mn><mo>+</mo><mn>6</mn></mrow><mn>36</mn></mfrac><mo>=</mo><mn>42</mn><mo>.</mo><mn>17</mn><mo>%</mo></math></span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2<span style=\"font-family: Cambria Math;\">. Find the median of the data</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> 11, 16, 33, 15, 51, 18, 71, 75, 22, 17.</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2325;&#2396;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2367;&#2351;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2375;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">11, 16, 33, 15, 51, 18, 71, 75, 22, 17 </span></p>\n",
                    options_en: ["<p>18</p>\n", "<p>24</p>\n", 
                                "<p>20</p>\n", "<p>22</p>\n"],
                    options_hi: ["<p>18</p>\n", "<p>24</p>\n",
                                "<p>20</p>\n", "<p>22</p>\n"],
                    solution_en: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Arranging in ascending order; 11, 15, 16, 17, 18, 22, 33, 51, 71, 75</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Median =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>t</mi><mi>h</mi><mo>+</mo><mn>6</mn><mi>t</mi><mi>h</mi></mrow><mn>2</mn></mfrac><mo>=</mo><mfrac><mrow><mn>18</mn><mo>+</mo><mn>22</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>20</mn></math></span></p>\n",
                    solution_hi: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2352;&#2379;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">; 11, 15, 16, 17, 18, 22, 33, 51, 71, 75</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>t</mi><mi>h</mi><mo>+</mo><mn>6</mn><mi>t</mi><mi>h</mi></mrow><mn>2</mn></mfrac><mo>=</mo><mfrac><mrow><mn>18</mn><mo>+</mo><mn>22</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>20</mn></math></span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">A group of class IX students conducted a survey for the number of family members in a household on 40 households in a locality, which resulted in the given frequency table. What is the mode of the data?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;<img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-04-01%20at%2010.32.16%20AM.png\" alt=\"\" width=\"313\" height=\"175\"></span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> IX </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2331;&#2366;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2350;&#2370;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2342;&#2360;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2354;&#2366;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 40 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2328;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2352;&#2381;&#2357;&#2375;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2352;&#2367;&#2339;&#2366;&#2350;&#2360;&#2381;&#2357;&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2352;&#2306;&#2348;&#2366;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2369;&#2312;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2337;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&nbsp;<img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-04-01%20at%2011.21.01%20AM.png\" alt=\"\" width=\"354\" height=\"200\"></span></p>\n",
                    options_en: ["<p>6.6</p>\n", "<p>6.7</p>\n", 
                                "<p>6.9</p>\n", "<p>6.8</p>\n"],
                    options_hi: ["<p>6.6</p>\n", "<p>6.7</p>\n",
                                "<p>6.9</p>\n", "<p>6.8</p>\n"],
                    solution_en: "<p>3.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">From the given table, the maximum class frequency = 12, and the corresponding class interval = 6-8(Modal Class)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The lower limit of modal class,&nbsp; l </span><span style=\"font-family: Cambria Math;\">= 6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Class size, h = 8 - 6 = 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Frequency of modal class, </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>f</mi><mn>1</mn></msub></math><span style=\"font-family: Cambria Math;\"> = 12</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Frequency of class proceeding to modal class, </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>f</mi><mn>0</mn></msub></math><span style=\"font-family: Cambria Math;\"> = 8</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Frequency of class succeeding to modal class, </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>f</mi><mn>2</mn></msub></math><span style=\"font-family: Cambria Math;\"> = 6</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Using formula, the mode of the grouped data =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>l</mi><mo>+</mo><mo>(</mo><mfrac><mrow><msub><mi>f</mi><mn>1</mn></msub><mo>-</mo><msub><mi>f</mi><mn>0</mn></msub></mrow><mrow><mn>2</mn><msub><mi>f</mi><mn>1</mn></msub><mo>-</mo><msub><mi>f</mi><mn>0</mn></msub><mo>-</mo><msub><mi>f</mi><mn>2</mn></msub></mrow></mfrac><mo>)</mo><mo>&times;</mo><mi>h</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=</span><span style=\"font-family: Cambria Math;\"> &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mo>+</mo><mo>(</mo><mfrac><mrow><mn>12</mn><mo>-</mo><mn>8</mn></mrow><mrow><mn>24</mn><mo>-</mo><mn>8</mn><mo>-</mo><mn>6</mn></mrow></mfrac><mo>)</mo><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mn>6</mn><mo>+</mo><mo>(</mo><mfrac><mn>4</mn><mn>10</mn></mfrac><mo>)</mo><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>+</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>34</mn></mrow><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 6.8</span></p>\n",
                    solution_hi: "<p>3.(d)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2350;&#2333;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2343;&#2367;&#2325;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2357;&#2371;&#2340;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> = 12 , </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2306;&#2340;&#2352;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> = 6-8 (</span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2379;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2354;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2379;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2354;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2367;&#2330;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\">, l </span><span style=\"font-family: Cambria Math;\">= 6</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, <span style=\"font-weight: 400;\">h</span> </span><span style=\"font-family: Cambria Math;\">= 8 - 6 = 2</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2379;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2357;&#2371;&#2340;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>f</mi><mn>1</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> &#8203;&#8203;= 12</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2379;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2354;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2352;&#2306;&#2348;&#2366;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>f</mi><mn>0</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> = 8</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2379;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2381;&#2354;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2352;&#2306;&#2348;&#2366;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>f</mi><mn>2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> = 6</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2344;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2350;&#2370;&#2361;&#2368;&#2325;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2337;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2326;&#2379;&#2332;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">: =</span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mi>l</mi><mo>+</mo><mo>(</mo><mfrac><mrow><msub><mi>f</mi><mn>1</mn></msub><mo>-</mo><msub><mi>f</mi><mn>0</mn></msub></mrow><mrow><mn>2</mn><msub><mi>f</mi><mn>1</mn></msub><mo>-</mo><msub><mi>f</mi><mn>0</mn></msub><mo>-</mo><msub><mi>f</mi><mn>2</mn></msub></mrow></mfrac><mo>)</mo><mo>&times;</mo><mi>h</mi></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mo>+</mo><mo>(</mo><mfrac><mrow><mn>12</mn><mo>-</mo><mn>8</mn></mrow><mrow><mn>24</mn><mo>-</mo><mn>8</mn><mo>-</mo><mn>6</mn></mrow></mfrac><mo>)</mo><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mn>6</mn><mo>+</mo><mo>(</mo><mfrac><mn>4</mn><mn>10</mn></mfrac><mo>)</mo><mo>&times;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>+</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>34</mn></mrow><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">= 6.8</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Find the mode of the data 2, 2, 3, 5, 15, 15, 15, 20, 21, 23, 25, 15, 23, 25.</p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2305;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> 2, 2, 3, 5, 15, 15, 15, 20, 21, 23, 25, 15, 23, 25 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>21</p>\n", "<p>25</p>\n", 
                                "<p>23</p>\n", "<p>15</p>\n"],
                    options_hi: ["<p>21</p>\n", "<p>25</p>\n",
                                "<p>23</p>\n", "<p>15</p>\n"],
                    solution_en: "<p>4.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">2 , 2 , 3 , 5 , 15 , 15 , 15 , 15 , 20 , 21 , 23 , 23 , 25 , 25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mode = highest frequency of number = 15 </span></p>\n",
                    solution_hi: "<p>4.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">2 , 2 , 3 , 5 , 15 , 15 , 15 , 15 , 20 , 21 , 23 , 23 , 25 , 25</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2330;&#2381;&#2330;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2357;&#2371;&#2340;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">= 15</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Find the median class of:</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image1.png\" width=\"413\" height=\"68\"></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image1.png\"></p>\n",
                    options_en: ["<p>50-100</p>\n", "<p>150-200</p>\n", 
                                "<p>0-50</p>\n", "<p>100-150</p>\n"],
                    options_hi: ["<p>50-100</p>\n", "<p>150-200</p>\n",
                                "<p>0-50</p>\n", "<p>100-150</p>\n"],
                    solution_en: "<p>5.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image2.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total number of terms = 50</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Median = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mn>25</mn><mrow><mi>t</mi><mi>h</mi><mo>&nbsp;</mo></mrow></msup></math><span style=\"font-family: Cambria Math;\">and </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mn>26</mn><mrow><mi>t</mi><mi>h</mi><mo>&nbsp;</mo></mrow></msup></math><span style=\"font-family: Cambria Math;\">term.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, Median class = (50 - 100) </span></p>\n",
                    solution_hi: "<p>5.(a)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image2.png\"></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 50</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 25</span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 26</span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2342;&#2404;</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> = (50 - 100)</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">For a grouped data, if <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>X</mi><mi>i</mi></msub></math></span><span style=\"font-family: Cambria Math;\"> is the class mark and <span style=\"font-weight: 400;\">&nbsp;</span><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>f</mi><mi>i</mi></msub></math></span></span><span style=\"font-family: Cambria Math;\">&nbsp;is the corresponding frequency, then by direct method, mean x is given by:</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Kohinoor Devanagari;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2350;&#2370;&#2361;&#2368;&#2325;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2306;&#2325;&#2396;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> X </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2330;&#2367;&#2361;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> f </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2352;&#2350;&#2381;&#2348;&#2366;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> ______ </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> :</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&sum;</mo><msub><mi>x</mi><mi>i</mi></msub></mrow><mrow><mo>&sum;</mo><msub><mi>f</mi><mi>i</mi></msub></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&sum;</mo><msub><mi>f</mi><mi>i</mi></msub><msub><mi>x</mi><mi>i</mi></msub></mrow><mrow><mo>&sum;</mo><msub><mi>f</mi><mi>i</mi></msub></mrow></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&sum;</mo><msub><mi>f</mi><mi>i</mi></msub><msub><mi>x</mi><mi>i</mi></msub></mrow><mrow><mo>&sum;</mo><msub><mi>x</mi><mi>i</mi></msub></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&sum;</mo><msub><mi>f</mi><mi>i</mi></msub><msub><mi>x</mi><mi>i</mi></msub></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&sum;</mo><msub><mi>x</mi><mi>i</mi></msub></mrow><mrow><mo>&sum;</mo><msub><mi>f</mi><mi>i</mi></msub></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&sum;</mo><msub><mi>f</mi><mi>i</mi></msub><msub><mi>x</mi><mi>i</mi></msub></mrow><mrow><mo>&sum;</mo><msub><mi>f</mi><mi>i</mi></msub></mrow></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&sum;</mo><msub><mi>f</mi><mi>i</mi></msub><msub><mi>x</mi><mi>i</mi></msub></mrow><mrow><mo>&sum;</mo><msub><mi>x</mi><mi>i</mi></msub></mrow></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&sum;</mo><msub><mi>f</mi><mi>i</mi></msub><msub><mi>x</mi><mi>i</mi></msub></math></p>\n"],
                    solution_en: "<p>6(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Mean =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&Sigma;</mi><msub><mi>f</mi><mi>i</mi></msub><msub><mi>x</mi><mi>i</mi></msub></mrow><mrow><mi>&Sigma;</mi><msub><mi>f</mi><mi>i</mi></msub></mrow></mfrac><mo>&nbsp;</mo><mo>,</mo><mo>&nbsp;</mo><mi>w</mi><mi>h</mi><mi>e</mi><mi>r</mi><mi>e</mi><mo>&nbsp;</mo><msub><mi>x</mi><mi>i</mi></msub><mo>=</mo><mi>c</mi><mi>l</mi><mi>a</mi><mi>s</mi><mi>s</mi><mo>&nbsp;</mo><mi>m</mi><mi>a</mi><mi>r</mi><mi>k</mi><mo>&nbsp;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><msub><mi>f</mi><mi>i</mi></msub><mo>&nbsp;</mo><mo>=</mo><mi>f</mi><mi>r</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>e</mi><mi>n</mi><mi>c</mi><mi>y</mi></math></span></p>\n",
                    solution_hi: "<p>6(b)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&Sigma;</mi><msub><mi>f</mi><mi>i</mi></msub><msub><mi>x</mi><mi>i</mi></msub></mrow><mrow><mi>&Sigma;</mi><msub><mi>f</mi><mi>i</mi></msub></mrow></mfrac></math> <span style=\"font-weight: 400;\">&nbsp; &#2332;&#2361;&#2366;&#2306;&nbsp; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>x</mi><mi>i</mi></msub></math><span style=\"font-weight: 400;\"> </span><span style=\"font-weight: 400;\">= &#2357;&#2352;&#2381;&#2327; &#2330;&#2367;&#2361;&#2381;&#2344;&nbsp; &#2324;&#2352; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><msub><mi>f</mi><mi>i</mi></msub><mo>&nbsp;</mo></math><span style=\"font-weight: 400;\">= &#2348;&#2366;&#2352;&#2306;&#2348;&#2366;&#2352;&#2340;&#2366;</span></span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> Find x, if the median is 28.5.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image3.png\"></p>\n",
                    question_hi: "<p>7.<span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 28.5 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image4.png\"></p>\n",
                    options_en: ["<p>8</p>\n", "<p>7</p>\n", 
                                "<p>2</p>\n", "<p>4</p>\n"],
                    options_hi: ["<p>8</p>\n", "<p>7</p>\n",
                                "<p>2</p>\n", "<p>4</p>\n"],
                    solution_en: "<p>7(a)</p>\r\n<p><span style=\"font-family: \'Cambria Math\';\"><img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-04-01%20at%2011.41.55%20AM.png\" alt=\"\" width=\"233\" height=\"150\"> </span></p>\r\n<p><span style=\"font-family: \'Cambria Math\';\">Total frequency(N) = 52+x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">N/2 = 26 +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So , the C.F. corresponding to this is (20-30)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">l = 20 , CF = 5+x , f = 20 and h = 10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Median = l+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mo>&nbsp;</mo><mi>N</mi></mrow><mn>2</mn></mfrac><mo>-</mo><mi>C</mi><mi>F</mi></mstyle><mi>f</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times;h</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">28.5= 20+&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>26</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>2</mn></mfrac></mstyle><mo>-</mo><mn>5</mn><mo>-</mo><mi>x</mi></mrow><mn>20</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times;10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8.5 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>26</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>2</mn></mfrac></mstyle><mo>-</mo><mn>5</mn><mo>-</mo><mi>x</mi></mrow><mn>2</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">17 = 21 -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x= 8</span></p>\n",
                    solution_hi: "<p>7(a)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\"><span style=\"font-family: Cambria Math;\">&nbsp;<img src=\"https://ssccglpinnacle.com/images/Screenshot%202023-04-01%20at%2011.43.33%20AM.png\" alt=\"\" width=\"237\" height=\"152\"></span></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2357;&#2371;&#2340;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> (N) = 52+x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">N/2 = 26 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2344;&#2369;&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> C.F. </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> :(20-30) </span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">l = 20, CF = 5+x , F = 20 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> H=10</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = l+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mrow><mo>&nbsp;</mo><mi>N</mi></mrow><mn>2</mn></mfrac></mstyle><mo>-</mo><mi>C</mi><mi>F</mi></mrow><mi>f</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times; h</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">28.5 = 20+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>26</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>2</mn></mfrac></mstyle><mo>-</mo><mn>5</mn><mo>-</mo><mi>x</mi></mrow><mn>20</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\">&times;10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8.5 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>26</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mi>x</mi><mn>2</mn></mfrac></mstyle><mo>-</mo><mn>5</mn><mo>-</mo><mi>x</mi></mrow><mn>2</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">17 = 21 -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x= 8</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">The mean of the data 9, 3, 5, 4, 4, 5 and y is y. What is the mode of the data?</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> 9, 3, 5, 4, 4, 5 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> y </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>4</p>\n", "<p>5</p>\n", 
                                "<p>9</p>\n", "<p>3</p>\n"],
                    options_hi: ["<p>4</p>\n", "<p>5</p>\n",
                                "<p>9</p>\n", "<p>3</p>\n"],
                    solution_en: "<p>8.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>y</mi></mrow><mn>7</mn></mfrac><mo>=</mo><mi>y</mi></math></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>30 + y = 7y</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>7y - y = 30</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>6y = 30 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-weight: 400;\">y = 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, 3 , 4 , 4 , 5 , 5 , 5 , 9</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>Mode = 5</span></p>\n",
                    solution_hi: "<p>8.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>3</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>5</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>y</mi></mrow><mn>7</mn></mfrac><mo>=</mo><mi>y</mi></math></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>30 + y = 7y</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>7y - y = 30</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>6y = 30 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>y = 5</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, 3 , 4 , 4 , 5 , 5 , 5 , 9</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= 5</span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> Find the arithmetic mean of 5, 15, 23, 26 and 29.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\"> 5, 15, 23, 26 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 29 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2350;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2404;</span></p>\n",
                    options_en: ["<p>20.6</p>\n", "<p>18.6</p>\n", 
                                "<p>19.6</p>\n", "<p>17.6</p>\n"],
                    options_hi: ["<p>20.6</p>\n", "<p>18.6</p>\n",
                                "<p>19.6</p>\n", "<p>17.6</p>\n"],
                    solution_en: "<p>9.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Arithmetic Mean =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>23</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>26</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>29</mn></mrow><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>98</mn><mn>5</mn></mfrac><mo>=</mo><mn>19</mn><mo>.</mo><mn>6</mn></math></span></p>\n",
                    solution_hi: "<p>9.(c)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2350;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>15</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>23</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>26</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>29</mn></mrow><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>98</mn><mn>5</mn></mfrac><mo>=</mo><mn>19</mn><mo>.</mo><mn>6</mn></math></span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Which of the following is the correct empirical formula?</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>o</mi><mi>d</mi><mi>e</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>M</mi><mi>e</mi><mi>a</mi><mi>n</mi></mrow><mn>3</mn></mfrac></math>= Median - Mean</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>o</mi><mi>d</mi><mi>e</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>M</mi><mi>e</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>3</mn><mi>x</mi><mn>2</mn></mrow></mfrac></math>= Median - Mean</span></p>\n", 
                                "<p>3 (mode-mean) = median - mean</p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>(Mode - Mean) = Median - Mean</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2348;&#2361;&#2369;&#2354;&#2325;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi></mrow><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></mfrac></math>= </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2348;&#2361;&#2369;&#2354;&#2325;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi></mrow><mrow><mn>3</mn><mi>x</mi><mn>2</mn></mrow></mfrac></math>= </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> &ndash; </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\n",
                                "<p>3 ((<span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">)) = </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\n", "<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>(</span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) = </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> &ndash; </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\n"],
                    solution_en: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>o</mi><mi>d</mi><mi>e</mi><mo>-</mo><mi>M</mi><mi>e</mi><mi>a</mi><mi>n</mi></mrow><mn>3</mn></mfrac><mo>=</mo><mi>M</mi><mi>e</mi><mi>d</mi><mi>i</mi><mi>a</mi><mi>n</mi><mo>-</mo><mi>M</mi><mi>e</mi><mi>a</mi><mi>n</mi></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&rArr; <span style=\"font-weight: 400;\">Mode - Mean = 3 Median -3 Mean</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&rArr; </span></span><span style=\"font-weight: 400;\">Mode = 3 Median - 3 Mean + Mean</span></p>\r\n<p><span style=\"font-weight: 400;\"><span style=\"font-family: Cambria Math;\">&rArr; </span>Mode = 3 Median - 2 Mean</span></p>\n",
                    solution_hi: "<p>10.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2348;&#2361;&#2369;&#2354;&#2325;</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>&#2350;&#2366;&#2343;&#2381;&#2351;</mi></mrow><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></mfrac></math>= </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 3</span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - 3</span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 3</span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - 3</span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> + </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 3</span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - 2</span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\n",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> Find the median of:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><img src=\"https://ssccglpinnacle.com/images/mceu_56652438911680331482696.png\" width=\"482\" height=\"60\"></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><img src=\"https://ssccglpinnacle.com/images/mceu_21838045911680331710221.png\" width=\"450\" height=\"56\"></span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">40</span></p>\n", "<p>43</p>\n", 
                                "<p>42</p>\n", "<p>41</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">40</span></p>\n", "<p>43</p>\n",
                                "<p>42</p>\n", "<p>41</p>\n"],
                    solution_en: "<p>11.(b)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image5.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Number of term n = 50</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">N/2 = 25 , Median-class = (40 - 50) , I = 40 , C.F = 22 , h = 10 , f = 10</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Median =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>I</mi><mo>+</mo><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mi>N</mi><mn>2</mn></mfrac></mstyle><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>C</mi><mo>.</mo><mi>F</mi></mrow><mi>f</mi></mfrac><mo>&times;</mo><mi>h</mi><mo>=</mo><mn>40</mn><mo>+</mo><mfrac><mrow><mn>25</mn><mo>-</mo><mn>22</mn></mrow><mn>110</mn></mfrac><mo>&times;</mo><mn>10</mn><mo>=</mo><mn>43</mn><mo>&nbsp;</mo></math></span></p>\n",
                    solution_hi: "<p>11.(b)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image5.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">n </span><span style=\"font-family: Kohinoor Devanagari;\">&#2346;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 50</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">N/2 = 25, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Kohinoor Devanagari;\">&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> = (40 - 50), I = 40, C.F = 22, h = 10, f = 10</span></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>I</mi><mo>+</mo><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mi>N</mi><mn>2</mn></mfrac></mstyle><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>C</mi><mo>.</mo><mi>F</mi></mrow><mi>f</mi></mfrac><mo>&times;</mo><mi>h</mi><mo>=</mo><mn>40</mn><mo>+</mo><mfrac><mrow><mn>25</mn><mo>-</mo><mn>22</mn></mrow><mn>110</mn></mfrac><mo>&times;</mo><mn>10</mn><mo>=</mo><mn>43</mn><mo>&nbsp;</mo></math></span></p>\n",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\">If the mean of the data 11, 17, x + 1, 3x, 19, 2x - 4, x + 5 is 21, then find the mode of the data.</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> 11, 17, x + 1, 3x, 19, 2x - 4, x + 5 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 21 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\"> 17</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">15</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">11</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">19</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"> 17</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">15</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">11</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">19</span></p>\n"],
                    solution_en: "<p>12.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>17</mn><mo>+</mo><mi>x</mi><mo>+</mo><mn>1</mn><mo>+</mo><mn>3</mn><mi>x</mi><mo>+</mo><mn>19</mn><mo>+</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>4</mn><mo>+</mo><mi>x</mi><mo>+</mo><mn>5</mn></mrow><mn>7</mn></mfrac><mo>=</mo><mn>21</mn></math></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math><span style=\"font-weight: 400;\">7x + 49 = 147</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mn>7</mn><mi>x</mi><mo>=</mo><mn>147</mn><mo>-</mo><mn>49</mn></math></span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mn>7</mn><mi>x</mi><mo>=</mo><mn>98</mn><mi>x</mi><mo>=</mo><mn>14</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">11, 17, 15, 42, 19, 24, 19;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Mode = 19</span></p>\n",
                    solution_hi: "<p>12.(d)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>17</mn><mo>+</mo><mi>x</mi><mo>+</mo><mn>1</mn><mo>+</mo><mn>3</mn><mi>x</mi><mo>+</mo><mn>19</mn><mo>+</mo><mn>2</mn><mi>x</mi><mo>-</mo><mn>4</mn><mo>+</mo><mi>x</mi><mo>+</mo><mn>5</mn></mrow><mn>7</mn></mfrac><mo>=</mo><mn>21</mn></math></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>7x + 49 = 147</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>7x = 147 - 49</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>7x = 98 x = 14</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">11, 17, 15, 42, 19, 24, 19; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 19</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">If the mean of the following data is 4, find the missing frequency.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image6.png\"></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2354;&#2369;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2366;&#2352;&#2306;&#2348;&#2366;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image6.png\"></p>\n",
                    options_en: ["<p>5</p>\n", "<p>4</p>\n", 
                                "<p>7</p>\n", "<p>3</p>\n"],
                    options_hi: ["<p>5</p>\n", "<p>4</p>\n",
                                "<p>7</p>\n", "<p>3</p>\n"],
                    solution_en: "<p>13.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image7.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mean =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>+</mo><mn>20</mn><mo>+</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>16</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>4</mn><mo>+</mo><mi>x</mi><mo>+</mo><mn>8</mn></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>+</mo><mn>9</mn><mi>x</mi></mrow><mrow><mn>15</mn><mo>+</mo><mi>x</mi></mrow></mfrac><mo>=</mo><mn>4</mn></math></span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>9x + 45 = 4x + 60&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>9x - 4x = 60 - 45</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>5x = 15<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>x = 3</span></p>\n",
                    solution_hi: "<p>13.(d)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image7.png\"></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>9</mn><mo>+</mo><mn>20</mn><mo>+</mo><mn>9</mn><mi>x</mi><mo>+</mo><mn>16</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>4</mn><mo>+</mo><mi>x</mi><mo>+</mo><mn>8</mn></mrow></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>45</mn><mo>+</mo><mn>9</mn><mi>x</mi></mrow><mrow><mn>15</mn><mo>+</mo><mi>x</mi></mrow></mfrac><mo>=</mo><mn>4</mn></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>9x + 45 = 4x + 60</span></span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>9x - 4x = 60 - 45</span></p>\r\n<p><span style=\"font-weight: 400;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>5x = 15x = 3</span></p>\n",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">Find the arithmetic mean of the given data.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image8.png\"></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Kohinoor Devanagari;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2337;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2309;&#2306;&#2325;&#2327;&#2339;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image8.png\"></p>\n",
                    options_en: ["<p>26.4</p>\n", "<p>30.6</p>\n", 
                                "<p>30.4</p>\n", "<p>16.4</p>\n"],
                    options_hi: ["<p>26.4</p>\n", "<p>30.6</p>\n",
                                "<p>30.4</p>\n", "<p>16.4</p>\n"],
                    solution_en: "<p>14.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image9.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Mean =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>120</mn><mo>+</mo><mn>240</mn><mo>+</mo><mn>250</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>760</mn><mn>25</mn></mfrac><mo>=</mo><mn>30</mn><mo>.</mo><mn>4</mn></math></span></p>\n",
                    solution_hi: "<p>14.(c)</p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1680324690/word/media/image9.png\"></p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>50</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>120</mn><mo>+</mo><mn>240</mn><mo>+</mo><mn>250</mn></mrow><mrow><mn>5</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>6</mn><mo>+</mo><mn>5</mn></mrow></mfrac><mo>=</mo><mfrac><mn>760</mn><mn>25</mn></mfrac><mo>=</mo><mn>30</mn><mo>.</mo><mn>4</mn></math></span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">If the mean of a data is 65 and its mode is 23 then the median using empirical formula is:</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2367;&#2344;&#2381;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 65 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2344;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> 23 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2370;&#2354;&#2366;&#2344;&#2369;&#2346;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Kohinoor Devanagari;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Kohinoor Devanagari;\">&#2404;</span></p>\n",
                    options_en: ["<p>53</p>\n", "<p>54</p>\n", 
                                "<p>51</p>\n", "<p>52</p>\n"],
                    options_hi: ["<p>53</p>\n", "<p>54</p>\n",
                                "<p>51</p>\n", "<p>52</p>\n"],
                    solution_en: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Mode = 3 Median - 2 Mean</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>23 = 3 Median - 2 &times; 65</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>3 Median = 23 + 130</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>3Median = 153</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>Median = 51</span></p>\n",
                    solution_hi: "<p>15.(c)</p>\r\n<p><span style=\"font-family: Kohinoor Devanagari;\">&#2348;&#2361;&#2369;&#2354;&#2325;</span><span style=\"font-family: Cambria Math;\"> = 3 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - 2 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>23 = 3 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> - 2 &times; 65</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>3 </span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 23 + 130</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math>3</span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 153</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo></math></span><span style=\"font-family: Kohinoor Devanagari;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 51</span></p>\n",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>