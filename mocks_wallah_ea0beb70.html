<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Four words have been given, out of which three are alike in some manner and one is different. Select the odd one.</p>",
                    question_hi: "<p>1. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। विषम का चयन करें।</p>",
                    options_en: ["<p>Postcard</p>", "<p>Pincode</p>", 
                                "<p>Parcel</p>", "<p>Letter</p>"],
                    options_hi: ["<p>पोस्टकार्ड</p>", "<p>पिनकोड</p>",
                                "<p>पार्सल</p>", "<p>पत्र</p>"],
                    solution_en: "<p>1.(b)<br>From the given options three words are alike : Postcard, parcel, letter, as they are all related to parcel something or post something and they are all some sort of material/paper.<br>But Pincode is different from them as it is a number.</p>",
                    solution_hi: "<p>1.(b)<br>दिए गए विकल्पों में से तीन शब्द समान हैं: पोस्टकार्ड, पार्सल, पत्र, क्योंकि वे सभी पार्सल से संबंधित हैं या कुछ पोस्ट करते हैं और वे सभी किसी प्रकार की सामग्री/कागज हैं।<br>लेकिन पिनकोड उनसे अलग है क्योंकि यह एक संख्या है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the Venn diagram that best represents the relationship between fruits , citrus fruits and oranges</p>",
                    question_hi: "<p>2. वेन आरेख का चयन करें जो फलों, खट्टे फलों और संतरे के बीच संबंध को सर्वोत्तम रूप से दर्शाता है</p>",
                    options_en: ["<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918743724.png\" alt=\"rId4\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918743835.png\" alt=\"rId5\" width=\"120\" height=\"72\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918743942.png\" alt=\"rId6\" width=\"80\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744039.png\" alt=\"rId7\" width=\"79\" height=\"80\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918743724.png\" alt=\"rId4\" width=\"94\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918743835.png\" alt=\"rId5\" width=\"120\" height=\"72\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918743942.png\" alt=\"rId6\" width=\"80\" height=\"81\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744039.png\" alt=\"rId7\" width=\"85\" height=\"86\"></p>"],
                    solution_en: "<p>2.(d)<br>The Venn diagram that best represents the relationship between fruits , citrus fruits and oranges is :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744039.png\" alt=\"rId7\" width=\"85\" height=\"86\"><br>Because Orange is an example of fruit which is classified as citrus fruit.</p>",
                    solution_hi: "<p>2.(d)<br>वेन आरेख जो फलों, खट्टे फलों और संतरे के बीच संबंध को सबसे अच्छा दर्शाता है वह है:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744039.png\" alt=\"rId7\" width=\"85\" height=\"86\"><br>क्योंकि संतरा फल का एक उदाहरण है जिसे खट्टे फल के रूप में वर्गीकृत किया गया है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "3. Select the option that will fill in the blanks and complete the given series.<br />ZA, BY, XC, DW, ____",
                    question_hi: "3. उस विकल्प का चयन करें जो रिक्त स्थानों को भरेगा और दी गई श्रृंखला को पूरा करेगा।<br />ZA, BY, XC, DW, ____",
                    options_en: [" WB", " OC", 
                                " XA", " VE"],
                    options_hi: [" WB", " OC",
                                " XA", " VE"],
                    solution_en: "3.(d)<br />ZA, BY, XC, DW, ?<br />Here the letters are written from the beginning and also from the end consequently.<br />So the fifth term will be VE.",
                    solution_hi: "3.(d)<br />ZA, BY, XC, DW, ?<br />यहाँ अक्षर शुरुआत से और अंत से भी लिखे जाते हैं।<br />अतः पाँचवाँ पद VE होगा।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "4. Four words have been given, out of which three are alike in some manner and one is different. Select the odd one. ",
                    question_hi: "4. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। विषम का चयन करें।",
                    options_en: [" Screwdriver", " Drill", 
                                " Hanger", " Spanner"],
                    options_hi: [" स्क्रूड्राइवर", " ड्रिल",
                                " हैंगर", " स्पैनर"],
                    solution_en: "4.(c)<br />Screwdriver, Drill, Spanner: these three are mechanical tools which are used for certain operations. <br />Whereas Hanger is different because it is a simple object to keep clothes or similar objects.",
                    solution_hi: "4.(c)<br />स्क्रूड्राइवर, ड्रिल, स्पैनर: ये तीनों यांत्रिक उपकरण हैं जिनका उपयोग कुछ कार्यों के लिए किया जाता है।<br />जबकि हैंगर अलग है क्योंकि कपड़े या इसी तरह की वस्तुओं को रखना एक साधारण वस्तु है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5. Four words have been given, out of which three are alike in some manner and one is different . Select the add one. ",
                    question_hi: "5. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। एक जोड़ें का चयन करें।",
                    options_en: [" Knight", " Ace", 
                                " Pawn", " Bishop"],
                    options_hi: [" योद्धा", " ऐस",
                                " प्यादा", " बिशप"],
                    solution_en: "5.(b)<br />Knight, Pawn, Bishop: these are the positions of various persons or Humans.<br />Whereas Ace is a playing card which has a single shape on it.",
                    solution_hi: "5.(b)<br />नाइट, प्यादा, बिशप: ये विभिन्न व्यक्तियों या मनुष्यों के पद हैं।<br />जबकि इक्का (ताश में) एक प्लेइंग कार्ड है जिस पर एक ही आकार होता है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Find the minimum number of straight lines in the following figure.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744156.png\" alt=\"rId8\" width=\"132\" height=\"105\"></p>",
                    question_hi: "<p>6. निम्नलिखित आकृति में सीधी रेखाओं की न्यूनतम संख्या ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744156.png\" alt=\"rId8\" width=\"132\" height=\"105\"></p>",
                    options_en: ["<p>10</p>", "<p>12</p>", 
                                "<p>13</p>", "<p>11</p>"],
                    options_hi: ["<p>10</p>", "<p>12</p>",
                                "<p>13</p>", "<p>11</p>"],
                    solution_en: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744156.png\" alt=\"rId8\" width=\"132\" height=\"105\"><br>If we look at the outer square the minimum number of straight lines required will be = 6 ;<br>And for the inner square the minimum number of squares required will be = 6 ;<br>So, the minimum number of straight lines in the given figure is 12 ;</p>",
                    solution_hi: "<p>6.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744156.png\" alt=\"rId8\" width=\"132\" height=\"105\"><br>यदि हम बाहरी वर्ग को देखें तो आवश्यक सीधी रेखाओं की न्यूनतम संख्या = 6 होगी;<br>और भीतरी वर्ग के लिए आवश्यक वर्गों की न्यूनतम संख्या = 6 होगी;<br>अत: दी गई आकृति में सरल रेखाओं की न्यूनतम संख्या 12 है;</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Consider the given statement and decide which of the given assumptions is/are implicit in the statement.<br><strong>Statement:</strong><br>&ldquo;Moral education should be compulsory at primary school level.&rdquo; - A part of the Education Minister&rsquo;s speech.<br><strong>Assumptions:</strong><br>1. Only young children can be provided moral education.<br>2. Older children do not require moral education.</p>",
                    question_hi: "<p>7. दिए गए कथन पर विचार करें और तय करें कि दिए गए अनुमानों में से कौन सा/से कथन में निहित है/हैं।<br><strong>कथन:</strong><br>\"प्राथमिक विद्यालय स्तर पर नैतिक शिक्षा अनिवार्य होनी चाहिए।\" - शिक्षा मंत्री के भाषण का एक अंश।<br><strong>धारणाएं:</strong><br>केवल छोटे बच्चों को ही नैतिक शिक्षा प्रदान की जा सकती है।<br>बड़े बच्चों को नैतिक शिक्षा की आवश्यकता नहीं होती है।</p>",
                    options_en: ["<p>Neither assumption 1 nor 2 are implicit,</p>", "<p>Only assumption 1 is implicit.</p>", 
                                "<p>Only assumption 2 is implicit</p>", "<p>Either assumption 1 or 2 are implicit.</p>"],
                    options_hi: ["<p>न तो धारणा 1 और न ही 2 निहित हैं,</p>", "<p>केवल धारणा 1 निहित है।</p>",
                                "<p>केवल धारणा 2 निहित है</p>", "<p>या तो धारणा 1 या 2 निहित हैं।</p>"],
                    solution_en: "<p>7.(a)<br><strong>Statement:</strong><br>&ldquo;Moral education should be compulsory at primary school level.&rdquo; - A part of the Education Minister&rsquo;s speech.<br>From the above statement we can say that it does not mean that Only young children can be provided moral education.<br>So, assumption 1 is wrong.<br>Again from the statement it is clear that moral education is needed for all that is why it should be compulsory right from the beginning i.e. at primary schools.<br>So, assumption 2 : &lsquo;Older children do not require moral education.&rsquo; is definitely wrong.</p>",
                    solution_hi: "<p>7.(a)<br><strong>कथन:</strong><br>\"प्राथमिक विद्यालय स्तर पर नैतिक शिक्षा अनिवार्य होनी चाहिए।\" - शिक्षा मंत्री के भाषण का एक अंश।<br>उपरोक्त कथन से हम कह सकते हैं कि इसका यह अर्थ नहीं है कि केवल छोटे बच्चों को ही नैतिक शिक्षा प्रदान की जा सकती है।<br>तो, धारणा 1 गलत है।<br>इस कथन से पुनः स्पष्ट है कि नैतिक शिक्षा सभी के लिए आवश्यक है, इसलिए इसे प्रारम्भ से ही अर्थात् प्राथमिक विद्यालयों में अनिवार्य होना चाहिए।<br>तो, धारणा 2 : \'बड़े बच्चों को नैतिक शिक्षा की आवश्यकता नहीं होती है।\' निश्चित रूप से गलत है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "8. In a group of four people, A is older than B, D is younger than B and C is older than D but younger than B. Who is the youngest among all?",
                    question_hi: "8. चार लोगों के एक समूह में, A, B से बड़ा है, D, B से छोटा है और C, D से बड़ा है लेकिन B से छोटा है। सभी में सबसे छोटा कौन है?",
                    options_en: [" B", " D", 
                                " A", " C"],
                    options_hi: [" B", " D",
                                " A", " C"],
                    solution_en: "8.(b)<br />In a group of four people, A is older than B, D is younger than B and C is older than D but younger than B.<br />So the descending order will be : <br />A > B > C > D <br />Then D is the youngest among all.",
                    solution_hi: "8.(b)<br />चार लोगों के समूह में, A, B से बड़ा है, D, B से छोटा है और C, D से बड़ा है लेकिन B से छोटा है।<br />तो अवरोही क्रम होगा:<br />A > B > C > D <br />तब D सभी में सबसे छोटा है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "9. In a certain code language, ABLE is written as 3781, HAIR is written as 4365 and TOOL is written as 9008. How will RATTEL be written as in that language?",
                    question_hi: "9. एक निश्चित कूट भाषा में ABLE को 3781, HAIR को 4365 और TOOL को 9008 लिखा जाता है। उस भाषा में RATTEL को किस प्रकार लिखा जाएगा?",
                    options_en: [" 593381", " 539981", 
                                " 53009", " 539918"],
                    options_hi: [" 593381", " 539981",
                                " 53009", " 539918"],
                    solution_en: "9.(b)<br />ABLE is written as 3781, HAIR is written as 4365 and TOOL is written as 9008 ;<br />Then the code for RATTLE will be = 539981 ;",
                    solution_hi: "9.(b)<br />ABLE को 3781, HAIR को 4365 और TOOL को 9008 लिखा जाता है;<br />तब RATTLE का कोड होगा = 539981;",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "10. In a certain code language RATE is written as IZGV, How will FUND  be written as in that language .",
                    question_hi: "10. एक निश्चित कोड भाषा में RATE को IZGV के रूप में लिखा जाता है, उस भाषा में FUND को कैसे लिखा जाएगा।",
                    options_en: [" UWFM", " WMFU", 
                                " UFMW", " MFWU"],
                    options_hi: [" UWFM", " WMFU",
                                " UFMW", " MFWU"],
                    solution_en: "10.(c)<br />In  a certain code language RATE is written as IZGV ;<br />i.e. the code is made by replacing the letters by its opposite letters ;<br />[opposite = (27 - place value)]<br />So, FUND will be written as UFMW in that language.",
                    solution_hi: "10.(c)<br />एक निश्चित कोड भाषा में RATE को IZGV के रूप में लिखा जाता है;<br />यानी अक्षरों को उसके विपरीत अक्षरों से बदलकर कोड बनाया जाता है;<br />[विपरीत = (27 - स्थानीय मान)]<br />तो उस भाषा में FUND को UFMW लिखा जाएगा।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Read the given statement and conclusions carefully and decide which of the conclusions logically follow (s) from the statement.<br><strong>Statement:</strong><br>It is appropriate to marry a girl who is 18 years of age and above.<br><strong>Conclusions:</strong><br>1. At this age, a girl reaches an appropriate level of physical, biological and psychological maturity to become a mother.<br>2. People do not marry girls less than 18 years of age because of fear.</p>",
                    question_hi: "<p>11. दिए गए कथन और निष्कर्षों को ध्यान से पढ़ें और तय करें कि कौन सा निष्कर्ष कथन का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>18 साल और उससे अधिक उम्र की लड़की से शादी करना उचित है।<br><strong>निष्कर्ष:</strong><br>इस उम्र में, एक लड़की मां बनने के लिए शारीरिक, जैविक और मनोवैज्ञानिक परिपक्वता के उचित स्तर तक पहुंच जाती है।<br>लोग डर के मारे 18 साल से कम उम्र की लड़कियों से शादी नहीं करते।</p>",
                    options_en: ["<p>Both conclusions 1 and 2 follow.</p>", "<p>Neither conclusions 1 nor 2 follows.</p>", 
                                "<p>Only conclusion 2 follows.</p>", "<p>Only conclusion 1 follows.</p>"],
                    options_hi: ["<p>निष्कर्ष 1 और 2 दोनों अनुसरण करते हैं।</p>", "<p>न तो निष्कर्ष 1 और न ही 2 अनुसरण करता है।</p>",
                                "<p>केवल निष्कर्ष 2 अनुसरण करता है।</p>", "<p>केवल निष्कर्ष 1 अनुसरण करता है।</p>"],
                    solution_en: "<p>11.(d)<br><strong>Statement:</strong><br>It is appropriate to marry a girl who is 18 years of age and above.<br>So, from the above statement we can say that conclusion 1 : &lsquo;At this age, a girl reaches an appropriate level of physical, biological and psychological maturity to become a mother.&rsquo; is logically correct.<br>But Conclusion 2 : &lsquo;People do not marry girls less than 18 years of age because of fear.&rsquo; is not correct.</p>",
                    solution_hi: "<p>11.(d)<br><strong>कथन:</strong><br>18 साल और उससे अधिक उम्र की लड़की से शादी करना उचित है।<br>अतः, उपरोक्त कथन से हम कह सकते हैं कि निष्कर्ष 1: \'इस उम्र में, एक लड़की माँ बनने के लिए शारीरिक, जैविक और मनोवैज्ञानिक परिपक्वता के उचित स्तर तक पहुँच जाती है।\' तार्किक रूप से सही है।<br>लेकिन निष्कर्ष 2: \'लोग डर के कारण 18 साल से कम उम्र की लड़कियों से शादी नहीं करते हैं।\' सही नहीं है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Four girls, Radha, Neena, Mohita and Jyoti, are to be served in a sequence by a service provider. However, there are some limitations as follows: Mohita and Jyoti cannot be served one after the other; Neena cannot be served at the third number in sequence; and Radha and Neena are to be served one after the other. Considering the given information. Which of the following statements is compulsorily true ?</p>",
                    question_hi: "<p>12. एक सेवा प्रदाता द्वारा चार लड़कियों, राधा, नीना, मोहिता और ज्योति को एक क्रम में परोसा जाना है। हालाँकि, कुछ सीमाएँ इस प्रकार हैं: मोहिता और ज्योति को एक के बाद एक नहीं परोसा जा सकता है; नीना को क्रम से तीसरे नंबर पर नहीं परोसा जा सकता है; और एक के बाद एक राधा और नीना की सेवा करनी है। दी गई जानकारी को ध्यान में रखते हुए। निम्नलिखित में से कौन सा कथन अनिवार्य रूप से सत्य है ?</p>",
                    options_en: ["<p>Mohita cannot be served first.</p>", "<p>Mohita will be served first.</p>", 
                                "<p>Radha will be served third.</p>", "<p>Jyoti will be served first.</p>"],
                    options_hi: ["<p>मोहिता को पहले परोसा नहीं जा सकता।</p>", "<p>मोहिता को पहले परोसा जाएगा।</p>",
                                "<p>राधा को तीसरी सेवा दी जाएगी।</p>", "<p>पहले ज्योति परोसी जाएगी।</p>"],
                    solution_en: "<p>12.(c)<br>From the given information it is not clear or definite that Mohina cannot be served first.<br>So, statement 1 is false.<br>But we can not surely tell that Mohina will be served first. So, statement 2 is also false.<br>It is also not definitely true that Jyoti will be served first. So, statement 4 is false.<br>Again, considering all the possibilities of the arrangement it is found that Radha has to be served in the third position. So, statement 3 is true.</p>",
                    solution_hi: "<p>12.(c)<br>दी गई जानकारी से यह स्पष्ट या निश्चित नहीं है कि मोहिना को पहले सर्व नहीं किया जा सकता।<br>अत: कथन 1 असत्य है।<br>लेकिन हम ये जरूर नहीं बता सकते कि मोहिना को पहले सर्व किया जाएगा. अत: कथन 2 भी असत्य है।<br>यह भी निश्चित रूप से सच नहीं है कि पहले ज्योति परोसी जाएगी। अत: कथन 4 असत्य है।<br>पुनः व्यवस्था की समस्त सम्भावनाओं पर विचार करते हुए यह पाया जाता है कि राधा को तृतीय स्थान पर सेवा देनी है। अत: कथन 3 सत्य है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. If &lsquo;P&rsquo; means &lsquo;+&rsquo; , &lsquo;S&rsquo; means &lsquo;-&rsquo;, &lsquo;M&rsquo; means &lsquo;&times;&rsquo; and &lsquo;D&rsquo; means &lsquo;&divide;&rsquo;, then, find the value of :<br>3P2M3S6D2</p>",
                    question_hi: "<p>13. यदि \'P\' का अर्थ \'+\', \'S\' का अर्थ \'-\', \'M\' का अर्थ \'&times;\' और \'D\' का अर्थ \'<math display=\"inline\"><mo>&#247;</mo></math>\' है, तो इसका मान ज्ञात कीजिए:<br>3P2M3S6D2</p>",
                    options_en: ["<p>5</p>", "<p>6</p>", 
                                "<p>4</p>", "<p>0</p>"],
                    options_hi: ["<p>5</p>", "<p>6</p>",
                                "<p>4</p>", "<p>0</p>"],
                    solution_en: "<p>13.(b)<br>&lsquo;P&rsquo; means &lsquo;+&rsquo; , &lsquo;S&rsquo; means &lsquo;-&rsquo;, &lsquo;M &rsquo; means &lsquo;&times;&rsquo; and &lsquo;D&rsquo; means &lsquo;&divide;&rsquo;, then,&nbsp;<br>Then the value of : 3P2M3S6D2 = 3 + 2 &times; 3 - 6 &divide; 2 = 6 ;</p>",
                    solution_hi: "<p>13.(b)<br>\'P\' का अर्थ \'+\', \'S\' का अर्थ \'-\', \'M\' का अर्थ \'&times;\' और \'D\' का अर्थ \'<math display=\"inline\"><mo>&#247;</mo></math>\', तो,<br>तब का मान : 3P2M3S6D2 = 3 + 2 &times; 3 - 6 &divide; 2 = 6;</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. If we interchange + with &times; and number 3 with 2, then which of the following equations will be correct ?</p>",
                    question_hi: "<p>14. यदि हम + को &times; के साथ और संख्या 3 को 2 से बदल दें, तो निम्नलिखित में से कौन सा समीकरण सही होगा ?</p>",
                    options_en: ["<p>4 + 6<math display=\"inline\"><mo>&#247;</mo></math>3 - 3 &times; 2 = 13</p>", "<p>4 + 6<math display=\"inline\"><mo>&#247;</mo></math>3 - 3 &times; 2 = 12</p>", 
                                "<p>4 + 6<math display=\"inline\"><mo>&#247;</mo></math>3 - 3 &times; 2 = 17</p>", "<p>4 + 6<math display=\"inline\"><mo>&#247;</mo></math>3 - 3 &times; 2 = 11</p>"],
                    options_hi: ["<p>4 + 6<math display=\"inline\"><mo>&#247;</mo></math>3 - 3 &times; 2 = 13</p>", "<p>4 + 6<math display=\"inline\"><mo>&#247;</mo></math>3 - 3 &times; 2 = 12</p>",
                                "<p>4 + 6<math display=\"inline\"><mo>&#247;</mo></math>3 - 3 &times; 2 = 17</p>", "<p>4 + 6<math display=\"inline\"><mo>&#247;</mo></math>3 - 3 &times; 2 = 11</p>"],
                    solution_en: "<p>14.(a)<br>If we interchange + with &times; and number 3 with 2, then the equation which will be correct<br>is <math display=\"inline\"><mo>&#8658;</mo></math>4 + 6 &divide; 3 - 3 &times; 2 = 4 &times; 6 &divide; 2 - 2 + 3 = 13 ;</p>",
                    solution_hi: "<p>14.(a)<br>यदि हम + को &times; के साथ और संख्या 3 को 2 से बदल दें, तो वह समीकरण जो सही होगा<br>4 + 6<math display=\"inline\"><mo>&#247;</mo></math>3 - 3 &times; 2 = 4 &times; 6 &divide; 2 - 2 + 3 = 13 है;</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "15. Most of the wrist watches in this shop are expensive. Which of the following conclusions can be drawn from the given statement.",
                    question_hi: "15. इस दुकान की ज्यादातर कलाई घड़ियां महंगी हैं। निम्नलिखित में से कौन सा निष्कर्ष दिए गए कथन से निकाला जा सकता है।",
                    options_en: [" All the ladies wrist watches are expensive.", " None of the wrist watches are cheap.", 
                                " There are cheap wrist watches also in the shop.", " All the wrist watches in shop are expensive."],
                    options_hi: [" सभी महिलाओं की कलाई घड़ियां महंगी हैं।", " कलाई घड़ियों में से कोई भी सस्ता नहीं है।",
                                " दुकान में सस्ते कलाई घड़ियां भी हैं।", " दुकान में सभी कलाई घड़ियां महंगी हैं।"],
                    solution_en: "15.(c)<br />Statement : Most of the wrist watches in this shop are expensive. <br />Which means that some wrist watches might be cheap also.<br />So, the conclusions : None of the wrist watches are cheap., All the wrist watches in the shop are expensive.,  All the ladies wrist watches are expensive do not follow.<br />Then the only conclusion :  “There are cheap wrist watches also in the shop.” can be drawn from the given statement.",
                    solution_hi: "15.(c)<br />कथन: इस दुकान की अधिकांश कलाई घड़ियाँ महंगी हैं।<br />जिसका अर्थ है कि कुछ कलाई घड़ियाँ सस्ती भी हो सकती हैं।<br />तो, निष्कर्ष: कोई भी कलाई घड़ियाँ सस्ती नहीं हैं। दुकान में सभी कलाई घड़ियाँ महंगी हैं। सभी महिलाओं की कलाई घड़ियाँ महंगी हैं, उनका पालन न करें।<br />तब एकमात्र निष्कर्ष: \"दुकान में सस्ती कलाई घड़ियाँ भी हैं।\" दिए गए कथन से निकाला जा सकता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow (s) from the statements.<br><strong>Statements:</strong><br>1. Some students are dancers.<br>2. All dancers are musicians.<br><strong>Conclusions:</strong><br>1. Some students are musicians.<br>2. No dancer is a student.</p>",
                    question_hi: "<p>16. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>कुछ छात्र नर्तक हैं।<br>सभी नर्तक संगीतकार हैं।<br><strong>निष्कर्ष:</strong><br>कुछ छात्र संगीतकार हैं।<br>कोई डांसर स्टूडेंट नहीं है।</p>",
                    options_en: ["<p>Neither conclusion 1 nor 2 follows.</p>", "<p>Only conclusion 1 follows.</p>", 
                                "<p>Only conclusion 2 follows.</p>", "<p>Both conclusions 1 and 2 follow.</p>"],
                    options_hi: ["<p>न तो निष्कर्ष 1 और न ही 2 अनुसरण करता है।</p>", "<p>केवल निष्कर्ष 1 अनुसरण करता है।</p>",
                                "<p>केवल निष्कर्ष 2 अनुसरण करता है।</p>", "<p>निष्कर्ष 1 और 2 दोनों अनुसरण करते हैं।</p>"],
                    solution_en: "<p>16.(b)<br>From the given statements we can draw the following diagram :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744287.png\" alt=\"rId9\" width=\"214\" height=\"83\"><br>So, only conclusion 1 i.e. &lsquo;Some students are dancers.&rsquo; follows.</p>",
                    solution_hi: "<p>16.(b)<br>दिए गए कथनों से हम निम्नलिखित आरेख बना सकते हैं:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744384.png\" alt=\"rId10\" width=\"207\" height=\"86\"><br>अतः, केवल निष्कर्ष 1 अर्थात् \'कुछ विद्यार्थी नर्तक इस प्रकार है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. If A is the father of B, who is the father of C, and D is the only son of C and C&rsquo;s wife F, then how is D related to A ?</p>",
                    question_hi: "<p>17. यदि A, B का पिता है, जो C का पिता है, और D, C और C की पत्नी F का इकलौता पुत्र है, तो D, A से किस प्रकार संबंधित है ?</p>",
                    options_en: ["<p>Great- grandson</p>", "<p>Son</p>", 
                                "<p>Grand-daughter</p>", "<p>Father</p>"],
                    options_hi: ["<p>परपोता</p>", "<p>बेटा</p>",
                                "<p>पोती</p>", "<p>पिता</p>"],
                    solution_en: "<p>17.(a)<br>From the given information we can draw the following diagram :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744529.png\" alt=\"rId11\" width=\"102\" height=\"156\"><br>So, D is the great - grand - son of A.</p>",
                    solution_hi: "<p>17.(a)<br>दी गई जानकारी से हम निम्नलिखित आरेख बना सकते हैं:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744529.png\" alt=\"rId11\" width=\"102\" height=\"156\"><br>तो, D, A का महान - भव्य - पुत्र है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow (s) from the statements.<br><strong>Statements:</strong><br>1. All calendars are mice.<br>2. All mice are dogs.<br><strong>Conclusions:</strong><br>1. All calendars are dogs.<br>2. Some dogs are calendars.</p>",
                    question_hi: "<p>18. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन:</strong><br>1. सभी कैलेंडर चूहे हैं।<br>2. सभी चूहे कुत्ते हैं।<br><strong>निष्कर्ष:</strong><br>1. सभी कैलेंडर कुत्ते हैं।<br>2. कुछ कुत्ते कैलेंडर हैं।</p>",
                    options_en: ["<p>Only conclusion 1 follows.</p>", "<p>Both conclusions 1 and 2 follow.</p>", 
                                "<p>Neither conclusions 1 nor 2 follows.</p>", "<p>Only conclusion 2 follows.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष 1 अनुसरण करता है।</p>", "<p>निष्कर्ष 1 और 2 दोनों अनुसरण करते हैं।</p>",
                                "<p>न तो निष्कर्ष 1 और न ही 2 अनुसरण करता है।</p>", "<p>केवल निष्कर्ष 2 अनुसरण करता है।</p>"],
                    solution_en: "<p>18.(b)<br>From the given statements we can draw the following diagram :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744638.png\" alt=\"rId12\" width=\"142\" height=\"105\"><br>So, Both conclusions 1 and 2 follow.</p>",
                    solution_hi: "<p>18.(b)<br>दिए गए कथनों से हम निम्नलिखित आरेख बना सकते हैं:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744780.png\" alt=\"rId13\" width=\"135\" height=\"100\"><br>अतः निष्कर्ष 1 और 2 दोनों अनुसरण करते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "19. Select the option that is related to the third term in the same way as the second term is related to the first term.<br />Cloth : Shirt : : Wood : ?",
                    question_hi: "19. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br />कपड़ा : कमीज : : लकड़ी : ?",
                    options_en: ["  Ash", " Trouser", 
                                " Furniture", " Tree"],
                    options_hi: [" आशू", " पतलून",
                                " फर्नीचर", " पेड़"],
                    solution_en: "19.(c)<br />Clothes : Shirt.   [Shirts are one type of variety of clothes]<br />Using the same analogy<br />Wood : Furniture.",
                    solution_hi: "19.(c)<br />कपड़े : कमीज [शर्ट एक प्रकार के कपड़े हैं]<br />उसी सादृश्य का उपयोग करना लकड़ी : फर्नीचर।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. R is the father of P and brother of K. whereas K is the daughter of D and sister-in -law of S. If S is the mother of P, then what is the relationship between S and R ?</p>",
                    question_hi: "<p>20. R, P का पिता और K का भाई है। जबकि K, D की पुत्री और S की भाभी है। यदि S, P की माता है, तो S और R के बीच क्या संबंध है ?</p>",
                    options_en: ["<p>Grand- Father and Grand- Daughter</p>", "<p>Wife and Husband</p>", 
                                "<p>Father and Daughter</p>", "<p>Sister and Brother</p>"],
                    options_hi: ["<p>ग्रैंड- पिता और ग्रैंड-बेटी</p>", "<p>पत्नी और पति</p>",
                                "<p>पिता और बेटी</p>", "<p>बहन और भाई</p>"],
                    solution_en: "<p>20.(b)<br>From the given information we can draw the following diagram :<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744886.png\" alt=\"rId14\" width=\"162\" height=\"113\"><br>So, the relationship between S and R is Wife and Husband.</p>",
                    solution_hi: "<p>20.(b)<br>दी गई जानकारी से हम निम्नलिखित आरेख बना सकते हैं:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729918744886.png\" alt=\"rId14\" width=\"162\" height=\"113\"><br>तो, S और R के बीच संबंध पत्नी और पति है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "21. Select the number that can replace the question mark (?) in the following series.<br />14,41,86,?,230,329",
                    question_hi: "21. उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकती है।<br />14,41,86,?,230,329",
                    options_en: [" 501", " 480", 
                                " 149", " 439"],
                    options_hi: [" 501", " 480",
                                " 149", " 439"],
                    solution_en: "21.(c)<br />14,41,86,?,230,329<br />Here the difference between the consecutive numbers is in the following pattern :<br />   41 - 14 = 27 = 3<math display=\"inline\"><mo>×</mo></math>9 ;<br />   86 - 41 = 45 = 5<math display=\"inline\"><mo>×</mo></math>9 ;<br />So, the next difference will be 7<math display=\"inline\"><mo>×</mo></math>9 = 63 ;<br />So the fourth term will be = (86 + 63) = (230 - 81) = 149 ;",
                    solution_hi: "21.(c)<br />14,41,86,?,230,329<br />यहाँ क्रमागत संख्याओं के बीच का अंतर निम्नलिखित पैटर्न में है:<br />   41 - 14 = 27 = 3<math display=\"inline\"><mo>×</mo></math>9;<br />   86 - 41 = 45 = 5<math display=\"inline\"><mo>×</mo></math>9;<br />अत: अगला अंतर 7<math display=\"inline\"><mo>×</mo></math>9 = 63 होगा;<br />तो चौथा पद होगा = (86 + 63) = (230 - 81) = 149;",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Consider the given statement and decide which of the given assumptions is/are implicit in the statement.<br><strong>Statement:</strong><br>&ldquo;In order to eradicate social evils, there is a need to educate people.&rdquo;- A statement made by a Social Activist.<br><strong>Assumptions:</strong><br>1. Education will quickly eliminate all social evils.<br>2. All uneducated people practice social evils.</p>",
                    question_hi: "<p>22. दिए गए कथन पर विचार करें और तय करें कि दिए गए अनुमानों में से कौन सा/से कथन में निहित है/हैं।<br><strong>कथन:</strong><br>\"सामाजिक बुराइयों को मिटाने के लिए लोगों को शिक्षित करने की आवश्यकता है।\" - एक सामाजिक कार्यकर्ता द्वारा दिया गया बयान।<br><strong>धारणाएं:</strong><br>1. शिक्षा सभी सामाजिक बुराइयों को शीघ्र ही समाप्त कर देगी।<br>2. सभी अशिक्षित लोग सामाजिक बुराइयों का अभ्यास करते हैं।</p>",
                    options_en: ["<p>Only assumption 1 is implicit.</p>", "<p>Neither assumption 1 nor 2 are implicit.</p>", 
                                "<p>Either assumption 1 or 2 are implicit.</p>", "<p>Only assumption 2 is implicit.</p>"],
                    options_hi: ["<p>केवल धारणा 1 निहित है।</p>", "<p>न तो धारणा 1 और न ही 2 निहित हैं।</p>",
                                "<p>या तो धारणा 1 या 2 निहित हैं।</p>", "<p>केवल धारणा 2 निहित है।</p>"],
                    solution_en: "<p>22.(b)<br><strong>Statement:</strong><br>&ldquo;In order to eradicate social evils, there is a need to educate people.&rdquo;- A statement made by a Social Activist.<br>So, the above statement does not guarantee any of the assumptions implicitly i.e. Neither assumption 1 nor 2 are implicit.</p>",
                    solution_hi: "<p>22.(b)<br><strong>कथन:</strong><br>\"सामाजिक बुराइयों को मिटाने के लिए लोगों को शिक्षित करने की आवश्यकता है।\" - एक सामाजिक कार्यकर्ता द्वारा दिया गया बयान।<br>इसलिए, उपरोक्त कथन किसी भी अनुमान की परोक्ष रूप से गारंटी नहीं देता है अर्थात न तो धारणा 1 और न ही 2 निहित हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "23. Select the option that is related to the third term in the same way as the second term is related to the first term.<br />Cricket : Umpire : : Volleyball : ?",
                    question_hi: "23. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br />क्रिकेट : अंपायर : : वॉलीबॉल : ?",
                    options_en: [" Player", " Umpire", 
                                " Service", " Referee"],
                    options_hi: ["  खिलाडी", " अंपायर",
                                " सेवा", " रेफरी"],
                    solution_en: "23.(d)<br />Cricket : Umpire  [Umpire’s role is to control athletic events and make sure that everything                  is going according to the laws of Game]<br />Using the same analogy :<br />Volleyball :  Referee;",
                    solution_hi: "23.(d)<br />क्रिकेट : अंपायर [अंपायर की भूमिका एथलेटिक घटनाओं को नियंत्रित करना और यह सुनिश्चित करना है कि सब कुछ खेल के नियमों के अनुसार चल रहा है]<br />समान सादृश्य का उपयोग करना:<br />वॉलीबॉल : रेफरी;",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the option that will fill in the blanks and complete the given series.<br>W, G, U, F, S, E, Q, D, ___, ___.</p>",
                    question_hi: "<p>24.उस विकल्प का चयन करें जो रिक्त स्थानों को भरेगा और दी गई श्रृंखला को पूरा करेगा।<br>W, G, U, F, S, E, Q, D, ___, ___.</p>",
                    options_en: ["<p>N,A</p>", "<p>W,B</p>", 
                                "<p>B,W</p>", "<p>O,C</p>"],
                    options_hi: ["<p>N,A</p>", "<p>W,B</p>",
                                "<p>B,W</p>", "<p>O,C</p>"],
                    solution_en: "<p>24.(d)<br>W, G, U, F, S, E, Q, D, ___, ___.<br>Here in the above series it forms an alternating series of :<br>W, U, S, Q, &hellip;.. &amp; G, F, E, D, &hellip;&hellip;..<br>So, the first letter in question will be (Q +2) = O ;<br>And the second letter will be (D - 1) = C ;<br>i.e. W,G,U,F,S,E,Q,D,<span style=\"text-decoration: underline;\">O,C ;</span></p>",
                    solution_hi: "<p>24.(d)<br>W,G,U,F,S,E,Q,D,___,___.<br>यहाँ उपरोक्त श्रृंखला में यह एक वैकल्पिक श्रृंखला बनाता है:<br>W, U, S, Q, &hellip;.. &amp; G, F, E, D, &hellip;&hellip;..<br>तो, प्रश्न में पहला अक्षर (Q + 2) = O होगा;<br>और दूसरा अक्षर होगा (D - 1) = C ;<br>i.e. W,G,U,F,S,E,Q,D,<span style=\"text-decoration: underline;\">O,C ;</span></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. In a line of students , one student is standing at the 9th place from the left end and 12th place from the right end. How many students are standing in the line ?</p>",
                    question_hi: "<p>25. विद्यार्थियों की एक पंक्ति में, एक विद्यार्थी बायें छोर से नौवें और दायें छोर से बारहवें स्थान पर खड़ा है। कितने छात्र लाइन में खड़े हैं ?</p>",
                    options_en: ["<p>22</p>", "<p>21</p>", 
                                "<p>19</p>", "<p>20</p>"],
                    options_hi: ["<p>22</p>", "<p>21</p>",
                                "<p>19</p>", "<p>20</p>"],
                    solution_en: "<p>25.(d)<br>In a line of students , one student is standing at the 9th place from the left end and 12th place from the right end. <br>So, the number of students standing in the line = (9 + 12 - 1) = 20 students ;</p>",
                    solution_hi: "<p>25.(d)<br>विद्यार्थियों की एक पंक्ति में, एक विद्यार्थी बायें छोर से नौवें और दायें छोर से बारहवें स्थान पर खड़ा है।<br>अतः, पंक्ति में खड़े विद्यार्थियों की संख्या = (9 + 12 - 1) = 20 विद्यार्थी;</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>