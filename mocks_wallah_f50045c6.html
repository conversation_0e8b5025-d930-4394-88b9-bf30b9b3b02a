<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Selling price of an article is Rs. 5175 and discount of 25 percent is offered. What is the marked price of the article?</p>",
                    question_hi: "<p>1. किसी वस्तु का विक्रय मूल्य 5175 रुपए है और उस पर 25 प्रतिशत की छूट दी गई है। वस्तु का अंकित मूल्य कितना है?</p>",
                    options_en: ["<p>Rs. 6900</p>", "<p>Rs. 8500</p>", 
                                "<p>Rs. 6600</p>", "<p>Rs. 7000</p>"],
                    options_hi: ["<p>6900 रुपए</p>", "<p>8500 रुपए</p>",
                                "<p>6600 रुपए</p>", "<p>7000 रुपए</p>"],
                    solution_en: "<p>1.(a)<br>MP of the article = 5175 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>75</mn></mrow></mfrac></math> = ₹6900</p>",
                    solution_hi: "<p>1.(a)<br>वस्तु का अंकित मूल्य = 5175 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>75</mn></mrow></mfrac></math> = ₹6900</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Marked price of a chair is Rs. 2000. After giving two successive discounts of 20 percent and <math display=\"inline\"><mi>x</mi></math> percent, the chair is sold for Rs. 1400. What is the value of x?</p>",
                    question_hi: "<p>2. एक कुर्सी का अंकित मूल्य 2000 रुपए है। 20 प्रतिशत और x प्रतिशत की दो क्रमिक छूट देने के बाद वह कुर्सी 1400 रुपए में बेची जाती है। x का मान कितना है?</p>",
                    options_en: ["<p>25 percent</p>", "<p>10 percent</p>", 
                                "<p>20 percent</p>", "<p>12.5 percent</p>"],
                    options_hi: ["<p>25 प्रतिशत</p>", "<p>10 प्रतिशत</p>",
                                "<p>20 प्रतिशत</p>", "<p>12.5 प्रतिशत</p>"],
                    solution_en: "<p>2.(d)<br>ATQ,<br>2000 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; x = 1400<br>1600<math display=\"inline\"><mi>x</mi></math> = 1400<br>x = <math display=\"inline\"><mfrac><mrow><mn>1400</mn></mrow><mrow><mn>1600</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math><br>Required% = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>8</mn></mfrac></math>= 12.5%</p>",
                    solution_hi: "<p>2.(d)<br>प्रश्न के अनुसार<br>2000 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; x = 1400<br>1600<math display=\"inline\"><mi>x</mi></math> = 1400<br>x = <math display=\"inline\"><mfrac><mrow><mn>1400</mn></mrow><mrow><mn>1600</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math><br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>8</mn></mfrac></math>= 12.5%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Marked price of a chair is Rs. 5000. After giving two successive discounts of 10 percent and <math display=\"inline\"><mi>x</mi></math> percent, the chair is sold for Rs. 3150. What is the value of x?</p>",
                    question_hi: "<p>3. एक कुर्सी का अंकित मूल्य 5000 रुपए है। 10 प्रतिशत और <math display=\"inline\"><mi>x</mi></math> प्रतिशत की दो क्रमागत छूट देने के बाद, कुर्सी 3150 रुपए में बेची जाती है। x का मान क्या है?</p>",
                    options_en: ["<p>40 percent</p>", "<p>20 percent</p>", 
                                "<p>25 percent</p>", "<p>30 percent</p>"],
                    options_hi: ["<p>40 प्रतिशत</p>", "<p>20 प्रतिशत</p>",
                                "<p>25 प्रतिशत</p>", "<p>30 प्रतिशत</p>"],
                    solution_en: "<p>3.(d)<br>ATQ,<br>5000 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; x = 3150<br>4500<math display=\"inline\"><mi>x</mi></math> = 3150<br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3150</mn><mn>4500</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math>&nbsp;<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>-</mo><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math>&times; 100 = 30%</p>",
                    solution_hi: "<p>3.(d)<br>प्रश्न के अनुसार,<br>5000 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; x = 3150<br>4500<math display=\"inline\"><mi>x</mi></math> = 3150<br><math display=\"inline\"><mi>x</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3150</mn><mn>4500</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>10</mn></mfrac></math>&nbsp;<br>आवश्यक % =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>-</mo><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math>&times; 100 = 30%<br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Marked price of an article is Rs. 28500 and its selling price is Rs. 15675. What is the discount percentage?</p>",
                    question_hi: "<p>4. एक वस्तु का अंकित मूल्य 28500 रुपए है और इसका विक्रय मूल्य 15675 रुपए है। छूट प्रतिशत कितना है?</p>",
                    options_en: ["<p>48 percent</p>", "<p>52 percent</p>", 
                                "<p>45 percent</p>", "<p>42 percent</p>"],
                    options_hi: ["<p>48 प्रतिशत</p>", "<p>52 प्रतिशत</p>",
                                "<p>45 प्रतिशत</p>", "<p>42 प्रतिशत</p>"],
                    solution_en: "<p>4.(c)<br>MP : SP = 28500 : 15675 = 380 : 209<br>Discount% = <math display=\"inline\"><mfrac><mrow><mn>380</mn><mo>-</mo><mn>209</mn></mrow><mrow><mn>380</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>171</mn><mn>380</mn></mfrac></math> &times; 100 = 45%</p>",
                    solution_hi: "<p>4.(c)<br>अंकित मूल्य : विक्रय मूल्य = 28500 : 15675 = 380 : 209<br>छूट % = <math display=\"inline\"><mfrac><mrow><mn>380</mn><mo>-</mo><mn>209</mn></mrow><mrow><mn>380</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>171</mn><mn>380</mn></mfrac></math> &times; 100 = 45%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. Marked price of an article is Rs. 11800 and its selling price is Rs. 7788. What is the discount percentage ?</p>",
                    question_hi: "<p>5. एक वस्तु का अंकित मूल्य 11800 रुपए और इसका विक्रय मूल्य 7788 रुपए है। वस्तु पर छूट प्रतिशत कितना है ?</p>",
                    options_en: ["<p>30 percent</p>", "<p>34 percent</p>", 
                                "<p>32 percent</p>", "<p>35 percent</p>"],
                    options_hi: ["<p>30 प्रतिशत</p>", "<p>34 प्रतिशत</p>",
                                "<p>32 प्रतिशत</p>", "<p>35 प्रतिशत</p>"],
                    solution_en: "<p>5.(b)<br>MP : SP = 11800 : 7788 = 50 : 33<br>Required discount% = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>-</mo><mn>33</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 100 = 34%</p>",
                    solution_hi: "<p>5.(b)<br>क्र∘ मू∘ : वि∘ मू∘ = 11800 : 7788 = 50 : 33 <br>आवश्यक छूट% = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mo>-</mo><mn>33</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math>&times; 100 = 34%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The selling price of an article is Rs. 1690. If discount of 35 percent is given, then what will be the marked price of the article?</p>",
                    question_hi: "<p>6. एक वस्तु का विक्रय मूल्य 1690 रुपए है। यदि 35 प्रतिशत की छूट दी जाती है, तो वस्तु का अंकित मूल्य कितना था?</p>",
                    options_en: ["<p>2600</p>", "<p>2500</p>", 
                                "<p>2800</p>", "<p>2700</p>"],
                    options_hi: ["<p>2600</p>", "<p>2500</p>",
                                "<p>2800</p>", "<p>2700</p>"],
                    solution_en: "<p>6.(a)<br>MP &times; (100 - 35)% = 1690<br>MP &times; 65% = 1690<br>MP = 1690 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>65</mn></mrow></mfrac></math> = ₹2600</p>",
                    solution_hi: "<p>6.(a)<br>अंकित मूल्य &times; (100 - 35)% = 1690<br>अंकित मूल्य &times; 65% = 1690<br>अंकित मूल्य = 1690 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>65</mn></mrow></mfrac></math> = ₹2600</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The marked price and selling price of an item are Rs 6600 and Rs 4752 respectively. What is the discount percentage ?</p>",
                    question_hi: "<p>7. एक वस्तु का अंकित मूल्य और विक्रय मूल्य क्रमशः 6600 रुपए और 4752 रुपए है। छूट प्रतिशत कितना है ?</p>",
                    options_en: ["<p>25 Percent</p>", "<p>34 Percent</p>", 
                                "<p>32 Percent</p>", "<p>28 Percent</p>"],
                    options_hi: ["<p>25 प्रतिशत</p>", "<p>34 प्रतिशत</p>",
                                "<p>32 प्रतिशत</p>", "<p>28 प्रतिशत</p>"],
                    solution_en: "<p>7.(d) Marked price = ₹ 6600 and selling price = ₹ 4752<br>Discount % = <math display=\"inline\"><mfrac><mrow><mn>6600</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4752</mn></mrow><mrow><mn>6600</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>1848</mn></mrow><mrow><mn>66</mn></mrow></mfrac></math> = 28%</p>",
                    solution_hi: "<p>7.(d) अंकित मूल्य = ₹ 6600 और विक्रय मूल्य = ₹ 4752<br>छूट % = <math display=\"inline\"><mfrac><mrow><mn>6600</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4752</mn></mrow><mrow><mn>6600</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>1848</mn></mrow><mrow><mn>66</mn></mrow></mfrac></math> = 28%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. An article is sold for ₹ 204 after two successive discounts of 40% and 50%. What is the marked price of the article?</p>",
                    question_hi: "<p>8. एक वस्तु 40% और 50% की दो क्रमिक छूट के बाद ₹ 204 में बेची जाती है। वस्तु का अंकित मूल्य कितना है?</p>",
                    options_en: ["<p>₹ 540</p>", "<p>₹ 720</p>", 
                                "<p>₹ 840</p>", "<p>₹ 680</p>"],
                    options_hi: ["<p>₹ 540</p>", "<p>₹ 720</p>",
                                "<p>₹ 840</p>", "<p>₹ 680</p>"],
                    solution_en: "<p>8.(d)<br>ATQ,<br>MP &times; <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac></math>= 204<br>MP &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 204<br>MP = 204 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = ₹680</p>",
                    solution_hi: "<p>8.(d)<br>प्रश्न के अनुसार,<br>अंकित मूल्य &times; <math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>50</mn><mn>100</mn></mfrac></math> = 204<br>अंकित मूल्य &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 204<br>अंकित मूल्य = 204 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = ₹680</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. The marked price of an article is Rs. 640. If discount of 37.5 percent is given, then what will be the selling price of the article?</p>",
                    question_hi: "<p>9. एक वस्तु का अंकित मूल्य 640 रुपए है। यदि उस वस्तु पर 37.5 प्रतिशत की छूट दी जाती है, तो वस्तु का विक्रय मूल्य कितना होगा ?</p>",
                    options_en: ["<p>Rs. 600</p>", "<p>Rs. 450</p>", 
                                "<p>Rs. 400</p>", "<p>Rs. 550</p>"],
                    options_hi: ["<p>600 रुपए</p>", "<p>450 रुपए</p>",
                                "<p>400 रुपए</p>", "<p>550 रुपए</p>"],
                    solution_en: "<p>9.(c)<br>37.5% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math><br>SP = 640 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = ₹400</p>",
                    solution_hi: "<p>9.(c)<br>37.5% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math><br>विक्रय मूल्य = 640 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = ₹400</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A toy originally costs $25, but it&rsquo;s on sale with a 30% discount. What is the sale price ?</p>",
                    question_hi: "<p>10. एक खिलौने का वास्तविक मूल्य $25 है, लेकिन यह 30% की छूट के साथ सेल में बेचा जाता है। खिलौने का विक्रय मूल्य कितना है?</p>",
                    options_en: ["<p>$14.6</p>", "<p>$17.5</p>", 
                                "<p>$19.3</p>", "<p>$13.8</p>"],
                    options_hi: ["<p>$14.6</p>", "<p>$17.5</p>",
                                "<p>$19.3</p>", "<p>$13.8</p>"],
                    solution_en: "<p>10.(b)<br>Selling price = 25 &times; <math display=\"inline\"><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = $17.5</p>",
                    solution_hi: "<p>10.(b)<br>बिक्री मूल्य = 25 &times; <math display=\"inline\"><mfrac><mrow><mn>70</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = $17.5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If Marked price of an article is Rs. 4500 and selling price is Rs. 3900. What is the discount?</p>",
                    question_hi: "<p>11. यदि किसी वस्तु का अंकित मूल्य 4500 रुपए और विक्रय मूल्य 3900 रुपए है, तो उस वस्तु पर छूट कितनी है?</p>",
                    options_en: ["<p>500</p>", "<p>600</p>", 
                                "<p>700</p>", "<p>850</p>"],
                    options_hi: ["<p>500</p>", "<p>600</p>",
                                "<p>700</p>", "<p>850</p>"],
                    solution_en: "<p>11.(b)<br>Discount = MP - SP = 4500 - 3900 = ₹600</p>",
                    solution_hi: "<p>11.(b)<br>छूट = अंकित मूल्य - विक्रय मूल्य = 4500 - 3900 = ₹600</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. After giving 50 percent discount on an article there is still a profit of 50 percent. What will be the profit percentage when no discount is given?</p>",
                    question_hi: "<p>12. किसी वस्तु पर 50 प्रतिशत की छूट देने के बाद भी 50 प्रतिशत का लाभ होता है। वस्तु पर कोई भी छूट न देने पर लाभ प्रतिशत कितना होगा?</p>",
                    options_en: ["<p>300 percent</p>", "<p>100 percent</p>", 
                                "<p>150 percent</p>", "<p>200 percent</p>"],
                    options_hi: ["<p>300 प्रतिशत</p>", "<p>100 प्रतिशत</p>",
                                "<p>150 प्रतिशत</p>", "<p>200 प्रतिशत</p>"],
                    solution_en: "<p>12.(d)<br><math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>)</mo><mo>%</mo></mrow><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi><mo>)</mo><mo>%</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>50</mn></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>1</mn></mfrac></math><br>If discount is not given, then MP = SP<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>1</mn></mfrac></math><br>Required profit % = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>-</mo><mn>1</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> &times; 100 = 200%</p>",
                    solution_hi: "<p>12.(d)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow></mfrac></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mi>&#2354;&#2366;&#2349;</mi><mo>&#160;</mo><mo>)</mo><mo>%</mo></mrow><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mi>&#2331;&#2370;&#2335;</mi><mo>&#160;</mo><mo>)</mo><mo>%</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mn>50</mn></mfrac><mo>=</mo><mfrac><mn>3</mn><mn>1</mn></mfrac></math><br>यदि छूट नहीं दी जाती है, तो अंकित मूल्य = विक्रय मूल्य<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mo>&#160;</mo><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>1</mn></mfrac></math><br>आवश्यक लाभ % = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>-</mo><mn>1</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> &times; 100 = 200%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. After giving 10 percent discount on an article there is still a profit of 10 percent. What will be the profit percentage when no discount is given?</p>",
                    question_hi: "<p>13. किसी वस्तु पर 10 प्रतिशत की छूट प्रदान करने के बाद भी 10 प्रतिशत का लाभ होता है। यदि कोई छूट न दी जाए, तो लाभ प्रतिशत कितना होगा?</p>",
                    options_en: ["<p>35 percent</p>", "<p>33.33 percent</p>", 
                                "<p>22.22 percent</p>", "<p>66.67 percent</p>"],
                    options_hi: ["<p>35 प्रतिशत</p>", "<p>33.33 प्रतिशत</p>",
                                "<p>22.22 प्रतिशत</p>", "<p>66.67 प्रतिशत</p>"],
                    solution_en: "<p>13.(c)<br>As we know, <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mi>M</mi><mi>P</mi><mi>&#160;</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>%</mo><mo>)</mo></mrow><mrow><mo>&#160;</mo><mo>(</mo><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>D</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi><mo>%</mo><mo>)</mo><mo>&#160;</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>90</mn></mfrac><mo>=</mo><mfrac><mn>11</mn><mn>9</mn></mfrac></math><br>If there is no discount given on the article then, MP = SP<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac><mo>=</mo><mfrac><mn>11</mn><mn>9</mn></mfrac></math><br>Required profit% = <math display=\"inline\"><mfrac><mrow><mn>11</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>9</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>9</mn></mfrac></math> &times; 100 = 22.22%</p>",
                    solution_hi: "<p>13.(c)<br>हम जानते हैं कि, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>+</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi><mo>&#160;</mo><mo>%</mo><mo>)</mo></mrow><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mi>&#2331;&#2370;&#2335;</mi><mo>%</mo><mo>)</mo></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>90</mn></mfrac><mo>=</mo><mfrac><mn>11</mn><mn>9</mn></mfrac></math><br>यदि वस्तु पर कोई छूट नहीं दी गई है, तो, अंकित मूल्य = विक्रय मूल्य&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&#160;</mo></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>9</mn></mfrac></math><br>आवश्यक लाभ% = <math display=\"inline\"><mfrac><mrow><mn>11</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>9</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>9</mn></mfrac></math>&times; 100 = 22.22%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Marked price and selling price of an article are Rs. 7600 and Rs. 6688 respectively. What is the discount percentage?</p>",
                    question_hi: "<p>14. एक वस्तु का अंकित मूल्य और विक्रय मूल्य क्रमशः 7600 रुपए और 6688 रुपए है। छूट प्रतिशत कितना है?</p>",
                    options_en: ["<p>8 percent</p>", "<p>12 percent</p>", 
                                "<p>10 percent</p>", "<p>16 percent</p>"],
                    options_hi: ["<p>8 प्रतिशत</p>", "<p>12 प्रतिशत</p>",
                                "<p>10 प्रतिशत</p>", "<p>16 प्रतिशत</p>"],
                    solution_en: "<p>14.(b)<br>MP : SP = 7600 : 6688 = 25 : 22<br>Discount % = <math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>22</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>25</mn></mfrac></math> &times; 100 = 12%</p>",
                    solution_hi: "<p>14.(b)<br>अंकित मूल्य : विक्रय मूल्य = 7600 : 6688 = 25 : 22<br>छूट % = <math display=\"inline\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>22</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; 100 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>25</mn></mfrac></math> &times; 100 = 12%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. After a discount of 28 percent an article is sold for Rs.1008. What is the marked price of the article?</p>",
                    question_hi: "<p>15. 28 प्रतिशत की छूट के बाद एक वस्तु 1008 रुपए में बेची जाती है। वस्तु का अंकित मूल्य कितना है?</p>",
                    options_en: ["<p>Rs.1450</p>", "<p>Rs.1400</p>", 
                                "<p>Rs.1305</p>", "<p>Rs.1350</p>"],
                    options_hi: ["<p>1450 रुपए</p>", "<p>1400 रुपए</p>",
                                "<p>1305 रुपए</p>", "<p>1350 रुपए</p>"],
                    solution_en: "<p>15.(b)<br>MP = 1008 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> = ₹1400</p>",
                    solution_hi: "<p>15.(b)<br>अंकित मूल्य = 1008 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>72</mn></mrow></mfrac></math> = ₹1400</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>