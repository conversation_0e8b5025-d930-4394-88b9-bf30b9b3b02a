<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The value of &nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>.</mo><mn>4</mn><menclose notation=\"top\"><mn>6</mn></menclose><mo>+</mo><mo>.</mo><mn>7</mn><menclose notation=\"top\"><mn>23</mn></menclose><mo>-</mo><mo>.</mo><mn>3</mn><menclose notation=\"top\"><mn>9</mn></menclose><mo>&#215;</mo><mo>.</mo><mover><mn>7</mn><mo>&#9180;</mo></mover></math>&nbsp; &nbsp;is:</p>\n<p>SSC CGL Tier-2, 29/01/2022</p>",
                    question_hi: "<p>1 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>.</mo><mn>4</mn><menclose notation=\"top\"><mn>6</mn></menclose><mo>+</mo><mo>.</mo><mn>7</mn><menclose notation=\"top\"><mn>23</mn></menclose><mo>-</mo><mo>.</mo><mn>3</mn><menclose notation=\"top\"><mn>9</mn></menclose><mo>&#215;</mo><mo>.</mo><menclose notation=\"top\"><mn>7</mn></menclose></math><span style=\"font-family: Palanquin Dark;\">का मान है:</span></p>\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    options_en: ["<p>0.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mo>.</mo><mn>57</mn></menclose></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mo>.</mo><mn>77</mn></menclose></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mo>.</mo><mn>87</mn></menclose></math></p>", "<p>0<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><menclose notation=\"top\"><mo>.</mo><mn>97</mn></menclose></math></p>"],
                    options_hi: ["<p>0<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>.</mo><menclose notation=\"top\"><mn>57</mn></menclose></math></p>", "<p>0<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>.</mo><menclose notation=\"top\"><mn>77</mn></menclose></math></p>",
                                "<p>0<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>.</mo><menclose notation=\"top\"><mn>87</mn></menclose></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>.</mo><menclose notation=\"top\"><mn>97</mn></menclose></math></p>"],
                    solution_en: "<p>&nbsp; 1. (c)&nbsp;</p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>.</mo><mn>4</mn><menclose notation=\"top\"><mn>6</mn><mo>&#160;</mo></menclose><mo>+</mo><mo>&#160;</mo><mo>.</mo><mn>7</mn><menclose notation=\"top\"><mn>23</mn></menclose><mo>-</mo><mo>&#160;</mo><mo>.</mo><mn>3</mn><menclose notation=\"top\"><mn>9</mn></menclose><mo>&#215;</mo><mn>0</mn><mo>.</mo><menclose notation=\"top\"><mn>7</mn></menclose><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>46</mn><mo>-</mo><mn>4</mn></mrow><mn>90</mn></mfrac><mo>+</mo><mfrac><mrow><mn>723</mn><mo>-</mo><mn>7</mn></mrow><mn>990</mn></mfrac><mo>-</mo><mfrac><mrow><mn>39</mn><mo>-</mo><mn>3</mn></mrow><mn>90</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>W</mi><mi>e</mi><mo>&#160;</mo><mi>b</mi><mi>a</mi><mi>l</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&#160;</mo><mi>f</mi><mi>r</mi><mi>a</mi><mi>c</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>&#160;</mo><mi>t</mi><mi>o</mi><mo>&#160;</mo><mi>g</mi><mi>e</mi><mi>t</mi><mo>&#160;</mo><mn>990</mn><mo>&#160;</mo><mi>i</mi><mi>n</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&#160;</mo><mi>d</mi><mi>e</mi><mi>n</mi><mi>o</mi><mi>m</mi><mi>i</mi><mi>n</mi><mi>a</mi><mi>t</mi><mi>o</mi><mi>r</mi><mo>.</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>42</mn><mo>&#215;</mo><mn>11</mn></mrow><mrow><mn>90</mn><mo>&#215;</mo><mn>11</mn></mrow></mfrac><mo>+</mo><mfrac><mn>716</mn><mn>990</mn></mfrac><mo>-</mo><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>11</mn></mrow><mrow><mn>90</mn><mo>&#215;</mo><mn>11</mn></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>462</mn><mo>+</mo><mn>716</mn><mo>-</mo><mn>308</mn></mrow><mn>990</mn></mfrac><mo>=</mo><mfrac><mn>870</mn><mn>990</mn></mfrac><mo>=</mo><mfrac><mn>87</mn><mn>99</mn></mfrac><mo>=</mo><mo>.</mo><mover><mn>87</mn><mo>&#9180;</mo></mover><mspace linebreak=\"newline\"></mspace></math></p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>1. (c)</p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>0</mn><mo>.</mo><mn>4</mn><menclose notation=\"top\"><mn>6</mn></menclose><mo>+</mo><mn>0</mn><mo>.</mo><mn>7</mn><menclose notation=\"top\"><mn>23</mn></menclose><mo>-</mo><mn>0</mn><mo>.</mo><mn>3</mn><menclose notation=\"top\"><mn>9</mn></menclose><mo>&#215;</mo><mn>0</mn><mo>.</mo><mn>7</mn><mspace linebreak=\"newline\"/><mfrac><mrow><mn>46</mn><mo>-</mo><mn>4</mn></mrow><mn>90</mn></mfrac><mo>+</mo><mfrac><mrow><mn>723</mn><mo>-</mo><mn>7</mn></mrow><mn>990</mn></mfrac><mo>-</mo><mfrac><mrow><mn>39</mn><mo>-</mo><mn>3</mn></mrow><mn>90</mn></mfrac><mo>&#215;</mo><mfrac><mn>7</mn><mn>9</mn></mfrac></math></p>\n<p><span style=\"font-family: Palanquin Dark;\">हम भिन्न के &lsquo;हर&rsquo; में 990 प्राप्त करने के लिए संतुलित करते हैं।</span></p>\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>&#215;</mo><mn>11</mn></mrow><mrow><mn>90</mn><mo>&#215;</mo><mn>11</mn></mrow></mfrac><mo>+</mo><mfrac><mn>716</mn><mn>990</mn></mfrac><mo>-</mo><mfrac><mrow><mn>4</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>11</mn></mrow><mrow><mn>90</mn><mo>&#215;</mo><mn>11</mn></mrow></mfrac></math></span></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>462</mn><mo>+</mo><mn>716</mn><mo>-</mo><mn>308</mn></mrow><mn>990</mn></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>870</mn><mn>990</mn></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>87</mn><mn>99</mn></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>.</mo><menclose notation=\"top\"><mn>87</mn></menclose></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Palanquin Dark;\">If 847 &times; 385 &times; 675 &times; 3025 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mi>a</mi></msup><mo>&#215;</mo><msup><mn>5</mn><mi>b</mi></msup><mo>&#215;</mo><msup><mn>7</mn><mi>c</mi></msup><mo>&#215;</mo><msup><mn>11</mn><mi>d</mi></msup></math> </span><span style=\"font-family: Palanquin Dark;\">, then the value of ab - cd is:</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    question_hi: "<p>2.<span style=\"font-family: Palanquin Dark;\"> यदि <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>847</mn><mo>&#215;</mo><mn>385</mn><mo>&#215;</mo><mn>675</mn><mo>&#215;</mo><mn>3025</mn><mo>=</mo><msup><mn>3</mn><mi>a</mi></msup><mo>&#215;</mo><msup><mn>5</mn><mi>b</mi></msup><mo>&#215;</mo><msup><mn>7</mn><mi>c</mi></msup><mo>&#215;</mo><msup><mn>11</mn><mi>d</mi></msup><mo>,</mo></math></span><span style=\"font-family: Palanquin Dark;\">तो ab-cd का मान है:</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    options_en: ["<p>5</p>", "<p>1</p>", 
                                "<p>4</p>", "<p>7</p>"],
                    options_hi: ["<p>5</p>", "<p>1</p>",
                                "<p>4</p>", "<p>7</p>"],
                    solution_en: "<p>2. (a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>847</mn><mo>=</mo><msup><mn>7</mn><mn>1</mn></msup><mo>&#215;</mo><msup><mn>11</mn><mn>2</mn></msup><mo>,</mo><mspace linebreak=\"newline\"></mspace><mn>385</mn><mo>=</mo><mn>5</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>11</mn><mspace linebreak=\"newline\"></mspace><mn>675</mn><mo>=</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mn>3025</mn><mo>=</mo><msup><mn>5</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>11</mn><mn>2</mn></msup><mspace linebreak=\"newline\"></mspace><mn>847</mn><mo>&#215;</mo><mn>385</mn><mo>&#215;</mo><mn>675</mn><mo>&#215;</mo><mn>3025</mn><mo>=</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>5</mn></msup><mo>&#215;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>11</mn><mn>5</mn></msup><mspace linebreak=\"newline\"></mspace><msup><mn>3</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>5</mn></msup><mo>&#215;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>11</mn><mn>5</mn></msup><mo>=</mo><msup><mn>3</mn><mi>a</mi></msup><mo>&#215;</mo><msup><mn>5</mn><mi>b</mi></msup><mo>&#215;</mo><msup><mn>7</mn><mi>c</mi></msup><mo>&#215;</mo><msup><mn>11</mn><mi>d</mi></msup><mspace linebreak=\"newline\"></mspace><mi>O</mi><mi>n</mi><mi>c</mi><mi>o</mi><mi>m</mi><mi>p</mi><mi>a</mi><mi>r</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>,</mo><mi>w</mi><mi>e</mi><mi>g</mi><mi>e</mi><mi>t</mi><mo>&#160;</mo><mi>a</mi><mo>=</mo><mn>3</mn><mo>,</mo><mi>b</mi><mo>=</mo><mn>5</mn><mo>,</mo><mi>c</mi><mo>=</mo><mn>2</mn><mo>,</mo><mi>d</mi><mo>=</mo><mn>5</mn><mspace linebreak=\"newline\"></mspace><mi>a</mi><mi>b</mi><mo>-</mo><mi>c</mi><mi>d</mi><mo>=</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>5</mn></math></p>",
                    solution_hi: "<p>2. (a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>847</mn><mo>=</mo><msup><mn>7</mn><mn>1</mn></msup><mo>&#215;</mo><msup><mn>11</mn><mn>2</mn></msup><mo>,</mo><mspace linebreak=\"newline\"/><mn>385</mn><mo>=</mo><mn>5</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>11</mn><mspace linebreak=\"newline\"/><mn>675</mn><mo>=</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>2</mn></msup><mn>3025</mn><mo>=</mo><msup><mn>5</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>11</mn><mn>2</mn></msup><mspace linebreak=\"newline\"/><mn>847</mn><mo>&#215;</mo><mn>385</mn><mo>&#215;</mo><mn>675</mn><mo>&#215;</mo><mn>3025</mn><mo>=</mo><msup><mn>3</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>5</mn></msup><mo>&#215;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>11</mn><mn>5</mn></msup><mspace linebreak=\"newline\"/><msup><mn>3</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>5</mn></msup><mo>&#215;</mo><msup><mn>7</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>11</mn><mn>5</mn></msup><mo>=</mo><msup><mn>3</mn><mi>a</mi></msup><mo>&#215;</mo><msup><mn>5</mn><mi>b</mi></msup><mo>&#215;</mo><msup><mn>7</mn><mi>c</mi></msup><mo>&#215;</mo><msup><mn>11</mn><mi>d</mi></msup><mspace linebreak=\"newline\"/><mi>O</mi><mi>n</mi><mi>c</mi><mi>o</mi><mi>m</mi><mi>p</mi><mi>a</mi><mi>r</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>,</mo><mi>w</mi><mi>e</mi><mi>g</mi><mi>e</mi><mi>t</mi><mi>a</mi><mo>=</mo><mn>3</mn><mo>,</mo><mi>b</mi><mo>=</mo><mn>5</mn><mo>,</mo><mi>c</mi><mo>=</mo><mn>2</mn><mo>,</mo><mi>d</mi><mo>=</mo><mn>5</mn><mi>a</mi><mi>b</mi><mo>-</mo><mi>c</mi><mi>d</mi><mo>=</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>5</mn></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Times New Roman;\">The sum of and difference between the LCM and HCF of two numbers are 512 and 496, respectively. If one number is 72, then the other number is</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    question_hi: "<p>3. <span style=\"font-family: Palanquin Dark;\">दो संख्याओं के LCM और HCF के बीच का योग और अंतर क्रमशः 512 और 496 है। यदि एक संख्या 72 है, तो दूसरी संख्या है:</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    options_en: ["<p>40</p>", "<p>64</p>", 
                                "<p>56</p>", "<p>80</p>"],
                    options_hi: ["<p>40</p>", "<p>64</p>",
                                "<p>56</p>", "<p>80</p>"],
                    solution_en: "<p>3. (c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>L</mi><mi>C</mi><mi>M</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><mn>512</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>496</mn><mo>)</mo></mrow><mn>2</mn></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>1008</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mo>&#160;</mo><mn>504</mn><mspace linebreak=\"newline\"></mspace><mo>&#160;</mo><mi>H</mi><mi>C</mi><mi>F</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><mn>512</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>496</mn><mo>)</mo><mo>&#160;</mo></mrow><mn>2</mn></mfrac><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>16</mn><mo>/</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>.</mo><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mi>P</mi><mi>r</mi><mi>o</mi><mi>d</mi><mi>u</mi><mi>c</mi><mi>t</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>t</mi><mi>w</mi><mi>o</mi><mo>&#160;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>s</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mi>L</mi><mi>C</mi><mi>M</mi><mo>&#215;</mo><mi>H</mi><mi>C</mi><mi>F</mi><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mn>72</mn><mo>&#215;</mo><mi>y</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>&#215;</mo><mn>504</mn><mo>&#160;</mo><mi>y</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>8</mn><mo>&#215;</mo><mn>504</mn><mo>)</mo><mo>/</mo><mn>72</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>56</mn></math></p>",
                    solution_hi: "<p>3. (c)</p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>L</mi><mi>C</mi><mi>M</mi><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mo>(</mo><mn>512</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>496</mn><mo>)</mo></mrow><mn>2</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>1008</mn><mo>/</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>504</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mi>H</mi><mi>C</mi><mi>F</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>512</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>496</mn><mo>)</mo><mo>/</mo><mn>2</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>&#160;</mo><mn>16</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>.</mo><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>P</mi><mi>r</mi><mi>o</mi><mi>d</mi><mi>u</mi><mi>c</mi><mi>t</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>t</mi><mi>w</mi><mi>o</mi><mo>&#160;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>s</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mi>L</mi><mi>C</mi><mi>M</mi><mo>&#215;</mo><mi>H</mi><mi>C</mi><mi>F</mi><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>72</mn><mo>&#215;</mo><mi>y</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn><mo>&#215;</mo><mn>504</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mi>y</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>8</mn><mo>&#215;</mo><mn>504</mn><mo>)</mo><mo>/</mo><mn>72</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>56</mn></math></p>\r\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Times New Roman;\">The value of</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mn>4</mn><mo>-</mo><mo>&#8730;</mo><mn>15</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mo>&#8730;</mo><mn>15</mn><mo>-</mo><mo>&#8730;</mo><mn>14</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mo>(</mo><mo>&#8730;</mo><mn>14</mn><mo>-</mo><mo>&#8730;</mo><mn>13</mn><mo>)</mo></mrow></mfrac><mo>-</mo><mo>&#160;</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mrow><mo>(</mo><mo>&#8730;</mo><mn>13</mn><mo>-</mo><mo>&#8730;</mo><mn>12</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mo>&#8730;</mo><mn>12</mn><mo>-</mo><mo>&#8730;</mo><mn>11</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mo>&#8730;</mo><mn>11</mn><mo>-</mo><mo>&#8730;</mo><mn>10</mn><mo>)</mo><mo>&#160;</mo></mrow></mfrac><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mo>&#8730;</mo><mn>10</mn><mo>-</mo><mn>3</mn><mo>)</mo><mo>&#160;</mo></mrow></mfrac><mo>-</mo><mo>&#160;</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mrow><mo>(</mo><mn>3</mn><mo>-</mo><mo>&#8730;</mo><mn>8</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    question_hi: "<p>4.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mn>4</mn><mo>-</mo><mo>&#8730;</mo><mn>15</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mo>&#8730;</mo><mn>15</mn><mo>-</mo><mo>&#8730;</mo><mn>14</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mrow><mo>(</mo><mo>&#8730;</mo><mn>14</mn><mo>-</mo><mo>&#8730;</mo><mn>13</mn><mo>)</mo></mrow></mfrac><mo>-</mo><mo>&#160;</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mrow><mo>(</mo><mo>&#8730;</mo><mn>13</mn><mo>-</mo><mo>&#8730;</mo><mn>12</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mo>&#8730;</mo><mn>12</mn><mo>-</mo><mo>&#8730;</mo><mn>11</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mo>&#8730;</mo><mn>11</mn><mo>-</mo><mo>&#8730;</mo><mn>10</mn><mo>)</mo><mo>&#160;</mo></mrow></mfrac><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mo>&#8730;</mo><mn>10</mn><mo>-</mo><mn>3</mn><mo>)</mo><mo>&#160;</mo></mrow></mfrac><mo>-</mo><mo>&#160;</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mrow><mo>(</mo><mn>3</mn><mo>-</mo><mo>&#8730;</mo><mn>8</mn><mo>)</mo></mrow></mfrac></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Palanquin Dark;\"> का मान है:</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    options_en: ["<p>4 - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>2 + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", 
                                "<p>4 + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>2 - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    options_hi: ["<p>4 - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>2 + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>",
                                "<p>4 + 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>2 - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    solution_en: "<p>4. (a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mo>(</mo><mn>4</mn><mo>-</mo><mo>&#8730;</mo><mn>15</mn><mo>)</mo><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>&#160;</mo><mo>(</mo><mo>(</mo><mn>4</mn><mo>+</mo><mo>&#8730;</mo><mn>15</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>4</mn><mo>-</mo><mo>&#8730;</mo><mn>15</mn><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>4</mn><mo>+</mo><mo>&#8730;</mo><mn>15</mn><mo>)</mo></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>15</mn><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>i</mi><mi>m</mi><mi>i</mi><mi>l</mi><mi>a</mi><mi>r</mi><mi>l</mi><mi>y</mi><mo>,</mo><mo>&#160;</mo><mi>w</mi><mi>e</mi><mo>&#160;</mo><mi>g</mi><mi>e</mi><mi>t</mi><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>n</mi><mi>j</mi><mi>u</mi><mi>g</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>a</mi><mi>l</mi><mi>l</mi><mo>&#160;</mo><mi>v</mi><mi>a</mi><mi>l</mi><mi>u</mi><mi>e</mi><mi>s</mi><mo>,</mo><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mn>4</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>15</mn><mo>&#160;</mo><mo>-</mo><mo>&#8730;</mo><mn>15</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>14</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>14</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>13</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mo>(</mo><mn>13</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>12</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>12</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>11</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>11</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>10</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>10</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>8</mn><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>8</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#8730;</mo><mn>2</mn></math></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>4. (a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mo>(</mo><mn>4</mn><mo>-</mo><mo>&#8730;</mo><mn>15</mn><mo>)</mo><mo>&#160;</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>&#160;</mo><mo>&#160;</mo><mo>(</mo><mn>4</mn><mo>+</mo><mo>&#8730;</mo><mn>15</mn><mo>)</mo><mo>/</mo></mrow><mrow><mo>(</mo><mn>4</mn><mo>-</mo><mo>&#8730;</mo><mn>15</mn><mo>)</mo><mo>&#160;</mo><mo>(</mo><mn>4</mn><mo>+</mo><mo>&#8730;</mo><mn>15</mn><mo>)</mo></mrow></mfrac><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>15</mn><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mi>S</mi><mi>i</mi><mi>m</mi><mi>i</mi><mi>l</mi><mi>a</mi><mi>r</mi><mi>l</mi><mi>y</mi><mo>,</mo><mo>&#160;</mo><mi>w</mi><mi>e</mi><mo>&#160;</mo><mi>g</mi><mi>e</mi><mi>t</mi><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>n</mi><mi>j</mi><mi>u</mi><mi>g</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>a</mi><mi>l</mi><mi>l</mi><mo>&#160;</mo><mi>v</mi><mi>a</mi><mi>l</mi><mi>u</mi><mi>e</mi><mi>s</mi><mo>,</mo><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mn>4</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>15</mn><mo>&#160;</mo><mo>-</mo><mo>&#8730;</mo><mn>15</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>14</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>14</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>13</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mo>(</mo><mn>13</mn><mo>&#160;</mo><mo>)</mo><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>12</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>12</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>11</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>11</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>10</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>10</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>8</mn><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mo>&#8730;</mo><mn>8</mn><mspace linebreak=\"newline\"></mspace><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mo>&#8730;</mo><mn>2</mn></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Times New Roman;\"> If the sum of two positive numbers is 65 and the square root of their product is 26, then the sum of their reciprocals is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    question_hi: "<p>5. <span style=\"font-family: Palanquin Dark;\">यदि दो धनात्मक संख्याओं का योग 65 है और उनके गुणनफल का वर्गमूल 26 है, तो उनके व्युत्क्रमों का योग है:</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>52</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>52</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>52</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>52</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>52</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>52</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>52</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>52</mn></mfrac></math></p>"],
                    solution_en: "<p>5. (c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>L</mi><mi>e</mi><mi>t</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&#160;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>s</mi><mo>&#160;</mo><mi>b</mi><mi>e</mi><mo>&#160;</mo><mo>&#8220;</mo><mi>a</mi><mo>&#8221;</mo><mo>&#160;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&#160;</mo><mo>&#8220;</mo><mi>b</mi><mo>&#8221;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mi>a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>65</mn><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&#160;</mo><mo>&#8730;</mo><mi>a</mi><mi>b</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>26</mn><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mi>a</mi><mi>b</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>676</mn><mspace linebreak=\"newline\"></mspace><mo>&#160;</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>+</mo><mfrac><mn>1</mn><mi>b</mi></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>65</mn></mrow><mn>676</mn></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>5</mn></mrow><mn>52</mn></mfrac></math>&rdquo;</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">a + b = 65, </span><span style=\"font-family: Palanquin Dark;\"> = 26</span></p>\r\n<p>&nbsp;</p>",
                    solution_hi: "<p>5. (c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>L</mi><mi>e</mi><mi>t</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&#160;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>s</mi><mo>&#160;</mo><mi>b</mi><mi>e</mi><mo>&#160;</mo><mo>&#8220;</mo><mi>a</mi><mo>&#8221;</mo><mo>&#160;</mo><mi>a</mi><mi>n</mi><mi>d</mi><mo>&#160;</mo><mo>&#8220;</mo><mi>b</mi><mo>&#8221;</mo><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mi>a</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>b</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>65</mn><mo>,</mo><mspace linebreak=\"newline\"></mspace><mo>&#160;</mo><mo>&#8730;</mo><mi>a</mi><mi>b</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>26</mn><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mi>a</mi><mi>b</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>676</mn><mspace linebreak=\"newline\"></mspace><mo>&#160;</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>+</mo><mfrac><mn>1</mn><mi>b</mi></mfrac><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mrow><mi>a</mi><mi>b</mi></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>65</mn></mrow><mn>676</mn></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>5</mn></mrow><mn>52</mn></mfrac></math></span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Times New Roman;\"> If a 10-digit number 75462A97B6 is divisible by 72, then the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>8</mn><mi>A</mi><mo>-</mo><mo>&#160;</mo><mn>4</mn><mi>B</mi></msqrt></math> </span><span style=\"font-family: Times New Roman;\">is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    question_hi: "<p>6.<span style=\"font-family: Palanquin Dark;\"> यदि 10 अंकों की एक संख्या 75462A97B6, 72 से विभाज्य है, तो&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>8</mn><mi>A</mi><mo>-</mo><mn>4</mn><mi>B</mi><mo>)</mo></msqrt></math>&nbsp;</span><span style=\"font-family: Palanquin Dark;\"> का मान है:</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">SSC CGL Tier-2, 29/01/2022</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>21</mn></msqrt></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>28</mn></msqrt></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>30</mn></msqrt></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>27</mn></msqrt></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>21</mn></msqrt></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>28</mn></msqrt></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>30</mn></msqrt></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>27</mn></msqrt></math></p>"],
                    solution_en: "<p>6. (b)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">75462A97B6 is divisible by 9&times;8</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">7+5+4+6+9+7+6+A+B = 46 + A +B</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">For this number to be divisible by 9, </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Either A + B =8, or A + B = 17</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">7B6 is divisible by 8 so, B = 3 or B = 7.</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">Now, A + B = 17 gets eliminated as for these values of B, A &gt; 9</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">, A + B = 8 and we take B = 3 so A = 5</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>8</mn><mi>A</mi><mo>-</mo><mn>4</mn><mi>B</mi><mo>)</mo><mo>&#160;</mo></msqrt><mo>=</mo><mo>&#160;</mo><msqrt><mo>(</mo><mn>8</mn><mo>&#215;</mo><mn>5</mn><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo><mo>&#160;</mo></msqrt><mo>=</mo><msqrt><mn>28</mn><mo>.</mo></msqrt></math></p>",
                    solution_hi: "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>8</mn><mi>A</mi><mo>-</mo><mn>4</mn><mi>B</mi><mo>)</mo><mo>&#160;</mo></msqrt><mo>=</mo><mo>&#160;</mo><msqrt><mo>(</mo><mn>8</mn><mo>&#215;</mo><mn>5</mn><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo></msqrt><mo>&#160;</mo><mo>=</mo><msqrt><mn>28</mn><mo>.</mo></msqrt></math></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">75462A97B6 9&times;8 से विभाज्य है</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">7+5+4+6+9+7+6+A+B = 46 + A +B</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">इस संख्या के 9 से विभाज्य होने के लिए,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">या तो A + B =8, or A + B = 17</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">7B6 8 से विभाज्य है, इसलिए B = 3 या B = 7 है।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">अब, A + B = 17 नहीं हो सकता है क्योंकि B, A&gt; 9 , A + B = 8 और हम</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> B = 3 लेते हैं इसलिए A = 5</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>8</mn><mi>A</mi><mo>-</mo><mn>4</mn><mi>B</mi><mo>)</mo><mo>&#160;</mo></msqrt><mo>&#160;</mo><mo>=</mo><msqrt><mo>&#160;</mo><mo>(</mo><mn>8</mn><mo>&#215;</mo><mn>5</mn><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>3</mn><mo>)</mo></msqrt><mo>&#160;</mo><mo>=</mo><msqrt><mn>28</mn><mo>.</mo></msqrt></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: " <p>7. </span><span style=\"font-family:Times New Roman\">The sum of the digits of the least number which when divided by 36, 72, 80 and 88 leaves the remainders 16, 52, 60 and 68, respectively, is:</span></p> <p><span style=\"font-family:Times New Roman\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    question_hi: " <p>7. </span><span style=\"font-family:Palanquin Dark\">वह छोटी से छोटी संख्या के अंकों का योग है जिसे 36, 72, 80 और 88 से विभाजित करने पर क्रमशः 16, 52, 60 और 68 शेष बचता है, वह संख्या है :</span></p> <p><span style=\"font-family:Times New Roman\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    options_en: [" <p> 17</span></p>", " <p> 11</span></p>", 
                                " <p> 14</span></p>", " <p> 16</span></p>"],
                    options_hi: [" <p> 17</span></p>", " <p> 11</span></p>",
                                " <p> 14</span></p>", " <p> 16</span></p>"],
                    solution_en: " <p>7. (d)</span></p> <p><span style=\"font-family:Times New Roman\">LCM (36, 72, 80, 88) = 7920</span></p> <p><span style=\"font-family:Times New Roman\">Here the difference between each divisor and each remainder given in question = 20 </span></p> <p><span style=\"font-family:Times New Roman\">So,for finding the required least number, we need to deduct the difference as obtained above from the LCM of the divisors.</span></p> <p><span style=\"font-family:Times New Roman\">Required least number = 7920 – 20 = 7900</span></p> <p><span style=\"font-family:Times New Roman\">So, sum of digits = 7 + 9 + 0 + 0 = 16</span></p>",
                    solution_hi: " <p>7. (d)</span></p> <p><span style=\"font-family:Times New Roman\">LCM (36, 72, 80, 88) = 7920</span></p> <p><span style=\"font-family:Palanquin Dark\">यहाँ प्रत्येक भाजक और प्रश्न में दिए गए प्रत्येक शेषफल के बीच का अंतर = 20</span></p> <p><span style=\"font-family:Palanquin Dark\">इसलिए, आवश्यक न्यूनतम संख्या ज्ञात करने के लिए, हमें भाजक के LCM से ऊपर प्राप्त अंतर को घटाना होगा।</span></p> <p><span style=\"font-family:Palanquin Dark\">अभीष्ट न्यूनतम संख्या = 7920 – 20 = 7900</span></p> <p><span style=\"font-family:Palanquin Dark\">अत: अंकों का योग =  7 + 9 + 0 + 0 = 16</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8.<span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>71</mn></msup><mo>+</mo><msup><mn>5</mn><mn>72</mn></msup><mo>+</mo><msup><mn>5</mn><mn>73</mn></msup><mo>+</mo><msup><mn>5</mn><mn>74</mn></msup><mo>+</mo><msup><mn>5</mn><mn>75</mn></msup></math><span style=\"font-family: Palanquin Dark;\"> is divisible by which of the following numbers?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    question_hi: "<p>8.<span style=\"font-family: Palanquin Dark;\"> निम्नलिखित में से <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>71</mn></msup><mo>+</mo><msup><mn>5</mn><mn>72</mn></msup><mo>+</mo><msup><mn>5</mn><mn>73</mn></msup><mo>+</mo><msup><mn>5</mn><mn>74</mn></msup><mo>+</mo><msup><mn>5</mn><mn>75</mn></msup></math>किस संख्या से विभाज्य है?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    options_en: ["<p>71</p>", "<p>69</p>", 
                                "<p>89</p>", "<p>73</p>"],
                    options_hi: ["<p>71</p>", "<p>69</p>",
                                "<p>89</p>", "<p>73</p>"],
                    solution_en: "<p>8. (a)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>71</mn></msup><mo>+</mo><msup><mn>5</mn><mn>72</mn></msup><mo>+</mo><msup><mn>5</mn><mn>73</mn></msup><mo>+</mo><msup><mn>5</mn><mn>74</mn></msup><mo>+</mo><msup><mn>5</mn><mn>75</mn></msup><mspace linebreak=\"newline\"/><msup><mn>5</mn><mn>71</mn></msup><mo>(</mo><mn>1</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>25</mn><mo>+</mo><mn>125</mn><mo>+</mo><mn>625</mn><mo>)</mo><mspace linebreak=\"newline\"/><msup><mn>5</mn><mn>71</mn></msup><mo>(</mo><mn>781</mn><mo>)</mo><mspace linebreak=\"newline\"/><msup><mn>5</mn><mn>71</mn></msup><mo>(</mo><mn>71</mn><mo>&#215;</mo><mn>11</mn><mo>)</mo><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mi>S</mi><mi>o</mi><mo>,</mo><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&#160;</mo><mi>e</mi><mi>x</mi><mi>p</mi><mi>r</mi><mi>e</mi><mi>s</mi><mi>s</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>&#160;</mo><mi>g</mi><mi>i</mi><mi>v</mi><mi>e</mi><mi>n</mi><mo>&#160;</mo><mi>i</mi><mi>n</mi><mo>&#160;</mo><mi>q</mi><mi>u</mi><mi>e</mi><mi>s</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>&#160;</mo><mi>i</mi><mi>s</mi><mo>&#160;</mo><mi>d</mi><mi>i</mi><mi>v</mi><mi>i</mi><mi>s</mi><mi>i</mi><mi>b</mi><mi>l</mi><mi>e</mi><mo>&#160;</mo><mi>b</mi><mi>y</mi><mo>&#160;</mo><mn>71</mn><mo>.</mo></math></p>",
                    solution_hi: "<p>8. (a)</p>\r\n<p>&nbsp;</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>71</mn></msup><mo>+</mo><msup><mn>5</mn><mn>72</mn></msup><mo>+</mo><msup><mn>5</mn><mn>73</mn></msup><mo>+</mo><msup><mn>5</mn><mn>74</mn></msup><mo>+</mo><msup><mn>5</mn><mn>75</mn></msup><mspace linebreak=\"newline\"/><msup><mn>5</mn><mn>71</mn></msup><mo>(</mo><mn>1</mn><mo>+</mo><mn>5</mn><mo>+</mo><mn>25</mn><mo>+</mo><mn>125</mn><mo>+</mo><mn>625</mn><mo>)</mo><mspace linebreak=\"newline\"/><msup><mn>5</mn><mn>71</mn></msup><mo>(</mo><mn>781</mn><mo>)</mo><mspace linebreak=\"newline\"/><msup><mn>5</mn><mn>71</mn></msup><mo>(</mo><mn>71</mn><mo>&#215;</mo><mn>11</mn><mo>)</mo><mspace linebreak=\"newline\"/><mspace linebreak=\"newline\"/><mi>S</mi><mi>o</mi><mo>,</mo><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&#160;</mo><mi>e</mi><mi>x</mi><mi>p</mi><mi>r</mi><mi>e</mi><mi>s</mi><mi>s</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>&#160;</mo><mi>g</mi><mi>i</mi><mi>v</mi><mi>e</mi><mi>n</mi><mo>&#160;</mo><mi>i</mi><mi>n</mi><mo>&#160;</mo><mi>q</mi><mi>u</mi><mi>e</mi><mi>s</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mo>&#160;</mo><mi>i</mi><mi>s</mi><mo>&#160;</mo><mi>d</mi><mi>i</mi><mi>v</mi><mi>i</mi><mi>s</mi><mi>i</mi><mi>b</mi><mi>l</mi><mi>e</mi><mo>&#160;</mo><mi>b</mi><mi>y</mi><mo>&#160;</mo><mn>71</mn><mo>.</mo></math></p>\r\n<p>&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Palanquin Dark;\"> Three numbers are in the ratio </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>:</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>:</mo><mfrac><mrow><mo>&#160;</mo><mn>3</mn><mo>&#160;</mo></mrow><mn>4</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\">. If the difference between the greatest number and the smallest number is 33, then HCF of the three numbers is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    question_hi: "<p>9.<span style=\"font-family: Palanquin Dark;\"> तीन संख्याएं</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>:</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>:</mo><mo>&#160;</mo><mfrac><mrow><mn>3</mn><mo>&#160;</mo></mrow><mn>4</mn></mfrac></math><span style=\"font-family: Palanquin Dark;\"> के अनुपात में हैं। यदि सबसे बड़ी संख्या और सबसे छोटी संख्या के बीच का अंतर 33 है, तो तीन संख्याओं का HCF है:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    options_en: ["<p>9</p>", "<p>5</p>", 
                                "<p>13</p>", "<p>11</p>"],
                    options_hi: ["<p>9</p>", "<p>5</p>",
                                "<p>13</p>", "<p>11</p>"],
                    solution_en: "<p>9. (d)</p>\r\n<p><span style=\"font-family: Times New Roman;\">According to the question,</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>2</mn></mfrac><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mfrac><mn>3</mn><mrow><mn>4</mn><mo>&#160;</mo></mrow></mfrac></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">= 6 : 8 : 9</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let three numbers be 6x, 8x and 9x</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, 9x &ndash; 6x = 33</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3x = 33</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 11</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">HCF (6x, 8x, 9x) = x = 11</span></p>",
                    solution_hi: "<p>9. (d)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">प्रश्न के अनुसार,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>:</mo><mo>&#160;</mo><mfrac><mn>3</mn><mrow><mn>4</mn><mo>&#160;</mo></mrow></mfrac></math><span style=\"font-family: Palanquin Dark;\"> </span><span style=\"font-family: Palanquin Dark;\"> </span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">= 6 : 8 : 9</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">माना कि तीन संख्याएँ 6x, 8x और 9x हैं</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">अतः, 9x &ndash; 6x = 33</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">3x = 33</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 11</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">HCF (6x, 8x, 9x) = x = 11</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. <span style=\"font-family: Times New Roman;\">The value of </span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>70</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>42</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>66</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>286</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>130</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>170</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    question_hi: "<p>10.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>70</mn><mo>&#160;</mo></mrow></mfrac><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mn>42</mn><mo>&#160;</mo></mrow></mfrac><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>66</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>3</mn><mn>286</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>130</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>170</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Palanquin Dark;\"> का मान है:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>85</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>85</mn></mfrac></math></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>85</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>85</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>85</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>85</mn></mfrac></math></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>85</mn></mfrac></math></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>85</mn></mfrac></math></p>"],
                    solution_en: "<p>10. (c)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>3</mn></mrow><mn>70</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>42</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>66</mn></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mo>&#160;</mo><mn>3</mn></mrow><mn>286</mn></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>130</mn></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mrow><mn>170</mn><mo>&#160;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>7</mn></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>10</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#160;</mo><mo>-</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>7</mn></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>22</mn></mfrac><mo>&#160;</mo><mfenced><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>13</mn></mfrac></mrow></mfenced><mo>+</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mrow><mn>130</mn><mo>&#160;</mo></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>170</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>&#160;</mo><mo>(</mo><mn>5</mn><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mn>30</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>22</mn></mfrac><mfrac><mrow><mo>(</mo><mn>22</mn><mo>)</mo></mrow><mn>39</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>130</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mn>170</mn><mo>&#160;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>1</mn><mn>15</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>39</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>130</mn></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mrow><mn>170</mn><mo>&#160;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mn>15</mn></mfrac><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>13</mn></mfrac><mo>(</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>10</mn></mfrac><mo>)</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mn>170</mn><mo>&#160;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>1</mn><mn>15</mn></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mo>&#160;</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>30</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mn>170</mn><mo>&#160;</mo></mrow></mfrac><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mn>1</mn><mn>10</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>170</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><mn>17</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>170</mn></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>18</mn><mo>/</mo></mrow><mn>170</mn></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>9</mn><mn>85</mn></mfrac></math></p>",
                    solution_hi: "<p>10 (c)&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>3</mn></mrow><mn>70</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>42</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>66</mn></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mo>&#160;</mo><mn>3</mn></mrow><mn>286</mn></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>130</mn></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mrow><mn>170</mn><mo>&#160;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>7</mn></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>10</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#160;</mo><mo>-</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mn>7</mn></mfrac><mo>&#160;</mo><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>22</mn></mfrac><mo>&#160;</mo><mfenced><mrow><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>3</mn><mn>13</mn></mfrac></mrow></mfenced><mo>+</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mrow><mn>130</mn><mo>&#160;</mo></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>170</mn></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mo>&#160;</mo><mo>(</mo><mn>5</mn><mo>-</mo><mn>3</mn><mo>)</mo></mrow><mn>30</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>22</mn></mfrac><mfrac><mrow><mo>(</mo><mn>22</mn><mo>)</mo></mrow><mn>39</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>130</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mn>170</mn><mo>&#160;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>1</mn><mn>15</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>39</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>130</mn></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mo>&#160;</mo><mn>1</mn></mrow><mrow><mn>170</mn><mo>&#160;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>1</mn><mo>&#160;</mo></mrow><mn>15</mn></mfrac><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>13</mn></mfrac><mo>(</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>10</mn></mfrac><mo>)</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mn>170</mn><mo>&#160;</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mn>1</mn><mn>15</mn></mfrac><mo>&#160;</mo><mo>+</mo><mfrac><mrow><mo>&#160;</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>30</mn></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mrow><mn>170</mn><mo>&#160;</mo></mrow></mfrac><mo>&#160;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mn>1</mn><mn>10</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>170</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mrow><mo>(</mo><mn>17</mn><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>170</mn></mfrac><mo>&#160;</mo><mo>=</mo><mfrac><mrow><mo>&#160;</mo><mn>18</mn><mo>/</mo></mrow><mn>170</mn></mfrac><mo>=</mo><mo>&#160;</mo><mfrac><mn>9</mn><mn>85</mn></mfrac></math></p>\r\n<p>&nbsp;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Times New Roman;\"> Let p, q, r and s be positive natural numbers having three exact factors including 1 and the number itself If q &gt; p and both are two-digit numbers, and r &gt; s and both are one-digit numbers, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>p</mi><mo>-</mo><mi>q</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><mi>r</mi><mo>-</mo><mi>s</mi><mo>)</mo></mrow></mfrac></math>the expression </span><span style=\"font-family: Times New Roman;\"> is:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    question_hi: "<p>11. <span style=\"font-family: Palanquin Dark;\">मान लीजिए कि p, q, r और s तीन सटीक गुणनखंडों वाली धनात्मक प्राकृत संख्याएँ हैं जिनमें 1 और स्वयं संख्या शामिल है यदि q &gt; p और दोनों दो अंकों की संख्याएँ हैं, और r &gt; s और दोनों एक -अंकीय संख्याएँ हैं, तो&nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>-</mo><mi>q</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>r</mi><mo>-</mo><mi>s</mi></mrow></mfrac></math>व्यंजक </span><span style=\"font-family: Palanquin Dark;\"> का मान है:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    options_en: ["<p>- S - 1</p>", "<p>S - 1</p>", 
                                "<p>1 - S</p>", "<p>S +1</p>"],
                    options_hi: ["<p>- S - 1</p>", "<p>S - 1</p>",
                                "<p>1 - S</p>", "<p>S +1</p>"],
                    solution_en: "<p>11. (a)</p>\r\n<p><span style=\"font-family: Times New Roman;\">As prime numbers have 2 factors</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Only the squares of prime numbers will have three factors.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let r = 9 and s = 4</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">And q = 49 and p = 25</span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>-</mo><mi>q</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>r</mi><mo>-</mo><mi>s</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>25</mn><mo>-</mo><mn>49</mn><mo>-</mo><mn>1</mn></mrow><mrow><mn>9</mn><mo>-</mo><mn>4</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>25</mn></mrow><mn>5</mn></mfrac><mo>=</mo><mo>-</mo><mn>5</mn></math></span></p>",
                    solution_hi: "<p>11. (a)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">चूंकि अभाज्य संख्याओं के 2 गुणनखंड होते हैं</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">केवल अभाज्य संख्याओं के वर्गों में तीन गुणनखंड होंगे।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">माना r = 9 और s = 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">और q = 49 और p = 25</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mo>-</mo><mi>q</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>r</mi><mo>-</mo><mi>s</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mn>25</mn><mo>-</mo><mn>49</mn><mo>-</mo><mn>1</mn></mrow><mrow><mn>9</mn><mo>-</mo><mn>4</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>25</mn></mrow><mn>5</mn></mfrac><mo>=</mo><mo>-</mo><mn>5</mn></math> </span><span style=\"font-family: Palanquin Dark;\"> = </span><span style=\"font-family: Palanquin Dark;\"> = -5</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">सभी विकल्पों में s = 4 रखने पर हमें केवल विकल्प (a) मिलता है जो इस मान को संतुष्ट करता है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: " <p>12. </span><span style=\"font-family:Times New Roman\">If a nine-digit number 789x6378y is divisible by 72, then the value of xy is:</span></p> <p><span style=\"font-family:Times New Roman\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    question_hi: "<p>12.<span style=\"font-family: Palanquin Dark;\"> यदि एक नौ अंकों की संख्या 789x6378y को 72 से विभाज्य है, तो xy का मान है:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 03/02/2022 </span></p>",
                    options_en: [" <p> 10 </span></p>", " <p> 12 </span></p>", 
                                " <p> 8 </span></p>", " <p> 15</span></p>"],
                    options_hi: ["<p>10</p>", "<p>12</p>",
                                "<p>8</p>", "<p>15</p>"],
                    solution_en: " <p>12. (c) </span></p> <p><span style=\"font-family:Times New Roman\">789x6378y is divisible by 8×9</span></p> <p><span style=\"font-family:Times New Roman\">7+8+9+x+6+3+7+8+y = 48+ x + y</span></p> <p><span style=\"font-family:Times New Roman\">48+ x + y = 54 or 63( to be divisible by 9 )</span></p> <p><span style=\"font-family:Times New Roman\">x + y = 6 or x + y = 15,</span></p> <p><span style=\"font-family:Times New Roman\">To be divisible by 8, 78y should be divisible by 8.</span></p> <p><span style=\"font-family:Times New Roman\">Clearly, y = 4</span></p> <p><span style=\"font-family:Times New Roman\">x + y = 6</span></p> <p><span style=\"font-family:Times New Roman\">x + 4= 6</span></p> <p><span style=\"font-family:Times New Roman\">x = 2</span></p> <p><span style=\"font-family:Times New Roman\">x + y = 15 gets eliminated as x>9</span></p> <p><span style=\"font-family:Times New Roman\">xy = 2×4 = 8</span></p>",
                    solution_hi: "<p>12. (c)</p>\r\n<p><span style=\"font-family: Palanquin Dark;\">789x6378y, 8x9 से विभाज्य है</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">7+8+9+x+6+3+7+8+y = 48+ x + y</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">48+ x + y = 54 or 63 (9 से विभाज्य होने के लिए)</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x + y = 6 or x + y = 15,</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">8 से विभाज्य होने के लिए, 78y को 8 से विभाज्य होना चाहिए।</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">स्पष्ट रूप से, y = 4</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x + y = 6</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x + 4= 6</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x = 2</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">x + y = 15, x&gt;9 के रूप में समाप्त हो जाता है</span></p>\r\n<p><span style=\"font-family: Palanquin Dark;\">xy = 2&times;4 = 8</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: " <p>13. </span><span style=\"font-family:Times New Roman\"> Two positive numbers differ by 1280. When the greater number is divided by the smaller number, the quotient is 7 and the remainder is 50. The greater number is:</span></p> <p><span style=\"font-family:Times New Roman\">SSC CGL Tier-2, 15/11/2020 </span></p>",
                    question_hi: " <p>13.</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">दो सकारात्मक संख्याएँ</span><span style=\"font-family:Times New Roman\"> 1280</span><span style=\"font-family:Baloo\"> से भिन्न होती हैं। जब बड़ी संख्या को छोटी संख्या से विभाजित किया जाता है, तो </span><span style=\"font-family:Baloo\">भागफल</span><span style=\"font-family:Baloo\"> 7 होता है और शेष </span><span style=\"font-family:Times New Roman\">50 </span><span style=\"font-family:Baloo\">होता है। बड़ी संख्या का मान ज्ञात कीजिए |</span></p> <p><span style=\"font-family:Times New Roman\">SSC CGL Tier-2, 15/11/2020 </span></p>",
                    options_en: [" <p> 1458</span><span style=\"font-family:Times New Roman\">   </span></p>", " <p> 1485    </span></p>", 
                                " <p> 1585  </span></p>", " <p> 1558</span></p>"],
                    options_hi: [" <p> 1458</span><span style=\"font-family:Times New Roman\">   </span></p>", " <p> 1485    </span></p>",
                                " <p> 1585  </span></p>", " <p> 1558</span></p>"],
                    solution_en: " <p>13.(b)</span><span style=\"font-family:Times New Roman\">  Let larger number = a</span></p> <p><span style=\"font-family:Times New Roman\">Smaller number = b </span></p> <p><span style=\"font-family:Times New Roman\">a - b =1280       .....(1)</span></p> <p><span style=\"font-family:Times New Roman\">a = 7b + 50</span></p> <p><span style=\"font-family:Times New Roman\">a - 7b = 50        .....(2)</span></p> <p><span style=\"font-family:Times New Roman\">Multiplying eq 1 by 7 </span></p> <p><span style=\"font-family:Times New Roman\">7a - 7b = 8960</span><span style=\"font-family:Times New Roman\">...,..(3)</span></p> <p><span style=\"font-family:Times New Roman\">Subtracting eq 2 from eq 3 </span></p> <p><span style=\"font-family:Times New Roman\">6a = 8910</span></p> <p><span style=\"font-family:Times New Roman\">a = 1485</span></p>",
                    solution_hi: " <p>13.(b)</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">माना की सबसे बड़ी संख्या </span><span style=\"font-family:Times New Roman\">= a</span></p> <p><span style=\"font-family:Baloo\"> सबसे छोटी संख्या </span><span style=\"font-family:Times New Roman\"> = b</span></p> <p><span style=\"font-family:Times New Roman\">a - b =1280       .....(1)</span></p> <p><span style=\"font-family:Times New Roman\">a = 7b + 50</span></p> <p><span style=\"font-family:Times New Roman\">a - 7b = 50        .....(2)</span></p> <p><span style=\"font-family:Baloo\">समीकरण</span><span style=\"font-family:Times New Roman\"> 1 </span><span style=\"font-family:Baloo\">को</span><span style=\"font-family:Times New Roman\"> 7 </span><span style=\"font-family:Baloo\">से गुणा करने पर</span><span style=\"font-family:Times New Roman\"> </span></p> <p><span style=\"font-family:Times New Roman\">7a - 7b = 8960</span><span style=\"font-family:Times New Roman\">...,..(3)</span></p> <p><span style=\"font-family:Baloo\">समीकरण</span><span style=\"font-family:Times New Roman\"> 2 </span><span style=\"font-family:Baloo\">को समीकरण</span><span style=\"font-family:Times New Roman\"> 3 </span><span style=\"font-family:Baloo\">से घटाने पर </span></p> <p><span style=\"font-family:Times New Roman\">6a = 8910 </span><span style=\"font-family:Times New Roman\"> a = 1485</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14<span style=\"font-family: Times New Roman;\">. When positive numbers x, y and z are divided by 31, the reminders are 17, 24 and 27 respectively. When (4x - 2y + 3z) is divided by 31, the reminder will be:</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 15/11/2020 </span></p>",
                    question_hi: "<p>14.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">जब सकारात्मक संख्या </span><span style=\"font-family: Times New Roman;\">x, y</span><span style=\"font-family: Baloo;\"> और </span><span style=\"font-family: Times New Roman;\">z</span><span style=\"font-family: Baloo;\"> को </span><span style=\"font-family: Times New Roman;\">31</span><span style=\"font-family: Baloo;\"> से विभाजित किया जाता है, तो </span><span style=\"font-family: Baloo;\">शेषफल</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">क्रमशः</span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">17, 24 </span><span style=\"font-family: Baloo;\">और </span><span style=\"font-family: Times New Roman;\">27</span><span style=\"font-family: Baloo;\"> होते हैं। जब</span><span style=\"font-family: Times New Roman;\"> (4x - 2y +3z)</span><span style=\"font-family: Baloo;\"> को </span><span style=\"font-family: Times New Roman;\">31</span><span style=\"font-family: Baloo;\"> से विभाजित किया जाता है, तो </span><span style=\"font-family: Baloo;\">शेषफल</span><span style=\"font-family: Baloo;\"> क्या होगा |</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 15/11/2020 </span></p>",
                    options_en: ["<p>9</p>", "<p>8</p>", 
                                "<p>16</p>", "<p>19</p>"],
                    options_hi: ["<p>9</p>", "<p>8</p>",
                                "<p>16</p>", "<p>19</p>"],
                    solution_en: "<p>14.(b)</p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>4</mn><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mi>y</mi><mo>+</mo><mn>3</mn><mi>z</mi><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>&#215;</mo><mn>17</mn><mo>&#160;</mo><mo>-</mo><mn>24</mn><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mn>27</mn><mo>&#215;</mo><mn>3</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>(</mo><mn>4</mn><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mi>y</mi><mo>+</mo><mn>3</mn><mi>z</mi><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>101</mn></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">When 101 is divided by 31 we get reminder = 8</span></p>",
                    solution_hi: "<p>14.(b)</p>\r\n<p>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>4</mn><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mi>y</mi><mo>+</mo><mn>3</mn><mi>z</mi><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>4</mn><mo>&#215;</mo><mn>17</mn><mo>&#160;</mo><mo>-</mo><mn>24</mn><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mn>27</mn><mo>&#215;</mo><mn>3</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mo>(</mo><mn>4</mn><mi>x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>2</mn><mi>y</mi><mo>+</mo><mn>3</mn><mi>z</mi><mo>)</mo><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>101</mn></math></p>\r\n<p><span style=\"font-family: Baloo;\">जब</span><span style=\"font-family: Times New Roman;\"> 101 </span><span style=\"font-family: Baloo;\">को</span><span style=\"font-family: Times New Roman;\"> 31 </span><span style=\"font-family: Baloo;\">से भाग दिया जाता है तो हमें शेषफल</span><span style=\"font-family: Times New Roman;\"> = 8 </span><span style=\"font-family: Baloo;\">मिलता है। </span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Times New Roman;\"> If the 5-digit number 535ab is divisible by 3, 7 and 11, then what is the value of </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mo>&#160;</mo><mi>a</mi><mi>b</mi><mo>)</mo><mo>?</mo></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 15/11/2020 </span></p>",
                    question_hi: "<p>15.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि 5-अंकीय संख्या </span><span style=\"font-family: Times New Roman;\">535ab, 3, 7</span><span style=\"font-family: Baloo;\"> और </span><span style=\"font-family: Times New Roman;\">11</span><span style=\"font-family: Baloo;\"> से </span><span style=\"font-family: Baloo;\">विभाज्य</span><span style=\"font-family: Baloo;\"> है, तो</span><span style=\"font-family: Times New Roman;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mo>&#160;</mo><mi>a</mi><mi>b</mi><mo>)</mo><mo>?</mo></math><span style=\"font-family: Baloo;\"> का मान क्या है?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 15/11/2020 </span></p>",
                    options_en: ["<p>95</p>", "<p>83</p>", 
                                "<p>89</p>", "<p>77</p>"],
                    options_hi: ["<p>95</p>", "<p>83</p>",
                                "<p>89</p>", "<p>77</p>"],
                    solution_en: "<p>15.(a) <span style=\"font-family: Times New Roman;\"> 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">11 = 231</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let a and b = 9 and 9</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">When 53599 is divided by 231 reminder = 7 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Means 53599 - 7 = 53592 is that number </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a = 9 and b =2</span></p>\r\n<p>= 81 - 4 + 18 = 95</p>",
                    solution_hi: "<p>15.(a)<span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>11</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>231</mn></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">माना</span><span style=\"font-family: Times New Roman;\"> a </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> b </span><span style=\"font-family: Baloo;\">क्रमशः</span><span style=\"font-family: Times New Roman;\"> 9 </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> 9 </span><span style=\"font-family: Baloo;\">हैं</span></p>\r\n<p><span style=\"font-family: Baloo;\">जब</span><span style=\"font-family: Times New Roman;\"> 53599 </span><span style=\"font-family: Baloo;\">को</span><span style=\"font-family: Times New Roman;\"> 231</span><span style=\"font-family: Baloo;\">से भाग दिया जाता है तो शेषफल</span><span style=\"font-family: Times New Roman;\"> = 7</span></p>\r\n<p><span style=\"font-family: Baloo;\">मतलब</span><span style=\"font-family: Times New Roman;\"> 53599 - 7 = 53592 </span><span style=\"font-family: Baloo;\">वह संख्या है।</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a = 9 </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> b = 2</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>-</mo><msup><mi>b</mi><mn>2</mn></msup><mo>+</mo><mi>a</mi><mi>b</mi><mo>)</mo><mo>=</mo><mo>&#160;</mo><mn>81</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>4</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>18</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>95</mn><mo>&#160;</mo></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. <span style=\"font-family: Times New Roman;\"> If the five digit number 235xy is divisible by 3, 7 and 11 then what is the value of (3x-4y)?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 16/11/2020 </span></p>",
                    question_hi: "<p>16.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि </span><span style=\"font-family: Baloo;\">पाँच</span><span style=\"font-family: Baloo;\">-संख्या वाली संख्या </span><span style=\"font-family: Times New Roman;\">235xy</span><span style=\"font-family: Times New Roman;\">, 3, 7 </span><span style=\"font-family: Baloo;\">और </span><span style=\"font-family: Times New Roman;\">11</span><span style=\"font-family: Baloo;\"> से </span><span style=\"font-family: Baloo;\">विभाज्य</span><span style=\"font-family: Baloo;\"> है तो मान क्या है </span><span style=\"font-family: Times New Roman;\">(3x-4y) </span><span style=\"font-family: Times New Roman;\">?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 16/11/2020 </span></p>",
                    options_en: ["<p>8</p>", "<p>9</p>", 
                                "<p>5</p>", "<p>10</p>"],
                    options_hi: ["<p>8</p>", "<p>9</p>",
                                "<p>5</p>", "<p>10</p>"],
                    solution_en: "<p>16.(d)<span style=\"font-family: Times New Roman;\"> LCM of 3,7,11 is = 231</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let the number be 23599</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">When 23599 is divided by 231 we get remainder as 37</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So the number is = 23599 - 37 = 23562</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x = 6 and y =2 </span></p>\r\n<p>(3x-4y) = 18 - 8 = 10</p>",
                    solution_hi: "<p>16.(d)<span style=\"font-family: Times New Roman;\"> 3,7,11 </span><span style=\"font-family: Baloo;\">का</span><span style=\"font-family: Times New Roman;\"> LCM = 231</span></p>\r\n<p><span style=\"font-family: Baloo;\">मान लीजिए संख्या</span><span style=\"font-family: Times New Roman;\"> 23599 </span><span style=\"font-family: Baloo;\">है</span></p>\r\n<p><span style=\"font-family: Baloo;\">जब</span><span style=\"font-family: Times New Roman;\"> 23599 </span><span style=\"font-family: Baloo;\">को</span><span style=\"font-family: Times New Roman;\"> 231 </span><span style=\"font-family: Baloo;\">से विभाजित किया जाता है तो हमें शेषफल</span><span style=\"font-family: Times New Roman;\"> 37 </span><span style=\"font-family: Baloo;\">मिलता है</span></p>\r\n<p><span style=\"font-family: Baloo;\">तो वह संख्या है =</span><span style=\"font-family: Times New Roman;\"> 23599 - 37 = 23562</span></p>\r\n<p><span style=\"font-family: Baloo;\">x = 6 और y = 2</span></p>\r\n<p>(3x-4Y) = 18 - 8 = 10</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17.<span style=\"font-family: Times New Roman;\"> Let a&ne;b,( ab)</span><span style=\"font-family: Times New Roman;\">&nbsp;is a 2-digit prime number such that ba is also a prime number. The sum of all such number is: </span></p>\r\n<p>&nbsp;</p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 16/11/2020 </span></p>",
                    question_hi: "<p>17.<span style=\"font-family: Times New Roman;\"> a&ne;b, (a</span><span style=\"font-family: Times New Roman;\">b)</span><span style=\"font-family: Baloo;\"> एक</span><span style=\"font-family: Times New Roman;\"> 2-</span><span style=\"font-family: Baloo;\">अंकीय अभाज्य संख्या है जैसे कि </span><span style=\"font-family: Times New Roman;\">ba</span><span style=\"font-family: Baloo;\"> भी एक अभाज्य संख्या है। ऐसी सभी संख्याओं का योग है</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 16/11/2020 </span></p>",
                    options_en: ["<p>374</p>", "<p>418</p>", 
                                "<p>407</p>", "<p>396</p>"],
                    options_hi: ["<p>374</p>", "<p>418</p>",
                                "<p>407</p>", "<p>396</p>"],
                    solution_en: "<p>17.(b) <span style=\"font-family: Times New Roman;\"> HINT: ab and ba both can be prime only and only when both are odd number </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Such numbers are 13,31,17,71,37,73,79,97</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Sum = 13 + 31 + 17 + 71 + 37 + 73 + 79 + 97 = 418</span></p>",
                    solution_hi: "<p>17.(b)<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">सुझाव:</span><span style=\"font-family: Times New Roman;\"> ab </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> ba </span><span style=\"font-family: Baloo;\">दोनों ही अभाज्य हो सकते हैं और केवल तभी जब दोनों विषम संख्याएँ हों</span></p>\r\n<p><span style=\"font-family: Baloo;\">ऐसी संख्याएं हैं</span><span style=\"font-family: Times New Roman;\"> :- 13,31,17,71,37,73,79,97</span></p>\r\n<p><span style=\"font-family: Baloo;\">योग</span><span style=\"font-family: Times New Roman;\"> = 13 + 31 + 17 + 71 + 37 + 73 + 79 + 97 = 418</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. <span style=\"font-family: Times New Roman;\"> Let x be the least number which subtracted from 10424 gives a perfect square number. What is the least number by which x should be multiplied to get a perfect square?</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 16/11/2020 </span></p>",
                    question_hi: "<p>18.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">मान लें कि </span><span style=\"font-family: Times New Roman;\">x </span><span style=\"font-family: Baloo;\">सबसे छोटी संख्या है जिसे </span><span style=\"font-family: Times New Roman;\">10424 </span><span style=\"font-family: Baloo;\">से घटाया जाए तो, एक पूर्ण वर्ग संख्या देता है। एक पूर्ण वर्ग प्राप्त करने के लिए किस संख्या को </span><span style=\"font-family: Times New Roman;\">x</span><span style=\"font-family: Baloo;\"> से </span><span style=\"font-family: Baloo;\">गुणा</span><span style=\"font-family: Baloo;\"> किया जाना चाहिए|</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 16/11/2020 </span></p>",
                    options_en: ["<p>3</p>", "<p>6</p>", 
                                "<p>5</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>6</p>",
                                "<p>5</p>", "<p>2</p>"],
                    solution_en: "<p>18.(c)<span style=\"font-family: Times New Roman;\"> As 10404 is a perfect square </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So x = 20</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Prime factorization of 20 = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">5 </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">So, when 20 is multiplied by 5 it become a perfect square</span></p>",
                    solution_hi: "<p>18.(c)<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">चूँकि</span><span style=\"font-family: Times New Roman;\"> 10404 </span><span style=\"font-family: Baloo;\">एक पूर्ण वर्ग संख्या है</span></p>\r\n<p><span style=\"font-family: Baloo;\">तो</span><span style=\"font-family: Times New Roman;\"> x = 20</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">20 </span><span style=\"font-family: Baloo;\">का अभाज्य गुणनखंडन </span><span style=\"font-family: Times New Roman;\">= 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math></span><span style=\"font-family: Times New Roman;\">5</span></p>\r\n<p><span style=\"font-family: Baloo;\">इसलिए, जब</span><span style=\"font-family: Times New Roman;\"> 20 </span><span style=\"font-family: Baloo;\">को</span><span style=\"font-family: Times New Roman;\"> 5 </span><span style=\"font-family: Baloo;\">से गुणा किया जाता है तो यह एक पूर्ण वर्ग संख्या बन जाता है |</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: " <p>19.</span><span style=\"font-family:Times New Roman\"> When positive number a, b and c are divided by 13, the remainder are 9, 7 and 10, respectively. What will be the remainder when (a + 2b + 5c) is divided by 13?</span></p> <p><span style=\"font-family:Times New Roman\">SSC CGL Tier-2, 16/11/2020 </span></p>",
                    question_hi: " <p>19.</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">जब सकारात्मक संख्या </span><span style=\"font-family:Times New Roman\">A, B</span><span style=\"font-family:Baloo\"> और </span><span style=\"font-family:Times New Roman\">C</span><span style=\"font-family:Baloo\"> को </span><span style=\"font-family:Times New Roman\">13 </span><span style=\"font-family:Baloo\">से विभाजित किया जाता है, तो शेष क्रमश </span><span style=\"font-family:Times New Roman\">9, 7</span><span style=\"font-family:Baloo\"> और </span><span style=\"font-family:Times New Roman\">10 </span><span style=\"font-family:Baloo\">होते हैं। जब </span><span style=\"font-family:Times New Roman\">(a + 2b + 5c) </span><span style=\"font-family:Baloo\">को </span><span style=\"font-family:Times New Roman\">13 </span><span style=\"font-family:Baloo\">से विभाजित किया जाता है तो </span><span style=\"font-family:Baloo\">शेषफल</span><span style=\"font-family:Baloo\"> क्या होगा?</span></p> <p><span style=\"font-family:Times New Roman\">SSC CGL Tier-2, 16/11/2020 </span></p>",
                    options_en: [" <p> 10</span></p>", " <p> 5</span></p>", 
                                " <p> 9</span></p>", " <p> 8</span></p>"],
                    options_hi: [" <p> 10</span></p>", " <p> 5</span></p>",
                                " <p> 9</span></p>", " <p> 8</span></p>"],
                    solution_en: " <p>19.(d)</span><span style=\"font-family:Times New Roman\">  a = 9,  b = 7 and c= 10</span></p> <p> =(9 + 14 + 50) = 73</span></p> <p><span style=\"font-family:Times New Roman\">When 73 is divided by 13 remainder is 8</span></p>",
                    solution_hi: " <p>19.(d)</span><span style=\"font-family:Times New Roman\"> a = 9,  b = 7 </span><span style=\"font-family:Baloo\">और</span><span style=\"font-family:Times New Roman\"> c= 10</span></p> <p> =(9 + 14 + 50) = 73</span></p> <p><span style=\"font-family:Baloo\">जब</span><span style=\"font-family:Times New Roman\"> 73 </span><span style=\"font-family:Baloo\">को</span><span style=\"font-family:Times New Roman\"> 13 </span><span style=\"font-family:Baloo\">से विभाजित किया जाता है तो शेषफल</span><span style=\"font-family:Times New Roman\"> 8 </span><span style=\"font-family:Baloo\">होता है|</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\">Find the sum of </span><span style=\"font-family: Times New Roman;\">6+8+10+12+14&hellip;&hellip;&hellip;&hellip;..+40</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020 </span></p>",
                    question_hi: "<p>20.<span style=\"font-family: Times New Roman;\"> 6+8+10+12+14&hellip;&hellip;&hellip;&hellip;..+40 </span><span style=\"font-family: Baloo;\">का योग ज्ञात करे </span><span style=\"font-family: Times New Roman;\">|</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020 </span></p>",
                    options_en: ["<p>400</p>", "<p>424 <span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p>1600</p>", "<p>414</p>"],
                    options_hi: ["<p>400</p>", "<p>424 <span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p>1600</p>", "<p>414</p>"],
                    solution_en: "<p>20.(d)<span style=\"font-family: Times New Roman;\"> Number of terms in the series = 18</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Sum = (number of terms/2) (first term + last term)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Sum =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>2</mn></mfrac><mo>(</mo><mn>6</mn><mo>+</mo><mn>40</mn><mo>)</mo><mo>=</mo><mn>414</mn></math></span><span style=\"font-family: Times New Roman;\"> </span></p>",
                    solution_hi: "<p>20.(d)<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">श्रृंखला में पदों की संख्या =</span><span style=\"font-family: Times New Roman;\"> 18</span></p>\r\n<p><span style=\"font-family: Baloo;\">योग = (पदों की संख्या/</span><span style=\"font-family: Times New Roman;\">2) (</span><span style=\"font-family: Baloo;\">पहला पद + अंतिम पद)</span></p>\r\n<p><span style=\"font-family: Baloo;\">योग =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>2</mn></mfrac><mo>(</mo><mn>6</mn><mo>+</mo><mn>40</mn><mo>)</mo><mo>=</mo><mn>414</mn></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. <span style=\"font-family: Times New Roman;\"> Find the number of prime factors in the product </span><span style=\"font-family: Times New Roman;\">.<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>30</mn><mo>)</mo></mrow><mn>5</mn></msup><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>24</mn><mo>)</mo></mrow><mn>5</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020 </span></p>",
                    question_hi: "<p>21. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>30</mn><mo>)</mo></mrow><mn>5</mn></msup><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>24</mn><mo>)</mo></mrow><mn>5</mn></msup></math><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">उत्पाद में अभाज्य गुणनखण्ड की संख्या ज्ञात कीजिए</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 18/11/2020 </span></p>",
                    options_en: ["<p>45</p>", "<p>35</p>", 
                                "<p>10</p>", "<p>30</p>"],
                    options_hi: ["<p>45</p>", "<p>35</p>",
                                "<p>10</p>", "<p>30</p>"],
                    solution_en: "<p>21.(b)<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p>&nbsp;</p>\r\n<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>30</mn><mo>)</mo></mrow><mn>5</mn></msup><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>24</mn><mo>)</mo></mrow><mn>5</mn></msup><mspace linebreak=\"newline\"></mspace><msup><mrow><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>)</mo></mrow><mn>5</mn></msup><mo>&#215;</mo><msup><mrow><mo>(</mo><msup><mn>2</mn><mn>3</mn></msup><mo>&#215;</mo><mn>3</mn><mo>)</mo></mrow><mn>5</mn></msup><mspace linebreak=\"newline\"></mspace><msup><mn>2</mn><mn>20</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>10</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>5</mn></msup><mspace linebreak=\"newline\"></mspace><mi>N</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>p</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mo>&#160;</mo><mi>f</mi><mi>a</mi><mi>c</mi><mi>t</mi><mi>o</mi><mi>r</mi><mi>s</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mo>(</mo><mn>20</mn><mo>+</mo><mn>10</mn><mo>+</mo><mn>5</mn><mo>)</mo><mo>&#160;</mo><mi>N</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>p</mi><mi>r</mi><mi>i</mi><mi>m</mi><mi>e</mi><mo>&#160;</mo><mi>f</mi><mi>a</mi><mi>c</mi><mi>t</mi><mi>o</mi><mi>r</mi><mi>s</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>35</mn></math></span></p>",
                    solution_hi: "<p>21.(b)<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>30</mn><mo>)</mo></mrow><mn>5</mn></msup><mo>&#215;</mo><msup><mrow><mo>(</mo><mn>24</mn><mo>)</mo></mrow><mn>5</mn></msup><mo> </mo><msup><mrow><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>)</mo></mrow><mn>5</mn></msup><mo>&#215;</mo><msup><mrow><mo>(</mo><msup><mn>2</mn><mn>3</mn></msup><mo>&#215;</mo><mn>3</mn><mo>)</mo></mrow><mn>5</mn></msup></math></p>\r\n<p><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Baloo;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msup><mn>2</mn><mn>20</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>10</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>5</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">अभाज्य गुणनखंडों की संख्या</span><span style=\"font-family: Times New Roman;\"> = (20 + 10 + 5)</span></p>\r\n<p><span style=\"font-family: Baloo;\">अभाज्य गुणनखंडों की संख्या =</span><span style=\"font-family: Times New Roman;\"> 35</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22.<span style=\"font-family: Times New Roman;\"> If a nine-digit number 389x6378y is divisible by 72, then the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>6</mn><mi>x</mi><mo>+</mo><mn>7</mn><mi>y</mi><mo>)</mo></msqrt></math> </span><span style=\"font-family: Times New Roman;\">will be :</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>22.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">यदि नौ अंकों की एक संख्या</span><span style=\"font-family: Times New Roman;\"> 389x6378y , 72</span><span style=\"font-family: Baloo;\"> से विभाजित है, तो&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>6</mn><mi>x</mi><mo>+</mo><mn>7</mn><mi>y</mi><mo>)</mo></msqrt></math></span><span style=\"font-family: Baloo;\">का मान होगा : </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>6</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>46</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>8</p>"],
                    options_hi: ["<p>6</p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>46</mn></msqrt></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p>8</p>"],
                    solution_en: "<p>22.(d)<span style=\"font-family: Times New Roman;\"> Since, 389x6378y is divisible by 72 it must be divisible by 9 and 8 (coprime factors of 72) and y must be an even number. So the sum of digits of this number must be divisible by 9 and last three digits by 8. 3+8+9+x+6+3+7+8+y = 44+x+y</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x+y must be 1 or 10 as after 45 nearest multiple of 9 is 45 and 54.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">For x+y =1, x must be 1 as y can&rsquo;t be an odd number. And 780 is not divisible by 8 so it will get neglected.</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Pairs for 10 = (1,9)(2,8), (3,7), (4,6), (5,5), (6,4)(7,3)(8,2)(9,1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Only pair which satisfies these conditions is (6,4). So the required value is </span><span style=\"font-family: Times New Roman;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>6</mn><mi>x</mi><mo>+</mo><mn>7</mn><mi>y</mi><mo>)</mo></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msqrt><mo>(</mo><mn>6</mn><mo>(</mo><mn>6</mn><mo>)</mo><mo>+</mo><mn>7</mn><mo>(</mo><mn>4</mn><mo>)</mo></msqrt><mo>=</mo><mo>&#160;</mo><mn>8</mn></math> </span></p>",
                    solution_hi: "<p>22.(d)<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">चूँकि,</span><span style=\"font-family: Times New Roman;\"> 389x6378y, 72 </span><span style=\"font-family: Baloo;\">से विभाज्य है, यह </span><span style=\"font-family: Times New Roman;\">9 </span><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> 8 </span><span style=\"font-family: Baloo;\">से विभाज्य होना चाहिए</span><span style=\"font-family: Times New Roman;\"> (72 </span><span style=\"font-family: Baloo;\">का सहअभाज्य गुणनखंड) और</span><span style=\"font-family: Times New Roman;\"> y </span><span style=\"font-family: Baloo;\">एक सम संख्या होनी चाहिए। अतः इस संख्या के अंकों का योग</span><span style=\"font-family: Times New Roman;\"> 9 </span><span style=\"font-family: Baloo;\">से और अंतिम तीन अंक </span><span style=\"font-family: Times New Roman;\"> 8 </span><span style=\"font-family: Baloo;\">से विभाज्य होना चाहिए। </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">3+8+9+x+6+3+7+8+y = 44+x+y</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x+y, 1 </span><span style=\"font-family: Baloo;\">या </span><span style=\"font-family: Times New Roman;\">10 </span><span style=\"font-family: Baloo;\">होना चाहिए क्योंकि</span><span style=\"font-family: Times New Roman;\"> 45 </span><span style=\"font-family: Baloo;\">के बाद</span><span style=\"font-family: Times New Roman;\"> 9 </span><span style=\"font-family: Baloo;\">का निकटतम गुणज</span><span style=\"font-family: Times New Roman;\"> 54 </span><span style=\"font-family: Baloo;\">है।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">x + y =1, x </span><span style=\"font-family: Baloo;\">के लिए </span><span style=\"font-family: Times New Roman;\">1 </span><span style=\"font-family: Baloo;\">होना चाहिए क्योंकि</span><span style=\"font-family: Times New Roman;\"> y </span><span style=\"font-family: Baloo;\">विषम संख्या नहीं हो सकता। और</span><span style=\"font-family: Times New Roman;\"> 780, 8 </span><span style=\"font-family: Baloo;\">से विभाज्य नहीं है इसलिए यह उपेक्षित हो जाएगा।</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">10 </span><span style=\"font-family: Baloo;\">के लिए जोड़े =</span><span style=\"font-family: Times New Roman;\"> (1,9)(2,8), (3,7), (4,6), (5,5), (6,4), (7,3)(8,2)(9 ,1)</span></p>\r\n<p><span style=\"font-family: Baloo;\">केवल युग्म जो इन शर्तों को पूरा करता है वह</span><span style=\"font-family: Times New Roman;\"> (6,4) </span><span style=\"font-family: Baloo;\">है। तो</span></p>\r\n<p><span style=\"font-family: Baloo;\">अभीष्ट मान </span><span style=\"font-family: Times New Roman;\">=</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><mn>6</mn><mi>x</mi><mo>+</mo><mn>7</mn><mi>y</mi><mo>)</mo><mo>&#160;</mo><mo>&#160;</mo><mo>&#160;</mo></msqrt><mo>&#160;</mo><mo>&#160;</mo><mo>=</mo><msqrt><mo>(</mo><mn>6</mn><mo>(</mo><mn>6</mn><mo>)</mo><mo>+</mo><mn>7</mn><mo>(</mo><mn>4</mn><mo>)</mo></msqrt><mo>=</mo><mn>8</mn></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: " <p>23.</span><span style=\"font-family:Times New Roman\"> When 7897, 8110 and 8536 are divided by the greatest number x, then the remainder in each case is the same. The sum of the digits of x is :</span></p> <p><span style=\"font-family:Times New Roman\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: " <p>23.</span><span style=\"font-family:Times New Roman\"> </span><span style=\"font-family:Baloo\">जब </span><span style=\"font-family:Times New Roman\">7897, 8110 </span><span style=\"font-family:Baloo\">और </span><span style=\"font-family:Times New Roman\">8536 </span><span style=\"font-family:Baloo\">को सबसे बड़ी संख्या </span><span style=\"font-family:Times New Roman\">x</span><span style=\"font-family:Baloo\"> से विभाजित किया जाता  है, तो प्रत्येक मामले में </span><span style=\"font-family:Baloo\">शेषफल</span><span style=\"font-family:Baloo\"> समान आता है | </span><span style=\"font-family:Times New Roman\">x </span><span style=\"font-family:Baloo\">के अंकों का जोड़ है : </span></p> <p><span style=\"font-family:Times New Roman\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: [" <p> 14</span></p>", " <p> 5  </span><span style=\"font-family:Times New Roman\">     </span></p>", 
                                " <p> 9</span></p>", " <p> 6</span></p>"],
                    options_hi: [" <p> 14</span></p>", " <p> 5  </span><span style=\"font-family:Times New Roman\">     </span></p>",
                                " <p> 9</span></p>", " <p> 6</span></p>"],
                    solution_en: " <p>23.(d)</span><span style=\"font-family:Times New Roman\"> Let the number be n which divides 7897, 8110 and 8536 leaving a reminder r.</span></p> <p><span style=\"font-family:Times New Roman\">The required number then becomes H.C.F of (7897-r), (8110-r) and (8536-r)</span></p> <p><span style=\"font-family:Times New Roman\">It could also be the H.C.F of </span></p> <p> - (8110 - r) and </span></p> <p> - (7897 - r).</span></p> <p><span style=\"font-family:Times New Roman\">i.e. 426 and 213</span></p> <p><span style=\"font-family:Times New Roman\">H.C.F of 426 and 213 = 213</span></p> <p><span style=\"font-family:Times New Roman\">the required sum = 2+1+3 = 6</span></p>",
                    solution_hi: " <p>23.(d)  </span><span style=\"font-family:Baloo\">मान लीजिए कि संख्या</span><span style=\"font-family:Times New Roman\"> n </span><span style=\"font-family:Baloo\">है जो</span><span style=\"font-family:Times New Roman\"> 7897, 8110 </span><span style=\"font-family:Baloo\">और</span><span style=\"font-family:Times New Roman\"> 8536 </span><span style=\"font-family:Baloo\">को विभाजित करती है और एक शेषफल</span><span style=\"font-family:Times New Roman\"> r </span><span style=\"font-family:Baloo\">छोड़ती है</span></p> <p><span style=\"font-family:Baloo\">तब आवश्यक संख्या</span><span style=\"font-family:Times New Roman\"> (7897-r), (8110-r) </span><span style=\"font-family:Baloo\">और </span><span style=\"font-family:Times New Roman\">(8536-r) </span><span style=\"font-family:Baloo\">का</span><span style=\"font-family:Times New Roman\"> H.C.F </span><span style=\"font-family:Baloo\">बन जाती है।</span></p> <p><span style=\"font-family:Baloo\">यह</span><span style=\"font-family:Times New Roman\"> {(8536 - r) - (8110 - r)} </span><span style=\"font-family:Baloo\">और</span><span style=\"font-family:Times New Roman\"> {(8110-r) - (7897-r)}</span><span style=\"font-family:Baloo\"> का</span><span style=\"font-family:Times New Roman\"> H.C.F </span><span style=\"font-family:Baloo\">भी हो सकता है</span></p> <p><span style=\"font-family:Baloo\">यानी</span><span style=\"font-family:Times New Roman\"> 426 </span><span style=\"font-family:Baloo\">और</span><span style=\"font-family:Times New Roman\"> 213</span></p> <p><span style=\"font-family:Times New Roman\">426 </span><span style=\"font-family:Baloo\">और</span><span style=\"font-family:Times New Roman\"> 213 </span><span style=\"font-family:Baloo\">का</span><span style=\"font-family:Times New Roman\"> H.C.F. = 213</span></p> <p><span style=\"font-family:Baloo\">अभीष्ट योग =</span><span style=\"font-family:Times New Roman\"> 2 + 1 + 3 = 6</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. <span style=\"font-family: Times New Roman;\"> Let a,b and c be the fractions such that </span><span style=\"font-family: Times New Roman;\">. If c is divided by a, the result is</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">, which exceeds b by<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">. If a+b+c = 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">, then (c-a) will be equal to : </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    question_hi: "<p>24.<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">मान लीजिये कि</span><span style=\"font-family: Times New Roman;\"> a, b</span><span style=\"font-family: Baloo;\"> और</span><span style=\"font-family: Times New Roman;\"> c </span><span style=\"font-family: Baloo;\">ऐसे भिन्न हैं कि </span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">है | यदि</span><span style=\"font-family: Times New Roman;\"> c </span><span style=\"font-family: Baloo;\">को </span><span style=\"font-family: Times New Roman;\">a </span><span style=\"font-family: Baloo;\">से विभाजित किया जाए, तो परिणाम </span><span style=\"font-family: Times New Roman;\">5/2</span><span style=\"font-family: Baloo;\"> आता है जो</span><span style=\"font-family: Times New Roman;\"> b</span><span style=\"font-family: Baloo;\"> से <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">अधिक है | यदि </span><span style=\"font-family: Times New Roman;\">a+b+c = 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Baloo;\"> है, तो </span><span style=\"font-family: Times New Roman;\">(c-a) </span><span style=\"font-family: Baloo;\">का मान किसके बराबर होगा ? </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><span style=\"font-family: Times New Roman;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> </span></p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math><span style=\"font-family: Times New Roman;\"> </span></p>", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>"],
                    solution_en: "<p>24.(d)<span style=\"font-family: Times New Roman;\"> Given, </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a+b+c=1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> &hellip;(1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">and&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>c</mi><mi>a</mi></mfrac></math> </span><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Let c = 5 unit and a = 2 unit</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">According to the question</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">b =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Times New Roman;\">- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>4</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Put this value in eq (1)</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a+c = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>12</mn></mfrac><mo>-</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>6</mn></mfrac></math></span></p>\r\n<p>(5+2) unit =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Times New Roman;\">1 unit =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">5 unit =&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2 unit =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">Required difference = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span></p>",
                    solution_hi: "<p>24.(d)<span style=\"font-family: Times New Roman;\"> </span><span style=\"font-family: Baloo;\">दिया गया है,</span><span style=\"font-family: Times New Roman;\"> </span></p>\r\n<p><span style=\"font-family: Times New Roman;\">a+b+c=1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Times New Roman;\"> &hellip;(1)</span></p>\r\n<p><span style=\"font-family: Baloo;\">और</span><span style=\"font-family: Times New Roman;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>c</mi><mi>a</mi></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>2</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">माना</span><span style=\"font-family: Times New Roman;\"> c = 5 </span><span style=\"font-family: Baloo;\">इकाई और</span><span style=\"font-family: Times New Roman;\"> a = 2</span><span style=\"font-family: Baloo;\"> इकाई</span></p>\r\n<p><span style=\"font-family: Baloo;\">प्रश्न के अनुसार,</span></p>\r\n<p><span style=\"font-family: Times New Roman;\">b =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>-</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">इस मान को समीकरण </span><span style=\"font-family: Times New Roman;\">(1) </span><span style=\"font-family: Baloo;\">में रखने पर,</span></p>\r\n<p><span style=\"font-family: Baloo;\">a+c =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>12</mn></mfrac><mo>-</mo><mfrac><mn>7</mn><mn>4</mn></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">(5+2) इकाई</span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">1 </span><span style=\"font-family: Baloo;\">इकाई</span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">5 </span><span style=\"font-family: Baloo;\">इकाई</span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Times New Roman;\">2 </span><span style=\"font-family: Baloo;\">इकाई</span><span style=\"font-family: Times New Roman;\"> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Baloo;\">आवश्यक अंतर =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-weight: 400;\">When 12,16,18,20 and 25 divide the least number x, the remainder in each case is 4 but x is divisible by 7. What is the digit at the thousands&rsquo; place in x ?</span></p>\r\n<p><span style=\"font-weight: 400;\">SSC CGL Tier-2, 11/09/2019</span></p>\r\n<p>&nbsp;</p>",
                    question_hi: "<p><span style=\"font-weight: 400;\">जब </span><span style=\"font-weight: 400;\">12, 16, 18, 20</span><span style=\"font-weight: 400;\"> और</span><span style=\"font-weight: 400;\"> 25 </span><span style=\"font-weight: 400;\">न्यूनतम संख्या</span><span style=\"font-weight: 400;\"> x </span><span style=\"font-weight: 400;\">को विभाजित करते हैं, तो हर मामले में शेषफल</span><span style=\"font-weight: 400;\"> 4 </span><span style=\"font-weight: 400;\">आता है लेकिन</span><span style=\"font-weight: 400;\"> x, 7 </span><span style=\"font-weight: 400;\">से विभाजित है </span><span style=\"font-weight: 400;\">| x</span><span style=\"font-weight: 400;\"> के हजारवें स्थान पर कौन सा अंक है ?&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">SSC CGL Tier-2, 11/09/2019</span></p>",
                    options_en: ["<p>5</p>", "<p>8</p>", 
                                "<p>4</p>", "<p>32</p>"],
                    options_hi: ["<p>5</p>", "<p>8</p>",
                                "<p>4</p>", "<p>32</p>"],
                    solution_en: "<p><span style=\"font-weight: 400;\">[ans]b</span></p>\r\n<p>&nbsp;</p>\r\n<p><strong>[Sol]25.(b)</strong></p>\r\n<p><span style=\"font-weight: 400;\">12 = 2x2x3</span></p>\r\n<p><span style=\"font-weight: 400;\">16 = 2x2x2x2</span></p>\r\n<p><span style=\"font-weight: 400;\">18 = 2x3x3</span></p>\r\n<p><span style=\"font-weight: 400;\">20=2x2x5</span></p>\r\n<p><span style=\"font-weight: 400;\">25 = 5x5</span></p>\r\n<p><span style=\"font-weight: 400;\">LCM of 12,16,18,20 and 25 = 2x2x2x2x3x3x5x5 = 3600</span></p>\r\n<p><span style=\"font-weight: 400;\">x must be = 3600k+4</span></p>\r\n<p><span style=\"font-weight: 400;\">Where 3600k+4 is multiple of 7</span></p>\r\n<p><span style=\"font-weight: 400;\">The condition gets satisfied when k=5</span></p>\r\n<p><span style=\"font-weight: 400;\">Required number = 3600(5)+4 = 18004</span></p>\r\n<p><span style=\"font-weight: 400;\">digit at the thousands&rsquo; place in x =8</span></p>",
                    solution_hi: "<p><span style=\"font-weight: 400;\">[ans]b</span></p>\r\n<p>&nbsp;</p>\r\n<p><strong>[Sol]25.(b)</strong></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>12</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn><mspace linebreak=\"newline\"/><mo>&#160;</mo><mn>16</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>18</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>3</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>20</mn><mo>=</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>5</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>25</mn><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mn>12</mn><mo>,</mo><mn>16</mn><mo>,</mo><mn>18</mn><mo>,</mo><mn>20</mn><mo>&#160;</mo><mi>&#2324;&#2352;</mi><mo>&#160;</mo><mn>25</mn><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>L</mi><mi>C</mi><mi>M</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>=</mo><mo>&#160;</mo><mn>3600</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>x</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>3600</mn><mi>k</mi><mo>+</mo><mn>4</mn><mo>&#160;</mo><mspace linebreak=\"newline\"/><mi>d</mi><mi>i</mi><mi>g</mi><mi>i</mi><mi>t</mi><mo>&#160;</mo><mi>a</mi><mi>t</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>e</mi><mo>&#160;</mo><mi>t</mi><mi>h</mi><mi>o</mi><mi>u</mi><mi>s</mi><mi>a</mi><mi>n</mi><mi>d</mi><mi>s</mi><mo>&#8217;</mo><mo>&#160;</mo><mi>p</mi><mi>l</mi><mi>a</mi><mi>c</mi><mi>e</mi><mo>&#160;</mo><mi>i</mi><mi>n</mi><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mn>8</mn></math></p>\r\n<p><span style=\"font-weight: 400;\">जहाँ</span><span style=\"font-weight: 400;\"> 3600k + 4, 7 </span><span style=\"font-weight: 400;\">का गुणज है</span></p>\r\n<p><span style=\"font-weight: 400;\">स्थिति संतुष्ट हो जाती है जब</span><span style=\"font-weight: 400;\"> k = 5</span></p>\r\n<p><span style=\"font-weight: 400;\">आवश्यक संख्या</span><span style=\"font-weight: 400;\"> = 3600(5)+4 = 18004</span></p>\r\n<p><span style=\"font-weight: 400;\"> x&nbsp; </span><span style=\"font-weight: 400;\">में हज़ार के स्थान पर अंक </span><span style=\"font-weight: 400;\">= 8</span></p>\r\n<p>&nbsp;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>