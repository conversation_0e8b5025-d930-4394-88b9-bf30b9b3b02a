<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">60:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Which of the following numbers will replace the question marks (?) in the given series ?<br>16, 36, 64, ?, ?, 196, 256, 324</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन-सी संख्याएँ दी गई शृंखला में प्रश्न-चिह्न(?) का स्थान लेंगी ?<br>16, 36, 64, ?, ?, 196, 256, 324</p>",
                    options_en: [
                        "<p>81, 121</p>",
                        "<p>100, 121</p>",
                        "<p>100, 144</p>",
                        "<p>81, 100</p>"
                    ],
                    options_hi: [
                        "<p>81, 121</p>",
                        "<p>100, 121</p>",
                        "<p>100, 144</p>",
                        "<p>81, 100</p>"
                    ],
                    solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699454402.png\" alt=\"rId4\" width=\"296\" height=\"66\"></p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699454402.png\" alt=\"rId4\" width=\"296\" height=\"66\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Q is the mother of T. R is the husband of P. S is the father of Q . T is the brother of P. How is P related to S ?</p>",
                    question_hi: "<p>2. Q, T की माता है। R, P का पति है। S, Q का पिता है। T, P का भाई है। P, S से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        "<p>Daughter&rsquo;s daughter</p>",
                        "<p>Mother&rsquo;s mother</p>",
                        "<p>Mother&rsquo;s father</p>",
                        "<p>Brother&rsquo;s wife</p>"
                    ],
                    options_hi: [
                        "<p>नातिन</p>",
                        "<p>नानी</p>",
                        "<p>नाना</p>",
                        "<p>भाभी</p>"
                    ],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699454543.png\" alt=\"rId5\" width=\"193\" height=\"126\"><br>Hence &lsquo;P&rsquo; is the daughter&rsquo;s daughter of &lsquo;S&rsquo;.</p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699454543.png\" alt=\"rId5\" width=\"193\" height=\"126\"><br>अतः \'P\', \'S\' की नातिन है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster. <br>MEK : NVP :: GYT : TBG :: JWH : ?</p>",
                    question_hi: "<p>3. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है। <br>MEK : NVP :: GYT : TBG :: JWH : ?</p>",
                    options_en: [
                        "<p>QDS</p>",
                        "<p>QDT</p>",
                        "<p>PCR</p>",
                        "<p>PCS</p>"
                    ],
                    options_hi: [
                        "<p>QDS</p>",
                        "<p>QDT</p>",
                        "<p>PCR</p>",
                        "<p>PCS</p>"
                    ],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699454659.png\" alt=\"rId6\" width=\"126\" height=\"103\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699454936.png\" alt=\"rId7\" width=\"125\" height=\"101\"><br>Similarly,&nbsp; &nbsp;</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455126.png\" alt=\"rId8\" width=\"136\" height=\"102\"></p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455229.png\" alt=\"rId9\" width=\"132\" height=\"111\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455325.png\" alt=\"rId10\" width=\"132\" height=\"110\"></p>\n<p>इसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455465.png\" alt=\"rId11\" width=\"145\" height=\"112\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the figure from among the given options that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455638.png\" alt=\"rId12\" width=\"318\" height=\"66\"></p>",
                    question_hi: "<p>4. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो नीचे दी गई श्रृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सके।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455638.png\" alt=\"rId12\" width=\"318\" height=\"66\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455843.png\" alt=\"rId13\" width=\"76\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455952.png\" alt=\"rId14\" width=\"76\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456061.png\" alt=\"rId15\" width=\"76\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456158.png\" alt=\"rId16\" width=\"77\" height=\"76\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455843.png\" alt=\"rId13\" width=\"76\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455952.png\" alt=\"rId14\" width=\"76\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456061.png\" alt=\"rId15\" width=\"76\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456158.png\" alt=\"rId16\" width=\"76\" height=\"75\"></p>"
                    ],
                    solution_en: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455952.png\" alt=\"rId14\" width=\"76\" height=\"76\"></p>",
                    solution_hi: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699455952.png\" alt=\"rId14\" width=\"76\" height=\"76\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Which two signs should be interchanged to make the given equation correct ?<br>152 + 8 &times; 16 &divide; 9 &ndash; 4 = 309</p>",
                    question_hi: "<p>5. दिए गए समीकरण को संतुलित करने के लिए किन दो चिह्नों को आपस में बदला जाना चाहिए ?<br>152 + 8 &times; 16 &divide; 9 &ndash; 4 = 309</p>",
                    options_en: [
                        "<p>&divide; and &ndash;</p>",
                        "<p>&divide; and +</p>",
                        "<p>&ndash; and +</p>",
                        "<p>+ and &times;</p>"
                    ],
                    options_hi: [
                        "<p>&divide; और &ndash;</p>",
                        "<p>&divide; और +</p>",
                        "<p>&ndash; और +</p>",
                        "<p>+ और &times;</p>"
                    ],
                    solution_en: "<p>5.(b)<br><strong>Given :-</strong> 152 + 8 &times; 16 &divide; 9 - 4 = 309<br>After going through all the options, option b satisfied. After interchanging &divide; and + we get<br>&rArr; 152 &divide; 8 &times; 16 + 9 - 4<br>&rArr; 19 &times; 16 + 5<br>&rArr; 304 + 5 = 309<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>5.(b)<br><strong>दिया गया है:-</strong> 152 + 8 &times; 16 &divide; 9 - 4 = 309<br>सभी विकल्पों की जांच करने पर विकल्प b संतुष्ट हो गया। &divide; और + को आपस में बदलने के बाद हमें प्राप्त होता है<br>&rArr; 152 &divide; 8 &times; 16 + 9 - 4<br>&rArr; 19 &times; 16 + 5<br>&rArr; 304 + 5 = 309<br>L.H.S. = R.H.S.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language, &lsquo;play in the sunshine&rsquo; is coded as &lsquo;sh ko zm fq&rsquo; and &lsquo;the sunshine is bright&rsquo; is coded as &lsquo;ko sh gp rs&rsquo;. What is the code for &lsquo;sunshine&rsquo; in that language ?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में, \'play in the sunshine\' को \'sh ko zm fq\' के रूप में कूटबद्ध किया जाता है और \'the sunshine is bright\' को \'ko sh gp rs\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'sunshine\' के लिए संभावित कूट क्या है ?</p>",
                    options_en: [
                        "<p>either &lsquo;gp&rsquo; or &lsquo;sh&rsquo;</p>",
                        "<p>ko</p>",
                        "<p>zm</p>",
                        "<p>either &lsquo;sh&rsquo; or &lsquo;ko</p>"
                    ],
                    options_hi: [
                        "<p>either &lsquo;gp&rsquo; or &lsquo;sh&rsquo;</p>",
                        "<p>ko</p>",
                        "<p>zm</p>",
                        "<p>either &lsquo;sh&rsquo; or &lsquo;ko</p>"
                    ],
                    solution_en: "<p>6.(d) &lsquo;play in the sunshine&rsquo; &rarr;&nbsp;&lsquo;sh ko zm fq&rsquo; &hellip;&hellip;&hellip;.. (i)<br>&lsquo;the sunshine is bright&rsquo; &rarr; &lsquo;ko sh gp rs&rsquo; &hellip;&hellip;&hellip;&hellip; (ii)<br>In equation (i) and (ii), &lsquo;the sunshine&rsquo; is common and code &lsquo;ko sh&rsquo; is also common.<br>So, the code of &lsquo;sunshine&rsquo; is &lsquo;either ko or sh&rsquo;.</p>",
                    solution_hi: "<p>6.(d) &lsquo;play in the sunshine&rsquo; &rarr; &lsquo;sh ko zm fq&rsquo; &hellip;&hellip;&hellip;.. (i)<br>&lsquo;the sunshine is bright&rsquo; &rarr; &lsquo;ko sh gp rs&rsquo; &hellip;&hellip;&hellip;&hellip; (ii)<br>समीकरण (i) और (ii) में, &lsquo;the sunshine&rsquo; उभयनिष्ठ है और कोड &lsquo;ko sh&rsquo; भी उभयनिष्ठ है.<br>तो, \'sunshine&rsquo; का कोड \'या तो ko या sh\' है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language,<br>X + Y means &lsquo;X is the father of Y&rsquo;,<br>X &minus; Y means &lsquo;X is the brother of Y&rsquo;,<br>X &times; Y means &lsquo;X is the sister of Y&rsquo;,<br>X &divide; Y means &lsquo;X is the wife of Y&rsquo;.<br>Based on the above, how is D related to H if &lsquo;D &times; E &divide; F + G &minus; H&rsquo; ?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में,<br>X + Y का अर्थ है \'X, Y का पिता है\',<br>X &minus; Y का अर्थ है \'X, Y का भाई है\',<br>X &times; Y का अर्थ है \'X, Y की बहन है\',<br>X &divide; Y का अर्थ है \'X, Y की पत्नी है\'।<br>उपर्युक्त के आधार पर, यदि \'D &times; E &divide; F + G &minus; H\' है, तो D, H से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        "<p>Sister</p>",
                        "<p>Father&rsquo;s sister</p>",
                        "<p>Mother&rsquo;s sister</p>",
                        "<p>Mother&rsquo;s mother</p>"
                    ],
                    options_hi: [
                        "<p>बहन</p>",
                        "<p>पिता की बहन</p>",
                        "<p>माता की बहन</p>",
                        "<p>माता की माता</p>"
                    ],
                    solution_en: "<p>7.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456298.png\" alt=\"rId17\" width=\"187\" height=\"129\"><br>D is the sister of H&rsquo;s mother.</p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456298.png\" alt=\"rId17\" width=\"187\" height=\"129\"><br>D, H की माता की बहन है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?</p>",
                    question_hi: "<p>8. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा ?<br>1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?</p>",
                    options_en: [
                        "<p>1974</p>",
                        "<p>1749</p>",
                        "<p>1947</p>",
                        "<p>1497</p>"
                    ],
                    options_hi: [
                        "<p>1974</p>",
                        "<p>1749</p>",
                        "<p>1947</p>",
                        "<p>1497</p>"
                    ],
                    solution_en: "<p>8.(b) <strong>Given: </strong>1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?<br>As per the instructions given in the question, after interchanging the symbols &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get.<br>1064 &divide; 2 + 653 &times; 2 - 89 = ?<br>532 + 653 &times; 2 - 89 <br>532 + 1306 - 89<br>1838 - 89 =1749</p>",
                    solution_hi: "<p>8.(b) <strong>दिया गया है:</strong> 1064 &times; 2 &ndash; 653 &divide; 2 + 89 = ?<br>प्रश्न में दिए गए निर्देशों के अनुसार, प्रतीकों \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है।<br>1064 &divide; 2 + 653 &times; 2 - 89 = ?<br>532 + 653 &times; 2 - 89 <br>532 + 1306 - 89<br>1838 - 89 =1749</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter- cluster that is different.<br><strong>Note :</strong> The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.</p>",
                    question_hi: "<p>9. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। उस अक्षर-समूह का चयन करें जो भिन्न हो।<br><strong>नोट: </strong>अक्षर समूह में, भिन्न व्यंजनों/नोंस्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है। .</p>",
                    options_en: [
                        "<p>LKF</p>",
                        "<p>YXT</p>",
                        "<p>NMI</p>",
                        "<p>SRN</p>"
                    ],
                    options_hi: [
                        "<p>LKF</p>",
                        "<p>YXT</p>",
                        "<p>NMI</p>",
                        "<p>SRN</p>"
                    ],
                    solution_en: "<p>9.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456420.png\" alt=\"rId18\" width=\"115\" height=\"75\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456569.png\" alt=\"rId19\" width=\"122\" height=\"76\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456685.png\" alt=\"rId20\" width=\"124\" height=\"74\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456770.png\" alt=\"rId21\" width=\"118\" height=\"76\"></p>",
                    solution_hi: "<p>9.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456420.png\" alt=\"rId18\" width=\"115\" height=\"75\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456569.png\" alt=\"rId19\" width=\"122\" height=\"76\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456685.png\" alt=\"rId20\" width=\"124\" height=\"74\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456770.png\" alt=\"rId21\" width=\"118\" height=\"76\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456946.png\" alt=\"rId22\" width=\"187\" height=\"182\"></p>",
                    question_hi: "<p>10. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699456946.png\" alt=\"rId22\" width=\"187\" height=\"182\"></p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>6</p>",
                        "<p>9</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>6</p>",
                        "<p>9</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457108.png\" alt=\"rId23\" width=\"179\" height=\"170\"><br>There are 7 triangles<br>ACB , CED, MJI, DGN, GEF, MCD, NHI</p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457108.png\" alt=\"rId23\" width=\"179\" height=\"170\"><br>7 त्रिभुज हैं<br>ACB , CED, MJI, DGN, GEF, MCD, NHI</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. The position of how many letters will remain unchanged if all the letters in the word MOLTEN are arranged in English alphabetical order ?</p>",
                    question_hi: "<p>11. यदि MOLTEN शब्द के सभी अक्षरों को अंग्रेजी वर्णानुक्रम में व्यवस्थित किया जाए, तो कितने अक्षरों की स्थिति अपरिवर्तित बनी रहेगी ?</p>",
                    options_en: [
                        "<p>One</p>",
                        "<p>Two</p>",
                        "<p>None</p>",
                        "<p>Three</p>"
                    ],
                    options_hi: [
                        "<p>एक</p>",
                        "<p>दो</p>",
                        "<p>किसी की भी नहीं</p>",
                        "<p>तीन</p>"
                    ],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457226.png\" alt=\"rId24\" width=\"202\" height=\"91\"><br>Hence, the position of all letters will be changed.</p>",
                    solution_hi: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457226.png\" alt=\"rId24\" width=\"202\" height=\"91\"><br>अतः , सभी अक्षरों की स्थिति बदल दी जाएगी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457326.png\" alt=\"rId25\" width=\"92\" height=\"131\"></p>",
                    question_hi: "<p>12. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457326.png\" alt=\"rId25\" width=\"92\" height=\"131\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457470.png\" alt=\"rId26\" width=\"112\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457650.png\" alt=\"rId27\" width=\"112\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457773.png\" alt=\"rId28\" width=\"112\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457950.png\" alt=\"rId29\" width=\"110\" height=\"24\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457470.png\" alt=\"rId26\" width=\"112\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457650.png\" alt=\"rId27\" width=\"112\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457773.png\" alt=\"rId28\" width=\"112\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457950.png\" alt=\"rId29\" width=\"110\" height=\"24\"></p>"
                    ],
                    solution_en: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457470.png\" alt=\"rId26\" width=\"112\" height=\"23\"></p>",
                    solution_hi: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699457470.png\" alt=\"rId26\" width=\"112\" height=\"23\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Which of the following terms will replace the question mark (?) in the given series ?&nbsp;<br>ABNI, DAQH, ?, JYWF</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगा ?<br>ABNI, DAQH, ?, JYWF</p>",
                    options_en: [
                        " GZTG  ",
                        " TGZG ",
                        " CAYG ",
                        " GTZG"
                    ],
                    options_hi: [
                        " GZTG  ",
                        " TGZG ",
                        " CAYG ",
                        " GTZG"
                    ],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458173.png\" alt=\"rId30\" width=\"308\" height=\"125\"></p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458173.png\" alt=\"rId30\" width=\"308\" height=\"125\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(173, 246)<br>(148, 196)<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>14. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित समुच्चयों की संख्याएँ संबंधित हैं।<br>(173, 246)<br>(148, 196)<br>(<strong>नोट :</strong> पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>(142, 176)</p>",
                        "<p>(169, 247)</p>",
                        "<p>(127, 194)</p>",
                        "<p>(154, 208)</p>"
                    ],
                    options_hi: [
                        "<p>(142, 176)</p>",
                        "<p>(169, 247)</p>",
                        "<p>(127, 194)</p>",
                        "<p>(154, 208)</p>"
                    ],
                    solution_en: "<p>14.(d) <strong>Logic :-</strong> (1st number) + (1st number - 100) = 2nd number<br>(173 , 246) :- (173) + (173 - 100) &rArr; (173) + (73) = 246<br>(148, 196) :- (148) + (148 - 100) &rArr; (148) + (48) = 196<br>Similarly,<br>(154, 208) :- (154) + (154 - 100) &rArr; (154) + (54) = 208</p>",
                    solution_hi: "<p>14.(d) <strong>तर्क :-</strong> (पहली संख्या) + (पहली संख्या - 100) = दूसरी संख्या <br>(173 , 246) :- (173) + (173 - 100) &rArr; (173) + (73) = 246<br>(148, 196) :- (148) + (148 - 100) &rArr; (148) + (48) = 196<br>इसी प्रकार,<br>(154, 208) :- (154) + (154 - 100) &rArr; (154) + (54) = 208</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group ?<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into<br>1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>15. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है ?<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें- संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>26 &ndash; 95 &ndash; 21</p>",
                        "<p>22 &ndash; 67 &ndash; 22</p>",
                        "<p>20 &ndash; 15 &ndash; 51</p>",
                        "<p>16 &ndash; 45 &ndash; 21</p>"
                    ],
                    options_hi: [
                        "<p>26 &ndash; 95 &ndash; 21</p>",
                        "<p>22 &ndash; 67 &ndash; 22</p>",
                        "<p>20 &ndash; 15 &ndash; 51</p>",
                        "<p>16 &ndash; 45 &ndash; 21</p>"
                    ],
                    solution_en: "<p>15.(b) <strong>Logic :-</strong> The sum of all the numbers in all the options except (b) is even.<br>(a) 26 &ndash; 95 &ndash; 21 &rArr; 26 + 95 + 21 = 142<br>(b) 22 &ndash; 67 &ndash; 22 &rArr; 22 + 67 + 22 = 111<br>(c) 20 &ndash; 15 &ndash; 51 &rArr; 20 + 15 + 51 = 86<br>(d) 16 &ndash; 45 &ndash; 21 &rArr; 16 + 45 + 21 = 82</p>",
                    solution_hi: "<p>15.(b)<strong> तर्क:- </strong>(B) को छोड़कर सभी विकल्पों में सभी संख्याओं का योग सम है।<br>(a) 26 &ndash; 95 &ndash; 21 &rArr; 26 + 95 + 21 = 142<br>(b) 22 &ndash; 67 &ndash; 22 &rArr; 22 + 67 + 22 = 111<br>(c) 20 &ndash; 15 &ndash; 51 &rArr; 20 + 15 + 51 = 86<br>(d) 16 &ndash; 45 &ndash; 21 &rArr; 16 + 45 + 21 = 82</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Which of the following numbers will replace the question mark (?) in the given series ? <br>168, 191, 218, 249, 284, ?</p>",
                    question_hi: "<p>16. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी ?<br>168, 191, 218, 249, 284, ?</p>",
                    options_en: [
                        "<p>333</p>",
                        "<p>323</p>",
                        "<p>319</p>",
                        "<p>329</p>"
                    ],
                    options_hi: [
                        "<p>333</p>",
                        "<p>323</p>",
                        "<p>319</p>",
                        "<p>329</p>"
                    ],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458337.png\" alt=\"rId31\" width=\"232\" height=\"79\"></p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458337.png\" alt=\"rId31\" width=\"232\" height=\"79\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement. <br><strong>Statements :</strong> <br>All ground is wind. <br>Some tables are ground. <br>All wind is earth. <br><strong>Conclusion (I) : </strong>No tables are earth. <br><strong>Conclusion (II) :</strong> All earth is ground.</p>",
                    question_hi: "<p>17. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहेवे समान्यतः ज्ञात तथ्यों सेअलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं। <br><strong>कथन :</strong> <br>सभी मैदान, पवन हैं। <br>कुछ मेजें, मैदान हैं। <br>सभी पवन, पृथ्वी है। <br><strong>निष्कर्ष (I) : </strong>कोई मेजें, पृथ्वी नहीं हैं। <br><strong>निष्कर्ष (II) : </strong>सभी पृथ्वी , मैदान है।</p>",
                    options_en: [
                        " Only conclusion (II) follows.  ",
                        " Only conclusion (I) follows.",
                        " Both conclusions (I) and (II) follow.  ",
                        " Neither conclusion (I) nor (II) follows."
                    ],
                    options_hi: [
                        " केवल निष्कर्ष (II) कथनों के अनुसार है।  ",
                        " केवल निष्कर्ष (I) कथनों के अनुसार है। ",
                        "  दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं। ",
                        " न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।"
                    ],
                    solution_en: "<p>17.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458448.png\" alt=\"rId32\" width=\"328\" height=\"105\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>17.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458566.png\" alt=\"rId33\" width=\"390\" height=\"124\"><br>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Select the correct mirror image of the given figure when the mirror is placed at MN as shown. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458682.png\" alt=\"rId34\" width=\"113\" height=\"118\"></p>",
                    question_hi: "<p>18. दर्पण को MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिम्ब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458682.png\" alt=\"rId34\" width=\"113\" height=\"118\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458786.png\" alt=\"rId35\" width=\"77\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458906.png\" alt=\"rId36\" width=\"78\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459052.png\" alt=\"rId37\" width=\"82\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459163.png\" alt=\"rId38\" width=\"78\" height=\"75\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458786.png\" alt=\"rId35\" width=\"77\" height=\"80\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699458906.png\" alt=\"rId36\" width=\"78\" height=\"81\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459052.png\" alt=\"rId37\" width=\"77\" height=\"72\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459163.png\" alt=\"rId38\" width=\"78\" height=\"75\"></p>"
                    ],
                    solution_en: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459052.png\" alt=\"rId37\" width=\"81\" height=\"76\"></p>",
                    solution_hi: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459052.png\" alt=\"rId37\" width=\"81\" height=\"76\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the option in which the given figure is embedded. (Rotation is NOT allowed.)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459273.png\" alt=\"rId39\" width=\"88\" height=\"82\"></p>",
                    question_hi: "<p>19. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति सन्निहित है। (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459273.png\" alt=\"rId39\" width=\"88\" height=\"82\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459411.png\" alt=\"rId40\" width=\"96\" height=\"56\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459543.png\" alt=\"rId41\" width=\"81\" height=\"110\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459649.png\" alt=\"rId42\" width=\"103\" height=\"56\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459774.png\" alt=\"rId43\" width=\"74\" height=\"119\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459411.png\" alt=\"rId40\" width=\"96\" height=\"56\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459543.png\" alt=\"rId41\" width=\"81\" height=\"110\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459649.png\" alt=\"rId42\" width=\"103\" height=\"56\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459774.png\" alt=\"rId43\" width=\"74\" height=\"119\"></p>"
                    ],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459900.png\" alt=\"rId44\" width=\"71\" height=\"123\"></p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699459900.png\" alt=\"rId44\" width=\"71\" height=\"123\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. &lsquo;WISK&rsquo; is related to &lsquo;DRHP&rsquo; in a certain way based on the English alphabetical order. In the same way, &lsquo;LENT&rsquo; is related to &lsquo;OVMG&rsquo;. To which of the following is &lsquo;BANG&rsquo; related, following the same logic ?</p>",
                    question_hi: "<p>20. अँग्रेजी वर्णमाला-क्रम के आधार पर &lsquo;WISK&rsquo; एक निश्चित तरीके से &lsquo;DRHP&rsquo; से संबंधित है। उसी तरह &lsquo;LENT&rsquo; का संबंध &lsquo;OVMG&rsquo; से है। उसी तर्क के अनुसार &lsquo;BANG&rsquo; का संबंध निम्नलिखित में से किससे है ?</p>",
                    options_en: [
                        "<p>ZYLU</p>",
                        "<p>ZYNS</p>",
                        "<p>YZNU</p>",
                        "<p>YZMT</p>"
                    ],
                    options_hi: [
                        "<p>ZYLU</p>",
                        "<p>ZYNS</p>",
                        "<p>YZNU</p>",
                        "<p>YZMT</p>"
                    ],
                    solution_en: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699460035.png\" alt=\"rId45\" width=\"121\" height=\"152\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699460150.png\" alt=\"rId46\" width=\"123\" height=\"151\"><br>Similarly,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699460245.png\" alt=\"rId47\" width=\"129\" height=\"152\"></p>",
                    solution_hi: "<p>20.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699460385.png\" alt=\"rId48\" width=\"130\" height=\"155\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699460478.png\" alt=\"rId49\" width=\"142\" height=\"160\"><br>इसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699460614.png\" alt=\"rId50\" width=\"125\" height=\"153\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. In a certain code language, BRING is coded as &lsquo;7149182&rsquo; and SMILE is coded as &lsquo;51291319&rsquo;. What will be the code for FORGIVE ?</p>",
                    question_hi: "<p>21. एक निश्चित कूट भाषा में, BRING को \'7149182\' कूटबद्ध किया जाता है और SMILE को \'51291319\' कूटबद्ध किया जाता है। उसी कूट भाषा में FORGIVE के लिए कूट क्या होगा ?</p>",
                    options_en: [
                        "<p>5229179156</p>",
                        "<p>5228717156</p>",
                        "<p>5219717166</p>",
                        "<p>5229718156</p>"
                    ],
                    options_hi: [
                        "<p>5229179156</p>",
                        "<p>5228717156</p>",
                        "<p>5219717166</p>",
                        "<p>5229718156</p>"
                    ],
                    solution_en: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699460807.png\" alt=\"rId51\" width=\"127\" height=\"111\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699460960.png\" alt=\"rId52\" width=\"137\" height=\"114\"><br>Similarly,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699461151.png\" alt=\"rId53\" width=\"237\" height=\"147\"></p>",
                    solution_hi: "<p>21.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699460807.png\" alt=\"rId51\" width=\"127\" height=\"111\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699460960.png\" alt=\"rId52\" width=\"137\" height=\"114\"></p>\n<p>उसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699461151.png\" alt=\"rId53\" width=\"237\" height=\"147\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series.<br>q p _ r _ r q _ p _ r p _ q _ p r _ p _ q</p>",
                    question_hi: "<p>22. अक्षरों के उस संयोजन का चयन कीजिए, जिसके अक्षरों को दी गई शृंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर शृंखला पूर्ण हो जाएगी।<br>q p _ r _ r q _ p _ r p _ q _ p r _ p _ q</p>",
                    options_en: [
                        "<p>r p q r r p r q</p>",
                        "<p>r p q r r q r r</p>",
                        "<p>r p q q r q r p</p>",
                        "<p>r p q r r q r p</p>"
                    ],
                    options_hi: [
                        "<p>r p q r r p r q</p>",
                        "<p>r p q r r q r r</p>",
                        "<p>r p q q r q r p</p>",
                        "<p>r p q r r q r p</p>"
                    ],
                    solution_en: "<p>22.(b)<br>q p <span style=\"text-decoration: underline;\"><strong>r</strong></span> r <span style=\"text-decoration: underline;\"><strong>p</strong></span> r q/ <span style=\"text-decoration: underline;\"><strong>q</strong></span> p <span style=\"text-decoration: underline;\"><strong>r</strong></span> r p <span style=\"text-decoration: underline;\"><strong>r</strong></span> q/ <span style=\"text-decoration: underline;\"><strong>q</strong></span> p r <span style=\"text-decoration: underline;\"><strong>r</strong></span> p <span style=\"text-decoration: underline;\"><strong>r</strong></span> q</p>",
                    solution_hi: "<p>22.(b)<br>q p <span style=\"text-decoration: underline;\"><strong>r</strong></span> r <span style=\"text-decoration: underline;\"><strong>p</strong></span> r q/ <span style=\"text-decoration: underline;\"><strong>q</strong></span> p <span style=\"text-decoration: underline;\"><strong>r</strong></span> r p <span style=\"text-decoration: underline;\"><strong>r</strong></span> q/ <span style=\"text-decoration: underline;\"><strong>q</strong></span> p r <span style=\"text-decoration: underline;\"><strong>r</strong></span> p <span style=\"text-decoration: underline;\"><strong>r</strong></span> q</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. 16 is related to 30 following a certain logic. Following the same logic, 81 is related to 110. To which of the following is 49 related, following the same logic ?&nbsp;<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>23. एक निश्चित तर्क के अनुसार 16 का संबंध 30 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 81 का संबंध 110 से है। उसी तर्क का अनुसरण करते हुए, 49 का संबंध निम्नलिखित में से किससे है ?&nbsp;<br>(<strong>नोट: </strong>संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- - 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>64</p>",
                        "<p>60</p>",
                        "<p>72</p>",
                        "<p>90</p>"
                    ],
                    options_hi: [
                        "<p>64</p>",
                        "<p>60</p>",
                        "<p>72</p>",
                        "<p>90</p>"
                    ],
                    solution_en: "<p>23.(c)<strong> Logic:</strong> (<math display=\"inline\"><msqrt><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>1</mn><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></msqrt></math> + 2) = 2<sup>nd</sup>no.<br>(16&nbsp;: 30) :- (<math display=\"inline\"><msqrt><mn>16</mn></msqrt></math> + 1) &times; (<math display=\"inline\"><msqrt><mn>16</mn></msqrt></math> + 2) &rArr; 5 &times; 6 = 30<br>(81 : 110) :- (<math display=\"inline\"><msqrt><mn>81</mn></msqrt></math> + 1) &times; (<math display=\"inline\"><msqrt><mn>81</mn></msqrt></math> + 2) &rArr; 10 &times; 11 = 110<br>Similarly<br>(49 : x) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 2) &rArr; 8 &times; 9 = 72</p>",
                    solution_hi: "<p>23.(c)<strong> तर्क : </strong>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></msqrt></math> + 1) &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></msqrt></math> + 2) = दूसरी संख्या <br>(16&nbsp;: 30) :- (<math display=\"inline\"><msqrt><mn>16</mn></msqrt></math> + 1) &times; (<math display=\"inline\"><msqrt><mn>16</mn></msqrt></math> + 2) &rArr; 5 &times; 6 = 30<br>(81 : 110) :- (<math display=\"inline\"><msqrt><mn>81</mn></msqrt></math> + 1) &times; (<math display=\"inline\"><msqrt><mn>81</mn></msqrt></math> + 2) &rArr; 10 &times; 11 = 110<br>उसी प्रकार<br>(49 : x) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 2) &rArr; 8 &times; 9 = 72</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Three statements are followed by three conclusions numbered I, II and III. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statements.<br><strong>Statements :</strong><br>All pens are pages.<br>Some pages are pins.<br>Some pins are boards.<br><strong>Conclusions :</strong><br>(I) Some pins are pens.<br>(II) Some boards are pages.<br>(III) Some pens are boards.</p>",
                    question_hi: "<p>24. तीन कथनों के बाद तीन निष्कर्ष I, II और III दिए गए हैं। आपको इन कथनों को सत्य मानना है, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों। हों निर्धारित करें कि दिए गए निष्कर्षों में से कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>सभी पेन, पेज हैं।<br>कुछ पेज, पिन हैं।<br>कुछ पिन, बोर्ड हैं।<br><strong>निष्कर्ष :</strong><br>(I) कुछ पिन, पेन हैं।<br>(II) कुछ बोर्ड, पेज हैं।<br>(III) कुछ पेन, बोर्ड हैं।</p>",
                    options_en: [
                        "<p>Only conclusion I follows.</p>",
                        "<p>None of the conclusions follow.</p>",
                        "<p>Only conclusion II follows.</p>",
                        "<p>Either conclusion I or conclusion III follows.</p>"
                    ],
                    options_hi: [
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>",
                        "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है।</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है।</p>",
                        "<p>या तो निष्कर्ष I अनुसरण करता है या निष्कर्ष III अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699462005.png\" alt=\"rId57\" width=\"250\" height=\"63\"><br>None of the conclusion follow.</p>",
                    solution_hi: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699462175.png\" alt=\"rId58\" width=\"237\" height=\"58\"><br>कोई भी निष्कर्ष अनुसरण नहीं करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Which figure should replace the question mark (?) if the following series were to be continued ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699462443.png\" alt=\"rId59\" width=\"356\" height=\"73\"></p>",
                    question_hi: "<p>25. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699462443.png\" alt=\"rId59\" width=\"356\" height=\"73\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699462694.png\" alt=\"rId60\" width=\"78\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699462848.png\" alt=\"rId61\" width=\"78\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699462983.png\" alt=\"rId62\" width=\"78\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699463126.png\" alt=\"rId63\" width=\"78\" height=\"78\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699463371.png\" alt=\"rId65\" width=\"78\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699463495.png\" alt=\"rId66\" width=\"78\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699463738.png\" alt=\"rId67\" width=\"78\" height=\"78\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699463887.png\" alt=\"rId68\" width=\"78\" height=\"78\"></p>"
                    ],
                    solution_en: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699463887.png\" alt=\"rId68\" width=\"79\" height=\"79\"></p>",
                    solution_hi: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699463887.png\" alt=\"rId68\" width=\"79\" height=\"79\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Microsoft introduced which quantum chip features topological qubits for enhanced stability ?</p>",
                    question_hi: "<p>26. माइक्रोसॉफ्ट (Microsoft) ने किस क्वांटम चिप (Quantum Chip) को लॉन्च किया, जिसमें बेहतर स्थिरता (Enhanced Stability) के लिए टोपोलॉजिकल क्यूबिट्स (Topological Qubits) हैं ?</p>",
                    options_en: [
                        "<p>Sycamore</p>",
                        "<p>Eagle</p>",
                        "<p>Majorana 1</p>",
                        "<p>Borealis</p>"
                    ],
                    options_hi: [
                        "<p>सायकामोर (Sycamore)</p>",
                        "<p>ईगल (Eagle)</p>",
                        "<p>मेजोराना 1 (Majorana 1)</p>",
                        "<p>बोरेलिस (Borealis)</p>"
                    ],
                    solution_en: "<p>26.(c) <strong>Majorana 1.</strong> Microsoft&rsquo;s quantum chip uses topological qubits for enhanced stability, reducing errors and improving scalability. It supports millions of qubits, making large-scale quantum computing more feasible.</p>",
                    solution_hi: "<p>26.(c) <strong>मेजोराना 1 (Majorana 1) ।</strong><br>माइक्रोसॉफ्ट की Majorana 1 क्वांटम चिप टोपोलॉजिकल क्यूबिट्स का उपयोग करके अधिक स्थिरता प्रदान करती है, जिससे त्रुटियों में कमी आती है और स्केलेबिलिटी में सुधार होता है। यह लार्ज-स्केल क्वांटम कंप्यूटिंग को संभव बनाती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which of the following is a correct order of basicity ?</p>",
                    question_hi: "<p>27. निम्नलिखित में से कौन-सा क्षारकता का सही क्रम है ?</p>",
                    options_en: [
                        "<p>LiOH &gt; NaOH &gt; KOH &gt; CsOH</p>",
                        "<p>LiOH &gt; KOH &gt; CsOH &gt; NaOH</p>",
                        "<p>KOH &gt; CsOH &gt; NaOH &gt; LiOH</p>",
                        "<p>CsOH &gt; KOH &gt; NaOH &gt; LiOH</p>"
                    ],
                    options_hi: [
                        "<p>LiOH &gt; NaOH &gt; KOH &gt; CsOH</p>",
                        "<p>LiOH &gt; KOH &gt; CsOH &gt; NaOH</p>",
                        "<p>KOH &gt; CsOH &gt; NaOH &gt; LiOH</p>",
                        "<p>CsOH &gt; KOH &gt; NaOH &gt; LiOH</p>"
                    ],
                    solution_en: "<p>27.(d) <strong>CsOH &gt; KOH &gt; NaOH &gt; LiOH.</strong> This order is due to the increasing size of the alkali metal ions, which leads to better solvation and greater basicity. As you move down the group, the basicity increases because the larger alkali metal ions (like Cs⁺) can more easily dissociate and interact with water compared to the smaller ions (like Li⁺).</p>",
                    solution_hi: "<p>27.(d) <strong>CsOH &gt; KOH &gt; NaOH &gt; LiOH. </strong>यह क्रम क्षारीय धातु आयनों के बढ़ते आकार के कारण है, जो बेहतर विलयन और अधिक क्षारीयता की ओर ले जाता है। जैसे-जैसे समूह में नीचे की ओर जाते हैं, क्षारीयता बढ़ती जाती है क्योंकि बड़े क्षारीय धातु आयन (जैसे Cs⁺) छोटे आयनों (जैसे Li⁺) की तुलना में अधिक आसानी से विघटित हो सकते हैं और जल के साथ अंतःक्रिया कर सकते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who among the following formed the Bihar Provincial Kisan Sabha in 1929 ?</p>",
                    question_hi: "<p>28. निम्नलिखित में से किसके द्वारा 1929 में बिहार प्रांतीय किसान सभा का गठन किया गया था ?</p>",
                    options_en: [
                        "<p>Kunwar Singh</p>",
                        "<p>JM Sengupta</p>",
                        "<p>Jayprakash Narayan</p>",
                        "<p>Swami Sahajanand Saraswati</p>"
                    ],
                    options_hi: [
                        "<p>कुँवर सिंह</p>",
                        "<p>जे.एम. सेनगुप्ता</p>",
                        "<p>जयप्रकाश नारायण</p>",
                        "<p>स्वामी सहजानंद सरस्वती</p>"
                    ],
                    solution_en: "<p>28.(d) <strong>Swami Sahajanand Saraswati.</strong> The Bihar Provincial Kisan Sabha (BPKS) emerged in response to the agricultural crisis, worsened by the Great Depression of 1929. Swami Sahajanand Saraswati wrote \"The Other Side of the Shield\" and \"Rent Reduction in Bihar,\" addressing peasant struggles and land reforms. Jayaprakash Narayan founded the Congress Socialist Party (CSP) in 1934 and the Praja Socialist Party (PSP) in 1952, while Kunwar Singh was a key leader in the Indian Rebellion of 1857 from Bihar.</p>",
                    solution_hi: "<p>28.(d) <strong>स्वामी सहजानंद सरस्वती।</strong> बिहार प्रांतीय किसान सभा (BPKS) का उदय 1929 की महामंदी से उत्पन्न कृषि संकट के प्रति हुआ था। स्वामी सहजानंद सरस्वती ने किसान संघर्ष और भूमि सुधारों को संबोधित करते हुए \"द अदर साइड ऑफ़ द शील्ड\" और \"रेंट रिडक्शन इन बिहार\" नामक पुस्तकें लिखीं। जयप्रकाश नारायण ने 1934 में कांग्रेस सोशलिस्ट पार्टी (CSP) और 1952 में प्रजा सोशलिस्ट पार्टी (PSP) की स्थापना की, जबकि कुंवर सिंह बिहार से 1857 के भारतीय विद्रोह में एक प्रमुख नेता थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which technology was recently introduced at the National Zoological Park in Delhi to improve pond water quality ?</p>",
                    question_hi: "<p>29. दिल्ली के राष्ट्रीय प्राणी उद्यान में हाल ही में किस प्रौद्योगिकी का परिचय दिया गया है, ताकि तालाब के पानी की गुणवत्ता में सुधार हो सके ?</p>",
                    options_en: [
                        "<p>Ultraviolet Filtration</p>",
                        "<p>Nano Bubble Technology</p>",
                        "<p>Reverse Osmosis</p>",
                        "<p>Biofiltration</p>"
                    ],
                    options_hi: [
                        "<p>पराबैंगनी फिल्ट्रेशन</p>",
                        "<p>नैनो बबल प्रौद्योगिकी</p>",
                        "<p>रिवर्स ऑस्मोसिस</p>",
                        "<p>बायोफिल्ट्रेशन</p>"
                    ],
                    solution_en: "<p>29.(b)<strong> Nano Bubble Technology.</strong><br>Union Minister Kirti Vardhan Singh launched a 15-day trial of Nano Bubble Technology at the Delhi Zoo to enhance pond water quality, aiming to remove algae, foul odors, and discoloration.</p>",
                    solution_hi: "<p>29.(b) <strong>नैनो बबल प्रौद्योगिकी।</strong><br>केंद्रीय मंत्री कीर्ति वर्धन सिंह ने दिल्ली चिड़ियाघर में तालाब के पानी की गुणवत्ता में सुधार करने के लिए नैनो बबल प्रौद्योगिकी का 15 दिन का परीक्षण शुरू किया, जिसका उद्देश्य शैवाल, बदबू और रंगहीनता को हटाना है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Mahendravarman I was the ruler of which of the following dynasties ?</p>",
                    question_hi: "<p>30. महेंद्रवर्मन प्रथम, निम्नलिखित में से किस राजवंश का शासक था ?</p>",
                    options_en: [
                        "<p>Pandya</p>",
                        "<p>Chola</p>",
                        "<p>Chalukya</p>",
                        "<p>Pallava</p>"
                    ],
                    options_hi: [
                        "<p>पंड्या</p>",
                        "<p>चोल</p>",
                        "<p>चालुक्य</p>",
                        "<p>पल्लव</p>"
                    ],
                    solution_en: "<p>30.(d) <strong>Pallava.</strong> Mahendravarman I ruled the southern portions of present-day Andhra region and northern regions of what forms present-day Tamil Nadu in India. He took up the title &lsquo;Chitrakarapuli&rsquo;, &lsquo;Matavilasa&rsquo;, and &lsquo;Vichirachita&rsquo;. The Pallava Dynasty was founded by Simhavishnu.</p>",
                    solution_hi: "<p>30.(d) <strong>पल्लव।</strong> महेंद्रवर्मन प्रथम ने वर्तमान आंध्र क्षेत्र के दक्षिणी भागों और भारत में वर्तमान तमिलनाडु के उत्तरी क्षेत्रों पर शासन किया। उन्होंने &lsquo;चित्रकारपुली&rsquo;, &lsquo;मातविलास&rsquo; और &lsquo;विचिरचिता&rsquo; की उपाधि धारण की थी। पल्लव राजवंश की स्थापना सिंहविष्णु ने की थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "31. Lathmar Holi is primarily celebrated in the state of:",
                    question_hi: "<p>31. लठमार होली मुख्य रूप से किस राज्य में खेली जाती है ?</p>",
                    options_en: [
                        " Karnataka ",
                        " Arunachal Pradesh  ",
                        " Uttar Pradesh",
                        " Himachal Pradesh<br /> "
                    ],
                    options_hi: [
                        " कर्नाटक ",
                        " अरुणाचल प्रदेश ",
                        " उत्तर प्रदेश ",
                        " हिमाचल प्रदेश "
                    ],
                    solution_en: "<p>31.(c) <strong>Uttar Pradesh. </strong>Lathmar Holi is celebrated in the twin towns of Barsana and Nandgaon, also known as the towns of Radha and Krishna respectively. Famous festivals of Uttar Pradesh - Gaurav Mahotsav, Ramotsav, Taj Mahotsav, Ayodhya Deepotsav, Ganga Mahotsav. Arunachal Pradesh - Sangken, Mopin, Pongtu. Himachal Pradesh - Halda, Doongri, Chet Festival, Kullu Dussehra. Karnataka - Ugadi, Mysuru Dasara, Kambala, Gowri Habba.</p>",
                    solution_hi: "<p>31.(c) <strong>उत्तर प्रदेश। </strong>लठमार होली बरसाना और नंदगांव कस्बों में मनाई जाती है, जिन्हें क्रमशः राधा और कृष्ण की नगरी के रूप में भी जाना जाता है। उत्तर प्रदेश के प्रसिद्ध त्यौहार - गौरव महोत्सव, रामोत्सव, ताज महोत्सव, अयोध्या दीपोत्सव, गंगा महोत्सव। अरुणाचल प्रदेश - सांगकेन, मोपिन, पोंगटू। हिमाचल प्रदेश - हल्दा, डूंगरी, चेत महोत्सव, कुल्लू दशहरा। कर्नाटक - उगादि, मैसूर दशहरा, कंबाला, गौरी हब्बा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. In which state is the recently inaugurated Archaeological Experiential Museum in Vadnagar located ?</p>",
                    question_hi: "<p>32. वडनगर में उद्घाटित पुरातात्विक अनुभव संग्रहालय किस राज्य में स्थित है ?</p>",
                    options_en: [
                        " Madhya Pradesh ",
                        " Gujarat",
                        " Rajasthan B          ",
                        " Maharashtra"
                    ],
                    options_hi: [
                        " मध्य प्रदेश",
                        " गुजरात",
                        " राजस्थान",
                        " महाराष्ट्र"
                    ],
                    solution_en: "<p>32.(b) <strong>Gujarat.</strong> Vadnagar, located in Gujarat, is an important historical and archaeological site. Recently, the Archaeological Experiential Museum was inaugurated there to preserve and showcase the town\'s 2,500-year-old heritage. The museum uses digital technology to exhibit ancient relics, architectural marvels, and the historical significance of the site.</p>",
                    solution_hi: "<p>32.(b) <strong>गुजरात । </strong>वडनगर, जो गुजरात राज्य में स्थित है, ऐतिहासिक और पुरातात्विक महत्व का स्थल है। हाल ही में यहां पुरातात्विक अनुभव संग्रहालय (Archaeological Experiential Museum) का उद्घाटन किया गया, जिसका उद्देश्य क्षेत्र की 2,500 वर्ष पुरानी विरासत को संरक्षित और प्रदर्शित करना है। संग्रहालय में डिजिटल तकनीक के माध्यम से प्राचीन अवशेषों, स्थापत्य कला और ऐतिहासिक महत्व को दर्शाया गया है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which plateaus are very fertile because they are rich in black soil that is very good for farming ?</p>",
                    question_hi: "<p>33. कौन-से पठार बहुत उपजाऊ होते हैं क्योंकि वे काली मृदा में समृद्ध होते हैं जो खेती के लिए बहुत अच्छी है ?</p>",
                    options_en: [
                        "<p>African plateau</p>",
                        "<p>Ethiopian plateau</p>",
                        "<p>Katanga plateau</p>",
                        "<p>Deccan lava plateau</p>"
                    ],
                    options_hi: [
                        "<p>अफ्रीकी पठार</p>",
                        "<p>इथियोपिया का पठार</p>",
                        "<p>कटंगा पठार</p>",
                        "<p>दक्कन लावा पठार</p>"
                    ],
                    solution_en: "<p>33.(d) <strong>Deccan lava plateau</strong> is a volcanic plateau in west-central India that was formed by the solidification of lava from deep within the Earth. It is a triangular landmass that lies to the south of the river Narmada. The African plateau is famous for gold and diamond mining. The Katanga Plateau in the Democratic Republic of the Congo is known for its rich deposits of copper and uranium.</p>",
                    solution_hi: "<p>33.(d)<strong> दक्कन लावा पठार </strong>पश्चिम-मध्य भारत में एक ज्वालामुखीय पठार है जिसका निर्माण पृथ्वी के भीतर लावा के जमने से हुआ था। यह एक त्रिकोणीय भूभाग है जो नर्मदा नदी के दक्षिण में स्थित है। अफ्रीकी पठार सोने और हीरे के खनन के लिए प्रसिद्ध है। कांगो लोकतांत्रिक गणराज्य में कटंगा पठार तांबे और यूरेनियम के समृद्ध भंडार के लिए जाना जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following states is the biggest producer of Pulses ?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन-सा राज्य दालों का सबसे बड़ा उत्पादक है ?</p>",
                    options_en: [
                        " Madhya Pradesh  ",
                        " Haryana ",
                        " Punjab",
                        " Bihar"
                    ],
                    options_hi: [
                        " मध्य प्रदेश",
                        " हरियाण ",
                        " पंजाब",
                        " बिहार"
                    ],
                    solution_en: "<p>34.(a) <strong>Madhya Pradesh. </strong>Based on the production estimates for the year 2022-23, Madhya Pradesh, Maharashtra and Rajasthan are the top three pulses producing states in the country. India is the largest producer of pulses in the world.</p>",
                    solution_hi: "<p>34.(a) <strong>मध्य प्रदेश।</strong> वर्ष 2022-23 के उत्पादन अनुमान के आधार पर मध्य प्रदेश, महाराष्ट्र और राजस्थान देश के शीर्ष तीन दलहन उत्पादक राज्य हैं। भारत दुनिया में दालों का सबसे बड़ा उत्पादक देश है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which condition, also known as icterus, causes a yellowing of your skin and the whites of your eyes ?</p>",
                    question_hi: "<p>35. कौन सी स्थिति, जिसे पीलिया (इक्टेरस) के रूप में भी जाना जाता है, आपकी त्वचा के पीलेपन और आपकी आंखों के सफेद होने का कारण बनती है ?</p>",
                    options_en: [
                        "<p>Ichthyosis</p>",
                        "<p>Jaundice</p>",
                        "<p>Eczema</p>",
                        "<p>Pemphigus</p>"
                    ],
                    options_hi: [
                        "<p>इक्थियोसिस</p>",
                        "<p>जॉन्डिस</p>",
                        "<p>एक्जिमा</p>",
                        "<p>पेम्फीगस</p>"
                    ],
                    solution_en: "<p>35.(b) <strong>Jaundice. </strong>This yellowing occurs due to high levels of bilirubin in the blood. Bilirubin is a yellow pigment produced during the normal breakdown of red blood cells. Ichthyosis is a group of genetic skin disorders that cause dry, scaly, thickened skin. Eczema is a common skin condition that causes itchiness, rashes, dry patches, and infection. Pemphigus is a rare group of autoimmune diseases that cause blisters on the skin and mucous membranes.</p>",
                    solution_hi: "<p>35.(b) <strong>जॉन्डिस।</strong> यह पीलापन रक्त में बिलिरुबिन के उच्च स्तर के कारण होता है। बिलिरुबिन एक पीला वर्णक है जो लाल रक्त कोशिकाओं के सामान्य टूटने के दौरान उत्पन्न होता है। इक्थियोसिस आनुवंशिक त्वचा विकारों का एक समूह है जो शुष्क, परतदार, मोटी त्वचा का कारण बनता है। एक्जिमा एक सामान्य त्वचा की स्थिति है जो खुजली, दाने, सूखे धब्बे, और संक्रमण का कारण बनती है। पेम्फिगस ऑटोइम्यून बीमारियों का एक दुर्लभ समूह है जो त्वचा और श्लेष्म झिल्ली पर छाले उत्पन्न करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. In which of the following states is Govindji Nartanalaya located ?</p>",
                    question_hi: "<p>36. गोविंदजी नर्तनालय निम्नलिखित में से किस राज्य में स्थित है ?</p>",
                    options_en: [
                        "<p>Manipur</p>",
                        "<p>Assam</p>",
                        "<p>Nagaland</p>",
                        "<p>Tripura</p>"
                    ],
                    options_hi: [
                        "<p>मणिपुर</p>",
                        "<p>असम</p>",
                        "<p>नागालैंड</p>",
                        "<p>त्रिपुरा</p>"
                    ],
                    solution_en: "<p>36.(a) <strong>Manipur. </strong>Govindji Nartanalaya is a well-known cultural institution dedicated to promoting Manipuri classical dance and other traditional art forms. This institute was founded by Manipuri dancer and choreographer Guru Bipin Singh. Some notable dance institutions established by famous dancers : Kalakshetra Foundation (Tamil Nadu) - Rukmini Devi Arundale; Shri Ram Bharatiya Kala Kendra (New Delhi) - Sumitra Charat Ram; Nrityabharti Kathak Dance Academy (Maharashtra) - Rohini Bhate.</p>",
                    solution_hi: "<p>36.(a) <strong>मणिपुर। </strong>गोविंदजी नर्तनालय एक प्रसिद्ध सांस्कृतिक संस्था है जो मणिपुरी शास्त्रीय नृत्य और अन्य पारंपरिक कला रूपों को बढ़ावा देने के लिए समर्पित है। इस संस्थान की स्थापना मणिपुरी नर्तक और कोरियोग्राफर गुरु बिपिन सिंह ने की थी। प्रसिद्ध नर्तकों द्वारा स्थापित कुछ उल्लेखनीय नृत्य संस्थाएँ: कलाक्षेत्र फाउंडेशन (तमिलनाडु) - रुक्मिणी देवी अरुंडेल; श्री राम भारतीय कला केंद्र (नई दिल्ली) - सुमित्रा चरत राम; नृत्यभारती कथक नृत्य अकादमी (महाराष्ट्र) - रोहिणी भाटे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. The first southern campaign of Alauddin Khilji in 1307-08 AD was led to which of the following regions ?</p>",
                    question_hi: "<p>37. 1307-08 ई. में अलाउद्दीन खिलजी का पहला दक्षिणी अभियान निम्नलिखित में से किस क्षेत्र में हुआ था ?</p>",
                    options_en: [
                        " Devagiri",
                        " Warangal",
                        " Dwar Samudra",
                        " Madura"
                    ],
                    options_hi: [
                        " देवगिरी ",
                        " वारंगल ",
                        " द्वार समुद्र  ",
                        " मदुरा"
                    ],
                    solution_en: "<p>37.(a) <strong>Devagiri</strong> (modern-day Daulatabad, Maharashtra). This campaign marked the beginning of the Delhi Sultanate\'s expansion into southern India. Alauddin Khilji (1296 -1316) was the second sultan of the Khilji dynasty. Campaigns of Alauddin Khilji: Gujarat (1299), Ranthambore (1301), Chittor (1303), Malwa (1305), Madurai (1311), Dwarasamudra (1310-1311).</p>",
                    solution_hi: "<p>37.(a) <strong>देवगिरि </strong>(आधुनिक दौलताबाद, महाराष्ट्र)। इस अभियान ने दिल्ली सल्तनत के दक्षिणी भारत में विस्तार की शुरुआत को चिह्नित किया। अलाउद्दीन खिलजी (1296 -1316) खिलजी वंश का दूसरा सुल्तान था। अलाउद्दीन खिलजी के अभियान: गुजरात (1299), रणथंभौर (1301), चित्तौड़ (1303), मालवा (1305), मदुरै (1311), द्वारसमुद्र (1310-1311)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following can be represented as a functional unit of nature ?</p>",
                    question_hi: "<p>38. निम्नलिखित में से किसे प्रकृति की कार्यात्मक इकाई के रूप में दर्शाया जा सकता है ?</p>",
                    options_en: [
                        "<p>Vehicles</p>",
                        "<p>Ecosystem</p>",
                        "<p>Humans</p>",
                        "<p>Plants</p>"
                    ],
                    options_hi: [
                        "<p>वाहन</p>",
                        "<p>पारिस्थितिकी तंत्र</p>",
                        "<p>मनुष्य</p>",
                        "<p>वनस्पति</p>"
                    ],
                    solution_en: "<p>38.(b) <strong>Ecosystem.</strong> This is because an ecosystem encompasses all living organisms (plants, animals, and microorganisms) interacting with each other and with the non-living components (like air, water, and soil) within their environment. These interactions allow for energy flow, nutrient cycling, and the maintenance of life-supporting processes, which are critical functions of nature.</p>",
                    solution_hi: "<p>38.(b)<strong> पारिस्थितिकी तंत्र। </strong>ऐसा इसलिए है क्योंकि पारिस्थितिकी तंत्र में सभी जीवित जीव (पौधे, जन्तुओ और सूक्ष्मजीव) शामिल होते हैं जो एक दूसरे के साथ और अपने पर्यावरण के भीतर निर्जीव घटकों (जैसे वायु, जल और मृदा) के साथ अंतःक्रिया करते हैं। ये अंतःक्रियाएँ ऊर्जा प्रवाह, पोषक तत्वों के चक्रण और जीवन को बनाए रखने वाली प्रक्रियाओं के रखरखाव को संभव बनाती हैं, जो प्रकृति के महत्वपूर्ण कार्य हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. At what latitude does the easterly jet stream blow over peninsular India during the summer months ?</p>",
                    question_hi: "<p>39. गर्मियों के महीनों में पूर्वी जेट धारा प्रायद्वीपीय भारत के ऊपर किस अक्षांश पर बहती है ?</p>",
                    options_en: [
                        " 24°N",
                        " 30°N",
                        " 14°N",
                        " 28°N"
                    ],
                    options_hi: [
                        " 24°N",
                        " 30°N",
                        " 14°N",
                        " 28°N"
                    ],
                    solution_en: "<p>39.(c) <strong>14&deg;N. </strong>The Jet Stream is a geostrophic wind blowing horizontally through the upper layers of the troposphere, generally from west to east. The tropical easterly jet stream, located between 8 and 35 degrees north latitude, is connected to the southwest monsoon in India.</p>",
                    solution_hi: "<p>39.(c) <strong>14&deg;N. </strong>जेट स्ट्रीम एक भूस्थैतिक वायु है जो क्षोभमंडल की ऊपरी परतों पर पश्चिम से पूर्व की ओर से होकर क्षैतिज रूप से चलती है। उष्णकटिबंधीय पूर्वी जेट स्ट्रीम, जो 8 से 35 डिग्री उत्तरी अक्षांश के बीच स्थित है, भारत में दक्षिण-पश्चिम मानसून से जुड़ी हुई है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which of the following statements correctly defines the green revolution ?</p>",
                    question_hi: "<p>40. निम्&zwj;न में से कौन-सा कथन हरित क्रांति को सही ढंग से परिभाषित करता है ?</p>",
                    options_en: [
                        "<p>It is a new strategy in agriculture to produce food grains, especially wheat and rice.</p>",
                        "<p>It is a new strategy to increase the share of the forest.</p>",
                        "<p>It is a new strategy to use green colour for all purposes.</p>",
                        "<p>It is a new strategy to use only herbal products.</p>"
                    ],
                    options_hi: [
                        "<p>यह कृषि में खाद्यान्न, विशेषकर गेहूं और चावल का उत्पादन करने की एक नई रणनीति है।</p>",
                        "<p>यह वन के योगदान में वृद्धि करने की एक नई रणनीति है।</p>",
                        "<p>यह सभी उद्देश्यों के लिए हरे रंग का उपयोग करने की एक नई रणनीति है।</p>",
                        "<p>यह केवल हर्बल उत्पादों का उपयोग करने की एक नई रणनीति है।</p>"
                    ],
                    solution_en: "<p>40.(a) The Green Revolution began in the 1960s, particularly in countries like India, where the government aimed to achieve food security and reduce dependence on food imports. Key elements of the Green Revolution included the introduction of high-yielding variety (HYV) seeds, mechanized farming tools, improved irrigation systems, as well as the use of pesticides and fertilizers.</p>",
                    solution_hi: "<p>40.(a) हरित क्रांति 1960 के दशक में शुरू हुई, खास तौर पर भारत जैसे देशों में, जहाँ सरकार का लक्ष्य खाद्य सुरक्षा हासिल करना और खाद्य आयात पर निर्भरता कम करना था। हरित क्रांति के मुख्य तत्वों में उच्च उपज वाली किस्म (HYV) के बीज, मशीनीकृत कृषि उपकरण, बेहतर सिंचाई प्रणाली, साथ ही कीटनाशकों और उर्वरकों का उपयोग शामिल था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "41. In the year 1952, who among the following lent his voice for the film ‘Amar Bhupali’, which was being produced in two languages simultaneously and he sang in both Bengali and Marathi ?",
                    question_hi: "<p>41. वर्ष 1952 में, निम्नलिखित में से किसने फिल्म अमर भूपाली, जो एक साथ दो भाषाओं में निर्मित की जा रही थी, के लिए अपनी आवाज दी और उन्होंने बंगाली और मराठी दोनों में गीत गाए ?</p>",
                    options_en: [
                        " Kishore Kumar",
                        " Hemanta Mukherjee",
                        " Debabrata Biswas",
                        " Manna Dey"
                    ],
                    options_hi: [
                        " किशोर कुमार ",
                        " हेमंत मुखर्जी  ",
                        " देवव्रत बिस्वास ",
                        " मन्ना डे"
                    ],
                    solution_en: "<p>41.(d)<strong> Manna Dey</strong> was a renowned Indian playback singer, music director, and musician. He had a classical music background, being part of the Bhendibazaar Gharana. He received Padma Shri in 1971, Padma Bhushan in 2005 and Dadasaheb Phalke award in 2007.</p>",
                    solution_hi: "<p>41.(d) <strong>मन्ना डे </strong>एक प्रसिद्ध भारतीय पार्श्व गायक, संगीत निर्देशक और संगीतकार थे। वे शास्त्रीय संगीत की पृष्ठभूमि से थे और इनका संबंध भिंडीबाजार घराने से था। उन्हें 1971 में पद्म श्री, 2005 में पद्म भूषण और 2007 में दादा साहब फाल्के पुरस्कार से सम्मानित किया गया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. At which Olympics did Gagan Narang win a bronze medal ?</p>",
                    question_hi: "<p>42. गगन नारंग ने किस ओलंपिक में कांस्य पदक जीता था ?</p>",
                    options_en: [
                        "<p>London 2012</p>",
                        "<p>Rio 2016</p>",
                        "<p>Athens 2004</p>",
                        "<p>Beijing 2008</p>"
                    ],
                    options_hi: [
                        "<p>लंदन 2012</p>",
                        "<p>रियो 2016</p>",
                        "<p>एथेंस 2004</p>",
                        "<p>बीजिंग 2008</p>"
                    ],
                    solution_en: "<p>42.(a) <strong>London 2012.</strong> Gagan Narang is an Indian sports shooter and is associated with air rifle shooting. He won the bronze medal in the Men&rsquo;s 10 meter air rifle event. He received the Arjuna award in 2005. Indian Shooters: Abhinav Bindra, Rajyavardhan Singh Rathore, Vijay Kumar, Manu Bhaker, Swapnil Kusale.</p>",
                    solution_hi: "<p>42.(a) <strong>लंदन 2012. </strong>गगन नारंग एक भारतीय खेल निशानेबाज हैं और एयर राइफल शूटिंग से संबंधित हैं। उन्होंने पुरुषों की 10 मीटर एयर राइफल स्पर्धा में कांस्य पदक जीता था। उन्हें 2005 में अर्जुन पुरस्कार से सम्मानित किया गया था। भारतीय निशानेबाज: अभिनव बिंद्रा, राज्यवर्धन सिंह राठौर, विजय कुमार, मनु भाकर, स्वप्निल कुसाले।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. In which of the following cities of Manipur is the Jawaharlal Nehru Manipur Dance Academy located ?</p>",
                    question_hi: "<p>43. मणिपुर के निम्नलिखित में से किस शहर में जवाहरलाल नेहरू मणिपुर नृत्य अकादमी स्थित है ?</p>",
                    options_en: [
                        "<p>Imphal</p>",
                        "<p>Ukhrul</p>",
                        "<p>Chandel</p>",
                        "<p>Thoubal</p>"
                    ],
                    options_hi: [
                        "<p>इंफाल</p>",
                        "<p>उखरूल</p>",
                        "<p>चंदेल</p>",
                        "<p>थौबल</p>"
                    ],
                    solution_en: "<p>43.(a) <strong>Imphal.</strong> Jawaharlal Nehru Manipur Dance Academy, a unit of the Sangeet Natak Academy in New Delhi, is dedicated to teaching Manipuri dance and music. Established in 1954. Other renowned Dance Institutes : National Institute of Kathak Dance (New Delhi), Nalanda Nritya Kala Mahavidyalaya (Mumbai), Ballet Repertoire Academy of India (Mumbai), Sri Thyagaraja College of Music and Dance (Hyderabad), and Nrityanjali Institute of Performing Arts (Mumbai).</p>",
                    solution_hi: "<p>43.(a) <strong>इंफाल। </strong>जवाहरलाल नेहरू मणिपुर नृत्य अकादमी, नई दिल्ली में संगीत नाटक अकादमी की एक इकाई है, जो मणिपुरी नृत्य और संगीत सिखाने के लिए समर्पित है। जिसकी स्थापना 1954 में हुई थी। अन्य प्रसिद्ध नृत्य संस्थान: राष्ट्रीय कथक नृत्य संस्थान (नई दिल्ली), नालंदा नृत्य कला महाविद्यालय (मुंबई), बैले रिपर्टरी एकेडमी ऑफ इंडिया (मुंबई), श्री त्यागराज कॉलेज ऑफ म्यूजिक एंड डांस (हैदराबाद) और नृत्यांजलि इंस्टीट्यूट ऑफ परफॉर्मिंग आर्ट्स (मुंबई)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following places is related to Gandhi&rsquo;s Satyagraha of the year 1917?</p>",
                    question_hi: "<p>44. निम्नलिखित में से कौन-सा स्थान वर्ष 1917 के गांधीजी के सत्याग्रह से संबंधित है?</p>",
                    options_en: [
                        " Kheda ",
                        " Ahmedabad",
                        " Champaran ",
                        " Bardoli"
                    ],
                    options_hi: [
                        " खेड़ा ",
                        " अहमदाबाद",
                        " चंपारण",
                        " बारदोली"
                    ],
                    solution_en: "<p>44.(c)<strong> Champaran.</strong> India\'s first Civil Disobedience movement, launched by Mahatma Gandhi in 1917, was prompted by Pandit Raj Kumar Shukla, who urged him to address the plight of Indigo farmers. Mahatma Gandhi organised the Kheda Satyagraha (1918) in Gujarat. Gandhi also led the Ahmedabad Mill Strike (1918) after returning from South Africa. The Bardoli Satyagraha (1928) was led by Sardar Vallabhbhai Patel for the farmers of Bardoli.</p>",
                    solution_hi: "<p>44.(c) <strong>चंपारण।</strong> भारत का प्रथम सविनय अवज्ञा आंदोलन, जिसे महात्मा गांधी ने 1917 में शुरू किया था, पंडित राज कुमार शुक्ला द्वारा प्रेरित था, जिन्होंने उनसे नील किसानों की दुर्दशा को संबोधित करने का आग्रह किया था। महात्मा गांधी ने गुजरात में खेड़ा सत्याग्रह (1918) का आयोजन किया था। गांधी ने दक्षिण अफ्रीका से लौटने के बाद अहमदाबाद मिल हड़ताल (1918) का भी नेतृत्व किया। बारदोली सत्याग्रह (1928) का नेतृत्व सरदार वल्लभभाई पटेल ने बारदोली के किसानों के लिए किया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following is in geographical proximity to Sri Lanka ?</p>",
                    question_hi: "<p>45. निम्नलिखित में से कौन-सा स्थान भौगोलिक रूप से श्रीलंका के समीप है ?</p>",
                    options_en: [
                        "<p>Only Karaikal</p>",
                        "<p>Karaikal and Yanam</p>",
                        "<p>Only Mahe</p>",
                        "<p>Only Yanam</p>"
                    ],
                    options_hi: [
                        "<p>केवल कराईकल</p>",
                        "<p>कराईकल और यानम</p>",
                        "<p>केवल माहे</p>",
                        "<p>केवल यानम</p>"
                    ],
                    solution_en: "<p>45.(a) <strong>Only Karaikal.</strong> Karaikal is a town in the Union Territory of Puducherry. Sri Lanka, formerly known as Ceylon, is an island nation in South Asia. Located in the Indian Ocean, southwest of the Bay of Bengal, it is separated from the Indian peninsula by the Gulf of Mannar and the Palk Strait. Sri Lanka shares a maritime border with the Maldives to the southwest and India to the northwest.</p>",
                    solution_hi: "<p>45.(a) <strong>केवल कराईकल।</strong> कराईकल पुडुचेरी के केंद्र शासित प्रदेश का एक शहर है। श्रीलंका, जिसे पहले सीलोन के नाम से जाना जाता था, दक्षिण एशिया में एक द्वीप राष्ट्र है। बंगाल की खाड़ी के दक्षिण-पश्चिम में हिंद महासागर में स्थित, यह मन्नार की खाड़ी और पाक जलडमरूमध्य द्वारा भारतीय प्रायद्वीप से अलग होता है। श्रीलंका दक्षिण-पश्चिम में मालदीव और उत्तर-पश्चिम में भारत के साथ समुद्री सीमा साझा करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following statements is INCORRECT about the Directive Principles of State Policy ?</p>",
                    question_hi: "<p>46. निम्नलिखित में से कौन-सा कथन राज्य के नीति निर्देशक सिद्धांतों के बारे में सही नहीं है ?</p>",
                    options_en: [
                        " They promote the welfare of individuals. Hence, they are personal and individualistic. ",
                        " These have moral and political sanctions. ",
                        " They aim at establishing social and economic democracy in the country. ",
                        " They are positive, as they require the State to do certain things."
                    ],
                    options_hi: [
                        " ये लोगों के कल्याण को बढ़ावा देते हैं। इसलिए ये निजी और व्यक्तिपरक हैं।",
                        " इन पर नैतिक और राजनीतिक प्रतिबंध हैं। ",
                        " इनका उद्देश्य देश में सामाजिक और आर्थिक लोकतंत्र की स्थापना करना है। ",
                        " ये प्रकृति में सकारात्मक होते हैं, क्योंकि राज्यों को कुछ मामलों में इनकी आवश्यकता होती है।"
                    ],
                    solution_en: "46.(a) The Directive Principles of State Policy (Articles 36-51, Part IV) are guidelines in the Indian Constitution designed to establish a welfare state in India. They are borrowed from the Irish Constitution. DPSPs are vital for the country\'s social, economic, and political development though they are non-justiciable.",
                    solution_hi: "46.(a)  राज्य  के नीति निर्देशक सिद्धांत (अनुच्छेद 36-51, भाग IV) भारतीय संविधान में दिशा-निर्देश हैं, जिन्हें भारत में कल्याणकारी राज्य की स्थापना के लिए डिज़ाइन किया गया है। इन्हें आयरिश संविधान से अपनाया गया है। DPSP देश के सामाजिक, आर्थिक और राजनीतिक विकास के लिए महत्वपूर्ण हैं और गैर-न्यायसंगत हैं।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. The climate of a place is NOT affected by which of the following ?</p>",
                    question_hi: "<p>47. किसी स्थान की जलवायु निम्नलिखित में से किसके द्वारा प्रभावित नहीं होती है ?</p>",
                    options_en: [
                        "<p>Relief</p>",
                        "<p>Type of soil</p>",
                        "<p>Location</p>",
                        "<p>Distance from the sea</p>"
                    ],
                    options_hi: [
                        "<p>उच्चावच</p>",
                        "<p>मिट्टी के प्रकार</p>",
                        "<p>स्थान</p>",
                        "<p>समुद्र से दूरी</p>"
                    ],
                    solution_en: "<p>47.(b) <strong>Type of soil. </strong>Climate refers to the long-term conditions of a specific region. The changes in climate within a region are influenced by its atmospheric and environmental conditions. The six major factors that influence the climate of a location are latitude, altitude, pressure, and wind systems, distance from the sea (continentality), ocean currents, and relief features.</p>",
                    solution_hi: "<p>47.(b) <strong>मिट्टी के प्रकार। </strong>जलवायु किसी विशिष्ट क्षेत्र की दीर्घकालिक स्थितियों को संदर्भित करता है। किसी क्षेत्र के भीतर जलवायु में परिवर्तन उसके वायुमंडलीय और पर्यावरणीय स्थितियों से प्रभावित होते हैं। किसी स्थान की जलवायु को प्रभावित करने वाले छह प्रमुख साधन अक्षांश, ऊँचाई, दाब और पवन प्रणाली, समुद्र से दूरी (महाद्वीपीयता), महासागरीय धाराएँ और उच्चावच विशेषताएँ हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following is NOT correct with regards to the history of calculating National Income (NI) in India ?</p>",
                    question_hi: "<p>48. भारत में राष्ट्रीय आय (NI)की गणना के इतिहास के संबंध में निम्नलिखित में से कौन-सा सही नहीं है ?</p>",
                    options_en: [
                        "<p>First attempt to compute NI was made by Dadabhai Naoroji.</p>",
                        "<p>First official attempt to compute NI was made by PC Mahalanobis.</p>",
                        "<p>Dadabhai Naoroji divided the Indian economy into two parts: primary sector and<br>secondary sector.</p>",
                        "<p>First scientific method to compute NI was used by Dr VKRV Rao.</p>"
                    ],
                    options_hi: [
                        "<p>NI की गणना करने का पहला प्रयास दादाभाई नौरोजी ने किया था।</p>",
                        "<p>NI की गणना करने का पहला आधिकारिक प्रयास पीसी महालनोबिस द्वारा किया गया था।</p>",
                        "<p>दादाभाई नौरोजी ने भारतीय अर्थव्यवस्था को दो भागों में विभाजित किया: प्राथमिक क्षेत्र और द्वितीयक क्षेत्र।</p>",
                        "<p>NI की गणना करने के लिए पहली वैज्ञानिक पद्धति का उपयोग डॉ वीकेआरवी राव ने किया था।</p>"
                    ],
                    solution_en: "<p>48.(c) Dr. VKRV Rao in 1931, divided the Indian economy into two sectors: the agricultural sector and the corporate sector. The agricultural sector encompassed fishing, hunting, forests, and agriculture, while the corporate sector included industries, business, transport, construction, and public services. National income represents the total value of all goods and services produced in a country during a specific period. In 1867, Dadabhai Naoroji, known as the Grand Old Man of India, proposed the drain theory, also referred to as the \"drain of wealth\" theory.</p>",
                    solution_hi: "<p>48.(c) डॉ. वी.के.आर.वी. राव ने 1931 में भारतीय अर्थव्यवस्था को दो क्षेत्रों में विभाजित किया: कृषि क्षेत्र और कॉर्पोरेट क्षेत्र। कृषि क्षेत्र में मछली पकड़ना, शिकार करना, जंगल और कृषि शामिल थे, जबकि कॉर्पोरेट क्षेत्र में उद्योग, व्यवसाय, परिवहन, निर्माण और सार्वजनिक सेवाएँ शामिल थीं। राष्ट्रीय आय एक विशिष्ट अवधि के दौरान किसी देश में उत्पादित सभी वस्तुओं और सेवाओं के कुल मूल्य का प्रतिनिधित्व करती है। 1867 में, दादाभाई नौरोजी, जिन्हें भारत के ग्रैंड ओल्ड मैन के रूप में जाना जाता है, ने ड्रेन थ्योरी का प्रस्ताव रखा, जिसे \"धन की निकासी\" सिद्धांत भी कहा जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following festivals is celebrated as the birth anniversary of Guru Nanak Dev ?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन-सा त्योहार गुरु नानक देव की जयंती के रूप में मनाया जाता है ?</p>",
                    options_en: [
                        "<p>Hola Mohalla</p>",
                        "<p>Baisakhi</p>",
                        "<p>Gurpurab</p>",
                        "<p>Chappar Mela</p>"
                    ],
                    options_hi: [
                        "<p>होला मोहल्ला</p>",
                        "<p>बैसाखी</p>",
                        "<p>गुरुपर्व (गुरपुरब)</p>",
                        "<p>छप्पर मेला</p>"
                    ],
                    solution_en: "<p>49.(c) <strong>Gurpurab.</strong> Guru Nanak Jayanti, also known as Gurpurab, celebrates the birth anniversary of the first Sikh Guru, Guru Nanak Dev. This festival occurs on Kartik Poornima, the fifteenth lunar day of the month of Kartik, typically in November on the Gregorian calendar. Hola Mohalla, or Hola, is a three-day Sikh festival that usually takes place in March. Vaisakhi, celebrated on April 13 or sometimes April 14, marks the first day of the month of Vaisakh. Chhapar Mela, a popular Punjabi fair, occurs annually in September in the village of Chhapar in the Ludhiana district of Punjab.</p>",
                    solution_hi: "<p>49.(c) <strong>गुरुपर्व (गुरपुरब)। </strong>गुरु नानक जयंती, जिसे गुरुपर्व के नाम से भी जाना जाता है, पहले सिख गुरु, गुरु नानक देव की जयंती के रूप में मनाई जाती है। यह त्योहार कार्तिक पूर्णिमा को मनाया जाता है, जो कार्तिक महीने के पंद्रहवें चंद्र दिवस पर पड़ता है, जो सामान्यतः ग्रेगोरियन कैलेंडर के अनुसार नवंबर में होता है। होला मोहल्ला, या होला, तीन दिवसीय सिख त्योहार है जो सामान्यतः मार्च में मनाया जाता है। वैसाखी, जिसे 13 अप्रैल या कभी-कभी 14 अप्रैल को मनाया जाता है, वैसाख महीने के पहले दिन को चिह्नित करता है। छपार मेला, एक लोकप्रिय पंजाबी मेला, पंजाब के लुधियाना जिले के छपर गाँव में प्रत्येक वर्ष सितंबर में होता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which of the following is a non-perishable food ?</p>",
                    question_hi: "<p>50. निम्नलिखित में से कौन-सा अविकारीय भोजन है ?</p>",
                    options_en: [
                        " Pulses ",
                        " Meat ",
                        " Milk",
                        " Curds"
                    ],
                    options_hi: [
                        " दाल ",
                        " मांस ",
                        " दूध ",
                        " दही"
                    ],
                    solution_en: "<p>50.(a)<strong> Pulses.</strong> Non-perishable foods are items that can be stored at room temperature for extended periods without spoiling. Examples include beans, coffee, honey, powdered milk, rice, and wheat. In contrast, frozen foods are not classified as non-perishable.</p>",
                    solution_hi: "<p>50.(a) <strong>दाल। </strong>अविकारीय खाद्य पदार्थ वे पदार्थ हैं जिन्हें बिना खराब हुए लंबे समय तक कमरे के तापमान पर रखा जा सकता है। उदाहरणों में बीन्स, कॉफी, शहद, पाउडर वाला दूध, चावल और गेहूं शामिल हैं। इसके विपरीत, फ्रोज़ेन खाद्य पदार्थ को अविकारीय के रूप में वर्गीकृत नहीं किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Let ABC be a right-angled triangle such that &ang;C = 90&deg;. Let D be a point on AB such that CD is perpendicular to AB. If AC = 5 cm and BC = 12 cm, find the length of CD (in cm).</p>",
                    question_hi: "<p>51. माना ABC एक समकोण त्रिभुज इस प्रकार है कि &ang;C = 90&deg; है। मान लीजिए D, AB पर एक बिंदु इस प्रकार है कि CD, AB के लंबवत है। यदि AC = 5 cm और BC = 12 cm है, तो CD की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>23</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>40</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>60</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>51.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699464029.png\" alt=\"rId69\" width=\"173\" height=\"139\"><br>By pythagorean triplet = (5 , 12 , 13)<br>So, AB = 13 cm<br>In <math display=\"inline\"><mi>&#916;</mi></math>ABC, <br>&rArr; Required length (CD) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi><mo>&#215;</mo><mi>B</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math><br>&rArr; CD = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>13</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>13</mn></mfrac></math> cm</p>",
                    solution_hi: "<p>51.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699464029.png\" alt=\"rId69\" width=\"173\" height=\"139\"><br>पाइथागोरस ट्रिपलेट द्वारा = (5, 12, 13)<br>अत: AB = 13 सेमी<br><math display=\"inline\"><mi>&#916;</mi></math>ABC में, <br>आवश्यक लंबाई (CD) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>C</mi><mo>&#215;</mo><mi>B</mi><mi>C</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math><br>&rArr; CD = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>13</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>13</mn></mfrac></math> cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Each of Ravi and Kavita had some marbles. Kavita had 12 more marbles than Ravi had. If each of them had one more marble, then three times the number of marbles Kavita would then have had would have been equal to four times the number of marbles Ravi would then have had. How many marbles did Kavita actually have ?</p>",
                    question_hi: "<p>52. रवि और कविता में से प्रत्येक के पास कुछ कंचे थे। कविता के पास रवि से 12 कंचे अधिक थे। यदि उनमें से प्रत्येक के पास एक कंचा अधिक होता, तो तब कविता के पास मौजूद कंचों की संख्&zwj;या का तीन गुना, तब रवि के पास मौजूद कंचों की संख्या के चार गुने के बराबर होते। कविता के पास वास्तव में कितने कंचे थे ?</p>",
                    options_en: [
                        "<p>43</p>",
                        "<p>47</p>",
                        "<p>51</p>",
                        "<p>48</p>"
                    ],
                    options_hi: [
                        "<p>43</p>",
                        "<p>47</p>",
                        "<p>51</p>",
                        "<p>48</p>"
                    ],
                    solution_en: "<p>52.(b) Let number of marbles Ravi had = x<br>Number of Marbles Kavita had = x&nbsp;+ 12<br>According to question , <br>&rArr; 3 (x + 13) = 4 (x + 1)<br>&rArr; 3x + 39 = 4x + 4<br>&rArr; x = 35 <br>&there4; Number of Marbles Kavita had (x&nbsp;+ 12) = 35 + 12 = 47</p>",
                    solution_hi: "<p>52.(b) मान लीजिए कि रवि के पास कंचों की संख्या = x<br>कविता के पास कंचों की संख्या = x&nbsp;+ 12<br>प्रश्न के अनुसार, <br>&rArr; 3 (x + 13) = 4 (x + 1)<br>&rArr; 3x + 39 = 4x + 4<br>&rArr; x = 35<br>&there4; कविता के पास कंचों की संख्या (x&nbsp;+ 12) = 35 + 12 = 47</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. What is the value of tan 570&deg; ?</p>",
                    question_hi: "<p>53. tan 570&deg; का मान कितना होगा ?</p>",
                    options_en: [
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>53.(c) <br>According to the question,<br>tan 570&deg; = tan(180&deg; &times; 3 + 30&deg;)&nbsp; &nbsp; [<math display=\"inline\"><mo>&#8757;</mo><mi>&#160;</mi></math>the value of tan&theta; in 3rd quadrant is positive]<br>tan30&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math>&nbsp;</p>",
                    solution_hi: "<p>53.(c) <br>प्रश्न के अनुसार,<br>tan 570&deg; = tan(180&deg; &times; 3 + 30&deg;)&nbsp; &nbsp; &nbsp;[∵ तीसरे चतुर्थांश में tan&theta; का मान धनात्मक होता है]<br>tan30&deg; = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. If <math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 7, find the value of x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>.</p>",
                    question_hi: "<p>54. यदि <math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 7 है, तो x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>343</p>",
                        "<p>332</p>",
                        "<p>340</p>",
                        "<p>322</p>"
                    ],
                    options_hi: [
                        "<p>343</p>",
                        "<p>332</p>",
                        "<p>340</p>",
                        "<p>322</p>"
                    ],
                    solution_en: "<p>54.(d) <strong>Given : </strong>x&nbsp;+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>&nbsp;= 7<br>We know that,<br><math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = a then x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = a<sup>3</sup> - 3a<br>So,<br>x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = 7<sup>3</sup> - 3 &times; 7 = 343 - 21 = 322</p>",
                    solution_hi: "<p>54.(d) <strong>दिया है: </strong>x&nbsp;+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 7<br>हम जानते है कि,<br><math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = a तो x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = a<sup>3</sup> - 3a<br>इसलिए,<br>x<sup>3</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = 7<sup>3</sup> - 3 &times; 7 = 343 - 21 = 322</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. A shopkeeper sells his goods using weights 27% less than true weights and claims to sell his goods at the cost price. His gain per cent (rounded off to 1 decimal place) is:</p>",
                    question_hi: "<p>55. एक दुकानदार अपना सामान वास्तविक भार से 27% कम भार के बाट का उपयोग करके बेचता है और क्रय मूल्य पर अपना सामान बेचने का दावा करता है। उसका लाभ प्रतिशत (दशमलव के 1 स्थान तक सन्निकटित) कितना है ?</p>",
                    options_en: [
                        "<p>39.8</p>",
                        "<p>47.9</p>",
                        "<p>37.0</p>",
                        "<p>40.0</p>"
                    ],
                    options_hi: [
                        "<p>39.8</p>",
                        "<p>47.9</p>",
                        "<p>37.0</p>",
                        "<p>40.0</p>"
                    ],
                    solution_en: "<p>55.(c)<br>27% = <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>CP of goods for shopkeeper = (100 - 27) = ₹73<br>SP of goods for shopkeeper = ₹100 <br>Profit% = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>73</mn></mrow><mrow><mn>73</mn></mrow></mfrac></math> &times; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2700</mn><mn>73</mn></mfrac></math> = 36.98 &asymp; 37%</p>",
                    solution_hi: "<p>55.(c)<br>27% = <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>दुकानदार के लिए माल का क्रय मूल्य = (100 - 27) = ₹73<br>दुकानदार के लिए सामान का विक्रय मूल्य = ₹100&nbsp;<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>73</mn></mrow><mrow><mn>73</mn></mrow></mfrac></math> &times; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2700</mn><mn>73</mn></mfrac></math> = 36.98 &asymp; 37%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Find the value of the following expression.<br>2 &times; 8 &divide; 4 - 5 of 3 + 6 &divide; 3 &times; 2 - 6 of 3 + 5</p>",
                    question_hi: "<p>56. निम्नलिखित व्यंजक का मान ज्ञात कीजिए।<br>2 &times; 8 &divide; 4 - 5 का 3 + 6 &divide; 3 &times; 2 - 6 का 3 + 5</p>",
                    options_en: [
                        "<p>&minus;15</p>",
                        "<p>&minus;20</p>",
                        "<p>20</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>&minus;15</p>",
                        "<p>&minus;20</p>",
                        "<p>20</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>56.(b)<br>2 &times; 8 &divide; 4 - 5 of 3 + 6 &divide; 3 &times; 2 - 6 of 3 + 5<br>= 4 - 15 + 4 - 18 + 5 = -20</p>",
                    solution_hi: "<p>56.(b)<br>2 &times; 8 &divide; 4 - 5 का 3 + 6 &divide; 3 &times; 2 - 6 का 3 + 5<br>= 4 - 15 + 4 - 18 + 5 = -20</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Find LCM of <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>10</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>20</mn></mfrac></math>.</p>",
                    question_hi: "<p>57. <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>10</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>20</mn></mfrac></math>&nbsp;का लघुत्तम समपवर्त्य ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>63</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>63</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>57.(d)<br>LCM of (<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>10</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>20</mn></mfrac></math>) </p>\n<p>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mi>C</mi><mi>M</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mo>(</mo><mn>9</mn><mo>,</mo><mn>21</mn><mo>,</mo><mn>63</mn><mo>)</mo></mrow><mrow><mi>H</mi><mi>C</mi><mi>F</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mo>(</mo><mn>4</mn><mo>,</mo><mn>10</mn><mo>,</mo><mn>20</mn><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>57.(d)<br>(<math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>10</mn></mfrac></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>20</mn></mfrac></math>) का महत्तम समापवर्तक</p>\n<p>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>9</mn><mo>,</mo><mn>21</mn><mo>,</mo><mn>63</mn><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2354;&#2328;&#2369;&#2340;&#2381;&#2340;&#2350;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2346;&#2357;&#2352;&#2381;&#2340;&#2381;&#2351;</mi></mrow><mrow><mo>(</mo><mn>4</mn><mo>,</mo><mn>10</mn><mo>,</mo><mn>20</mn><mo>)</mo><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2350;&#2361;&#2340;&#2381;&#2340;&#2350;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2366;&#2346;&#2357;&#2352;&#2381;&#2340;&#2325;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>2</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. The difference between 31% and 26% of votes for the same party at different places is 2350. What is 7% of those votes ?</p>",
                    question_hi: "<p>58. एक ही पार्टी को अलग-अलग थानों पर प्राप्त मतों के 31% और 26% मतों के बीच का अंतर 2350 है। इन मतों का 7% कितना है ?</p>",
                    options_en: [
                        "<p>2270</p>",
                        "<p>4090</p>",
                        "<p>3090</p>",
                        "<p>3290</p>"
                    ],
                    options_hi: [
                        "<p>2270</p>",
                        "<p>4090</p>",
                        "<p>3090</p>",
                        "<p>3290</p>"
                    ],
                    solution_en: "<p>58.(d) According to question,<br>&rArr; 31% - 26% = 5% = 2350<br>&rArr; 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2350</mn><mn>5</mn></mfrac></math> &times; 100 = 47000<br>Now, <br>&rArr; 47000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>100</mn></mfrac></math> = 3290</p>",
                    solution_hi: "<p>58.(d) प्रश्न के अनुसार,<br>&rArr; 31% - 26% = 5% = 2350<br>&rArr; 100% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2350</mn><mn>5</mn></mfrac></math> &times; 100 = 47000<br>अब, <br>&rArr; 47000 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>100</mn></mfrac></math> = 3290</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. A man standing on the banks of a river observes that the angle subtended by a tree on the opposite bank is 60&deg;. He walks 36 meters backward on the bank and observes the angle to be 30&deg;. What is the breadth of the river ?</p>",
                    question_hi: "<p>59. एक नदी के किनारे खड़ा एक व्यक्ति देखता है कि एक पेड़ द्वारा विपरीत किनारे पर बनाया गया कोण 60&deg; है। वह किनारे पर 36 मीटर पीछे चलता है और कोण को 30&deg; पाताहै। नदी की चौड़ाई कितनी है ?</p>",
                    options_en: [
                        "<p>10 meters</p>",
                        "<p>18 Meters</p>",
                        "<p>20 Meters</p>",
                        "<p>28 meters</p>"
                    ],
                    options_hi: [
                        "<p>10 मीटर</p>",
                        "<p>18 मीटर</p>",
                        "<p>20 मीटर</p>",
                        "<p>28 मीटर</p>"
                    ],
                    solution_en: "<p>59.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699464206.png\" alt=\"rId70\" width=\"133\" height=\"143\"><br>In <math display=\"inline\"><mi>&#916;</mi></math>ABC,<br>tan 60&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math><br>&rArr; AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>x ----(1)<br>In <math display=\"inline\"><mi>&#916;</mi></math> ABD,<br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>x</mi><mo>+</mo><mn>36</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>x</mi><mo>+</mo><mn>36</mn></mrow></mfrac></math><br>&rArr; AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>36</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac></math> ----(2)<br>From equation (1) and (2),<br><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>36</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br>&rArr; 3x = x + 36<br>&rArr; x = 18 m (breadth of the river)</p>",
                    solution_hi: "<p>59.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699464206.png\" alt=\"rId70\" width=\"133\" height=\"143\"><br>त्रिभुज ABC में,<br>tan 60&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math><br>&rArr; AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>x ----(1)<br>त्रिभुज ABD में,<br>tan 30&deg; = <math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>x</mi><mo>+</mo><mn>36</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>B</mi></mrow><mrow><mi>x</mi><mo>+</mo><mn>36</mn></mrow></mfrac></math><br>&rArr; AB = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>36</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac></math> ----(2)<br>समीकरण (1) और (2) से,<br><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>36</mn></mrow><msqrt><mn>3</mn></msqrt></mfrac></math><br>&rArr; 3x = x + 36<br>&rArr; x = 18मीटर (नदी की चौड़ाई)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Find the fourth proportional to 2, 6, 8.</p>",
                    question_hi: "<p>60. 2, 6, 8 का चतुर्थ समानुपाती ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>36</p>",
                        "<p>24</p>",
                        "<p>12</p>",
                        "<p>48</p>"
                    ],
                    options_hi: [
                        "<p>36</p>",
                        "<p>24</p>",
                        "<p>12</p>",
                        "<p>48</p>"
                    ],
                    solution_en: "<p>60.(b) <br>Fourth proportional = <math display=\"inline\"><mfrac><mrow><mi>b</mi><mi>c</mi></mrow><mrow><mi>a</mi></mrow></mfrac></math><br>Fourth proportional of 2, 6, 8 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>2</mn></mfrac></math> = 24</p>",
                    solution_hi: "<p>60.(b) <br>चतुर्थानुपात = <math display=\"inline\"><mfrac><mrow><mi>b</mi><mi>c</mi></mrow><mrow><mi>a</mi></mrow></mfrac></math><br>2, 6, 8 का चतुर्थानुपात =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>2</mn></mfrac></math>&nbsp;= 24</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. AB is parallel to DC in a trapezium ABCD. It is given that AB &gt; DC and the diagonals AC and BD intersect at O. If AO = 3<math display=\"inline\"><mi>x</mi></math> - 15, OB = x + 9, OC = x - 5 and OD = 5, and x has two values x<sub>1</sub> and x<sub>2</sub>, then the value of (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>x</mi><mn>1</mn><mn>2</mn></msubsup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>x</mi><mn>2</mn><mn>2</mn></msubsup></math>) is:</p>",
                    question_hi: "<p>61. समलंब चतुर्भुज ABCD में AB, DC के समानांतर है। यह दिया गया है कि AB &gt; DC है तथा विकर्ण AC और BD, O पर प्रतिच्छेद करते हैं। यदि AO = 3<math display=\"inline\"><mi>x</mi></math> - 15, OB = x + 9, OC = x - 5 और OD = 5 है, तथा x के दो मान x<sub>1</sub> और x<sub>2</sub> हैं, तो (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>x</mi><mn>1</mn><mn>2</mn></msubsup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>x</mi><mn>2</mn><mn>2</mn></msubsup></math>) का मान क्या है ?</p>",
                    options_en: [
                        "<p>45</p>",
                        "<p>56</p>",
                        "<p>61</p>",
                        "<p>73</p>"
                    ],
                    options_hi: [
                        "<p>45</p>",
                        "<p>56</p>",
                        "<p>61</p>",
                        "<p>73</p>"
                    ],
                    solution_en: "<p>61.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699464453.png\" alt=\"rId71\" width=\"247\" height=\"136\"><br>In trapezium ABCD,<br>The diagonals of a trapezium divide each other proportionally,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>O</mi></mrow><mrow><mi>O</mi><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>O</mi><mi>B</mi></mrow><mrow><mi>O</mi><mi>D</mi></mrow></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi><mo>-</mo><mn>15</mn></mrow><mrow><mi>x</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>9</mn></mrow><mn>5</mn></mfrac></math><br>&rArr; 15x - 75 = x<sup>2</sup> + 9x - 5x - 45<br>&rArr; x<sup>2 </sup>+ 4x - 15x + 75 - 45 = 0<br>&rArr; x<sup>2 </sup>- 11x + 30 = 0 &hellip;&hellip;(i)<br>On solving the eqn. (i), we get<br>&rArr; x = 5 or 6<br>Now,<br>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>x</mi><mn>1</mn><mn>2</mn></msubsup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>x</mi><mn>2</mn><mn>2</mn></msubsup></math>) = (5)<sup>2</sup> + (6)<sup>2</sup> = 25 + 36 = 61</p>",
                    solution_hi: "<p>61.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699464453.png\" alt=\"rId71\" width=\"247\" height=\"136\"><br>समलंब चतुर्भुज ABCD में,<br>एक समलंब चतुर्भुज के विकर्ण एक दूसरे को आनुपातिक रूप से विभाजित करते हैं,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>A</mi><mi>O</mi></mrow><mrow><mi>O</mi><mi>C</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>O</mi><mi>B</mi></mrow><mrow><mi>O</mi><mi>D</mi></mrow></mfrac></math> <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi>x</mi><mo>-</mo><mn>15</mn></mrow><mrow><mi>x</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>9</mn></mrow><mn>5</mn></mfrac></math><br>&rArr; 15x - 75 = x<sup>2</sup> + 9x - 5x - 45<br>&rArr; x<sup>2 </sup>+ 4x - 15x + 75 - 45 = 0<br>&rArr; x<sup>2 </sup>- 11x + 30 = 0 &hellip;&hellip;(i)<br>समीकरण (i) को हल करने पर, हमें मिलता है<br>&rArr; x = 5 या 6<br>अब,<br>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>x</mi><mn>1</mn><mn>2</mn></msubsup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi>x</mi><mn>2</mn><mn>2</mn></msubsup></math>) = (5)<sup>2</sup> + (6)<sup>2</sup> = 25 + 36 = 61</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Trader A gives a single discount of 45% and Trader B gives two successive discounts of 35% and 10% on an identical item of the same marked price. If the discount given by A is ₹539 more than the discount given by Trader B, then what is the marked price (in ₹) of the item ?</p>",
                    question_hi: "<p>62. व्यापारी A, 45% की एकल छूट देता है और व्यापारी B समान अंकित मूल्य वाली एक समान वस्तु पर 35% और 10% की दो क्रमिक छूट देता है। यदि व्यापारी A द्वारा दी गई छूट, व्यापारी B द्वारा दी गई छूट से ₹539 अधिक है, तो वस्तु का अंकित मूल्य (₹ में) कितना है ?</p>",
                    options_en: [
                        "<p>15,600</p>",
                        "<p>16,500</p>",
                        "<p>14,500</p>",
                        "<p>15,400</p>"
                    ],
                    options_hi: [
                        "<p>15,600</p>",
                        "<p>16,500</p>",
                        "<p>14,500</p>",
                        "<p>15,400</p>"
                    ],
                    solution_en: "<p>62.(d) Let the marked price be 100<br>According to question,<br>&rArr; 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math>% - 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>100</mn></mfrac></math>% = ₹539<br>&rArr; 58.5% - 55% = ₹539<br>&rArr; 3.5% = ₹539<br>&rArr; 100% = ₹ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8377;</mo><mn>539</mn></mrow><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times; 100 = ₹ 15400</p>",
                    solution_hi: "<p>62.(d) अंकित मूल्य 100 है। <br>प्रश्न के अनुसार,<br>&rArr; 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math>% - 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>100</mn></mfrac></math>% = ₹539<br>&rArr; 58.5% - 55% = ₹539<br>&rArr; 3.5% = ₹539<br>&rArr; 100% = ₹ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#8377;</mo><mn>539</mn></mrow><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> &times; 100 = ₹ 15400</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. A train starts from station P with \'n\' number of passengers. At station Q, 20% of the passengers get down and 50 passengers get in. At station R, 40% of the passengers get down and 10 passengers get in. If a total of 520 passengers are left on the train, find the value of \'n\'.</p>",
                    question_hi: "<p>63. एक ट्रेन स्टेशन P से यात्रियों की संख्या \'n\' के साथ चलना शुरू होती है। स्टेशन Q पर, 20% यात्री उतर जाते हैं और 50 यात्री चढ़ जाते हैं। स्टेशन R पर, 40% यात्री उतर जाते हैं और 10 यात्री चढ़ जाते हैं। यदि ट्रेन में कुल 520 यात्री बचे हैं, तो \'n\' का मान ज्ञात करें।</p>",
                    options_en: [
                        "<p>855</p>",
                        "<p>1000</p>",
                        "<p>750</p>",
                        "<p>975</p>"
                    ],
                    options_hi: [
                        "<p>855</p>",
                        "<p>1000</p>",
                        "<p>750</p>",
                        "<p>975</p>"
                    ],
                    solution_en: "<p>63.(b)<br>ATQ,<br>((<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>n + 50) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>) + 10 = 520<br>((<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>n + 50) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>) = 510<br>(<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>n + 50) = 850<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>n = 800<br>n = 800 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 1000</p>",
                    solution_hi: "<p>63.(b)<br>प्रश्न के अनुसार ,<br>((<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>n + 50) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>) + 10 = 520<br>((<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>n + 50) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math>) = 510<br>(<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>n + 50) = 850<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>n = 800<br>n = 800 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 1000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Answer the following question on the basis of the bar graph given.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699464627.png\" alt=\"rId72\" width=\"386\" height=\"259\"> <br>In 1991, what percentage of the total revenue came from journals ? (Correct up to one decimal place)</p>",
                    question_hi: "<p>64. दिए गए बार ग्राफ के आधार पर निम्नलिखित प्रश्न का उत्तर दें।<br>प्रकाशक को समाचार-पत्रो, पत्रिकाओं और पुस्तकों से हुई आमदनी<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699464808.png\" alt=\"rId73\" width=\"373\" height=\"242\"> <br>1991 में, कुल आमदनी का कितना प्रतिशत जर्नल से आया ? (दशमलव के बाद एक स्थान तक पूर्णांकित)</p>",
                    options_en: [
                        "<p>25.8%</p>",
                        "<p>22.9%</p>",
                        "<p>24.73%</p>",
                        "<p>26.5%</p>"
                    ],
                    options_hi: [
                        "<p>25.8%</p>",
                        "<p>22.9%</p>",
                        "<p>24.73%</p>",
                        "<p>26.5%</p>"
                    ],
                    solution_en: "<p>64.(d)<br>Total revenue of 1991 = (45 + 45 + 80) = 170 lakh<br>Revenue came from journals = 45<br>So, required % = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>170</mn></mrow></mfrac></math> &times; 100 = 26.5%</p>",
                    solution_hi: "<p>64.(d)<br>1991 का कुल राजस्व = (45 + 45 + 80) = 170 लाख<br>जर्नल से प्राप्त राजस्व = 45<br>तो, आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>170</mn></mrow></mfrac></math> &times; 100 = 26.5%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. If cot A = 7, then the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>4</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#179;</mo><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>7</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#179;</mo><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>6</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math> is :</p>",
                    question_hi: "<p>65. यदि cot A = 7 है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#160;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>4</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mo>&#179;</mo><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>7</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mo>&#179;</mo><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>6</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math>&nbsp;का मान ज्ञात करें।</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>0</p>",
                        "<p>3</p>",
                        "<p>1</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>0</p>",
                        "<p>3</p>",
                        "<p>1</p>"
                    ],
                    solution_en: "<p>65.(c),<br>cotA = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>b</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow></mfrac></math><br>Hypotenuse = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>7</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>Hypotenuse = <math display=\"inline\"><msqrt><mn>50</mn></msqrt></math> = 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br>Now, <br><math display=\"inline\"><mfrac><mrow><mn>5</mn><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>7</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mrow></mfenced><mo>+</mo><mn>4</mn><mi>&#160;</mi><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mrow></mfenced></mrow><mrow><msup><mrow><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>7</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mrow></mfenced></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mn>7</mn><mi>&#160;</mi><msup><mrow><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mrow></mfenced></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><mn>6</mn><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mrow></mfenced></mrow></mfrac></math>&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfenced><mfrac><mn>7</mn><msqrt><mn>2</mn></msqrt></mfrac></mfenced><mo>+</mo><mfenced><mfrac><mn>4</mn><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mfenced></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>343</mn><mrow><mn>250</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mrow><mn>250</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle></mrow></mfrac></math></p>\n<p>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfenced><mfrac><mrow><mn>35</mn><mo>+</mo><mn>4</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mfenced><mstyle displaystyle=\"true\"><mfrac><mrow><mn>343</mn><mo>+</mo><mn>7</mn><mo>+</mo><mn>300</mn></mrow><mrow><mn>250</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>39</mn><mo>&#215;</mo><mn>250</mn><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mn>650</mn></mrow></mfrac></math> = 3<br><strong>Alternate method:</strong></p>\n<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>4</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>3</mn></msup><mi>A</mi><mo>+</mo><mn>7</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>3</mn></msup><mi>A</mi><mo>+</mo><mn>6</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math><br>Divide numerator and denominator by sin A<strong id=\"docs-internal-guid-65ca6637-7fff-340f-fa75-8b27a47b1c04\"> </strong><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mo>+</mo><mn>4</mn><mo>&#215;</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></mrow><mrow><mfrac><mrow><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>3</mn></msup><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mo>+</mo><mn>7</mn><mfrac><mrow><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>3</mn></msup><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mo>+</mo><mn>6</mn><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>4</mn><mo>&#215;</mo><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>&#215;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>7</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>+</mo><mn>6</mn></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mn>4</mn><mo>&#215;</mo><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>&#215;</mo><mn>7</mn><mo>+</mo><mn>7</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>+</mo><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>4</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mo>(</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo><mo>+</mo><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>7</mn><mo>+</mo><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>13</mn></mfrac></math> = 3</p>",
                    solution_hi: "<p>65.(c),<br>cotA = <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2354;&#2306;&#2348;</mi></mfrac></math><br>कर्ण = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>7</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>कर्ण = <math display=\"inline\"><msqrt><mn>50</mn></msqrt></math> = 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math><br>अब,</p>\n<p><math display=\"inline\"><mfrac><mrow><mn>5</mn><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>7</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mrow></mfenced><mo>+</mo><mn>4</mn><mi>&#160;</mi><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mrow></mfenced></mrow><mrow><msup><mrow><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>7</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mrow></mfenced></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mn>7</mn><mi>&#160;</mi><msup><mrow><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mrow></mfenced></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><mn>6</mn><mfenced separators=\"|\"><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mrow></mfenced></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfenced><mfrac><mn>7</mn><msqrt><mn>2</mn></msqrt></mfrac></mfenced><mo>+</mo><mfenced><mfrac><mn>4</mn><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mfenced></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>343</mn><mrow><mn>250</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mrow><mn>250</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle></mrow></mfrac></math></p>\n<p>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfenced><mfrac><mrow><mn>35</mn><mo>+</mo><mn>4</mn></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mfenced><mstyle displaystyle=\"true\"><mfrac><mrow><mn>343</mn><mo>+</mo><mn>7</mn><mo>+</mo><mn>300</mn></mrow><mrow><mn>250</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>39</mn><mo>&#215;</mo><mn>250</mn><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>5</mn><msqrt><mn>2</mn></msqrt><mo>&#215;</mo><mn>650</mn></mrow></mfrac></math> = 3</p>\n<p><strong>वैकल्पिक विधि :</strong> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>4</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>3</mn></msup><mi>A</mi><mo>+</mo><mn>7</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>3</mn></msup><mi>A</mi><mo>+</mo><mn>6</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></math></p>\n<p>अंश और हर को sin A से विभाजित करने पर</p>\n<p>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mo>+</mo><mn>4</mn><mo>&#215;</mo><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></mrow><mrow><mfrac><mrow><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>3</mn></msup><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mo>+</mo><mn>7</mn><mfrac><mrow><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>3</mn></msup><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac><mo>+</mo><mn>6</mn><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mi>A</mi></mrow></mfrac></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>4</mn><mo>&#215;</mo><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>&#215;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&#160;</mi><mi>A</mi><mo>+</mo><mn>7</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>+</mo><mn>6</mn></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#215;</mo><mn>7</mn><mo>+</mo><mn>4</mn><mo>&#215;</mo><mn>1</mn></mrow><mrow><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>&#215;</mo><mn>7</mn><mo>+</mo><mn>7</mn><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>+</mo><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>4</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mo>(</mo><mi>c</mi><mi>o</mi><msup><mi>s</mi><mn>2</mn></msup><mi>A</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi><mo>)</mo><mo>+</mo><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>39</mn></mrow><mrow><mn>7</mn><mo>+</mo><mn>6</mn><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39</mn><mn>13</mn></mfrac></math> = 3</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A sum of ₹5,000 was lent to two people, one at the rate of 16% and the other at the rate of 24%. If the simple interest after one year is ₹960, what is the sum lent at the higher rate ?</p>",
                    question_hi: "<p>66. ₹5,000 की धनराशि दो लोगों को ऋण पर दी गई, एक को 16% की दर पर और दूसरे को 24% की दर पर। यदि एक वर्ष के बाद साधारण ब्याज ₹960 है , तो उच्च दर पर ऋण पर दी गई धनराशि कितनी है ?</p>",
                    options_en: [
                        "<p>₹2,500</p>",
                        "<p>₹2,000</p>",
                        "<p>₹3,000</p>",
                        "<p>₹1,500</p>"
                    ],
                    options_hi: [
                        "<p>₹2,500</p>",
                        "<p>₹2,000</p>",
                        "<p>₹3,000</p>",
                        "<p>₹1,500</p>"
                    ],
                    solution_en: "<p>66.(b)<br>Rate% = <math display=\"inline\"><mfrac><mrow><mn>960</mn></mrow><mrow><mn>5000</mn></mrow></mfrac></math> &times; 100 = 19.2%<br>Now, we have ;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699464952.png\" alt=\"rId74\" width=\"108\" height=\"133\"><br>So, the sum lent at the higher rate = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> &times; 5000 = ₹2000</p>",
                    solution_hi: "<p>66.(b)<br>दर% = <math display=\"inline\"><mfrac><mrow><mn>960</mn></mrow><mrow><mn>5000</mn></mrow></mfrac></math> &times; 100 = 19.2%<br>अब, हमारे पास है;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699464952.png\" alt=\"rId74\" width=\"108\" height=\"133\"><br>तो, उच्च दर पर उधार दी गई राशि = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> &times; 5000 = ₹2000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A is four times as good a workman as B and together they finish a piece of work in <math display=\"inline\"><mfrac><mrow><mn>84</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> days. In how many days can A alone finish the work ?</p>",
                    question_hi: "<p>67. A, B से चार गुना अच्छा कारीगर है और दोनों साथ मिलकर एक काम <math display=\"inline\"><mfrac><mrow><mn>84</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> दिनों में पूरा करते हैं। A अकेले वह काम कितने दिनों में पूरा कर सकता है ?</p>",
                    options_en: [
                        "<p>15 days</p>",
                        "<p>7 days</p>",
                        "<p>9 days</p>",
                        "<p>10 days</p>"
                    ],
                    options_hi: [
                        "<p>15 दिन</p>",
                        "<p>7 दिन</p>",
                        "<p>9 दिन</p>",
                        "<p>10 दिन</p>"
                    ],
                    solution_en: "<p>67.(b)<br>Efficiency of A and B = 4 : 1<br>Total work = (4 + 1) &times; <math display=\"inline\"><mfrac><mrow><mn>84</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 28 unit<br>Time taken by A alone to finish the work = <math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 7 days</p>",
                    solution_hi: "<p>67.(b)<br>A और B की दक्षता = 4 : 1<br>कुल कार्य = (4 + 1) &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>84</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> = 28 इकाई<br>A द्वारा अकेले कार्य समाप्त करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 7 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. From a circle of radius 7 units, an arc length is cut by a chord of length 7 units. What is the arc length of the smaller portion (in units) ?</p>",
                    question_hi: "<p>68. 7 इकाई की त्रिज्या वाले एक वृत्त से 7 इकाई लंबाई की जीवा से एक चाप काटा जाता है। छोटे हिस्से के चाप की लंबाई (इकाई में) क्या है ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &pi;</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &pi;</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &pi;</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &pi;</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &pi;</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> &pi;</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &pi;</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &pi;</p>"
                    ],
                    solution_en: "<p>68.(c) According to the question,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699465090.png\" alt=\"rId75\" width=\"177\" height=\"164\"><br>Arc length of smaller portion = 2<math display=\"inline\"><mi>&#960;</mi></math>r &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math><br>= 2<math display=\"inline\"><mi>&#960;</mi></math> &times; 7 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac><mi>&#960;</mi></math> units</p>",
                    solution_hi: "<p>68.(c) प्रश्न के अनुसार <br><strong id=\"docs-internal-guid-719857f0-7fff-aa47-ff55-220ca1d7f5bc\"></strong><strong id=\"docs-internal-guid-719857f0-7fff-aa47-ff55-220ca1d7f5bc\"></strong><strong id=\"docs-internal-guid-719857f0-7fff-aa47-ff55-220ca1d7f5bc\"></strong><strong id=\"docs-internal-guid-719857f0-7fff-aa47-ff55-220ca1d7f5bc\"></strong><strong id=\"docs-internal-guid-d8105e20-7fff-c2b3-9ee5-19a1032fae21\"></strong><strong id=\"docs-internal-guid-d8105e20-7fff-c2b3-9ee5-19a1032fae21\"></strong><strong id=\"docs-internal-guid-ac02cdfa-7fff-5ac9-7174-09301053a628\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXejsUr1B4uEeu6ssGmngAeLKpFMu91bHIj66C0fLg6iAaLJpljAyXZo4b51shsrBTzF8E7yM4dC5bEjZR8dhROrLpsx72C9LJWTi_pbOst7uvu_NoZkoQnvYQaAg1C34H6ITVNFqQ?key=mwbtRScIzQF5BxYB4uOCSCLW\" width=\"187\" height=\"186\"></strong><br>छोटे भाग की चाप की लंबाई = 2<math display=\"inline\"><mi>&#960;</mi></math>r &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math><br>= 2<math display=\"inline\"><mi>&#960;</mi></math> &times; 7 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac><mi>&#960;</mi></math> इकाई</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. At a fitness club, 70% of the members are men and 30% of the members are women. If the average age of the men is 30 years and that of the women is 40 years, then what is the average age of all the members ?</p>",
                    question_hi: "<p>69. एक फिटनेस क्लब में 70% सदस्य पुरुष हैं और 30% सदस्य महिलाएँ हैं। यदि पुरुषों की औसत आयु 30 वर्ष और महिलाओं की औसत आयु 40 वर्ष है, तो सभी सदस्यों की औसत आयु कितनी है ?</p>",
                    options_en: [
                        "<p>38 years</p>",
                        "<p>33 years</p>",
                        "<p>35 years</p>",
                        "<p>30 years</p>"
                    ],
                    options_hi: [
                        "<p>38 वर्ष</p>",
                        "<p>33 वर्ष</p>",
                        "<p>35 वर्ष</p>",
                        "<p>30 वर्ष</p>"
                    ],
                    solution_en: "<p>69.(b)<br>Let total members = 100x<br>&rArr; total men = 70x<br>And total women = 30x<br>Average age of men = 30 years<br>&rArr; total age of men = 70x &times; 30 = 2100x years <br>And average age of women = 40 years<br>&rArr; total age of women = 30x &times; 40 = 1200x years<br>Now, <br>Average age of all members(A) = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>a</mi><mi>g</mi><mi>e</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>a</mi><mi>l</mi><mi>l</mi><mi>&#160;</mi><mi>m</mi><mi>e</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>s</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>m</mi><mi>e</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>s</mi></mrow></mfrac></math><br>&rArr; A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2100</mn><mo>&#160;</mo><mi>x</mi><mo>+</mo><mn>1200</mn><mo>&#160;</mo><mi>x</mi></mrow><mrow><mn>100</mn><mo>&#160;</mo><mi>x</mi></mrow></mfrac></math><br>&rArr; A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3300</mn><mo>&#160;</mo><mi>x</mi></mrow><mrow><mn>100</mn><mo>&#160;</mo><mi>x</mi></mrow></mfrac></math> = 33 years</p>",
                    solution_hi: "<p>69.(b)<br>माना कुल सदस्य = 100x<br>&rArr; कुल पुरुष = 70x<br>और कुल महिलाएँ = 30x<br>पुरुषों की औसत आयु = 30 वर्ष<br>&rArr; पुरुषों की कुल आयु = 70x &times; 30 = 2100x वर्ष <br>तथा महिलाओं की औसत आयु = 40 वर्ष<br>&rArr; महिलाओं की कुल आयु = 30x &times; 40 = 1200x वर्ष<br>अब, <br>सभी सदस्यों की औसत आयु(A) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2360;&#2349;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2342;&#2360;&#2381;&#2351;&#2379;&#2306;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2310;&#2351;&#2369;</mi></mrow><mrow><mi>&#2360;&#2349;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2342;&#2360;&#2381;&#2351;&#2379;&#2306;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow></mfrac></math><br>&rArr; A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2100</mn><mo>&#160;</mo><mi>x</mi><mo>+</mo><mn>1200</mn><mo>&#160;</mo><mi>x</mi></mrow><mrow><mn>100</mn><mo>&#160;</mo><mi>x</mi></mrow></mfrac></math><br>&rArr; A = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3300</mn><mo>&#160;</mo><mi>x</mi></mrow><mrow><mn>100</mn><mo>&#160;</mo><mi>x</mi></mrow></mfrac></math> = 33 वर्ष</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Mohan gets 12% increase in his sale amount in the first year and 15% increase in the second year, with that his present sale is ₹1,28,800. What was his sale two years ago ?</p>",
                    question_hi: "<p>70. मोहन को पहले वर्ष में उसकी बिक्री राशि में 12% की वृद्धि प्राप्त होती है और दूसरे वर्ष में 15% की वृद्धि प्राप्त होती है, और अब उसकी वर्तमान बिक्री ₹1,28,800 है। दो वर्ष पूर्व उसकी बिक्री कितनी थी ?</p>",
                    options_en: [
                        " ₹1,75,000",
                        " ₹1,25,000 ",
                        " ₹1,00,000 ",
                        " ₹1,50,000"
                    ],
                    options_hi: [
                        " ₹1,75,000",
                        " ₹1,25,000 ",
                        " ₹1,00,000 ",
                        " ₹1,50,000"
                    ],
                    solution_en: "<p>70.(c) 12 % = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, 15 % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>20</mn></mfrac></math><br>Let the sale of 2 years ago = ₹ x<br><math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> = 128800<br><math display=\"inline\"><mi>x</mi></math> = 100,000</p>",
                    solution_hi: "<p>70.(c) 12 % = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, 15 % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>20</mn></mfrac></math><br>माना 2 वर्ष पहले की बिक्री = ₹ <math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> = 128800<br><math display=\"inline\"><mi>x</mi></math> = 100,000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A shopkeeper sells an item at 20% discount on the marked price and earns a profit of 90%. If he sells the same item at 40% discount, then his new profit percentage will be:</p>",
                    question_hi: "<p>71. एक दुकानदार अंकित मूल्य पर 20% छूट पर एक वस्तु बेचता है और 90% का लाभ अर्जित करता है। यदि वह उसी वस्तु को 40% छूट पर बेचता है, तो उसका नया लाभ प्रतिशत क्या होगा ?</p>",
                    options_en: [
                        "<p>48.2%</p>",
                        "<p>45.8%</p>",
                        "<p>42.5%</p>",
                        "<p>41.8%</p>"
                    ],
                    options_hi: [
                        "<p>48.2%</p>",
                        "<p>45.8%</p>",
                        "<p>42.5%</p>",
                        "<p>41.8%</p>"
                    ],
                    solution_en: "<p>71.(c) <br><math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi><mo>&#160;</mo><mo>%</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>190</mn><mn>80</mn></mfrac></math><br>Now, after selling the same item at 40% discount<br>New selling price = 190 &times; 60% = 114<br>So, new profit % = <math display=\"inline\"><mfrac><mrow><mn>114</mn><mi>&#160;</mi><mo>-</mo><mn>80</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> &times; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>4</mn></mfrac></math> &times; 5 = 42.5%</p>",
                    solution_hi: "<p>71.(c) <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>&#2331;&#2370;&#2335;</mi><mo>%</mo></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>190</mn><mn>80</mn></mfrac></math><br>अब, उसी वस्तु को 40% छूट पर बेचने के बाद<br>नया विक्रय मूल्य = 190 &times; 60% = 114<br>तो, नया लाभ % = <math display=\"inline\"><mfrac><mrow><mn>114</mn><mi>&#160;</mi><mo>-</mo><mn>80</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> &times; 100 <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>34</mn><mn>4</mn></mfrac></math> &times; 5 = 42.5%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. Two pipes A and B can fill a tank in 20 and 30 hours, respectively. Both pipes are opened to fill the tank, but when the tank is one-third full, a leak develops through which one-fourth of the water supplied by both pipes goes out. Find the total time (in hours) taken to fill the tank.</p>",
                    question_hi: "<p>72. दो पाइप A और B एक टंकी को क्रमशः 20 और 30 घंटे में भर सकते हैं। टंकी को भरने के लिए दोनों पाइप खोले जाते हैं, लेकिन जब टंकी एक तिहाई भर जाती है, तो एक रिसाव विकसित होता है जिससे दोनों पाइपों द्वारा भरा गया एक-चौथाई पानी निकल जाता है। टंकी को भरने में लगने वाला कुल समय (घंटे में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>14</p>",
                        "<p>11<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p>14</p>",
                        "<p>11<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>72.(a) <br>Two pipes can separately fill a tank in 20 hrs and 30 hrs respectively<br>Time taken by the two pipes to fill the tank = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>20</mn><mo>+</mo><mn>30</mn></mrow></mfrac></math> = 12 hours <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of the tank if filled = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>3</mn></mfrac></math> = 4 hours <br>Now , <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> of the supplied water leak out<br>&rArr; the filler pipes earlier efficiency = 1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> units <br>Work done complete = (12 - 4) &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>3</mn></mfrac></math> = 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours <br>So, Total time = 4 + 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> hours</p>",
                    solution_hi: "<p>72.(a) दो पाइप अलग-अलग एक टैंक को क्रमशः 20 घंटे और 30 घंटे में भर सकते हैं<br>दोनों पाइपों द्वारा टंकी को भरने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mo>&#215;</mo><mn>30</mn></mrow><mrow><mn>20</mn><mo>+</mo><mn>30</mn></mrow></mfrac></math>&nbsp;= 12 घंटे <br>यदि टंकी का <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> भाग भरा हुआ है = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>3</mn></mfrac></math> = 4 घंटे&nbsp;<br>अब, आपूर्ति किए गए पानी में से <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> लीक हो गए हैं<br>&rArr; भराव पाइप की पिछली दक्षता = 1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> इकाई<br>पूरा कार्य = (12 - 4) &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>3</mn></mfrac></math> = 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>&nbsp;घंटे <br>अत: कुल समय = 4 + 10<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 14<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>&nbsp;घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. A public library has an average attendance of 410 on Sundays and 230 for the remaining days. The average attendance per day of a month of 30 days beginning with Sunday would be:</p>",
                    question_hi: "<p>73. एक सार्वजनिक पुस्तकालय में रविवार को औसत उपस्थिति 410 और शेष दिनों में 230 होती है। रविवार से शुरू होने वाले 30 दिनों के महीने की प्रति दिन औसत उपस्थिति क्या होगी ?</p>",
                    options_en: [
                        "<p>230</p>",
                        "<p>254</p>",
                        "<p>260</p>",
                        "<p>320</p>"
                    ],
                    options_hi: [
                        "<p>230</p>",
                        "<p>254</p>",
                        "<p>260</p>",
                        "<p>320</p>"
                    ],
                    solution_en: "<p>73.(c)<br>As we know, no. of Sunday in the month of 30 days beginning with sunday = 5<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Sunday&nbsp; &nbsp; &nbsp;Remaining days<br>No of days&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; =&nbsp; &nbsp;1&nbsp; :&nbsp; 5<br>Average&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 410&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 230 <br>Overall average = <math display=\"inline\"><mfrac><mrow><mn>410</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mn>230</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>410</mn><mo>+</mo><mn>1150</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1560</mn><mn>6</mn></mfrac></math> = 260</p>",
                    solution_hi: "<p>73.(c)<br>जैसा कि हम जानते हैं, रविवार से शुरू होने वाले 30 दिनों के महीने में रविवार की संख्या = 5<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; रविवार&nbsp; :&nbsp; शेष दिन<br>दिनों की संख्या -&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; =&nbsp; 1&nbsp; :&nbsp; 5<br>औसत -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;410&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;230 <br>कुल औसत = <math display=\"inline\"><mfrac><mrow><mn>410</mn><mo>&#215;</mo><mn>1</mn><mo>+</mo><mn>230</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> <br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>410</mn><mo>+</mo><mn>1150</mn></mrow><mn>6</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1560</mn><mn>6</mn></mfrac></math> = 260</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. In a circular path of 619 m, Preeti and Rani start walking in opposite directions from the same point at the speed of 2.85 km/h and 5.4 km/h, respectively. When will they meet for the first time approximately ?</p>",
                    question_hi: "<p>74. 619 मीटर के एक वृत्ताकार पथ में, प्रीति और रानी एक ही बिंदु से विपरीत दिशाओं में क्रमशः 2.85 किमी/घंटा और 5.4 किमी/घंटा की गति से चलना शुरू करती हैं। वे पहली बार लगभग किस समय मिलेंगी ?</p>",
                    options_en: [
                        "<p>After 4.75 minutes</p>",
                        "<p>After 6.05 minutes</p>",
                        "<p>After 4.50 minutes</p>",
                        "<p>After 6.75 minutes</p>"
                    ],
                    options_hi: [
                        "<p>4.75 मिनट के बाद</p>",
                        "<p>6.05 मिनट के बाद</p>",
                        "<p>4.50 मिनट के बाद</p>",
                        "<p>6.75 मिनट के बाद</p>"
                    ],
                    solution_en: "<p>74.(c)<br>Distance = 619 m = 0.619 km<br>Relative speed when they are in opposite direction = 2.85 + 5.4 = 8.25 km/hr <br>Meeting time = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>619</mn></mrow><mrow><mn>8</mn><mo>.</mo><mn>25</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 60&nbsp;= <math display=\"inline\"><mfrac><mrow><mn>3714</mn></mrow><mrow><mn>825</mn></mrow></mfrac></math> = 4.50 min</p>",
                    solution_hi: "<p>74.(c)<br>दूरी = 619 m = 0.619 km<br>जब वे विपरीत दिशा में हों तो सापेक्ष गति = 2.85 + 5.4 = 8.25 km/hr <br>मिलने का समय = <math display=\"inline\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>619</mn></mrow><mrow><mn>8</mn><mo>.</mo><mn>25</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 60 = <math display=\"inline\"><mfrac><mrow><mn>3714</mn></mrow><mrow><mn>825</mn></mrow></mfrac></math>= 4.50 मिनट</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Study the given pie-chart and answer the question that follows.<br>The pie-chart represents the total number of valid votes obtained by four students who contested for school leadership. The total number of valid votes polled was 720.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699465271.png\" alt=\"rId76\" width=\"263\" height=\"231\"> <br>What is the minimum number of votes obtained by any candidate ?</p>",
                    question_hi: "<p>75. दिए गए पाई चार्ट का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए। यह पाई-चार्ट में स्कूल लीडरशिप के लिए चुनाव लड़ने वाले चार वि&zwnj;द्यार्थियों द्वारा प्राप्त वैध मतों की कुल संख्या का निरूपण करता है। डाले गए वैध मतों की कुल संख्या 720 थी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744699465387.png\" alt=\"rId77\" width=\"239\" height=\"212\"> <br>किसी भी उम्मीदवार को प्राप्त मतों की न्यूनतम संख्या क्या है ?</p>",
                    options_en: [
                        "<p>120</p>",
                        "<p>200</p>",
                        "<p>160</p>",
                        "<p>240</p>"
                    ],
                    options_hi: [
                        "<p>120</p>",
                        "<p>200</p>",
                        "<p>160</p>",
                        "<p>240</p>"
                    ],
                    solution_en: "<p>75.(a)<br>Total number of votes (360&deg;) = 720<br>Minimum number of votes obtained by Yasin (60&deg;) = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 60&deg; = 120</p>",
                    solution_hi: "<p>75.(a)<br>कुल मतों की संख्या (360&deg;) = 720<br>यासीन को प्राप्त वोटों की न्यूनतम संख्या (60&deg;) = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 60&deg; = 120</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been divided into four segments. Identify the segment in which the article has been used INCORRECTLY. <br>UNESCO works to / strengthen an intellectual / and moral solidarity / of humankind.</p>",
                    question_hi: "<p>76. The following sentence has been divided into four segments. Identify the segment in which the article has been used INCORRECTLY. <br>UNESCO works to / strengthen an intellectual / and moral solidarity / of humankind.</p>",
                    options_en: [
                        "<p>strengthen an intellectual</p>",
                        "<p>of humankind.</p>",
                        "<p>UNESCO works to</p>",
                        "<p>and moral solidarity</p>"
                    ],
                    options_hi: [
                        "<p>strengthen an intellectual</p>",
                        "<p>of humankind.</p>",
                        "<p>UNESCO works to</p>",
                        "<p>and moral solidarity</p>"
                    ],
                    solution_en: "<p>76.(a) strengthen an intellectual<br>Indefinite article &lsquo;a/an&rsquo; is not used for an abstract noun(solidarity). The definite article &lsquo;the&rsquo; is used before a specific or particular noun. The given sentence is referring to a specific type of solidarity. Hence, &lsquo;strengthen the intellectual and moral solidarity&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(a) strengthen an intellectual<br>Indefinite article &lsquo;a/an&rsquo; का प्रयोग abstract noun (solidarity) के लिए नहीं किया जाता है। Definite article &lsquo;the&rsquo; का प्रयोग specific या particular noun से पहले किया जाता है। दिया गया sentence, specific प्रकार की एकजुटता(solidarity) को संदर्भित कर रहा है। अतः, &lsquo;strengthen the intellectual and moral solidarity&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Find the misspelt world.</p>",
                    question_hi: "<p>77. Find the misspelt world.</p>",
                    options_en: [
                        "<p>Preposterous</p>",
                        "<p>Disasterous</p>",
                        "<p>Murderous</p>",
                        "<p>Onerous</p>"
                    ],
                    options_hi: [
                        "<p>Preposterous</p>",
                        "<p>Disasterous</p>",
                        "<p>Murderous</p>",
                        "<p>Onerous</p>"
                    ],
                    solution_en: "<p>77.(b) Disasterous<br>&ldquo;Disastrous&rdquo; is the correct spelling.</p>",
                    solution_hi: "<p>77.(b) Disasterous<br>&ldquo;Disastrous&rdquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>She insisted<strong> <span style=\"text-decoration: underline;\">to go</span></strong> there, though her husband cautioned her on it.</p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;. <br>She insisted <strong><span style=\"text-decoration: underline;\">to go</span></strong> there, though her husband cautioned her on it.</p>",
                    options_en: [
                        "<p>on going.</p>",
                        "<p>upon going</p>",
                        "<p>going</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>on going.</p>",
                        "<p>upon going</p>",
                        "<p>going</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>78.(a) upon going<br>We generally use the preposition &lsquo;on&rsquo; with the verb &lsquo;insist&rsquo;. However, &lsquo;Insist on&rsquo; means to be firm about something. Similarly, in the given sentence, she was firm about going there. Hence, &lsquo;on going&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a) upon going<br>हम आम तौर पर verb &lsquo;insist&rsquo; के साथ preposition &lsquo;on&rsquo; का उपयोग करते हैं। हालाँकि, &lsquo;Insist on&rsquo; का अर्थ है किसी बात को लेकर दृढ़ रहना। इसी प्रकार दिए गए वाक्य में , &lsquo;वह वहाँ जाने के लिए दृढ़ थी&rsquo;। इसलिए, &lsquo;on going&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a       ",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>Ria is liked by everyone as she is very <span style=\"text-decoration: underline;\">amicable</span>.</p>",
                    question_hi: "<p>79. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>Ria is liked by everyone as she is very <span style=\"text-decoration: underline;\">amicable.</span></p>",
                    options_en: [
                        "<p>Dangerous</p>",
                        "<p>Stupid</p>",
                        "<p>Unfriendly</p>",
                        "<p>Hateful</p>"
                    ],
                    options_hi: [
                        "<p>Dangerous</p>",
                        "<p>Stupid</p>",
                        "<p>Unfriendly</p>",
                        "<p>Hateful</p>"
                    ],
                    solution_en: "<p>79.(c) <strong>Unfriendly-</strong> showing dislike for each other.<br><strong>Amicable-</strong> friendly.<br><strong>Dangerous-</strong> likely to cause harm or injury.<br><strong>Stupid-</strong> not intelligent<br><strong>Hateful-</strong> very unpleasant.</p>",
                    solution_hi: "<p>79.(c) <strong>Unfriendly</strong> (अमित्र) - showing dislike for each other.<br><strong>Amicable</strong> (मैत्रीपूर्ण) - friendly.<br><strong>Dangerous</strong> (खतरनाक) - likely to cause harm or injury.<br><strong>Stupid</strong> (मूर्ख) - not intelligent<br><strong>Hateful</strong> (घृणित/अप्रिय) - very unpleasant.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Must we clear the whole garbage ?</p>",
                    question_hi: "<p>80. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Must we clear the whole garbage ?</p>",
                    options_en: [
                        "<p>Must the whole garbage be cleared?</p>",
                        "<p>Must the whole garbage be clear?</p>",
                        "<p>Must the whole garbage be clear by us?</p>",
                        "<p>Must the whole garbage is clear?</p>"
                    ],
                    options_hi: [
                        "<p>Must the whole garbage be cleared?</p>",
                        "<p>Must the whole garbage be clear?</p>",
                        "<p>Must the whole garbage be clear by us?</p>",
                        "<p>Must the whole garbage is clear?</p>"
                    ],
                    solution_en: "<p>80.(a) <strong>Must the whole garbage be cleared?</strong><br>(b) Must the whole garbage be <strong>clear</strong>? (The third form of the verb is not used)<br>(c) Must the whole garbage be <strong>clear </strong>by us? (The third form of the verb is not used)<br>(d) Must the whole garbage is <strong>clear</strong>? (The third form of the verb is not used)</p>",
                    solution_hi: "<p>80.(a)<strong> Must the whole garbage be cleared?</strong><br>(b) Must the whole garbage be <strong>clear</strong>? ( Verb के third form का प्रयोग नहीं हुआ है)<br>(c) Must the whole garbage be <strong>clear</strong> by us? (Verb के third form का प्रयोग नहीं हुआ है )<br>(d) Must the whole garbage is <strong>clear</strong>? (Verb के third form का प्रयोग नहीं हुआ है)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. In the following question, out of the four alternatives choose the one which can be substituted for the given words/ sentence.<br>The profession of writing dictionaries</p>",
                    question_hi: "<p>81. In the following question, out of the four alternatives choose the one which can be substituted for the given words/ sentence.<br>The profession of writing dictionaries</p>",
                    options_en: [
                        "<p>Typography</p>",
                        "<p>Biography</p>",
                        "<p>Cartography</p>",
                        "<p>Lexicography</p>"
                    ],
                    options_hi: [
                        "<p>Typography</p>",
                        "<p>Biography</p>",
                        "<p>Cartography</p>",
                        "<p>Lexicography</p>"
                    ],
                    solution_en: "<p>81.(d) <strong>Lexicography</strong><br><strong>Typography</strong> - the style and appearance of printed matter.<br><strong>Biography</strong> - an account of someone\'s life written by someone else.<br><strong>Cartography</strong> - the science or practice of drawing maps<br><strong>Lexicography</strong> - the activity or occupation of compiling dictionaries.</p>",
                    solution_hi: "<p>81.(d) <strong>Lexicography</strong><br><strong>Typography</strong> - मुद्रित पदार्थ की आकार और शैली। <br><strong>Biography</strong> - किसी और के द्वारा लिखे गए किसी के जीवन का लेखा-जोखा। <br><strong>Cartography</strong> - मानचित्र बनाने का विज्ञान या अभ्यास। <br><strong>Lexicography</strong> - शब्दकोशों को संकलित करने की गतिविधि या व्यवसाय। </p>",
                    correct: " d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct sequence to form a meaningful sentence. <br>(P) an open mind <br>(Q) seek it with <br>(O) for personal growth and development <br>(R) knowledge is a powerful tool</p>",
                    question_hi: "<p>82. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct sequence to form a meaningful sentence. <br>(P) an open mind <br>(Q) seek it with <br>(O) for personal growth and development <br>(R) knowledge is a powerful tool</p>",
                    options_en: [
                        "<p>PQRO</p>",
                        "<p>ORPQ</p>",
                        "<p>QROP</p>",
                        "<p>ROQP</p>"
                    ],
                    options_hi: [
                        "<p>PQRO</p>",
                        "<p>ORPQ</p>",
                        "<p>QROP</p>",
                        "<p>ROQP</p>"
                    ],
                    solution_en: "<p>82.(d) ROQP<br>The given sentence starts with Part R as it introduces the main idea of the sentence, i.e. &lsquo;Knowledge is a powerful tool&rsquo;. Part R will be followed by Part O as it states that knowledge is a powerful tool for personal growth and development. Further, Part Q gives advice on seeking knowledge and Part P tells us to have an open mind. So, P will follow Q . Going through the options, option &lsquo;d&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>82.(d) ROQP<br>दिया गया sentence, Part R से प्रारंभ होता है क्योंकि यह sentence के मुख्य विचार &lsquo;Knowledge is a powerful tool&rsquo; को प्रस्तुत करता है। Part R के बाद Part O आएगा क्योंकि यह बताता है कि knowledge, personal growth और development के लिए एक शक्तिशाली साधन है। इसके अलावा, Part Q ज्ञान प्राप्त करने की सलाह देता है और Part P हमें open mind रखने के लिए कहता है। इसलिए, Q के बाद P आएगा। अतः options के माध्यम से जाने पर, option &lsquo;d&rsquo; में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the correctly spelt word :</p>",
                    question_hi: "<p>83. Select the correctly spelt word :</p>",
                    options_en: [
                        "<p>miscellanous</p>",
                        "<p>misscelanious</p>",
                        "<p>misscelleneous</p>",
                        "<p>miscellaneous</p>"
                    ],
                    options_hi: [
                        "<p>miscellanous</p>",
                        "<p>misscelanious</p>",
                        "<p>misscelleneous</p>",
                        "<p>miscellaneous</p>"
                    ],
                    solution_en: "<p>83.(d) miscellaneous.</p>",
                    solution_hi: "<p>83.(d) miscellaneous.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Anyone interested in computer programming can find a job in contemporary industry <span style=\"text-decoration: underline;\"><strong>if you </strong><strong>learn</strong></span> the basic programming languages such as COBOL and FORTRAN.</p>",
                    question_hi: "<p>84. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Anyone interested in computer programming can find a job in contemporary industry <span style=\"text-decoration: underline;\"><strong>if you learn</strong></span> the basic programming languages such as COBOL and FORTRAN.</p>",
                    options_en: [
                        "<p>By studying</p>",
                        "<p>By the study of</p>",
                        "<p>If he would learn</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>By studying</p>",
                        "<p>By the study of</p>",
                        "<p>If he would learn</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>84.(a) By studying <br>By is a preposition and prepositions are followed by verb+ing.</p>",
                    solution_hi: "<p>84.(a) By studying <br>By एक preposition है और prepositions के बाद verb+ing होगा ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>To be successful in today&rsquo;s world, we require the <strong><span style=\"text-decoration: underline;\">gift of the gab.</span></strong></p>",
                    question_hi: "<p>85. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>To be successful in today&rsquo;s world, we require the <strong><span style=\"text-decoration: underline;\">gift of the gab.</span></strong></p>",
                    options_en: [
                        "<p>ability to speak well</p>",
                        "<p>good interpersonal skills</p>",
                        "<p>divine help and guidance</p>",
                        "<p>a fierce competitive spirit</p>"
                    ],
                    options_hi: [
                        "<p>ability to speak well</p>",
                        "<p>good interpersonal skills</p>",
                        "<p>divine help and guidance</p>",
                        "<p>a fierce competitive spirit</p>"
                    ],
                    solution_en: "<p>85.(a) ability to speak well.</p>",
                    solution_hi: "<p>85.(a) ability to speak well./ अच्छा बोलने की क्षमता</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate option to fill in the blank.<br>In both countries we have leaders of _____ability.</p>",
                    question_hi: "<p>86. Select the most appropriate option to fill in the blank.<br>In both countries we have leaders of ______ability.</p>",
                    options_en: [
                        "<p>exceptional</p>",
                        "<p>almighty</p>",
                        "<p>powerful</p>",
                        "<p>compelling</p>"
                    ],
                    options_hi: [
                        "<p>exceptional</p>",
                        "<p>almighty</p>",
                        "<p>powerful</p>",
                        "<p>compelling</p>"
                    ],
                    solution_en: "<p>86.(a) <strong>exceptional</strong><br>&lsquo;Exceptional&rsquo; means much greater or better than usual. The given sentence states that we have leaders of exceptional ability in both countries. Hence &lsquo;exceptional&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>86.(a) <strong>exceptional</strong><br>&lsquo;Exceptional&rsquo; का अर्थ है सामान्य से कहीं अधिक या बेहतर। दिए गए sentence में कहा गया है कि हमारे पास दोनों देशों में असाधारण क्षमता वाले नेता हैं। अतः &lsquo;exceptional&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>To make one&rsquo;s blood boil</p>",
                    question_hi: "<p>87. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>To make one&rsquo;s blood boil</p>",
                    options_en: [
                        "<p>to make somebody furious</p>",
                        "<p>to develop fever</p>",
                        "<p>to get excited</p>",
                        "<p>to make someone nervous</p>"
                    ],
                    options_hi: [
                        "<p>to make somebody furious</p>",
                        "<p>to develop fever</p>",
                        "<p>to get excited</p>",
                        "<p>to make someone nervous</p>"
                    ],
                    solution_en: "<p>87.(a) To make one&rsquo;s blood boil - To make somebody furious.<br>Eg - When I hear stories of cruelty to animals, it makes my blood boil.</p>",
                    solution_hi: "<p>87.(a) To make one&rsquo;s blood boil (खून खौलना) - To make somebody furious.(किसी को भड़काना/ किसी को भड़काना/ गुस्सा दिलाना।)<br>Eg - जब मैं जानवरों के प्रति क्रूरता की कहानियां सुनता हूं तो मेरा खून खौल उठता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Choose the most appropriate option to change the voice (active/passive)form of the given sentence.<br>The Management has given a detailed report about the expenses incurred.</p>",
                    question_hi: "<p>88. Choose the most appropriate option to change the voice (active/passive)form of the given sentence.<br>The Management has given a detailed report about the expenses incurred.</p>",
                    options_en: [
                        "<p>A detailed report has been given by the management.</p>",
                        "<p>A detailed report about the expenses incurred has been given by the management.</p>",
                        "<p>A detail report about the expenses incurred has been given by the management.</p>",
                        "<p>A detailed report on the expenses incurred has been given by the management.</p>"
                    ],
                    options_hi: [
                        "<p>A detailed report has been given by the management.</p>",
                        "<p>A detailed report about the expenses incurred has been given by the management.</p>",
                        "<p>A detail report about the expenses incurred has been given by the management.</p>",
                        "<p>A detailed report on the expenses incurred has been given by the management.</p>"
                    ],
                    solution_en: "<p>88.(b) A detailed report about the expenses incurred has been given by the management. <br>(a) A detailed report has been given by the management. (Incomplete information)<br>(c) A <strong>detail </strong>report about the expenses incurred has been given by the management. (Incorrect word)<br>(d) A detailed report <strong>on </strong>the expenses incurred has been given by the management. (Incorrect preposition)</p>",
                    solution_hi: "<p>88.(b) A detailed report about the expenses incurred has been given by the management. <br>(a) A detailed report has been given by the management. (अधूरी information)<br>(c) A <strong>detail </strong>report about the expenses incurred has been given by the management. (गलत word)<br>(d) A detailed report <strong>on </strong>the expenses incurred has been given by the management. (गलत preposition)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. The following sentence has been split into four segments. Identify the segment that contains an error. <br>Dr. Sinha has / brought about a new / book on social / impact of child labour.</p>",
                    question_hi: "<p>89. The following sentence has been split into four segments. Identify the segment that contains an error. <br>Dr. Sinha has / brought about a new / book on social / impact of child labour.</p>",
                    options_en: [
                        "<p>impact of child labour</p>",
                        "<p>book on social</p>",
                        "<p>brought about a new</p>",
                        "<p>Dr. Sinha has</p>"
                    ],
                    options_hi: [
                        "<p>impact of child labour</p>",
                        "<p>book on social</p>",
                        "<p>brought about a new</p>",
                        "<p>Dr. Sinha has</p>"
                    ],
                    solution_en: "<p>89.(c) brought about a new<br>&lsquo;Brought out&rsquo; is the correct phrasal verb here, which means &lsquo;published&rsquo;. The given sentence is talking about publishing a new book. Hence, &lsquo;brought out a new&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(c) brought about a new<br>&lsquo;Brought out&rsquo; यहाँ सही phrasal verb है, जिसका अर्थ है &lsquo;प्रकाशित करना&rsquo;। दिया गया sentence एक नई पुस्तक के प्रकाशन के बारे में बात कर रहा है। अतः, &lsquo;brought out a new&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>Underground place for storing wine or other provisions.</p>",
                    question_hi: "<p>90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>Underground place for storing wine or other provisions.</p>",
                    options_en: [
                        "<p>Garage</p>",
                        "<p>Cellar</p>",
                        "<p>attic</p>",
                        "<p>Hall</p>"
                    ],
                    options_hi: [
                        "<p>Garage</p>",
                        "<p>Cellar</p>",
                        "<p>attic</p>",
                        "<p>Hall</p>"
                    ],
                    solution_en: "<p>90.(b) <strong>Cellar</strong><br><strong>Garage</strong> - a building for housing a motor vehicle or vehicles and repairing them.<br><strong>Cellar</strong> - a room below ground level in a house, often used for storing wine or coal<br><strong>Attic</strong> - a space or room inside or partly inside the roof of a building.<br><strong>Hall</strong> - the room or space just inside the front entrance of a house or flat.</p>",
                    solution_hi: "<p>90.(b) <strong>Cellar</strong><br><strong>Garage</strong> -एक मोटर वाहन या वाहनों के आवास के लिए और उनकी मरम्मत के लिए एक इमारत।<br><strong>Cellar</strong> -एक घर में जमीनी स्तर से नीचे का कमरा, जिसका इस्तेमाल अक्सर शराब या कोयले के भंडारण के लिए किया जाता है<br><strong>Attic</strong> - किसी भवन की छत के अंदर या आंशिक रूप से एक स्थान या कमरा।<br><strong>Hall</strong> - घर या फ्लैट के सामने के प्रवेश द्वार के ठीक अंदर का कमरा या स्थान।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym of the given word<br>Resign</p>",
                    question_hi: "<p>91. Select the most appropriate synonym of the given word<br>Resign</p>",
                    options_en: [
                        "<p>Achieve</p>",
                        "<p>Insist</p>",
                        "<p>Stay</p>",
                        "<p>Surrender</p>"
                    ],
                    options_hi: [
                        "<p>Achieve</p>",
                        "<p>Insist</p>",
                        "<p>Stay</p>",
                        "<p>Surrender</p>"
                    ],
                    solution_en: "<p>91.(d) <strong>Surrender-</strong> to give up or yield to someone or something.<br><strong>Resign-</strong> to voluntarily leave a position or job.<br><strong>Achieve-</strong> to successfully accomplish something.<br><strong>Insist-</strong> to demand something firmly or persistently.<br><strong>Stay-</strong> to remain in a place or position.</p>",
                    solution_hi: "<p>91.(d) <strong>Surrender</strong> (आत्मसमर्पण करना) - to give up or yield to someone or something.<br><strong>Resign</strong> (इस्तीफ़ा देना) - to voluntarily leave a position or job.<br><strong>Achieve</strong> (प्राप्त करना) - to successfully accomplish something.<br><strong>Insist</strong> (आग्रह करना) - to demand something firmly or persistently.<br><strong>Stay</strong> (ठहरना) - to remain in a place or position.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate synonym of the bracketed word in the following sentence&nbsp;to fill in the blank.<br>The documentary explored the historical and cultural significance of the ______(antique) ruins in the region.</p>",
                    question_hi: "<p>92. Select the most appropriate synonym of the bracketed word in the following sentence&nbsp;to fill in the blank.<br>The documentary explored the historical and cultural significance of the ______(antique) ruins in the region.</p>",
                    options_en: [
                        "<p>obsolete</p>",
                        "<p>contemporary</p>",
                        "<p>ancient</p>",
                        "<p>modern</p>"
                    ],
                    options_hi: [
                        "<p>obsolete</p>",
                        "<p>contemporary</p>",
                        "<p>ancient</p>",
                        "<p>modern</p>"
                    ],
                    solution_en: "<p>92.(c) <strong>Ancient-</strong> belonging to time long ago in history.<br><strong>Antique-</strong> an item that is very old, typically over 100 years old.<br><strong>Obsolete-</strong> no longer in use.<br><strong>Contemporary-</strong> living at the same time.<br><strong>Modern-</strong> something that is current and up-to-date.</p>",
                    solution_hi: "<p>92.(c) <strong>Ancient</strong> (प्राचीन) - belonging to time long ago in history.<br><strong>Antique</strong> (प्राचीन) - an item that is very old, typically over 100 years old.<br><strong>Obsolete</strong> (अप्रचलित) - no longer in use.<br><strong>Contemporary</strong> (समकालीन) - living at the same time.<br><strong>Modern</strong> (आधुनिक) - something that is current and up-to-date.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. <br>A. A big fancy dress ball was arranged to celebrate their homecoming. <br>B. It must have been a terrible shock for a young girl. <br>C. They had been married a month, just home from their honeymoon. <br>D. Just as the guests were starting to arrive, Charnley locked himself into his bedroom and shot himself.</p>",
                    question_hi: "<p>93. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. <br>A. A big fancy dress ball was arranged to celebrate their homecoming. <br>B. It must have been a terrible shock for a young girl. <br>C. They had been married a month, just home from their honeymoon. <br>D. Just as the guests were starting to arrive, Charnley locked himself into his bedroom and shot himself.</p>",
                    options_en: [
                        "<p>BCDA</p>",
                        "<p>ABCD</p>",
                        "<p>BCAD</p>",
                        "<p>DACB</p>"
                    ],
                    options_hi: [
                        "<p>BCDA</p>",
                        "<p>ABCD</p>",
                        "<p>BCAD</p>",
                        "<p>DACB</p>"
                    ],
                    solution_en: "<p>93.(c) BCAD<br>Sentence B will be the starting line as it introduces the main idea of the parajumble i.e. &lsquo;a terrible shock for a young girl&rsquo;. And, Sentence C states that they had been married a month, just home from their honeymoon. So, C will follow B. Further, Sentence A states that a big fancy dress ball was arranged to celebrate their homecoming &amp; Sentence D states that Charnley shot himself just as the guests were starting to arrive. So, D will follow A. Going through the options, option &lsquo;c&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>93.(c) BCAD<br>Sentence B प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार &lsquo;a terrible shock for a young girl&rsquo; को प्रस्तुत करता है। और, Sentence C बताता है कि उनकी शादी को एक महीना हो गया था, वे अपने honeymoon से घर लौटे थे। इसलिए, B के बाद C आएगा। इसके अलावा, Sentence A बताता है कि उनके घर वापसी का जश्न मनाने के लिए एक बड़े fancy dress ball का आयोजन किया गया था और Sentence D बताता है कि Charnley ने मेहमानों के आने से ठीक पहले खुद को गोली मार ली। इसलिए, A के बाद D आएगा। अतः options के माध्यम से जाने पर, option &lsquo;c&rsquo; में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The committee <span style=\"text-decoration: underline;\">deposed</span> him from his office.</p>",
                    question_hi: "<p>94. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>The committee <span style=\"text-decoration: underline;\">deposed</span> him from his office.</p>",
                    options_en: [
                        "<p>Demolished</p>",
                        "<p>Segregated</p>",
                        "<p>Interacted</p>",
                        "<p>Promoted</p>"
                    ],
                    options_hi: [
                        "<p>Demolished</p>",
                        "<p>Segregated</p>",
                        "<p>Interacted</p>",
                        "<p>Promoted</p>"
                    ],
                    solution_en: "<p>94.(d) <strong>Promoted-</strong> raise (someone) to a higher position or rank.<br><strong>Deposed-</strong> to remove someone from a position of authority.<br><strong>Demolished-</strong> to destroy something such as a building. <br><strong>Segregated-</strong> to separate one group of people from another, especially one sex or race from another.<br><strong>Interacted-</strong> to talk and do things with other people.</p>",
                    solution_hi: "<p>94.(d) <strong>Promoted</strong> (पदोन्नत करना) - raise (someone) to a higher position or rank.<br><strong>Deposed</strong> (पदच्युत करना) - to remove someone from a position of authority.<br><strong>Demolished</strong> (ध्वस्त करना) - to destroy something such as a building. <br><strong>Segregated</strong> (पृथक करना) - to separate one group of people from another, especially one sex or race from another.<br><strong>Interacted</strong> (बातचीत करना) - to talk and do things with other people.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>Yesterday, I ate only a ______of apples for my dinner.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>Yesterday, I ate only a ______of apples for my dinner.</p>",
                    options_en: [
                        "<p>pier</p>",
                        "<p>pair</p>",
                        "<p>pear</p>",
                        "<p>pare</p>"
                    ],
                    options_hi: [
                        "<p>pier</p>",
                        "<p>pair</p>",
                        "<p>pear</p>",
                        "<p>pare</p>"
                    ],
                    solution_en: "<p>95.(b) <strong>pair</strong><br>&lsquo;Pair&rsquo; means a set of two things. The given sentence states that yesterday, I ate only a pair of apples for my dinner. Hence &lsquo;pair&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(b) <strong>pair</strong><br>&lsquo;Pair&rsquo; का अर्थ है दो चीजों का समूह या जोड़ी। दिए गए sentence में कहा गया है कि कल मैंने अपने खाने में केवल दो सेब खाए। अतः &lsquo;pair&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:</strong><br>As Paul was (96)____ his supper, Aunt Jenny (97)_____him questions that have been full of deceit , and had a very deep meaning, for she wanted to trap him. It seemed as if she was endowed with a (98)_____which could hunt for dark and mysterious tact.<br>She asked, \"Did you now not want to swim, Paul?\"<br>Paul felt an uncomfortable (99)_____. He (100)____ Aunt Jenny\'s face; however it informed him nothing. So, he said, \"No, maʼam.\"<br>Select the most appropriate option to fill in blank no.96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong><br>As Paul was (96)____ his supper, Aunt Jenny (97)_____him questions that have been full of deceit , and had a very deep meaning, for she wanted to trap him. It seemed as if she was endowed with a (98)_____which could hunt for dark and mysterious tact.<br>She asked, \"Did you now not want to swim, Paul?\"<br>Paul felt an uncomfortable (99)_____. He (100)____ Aunt Jenny\'s face; however it informed him nothing. So, he said, \"No, maʼam.\"<br>Select the most appropriate option to fill in blank no. 96.</p>",
                    options_en: [
                        "<p>grazing</p>",
                        "<p>eating</p>",
                        "<p>feeding</p>",
                        "<p>churning</p>"
                    ],
                    options_hi: [
                        "<p>grazing</p>",
                        "<p>eating</p>",
                        "<p>feeding</p>",
                        "<p>churning</p>"
                    ],
                    solution_en: "<p>96.(b) <strong>eating</strong><br>The given passage states that Paul was eating his supper. Hence &lsquo;eating&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(b) <strong>eating</strong><br>दिए गए passage में बताया गया है कि Paul अपना खाना खा रहा था। अतः &lsquo;eating&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong><br>As Paul was (96)____ his supper, Aunt Jenny (97)_____him questions that have been full of deceit , and had a very deep meaning, for she wanted to trap him. It seemed as if she was endowed with a (98)_____which could hunt for dark and mysterious tact.<br>She asked, \"Did you now not want to swim, Paul?\"<br>Paul felt an uncomfortable (99)_____. He (100)____ Aunt Jenny\'s face; however it informed him nothing. So, he said, \"No, maʼam.\"<br>Select the most appropriate option to fill in blank no. 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong><br>As Paul was (96)____ his supper, Aunt Jenny (97)_____him questions that have been full of deceit , and had a very deep meaning, for she wanted to trap him. It seemed as if she was endowed with a (98)_____which could hunt for dark and mysterious tact.<br>She asked, \"Did you now not want to swim, Paul?\"<br>Paul felt an uncomfortable (99)_____. He (100)____ Aunt Jenny\'s face; however it informed him nothing. So, he said, \"No, maʼam.\"<br>Select the most appropriate option to fill in blank no.97.</p>",
                    options_en: [
                        "<p>demanded</p>",
                        "<p>asked</p>",
                        "<p>allowed</p>",
                        "<p>submitted</p>"
                    ],
                    options_hi: [
                        "<p>demanded</p>",
                        "<p>asked</p>",
                        "<p>allowed</p>",
                        "<p>submitted</p>"
                    ],
                    solution_en: "<p>97.(b) <strong>asked</strong><br>The given passage states that aunt Jenny asked him questions that have been full of deceit. Hence &lsquo;asked&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) <strong>asked</strong><br>दिए गए passage में बताया गया है कि aunt Jenny ने उनसे ऐसे सवाल पूछे जो धोखे (deceit) से भरे हुए थे। अतः &lsquo;asked&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong><br>As Paul was (96)____ his supper, Aunt Jenny (97)_____him questions that have been full of deceit , and had a very deep meaning, for she wanted to trap him. It seemed as if she was endowed with a (98)_____which could hunt for dark and mysterious tact.<br>She asked, \"Did you now not want to swim, Paul?\"<br>Paul felt an uncomfortable (99)_____. He (100)____ Aunt Jenny\'s face; however it informed him nothing. So, he said, \"No, maʼam.\"<br>Select the most appropriate option to fill in blank no. 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong><br>As Paul was (96)____ his supper, Aunt Jenny (97)_____him questions that have been full of deceit , and had a very deep meaning, for she wanted to trap him. It seemed as if she was endowed with a (98)_____which could hunt for dark and mysterious tact.<br>She asked, \"Did you now not want to swim, Paul?\"<br>Paul felt an uncomfortable (99)_____. He (100)____ Aunt Jenny\'s face; however it informed him nothing. So, he said, \"No, maʼam.\"<br>Select the most appropriate option to fill in blank no.98.</p>",
                    options_en: [
                        "<p>weakness</p>",
                        "<p>talent</p>",
                        "<p>technique</p>",
                        "<p>purpose</p>"
                    ],
                    options_hi: [
                        "<p>weakness</p>",
                        "<p>talent</p>",
                        "<p>technique</p>",
                        "<p>purpose</p>"
                    ],
                    solution_en: "<p>98.(b) <strong>talent</strong><br>&lsquo;Talent&rsquo; means a natural ability or skill in doing something. The given passage states that It seemed as if she was endowed with a talent which could hunt for dark and mysterious tact. Hence &lsquo;talent&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(b) <strong>talent</strong><br>&lsquo;Talent&rsquo; का अर्थ है किसी काम को करने की स्वाभाविक क्षमता (natural ability) या कौशल (skill)। दिए गए passage में बताया गया है कि ऐसा लग रहा था जैसे उसे ऐसी प्रतिभा (talent) मिली हुई थी जो अंधेरे और रहस्यमयी चालों (mysterious tact) का शिकार कर सकती थी। अतः &lsquo;talent&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:</strong><br>As Paul was (96)____ his supper, Aunt Jenny (97)_____him questions that have been full of deceit , and had a very deep meaning, for she wanted to trap him. It seemed as if she was endowed with a (98)_____which could hunt for dark and mysterious tact.<br>She asked, \"Did you now not want to swim, Paul?\"<br>Paul felt an uncomfortable (99)_____. He (100)____ Aunt Jenny\'s face; however it informed him nothing. So, he said, \"No, maʼam.\"<br>Select the most appropriate option to fill in blank no. 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong><br>As Paul was (96)____ his supper, Aunt Jenny (97)_____him questions that have been full of deceit , and had a very deep meaning, for she wanted to trap him. It seemed as if she was endowed with a (98)_____which could hunt for dark and mysterious tact.<br>She asked, \"Did you now not want to swim, Paul?\"<br>Paul felt an uncomfortable (99)_____. He (100)____ Aunt Jenny\'s face; however it informed him nothing. So, he said, \"No, maʼam.\"<br>Select the most appropriate option to fill in blank no. 99.</p>",
                    options_en: [
                        "<p>view</p>",
                        "<p>drop</p>",
                        "<p>guess</p>",
                        "<p>suspicion</p>"
                    ],
                    options_hi: [
                        "<p>view</p>",
                        "<p>drop</p>",
                        "<p>guess</p>",
                        "<p>suspicion</p>"
                    ],
                    solution_en: "<p>99.(d) <strong>suspicion</strong><br>&lsquo;Suspicion&rsquo; means a feeling or belief that someone is not telling the truth. The given passage states that Paul felt an uncomfortable suspicion. Hence &lsquo;suspicion&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) <strong>suspicion</strong><br>&lsquo;Suspicion&rsquo; का अर्थ है ऐसी भावना (feeling) या विश्वास (belief) कि कोई व्यक्ति सच (truth) नहीं बोल रहा है। दिए गए passage में बताया गया है कि Paul को असहज संदेह महसूस हुआ। अतः &lsquo;suspicion&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:</strong><br>As Paul was (96)____ his supper, Aunt Jenny (97)_____him questions that have been full of deceit , and had a very deep meaning, for she wanted to trap him. It seemed as if she was endowed with a (98)_____which could hunt for dark and mysterious tact.<br>She asked, \"Did you now not want to swim, Paul?\"<br>Paul felt an uncomfortable (99)_____. He (100)____ Aunt Jenny\'s face; however it informed him nothing. So, he said, \"No, maʼam.\"<br>Select the most appropriate option to fill in blank no. 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong><br>As Paul was (96)____ his supper, Aunt Jenny (97)_____him questions that have been full of deceit , and had a very deep meaning, for she wanted to trap him. It seemed as if she was endowed with a (98)_____which could hunt for dark and mysterious tact.<br>She asked, \"Did you now not want to swim, Paul?\"<br>Paul felt an uncomfortable (99)_____. He (100)____ Aunt Jenny\'s face; however it informed him nothing. So, he said, \"No, maʼam.\"<br>Select the most appropriate option to fill in blank no.100.</p>",
                    options_en: [
                        "<p>pursued</p>",
                        "<p>ruffled</p>",
                        "<p>searched</p>",
                        "<p>chased</p>"
                    ],
                    options_hi: [
                        "<p>pursued</p>",
                        "<p>ruffled</p>",
                        "<p>searched</p>",
                        "<p>chased</p>"
                    ],
                    solution_en: "<p>100.(c) <strong>searched</strong><br>&lsquo;Search&rsquo; means to look somewhere carefully in order to find something. The given passage states that he searched Aunt Jennys face. Hence, &lsquo;searched&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) <strong>searched</strong><br>&lsquo;Search&rsquo; का अर्थ है किसी चीज़ को खोजने के लिए कहीं ध्यानपूर्वक (carefully) देखना। दिए गए passage में बताया गया है कि उसने आंटी जेनी (Aunt Jennys) का चेहरा खोजा। अतः, &lsquo;searched&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>