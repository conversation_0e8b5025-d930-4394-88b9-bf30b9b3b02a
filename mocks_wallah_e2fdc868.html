<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> A sells a television to B for &#8377;4,860, thereby losing 19%. B sells it to C at a price which would have given A a 17% profit. Find the gain of B.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1.</span><span style=\"font-family: Cambria Math;\"> A, B &#2325;&#2379; &#8377;4,860 &#2350;&#2375;&#2306; &#2319;&#2325; &#2335;&#2375;&#2354;&#2368;&#2357;&#2367;&#2332;&#2344; &#2348;&#2375;&#2330;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2367;&#2360;&#2360;&#2375; &#2313;&#2360;&#2375; 19% &#2325;&#2368; &#2361;&#2366;&#2344;&#2367; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; B &#2311;&#2360;&#2375; C &#2325;&#2379; &#2313;&#2360; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; &#2348;&#2375;&#2330;&#2340;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2360;&#2375; A &#2325;&#2379; 17% &#2325;&#2366; &#2354;&#2366;&#2349; &#2361;&#2379;&#2340;&#2366; &#2404; B &#2325;&#2366; &#2354;&#2366;&#2349; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>&#8377;4,160</p>\n", "<p>&#8377;2,160</p>\n", 
                                "<p>&#8377;3,160</p>\n", "<p>&#8377;1,160</p>\n"],
                    options_hi: ["<p>&#8377;4,160</p>\n", "<p>&#8377;2,160</p>\n",
                                "<p>&#8377;3,160</p>\n", "<p>&#8377;1,160</p>\n"],
                    solution_en: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">C.P. for A = 4860 &times;</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>81</mn></mfrac></math>= 6000 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Selling price for B = 6000 &times;117% = 7020 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Gain of B = 7020 - </span><span style=\"font-family: Cambria Math;\">4860 = 2160 &#8377;</span></p>\n",
                    solution_hi: "<p>1.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">A &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 4860 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>81</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 6000 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B &#2325;&#2375; &#2354;&#2367;&#2319; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;= 6000 &times;117% = 7020 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B &#2325;&#2366; &#2354;&#2366;&#2349; = 7020 - </span><span style=\"font-family: Cambria Math;\">4860 = 2160 &#8377;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> The cost price of an article is 72% of the marked price. The profit percentage after allowing a discount of 6% on the marked price is (rounded off to 2 decimal places):</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> &#2319;&#2325; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#2313;&#2360;&#2325;&#2375; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2366; 72% &#2361;&#2376;&#2404; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; 6% &#2325;&#2368; &#2331;&#2370;&#2335; &#2342;&#2375;&#2344;&#2375; &#2325;&#2375; &#2348;&#2366;&#2342; &#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; __________&#2361;&#2376; &#2404; (&#2342;&#2358;&#2350;&#2354;&#2357; &#2325;&#2375; &#2342;&#2379; &#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306; &#2340;&#2325; &#2360;&#2344;&#2381;&#2344;&#2367;&#2325;&#2335;&#2367;&#2340;)</span></p>\n",
                    options_en: ["<p>32.50%</p>\n", "<p>28.23%</p>\n", 
                                "<p>30.56%</p>\n", "<p>35.65%</p>\n"],
                    options_hi: ["<p>32.50%</p>\n", "<p>28.23%</p>\n",
                                "<p>30.56%</p>\n", "<p>35.65%</p>\n"],
                    solution_en: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let marked price = 100 &#8377; , </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then cost price = 72 &#8377; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">and selling price = 100&times;94% = 94 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit % =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>94</mn><mo>-</mo><mn>72</mn></mrow><mn>72</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 30.56%</span></p>\n",
                    solution_hi: "<p>2.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = 100 &#8377;,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2379; &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = 72 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 100&times;94%= <span style=\"font-weight: 400;\">94 &#8377;</span></span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><span style=\"font-weight: 400;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2354;&#2366;&#2349; % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>94</mn><mo>-</mo><mn>72</mn></mrow><mn>72</mn></mfrac></math>&times;100 </span><span style=\"font-weight: 400;\">= 30.56%</span></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> A shopkeeper does a faulty weighing of an item of 930 gram instead of 1 kg and sells it at its cost price. Find his profit percentage on selling 15 kg of the item. </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> &#2319;&#2325; &#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352; &#2319;&#2325; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; 1 kg &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; 930 &#2327;&#2381;&#2352;&#2366;&#2350; &#2325;&#2366; &#2327;&#2354;&#2340; &#2340;&#2380;&#2354; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2313;&#2360;&#2375; &#2313;&#2360;&#2325;&#2375; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; &#2348;&#2375;&#2330; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; 15 kg &#2357;&#2360;&#2381;&#2340;&#2369; &#2348;&#2375;&#2330;&#2344;&#2375; &#2346;&#2352; &#2313;&#2360;&#2325;&#2366; &#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>79</mn><mn>93</mn></mfrac><mo>%</mo></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>39</mn></mfrac><mo>%</mo></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p>7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>93</mn></mfrac><mo>%</mo></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>93</mn></mfrac><mo>%</mo></math></p>\n"],
                    options_hi: ["<p>8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>79</mn><mn>93</mn></mfrac><mo>%</mo></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>93</mn></mfrac><mo>%</mo></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>93</mn></mfrac><mo>%</mo></math><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>6<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>19</mn><mn>93</mn></mfrac><mo>%</mo></math></p>\n"],
                    solution_en: "<p>3.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mo>-</mo><mn>930</mn></mrow><mn>930</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>700</mn><mn>93</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>93</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> %</span></p>\n",
                    solution_hi: "<p>3.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2354;&#2366;&#2349; % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1000</mn><mo>-</mo><mn>930</mn></mrow><mn>930</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>700</mn></mrow><mn>93</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>49</mn><mn>93</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Upon selling an article at <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> of the marked price, a loss of 30% is incurred. What is the ratio between the marked price and the cost price of the article? </span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2379; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2375;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&#2346;&#2352; &#2348;&#2375;&#2330;&#2344;&#2375; &#2346;&#2352; 30% &#2325;&#2368; &#2361;&#2366;&#2344;&#2367; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2375; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2324;&#2352; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>48 : 60</p>\n", "<p>49 : 30</p>\n", 
                                "<p>25 : 37</p>\n", "<p>50 : 80</p>\n"],
                    options_hi: ["<p>48 : 60</p>\n", "<p>49 : 30</p>\n",
                                "<p>25 : 37</p>\n", "<p>50 : 80</p>\n"],
                    solution_en: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let marked price = 49 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then Selling price = 21 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cost price = 21 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>70</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 30 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required ratio :- M.P. : C.P. = 49 : 30 </span></p>\n",
                    solution_hi: "<p>4.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366; , &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = 49 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2348; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 21 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = 21 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>70</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 30 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; :- &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; : &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = 49 : 30 </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">A shopkeeper publishes a list of prices which are 60% above the CP. He proposes to </span><span style=\"font-family: Cambria Math;\">give 20%</span><span style=\"font-family: Cambria Math;\"> discount. How much profit does he really make?</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352; &#2350;&#2370;&#2354;&#2381;&#2351;&#2379;&#2306; &#2325;&#2368; &#2319;&#2325; &#2360;&#2370;&#2330;&#2368; &#2332;&#2366;&#2352;&#2368; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2379; CP( &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351;) &#2360;&#2375; 60% &#2309;&#2343;&#2367;&#2325; &#2361;&#2376;&#2404; &#2357;&#2361; 20% &#2325;&#2368; &#2331;&#2370;&#2335; &#2342;&#2375;&#2325;&#2352; &#2309;&#2346;&#2344;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369;&#2319;&#2306; &#2348;&#2375;&#2330; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; &#2357;&#2361; &#2357;&#2366;&#2360;&#2381;&#2340;&#2357; &#2350;&#2375;&#2306; &#2325;&#2367;&#2340;&#2344;&#2366; &#2354;&#2366;&#2349; &#2309;&#2352;&#2381;&#2332;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>35%</p>\n", "<p>22%</p>\n", 
                                "<p>18%</p>\n", "<p>28%</p>\n"],
                    options_hi: ["<p>35%</p>\n", "<p>22%</p>\n",
                                "<p>18%</p>\n", "<p>28%</p>\n"],
                    solution_en: "<p>5.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit % = 60 - </span><span style=\"font-family: Cambria Math;\">20 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&times;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 40 - </span><span style=\"font-family: Cambria Math;\">12 = 28%</span></p>\n",
                    solution_hi: "<p>5.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2354;&#2366;&#2349; % = 60 - </span><span style=\"font-family: Cambria Math;\">20 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&times;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 40 - </span><span style=\"font-family: Cambria Math;\">12 = 28%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Ranjeet buys 1 kg potato for &#8377;20 and sells it for &#8377;25. He also uses a weight of 800 gm in place of 1 kg. What is the Ranjeet&rsquo;s actual profit percentage on the sale of 1 kg potato?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">&#2352;&#2306;&#2332;&#2368;&#2340; &#8377;20 &#2350;&#2375;&#2306; 1 kg &#2310;&#2354;&#2370; &#2326;&#2352;&#2368;&#2342;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2313;&#2360;&#2375; &#8377;25 &#2350;&#2375;&#2306; &#2348;&#2375;&#2330; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; &#2357;&#2361; &#2310;&#2354;&#2370; &#2348;&#2375;&#2330;&#2340;&#2375; &#2360;&#2350;&#2351; 1 kg &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2346;&#2352; 800 gm &#2325;&#2375; &#2348;&#2366;&#2335; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; 1 kg &#2310;&#2354;&#2370; &#2325;&#2368; &#2348;&#2367;&#2325;&#2381;&#2352;&#2368; &#2346;&#2352; &#2352;&#2306;&#2332;&#2368;&#2340; &#2325;&#2366; &#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325; &#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2367;&#2340;&#2344;&#2366; &#2361;&#2379;&#2327;&#2366;?</span></p>\n",
                    options_en: ["<p>56.75%</p>\n", "<p>56.25%</p>\n", 
                                "<p>55.75%</p>\n", "<p>57.25%</p>\n"],
                    options_hi: ["<p>56.75%</p>\n", "<p>56.25%</p>\n",
                                "<p>55.75%</p>\n", "<p>57.25%</p>\n"],
                    solution_en: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio = Cost price : Selling price </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">At time of selling = 20 : 25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Faulty weight = 800 : 1000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">----------------------------------</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Final = 16 : 25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>16</mn></mrow><mn>16</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\">= 56.25 %</span></p>\n",
                    solution_hi: "<p>6.(b)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340; = &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; : &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2330;&#2344;&#2375; &#2325;&#2375; &#2360;&#2350;&#2351; = 20 : 25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2359;&#2346;&#2370;&#2352;&#2381;&#2339; &#2357;&#2332;&#2344; = 800 : 1000</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">------------------------------------</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2367;&#2350; = 16 : 25</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2354;&#2366;&#2349; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>16</mn></mrow><mn>16</mn></mfrac></math>&times;100 </span><span style=\"font-family: Cambria Math;\">= 56.25 %</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">A merchant increases the cost price of an item by 30% and offers a discount of 15% on this marked price. What is his profit percentage?</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368; &#2319;&#2325; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2375; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#2350;&#2375;&#2306; 30% &#2325;&#2368; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2313;&#2360; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2375; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; 15% &#2325;&#2368; &#2331;&#2370;&#2335; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368; &#2325;&#2366; &#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>10.5%</p>\n", "<p>8.2%</p>\n", 
                                "<p>9.5%</p>\n", "<p>11.4%</p>\n"],
                    options_hi: ["<p>10.5%</p>\n", "<p>8.2%</p>\n",
                                "<p>9.5%</p>\n", "<p>11.4%</p>\n"],
                    solution_en: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit % = 30 - </span><span style=\"font-family: Cambria Math;\">15 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&times;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 10.5%</span></p>\n",
                    solution_hi: "<p>7.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2354;&#2366;&#2349; % = 30 - </span><span style=\"font-family: Cambria Math;\">15 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&times;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 10.5%</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">A dealer offers a discount of 10% on the marked price of an article and still makes a profit of 20%. If the cost price of the article is &#8377;540, then find the marked price (in &#8377;) of the article.</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368; &#2319;&#2325; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2375; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; 10% &#2325;&#2368; &#2331;&#2370;&#2335; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2347;&#2367;&#2352; &#2349;&#2368; 20% &#2325;&#2366; &#2354;&#2366;&#2349; &#2325;&#2350;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#8377;540 &#2361;&#2376;, &#2340;&#2379; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; (&#8377; &#2350;&#2375;&#2306;) &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2404;</span></p>\n",
                    options_en: ["<p>810</p>\n", "<p>750</p>\n", 
                                "<p>720</p>\n", "<p>690</p>\n"],
                    options_hi: ["<p>810</p>\n", "<p>750</p>\n",
                                "<p>720</p>\n", "<p>690</p>\n"],
                    solution_en: "<p>8.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let marked price = x </span><span style=\"font-family: Cambria Math;\">Rs,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then , 540 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = M.P. &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">M.P. = 720 Rs.</span></p>\n",
                    solution_hi: "<p>8.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = x&nbsp;</span><span style=\"font-family: Cambria Math;\"> Rs,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2340;&#2348; , 540 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>100</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = 720 Rs.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">A Sold an item to B at a gain of 25% and B Sold it to C at a loss of 10%. If C paid &#8377;6,520 for it, then the cost price of the item for A was:</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> A &#2344;&#2375; B &#2325;&#2379; 25% &#2325;&#2375; &#2354;&#2366;&#2349; &#2346;&#2352; &#2319;&#2325; &#2357;&#2360;&#2381;&#2340;&#2369; &#2348;&#2375;&#2330;&#2368; &#2324;&#2352; B &#2344;&#2375; &#2313;&#2360;&#2368; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2379; C &#2325;&#2379; 10% &#2325;&#2368; &#2361;&#2366;&#2344;&#2367; &#2346;&#2352; &#2348;&#2375;&#2330; &#2342;&#2367;&#2351;&#2366;&#2404; &#2351;&#2342;&#2367; C &#2344;&#2375; &#2311;&#2360;&#2325;&#2375; &#2354;&#2367;&#2319; &#8377;6,520 &#2325;&#2366; &#2349;&#2369;&#2327;&#2340;&#2366;&#2344; &#2325;&#2367;&#2351;&#2366;, &#2340;&#2379; A &#2325;&#2375; &#2354;&#2367;&#2319; &#2357;&#2360;&#2381;&#2340;&#2369; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2381;&#2351;&#2366; &#2341;&#2366; ?</span></p>\n",
                    options_en: ["<p>&#8377;4,355<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>\n", "<p>&#8377;6,125<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></p>\n", 
                                "<p>&#8377;4,785<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>\n", "<p>&#8377;5,795<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math></p>\n"],
                    options_hi: ["<p>&#8377;4,355<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>\n", "<p>&#8377;6,125<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math></p>\n",
                                "<p>&#8377;4,785<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>9</mn></mfrac></math></p>\n", "<p>&#8377;5,795<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math></p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let cost price for A = x&nbsp;</span><span style=\"font-family: Cambria Math;\"> Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then , x </span><span style=\"font-family: Cambria Math;\">&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 6520 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6520</mn><mo>&times;</mo><mn>8</mn></mrow><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 5795<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">Rs. </span></p>\n",
                    solution_hi: "<p>9.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366; , A &#2325;&#2366; &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = x </span><span style=\"font-family: Cambria Math;\">Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2347;&#2367;&#2352; , x </span><span style=\"font-family: Cambria Math;\">&times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 6520 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6520</mn><mo>&times;</mo><mn>8</mn></mrow><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 5795<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> Rs. </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">A fruit seller buys 480 bananas for &#8377;1,200. Some of these bananas are rotten and hence, they are thrown away. He sells the remaining bananas at &#8377;3.5 each and makes a profit of &#8377;396. What is the percentage of bananas thrown away ?</span></p>\n",
                    question_hi: "<p>10. <span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2347;&#2354; &#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366; &#8377;1,200 &#2350;&#2375;&#2306; 480 &#2325;&#2375;&#2354;&#2375; &#2326;&#2352;&#2368;&#2342;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2344;&#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2369;&#2331; &#2325;&#2375;&#2354;&#2375; &#2360;&#2337;&#2364;&#2375; &#2361;&#2369;&#2319; &#2344;&#2367;&#2325;&#2354; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2311;&#2360;&#2354;&#2367;&#2319; &#2357;&#2361; &#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2347;&#2375;&#2306;&#2325; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; &#2357;&#2361; &#2358;&#2375;&#2359; &#2325;&#2375;&#2354;&#2379;&#2306; &#2325;&#2379; &#8377;3.5 &#2346;&#2381;&#2352;&#2340;&#2367; &#2325;&#2375;&#2354;&#2375; &#2325;&#2375; &#2349;&#2366;&#2357; &#2360;&#2375; &#2348;&#2375;&#2330;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#8377;396 &#2325;&#2366; &#2354;&#2366;&#2349; &#2325;&#2350;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2347;&#2375;&#2306;&#2325;&#2375; &#2327;&#2319; &#2325;&#2375;&#2354;&#2379;&#2306; &#2325;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376; ?</span></p>\n",
                    options_en: ["<p>6%</p>\n", "<p>4.5 %</p>\n", 
                                "<p>5 %</p>\n", "<p>4 %</p>\n"],
                    options_hi: ["<p>6%</p>\n", "<p>4.5 %</p>\n",
                                "<p>5 %</p>\n", "<p>4 %</p>\n"],
                    solution_en: "<p>10.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let x bananas be rotten</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total C.P of 480 bananas = Rs. 1200</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">After getting a profit of 396 , Total S.P = 1200 + 396 = 1596</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">According to question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>.</mo><mn>5</mn><mo>(</mo><mn>480</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo><mo>=</mo><mn>1596</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>1680</mn><mo>-</mo><mn>3</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>1596</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>1680</mn><mo>-</mo><mn>1596</mn><mo>=</mo><mn>3</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>3</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>84</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>24</mn><mspace linebreak=\"newline\"></mspace><mi>So</mi><mo>,</mo><mo>&nbsp;</mo><mi>required</mi><mo>&nbsp;</mo><mi>percentage</mi><mo>&nbsp;</mo><mi>of</mi><mo>&nbsp;</mo><mi>bananas</mi><mo>&nbsp;</mo><mi>thrown</mi><mo>&nbsp;</mo><mi>away</mi><mo>=</mo><mfrac><mn>24</mn><mn>480</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>5</mn><mo>%</mo></math></span></p>\n",
                    solution_hi: "<p>10.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366; &#2325;&#2367; x &#2325;&#2375;&#2354;&#2375; &#2360;&#2337;&#2364;&#2375; &#2361;&#2369;&#2319; &#2361;&#2376;&#2306;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">480 &#2325;&#2375;&#2354;&#2375; &#2325;&#2366; &#2325;&#2369;&#2354; C.P = &#2352;&#2369;. 1200</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">396 &#2325;&#2366; &#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2348;&#2366;&#2342;, &#2325;&#2369;&#2354; S.P = 1200 + 396 = 1596</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>.</mo><mn>5</mn><mo>(</mo><mn>480</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo><mo>=</mo><mn>1596</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>1680</mn><mo>-</mo><mn>3</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>1596</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>1680</mn><mo>-</mo><mn>1596</mn><mo>=</mo><mn>3</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>3</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>84</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mi mathvariant=\"normal\">x</mi><mo>=</mo><mn>24</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2340;&#2379;</mi><mo>,</mo><mo>&nbsp;</mo><mi>&#2347;&#2375;&#2306;&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2327;&#2319;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;&#2354;&#2379;&#2306;</mi><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</mi><mo>=</mo><mfrac><mn>24</mn><mn>480</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>=</mo><mn>5</mn><mo>%</mo></math></span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\"> Arun brought 50 kg of rice at a discount of 15% on the marked price. For purchasing bulk </span><span style=\"font-family: Cambria Math;\">quantity</span><span style=\"font-family: Cambria Math;\">, he was given 5 kg of wheat (the marked price of which was 60% the marked price of rice) free of cost. Had the retailer Sold both items at their marked price to his other customers, then approximately what would be the profit percentage that the shopkeeper could earn ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11.</span><span style=\"font-family: Cambria Math;\"> &#2309;&#2352;&#2369;&#2339; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; 15% &#2325;&#2368; &#2331;&#2370;&#2335; &#2346;&#2352; 50 kg &#2330;&#2366;&#2357;&#2354; &#2326;&#2352;&#2368;&#2342;&#2340;&#2366; &#2361;&#2376;&#2404; &#2341;&#2379;&#2325; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366; &#2350;&#2375;&#2306; &#2330;&#2366;&#2357;&#2354; &#2326;&#2352;&#2368;&#2342;&#2344;&#2375; &#2346;&#2352; &#2313;&#2360;&#2375; 5 kg &#2327;&#2375;&#2361;&#2370;&#2305; (&#2332;&#2367;&#2360;&#2325;&#2366; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2330;&#2366;&#2357;&#2354; &#2325;&#2375; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2366; 60% &#2361;&#2376;) &#2350;&#2369;&#2347;&#2381;&#2340; &#2350;&#2375;&#2306; &#2342;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2326;&#2369;&#2342;&#2352;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366; &#2342;&#2379;&#2344;&#2379;&#2306; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2325;&#2379; &#2313;&#2344;&#2325;&#2375; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; &#2309;&#2346;&#2344;&#2375; &#2342;&#2370;&#2360;&#2352;&#2375; &#2327;&#2381;&#2352;&#2366;&#2361;&#2325;&#2379;&#2306; &#2325;&#2379; &#2348;&#2375;&#2330;&#2340;&#2366;, &#2340;&#2379; &#2354;&#2366;&#2349; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2354;&#2327;&#2349;&#2327; &#2325;&#2367;&#2340;&#2344;&#2366; &#2361;&#2379;&#2340;&#2366; &#2332;&#2379; &#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352; &#2309;&#2352;&#2381;&#2332;&#2367;&#2340; &#2325;&#2352; &#2360;&#2325;&#2340;&#2366; &#2341;&#2366;?</span></p>\n",
                    options_en: ["<p>24.7%</p>\n", "<p>25.2%</p>\n", 
                                "<p>22.8%</p>\n", "<p>23.5%</p>\n"],
                    options_hi: ["<p>24.7%</p>\n", "<p>25.2%</p>\n",
                                "<p>22.8%</p>\n", "<p>23.5%</p>\n"],
                    solution_en: "<p>11.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Ratio - Marked Price : Cost Price </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cost - 20&times;50 : 17&times;50 = 1000 : 850</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">M.P of 5 kg wheat = (<span style=\"font-weight: 400;\">20&times;60%)</span></span><span style=\"font-family: Cambria Math;\"> &times; 5 kg = </span><span style=\"font-family: Cambria Math;\">&#8377; </span><span style=\"font-family: Cambria Math;\">60</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Here, Total marked price = total selling price = 1060 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1060</mn><mo>&times;</mo><mn>850</mn></mrow><mn>850</mn></mfrac><mo>&times;</mo><mn>100</mn></math></span><span style=\"font-family: Cambria Math;\">=24.7%</span></p>\n",
                    solution_hi: "<p>11.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340; - &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; : &#2354;&#2366;&#2327;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2327;&#2340; - 20&times;50 : 17&times;50 = 1000 : 850</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5 &#2325;&#2367;&#2354;&#2379; &#2327;&#2375;&#2361;&#2370;&#2306; &#2325;&#2366; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; =(<span style=\"font-weight: 400;\">20&times;60%)</span></span><span style=\"font-family: Cambria Math;\"> &times; 5 kg = &#8377;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">60</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2351;&#2361;&#2366;&#2305;, &#2325;&#2369;&#2354; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = &#2325;&#2369;&#2354; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = 1060 &#8377;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2354;&#2366;&#2349; % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1060</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>850</mn></mrow><mn>850</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 24.7%</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> A shopkeeper earns 15% profit on his goods. He loses 10% of his goods during transportation. What is his overall profit/loss percentage ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> &#2319;&#2325; &#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352; &#2309;&#2346;&#2344;&#2375; &#2350;&#2366;&#2354; &#2346;&#2352; 15% &#2325;&#2366; &#2354;&#2366;&#2349; &#2325;&#2350;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2357;&#2361; &#2350;&#2366;&#2354; &#2325;&#2366; 10% &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352;&#2367;&#2357;&#2361;&#2344; &#2350;&#2375;&#2306; &#2327;&#2306;&#2357;&#2366; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2360;&#2325;&#2366; &#2325;&#2369;&#2354; &#2354;&#2366;&#2349; / &#2361;&#2366;&#2344;&#2367; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Profit, 3.5%</p>\n", "<p>Profit, 5%</p>\n", 
                                "<p>Loss, 3.5%</p>\n", "<p>Loss, 5%</p>\n"],
                    options_hi: ["<p>&#2354;&#2366;&#2349;, 3.5%</p>\n", "<p>&#2354;&#2366;&#2349;, 5%</p>\n",
                                "<p>&#2361;&#2366;&#2344;&#2367;, 3.5%</p>\n", "<p>&#2361;&#2366;&#2344;&#2367;, 5%</p>\n"],
                    solution_en: "<p>12.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Required % = 15 - </span><span style=\"font-family: Cambria Math;\">10 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&times;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">= 3.5 % profit </span></p>\n",
                    solution_hi: "<p>12.(a)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; % = 15 - </span><span style=\"font-family: Cambria Math;\">10 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&times;</mo><mn>15</mn></mrow><mn>100</mn></mfrac></math>&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = 3.5 % &#2354;&#2366;&#2349;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\"> If an article is Sold for &#8377; 402, there is a loss of 33%. At what price should it be Sold to get 37% profit?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367; &#2319;&#2325; &#2357;&#2360;&#2381;&#2340;&#2369; &#8377; 402 &#2350;&#2375;&#2306; &#2348;&#2375;&#2330;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;, &#2340;&#2379; 33% &#2325;&#2368; &#2361;&#2366;&#2344;&#2367; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; 37% &#2325;&#2366; &#2354;&#2366;&#2349; &#2309;&#2352;&#2381;&#2332;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2311;&#2360;&#2375; &#2325;&#2367;&#2360; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; &#2348;&#2375;&#2330;&#2366; &#2332;&#2366;&#2344;&#2366; &#2330;&#2366;&#2361;&#2367;&#2319;?</span></p>\n",
                    options_en: ["<p>&#8377;911</p>\n", "<p>&#8377;618</p>\n", 
                                "<p>&#8377;822</p>\n", "<p>&#8377;781</p>\n"],
                    options_hi: ["<p>&#8377;911</p>\n", "<p>&#8377;618</p>\n",
                                "<p>&#8377;822</p>\n", "<p>&#8377;781</p>\n"],
                    solution_en: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Required price = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>402</mn><mn>67</mn></mfrac><mo>&times;</mo><mn>137</mn></math></span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\"> &#8377;822</span></p>\n",
                    solution_hi: "<p>13.(c)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2350;&#2370;&#2354;&#2381;&#2351; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>402</mn><mn>67</mn></mfrac><mo>&times;</mo><mn>137</mn></math></span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\"> &#8377;822</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> A discount of 20% is given on the marked price of a cycle, and still there is a profit of 20%. If the profit is &#8377;800, find the marked price of the cycle.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> &#2319;&#2325; &#2360;&#2366;&#2311;&#2325;&#2367;&#2354; &#2325;&#2375; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2346;&#2352; 20% &#2325;&#2368; &#2331;&#2370;&#2335; &#2342;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;, &#2324;&#2352; &#2313;&#2360;&#2325;&#2375; &#2348;&#2366;&#2342; &#2349;&#2368; &#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366; &#2325;&#2379; 20% &#2325;&#2366; &#2354;&#2366;&#2349; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2342;&#2367; &#2354;&#2366;&#2349; &#8377;800 &#2361;&#2376;, &#2340;&#2379; &#2360;&#2366;&#2311;&#2325;&#2367;&#2354; &#2325;&#2366; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>&#8377;4,000</p>\n", "<p>&#8377;5,800</p>\n", 
                                "<p>&#8377;4,800</p>\n", "<p>&#8377;6,000</p>\n"],
                    options_hi: ["<p>&#8377;4,000</p>\n", "<p>&#8377;5,800</p>\n",
                                "<p>&#8377;4,800</p>\n", "<p>&#8377;6,000</p>\n"],
                    solution_en: "<p>14.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">Let marked price = x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Selling</mi><mo>&nbsp;</mo><mi>price</mi><mo>=</mo><mi mathvariant=\"normal\">x</mi><mo>&times;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>Cost</mi><mo>&nbsp;</mo><mi>price</mi><mo>=</mo><mi mathvariant=\"normal\">x</mi><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&times;</mo><mfrac><mn>100</mn><mn>120</mn></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>3</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>Profit</mi><mo>&nbsp;</mo><mo>=</mo><mi>selling</mi><mo>&nbsp;</mo><mi>price</mi><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mi>cost</mi><mo>&nbsp;</mo><mi>price</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>4</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>5</mn></mfrac><mo>-</mo><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>3</mn></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>15</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>According</mi><mo>&nbsp;</mo><mi>to</mi><mo>&nbsp;</mo><mi>the</mi><mo>&nbsp;</mo><mi>question</mi><mo>,</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>15</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>800</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mi>marked</mi><mo>&nbsp;</mo><mi>price</mi><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&#8377;</mo><mn>6</mn><mo>,</mo><mn>000</mn></math></span></p>\n",
                    solution_hi: "<p>14.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2344;&#2366; &#2309;&#2306;&#2325;&#2367;&#2340; &#2350;&#2370;&#2354;&#2381;&#2351; = x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&nbsp;</mo><mo>=</mo><mi mathvariant=\"normal\">x</mi><mo>&times;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mspace linebreak=\"newline\"></mspace><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>=</mo><mi mathvariant=\"normal\">x</mi><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&times;</mo><mfrac><mn>100</mn><mn>120</mn></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>3</mn></mfrac><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2354;&#2366;&#2349;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>-</mo><mi>&#2354;&#2366;&#2327;&#2340;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>4</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>5</mn></mfrac><mo>-</mo><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>3</mn></mfrac><mo>=</mo><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>15</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</mi><mo>&nbsp;</mo><mi>&#2325;&#2375;</mi><mo>&nbsp;</mo><mi>&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</mi><mo>,</mo><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi></mrow><mn>15</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>800</mn><mo>&nbsp;</mo><mo>&rArr;</mo><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&nbsp;</mo><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo><mo>&nbsp;</mo><mo>=</mo><mo>&#8377;</mo><mn>6</mn><mo>,</mo><mn>000</mn></math></span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15.</span><span style=\"font-family: Cambria Math;\"> If oranges are bought at 11 for &#8377; 10 and Sold at 10 for &#8377;11, what is the gain or loss percentage?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">&#2351;&#2342;&#2367; 11 &#2360;&#2306;&#2340;&#2352;&#2375; &#8377;10 &#2350;&#2375;&#2306; &#2326;&#2352;&#2368;&#2342;&#2375; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#8377;11 &#2350;&#2375;&#2306; 10 &#2360;&#2306;&#2340;&#2352;&#2375; &#2325;&#2368; &#2342;&#2352; &#2360;&#2375; &#2348;&#2375;&#2330;&#2375; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2340;&#2379; &#2354;&#2366;&#2349; &#2351;&#2366; &#2361;&#2366;&#2344;&#2367; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Gain = 11%</p>\n", "<p>Loss = 11%</p>\n", 
                                "<p>Loss 21%</p>\n", "<p>Gain=21%</p>\n"],
                    options_hi: ["<p>&#2354;&#2366;&#2349; = 11%</p>\n", "<p>&#2361;&#2366;&#2344;&#2367; = 11%</p>\n",
                                "<p>&#2361;&#2366;&#2344;&#2367; = 21%</p>\n", "<p>&#2354;&#2366;&#2349; = 21%</p>\n"],
                    solution_en: "<p>15.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">According to the question,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C.P of 1 orange = </span><span style=\"font-family: Cambria Math;\">&#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>11</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">S.P of 1 orange = </span><span style=\"font-family: Cambria Math;\">&#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Gain % =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mstyle displaystyle=\"true\"><mfrac><mn>11</mn><mn>10</mn></mfrac></mstyle><mo>-</mo><mstyle displaystyle=\"true\"><mfrac><mn>10</mn><mn>11</mn></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mn>10</mn><mn>11</mn></mfrac></mstyle></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>21</mn><mn>110</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>10</mn><mn>11</mn></mfrac></mstyle></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 100 = 21%</span></p>\n",
                    solution_hi: "<p>15.(d)</p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344; &#2325;&#2375; &#2309;&#2344;&#2369;&#2360;&#2366;&#2352;,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 &#2360;&#2306;&#2340;&#2352;&#2375; &#2325;&#2366; &#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = </span><span style=\"font-family: Cambria Math;\">&#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>11</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 &#2360;&#2306;&#2340;&#2352;&#2375; &#2325;&#2366; &#2357;&#2367;&#2325;&#2381;&#2352;&#2351; &#2350;&#2370;&#2354;&#2381;&#2351; = </span><span style=\"font-family: Cambria Math;\">&#8377;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349; % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>11</mn><mn>10</mn></mfrac><mo>-</mo><mfrac><mn>10</mn><mn>11</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>10</mn><mn>11</mn></mfrac></mstyle></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>21</mn><mn>110</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>10</mn><mn>11</mn></mfrac></mstyle></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times;100 = 21%</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>