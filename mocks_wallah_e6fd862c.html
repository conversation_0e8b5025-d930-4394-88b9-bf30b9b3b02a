<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster. <br>MEK: NVP :: GYT: TBG :: JWH:?</p>",
                    question_hi: "<p>1. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है। <br>MEK: NVP :: GYT: TBG :: JWH:?</p>",
                    options_en: ["<p>QDS</p>", "<p>QDT</p>", 
                                "<p>PCR</p>", "<p>PCS</p>"],
                    options_hi: ["<p>QDS</p>", "<p>QDT</p>",
                                "<p>PCR</p>", "<p>PCS</p>"],
                    solution_en: "<p>1.(a)<br>,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732087062629.png\" alt=\"rId4\" width=\"201\" height=\"164\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732087062780.png\" alt=\"rId5\" width=\"204\" height=\"166\"><br>Similarly,<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732087062886.png\" alt=\"rId6\" width=\"207\" height=\"155\"></p>",
                    solution_hi: "<p>1.(a)<br>,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732087062992.png\" alt=\"rId7\" width=\"210\" height=\"177\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732087063098.png\" alt=\"rId8\" width=\"211\" height=\"175\"><br>इसी प्रकार,<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732087063197.png\" alt=\"rId9\" width=\"209\" height=\"162\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. 19 is related to 209 following a certain logic. Following the same logic, 27 is related to 297. To which of the following is 61 related, following the same logic? <br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>2. एक निश्चित तर्क का अनुसरण करते हुए 19, 209 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 27, 297 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 61 निम्नलिखित में से किससे संबंधित है? <br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>653</p>", "<p>671</p>", 
                                "<p>571</p>", "<p>600</p>"],
                    options_hi: ["<p>653</p>", "<p>671</p>",
                                "<p>571</p>", "<p>600</p>"],
                    solution_en: "<p>2.(b)<strong> Logic:-</strong> 1st number &times; 11 = 2nd number<br>(19, 209) :- 19 &times; 11 = 209<br>(27, 297) :- 27 &times; 11 = 297<br>Similarly,<br>(61 , 671) :- 61&times; 11 = 671</p>",
                    solution_hi: "<p>2.(b) <strong>तर्क:- </strong>पहली संख्या &times; 11 = दूसरी संख्या<br>(19, 209) :- 19 &times; 11 = 209<br>(27, 297) :- 27 &times; 11 = 297<br>इसी प्रकार,<br>(61 , 671) :- 61 &times; 11 = 671</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. 31 is related to 152 by certain logic. Following the same logic, 47 is related to 168. To which of the following is 66 related, following the same logic? (<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>3. 31 एक निश्चित तर्क का अनुसरण करते हुए 152 से संबंधित है। इसी तर्क का अनुसरण करते हुए 47, 168 से संबंधित है। समान तर्क का अनुसरण करते हुए, 66 निम्नलिखित में से किससे संबंधित है? <br>नोट: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>180</p>", "<p>190</p>", 
                                "<p>185</p>", "<p>187</p>"],
                    options_hi: ["<p>180</p>", "<p>190</p>",
                                "<p>185</p>", "<p>187</p>"],
                    solution_en: "<p>3.(d) <strong>Logic:-</strong> (1st number - 2nd number) = 121<br>(152 , 31):- (152 - 31) = 121<br>(168, 47):- (168 - 47) = 121<br>Similarly,<br>(187 , 66):- (187 - 66) = 121</p>",
                    solution_hi: "<p>3.(d)<strong> तर्क:-</strong> (पहली संख्या - दूसरी संख्या) = 121<br>(152 , 31):- (152 - 31) = 121<br>(168, 47):- (168 - 47) = 121<br>इसी प्रकार,<br>(187 , 66):- (187 - 66) = 121</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers. <br>(149, 213) <br>(168, 232)<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>4. उस विकल्प का चयन कीजिए, जिसमें संख्याएँ वही संबंध साझा करती हैं, जो संख्याओं के दिए गए युग्म द्वारा साझा किया गया है। <br>(149, 213) <br>(168, 232) <br>(नोट: पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(162, 222)</p>", "<p>(137, 211)</p>", 
                                "<p>(153, 217)</p>", "<p>(144, 198)</p>"],
                    options_hi: ["<p>(162, 222)</p>", "<p>(137, 211)</p>",
                                "<p>(153, 217)</p>", "<p>(144, 198)</p>"],
                    solution_en: "<p>4.(c) <strong>Logic :- </strong>(2nd number - 1st number) = 64<br>(149, 213) :- (213 - 149) = 64<br>(168 , 232):- (232 - 168) = 64<br>Similarly,<br>(153, 217):- (217 - 153) = 64</p>",
                    solution_hi: "<p>4.(c) <strong>तर्क :- </strong>(दूसरी संख्या - पहली संख्या) = 64<br>(149, 213) :- (213 - 149) = 64<br>(168 , 232):- (232 - 168) = 64<br>इसी प्रकार,<br>(153, 217):- (217 - 153) = 64</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "5. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br />(The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters/number of consonants/vowels in the word) <br />Egypt : Nile",
                    question_hi: "5. उस शब्द-युग्म का चयन करें, जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए संबंध के समान संबंधों का सबसे बेहतर ढंग से प्रतिनिधित्व करता है।<br />(शब्दों को अर्थपूर्ण हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों की संख्या/व्यंजनों/नोंस्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)<br />Egypt : Nile (मिस्र : नील)",
                    options_en: [" Russia : Mississippi", " South Korea : Mekong", 
                                " Brazil : Colorado", " Myanmar : Irrawaddy"],
                    options_hi: [" Russia : Mississippi (रूस : मिसीसिप्पी)", " South Korea : Mekong (दक्षिण कोरिया : मेकोंग)",
                                " Brazil : Colorado (ब्राजील : कोलोराडो) ", " Myanmar : Irrawaddy (म्यांमार : इरावदी)"],
                    solution_en: "5.(d)   <br />The ‘Nile’ is the river of egypt. Similarly the ‘Irrawaddy’ is the river of myanmar.",
                    solution_hi: "5.(d)   <br />\'नील\' मिस्र की  नदी है। इसी प्रकार \'इरावदी\' म्यांमार की नदी है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(18, 378, 7)<br>(14, 210, 5)</p>",
                    question_hi: "<p>6. उस समुच्चय का चयन करें जिसमें दी गई संख्याएँ आपस में उसी प्रकार संबंधित हैं, जिस प्रकार प्रश्न में दिए गए समुच्चय की संख्याएँ आपस में संबंधित हैं।<br>(<strong>ध्यान दें : </strong>संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएँ की जानी चाहिए। जैसे, 13 के मामले में - 13 पर की जाने वाली विभिन्न गणितीय संक्रियाएँ जैसे जोड़ना / घटाना / गुणा करना आदि केवल 13 के साथ की जा सकती हैं। लेकिन 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>(18, 378, 7)<br>(14, 210, 5)</p>",
                    options_en: ["<p>(15, 405, 9)</p>", "<p>(23, 230, 5)</p>", 
                                "<p>(17, 204, 6)</p>", "<p>(17, 272, 8)</p>"],
                    options_hi: ["<p>(15, 405, 9)</p>", "<p>(23, 230, 5)</p>",
                                "<p>(17, 204, 6)</p>", "<p>(17, 272, 8)</p>"],
                    solution_en: "<p>6.(a) <strong>Logic: </strong>(first number &times; third number) &times; 3 = second number<br>(18, 378, 7) <math display=\"inline\"><mo>&#8658;</mo></math> (18 &times; 7) &times; 3 = 378<br>(14, 210, 5) <math display=\"inline\"><mo>&#8658;</mo></math>(14 &times; 5) &times; 3 = 210<br>Similarly,<br>(15, 405, 9) <math display=\"inline\"><mo>&#8658;</mo></math> (15 &times; 9) &times; 3 = 405</p>",
                    solution_hi: "<p>6.(a) <strong>तर्क: </strong>(पहली संख्या &times; तीसरी संख्या) &times; 3 = दूसरी संख्या<br>(18, 378, 7) <math display=\"inline\"><mo>&#8658;</mo></math> (18 &times; 7) &times; 3 = 378<br>(14, 210, 5) <math display=\"inline\"><mo>&#8658;</mo></math>(14 &times; 5) &times; 3 = 210<br>इसी प्रकार,<br>(15, 405, 9) <math display=\"inline\"><mo>&#8658;</mo></math> (15 &times; 9) &times; 3 = 405</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the triad in which the numbers are related in the same way as are the numbers of the following triads.<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g., 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(2, 3, 17)<br>(5, 2, 21)</p>",
                    question_hi: "<p>7. उस त्रिक का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित त्रिकों की संख्याएँ संबंधित हैं।<br>(<strong>नोट :</strong> संख्याओं को उनके घटक अंकों में विभक्त किए बिना, पूर्ण संख्याओं पर संक्रिया किया जाना चाहिए। उदाहरण के लिए 13 :- 13 पर संक्रिया जैसे 13 में जोड़/घटाव/गुणा आदि किया जा सकता है। 13 को 1 और 3 में विभक्त कर और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)।<br>(2, 3, 17)<br>(5, 2, 21)</p>",
                    options_en: ["<p>(3, 4, 23)</p>", "<p>(5, 6, 72)</p>", 
                                "<p>(1, 4, 17)</p>", "<p>(2, 5, 42)</p>"],
                    options_hi: ["<p>(3, 4, 23)</p>", "<p>(5, 6, 72)</p>",
                                "<p>(1, 4, 17)</p>", "<p>(2, 5, 42)</p>"],
                    solution_en: "<p>7.(a) <strong>Logic:</strong> (first number &times; second number) + 11 = third number<br>(2, 3, 17) <math display=\"inline\"><mo>&#8658;</mo></math> (2 &times; 3) + 11 = 17<br>(5, 2, 21) <math display=\"inline\"><mo>&#8658;</mo></math> (5 &times; 2) + 11 = 21<br>Similarly,<br>(3, 4, 23) <math display=\"inline\"><mo>&#8658;</mo></math> (3 &times; 4) + 11 = 23</p>",
                    solution_hi: "<p>7.(a) <strong>तर्क: </strong>(पहली संख्या &times; दूसरी संख्या) + 11 = तीसरी संख्या<br>(2, 3, 17) <math display=\"inline\"><mo>&#8658;</mo></math> (2 &times; 3) + 11 = 17<br>(5, 2, 21) <math display=\"inline\"><mo>&#8658;</mo></math> (5 &times; 2) + 11 = 21<br>इसी प्रकार,<br>(3, 4, 23) <math display=\"inline\"><mo>&#8658;</mo></math> (3 &times; 4) + 11 = 23</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the triad in which the numbers are related in the same way as are the numbers of the given triads.<br>(16, 33, 67), (19, 39, 79)<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>8. उस त्रिक का चयन कीजिए, जिसमें संख्याएँ एक-दूसरे से उसी तरह संबंधित हैं, जैसे दिए गए त्रिकों की संख्याएँ हैं।<br>(16, 33, 67), (19, 39, 79)<br>(<strong>ध्यान दीजिए :</strong> संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 - 13 पर संक्रिया जैसे जोड़ना / घटाना / गुणा करना आदि 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>(22, 45, 91)</p>", "<p>(21, 45, 90)</p>", 
                                "<p>(18, 36, 80)</p>", "<p>(14, 27, 53)</p>"],
                    options_hi: ["<p>(22, 45, 91)</p>", "<p>(21, 45, 90)</p>",
                                "<p>(18, 36, 80)</p>", "<p>(14, 27, 53)</p>"],
                    solution_en: "<p>8.(a) <strong>Logic:-</strong> (3rd number - 2nd number)<math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 = 1st number<br>(16, 33, 67):- (67 -33)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (34)&divide; 2 - 1 &rArr; (17) - 1 = 16 <br>(19, 39, 79):- (79 - 39)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (40)&divide; 2 -1 &rArr; (20)-1 = 19<br>Similarly,<br>(22, 45, 91):- (91 - 45)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (46)&divide;2 -1 &rArr; (23) -1 = 22</p>",
                    solution_hi: "<p>8.(a) <strong>तर्क:</strong> (तीसरी संख्या - दूसरी संख्या) <math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 = पहली संख्या<br>(16, 33, 67):- (67 -33)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (34)&divide; 2 - 1 &rArr; (17) - 1 = 16 <br>(19, 39, 79):- (79 - 39)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (40)&divide; 2 -1 &rArr; (20)-1 = 19<br>इसी प्रकार,<br>(22, 45, 91):- (91 - 45)<math display=\"inline\"><mo>&#247;</mo></math>2 -1 &rArr; (46)&divide;2 -1 &rArr; (23) -1 = 22</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>(19, 456, 6) <br>(16, 320, 5)</p>",
                    question_hi: "<p>9. उस समुच्चय का चयन करें जिसमें दी गई संख्याएँ आपस में उसी प्रकार संबंधित हैं, जिस प्रकार प्रश्न में दिए गए समुच्चय की संख्याएँ आपस में संबंधित हैं।<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएँ की जानी चाहिए। जैसे, 13 के मामले में - 13 पर की जाने वाली विभिन्न गणितीय संक्रियाएँ जैसे जोड़ना / घटाना / गुणा करना आदि केवल 13 के साथ की जा सकती हैं। लेकिन 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>(19, 456, 6) <br>(16, 320, 5)</p>",
                    options_en: ["<p>(18, 378, 7)</p>", "<p>(14, 210, 5)</p>", 
                                "<p>(21, 504, 8)</p>", "<p>(18, 648, 9)</p>"],
                    options_hi: ["<p>(18, 378, 7)</p>", "<p>(14, 210, 5)</p>",
                                "<p>(21, 504, 8)</p>", "<p>(18, 648, 9)</p>"],
                    solution_en: "<p>9.(d) <strong>Logic :- </strong>(1st number &times; 3rd number) &times; 4 = 2nd number<br>(19, 456, 6) :- (19 &times; 6)&times;4 &rArr; (114)&times;4 = 456<br>(16, 320,5):- (16 &times; 5)&times;4 &rArr; (80)&times;4 = 320<br>Similarly,<br>(18, 648, 9) :- (18 &times; 9)&times;4 &rArr; (162)&times;4 = 648</p>",
                    solution_hi: "<p>9.(d) <strong>तर्क :- </strong>(पहली संख्या &times; तीसरी संख्या) &times; 4 = दूसरी संख्या<br>(19, 456, 6) :- (19 &times; 6)&times;4 &rArr; (114)&times;4 = 456<br>(16, 320,5):- (16 &times; 5)&times;4 &rArr; (80)&times;4 = 320<br>इसी प्रकार,<br>(18, 648, 9) :- (18 &times; 9)&times;4 &rArr; (162)&times;4 = 648</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In the following number-pairs, the second number is obtained by applying certain mathematical operations to the first number. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>(12, 145) <br>(9, 82)</p>",
                    question_hi: "<p>10. निम्नलिखित संख्या-युग्&zwj;मों में, पहली संख्या पर निश्चित गणितीय संक्रियाएँ करके दूसरी संख्या प्राप्त की जाती है। उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्&zwj;चय की संख्याएँ संबंधित हैं।<br>(<strong>नोट :</strong> संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(12, 145) <br>(9, 82)</p>",
                    options_en: ["<p>(16, 256)</p>", "<p>(14, 197)</p>", 
                                "<p>(22, 483)</p>", "<p>(8, 69)</p>"],
                    options_hi: ["<p>(16, 256)</p>", "<p>(14, 197)</p>",
                                "<p>(22, 483)</p>", "<p>(8, 69)</p>"],
                    solution_en: "<p>10.(b) <strong>Logic :-</strong> (1st number)<sup>2</sup> + 1 = 2nd number<br>(12, 145) :- (12)<sup>2</sup> + 1 &rArr; 144 + 1 = 145<br>(9, 82) :- (9)<sup>2</sup> + 1 &rArr; 81 + 1 = 82<br>Similarly,<br>(14, 197) :- (14)<sup>2</sup> + 1 &rArr; 196 + 1 = 197</p>",
                    solution_hi: "<p>10.(b) <strong>तर्क :-</strong> (पहली संख्या)<sup>2</sup> + 1 = दूसरी संख्या<br>(12, 145) :- (12)<sup>2</sup> + 1 &rArr; 144 + 1 = 145<br>(9, 82) :- (9)<sup>2</sup> + 1 &rArr; 81 + 1 = 82<br>इसी प्रकार,<br>(14, 197) :- (14)<sup>2</sup> + 1 &rArr; 196 + 1 = 197</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster. <br>CWE : ZFX : : PYT : MDI : : KIL : ?</p>",
                    question_hi: "<p>11. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है।<br>CWE : ZFX : : PYT : MDI : : KIL : ?</p>",
                    options_en: ["<p>PRO</p>", "<p>RTQ</p>", 
                                "<p>STQ</p>", "<p>PQO</p>"],
                    options_hi: ["<p>PRO</p>", "<p>RTQ</p>",
                                "<p>STQ</p>", "<p>PQO</p>"],
                    solution_en: "<p>11.(b)<br>,<img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfUSbPKizfsaLqrVxNCAkykBT-aAXQzHaEw5P-_LeEWUHyS-WuMcnuVPYYovLdaoZ12FUXsrkkH2dymSsEN-EWfrUHQLA20sfimfCV9RQHxCSXdE3MGpHF63vv7GE9PP-deWPUY2Q?key=mCjNNuXUQ0uz2WAbUWIdBtZd\" width=\"86\" height=\"238\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc9f-fQvHERhjoJVT0tvB50xtgvnaShsRksVvjVT7pxq37ov-mf71bFvuUqnqzV-HHXN-zoenpBvy4_FNmQ9qbndFACt7j5rJ2t1RMYIVcgib7URkS5POz4orQepbgR4tyA0UlG9g?key=mCjNNuXUQ0uz2WAbUWIdBtZd\" width=\"85\" height=\"240\"><br>Similarly,<br><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcdRKNszDkL4XSX8e-NPMuvXJUjLfTBJ4-f9t888jozgLP622xBYrNKJjzlMJH7AEApUbMA3p2RrXNsvpB6STaMY507aJiRFgE5T9J_cLZTB3HRkQeQWgzFchCghayDig-RyZK4pA?key=mCjNNuXUQ0uz2WAbUWIdBtZd\" width=\"98\" height=\"249\"></p>",
                    solution_hi: "<p>11.(b)<br>,<img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcjWlCbtCrGV3sTLrlD_eZNggQGw2oDIjA3aRc6sZIfQ7TmnRwbCcXVO-GLnhvdO9iRycrbPffE0BWol6nfFRKpd7Yr33eyUmEdAJjFr7AvVd2w4uukXOMplUIqMZnGu6emy0mB4A?key=mCjNNuXUQ0uz2WAbUWIdBtZd\" width=\"80\" height=\"226\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdVVJ0lkVETEImJeLVIbJ2bIMLMjIf0muKdtTmsR1qkgUqLclt89ganDHW4F-ClQC1zSg0XEmg7vUgFLxlGTp-NoyN9fHz-9Fm2m4ARxcG8CjRA70A0a4hNJzjUIHB7CjyLJljA1w?key=mCjNNuXUQ0uz2WAbUWIdBtZd\" width=\"80\" height=\"223\"><br>इसी प्रकार,<br><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeuKjwq2SBCLAboTSAmzgwMqfe57eI8UwnMVwaike2S6yHfhpM2K5RjLcUzSL-c4uvtkGFThu9DBpQIOFr87B-SnVxO5Uc0yzhdZudGpw6u3didpgOhVhzuy3XPnGtGTKPC5lEzyQ?key=mCjNNuXUQ0uz2WAbUWIdBtZd\" width=\"89\" height=\"235\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the set in which the numbers are related in the same way as are the numbers of the following sets. <br>(34, 47, 62) <br>(119, 142, 167) <br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>12. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित समुच्चयों की संख्याएँ संबंधित हैं।<br>(34, 47, 62)<br>(119, 142, 167)<br>(नोट: पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(27, 38, 51)</p>", "<p>(48, 65, 82)</p>", 
                                "<p>(64, 78, 99)</p>", "<p>(79, 98, 119)</p>"],
                    options_hi: ["<p>(27, 38, 51)</p>", "<p>(48, 65, 82)</p>",
                                "<p>(64, 78, 99)</p>", "<p>(79, 98, 119)</p>"],
                    solution_en: "<p>12.(d) <strong>Logic:-</strong> (1st number + 3rd number)<math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 = 2nd number<br>(34, 47, 62) :- (62 + 34)<math display=\"inline\"><mo>&#247;</mo></math>2 - 1 &rArr; (96)&divide;2 - 1 &rArr; 48 - 1 = 47<br>(119, 142, 167) :- (167 + 119)<math display=\"inline\"><mo>&#247;</mo></math>2 - 1 &rArr; (286)&divide;2 -1 &rArr; 143 - 1 = 142<br>Similarly,<br>(79, 98, 119) :- (119 + 79)<math display=\"inline\"><mo>&#247;</mo></math>2 - 1 &rArr; (198)&divide;2 - 1 &rArr; 99 - 1 = 98</p>",
                    solution_hi: "<p>12.(d) <strong>तर्क:-</strong> (पहली संख्या + तीसरी संख्या) <math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 = दूसरी संख्या<br>(34, 47, 62) :- (62 + 34)<math display=\"inline\"><mo>&#247;</mo></math>2 - 1 &rArr; (96)&divide;2 - 1 &rArr; 48 - 1 = 47<br>(119, 142, 167) :- (167 + 119)<math display=\"inline\"><mo>&#247;</mo></math>2 - 1 &rArr; (286)&divide;2 -1 &rArr; 143 - 1 = 142<br>इसी प्रकार,<br>(79, 98, 119) :- (119 + 79)<math display=\"inline\"><mo>&#247;</mo></math>2 - 1 &rArr; (198)&divide;2 - 1 &rArr; 99 - 1 = 98</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13.  ‘Blossom’ is related to ‘Wither’ in the same way as ‘Stagnate’ is related to ‘________’.<br />(The words must be considered as meaningful English words and must not be related to each<br />other based on the number of letters/number of consonants/vowels in the word.)",
                    question_hi: "13. \'खिलना\' (Blossom),\'मुरझाना\' (Wither) से उसी प्रकार संबंधित है, जिस प्रकार \'स्थिर होना\' (Stagnate) \'________\' से संबंधित है।<br />(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्‍दों को अक्षरों की संख्या/व्यंजनों/नोंस्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं<br />किया जाना चाहिए।",
                    options_en: [" Stand", " Rest", 
                                " Languish", " Flow"],
                    options_hi: [" खड़े रहना (Stand) ", " विराम (Rest) ",
                                " मंद पड़ना (Languish)  ", " बहना (Flow)"],
                    solution_en: "13.(d) As ‘Blossom’ and ‘Wither’ are opposite of each other similarly, ‘Stagnate’ and ‘Flow’ are opposite of each other.",
                    solution_hi: "13.(d) जैसे \'खिलना\' और \'मुरझाना\' एक दूसरे के विपरीत हैं, उसी प्रकार \'स्थिर\' और \'बहना\' एक दूसरे के विपरीत हैं।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. 10 is related to 15 following certain logic. Following the same logic, 42 is related to 63. To which of the following numbers is 22 related, following the same logic? (<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>14. किसी निश्चित तर्क के अनुसार 10 का संबंध 15 से है। उसी तर्क के अनुसार 42 का संबंध 63 से है। उसी तर्क के अनुसार 22 निम्नलिखित में से किस संख्या से संबंधित है?<br><strong>नोट :</strong> संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>28</p>", "<p>30</p>", 
                                "<p>33</p>", "<p>31</p>"],
                    options_hi: ["<p>28</p>", "<p>30</p>",
                                "<p>33</p>", "<p>31</p>"],
                    solution_en: "<p>14.(c) <strong>Logic:-</strong> <math display=\"inline\"><mfrac><mrow><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>&#160;</mi><mi>n</mi><mi>o</mi><mo>.</mo></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 3 = 2<sup>nd</sup>no.<br>(10 , 15) : <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5 &times; 3 = 15<br>(42 , 63) :- <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 21 &times; 3 = 63<br>Similarly,<br>(22 , 33) :- <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 11 &times; 3 = 33</p>",
                    solution_hi: "<p>14.(c)<strong> तर्क:-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;</mi><mi>&#2361;</mi><mi>&#2354;&#2368;</mi><mi>&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi>&#160;</mi></mrow><mn>2</mn></mfrac></math> &times; 3 = दूसरी संख्या <br>(10 , 15) : <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 5 &times; 3 = 15<br>(42 , 63) :- <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 21 &times; 3 = 63<br>इसी प्रकार,<br>(22 , 33) :- <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 11 &times; 3 = 33</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "15. Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br />Temerity : Shyness :: Pique : ?",
                    question_hi: "15. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है। (शब्दों को अर्थपूर्ण अंग्रेजी शब्दों के रूप में माना जाना चाहिए और शब्द आपस में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)<br />उतावलापन : शर्मीला :: मनमुटाव : ?",
                    options_en: [" Resentment  ", " Anger ", 
                                " Delight ", " Offence"],
                    options_hi: [" नाराज़गी  ", " गुस्सा ",
                                " आनंद", " अपराध"],
                    solution_en: "15.(c) As ‘Temerity’ and ‘Shyness’ are opposite of each other. Similarly ‘Pique’ and ‘Delight’ are opposite of each other.",
                    solution_hi: "15.(c) चूँकि \'उतावलापन\' और \'शर्मीला\' एक दूसरे के विपरीत हैं। इसी प्रकार \'मनमुटाव\' और \'आनंद\' एक दूसरे के विपरीत हैं।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>