<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the word OPPOSITE in meaning to the given word.<br>Deficient</p>",
                    question_hi: "<p>1. Select the word OPPOSITE in meaning to the given word.<br>Deficient</p>",
                    options_en: ["<p>Skimpy</p>", "<p>Broken</p>", 
                                "<p>Ample</p>", "<p>Terrible</p>"],
                    options_hi: ["<p>Skimpy</p>", "<p>Broken</p>",
                                "<p>Ample</p>", "<p>Terrible</p>"],
                    solution_en: "<p>1.(c) <strong>Ample</strong>- more than enough.<br><strong>Deficient</strong>- lacking in some essential quality or quantity.<br><strong>Skimpy</strong>- inadequate in amount.<br><strong>Broken</strong>- damaged or in pieces.<br><strong>Terrible</strong>- extremely bad or unpleasant.</p>",
                    solution_hi: "<p>1.(c) <strong>Ample </strong>(प्रचुर)- more than enough.<br><strong>Deficient </strong>(अपर्याप्त)- lacking in some essential quality or quantity.<br><strong>Skimpy </strong>(अपर्याप्त)- inadequate in amount.<br><strong>Broken </strong>(क्षतिग्रस्त)- damaged or in pieces.<br><strong>Terrible </strong>(अप्रिय)- extremely bad or unpleasant.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate ANTONYM of the given word.<br>Meander</p>",
                    question_hi: "<p>2. Select the most appropriate ANTONYM of the given word.<br>Meander</p>",
                    options_en: ["<p>Ramble</p>", "<p>Deputise</p>", 
                                "<p>Confirm</p>", "<p>Direct</p>"],
                    options_hi: ["<p>Ramble</p>", "<p>Deputise</p>",
                                "<p>Confirm</p>", "<p>Direct</p>"],
                    solution_en: "<p>2.(d) <strong>Direct</strong>- straight and clear in course or action.<br><strong>Meander</strong>- to wander aimlessly or without a clear direction.<br><strong>Ramble</strong>- to talk or walk aimlessly without purpose.<br><strong>Deputise</strong>- to act on behalf of someone in their absence.<br><strong>Confirm</strong>- to establish the truth or correctness of something.</p>",
                    solution_hi: "<p>2.(d) <strong>Direct </strong>(प्रत्यक्ष)- straight and clear in course or action.<br><strong>Meander </strong>(लक्ष्यहीन)- to wander aimlessly or without a clear direction.<br><strong>Ramble </strong>(उद्देश्यहीन)- to talk or walk aimlessly without purpose.<br><strong>Deputise </strong>(प्रतिनिधित्व करना)- to act on behalf of someone in their absence.<br><strong>Confirm </strong>(पुष्टि करना)- to establish the truth or correctness of something.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the most appropriate ANTONYM of the underlined word in the following sentence.<br>In Shakespeare\'s The Merchant of Venice, Shylock\'s connivance aimed to <span style=\"text-decoration: underline;\">jeopardise</span> Antonio\'s life.</p>",
                    question_hi: "<p>3. Select the most appropriate ANTONYM of the underlined word in the following sentence.<br>In Shakespeare\'s The Merchant of Venice, Shylock\'s connivance aimed to <span style=\"text-decoration: underline;\">jeopardise</span> Antonio\'s life.</p>",
                    options_en: ["<p>Safeguard</p>", "<p>Risk</p>", 
                                "<p>Peril</p>", "<p>Jape</p>"],
                    options_hi: ["<p>Safeguard</p>", "<p>Risk</p>",
                                "<p>Peril</p>", "<p>Jape</p>"],
                    solution_en: "<p>3.(a) <strong>Safeguard</strong>- to protect or defend from harm.<br><strong>Jeopardise</strong>- to put in danger or at risk.<br><strong>Risk</strong>- the possibility of loss or harm.<br><strong>Peril</strong>- serious and immediate danger.<br><strong>Jape</strong>- a joke or prank.</p>",
                    solution_hi: "<p>3.(a) <strong>Safeguard </strong>(सुरक्षा करना)- to protect or defend from harm.<br><strong>Jeopardise </strong>(जोखिम में डालना)- to put in danger or at risk.<br><strong>Risk </strong>(जोखिम)- the possibility of loss or harm.<br><strong>Peril </strong>(संकट)- serious and immediate danger.<br><strong>Jape </strong>(मजाक)- a joke or prank.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the most appropriate ANTONYM of the given word. <br>Brittle</p>",
                    question_hi: "<p>4. Select the most appropriate ANTONYM of the given word. <br>Brittle</p>",
                    options_en: ["<p>Crunchy</p>", "<p>Resilient</p>", 
                                "<p>Delicate</p>", "<p>Aloof</p>"],
                    options_hi: ["<p>Crunchy</p>", "<p>Resilient</p>",
                                "<p>Delicate</p>", "<p>Aloof</p>"],
                    solution_en: "<p>4.(b) <strong>Resilient</strong>- able to withstand or recover quickly.<br><strong>Brittle</strong>- easily broken or cracked.<br><strong>Crunchy</strong>- making a crisp sound when bitten.<br><strong>Delicate</strong>- fragile or easily damaged.<br><strong>Aloof</strong>- emotionally distant or detached.</p>",
                    solution_hi: "<p>4.(b) <strong>Resilient </strong>(दृढ़)- able to withstand or recover quickly.<br><strong>Brittle </strong>(भंगुर)- easily broken or cracked.<br><strong>Crunchy </strong>(कुरकुरा)- making a crisp sound when bitten.<br><strong>Delicate </strong>(नाजुक)- fragile or easily damaged.<br><strong>Aloof </strong>(अलग/पृथक)- emotionally distant or detached.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the most appropriate ANTONYM of the underlined word in the given sentence. <br><span style=\"text-decoration: underline;\">Success </span>is not a destination, it&rsquo;s a journey.</p>",
                    question_hi: "<p>5. Select the most appropriate ANTONYM of the underlined word in the given sentence. <br><span style=\"text-decoration: underline;\">Success </span>is not a destination, it&rsquo;s a journey.</p>",
                    options_en: ["<p>Sensation</p>", "<p>Opulence</p>", 
                                "<p>Victory</p>", "<p>Failure</p>"],
                    options_hi: ["<p>Sensation</p>", "<p>Opulence</p>",
                                "<p>Victory</p>", "<p>Failure</p>"],
                    solution_en: "<p>5.(d) <strong>Failure</strong>- lack of success.<br><strong>Success</strong>- accomplishment of an aim or purpose.<br><strong>Sensation</strong>- a widespread reaction of interest and excitement.<br><strong>Opulence</strong>- great wealth or luxuriousness.<br><strong>Victory</strong>- an act of defeating an opponent or overcoming an obstacle.</p>",
                    solution_hi: "<p>5.(d) <strong>Failure</strong> (असफलता)- lack of success.<br><strong>Success </strong>(सफलता)- accomplishment of an aim or purpose.<br><strong>Sensation </strong>(संवेदना)- a widespread reaction of interest and excitement.<br><strong>Opulence </strong>(ऐश्वर्य)- great wealth or luxuriousness.<br><strong>Victory </strong>(विजय)- an act of defeating an opponent or overcoming an obstacle.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Identity the most appropriate ANTONYM of the given word.<br>Secure</p>",
                    question_hi: "<p>6. Identity the most appropriate ANTONYM of the given word.<br>Secure</p>",
                    options_en: ["<p>Succeed</p>", "<p>Save</p>", 
                                "<p>Attack</p>", "<p>Trundle</p>"],
                    options_hi: ["<p>Succeed</p>", "<p>Save</p>",
                                "<p>Attack</p>", "<p>Trundle</p>"],
                    solution_en: "<p>6.(c) <strong>Attack</strong>- to take aggressive action against.<br><strong>Secure</strong>- to make sure something is protected from danger or threat.<br><strong>Succeed</strong>- to achieve a desired aim.<br><strong>Save</strong>- to keep safe from harm or loss.<br><strong>Trundle</strong>- to move or roll heavily or clumsily.</p>",
                    solution_hi: "<p>6.(c) <strong>Attack </strong>(हमला करना)- to take aggressive action against.<br><strong>Secure </strong>(सुरक्षित करना)- to make sure something is protected from danger or threat.<br><strong>Succeed </strong>(सफल होना)- to achieve a desired aim.<br><strong>Save </strong>(बचाना)- to keep safe from harm or loss.<br><strong>Trundle </strong>(लुढ़कना)- to move or roll heavily or clumsily.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate ANTONYM of the given word.<br>Receive</p>",
                    question_hi: "<p>7. Select the most appropriate ANTONYM of the given word.<br>Receive</p>",
                    options_en: ["<p>Admit</p>", "<p>Exile</p>", 
                                "<p>Reward</p>", "<p>Fund</p>"],
                    options_hi: ["<p>Admit</p>", "<p>Exile</p>",
                                "<p>Reward</p>", "<p>Fund</p>"],
                    solution_en: "<p>7.(b) <strong>Exile</strong>- the state of being barred from one\'s native country.<br><strong>Receive</strong>- to be given or presented with something.<br><strong>Admit</strong>- to allow entry or access.<br><strong>Reward</strong>- something given in recognition of service, effort, or achievement.<br><strong>Fund</strong>- a sum of money saved or made available for a particular purpose.</p>",
                    solution_hi: "<p>7.(b) <strong>Exile </strong>(निर्वासन)- the state of being barred from one\'s native country.<br><strong>Receive </strong>(ग्रहण करना)- to be given or presented with something.<br><strong>Admit </strong>(स्वीकार करना)- to allow entry or access.<br><strong>Reward </strong>(पुरस्कार)- something given in recognition of service, effort, or achievement.<br><strong>Fund </strong>(निधि/पूंजी)- a sum of money saved or made available for a particular purpose.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate ANTONYM of the word in brackets to fill in the blank.<br>She has _______ (witty) remarks about everything.</p>",
                    question_hi: "<p>8. Select the most appropriate ANTONYM of the word in brackets to fill in the blank.<br>She has _______ (witty) remarks about everything.</p>",
                    options_en: ["<p>sorry</p>", "<p>whimsical</p>", 
                                "<p>unamusing</p>", "<p>woeful</p>"],
                    options_hi: ["<p>sorry</p>", "<p>whimsical</p>",
                                "<p>unamusing</p>", "<p>woeful</p>"],
                    solution_en: "<p>8.(c) <strong>Unamusing</strong>- not entertaining.<br><strong>Witty</strong>- cleverly humorous or sharp in expression.<br><strong>Sorry</strong>- feeling regret or sorrow.<br><strong>Whimsical</strong>- playfully unpredictable or fanciful.<br><strong>Woeful</strong>- full of sorrow or misery.</p>",
                    solution_hi: "<p>8.(c) <strong>Unamusing </strong>(नीरस)- not entertaining.<br><strong>Witty </strong>(हास्यकर)- cleverly humorous or sharp in expression.<br><strong>Sorry </strong>(पछतावा)- feeling regret or sorrow.<br><strong>Whimsical </strong>(मनमौजी)- playfully unpredictable or fanciful.<br><strong>Woeful </strong>(दुःखदायी)- full of sorrow or misery.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate ANTONYM of the given word.<br>Paralysed</p>",
                    question_hi: "<p>9. Select the most appropriate ANTONYM of the given word.<br>Paralysed</p>",
                    options_en: ["<p>Demure</p>", "<p>Stifled</p>", 
                                "<p>Sultry</p>", "<p>Healthy</p>"],
                    options_hi: ["<p>Demure</p>", "<p>Stifled</p>",
                                "<p>Sultry</p>", "<p>Healthy</p>"],
                    solution_en: "<p>9.(d) <strong>Healthy</strong>- in good physical or mental condition.<br><strong>Paralysed</strong>- unable to move or act.<br><strong>Demure</strong>- modest, shy, or reserved.<br><strong>Stifled</strong>- unable to breathe properly.<br><strong>Sultry</strong>- hot and humid, or passionate and alluring.</p>",
                    solution_hi: "<p>9.(d) <strong>Healthy </strong>(स्वस्थ)- in good physical or mental condition.<br><strong>Paralysed </strong>(लकवाग्रस्त/शक्तिहीन)- unable to move or act.<br><strong>Demure </strong>(संकोची)- modest, shy, or reserved.<br><strong>Stifled </strong>(घुटन होना)- unable to breathe properly.<br><strong>Sultry </strong>(उमसदार/वातहीन )- hot and humid, or passionate and alluring.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the most appropriate ANTONYM of the given word.<br>Catastrophe</p>",
                    question_hi: "<p>10. Select the most appropriate ANTONYM of the given word.<br>Catastrophe</p>",
                    options_en: ["<p>Success</p>", "<p>Nurture</p>", 
                                "<p>Immaculate</p>", "<p>Disaster</p>"],
                    options_hi: ["<p>Success</p>", "<p>Nurture</p>",
                                "<p>Immaculate</p>", "<p>Disaster</p>"],
                    solution_en: "<p>10.(a) <strong>Success</strong>- the accomplishment of an aim or purpose.<br><strong>Catastrophe</strong>- a sudden and widespread disaster.<br><strong>Nurture</strong>- to care for and encourage growth or development.<br><strong>Immaculate</strong>- perfectly clean or free from flaws.<br><strong>Disaster</strong>- a sudden event causing great damage or suffering.</p>",
                    solution_hi: "<p>10.(a) <strong>Success </strong>(सफलता)- the accomplishment of an aim or purpose.<br><strong>Catastrophe </strong>(प्रलय)- a sudden and widespread disaster.<br><strong>Nurture </strong>(पालन-पोषण)- to care for and encourage growth or development.<br><strong>Immaculate </strong>(निर्मल)- perfectly clean or free from flaws.<br><strong>Disaster </strong>(आपदा/विपत्ति)- a sudden event causing great damage or suffering.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>One must be very <span style=\"text-decoration: underline;\">strong </span>to lift this box of glass jars.</p>",
                    question_hi: "<p>11. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>One must be very <span style=\"text-decoration: underline;\">strong </span>to lift this box of glass jars.</p>",
                    options_en: ["<p>Mighty</p>", "<p>Weak</p>", 
                                "<p>Complicated</p>", "<p>Rugged</p>"],
                    options_hi: ["<p>Mighty</p>", "<p>Weak</p>",
                                "<p>Complicated</p>", "<p>Rugged</p>"],
                    solution_en: "<p>11.(b) <strong>Weak</strong><br><strong>Mighty</strong>- possessing great and impressive power or strength.<br><strong>Complicated</strong>- consisting of many interconnecting parts or elements.<br><strong>Rugged</strong>- having a rough, uneven surface.</p>",
                    solution_hi: "<p>11.(b) <strong>Weak</strong><br><strong>Mighty </strong>(पराक्रमी/शक्तिशाली)- possessing great and impressive power or strength.<br><strong>Complicated </strong>(जटिल)- consisting of many interconnecting parts or elements.<br><strong>Rugged </strong>(खुरदरा/ऊबड़-खाबड़)- having a rough, uneven surface.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate ANTONYM of the given word.<br>Monotonous</p>",
                    question_hi: "<p>12. Select the most appropriate ANTONYM of the given word.<br>Monotonous</p>",
                    options_en: ["<p>Tiresome</p>", "<p>Tedious</p>", 
                                "<p>Engrossing</p>", "<p>Rational</p>"],
                    options_hi: ["<p>Tiresome</p>", "<p>Tedious</p>",
                                "<p>Engrossing</p>", "<p>Rational</p>"],
                    solution_en: "<p>12.(c) <strong>Engrossing</strong>- absorbing all attention or interest<br><strong>Monotonous</strong>- lacking in variety and interest.<br><strong>Tiresome</strong>- causing one to feel bored or annoyed.<br><strong>Tedious</strong>- too long, slow, or dull.<br><strong>Rational</strong>- based on or in accordance with reason or logic.</p>",
                    solution_hi: "<p>12.(c) <strong>Engrossing </strong>(मनोरंजक)- absorbing all attention or interest<br><strong>Monotonous </strong>(नीरस)- lacking in variety and interest.<br><strong>Tiresome </strong>(नीरस)- causing one to feel bored or annoyed.<br><strong>Tedious </strong>(अरोचक/उबाऊ)- too long, slow, or dull.<br><strong>Rational </strong>(तार्किक)- based on or in accordance with reason or logic.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate ANTONYM of the given word.<br>Epitome</p>",
                    question_hi: "<p>13. Select the most appropriate ANTONYM of the given word.<br>Epitome</p>",
                    options_en: ["<p>Exhort</p>", "<p>Expansion</p>", 
                                "<p>Lucid</p>", "<p>Precise</p>"],
                    options_hi: ["<p>Exhort</p>", "<p>Expansion</p>",
                                "<p>Lucid</p>", "<p>Precise</p>"],
                    solution_en: "<p>13.(b) <strong>Expansion</strong>- the action of becoming larger or more extensive.<br><strong>Epitome</strong>- a summary of a written work.<br><strong>Exhort</strong>- to strongly encourage or urge someone to do something.<br><strong>Lucid</strong>- clear and easy to understand.<br><strong>Precise</strong>- exact, accurate, and detailed.</p>",
                    solution_hi: "<p>13.(b) <strong>Expansion </strong>(विस्तार)- the action of becoming larger or more extensive.<br><strong>Epitome </strong>(सारांश)- a summary of a written work.<br><strong>Exhort </strong>(प्रोत्साहित करना)- to strongly encourage or urge someone to do something.<br><strong>Lucid </strong>(स्पष्ट)- clear and easy to understand.<br><strong>Precise </strong>(सटीक)- exact, accurate, and detailed.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>An elephant is a <span style=\"text-decoration: underline;\">large </span>mammal.</p>",
                    question_hi: "<p>14. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>An elephant is a <span style=\"text-decoration: underline;\">large </span>mammal.</p>",
                    options_en: ["<p>Bumper</p>", "<p>Gigantic</p>", 
                                "<p>Tiny</p>", "<p>Heroic</p>"],
                    options_hi: ["<p>Bumper</p>", "<p>Gigantic</p>",
                                "<p>Tiny</p>", "<p>Heroic</p>"],
                    solution_en: "<p>14.(c) <strong>Tiny</strong><br><strong>Bumper</strong>- larger than usual, often used to describe something abundant or plentiful.<br><strong>Gigantic</strong>- extremely large or massive.<br><strong>Heroic</strong>- showing great courage or determination, often in the face of adversity.</p>",
                    solution_hi: "<p>14.(c) <strong>Tiny</strong><br><strong>Bumper </strong>(बहुत बड़ा)- larger than usual, often used to describe something abundant or plentiful.<br><strong>Gigantic </strong>(विशाल)- extremely large or massive.<br><strong>Heroic </strong>(वीरतापूर्ण)- showing great courage or determination, often in the face of adversity.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Select the most appropriate ANTONYM of the word in bold to fill in the blank.<br>The modern work is deliberately ________ (ambiguous).</p>",
                    question_hi: "<p>15. Select the most appropriate ANTONYM of the word in bold to fill in the blank.<br>The modern work is deliberately ________ (ambiguous).</p>",
                    options_en: ["<p>puzzling</p>", "<p>certain</p>", 
                                "<p>dubious</p>", "<p>vague</p>"],
                    options_hi: ["<p>puzzling</p>", "<p>certain</p>",
                                "<p>dubious</p>", "<p>vague</p>"],
                    solution_en: "<p>15.(b) <strong>Certain</strong>- known for sure.<br><strong>Ambiguous</strong>- open to more than one interpretation.<br><strong>Puzzling</strong>- confusing or difficult to understand.<br><strong>Dubious</strong>- hesitating or doubting.<br><strong>Vague</strong>- not clearly expressed or defined.</p>",
                    solution_hi: "<p>15.(b) <strong>Certain </strong>(निश्चित)- known for sure.<br><strong>Ambiguous </strong>(संदिग्धार्थक)- open to more than one interpretation.<br><strong>Puzzling </strong>(पेचीदा)- confusing or difficult to understand.<br><strong>Dubious </strong>(संदिग्ध)- hesitating or doubting.<br><strong>Vague </strong>(अस्पष्ट)- not clearly expressed or defined.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>