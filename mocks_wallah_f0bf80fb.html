<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Name the world famous scientist known for his theory of relativity.</p>",
                    question_hi: "<p>1. उस विश्व प्रसिद्ध वैज्ञानिक का नाम बताइए जिसे उनके सापेक्षता के सिद्धांत के लिए जाना जाता है।</p>",
                    options_en: ["<p>Thomas Alva Edison</p>", "<p>Christian Bernard</p>", 
                                "<p>Albert Einstein</p>", "<p>John Dalton</p>"],
                    options_hi: ["<p>थॉमस अल्वा एडीसन</p>", "<p>क्रिश्चियन बर्नार्ड</p>",
                                "<p>अल्बर्ट आइंस्टीन</p>", "<p>जॉन डाल्टन</p>"],
                    solution_en: "<p>1.(c) Albert Einstein is known for his theory of relativity. Christian Bernard was a South African Cardiac Surgeon who performed the world\'s first human-to-human Heart transplant operation. John Dalton&rsquo;s important contribution was the atomic theory. Thomas Alva Edison\'s invention is the phonograph, the incandescent light bulb.</p>",
                    solution_hi: "<p>1.(c) अल्बर्ट आइंस्टीन को उनके सापेक्षता के सिद्धांत के लिए जाना जाता है। क्रिश्चियन बर्नार्ड एक दक्षिण अफ्रीकी कार्डियक सर्जन थे जिन्होंने दुनिया का पहला मानव-से-मानव हृदय प्रत्यारोपण ऑपरेशन किया था। जॉन डाल्टन का महत्वपूर्ण योगदान परमाणु सिद्धांत था। थॉमस अल्वा एडिसन का आविष्कार फोनोग्राफ, तापदीप्त प्रकाश बल्ब है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which of the following is NOT a part of the Indian Parliament?</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन भारतीय संसद का हिस्सा नहीं है?</p>",
                    options_en: ["<p>President</p>", "<p>Rajya Sabha</p>", 
                                "<p>Lok Sabha</p>", "<p>State Legislative Assemblies</p>"],
                    options_hi: ["<p>राष्ट्रपति</p>", "<p>राज्य सभा</p>",
                                "<p>लोकसभा</p>", "<p>राज्य विधान सभाएं</p>"],
                    solution_en: "<p>2.(d) President , Rajya Sabha, Lok Sabha are the part of the Indian Parliament. The State Legislative Assembly, or Vidhan Sabha, or also Sasana Sabha, is a legislative body in the states and union territories of India.</p>",
                    solution_hi: "<p>2.(d) राष्ट्रपति, राज्य सभा, लोकसभा भारतीय संसद का हिस्सा हैं। राज्य विधान सभा, या विधानसभा, या ससाना सभा, भारत के राज्यों और केंद्र शासित प्रदेशों में एक विधायी निकाय है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Addhaar is a 12-digit unique number that is issued by the ______________.</p>",
                    question_hi: "<p>3. आधार एक 12-अंकीय अद्वितीय संख्या है जो _______ द्वारा जारी की जाती है।</p>",
                    options_en: ["<p>Unique Identification Authority of India</p>", "<p>Unique Indian Digital Authority of India</p>", 
                                "<p>Unique Indian Demographic Association of India</p>", "<p>Unique Identification Association of India</p>"],
                    options_hi: ["<p>भारतीय विशिष्ट पहचान प्राधिकरण</p>", "<p>भारतीय विशिष्ट भारत प्राधिकरण</p>",
                                "<p>भारत का विशिष्ट भारतीय जनसांख्यिकीय संघ</p>", "<p>भारतीय विशिष्ट पहचान संघ</p>"],
                    solution_en: "<p>3.(a) Addhaar is a 12-digit unique number that is issued by the Unique Identification Authority of India. PAN card number is a 10 digit special number. The Passport Reference File Number will consist of 12-digit unique numbers.</p>",
                    solution_hi: "<p>3.(a) आधार एक 12-अंकीय विशिष्ट संख्या है जो भारतीय विशिष्ट पहचान प्राधिकरण द्वारा जारी की जाती है। पैन कार्ड नंबर 10 अंकों का विशेष नंबर होता है। पासपोर्ट संदर्भ फ़ाइल संख्या में 12-अंकीय विशिष्ट संख्याएँ होंगी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The NITI Aayog was formed by replacing the Planning Commission of India on _______2015.</p>",
                    question_hi: "<p>4. नीति आयोग का गठन _______2015 को भारत के योजना आयोग को बदलकर किया गया था।</p>",
                    options_en: ["<p>1st February</p>", "<p>2nd March</p>", 
                                "<p>2nd April</p>", "<p>1st January</p>"],
                    options_hi: ["<p>1 फरवरी</p>", "<p>2 मार्च</p>",
                                "<p>2 अप्रैल</p>", "<p>1 जनवरी</p>"],
                    solution_en: "<p>4.(d) National Institution for Transforming India(NITI Aayog), was formed on 1 January 2015. NITI Aayog is the premier policy think tank of the Government of India, providing directional and policy inputs. Chairperson of Niti Aayog is Shri Narendra Modi.Shri Amitabh Kant is currently the CEO of NITI Aayog.</p>",
                    solution_hi: "<p>4.(d) नेशनल इंस्टीट्यूशन फॉर ट्रांसफॉर्मिंग इंडिया (NITI Aayog) का गठन 1 जनवरी 2015 को किया गया था। NITI Aayog भारत सरकार का प्रमुख नीति थिंक टैंक है, जो दिशात्मक और नीति इनपुट प्रदान करता है। नीति आयोग के अध्यक्ष श्री नरेंद्र मोदी हैं।श्री अमिताभ कांत वर्तमान में नीति आयोग के CEO हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "5. Who was the First Governor General of free India?",
                    question_hi: "5. स्वतंत्र भारत के प्रथम गवर्नर जनरल कौन थे?",
                    options_en: [" Lord Mountbatten ", " Lord Wavel ", 
                                " Lord Irwin ", " Lord Willingdon "],
                    options_hi: [" लॉर्ड माउंटबेटन", " लॉर्ड वेवेल",
                                " लॉर्ड इरविन", " लॉर्ड विलिंगडन"],
                    solution_en: "5.(a) Lord Mountbatten was the First Governor General of free India. Warren Hastings the first and most famous of the British governors-general of India. C. Rajagopalachari became the only Indian governor-general.",
                    solution_hi: "5.(a) लॉर्ड माउंटबेटन स्वतंत्र भारत के पहले गवर्नर जनरल थे। वारेन हेस्टिंग्स भारत के पहले और सबसे प्रसिद्ध ब्रिटिश गवर्नर-जनरल थे। सी. राजगोपालाचारी एकमात्र भारतीय गवर्नर-जनरल बने।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Theophrastus is called the father of:",
                    question_hi: "6. थियोफ्रेस्टस को ________पिता कहा जाता है।",
                    options_en: [" Chemistry ", " Zoology ", 
                                " Botany ", " Physics "],
                    options_hi: [" रसायन शास्त्र", " जंतुविज्ञान",
                                " वनस्पति विज्ञान", " भौतिक विज्ञान"],
                    solution_en: "6.(c) Theophrastus is called the father of Botany. Antoine Lavoisier isFather of chemistry. Aristotle is considered the father of zoology . Galileo Galilei is often referred to as the “father of modern astronomy” and the “father of modern physics”.",
                    solution_hi: "6.(c) थियोफ्रेस्टस को वनस्पति विज्ञान का जनक कहा जाता है। एंटोनी लवॉज़ियर रसायन शास्त्र के जनक हैं। अरस्तू को प्राणीशास्त्र का जनक माना जाता है। गैलीलियो गैलीली को अक्सर \"आधुनिक खगोल विज्ञान के पिता\" और \"आधुनिक भौतिकी के पिता\" के रूप में जाना जाता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. Who provided the basic theory about the nature of matter?",
                    question_hi: "7. पदार्थ की प्रकृति के बारे में मूल सिद्धांत किसने प्रदान किया?",
                    options_en: [" Mendel ", " John Dalton", 
                                " John Milton ", " Lavoisier "],
                    options_hi: [" मेंडेल", " जॉन डाल्टन",
                                " जॉन मिल्टन", " लावोइसियर"],
                    solution_en: "7.(b) John Dalton provided the basic theory about the nature of matter.Gregor Mendel discovered the basic principles of heredity through experiments in his garden(pea).",
                    solution_hi: "7.(b) जॉन डाल्टन ने पदार्थ की प्रकृति के बारे में बुनियादी सिद्धांत प्रदान किया। ग्रेगर मेंडल ने अपने बगीचे (मटर) में प्रयोगों के माध्यम से आनुवंशिकता के मूल सिद्धांतों की खोज की।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. India’s ‘Statue of Unity’ is situated in the state of:",
                    question_hi: "8. भारत की \'स्टैच्यू ऑफ यूनिटी\' किस राज्य में स्थित है?",
                    options_en: [" Tamil Nadu ", " Karnataka ", 
                                " Gujarat ", " Maharashtra "],
                    options_hi: [" तमिलनाडु", " कर्नाटक",
                                " गुजरात", " महाराष्ट्र"],
                    solution_en: "8.(c) India\'s Statue of Unity’ is situated in  Kevadia, Gujarat on the bank of River Narmada.The Statue of Unity is built in dedication to Iron Man Sardar Vallabhbhai Patel, who served as the first home minister of independent India. Architect is Ram V. Sutar. The height of the statue of unity is 182m. ",
                    solution_hi: "8.(c) भारत का ‘स्टैच्यू ऑफ यूनिटी\' नर्मदा नदी के तट पर केवडिया, गुजरात में स्थित है। स्टैच्यू ऑफ यूनिटी को लौह पुरुष सरदार वल्लभभाई पटेल को समर्पित किया गया है, जिन्होंने स्वतंत्र भारत के पहले गृह मंत्री के रूप में कार्य किया था। वास्तुकार राम वी. सुतार हैं। स्टैच्यू ऑफ यूनिटी की ऊंचाई 182 मीटर है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "9. The region where all of the light from the source is blocked is called:",
                    question_hi: "9. वह क्षेत्र जहाँ स्रोत से आने वाला सारा प्रकाश अवरुद्ध हो जाता है____________कहलाता है:",
                    options_en: [" Penumbra ", " Shadow ", 
                                " Antumbra ", " Umbra "],
                    options_hi: [" पेनुम्ब्रा", " प्रतिच्छाया",
                                " अंतम्बरा", " प्रच्छाया"],
                    solution_en: "9.(d) The region where all of the light from the source is blocked is called Umbra.<br />Penumbra is a space of partial illumination between the perfect shadow on all sides and the full light.The antumbra is that part of the Moon\'s shadow that extends beyond the umbra.",
                    solution_hi: "9.(d) वह क्षेत्र जहाँ स्रोत से आने वाला सारा प्रकाश अवरुद्ध हो जाता है, Umbra कहलाता है।<br />पेनम्ब्रा सभी पक्षों पर पूर्ण छाया और पूर्ण प्रकाश के बीच आंशिक रोशनी का स्थान है। अंतम्बरा चंद्रमा की छाया का वह हिस्सा है जो गर्भ से परे फैला हुआ है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "10. Which of the following principles is related to solar power systems? ",
                    question_hi: "10. निम्नलिखित में से कौन सा सिद्धांत सौर ऊर्जा प्रणालियों से संबंधित है?",
                    options_en: [" Photovoltaic effect ", " Photoelectric effect ", 
                                " Photokinetic effect ", " Photosynthesis effect "],
                    options_hi: [" प्रकाशवोल्टीय प्रभाव", " प्रकाश विद्युत प्रभाव",
                                " प्रकाशगतिक प्रभाव", " प्रकाश संश्लेषण प्रभाव"],
                    solution_en: "10.(a) Photovoltaic effect is related to solar power systems.The photoelectric effect is the emission of electrons when electromagnetic radiation, such as light, hits a material.The theory of photokinetic effects expresses the forces and torques exerted by a beam of light.Photosynthesis converts light energy into usable chemical energy for plant growth and development. ",
                    solution_hi: "10.(a) फोटोवोल्टिक प्रभाव सौर ऊर्जा प्रणालियों से संबंधित है। फोटोइलेक्ट्रिक प्रभाव इलेक्ट्रॉनों का उत्सर्जन है जब विद्युत चुम्बकीय विकिरण, जैसे प्रकाश, एक सामग्री को हिट करता है। फोटोकिनेटिक प्रभाव का सिद्धांत प्रकाश की किरण द्वारा लगाए गए बलों और टोक़ को व्यक्त करता है। प्रकाश संश्लेषण प्रकाश ऊर्जा को परिवर्तित करता है पौधों की वृद्धि और विकास के लिए प्रयोग करने योग्य रासायनिक ऊर्जा में।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. When did India’s first biofuel- powered flight land at the Indira Gandhi International Airport in New Delhi from Dehradun?",
                    question_hi: "11. भारत की पहली जैव ईंधन से चलने वाली उड़ान नई दिल्ली के इंदिरा गांधी अंतर्राष्ट्रीय हवाई अड्डे पर देहरादून से कब उतरी?",
                    options_en: [" August , 2018 ", " September, 2018 ", 
                                " July, 2018 ", " June, 2018 "],
                    options_hi: [" अगस्त , 2018", " सितंबर, 2018",
                                " जुलाई, 2018", " जून, 2018"],
                    solution_en: "11.(a) India’s first biofuel- powered flight landed at the Indira Gandhi International Airport in New Delhi from Dehradun in August ,2018. The flight was powered by a blend of 75% air turbine fuel and 25% biojet fuel. Bio-jet-fuel or bio-aviation fuel (BAF) is a biofuel used to power aircraft.",
                    solution_hi: "11.(a) भारत की पहली जैव ईंधन संचालित उड़ान अगस्त, 2018 में देहरादून से नई दिल्ली के इंदिरा गांधी अंतर्राष्ट्रीय हवाई अड्डे पर उतरी। उड़ान 75% एयर टर्बाइन ईंधन और 25% बायोजेट ईंधन के मिश्रण द्वारा संचालित थी। बायो-जेट-फ्यूल या बायो-एविएशन फ्यूल (BAF) एक बायोफ्यूल है जिसका इस्तेमाल एयरक्राफ्ट को पावर देने के लिए किया जाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. Which of the following is the headquarters of the World Health Organisation?",
                    question_hi: "12. निम्नलिखित में कहाँ विश्व स्वास्थ्य संगठन का मुख्यालय है?",
                    options_en: [" Paris", " Vienna ", 
                                " Geneva ", " New York "],
                    options_hi: [" पेरिस", " वियना",
                                " जिनेवा", " न्यूयॉर्क"],
                    solution_en: "12.(c) Geneva, Switzerland is the headquarters of the World Health Organisation. Tedros Adhanom (Director-General) of WHO which was formed on 7 April 1948.",
                    solution_hi: "12.(c) जिनेवा, स्विट्जरलैंड विश्व स्वास्थ्य संगठन का मुख्यालय है। डब्ल्यूएचओ के टेड्रोस एडनॉम (महानिदेशक) जो 7 अप्रैल 1948 को गठित किया गया था।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "13. __________ was Chef de Mission of the Indian contingent at the 2018 Asian Games.",
                    question_hi: "13. __________ 2018 एशियाई खेलों में भारतीय दल का शेफ डी मिशन था",
                    options_en: [" Sushil Kumar ", " Brij Bhushan Saran Singh ", 
                                " Vinayak Singh ", " Pawan Kumar "],
                    options_hi: [" सुशील कुमार", " बृज भूषण सरन सिंह",
                                " विनायक सिंह", " पवन कुमार"],
                    solution_en: "13.(b) Brij Bhushan Saran Singh was Chef de Mission of the Indian contingent at the 2018 Asian Games. The 2018 Asian Games,held in Jakarta, Indonesia. India ranked 8th in Asian Games 2018 with medal tally (Gold- 16, Silver-23, Bronze-31 =70).",
                    solution_hi: "13.(b) बृज भूषण सरन सिंह 2018 एशियाई खेलों में भारतीय दल के शेफ डी मिशन थे। 2018 एशियाई खेल इंडोनेशिया के जकार्ता में आयोजित हुए। भारत एशियाई खेलों 2018 में पदक तालिका (स्वर्ण- 16, रजत-23, कांस्य-31 = 70) के साथ 8वें स्थान पर है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "14. Name the person who was appointed as the first Surveyor General of India in 1815.",
                    question_hi: "14. उस व्यक्ति का नाम बताइए जिसे 1815 में भारत के पहले सर्वेयर जनरल के रूप में नियुक्त किया गया था।",
                    options_en: [" Ottoman ", " Colin Mackenzie", 
                                " Bernier ", " Abul Fazal "],
                    options_hi: [" तुर्क", " कॉलिन मैकेंज़ी",
                                " बर्नियर", " अबुल फजल"],
                    solution_en: "14.(b) Colin Mackenzie was appointed as the first Surveyor General of India in 1815. Present Surveyor General of India (16 January 2021 – present) is Sh Naveen Tomar.",
                    solution_hi: "14.(b) कॉलिन मैकेंज़ी को 1815 में भारत के पहले सर्वेयर जनरल के रूप में नियुक्त किया गया था। भारत के वर्तमान सर्वेयर जनरल (16 जनवरी 2021 - वर्तमान) श्री नवीन तोमर हैं।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. A collection of interrelated files and a set of programs that allow users to access and modify these files is known as .. ",
                    question_hi: "15. परस्पर संबंधित फाइलों का एक संग्रह और प्रोग्राम का एक सेट जो उपयोगकर्ताओं को इन फाइलों तक पहुंचने और संशोधित करने की अनुमति देता है, _________के रूप में जाना जाता है।",
                    options_en: [" data analytic system ", " Database management systems ", 
                                " data files ", " System management "],
                    options_hi: [" डेटा विश्लेषणात्मक प्रणाली", " डेटाबेस प्रबंधन प्रणाली",
                                " डाटा फाइल", " सिस्टम प्रबंधन"],
                    solution_en: "15.(b) A collection of interrelated files and a set of programs that allow users to access and modify these files is known as Database management systems. In 1960, Charles W. Bachman designed the Integrated Database System, the “first” DBMS.",
                    solution_hi: "15.(b) परस्पर संबंधित फ़ाइलों का एक संग्रह और प्रोग्रामों का एक सेट जो उपयोगकर्ताओं को इन फ़ाइलों तक पहुंचने और संशोधित करने की अनुमति देता है, को डेटाबेस प्रबंधन सिस्टम के रूप में जाना जाता है। 1 9 60 में, चार्ल्स डब्ल्यू बाचमैन ने एकीकृत डेटाबेस सिस्टम, \"पहला\" DBMS डिजाइन किया।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "16. The ‘Ganga Gram Project’ is launched by the ..",
                    question_hi: "16. \'गंगा ग्राम परियोजना\' किसके द्वारा शुरू की गई है?",
                    options_en: [" Ministry of Youth Affairs and Sports ", " Ministry of Human Resources Development ", 
                                " Ministry of Rural Development ", " Ministry of Drinking Water and Sanitation "],
                    options_hi: [" युवा मामले और खेल मंत्रालय", " मानव संसाधन विकास मंत्रालय",
                                " ग्रामीण विकास मंत्रालय", " पेयजल और स्वच्छता मंत्रालय "],
                    solution_en: "16.(d) The Ministry of Drinking Water and Sanitation (MDWS) launched \'Ganga Gram\' - a project for sanitation based integrated development of all 4470 villages along the River Ganga in 2017. Present(2021) Minister of Jal Shakti is Shri Gajendra Singh Shekhawat.",
                    solution_hi: "16.(d) पेयजल और स्वच्छता मंत्रालय (एमडीडब्ल्यूएस) ने 2017 में गंगा नदी के किनारे सभी 4470 गांवों के स्वच्छता आधारित एकीकृत विकास के लिए एक परियोजना \'गंगा ग्राम\' शुरू की। वर्तमान (2021) जल शक्ति मंत्री श्री गजेंद्र सिंह शेखावत हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. How many times has India entered the ICC Men’s Cricket World Cup (ODI) finals?",
                    question_hi: "17. भारत ने कितनी बार ICC पुरुष क्रिकेट विश्व कप (ODI) के फाइनल में प्रवेश किया है?",
                    options_en: [" Two  ", " Four ", 
                                " One ", " Three "],
                    options_hi: [" दो", " चार",
                                " एक ", " तीन "],
                    solution_en: "17.(d) Three times India has entered the ICC Men’s Cricket World Cup (ODI) finals. In 1983 and 2011 India won The World Cup Title and in 2003 India was runner up.",
                    solution_hi: "17.(d) भारत ने तीन बार ICC मेन्स क्रिकेट वर्ल्ड कप (ODI) के फाइनल में प्रवेश किया है। 1983 और 2011 में भारत ने विश्व कप का खिताब जीता और 2003 में भारत उपविजेता रहा।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. Who built the Shahi (Royal) road to strengthen and consolidate his empire from the Indus valley to the Sonar valley in Bengal, and it was renamed the GT road during the British period?",
                    question_hi: "18. सिंधु घाटी से बंगाल में सोनार घाटी तक अपने साम्राज्य को मजबूत और संघटित करने के लिए शाही (रॉयल) सड़क का निर्माण किसने किया था, और ब्रिटिश काल के दौरान इसका नाम बदलकर जीटी रोड कर दिया गया था?",
                    options_en: [" Sher Shah Suri", " Bahadur Shah Zafar", 
                                " Alam Shah", " Aurangzeb "],
                    options_hi: [" शेर शाह सूरी", " बहादुर शाह जफर",
                                " आलम शाह", " औरंगजेब"],
                    solution_en: "18.(a) Sher Shah Suri built the the GT road during the British period.Bahadur Shah Zafar was the twentieth and last Mughal Emperor of India.Alam Shah was the fourth and last ruler of the Sayyid dynasty which ruled the Delhi Sultanate.Aurangzeb is known for being the emperor of India from 1658 to 1707. He was the last of the great Mughal emperors.",
                    solution_hi: "18.(a) शेर शाह सूरी ने ब्रिटिश काल के दौरान जीटी रोड का निर्माण किया था। बहादुर शाह जफर भारत के बीसवें और अंतिम मुगल सम्राट थे। आलम शाह सैय्यद वंश के चौथे और अंतिम शासक थे जिन्होंने दिल्ली सल्तनत पर शासन किया था। औरंगजेब को इसके लिए जाना जाता है 1658 से 1707 तक भारत के सम्राट। वह महान मुगल सम्राटों में से अंतिम थे।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "19. Which of the following banks was established in India at Calcutta under European Management (in 1770)?",
                    question_hi: "19. निम्नलिखित में से कौन सा बैंक भारत में यूरोपीय प्रबंधन (1770 में) के तहत कलकत्ता में स्थापित किया गया था?",
                    options_en: [" Bank of India ", " Indus Bank of India ", 
                                " National Bank of India ", " Bank of Hindustan "],
                    options_hi: [" बैंक ऑफ इंडिया", " इंडस बैंक ऑफ इंडिया",
                                " नेशनल बैंक ऑफ इंडिया", " बैंक ऑफ हिंदुस्तान"],
                    solution_en: "19.(d) Bank of Hindustan Was established in India at Calcutta under European Management in 1770.This bank was established at Calcutta under European management. It was liquidated in 1830-32.",
                    solution_hi: "19.(d) बैंक ऑफ हिंदुस्तान की स्थापना भारत में 1770 में यूरोपीय प्रबंधन के तहत कलकत्ता में हुई थी। यह बैंक यूरोपीय प्रबंधन के तहत कलकत्ता में स्थापित किया गया था। इसे 1830-32 में समाप्त कर दिया गया था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "20. United Nations Day is celebrated every year on:",
                    question_hi: "20. संयुक्त राष्ट्र दिवस प्रतिवर्ष कब मनाया जाता है ?",
                    options_en: [" 30th October  ", " 26th June ", 
                                " 24th October ", " 4th November "],
                    options_hi: [" 30 अक्टूबर ", " 26 जून",
                                " 24 अक्टूबर", " 4 नवंबर"],
                    solution_en: "20.(c) The United Nations Day is observed on October 24 and marks the anniversary of the entry into force in 1945 of the UN Charter. ",
                    solution_hi: "20.(c) संयुक्त राष्ट्र दिवस 24 अक्टूबर को मनाया जाता है और संयुक्त राष्ट्र चार्टर के 1945 में लागू होने की वर्षगांठ का प्रतीक है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. The Swaraj Party was formed in the year:",
                    question_hi: "21. स्वराज पार्टी का गठन वर्ष में किया गया था ?",
                    options_en: [" 1923", " 1930", 
                                " 1933", " 1921"],
                    options_hi: [" 1923", " 1930",
                                " 1933", " 1921"],
                    solution_en: "21.(a) The Swaraj Party was formed in the year 1923. Swaraj Party was founded by Moti Lal Nehru and Chitranjan Das. It was established as the Congress-Khilafat Swaraj Party.",
                    solution_hi: "21.(a) स्वराज पार्टी का गठन वर्ष 1923 में हुआ था। स्वराज पार्टी की स्थापना मोती लाल नेहरू और चितरंजन दास ने की थी। इसे कांग्रेस-खिलाफत स्वराज पार्टी के रूप में स्थापित किया गया था।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. Megalopolis, a Greek word meaning “great city”, was popularised by __________ (1957).",
                    question_hi: "22. मेगालोपोलिस, एक ग्रीक शब्द जिसका अर्थ \"महान शहर\" है, को __________ (1957) द्वारा लोकप्रिय बनाया गया था।",
                    options_en: [" Patrick ", " Griffin ", 
                                " Lewis Mumford ", " Jean Gottman "],
                    options_hi: [" पैट्रिक ", " ग्रिफिन",
                                " लुईस ममफोर्ड", " जीन गॉटमैन"],
                    solution_en: "22.(c) Megalopolis, a Greek word meaning “great city”, was popularised by Jean Gottman. His main contributions to human geography were in the sub-fields of urban, political, economic, historical and regional geography",
                    solution_hi: "22.(c) मेगालोपोलिस, एक ग्रीक शब्द जिसका अर्थ है \"महान शहर\", जीन गॉटमैन द्वारा लोकप्रिय किया गया था। मानव भूगोल में उनका मुख्य योगदान शहरी, राजनीतिक, आर्थिक, ऐतिहासिक और क्षेत्रीय भूगोल के उप-क्षेत्रों में था",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "23. The C++ programming language was developed at AT & T Bell Laboratories in the early __________ by Bjame Stroustrup.",
                    question_hi: "23. C++ प्रोग्रामिंग भाषा को ब्जेम स्ट्रूस्ट्रप द्वारा __________ की शुरुआत में AT & T बेल लेबोरेटरीज में विकसित किया गया था।",
                    options_en: [" 1960s", " 1970s", 
                                " 1980s", " 1990s"],
                    options_hi: [" 1960s", " 1970s",
                                " 1980s", " 1990s"],
                    solution_en: "23.(c) The C++ programming language was developed at AT & T Bell Laboratories in the early 1980s  by Bjarne Stroustrup. Dennis Ritchie, the inventor of C programming language and co-developer of Unix. James Gosling, creator of the Java computer language.",
                    solution_hi: "23.(c) C++  प्रोग्रामिंग भाषा एटी एंड टी बेल प्रयोगशालाओं में 1980 के दशक की शुरुआत में बज्ने स्ट्राउस्ट्रुप द्वारा विकसित की गई थी ।  डेनिस रिची, C प्रोग्रामिंग भाषा के आविष्कारक और यूनिक्स के सह-डेवलपर। जेम्स गोसलिंग, जावा कंप्यूटर भाषा के निर्माता।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "24. Who was the young economist involved in drafting the plan of the First Five Year Plan (1951-1956)?",
                    question_hi: "24. प्रथम पंचवर्षीय योजना (1951-1956) की योजना का मसौदा तैयार करने में शामिल युवा अर्थशास्त्री कौन थे?",
                    options_en: [" Swaran Singh ", " PC Mahalanobis ", 
                                " KN Raj ", " BV Keskar "],
                    options_hi: [" स्वर्ण सिंह", " पीसी महालनोबिस",
                                " केएन राज", " बी.वी. केसकर"],
                    solution_en: "24.(c) KN Raj was the young economist involved in drafting the plan of the First Five Year Plan (1951-1956). PC Mahalanobis who devised the Mahalanobis distance and was instrumental in formulating India\'s strategy for industrialization in the Second Five-Year Plan (1956–61).Sardar Swaran Singh was India\'s longest-serving union cabinet minister.",
                    solution_hi: "24.(c) केएन राज प्रथम पंचवर्षीय योजना (1951-1956) की योजना तैयार करने में शामिल युवा अर्थशास्त्री थे। पीसी महलानोबिस जिन्होंने महालनोबिस दूरी तैयार की और दूसरी पंचवर्षीय योजना (1956-61) में औद्योगीकरण के लिए भारत की रणनीति तैयार करने में महत्वपूर्ण भूमिका निभाई। सरदार स्वर्ण सिंह भारत के सबसे लंबे समय तक सेवा करने वाले केंद्रीय कैबिनेट मंत्री थे।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. In 1798, who became the Governor General and built a massive palace, Government House, for himself in Calcutta?",
                    question_hi: "25. 1798 में, गवर्नर जनरल कौन बना और कलकत्ता में अपने लिए एक विशाल महल, गवर्नमेंट हाउस का निर्माण किया?",
                    options_en: [" Lord Cornwallis ", " Lord Wellesley ", 
                                " Lord Bentinck ", " Lord Clive "],
                    options_hi: [" लॉर्ड कार्नवालिस", " लॉर्ड वैलेस्ली",
                                " लॉर्ड बेंटिक", " लॉर्ड क्लाइव"],
                    solution_en: "25.(b) In 1798, Lord Wellesley became the Governor General and built a massive palace, Government House, for himself in Calcutta. The Subsidiary Alliance System was a “Non-Intervention Policy” used by Lord Wellesley .",
                    solution_hi: "25.(b) 1798 में, लॉर्ड वेलेस्ली गवर्नर जनरल बने और कलकत्ता में अपने लिए एक विशाल महल, गवर्नमेंट हाउस का निर्माण किया। सब्सिडियरी एलायंस सिस्टम लॉर्ड वेलेस्ली द्वारा इस्तेमाल की जाने वाली एक \"गैर-हस्तक्षेप नीति\" थी।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Which of the following correctly depicts Indian Standard Time?",
                    question_hi: "26. निम्नलिखित में से कौन भारतीय मानक समय को सही ढंग से दर्शाता है?",
                    options_en: [" GMT - 4.30", " GMT + 4.30", 
                                " GMT - 5.30", " GMT + 5.30"],
                    options_hi: [" GMT - 4.30", " GMT + 4.30",
                                " GMT - 5.30", " GMT + 5.30"],
                    solution_en: "26.(d) GMT + 5.30 depicts Indian Standard Time.Indian Standard Time calculates on the basis of 82.5° E longitude, just west of the town of Mirzapur, near Allahabad in the state of Uttar Pradesh.",
                    solution_hi: "26.(d) GMT + 5.30 भारतीय मानक समय को दर्शाता है। भारतीय मानक समय की गणना उत्तर प्रदेश राज्य में इलाहाबाद के पास मिर्जापुर शहर के पश्चिम में 82.5 ° E देशांतर के आधार पर की जाती है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. The Reserve Bank of India was established on 1 April __________ with a share capital of Rs. 5 crore. ",
                    question_hi: "27. भारतीय रिजर्व बैंक की स्थापना 1 अप्रैल __________ को 5 करोड़ रुपये की शेयर पूंजी के साथ की गई थी।",
                    options_en: [" 1940", " 1930", 
                                " 1945", " 1935"],
                    options_hi: [" 1940", " 1930",
                                " 1945", " 1935"],
                    solution_en: "27.(d) The Reserve Bank of India was established (Hilton Young Commission)on 1 April 1935 with a share capital of Rs. 5 crore. RBI nationalised on 1st January 1949. Sir Osborne Smith was first Governor of RBI. Present(2021) governor of RBI is Shri Shaktikanta Das.",
                    solution_hi: "27.(d) भारतीय रिजर्व बैंक की स्थापना (हिल्टन यंग कमीशन) 1 अप्रैल 1935 को रुपये की शेयर पूंजी के साथ की गई थी। 5 करोड़। 1 जनवरी 1949 को आरबीआई का राष्ट्रीयकरण किया गया। सर ओसबोर्न स्मिथ आरबीआई के पहले गवर्नर थे। आरबीआई के वर्तमान (2021) गवर्नर श्री शक्तिकांत दास हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. In which year did India conduct its second nuclear test in Pokhran?",
                    question_hi: "28. भारत ने पोखरण में अपना दूसरा परमाणु परीक्षण किस वर्ष किया था?",
                    options_en: [" 1996", " 2003", 
                                " 2001", " 1998"],
                    options_hi: [" 1996", " 2003",
                                " 2001", " 1998"],
                    solution_en: "28.(d) In year1998 India conducted its second nuclear test in Pokhran with code Operation Shakti.The first test, code-named Smiling Buddha, was conducted in May 1974.",
                    solution_hi: "28.(d) वर्ष 1998 में भारत ने कोड ऑपरेशन शक्ति के साथ पोखरण में अपना दूसरा परमाणु परीक्षण किया। पहला परीक्षण, कोड-नाम स्माइलिंग बुद्धा, मई 1974 में आयोजित किया गया था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "29. Under whose presidency did the Indian National Congress declared Poorna Swaraj as its ultimate goal on December 19, 1929 at Lahore?",
                    question_hi: "29. 19 दिसंबर, 1929 को लाहौर में भारतीय राष्ट्रीय कांग्रेस ने किसकी अध्यक्षता में पूर्ण स्वराज को अपना अंतिम लक्ष्य घोषित किया था?",
                    options_en: [" Subhash Chandra Bose ", " VD Savarkar ", 
                                " Jawaharlal Nehru ", " Mahatma Gandhi "],
                    options_hi: [" सुभाष चंद्र बोस", " वीडी सावरकर",
                                " जवाहर लाल नेहरू", " महात्मा गांधी"],
                    solution_en: "29.(c) The Indian National Congress declared Poorna Swaraj as its ultimate goal on December 19, 1929 at Lahore under the presidency of Jawahar Lal Nehru.",
                    solution_hi: "29.(c) भारतीय राष्ट्रीय कांग्रेस ने जवाहर लाल नेहरू की अध्यक्षता में लाहौर में 19 दिसंबर, 1929 को पूर्ण स्वराज को अपना अंतिम लक्ष्य घोषित किया।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. Name the first Indian Woman to be appointed to the International Cricket Council (ICC) as a match referee.",
                    question_hi: "30. मैच रेफरी के रूप में अंतर्राष्ट्रीय क्रिकेट परिषद (ICC) में नियुक्त होने वाली पहली भारतीय महिला का नाम बताइए।",
                    options_en: [" GS Lakshmi ", " Mithali Raj", 
                                " Harpreet Kaur ", " Jhulan Nishit Goswami "],
                    options_hi: [" जी.एस. लक्ष्मी", " मिताली राज",
                                " हरप्रीत कौर", " झूलन निशित गोस्वामी"],
                    solution_en: "30.(a) GS Lakshmi,Named the first Indian Woman to be appointed to the International Cricket Council (ICC) as a match referee. Mithali Raj became the leading run-getter in women\'s international cricket, going past Charlotte Edwards\' tally of 10,273 runs(July 2021).",
                    solution_hi: "30.(a) GS लक्ष्मी, अंतरराष्ट्रीय क्रिकेट परिषद (आईसीसी) में मैच रेफरी के रूप में नियुक्त होने वाली पहली भारतीय महिला बनीं। मिताली राज शार्लोट एडवर्ड्स के 10,273 रनों (जुलाई 2021) को पीछे छोड़ते हुए महिला अंतरराष्ट्रीय क्रिकेट में सबसे अधिक रन बनाने वाली खिलाड़ी बन गईं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "31. Vikram Sarabhai Space Centre is located at.",
                    question_hi: "31. विक्रम साराभाई अंतरिक्ष केंद्र कहाँ स्थित है?",
                    options_en: [" Bengaluru ", " Thiruvananthapuram ", 
                                " Hyderabad ", " Mumbai "],
                    options_hi: [" बेंगलुरु", " तिरुवनंतपुरम",
                                " हैदराबाद", " मुंबई"],
                    solution_en: "31.(b) Vikram Sarabhai Space Centre is located at Thiruvananthapuram. Dr Vikram Sarabhai is widely known as the \'father of the Indian space programme’ . He helped establish the Indian space agency, the Indian Space Research Organisation (ISRO), and led it as chairman.",
                    solution_hi: "31.(b) विक्रम साराभाई अंतरिक्ष केंद्र तिरुवनंतपुरम में स्थित है। विक्रम साराभाई को व्यापक रूप से \'भारतीय अंतरिक्ष कार्यक्रम के जनक\' के रूप में जाना जाता है। उन्होंने भारतीय अंतरिक्ष एजेंसी, भारतीय अंतरिक्ष अनुसंधान संगठन (ISRO) की स्थापना में मदद की और अध्यक्ष के रूप में इसका नेतृत्व किया।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. The ________ is a simple query language used for accessing, handling and managing data in a relational database.",
                    question_hi: "32. ________ एक साधारण क्वेरी भाषा है जिसका उपयोग रिलेशनल डेटाबेस में डेटा तक पहुँचने, संभालने और प्रबंधित करने के लिए किया जाता है।",
                    options_en: [" DML", " ISO", 
                                " SQL", " DDL"],
                    options_hi: [" DML", " ISO",
                                " SQL", " DDL"],
                    solution_en: "32.(c) The Structured Query Language(SQL) is a simple query language used for accessing, handling and managing data in a relational database.A data manipulation language (DML) is a computer programming language used for adding, deleting, and modifying data in a database.ISO (International Organization for Standardization) is a worldwide federation of national standards bodies.Data Definition Language (DDL) is a standard for commands that define the different structures in a database.",
                    solution_hi: "32.(c) संरचित क्वेरी भाषा (SQL) एक रिलेशनल डेटाबेस में डेटा तक पहुंचने, संभालने और प्रबंधित करने के लिए उपयोग की जाने वाली एक साधारण क्वेरी भाषा है। डेटा मैनिपुलेशन भाषा (डीएमएल) एक कंप्यूटर प्रोग्रामिंग भाषा है जिसका उपयोग डेटाबेस में डेटा जोड़ने, हटाने और संशोधित करने के लिए किया जाता है। आईएसओ (मानकीकरण के लिए अंतर्राष्ट्रीय संगठन) राष्ट्रीय मानक निकायों का एक विश्वव्यापी संघ है। डेटा डेफिनिशन लैंग्वेज (DDL) कमांड के लिए एक मानक है जो डेटाबेस में विभिन्न संरचनाओं को परिभाषित करता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "33. Which of the following cities is known as ‘The city of Palaces’ in India’?",
                    question_hi: "33. निम्नलिखित में से कौन सा शहर भारत में महलों का शहर\' के रूप में जाना जाता है?",
                    options_en: [" Jaipur ", " Lucknow ", 
                                " Kolkata ", " Patna "],
                    options_hi: [" जयपुर", " लखनऊ",
                                " कोलकाता", " पटना"],
                    solution_en: "33.(c) Kolkata is called The city of Palaces in India. Jaipur is called the Pink City of India.",
                    solution_hi: "33.(c) कोलकाता को भारत में महलों का शहर कहा जाता है। जयपुर को भारत का गुलाबी शहर कहा जाता है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. In which of the following continents are the 6 most populated countries located?</p>",
                    question_hi: "<p>34. निम्नलिखित में से किस महाद्वीप में 6 सबसे अधिक आबादी वाले देश स्थित हैं?</p>",
                    options_en: ["<p>South America</p>", "<p>Africa</p>", 
                                "<p>North America</p>", "<p>Asia</p>"],
                    options_hi: ["<p>दक्षिण अमेरिका</p>", "<p>अफ्रीका</p>",
                                "<p>उत्तरी अमेरिका</p>", "<p>एशिया</p>"],
                    solution_en: "<p>34.(d)&nbsp; The 6 most populated countries located In Asia(China,India,Indonesia,Pakistan,Bangladesh,Japan). Uttar Pradesh is the most populated state in India. Sikkim is the least populated state of India.</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>34.(d) एशिया (चीन, भारत, इंडोनेशिया, पाकिस्तान, बांग्लादेश, जापान) में स्थित 6 सबसे अधिक आबादी वाले देश। उत्तर प्रदेश भारत में सबसे अधिक आबादी वाला राज्य है। सिक्किम भारत का सबसे कम आबादी वाला राज्य है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. Name the place in India where one can find the Zero Mile Stone consisting of four horses and a pillar made up of sandstone. ",
                    question_hi: "35. भारत में उस स्थान का नाम बताइए जहाँ चार घोड़ों से युक्त जीरो माइल स्टोन और बलुआ पत्थर से बना एक स्तंभ पाया गया है।",
                    options_en: [" Surat ", " Bhopal", 
                                " Nagpur ", " Itarsi"],
                    options_hi: [" सूरत", " भोपाल",
                                " नागपुर", " इटारसी"],
                    solution_en: "35.(c) In India Nagpur is the place where one can find the Zero Mile Stone consisting of four horses and a pillar made up of sandstone. ",
                    solution_hi: "35.(c) भारत में नागपुर वह स्थान है जहाँ शून्य मील का पत्थर पाया जा सकता है जिसमें चार घोड़े और बलुआ पत्थर से बना एक स्तंभ है।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "36. Biogas is an excellent fuel as it contains up to 75% ____________.",
                    question_hi: "36. बायोगैस एक उत्कृष्ट ईंधन है क्योंकि इसमें 75% तक _________ होती है।",
                    options_en: [" methane ", " Oxygen ", 
                                " Sulphide  ", " Hydrogen "],
                    options_hi: [" मीथेन", " ऑक्सीजन",
                                " सल्फाइड", " हाइड्रोजन"],
                    solution_en: "<p>36.(a) Biogas is an excellent fuel as it contains up to 75% Methane(CH₄).Methane (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>4</mn></msub></math>) is one of the three main greenhouse gases, along with carbon dioxide (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CO</mi><mn>2</mn></msub></math>) and nitrous oxide (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math>).</p>",
                    solution_hi: "<p>36.(a) बायोगैस एक उत्कृष्ट ईंधन है क्योंकि इसमें 75% तक मीथेन (CH₄) होता है। मीथेन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>4</mn></msub></math>) कार्बन डाइऑक्साइड (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CO</mi><mn>2</mn></msub></math>) और नाइट्रस ऑक्साइड (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi></math>) के साथ तीन मुख्य ग्रीनहाउस गैसों में से एक है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "37. In which year did the voting age for the Lok Sabha and the State Legislative Assembly elections change from 21 to 18 year?",
                    question_hi: "37. किस वर्ष लोकसभा और राज्य विधानसभा चुनावों के लिए मतदान की आयु 21 से 18 वर्ष में बदल गई थी ?",
                    options_en: [" 1998", " 1988", 
                                " 2001", " 1991"],
                    options_hi: [" 1998", " 1988",
                                " 2001", " 1991"],
                    solution_en: "37.(b) In 1988  the voting age for the Lok Sabha and the State Legislative Assembly elections changed from 21 to 18 years(The Sixty-first Amendment of the Constitution of India).",
                    solution_hi: "37.(b) 1988 में लोकसभा और राज्य विधान सभा चुनावों के लिए मतदान की आयु 21 से बदलकर 18 वर्ष कर दी गई (भारत के संविधान का इकसठवां संशोधन)।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Famous singer and Bharat Ratna awardee, Bhupen Hazarika, belongs to the state of..",
                    question_hi: "38. प्रसिद्ध गायक और भारत रत्न से सम्मानित भूपेन हजारिका किस राज्य से संबंधित हैं?",
                    options_en: [" Assam  ", " Nagaland ", 
                                " Odisha ", " West Bengal"],
                    options_hi: [" असम", " नगालैंड",
                                " उड़ीसा", " पश्चिम बंगाल"],
                    solution_en: "38.(a) Bhupen Hazarika, belongs to the state of Assam. He won Bharat Ratna in 2019.",
                    solution_hi: "38.(a) भूपेन हजारिका, असम राज्य से संबंधित हैं। उन्होंने 2019 में भारत रत्न जीता।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "39. Who built the Buland Darwaza, which is located in Fatehpur Sikri?",
                    question_hi: "39. फतेहपुर सीकरी में स्थित बुलंद दरवाजा किसने बनवाया था?",
                    options_en: [" Akbar ", " Noor Jahan  ", 
                                " Babar ", " Shah Jahan <br />."],
                    options_hi: [" अकबर", " नूरजहाँ",
                                " बाबर", " शाहजहाँ"],
                    solution_en: "39.(a) Buland Darwaza or the \"Door of victory\", was built in 1575 A.D. by Mughal emperor Akbar to commemorate his victory over Gujarat.",
                    solution_hi: "39.(a) बुलंद दरवाजा या \"जीत का द्वार\", 1575 ई. में मुगल सम्राट अकबर द्वारा गुजरात पर अपनी जीत की स्मृति में बनाया गया था।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "40. Who described the kingdom of Awadh as ’a cherry that will drop into our mouth one day’?",
                    question_hi: "40. अवध के राज्य को किसके द्वारा \'एक चेरी जो एक दिन हमारे मुंह में गिर जाएगी\' के रूप में वर्णित किया है?",
                    options_en: [" Lord Dalhousie ", " Lord Wellesley ", 
                                " Lord Curzon ", " Warren Hastings "],
                    options_hi: [" लॉर्ड डलहौजी", " लॉर्ड वैलेस्ली",
                                " लॉर्ड कर्जन", " वारेन हेस्टिंग्स"],
                    solution_en: "40.(a) Lord Dalhousie described the kingdom of Awadh as ’a cherry that will drop into our mouth one day’ because the soil of Awadh was good for producing indigo and cotton and  the British extended its control over this territory and annexed it in 1856.",
                    solution_hi: "40.(a) लॉर्ड डलहौजी ने अवध के राज्य को \'एक चेरी जो एक दिन हमारे मुंह में गिर जाएगी\' के रूप में वर्णित किया क्योंकि अवध की मिट्टी नील और कपास के उत्पादन के लिए अच्छी थी और अंग्रेजों ने इस क्षेत्र पर अपना नियंत्रण बढ़ाया और 1856 में इसे कब्जा कर लिया।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>