<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that represents the letters that when sequentially placed from left to right in the blanks below will complete the letter series.<br>_ X _ F _ _ _ X T _ X _</p>",
                    question_hi: "<p>1. उस विकल्प का चयन कीजिए जो उन अक्षरों को प्रदर्शित करता है जिन्हें नीचे दिए गए रिक्&zwj;त स्&zwj;थानों में बाएं से दाएं क्रमिक रूप से रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी।<br>_ X _ F _ _ _ X T _ X _</p>",
                    options_en: [
                        "<p>FXXTFXT</p>",
                        "<p>FTXTFFT</p>",
                        "<p>XFTTXFX</p>",
                        "<p>XTTFXFX</p>"
                    ],
                    options_hi: [
                        "<p>FXXTFXT</p>",
                        "<p>FTXTFFT</p>",
                        "<p>XFTTXFX</p>",
                        "<p>XTTFXFX</p>"
                    ],
                    solution_en: "<p>1.(b) <span style=\"text-decoration: underline;\"><strong>F</strong></span> X<span style=\"text-decoration: underline;\"><strong>T</strong></span> / F<span style=\"text-decoration: underline;\"><strong>X</strong> <strong>T</strong></span> / <span style=\"text-decoration: underline;\"><strong>F</strong></span> X T / <span style=\"text-decoration: underline;\"><strong>F</strong></span> X <span style=\"text-decoration: underline;\"><strong>T</strong></span></p>",
                    solution_hi: "<p>1.(b) <span style=\"text-decoration: underline;\"><strong>F</strong></span> X<span style=\"text-decoration: underline;\"><strong>T</strong></span> / F<span style=\"text-decoration: underline;\"><strong>X</strong> <strong>T</strong></span> / <span style=\"text-decoration: underline;\"><strong>F</strong></span> X T / <span style=\"text-decoration: underline;\"><strong>F</strong></span> X <span style=\"text-decoration: underline;\"><strong>T</strong></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In the given number-pairs, the second number is obtained by applying certain mathematical operations to the first number. Select the number-pair in which the numbers are related in the same way as are the numbers of the given number-pairs.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g., 13 - Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(45, 88)<br>(26, 50)</p>",
                    question_hi: "<p>2. दिए गए संख्या-युग्मों में, पहली संख्या पर कुछ गणितीय संक्रियाओं को लागू करके दूसरी संख्या प्राप्त की जाती है। ऐसे संख्या-युग्म का चयन कीजिए, जिसमें संख्याएँ ठीक उसी प्रकार संबंधित हैं, जैसे दिए गए संख्या-युग्मों की संख्याएँ संबंधित हैं।<br>(<strong>नोट : </strong>संख्याओं को उनके घटक अंकों में तोड़े बिना संक्रियाएँ पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए, 13 को लीजिए - 13 पर संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ना तथा फिर 1 और 3 पर गणितीय संक्रियाएँ करना अनुमन्य नहीं है।)<br>(45, 88)<br>(26, 50)</p>",
                    options_en: [
                        "<p>(18, 36)</p>",
                        "<p>(15, 32)</p>",
                        "<p>(22, 42)</p>",
                        "<p>(21, 41)</p>"
                    ],
                    options_hi: [
                        "<p>(18,36)</p>",
                        "<p>(15, 32)</p>",
                        "<p>(22, 42)</p>",
                        "<p>(21,41)</p>"
                    ],
                    solution_en: "<p>2.(c) <strong>Logic :-</strong> (1st number &times; 2) - 2 = 2nd number<br>(45, 88) :- (45 &times; 2) - 2 = 88<br>(26, 50) :- (26 &times; 2) - 2 = 50<br>Similarly,<br>(22, 42) :- (22 &times; 2) - 2 = 42</p>",
                    solution_hi: "<p>2.(c) <strong>तर्क:-</strong> (पहली संख्या &times; 2) - 2 = दूसरी संख्या<br>(45, 88) :- (45 &times; 2) - 2 = 88<br>(26, 50) :- (26 &times; 2) - 2 = 50<br>इसी प्रकार,<br>(22, 42) :- (22 &times; 2) - 2 = 42</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. A paper is folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253083512.png\" alt=\"rId4\" width=\"259\" height=\"91\"></p>",
                    question_hi: "<p>3. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253083512.png\" alt=\"rId4\" width=\"259\" height=\"91\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253083700.png\" alt=\"rId5\" width=\"84\" height=\"83\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253083816.png\" alt=\"rId6\" width=\"86\" height=\"91\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253083987.png\" alt=\"rId7\" width=\"85\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253084143.png\" alt=\"rId8\" width=\"91\" height=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253083700.png\" alt=\"rId5\" width=\"85\" height=\"84\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253083816.png\" alt=\"rId6\" width=\"84\" height=\"89\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253083987.png\" alt=\"rId7\" width=\"86\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253084143.png\" alt=\"rId8\" width=\"88\" height=\"87\"></p>"
                    ],
                    solution_en: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253083816.png\" alt=\"rId6\" width=\"85\" height=\"90\"></p>",
                    solution_hi: "<p>3.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253083816.png\" alt=\"rId6\" width=\"85\" height=\"90\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Based on the alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group ?<br>(<strong>Note: </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>4. अंग्रेजी वर्णमाला क्रमानुसार, निम्नलिखित चार अक्षर-समूहों में से तीन एक निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा अक्षर-समूह उस समूह से संबंधित नहीं है ?<br>(<strong>नोट: </strong>असंगत अक्षर-समूह, उस अक्षर-समूह मेें व्यंजन/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>EGJ</p>",
                        "<p>PRV</p>",
                        "<p>GIM</p>",
                        "<p>HJN</p>"
                    ],
                    options_hi: [
                        "<p>EGJ</p>",
                        "<p>PRV</p>",
                        "<p>GIM</p>",
                        "<p>HJN</p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253084371.png\" alt=\"rId9\" width=\"113\" height=\"66\">&nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253084572.png\" alt=\"rId10\" width=\"113\" height=\"65\">&nbsp; &nbsp; ,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253084900.png\" alt=\"rId11\" width=\"110\" height=\"64\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085239.png\" alt=\"rId12\" width=\"109\" height=\"64\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253084371.png\" alt=\"rId9\" width=\"113\" height=\"66\">&nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253084572.png\" alt=\"rId10\" width=\"113\" height=\"65\">&nbsp; &nbsp; ,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253084900.png\" alt=\"rId11\" width=\"110\" height=\"64\"><br><strong><strong id=\"docs-internal-guid-5aed4221-7fff-4c6c-a39d-5540c51e28f6\">लेकिन</strong></strong>&nbsp;,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085239.png\" alt=\"rId12\" width=\"109\" height=\"64\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085389.png\" alt=\"rId13\" width=\"106\" height=\"130\"></p>",
                    question_hi: "<p>5. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085389.png\" alt=\"rId13\" width=\"106\" height=\"130\"></p>",
                    options_en: [
                        " 14 ",
                        " 12",
                        " 16",
                        " 15"
                    ],
                    options_hi: [
                        " 14 ",
                        " 12",
                        " 16",
                        " 15"
                    ],
                    solution_en: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085477.png\" alt=\"rId14\" width=\"159\" height=\"200\"><br>There are 14 triangle<br>ABC, ACD, ABD, EFM, MFG, MGH, EMH, EFG, FGH, EGH, EFH, ILJ, LJK, IJK</p>",
                    solution_hi: "<p>5.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085477.png\" alt=\"rId14\" width=\"159\" height=\"200\"><br>14 त्रिभुज हैं<br>ABC, ACD, ABD, EFM, MFG, MGH, EMH, EFG, FGH, EGH, EFH, ILJ, LJK, IJK</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language, &lsquo;IFF&rsquo; is coded as &lsquo;21&rsquo; and &lsquo;ARB&rsquo; is coded as &lsquo;21&rsquo;. How will &lsquo;CAW&rsquo; be coded in that language ?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में, \'IFF\' को \'21\' के रूप में कूटबद्ध किया जाता है और \'ARB\' को \'21\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'CAW\' को किस प्रकार कूटबद्ध किया जाएगा ?</p>",
                    options_en: [
                        "<p>25</p>",
                        "<p>27</p>",
                        "<p>23</p>",
                        "<p>26</p>"
                    ],
                    options_hi: [
                        "<p>25</p>",
                        "<p>27</p>",
                        "<p>23</p>",
                        "<p>26</p>"
                    ],
                    solution_en: "<p>6.(b)<br><strong>Logic :-</strong> Sum of place value in alphabetical series<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085569.png\" alt=\"rId15\" width=\"138\" height=\"62\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085788.png\" alt=\"rId16\" width=\"137\" height=\"62\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085918.png\" alt=\"rId17\" width=\"139\" height=\"59\"></p>",
                    solution_hi: "<p>6.(b)<br><strong>तर्क :-</strong> वर्णमाला क्रम में स्थानीय मान का योग<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085569.png\" alt=\"rId15\" width=\"138\" height=\"62\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085788.png\" alt=\"rId16\" width=\"137\" height=\"62\"><br>उसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253085918.png\" alt=\"rId17\" width=\"139\" height=\"59\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Which two signs should be interchanged to make the given equation correct ?&nbsp;<br>7 &divide; 255 + 5 &times; 3 &minus; 20 = 140</p>",
                    question_hi: "<p>7. दिए गए समीकरण को सही बनाने के लिए कौन से दो चिह्नों को परस्&zwj;पर बदलना चाहिए ?<br>7 &divide; 255 + 5 &times; 3 &minus; 20 = 140</p>",
                    options_en: [
                        " × and + ",
                        " + and − ",
                        " + and ÷ ",
                        " − and ÷"
                    ],
                    options_hi: [
                        " × और + ",
                        " + और − ",
                        " + और ÷ ",
                        " − और ÷"
                    ],
                    solution_en: "<p>7.(c) <strong>Given :-</strong> 7 &divide;&nbsp;255 + 5 &times; 3 - 20 = 140<br>As per given instruction after interchanging the &lsquo;+&rsquo; and &lsquo;&divide;&rsquo; we get<br>7 + 255 &divide; 5 &times; 3 - 20<br>7 + 51 &times; 3 - 20<br>7 + 153 - 20<br>160 - 20 = 140<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>7.(c) <strong>दिया गया :- </strong>7 &divide; 255 + 5 &times; 3 - 20 = 140<br>दिए गए निर्देश के अनुसार \'+\' और \'&divide;\' को आपस में बदलने पर हमें प्राप्त होता है<br>7 + 255 &divide; 5 &times; 3 - 20<br>7 + 51 &times; 3 - 20<br>7 + 153 - 20<br>160 - 20 = 140<br>L.H.S. = R.H.S.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. What should come in place of the question mark (?) in the given series based on the English alphabetical order ?<br>BOJ, FQO, JST, NUY, ?</p>",
                    question_hi: "<p>8. अंग्रेजी वर्णमाला क्रम केआधार पर दी गई श्रृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>BOJ, FQO, JST, NUY, ?</p>",
                    options_en: [
                        "<p>RVB</p>",
                        "<p>QVC</p>",
                        "<p>RWD</p>",
                        "<p>QWD</p>"
                    ],
                    options_hi: [
                        "<p>RVB</p>",
                        "<p>QVC</p>",
                        "<p>RWD</p>",
                        "<p>QWD</p>"
                    ],
                    solution_en: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253086032.png\" alt=\"rId18\" width=\"357\" height=\"121\"></p>",
                    solution_hi: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253086032.png\" alt=\"rId18\" width=\"357\" height=\"121\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement.<br><strong>Statements :</strong><br>Some bags are paper.<br>All wood is paper.<br>Some bags are dolls.<br><strong>Conclusion (I) : </strong>All dolls are paper.<br><strong>Conclusion (II) :</strong> Some bags are wood.</p>",
                    question_hi: "<p>9. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong><br>कुछ बैग, कागज हैं।<br>सभी लकड़ी, कागज हैं।<br>कुछ बैग, गुड़ियां हैं।<br><strong>निष्कर्ष (I) :</strong> सभी गुड़ियां, कागज हैं।<br><strong>निष्कर्ष (II) :</strong> कुछ बैग, लकड़ी हैं।</p>",
                    options_en: [
                        " Both conclusions (I) and (II) follow.",
                        " Neither conclusion (I) nor (II) follows. ",
                        " Only conclusion (I) follows. ",
                        " Only conclusion (II) follows."
                    ],
                    options_hi: [
                        " दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं। ",
                        " न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है। ",
                        " केवल निष्कर्ष (I) कथनों के अनुसार है। ",
                        " केवल निष्कर्ष (II) कथनों के अनुसार है।"
                    ],
                    solution_en: "<p>9.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253086187.png\" alt=\"rId19\" width=\"296\" height=\"95\"><br>Neither conclusion I nor II follows.</p>",
                    solution_hi: "<p>9.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253086536.png\" alt=\"rId20\" width=\"310\" height=\"99\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;,<br>&lsquo;A - B&rsquo; means &lsquo;A is the daughter of B&rsquo;,<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the husband of B&rsquo;,<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the sister of B&rsquo;.<br>Based on the above, how is M related to Q if &lsquo;M &divide; N &ndash; O &times; P + Q&rsquo; ?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में,<br>A + B का अर्थ है, A , B की माँ है\',<br>A - B का अर्थ है, A , B की पुत्री है\',<br>A &times; B का अर्थ है, A , B का पति है\',<br>A &divide; B का अर्थ है, A , B की बहन है\'।<br>उपरोक्त के आधार पर, यदि \'M &divide; N - O &times; P + Q\' है, तो M, Q से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        "<p>Daughter</p>",
                        "<p>Sister</p>",
                        "<p>Father&rsquo;s sister</p>",
                        "<p>Mother</p>"
                    ],
                    options_hi: [
                        "<p>पुत्री</p>",
                        "<p>बहन</p>",
                        "<p>पिता की बहन</p>",
                        "<p>माँ</p>"
                    ],
                    solution_en: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253086653.png\" alt=\"rId21\" width=\"175\" height=\"99\"><br>M is the sister of Q.</p>",
                    solution_hi: "<p>10.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253086653.png\" alt=\"rId21\" width=\"175\" height=\"99\"><br>M, Q की बहन है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Study the given diagram carefully and answer the question that follows. The numbers in different sections indicate the number of persons who have applied for different posts.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253086864.png\" alt=\"rId22\" width=\"195\" height=\"254\"> <br>What is the total number of persons who have applied for more than one post ?</p>",
                    question_hi: "<p>11. दिए गए आरेख का ध्यानपूर्वक अध्ययन करें और निम्नलिखित प्रश्&zwj;न का उत्तर दें। विभिन्न खंडों में दी गई संख्याएं अलग-अलग पदों के लिए आवेदन करने वाले व्यक्तियों की संख्या को दर्शाती हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253087005.png\" alt=\"rId23\" width=\"238\" height=\"279\"> <br>एक से अधिक पदों के लिए आवेदन करने वाले व्यक्तियों की कुल संख्या कितनी है ?</p>",
                    options_en: [
                        "<p>21</p>",
                        "<p>113</p>",
                        "<p>15</p>",
                        "<p>18</p>"
                    ],
                    options_hi: [
                        "<p>21</p>",
                        "<p>113</p>",
                        "<p>15</p>",
                        "<p>18</p>"
                    ],
                    solution_en: "<p>11.(a) Total number of person who have applied for more than 1 post = 8 + 6 + 4 + 3 = 21.</p>",
                    solution_hi: "<p>11.(a) 1 से अधिक पद के लिए आवेदन करने वाले व्यक्तियों की कुल संख्या = 8 + 6 + 4 + 3 = 21.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Six letters O, Z, F, T, R and M are written on different faces of a dice. Two positions of this dice are shown in the figures below. Find the letter on the face opposite to Z.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253087131.png\" alt=\"rId24\" width=\"198\" height=\"105\"></p>",
                    question_hi: "<p>12. एक पासे के विभिन्न फलकों पर छह अक्षर O, Z, F, T, R और M लिखे गए हैं। इस पासे की दो स्थितियाँ नीचे चित्र में दर्शाई गई हैं। Z के विपरीत वाले फलक पर आने वाला अक्षर ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253087131.png\" alt=\"rId24\" width=\"198\" height=\"105\"></p>",
                    options_en: [
                        " T   ",
                        " F ",
                        " R ",
                        " M"
                    ],
                    options_hi: [
                        " T   ",
                        " F ",
                        " R ",
                        " M"
                    ],
                    solution_en: "12.(d)<br />From the two dice the opposite faces are<br />Z ↔ M , O ↔ T , F ↔ R",
                    solution_hi: "12.(d)<br />दोनो  पासों के विपरीत फलक हैं<br />Z ↔ M , O ↔ T , F ↔ R",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Sunil drives 5 km from point A towards the west. He then takes a left turn and drives 6 km. He again takes a left turn and drives 11 km. He then takes a left turn and drives 6 km. He finally takes a right turn and drives 6 km to arrive at point B. How much and in which direction does he have to drive to return to point A ?<br>(All the turns are 90&deg; turns only.)</p>",
                    question_hi: "13. सुनील बिंदु A से पश्चिम की ओर 5 km ड्राइव करता है। फिर वह बाएं मुड़ता है और 6 km ड्राइव करता है। वह फिर से बाएं मुड़ता है और 11 km ड्राइव करता है। फिर वह बाएं मुड़ता है और 6 km ड्राइव करता है। अंत में वह दाएं मुड़ता है और बिंदु B पर पहुँचने के लिए 6 km ड्राइव करता है। बिंदु A पर लौटने के लिए उसे कितना और किस दिशा में ड्राइव करना होगा ?<br />(सभी मोड़ केवल 90° के मोड़ हैं।)",
                    options_en: [
                        " 10 km towards the west ",
                        " 10 km towards the east ",
                        " 14 km towards the east ",
                        " 12 km towards the west"
                    ],
                    options_hi: [
                        " पश्चिम दिशा में 10 km ",
                        " पूर्व दिशा में 10 km ",
                        " पूर्व दिशा में 14 km",
                        " पश्चिम दिशा में 12 km"
                    ],
                    solution_en: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253087961.png\" alt=\"rId25\" width=\"349\" height=\"148\"><br>He has to drive 12 km towards the west to return to point A.</p>",
                    solution_hi: "<p>13.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253087961.png\" alt=\"rId25\" width=\"349\" height=\"148\"><br>बिंदु A पर लौटने के लिए उसे पश्चिम दिशा में 12 किमी ड्राइव करना होगा ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Six people A, B, C, D, E and F are sitting around a circular table, facing the centre (but not necessarily in the same order). F is sitting second to right of D. C is sitting third to left of F. B is sitting to the immediate right of D. A is not an immediate neighbour of C. How many people are sitting between E and B when counted from right of E ?</p>",
                    question_hi: "<p>14. छः व्यक्ति A, B, C, D, E और F एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। F, D के दाएँ से दूसरे स्थान पर बैठा है। C, F के बाएँ से तीसरे स्थान पर बैठा है। B, D के ठीक दाएँ बैठा है। A, C का निकटतम पड़ोसी नहीं है। E के दाएँ से गिनने पर E और B के बीच में कितने व्यक्ति बैठे हैं ?</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253088132.png\" alt=\"rId26\" width=\"182\" height=\"168\"><br>Two people are sitting between E and B.</p>",
                    solution_hi: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253088132.png\" alt=\"rId26\" width=\"182\" height=\"168\"><br>E और B के बीच दो व्यक्ति बैठे हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. If \'A\' stands for \'&divide;\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for \'-\', what will come in place of the question mark (?) in the following equation ?<br>26 B 3 C 5 D 120 A 4 = ?</p>",
                    question_hi: "<p>15. यदि \'A\' का अर्थ \'&divide;\', \'B\' का अर्थ \'&times;\', \'C\' का अर्थ \'+\' और \'D\' का अर्थ \'-\' है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा ?<br>26 B 3 C 5 D 120 A 4 = ?</p>",
                    options_en: [
                        " 55",
                        " 57 ",
                        " 53 ",
                        " 51"
                    ],
                    options_hi: [
                        " 55",
                        " 57 ",
                        " 53 ",
                        " 51"
                    ],
                    solution_en: "<p>15.(c) <strong>Given :- </strong>26 B 3 C 5 D 120 A 4<br>As per given instructions after interchanging the letter with sign we get<br>26 &times; 3 + 5 - 120 &divide; 4<br>78 + 5 - 30 = 53</p>",
                    solution_hi: "<p>15.(c)<strong> दिया गया :- </strong>26 B 3 C 5 D 120 A 4<br>दिए गए निर्देशों के अनुसार अक्षर को चिह्न से बदलने पर हमें प्राप्त होता है<br>26 &times; 3 + 5 - 120 &divide; 4<br>78 + 5 - 30 = 53</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;NIGHTMARES&rsquo; is re-arranged in the English alphabetical order from left to right ?</p>",
                    question_hi: "<p>16. यदि शब्द &lsquo;NIGHTMARES&rsquo; के प्रत्येक अक्षर को अंग्रेजी वर्णमाला के क्रम में बाएँ से दाएँ पुनर्व्यवस्थित किया जाए तो कितने अक्षरों की स्थिति अपरिवर्तित रहेगी ?</p>",
                    options_en: [
                        " One",
                        " Three",
                        " Two",
                        " Four"
                    ],
                    options_hi: [
                        " एक ",
                        " तीन ",
                        " दो ",
                        " चार"
                    ],
                    solution_en: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253088330.png\" alt=\"rId27\" width=\"192\" height=\"81\"><br>The position of 4 letters remain unchanged.</p>",
                    solution_hi: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253088330.png\" alt=\"rId27\" width=\"192\" height=\"81\"><br>4 अक्षरों का स्थान अपरिवर्तित रहता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Identify the figure given in the options which when put in place of ? will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253089310.png\" alt=\"rId28\" width=\"415\" height=\"83\"></p>",
                    question_hi: "<p>17. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे ? के स्थान पर रखने पर श्रृंखला तर्कसंगत रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253089310.png\" alt=\"rId28\" width=\"415\" height=\"83\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253089626.png\" alt=\"rId29\" width=\"113\" height=\"114\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253089832.png\" alt=\"rId30\" width=\"115\" height=\"116\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253089967.png\" alt=\"rId31\" width=\"118\" height=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090102.png\" alt=\"rId32\" width=\"117\" height=\"118\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253089626.png\" alt=\"rId29\" width=\"117\" height=\"118\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253089832.png\" alt=\"rId30\" width=\"116\" height=\"117\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253089967.png\" alt=\"rId31\" width=\"116\" height=\"117\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090102.png\" alt=\"rId32\" width=\"117\" height=\"118\"></p>"
                    ],
                    solution_en: "<p>17.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090102.png\" alt=\"rId32\" width=\"118\" height=\"119\"></p>",
                    solution_hi: "<p>17.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090102.png\" alt=\"rId32\" width=\"118\" height=\"119\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. What should come in place of the question mark (?) in the given series ?&nbsp;<br>8, 21, 34, 47, 60, ?</p>",
                    question_hi: "<p>18. दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए ?<br>8, 21, 34, 47, 60, ?</p>",
                    options_en: [
                        "<p>71</p>",
                        "<p>72</p>",
                        "<p>70</p>",
                        "<p>73</p>"
                    ],
                    options_hi: [
                        "<p>71</p>",
                        "<p>72</p>",
                        "<p>70</p>",
                        "<p>73</p>"
                    ],
                    solution_en: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090280.png\" alt=\"rId33\" width=\"260\" height=\"51\"></p>",
                    solution_hi: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090280.png\" alt=\"rId33\" width=\"260\" height=\"51\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. If 24 August 1932 was a Wednesday, then what was the day of the week on 11 September 1943 ?</p>",
                    question_hi: "<p>19. यदि 24 अगस्त 1932 को बुधवार था, तो 11 सितंबर 1943 को सप्ताह का कौन-सा दिन था ?</p>",
                    options_en: [
                        "<p>Sunday</p>",
                        "<p>Wednesday</p>",
                        "<p>Saturday</p>",
                        "<p>Monday</p>"
                    ],
                    options_hi: [
                        "<p>रविवार</p>",
                        "<p>बुधवार</p>",
                        "<p>शनिवार</p>",
                        "<p>सोमवार</p>"
                    ],
                    solution_en: "<p>19.(c) 24 August 1932 is Wednesday. On moving from 1932 to 1943 the number of odd days =&nbsp;1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 + 1 = 13. We have reached till 24 August 1943, we have to reach till 11 september 1942. Number of days in between = 7 + 11 = 18. Total number of odd days = 18 + 13 = 31. On dividing 31 by 7,remainder = 3. Wednesday + 3 = Saturday.</p>",
                    solution_hi: "<p>19.(c) 24 अगस्त 1932 को बुधवार है. 1932 से 1943 तक जाने पर विषम दिनों की संख्या =&nbsp;1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 + 1 = 13. हम 24 अगस्त 1943 तक पहुँच चुके हैं, हमें 11 सितम्बर 1942 तक पहुँचना है। बीच में दिनों की संख्या = 7 + 11 = 18. विषम दिनों की कुल संख्या = 18 + 13 = 31. 31 को 7 से विभाजित करने पर शेषफल = 3. बुधवार + 3 = शनिवार.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. This question consists of a pair of words which have a certain relationship to each&nbsp;other. Select the pair which does NOT have the same relationship.<br>Allure : Repulse<br>1) Creation : Destruction<br>2) Fluctuate : Stabilize<br>3) Immense : Huge<br>4) Notion : Reality</p>",
                    question_hi: "<p>20. इस प्रश्न में शब्द -युग्म है जिनका एक दूसरे से एक निश्चित संबंध है। उस युग्म का चयन कीजिए जिसमें समान संबंध नहीं है।<br>आकर्षण : विकर्षण<br>1) सृजन : विनाश<br>2) उतार-चढ़ाव : स्थिर होना<br>3) अपार : विशाल<br>4) ख्याल : वास्तविकता</p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>4</p>",
                        "<p>2</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>20.(d) As Allure and Repulse are opposite of each similarly the words in all the three options are opposite of each other except Immense : Huge.</p>",
                    solution_hi: "<p>20.(d) जिस प्रकार आकर्षण और विकर्षण एक दूसरे के विपरीत हैं उसी प्रकार अपार : विशाल को छोड़कर तीनों विकल्पों में शब्द एक दूसरे के विपरीत हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. In a certain code language, \'get enough sleep\' is coded as \'ab de jf\' and \'go get it\' is coded as \'jk ab gh\'. How is \'get\' coded in that language ?</p>",
                    question_hi: "<p>21. एक निश्चित कूट भाषा में, get enough sleep को \'ab de jf लिखा जाता है और \'go get it\' को \'jk ab gh\' लिखा जाता है। उस कूट भाषा में \'get\' को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>jf</p>",
                        "<p>ab</p>",
                        "<p>de</p>",
                        "<p>jk</p>"
                    ],
                    options_hi: [
                        "<p>jf</p>",
                        "<p>ab</p>",
                        "<p>de</p>",
                        "<p>jk</p>"
                    ],
                    solution_en: "<p>21.(b) get enough sleep &rarr; ab de jf&hellip;&hellip;.(i)<br>go get it &rarr; jk ab gh&hellip;&hellip;.(ii)<br>From (i) and (ii) &lsquo;get&rsquo; and &lsquo;ab&rsquo; are common. The code of &lsquo;get&rsquo; = &lsquo;ab&rsquo;.</p>",
                    solution_hi: "<p>21.(b) get enough sleep &rarr; ab de jf&hellip;&hellip;.(i)<br>go get it &rarr; jk ab gh&hellip;&hellip;.(ii)<br>(i) और (ii) से \'get\' और \'ab\' उभयनिष्ठ हैं। \'get\' का कूट = \'ab\'।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. A father is presently 3 times his daughter&rsquo;s age. After 10 years he will be twice as old as her. Find the daughter&rsquo;s present age.</p>",
                    question_hi: "<p>22. एक पिता की वर्तमान आयु, उसकी पुत्री की आयु की 3 गुनी है। 10 वर्ष बाद उसकी आयु उसकी पुत्री की आयु की दोगुनी होगी। पुत्री की वर्तमान आयु ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>10 years</p>",
                        "<p>5 years</p>",
                        "<p>15 years</p>",
                        "<p>20 years</p>"
                    ],
                    options_hi: [
                        "<p>10 वर्ष</p>",
                        "<p>5 वर्ष</p>",
                        "<p>15 वर्ष</p>",
                        "<p>20 वर्ष</p>"
                    ],
                    solution_en: "<p>22.(a)<br>Let the present age of the father and his daughter is 3x&nbsp;and x years respectively.<br>According to the question,<br>3x&nbsp;+ 10 = 2(x + 10)<br>3x&nbsp;+ 10 = 2x + 20<br>x = 10<br>Hence, the age of the daughter (x) = 10 years</p>",
                    solution_hi: "<p>22.(a)<br>माना पिता और उसकी पुत्री की वर्तमान आयु क्रमशः 3x&nbsp;और x वर्ष है।<br>प्रश्न के अनुसार,<br>3x&nbsp;+ 10 = 2(x + 10)<br>3x&nbsp;+ 10 = 2x + 20<br>x = 10<br>अत: पुत्री की आयु (x) = 10 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>JOCK : GLZH :: HURT : EROQ :: SPIN : ?</p>",
                    question_hi: "<p>23. उस विकल्प का चयन करें जो पांचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है।<br>JOCK : GLZH :: HURT : EROQ :: SPIN : ?</p>",
                    options_en: [
                        "<p>PMFK</p>",
                        "<p>QMEK</p>",
                        "<p>QNFK</p>",
                        "<p>PNEL</p>"
                    ],
                    options_hi: [
                        "<p>PMFK</p>",
                        "<p>QMEK</p>",
                        "<p>QNFK</p>",
                        "<p>PNEL</p>"
                    ],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090448.png\" alt=\"rId34\" width=\"176\" height=\"124\">&nbsp; &nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090585.png\" alt=\"rId35\" width=\"189\" height=\"127\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090771.png\" alt=\"rId36\" width=\"179\" height=\"122\"></p>",
                    solution_hi: "<p>23.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090448.png\" alt=\"rId34\" width=\"176\" height=\"124\">&nbsp; &nbsp; ,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090585.png\" alt=\"rId35\" width=\"189\" height=\"127\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090771.png\" alt=\"rId36\" width=\"179\" height=\"122\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090919.png\" alt=\"rId37\" width=\"107\" height=\"135\"></p>",
                    question_hi: "<p>24. दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253090919.png\" alt=\"rId37\" width=\"107\" height=\"135\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091039.png\" alt=\"rId38\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091177.png\" alt=\"rId39\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091302.png\" alt=\"rId40\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091410.png\" alt=\"rId41\" width=\"90\" height=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091039.png\" alt=\"rId38\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091177.png\" alt=\"rId39\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091302.png\" alt=\"rId40\" width=\"90\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091410.png\" alt=\"rId41\" width=\"90\" height=\"90\"></p>"
                    ],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091302.png\" alt=\"rId40\" width=\"90\" height=\"90\"></p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091302.png\" alt=\"rId40\" width=\"90\" height=\"90\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In a certain code language, \'MAIN\' is written as \'OHBL\' and \'MAIL\' is written as \'MHBL\'. How will \'MANY&rsquo; be written in that language ?</p>",
                    question_hi: "<p>25. एक निश्चित कूट भाषा में, \'MAIN\' को \'OHBL\' लिखा जाता है और &lsquo;MAIL\' को \'MHBL\' लिखा जाता है। इसी कूट भाषा में \'MANY&rsquo; को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>ZLMB</p>",
                        "<p>ZMBL</p>",
                        "<p>ZBLM</p>",
                        "<p>ZMLB</p>"
                    ],
                    options_hi: [
                        "<p>ZLMB</p>",
                        "<p>ZMBL</p>",
                        "<p>ZBLM</p>",
                        "<p>ZMLB</p>"
                    ],
                    solution_en: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091563.png\" alt=\"rId42\" width=\"128\" height=\"129\">&nbsp; &nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091667.png\" alt=\"rId43\" width=\"132\" height=\"126\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091817.png\" alt=\"rId44\" width=\"130\" height=\"119\"></p>",
                    solution_hi: "<p>25.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091563.png\" alt=\"rId42\" width=\"128\" height=\"129\">&nbsp; &nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091667.png\" alt=\"rId43\" width=\"132\" height=\"126\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091817.png\" alt=\"rId44\" width=\"130\" height=\"119\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following percussion instruments is used in the popular folk dance &lsquo;Lavani&rsquo; ?</p>",
                    question_hi: "<p>26. लोकप्रिय लोक नृत्य \'लावणी\' में निम्नलिखित में से कौन सा ताल वाद्य यंत्र प्रयोग किया जाता है ?</p>",
                    options_en: [
                        "<p>Dholak</p>",
                        "<p>Xylophone</p>",
                        "<p>Piano</p>",
                        "<p>Sleigh Bells</p>"
                    ],
                    options_hi: [
                        "<p>ढोलक</p>",
                        "<p>ज़ाइलोफोन</p>",
                        "<p>पियानो</p>",
                        "<p>स्लीघ बेल्स</p>"
                    ],
                    solution_en: "<p>26.(a) <strong>Dholak.</strong> Lavani is a combination of song and dance that is popular in Maharashtra. Dances and related music instruments: Kachchi Godi dance (Jhanj), Kathak (Tabla, Pakhawaj, Sitar, Flute, Sarangi etc), Bharatanatyam (Mridangam, Nadaswaram), Kathakali (Chenda, maddalam), Mohiniyattam Dance (Idakka).</p>",
                    solution_hi: "<p>26.(a) <strong>ढोलक। </strong>लावणी गीत और नृत्य का एक संयोजन है जो महाराष्ट्र में लोकप्रिय है। नृत्य और उससे संबंधित संगीत वाद्ययंत्र: कच्छी गोदी नृत्य (झांझ), कथक (तबला, पखावज, सितार, बांसुरी, सारंगी आदि), भरतनाट्यम (मृदंगम, नादस्वरम), कथकली (चेंडा, मद्दलम), मोहिनीअट्टम नृत्य (इडक्का)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Who described Archimedes\' method of finding the specific gravity of substances using a balance in the treatise \'The Little Balance\' ?</p>",
                    question_hi: "<p>27. \'द लिटिल बैलेंस\' पुस्तक में तुला का उपयोग करके पदार्थों के विशिष्ट गुरुत्व (specific gravity) को ज्ञात करने की आर्किमिडीज़ की विधि का वर्णन किसने किया था ?</p>",
                    options_en: [
                        "<p>Galileo Galilei</p>",
                        "<p>Michael Faraday</p>",
                        "<p>Daniel Bernoulli</p>",
                        "<p>Albert Einstein</p>"
                    ],
                    options_hi: [
                        "<p>गैलीलियो गैलिली (Galileo Galilei)</p>",
                        "<p>माइकल फैराडे (Michael Faraday)</p>",
                        "<p>डैनियल बर्नोली (Daniel Bernoulli)</p>",
                        "<p>अल्बर्ट आइंस्टीन (Albert Einstein)</p>"
                    ],
                    solution_en: "<p>27.(a) <strong>Galileo Galilei.</strong> The little balance was the first scientific book of Galileo. Archimedes\' principle states that the buoyant force on an object equals the weight of the fluid it displaces. Specific gravity is the ratio of the density of an object to a fluid (usually water).</p>",
                    solution_hi: "<p>27.(a) <strong>गैलीलियो गैलिली (Galileo Galilei)। </strong>&lsquo;द लिटिल बैलेंस&rsquo; गैलीलियो की प्रथम वैज्ञानिक पुस्तक थी। आर्किमिडीज़ का सिद्धांत कहता है कि किसी वस्तु पर लगने वाला उत्प्लावन बल उसके द्वारा विस्थापित किए गए द्रव के भार के बराबर होता है। विशिष्ट गुरुत्व, किसी वस्तु के घनत्व और द्रव (सामान्यतः जल) के घनत्व का अनुपात है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. In which year was Indian Olympic Association formed ?</p>",
                    question_hi: "<p>28. भारतीय ओलंपिक संघ का गठन किस वर्ष हुआ था ?</p>",
                    options_en: [
                        "<p>1920</p>",
                        "<p>1927</p>",
                        "<p>1900</p>",
                        "<p>1928</p>"
                    ],
                    options_hi: [
                        "<p>1920 में</p>",
                        "<p>1927 में</p>",
                        "<p>1900 में</p>",
                        "<p>1928 में</p>"
                    ],
                    solution_en: "<p>28.(b)<strong> 1927.</strong> Indian Olympic Association : Founding President - Sir Dorabji Tata. First Secretary General - Dr. A.G. Noehren. It is registered as a Non-Profit Organisation under the Societies Registration Act of 1860. Members : National Sports Federations, State Olympic Associations, IOC Members and other select multi-sport organisations. Other Associations: Olympic Council of Asia - 1982. International Olympic Committee - 1894.</p>",
                    solution_hi: "<p>28.(b)<strong> 1927 में। </strong>भारतीय ओलंपिक संघ: संस्थापक अध्यक्ष - सर दोराबजी टाटा। प्रथम महासचिव - डॉ. ए.जी. नोएरेन। यह 1860 के सोसायटी पंजीकृत अधिनियम के तहत एक गैर-लाभकारी संगठन है। सदस्य: राष्ट्रीय खेल महासंघ, राज्य ओलंपिक संघ, IOC सदस्य और अन्य चुनिंदा बहु-खेल संगठन। अन्य संघ: एशिया ओलंपिक परिषद - 1982। अंतर्राष्ट्रीय ओलंपिक समिति - 1894।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who was appointed as the Chief Justice of the Himachal Pradesh High Court in December 2024 ?</p>",
                    question_hi: "<p>29. दिसंबर 2024 में हिमाचल प्रदेश उच्च न्यायालय के मुख्य न्यायाधीश के रूप में किसे नियुक्त किया गया ?</p>",
                    options_en: [
                        "<p>Justice Rajiv Shakdher</p>",
                        "<p>Justice Sheel Nagu</p>",
                        "<p>Justice Gurmeet Singh Sandhawalia</p>",
                        "<p>Justice Amjad Ahtesham Sayed</p>"
                    ],
                    options_hi: [
                        "<p>न्यायमूर्ति राजीव शकधर</p>",
                        "<p>न्यायमूर्ति शील नागू</p>",
                        "<p>न्यायमूर्ति गुरमीत सिंह संधावालिया</p>",
                        "<p>न्यायमूर्ति अमजद अहतेशाम सईद</p>"
                    ],
                    solution_en: "<p>29.(c) <strong>Justice Gurmeet Singh Sandhawalia. </strong>The Himachal Pradesh High Court, established in 1971, is headquartered in Shimla. The first three High Courts in India were established in 1862: Calcutta High Court (the first), followed by Bombay and Madras High Courts.</p>",
                    solution_hi: "<p>29.(c) <strong>न्यायमूर्ति गुरमीत सिंह संधावालिया। </strong>1971 में स्थापित हिमाचल प्रदेश उच्च न्यायालय का मुख्यालय शिमला में है। भारत में पहले तीन उच्च न्यायालय 1862 में स्थापित किए गए थे: कलकत्ता उच्च न्यायालय (पहला), उसके बाद बॉम्बे और मद्रास उच्च न्यायालय।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. Writ petition can be filed in the Supreme Court of India under:",
                    question_hi: "<p>30. ______के अंतर्गत भारत के सर्वोच्च न्यायालय में रिट याचिका (Writ petition) दायर की जा सकती है।</p>",
                    options_en: [
                        " Article 40 ",
                        " Article 32 ",
                        " Article 270 ",
                        " Article 226"
                    ],
                    options_hi: [
                        " अनुच्छेद 40 ",
                        " अनुच्छेद 32 ",
                        " अनुच्छेद 270 ",
                        " अनुच्छेद 226"
                    ],
                    solution_en: "<p>30.(b)<strong> Article 32 </strong>(Right to Constitutional Remedies): It is a fundamental right, which states that individuals have the right to approach the Supreme Court (SC) seeking enforcement of other fundamental rights recognised by the Constitution. Other important Articles: Article 226 allows individuals to file writ petitions in the High Courts of India for the enforcement of fundamental rights and for other purposes. Article 40 relates to the organization of village panchayats. Article 270 deals with the distribution of taxes between the Union and the States.</p>",
                    solution_hi: "<p>30.(b) <strong>अनुच्छेद 32 </strong>(संवैधानिक उपचारों का अधिकार): यह एक मौलिक अधिकार है, जिसके अनुसार व्यक्तियों को संविधान द्वारा मान्यता प्राप्त अन्य मौलिक अधिकारों के प्रवर्तन की मांग करते हुए सर्वोच्च न्यायालय (SC) में जाने का अधिकार है। अन्य महत्वपूर्ण अनुच्छेद: अनुच्छेद 226 व्यक्तियों को मौलिक अधिकारों के प्रवर्तन और अन्य उद्देश्यों के लिए भारत के उच्च न्यायालयों में रिट याचिका दायर करने की अनुमति देता है। अनुच्छेद 40 ग्राम पंचायतों के संगठन से संबंधित है। अनुच्छेद 270 संघ और राज्यों के बीच करों के वितरण से संबंधित है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which of the following fungus is used in fermentation technology ?</p>",
                    question_hi: "<p>31. निम्नलिखित में से किस कवक का उपयोग किण्वन तकनीकी में प्रयोग किया जाता है ?</p>",
                    options_en: [
                        "<p>Rhizobium</p>",
                        "<p>Molds</p>",
                        "<p>Saccharomyces</p>",
                        "<p>Smuts</p>"
                    ],
                    options_hi: [
                        "<p>राइजोबियम</p>",
                        "<p>कवकच्छद</p>",
                        "<p>सैक्रोमाइसीज</p>",
                        "<p>स्मट</p>"
                    ],
                    solution_en: "<p>31.(c) <strong>Saccharomyces</strong> cerevisiae (Baker\'s Yeast) is a unicellular fungus that is used for the purpose of making bread and other wheat-based products. Rhizobium - A non spore-forming rod-shaped, motile, aerobic, gram-negative soil bacterium. Smuts - The plant disease primarily affecting grasses, including corn (maize), wheat, sugarcane, and sorghum. Molds are multinucleated, filamentous fungi composed of hyphae.</p>",
                    solution_hi: "<p>31.(c) <strong>सैक्रोमाइसीज </strong>सेरेविसिया (बेकर का खमीर) - एक एककोशिकीय कवक जिसका उपयोग रोटी और अन्य गेहूं-आधारित उत्पाद बनाने के लिए किया जाता है। राइजोबियम - एक गैर बीजाणु बनाने वाली छड़ के आकार का, गतिशील, एरोबिक, ग्राम-नकारात्मक मिट्टी का जीवाणु। स्मट्स - पौधों की बीमारी मुख्य रूप से मक्का (मक्का), गेहूं, गन्ना और ज्वार सहित घास को प्रभावित करती है। साँचे बहुकेंद्रीय, फिलामेंटस कवक हैं जो हाइफ़े (hyphae) से बने होते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. &lsquo;As I Lay Dying\' is an English novel by which of the following authors ?</p>",
                    question_hi: "<p>32. \'एज़ आई ले डाइंग\' निम्नलिखित में से किस लेखक द्वारा लिखित अंग्रेजी उपन्यास है ?</p>",
                    options_en: [
                        "<p>William Faulkner</p>",
                        "<p>John Dos Passos</p>",
                        "<p>Stella Gibbons</p>",
                        "<p>Aldous Huxley</p>"
                    ],
                    options_hi: [
                        "<p>विलियम फॉकनर</p>",
                        "<p>जॉन डॉस पासोस</p>",
                        "<p>स्टेला गिबन्स</p>",
                        "<p>ऐलडस हक्सले</p>"
                    ],
                    solution_en: "<p>32.(a) <strong>William Faulkner :</strong> Other Novels written by him - &ldquo;The Sound and the Fury&rdquo;, &ldquo;A Rose for Emily&rdquo;, &ldquo;Absalom, Absalom&rdquo;, &ldquo;Light in August&rdquo;, &ldquo;Barn Burning&rdquo;. Authors and their books : John Dos Passos - &ldquo;The Big Money&rdquo;, &ldquo;The 42nd Parallel&rdquo;. Stella Gibbons - &ldquo;Nightingale Wood&rdquo;, &ldquo;Conference at Cold Comfort Farm&rdquo;, &ldquo;The woods in winter&rdquo;. Aldous Huxley - &ldquo;The Doors of Perception&rdquo;, &ldquo;The Perennial Philosophy&rdquo;, &ldquo;Brave New World Revisited&rdquo;.</p>",
                    solution_hi: "<p>32.(a) <strong>विलियम फॉकनर :&nbsp;</strong>उनके द्वारा लिखे गए अन्य उपन्यास - \"द साउंड एंड द फ्यूरी\", \"ए रोज़ फॉर एमिली\", \"एबशालोम, एबशालोम\", \"लाइट इन अगस्त\", \"बार्न बर्निंग\"। लेखक और उनकी पुस्तकें: जॉन डॉस पासोस - \"द बिग मनी\", \"द 42वां पैरेलल\"। स्टेला गिबन्स - \"नाइटिंगेल वुड\", \"कॉन्फ्रेंस एट कोल्ड कम्फर्ट फार्म\", \"द वुड्स इन विंटर\"। एल्डस हक्सले - \"द डोर्स ऑफ परसेप्शन\", \"द पेरेनियल फिलॉसफी\", \"ब्रेव न्यू वर्ल्ड रिविजिटेड\"।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following authors has written the famous novel \'Gulliver\'s Travels\' ?</p>",
                    question_hi: "<p>33. निम्नलिखित में से किस लेखक ने प्रसिद्ध उपन्यास \'गुलिवर्स ट्रेवल्स\' (Gulliver\'s Travels) लिखा है ?</p>",
                    options_en: [
                        " Oscar Wilde ",
                        " Samueal Richardson ",
                        " Jonathan Swift ",
                        " Ruskin Bond"
                    ],
                    options_hi: [
                        " ऑस्कर वाइल्ड ",
                        " सैमुअल रिचर्डसन ",
                        " जोनाथन स्विफ्ट  ",
                        " रस्किन बॉन्ड"
                    ],
                    solution_en: "<p>33.(c) <strong>Jonathan Swift. </strong>His other books: &ldquo;A Modest Proposal&rdquo;, &ldquo;A Tale of a Tub \'\', &ldquo;The Battle of the Books&rdquo;. Authors and books: Oscar Wilde - &ldquo;The Picture of Dorian Gray&rdquo;, &ldquo;The Happy Prince&rdquo;. Samueal Richardson - &ldquo;The History of Sir Charles Grandison&rdquo;. Ruskin Bond - &ldquo;The Blue Umbrella&rdquo; , &ldquo;The Room on the Roof&rdquo;.</p>",
                    solution_hi: "<p>33.(c) <strong>जोनाथन स्विफ़्ट।</strong> उनकी अन्य पुस्तकें: \"ए मॉडेस्ट प्रपोज़ल\", \"ए टेल ऑफ़ ए टब\", \"द बैटल ऑफ़ द बुक्स\"। लेखक एवं पुस्तकें: ऑस्कर वाइल्ड - \"द पिक्चर ऑफ़ डोरियन ग्रे\", \"द हैप्पी प्रिंस\"। सैमुअल रिचर्डसन - \" द हिस्ट्री ऑफ सर चार्ल्स ग्रैंडिसन\"। रस्किन बॉन्ड - \"द ब्लू अम्ब्रेला\", \"द रूम ऑन द रूफ\"।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. What is the percentage of people below the poverty line in India according to Government of India, planning commission 2013 ?</p>",
                    question_hi: "<p>34. भारत सरकार, योजना आयोग 2013 के अनुसार भारत में गरीबी रेखा से नीचे के लोगों का प्रतिशत कितना है ?</p>",
                    options_en: [
                        "<p>5.09%</p>",
                        "<p>13.98%</p>",
                        "<p>23.67%</p>",
                        "<p>21.92%</p>"
                    ],
                    options_hi: [
                        "<p>5.09%</p>",
                        "<p>13.98%</p>",
                        "<p>23.67%</p>",
                        "<p>21.92%</p>"
                    ],
                    solution_en: "<p>34.(d) <strong>21.92%.</strong> According to the Planning Commission, the percentage of people below the poverty line in urban areas was 13.7% in 2011-12, while in rural areas it was 25.7%. The current methodology for poverty estimation is based on the recommendations of an Expert Group to Review the Methodology for Estimation of Poverty (Tendulkar Committee) established in 2005.</p>",
                    solution_hi: "<p>34.(d)<strong> 21.92%. </strong>योजना आयोग के अनुसार, शहरी क्षेत्रों में गरीबी रेखा से नीचे जीवनयापन करने वाले लोगों का प्रतिशत 2011-12 में 13.7% था, जबकि ग्रामीण क्षेत्रों में यह प्रतिशत 25.7% था। गरीबी आकलन के लिए वर्तमान पद्धति गरीबी आकलन पद्धति की समीक्षा करने के लिए 2005 में स्थापित विशेषज्ञ समूह (तेंदुलकर समिति) की सिफारिशों पर आधारित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. In (PMGDISHA), what does the letter \"D\" stand for ?</p>",
                    question_hi: "<p>35.&nbsp; (PMGDISHA) में, \"D\" अक्षर का क्या मतलब है ?</p>",
                    options_en: [
                        "<p>Development</p>",
                        "<p>Digital</p>",
                        "<p>Distribution</p>",
                        "<p>Deployment</p>"
                    ],
                    options_hi: [
                        "<p>Development</p>",
                        "<p>Digital</p>",
                        "<p>Distribution</p>",
                        "<p>Deployment</p>"
                    ],
                    solution_en: "<p>35.(b) <strong>Digital.</strong> The full form of PMGDISHA is Pradhan Mantri Gramin Digital Saksharta Abhiyan, which translates to Prime Minister\'s Rural Digital Literacy Campaign. This initiative aims to promote digital literacy in rural India, targeting 6 crore rural households to bridge the digital divide.</p>",
                    solution_hi: "<p>35.(b) <strong>Digital.</strong><strong>&nbsp;</strong>PMGDISHA का पूरा नाम है प्रधानमंत्री ग्रामीण डिजिटल साक्षरता अभियान, जो ग्रामीण भारत में डिजिटल साक्षरता को बढ़ावा देने के उद्देश्य से चलाया जा रहा है। इस पहल का लक्ष्य 6 करोड़ ग्रामीण परिवारों को डिजिटल साक्षरता प्रदान करना है, जिससे डिजिटल खाई को पाटा जा सके।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which artificial element has been provisionally named seaborgium by American researchers in honour of Nobel Laureate Glenn T Seaborg ?</p>",
                    question_hi: "<p>36. नोबेल पुरस्कार विजेता ग्लेन टी. सीबोर्ग के सम्मान में अमेरिकी शोधकर्ताओं द्वारा किस कृत्रिम तत्व को अस्थायी रूप से सीबोर्गियम नाम दिया गया है ?</p>",
                    options_en: [
                        "<p>Element 103</p>",
                        "<p>Element 106</p>",
                        "<p>Element 90</p>",
                        "<p>Element 97</p>"
                    ],
                    options_hi: [
                        "<p>तत्व 103</p>",
                        "<p>तत्व 106</p>",
                        "<p>तत्व 90</p>",
                        "<p>तत्व 97</p>"
                    ],
                    solution_en: "<p>36.(b) <strong>Element 106.</strong> Seaborgium is a highly radioactive element, with its most stable isotope possessing a half-life of only a few minutes. Its discovery was first reported in 1974 by a team of scientists at the Lawrence Berkeley National Laboratory in California. The Periodic Table is a tabular arrangement of chemical elements, organized according to their atomic number, electron configuration, and recurring chemical properties. Notable elements include Element 103 - Lawrencium, Element 90 - Thorium, and Element 97 - Berkelium.</p>",
                    solution_hi: "<p>36.(b) <strong>तत्व 106.</strong> सीबोर्गियम एक अत्यधिक रेडियोधर्मी तत्व है, जिसका सबसे स्थिर समस्थानिक केवल कुछ मिनटों का अर्ध-आयु काल रखता है। इसकी खोज सबसे पहले 1974 में कैलिफोर्निया में लॉरेंस बर्कले, नेशनल लेबोरेटरी के वैज्ञानिकों की एक टीम द्वारा की गई थी। आवर्त सारणी रासायनिक तत्वों की एक सारणीबद्ध व्यवस्था है, जिसे उनके परमाणु क्रमांक, इलेक्ट्रॉन विन्यास और आवर्ती रासायनिक गुणों के अनुसार व्यवस्थित किया जाता है। उल्लेखनीय तत्वों में तत्व 103 - लॉरेन्सियम, तत्व 90 - थोरियम और तत्व 97 - बर्केलियम शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. According to Census 2011, which union territory has the highest male literacy rate ?</p>",
                    question_hi: "<p>37. 2011 की जनगणना के अनुसार, किस केंद्र शासित प्रदेश की पुरुष साक्षरता दर सर्वाधिक है ?</p>",
                    options_en: [
                        "<p>Delhi</p>",
                        "<p>Daman &amp; Diu</p>",
                        "<p>Lakshadweep</p>",
                        "<p>Puducherry</p>"
                    ],
                    options_hi: [
                        "<p>दिल्ली</p>",
                        "<p>दमन और दीव</p>",
                        "<p>लक्षद्वीप</p>",
                        "<p>पुदुचेरी</p>"
                    ],
                    solution_en: "<p>37.(c) <strong>Lakshadweep. </strong>As per census 2011, Highest Literate State: Kerala, Mizoram, Goa. lowest literate state: Bihar, Arunachal Pradesh, Andhra Pradesh. Union territory: Highest - Lakshadweep, Daman and Diu, Delhi. Lowest: Dadra and Nagar Haveli.</p>",
                    solution_hi: "<p>37.(c) <strong>लक्षद्वीप। </strong>2011 की जनगणना के अनुसार, उच्चतम साक्षर राज्य: केरल, मिजोरम, गोवा। सबसे कम साक्षर राज्य: बिहार, अरुणाचल प्रदेश, आंध्र प्रदेश। केंद्र शासित प्रदेश: उच्चतम - लक्षद्वीप, दमन और दीव, दिल्ली। निम्नतम: दादरा और नगर हवेली।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which Indian dancer won the Sangeet Natak Akademi Award in 2018 for Chhau Dance ?</p>",
                    question_hi: "<p>38. किस भारतीय नृत्य कलाकार ने छऊ नृत्य के लिए 2018 में संगीत नाटक अकादमी पुरस्कार जीता ?</p>",
                    options_en: [
                        "<p>Akham Lakshmi Devi</p>",
                        "<p>Tapan kumar pattanayak</p>",
                        "<p>Dr. Aruna Mohanty</p>",
                        "<p>Gopika Varma</p>"
                    ],
                    options_hi: [
                        "<p>अखम लक्ष्मी देवी (Akham Lakshmi Devi)</p>",
                        "<p>तपन कुमार पट्टनायक (Tapan Kumar Pattanayak)</p>",
                        "<p>डॉ. अरुणा मोहंती (Dr. Aruna Mohanty)</p>",
                        "<p>गोपिका वर्मा (Gopika Varma)</p>"
                    ],
                    solution_en: "<p>38.(b) <strong>Tapan Kumar Pattanayak</strong> (Seraikella). Chhau is performed in three styles: Purulia Chhau (West Bengal), Seraikella Chhau (Jharkhand), and Mayurbhanj Chhau (Odisha). Notable Chhau dancers include Kedar Nath Sahoo (Seraikella), Shashadhar Acharya, and Gambhir Singh Mura (Purulia).</p>",
                    solution_hi: "<p>38.(b) <strong>तपन कुमार पटनायक</strong> (सरायकेला)। छऊ तीन शैलियों में प्रस्तुत किया जाता है: पुरुलिया छऊ (पश्चिम बंगाल), सरायकेला छऊ (झारखंड), और मयूरभंज छऊ (ओडिशा)। प्रसिद्ध छऊ नर्तकों में केदार नाथ साहू (सरायकेला), शशधर आचार्य और गंभीर सिंह मुरा (पुरुलिया) शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Kalidasa was a famous poet in the court of_______.</p>",
                    question_hi: "<p>39. कालिदास _______के दरबार में एक प्रसिद्ध कवि थे।</p>",
                    options_en: [
                        " Kanishka ",
                        " Pushyamitra Shunga ",
                        " Harshavardhana ",
                        " Chandragupta II"
                    ],
                    options_hi: [
                        " कनिष्क ",
                        " पुष्यमित्र शुंग ",
                        " हर्षवर्धन ",
                        " चंद्रगुप्त द्वितीय "
                    ],
                    solution_en: "<p>39.(d) <strong>Chandragupta II,</strong> also known as Vikramaditya, ruled from 380 to 415 CE and was a renowned patron of arts and literature. Kalidasa\'s notable works, such as \"Abhijnanasakuntalam\", \"Meghaduta\", and \"Raghuvamsa\", showcase his mastery of Sanskrit literature. Kanishka belonged to the Kushan dynasty. Harshavardhana belonged to the Pushyabhuti dynasty.</p>",
                    solution_hi: "<p>39.(d) <strong>चंद्रगुप्त द्वितीय,</strong> जिन्हें विक्रमादित्य के नाम से भी जाना जाता है, ने 380 से 415 ई. तक शासन किया और वे कला एवं साहित्य के प्रसिद्ध संरक्षक थे। कालिदास की उल्लेखनीय कृतियाँ, \"अभिज्ञानशाकुंतलम\", \"मेघदूत\" और \"रघुवंश\", संस्कृत साहित्य पर उनकी महानता को प्रदर्शित करती हैं। कनिष्क, कुषाण वंश से संबंधित थे। हर्षवर्धन, पुष्यभूति वंश से संबंधित थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which group of animals has a segmented spinal column together with a few primitive forms in which the backbone is represented by a notochord ?</p>",
                    question_hi: "<p>40. जंतुओं के निम्नलिखित में से किस समूह में कुछ आदिम रूपों (primitive forms) के साथ एक खंडित रीढ़ की हड्डी का स्तंभ (segmented spinal column) होता है, जिसमें रीढ़ की हड्डी का प्रतिनिधित्व एक नोटोकॉर्ड (notochord) द्वारा किया जाता है ?</p>",
                    options_en: [
                        "<p>Vertebrata</p>",
                        "<p>Echinodermata</p>",
                        "<p>Mollusca</p>",
                        "<p>Arthropoda</p>"
                    ],
                    options_hi: [
                        "<p>वर्टिब्रेटा (Vertebrata)</p>",
                        "<p>इकाइनोडर्मेटा (Echinodermata)</p>",
                        "<p>मोलस्का (Mollusca)</p>",
                        "<p>आर्थ्रोपोड़ा (Arthropoda)</p>"
                    ],
                    solution_en: "<p>40.(a)<strong> Vertebrata.</strong> These animals have a segmented spinal column and, during embryonic development, a notochord that is replaced by a vertebral column in adults. Vertebrates are chordates but not all chordates are vertebrates. They also have a muscular heart, kidneys, and paired appendages.</p>",
                    solution_hi: "<p>40.(a) <strong>वर्टिब्रेटा </strong>(Vertebrata)। इन जंतुओं में एक खंडित मेरुदण्ड होती है और भ्रूण के विकास के दौरान, एक नोटोकॉर्ड (notochord) जो वयस्कों में कशेरुक दण्ड द्वारा प्रतिस्थापित हो जाता है। वर्टिब्रेटा, कॉर्डेटा होते हैं लेकिन सभी कॉर्डेटा, वर्टिब्रेटा नहीं होते हैं। इनमें मांसपेशीय हृदय, गुर्दे और युग्मित उपांग भी होते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. From which state, the performing art \'Ottamthullal\' belongs ?</p>",
                    question_hi: "<p>41. प्रदर्शन कला \'ओट्टमथुल्लल (Ottamthullal)\' का संबंध किस राज्य से है ?</p>",
                    options_en: [
                        "<p>Assam</p>",
                        "<p>Kerala</p>",
                        "<p>Bihar</p>",
                        "<p>Jharkhand</p>"
                    ],
                    options_hi: [
                        "<p>असम</p>",
                        "<p>केरल</p>",
                        "<p>बिहार</p>",
                        "<p>झारखंड</p>"
                    ],
                    solution_en: "<p>41.(b) <strong>Kerala.</strong> Ottan Thullal (also known as Ottamthullal) is a recite-and-dance art form introduced in the eighteenth century by Kunchan Nambiar. Notable art forms of Kerala include Kathakali, Kalaripayattu, Thiruvathirakali, Kutiyattam, Theyyam, Mohiniyattam, Velakali, Patayani, Kolkali, Mudiyettu, Kummattikkali, and Oppana.</p>",
                    solution_hi: "<p>41.(b) <strong>केरल।</strong> ओट्टन थुल्लल (ओट्टमथुलाल के नाम से भी जाना जाता है) अठारहवीं शताब्दी में कुंचन नांबियार द्वारा शुरू की गई एक गायन-और-नृत्य कला है। केरल के उल्लेखनीय कला रूपों में कथकली, कलारीपयट्टु, तिरुवथिराकली, कुटियाट्टम, थेय्यम, मोहिनीअट्टम, वेलाकली, पाटयानी, कोलकाली, मुडियेट्टु, कुम्मट्टिककली और ओप्पाना शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Who requested the Syrian king to send a Greek philosopher ?</p>",
                    question_hi: "<p>42. सीरिया के राजा से यूनानी दार्शनिक भेजने का अनुरोध किसने किया था ?</p>",
                    options_en: [
                        " Chandragupta Maurya",
                        " Bindusara",
                        " Ashok",
                        " Kunal"
                    ],
                    options_hi: [
                        " चंद्रगुप्त मौर्य",
                        " बिन्दुसार",
                        " अशोक",
                        " कुणाल"
                    ],
                    solution_en: "<p>42.(b) <strong>Bindusara </strong>was the second Mauryan Empire, father of Ashoka, and son of Chandragupta (founder of Mauryan empire). Syrian King Antiochus-I sent his ambassador Deimachus to the court of Bindusara.</p>",
                    solution_hi: "<p>42.(b) <strong>बिन्दुसार</strong> मौर्य साम्राज्य का दूसरा शासक था, जो अशोक का पिता और चंद्रगुप्त (मौर्य साम्राज्य के संस्थापक) का पुत्र था। सीरिया के राजा एंटिओकस प्रथम ने अपने राजदूत डाइमेकस को बिन्दुसार के दरबार में भेजा था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "43. Definite shape, clear boundaries are the characteristics of which state of matter ?",
                    question_hi: "43. निश्चित आकार, स्पष्ट सीमाएं पदार्थ की किस अवस्था की विशेषताएं हैं ?",
                    options_en: [
                        " gas",
                        " semi-solid",
                        " solid",
                        " liquid"
                    ],
                    options_hi: [
                        " गैस ",
                        " अर्ध ठोस ",
                        " ठोस ",
                        " द्रव"
                    ],
                    solution_en: "<p>43.(c) <strong>solid. </strong>This is due to the rigid arrangement of particles in a solid, where the particles have a fixed average position and are closely packed. Gasses have neither a definite shape nor clear boundaries, as their particles are randomly arranged, widely spaced, and free to move. Liquids have clear boundaries, but not a definite shape, as their particles are close together, attracted to each other, but still have some freedom of movement.</p>",
                    solution_hi: "<p>43.(c)<strong> ठोस। </strong>ऐसा ठोस में कणों की कठोर व्यवस्था के कारण होता है, जहां कणों की एक निश्चित औसत स्थिति होती है तथा वे एक दूसरे से घनिष्ठ रूप से जुड़े होते हैं। गैसों का न तो कोई निश्चित आकार होता है और न ही स्पष्ट सीमाएं होती हैं, क्योंकि उनके कण अनियमित रूप से व्यवस्थित होते हैं, व्यापक दूरी पर होते हैं, तथा गतिविधि के लिए स्वतंत्र होते हैं। तरल पदार्थों की सीमाएं स्पष्ट होती हैं, लेकिन उनका कोई निश्चित आकार नहीं होता, क्योंकि उनके कण एक दूसरे के करीब होते हैं, एक दूसरे की ओर आकर्षित होते हैं, लेकिन फिर भी उनमें गतिविधि की कुछ स्वतंत्रता होती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. What is the minimum age to become a member of Rajya Sabha ?</p>",
                    question_hi: "<p>44. राज्य सभा का सदस्य बनने के लिए न्यूनतम आयु कितनी है ?</p>",
                    options_en: [
                        "<p>35</p>",
                        "<p>40</p>",
                        "<p>25</p>",
                        "<p>30</p>"
                    ],
                    options_hi: [
                        "<p>35</p>",
                        "<p>40</p>",
                        "<p>25</p>",
                        "<p>30</p>"
                    ],
                    solution_en: "<p>44.(d) <strong>30.</strong> The Rajya Sabha, or Council of States, is the upper house of the Indian Parliament. The minimum ages for other Constitutional positions are: President (35), Governor (35), Lok Sabha member (25), Member of Legislative Assembly (25), and Member of Legislative Council (30).</p>",
                    solution_hi: "<p>44.(d)<strong> 30. </strong>राज्य सभा या राज्य परिषद भारतीय संसद का उच्च सदन है। अन्य संवैधानिक पदों के लिए न्यूनतम आयु सीमा : राष्ट्रपति (35), राज्यपाल (35), लोकसभा सदस्य (25), विधान सभा सदस्य (25), और विधान परिषद सदस्य (30)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Milk of magnesia is a suspension of _______ in water.</p>",
                    question_hi: "<p>45. मिल्क ऑफ मैग्नीशिया जल में _______का निलंबन होता है।</p>",
                    options_en: [
                        "<p>magnesium chlorate</p>",
                        "<p>magnesium bromide</p>",
                        "<p>magnesium hydroxide</p>",
                        "<p>magnesium oxalate</p>"
                    ],
                    options_hi: [
                        "<p>मैग्नीशियम क्लोरेट</p>",
                        "<p>मैग्नीशियम ब्रोमाइड</p>",
                        "<p>मैग्नीशियम हाइड्रॉक्साइड</p>",
                        "<p>मैग्नीशियम ऑक्सालेट</p>"
                    ],
                    solution_en: "<p>45.(c) <strong>Magnesium hydroxide. </strong>It is an inorganic compound. As a suspension in water, it is often called milk of magnesia because of its milk-like appearance. Name of base and Found in: Calcium hydroxide - Lime water, Ammonium hydroxide - Window cleaner, Sodium hydroxide/Potassium hydroxide - Soap.</p>",
                    solution_hi: "<p>45.(c)<strong> मैग्नीशियम हाइड्रॉक्साइड।</strong> यह एक अकार्बनिक यौगिक है। जल में निलंबन के रूप में इसे प्रायः \"मिल्क ऑफ मैग्नेशिया\" कहा जाता है, क्योंकि इसका रूप दूध जैसा होता है। क्षार का नाम और उपयोग: कैल्शियम हाइड्रॉक्साइड - चूने का पानी, अमोनियम हाइड्रॉक्साइड - विंडो क्लीनर, सोडियम हाइड्रॉक्साइड/पोटैशियम हाइड्रॉक्साइड - साबुन।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Pakyong airport is located in _______.</p>",
                    question_hi: "<p>46. पाक्योंग हवाई अड्डा _______में स्थित है।</p>",
                    options_en: [
                        "<p>Sikkim</p>",
                        "<p>Assam</p>",
                        "<p>Nagaland</p>",
                        "<p>Arunachal Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>सिक्किम</p>",
                        "<p>असम</p>",
                        "<p>नागालैंड</p>",
                        "<p>अरुणाचल प्रदेश</p>"
                    ],
                    solution_en: "<p>46.(a)<strong> Sikkim.</strong> Pakyong Airport is one of the five highest airports in India. Airports in India: Lokpriya Gopinath Bordoloi International Airport (Assam), Pasighat airport (Arunachal Pradesh), Dimapur Airport (Nagaland), Netaji Subhas Chandra Bose International Airport (Kolkata), Rajiv Gandhi International Airport (Hyderabad), Veer Savarkar International Airport (Port Blair), Dr. Babasaheb Ambedkar International Airport (Nagpur).</p>",
                    solution_hi: "<p>46.(a) <strong id=\"docs-internal-guid-5deb2772-7fff-5ea4-67b0-d7af3443607e\">सिक्किम | </strong>पाक्योंग हवाई अड्डा भारत के पाँच सबसे ऊँचे हवाई अड्डों में से एक है। भारत में हवाई अड्डे: लोकप्रिय गोपीनाथ बोरदोलोई अंतर्राष्ट्रीय हवाई अड्डा (असम), पासीघाट हवाई अड्डा (अरुणाचल प्रदेश), दीमापुर हवाई अड्डा (नागालैंड), नेताजी सुभाष चंद्र बोस अंतर्राष्ट्रीय हवाई अड्डा (कोलकाता), राजीव गांधी अंतर्राष्ट्रीय हवाई अड्डा (हैदराबाद), वीर सावरकर अंतर्राष्ट्रीय हवाई अड्डा (पोर्ट ब्लेयर), डॉ. बाबासाहेब अम्बेडकर अंतर्राष्ट्रीय हवाई अड्डा (नागपुर) आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. The term \'Green Revolution\' refers to the new agricultural technology developed during the 1950s and 1960s by a team of agricultural experts at the International Centre for Maize and Wheat Improvement in Mexico and at the International Rice Research Institute (IRRI) in ________.</p>",
                    question_hi: "<p>47. \'हरित क्रांति (Green Revolution)\' शब्द 1950 और 1960 के दशक के दौरान मेक्सिको में अंतर्राष्ट्रीय मक्का और गेहूं सुधार केंद्र (International Centre for Maize and Wheat Improvement) और ______ में अंतर्राष्ट्रीय चावल अनुसंधान संस्थान (International Rice Research Institute - IRRI) में कृषि विशेषज्ञों की एक टीम द्वारा विकसित की गई नई कृषि तकनीक को कहा जाता है।</p>",
                    options_en: [
                        "<p>Philippines</p>",
                        "<p>Mexico</p>",
                        "<p>India</p>",
                        "<p>New York</p>"
                    ],
                    options_hi: [
                        "<p>फिलीपींस</p>",
                        "<p>मेक्सिको</p>",
                        "<p>भारत</p>",
                        "<p>न्यूयॉर्क</p>"
                    ],
                    solution_en: "<p>47.(a) <strong>Philippines. </strong>Father of the Green Revolution in the World - Dr. Norman Borlaug. The technology involved the use of high yielding variety (HYV) seeds and adoption of a package of modern agricultural inputs, tools and practices. The term &lsquo;green revolution&rsquo;, was coined by Dr. William Gaud.</p>",
                    solution_hi: "<p>47.(a) <strong>फिलीपींस। </strong>विश्व में हरित क्रांति के जनक - डॉ. नॉर्मन बोरलॉग। इस तकनीक में उच्च उपज देने वाली किस्म (HYV) के बीजों का उपयोग और आधुनिक कृषि इनपुट, उपकरण और कार्यों के पैकेज को अपनाना शामिल था। \'हरित क्रांति\' शब्द डॉ. विलियम गौड द्वारा दिया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following is/are an example of public goods ?<br>a) Defense<br>b) House</p>",
                    question_hi: "<p>48. निम्नलिखित में से कौन-सा/से सार्वजनिक वस्तुओं का/के उदाहरण है/हैं ?<br>a) रक्षा<br>b) घर</p>",
                    options_en: [
                        "<p>Both a and b</p>",
                        "<p>Only b</p>",
                        "<p>Neither a nor b</p>",
                        "<p>Only a</p>"
                    ],
                    options_hi: [
                        "<p>a और b, दोनों</p>",
                        "<p>केवल b</p>",
                        "<p>न तो a और न ही b</p>",
                        "<p>केवल a</p>"
                    ],
                    solution_en: "<p>48.(d)<strong> Only a. </strong>Public goods are those that are non-excludable (difficult or impossible to restrict people from using them) and non-rivalrous (can be consumed by many people without reducing the supply of the good). For example: National Defence, Public transportation, Roads, and rivers. Private Goods: Excludable and rivalrous. For example : Ice cream, Houses, Cars etc .</p>",
                    solution_hi: "<p>48.(d) <strong>केवल a.</strong> सार्वजनिक वस्तुएँ वे हैं जो गैर-बहिष्कृत (जिन्हें लोगों द्वारा उपयोग करने से प्रतिबंधित करना कठिन या असंभव है) और गैर-प्रतिद्वंद्वी (जिनका उपभोग कई लोगों द्वारा वस्तु की आपूर्ति को कम किए बिना किया जा सके) होती हैं। उदाहरण के लिए: राष्ट्रीय रक्षा, सार्वजनिक परिवहन, सड़कें और नदियाँ। निजी वस्तुएं : ऐसी वस्तुएं जो बहिष्कृत और प्रतिद्वंदी होती है। उदाहरण: आइसक्रीम, घर, कार आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which Union Minister released the book and video on &ldquo;Sagar Parikrama&rdquo; in March 2024, in Rajkot, Gujarat ?</p>",
                    question_hi: "<p>49. किस केंद्रीय मंत्री ने मार्च 2024 में राजकोट, गुजरात में &ldquo;सागर परिक्रमा&rdquo; पर पुस्तक और वीडियो जारी किया ?</p>",
                    options_en: [
                        " Anurag Singh Thakur",
                        " Nitin Gadkari",
                        " Parshottam Rupala",
                        " Mansukh Mandaviya"
                    ],
                    options_hi: [
                        " अनुराग सिंह ठाकुर     ",
                        " नितिन गडकरी",
                        " पुरुषोत्तम रूपाला      ",
                        " मनसुख मंडाविया"
                    ],
                    solution_en: "<p>49.(c) <strong>Parshottam Rupala. </strong>The book and video document the Sagar Parikrama Yatra, which covered 7,986 kilometers of India&rsquo;s coastal length, visiting 3,071 fishing villages across 80 coastal districts to highlight the development of the coastal and fisheries sector.</p>",
                    solution_hi: "<p>49.(c) <strong>पुरुषोत्तम रूपाला। </strong>पुस्तक और वीडियो सागर परिक्रमा यात्रा का दस्तावेज है, जिसने भारत की तटीय लंबाई के 7,986 किलोमीटर को कवर किया, तटीय और मत्स्य पालन क्षेत्र के विकास को उजागर करने के लिए 80 तटीय जिलों में 3,071 मछली पकड़ने वाले गांवों का दौरा किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. What is the colour of the antenna used on the volleyball net ?</p>",
                    question_hi: "<p>50. वॉलीबॉल नेट पर प्रयुक्त एंटीना का रंग कैसा होता है ?</p>",
                    options_en: [
                        "<p>Red and white</p>",
                        "<p>Yellow and black</p>",
                        "<p>Black and white</p>",
                        "<p>Green and white</p>"
                    ],
                    options_hi: [
                        "<p>लाल और सफेद</p>",
                        "<p>पीला और काला</p>",
                        "<p>काला और सफेद</p>",
                        "<p>हरा और सफेद</p>"
                    ],
                    solution_en: "<p>50.(a) <strong>Red and white.</strong> Antennas are used to help players and referees determine if a ball is in or out of bounds. Court Dimensions: Court = 18 &times; 9m; Diagonal = 20.12m; Attack line = 3m from centre. Ball: circumference is 65&ndash;67 cm and its weight is 260&ndash;280 grams. Net top is set at the height of 2.43 m for men and 2.24 m. for women. Height is measured at the centre of the court. There are two teams comprising 6 players each on the court. Volleyball was invented by William G. Morgan in the year 1895.</p>",
                    solution_hi: "<p>50.(a)<strong> लाल और सफेद।</strong> एंटेना का उपयोग खिलाड़ियों और रेफरी को यह निर्धारित करने में सहायता करने के लिए किया जाता है कि गेंद सीमा के अंदर है या बाहर। कोर्ट के आयाम: कोर्ट = 18 &times; 9 मीटर; विकर्ण = 20.12 मीटर; अटैक लाइन = केंद्र से 3 मीटर। गेंद: परिधि 65-67 सेमी और वजन 260-280 ग्राम। नेट टॉप पुरुषों के लिए 2.43 मीटर और महिलाओं के लिए 2.24 मीटर की ऊंचाई पर सेट किया गया है। ऊंचाई, कोर्ट के केंद्र में मापी जाती है। कोर्ट पर 6-6 खिलाड़ियों वाली दो टीमें होती हैं। वॉलीबॉल का आविष्कार विलियम जी मॉर्गन ने वर्ष 1895 में किया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. In a triangle ABC, BD is perpendicular to AC. E is a point on BC such that angle BEA = x&deg;. If angle EAC = 38&deg; and angle EBD = 40&deg;, then the value of x is:</p>",
                    question_hi: "<p>51. त्रिभुज ABC में, BD, AC पर लंबवत है। E, BC पर एक बिंदु इस प्रकार है कि कोण BEA = x&deg; है। यदि कोण EAC = 38&deg; और कोण EBD = 40&deg; है, तो x का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>88</p>",
                        "<p>82</p>",
                        "<p>48</p>",
                        "<p>96</p>"
                    ],
                    options_hi: [
                        "<p>88</p>",
                        "<p>82</p>",
                        "<p>48</p>",
                        "<p>96</p>"
                    ],
                    solution_en: "<p>51.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091959.png\" alt=\"rId45\" width=\"209\" height=\"175\"><br>In the right angle <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>AFD,<br>38&deg; + 90&deg; + &ang;ADF = 180&deg;<br>&ang;ADF = 52&deg;<br>Now, &ang;ADF = &ang;BDE = 52&deg;&nbsp; &nbsp; &nbsp; [vertically opposite angle]<br>In the <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>BDE<br>40&deg; + 52&deg; + &ang;BED = 180&deg;<br>&ang;BED = 88&deg;</p>",
                    solution_hi: "<p>51.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253091959.png\" alt=\"rId45\" width=\"209\" height=\"175\"><br>समकोण <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>AFD में,<br>38&deg; + 90&deg; + &ang;ADF = 180&deg;<br>&ang;ADF = 52&deg;<br>अब, &ang;ADF = &ang;BDE = 52&deg;&nbsp; &nbsp; &nbsp; &nbsp; [शीर्षाभिमुख कोण]<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8710;</mo></math>BDE में,<br>40&deg; + 52&deg; + &ang;BED = 180&deg;<br>&ang;BED = 88&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. A pipe can fill an overhead tank in 12 hours. But due to a leak at the bottom, it is filled in 18 hours. If the tank is full, how much time will the leak take to empty it ?</p>",
                    question_hi: "<p>52. एक पाइप एक ओवरहेड टैंक को 12 घंटे में भर सकता है। लेकिन तली में रिसाव के कारण टैंक 18 घंटे में भरता है। यदि टैंक भरा हुआ है, तो रिसाव के कारण इसे खाली होने में कितना समय लगेगा ?</p>",
                    options_en: [
                        "<p>3.6 hours</p>",
                        "<p>63 hours</p>",
                        "<p>7.2 hours</p>",
                        "<p>36 hours</p>"
                    ],
                    options_hi: [
                        "<p>3.6 घंटे</p>",
                        "<p>63 घंटे</p>",
                        "<p>7.2 घंटे</p>",
                        "<p>36 घंटे</p>"
                    ],
                    solution_en: "<p>52.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253092167.png\" alt=\"rId46\" width=\"280\" height=\"126\"><br>Efficiency of leak = 4 - 6 = - 2 unit<br>Total time taken by leak to empty the full tank = <math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 36 hours</p>",
                    solution_hi: "<p>52.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253092307.png\" alt=\"rId47\" width=\"289\" height=\"152\"><br>रिसाव की क्षमता = 4 - 6 = - 2 इकाई<br>रिसाव द्वारा पूरी टंकी को खाली करने में लगा कुल समय =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>72</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 36 घंटे</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Which of following numbers is NOT divisible by 11 ?</p>",
                    question_hi: "<p>53. निम्नलिखित में से कौन-सी संख्या 11 से विभाज्य नहीं है ?</p>",
                    options_en: [
                        " 109153",
                        "  5445",
                        " 128041",
                        "  80124"
                    ],
                    options_hi: [
                        " 109153",
                        "  5445",
                        " 128041",
                        "  80124"
                    ],
                    solution_en: "<p>53.(c) <br><strong>Divisibility by 11 :</strong> if the difference between the sum of the digits in the odd places and the sum of the digits in the even places is 0 or a multiple of 11.<br>By checking all the options one by one, we get only option (c) that satisfies the rule.<br>128041<br>(1 + 8 + 4) - (2 + 0 + 1) = 13 - 3 =10 <math display=\"inline\"><mo>&#8800;</mo></math> 0 or 11k.<br>Hence 128041 is not divisible by 11.</p>",
                    solution_hi: "<p>53.(c) <br><strong>11 से विभाज्यता : </strong>यदि विषम स्थानों के अंकों के योग और सम स्थानों के अंकों के योग के बीच का अंतर 0 या 11 का गुणज है।<br>एक-एक करके सभी विकल्पों की जांच करने पर, हमें केवल विकल्प (c) मिलता है जो नियम को पूरा करता है।<br>128041<br>(1 + 8 + 4) - (2 + 0 + 1) = 13 - 3 =10 <math display=\"inline\"><mo>&#8800;</mo></math> 0 or 11k.<br>अतः 128041, 11 से विभाज्य नहीं है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. A man can row 18 km/h in still water. It takes him two times as long to row up as to row&nbsp;down the river. Find the rate of the stream.</p>",
                    question_hi: "<p>54. एक आदमी स्थिर जल में 18 km/h की चाल से नाव चला सकता है। उसे नदी में धारा के प्रतिकूल नाव चलाने में, धारा के अनुकूल नाव चलाने में लगने वाले समय से दोगुना समय लगता है। धारा की चाल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>9 km/h</p>",
                        "<p>12 km/h</p>",
                        "<p>24 km/h</p>",
                        "<p>6 km/h</p>"
                    ],
                    options_hi: [
                        "<p>9 km/h</p>",
                        "<p>12 km/h</p>",
                        "<p>24 km/h</p>",
                        "<p>6 km/h</p>"
                    ],
                    solution_en: "<p>54.(d)<br>Let the speed of the boat and stream be x&nbsp;and y km/h respectively.<br>Ratio&nbsp; &nbsp; - (x&nbsp;- y) : (x + y)<br>Time&nbsp; &nbsp; -&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;1<br>Speed -&nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;2<br>Speed of the boat = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> units <br>Speed of the stream = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math>) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> units&nbsp;<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> units = 18 km/h<br>(Speed of the stream) <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> units = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>3</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 6 km/h</p>",
                    solution_hi: "<p>54.(d)<br>माना नाव और धारा की गति क्रमशः x&nbsp;और y किमी/घंटा है।<br>अनुपात - (x&nbsp;- y) : (x + y)<br>समय&nbsp; &nbsp; -&nbsp; &nbsp; &nbsp;2&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;1<br>गति&nbsp; &nbsp; &nbsp; -&nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;2<br>नाव की गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>&nbsp;इकाई<br>धारा की गति = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math>) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>&nbsp;इकाई<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> इकाई = 18 किमी/घंटा<br>(धारा की गति) <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>3</mn></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 6 किमी/घंटा</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The height of a house is h and 5.2 m long ladder is just sufficient to go at the top of the house when its inclination with the horizontal ground is 45&deg;. Find the height of the house.</p>",
                    question_hi: "<p>55. एक घर की ऊंचाई h है और 5.2 मीटर लंबी सीढ़ी घर के शीर्ष पर जाने के लिए पर्याप्त है जब क्षैतिज जमीन के साथ इसका झुकाव 45&deg; है। घर की ऊंचाई ज्ञात करे ।</p>",
                    options_en: [
                        "<p>14 m</p>",
                        "<p>10.4 m</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math>m</p>",
                        "<p>13 m</p>"
                    ],
                    options_hi: [
                        "<p>14 m</p>",
                        "<p>10.4 m</p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn><msqrt><mn>2</mn></msqrt></mrow><mrow><mn>5</mn></mrow></mfrac></math>m</p>",
                        "<p>13 m</p>"
                    ],
                    solution_en: "<p>55.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253092410.png\" alt=\"rId48\" width=\"158\" height=\"126\"><br>Sin 45&deg; = <math display=\"inline\"><mfrac><mrow><mi>h</mi></mrow><mrow><mn>5</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>h</mi><mrow><mn>5</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; h = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>.</mo><mn>2</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>52</mn><msqrt><mn>2</mn></msqrt></mrow><mn>20</mn></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>13</mn><msqrt><mn>2</mn></msqrt></mrow><mn>5</mn></mfrac></mstyle></math>m</p>",
                    solution_hi: "<p>55.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253092410.png\" alt=\"rId48\" width=\"158\" height=\"126\"><br>Sin 45&deg; = <math display=\"inline\"><mfrac><mrow><mi>h</mi></mrow><mrow><mn>5</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>h</mi><mrow><mn>5</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math><br>&rArr; h = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>.</mo><mn>2</mn></mrow><msqrt><mn>2</mn></msqrt></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>52</mn><msqrt><mn>2</mn></msqrt></mrow><mn>20</mn></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>13</mn><msqrt><mn>2</mn></msqrt></mrow><mn>5</mn></mfrac></mstyle></math>m</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A and B start running on a circular race track whose length is 3240 m. A and B start from the same point and at the same time, but in opposite directions. If A runs at a speed of 10 m/s and they cross each other 3 minutes after they start running, then what is B&rsquo;s speed (in m/s) ?</p>",
                    question_hi: "<p>56. A और B एक वृत्ताकार रेस ट्रैक पर दौड़ना शुरू करते हैं जिसकी लंबाई 3240 m है। A और B एक ही बिंदु से और एक ही समय पर, लेकिन विपरीत दिशाओं में दौड़ना शुरू करते हैं। यदि A, 10 m/s की चाल से दौड़ता है और दौड़ शुरू करने के 3 मिनट बाद वे एक दूसरे को पार करते हैं, तो B की चाल (m/s में) कितनी है ?</p>",
                    options_en: [
                        "<p>8.5</p>",
                        "<p>9</p>",
                        "<p>7.5</p>",
                        "<p>8</p>"
                    ],
                    options_hi: [
                        "<p>8.5</p>",
                        "<p>9</p>",
                        "<p>7.5</p>",
                        "<p>8</p>"
                    ],
                    solution_en: "<p>56.(d) Let speed of B = xm/s<br>According to question , <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3240</mn><mrow><mn>10</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math> = 3 &times; 60 <br>&rArr; 3240 = 1800 + 180x<br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1440</mn><mn>180</mn></mfrac></math> = 8<br><math display=\"inline\"><mo>&#8756;</mo></math> Speed of B = 8m/s</p>",
                    solution_hi: "<p>56.(d) माना B की गति = xm/s<br>प्रश्न के अनुसार, <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3240</mn><mrow><mn>10</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math> = 3 &times; 60 <br>&rArr; 3240 = 1800 + 180x<br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1440</mn><mn>180</mn></mfrac></math> = 8<br><math display=\"inline\"><mo>&#8756;</mo></math> B की गति = 8m/s</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. Simplify the following expression 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &divide; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> + 3 of 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> + 2.5 &times; 2.4 &divide; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> - 4 of 3</p>",
                    question_hi: "<p>57. निम्नलिखित व्यंजक 3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &divide; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> + 3 of 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> + 2.5 &times; 2.4 &divide; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;- 4 of 3 को सरल कीजिए</p>",
                    options_en: [
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>57.(c)<br>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &divide; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> + 3 of 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> + 2.5 &times; 2.4 &divide; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> - 4 of 3<br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>7</mn></mfrac></math>+ 3 of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> + 2.5 &times; 2.4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math> - 12<br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>11</mn></mfrac></math> + 8 + 6 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math> - 12<br>2 + 8 + 1.8 - 12<br>= - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>57.(c)<br>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &divide; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>7</mn></mfrac></math> + 3 of 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math> + 2.5 &times; 2.4 &divide; 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> - 4 of 3<br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>7</mn></mfrac></math>+ 3 of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math> + 2.5 &times; 2.4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math> - 12<br><math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>11</mn></mfrac></math> + 8 + 6 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>10</mn></mfrac></math> - 12<br>2 + 8 + 1.8 - 12<br>= - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. If cot &theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>11</mn></msqrt></math>, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>58. यदि cot&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>11</mn></msqrt></math> हो, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>-</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi></mrow><mrow><mi>cos</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi><mo>+</mo><mi>s</mi><mi>e</mi><msup><mi>c</mi><mn>2</mn></msup><mi>&#952;</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>58.(b)<br>cot&theta;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>11</mn></msqrt><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>b</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow></mfrac></math><br><math display=\"inline\"><mi>h</mi></math>ypotenuse (h) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><msqrt><mn>11</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mn>1</mn><mn>2</mn></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>12</mn></msqrt></math><br>Now, <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">p</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">b</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">p</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">b</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math>&nbsp;<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><msqrt><mn>12</mn></msqrt><mn>1</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mfrac><msqrt><mn>12</mn></msqrt><msqrt><mn>11</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mfrac><msqrt><mn>12</mn></msqrt><mn>1</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><msqrt><mn>12</mn></msqrt><msqrt><mn>11</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>-</mo><mfrac><mn>12</mn><mn>11</mn></mfrac></mrow><mrow><mn>12</mn><mo>+</mo><mfrac><mn>12</mn><mn>11</mn></mfrac></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>132</mn><mo>-</mo><mn>12</mn></mrow><mrow><mn>132</mn><mo>+</mo><mn>12</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>144</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></p>",
                    solution_hi: "<p>58.(b)<br>cot&theta;&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>11</mn></msqrt><mn>1</mn></mfrac></math> = <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2354;&#2306;&#2348;</mi></mfrac></math><br>कर्ण (h) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><msqrt><mn>11</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mn>1</mn><mn>2</mn></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>12</mn></msqrt></math><br>अब ,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>-</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>sec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">p</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">b</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">p</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><mi mathvariant=\"normal\">h</mi><mi mathvariant=\"normal\">b</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math>&nbsp;<br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mo>(</mo><mfrac><msqrt><mn>12</mn></msqrt><mn>1</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mfrac><msqrt><mn>12</mn></msqrt><msqrt><mn>11</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><mfrac><msqrt><mn>12</mn></msqrt><mn>1</mn></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>+</mo><msup><mrow><mo>(</mo><mfrac><msqrt><mn>12</mn></msqrt><msqrt><mn>11</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>-</mo><mfrac><mn>12</mn><mn>11</mn></mfrac></mrow><mrow><mn>12</mn><mo>+</mo><mfrac><mn>12</mn><mn>11</mn></mfrac></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>132</mn><mo>-</mo><mn>12</mn></mrow><mrow><mn>132</mn><mo>+</mo><mn>12</mn></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>144</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Madhav purchased an item for ₹42,000 and sold it at a loss of 20%. With that amount, he purchased another item and sold it at a gain of 30%. What is the overall gain (in ₹) ?</p>",
                    question_hi: "<p>59. माधव ने ₹42,000 में एक वस्तु खरीदी और उसे 20% की हानि पर बेच दिया। उस राशि से उसने एक और वस्तु खरीदी और उसे 30% के लाभ पर बेच दिया। कुल लाभ (₹ में) कितना है ?</p>",
                    options_en: [
                        "<p>6720</p>",
                        "<p>4200</p>",
                        "<p>1680</p>",
                        "<p>2520</p>"
                    ],
                    options_hi: [
                        "<p>6720</p>",
                        "<p>4200</p>",
                        "<p>1680</p>",
                        "<p>2520</p>"
                    ],
                    solution_en: "<p>59.(c)<br>Overall gain% = 30 - 20 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math> <br>= 10 - 6 = 4%<br>Hence, overall gain = 42000 &times; 4% = Rs.1680</p>",
                    solution_hi: "<p>59.(c)<br>कुल लाभ% = 30 - 20 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#215;</mo><mn>20</mn></mrow><mn>100</mn></mfrac></math><br>= 10 - 6 = 4%<br>अतः, कुल लाभ = 42000 &times; 4% = Rs.1680</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Rajnish borrowed ₹1,500 from a bank and repaid the entire amount with interest in two equal annual installments, the first installment being paid a year after Rajnish borrowed from the bank. If the rate of interest was 40% per annum, compounded annually, then what was the value (in ₹) of each installment paid by Rajnish ?</p>",
                    question_hi: "<p>60. रजनीश ने एक बैंक से ₹ 1,500 की राशि उधार ली और दो समान वार्षिक किश्तों में ब्याज सहित पूरी राशि चुका दी, जहा रजनीश ने बैंक से उधार लेने के एक वर्ष बाद पहली किस्त का भुगतान किया। यदि ब्याज की दर 40% वार्षिक थी, और ब्याज वार्षिक रूप से संयोजित होता था, तो रजनीश द्वारा भुगतान की गई प्रत्येक किस्त का मूल्य (₹ में) ज्ञात करे I</p>",
                    options_en: [
                        "<p>1125</p>",
                        "<p>1470</p>",
                        "<p>1225</p>",
                        "<p>1350</p>"
                    ],
                    options_hi: [
                        "<p>1125</p>",
                        "<p>1470</p>",
                        "<p>1225</p>",
                        "<p>1350</p>"
                    ],
                    solution_en: "<p>60.(c) R = 40 % = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>CI</mi><mi>Principal</mi></mfrac></math><br>Ratio&nbsp; &nbsp; &nbsp; &rarr; Principal : Installment<br>1st year &rarr;&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;7 <strong>)&times; 7</strong> <br>2nd year &rarr;&nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;49<br>-------------------------------------------<br>Final&nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; 60&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 98<br>60 units (Principal) = ₹ 1500<br>49 units (Installment) = ₹ 1225</p>",
                    solution_hi: "<p>60.(c) <br>दर = 40 % = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</mi><mo>&#160;</mo><mi>&#2348;&#2381;&#2351;&#2366;&#2332;</mi></mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi></mfrac></math> <br>अनुपात&nbsp; &nbsp; &nbsp;&rarr;&nbsp; मूलधन&nbsp; :&nbsp; किश्त<br>पहला वर्ष&nbsp; &rarr;&nbsp; &nbsp; &nbsp; 5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;7 <strong>)&times;7</strong> <br>दूसरा वर्ष&nbsp; &rarr;&nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; :&nbsp; &nbsp; 49 <br>--------------------------------------<br>अंतिम&nbsp; &nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp; 60&nbsp; &nbsp; :&nbsp; &nbsp;98<br>60 इकाई (मूलधन ) = ₹ 1500 <br>49 इकाई (किस्त) = ₹ 1225</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The volume of a right circular cylindrical box of radius 30cm is 600cm<sup>3</sup> . Find the height of the box.</p>",
                    question_hi: "<p>61. 30 cm त्रिज्या वाले एक लम्ब वृत्तीय बेलनाकार बॉक्स का आयतन 600 cm<sup>3</sup> है। बॉक्स की ऊंचाई ज्ञात करें।</p>",
                    options_en: [
                        " 0.61cm",
                        " 0.31cm ",
                        " 0.51cm  ",
                        " 0.21cm"
                    ],
                    options_hi: [
                        " 0.61cm",
                        " 0.31cm ",
                        " 0.51cm  ",
                        " 0.21cm"
                    ],
                    solution_en: "<p>61.(d) Volume of a right circular cylindrical box (&pi;R<sup>2</sup>h) = 600cm<sup>3</sup><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 30 &times; 30 &times; h = 600<br>&rArr; h = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>600</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>22</mn><mo>&#215;</mo><mn>30</mn><mo>&#215;</mo><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>33</mn></mfrac></math> = 0.21cm</p>",
                    solution_hi: "<p>61.(d) एक लम्ब वृत्ताकार बेलनाकार बक्से का आयतन (&pi;R<sup>2</sup>h) = 600cm<sup>3</sup><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 30 &times; 30 &times; h = 600<br>&rArr; h = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>600</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>22</mn><mo>&#215;</mo><mn>30</mn><mo>&#215;</mo><mn>30</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>33</mn></mfrac></math> = 0.21cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. If a + b + c = 15 and ab + bc + ca = 35 then find the value of a<sup>3</sup> + b<sup>3</sup> + c<sup>3 </sup>- 3abc.</p>",
                    question_hi: "<p>62. यदि a + b + c = 15 और ab + bc + ca = 35 है, तो a<sup>3</sup> + b<sup>3</sup> + c<sup>3 </sup>- 3abc का मान ज्ञात करें।</p>",
                    options_en: [
                        "<p>2100</p>",
                        "<p>1500</p>",
                        "<p>1200</p>",
                        "<p>1800</p>"
                    ],
                    options_hi: [
                        "<p>2100</p>",
                        "<p>1500</p>",
                        "<p>1200</p>",
                        "<p>1800</p>"
                    ],
                    solution_en: "<p>62.(d)<br>a<sup>3</sup> + b<sup>3</sup> + c<sup>3 </sup>- 3abc = (a + b + c) [(a + b + c)<sup>2</sup> - 3(ab + bc + ca)] <br>= (15) [(15)<sup>2</sup>&nbsp;- 3(35)] <br>= (15) [225 - 105] <br>= 15 &times;&nbsp;120 <br>= 1800</p>",
                    solution_hi: "<p>62.(d)<br>a<sup>3</sup> + b<sup>3</sup> + c<sup>3 </sup>- 3abc = (a + b + c) [(a + b + c)<sup>2</sup> - 3(ab + bc + ca)] <br>= (15) [(15)<sup>2</sup>&nbsp;- 3(35)] <br>= (15) [225 - 105] <br>= 15 &times;&nbsp;120 <br>= 1800</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If the numbers X + 2, X + 5, 2X &minus; 3 and 3X &minus; 5 are in proportion, then which of the following pairs of values is possible for X ?</p>",
                    question_hi: "<p>63. यदि संख्याएँ X + 2, X + 5, 2X &minus; 3 और 3X &minus; 5 समानुपात में हैं, तो निम्नलिखित में से किस युग्म वाले मान X के लिए संभव है ?</p>",
                    options_en: [
                        "<p>3 and 2</p>",
                        "<p>5 and 3</p>",
                        "<p>5 and 1</p>",
                        "<p>4 and 2</p>"
                    ],
                    options_hi: [
                        "<p>3 और 2</p>",
                        "<p>5 और 3</p>",
                        "<p>5 और 1</p>",
                        "<p>4 और 2</p>"
                    ],
                    solution_en: "<p>63.(c) According to question ,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mo>+</mo><mn>2</mn></mrow><mrow><mi>X</mi><mo>+</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>X</mi><mo>-</mo><mn>3</mn></mrow><mrow><mn>3</mn><mi>X</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math><br>&rArr; (X + 2) (3X - 5) = (2X - 3) (X + 5)<br>&rArr; X<sup>2</sup> - 6X + 5 = 0<br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mo>(</mo><mo>-</mo><mn>6</mn><mo>)</mo><mo>&#177;</mo><msqrt><msup><mrow><mo>(</mo><mo>-</mo><mn>6</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>1</mn><mo>&#215;</mo><mn>5</mn></msqrt></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>1</mn></mrow></mfrac></math> <br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#177;</mo><msqrt><mn>36</mn><mo>-</mo><mn>20</mn></msqrt></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#177;</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> <br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>+</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> ,<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>-</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> = 5 , 1</p>",
                    solution_hi: "<p>63.(c) प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>X</mi><mo>+</mo><mn>2</mn></mrow><mrow><mi>X</mi><mo>+</mo><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>X</mi><mo>-</mo><mn>3</mn></mrow><mrow><mn>3</mn><mi>X</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math><br>&rArr; (X + 2) (3X - 5) = (2X - 3) (X + 5)<br>&rArr; X<sup>2</sup> - 6X + 5 = 0<br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mo>(</mo><mo>-</mo><mn>6</mn><mo>)</mo><mo>&#177;</mo><msqrt><msup><mrow><mo>(</mo><mo>-</mo><mn>6</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn><mo>&#215;</mo><mn>1</mn><mo>&#215;</mo><mn>5</mn></msqrt></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>1</mn></mrow></mfrac></math> <br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#177;</mo><msqrt><mn>36</mn><mo>-</mo><mn>20</mn></msqrt></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#177;</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> <br>X = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>+</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> ,<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>-</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> = 5 , 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If the radii of two circles are 6 cm and 3 cm and the length of the transverse common tangent is 8 cm, then the distance between the centers of both circles is:</p>",
                    question_hi: "<p>64. यदि दो वृत्तों की त्रिज्याएं 6 cm और 3 cm हैं तथा अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा (transverse common tangent) की लंबाई 8 cm है, तो दोनो वृत्तों के केन्द्रो के बीच की दुरी ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>145</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>141</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>147</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>143</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>145</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>141</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>147</mn></msqrt></math></p>",
                        "<p><math display=\"inline\"><msqrt><mn>143</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>64.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253092539.png\" alt=\"rId49\" width=\"293\" height=\"148\"><br>Transverse common tangent (l) = 8 <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>R</mi><mo>+</mo><mi>r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = 8<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>6</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = 8<br><math display=\"inline\"><msqrt><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>81</mn></msqrt></math> = 8<br>d<sup>2</sup> - 81 = 64&nbsp;<br>d<sup>2</sup> = 64 + 81<br>= 145<br><math display=\"inline\"><mi>d</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>145</mn></msqrt></math></p>",
                    solution_hi: "<p>64.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253092539.png\" alt=\"rId49\" width=\"293\" height=\"148\"><br>अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा = <math display=\"inline\"><mn>8</mn></math> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>R</mi><mo>+</mo><mi>r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = 8<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>6</mn><mo>+</mo><mn>3</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = 8<br><math display=\"inline\"><msqrt><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>81</mn></msqrt></math> = 8<br>d<sup>2</sup> - 81 = 64&nbsp;<br>d<sup>2</sup> = 64 + 81<br>= 145<br><math display=\"inline\"><mi>d</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>145</mn></msqrt></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Raj prepares a 60 gm mixture by combining two ingredients X and Y. The cost of ingredient X is ₹80 per 5 gm, and the cost of ingredient Y is ₹80 per 10 gm. Ingredients X and Y are mixed in a manner that the cost of the resulting mixture is ₹14 per gm. What is the quantity of ingredient X (in gm) in the mixture ?</p>",
                    question_hi: "<p>65. राज दो सामग्रियों X और Y को मिलाकर 60 gm मिश्रण तैयार करता है। सामग्री X की कीमत ₹80 प्रति 5 gm है और सामग्री Y की कीमत ₹80 प्रति 10 gm है। सामग्री X और Y को इस प्रकार मिश्रित किया जाता है कि परिणामी मिश्रण की कीमत ₹14 प्रति gm हो। मिश्रण में सामग्री X की मात्रा (gm में) क्या है ?</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>45</p>",
                        "<p>15</p>",
                        "<p>60</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>45</p>",
                        "<p>15</p>",
                        "<p>60</p>"
                    ],
                    solution_en: "<p>65.(b) Cost of ingredient X in 1 gm = <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = ₹ 16<br>Cost of ingredient Y in 1 gm = <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = ₹ 8<br>Cost of ingredient X and Y in 1 gm = ₹ 14<br>By using allegation method,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253092701.png\" alt=\"rId50\" width=\"169\" height=\"189\"><br>4 unit = 60 gm<br>3 unit = 45 gm<br>Hence, Quantity of ingredient of X is 45 gm</p>",
                    solution_hi: "<p>65.(b) 1 ग्राम में सामग्री X की कीमत = <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = ₹ 16<br>1 ग्राम में सामग्री Y की कीमत =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = ₹ 8<br>1 ग्राम में सामग्री X और Y की कीमत = ₹ 14<br>एलिगेशन विधि का प्रयोग करके,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739253092701.png\" alt=\"rId50\" width=\"169\" height=\"189\"><br>4 इकाई = 60 ग्राम<br>3 इकाई = 45 ग्राम<br>अतः, सामग्री X&nbsp; की मात्रा 45 ग्राम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Avinash can make a machine eleven times as fast as Bipin and takes 100 days less than Bipin to make the machine. In how many days can Avinash and Bipin build the machine individually ?</p>",
                    question_hi: "<p>66. अविनाश, बिपिन से ग्यारह गुना तेजी से एक मशीन बना सकता है और मशीन बनाने में उसे बिपिन से 100 दिन कम का समय लगता है। अविनाश और बिपिन अकेले कितने दिनों में मशीन बना सकते हैं ?</p>",
                    options_en: [
                        "<p>20 and 120 Days</p>",
                        "<p>10 and 110 Days</p>",
                        "<p>15 and 105 Days</p>",
                        "<p>15 and 115 Days</p>"
                    ],
                    options_hi: [
                        "<p>20 और 120 दिन</p>",
                        "<p>10 और 110 दिन</p>",
                        "<p>15 और 105 दिन</p>",
                        "<p>15 और 115 दिन</p>"
                    ],
                    solution_en: "<p>66.(b)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Avinash&nbsp; &nbsp;Bipin<br>Efficiency &rarr;&nbsp; &nbsp; &nbsp; 11&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;1<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;11<br>According to question,<br>&rArr; (11 - 1) unit = 100 days<br>&rArr; 1 unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>10</mn></mfrac></math> = 10 days<br>Time taken by Avinash alone to build the machine = 1 unit = 10 days<br>Time taken by Bipin alone to build the machine = 11 unit = 11 &times; 10 = 110 days</p>",
                    solution_hi: "<p>66.(b)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;अविनाश&nbsp; &nbsp;बिपिन<br>क्षमता&nbsp; &rarr;&nbsp; &nbsp; 11&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 1<br>समय&nbsp; &nbsp;&rarr;&nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; 11<br>प्रश्न के अनुसार,<br>&rArr; (11 - 1) इकाई = 100 दिन<br>&rArr; 1 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>10</mn></mfrac></math> = 10 दिन<br>अविनाश द्वारा अकेले मशीन बनाने में लगा समय = 1 इकाई = 10 दिन<br>बिपिन द्वारा अकेले मशीन बनाने में लगा समय = 11 इकाई = 11 &times; 10 = 110 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A car travels at a uniform speed of 45 km/h for 210 km and then travels for 420 km at a uniform speed of 70 km/h. What is the average speed of the car during the entire journey ?</p>",
                    question_hi: "<p>67. एक कार 45 km/h की एक समान चाल से 210 km यात्रा करती है और फिर 70 km/h की एक समान चाल से 420 km यात्रा करती है। पूरी यात्रा के दौरान कार की औसत चाल क्या है ?</p>",
                    options_en: [
                        "<p>63 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> km/h</p>",
                        "<p>59 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> km/h</p>",
                        "<p>59 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math>km/h</p>",
                        "<p>63 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math>km/h</p>"
                    ],
                    options_hi: [
                        "<p>63 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> km/h</p>",
                        "<p>59 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math> km/h</p>",
                        "<p>59 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math>km/h</p>",
                        "<p>63 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math>km/h</p>"
                    ],
                    solution_en: "<p>67.(b) Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>210</mn><mo>+</mo><mn>420</mn></mrow><mrow><mfrac><mn>210</mn><mn>45</mn></mfrac><mo>+</mo><mfrac><mn>420</mn><mn>70</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>630</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mn>14</mn><mn>3</mn></mfrac></mstyle><mo>+</mo><mn>6</mn></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>630</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>14</mn><mo>+</mo><mn>18</mn></mrow></mfrac></math>&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>945</mn><mn>16</mn></mfrac></math> = 59<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>16</mn></mfrac></math> km/h</p>",
                    solution_hi: "<p>67.(b) औसत गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>210</mn><mo>+</mo><mn>420</mn></mrow><mrow><mfrac><mn>210</mn><mn>45</mn></mfrac><mo>+</mo><mfrac><mn>420</mn><mn>70</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>630</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mn>14</mn><mn>3</mn></mfrac></mstyle><mo>+</mo><mn>6</mn></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>630</mn><mo>&#215;</mo><mn>3</mn></mrow><mrow><mn>14</mn><mo>+</mo><mn>18</mn></mrow></mfrac></math>&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>945</mn><mn>16</mn></mfrac></math> = 59<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>16</mn></mfrac></math> km/h</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. A woman spends <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> of her income. If her income increases by 18% and the expenditure increases by 24%, then the percentage increase in her savings will be:</p>",
                    question_hi: "<p>68. एक महिला अपनी आय का <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> व्यय करती है। यदि उसकी आय में 18% की वृद्धि होती है और व्यय में 24% की वृ&zwnj;द्धि होती है, तो उसकी बचत में कितने प्रतिशत की वृद्धि होगी ?</p>",
                    options_en: [
                        "<p>6%</p>",
                        "<p>3%</p>",
                        "<p>5%</p>",
                        "<p>4%</p>"
                    ],
                    options_hi: [
                        "<p>6%</p>",
                        "<p>3%</p>",
                        "<p>5%</p>",
                        "<p>4%</p>"
                    ],
                    solution_en: "<p>68.(a)<br>Let the income and expenditure of the woman be 300 &amp; 200 respectively<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Income = Expenditure + Saving<br>Initial&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;300&nbsp; &nbsp;=&nbsp; &nbsp; &nbsp; &nbsp; 200&nbsp; &nbsp; &nbsp; &nbsp;+&nbsp; &nbsp; 100<br>% increase&nbsp; 54&nbsp; &nbsp; &nbsp;=&nbsp; &nbsp; &nbsp; &nbsp; 48&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;+&nbsp; &nbsp; &nbsp;6<br>%increase in savings = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 6%</p>",
                    solution_hi: "<p>68.(a)<br>माना महिला का आय और व्यय क्रमशः 300 और 200 है.<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;आय&nbsp; =&nbsp; &nbsp;व्यय&nbsp; +&nbsp; बचत<br>प्रारम्भिक&nbsp; &nbsp; &nbsp; &nbsp;300&nbsp; =&nbsp; &nbsp;200&nbsp; +&nbsp; 100<br>वृद्धि के बाद&nbsp; &nbsp; 54&nbsp; &nbsp;=&nbsp; &nbsp; 48&nbsp; &nbsp;+&nbsp; &nbsp;6<br>बचत में प्रतिशत वृद्धि होगी = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mi>&#160;</mi></math>&times; 100 = 6%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The cost price of two articles is equal. One article is sold at a profit of 12% and the other article for ₹3,600 more than the first. If the net profit is 15<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>51</mn></mfrac></math>%&nbsp;, then what is the cost price of each article ?</p>",
                    question_hi: "<p>69. दो वस्तुओं का क्रय मूल्य बराबर है। एक वस्तु को 12% के लाभ पर और दूसरी वस्तु को पहली वस्तु से ₹3,600 अधिक में बेचा जाता है I यदि कुल लाभ 15<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>51</mn></mfrac></math>% है, तो प्रत्येक वस्तु का क्रय मूल्य क्या है ?</p>",
                    options_en: [
                        "<p>₹50,000</p>",
                        "<p>₹51,000</p>",
                        "<p>₹50,990</p>",
                        "<p>₹52,150</p>"
                    ],
                    options_hi: [
                        "<p>₹50,000</p>",
                        "<p>₹51,000</p>",
                        "<p>₹50,990</p>",
                        "<p>₹52,150</p>"
                    ],
                    solution_en: "<p>69.(b) let profit of 1<sup>st</sup> article = x% and profit of 2<sup>nd</sup> article = y%<br>Net profit% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow><mn>2</mn></mfrac></math><br>15<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>51</mn></mfrac></math>%&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mi>y</mi></mrow><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>792</mn></mrow><mrow><mn>51</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mi>y</mi></mrow><mn>2</mn></mfrac></math><br>12 + y&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1584</mn><mn>51</mn></mfrac></math><br>y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1584</mn><mo>-</mo><mn>612</mn></mrow><mn>51</mn></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>972</mn></mrow><mrow><mn>51</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>324</mn><mn>17</mn></mfrac></math><br>Difference in profit = <math display=\"inline\"><mfrac><mrow><mn>324</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> - 12 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>17</mn></mfrac></math>%<br><math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>% = 3600<br>CP of article (100%) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3600</mn><mn>120</mn></mfrac></math> &times; 17 &times; 100 = 51,000</p>",
                    solution_hi: "<p>69.(b) माना पहली वस्तु का लाभ = x% और दूसरी वस्तु का लाभ = y%<br>शुद्ध लाभ % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow><mn>2</mn></mfrac></math><br>15<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>51</mn></mfrac></math>%&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mi>y</mi></mrow><mn>2</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>792</mn></mrow><mrow><mn>51</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>+</mo><mi>y</mi></mrow><mn>2</mn></mfrac></math><br>12 + y&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1584</mn><mn>51</mn></mfrac></math><br>y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1584</mn><mo>-</mo><mn>612</mn></mrow><mn>51</mn></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mn>972</mn></mrow><mrow><mn>51</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>324</mn><mn>17</mn></mfrac></math><br>लाभ में अंतर = <math display=\"inline\"><mfrac><mrow><mn>324</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math> - 12 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>17</mn></mfrac></math>%<br><math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math>% = 3600<br>वस्तु का क्रय मूल्य (100%) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3600</mn><mn>120</mn></mfrac></math> &times; 17 &times; 100 = 51,000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Find the greatest possible length which can be used to measure exactly the lengths 2 m 25 cm, 2 m 70 cm, 3 m 15 cm.</p>",
                    question_hi: "<p>70. वह अधिकतम संभव लंबाई ज्ञात कीजिए जिसका उपयोग 2 m 25 cm, 2 m 70 cm, 3 m 15 cm लंबाई को यथार्थत: मापने के लिए किया जा सके।</p>",
                    options_en: [
                        "<p>15 cm</p>",
                        "<p>45 cm</p>",
                        "<p>25 cm</p>",
                        "<p>35 cm</p>"
                    ],
                    options_hi: [
                        "<p>15 cm</p>",
                        "<p>45 cm</p>",
                        "<p>25 cm</p>",
                        "<p>35 cm</p>"
                    ],
                    solution_en: "<p>70.(b)<br>HCF of 225, 270, 315 = 45 cm</p>",
                    solution_hi: "<p>70.(b)<br>225, 270, 315 का HCF = 45 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Which of the following options gives the correct empirical relationship between mean, median and mode of a data set ?</p>",
                    question_hi: "<p>71. निम्नलिखित में से कौन सा विकल्प, आंकड़ों के एक समुच्चय के माध्य माध्यिका और बहुलक के बीच सही प्रयोगसिद्ध संबंध को दर्शाता है ?</p>",
                    options_en: [
                        "<p>Mean -&nbsp;Mode = 3 (Mean + Median)</p>",
                        "<p>Mean -&nbsp;Mode = 3 (Mean - Median)</p>",
                        "<p>Mean + Mode = 3 (Mean -&nbsp;Median)</p>",
                        "<p>Mean + Mode = 3 (Mean + Median)</p>"
                    ],
                    options_hi: [
                        "<p>माध्य -&nbsp;बहुलक = 3 (माध्य + माध्यिका)</p>",
                        "<p>माध्य -&nbsp;बहुलक = 3 (माध्य - माध्यिका)</p>",
                        "<p>माध्य + बहुलक = 3 (माध्य -&nbsp;माध्यिका)</p>",
                        "<p>माध्य + बहुलक = 3 (माध्य + माध्यिका)</p>"
                    ],
                    solution_en: "<p>71.(b)<br>We know that ,<br>Mode = 3 Median - 2mean<br>By hit and trial method ,<br>From option (b)<br>Mean - Mode = 3(Mean - Median) <br>&rArr; Mean &ndash;3Mean +3 Median = Mode&nbsp;<br>Now , Mode = 3 Median - 2 Mean (satisfies)<br>Hence option (b) is the right answer.</p>",
                    solution_hi: "<p>71.(b)<br>हम जानते हैं कि ,<br>बहुलक = 3 माध्यिका - 2 माध्य<br>हिट एंड ट्रायल पद्धति से,<br>विकल्प (b) से <br>माध्य - बहुलक = 3(माध्य - माध्यिका)&nbsp;<br>&rArr; माध्य &ndash; 3 माध्य + 3 माध्यिका = बहुलक <br>अब , बहुलक = 3 माध्यिका - 2 माध्य (संतुष्ट करता है)<br>इसलिए विकल्प (b) सही उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. In an election between two candidates P and Q, P got 78% of the total valid votes. If the total votes of the electorate were 75,60,000, then what was the number of valid votes that Q got if 10% of the voters did not cast their vote and 15% of the votes polled were declared invalid ?</p>",
                    question_hi: "<p>72. दो उम्मीदवारों P और Q के बीच हुए एक चुनाव में, P को कुल वैध मतों के 78% मत प्राप्त हुए। यदि निर्वाचन क्षेत्र में कुल मतों की संख्या 75,60,000 थी और 10% मतदाताओं ने अपने मत नहीं डाले और डाले गए 15% मत अवैध घोषित कर दिए गए, तो Q को प्राप्त वैध मतों की संख्या क्या थी ?</p>",
                    options_en: [
                        "<p>1,41,372</p>",
                        "<p>1,41,732</p>",
                        "<p>12,72,348</p>",
                        "<p>1,47,312</p>"
                    ],
                    options_hi: [
                        "<p>1,41,372</p>",
                        "<p>1,41,732</p>",
                        "<p>12,72,348</p>",
                        "<p>1,47,312</p>"
                    ],
                    solution_en: "<p>72.(c)<br>Total valid votes = 7560000 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 6804000<br>Votes that Q got = 6804000 &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>100</mn></mfrac></math> = 1272348</p>",
                    solution_hi: "<p>72.(c)<br>कुल वैध वोट = 7560000 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 6804000<br>Q को प्राप्त कुल वोट = 6804000 &times; <math display=\"inline\"><mfrac><mrow><mn>85</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>100</mn></mfrac></math> = 1272348</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. What will be the average of the odd numbers from 1 to 51, both inclusive ?</p>",
                    question_hi: "<p>73. 1 से 51 (इन दोनों को मिलाकर) तक की विषम संख्याओं का औसत क्या होगा ?</p>",
                    options_en: [
                        "<p>24</p>",
                        "<p>26</p>",
                        "<p>25</p>",
                        "<p>26.5</p>"
                    ],
                    options_hi: [
                        "<p>24</p>",
                        "<p>26</p>",
                        "<p>25</p>",
                        "<p>26.5</p>"
                    ],
                    solution_en: "<p>73.(b) Average = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>F</mi><mi>i</mi><mi>r</mi><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>n</mi><mi>o</mi><mo>.</mo><mo>+</mo><mi>l</mi><mi>a</mi><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>n</mi><mi>o</mi><mo>.</mo></mrow><mn>2</mn></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>51</mn></mrow><mn>2</mn></mfrac></math> = 26</p>",
                    solution_hi: "<p>73.(b) औसत = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2381;&#2352;&#2341;&#2350;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>+</mo><mi>&#2309;&#2306;&#2340;&#2367;&#2350;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow><mn>2</mn></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mn>51</mn></mrow><mn>2</mn></mfrac></math> = 26</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. If sec&theta; + tan&theta; = x, then find sin&theta;.</p>",
                    question_hi: "<p>74. यदि sec&theta; + tan&theta; = x है, तो sin&theta; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mi>x</mi><mn>2</mn></msup></mrow><mrow><mn>1</mn><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><mn>2</mn><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><mn>1</mn><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><msup><mi>x</mi><mn>2</mn></msup></mrow><mrow><mn>1</mn><mo>+</mo><msup><mi>x</mi><mn>2</mn></msup></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>74.(c) <br>sec&theta; + tan&theta; = x &hellip;(i)<br>and <br>sec&theta; - tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>&hellip;(i)<br>On adding (i) and (ii) we get,<br>2sec&theta; = x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math><br>sec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow><mrow><mn>2</mn><mi>x</mi></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>h</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow><mrow><mi>b</mi><mi>a</mi><mi>s</mi><mi>e</mi></mrow></mfrac></math><br>then, perpendicular = x<sup>2</sup> - 1<br>sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mi>e</mi><mi>r</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>c</mi><mi>u</mi><mi>l</mi><mi>a</mi><mi>r</mi></mrow><mrow><mi>h</mi><mi>y</mi><mi>p</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>n</mi><mi>u</mi><mi>s</mi><mi>e</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>74.(c) <br>sec&theta; + tan&theta; = x &hellip;(i)<br>और<br>sec&theta; - tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>&hellip;(i)<br>(i) और (ii) जोड़ने पर हमें मिलता है,<br>2sec&theta; = x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math><br>sec&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow><mrow><mn>2</mn><mi>x</mi></mrow></mfrac></math> = <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></math><br>तब, लंब = <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>1</mn></math><br>sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&#160;</mo></mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>1</mn></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>+</mo><mn>1</mn></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Which of the following numbers is divisible by 2, 5, 10 and 11 ?</p>",
                    question_hi: "<p>75. निम्नलिखित में से कौन सी संख्या 2, 5, 10 और 11 से विभाज्य है ?</p>",
                    options_en: [
                        "<p>203467</p>",
                        "<p>830942</p>",
                        "<p>589270</p>",
                        "<p>1234560</p>"
                    ],
                    options_hi: [
                        "<p>203467</p>",
                        "<p>830942</p>",
                        "<p>589270</p>",
                        "<p>1234560</p>"
                    ],
                    solution_en: "<p>75.(c) Divisibility rule of 2 : Last number should be an even number <br>By checking options , option (b) , (c) , (d) satisfy this condition <br>Divisibility rule of 5 : Last number should be 0 or 5 <br>By checking option , option (c) , (d) satisfy this condition<br>Divisibility rule of 10 : Number should be divisible by 2 and 5 <br>Divisibility rule of 11 : Difference between sum of digit odd place and sum of digit at even place should be 0 or multiple of 11 <br>By checking option , option (c) satisfy this condition<br>(5 + 9 + 7 ) - (8 + 2 + 0)<br>21 - 10 = 11 (multiple of 11)<br>So number 589270 is divisible by 2 , 5 , 10 and 11</p>",
                    solution_hi: "<p>75.(c) 2 की विभाज्यता नियम: अंतिम संख्या एक सम संख्या होनी चाहिए <br>विकल्पों की जाँच करके, विकल्प (b) , (c) , (d) इस शर्त को पूरा करते हैं <br>5 की विभाज्यता नियम: अंतिम संख्या 0 या 5 होनी चाहिए <br>विकल्प, विकल्प (c), (d) की जांच करके इस शर्त को पूरा करें<br>10 की विभाज्यता नियम: संख्या 2 और 5 से विभाज्य होनी चाहिए <br>11 का विभाज्यता नियम: विषम स्थान के अंकों के योग और सम स्थान के अंकों के योग के बीच अंतर 0 या 11 का गुणक होना चाहिए <br>विकल्प की जाँच करने पर , विकल्प (c) संतुष्ट हो रहा है । <br>(5 + 9 + 7 ) - (8 + 2 + 0)<br>21 - 10 = 11 (11 का गुणक)<br>तो, संख्या 589270 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><munder><mo>&#8594;</mo><mrow/></munder></math>2 , 5 , 10 और 11 से विभाज्य है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>As poor as they are they never refuse to donate what they have for any noble cause.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>As poor as they are they never refuse to donate what they have for any noble cause.</p>",
                    options_en: [
                        "<p>As poor as they are</p>",
                        "<p>they never refuse to donate, what they have</p>",
                        "<p>for any noble cause.</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>As poor as they are</p>",
                        "<p>they never refuse to donate, what they have</p>",
                        "<p>for any noble cause.</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>76.(a) Use \"though they are poor\" in place of &ldquo;As poor as they are&rdquo;.</p>",
                    solution_hi: "<p>76.(a) &ldquo;As poor as they are&rdquo;&nbsp; के स्थान पर \"Though they are poor\" का प्रयोग करें।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the option that expresses the given sentence in active voice. <br>The agency had been robbed by two masked intruders.</p>",
                    question_hi: "<p>77. Select the option that expresses the given sentence in active voice. <br>The agency had been robbed by two masked intruders.</p>",
                    options_en: [
                        "<p>Two masked intruders had rob the agency</p>",
                        "<p>Two masked intruders had been robbed the agency.</p>",
                        "<p>Two masked intruders had robbed the agency</p>",
                        "<p>Two masked intruders have been robbing the agency.</p>"
                    ],
                    options_hi: [
                        "<p>Two masked intruders had rob the agency</p>",
                        "<p>Two masked intruders had been robbed the agency.</p>",
                        "<p>Two masked intruders had robbed the agency</p>",
                        "<p>Two masked intruders have been robbing the agency. </p>"
                    ],
                    solution_en: "<p>77.(c) Two masked intruders had robbed the agency.(Correct)<br>(a) Two masked intruders had <span style=\"text-decoration: underline;\">rob</span> the agency.(Incorrect form of Verb)<br>(b) Two masked intruders <span style=\"text-decoration: underline;\">had been robbed</span> the agency.(Incorrect Verb)<br>(d) Two masked intruders <span style=\"text-decoration: underline;\">have been robbing</span> the agency.(Incorrect Tense)</p>",
                    solution_hi: "<p>77.(c) Two masked intruders had robbed the agency.(Correct)<br>(a) Two masked intruders had <span style=\"text-decoration: underline;\">rob</span> the agency.(Verb की गलत form)<br>(b) Two masked intruders <span style=\"text-decoration: underline;\">had been robbed</span> the agency.(गलत Verb)<br>(d) Two masked intruders <span style=\"text-decoration: underline;\">have been robbing</span> the agency.(गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution&rsquo;. <br><span style=\"text-decoration: underline;\">Inspite mother&rsquo;s milk</span> is vital to a child&rsquo;s health, a number of FMCG companies wrongly advertise artificially prepared infant formulas as being the best for the child.</p>",
                    question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select &lsquo;No substitution&rsquo;. <br><span style=\"text-decoration: underline;\">Inspite mother&rsquo;s milk</span> is vital to a child&rsquo;s health, a number of FMCG companies wrongly advertise artificially prepared infant formulas as being the best for the child.</p>",
                    options_en: [
                        "<p>Mother&rsquo;s milk</p>",
                        "<p>Though mother&rsquo;s milk</p>",
                        "<p>No substitution</p>",
                        "<p>Despite mother&rsquo;s milk</p>"
                    ],
                    options_hi: [
                        "<p>Mother&rsquo;s milk</p>",
                        "<p>Though mother&rsquo;s milk</p>",
                        "<p>No substitution</p>",
                        "<p>Despite mother&rsquo;s milk</p>"
                    ],
                    solution_en: "<p>78.(b) Though mother&rsquo;s milk<br>&lsquo;Though&rsquo; is used to express contrast between two ideas. The given sentence states that mother&rsquo;s milk is vital to a child\'s health, which contrasts with the advertisement of artificially prepared infant formulas. Hence, &ldquo;Though mother&rsquo;s milk&rdquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(b) Though mother&rsquo;s milk<br>&lsquo;Though&rsquo; का प्रयोग two ideas के बीच अंतर को व्यक्त करने के लिए किया जाता है। दिए गए sentence में कहा गया है कि माँ का दूध (mother&rsquo;s milk) बच्चे के स्वास्थ्य के लिए महत्वपूर्ण है, जो कृत्रिम रूप से (artificially) तैयार शिशु फ़ार्मुलों के विज्ञापन के विपरीत है। इसलिए, &ldquo;Though mother&rsquo;s milk&rdquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate synonym of the given word.<br>Lullaby</p>",
                    question_hi: "<p>79. Select the most appropriate synonym of the given word.<br>Lullaby</p>",
                    options_en: [
                        "<p>Disturb</p>",
                        "<p>Song</p>",
                        "<p>Baby</p>",
                        "<p>Love</p>"
                    ],
                    options_hi: [
                        "<p>Disturb</p>",
                        "<p>Song</p>",
                        "<p>Baby</p>",
                        "<p>Love</p>"
                    ],
                    solution_en: "<p>79.(b) <strong>Song</strong>- a short poem or other set of words set to music or meant to be sung. <br><strong>Lullaby-</strong> a quiet, gentle song sung to send a child to sleep.<br><strong>Disturb-</strong> to interrupt what someone is doing.<br><strong>Baby-</strong> a very young child.<br><strong>Love-</strong> an intense feeling of deep affection.</p>",
                    solution_hi: "<p>79.(b) <strong>Song</strong> (गीत)- a short poem or other set of words set to music or meant to be sung. <br><strong>Lullaby</strong> (लोरी)- a quiet, gentle song sung to send a child to sleep.<br><strong>Disturb</strong> (परेशान करना)- to interrupt what someone is doing.<br><strong>Baby</strong> (शिशु)- a very young child.<br><strong>Love</strong> (प्रेम)- an intense feeling of deep affection.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence.<br>Ramu said. &ldquo;My master is planning to build a huge house in Khandala.&rdquo;</p>",
                    question_hi: "<p>80. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence.<br>Ramu said. &ldquo;My master is planning to build a huge house in Khandala.&rdquo;</p>",
                    options_en: [
                        "<p>Ramu said that his master planned to build a huge house in Khandala</p>",
                        "<p>Ramu said that his master is planning to build a huge house in Khandala</p>",
                        "<p>Ramu said that his master&rsquo;s plan is to build a huge house in Khandala</p>",
                        "<p>Ramu said that his master was planning to build a huge house in Khandala.</p>"
                    ],
                    options_hi: [
                        "<p>Ramu said that his master planned to build a huge house in Khandala</p>",
                        "<p>Ramu said that his master is planning to build a huge house in Khandala</p>",
                        "<p>Ramu said that his master&rsquo;s plan is to build a huge house in Khandala</p>",
                        "<p>Ramu said that his master was planning to build a huge house in Khandala.</p>"
                    ],
                    solution_en: "<p>80.(d) Ramu said that his master was planning to build a huge house in Khandala.<br>(a) Ramu said that his master <span style=\"text-decoration: underline;\">planned to build</span> a huge house in Khandala. (Incorrect change of tense)<br>(b) Ramu said that his <span style=\"text-decoration: underline;\">master is planning</span> to build a huge house in Khandala. (Incorrect Tense)<br>(c) Ramu said that his master&rsquo;s <span style=\"text-decoration: underline;\">plan is to build</span> a huge house in Khandala. (Incorrect change of tense)</p>",
                    solution_hi: "<p>80.(d) Ramu said that his master was planning to build a huge house in Khandala.<br>(a) Ramu said that his master <span style=\"text-decoration: underline;\">planned to build</span> a huge house in Khandala.(Tense का का गलत प्रयोग किया गया है ) <br>(b) Ramu said that his master is <span style=\"text-decoration: underline;\">planning to build</span> a huge house in Khandala.(Tense का गलत प्रयोग किया गया है ) <br>(c) Ramu said that his master&rsquo;s <span style=\"text-decoration: underline;\">plan is to build</span> a huge house in Khandala. (Tense का गलत प्रयोग किया गया है )</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. select the option that expresses the following sentence in passive voice.<br>An agitated gathering of workers in the company disturbed the meeting.</p>",
                    question_hi: "<p>81. select the option that expresses the following sentence in passive voice.<br>An agitated gathering of workers in the company disturbed the meeting.</p>",
                    options_en: [
                        "<p>Meeting of the company was disturbed.</p>",
                        "<p>An agitated meeting of workers in the company disturbed the gathering.</p>",
                        "<p>The meeting was disturbed by an agitated gathering of workers in the company.</p>",
                        "<p>The gathering agitated the meeting of the company.</p>"
                    ],
                    options_hi: [
                        "<p>Meeting of the company was disturbed.</p>",
                        "<p>An agitated meeting of workers in the company disturbed the gathering.</p>",
                        "<p>The meeting was disturbed by an agitated gathering of workers in the company.</p>",
                        "<p>The gathering agitated the meeting of the company.</p>"
                    ],
                    solution_en: "<p>81.(c) The meeting was disturbed by an agitated gathering of workers in the company.(Correct)<br>(a) Meeting of the company was disturbed.(Incorrect Sentence Structure)<br>(b) An agitated meeting of workers in the company disturbed the gathering.(Incorrect Sentence Structure)<br>(d) The gathering agitated the meeting of the company. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>81.(c) The meeting was disturbed by an agitated gathering of workers in the company.(Correct)<br>(a) Meeting of the company was disturbed.(गलत Sentence Structure)<br>(b) An agitated meeting of workers in the company disturbed the gathering.(गलत Sentence Structure)<br>(d) The gathering agitated the meeting of the company.(गलत Sentence Structure)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the most appropriate option to fill in the blank. <br>The guard did not want anything bad to happen, so he ordered the group to step back to avoid anything ________.</p>",
                    question_hi: "<p>82. Select the most appropriate option to fill in the blank. <br>The guard did not want anything bad to happen, so he ordered the group to step back to avoid anything ________.</p>",
                    options_en: [
                        "<p>diplomatic</p>",
                        "<p>habitual</p>",
                        "<p>customary</p>",
                        "<p>terrible</p>"
                    ],
                    options_hi: [
                        "<p>diplomatic</p>",
                        "<p>habitual</p>",
                        "<p>customary</p>",
                        "<p>terrible</p>"
                    ],
                    solution_en: "<p>82.(d) terrible<br>&lsquo;Terrible&rsquo; means very bad or unpleasant. The given sentence states that the guard did not want anything bad to happen, so he ordered the group to step back to avoid anything terrible. Hence, &lsquo;terrible&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>82.(d) terrible<br>&lsquo;Terrible&rsquo; का अर्थ है बहुत बुरा या अप्रिय। दिए गए sentence में कहा गया है कि guard नहीं चाहता था कि कुछ बुरा हो, इसलिए उसने समूह को पीछे हटने का आदेश दिया ताकि कुछ भी भयानक (terrible) न हो। अतः, &lsquo;terrible&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate ANTONYM of the underlined word.<br>Maria thinks the animals that live in <span style=\"text-decoration: underline;\">freedom</span> have a sad look in their eyes.</p>",
                    question_hi: "<p>83. Select the most appropriate ANTONYM of the underlined word.<br>Maria thinks the animals that live in <span style=\"text-decoration: underline;\">freedom</span> have a sad look in their eyes.</p>",
                    options_en: [
                        "<p>Liberty</p>",
                        "<p>Surrendered</p>",
                        "<p>Captivity</p>",
                        "<p>Carefree</p>"
                    ],
                    options_hi: [
                        "<p>Liberty</p>",
                        "<p>Surrendered</p>",
                        "<p>Captivity</p>",
                        "<p>Carefree</p>"
                    ],
                    solution_en: "<p>83.(c) <strong>Captivity</strong>- the state of being confined or imprisoned.<br><strong>Freedom-</strong> the state of being free, without restrictions.<br><strong>Liberty-</strong> the condition of being free from oppressive restrictions.<br><strong>Surrendered-</strong> to give up control or submit.<br><strong>Carefree-</strong> free from worries or responsibilities.</p>",
                    solution_hi: "<p>83.(c) <strong>Captivity</strong> (क़ैद) - the state of being confined or imprisoned.<br><strong>Freedom</strong> (स्वतंत्रता) - the state of being free, without restrictions.<br><strong>Liberty</strong> (स्वतंत्रता) - the condition of being free from oppressive restrictions.<br><strong>Surrendered</strong> (आत्मसमर्पण कर दिया) - to give up control or submit.<br><strong>Carefree</strong> (बेपरवाह) - free from worries or responsibilities.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. It is also called a nuclear family.<br>B. It is good to be in a small family because there is better management of resources.<br>C. A small family is one with parents and a maximum of two children.<br>D. The advantages of a small family are numerous.</p>",
                    question_hi: "<p>84. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. It is also called a nuclear family.<br>B. It is good to be in a small family because there is better management of resources.<br>C. A small family is one with parents and a maximum of two children.<br>D. The advantages of a small family are numerous.</p>",
                    options_en: [
                        "<p>CADB</p>",
                        "<p>BACD</p>",
                        "<p>CBDA</p>",
                        "<p>ACBD</p>"
                    ],
                    options_hi: [
                        "<p>CADB</p>",
                        "<p>BACD</p>",
                        "<p>CBDA</p>",
                        "<p>ACBD</p>"
                    ],
                    solution_en: "<p>84.(a) <strong>CADB</strong><br>Sentence C will be the starting line as it contains the main subject of the parajumble i.e. a small family. And, Sentence A states nuclear family as another name for a small family. So, A will follow C. Further, Sentence D states that the advantages of a small family are numerous &amp; Sentence B gives one reason why it is good to be in a small family. So, B will follow D. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>84.(a) <strong>CADB</strong><br>Sentence C प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विषय &lsquo;a small family&rsquo; शामिल है। और, Sentence A में छोटे परिवार के दूसरे नाम के रूप में nuclear family का उल्लेख किया गया है। इसलिए, C के बाद A आएगा। इसके अलावा, Sentence D बताता है कि छोटे परिवार के कई फायदे हैं और Sentence B कारण बताता है कि छोटे परिवार में रहना क्यों अच्छा है। इसलिए, D के बाद B आएगा। अतः options के माध्यम से जाने पर, option \'a\' में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "85. Select the INCORRECTLY spelt word.",
                    question_hi: "85. Select the INCORRECTLY spelt word.",
                    options_en: [
                        " ettiquete ",
                        " elopement ",
                        " occasion",
                        " rationalisation          "
                    ],
                    options_hi: [
                        " ettiquete ",
                        " elopement ",
                        " occasion",
                        " rationalisation              <br />   "
                    ],
                    solution_en: "85.(a) ettiquete <br />‘Etiquette’ is the correct spelling.",
                    solution_hi: "85.(a) ettiquete <br />‘Etiquette’ सही spelling है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>Winter was so bad that the nomadic tribesmen found it difficult <strong><span style=\"text-decoration: underline;\">to keep the wolf from the door</span></strong>.</p>",
                    question_hi: "<p>86. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>Winter was so bad that the nomadic tribesmen found it difficult <strong><span style=\"text-decoration: underline;\">to keep the wolf from the door</span>.</strong></p>",
                    options_en: [
                        "<p>hunt wild animals</p>",
                        "<p>escape starvation</p>",
                        "<p>get woollen clothes</p>",
                        "<p>walk on ice</p>"
                    ],
                    options_hi: [
                        "<p>hunt wild animals</p>",
                        "<p>escape starvation</p>",
                        "<p>get woollen clothes</p>",
                        "<p>walk on ice</p>"
                    ],
                    solution_en: "<p>86.(b) To keep the wolf from the door - escape starvation.</p>",
                    solution_hi: "<p>86.(b) To keep the wolf from the door- escape starvation./ भुखमरी से बचना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "87. Parts of the following sentence have been given as options. Select the option that contains an error. <br />She is enough wise not to interfere in their matter, once being insulted publicly by them.",
                    question_hi: "87. Parts of the following sentence have been given as options. Select the option that contains an error. <br />She is enough wise not to interfere in their matter, once being insulted publicly by them.",
                    options_en: [
                        " not to interfere in  ",
                        " their matter, once being ",
                        " She is enough wise ",
                        " insulted publicly by them. "
                    ],
                    options_hi: [
                        " not to interfere in  ",
                        " their matter, once being ",
                        " She is enough wise ",
                        " insulted publicly by them. "
                    ],
                    solution_en: "87.(c) She is enough wise<br />The adverb ‘enough’ is always used after the adjective. Similarly, in the given sentence, ‘enough’ will be used after the adjective ‘wise’. Hence, ‘She is wise enough’ is the most appropriate answer.",
                    solution_hi: "87.(c) She is enough wise<br />Adverb ‘enough’ हमेशा adjective के बाद प्रयोग किया जाता है। इसी तरह, दिए गए sentence में, ‘enough’ का प्रयोग adjective ‘wise’ के बाद किया जाएगा। अतः, ‘She is wise enough’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>One who is rather fastidious.</p>",
                    question_hi: "<p>88. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence. <br>One who is rather fastidious.</p>",
                    options_en: [
                        "<p>Tempestuous</p>",
                        "<p>Punctual</p>",
                        "<p>Meticulous</p>",
                        "<p>Carefree</p>"
                    ],
                    options_hi: [
                        "<p>Tempestuous</p>",
                        "<p>Punctual</p>",
                        "<p>Meticulous</p>",
                        "<p>Carefree</p>"
                    ],
                    solution_en: "<p>88.(c) <strong>Meticulous.</strong> <br><strong>Tempestuous</strong> - Characterized by strong and turbulent or conflicting emotion.<br><strong>Punctual</strong> - Happening or doing something at the agreed or proper time.<br><strong>Meticulous</strong> - Showing great attention to detail; very careful and precise.<br><strong>Carefree</strong> - Free from anxiety or responsibility.</p>",
                    solution_hi: "<p>88.(c)<strong> Meticulous.</strong> <br><strong>Tempestuous</strong> - मजबूत और अशांत या परस्पर विरोधी भावना द्वारा विवरण करना।<br><strong>Punctual</strong> - उचित समय पर कुछ होना या करना। <br><strong>Meticulous</strong> -विवरण पर बहुत ध्यान देना बहुत सावधान और सटीक।<br><strong>Carefree</strong> - चिंता या जिम्मेदारी से मुक्त।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>An <span style=\"text-decoration: underline;\">awkward neither grammatically</span> accurate sentence is the result of centre implanting.</p>",
                    question_hi: "<p>89. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>An <span style=\"text-decoration: underline;\">awkward neither grammatically</span> accurate sentence is the result of centre implanting.</p>",
                    options_en: [
                        "<p>awkward unless grammatically</p>",
                        "<p>awkward either grammatically</p>",
                        "<p>awkward beyond grammatically</p>",
                        "<p>awkward but grammatically</p>"
                    ],
                    options_hi: [
                        "<p>awkward unless grammatically</p>",
                        "<p>awkward either grammatically</p>",
                        "<p>awkward beyond grammatically</p>",
                        "<p>awkward but grammatically</p>"
                    ],
                    solution_en: "<p>89.(d) awkward but grammatically<br>&lsquo;But&rsquo; is used to indicate a contrasting idea. Similarly, the word &lsquo;awkward&rsquo; contrasts with the idea &lsquo;grammatically accurate&rsquo;. Hence, &lsquo;awkward but grammatically&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(d) awkward but grammatically<br>&lsquo;But&rsquo; का प्रयोग विपरीत विचार (contrasting idea) को indicate करने के लिए किया जाता है। इसी तरह, word &lsquo;awkward&rsquo; idea &lsquo;grammatically accurate&rsquo; के विपरीत है। इसलिए, &lsquo;awkward but grammatically&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that can be used as a one-word substitute for the given group of words.<br>She was <span style=\"text-decoration: underline;\">engaged to be married</span> to him.</p>",
                    question_hi: "<p>90. Select the option that can be used as a one-word substitute for the given group of words.<br>She was <span style=\"text-decoration: underline;\">engaged to be married</span> to him.</p>",
                    options_en: [
                        "<p>betrothed</p>",
                        "<p>betrayed</p>",
                        "<p>bestowed</p>",
                        "<p>baptised</p>"
                    ],
                    options_hi: [
                        "<p>betrothed</p>",
                        "<p>betrayed</p>",
                        "<p>bestowed</p>",
                        "<p>baptised</p>"
                    ],
                    solution_en: "<p>90.(a) <strong>Betrothed-</strong> engaged to be married.<br><strong>Betrayed-</strong> to not be loyal to your country or a person, often by doing something harmful such as helping their enemies.<br><strong>Bestowed-</strong> to give something as an honour or present.<br><strong>Baptised-</strong> a Christian ceremony in which water is poured over someone\'s head or the person is briefly covered completely by water, and is named as a Christian.</p>",
                    solution_hi: "<p>90.(a) <strong>Betrothed</strong> (मंगेतर) - engaged to be married.<br><strong>Betrayed</strong> (विश्वासघात करना) - to not be loyal to your country or a person, often by doing something harmful such as helping their enemies.<br><strong>Bestowed</strong> (प्रदान करना) - to give something as an honour or present.<br><strong>Baptised</strong> (ईसाई दीक्षा) - a Christian ceremony in which water is poured over someone\'s head or the person is briefly covered completely by water, and is named as a Christian.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>O. delicious<br>R. she<br>Q. a<br>P. baked<br>S. chocolate cake</p>",
                    question_hi: "<p>91. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br>O. delicious<br>R. she<br>Q. a<br>P. baked<br>S. chocolate cake</p>",
                    options_en: [
                        "<p>RPQOS</p>",
                        "<p>QOSRP</p>",
                        "<p>OPQRS</p>",
                        "<p>SRPQO</p>"
                    ],
                    options_hi: [
                        "<p>RPQOS</p>",
                        "<p>QOSRP</p>",
                        "<p>OPQRS</p>",
                        "<p>SRPQO</p>"
                    ],
                    solution_en: "<p>91.(a) RPQOS<br>The given sentence starts with Part R as it contains the main subject of the parajumble, &lsquo;She&rsquo;. Part P contains the main verb of the sentence, &lsquo;baked&rsquo; and Part Q has the article &lsquo;a&rsquo; for the object of the verb. So, Q will follow P. Further, Part O has the adjective &lsquo;delicious&rsquo; to describe the object and Part S contains the object of the verb, &lsquo;chocolate cake&rsquo;. So, S will follow O. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>91.(a) RPQOS<br>दिया गया sentence, Part R से शुरू होता है क्योंकि इसमें parajumble का मुख्य विषय, &lsquo;She&rsquo; शामिल है। Part P में sentence की main verb &lsquo;baked&rsquo; शामिल है और Part Q में verb के object के लिए article &lsquo;a&rsquo; है। इसलिए, P के बाद Q आएगा। इसके अलावा, Part O में object का वर्णन करने के लिए adjective &lsquo;delicious&rsquo; है और Part S में verb का object, &lsquo;chocolate cake&rsquo; शामिल है। इसलिए, O के बाद S आएगा। अतः options के माध्यम से जाने पर, option \'a\' में सही sequence है। </p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Find a word that is the synonym of<br>TRITE</p>",
                    question_hi: "<p>92. Find a word that is the synonym of<br>TRITE</p>",
                    options_en: [
                        "<p>Commonplace</p>",
                        "<p>Clever</p>",
                        "<p>Brief</p>",
                        "<p>Impudent</p>"
                    ],
                    options_hi: [
                        "<p>Commonplace</p>",
                        "<p>Clever</p>",
                        "<p>Brief</p>",
                        "<p>Impudent</p>"
                    ],
                    solution_en: "<p>92.(a) Commonplace .<br><strong>Trite </strong>- (of a remark or idea) lacking originality or freshness; dull on account of overuse.<br>(d) <strong>Impudent </strong>- not showing due respect for another person; impertinent.</p>",
                    solution_hi: "<p>92.(a) Commonplace .<br><strong>Trite </strong>- बहुत बार उपयोग किए जाने के कारण प्रभावी नहीं होना।<br>(d) <strong>Impudent </strong>-किसी अन्य व्यक्ति के लिए उचित सम्मान नहीं दिखाना; ढीठ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Pick a word opposite in meaning to <br>Ingenious</p>",
                    question_hi: "<p>93. Pick a word opposite in meaning to <br>Ingenious</p>",
                    options_en: [
                        "<p>clever</p>",
                        "<p>stupid</p>",
                        "<p>sophisticated</p>",
                        "<p>na&iuml;ve</p>"
                    ],
                    options_hi: [
                        "<p>clever</p>",
                        "<p>stupid</p>",
                        "<p>sophisticated</p>",
                        "<p>na&iuml;ve</p>"
                    ],
                    solution_en: "<p>93.(b) <strong>Stupid </strong>- not intelligent or sensible. <br><strong>Ingenious</strong> - clever, original, and inventive. <br><strong>Naive</strong> - showing a lack of experience, wisdom, or judgment.<br><strong>Clever</strong> - able to learn, understand or do something quickly and easily.<br><strong>Sophisticated</strong> - having, revealing, or involving a great deal of worldly experience and knowledge of fashion and culture.</p>",
                    solution_hi: "<p>93.(b) <strong>Stupid </strong>- बुद्धिमान या समझदार ना होना ।<br><strong>Ingenious</strong> - बुद्धिमान, कुशल और आविष्कारशील।<br><strong>Naive</strong> - अनुभव, ज्ञान की कमी होना । <br><strong>Clever</strong> - जल्दी और आसानी से कुछ सीखने, समझने या करने में सक्षम।<br><strong>Sophisticated</strong> - बहुत सारे सांसारिक अनुभव और फैशन और संस्कृति का ज्ञान होना, प्रकट करना या शामिल करना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate idiom that can substitute the underlined words in the given sentence. <br>I know you have been <span style=\"text-decoration: underline;\">really busy</span> with your work, but can you just give me a few minutes?</p>",
                    question_hi: "<p>94. Select the most appropriate idiom that can substitute the underlined words in the given sentence. <br>I know you have been <span style=\"text-decoration: underline;\">really busy</span> with your work, but can you just give me a few minutes?</p>",
                    options_en: [
                        "<p>snowed under</p>",
                        "<p>through thick and thin</p>",
                        "<p>jumped on the bandwagon</p>",
                        "<p>on cloud nine</p>"
                    ],
                    options_hi: [
                        "<p>snowed under</p>",
                        "<p>through thick and thin</p>",
                        "<p>jumped on the bandwagon</p>",
                        "<p>on cloud nine</p>"
                    ],
                    solution_en: "<p>94.(a) <strong>Snowed under- </strong>really busy.</p>",
                    solution_hi: "<p>94.(a) <strong>Snowed under-</strong> really busy./अत्यधिक व्यस्त होना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "95. Select the most appropriate option to fill in the blank. .<br />How have you come_______such a precious diamond?",
                    question_hi: "95. Select the most appropriate option to fill in the blank. .<br />How have you come_______such a precious diamond?",
                    options_en: [
                        " across  ",
                        " upon  ",
                        " by ",
                        " None"
                    ],
                    options_hi: [
                        " across  ",
                        " upon  ",
                        " by ",
                        " None"
                    ],
                    solution_en: "95.(a) ‘Come across’ is a phrasal verb which means to meet or find by chance. The given sentence states how someone came across (meet or find) such a precious diamond. Hence, ‘across’ is the most appropriate answer. ",
                    solution_hi: "95.(a) ‘Come across’ एक phrasal verb है जिसका अर्थ संयोग से मिलना है। दिए गए वाक्य में बताया गया है कि किसी को इतना कीमती हीरा कैसे मिला। इसलिए, ‘across’ सबसे उपयुक्त उत्तर है। ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:</strong><br>Father read out to Swami the news of a young boy (96) ______had faced a tiger while returning home (97)_____the jungle path. \"What do you (98) _____to that, Swami?\" he asked. \"I (99) _____he must have been a very strong and (100) ______person, not at all a boy. How could a boy fight a tiger ?\" said Swami.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong><br>Father read out to Swami the news of a young boy (96) ______had faced a tiger while returning home (97)_____the jungle path. \"What do you (98) _____to that, Swami?\" he asked. \"I (99) _____he must have been a very strong and (100) ______person, not at all a boy. How could a boy fight a tiger ?\" said Swami.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    options_en: [
                        "<p>which</p>",
                        "<p>whom</p>",
                        "<p>that</p>",
                        "<p>who</p>"
                    ],
                    options_hi: [
                        "<p>which</p>",
                        "<p>whom</p>",
                        "<p>that</p>",
                        "<p>who</p>"
                    ],
                    solution_en: "<p>96.(d) who.<br>Relative pronoun &lsquo;who&rsquo; should be used for a person. In this sentence, &lsquo;who&rsquo; is used for the young boy.</p>",
                    solution_hi: "<p>96.(d) who <br>Relative pronoun &lsquo;who&rsquo; का प्रयोग किसी person के लिए किया जाना चाहिए। इस वाक्य में छोटे लड़के के लिए &lsquo;who&rsquo; का प्रयोग किया गया है। अतः, विकल्प (d) उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong><br>Father read out to Swami the news of a young boy (96) ______had faced a tiger while returning home (97)_____the jungle path. \"What do you (98) _____to that, Swami?\" he asked. \"I (99) _____he must have been a very strong and (100) ______person, not at all a boy. How could a boy fight a tiger ?\" said Swami.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong><br>Father read out to Swami the news of a young boy (96) ______had faced a tiger while returning home (97)_____the jungle path. \"What do you (98) _____to that, Swami?\" he asked. \"I (99) _____he must have been a very strong and (100) ______person, not at all a boy. How could a boy fight a tiger ?\" said Swami.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    options_en: [
                        "<p>at</p>",
                        "<p>from</p>",
                        "<p>for</p>",
                        "<p>to</p>"
                    ],
                    options_hi: [
                        "<p>at</p>",
                        "<p>from</p>",
                        "<p>for</p>",
                        "<p>to</p>"
                    ],
                    solution_en: "<p>97.(b) from.<br>The preposition &ldquo;from&rdquo; should be used with &ldquo;return&rdquo;.</p>",
                    solution_hi: "<p>97.(b) from.<br>Preposition &ldquo;from&rdquo; का उपयोग &ldquo;return&rdquo; के साथ किया जाना चाहिए।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong><br>Father read out to Swami the news of a young boy (96) ______had faced a tiger while returning home (97)_____the jungle path. \"What do you (98) _____to that, Swami?\" he asked. \"I (99) _____he must have been a very strong and (100) ______person, not at all a boy. How could a boy fight a tiger ?\" said Swami.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong><br>Father read out to Swami the news of a young boy (96) ______had faced a tiger while returning home (97)_____the jungle path. \"What do you (98) _____to that, Swami?\" he asked. \"I (99) _____he must have been a very strong and (100) ______person, not at all a boy. How could a boy fight a tiger ?\" said Swami.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    options_en: [
                        "<p>say</p>",
                        "<p>talk</p>",
                        "<p>tell</p>",
                        "<p>speak</p>"
                    ],
                    options_hi: [
                        "<p>say</p>",
                        "<p>talk</p>",
                        "<p>tell</p>",
                        "<p>speak</p>"
                    ],
                    solution_en: "<p>98.(a) <strong>Say</strong> - to speak or tell somebody something, using words.<br><strong>Tell </strong>- to give information to somebody by speaking or writing.<br><strong>Talk</strong> - to speak in order to give information or to express feelings, ideas, etc.<br><strong>Speak</strong> - to talk or say things.</p>",
                    solution_hi: "<p>98.(a) <strong>Say </strong>- किसी को कुछ बोलना या बताना। <br><strong>Tell</strong> - बोलकर या लिखकर किसी को जानकारी देना।<br><strong>Talk</strong> - जानकारी देने या भावनाओं, विचारों आदि को व्यक्त करने के लिए बोलना।<br><strong>Speak</strong> - बात करना या बातें करना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:</strong><br>Father read out to Swami the news of a young boy (96) ______had faced a tiger while returning home (97)_____the jungle path. \"What do you (98) _____to that, Swami?\" he asked. \"I (99) _____he must have been a very strong and (100) ______person, not at all a boy. How could a boy fight a tiger ?\" said Swami.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong><br>Father read out to Swami the news of a young boy (96) ______had faced a tiger while returning home (97)_____the jungle path. \"What do you (98) _____to that, Swami?\" he asked. \"I (99) _____he must have been a very strong and (100) ______person, not at all a boy. How could a boy fight a tiger ?\" said Swami.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    options_en: [
                        "<p>know</p>",
                        "<p>regard</p>",
                        "<p>think</p>",
                        "<p>suggest</p>"
                    ],
                    options_hi: [
                        "<p>know</p>",
                        "<p>regard</p>",
                        "<p>think</p>",
                        "<p>suggest</p>"
                    ],
                    solution_en: "<p>99.(c) <strong>Think</strong> - to have a particular idea or opinion about something/somebody; to believe<br><strong>Know</strong> - to have knowledge or information in your mind<br><strong>Suggest</strong> - to mention a plan or an idea that you have for somebody to discuss or consider<br><strong>Regard</strong> - attention to or care for somebody/something</p>",
                    solution_hi: "<p>99.(c) <strong>Think</strong> - किसी चीज़/किसी के बारे में कोई विशेष विचार या राय रखना<br><strong>Know</strong> - ज्ञान या जानकारी होना<br><strong>Suggest</strong> - एक योजना या एक विचार का उल्लेख करने के लिए जो आपके पास किसी के बारे में चर्चा करने या विचार करने के लिए है<br><strong>Regard</strong> -किसी पर ध्यान देना या उसकी देखभाल करना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:</strong><br>Father read out to Swami the news of a young boy (96) ______had faced a tiger while returning home (97)_____the jungle path. \"What do you (98) _____to that, Swami?\" he asked. \"I (99) _____he must have been a very strong and (100) ______person, not at all a boy. How could a boy fight a tiger ?\" said Swami.Select the most appropriate option to fill in the blank no 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong><br>Father read out to Swami the news of a young boy (96) ______had faced a tiger while returning home (97)_____the jungle path. \"What do you (98) _____to that, Swami?\" he asked. \"I (99) _____he must have been a very strong and (100) ______person, not at all a boy. How could a boy fight a tiger ?\" said Swami.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    options_en: [
                        " grown over",
                        " grown out",
                        " grown in  ",
                        " grown up"
                    ],
                    options_hi: [
                        " grown over",
                        " grown out",
                        " grown in  ",
                        " grown up"
                    ],
                    solution_en: "100.(d) grown up.<br />‘Grown up’  is the correct phrase which means to develop into an adult. ",
                    solution_hi: "100.(d) grown up.<br />‘Grown up’  सही phrase  है जिसका अर्थ है एक adult  के रूप में विकसित होना। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>