<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> The Napier&rsquo;s technology used for calculation called .</span></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2375;&#2346;&#2367;&#2351;&#2352; </span><span style=\"font-family: Cambria Math;\">(Napier) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368; &#2327;&#2339;&#2344;&#2366; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2368; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2340;&#2325;&#2344;&#2368;&#2325; &#2325;&#2379; &#2325;&#2381;&#2351;&#2366; &#2325;&#2375;&#2361;&#2340;&#2375; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Rabdologia</p>\n", "<p>Naptologia</p>\n", 
                                "<p>Vibologia</p>\n", "<p>Semiconductor</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2352;&#2348;&#2337;&#2379;&#2354;&#2379;&#2327;&#2367;&#2351;&#2366; (Rabdologia)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2344;&#2376;&#2346;&#2381;&#2335;&#2379;&#2354;&#2367;&#2351;&#2366; (Naptologia)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2357;&#2367;&#2348;&#2379;&#2354;&#2379;&#2327;&#2367;&#2351;&#2366; (Vibologia)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2375;&#2350;&#2368;&#2325;&#2306;&#2337;&#2325;&#2381;&#2335;&#2352; (Semiconductor)</span></p>\n"],
                    solution_en: "<p>1.(a)<span style=\"font-family: Cambria Math;\"> The correct answer is </span><span style=\"font-family: Cambria Math;\"><strong>Rabdologia</strong>. </span><span style=\"font-family: Cambria Math;\">Napier coined the word rabdology to describe this technique. The rods were used to multiply, divide and even find the square roots and cube roots of numbers.</span></p>\n",
                    solution_hi: "<p>1.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368; &#2313;&#2340;&#2381;</span><span style=\"font-family: Cambria Math;\">&zwj;</span><span style=\"font-family: Cambria Math;\">&#2340;&#2352; &#2361;&#2376; </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\"><strong>&#2352;&#2348;&#2337;&#2379;&#2354;&#2379;&#2327;&#2367;&#2351;&#2366;</strong>&#2404; </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360; &#2340;&#2325;&#2344;&#2368;&#2325; &#2325;&#2366; &#2357;&#2352;&#2381;&#2339;&#2344; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2344;&#2375;&#2346;&#2367;&#2351;&#2352; &#2344;&#2375; &#2352;&#2376;&#2348;&#2337;&#2379;&#2354;&#2377;&#2332;&#2368; &#2358;&#2348;&#2381;&#2342; &#2327;&#2338;&#2364;&#2366;&#2404; &#2352;&#2377;&#2337; </span><span style=\"font-family: Cambria Math;\">(rods) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2327;&#2369;&#2339;&#2366; &#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2351;&#2361;&#2366;&#2306; &#2340;&#2325; </span><span style=\"font-family: Cambria Math;\">&#8203;&#8203;</span><span style=\"font-family: Cambria Math;\">&#2325;&#2367; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2323;&#2306; &#2325;&#2375; &#2357;&#2352;&#2381;&#2327;&#2350;&#2370;&#2354; &#2324;&#2352; &#2328;&#2344;&#2350;&#2370;&#2354; &#2344;&#2367;&#2325;&#2366;&#2354;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2341;&#2366;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Which unit of computer helps in communication between the memory and the arithmetic logical unit?</span></p>\n",
                    question_hi: "<p>2.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2368; &#2325;&#2380;&#2344; &#2360;&#2368; &#2351;&#2370;&#2344;&#2367;&#2335; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; </span><span style=\"font-family: Cambria Math;\">(unit memory) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2309;&#2352;&#2367;&#2341;&#2350;&#2375;&#2335;&#2367;&#2325; &#2354;&#2377;&#2332;&#2367;&#2325;&#2354; &#2351;&#2370;&#2344;&#2367;&#2335; </span><span style=\"font-family: Cambria Math;\">(arithmetic logical unit) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2348;&#2368;&#2330; &#2360;&#2306;&#2330;&#2366;&#2352; </span><span style=\"font-family: Cambria Math;\">(communication) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2350;&#2342;&#2342; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>CMU</p>\n", "<p>CCU</p>\n", 
                                "<p>ALU</p>\n", "<p>CU</p>\n"],
                    options_hi: ["<p>CMU</p>\n", "<p>CCU</p>\n",
                                "<p>ALU</p>\n", "<p>CU</p>\n"],
                    solution_en: "<p>2.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">The </span><strong><span style=\"font-family: Cambria Math;\">control unit</span></strong><span style=\"font-family: Cambria Math;\"> helps in communicating with both the arithmetic/logic unit and memory. </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">An arithmetic logic unit (ALU)</span><span style=\"font-family: Cambria Math;\"> is a digital circuit used to perform arithmetic and logic operations.</span></p>\n",
                    solution_hi: "<p>2.(d)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2351;&#2370;&#2344;&#2367;&#2335;</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2367;&#2341;&#2350;&#2375;&#2335;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2354;&#2377;&#2332;&#2367;&#2325; &#2351;&#2370;&#2344;&#2367;&#2335; &#2324;&#2352; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; &#2342;&#2379;&#2344;&#2379;&#2306; &#2325;&#2375; &#2360;&#2366;&#2341; &#2360;&#2306;&#2330;&#2366;&#2352; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2350;&#2342;&#2342; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404; &#2319;&#2325; </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2367;&#2341;&#2350;&#2375;&#2335;&#2367;&#2325; &#2354;&#2377;&#2332;&#2367;&#2325; &#2351;&#2370;&#2344;&#2367;&#2335; </span><span style=\"font-family: Cambria Math;\">(ALU) </span><span style=\"font-family: Cambria Math;\">&#2309;&#2352;&#2367;&#2341;&#2350;&#2375;&#2335;&#2367;&#2325; &#2324;&#2352; &#2354;&#2377;&#2332;&#2367;&#2325; &#2360;&#2306;&#2330;&#2366;&#2354;&#2344; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2319;&#2325; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2360;&#2352;&#2381;&#2325;&#2367;&#2335; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "<p>3.<span style=\"font-family: Cambria Math;\"> A collection of worksheets in ms excel is called __________ .</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Cambria Math;\">&#2319;&#2350;&#2319;&#2360; &#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2350;&#2375;&#2306; &#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335;&#2381;&#2360; &#2325;&#2375; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361; &#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">__________ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Excel book</p>\n", "<p>Worksheets</p>\n", 
                                "<p>Excel sheets</p>\n", "<p>Workbook</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2348;&#2369;&#2325; (Excel book)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335; (Worksheet)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354; &#2358;&#2368;&#2335; (Excel sheet)</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2357;&#2352;&#2381;&#2325;&#2348;&#2369;&#2325; (Workbook)</span></p>\n"],
                    solution_en: "<p>3.(d)<span style=\"font-family: Cambria Math;\"> A worksheet consists of cells in which you can enter and calculate data.A collection of worksheets is called <strong>Workbook</strong>.</span></p>\n",
                    solution_hi: "<p>3.(d) <span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335; &#2350;&#2375;&#2306; &#2360;&#2375;&#2354; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306; &#2332;&#2367;&#2344;&#2350;&#2375;&#2306; &#2310;&#2346; &#2337;&#2375;&#2335;&#2366; &#2342;&#2352;&#2381;&#2332; &#2325;&#2352; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2327;&#2339;&#2344;&#2366; &#2325;&#2352; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2357;&#2352;&#2381;&#2325;&#2358;&#2368;&#2335; &#2325;&#2375; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361; &#2325;&#2379; <strong>&#2357;&#2352;&#2381;&#2325;&#2348;&#2369;&#2325;</strong></span><span style=\"font-family: Cambria Math;\"><strong>(workbook)</strong> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> When you cut or copy information it gets placed in the ...... .</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\">&#2332;&#2348; &#2310;&#2346; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2325;&#2379; &#2325;&#2366;&#2335;&#2340;&#2375; &#2351;&#2366; &#2325;&#2377;&#2346;&#2368; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306; &#2340;&#2379; &#2351;&#2361; </span><span style=\"font-family: Cambria Math;\">...... </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2352;&#2326;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404; </span></p>\n",
                    options_en: ["<p>clipart</p>\n", "<p>clipboard</p>\n", 
                                "<p>motherboard</p>\n", "<p>Both &lsquo;a&rsquo; and &lsquo;b&rsquo;</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2325;&#2381;&#2354;&#2367;&#2346; &#2310;&#2352;&#2381;&#2335;(clipart)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2381;&#2354;&#2367;&#2346;&#2348;&#2379;&#2352;&#2381;&#2337;(clipboard)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2350;&#2342;&#2352;&#2348;&#2379;&#2352;&#2381;&#2337;(motherboard)</span></p>\n", "<p><span style=\"font-weight: 400;\">\'a\' &#2324;&#2352; \'b\' &#2342;&#2379;&#2344;&#2379;&#2306;(Both &lsquo;a&rsquo; and &lsquo;b&rsquo;)</span></p>\n"],
                    solution_en: "<p>4.(b)<span style=\"font-family: Cambria Math;\"> <strong>Clipboard</strong> is </span><span style=\"font-family: Cambria Math;\">a place where information from a computer file is stored when we cut or copy information.</span></p>\n",
                    solution_hi: "<p>4.(b)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2354;&#2367;&#2346;&#2348;&#2379;&#2352;&#2381;&#2337;</span></strong><span style=\"font-family: Cambria Math;\"><strong>(clipboard)</strong> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2320;&#2360;&#2366; &#2360;&#2381;&#2341;&#2366;&#2344; &#2361;&#2376; &#2332;&#2361;&#2366;&#2306; &#2332;&#2348; &#2361;&#2350; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2325;&#2379; &#2325;&#2366;&#2335;&#2340;&#2375; &#2351;&#2366; &#2325;&#2377;&#2346;&#2368; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306; &#2340;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2347;&#2364;&#2366;&#2311;&#2354; &#2360;&#2375; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2360;&#2381;&#2335;&#2379;&#2352;&#2381;&#2337; &#2325;&#2368; &#2332;&#2366;&#2340;&#2368; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> Lisp is the second oldest high level programming language. Here, Lisp stands </span><span style=\"font-family: Cambria Math;\">for______ .</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> Lisp </span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2348;&#2360;&#2375; &#2346;&#2369;&#2352;&#2366;&#2344;&#2368; &#2313;&#2330;&#2381;&#2330; &#2360;&#2381;&#2340;&#2352;&#2368;&#2351; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2367;&#2306;&#2327; &#2349;&#2366;&#2359;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\">, Lisp </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">________ &#2404;</span></p>\r\n<p>&nbsp;</p>\n",
                    options_en: ["<p>Level Program</p>\n", "<p>Level Process</p>\n", 
                                "<p>List Program</p>\n", "<p>List Processing</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2354;&#2375;&#2357;&#2354; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\">(Level Program) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2354;&#2375;&#2357;&#2354; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;(Level Process)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2354;&#2367;&#2360;&#2381;&#2335; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;(List Program)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2354;&#2367;&#2360;&#2381;&#2335; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327;(List Processing) </span></p>\n"],
                    solution_en: "<p>5.(d) <span style=\"font-family: Cambria Math;\">The meaning of LISP is </span><span style=\"font-family: Cambria Math;\"><strong>List Processing</strong>.</span></p>\n",
                    solution_hi: "<p>5.(d)<span style=\"font-family: Cambria Math;\"> LISP </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2354;&#2367;&#2360;&#2381;&#2335; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327;</span><strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">List Processing</span></strong><span style=\"font-family: Cambria Math;\"><strong>)</strong> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> What does CO stand for in COBOL?</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">COBOL </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; </span><span style=\"font-family: Cambria Math;\">CO </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2325;&#2381;&#2351;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Common Object</p>\n", "<p>Common Oriented</p>\n", 
                                "<p>Common Operating</p>\n", "<p>Computer Oriented</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2325;&#2377;&#2350;&#2344; &#2323;&#2348;&#2381;&#2332;&#2375;&#2325;&#2335; (Common Object)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2377;&#2350;&#2344; &#2323;&#2352;&#2367;&#2319;&#2306;&#2335;&#2375;&#2337; (Common Oriented)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2325;&#2377;&#2350;&#2344; &#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; (Common Operating)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2323;&#2352;&#2367;&#2319;&#2306;&#2335;&#2375;&#2337; (Computer Oriented)</span></p>\n"],
                    solution_en: "<p>6.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">COBOL stands for </span><span style=\"font-family: Cambria Math;\"><strong>Common Business Oriented Language</strong> </span><span style=\"font-family: Cambria Math;\">which is designed for business uses.</span></p>\n",
                    solution_hi: "<p>6.(b)<span style=\"font-family: Cambria Math;\"> COBOL </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2350;&#2340;&#2354;&#2348; &#2325;&#2377;&#2350;&#2344; &#2348;&#2367;&#2332;&#2344;&#2375;&#2360; &#2323;&#2352;&#2367;&#2319;&#2306;&#2335;&#2375;&#2337; &#2354;&#2376;&#2306;&#2327;&#2381;&#2357;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"><strong>(Common Business Oriented Language)</strong> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2332;&#2367;&#2360;&#2375; &#2348;&#2367;&#2332;&#2344;&#2375;&#2360; &#2351;&#2370;&#2332; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2367;&#2332;&#2366;&#2311;&#2344; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">System running more than one process concurrently are called________ </span></p>\n",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2357;&#2352;&#2381;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346; &#2360;&#2375; &#2319;&#2325; &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366; &#2330;&#2354;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366;&nbsp; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\">(system) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2354;&#2366;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">________</span></p>\n",
                    options_en: ["<p>Multiprocessing</p>\n", "<p>Multiprogramming</p>\n", 
                                "<p>Real time</p>\n", "<p>Batch processing</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2350;&#2354;&#2381;&#2335;&#2368;&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; </span><span style=\"font-family: Cambria Math;\">(Multiprocessing) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2354;&#2381;&#2335;&#2368;&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2367;&#2306;&#2327; (Multiprogramming)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2352;&#2367;&#2351;&#2354; &#2335;&#2366;&#2311;&#2350;&nbsp; (Real time)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2348;&#2376;&#2330; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; (Batch processing)</span></p>\n"],
                    solution_en: "<p>7.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>Multiprogramming</strong> operating systems</span><span style=\"font-family: Cambria Math;\"> allow the execution of multiple processes by monitoring their process states and switching in between processes. It executes multiple programs to avoid CPU and memory underutilization</span><span style=\"font-family: Cambria Math;\">. for e.g.</span><span style=\"font-family: Cambria Math;\">MS-Excel,Firefox or Google Chrome browser.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Real time operating system(RTOS)</strong>&rarr;</span><span style=\"font-family: Cambria Math;\">These operating systems are designed to respond to an event within a predetermined Time for e.g. linux.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Batch processing</span></strong><span style=\"font-family: Cambria Math;\"> is a technique for automating and processing multiple transactions as a single group for e.g.</span><span style=\"font-family: Cambria Math;\">Unix.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Multiprocessing</strong>&rarr;</span><span style=\"font-family: Cambria Math;\">Multiprocessor operating systems are used in operating systems to boost the performance of multiple CPUs within a single computer system. for e.g.Windows NT, 2000, XP, and Unix.</span></p>\n",
                    solution_hi: "<p>7.(b) <span style=\"font-family: Cambria Math;\">&#2350;&#2354;&#2381;&#2335;&#2368;&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2367;&#2306;&#2327; &#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\"><strong>(Multiprogramming operating systems)</strong> </span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2344;&#2375; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360; &#2325;&#2368; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2325;&#2368; &#2344;&#2367;&#2327;&#2352;&#2366;&#2344;&#2368; &#2325;&#2352;&#2325;&#2375; &#2324;&#2352; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2375;&#2360; &#2325;&#2375; &#2348;&#2368;&#2330; &#2360;&#2381;&#2357;&#2367;&#2330; &#2325;&#2352;&#2325;&#2375; &#2325;&#2312; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\">(processes) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2319;&#2325;&#2381;&#2360;&#2375;&#2325;&#2381;&#2351;&#2370;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\">(execution) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; </span><span style=\"font-family: Cambria Math;\">CPU </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">(memory) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2325;&#2350; &#2313;&#2346;&#2351;&#2379;&#2327; &#2360;&#2375; &#2348;&#2330;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2312; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2319;&#2325;&#2381;&#2360;&#2375;&#2325;&#2381;&#2351;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\">(execute) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; </span><span style=\"font-family: Cambria Math;\">MS-Excel, Firefox </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366; </span><span style=\"font-family: Cambria Math;\">Google Chrome </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2352;&#2366;&#2313;&#2332;&#2364;&#2352;&#2404; </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2352;&#2368;&#2351;&#2354; &#2335;&#2366;&#2311;&#2350; &#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span></strong><span style=\"font-family: Cambria Math;\"><strong>(Real time operating system(RTOS)</strong> (</span><span style=\"font-family: Cambria Math;\">&#2310;&#2352;&#2335;&#2368;&#2323;&#2319;&#2360;</span><span style=\"font-family: Cambria Math;\">) &rarr; </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344; &#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\">(operating system) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2346;&#2370;&#2352;&#2381;&#2357; &#2344;&#2367;&#2352;&#2381;&#2343;&#2366;&#2352;&#2367;&#2340; &#2360;&#2350;&#2351; &#2325;&#2375; &#2349;&#2368;&#2340;&#2352; &#2325;&#2367;&#2360;&#2368; &#2328;&#2335;&#2344;&#2366; &#2325;&#2366; &#2332;&#2357;&#2366;&#2348; &#2342;&#2375;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2367;&#2332;&#2364;&#2366;&#2311;&#2344; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; &#2354;&#2367;&#2344;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(linux)&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2330; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\">(batch processing)</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2360;&#2350;&#2370;&#2361; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2312; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2325;&#2381;&#2358;&#2344;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(transaction) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2360;&#2381;&#2357;&#2330;&#2366;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">(automate) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2360;&#2306;&#2360;&#2366;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">(process) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2319;&#2325; &#2340;&#2325;&#2344;&#2368;&#2325; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; &#2351;&#2370;&#2344;&#2367;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(unix)</span><span style=\"font-family: Cambria Math;\">&#2404; </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2350;&#2354;&#2381;&#2335;&#2368;&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\">(multiprocessing)</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2350;&#2354;&#2381;&#2335;&#2368;&#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352; &#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">Multiprocessor operating system</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2350;&#2375;&#2306; &#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2375; &#2349;&#2368;&#2340;&#2352; &#2325;&#2312; &#2360;&#2368;&#2346;&#2368;&#2351;&#2370;</span><span style=\"font-family: Cambria Math;\">(cpu) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2344; &#2325;&#2379; &#2348;&#2338;&#2364;&#2366;&#2357;&#2366; &#2342;&#2375;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; </span><span style=\"font-family: Cambria Math;\">Windows NT, 2000, XP </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2351;&#2370;&#2344;&#2367;&#2325;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">(unix)</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "<p>8.<span style=\"font-family: Cambria Math;\"> Which of the following refers to the means by which an OS or any other program </span><span style=\"font-family: Cambria Math;\">interacts with the user?</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2360;&#2366;&#2343;&#2344; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2375; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2319;&#2325; </span><span style=\"font-family: Cambria Math;\">OS </span><span style=\"font-family: Cambria Math;\">&#2351;&#2366; &#2325;&#2379;&#2312; &#2309;&#2344;&#2381;&#2351; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; </span><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2332;&#2352; &#2325;&#2375; &#2360;&#2366;&#2341; &#2311;&#2306;&#2335;&#2352;&#2376;&#2325;&#2381;&#2335; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p>Program front-end</p>\n", "<p>Programming interface</p>\n", 
                                "<p>User login</p>\n", "<p>User interface</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2347;&#2381;&#2352;&#2306;&#2335;-&#2319;&#2306;&#2337; (Program front-end)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2367;&#2306;&#2327; &#2311;&#2306;&#2335;&#2352;&#2347;&#2364;&#2375;&#2360; (Programming interface)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2351;&#2370;&#2332;&#2352;&nbsp; &#2354;&#2377;&#2327;&#2367;&#2344; (User login)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2351;&#2370;&#2332;&#2352; &#2311;&#2306;&#2335;&#2352;&#2347;&#2375;&#2360; (User interface)</span></p>\n"],
                    solution_en: "<p>8.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>User interface</strong>&rarr;</span><span style=\"font-family: Cambria Math;\">T</span><span style=\"font-family: Cambria Math;\">he </span><span style=\"font-family: Cambria Math;\">user interface(UI) </span><span style=\"font-family: Cambria Math;\">is one of the most important </span><span style=\"font-family: Cambria Math;\">parts of any operating system. It allows users to easily access and communicate with the applications and the hardware. The user can interact with the computer by using mainly two kinds of interface: Graphical User Interface (GUI),Character User Interface (CUI).</span></p>\n",
                    solution_hi: "<p>8.(d)<span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2332;&#2352; &#2311;&#2306;&#2335;&#2352;&#2347;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\">(User interface)</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2351;&#2370;&#2332;&#2352; &#2311;&#2306;&#2335;&#2352;&#2347;&#2375;&#2360; </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">UI</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2360;&#2368; &#2349;&#2368; &#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339; &#2349;&#2366;&#2327; &#2350;&#2375;&#2306; &#2360;&#2375; &#2319;&#2325; &#2361;&#2376;&#2404; &#2351;&#2361; &#2351;&#2370;&#2332;&#2352;&#2360; &#2325;&#2379; &#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\">(application) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2361;&#2366;&#2352;&#2381;&#2337;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\">(hardware) </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325; &#2310;&#2360;&#2366;&#2344;&#2368; &#2360;&#2375; &#2346;&#2361;&#2369;&#2306;&#2330;&#2344;&#2375; &#2324;&#2352; &#2360;&#2306;&#2330;&#2366;&#2352; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2370;&#2332;&#2352; &#2350;&#2369;&#2326;&#2381;&#2351; &#2352;&#2370;&#2346; &#2360;&#2375; &#2342;&#2379; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2375; &#2311;&#2306;&#2335;&#2352;&#2347;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\">(interface) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2360;&#2366;&#2341; &#2348;&#2366;&#2340;&#2330;&#2368;&#2340; &#2325;&#2352; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Cambria Math;\">&#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325;&#2354; &#2351;&#2370;&#2332;&#2352; &#2311;&#2306;&#2335;&#2352;&#2347;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\">(Graphical User Interface) (GUI), </span><span style=\"font-family: Cambria Math;\">&#2325;&#2376;&#2352;&#2375;&#2325;&#2381;&#2335;&#2352; &#2351;&#2370;&#2332;&#2352; &#2311;&#2306;&#2335;&#2352;&#2347;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\">(Character User Interface) (CUI)</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> A collection of various programs that helps control your computer is called________ .</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2379; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2350;&#2342;&#2342; &#2325;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; </span><span style=\"font-family: Cambria Math;\">(various programs) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2325;&#2354;&#2375;&#2325;&#2381;&#2358;&#2344; &#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">_____ </span><span style=\"font-family: Cambria Math;\">&#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>system software</p>\n", "<p>application software</p>\n", 
                                "<p>Microsoft Excel</p>\n", "<p>Microsoft Word</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (system software)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2346;&#2381;&#2354;&#2368;&#2325;&#2375;&#2358;&#2344; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; (application software)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2360;&#2377;&#2347;&#2381;&#2335; &#2319;&#2325;&#2381;&#2360;&#2375;&#2354; (Microsoft Excel)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2360;&#2377;&#2347;&#2381;&#2335; &#2357;&#2352;&#2381;&#2337; (Microsoft Word)</span></p>\n"],
                    solution_en: "<p>9.(a)<strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">System software</span></strong><span style=\"font-family: Cambria Math;\"> consists of several programs, which are directly responsible for </span><span style=\"font-family: Cambria Math;\">controlling, integrating and managing </span><span style=\"font-family: Cambria Math;\">the individual hardware components of a computer system.</span></p>\n",
                    solution_hi: "<p>9.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"><strong>&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</strong> </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">&#2311;&#2360;&#2350;&#2375;&#2306; &#2325;&#2312; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2325;&#2375; &#2309;&#2354;&#2327;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2309;&#2354;&#2327; &#2361;&#2366;&#2352;&#2381;&#2337;&#2357;&#2375;&#2351;&#2352; &#2325;&#2306;&#2346;&#2379;&#2344;&#2375;&#2306;&#2335;&#2381;&#2360; </span><span style=\"font-family: Cambria Math;\">(hardware components) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; &#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2335;&#2375;&#2327;&#2381;&#2352;&#2335;&#2375; </span><span style=\"font-family: Cambria Math;\">(integrate) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2350;&#2376;&#2344;&#2375;&#2332; </span><span style=\"font-family: Cambria Math;\">(manage) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2325;&#2381;&#2359; &#2352;&#2370;&#2346; &#2360;&#2375; &#2332;&#2367;&#2350;&#2381;&#2350;&#2375;&#2342;&#2366;&#2352; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "31",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> Which of the following is system software?</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Device drivers</p>\n", "<p>Database programs</p>\n", 
                                "<p>Word processors</p>\n", "<p>Spreadsheets</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2337;&#2381;&#2352;&#2366;&#2311;&#2357;&#2352; (Device drivers)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; (Database programs)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2337; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352; (Word processors)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2346;&#2381;&#2352;&#2375;&#2337;&#2358;&#2368;&#2335; (Spreadsheets)</span></p>\n"],
                    solution_en: "<p>10.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">In the most fundamental sense, a</span><span style=\"font-family: Cambria Math;\"> device driver</span><span style=\"font-family: Cambria Math;\"> is a system software that acts like an interface between the device and the user.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Database programs </strong>&rarr; </span><span style=\"font-family: Cambria Math;\">Database software is used to create, edit, and maintain database files and records. The software also handles data storage, backup and reporting, multi-access control, and security for e.g. MY SQL.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Word processors</strong> &rarr; </span><span style=\"font-family: Cambria Math;\">It is a computer</span><span style=\"font-family: Cambria Math;\"> program used to write and revise documents, compose the layout of the text, and preview on a computer monitor how the printed copy will appear for e.g. MS Word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Spreadsheets</strong> &rarr; </span><span style=\"font-family: Cambria Math;\">A spreadsheet is a computer program that can capture, display and manipulate data arranged in rows and columns for e.g. MS Excel.</span></p>\n",
                    solution_hi: "<p>10.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2348;&#2360;&#2375; &#2350;&#2370;&#2354; &#2309;&#2352;&#2381;&#2341; &#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2337;&#2381;&#2352;&#2366;&#2311;&#2357;&#2352; </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2361;&#2376; &#2332;&#2379; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2324;&#2352; &#2351;&#2370;&#2332;&#2352; </span><span style=\"font-family: Cambria Math;\">(user) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2348;&#2368;&#2330; &#2319;&#2325; &#2311;&#2306;&#2335;&#2352;&#2347;&#2375;&#2360; </span><span style=\"font-family: Cambria Math;\">(interface) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2368; &#2340;&#2352;&#2361; &#2325;&#2366;&#2350; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2347;&#2364;&#2366;&#2311;&#2354; &#2324;&#2352; &#2352;&#2367;&#2325;&#2377;&#2352;&#2381;&#2337; &#2325;&#2379; &#2348;&#2344;&#2366;&#2344;&#2375; </span><span style=\"font-family: Cambria Math;\">(create), </span><span style=\"font-family: Cambria Math;\">&#2319;&#2337;&#2367;&#2335; </span><span style=\"font-family: Cambria Math;\">(edit) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2350;&#2375;&#2344;&#2381;&#2335;&#2375;&#2344; </span><span style=\"font-family: Cambria Math;\">(maintain) </span><span style=\"font-family: Cambria Math;\">&#2352;&#2326;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2337;&#2375;&#2335;&#2366; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; </span><span style=\"font-family: Cambria Math;\">(data storage), </span><span style=\"font-family: Cambria Math;\">&#2348;&#2376;&#2325;&#2309;&#2346; </span><span style=\"font-family: Cambria Math;\">(backup) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2352;&#2367;&#2346;&#2379;&#2352;&#2381;&#2335;&#2367;&#2306;&#2327; </span><span style=\"font-family: Cambria Math;\">(reporting), </span><span style=\"font-family: Cambria Math;\">&#2350;&#2354;&#2381;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2360; &#2325;&#2306;&#2335;&#2381;&#2352;&#2379;&#2354; </span><span style=\"font-family: Cambria Math;\">(multi-access control) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2360;&#2367;&#2325;&#2381;&#2351;&#2379;&#2352;&#2367;&#2335;&#2368; </span><span style=\"font-family: Cambria Math;\">(security) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2349;&#2368; &#2361;&#2376;&#2306;&#2337;&#2354; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">- MYSQL</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2337; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352;</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361; &#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; &#2325;&#2379; &#2354;&#2367;&#2326;&#2344;&#2375; &#2324;&#2352; &#2360;&#2306;&#2358;&#2379;&#2343;&#2367;&#2340; </span><span style=\"font-family: Cambria Math;\">(write and revise) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2325;&#2375; &#2354;&#2375;&#2310;&#2313;&#2335; &#2325;&#2379; &#2325;&#2306;&#2346;&#2379;&#2395; </span><span style=\"font-family: Cambria Math;\">(compose) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2350;&#2377;&#2344;&#2368;&#2335;&#2352; &#2346;&#2352; &#2346;&#2381;&#2352;&#2368;&#2357;&#2381;&#2351;&#2370; </span><span style=\"font-family: Cambria Math;\">(preview) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376; &#2325;&#2367; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335;&#2375;&#2337; &#2325;&#2377;&#2346;&#2368; </span><span style=\"font-family: Cambria Math;\">(printed copy) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2376;&#2360;&#2368; &#2342;&#2367;&#2326;&#2366;&#2312; &#2342;&#2375;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; </span><span style=\"font-family: Cambria Math;\">MS Word</span><span style=\"font-family: Cambria Math;\">&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2381;&#2352;&#2375;&#2337;&#2358;&#2368;&#2335;&#2381;&#2360;</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2381;&#2352;&#2375;&#2337;&#2358;&#2368;&#2335; &#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2376; &#2332;&#2379; &#2352;&#2380; &#2324;&#2352; &#2325;&#2377;&#2354;&#2350; </span><span style=\"font-family: Cambria Math;\">(rows and columns) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2367;&#2340; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2325;&#2376;&#2346;&#2381;&#2330;&#2352; </span><span style=\"font-family: Cambria Math;\">(capture), </span><span style=\"font-family: Cambria Math;\">&#2337;&#2367;&#2360;&#2381;&#2346;&#2381;&#2354;&#2375; </span><span style=\"font-family: Cambria Math;\">(display) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2350;&#2376;&#2344;&#2367;&#2346;&#2369;&#2354;&#2375;&#2335; </span><span style=\"font-family: Cambria Math;\">(manipulate) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2325;&#2375; &#2354;&#2367;&#2319; </span><span style=\"font-family: Cambria Math;\">MS </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2381;&#2360;&#2375;&#2354;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "31",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> Conversion of decimal number &nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>61</mn><mn>10</mn></msub></math> </span><span style=\"font-family: Cambria Math;\">&nbsp;to its binary number is equivalent of________ .</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2360;&#2368;&#2350;&#2354; &#2344;&#2306;&#2348;&#2352; </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>61</mn><mn>10</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2311;&#2360;&#2325;&#2375; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2344;&#2306;&#2348;&#2352; &#2311;&#2325;&#2381;&#2357;&#2367;&#2357;&#2376;&#2354;&#2375;&#2344;&#2381;&#2335; &#2350;&#2375;&#2306; &#2352;&#2370;&#2346;&#2366;&#2306;&#2340;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">___ </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>110011</mn><mo>)</mo></mrow><mn>2</mn></msub></math></p>\r\n<p>&nbsp;</p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>11001110</mn><mo>)</mo></mrow><mn>2</mn></msub></math></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>111101</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>11111</mn><mo>)</mo></mrow><mn>2</mn></msub></math></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>110011</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>11001110</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>111101</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>11111</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>11.(c) Step 1<span style=\"font-family: Cambria Math;\">: Divide (61)</span><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\"> successively by 2 until the quotient is 0:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">61/2 = 30, remainder is 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">30/2 = 15, remainder is 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">15/2 = 7, remainder is 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7/2 = 3, remainder is 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3/2 = 1, remainder is 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1/2 = 0, remainder is 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Step 2</span><span style=\"font-family: Cambria Math;\">: Read from the bottom MSB(Most significant Bit) to top LSB (Least significant Bit) as 111101.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, 111101 is the binary equivalent of decimal number 61 (Answer).</span></p>\n",
                    solution_hi: "<p>11.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2375;&#2346; </span><span style=\"font-family: Cambria Math;\">1:</span><span style=\"font-family: Cambria Math;\"> (61)</span><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2325;&#2381;&#2352;&#2350;&#2367;&#2325; &#2352;&#2370;&#2346; &#2360;&#2375; </span><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2375;&#2306; &#2332;&#2348; &#2340;&#2325; &#2325;&#2367; &#2349;&#2366;&#2327;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">0 </span><span style=\"font-family: Cambria Math;\">&#2344; &#2361;&#2379;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">61/2 = 30, </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">30/2 = 15, </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">0 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">15/2 = 7, </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7/2 = 3, </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3/2 = 1, </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1/2 = 0, </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2375;&#2346; </span><span style=\"font-family: Cambria Math;\">2: </span><span style=\"font-family: Cambria Math;\">&#2344;&#2368;&#2330;&#2375; </span><span style=\"font-family: Cambria Math;\">MSB (</span><span style=\"font-family: Cambria Math;\">&#2350;&#2379;&#2360;&#2381;&#2335; &#2360;&#2367;&#2327;&#2381;&#2344;&#2368;&#2347;&#2367;&#2325;&#2375;&#2306;&#2335; &#2348;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2358;&#2368;&#2352;&#2381;&#2359; </span><span style=\"font-family: Cambria Math;\">LSB (</span><span style=\"font-family: Cambria Math;\">&#2354;&#2368;&#2360;&#2381;&#2335; &#2360;&#2367;&#2327;&#2381;&#2344;&#2368;&#2347;&#2367;&#2325;&#2375;&#2306;&#2335; &#2348;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">111101 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2346;&#2338;&#2364;&#2375;&#2306;&#2404; &#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, 111101 </span><span style=\"font-family: Cambria Math;\">&#2337;&#2375;&#2360;&#2368;&#2350;&#2354; &#2344;&#2306;&#2348;&#2352; </span><span style=\"font-family: Cambria Math;\">61 (</span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2311;&#2325;&#2381;&#2357;&#2367;&#2357;&#2376;&#2354;&#2375;&#2344;&#2381;&#2335; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "31",
                    question_en: "<p>12.<span style=\"font-family: Cambria Math;\"> What is the advantage of DRAM ?</span></p>\n",
                    question_hi: "<p>12.<span style=\"font-family: Cambria Math;\"> DRAM </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2325;&#2381;&#2351;&#2366; &#2354;&#2366;&#2349; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>it can store more than that of SRAM</p>\n", "<p>it is cheaper than SRAM</p>\n", 
                                "<p>it is faster than SRAM</p>\n", "<p>data can be erased easily in it as compared to SRAM</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">&#2351;&#2361; </span><span style=\"font-family: Cambria Math;\">SRAM </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2360;&#2381;&#2335;&#2379;&#2352; &#2325;&#2352; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2351;&#2361; </span><span style=\"font-family: Cambria Math;\">SRAM </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2360;&#2360;&#2381;&#2340;&#2366; &#2361;&#2376;&#2404; </span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2351;&#2361; </span><span style=\"font-family: Cambria Math;\">SRAM </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2340;&#2375;&#2332; &#2361;&#2376;&#2404; </span></p>\n", "<p>SRAM <span style=\"font-family: Cambria Math;\">&#2325;&#2368; &#2340;&#2369;&#2354;&#2344;&#2366; &#2350;&#2375;&#2306; &#2311;&#2360;&#2350;&#2375;&#2306; &#2337;&#2366;&#2335;&#2366; &#2325;&#2379; &#2310;&#2360;&#2366;&#2344;&#2368; &#2360;&#2375; &#2350;&#2367;&#2335;&#2366;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404; </span></p>\n"],
                    solution_en: "<p>12.(b)<span style=\"font-family: Cambria Math;\"> Some of the advantages of <strong>DRAM</strong> are it is slower,</span><span style=\"font-family: Cambria Math;\"> less- expensive</span><span style=\"font-family: Cambria Math;\"> and occupies </span><span style=\"font-family: Cambria Math;\">less space on the computer&rsquo;s motherboard.</span></p>\n",
                    solution_hi: "<p>12.(b)<span style=\"font-family: Cambria Math;\"> <strong>DRAM</strong> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2325;&#2369;&#2331; &#2354;&#2366;&#2349; &#2351;&#2361; &#2361;&#2376;&#2306; &#2325;&#2367; &#2351;&#2361; &#2343;&#2368;&#2350;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2350; &#2326;&#2352;&#2381;&#2330;&#2368;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2324;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2350;&#2342;&#2352;&#2348;&#2379;&#2352;&#2381;&#2337; &#2346;&#2352; &#2325;&#2350; &#2360;&#2381;&#2346;&#2375;&#2360; &#2328;&#2375;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "31",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> Which of the following is a binary number equivalent to octal number <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mo>.</mo><mn>431</mn><mo>)</mo></mrow><mn>8</mn></msub></math></span><span style=\"font-family: Cambria Math;\">&nbsp;?</span></p>\n",
                    question_hi: "<p>13.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2319;&#2325; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2344;&#2306;&#2348;&#2352; &#2321;&#2325;&#2381;&#2335;&#2354; &#2344;&#2306;&#2348;&#2352; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mo>.</mo><mn>431</mn><mo>)</mo></mrow><mn>8</mn></msub></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mo>.</mo><mn>100110001</mn><mo>)</mo></mrow><mn>2</mn></msub></math></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mo>.</mo><mn>100011001</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>100110100</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>100011001</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mo>.</mo><mn>100110001</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mo>.</mo><mn>100011001</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>100110100</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>100011001</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p>13.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">In numeral systems, we know octal is base-8 and binary is base-2. To convert octal 431 to binary, you follow these steps:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">To do this, first convert octal into decimal, then the resulting decimal into binary</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Start from one\'s place in octal : multiply ones place with 8^0, tens place with 8^1, hundreds place with 8^2 and so on from right to left</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Add all the products we got from step 1 to get the decimal equivalent of the given octal value.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then, divide the decimal value we got from step-2 by 2 keeping notice of the quotient and the remainder.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Continue dividing the quotient by 2 until you get a quotient of zero.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then just write out the remainder in the reverse order to get the binary equivalent of the decimal number.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">First, convert <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>431</mn><mn>8</mn></msub></math></span><span style=\"font-family: Cambria Math;\">into decimal, by using above steps:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>431</mn><mn>8</mn></msub></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=</span><span style=\"font-family: Cambria Math;\"> 4 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> + 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>1</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> + 1 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>0</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>281</mn><mn>10</mn></msub></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Now, we have to convert 281</span><span style=\"font-family: Cambria Math;\">10</span><span style=\"font-family: Cambria Math;\"> to binary</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">281 / 2 = 140 with remainder 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">140 / 2 = 70 with remainder 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">70 / 2 = 35 with remainder 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">35 / 2 = 17 with remainder 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">17 / 2 = 8 with remainder 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8 / 2 = 4 with remainder 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4 / 2 = 2 with remainder 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2 / 2 = 1 with remainder 0</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 / 2 = 0 with remainder 1</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then just write down the remainders in the reverse order to get the answer, The octal number 431 converted to binary is therefore equal to :<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>100011001</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span></p>\n",
                    solution_hi: "<p>13.(a) <span style=\"font-family: Cambria Math;\">&#2344;&#2381;&#2351;&#2370;&#2350;&#2352;&#2354; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; </span><span style=\"font-family: Cambria Math;\">(numeral system) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2361;&#2350; &#2332;&#2366;&#2344;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; &#2321;&#2325;&#2381;&#2335;&#2354; &#2348;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\">-8 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2324;&#2352; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2348;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\">-2 </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376;&#2404; &#2321;&#2325;&#2381;&#2335;&#2354; </span><span style=\"font-family: Cambria Math;\">431 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2311;&#2344; &#2360;&#2381;&#2335;&#2375;&#2346; &#2325;&#2366; &#2346;&#2366;&#2354;&#2344; &#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">&#2320;&#2360;&#2366; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2354;&#2375; &#2321;&#2325;&#2381;&#2335;&#2354; &#2325;&#2379; &#2337;&#2375;&#2360;&#2368;&#2350;&#2354; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2347;&#2367;&#2352; &#2352;&#2367;&#2332;&#2354;&#2381;&#2335;&#2367;&#2306;&#2327; &#2337;&#2375;&#2360;&#2368;&#2350;&#2354; &#2325;&#2379; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354;&#2375;&#2306;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2321;&#2325;&#2381;&#2335;&#2354; &#2350;&#2375;&#2306; &#2319;&#2325; &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2360;&#2375; &#2358;&#2369;&#2352;&#2370; &#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Cambria Math;\">&#2311;&#2325;&#2366;&#2312; &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">8^0 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2327;&#2369;&#2339;&#2366; &#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2342;&#2361;&#2366;&#2312; &#2325;&#2375; &#2360;&#2381;&#2341;&#2366;&#2344; &#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">8^1 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2380; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2366;&#2344; &#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">8^2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2327;&#2369;&#2339;&#2366; &#2325;&#2352;&#2375;&#2306; &#2324;&#2352; &#2311;&#2360;&#2368; &#2340;&#2352;&#2361; &#2342;&#2366;&#2319;&#2306; &#2360;&#2375; &#2348;&#2366;&#2319;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2319; &#2327;&#2319; &#2321;&#2325;&#2381;&#2335;&#2354; &#2350;&#2366;&#2344; &#2325;&#2375; &#2337;&#2375;&#2360;&#2368;&#2350;&#2354; &#2311;&#2325;&#2381;&#2357;&#2367;&#2357;&#2376;&#2354;&#2375;&#2344;&#2381;&#2335; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2330;&#2352;&#2339; </span><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2360;&#2349;&#2368; &#2327;&#2369;&#2339;&#2344;&#2347;&#2354; &#2325;&#2379; &#2332;&#2379;&#2337;&#2364;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">&#2347;&#2367;&#2352; &#2349;&#2366;&#2327;&#2347;&#2354; &#2324;&#2352; &#2358;&#2375;&#2359;&#2347;&#2354; &#2325;&#2366; &#2343;&#2381;&#2351;&#2366;&#2344; &#2352;&#2326;&#2340;&#2375; &#2361;&#2369;&#2319; &#2330;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">-2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2337;&#2375;&#2360;&#2368;&#2350;&#2354; &#2350;&#2366;&#2344; &#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">&#2349;&#2366;&#2327;&#2347;&#2354; &#2325;&#2379; </span><span style=\"font-family: Cambria Math;\">2 </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2340;&#2348; &#2340;&#2325; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2344;&#2366; &#2332;&#2366;&#2352;&#2368; &#2352;&#2326;&#2375;&#2306; &#2332;&#2348; &#2340;&#2325; &#2310;&#2346;&#2325;&#2379; &#2349;&#2366;&#2327;&#2347;&#2354; &#2358;&#2370;&#2344;&#2381;&#2351; &#2344; &#2350;&#2367;&#2354; &#2332;&#2366;&#2319;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">&#2347;&#2367;&#2352; &#2342;&#2358;&#2350;&#2354;&#2357; &#2360;&#2306;&#2326;&#2381;&#2351;&#2366; &#2325;&#2375; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2311;&#2325;&#2381;&#2357;&#2367;&#2357;&#2376;&#2354;&#2375;&#2344;&#2381;&#2335; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2360; &#2358;&#2375;&#2359; &#2325;&#2379; &#2313;&#2354;&#2381;&#2335;&#2375; &#2325;&#2381;&#2352;&#2350; &#2350;&#2375;&#2306; &#2354;&#2367;&#2326;&#2375;&#2306;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2330;&#2352;&#2339;&#2379;&#2306; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2325;&#2375; &#2346;&#2361;&#2354;&#2375; </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>431</mn><mn>8</mn></msub></math> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2342;&#2358;&#2350;&#2354;&#2357; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>431</mn><mn>8</mn></msub></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=</span><span style=\"font-family: Cambria Math;\"> 4 &times; </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> + 3 &times; </span><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>1</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> + 1 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>8</mn><mn>0</mn></msup></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>281</mn><mn>10</mn></msub></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\">, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mn>281</mn><mn>10</mn></msub></math></span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2350;&#2375;&#2306; &#2348;&#2342;&#2354;&#2375; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">281/2 = 140 </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2360;&#2366;&#2341;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">140/2 = 70 </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">0 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2360;&#2366;&#2341;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">70/2 = 35 </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">0 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2360;&#2366;&#2341;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">35/2 = 17 </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2360;&#2366;&#2341;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">17/2 = 8 </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2360;&#2366;&#2341;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">8/2 = 4 </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">0 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2360;&#2366;&#2341;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">4/2 = 2 </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">0 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2360;&#2366;&#2341;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2/2 = 1 </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">0 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2360;&#2366;&#2341;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1/2 = 0 </span><span style=\"font-family: Cambria Math;\">&#2358;&#2375;&#2359;&#2347;&#2354; </span><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2360;&#2366;&#2341;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&#2347;&#2367;&#2352; &#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2348;&#2360; &#2358;&#2375;&#2359; &#2325;&#2379; &#2313;&#2354;&#2381;&#2335;&#2375; &#2325;&#2381;&#2352;&#2350; &#2350;&#2375;&#2306; &#2354;&#2367;&#2326;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2321;&#2325;&#2381;&#2335;&#2354; &#2344;&#2306;&#2348;&#2352; </span><span style=\"font-family: Cambria Math;\">431 </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2348;&#2366;&#2311;&#2344;&#2352;&#2368; &#2350;&#2375;&#2306; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2361;&#2376; &#2311;&#2360;&#2354;&#2367;&#2319; &#2311;&#2360;&#2325;&#2375; &#2348;&#2352;&#2366;&#2348;&#2352; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">:&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mn>100110001</mn><mo>)</mo></mrow><mn>2</mn></msub></math></span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "31",
                    question_en: "<p>14.<span style=\"font-family: Cambria Math;\"> MS-Word is a text or document editing application program that comes in the package of </span><span style=\"font-family: Cambria Math;\">MS-Office Suite. Which among the given options is not related with MS-Word? </span></p>\n",
                    question_hi: "<p>14.<span style=\"font-family: Cambria Math;\"> MS-</span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2337; &#2319;&#2325; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2351;&#2366; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; &#2319;&#2337;&#2367;&#2335;&#2367;&#2306;&#2327; &#2319;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2358;&#2344; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; </span><span style=\"font-family: Cambria Math;\">(text or document editing application program) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2332;&#2379; </span><span style=\"font-family: Cambria Math;\">MS-</span><span style=\"font-family: Cambria Math;\">&#2321;&#2347;&#2367;&#2360; &#2360;&#2370;&#2335; </span><span style=\"font-family: Cambria Math;\">(Office Suite) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2346;&#2376;&#2325;&#2375;&#2332; </span><span style=\"font-family: Cambria Math;\">(package) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306; &#2310;&#2340;&#2366; &#2361;&#2376;&#2404; &#2342;&#2367;&#2319; &#2327;&#2319; &#2357;&#2367;&#2325;&#2354;&#2381;&#2346;&#2379;&#2306; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; </span><span style=\"font-family: Cambria Math;\">MS-</span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2337; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Page Layout</p>\n", "<p>Antivirus</p>\n", 
                                "<p>Mailings</p>\n", "<p>Format Painter</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2346;&#2375;&#2332; &#2354;&#2375;&#2310;&#2313;&#2335; (Page Layout)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2319;&#2306;&#2335;&#2368;&#2357;&#2366;&#2351;&#2352;&#2360; (Antivirus)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2350;&#2375;&#2354;&#2367;&#2306;&#2327; (Mailings)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2347;&#2377;&#2352;&#2381;&#2350;&#2375;&#2335; &#2346;&#2375;&#2306;&#2335;&#2352; (Format Painter)</span></p>\n"],
                    solution_en: "<p>14.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Word processing</span><span style=\"font-family: Cambria Math;\"> is the composition,editing,formatting it helps us in sending mails etc. <strong>Antivirus</strong> is not a part of MS-Office Suite.</span></p>\n",
                    solution_hi: "<p>14.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2337; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> (word processing) </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2325;&#2350;&#2381;&#2346;&#2379;&#2332;&#2368;&#2358;&#2344; </span><span style=\"font-family: Cambria Math;\">(composition), </span><span style=\"font-family: Cambria Math;\">&#2319;&#2337;&#2367;&#2335;&#2367;&#2306;&#2327; </span><span style=\"font-family: Cambria Math;\">(editing), </span><span style=\"font-family: Cambria Math;\">&#2347;&#2377;&#2352;&#2381;&#2350;&#2375;&#2335;&#2367;&#2306;&#2327; </span><span style=\"font-family: Cambria Math;\">(formatting) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2332;&#2379; &#2361;&#2350;&#2375;&#2306; &#2350;&#2375;&#2354; &#2310;&#2342;&#2367; &#2349;&#2375;&#2332;&#2344;&#2375; &#2350;&#2375;&#2306; &#2350;&#2342;&#2342; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; <strong>&#2319;&#2306;&#2335;&#2368;&#2357;&#2366;&#2351;&#2352;&#2360;</strong> </span><span style=\"font-family: Cambria Math;\">MS-</span><span style=\"font-family: Cambria Math;\">&#2321;&#2347;&#2367;&#2360; &#2360;&#2370;&#2335; &#2325;&#2366; &#2361;&#2367;&#2360;&#2381;&#2360;&#2366; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "31",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> In computing, IP address means.</span></p>\n",
                    question_hi: "<p>15.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2367;&#2306;&#2327; &#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2310;&#2312;&#2346;&#2368; &#2319;&#2337;&#2381;&#2352;&#2375;&#2360; &#2325;&#2366; &#2350;&#2340;&#2354;&#2348; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Internet Protocol</p>\n", "<p>Invalid Pin</p>\n", 
                                "<p>Insert Pin</p>\n", "<p>International Pin</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2335; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354;(Internet Protocol)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2311;&#2344;&#2357;&#2376;&#2354;&#2367;&#2337; &#2346;&#2367;&#2344;(Invalid Pin)</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2381;&#2360;&#2352;&#2381;&#2335; &#2346;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\">(Insert Pin) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2346;&#2367;&#2344;(International Pin)</span></p>\n"],
                    solution_en: "<p>15.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">IP address stands for <strong>&ldquo;</strong></span><strong><span style=\"font-family: Cambria Math;\">Internet Protocol address\"</span></strong><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    solution_hi: "<p>15.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">IP</span><span style=\"font-family: Cambria Math;\"> &#8203;&#8203;&#8203;&#8203;</span><span style=\"font-family: Cambria Math;\">&#2346;&#2340;&#2366; &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; </span><strong><span style=\"font-family: Cambria Math;\">\"</span><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2335; &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354; &#2346;&#2340;&#2366;</span></strong><span style=\"font-family: Cambria Math;\"><strong>\"</strong>&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "16",
                    section: "31",
                    question_en: "<p>16.<span style=\"font-family: Cambria Math;\"> Like a virus, it is also a self-replicating program. The difference between a virus </span><span style=\"font-family: Cambria Math;\">and it is that a worm does not create copies of itself on one system it propagates </span><span style=\"font-family: Cambria Math;\">through computer networks.</span></p>\n",
                    question_hi: "<p>16.<span style=\"font-family: Cambria Math;\">&#2357;&#2366;&#2351;&#2352;&#2360; &#2325;&#2368; &#2340;&#2352;&#2361; &#2351;&#2361; &#2349;&#2368; &#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2325;&#2371;&#2340;&#2367; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2376;&#2404; &#2319;&#2325; &#2357;&#2366;&#2351;&#2352;&#2360; &#2325;&#2375; &#2348;&#2368;&#2330; &#2325;&#2366; &#2309;&#2306;&#2340;&#2352; </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2351;&#2361; &#2361;&#2376; &#2325;&#2367; &#2319;&#2325; &#2357;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\">(worm) </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368; &#2346;&#2352; &#2326;&#2369;&#2342; &#2325;&#2368; &#2346;&#2381;&#2352;&#2340;&#2367;&#2351;&#2366;&#2306; &#2344;&#2361;&#2368;&#2306; &#2348;&#2344;&#2366;&#2340;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2375; &#2357;&#2361; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2346;&#2381;&#2352;&#2330;&#2366;&#2352;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Worm</p>\n", "<p>Keylogger</p>\n", 
                                "<p>Cracker</p>\n", "<p>None of these</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2350; (worm)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2368;&#2354;&#2377;&#2327;&#2352;(keylogger)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2325;&#2381;&#2352;&#2376;&#2325;&#2352; (cracker)</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#2311;&#2344;&#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2379;&#2312; &#2344;&#2361;&#2368;&#2306;</span></p>\n"],
                    solution_en: "<p>16.(b)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Cambria Math;\">computer <strong>worm</strong></span><span style=\"font-family: Cambria Math;\"> is a type of malware whose primary function is to self-replicate and infect other computers while remaining active on infected systems. A computer worm duplicates itself to spread to uninfected computers. </span><strong><span style=\"font-family: Cambria Math;\">Keylogger</span></strong><span style=\"font-family: Cambria Math;\">&rarr;</span><span style=\"font-family: Cambria Math;\">It </span><span style=\"font-family: Cambria Math;\"> is a type of malicious software that records every keystroke you make on your computer. Keyloggers are a type of spyware &mdash; malware designed to spy on victims. </span><span style=\"font-family: Cambria Math;\"><strong>Cracker</strong> </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">Cracker </span><span style=\"font-family: Cambria Math;\">an individual who attempts to access computer systems without authorization. </span></p>\n",
                    solution_hi: "<p>16.(b) <span style=\"font-family: Cambria Math;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; <strong>&#2357;&#2352;&#2381;&#2350;</strong></span><span style=\"font-family: Cambria Math;\"><strong>(worm)</strong> </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2350;&#2376;&#2354;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\">(malware) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2346;&#2381;&#2352;&#2366;&#2341;&#2350;&#2367;&#2325; &#2325;&#2366;&#2352;&#2381;&#2351; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2367;&#2340; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2346;&#2352; &#2360;&#2325;&#2381;&#2352;&#2367;&#2351; &#2352;&#2361;&#2340;&#2375; &#2361;&#2369;&#2319; &#2309;&#2344;&#2381;&#2351; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;&#2379;&#2306; &#2325;&#2379; &#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2342;&#2379;&#2361;&#2352;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">(self-replicating) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2367;&#2340; &#2325;&#2352;&#2344;&#2366; &#2361;&#2376;&#2404; &#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2357;&#2352;&#2381;&#2350; &#2326;&#2369;&#2342; &#2325;&#2379; &#2309;&#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2367;&#2340; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;&#2379;&#2306; &#2350;&#2375;&#2306; &#2347;&#2376;&#2354;&#2366;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2369;&#2346;&#2381;&#2354;&#2367;&#2325;&#2375;&#2335; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong><span style=\"font-family: Cambria Math;\">&#2325;&#2368;&#2354;&#2377;&#2327;&#2352;</span><span style=\"font-family: Cambria Math;\">(keylogger)</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361; &#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2342;&#2369;&#2352;&#2381;&#2349;&#2366;&#2357;&#2344;&#2366;&#2346;&#2370;&#2352;&#2381;&#2339; &#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">malicious software</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2332;&#2379; &#2310;&#2346;&#2325;&#2375; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2346;&#2352; &#2310;&#2346;&#2325;&#2375; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2325;&#2367;&#2319; &#2327;&#2319; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325; &#2325;&#2368;&#2360;&#2381;&#2335;&#2381;&#2352;&#2379;&#2325; &#2325;&#2379; &#2352;&#2367;&#2325;&#2377;&#2352;&#2381;&#2337; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2325;&#2368;&#2354;&#2377;&#2327;&#2352; &#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2360;&#2381;&#2346;&#2366;&#2311;&#2357;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\">(spyware) </span><span style=\"font-family: Cambria Math;\">&#2361;&#2376; &#2332;&#2379; &#2346;&#2368;&#2337;&#2364;&#2367;&#2340;&#2379;&#2306; &#2325;&#2368; &#2332;&#2366;&#2360;&#2370;&#2360;&#2368; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2367;&#2332;&#2364;&#2366;&#2311;&#2344; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2350;&#2376;&#2354;&#2357;&#2375;&#2351;&#2352;&#2404; </span><strong><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2376;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\">(cracker)</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2352;&#2376;&#2325;&#2352; &#2319;&#2325; &#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367; &#2361;&#2376; &#2332;&#2379; &#2348;&#2367;&#2344;&#2366; &#2346;&#2381;&#2352;&#2366;&#2343;&#2367;&#2325;&#2352;&#2339; &#2325;&#2375; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2340;&#2325; &#2346;&#2361;&#2369;&#2305;&#2330;&#2344;&#2375; &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2366;&#2360; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "17",
                    section: "31",
                    question_en: "<p>17.<span style=\"font-family: Cambria Math;\"> What is the overall term for creating, editing, formatting, storing, retrieving a text </span><span style=\"font-family: Cambria Math;\">document? </span></p>\n",
                    question_hi: "<p>17.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; </span><span style=\"font-family: Cambria Math;\">(text document) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2379; &#2325;&#2381;&#2352;&#2367;&#2319;&#2335; </span><span style=\"font-family: Cambria Math;\">(create), </span><span style=\"font-family: Cambria Math;\">&#2319;&#2337;&#2367;&#2335; </span><span style=\"font-family: Cambria Math;\">(edit), </span><span style=\"font-family: Cambria Math;\">&#2347;&#2377;&#2352;&#2381;&#2350;&#2375;&#2335; </span><span style=\"font-family: Cambria Math;\">(format), </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2379;&#2352; </span><span style=\"font-family: Cambria Math;\">(store) </span><span style=\"font-family: Cambria Math;\">&#2324;&#2352; &#2346;&#2369;&#2344;&#2307; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2360;&#2350;&#2327;&#2381;&#2352; &#2358;&#2348;&#2381;&#2342; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Word processing</p>\n", "<p>Spreadsheet design</p>\n", 
                                "<p>Web design</p>\n", "<p>Database management</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2337; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327;(Word processing)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2346;&#2381;&#2352;&#2375;&#2337;&#2358;&#2368;&#2335; &#2337;&#2367;&#2332;&#2366;&#2311;&#2344;( Spreadsheet design)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2357;&#2375;&#2348; &#2337;&#2367;&#2332;&#2364;&#2366;&#2311;&#2344;(Web design)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2337;&#2375;&#2335;&#2366;&#2348;&#2375;&#2360; &#2350;&#2376;&#2344;&#2375;&#2332;&#2350;&#2375;&#2306;&#2335;(Database management)</span></p>\n"],
                    solution_en: "<p>17.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">Text processing or </span><span style=\"font-family: Cambria Math;\"><strong>word processing</strong> </span><span style=\"font-family: Cambria Math;\">is the process of creating, editing, formatting, storing, retrieving and printing a text document.</span></p>\n",
                    solution_hi: "<p>17.(a)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; &#2351;&#2366; </span><strong><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2337; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327;</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2337;&#2377;&#2325;&#2381;&#2351;&#2370;&#2350;&#2375;&#2306;&#2335; &#2348;&#2344;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2319;&#2337;&#2367;&#2335; &#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2347;&#2377;&#2352;&#2381;&#2350;&#2375;&#2335; &#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2335;&#2379;&#2352; &#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2344;&#2352;&#2381;&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2346;&#2381;&#2352;&#2367;&#2306;&#2335; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366; &#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "18",
                    section: "31",
                    question_en: "<p>18.<span style=\"font-family: Cambria Math;\"> Which one of the following input devices is user-programmable? </span></p>\n",
                    question_hi: "<p>18.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2311;&#2344;&#2346;&#2369;&#2335; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2351;&#2370;&#2332;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2375;&#2348;&#2354; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Dumb terminal</p>\n", "<p>Smart terminal</p>\n", 
                                "<p>VDT</p>\n", "<p>Intelligent terminal</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2337;&#2350;&#2381;&#2348; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2354; (Dumb terminal)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2350;&#2366;&#2352;&#2381;&#2335; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2354; (Smart terminal)</span></p>\n",
                                "<p>VDT</p>\n", "<p><span style=\"font-weight: 400;\">&#2311;&#2306;&#2335;&#2375;&#2354;&#2367;&#2332;&#2375;&#2306;&#2335; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2354; (Intelligent terminal)</span></p>\n"],
                    solution_en: "<p>18.(b) <strong>Intelligent terminal </strong>&rarr; <span style=\"font-family: Cambria Math;\">A computer operating terminal that can carry out some data processing, as well as sending data to and receiving it from a central processor.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Dumb terminal</strong> &rarr; </span><span style=\"font-family: Cambria Math;\">A </span><span style=\"font-family: Cambria Math;\">dumb terminal</span><span style=\"font-family: Cambria Math;\"> is simply an output device that </span><span style=\"font-family: Cambria Math;\">accepts data from the CPU. Examples are monitor or speakers</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>VDT(Video Display Terminal)</strong> &rarr;</span><span style=\"font-family: Cambria Math;\"> It enables the operator to see and correct the words as they are being typed.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Smart terminal</strong> &rarr; </span><span style=\"font-family: Cambria Math;\">Block-oriented terminal, which typically offloads form or panel editing from a mainframe computer.</span></p>\n",
                    solution_hi: "<p>18.(b) <strong><span style=\"font-family: Cambria Math;\">&#2311;&#2306;&#2335;&#2375;&#2354;&#2367;&#2332;&#2375;&#2306;&#2335; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2354;</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2354; &#2332;&#2379; &#2325;&#2369;&#2331; &#2337;&#2375;&#2335;&#2366; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2367;&#2306;&#2327; &#2325;&#2352; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2341; &#2361;&#2368; &#2360;&#2375;&#2306;&#2335;&#2381;&#2352;&#2354; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352; &#2360;&#2375; &#2337;&#2375;&#2335;&#2366; &#2349;&#2375;&#2332; &#2324;&#2352; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2325;&#2352; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">&#2337;&#2306;&#2348; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2354;</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2337;&#2306;&#2348; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2354; &#2325;&#2375;&#2357;&#2354; &#2319;&#2325; &#2310;&#2313;&#2335;&#2346;&#2369;&#2335; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2361;&#2376; &#2332;&#2379; </span><span style=\"font-family: Cambria Math;\">CPU </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375; &#2337;&#2375;&#2335;&#2366; &#2360;&#2381;&#2357;&#2368;&#2325;&#2366;&#2352; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2350;&#2377;&#2344;&#2367;&#2335;&#2352; &#2351;&#2366; &#2360;&#2381;&#2346;&#2368;&#2325;&#2352; &#2361;&#2376;&#2306;&#2404;</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">VDT (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2368;&#2337;&#2367;&#2351;&#2379; &#2337;&#2367;&#2360;&#2381;&#2346;&#2381;&#2354;&#2375; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2354;</span><span style=\"font-family: Cambria Math;\">)</span></strong><span style=\"font-family: Cambria Math;\"> &rarr; </span><span style=\"font-family: Cambria Math;\">&#2351;&#2361; &#2321;&#2346;&#2352;&#2375;&#2335;&#2352; &#2325;&#2379; &#2358;&#2348;&#2381;&#2342;&#2379;&#2306; &#2325;&#2379; &#2335;&#2366;&#2311;&#2346; &#2325;&#2367;&#2319; &#2332;&#2366;&#2344;&#2375; &#2346;&#2352; &#2342;&#2375;&#2326;&#2344;&#2375; &#2324;&#2352; &#2360;&#2361;&#2368; &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2360;&#2325;&#2381;&#2359;&#2350; &#2348;&#2344;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>&#2360;&#2381;&#2350;&#2366;&#2352;&#2381;&#2335; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2354;</strong> </span><span style=\"font-family: Cambria Math;\">&rarr; </span><span style=\"font-family: Cambria Math;\">&#2348;&#2381;&#2354;&#2377;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2323;&#2352;&#2367;&#2319;&#2306;&#2335;&#2375;&#2337; &#2335;&#2352;&#2381;&#2350;&#2367;&#2344;&#2354;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2332;&#2379; &#2310;&#2350;&#2340;&#2380;&#2352; &#2346;&#2352; &#2350;&#2375;&#2344;&#2347;&#2381;&#2352;&#2375;&#2350; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2375; &#2347;&#2377;&#2352;&#2381;&#2350; &#2351;&#2366; &#2346;&#2376;&#2344;&#2354; &#2319;&#2337;&#2367;&#2335;&#2367;&#2306;&#2327; &#2325;&#2379; &#2321;&#2347;&#2354;&#2379;&#2337; </span><span style=\"font-family: Cambria Math;\">(offload) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "19",
                    section: "31",
                    question_en: "<p>19.<span style=\"font-family: Cambria Math;\"> Which of the following is not related to application software?</span></p>\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2319;&#2346;&#2381;&#2354;&#2368;&#2325;&#2375;&#2358;&#2344; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2344;&#2361;&#2368;&#2306; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Word processor</p>\n", "<p>DBMS</p>\n", 
                                "<p>Railway reservation system</p>\n", "<p>Operating system</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2357;&#2352;&#2381;&#2337; &#2346;&#2381;&#2352;&#2379;&#2360;&#2375;&#2360;&#2352; (Word processor)</span></p>\n", "<p>DBMS</p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2352;&#2375;&#2354;&#2357;&#2375; &#2352;&#2367;&#2332;&#2352;&#2381;&#2357;&#2375;&#2358;&#2344; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; (Railway reservation system)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; (Operating system)</span></p>\n"],
                    solution_en: "<p>19.(d)<span style=\"font-family: Cambria Math;\"> Operating system is an example of </span><strong><span style=\"font-family: Cambria Math;\">system software</span></strong><span style=\"font-family: Cambria Math;\">. And all the others are examples of application software.</span></p>\n",
                    solution_hi: "<p>19.(d)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2321;&#2346;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; </span><span style=\"font-family: Cambria Math;\">(OS) </span><strong><span style=\"font-family: Cambria Math;\">&#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; </span><span style=\"font-family: Cambria Math;\">(system software)</span></strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2319;&#2325; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2361;&#2376;&#2404; &#2324;&#2352; &#2348;&#2366;&#2325;&#2368; &#2360;&#2349;&#2368; &#2319;&#2346;&#2381;&#2354;&#2368;&#2325;&#2375;&#2358;&#2344; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; </span><span style=\"font-family: Cambria Math;\">(application software) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20. <span style=\"font-family: Cambria Math;\">Which of the following terms is associated with Internet E-mail?</span></p>\n",
                    question_hi: "<p>20. <span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2358;&#2348;&#2381;&#2342; &#2311;&#2306;&#2335;&#2352;&#2344;&#2375;&#2335; &#2312;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2354; &#2360;&#2375; &#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340; &#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Plotter</p>\n", "<p>Slide presentation</p>\n", 
                                "<p>Bookmark</p>\n", "<p>Pie chart</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2354;&#2377;&#2335;&#2352;(plotter)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2381;&#2354;&#2366;&#2311;&#2337; &#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2369;&#2340;&#2367;(slide presentation)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2348;&#2369;&#2325;&#2350;&#2366;&#2352;&#2381;&#2325;(bookmark)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2346;&#2366;&#2312; &#2330;&#2366;&#2352;&#2381;&#2335;(pie chart)</span></p>\n"],
                    solution_en: "<p>20.(c)<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">In Outlook, the <strong>Bookmark</strong> feature can help you find or skip to a specific place at ease without scrolling through large blocks of text.Plotters are used </span><span style=\"font-family: Cambria Math;\">to print graphical output on paper</span><span style=\"font-family: Cambria Math;\">. It interprets computer commands and makes line drawings on paper using multicolored automated pens.</span></p>\n",
                    solution_hi: "<p>20.(c) <span style=\"font-family: Cambria Math;\">&#2310;&#2313;&#2335;&#2354;&#2369;&#2325;</span><span style=\"font-family: Cambria Math;\">(outlook) </span><span style=\"font-family: Cambria Math;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><strong><span style=\"font-family: Cambria Math;\">&#2348;&#2369;&#2325;&#2350;&#2366;&#2352;&#2381;&#2325;</span></strong><span style=\"font-family: Cambria Math;\"><strong>(bookmark)</strong> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2357;&#2367;&#2343;&#2366; &#2310;&#2346;&#2325;&#2379; &#2346;&#2366;&#2336; &#2325;&#2375; &#2348;&#2337;&#2364;&#2375; &#2348;&#2381;&#2354;&#2377;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\">(block of text) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2360;&#2381;&#2325;&#2381;&#2352;&#2377;&#2354; &#2325;&#2367;&#2319; &#2348;&#2367;&#2344;&#2366; &#2310;&#2360;&#2366;&#2344;&#2368; &#2360;&#2375; &#2325;&#2367;&#2360;&#2368; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2360;&#2381;&#2341;&#2366;&#2344; &#2325;&#2379; &#2326;&#2379;&#2332;&#2344;&#2375; &#2351;&#2366; &#2331;&#2379;&#2337;&#2364;&#2344;&#2375; &#2350;&#2375;&#2306; &#2350;&#2342;&#2342; &#2325;&#2352; &#2360;&#2325;&#2340;&#2368; &#2361;&#2376;&#2404; &#2346;&#2381;&#2354;&#2377;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\">(plotter) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2366;&#2327;&#2332; &#2346;&#2352; &#2327;&#2381;&#2352;&#2366;&#2347;&#2367;&#2325;&#2354; &#2310;&#2313;&#2335;&#2346;&#2369;&#2335;</span><span style=\"font-family: Cambria Math;\">(Graphical output) </span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2367;&#2306;&#2335; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2350;&#2366;&#2306;&#2337; &#2325;&#2368; &#2357;&#2381;&#2351;&#2366;&#2326;&#2381;&#2351;&#2366; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2348;&#2361;&#2369;&#2352;&#2306;&#2327;&#2368; &#2360;&#2381;&#2357;&#2330;&#2366;&#2354;&#2367;&#2340; &#2346;&#2375;&#2344;</span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">multicolored automated pens</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2325;&#2375; &#2325;&#2366;&#2327;&#2332; &#2346;&#2352; &#2352;&#2375;&#2326;&#2366; &#2330;&#2367;&#2340;&#2381;&#2352; &#2348;&#2344;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>