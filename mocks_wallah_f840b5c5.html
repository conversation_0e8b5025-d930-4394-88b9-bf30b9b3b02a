<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Identify the most appropriate antonym of the given word.<br>Timid</p>",
                    question_hi: "<p>1. Identify the most appropriate antonym of the given word.<br>Timid</p>",
                    options_en: ["<p>Meek</p>", "<p>Anxious</p>", 
                                "<p>Daring</p>", "<p>Spooky</p>"],
                    options_hi: ["<p>Meek</p>", "<p>Anxious</p>",
                                "<p>Daring</p>", "<p>Spooky</p>"],
                    solution_en: "<p>1.(c) <strong>Daring-</strong> willing to take risks<br><strong>Timid-</strong> lacking in courage or confidence.<br><strong>Meek-</strong> quiet, gentle, and submissive.<br><strong>Anxious-</strong> experiencing worry, unease, or nervousness.<br><strong>Spooky-</strong> strange and frightening.</p>",
                    solution_hi: "<p>1.(c) <strong>Daring</strong> (साहसी)- willing to take risks<br><strong>Timid</strong> (कायर)- lacking in courage or confidence.<br><strong>Meek</strong> (विनम्र)- quiet, gentle, and submissive.<br><strong>Anxious</strong> (चिंतित)- experiencing worry, unease, or nervousness.<br><strong>Spooky</strong> (साहसी)- strange and frightening.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the most appropriate meaning of the given idiom.<br>On the ball</p>",
                    question_hi: "<p>2. Select the most appropriate meaning of the given idiom.<br>On the ball</p>",
                    options_en: ["<p>Unaware of any changes or developments and lazy to react to them</p>", "<p>Aware of any changes or developments but slow to react to them</p>", 
                                "<p>Aware of any changes or developments and quick to react to them</p>", "<p>Aware of any changes or developments but doing nothing</p>"],
                    options_hi: ["<p>Unaware of any changes or developments and lazy to react to them</p>", "<p>Aware of any changes or developments but slow to react to them</p>",
                                "<p>Aware of any changes or developments and quick to react to them</p>", "<p>Aware of any changes or developments but doing nothing</p>"],
                    solution_en: "<p>2.(c) <strong>On the ball</strong>- aware of any changes or developments and quick to react to them.<br>E.g.- The manager is really on the ball. She noticed the issues before they became major problems.</p>",
                    solution_hi: "<p>2.(c) <strong>On the ball</strong>- aware of any changes or developments and quick to react to them./शीघ्र प्रतिक्रिया देने वाला। <br>E.g.- The manager is really on the ball. She noticed the issues before they became major problems.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the most appropriate option to substitute the underlined segment in the given&nbsp;sentence.<br>I have been living in Hyderabad <span style=\"text-decoration: underline;\">since I am born </span>.</p>",
                    question_hi: "<p>3. Select the most appropriate option to substitute the underlined segment in the given&nbsp;sentence.<br>I have been living in Hyderabad <span style=\"text-decoration: underline;\">since I am born </span>.</p>",
                    options_en: ["<p>since I have born</p>", "<p>since I will be born</p>", 
                                "<p>since I born</p>", "<p>since I was born</p>"],
                    options_hi: ["<p>since I have born</p>", "<p>since I will be born</p>",
                                "<p>since I born</p>", "<p>since I was born</p>"],
                    solution_en: "<p>3.(d) since I was born<br>&lsquo;Since&rsquo; is used to indicate the point of time in the past when something began. Therefore, past tense will be used in the clause beginning with &lsquo;since&rsquo;. Hence, &lsquo;since I was born&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>3.(d) since I was born<br>&lsquo;Since&rsquo; का प्रयोग past में उस समय को इंगित करने के लिए किया जाता है जब कुछ शुरू हुआ था। इसलिए, &lsquo;&lsquo;since&rsquo;&rsquo; से शुरू होने वाले clause में past tense का प्रयोग किया जाएगा। अतः, &lsquo;since I was born&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who sells and arranges cut flowers.</p>",
                    question_hi: "<p>4. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who sells and arranges cut flowers.</p>",
                    options_en: ["<p>Horticulturist</p>", "<p>Botanist</p>", 
                                "<p>Gardener</p>", "<p>Florist</p>"],
                    options_hi: ["<p>Horticulturist</p>", "<p>Botanist</p>",
                                "<p>Gardener</p>", "<p>Florist</p>"],
                    solution_en: "<p>4.(d) <strong>Florist-</strong> a person who sells and arranges cut flowers.<br><strong>Horticulturist-</strong> a person who studies or grows garden plants.<br><strong>Botanist-</strong> an expert in or student of the scientific study of plants.<br><strong>Gardener-</strong> someone who works in a garden, growing and taking care of plants.</p>",
                    solution_hi: "<p>4.(d) <strong>Florist</strong> (पुष्प-विक्रेता)- a person who sells and arranges cut flowers.<br><strong>Horticulturist</strong> (उद्यान विशेषज्ञ)- a person who studies or grows garden plants.<br><strong>Botanist</strong> (वनस्पतिशास्त्री)- an expert in or student of the scientific study of plants.<br><strong>Gardener</strong> (माली)- someone who works in a garden, growing and taking care of plants.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>After the concert was over, / they go to a restaurant, / had dinner together / and talked until midnight.</p>",
                    question_hi: "<p>5. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>After the concert was over, / they go to a restaurant, / had dinner together / and talked until midnight.</p>",
                    options_en: ["<p>had dinner together,</p>", "<p>they go to a restaurant,</p>", 
                                "<p>After the concert was over,</p>", "<p>and talked until midnight </p>"],
                    options_hi: ["<p>had dinner together,</p>", "<p>they go to a restaurant,</p>",
                                "<p>After the concert was over,</p>", "<p>and talked until midnight</p>"],
                    solution_en: "<p>5.(b) they go to a restaurant,<br>The given sentence is in the past tense, so it must have the verb in the past form (V<sub>2</sub>), not the present form (go). Hence, &lsquo;they went to a restaurant&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>5.(b) they go to a restaurant,<br>दिया गया sentence, past tense में है, इसलिए इसमें इसमें verb, present form में नहीं बल्कि past form (V<sub>2</sub>) में होनी चाहिए। अतः , &lsquo;they went to a restaurant&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the option that can be used as a one-word substitute for the given group of words.<br>A small group of people who spend their time together and do not welcome other people into that group</p>",
                    question_hi: "<p>6. Select the option that can be used as a one-word substitute for the given group of words.<br>A small group of people who spend their time together and do not welcome other people into that group</p>",
                    options_en: ["<p>Squad</p>", "<p>Clique</p>", 
                                "<p>Employees</p>", "<p>Lobby</p>"],
                    options_hi: ["<p>Squad</p>", "<p>Clique</p>",
                                "<p>Employees</p>", "<p>Lobby</p>"],
                    solution_en: "<p>6.(b) <strong>Clique-</strong> a small group of people who spend their time together and do not welcome other people into that group.<br><strong>Squad-</strong> a small organized group of military personnel.<br><strong>Employees-</strong> someone who is paid to work for someone else.<br><strong>Lobby-</strong> a group of people who try to persuade the government or an official group to do something.</p>",
                    solution_hi: "<p>6.(b) <strong>Clique</strong> (गुट/गिरोह)- a small group of people who spend their time together and do not welcome other people into that group.<br><strong>Squad</strong> (सैन्य दल)- a small organized group of military personnel.<br><strong>Employees</strong> (कर्मचारी)- someone who is paid to work for someone else.<br><strong>Lobby</strong> (सभाकक्ष)- a group of people who try to persuade the government or an official group to do something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "7. The following sentence contains a word with a spelling error. Rectify the sentence by selecting the correct spelling of the identified word from the given options.<br />He has to be conscious of his public manners as he is a decendent of a reputed family in this region.",
                    question_hi: "7. The following sentence contains a word with a spelling error. Rectify the sentence by selecting the correct spelling of the identified word from the given options.<br />He has to be conscious of his public manners as he is a decendent of a reputed family in this region.",
                    options_en: [" Dicsendent", " Descendant ", 
                                " Discendent", " Decendant"],
                    options_hi: [" Dicsendent", " Descendant ",
                                " Discendent", " Decendant"],
                    solution_en: "7.(b) Descendant<br />\'Descendant\' is the correct spelling. ",
                    solution_hi: "7.(b) Descendant<br />\'Descendant\' सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the most appropriate synonym of the underlined word in the given sentence.<br>The Morbi bridge mishap has left Gujarat in a <span style=\"text-decoration: underline;\">deplorable</span> state.</p>",
                    question_hi: "<p>8. Select the most appropriate synonym of the underlined word in the given sentence.<br>The Morbi bridge mishap has left Gujarat in a <span style=\"text-decoration: underline;\">deplorable</span> state.</p>",
                    options_en: ["<p>Jubilation</p>", "<p>Despicable</p>", 
                                "<p>Complaining</p>", "<p>Rejoicing</p>"],
                    options_hi: ["<p>Jubilation</p>", "<p>Despicable</p>",
                                "<p>Complaining</p>", "<p>Rejoicing</p>"],
                    solution_en: "<p>8.(b) <strong>Despicable-</strong> deserving hatred and contempt.<br><strong>Deplorable-</strong> shockingly bad in quality.<br><strong>Jubilation-</strong> a feeling of great happiness and triumph.<br><strong>Complaining-</strong> expressing dissatisfaction or annoyance about something.<br><strong>Rejoicing-</strong> feeling or showing great joy or delight.</p>",
                    solution_hi: "<p>8.(b) <strong>Despicable</strong> (घृणित)- deserving hatred and contempt.<br><strong>Deplorable</strong> (निंदनीय)- shockingly bad in quality.<br><strong>Jubilation</strong> (उल्लास)- a feeling of great happiness and triumph.<br><strong>Complaining</strong> (शिकायत करना)- expressing dissatisfaction or annoyance about something.<br><strong>Rejoicing</strong> (आनन्दित होना)- feeling or showing great joy or delight.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the most appropriate synonym of the underlined word.<br>He was <span style=\"text-decoration: underline;\">trembling</span> with fear.</p>",
                    question_hi: "<p>9. Select the most appropriate synonym of the underlined word.<br>He was <span style=\"text-decoration: underline;\">trembling</span> with fear.</p>",
                    options_en: ["<p>Shivering</p>", "<p>Weeping</p>", 
                                "<p>Running</p>", "<p>Intrepid</p>"],
                    options_hi: ["<p>Shivering</p>", "<p>Weeping</p>",
                                "<p>Running</p>", "<p>Intrepid</p>"],
                    solution_en: "<p>9.(a) <strong>Shivering-</strong> shaking slightly due to cold, fear, or excitement.<br><strong>Trembling-</strong> shaking involuntarily, typically as a result of anxiety or excitement.<br><strong>Intrepid-</strong> extremely brave and showing no fear of dangerous situations.</p>",
                    solution_hi: "<p>9.(a) <strong>Shivering</strong> (कांपना)- shaking slightly due to cold, fear, or excitement.<br><strong>Trembling</strong> (कांपना)- shaking involuntarily, typically as a result of anxiety or excitement.<br><strong>Intrepid</strong> (साहसी)- extremely brave and showing no fear of dangerous situations.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. More children go to school than at any time in the past. But more children today are out of school than any time in the past.<br>B. But it is not enough to blame the high birth rate for this state of affairs.<br>C. Indeed, it can be reasonably argued that continued mass illiteracy is not the result but the cause of the high birth rate.<br>D. There are more literate people in India today than ever before. But there are also more illiterates than ever before.</p>",
                    question_hi: "<p>10. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. More children go to school than at any time in the past. But more children today are out of school than any time in the past.<br>B. But it is not enough to blame the high birth rate for this state of affairs.<br>C. Indeed, it can be reasonably argued that continued mass illiteracy is not the result but the cause of the high birth rate.<br>D. There are more literate people in India today than ever before. But there are also more illiterates than ever before.</p>",
                    options_en: ["<p>ABCD</p>", "<p>CBAD</p>", 
                                "<p>DABC</p>", "<p>BADC</p>"],
                    options_hi: ["<p>ABCD</p>", "<p>CBAD</p>",
                                "<p>DABC</p>", "<p>BADC</p>"],
                    solution_en: "<p>10.(c) <strong>DABC</strong><br>Sentence D will be the starting line as it introduces the main idea of the parajumble, i.e. &lsquo;more number of literate and illiterate people than ever before&rsquo;. And, Sentence A states that more children go to school while at the same time more children are out of school, which is the reason for more number of literates and illiterates. So, A will follow D. Further, Sentence B states that it is not enough to blame the high birth rate for this situation &amp; Sentence C states that continued mass illiteracy is not the result but the cause of the high birth rate. So, C will follow B. Going through the options, option &lsquo;c&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>10.(c) <strong>DABC</strong><br>Sentence D प्रारम्भिक line होगी क्योंकि यह parajumble के मुख्य विचार &lsquo;more number of literate and illiterate people than ever before&rsquo; को प्रस्तुत करता है। और, Sentence A में कहा गया है कि अधिक बच्चे स्कूल जाते हैं जबकि साथ ही अधिक बच्चे स्कूल से बाहर रहते हैं, जो साक्षर (literates) और निरक्षरों(illiterates) की अधिक संख्या का कारण है। इसलिए, D के बाद A आएगा। इसके अलावा, Sentence B बताता है कि इस स्थिति के लिए उच्च जन्म दर (high birth rate) को दोष देना पर्याप्त नहीं है और Sentence C में कहा गया है कि निरंतर व्यापक निरक्षरता (mass illiteracy) उच्च जन्म दर का परिणाम नहीं, बल्कि कारण है। इसलिए, B के बाद C आएगा। अतः options के माध्यम से जाने पर, option &lsquo;c&rsquo; में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the most appropriate synonym of the bold word in the following sentence.<br>He tried to <strong>alleviate</strong> the sufferings of his neighbours.</p>",
                    question_hi: "<p>11. Select the most appropriate synonym of the bold word in the following sentence.<br>He tried to <strong>alleviate</strong> the sufferings of his neighbours.</p>",
                    options_en: ["<p>Swell</p>", "<p>Add</p>", 
                                "<p>Intensify</p>", "<p>Relieve</p>"],
                    options_hi: ["<p>Swell</p>", "<p>Add</p>",
                                "<p>Intensify</p>", "<p>Relieve</p>"],
                    solution_en: "<p>11.(d) <strong>Relieve-</strong> to free from a burden or distress.<br><strong>Alleviate-</strong> to make something less severe.<br><strong>Swell-</strong> to increase in size or volume.<br><strong>Add-</strong> to join something to another, increasing its size or quantity.<br><strong>Intensify-</strong> to make something stronger or more extreme.</p>",
                    solution_hi: "<p>11.(d) <strong>Relieve</strong> (कार्य मुक्त करना)- to free from a burden or distress.<br><strong>Alleviate</strong> (हल्का करना)- to make something less severe.<br><strong>Swell</strong> (बढ़ाना)- to increase in size or volume.<br><strong>Add</strong> (जोड़ना)- to join something to another, increasing its size or quantity.<br><strong>Intensify</strong> (अधिक मजबूत बनाना)- to make something stronger or more extreme.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>Renewable energy / sources like solar and wind power / will replaced traditional fossil fuels / and help reduce carbon emissions.</p>",
                    question_hi: "<p>12. The following sentence has been split into four segments. Identify the segment that contains a grammatical error.<br>Renewable energy / sources like solar and wind power / will replaced traditional fossil fuels / and help reduce carbon&nbsp;emissions.</p>",
                    options_en: ["<p>and help reduce carbon emissions</p>", "<p>Renewable energy</p>", 
                                "<p>will replaced traditional fossil fuels</p>", "<p>sources like solar and wind power</p>"],
                    options_hi: ["<p>and help reduce carbon emissions</p>", "<p>Renewable energy</p>",
                                "<p>will replaced traditional fossil fuels</p>", "<p>sources like solar and wind power</p>"],
                    solution_en: "<p>12.(c) will replaced traditional fossil fuels<br>&lsquo;Modal verbs&rsquo; are auxiliary verbs(also called helping verbs) like can, will, could, shall, must, would, might, and should. However, after a modal verb, we generally use the first form of the verb (V<sub>1</sub>). Hence, &lsquo;will replace(V<sub>1</sub>) traditional fossil fuels&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(c) will replaced traditional fossil fuels<br>&lsquo;Modal verbs&rsquo; auxiliary verbs (जिन्हें helping verbs भी कहा जाता है) होते हैं जैसे कि can, will, could, should, must, would, might, and should. हालाँकि, modal verb के बाद, आम तौर पर verb के first form (V<sub>1</sub>) का प्रयोग किया जाता हैं। अतः , &lsquo;will replace (V<sub>1</sub>) traditional fossil fuels&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Identify from the given options the word which is similar in meaning to the following word.<br>Equivocal</p>",
                    question_hi: "<p>13. Identify from the given options the word which is similar in meaning to the following word.<br>Equivocal</p>",
                    options_en: ["<p>Clear</p>", "<p>Ambiguous</p>", 
                                "<p>Representative</p>", "<p>Balanced</p>"],
                    options_hi: ["<p>Clear</p>", "<p>Ambiguous</p>",
                                "<p>Representative</p>", "<p>Balanced</p>"],
                    solution_en: "<p>13.(b) <strong>Ambiguous-</strong> having or expressing more than one possible meaning.<br><strong>Equivocal-</strong> open to more than one interpretation.<br><strong>Clear-</strong> easy to perceive or understand.<br><strong>Representative-</strong> serving as an example or symbol.<br><strong>Balanced-</strong> evenly distributed or stable.</p>",
                    solution_hi: "<p>13.(b) <strong>Ambiguous</strong> (अनेकार्थक)- having or expressing more than one possible meaning.<br><strong>Equivocal</strong> (संदिग्धार्थक)- open to more than one interpretation.<br><strong>Clear</strong> (स्पष्ट)- easy to perceive or understand.<br><strong>Representative</strong> (प्रतिनिधि)- serving as an example or symbol.<br><strong>Balanced</strong> (संतुलित)- evenly distributed or stable.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate ANTONYM of the underlined word.<br>She was <span style=\"text-decoration: underline;\">elated</span> about her promotion, but her colleague felt quite disheartened.</p>",
                    question_hi: "<p>14. Select the most appropriate ANTONYM of the underlined word.<br>She was <span style=\"text-decoration: underline;\">elated</span> about her promotion, but her colleague felt quite disheartened.</p>",
                    options_en: ["<p>cheerful</p>", "<p>proud</p>", 
                                "<p>discouraged</p>", "<p>delighted</p>"],
                    options_hi: ["<p>cheerful</p>", "<p>proud</p>",
                                "<p>discouraged</p>", "<p>delighted</p>"],
                    solution_en: "<p>14.(c) <strong>Discouraged-</strong> lacking confidence or enthusiasm.<br><strong>Elated-</strong> extremely happy or excited.<br><strong>Cheerful-</strong> noticeably happy or optimistic.<br><strong>Proud-</strong> feeling deep pleasure or satisfaction.<br><strong>Delighted-</strong> greatly pleased or happy.</p>",
                    solution_hi: "<p>14.(c) <strong>Discouraged</strong> (हतोत्साहित)- lacking confidence or enthusiasm.<br><strong>Elated</strong> (प्रसन्न)- extremely happy or excited.<br><strong>Cheerful</strong> (प्रसन्नचित्त) - noticeably happy or optimistic.<br><strong>Proud</strong> (गर्व)- feeling deep pleasure or satisfaction.<br><strong>Delighted</strong> (प्रसन्न)- greatly pleased or happy.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the option that expresses the given sentence in active voice.<br>The contract was grabbed by the biggest telecom company in India.</p>",
                    question_hi: "<p>15. Select the option that expresses the given sentence in active voice.<br>The contract was grabbed by the biggest telecom company in India.</p>",
                    options_en: ["<p>The biggest telecom company in India grabbed the contract.</p>", "<p>The biggest telecom company in India grabs the contract.</p>", 
                                "<p>The biggest telecom company in India will grab the contract.</p>", "<p>The biggest telecom company in India has grabbed the contract.</p>"],
                    options_hi: ["<p>The biggest telecom company in India grabbed the contract.</p>", "<p>The biggest telecom company in India grabs the contract.</p>",
                                "<p>The biggest telecom company in India will grab the contract.</p>", "<p>The biggest telecom company in India has grabbed the contract.</p>"],
                    solution_en: "<p>15.(a) The biggest telecom company in India grabbed the contract. (Correct)<br>(b) The biggest telecom company in India <span style=\"text-decoration: underline;\">grabs</span> the contract. (Incorrect Tense)<br>(c) The biggest telecom company in India <span style=\"text-decoration: underline;\">will grab</span> the contract. (Incorrect Tense)<br>(d) The biggest telecom company in India <span style=\"text-decoration: underline;\">has grabbed</span> the contract. (Incorrect Tense)</p>",
                    solution_hi: "<p>15.(a) The biggest telecom company in India grabbed the contract. (Correct)<br>(b) The biggest telecom company in India <span style=\"text-decoration: underline;\">grabs</span> the contract. (गलत Tense)<br>(c) The biggest telecom company in India <span style=\"text-decoration: underline;\">will grab</span> the contract. (गलत Tense)<br>(d) The biggest telecom company in India <span style=\"text-decoration: underline;\">has grabbed</span> the contract. (गलत Tense)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. I\'m _______________ about whether to accept the job offer or continue freelancing. Both options have their advantages.</p>",
                    question_hi: "<p>16. I\'m _______________ about whether to accept the job offer or continue freelancing. Both options have their advantages.</p>",
                    options_en: ["<p>on the fence</p>", "<p>cutting corners</p>", 
                                "<p>left out in cold</p>", "<p>up in arms</p>"],
                    options_hi: ["<p>on the fence</p>", "<p>cutting corners</p>",
                                "<p>left out in cold</p>", "<p>up in arms</p>"],
                    solution_en: "<p>16.(a) <strong>On the fence-</strong> not able to decide something.</p>",
                    solution_hi: "<p>16.(a) <strong>On the fence- </strong>not able to decide something./कुछ निर्णय न ले पाना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "17. Select the correctly spelt word.",
                    question_hi: "17. Select the correctly spelt word.",
                    options_en: [" Conscensus ", " Entrepreneurship ", 
                                " Collaegue", " Bizzaire"],
                    options_hi: [" Conscensus ", " Entrepreneurship ",
                                " Collaegue", " Bizzaire"],
                    solution_en: "17.(b) Entrepreneurship<br />\'Entrepreneurship\' is the correct spelling. ",
                    solution_hi: "17.(b) Entrepreneurship<br />\'Entrepreneurship\' सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the option that expresses the given sentence in active voice.<br>By whom was grammar taught to you?</p>",
                    question_hi: "<p>18. Select the option that expresses the given sentence in active voice.<br>By whom was grammar taught to you?</p>",
                    options_en: ["<p>Did he teach you grammar?</p>", "<p>Who had taught you grammar?</p>", 
                                "<p>You were taught grammar by whom?</p>", "<p>Who taught you grammar?</p>"],
                    options_hi: ["<p>Did he teach you grammar?</p>", "<p>Who had taught you grammar?</p>",
                                "<p>You were taught grammar by whom?</p>", "<p>Who taught you grammar?</p>"],
                    solution_en: "<p>18.(d) Who taught you grammar? (Correct)<br>(a) Did he teach you grammar? (Incorrect Sentence Structure)<br>(b) Who <span style=\"text-decoration: underline;\">had taught</span> you grammar? (Incorrect Tense)<br>(c) You were taught grammar by whom? (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>18.(d) Who taught you grammar? (Correct)<br>(a) Did he teach you grammar? (गलत Sentence Structure)<br>(b) Who <span style=\"text-decoration: underline;\">had taught</span> you grammar? (गलत Tense)<br>(c) You were taught grammar by whom? (गलत Sentence Structure)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the option that can be used as a one-word substitute for the given group of words.<br>A place where fruit trees are grown</p>",
                    question_hi: "<p>19. Select the option that can be used as a one-word substitute for the given group of words.<br>A place where fruit trees are grown</p>",
                    options_en: ["<p>Orchard</p>", "<p>Garden</p>", 
                                "<p>Quay</p>", "<p>Museum</p>"],
                    options_hi: ["<p>Orchard</p>", "<p>Garden</p>",
                                "<p>Quay</p>", "<p>Museum</p>"],
                    solution_en: "<p>19.(a) <strong>Orchard-</strong> a place where fruit trees are grown.<br><strong>Garden-</strong> a piece of land, usually near a home, where flowers and other plants are grown.<br><strong>Quay-</strong> a long structure, usually built of stone, where boats can be tied up to take on and off their goods.<br><strong>Museum-</strong> a building in which objects of historical, scientific, artistic, or cultural interest are stored and exhibited.</p>",
                    solution_hi: "<p>19.(a) <strong>Orchard</strong> (फलोद्यान)- a place where fruit trees are grown.<br><strong>Garden</strong> (बगीचा)- a piece of land, usually near a home, where flowers and other plants are grown.<br><strong>Quay</strong> (घाट)- a long structure, usually built of stone, where boats can be tied up to take on and off their goods.<br><strong>Museum</strong> (संग्रहालय)- a building in which objects of historical, scientific, artistic, or cultural interest are stored and exhibited.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the option that can be used as a one-word substitute for the given group of words.<br>A government by the officials</p>",
                    question_hi: "<p>20. Select the option that can be used as a one-word substitute for the given group of words.<br>A government by the officials</p>",
                    options_en: ["<p>Autocracy</p>", "<p>Bureaucracy</p>", 
                                "<p>Monarchy</p>", "<p>Plutocracy</p>"],
                    options_hi: ["<p>Autocracy</p>", "<p>Bureaucracy</p>",
                                "<p>Monarchy</p>", "<p>Plutocracy</p>"],
                    solution_en: "<p>20.(b) <strong>Bureaucracy-</strong> a government by the officials.<br><strong>Autocracy-</strong> a system of government by one person with absolute power.<br><strong>Monarchy-</strong> a form of government with a monarch at the head.<br><strong>Plutocracy-</strong> a system of government in which the richest people in a country rule or have power.</p>",
                    solution_hi: "<p>20.(b) <strong>Bureaucracy</strong> (नौकरशाही)- a government by the officials.<br><strong>Autocracy</strong> (एकतंत्र)- a system of government by one person with absolute power.<br><strong>Monarchy</strong> (राजतंत्र)- a form of government with a monarch at the head.<br><strong>Plutocracy</strong> (धनिकतंत्र)- a system of government in which the richest people in a country rule or have power.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (21)______ the Global Wildlife Programme conference. The plan recognises (22)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (23)______ survival and sustainable development. It also emphasises preservation of genetic (24)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (25)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (21)______ the Global Wildlife Programme conference. The plan recognises (22)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (23)______ survival and sustainable development. It also emphasises preservation of genetic (24)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (25)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>at</p>", "<p>for</p>", 
                                "<p>in</p>", "<p>to</p>"],
                    options_hi: ["<p>at</p>", "<p>for</p>",
                                "<p>in</p>", "<p>to</p>"],
                    solution_en: "<p>21.(a) <strong>at</strong><br>&lsquo;At&rsquo; is used to indicate the specific place or an event. Similarly, in the given passage, the Global Wildlife Programme conference is the specific event. Hence, &lsquo;at&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(a) <strong>at</strong><br>&lsquo;At&rsquo; का प्रयोग किसी विशिष्ट स्थान या कार्यक्रम को इंगित करने के लिए किया जाता है। इसी प्रकार, दिए गए passage में, Global Wildlife Programme conference एक विशिष्ट कार्यक्रम है। अतः, &lsquo;at&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (21)______ the Global Wildlife Programme conference. The plan recognises (22)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (23)______ survival and sustainable development. It also emphasises preservation of genetic (24)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (25)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (21)______ the Global Wildlife Programme conference. The plan recognises (22)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (23)______ survival and sustainable development. It also emphasises preservation of genetic (24)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (25)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>to</p>", "<p>and</p>", 
                                "<p>in</p>", "<p>for</p>"],
                    options_hi: ["<p>to</p>", "<p>and</p>",
                                "<p>in</p>", "<p>for</p>"],
                    solution_en: "<p>22.(b) <strong>and</strong><br>&lsquo;And&rsquo; is used to connect to similar parts of speech. Similarly, in the given passage, &lsquo;and&rsquo; has been used to connect two verbs &lsquo;recognises&rsquo; &amp; &lsquo;addresses&rsquo;. Hence, &lsquo;and&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(b) <strong>and</strong><br>&lsquo;And&rsquo; का प्रयोग same parts of speech को जोड़ने के लिए किया जाता है। इसी तरह, दिए गए passage में, &lsquo;and&rsquo; का प्रयोग दो verb &lsquo;recognises&rsquo; और &lsquo;addresses&rsquo; को जोड़ने के लिए किया गया है। अतः, &lsquo;and&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (21)______ the Global Wildlife Programme conference. The plan recognises (22)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (23)______ survival and sustainable development. It also emphasises preservation of genetic (24)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (25)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (21)______ the Global Wildlife Programme conference. The plan recognises (22)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (23)______ survival and sustainable development. It also emphasises preservation of genetic (24)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (25)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>group</p>", "<p>human</p>", 
                                "<p>species</p>", "<p>breed</p>"],
                    options_hi: ["<p>group</p>", "<p>human</p>",
                                "<p>species</p>", "<p>breed</p>"],
                    solution_en: "<p>23.(b) <strong>human</strong><br>The given passage states that the plan details the importance of ecosystems for food production, health and other aspects of human survival and sustainable development. Hence, \'human\' is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(b) <strong>human</strong><br>दिए गए passage में कहा गया है कि योजना खाद्य उत्पादन, स्वास्थ्य और मानव अस्तित्व एवं सतत विकास(sustainable development) के अन्य पहलुओं के लिए पारिस्थितिकी तंत्र (ecosystems) के महत्व का विवरण देती है। अतः , \'human\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (21)______ the Global Wildlife Programme conference. The plan recognises (22)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (23)______ survival and sustainable development. It also emphasises preservation of genetic (24)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (25)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (21)______ the Global Wildlife Programme conference. The plan recognises (22)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (23)______ survival and sustainable development. It also emphasises preservation of genetic (24)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (25)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>conflict</p>", "<p>diversity</p>", 
                                "<p>focus</p>", "<p>rejection</p>"],
                    options_hi: ["<p>conflict</p>", "<p>diversity</p>",
                                "<p>focus</p>", "<p>rejection</p>"],
                    solution_en: "<p>24.(b) <strong>diversity</strong><br>&lsquo;Diversity&rsquo; means the state of being different from one another. The given passage states that it also emphasises preservation of genetic diversity and sustainable utilisation of species and ecosystems. Hence, &lsquo;diversity&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(b) <strong>diversity</strong><br>&lsquo;Diversity&rsquo; का अर्थ है एक दूसरे से अलग होने की अवस्था। दिए गए passage में कहा गया है कि यह आनुवंशिक विविधता (genetic diversity) के संरक्षण और प्रजातियों (species) एवं पारिस्थितिकी तंत्र (ecosystems) के सतत उपयोग पर भी जोर देता है। अतः, &lsquo;diversity&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (21)______ the Global Wildlife Programme conference. The plan recognises (22)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (23)______ survival and sustainable development. It also emphasises preservation of genetic (24)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (25)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25. <strong>Cloze test:</strong><br>The third National Wildlife Action Plan for 2017- 2031, underlying India&rsquo;s plan for wildlife conservation, was unveiled (21)______ the Global Wildlife Programme conference. The plan recognises (22)________ addresses concerns pertaining to climate changes and its impact on wildlife.<br>The plan details the importance of ecosystems for food production, health and other aspects of (23)______ survival and sustainable development. It also emphasises preservation of genetic (24)_______ and sustainable utilisation of species and ecosystems which has direct bearing on our scientific advancements and support to millions of rural communities.<br>The plan underscores rehabilitation of threatened wildlife species (25)_______ inland aquatic, coastal and marine ecosystems while conserving their habitats.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>including</p>", "<p>gathering</p>", 
                                "<p>counting</p>", "<p>mixing</p>"],
                    options_hi: ["<p>including</p>", "<p>gathering</p>",
                                "<p>counting</p>", "<p>mixing</p>"],
                    solution_en: "<p>25.(a) <strong>including</strong><br>&lsquo;Including&rsquo; means having something as a part. According to the passage, inland aquatic, coastal and marine ecosystems are also a part of the rehabilitation plan. Hence, &lsquo;including&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(a) <strong>including</strong><br>&lsquo;Including&rsquo; का अर्थ है कोई चीज़ शामिल होना। दिए गए passage के अनुसार, अंतर्देशीय(inland) जलीय, तटीय और समुद्री पारिस्थितिकी तंत्र भी पुनर्वास योजना (rehabilitation plan) का एक हिस्सा हैं। अतः, &lsquo;including&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>