<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. Rohit buys an old bat for Rs. 2320 and spends Rs. 180 on its repairing. If he sells it for Rs. 2400, then what is the loss percentage?</p>",
                    question_hi: "<p>1. रोहित 2320 रुपए में एक पुराना बल्ला खरीदता है और उसकी मरम्मत में 180 रुपए खर्च करता है। यदि वह इसे 2400 रुपए में बेचता है, तो हानि प्रतिशत कितना है?</p>",
                    options_en: ["<p>8 percent</p>", "<p>5 percent</p>", 
                                "<p>4 percent</p>", "<p>10 percent</p>"],
                    options_hi: ["<p>8 प्रतिशत</p>", "<p>5 प्रतिशत</p>",
                                "<p>4 प्रतिशत</p>", "<p>10 प्रतिशत</p>"],
                    solution_en: "<p>1.(c)<br>Total CP of bat = 2320 + 180 = ₹2500<br>SP of bat = ₹2400<br>loss% = <math display=\"inline\"><mfrac><mrow><mn>2500</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2400</mn></mrow><mrow><mn>2500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>25</mn></mfrac></math>= 4%</p>",
                    solution_hi: "<p>1.(c)<br>बल्ले का क्रयमूल्य = 2320 + 180 = ₹2500<br>बल्ले का विक्रयमूल्य = ₹2400<br>हानि% = <math display=\"inline\"><mfrac><mrow><mn>2500</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2400</mn></mrow><mrow><mn>2500</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>25</mn></mfrac></math>= 4%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Both A and B sells their watch at the rate of Rs. 5000 per watch. A earned a profit of 25 percent while B incurred a loss of 10 percent. What is the ratio of cost prices of A and B?</p>",
                    question_hi: "<p>2. A और B दोनों 5000 रुपए प्रति घड़ी की दर से अपनी घड़ियां बेचते हैं। A, 25 प्रतिशत का लाभ अर्जित करता है जबकि B को 10 प्रतिशत की हानि होती है। A और B के क्रय मूल्यों का अनुपात कितना है?</p>",
                    options_en: ["<p>18 : 25</p>", "<p>17 : 20</p>", 
                                "<p>16 : 25</p>", "<p>9 : 10</p>"],
                    options_hi: ["<p>18 : 25</p>", "<p>17 : 20</p>",
                                "<p>16 : 25</p>", "<p>9 : 10</p>"],
                    solution_en: "<p>2.(a)<br>ATQ,<br>125% &times; A = 90% of B<br><math display=\"inline\"><mfrac><mrow><mi>A</mi></mrow><mrow><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>125</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>25</mn></mfrac></math></p>",
                    solution_hi: "<p>2.(a)<br>प्रश्न के अनुसार,<br>125% &times; A = 90% of B<br><math display=\"inline\"><mfrac><mrow><mi>A</mi></mrow><mrow><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>90</mn><mn>125</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>25</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3. Selling price of a table is Rs. 6875. If profit percentage is 25 percent, then what is the cost price of table ?",
                    question_hi: "3. एक मेज का विक्रय मूल्य 6875 रुपए है। यदि लाभ प्रतिशत 25 प्रतिशत है, तो मेज का क्रय मूल्य कितना है ?",
                    options_en: [" Rs. 5500", " Rs. 5000", 
                                " Rs. 6400", " Rs. 5200"],
                    options_hi: [" 5500 रुपए", " 5000 रुपए",
                                " 6400 रुपए", " 5200 रुपए"],
                    solution_en: "3.(a)<br />125% → 6875<br />100% → <math display=\"inline\"><mfrac><mrow><mn>6875</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> × 100 = ₹5500",
                    solution_hi: "3.(a)<br />125% → 6875<br />100% → <math display=\"inline\"><mfrac><mrow><mn>6875</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math> × 100 = ₹5500",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. The marked price of an article is 60 percent more than its cost price. If 20 percent discount is given, then what will be the profit percentage ?</p>",
                    question_hi: "<p>4. एक वस्तु का अंकित मूल्य उसके क्रय मूल्य से 60 प्रतिशत अधिक है। यदि उस वस्तु पर 20 प्रतिशत की छूट दी जाती है, तो लाभ प्रतिशत कितना होगा ?</p>",
                    options_en: ["<p>33 percent</p>", "<p>34 percent</p>", 
                                "<p>28 percent</p>", "<p>30 percent</p>"],
                    options_hi: ["<p>33 प्रतिशत</p>", "<p>34 प्रतिशत</p>",
                                "<p>28 प्रतिशत</p>", "<p>30 प्रतिशत</p>"],
                    solution_en: "<p>4.(c)<br>As we know, <math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi><mo>%</mo></mrow></mfrac></math>&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>5</mn></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>P</mi><mo>%</mo></mrow><mn>80</mn></mfrac></math>&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 + P% = 128<br><math display=\"inline\"><mo>&#8658;</mo></math> P% = 128 - 100 = 28%</p>",
                    solution_hi: "<p>4.(c)<br>जैसा कि हम जानते हैं, <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;</mi><mi>&#2306;</mi><mi>&#2325;</mi><mi>&#2367;</mi><mi>&#2340;</mi><mi>&#160;</mi><mi>&#2350;</mi><mi>&#2370;</mi><mi>&#2354;</mi><mi>&#2381;</mi><mi>&#2351;</mi></mrow><mrow><mi>&#160;</mi><mi>&#2325;</mi><mi>&#2381;</mi><mi>&#2352;</mi><mi>&#2351;</mi><mi>&#160;</mi><mi>&#2350;</mi><mi>&#2370;</mi><mi>&#2354;</mi><mi>&#2381;</mi><mi>&#2351;</mi></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi><mo>&#160;</mo><mo>%</mo></mrow><mrow><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>&#2331;&#2370;&#2335;</mi><mo>%</mo></mrow></mfrac></math>&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>5</mn></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>P</mi><mo>%</mo></mrow><mn>80</mn></mfrac></math>&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 + P% = 128<br><math display=\"inline\"><mo>&#8658;</mo></math> P% = 128 - 100 = 28%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If the cost price of 6 pens is the same as the selling price of 5 pens, find the profit/loss percentage.</p>",
                    question_hi: "<p>5. यदि 6 पेन का क्रय मूल्य, 5 पेन के विक्रय मूल्य के समान है, तो लाभ/हानि प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>20% loss</p>", "<p>20% profit</p>", 
                                "<p>10% profit</p>", "<p>10% loss</p>"],
                    options_hi: ["<p>20% हानि</p>", "<p>20% लाभ</p>",
                                "<p>10% लाभ</p>", "<p>10% हानि</p>"],
                    solution_en: "<p>5.(b)<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 6CP = 5SP<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>C</mi><mi>P</mi></mrow><mrow><mi>S</mi><mi>P</mi></mrow></mfrac></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math><br>Required profit% = <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times;100 = 20%</p>",
                    solution_hi: "<p>5.(b)<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 6 क्रय मूल्य = 5 विक्रय मूल्य<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math>&nbsp; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math><br>आवश्यक लाभ% = <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times;100 = 20%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A trader marks his goods 50% above the cost price and offers a discount of 20%. What is the percentage profit the trader makes after offering the discount ?</p>",
                    question_hi: "<p>6. एक व्यापारी अपने सामान को क्रय मूल्य से 50% अधिक पर अंकित करता है और 20% की छूट देता है। छूट देने के बाद व्यापारी को कितने प्रतिशत का लाभ होता है ?</p>",
                    options_en: ["<p>12%</p>", "<p>10%</p>", 
                                "<p>20%</p>", "<p>18%</p>"],
                    options_hi: ["<p>12%</p>", "<p>10%</p>",
                                "<p>20%</p>", "<p>18%</p>"],
                    solution_en: "<p>6.(c)<br>As we know, <math display=\"inline\"><mfrac><mrow><mi>M</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>)</mo><mo>%</mo></mrow><mrow><mo>(</mo><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi><mo>)</mo><mo>%</mo></mrow></mfrac></math>&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>P</mi><mo>%</mo><mo>)</mo></mrow><mn>80</mn></mfrac></math>&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 + P% = 120<br><math display=\"inline\"><mo>&#8658;</mo></math> P % = 120 - 100 <br><math display=\"inline\"><mo>&#8658;</mo></math> P % = 20</p>",
                    solution_hi: "<p>6.(c)<br>जैसा कि हम जानते हैं, <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;</mi><mi>&#2306;</mi><mi>&#2325;</mi><mi>&#2367;</mi><mi>&#2340;</mi><mi>&#160;</mi><mi>&#2350;</mi><mi>&#2370;</mi><mi>&#2354;</mi><mi>&#2381;</mi><mi>&#2351;</mi></mrow><mrow><mi>&#160;</mi><mi>&#2325;</mi><mi>&#2381;</mi><mi>&#2352;</mi><mi>&#2351;</mi><mi>&#160;</mi><mi>&#2350;</mi><mi>&#2370;</mi><mi>&#2354;</mi><mi>&#2381;</mi><mi>&#2351;</mi></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>+</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi><mo>&#160;</mo><mo>%</mo></mrow><mrow><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>&#2331;&#2370;&#2335;</mi><mo>%</mo></mrow></mfrac></math>&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>P</mi><mo>%</mo><mo>)</mo></mrow><mn>80</mn></mfrac></math>&nbsp;<br><math display=\"inline\"><mo>&#8658;</mo></math> 100 + P% = 120<br><math display=\"inline\"><mo>&#8658;</mo></math> P % = 120 - 100 <br><math display=\"inline\"><mo>&#8658;</mo></math> P % = 20</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "7. The difference between cost price and selling price is Rs. 264. If profit percentage is 33 percent, then what is the selling price?",
                    question_hi: "7. क्रय मूल्य और विक्रय मूल्य के बीच का अंतर 264 रुपए है। यदि लाभ प्रतिशत 33 प्रतिशत है, तो विक्रय मूल्य कितना है?",
                    options_en: [" Rs. 1064  ", " Rs. 1526 ", 
                                " Rs. 1084 ", " Rs. 1024"],
                    options_hi: [" 1064  रुपए", " 1526 रुपए",
                                " 1084 रुपए", " 1024 रुपए"],
                    solution_en: "7.(a)<br />Let CP be 100%<br />33% --------------- ₹264<br />100%  ---------------  <math display=\"inline\"><mfrac><mrow><mn>264</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math> × 100 = ₹800<br />SP = 800 + 264 = ₹1064",
                    solution_hi: "7.(a)<br />माना क्रय मूल्य  = 100%<br />33% --------------- ₹264<br />100%  ---------------  <math display=\"inline\"><mfrac><mrow><mn>264</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math> × 100 = ₹800<br />SP = 800 + 264 = ₹1064",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. If selling price of a table is 25 percent of its cost price, then what is the loss percentage?</p>",
                    question_hi: "<p>8. यदि किसी टेबल का विक्रय मूल्य उसके क्रय मूल्य का 25 प्रतिशत है, तो हानि प्रतिशत कितना है?</p>",
                    options_en: ["<p>55 percent</p>", "<p>45 percent</p>", 
                                "<p>35 percent</p>", "<p>75 percent</p>"],
                    options_hi: ["<p>55 प्रतिशत</p>", "<p>45 प्रतिशत</p>",
                                "<p>35 प्रतिशत</p>", "<p>75 प्रतिशत</p>"],
                    solution_en: "<p>8.(d)<br>ATQ,<br>SP = 25% of CP <br><math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>loss% = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>&times; 100 = 75%</p>",
                    solution_hi: "<p>8.(d)<br>प्रश्न के अनुसार,<br>बिक्रय मूल्य ( SP) = CP का 25%<br><math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>हानि % = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>&times; 100 = 75%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Ankit sells an article at a loss of 10 percent. If he buys it for 30 percent less price and sells it for Rs. 26 less price, then his profit will be 10 percent. What is the cost price of the article ?</p>",
                    question_hi: "<p>9. अंकित एक वस्तु को 10 प्रतिशत की हानि पर बेचता है। यदि वह इसे 30 प्रतिशत कम मूल्य पर खरीदता है और इसे 26 रुपए कम में बेचता है, तो उसे 10 प्रतिशत का लाभ होगा। वस्तु का क्रय मूल्य कितना है ?</p>",
                    options_en: ["<p>Rs. 300</p>", "<p>Rs. 250</p>", 
                                "<p>Rs. 320</p>", "<p>Rs. 200</p>"],
                    options_hi: ["<p>300 रुपए</p>", "<p>250 रुपए</p>",
                                "<p>320 रुपए</p>", "<p>200 रुपए</p>"],
                    solution_en: "<p>9.(d)<br>Let the original CP of an article be 100<math display=\"inline\"><mi>x</mi></math><br>Original SP of the article = 100<math display=\"inline\"><mi>x</mi></math> &times;90% = 90x<br>New CP =100<math display=\"inline\"><mi>x</mi></math> &times; 70% = 70x<br>New SP = 90<math display=\"inline\"><mi>x</mi></math> - 26<br>ATQ,<br>70<math display=\"inline\"><mi>x</mi><mi>&#160;</mi></math>&times; 110% = 90x - 26<br>77<math display=\"inline\"><mi>x</mi></math> = 90x - 26<br>13<math display=\"inline\"><mi>x</mi></math> = 26<br><math display=\"inline\"><mi>x</mi></math> = 2<br>So, the CP of the article = 100<math display=\"inline\"><mi>x</mi></math> = 100 &times; 2 = ₹200</p>",
                    solution_hi: "<p>9.(d)<br>माना कि किसी वस्तु का मूल क्र<math display=\"inline\"><mo>&#8728;</mo></math> मू ∘100x है<br>वस्तु का मूल वि <math display=\"inline\"><mo>&#8728;</mo></math>मू ∘ = 100x &times; 90% = 90x<br>नया क्र<math display=\"inline\"><mo>&#8728;</mo></math> मू ∘ =100x &times; 70% = 70x<br>नया वि <math display=\"inline\"><mo>&#8728;</mo></math>मू ∘ = 90x - 26<br>प्रश्नानुसार ,<br>70<math display=\"inline\"><mi>x</mi></math> &times; 110% = 90x - 26<br>77<math display=\"inline\"><mi>x</mi></math> = 90x - 26<br>13<math display=\"inline\"><mi>x</mi></math> = 26<br><math display=\"inline\"><mi>x</mi></math>= 2<br>तो, वस्तु का क्रय मूल्य = 100<math display=\"inline\"><mi>x</mi></math> = 100 &times; 2 = ₹200</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A man buys 35 mangoes for a rupee. How many mangoes had he sold for a rupee so that there is a loss of 30 percent ?</p>",
                    question_hi: "<p>10. एक आदमी एक रुपए में 35 आम खरीदता है। एक रुपए में कितने आम बेचने पर उसे 30 प्रतिशत की हानि होगी ?</p>",
                    options_en: ["<p>33</p>", "<p>45</p>", 
                                "<p>27</p>", "<p>50</p>"],
                    options_hi: ["<p>33</p>", "<p>45</p>",
                                "<p>27</p>", "<p>50</p>"],
                    solution_en: "<p>10.(d)<br>Price &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>Q</mi><mi>u</mi><mi>a</mi><mi>n</mi><mi>t</mi><mi>i</mi><mi>t</mi><mi>y</mi></mrow></mfrac></math><br>Price 100 : 70 = 10 : 7<br>Quantity&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;7 : 10<br>7 unit -------------- 35 mangoes<br>10 unit -------------- <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 10 = 50 mangoes</p>",
                    solution_hi: "<p>10.(d)<br>कीमत &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>&#2350;</mi><mi>&#2366;</mi><mi>&#2340;</mi><mi>&#2381;</mi><mi>&#2352;</mi><mi>&#2366;</mi></mrow></mfrac></math><br>कीमत 100 : 70 = 10 : 7<br>मात्रा&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 7 : 10<br>7 यूनिट ----------------- 35 आम<br>10 यूनिट -----------------<math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 10 = 50 आम</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "11. The marked price of a chair is 20 percent more than its cost price. If the chair is sold for Rs. 220 after a discount of Rs. 20, then what will be the profit percentage ?",
                    question_hi: "11. एक कुर्सी का अंकित मूल्य, उसके क्रय मूल्य से 20 प्रतिशत अधिक है। यदि 20 रुपए की छूट के बाद कुर्सी 220 रुपए में बेची जाती है, तो लाभ प्रतिशत कितना होगा ?",
                    options_en: [" 20 percent   ", " 30 percent", 
                                " 10 percent", " 15 percent"],
                    options_hi: [" 20 प्रतिशत", " 30 प्रतिशत",
                                " 10 प्रतिशत", " 15 प्रतिशत"],
                    solution_en: "11.(c)<br />MP : CP = 6 : 5<br />MP of chair = 220 + 20 = ₹240<br />6 unit --------------- ₹240<br />5 unit ---------------<math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> × 5 = ₹200<br />Profit = 220 - 200 = ₹20<br />profit% = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math>×100 = 10%",
                    solution_hi: "11.(c)<br />अंकित मू<math display=\"inline\"><mo>∘</mo></math> : क्र∘ मू∘ = 6 : 5<br />कुर्सी का अंकित मू<math display=\"inline\"><mo>∘</mo></math>  = 220 + 20 = ₹240<br />6 यूनिट---- ₹240<br />5 यूनिट -----------------<math display=\"inline\"><mfrac><mrow><mn>240</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> × 5 = ₹200<br />लाभ = 220 - 200 = ₹20<br />लाभ% = <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math> × 100 = 10%",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. If 70 percent of total articles are sold at a profit of 20 percent and remaining articles are sold at a loss of 10 percent, ther what will be the overall profit percentage?</p>",
                    question_hi: "<p>12. यदि कुल वस्तुओं का 70 प्रतिशत, 20 प्रतिशत के लाभ पर बेचा जाता है और शेष वस्तुओं को 10 प्रतिशत की हानि पर बेचा जाता है, तो कुल लाभ प्रतिशत कितना होगा ?</p>",
                    options_en: ["<p>12 percent</p>", "<p>8 percent</p>", 
                                "<p>15 percent</p>", "<p>11 percent</p>"],
                    options_hi: ["<p>12 प्रतिशत</p>", "<p>8 प्रतिशत</p>",
                                "<p>15 प्रतिशत</p>", "<p>11 प्रतिशत</p>"],
                    solution_en: "<p>12.(d)<br>No of articles&nbsp; &nbsp;70&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 30 = 7 : 3<br>Profit/Loss%&nbsp; &nbsp;+20%&nbsp; &nbsp; -10%<br>Overall profit% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mn>20</mn><mi>%</mi><mo>-</mo><mn>3</mn><mo>&#215;</mo><mn>10</mn><mi>%</mi></mrow><mrow><mn>7</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>10</mn></mfrac></math>% = 11%</p>",
                    solution_hi: "<p>12.(d)<br>वस्तुओं की संख्या&nbsp; 70&nbsp; &nbsp; &nbsp; &nbsp;30 = 7 : 3<br>लाभ/हानि%&nbsp; &nbsp; &nbsp; &nbsp; +20%&nbsp; &nbsp;-10%<br>कुल लाभ% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#215;</mo><mn>20</mn><mi>%</mi><mo>-</mo><mn>3</mn><mo>&#215;</mo><mn>10</mn><mi>%</mi></mrow><mrow><mn>7</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>10</mn></mfrac></math>% = 11%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A man buys 35 pens for a rupee. How many pens should he sell for a rupee to earn a profit of 40 percent?</p>",
                    question_hi: "<p>13. एक आदमी एक रुपए में 35 कलम खरीदता है। 40 प्रतिशत का लाभ अर्जित करने के लिए उसे एक रुपए में कितने कलम बेचने चाहिए?</p>",
                    options_en: ["<p>34</p>", "<p>25</p>", 
                                "<p>27</p>", "<p>37</p>"],
                    options_hi: ["<p>34</p>", "<p>25</p>",
                                "<p>27</p>", "<p>37</p>"],
                    solution_en: "<p>13.(b)<br>Price &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>Q</mi><mi>u</mi><mi>a</mi><mi>n</mi><mi>t</mi><mi>i</mi><mi>t</mi><mi>y</mi></mrow></mfrac></math><br>Price&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100 : 140 = 5 : 7<br>Quantity&nbsp; &nbsp; &nbsp; &nbsp; 7 : 5<br>7 unit -------------- 35 pens <br>5 unit -------------- <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 5 = 25 pens</p>",
                    solution_hi: "<p>13.(b)<br>कीमत &prop; <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>&#2350;</mi><mi>&#2366;</mi><mi>&#2340;</mi><mi>&#2381;</mi><mi>&#2352;</mi><mi>&#2366;</mi></mrow></mfrac></math><br>कीमत&nbsp; &nbsp;100 : 140 = 5 : 7<br>मात्रा&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;7 : 5<br>7 इकाई = 35 कलम <br>5 इकाई = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 5 = 25 कलम</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "14. On Selling a book at 16/25 of the marked price there is a loss. What will be the ratio of marked price and selling price of the book ?",
                    question_hi: "14. एक पुस्तक को उसके अंकित मूल्य के 16/25 पर बेचने पर हानि होती है। पुस्तक के अंकित मूल्य और विक्रय मूल्य का अनुपात कितना होगा?",
                    options_en: [" 3 : 8", " 8 : 3", 
                                " 25 : 16", " 16 : 15"],
                    options_hi: [" 3 : 8", " 8 : 3",
                                " 25 : 16", " 16 : 15"],
                    solution_en: "14.(c)<br />MP : SP = 25 : 16",
                    solution_hi: "14.(c)<br />MP : SP = 25 : 16",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. There is a loss of 18% when a table is sold for ₹ 11,480. Calculate the loss or profit percentage when the same table is sold for ₹ 15,540.</p>",
                    question_hi: "<p>15. एक मेज को ₹ 11,480 में बेचने पर 18% की हानि होती है। जब वही मेज ₹ 15,540 में बेची जाती है, तो हानि या लाभ प्रतिशत की गणना कीजिए।</p>",
                    options_en: ["<p>loss of 12%</p>", "<p>loss of 10%</p>", 
                                "<p>profit of 11%</p>", "<p>profit of 12%</p>"],
                    options_hi: ["<p>12% की हानि</p>", "<p>10% की हानि</p>",
                                "<p>11% का लाभ</p>", "<p>12% का लाभ</p>"],
                    solution_en: "<p>15.(c) According to question,<br><math display=\"inline\"><mo>&#8658;</mo></math> CP of a table = 11480 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>82</mn></mfrac></math>= 14000<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>15540</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>14000</mn></mrow><mrow><mn>14000</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1540</mn><mn>140</mn></mfrac></math>= 11%</p>",
                    solution_hi: "<p>15.(c) प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> मेज का क्रय मूल्य = 11480 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>82</mn></mfrac></math>= 14000<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>15540</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>14000</mn></mrow><mrow><mn>14000</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1540</mn><mn>140</mn></mfrac></math>= 11%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>