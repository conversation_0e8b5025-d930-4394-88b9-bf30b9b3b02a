<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 75</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">75</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 74,
                end: 74
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. A starts a business with ₹7,250 and after 3 months, B joins A as his partner. After a year since A started the business, the profit is divided between A and B in the ratio 8 : 9. What is B\'s contribution in the capital ?</p>",
                    question_hi: "<p>1. A, ₹7,250 की पूंजी के साथ एक व्यवसाय शुरू करता है, और 3 महीने बाद, B साझेदार के रूप में A के साथ जुड़ जाता है। A द्वारा व्यवसाय शुरू करने के एक वर्ष बाद, लाभ को A और B के बीच 8 : 9 के अनुपात में विभाजित किया जाता है। पूंजी में B का योगदान ज्ञात कीजिए ।</p>",
                    options_en: ["<p>₹ 10,250</p>", "<p>₹ 10,500</p>", 
                                "<p>₹ 10,000</p>", "<p>₹ 10,875</p>"],
                    options_hi: ["<p>₹ 10,250</p>", "<p>₹ 10,500</p>",
                                "<p>₹ 10,000</p>", "<p>₹ 10,875</p>"],
                    solution_en: "<p>1.(d) Ratio of profit of A and B = 8 : 9<br>Let the amount invested by B = b<br>According to the question,<br>7250 &times; 12 : b &times; 9 = 8 : 9<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7250</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>12</mn></mrow><mrow><mi>b</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>8</mn><mn>9</mn></mfrac></mstyle></math> &rArr; b = 10,875<br>Hence, Amount invested by B = 10,875</p>",
                    solution_hi: "<p>1.(d)<br>A और B के लाभ का अनुपात = 8 : 9<br>माना B द्वारा निवेश की गई राशि = b<br>प्रश्न के अनुसार,<br>7250 &times; 12 : b &times; 9 = 8 : 9<br><math display=\"inline\"><mfrac><mrow><mn>7250</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>12</mn></mrow><mrow><mi>b</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>8</mn><mn>9</mn></mfrac></mstyle></math> &rArr; b = 10,875<br>अतः B द्वारा निवेश की गई राशि = 10,875</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. The Sahitya Akademi was formally inaugurated by the Government of India on 12 March _______</p>",
                    question_hi: "<p>2. साहित्य अकादमी का औपचारिक उद्घाटन भारत सरकार द्वारा 12 मार्च ___ को किया गया था।</p>",
                    options_en: ["<p>1956</p>", "<p>1954</p>", 
                                "<p>1958</p>", "<p>1952</p>"],
                    options_hi: ["<p>1956</p>", "<p>1954</p>",
                                "<p>1958</p>", "<p>1952</p>"],
                    solution_en: "<p>2.(b) <strong>1954. The Sahitya Akademi </strong>was formally inaugurated by the Government of India on 12 March 1954. The award is given in 24 languages. It is a literary Award in India. Rabindra Bhawan, Delhi which houses the Sangeet Natak Akademi, Lalit Kala Akademi, and Sahitya Akademi. </p>",
                    solution_hi: "<p>2.(b) <strong>1954। साहित्य अकादमी</strong> का औपचारिक उद्घाटन भारत सरकार द्वारा 12 मार्च 1954 को किया गया था। यह पुरस्कार 24 भाषाओं में दिया जाता है। यह भारत में एक साहित्यिक पुरस्कार है। रवींद्र भवन, दिल्ली जिसमें संगीत नाटक अकादमी, ललित कला अकादमी और साहित्य अकादमी है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. Select the missing figure based on the given related pair of figures.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252146.png\" alt=\"rId4\" width=\"277\" height=\"75\"></p>",
                    question_hi: "<p>3. चित्र के दिए गए संबंधित जोड़े के आधार पर अनुपस्थित चित्र चुनें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252146.png\" alt=\"rId4\" width=\"277\" height=\"75\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252255.png\" alt=\"rId5\" width=\"116\" height=\"130\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252352.png\" alt=\"rId6\" width=\"163\" height=\"100\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252482.png\" alt=\"rId7\" width=\"135\" height=\"113\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252876.png\" alt=\"rId8\" width=\"130\" height=\"104\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252255.png\" alt=\"rId5\" width=\"116\" height=\"130\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252352.png\" alt=\"rId6\" width=\"163\" height=\"100\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252482.png\" alt=\"rId7\" width=\"135\" height=\"113\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252876.png\" alt=\"rId8\" width=\"130\" height=\"104\"></p>"],
                    solution_en: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252255.png\" alt=\"rId5\" width=\"116\" height=\"130\"></p>",
                    solution_hi: "<p>3.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679252255.png\" alt=\"rId5\" width=\"116\" height=\"130\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. If a force of 20N is applied on a body with a mass of 10kg, what will be the acceleration produced?</p>",
                    question_hi: "<p>4. यदि 10kg द्रव्यमान की किसी वस्तु पर 20N बल लगाया जाता है, तो त्वरण कितना होगा ?</p>",
                    options_en: ["<p>200m/sec&sup2;</p>", "<p>2m/sec&sup2;</p>", 
                                "<p>100m/sec&sup2;</p>", "<p>5m/sec&sup2;</p>"],
                    options_hi: ["<p>200m/sec&sup2;</p>", "<p>2m/sec&sup2;</p>",
                                "<p>100m/sec&sup2;</p>", "<p>5m/sec&sup2;</p>"],
                    solution_en: "<p>4.(b) <strong>2m/sec&sup2;.</strong><br>Force = Mass &times; Acceleration = m &times; a<br>Force has magnitude as well as direction. It is a vector quantity.<br>Force = 20 N<br>Mass= 10 kg<br>Acceleration = ?<br>20 = 10 &times; a<br>a = 2 m/sec&sup2;</p>",
                    solution_hi: "<p>4.(b) <strong>2 मीटर/सेकंड&sup2;।</strong><br>बल = द्रव्यमान &times; त्वरण = m &times; a <br>बल में परिमाण के साथ-साथ दिशा भी होती है। यह एक सदिश राशि है।<br>बल = 20 N<br>द्रव्यमान = 10 kg<br>त्वरण = ?<br>20 = 10 &times; a<br>a = 2 मीटर/सेकंड&sup2; ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. Select the odd figure one out the given series.<br><strong id=\"docs-internal-guid-ac5c0af2-7fff-8715-ea1c-2f372d2dbf3e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeZKMO27Uz0yaiGl_liXyojx-y9HqretJuLGkLxg-tAm6ivYDAZsMCspk7W5DcpVJGj0Lo5GdJt9tZap749J8MfmeM7Ji6o1pixoDiVYOjh47CSYe37SaUueYMuHZ84KadNJ2XhGTFvXDwWFuBYvG0-vpEq?key=173xHP_0HTfmOuXxTPd7aQ\" width=\"243\" height=\"83\"></strong></p>",
                    question_hi: "<p>5. दी गई श्रृंखला में असंगत चित्र का चयन करें।<br><strong id=\"docs-internal-guid-ac5c0af2-7fff-8715-ea1c-2f372d2dbf3e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeZKMO27Uz0yaiGl_liXyojx-y9HqretJuLGkLxg-tAm6ivYDAZsMCspk7W5DcpVJGj0Lo5GdJt9tZap749J8MfmeM7Ji6o1pixoDiVYOjh47CSYe37SaUueYMuHZ84KadNJ2XhGTFvXDwWFuBYvG0-vpEq?key=173xHP_0HTfmOuXxTPd7aQ\" width=\"243\" height=\"83\"></strong></p>",
                    options_en: ["<p>d</p>", "<p>b</p>", 
                                "<p>a</p>", "<p>c</p>"],
                    options_hi: ["<p>d</p>", "<p>b</p>",
                                "<p>a</p>", "<p>c</p>"],
                    solution_en: "<p>5.(c) a<br><strong id=\"docs-internal-guid-55b6dfff-7fff-3d77-f424-dfcd43c48c8e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfGaDKV-vQtotoEtyXf0Vqbqd-Bjd1FtCSgFA4btrCyNgM5HsY3kxPIqHf2ZKZK4EcsGSS5LKCr0w4na-L5etMxVOt5DlPqMusXQ992b91y5vxuXc0vcVjgiDLFVgrxw311cK6NSeaG7sa7WjOGfa9W7Lcd?key=173xHP_0HTfmOuXxTPd7aQ\" width=\"112\" height=\"67\"></strong><br>All the options uses electricity to operate, and are modern technologies except the postcard.</p>",
                    solution_hi: "<p>5.(c) a<br><strong id=\"docs-internal-guid-55b6dfff-7fff-3d77-f424-dfcd43c48c8e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfGaDKV-vQtotoEtyXf0Vqbqd-Bjd1FtCSgFA4btrCyNgM5HsY3kxPIqHf2ZKZK4EcsGSS5LKCr0w4na-L5etMxVOt5DlPqMusXQ992b91y5vxuXc0vcVjgiDLFVgrxw311cK6NSeaG7sa7WjOGfa9W7Lcd?key=173xHP_0HTfmOuXxTPd7aQ\" width=\"112\" height=\"67\"></strong><br>पोस्टकार्ड को छोड़कर सभी विकल्प संचालित होने के लिए बिजली का उपयोग करते हैं और आधुनिक प्रौद्योगिकियां हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. The component of white light that deviates the most on passing through a glass prism is:</p>",
                    question_hi: "<p>6.कांच के प्रिज्म से होकर गुजरने पर श्वेत प्रकाश के किस रंग घटक का विचलन अधिकतम होता है ?</p>",
                    options_en: ["<p>blue</p>", "<p>red</p>", 
                                "<p>violet</p>", "<p>green</p>"],
                    options_hi: ["<p>नीला</p>", "<p>लाल</p>",
                                "<p>बैंगनी</p>", "<p>हरा</p>"],
                    solution_en: "<p>6.(c) <strong>Violet. Dispersion of light:</strong> When a white light is passed through a prism it splits into its seven constituent colors <strong>(VIBGYOR)</strong>. The <strong>Red color</strong> ( wavelength-700 nm) deviates least and <strong>Violet</strong> color (wavelength-400nm) deviates most because its wavelength is shortest among other colors.</p>",
                    solution_hi: "<p>6.(c) <strong>बैंगनी। प्रकाश का विक्षेपण:</strong> जब सफेद प्रकाश एक प्रिज्म के माध्यम से गुजरता है तो यह अपने सात घटक रंगों में <strong>(VIBGYOR) </strong>विभाजित हो जाता है। लाल रंग (तरंग दैर्ध्य-700nm) सबसे कम विचलित होता है और <strong>बैंगनी</strong> रंग (तरंग दैर्ध्य-400nm) सबसे अधिक विचलित होता है क्योंकि इसकी तरंग दैर्ध्य अन्य रंगों की तुलना में कम होती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. How many triangles are present in the below figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679253383.png\" alt=\"rId12\" width=\"138\" height=\"125\"></p>",
                    question_hi: "<p>7. नीचे दी गई आकृति में कितने त्रिभुज हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679253383.png\" alt=\"rId12\" width=\"138\" height=\"125\"></p>",
                    options_en: ["<p>16</p>", "<p>19</p>", 
                                "<p>18</p>", "<p>17</p>"],
                    options_hi: ["<p>16</p>", "<p>19</p>",
                                "<p>18</p>", "<p>17</p>"],
                    solution_en: "<p>7.(c) there are 18 triangles</p>",
                    solution_hi: "<p>7.(c) 18 त्रिभुज हैं</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. A trader owes a merchant ₹12,500 due in one year. However, the trader wants to settle the amount after 6 months. If the rate of interest is 8% per annum, then how much money should he pay ? (Give your answer correct to the nearest whole number value.)</p>",
                    question_hi: "<p>8. एक व्यापारी पर एक साहूकार का एक वर्ष में देय ₹12,500 बकाया है। हालांकि, व्यापारी 6 माह बाद ऋण का निपटान करना चाहता है। यदि ब्याज की दर 8% वार्षिक है, तो उसे कितनी राशि का भुगतान करना होगा? (अपना उत्तर निकटतम पूर्ण संख्या मान तक पूर्णांकित कीजिए ।)</p>",
                    options_en: ["<p>₹12,091</p>", "<p>₹10,291</p>", 
                                "<p>₹10,219</p>", "<p>₹12,019</p>"],
                    options_hi: ["<p>₹12,091</p>", "<p>₹10,291</p>",
                                "<p>₹10,219</p>", "<p>₹12,019</p>"],
                    solution_en: "<p>8.(d) Let owed amount = x<br>Amount in 1 year = 12,500<br>Rate = 8%<br>x &times; <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 12,500 &rArr; x = 11,574<br>Rate of interest in 6 months = 4%<br>Amount in 6 months = 11574 &times; <math display=\"inline\"><mfrac><mrow><mn>104</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>= ₹12,036.96<math display=\"inline\"><mo>&#8776;</mo></math>₹12,019</p>",
                    solution_hi: "<p>8.(d) माना, बकाया राशि = x<br>1 वर्ष में राशि = 12,500<br>दर = 8%<br>x &times; <math display=\"inline\"><mfrac><mrow><mn>108</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 12,500 &rArr; x = 11,574<br>6 महीने में ब्याज दर = 4%<br>6 महीने में राशि =11574 &times; <math display=\"inline\"><mfrac><mrow><mn>104</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>= ₹12,036.96<math display=\"inline\"><mo>&#8776;</mo></math>₹12,019</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. &lsquo;A Nation in Making&rsquo; a book written by ______.</p>",
                    question_hi: "<p>9. \'A Nation in Making (ए नेशन इन मेकिंग)\' नामक पुस्तक के लेखक हैं।</p>",
                    options_en: ["<p>M.G. Ranade</p>", "<p>Surendranath Banerjee</p>", 
                                "<p>G.K. Gokhale</p>", "<p>Vallabhbhai Patel</p>"],
                    options_hi: ["<p>जी. के. गोखले</p>", "<p>सुरेन्द्रनाथ बनर्जी</p>",
                                "<p>जी. के. गोखले</p>", "<p>वल्लभभाई पटेल</p>"],
                    solution_en: "<p>9.(b) <strong>Surendranath Banerjee:-</strong> Founder of the Indian National Association, founder member of Indian National Congress. He is also known as <strong>Rashtraguru</strong> and<strong> Indian Burke</strong> as he was greatly influenced by the liberal philosophy and ideology of Edmund Burke. <strong>&ldquo;A Nation in Making&rdquo;</strong> is his autobiography. <strong>Authors and their books </strong>: M.G. Ranade - &ldquo;Rise of the Maratha Power&rdquo;, Vallabhbhai Patel - &ldquo;Bharat Vibhajan&rdquo;, Lala Lajpat Rai - &ldquo;Unhappy India&rdquo;, Rajendra Prasad - &ldquo;India Undivided&rdquo;. Rabindranath Tagore - &ldquo;Gitanjali&rdquo;.</p>",
                    solution_hi: "<p>9.(b) <strong>सुरेंद्रनाथ बनर्जी:-</strong> इंडियन नेशनल एसोसिएशन के संस्थापक, भारतीय राष्ट्रीय कांग्रेस के संस्थापक सदस्य थे। उन्हें <strong>राष्ट्रगुरु </strong>और <strong>भारतीय बर्क</strong> के नाम से भी जाना जाता है क्योंकि ये एडमंड बर्क के उदार दर्शन और विचारधारा से बहुत प्रभावित थे। <strong>\"ए नेशन इन मेकिंग</strong>\" इनकी आत्मकथा है। <strong>लेखक और उनकी पुस्तकें:</strong> एम.जी. रानाडे - \"राइज़ ऑफ़ द मराठा पावर\", वल्लभभाई पटेल - \"भारत विभाजन\", लाला लाजपत राय - \"अनहैप्पी इंडिया\", राजेंद्र प्रसाद - \"इंडिया अनडिवाइडेड\"। रवीन्द्रनाथ टैगोर - \"गीतांजलि\"।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10. In a certain code language, STOLE is written as 39468 and RICH is written as 5271. In that code language, the word THIS will be written as:</p>",
                    question_hi: "<p>10.एक निश्चित कूटभाषा में, STOLE को 39468 और RICH को 5271 लिखा जाता है। उसी कूटभाषा में, शब्द THIS को लिखा जाएगा:</p>",
                    options_en: ["<p>3274</p>", "<p>9123</p>", 
                                "<p>9283</p>", "<p>7583</p>"],
                    options_hi: ["<p>3274</p>", "<p>9123</p>",
                                "<p>9283</p>", "<p>7583</p>"],
                    solution_en: "<p>10.(b) S - T - O - L - E = 3 - 9 - 4 - 6 - 8<br>R - I - C - H = 5 - 2 - 7 - 1<br>Now, T - H - I- S = 9 - 1 - 2 - 3</p>",
                    solution_hi: "<p>10. (b) S - T - O - L - E = 3 - 9 - 4 - 6 -&nbsp; 8<br>R - I - C - H = 5 - 2 - 7 - 1<br>अब, T - H - I - S = 9 - 1 - 2 - 3</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. Select the combination of letters that when sequentially placed in the blanks will create a repetitive pattern.<br>pq _ ppq _ ppq _ pp _ pp</p>",
                    question_hi: "<p>11. अक्षरों के उस संयोजन का चयन करें जिसे जब क्रमिक रूप से रिक्त स्थान में रखा जाए तो एक दोहराव वाला पैटर्न बन जाएगा।<br>pq _ ppq _ ppq _ pp _ pp</p>",
                    options_en: ["<p>pppq</p>", "<p>pqpq</p>", 
                                "<p>qppp</p>", "<p>pqqp</p>"],
                    options_hi: ["<p>pppq</p>", "<p>pqpq</p>",
                                "<p>qppp</p>", "<p>pqqp</p>"],
                    solution_en: "<p>11.(a) pq<span style=\"text-decoration: underline;\"><strong>p</strong></span>p/pq<span style=\"text-decoration: underline;\"><strong>p</strong></span>p/pq<span style=\"text-decoration: underline;\"><strong>p</strong></span>p/p<span style=\"text-decoration: underline;\"><strong>q</strong></span>pp</p>",
                    solution_hi: "<p>11.(a) pq<span style=\"text-decoration: underline;\"><strong>p</strong></span>p/pq<span style=\"text-decoration: underline;\"><strong>p</strong></span>p/pq<span style=\"text-decoration: underline;\"><strong>p</strong></span>p/p<span style=\"text-decoration: underline;\"><strong>q</strong></span>pp</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. An object of mass 15 kg is moving with a uniform velocity of 4 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>ms</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup></math>. What is the kinetic energy possessed by the object?</p>",
                    question_hi: "<p>12. एक वस्तु जिसका द्रव्यमान 15 किलोग्राम है तथा 4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>ms</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup></math> के एक समान वेग से गति कर रही है। वस्तु की गतिज ऊर्जा कितनी है?</p>",
                    options_en: ["<p>60 J</p>", "<p>12 J</p>", 
                                "<p>1.2 J</p>", "<p>120 J</p>"],
                    options_hi: ["<p>60 जूल</p>", "<p>12 जूल</p>",
                                "<p>1.2 जूल</p>", "<p>120 जूल</p>"],
                    solution_en: "<p>12.(d) <strong>120 J. Given:</strong> Mass(m) = 15 Kg, Velocity(v) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi>ms</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup></math><br>Kinetic Energy (K.E.) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>&nbsp;mv&sup2;<br>K.E. = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 15 &times; 4&sup2; = 120 J</p>",
                    solution_hi: "<p>12.(d) <strong>120 जूल।</strong> दिया गया है: द्रव्यमान(m) = 15 किग्रा, वेग(v) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>4</mn><msup><mi>ms</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup></math><br>गतिज ऊर्जा (K.E.) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> mv&sup2;<br>K.E. = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 15 &times; 4&sup2; = 120 जूल।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. The given Problem Figure is embedded in one of the given Answer Figures. Which is that Answer Figure?<br>Problem Figure<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679253490.png\" alt=\"rId13\" width=\"120\" height=\"70\"> <br>Answer figures <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679253684.png\" alt=\"rId14\" width=\"259\" height=\"130\"></p>",
                    question_hi: "<p>13. दिए गए समस्या चित्र को उत्तर चित्रों में से किसी एक में सन्निहित किया गया है। वह उत्तर चित्र कौन सा है?<br>समस्या चित्र<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679253490.png\" alt=\"rId13\" width=\"120\" height=\"70\"> <br>उत्तर चित्र<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679253684.png\" alt=\"rId14\" width=\"259\" height=\"130\"></p>",
                    options_en: ["<p>B</p>", "<p>A</p>", 
                                "<p>C</p>", "<p>D</p>"],
                    options_hi: ["<p>B</p>", "<p>A</p>",
                                "<p>C</p>", "<p>D</p>"],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679253782.png\" alt=\"rId15\" width=\"110\" height=\"113\"></p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679253782.png\" alt=\"rId15\" width=\"110\" height=\"113\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. Who was the founder of the Prarthana Samaj?</p>",
                    question_hi: "<p>14. प्रार्थना समाज के संस्थापक कौन थे</p>",
                    options_en: ["<p>Atmaram Pandurang</p>", "<p>Swami Vivekananda</p>", 
                                "<p>Raja Ram Mohan Roy</p>", "<p>Swami Dayanand Saraswati</p>"],
                    options_hi: ["<p>आत्माराम पांडुरंग</p>", "<p>स्वामी विवेकानंद</p>",
                                "<p>राजा राम मोहन राय</p>", "<p>स्वामी दयानंद सरस्वती</p>"],
                    solution_en: "<p>14.(a) <strong>Atmaram Pandurang. Prarthana Samaj -</strong> Established in 1867 at Bombay. <strong>The Ramakrishna Mission </strong>was established on 1 May 1897 by Swami Vivekananda. Raja Ram Mohan Roy founded the <strong>Atmiya Sabha</strong> in 1815, the Calcutta Unitarian Association in 1821 and the <strong>Brahmo Sabha</strong> (which later became the Brahmo Samaj) in 1828. <strong>Arya Samaj </strong>is a Hindu reform movement founded by Swami Dayanand Saraswati in Bombay in 1875 under the inspiration of Swami Virjanand of Mathura.</p>",
                    solution_hi: "<p>14.(a) <strong>आत्माराम पांडुरंग। प्रार्थना समाज</strong> - बंबई में 1867 में स्थापित। <strong>रामकृष्ण मिशन</strong> की स्थापना 1 मई 1897 को स्वामी विवेकानंद ने की थी। राजा राम मोहन राय ने 1815 में <strong>आत्मीय सभा, </strong>1821 में कलकत्ता यूनिटेरियन एसोसिएशन और 1828 में <strong>ब्रह्म सभा</strong> (जो बाद में ब्रह्म समाज बन गया) की स्थापना की। <strong>आर्य समाज </strong>एक हिंदू सुधार आंदोलन है जिसकी स्थापना स्वामी दयानंद सरस्वती ने 1875 में बॉम्बे में मथुरा के स्वामी विरजानन्द की प्रेरणा से बॉम्बे मे की थी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.</p>\n<p><strong>Statements:</strong> <br>S = L &gt; I &gt; N; G &gt; B = A &lt; D; N &lt; E = M &lt; G<br><strong>Conclusions:</strong><br>i) S &gt; N<br>ii) L &gt; M<br>iii) L &le; M</p>",
                    question_hi: "<p>15. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है,&nbsp;भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, और तय करें कि दिए गए निष्कर्षों में से&nbsp;कौन-सा/से निष्&zwj;कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।&nbsp;</p>\n<p><strong>कथन:</strong><br>S = L &gt; I &gt; N; G &gt; B = A &lt; D; N &lt; E = M &lt; G<br><strong>निष्कर्ष:</strong><br>i) S &gt; N<br>ii) L &gt; M<br>iii) L &le; M</p>",
                    options_en: ["<p>Only i and either ii or iii</p>", "<p>Only i and ii</p>", 
                                "<p>Only iii</p>", "<p>Only i and iii</p>"],
                    options_hi: ["<p>केवल i और या तो ii या iii</p>", "<p>केवल । और ii</p>",
                                "<p>केवल iii</p>", "<p>केवल i और iii</p>"],
                    solution_en: "<p>15.(a) Only i and either ii or iii</p>",
                    solution_hi: "<p>15.(a) केवल i और या तो ii या iii</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. Which energy of the wind does a windmill use?</p>",
                    question_hi: "<p>16. पवनचक्की पवन की किस ऊर्जा का उपयोग करती है?</p>",
                    options_en: ["<p>Thermal energy</p>", "<p>Kinetic energy</p>", 
                                "<p>Heat energy</p>", "<p>Hydro energy</p>"],
                    options_hi: ["<p>तापीय ऊर्जा</p>", "<p>गतिज ऊर्जा</p>",
                                "<p>उष्मीय उर्जा</p>", "<p>जलविद्युत ऊर्जा</p>"],
                    solution_en: "<p>16.(b) <strong>Kinetic energy.</strong> Wind turbines are the windmill\'s modern equivalent, converting the kinetic energy in wind into clean, renewable electricity. Tamil Nadu is a leader in Wind Power in India with favorable wind conditions along its coastline. Muppandal wind farm (Kanyakumari) : The largest wind power plant in India, Total capacity - 1500 MW, . The Gansu Wind Farm in China - The largest wind farm in the world, with a target capacity of 20,000 MW .</p>",
                    solution_hi: "<p>16.(b) <strong>गतिज ऊर्जा।</strong> पवन टरबाइन पवनचक्की के आधुनिक समकक्ष हैं, जो हवा में मौजूद गतिज ऊर्जा को स्वच्छ, नवीकरणीय बिजली में परिवर्तित करते हैं। तमिलनाडु अपने समुद्र तट पर अनुकूल पवन स्थितियों के साथ भारत में पवन ऊर्जा में अग्रणी है। मुप्पांडल पवन फार्म (कन्याकुमारी) : भारत में सबसे बड़ा पवन ऊर्जा संयंत्र, कुल क्षमता - 1500 मेगावाट। चीन में गांसु पवन फार्म - 20,000 मेगावाट की लक्ष्य क्षमता के साथ दुनिया का सबसे बड़ा पवन फार्म।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. The sum of a number, its half, its <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> and 27 , is 71. Find the number.</p>",
                    question_hi: "<p>17. एक संख्या, उसके आधे, उसके <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> और 27 का योगफल 71 है। वह संख्या ज्ञात कीजिए ।</p>",
                    options_en: ["<p>25</p>", "<p>24</p>", 
                                "<p>22</p>", "<p>23</p>"],
                    options_hi: ["<p>25</p>", "<p>24</p>",
                                "<p>22</p>", "<p>23</p>"],
                    solution_en: "<p>17.(b) Let the number = x<br>ATQ, x +<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math>+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>x</mi><mn>3</mn></mfrac></mstyle></math> + 27 = 71<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>6</mn><mi>x</mi><mo>+</mo><mn>3</mn><mi>x</mi><mo>+</mo><mn>2</mn><mi>x</mi></mrow><mn>6</mn></mfrac></mstyle></math> = 44 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>11</mn><mi>x</mi></mrow><mn>6</mn></mfrac></mstyle></math> = 44<br>So, x = 24</p>",
                    solution_hi: "<p>17.(b) माना संख्या = x<br>प्रश्न के अनुसार x +<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math>+<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>x</mi><mn>3</mn></mfrac></mstyle></math> + 27 = 71<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>6</mn><mi>x</mi><mo>+</mo><mn>3</mn><mi>x</mi><mo>+</mo><mn>2</mn><mi>x</mi></mrow><mn>6</mn></mfrac></mstyle></math>= 44<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><mn>11</mn><mi>x</mi></mrow><mn>6</mn></mfrac></math> = 44 <br>अतः x = 24</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. Select the number from among the given options that can replace the question mark (?) in the following table.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679253891.png\" alt=\"rId16\" width=\"259\" height=\"80\"></p>",
                    question_hi: "<p>18. दिए गए विकल्पों में से वह संख्या चुनें जो निम्नलिखित तालिका में प्रश्न चिह्न (?) को प्रतिस्थापित कर सके। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679253891.png\" alt=\"rId16\" width=\"259\" height=\"80\"></p>",
                    options_en: ["<p>22</p>", "<p>40</p>", 
                                "<p>68</p>", "<p>11</p>"],
                    options_hi: ["<p>22</p>", "<p>40</p>",
                                "<p>68</p>", "<p>11</p>"],
                    solution_en: "<p>18.(b) Here the pattern is :<br>For the first term<math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 6 = 2 + 3 + 1 ; For the second term&nbsp;<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>3</mn></mfrac><mo>=</mo><mn>14</mn><mo>=</mo><mn>3</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>7</mn><mo>;</mo></math><br>Similarly for the third term <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math>= 20&nbsp;= 11 + 3 + 6 ;</p>\n<p>So, x = 40 ;</p>",
                    solution_hi: "<p>18 .(b) यहाँ पैटर्न है:<br>प्रथम पद के लिए <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>3</mn></mfrac><mo>=</mo><mn>6</mn><mo>=</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>+</mo><mn>1</mn><mo>;</mo></math> <br>दूसरे पद के लिए <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>42</mn><mn>3</mn></mfrac><mo>=</mo><mn>14</mn><mo>=</mo><mn>3</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>7</mn></math> <br>इसी प्रकार तीसरे पद के लिए <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>2</mn></mfrac></math> = 20&nbsp;= 11 + 3 + 6 ;</p>\n<p>तो, x = 40;</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. A concave mirror having focal length of magnitude 20 cm forms a real image at a distance of 60 cm from it. The object distance (in cm) is</p>",
                    question_hi: "<p>19. एक अवतल दर्पण, जिसकी फोकस दूरी का परिमाण 20 cm है, उससे 60 cm की दूरी पर एक वास्तविक प्रतिबिंब निर्मित करता है। वस्तु की दूरी (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>+15</p>", "<p>+30</p>", 
                                "<p>-30</p>", "<p>-15</p>"],
                    options_hi: ["<p>+15</p>", "<p>+30</p>",
                                "<p>-30</p>", "<p>-15</p>"],
                    solution_en: "<p>19.(c) <strong>-30.</strong> The focal length of the concave mirror is always negative, Given f = -20 cm. The image distance of the concave mirror is negative (image is formed in front of the mirror (real image)), Given v = -60 cm.</p>\n<p>Mirror formula =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>v</mi></mfrac><mo>+</mo><mfrac><mn>1</mn><mi>u</mi></mfrac><mo>=</mo><mfrac><mn>1</mn><mi>f</mi></mfrac><mo>&#8594;</mo><mfrac><mn>1</mn><mrow><mo>-</mo><mn>60</mn></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mi>u</mi></mfrac><mo>=</mo><mfrac><mn>1</mn><mrow><mo>-</mo><mn>20</mn></mrow></mfrac><mo>&#8594;</mo><mfrac><mn>1</mn><mi>u</mi></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>60</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>20</mn></mfrac><mo>&#8594;</mo><mfrac><mn>1</mn><mi>u</mi></mfrac></math></p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mrow><mo>=</mo><mfrac><mrow><mn>1</mn><mo>-</mo><mn>3</mn></mrow><mn>60</mn></mfrac><mo>&#8594;</mo><mfrac><mn>1</mn><mi>u</mi></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>2</mn></mrow><mn>60</mn></mfrac><mo>&#8594;</mo><mi>u</mi><mo>=</mo><mo>-</mo><mn>30</mn></mrow><mi>cm</mi></mstyle></math></p>",
                    solution_hi: "<p>19.(c) <strong>-30।</strong> अवतल दर्पण की फोकस दूरी हमेशा ऋणात्मक होती है, f = -20 cm दिया गया है। अवतल दर्पण की प्रतिबिम्ब दूरी ऋणात्मक होती है (प्रतिबिम्ब दर्पण के सामने बनता है (वास्तविक प्रतिबिम्ब)), दिया गया है v = -60 cm.</p>\n<p>दर्पण सूत्र = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>v</mi></mfrac><mo>+</mo><mfrac><mn>1</mn><mi>u</mi></mfrac><mo>=</mo><mfrac><mn>1</mn><mi>f</mi></mfrac><mo>&#8594;</mo><mfrac><mn>1</mn><mrow><mo>-</mo><mn>60</mn></mrow></mfrac><mo>+</mo><mfrac><mn>1</mn><mi>u</mi></mfrac><mo>=</mo><mfrac><mn>1</mn><mrow><mo>-</mo><mn>20</mn></mrow></mfrac><mo>&#8594;</mo><mfrac><mn>1</mn><mi>u</mi></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>60</mn></mfrac><mo>-</mo><mfrac><mn>1</mn><mn>20</mn></mfrac><mo>&#8594;</mo><mfrac><mn>1</mn><mi>u</mi></mfrac></mstyle></math>&nbsp;</p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mrow><mo>=</mo><mfrac><mrow><mn>1</mn><mo>-</mo><mn>3</mn></mrow><mn>60</mn></mfrac><mo>&#8594;</mo><mfrac><mn>1</mn><mi>u</mi></mfrac><mo>=</mo><mfrac><mrow><mo>-</mo><mn>2</mn></mrow><mn>60</mn></mfrac><mo>&#8594;</mo><mi>u</mi><mo>=</mo><mo>-</mo><mn>30</mn></mrow><mi>cm</mi></mstyle></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. The Fundamental Duties were added in the Indian Constitution by the recommendation of which of the following committees?</p>",
                    question_hi: "<p>20. भारतीय संविधान में मौलिक कर्तव्यों को इनमें से किस समिति की सिफारिश से जोड़ा गया था?</p>",
                    options_en: ["<p>Union Constitution Committee</p>", "<p>Swaran Singh Committee</p>", 
                                "<p>Union Power Committee</p>", "<p>Provincial Constitution Committee</p>"],
                    options_hi: ["<p>संघ संविधान समिति</p>", "<p>स्वर्ण सिंह समिति</p>",
                                "<p>संघ शक्ति समिति</p>", "<p>प्रांतीय संविधान समिति</p>"],
                    solution_en: "<p>20.(b) <strong>Swaran Singh Committee.</strong> Fundamental Duties - Part IV A ( Article 51A) was added by the 42nd Amendment Act 1976. Important Committees of the Constituent Assembly (formed in 1946) - Provincial Constitution Committee (Sardar Vllabhai Patel), Union power committee and Union Constitution committee (JawaharLal Nehru), Drafting Committee (Bhimrao Ambedkar).</p>",
                    solution_hi: "<p>20.(b) <strong>स्वर्ण सिंह समिति । </strong>मौलिक कर्तव्य - भाग IV A (अनुच्छेद 51A) को 42वें संशोधन अधिनियम 1976 द्वारा जोड़ा गया। संविधान सभा की महत्वपूर्ण समितियाँ (1946 में गठित) - प्रांतीय संविधान समिति (सरदार वल्लभाई पटेल), संघ शक्ति समिति और संघ संविधान समिति (जवाहरलाल नेहरू) , प्रारूप समिति (भीमराव अम्बेडकर)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. A question is given followed by two arguments. Decide which of the arguments is/are strong with respect to the question.<br><strong>Question:</strong><br>Should degrees be no longer considered as a criterion for selection in jobs?<br><strong>Arguments:</strong><br>i) Yes, this will help in decreasing crowding in colleges.<br>ii) No, every job requires certain minimum qualifications.</p>",
                    question_hi: "<p>21. एक प्रश्न के बाद दो तर्क दिए गए हैं। निर्णय लें कि प्रश्न के संबंध में कौन-सा/से तर्क प्रबल है/हैं।<br><strong>प्रश्न:</strong><br>क्या अब डिग्री को नौकरियों में चयन का मानदंड नहीं माना जाना चाहिए?<br><strong>तर्क:</strong><br>i) हां, इससे कॉलेजों में भीड़ कम करने में मदद मिलेगी।<br>ii) नहीं, प्रत्येक कार्य के लिए कुछ न्यूनतम योग्यताओं की आवश्यकता होती है।</p>",
                    options_en: ["<p>Only argument (i) is strong.</p>", "<p>Neither argument (i) nor (ii) is strong.</p>", 
                                "<p>only argument (ii) is strong.</p>", "<p>Both arguments (i) and (ii) are strong.</p>"],
                    options_hi: ["<p>केवल तर्क (i) मजबूत है।</p>", "<p>न तो तर्क (i) और न ही (ii) मजबूत है।</p>",
                                "<p>केवल तर्क (ii) मजबूत है।</p>", "<p>दोनों तर्क (i) और (ii) मजबूत हैं।</p>"],
                    solution_en: "<p>21.(c) only argument (ii) is strong.<br>No, every job requires certain minimum qualifications&nbsp;Many types of jobs are available .</p>",
                    solution_hi: "<p>21.(c) केवल तर्क (ii) प्रबल है।<br>नहीं, प्रत्येक कार्य के लिए कुछ न्यूनतम योग्यताओं की आवश्यकता होती है&nbsp;कई प्रकार की नौकरियां उपलब्ध हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. Sound travels at a speed of 333 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>ms</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup></math> in the air, thus, in 1 s, a distance of 333 m is travelled by:</p>",
                    question_hi: "<p>22. ध्वनि हवा में 333&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>ms</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup></math>&nbsp;की चाल से यात्रा करती है; इस प्रकार, 1 सेकंड में, 333 मीटर की दूरी किसके द्वारा तय की जाती है?</p>",
                    options_en: ["<p>particles</p>", "<p>receiver</p>", 
                                "<p>source</p>", "<p>disturbance</p>"],
                    options_hi: ["<p>कण</p>", "<p>रिसीवर</p>",
                                "<p>स्रोत</p>", "<p>अशांति</p>"],
                    solution_en: "<p>22.(d) <strong>disturbance. </strong>Sound travels as disturbance in air as compression and rarefaction. A <strong>receiver</strong> is a device that accepts signals, such as radio waves and converts them into a useful form. <strong>Sources</strong> are objects which encode message data and transmit the information, via a channel, to one or more observers/receivers. A <strong>particle</strong> is a small portion of matter.</p>",
                    solution_hi: "<p>22.(d) <strong>अशांति। </strong>ध्वनि वायु में विक्षोभ के रूप में संपीडन तथा विरलन के रूप में संचरित होती है। <strong>रिसीवर</strong> एक उपकरण है जो संकेतों (जैसे रेडियो तरंगों) को स्वीकार करता है और उन्हें उपयोगी रूप में परिवर्तित करता है। <strong>स्रोत</strong> वे वस्तुएँ हैं जो संदेश डेटा को एनकोड (कूटलेखन) करती हैं और एक चैनल के माध्यम से एक या अधिक पर्यवेक्षकों/प्राप्तकर्ताओं को सूचना प्रेषित करती हैं। एक कण पदार्थ का एक छोटा सा हिस्सा है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. Select an option which is true regarding the following two statements labelled Assertion (A) and Reason (R). <br><strong>A.</strong> Some people have darker skin colour. <br><strong>R. </strong>Some people have blood of darker colour.</p>",
                    question_hi: "<p>23. एक विकल्प का चयन करें जो अभिकथन (A) और कारण (R) लेबल वाले निम्नलिखित दो कथनों के संबंध में सत्य है।<br>A. कुछ लोगों की त्वचा का रंग सांवला होता है।<br>R. कुछ लोगों का रक्त गहरे रंग का होता है।</p>",
                    options_en: ["<p>Both &lsquo;A&rsquo; and &lsquo;R&rsquo; are true but &lsquo;R&rsquo; is not the correct explanation of &lsquo;A&rsquo;.</p>", "<p>Both &lsquo;A&rsquo; and &lsquo;R&rsquo; are false.</p>", 
                                "<p>&lsquo;A&rsquo; is true but &lsquo;R&rsquo; is false.</p>", "<p>Both &lsquo;A&rsquo; and &lsquo;R&rsquo; are true and &lsquo;R&rsquo; is the correct explanation of &lsquo;A&rsquo;.</p>"],
                    options_hi: ["<p>\'A\' और \'R\' दोनों सत्य हैं लेकिन \'R\' \'A\' की सही व्याख्या नहीं है।</p>", "<p>\'A\' और \'R\' दोनों असत्य हैं</p>",
                                "<p>\'A\' सत्य है लेकिन R असत्य है।</p>", "<p>\'A\' और \'R\' दोनों सत्य हैं और \'R\', \'A\' की सही व्याख्या है।</p>"],
                    solution_en: "<p>23.(a) Both A and R are true because some people have darker skin colour and Some people have blood of darker colour. But it is not necessary that the people having darker skin colour have blood of darker colour.</p>",
                    solution_hi: "<p>23.(a) A और R दोनों सत्य हैं क्योंकि कुछ लोगों की त्वचा का रंग सांवला होता है और कुछ लोगों का रक्त गहरे रंग का होता है। लेकिन यह जरूरी नहीं है कि जिन लोगों की त्वचा का रंग सांवला होता है, उनका खून गहरे रंग का होता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. Which of the following is the oldest production unit of the Indian Railway?</p>",
                    question_hi: "<p>24. निम्नलिखित में से कौन सी भारतीय रेलवे की सबसे पुरानी उत्पादन इकाई है?</p>",
                    options_en: ["<p>Rail Wheel Factory</p>", "<p>Integral coach Factory</p>", 
                                "<p>Diesel Locomotive Works</p>", "<p>Chittaranjan Locomotive Works</p>"],
                    options_hi: ["<p>रेल पहिया कारखाना</p>", "<p>इंटीग्रल कोच फैक्टरी</p>",
                                "<p>डीजल लोकोमोटिव कार्य</p>", "<p>चितरंजन लोकोमोटिव वर्क्स</p>"],
                    solution_en: "<p>24.(d)<strong> Chittaranjan Locomotive works </strong>(West bengal) is the oldest production unit of the Indian Railway, established in 1950. Chittaranjan Locomotive Works (CLW), manufactures mainline electric broad gauge locomotives. Rail Wheel Factory (Formerly known as Wheel and Axle Plant) is situated in Bangalore and founded in 1984. Integral coach factory located at Perambur, Chennai and inaugurated in 1955. Diesel Locomotive works located at Varanasi, setup in 1961. Diesel Loco Modernisation Works, Patiala, Rail Coach Factory- Kapurthala. Modern Coach Factory- Raebareli.</p>",
                    solution_hi: "<p>24.(d) <strong>चित्तरंजन लोकोमोटिव वर्क्स </strong>(पश्चिम बंगाल) 1950 में स्थापित भारतीय रेलवे की सबसे पुरानी उत्पादन इकाई है। चित्तरंजन लोकोमोटिव वर्क्स (CLW), मेनलाइन इलेक्ट्रिक ब्रॉड गेज लोकोमोटिव बनाती है। रेल व्हील फैक्ट्री (पूर्व में व्हील और एक्सल प्लांट के रूप में जाना जाता है) बैंगलोर में स्थित है और 1984 में स्थापित की गई थी। इंटीग्रल कोच फैक्ट्री पेरम्बूर, चेन्नई में स्थित है और 1955 में इसका उद्घाटन किया गया। वाराणसी में स्थित डीजल लोकोमोटिव वर्क्स, 1961 में स्थापित किया गया। डीजल लोको मोडर्निज़ेशन वर्क्स, पटियाला, रेल कोच फैक्ट्री- कपूरथला। मॉडर्न कोच फैक्ट्री- रायबरेली।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. The graph and the table below show the number of boys and girls passed in five different subjects i.e. Physics, Chemistry, Biology, English and Hindi <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254037.png\" alt=\"rId17\" width=\"373\" height=\"240\"> <br>What is the ratio of the girls who have passed in Physics, Chemistry and Biology respectively?</p>",
                    question_hi: "<p>25. नीचे दिया गया ग्राफ और तालिका पांच अलग-अलग विषयों यानी भौतिकी, रसायन विज्ञान, जीव विज्ञान, अंग्रेजी और हिंदी में पास हुए लड़कों और लड़कियों की संख्या को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254198.png\" alt=\"rId18\" width=\"390\" height=\"240\"> <br>भौतिकी, रसायन विज्ञान और जीव विज्ञान में उत्तीर्ण लड़कियों का क्रमशः अनुपात क्या है?</p>",
                    options_en: ["<p>9 : 13 : 11</p>", "<p>9 : 13 : 8</p>", 
                                "<p>8 : 11 : 14</p>", "<p>9 : 15 : 13</p>"],
                    options_hi: ["<p>9 : 13 : 11</p>", "<p>9 : 13 : 8</p>",
                                "<p>8 : 11 : 14</p>", "<p>9 : 15 : 13</p>"],
                    solution_en: "<p>25.(a) Ratio of girls who have passed in Physics, Chemistry and Biology = 135 : 195 : 165 = 9 : 13 : 11</p>",
                    solution_hi: "<p>25.(a)&nbsp;भौतिकी, रसायन विज्ञान और जीव विज्ञान में उत्तीर्ण लड़कियों का अनुपात&nbsp;= 135 : 195 : 165 = 9 : 13 : 11</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. For communication, elephants can make noise as loud as 103 decibels but they often use low frequency sound, some of which is passed through the ground. These sounds are called:</p>",
                    question_hi: "<p>26. संचार के लिए, हाथी 103 डेसिबल तक का शोर कर सकते हैं लेकिन वे अक्सर कम आवृत्ति वाली ध्वनि का उपयोग करते हैं, जिनमें से कुछ को जमीन से गुजारा जाता है। इन ध्वनियों को क्या कहा जाता है:</p>",
                    options_en: ["<p>Supersonic sound</p>", "<p>Infrasound</p>", 
                                "<p>Ultrasound</p>", "<p>Plosives</p>"],
                    options_hi: ["<p>पराध्वनिक ध्वनि</p>", "<p>इन्फ्रासाउंड</p>",
                                "<p>अल्ट्रासाउंड</p>", "<p>प्लोसिव्स</p>"],
                    solution_en: "<p>26.(b)<strong> Infrasound</strong> (low-frequency sound) - Other animals can make noise as loud as (in decibel): Sperm Whales (233 decibel), Mantis Shrimp (200), Blue whales (188), Greater Bulldog Bat (140), Howler Monkey (140), Elephant Seal (126), African Cicadas (120), North American Bullfrog (119). Animals With the best Hearing (Frequency Range): Greater Wax Moth (Up to 30000 Hz), Dolphin (2 - 200000 Hz), Bat (9000 - 200000 Hz), Rat (250 - 80000 Hz), Cat (55 - 79000 Hz), Dog (67 - 45000 Hz), Elephant (12 - 12000 Hz), Horse (14 - 2500 Hz).</p>",
                    solution_hi: "<p>26.(b)<strong> इन्फ़्रासाउंड</strong> (कम-आवृत्ति ध्वनि) - अन्य जानवर इतनी तेज़ (डेसिबल में) शोर कर सकते हैं: स्पर्म व्हेल (233 डेसिबल), मेंटिस श्रिम्प (200), ब्लू व्हेल (188), ग्रेटर बुलडॉग बैट (140), हाउलर मंकी (140), एलीफेंट सील (126), अफ़्रीकी सिकाडस (120), उत्तरी अमेरिकी बुलफ्रॉग (119)। सर्वोत्तम श्रवण शक्ति (फ़्रीक्वेंसी रेंज) वाले जानवर: ग्रेटर वैक्स मोथ (30000 हर्ट्ज़ तक), डॉल्फ़िन (2 - 200000 हर्ट्ज़), चमगादड़ (9000 - 200000 हर्ट्ज़), चूहा (250 - 80000 हर्ट्ज़), बिल्ली (55 - 79000 हर्ट्ज़) ), कुत्ता (67 - 45000 हर्ट्ज़), हाथी (12 - 12000 हर्ट्ज़), घोड़ा (14 - 2500 हर्ट्ज़)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. The FRBMA was enacted in 2003, which set targets for the government to reduce fiscal deficits. What does FRBMA stand for?</p>",
                    question_hi: "<p>27. FRBMA को 2003 में अधिनियमित किया गया था, जिसने राजकोषीय घाटे को कम करने के लिए सरकार के लिए लक्ष्य निर्धारित किए थे। FRBMA का मतलब क्या है?</p>",
                    options_en: ["<p>Fiscal Relations and Budget Maintenance Act</p>", "<p>Financial Relations and Budget Management Act</p>", 
                                "<p>Financial Responsibility and Budget Maintenance Act</p>", "<p>Fiscal Responsibility and Budget Management Act</p>"],
                    options_hi: ["<p>राजकोषीय संबंध और बजट अनुरक्षण अधिनियम</p>", "<p>वित्तीय संबंध और बजट प्रबंधन अधिनियम</p>",
                                "<p>वित्तीय उत्तरदायित्व और बजट अनुरक्षण अधिनियम</p>", "<p>राजकोषीय उत्तरदायित्व और बजट प्रबंधन अधिनियम</p>"],
                    solution_en: "<p>27.(d) <strong>Fiscal Responsibility and Budget Management Act -</strong> It was enacted by Parliament of India under the leadership of Mr. Yashwant Sinha, who was the then Finance Minister of India. It sets a target for the government to establish financial discipline in the economy, reduce <strong>fiscal deficit</strong> and improve the management of public funds. A fiscal deficit is a shortfall in a government\'s income compared with its spending.</p>",
                    solution_hi: "<p>27. (d) <strong>राजकोषीय उत्तरदायित्व और बजट प्रबंधन अधिनियम </strong>- इसे भारत की संसद द्वारा श्री यशवंत सिन्ह के नेतृत्व में अधिनियमित किया गया था जो भारत के तत्कालीन वित्त मंत्री थे। यह सरकार के लिए अर्थव्यवस्था में वित्तीय अनुशासन स्थापित करने, राजकोषीय घाटे को कम करने और सार्वजनिक धन के प्रबंधन में सुधार करने का लक्ष्य निर्धारित करता है। <strong>राजकोषीय घाटा </strong>किसी सरकार की आय में उसके खर्च की तुलना में कमी है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. What is the sum of median and mode of the data : <br>8, 1, 5, 4, 9, 6, 3, 6, 1, 3, 6, 9, 1, 7, 2, 6, 5 ?</p>",
                    question_hi: "<p>28. इन आंकड़ों की माध्यिका एवं बहुलक का जोड़ ज्ञात करें | <br>8,1,5,4,9,6,3,6,1,3,6,9,1,7,2,6,5 ?</p>",
                    options_en: ["<p>13</p>", "<p>11</p>", 
                                "<p>12</p>", "<p>14</p>"],
                    options_hi: ["<p>13</p>", "<p>11</p>",
                                "<p>12</p>", "<p>14</p>"],
                    solution_en: "<p>28.(b)<br>Ascending order of the data = 1, 1, 1, 2, 3, 3, 4, 5, 5, 6, 6, 6, 6, 7, 8, 9, 9<br>Middle value = Median = 5<br>The mode is the number that is repeated most often. Here 6 is repeated the maximum number of times (6 times) so the mode will be 6.<br>Required sum = 5 + 6 = 11</p>",
                    solution_hi: "<p>28.(b)<br>आंकड़ों का आरोही क्रम = 1, 1, 1, 2, 3, 3, 4, 5, 5, 6, 6,6 ,6, 7,8 ,9, 9<br>मध्य मान = माध्यिका = 5<br>बहुलक वह संख्या है जिसे सबसे अधिक बार दोहराया जाता है। यहां 6 को अधिकतम बार (6 बार) दोहराया गया है, इसलिए बहुलक 6 होगा।<br>जोड़ = 5 + 6 = 11</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. Consider the given statement true and decide which of the given assumptions is/are implicit.<br><strong>Statement:</strong><br>It is desirable to start writing from 6 years of age.<br><strong>Assumptions:</strong><br>1: Fine motor skills are well developed by 6 years of age. <br>2: Children cannot write before 6 years of age.</p>",
                    question_hi: "<p>29. निम्नलिखित कथन को सही मानते हुए निर्णीत करें कि नीचे प्रस्तुत धारणाओं में से कौन&nbsp;सा अनुमान वाक्य में निहित है।<br><strong>वाक्य:</strong><br>6 वर्ष की आयु से लिखने की शुरुआत करनी चाहिए।<br><strong>धारणा:</strong><br>1: 6 वर्ष की आयु तक काम करने की कुशलता अच्छी तरह से विकसित हो जाती है। <br>2: बच्चे 6 वर्ष की आयु से पहले लिख नहीं सकते हैं।</p>",
                    options_en: ["<p>Only assumption 2 is implicit.</p>", "<p>Both assumptions 1 and 2 are implicit.</p>", 
                                "<p>Only assumption 1 is implicit.</p>", "<p>Neither assumption 1 nor 2 is implicit.</p>"],
                    options_hi: ["<p>केवल धारणा 2 ही निहित है।</p>", "<p>धारणा 1 एवं 2 दोनों ही निहित हैं।</p>",
                                "<p>केवल धारणा 1 ही निहित है।</p>", "<p>न तो धारणा 1 और न ही धारणा 2 निहित हैं।</p>"],
                    solution_en: "<p>29.(d) Neither assumption 1 nor 2 is implicit.</p>",
                    solution_hi: "<p>29.(d) न तो धारणा 1 और न ही धारणा 2 निहित है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. A boat at anchor is rocked by waves whose consecutive crests are 100 m apart. The wave velocity of the moving crests is 25 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>ms</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup></math>. What is the frequency of rocking of the boat?</p>",
                    question_hi: "<p>30. लंगर डालकर रोकी हुई नाव लहरों से हिल रही है, जिनके तरंगों का ऊपरी सिरा 100 मीटर अलग है। तरंग के ऊपरी सिरों का वेग 25&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>ms</mi><mrow><mo>-</mo><mn>1</mn></mrow></msup></math> है। नाव की हिलने की आवृत्ति क्या है?</p>",
                    options_en: ["<p>100 Hz</p>", "<p>0.25 Hz</p>", 
                                "<p>625 Hz</p>", "<p>25 Hz</p>"],
                    options_hi: ["<p>100 हर्ट्ज</p>", "<p>0.25 हर्ट्ज</p>",
                                "<p>625 हर्ट्ज</p>", "<p>25 हर्ट्ज</p>"],
                    solution_en: "<p>30.(b) <strong>0.25 Hz.</strong> Distance between two consecutive crests (&lambda;) = 100 m<br>Velocity of the waves (v) = 25 m/s<br>velocity (v) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#955;</mi><mi>t</mi></mfrac></math> &rArr; t =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>&#955;</mi><mi>v</mi></mfrac></mstyle></math> where t = time<br>t = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &rArr; time (t) = 4<br>frequency (f) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>t</mi></mrow></mfrac></math><br>f = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 0. 25 Hz.<br>frequency of the boat (f) = 0.25 Hz.</p>",
                    solution_hi: "<p>30.(b) <strong>0.25 हर्ट्ज।</strong> दो क्रमागत श्रृंगों (crests) के बीच की दूरी (&lambda;) = 100 मीटर ।<br>तरंगों का वेग (v) = 25 मीटर प्रति सेकेण्ड <br>वेग (v )= <math display=\"inline\"><mfrac><mrow><mi>&#955;</mi></mrow><mrow><mi>t</mi></mrow></mfrac></math> &rArr; t = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>&#955;</mi><mi>v</mi></mfrac></mstyle></math> जहाँ t = समय<br>t = <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &rArr; समय (t) = 4<br>आवृत्ति (f) = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>t</mi></mrow></mfrac></math><br>f = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 0. 25 हर्ट्ज।<br>नाव की आवृत्ति (f) = 0.25 हर्ट्ज।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. The sum of three consecutive natural numbers is 141. The middle number is :</p>",
                    question_hi: "<p>31. तीन क्रमागत प्राकृतिक संख्याओं का योगफल 141 है। मध्य वाली संख्या का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>46</p>", "<p>47</p>", 
                                "<p>56</p>", "<p>57</p>"],
                    options_hi: ["<p>46</p>", "<p>47</p>",
                                "<p>56</p>", "<p>57</p>"],
                    solution_en: "<p>31.(b) Middle number of consecutive numbers = average of the numbers<br>So middle number = <math display=\"inline\"><mfrac><mrow><mn>141</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 47</p>",
                    solution_hi: "<p>31.(b)<br>क्रमागत संख्याओं की मध्य संख्या = संख्याओं का औसत <br>अतः मध्य संख्या =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>141</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 47</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. Which of the following is dedicated as India&rsquo;s first educational satellite?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन भारत के पहले शैक्षिक उपग्रह के रूप में समर्पित है?</p>",
                    options_en: ["<p>GSAT-3</p>", "<p>INSAT- 4A</p>", 
                                "<p>HAMSAT</p>", "<p>CARTOSAT-1</p>"],
                    options_hi: ["<p>जीसैट-3</p>", "<p>इन्सैट- 4A</p>",
                                "<p>हैमसेट</p>", "<p>कार्टोसैट-1</p>"],
                    solution_en: "<p>32.(a)<strong> GSAT-3 </strong>(EDUSAT): Launched - 2004 by ISRO (Indian Space Research Organisation). <strong>Indian Satellites:</strong> Aryabhatta (1975) - India\'s first satellite. Bhaskara-I (1979) - First experimental remote sensing satellite that carried TV and microwave cameras.<strong> INSAT-4A</strong> (2005) - Advanced satellite for direct-to-home television broadcasting services. CartoSat-1 (2005) - Earth observation satellite. <strong>HAMSAT </strong>(2005) - Microsatellite for radio services to national and international community.</p>",
                    solution_hi: "<p>32.(a) <strong>GSAT-3 </strong>(EDUSAT): 2004 में ISRO (भारतीय अंतरिक्ष अनुसंधान संगठन) द्वारा लॉन्च किया गया। <strong>भारतीय उपग्रह: </strong>आर्यभट्ट (1975) - भारत का प्रथम उपग्रह है। भास्कर - I (1979) - प्रथम प्रायोगिक रिमोट सेंसिंग उपग्रह जो TV और माइक्रोवेव कैमरे ले गया। <strong>INSAT-4A </strong>(2005) - डायरेक्ट-टू-होम टेलीविजन प्रसारण सेवाओं के लिए उन्नत उपग्रह है।<strong> CartoSat-1</strong> (2005) - पृथ्वी अवलोकन उपग्रह है। <strong>HAMSAT</strong> (2005) - राष्ट्रीय और अंतर्राष्ट्रीय समुदाय के लिए रेडियो सेवाओं के लिए माइक्रोसैटेलाइट है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. Select the correct conclusion(s) / option that could be drawn from the given statements(s).<br><strong>Statement:</strong><br>Some bugs are birds. <br>All the birds are fruits.<br><strong>Conclusions:</strong><br>1. All the fruits are birds.<br>2. Some of the bugs are fruits.</p>",
                    question_hi: "<p>33. दिए गए कथनों से निकाले जा सकने वाले सही निष्कर्ष/विकल्प का चयन करें।<br><strong>कथन:</strong><br>कुछ कीड़े पक्षी हैं। <br>सभी पक्षी फल हैं।<br><strong>निष्कर्ष:</strong><br>1. सभी फल पक्षी हैं ।<br>2. कुछ कीड़े फल हैं।</p>",
                    options_en: ["<p>Only conclusion 2 follows.</p>", "<p>Both conclusions 1 and 2 follow.</p>", 
                                "<p>Neither conclusion 1 nor 2 follows.</p>", "<p>Only conclusion 1 follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष 2 अनुसरण करता है।</p>", "<p>1 और 2 दोनों अनुसरण करते हैं।</p>",
                                "<p>1 और 2 दोनों अनुसरण नहीं करते हैं।</p>", "<p>केवल निष्कर्ष 1 अनुसरण करता है।</p>"],
                    solution_en: "<p>33.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254299.png\" alt=\"rId19\" width=\"221\" height=\"46\"><br>Only conclusion 2 follow</p>",
                    solution_hi: "<p>33.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254416.png\" alt=\"rId20\" width=\"210\" height=\"53\"><br>केवल निष्कर्ष 2 अनुसरण करता है</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. How many elements exist in nature according to Newlands law of octaves?</p>",
                    question_hi: "<p>34. न्यूलैंड्स के अष्टक सिद्धांत के अनुसार, प्रकृति में कितने तत्व मौजूद हैं?</p>",
                    options_en: ["<p>56</p>", "<p>36</p>", 
                                "<p>46</p>", "<p>76</p>"],
                    options_hi: ["<p>56</p>", "<p>36</p>",
                                "<p>46</p>", "<p>76</p>"],
                    solution_en: "<p>34.(a) <strong>56. Newland\'s law of octaves:</strong> Every eighth element has similar properties when the elements are arranged in the increasing order of their atomic masses. <strong>Limitations</strong> - Several elements such as cobalt and nickel were fit into the same slots, Elements with dissimilar properties were grouped together (For example, the halogens were grouped with some metals such as cobalt, nickel and platinum), true only for elements up to calcium, Elements with greater atomic masses could not be accommodated into octaves, later discovered elements could not fit into the octave pattern.</p>",
                    solution_hi: "<p>34.(a) <strong>56 । न्यूलैंड का अष्टक नियम: </strong>जब तत्वों को उनके परमाणु द्रव्यमान के बढ़ते क्रम में व्यवस्थित किया जाता है तो प्रत्येक आठवें तत्व के समान गुण होते हैं। <strong>सीमाएं</strong> - कोबाल्ट और निकेल जैसे कई तत्वों को एक ही तालिका में रखा गया था, असमान गुणों वाले तत्वों को एक साथ समूहीकृत किया गया था (उदाहरण के लिए, हैलोजन को कुछ धातुओं जैसे कोबाल्ट, निकल और प्लैटिनम के साथ समूहीकृत किया गया था), केवल कैल्शियम तक के तत्वों के लिए यह नियम सही , था और अधिक परमाणु द्रव्यमान वाले तत्वों को अष्टक में समायोजित नहीं किया जा सका, बाद में खोजे गए तत्व अष्टक समूह में नहीं रखे जा सके।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. The HCF of 24 and 144 is &lsquo;10p + 4&rsquo; , then the value of p is:</p>",
                    question_hi: "<p>35. 24 और 144 का HCF \'10p + 4\' है, तो p का मान कितना है?</p>",
                    options_en: ["<p>1</p>", "<p>4</p>", 
                                "<p>2</p>", "<p>3</p>"],
                    options_hi: ["<p>1</p>", "<p>4</p>",
                                "<p>2</p>", "<p>3</p>"],
                    solution_en: "<p>35.(c) H.C.F. of 24 and 144 = 24<br>A/Q, 10p + 4 = 24 <br><math display=\"inline\"><mo>&#8658;</mo></math>10p = 20 &rArr; p = 2</p>",
                    solution_hi: "<p>35.(c) 24 और 144 का H.C.F. = 24<br>प्रश्न के अनुसार , 10p+4 = 24 <br><math display=\"inline\"><mo>&#8658;</mo></math>10p = 20 &rArr; p = 2</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. India won the Thomas Cup in the year ____________ .</p>",
                    question_hi: "<p>36. भारत ने वर्ष ____________ में थॉमस कप जीता था।</p>",
                    options_en: ["<p>2018</p>", "<p>2019</p>", 
                                "<p>2022</p>", "<p>2020</p>"],
                    options_hi: ["<p>2018</p>", "<p>2019</p>",
                                "<p>2022</p>", "<p>2020</p>"],
                    solution_en: "<p>36.(c) <strong>2022.</strong> Venue - Bangkok (Thailand). Lakshya Sen (single) defeated Anthony Ginting, Kidambi Srikanth (single) defeated Jonatan Christie, Doubles pair Satwik and Chirag defeated Ahsan-Sukamuljo. <strong>Thomas cup</strong> is an international Badminton championship for Men. <strong>Uber Cup</strong> is an international Badminton championship for Women.</p>",
                    solution_hi: "<p>36.(c)<strong> 2022.</strong> स्थान - बैंकॉक (थाईलैंड)। लक्ष्य सेन (एकल) ने एंथोनी गिनटिंग को, किदाम्बी श्रीकांत (एकल) ने जोनाटन क्रिस्टी को, युगल जोड़ी सात्विक और चिराग ने अहसान-सुकामुलजो को हराया। <strong>थॉमस कप</strong> पुरुषों के लिए एक अंतरराष्ट्रीय बैडमिंटन चैम्पियनशिप है। <strong>उबेर कप</strong> महिलाओं के लिए एक अंतरराष्ट्रीय बैडमिंटन चैंपियनशिप है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. Which of the following is a copper ore?</p>",
                    question_hi: "<p>37. निम्नलिखित में से कौन एक तांबे का अयस्क है?</p>",
                    options_en: ["<p>Haematite</p>", "<p>Chromite</p>", 
                                "<p>Malachite</p>", "<p>Magnetite</p>"],
                    options_hi: ["<p>हेमेटाइट</p>", "<p>क्रोमाइट</p>",
                                "<p>मैलाकाइट</p>", "<p>मैगनेटाइट</p>"],
                    solution_en: "<p>37.(c)&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"bold\">Malachite</mi><mo mathvariant=\"bold\">(</mo><msub><mi mathvariant=\"bold\">Cu</mi><mn mathvariant=\"bold\">2</mn></msub><msub><mi mathvariant=\"bold\">CO</mi><mn mathvariant=\"bold\">3</mn></msub><msub><mrow><mo mathvariant=\"bold\">(</mo><mi mathvariant=\"bold\">OH</mi><mo mathvariant=\"bold\">)</mo></mrow><mn mathvariant=\"bold\">2</mn></msub><mo mathvariant=\"bold\">)</mo></math> is a copper carbonate hydroxide mineral that is often found in association with copper deposits. It has a distinctive green colour and is commonly used as an ornamental stone.<strong> Haematite&nbsp;</strong><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msub><mi>Fe</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>3</mn></msub><mo>)</mo></math> is an Iron ore. <strong>Chromite</strong> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msub><mi>Cr</mi><mn>2</mn></msub><msub><mi>FeO</mi><mn>4</mn></msub><mo>)</mo></math> is an ore of Chromium. <strong>Magnetite</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Fe</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>4</mn></msub></math>) is the main ore of Iron.</p>",
                    solution_hi: "<p>37.(c) <strong>मैलाकाइट (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"bold\">Cu</mi><mn mathvariant=\"bold\">2</mn></msub><msub><mi mathvariant=\"bold\">CO</mi><mn mathvariant=\"bold\">3</mn></msub><msub><mrow><mo mathvariant=\"bold\">(</mo><mi mathvariant=\"bold\">OH</mi><mo mathvariant=\"bold\">)</mo></mrow><mn mathvariant=\"bold\">2</mn></msub></math>)</strong> एक कॉपर कार्बोनेट हाइड्रॉक्साइड खनिज है जो अक्सर तांबे के भंडार के साथ पाया जाता है। इसका एक विशिष्ट हरा रंग है और इसका उपयोग आमतौर पर सजावटी पत्थर के रूप में किया जाता है। <strong>हेमेटाइट</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Fe</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>3</mn></msub></math>) एक लौह अयस्क है।<strong> क्रोमाइट</strong>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Cr</mi><mn>2</mn></msub><msub><mi>FeO</mi><mn>4</mn></msub></math>) क्रोमियम का एक अयस्क है।&nbsp;<strong>मैग्नेटाइट</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Fe</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>4</mn></msub></math>) लोहे का मुख्य अयस्क है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. The value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>216</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></msup><mo>+</mo><mfrac><mn>1</mn><msup><mn>256</mn><mstyle displaystyle=\"true\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>4</mn></mfrac></mstyle></msup></mfrac><mo>+</mo><msup><mn>32</mn><mfrac><mn>1</mn><mn>5</mn></mfrac></msup><mo>-</mo><mfrac><mn>1</mn><msup><mn>10</mn><mrow><mo>-</mo><mn>2</mn></mrow></msup></mfrac><mo>-</mo><mn>2</mn></math>&nbsp;is______.&nbsp;</p>",
                    question_hi: "<p>38.&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>216</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></msup><mo>+</mo><mfrac><mn>1</mn><msup><mn>256</mn><mstyle displaystyle=\"true\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>4</mn></mfrac></mstyle></msup></mfrac><mo>+</mo><msup><mn>32</mn><mfrac><mn>1</mn><mn>5</mn></mfrac></msup><mo>-</mo><mfrac><mn>1</mn><msup><mn>10</mn><mrow><mo>-</mo><mn>2</mn></mrow></msup></mfrac><mo>-</mo><mn>2</mn></math>का मान क्या होगा?</p>",
                    options_en: ["<p>-1</p>", "<p>-2</p>", 
                                "<p>0</p>", "<p>1</p>"],
                    options_hi: ["<p>&ndash;1</p>", "<p>&ndash;2</p>",
                                "<p>0</p>", "<p>1</p>"],
                    solution_en: "<p>38.(c)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>216</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></msup><mo>+</mo><mfrac><mn>1</mn><msup><mn>256</mn><mstyle displaystyle=\"true\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>4</mn></mfrac></mstyle></msup></mfrac><mo>+</mo><msup><mn>32</mn><mfrac><mn>1</mn><mn>5</mn></mfrac></msup><mo>-</mo><mfrac><mn>1</mn><msup><mn>10</mn><mrow><mo>-</mo><mn>2</mn></mrow></msup></mfrac><mo>-</mo><mn>2</mn></math><br>= <math display=\"inline\"><mroot><mrow><msup><mrow><mn>216</mn></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>3</mn></mrow></mroot><mo>+</mo><mroot><mrow><msup><mrow><mn>256</mn></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mn>4</mn></mrow></mroot><mo>+</mo><mroot><mrow><msup><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></msup></mrow><mrow><mn>5</mn></mrow></mroot><mo>-</mo><msup><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>2</mn></math><br>= 36 + 64 + 2 - 100 - 2 = 0</p>",
                    solution_hi: "<p>38.(c)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>216</mn><mfrac><mn>2</mn><mn>3</mn></mfrac></msup><mo>+</mo><mfrac><mn>1</mn><msup><mn>256</mn><mstyle displaystyle=\"true\"><mfrac><mrow><mo>-</mo><mn>3</mn></mrow><mn>4</mn></mfrac></mstyle></msup></mfrac><mo>+</mo><msup><mn>32</mn><mfrac><mn>1</mn><mn>5</mn></mfrac></msup><mo>-</mo><mfrac><mn>1</mn><msup><mn>10</mn><mrow><mo>-</mo><mn>2</mn></mrow></msup></mfrac><mo>-</mo><mn>2</mn></math><br>= <math display=\"inline\"><mroot><mrow><msup><mrow><mn>216</mn></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>3</mn></mrow></mroot><mo>+</mo><mroot><mrow><msup><mrow><mn>256</mn></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><mn>4</mn></mrow></mroot><mo>+</mo><mroot><mrow><msup><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></msup></mrow><mrow><mn>5</mn></mrow></mroot><mo>-</mo><msup><mrow><mn>10</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>2</mn></math><br>= 36 + 64 + 2 - 100 - 2 = 0</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39. <strong>Question:</strong></p>\n<p dir=\"ltr\">When did Mr. Y purchase his car?</p>\n<p dir=\"ltr\"><strong>Statements:</strong></p>\n<p dir=\"ltr\">1. Certainly before 18th August but not before 15th August.&nbsp;</p>\n<p dir=\"ltr\">2. Certainly after 16th August but not later than 19s August.</p>\n<p>&nbsp;</p>",
                    question_hi: "<p>39. <strong>प्रश्न: </strong>श्री Y ने अपनी कार कब खरीदी ?<br><strong>कथन:</strong><br>1. निश्चित रूप से 18 अगस्त से पहले परंतु 15 अगस्त से पहले नहीं।<br>2. निश्चित रूप से 16 अगस्त के बाद परंतु 19 अगस्त के बाद नहीं।</p>",
                    options_en: ["<p>Both statement 1 and 2 are sufficient</p>", "<p>Either statement 1 or 2 is sufficient</p>", 
                                "<p>Statement 2 alone is sufficient while statement 1 alone is insufficient</p>", "<p>Statement 1 alone is sufficient while&nbsp; statement 2 alone is insufficient</p>"],
                    options_hi: ["<p>दोनों कथन 1 और 2 पर्याप्त है</p>", "<p>या तो कथन 1 पर्याप्त है या 2</p>",
                                "<p>अकेला कथन 2 पर्याप्त है जबकि अकेला 1 कथन अपर्याप्त है</p>", "<p>अकेला कथन 1 पर्याप्त है जबकि अकेला कथन 2 अपर्याप्त है</p>"],
                    solution_en: "<p>39.(a) From statement 1 we get the idea that he purchased before 18 august but not before 15 august, so the date may be 16 or 17. We cannot tell on which date he bought the car.<br>From statement 2 we get the idea that he bought after 16 august but not later than 19 august, so the date may be 17 or 18 .Still we cannot tell on which date he bought the car.<br>After combining statement 1 and 2 we can tell that in both the cases one common date is 17 august. So we can tell that he bought car on 17 august .Both statements are sufficient to answer the question.</p>",
                    solution_hi: "<p>39.(a) कथन 1 से हमें पता चलता है कि उसने 18 अगस्त से पहले खरीदा था लेकिन 15 अगस्त से पहले नहीं, इसलिए तारीख 16 या 17 हो सकती है। हम यह नहीं बता सकते कि उसने कार किस तारीख को खरीदा था।<br>कथन 2 से हमें यह पता चलता है कि उसने 16 अगस्त के बाद खरीदा लेकिन 19 अगस्त के बाद नहीं, इसलिए तारीख 17 या 18 हो सकती है। फिर भी हम यह नहीं बता सकते कि उसने कार किस तारीख को खरीदा था।<br>कथन 1 और 2 को मिलाकर हम कह सकते हैं कि&nbsp;दोनों स्थितियों में एक सामान्य तिथि 17 अगस्त है। तो हम बता सकते हैं कि उन्होंने 17 अगस्त को एक कार खरीदा था। अत: दोनों कथन प्रश्न का उत्तर देने के लिए पर्याप्त हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. An element has an atomic weight of W and an atomic number of N. The number of protons in the nucleus of its atom is:</p>",
                    question_hi: "<p>40. एक तत्व का परमाणु भार W और परमाणु संख्या N है। इस परमाणु के केंद्र में प्रोटॉन की संख्या कितनी होगी?</p>",
                    options_en: ["<p>W-N</p>", "<p>W</p>", 
                                "<p>N</p>", "<p>W+N</p>"],
                    options_hi: ["<p>W-N</p>", "<p>W</p>",
                                "<p>N</p>", "<p>W+N</p>"],
                    solution_en: "<p>40.(c) <strong>N. Atomic weight - </strong>The total weight of an atom which is equal to the number of protons and neutrons. <strong>Atomic Number - </strong>The number of protons in the nucleus of an atom. Isobars - Nuclei with the same number of protons but a different number of neutrons. <strong>Isotopes -</strong> Nuclei with the same number of neutrons but a different atomic number.</p>",
                    solution_hi: "<p>40.(c) <strong>N । परमाणु भार -</strong> परमाणु का कुल भार जो प्रोटॉन और न्यूट्रॉन की संख्या के बराबर होता है।<strong> परमाणु संख्या</strong> - परमाणु के नाभिक में प्रोटॉन की संख्या। आइसोबार - प्रोटॉन की समान संख्या लेकिन न्यूट्रॉन की भिन्न संख्या वाले नाभिक। <strong>आइसोटोप</strong> - न्यूट्रॉन की समान संख्या लेकिन भिन्न परमाणु क्रमांक वाले नाभिक।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. If 3cos&sup2;&theta; + 1 = 4sin&theta;, 0&deg; &lt; &theta; &lt; 90&deg;, then the value of sec&sup2;&theta; is :</p>",
                    question_hi: "<p>41. यदि 3cos&sup2;&theta; + 1 = 4sin&theta;, 0&deg; &lt; &theta; &lt; 90&deg;, तो sec&sup2;&theta; का मान है:</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math></p>"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>9</mn><mn>5</mn></mfrac></mstyle></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>41. 3cos&sup2;&theta;&nbsp;+ 1 = 4sin&theta;<br>3(1 - sin&sup2;&theta;) + 1 = 4sin&theta;<br>3 - 3sin&sup2;&theta; + 1 = 4sin&theta;<br>3sin&sup2;&theta; + 4sin&theta; - 4 = 0<br>3sin&sup2;&theta; + 6sin&theta; - 2sin&theta; - 4 = 0<br>3sin&theta; (sin&theta; + 2) - 2(sin&theta; + 2) = 0<br>(3sin&theta; - 2) = 0</p>\n<p>&rArr; sin&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>P</mi><mi>H</mi></mfrac></math></p>\n<p>B =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>3</mn><mn>2</mn></msup><mo>-</mo><msup><mn>2</mn><mn>2</mn></msup></msqrt><mo>=</mo><msqrt><mn>5</mn></msqrt></math></p>\n<p>sec&sup2;&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mi>H</mi><mi>B</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><msup><mrow><mo>(</mo><mfrac><mn>3</mn><msqrt><mn>5</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mfrac><mrow><mn>9</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>41.(a) 3cos&sup2;&theta;&nbsp;+ 1 = 4sin&theta;<br>3(1 - sin&sup2;&theta;) + 1 = 4sin&theta;<br>3 - 3sin&sup2;&theta; + 1 = 4sin&theta;<br>3sin&sup2;&theta; + 4sin&theta; - 4 = 0<br>3sin&sup2;&theta; + 6sin&theta; - 2sin&theta; - 4 = 0<br>3sin&theta; (sin&theta; + 2) - 2(sin&theta; + 2) = 0<br>(3sin&theta; - 2) = 0</p>\n<p>sin&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>=</mo><mfrac><mi>P</mi><mi>H</mi></mfrac></math></p>\n<p>B =&nbsp;<math display=\"inline\"><msqrt><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math></p>\n<p>sec&sup2;&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mi>H</mi><mi>B</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><msup><mrow><mo>(</mo><mfrac><mn>3</mn><msqrt><mn>5</mn></msqrt></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>=</mo><mfrac><mn>9</mn><mn>5</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. Who among the following won the Padma award for the year 2023 for his efforts of reviving the traditional art of making Etikoppaka toys by adoption of eco-friendly technologies?</p>",
                    question_hi: "<p>42. निम्नलिखित में से किसने पर्यावरण के अनुकूल तकनीकों को अपनाकर ऐटीकोप्पका खिलौने बनाने की पारंपरिक कला को पुनर्जीवित करने के अपने प्रयासों के लिए वर्ष 2023 के लिए पद्म पुरस्कार जीता ?</p>",
                    options_en: ["<p>Victor Banerjee</p>", "<p>Devendra Jhajharia</p>", 
                                "<p>Radheyshyam Khemka</p>", "<p>CV Raju</p>"],
                    options_hi: ["<p>विक्टर बनर्जी</p>", "<p>देवेंद्र झाझरिया</p>",
                                "<p>राधेश्याम खेमका</p>", "<p>सी. वी. राजू</p>"],
                    solution_en: "<p>42.(d)<strong> CV Raju </strong>(Padma Shri awardee in 2023). Etikoppaka toys (lacquer toys) are made out of wood and are coloured with natural dyes derived from seeds, lacquer, bark, roots and leaves. It received the Geographical Indication (GI) Tag in 2017. <strong>Victor Banerjee</strong> (Padma Bhushan awardee in 2022) <strong>Devendra Jhajharia </strong>- An Indian Paralympic javelin thrower competing in F46 events. <strong>Radheyshyam Khemka</strong> (Padma Vibhushan posthumously awarded in 2022).</p>",
                    solution_hi: "<p>42.(d) <strong>सी.वी. राजू </strong>(2023 में पद्म श्री पुरस्कार विजेता)। ऐटीकोप्पका खिलौने (लाह के खिलौने) लकड़ी से बने होते हैं और बीज, लाह, छाल, जड़ों और पत्तियों से प्राप्त प्राकृतिक रंगों से रंगे जाते हैं। इसे 2017 में भौगोलिक संकेत (GI) टैग प्राप्त हुआ। <strong>विक्टर बनर्जी</strong> (2022 में पद्म भूषण पुरस्कार विजेता)।<strong> देवेन्द्र झाझरिया </strong>- वह एक भारतीय पैरालंपिक भाला फेंक खिलाड़ी हैं जो F46 स्पर्धाओं में प्रतिस्पर्धा करते हैं। <strong>राधेश्याम खेमका</strong> (2022 में मरणोपरांत पद्म विभूषण से सम्मानित)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. A cryogenic engine makes use of which of the following as its fuel?</p>",
                    question_hi: "<p>43. क्रायोजेनिक इंजन अपने ईंधन के रूप में निम्नलिखित में से किसका उपयोग करता है?</p>",
                    options_en: ["<p>Liquid oxygen and liquid hydrogen</p>", "<p>Oxygen and hydrogen</p>", 
                                "<p>Liquid hydrogen</p>", "<p>Liquid oxygen</p>"],
                    options_hi: ["<p>द्रवित ऑक्सीजन और द्रवित हाइड्रोजन</p>", "<p>ऑक्सीजन और हाइड्रोजन</p>",
                                "<p>द्रवित हाइड्रोजन</p>", "<p>द्रवित ऑक्सीजन</p>"],
                    solution_en: "<p>43.(a)<strong> Liquid oxygen and liquid hydrogen.</strong> A cryogenic engine/cryogenic stage is the last stage of space launch vehicles which makes use of cryogenics to store its fuel and oxidiser as liquids instead of gas. Cryogenics is the study of materials at low temperature. <strong>First Cryogenic Rocket Engine: RL-10</strong> (in the World), developed by the Glenn Research Center (United States) in 1963; <strong>CE-20 </strong>(in India) developed by the Liquid Propulsion Systems Centre (First flight - 5 June 2017).</p>",
                    solution_hi: "<p>43.(a) <strong>द्रवित ऑक्सीजन और द्रवित हाइड्रोजन। </strong>क्रायोजेनिक इंजन/क्रायोजेनिक चरण अंतरिक्ष प्रक्षेपण वाहनों का अंतिम चरण है जो गैस के बजाय अपने ईंधन और ऑक्सीडाइज़र को तरल के रूप में संग्रहीत करने के लिए क्रायोजेनिक का उपयोग करता है। क्रायोजेनिक्स कम तापमान पर सामग्रियों का अध्ययन है। <strong>प्रथम क्रायोजेनिक रॉकेट इंजन: RL-10</strong> (विश्व में), 1963 में ग्लेन रिसर्च सेंटर (संयुक्त राज्य अमेरिका) द्वारा विकसित; CE-20 (भारत में) लिक्विड प्रोपल्शन सिस्टम सेंटर द्वारा विकसित (प्रथम उड़ान - 5 जून 2017)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. Read the following logic and answer the given question.<br>A @ B means A is the husband of B.<br>A # B means A is the wife of B.<br>A $ B means A is the son of B.<br>A% B means A is the daughter of B.<br>In the equation X% Y @Z% W, how is the relation to the father of X related to W?</p>",
                    question_hi: "<p>44. निम्नलिखित तर्क को पढ़िए और दिए गए प्रश्न का उत्तर दीजिए।<br>A @ B का अर्थ है A, B का पति है।<br>A # B का अर्थ है A, B की पत्नी है।<br>A $ B का अर्थ है A, B का पुत्र है।<br>A % B का अर्थ है A, B की पुत्री है।<br>समीकरण X% Y @Z% W, में, X के पिता का W से क्या संबंध है?</p>",
                    options_en: ["<p>Son-in-law</p>", "<p>uncle</p>", 
                                "<p>Father</p>", "<p>Nephew</p>"],
                    options_hi: ["<p>दामाद</p>", "<p>चाचा / मामा</p>",
                                "<p>पिता</p>", "<p>भतीजा</p>"],
                    solution_en: "<p>44.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254549.png\" alt=\"rId21\" width=\"131\" height=\"141\"><br>From the above diagram it is clearly seen that X&rsquo;s father Y is the son - in - law of <strong>W</strong>.</p>",
                    solution_hi: "<p>44.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254549.png\" alt=\"rId21\" width=\"131\" height=\"141\"><br>उपरोक्त आरेख से यह स्पष्ट रूप से देखा जाता है कि X का पिता Y, <strong>W</strong> का दामाद है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. From the top of a platform, the angle of elevation of a tower was 30&deg;. The tower was 45 m high and the horizontal distance between the platform and the tower was 40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>&nbsp;m. What was the height of the platform? (in meters)</p>",
                    question_hi: "<p>45. किसी प्लेटफार्म के ऊपर से, एक टावर का उन्नयन कोण 30&deg; था। टावर 45 मीटर ऊंचा था और प्लेटफार्म तथा टावर के बीच क्षैतिज दूरी 40<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> मीटर थी। प्लेटफॉर्म की ऊंचाई कितनी थी?</p>",
                    options_en: ["<p>45<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>40</p>", 
                                "<p>5</p>", "<p>20<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p>45<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>40</p>",
                                "<p>5</p>", "<p>20<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>45.(c) Let AB and CE be the height of platform and tower respectively<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254653.png\" alt=\"rId22\" width=\"164\" height=\"141\"><br>In <math display=\"inline\"><mo>&#9651;</mo></math> BDE, we have <br>tan30&deg; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>E</mi><mi>D</mi></mrow><mrow><mi>B</mi><mi>D</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>E</mi><mi>D</mi></mrow><mrow><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></mstyle></math> &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mi>E</mi><mi>D</mi></mrow><mrow><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> ED = 40 m , DC = 45 - 40 = 5 m<br>So,the height of platform = 5m</p>",
                    solution_hi: "<p>45.(c) माना AB और CE क्रमशः प्लेटफॉर्म और टावर की ऊंचाई हैं | <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254653.png\" alt=\"rId22\" width=\"164\" height=\"141\"><br><math display=\"inline\"><mo>&#9651;</mo></math>BDE में, <br><math display=\"inline\"><mo>&#8658;</mo></math>tan30&deg; =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>E</mi><mi>D</mi></mrow><mrow><mi>B</mi><mi>D</mi></mrow></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>E</mi><mi>D</mi></mrow><mrow><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></mstyle></math>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mi>E</mi><mi>D</mi></mrow><mrow><mn>40</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math>ED = 40 m &rArr; DC = 45 - 40 = 5 m<br>अत: प्लेटफार्म की ऊँचाई = 5m</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. Which compound is formed when a Magnesium ribbon is burnt in air?</p>",
                    question_hi: "<p>46. जब मैग्नेशियम रिबन हवा में जलाया जाता है तो कौन सा यौगिक उत्पन्न होता है ?</p>",
                    options_en: ["<p>Magnesium Nitride</p>", "<p>Magnesium Oxide</p>", 
                                "<p>Magnesium Nitrate</p>", "<p>Magnesium Carbonate</p>"],
                    options_hi: ["<p>मैग्नीशियम नाइट्राइड</p>", "<p>मैग्नीशियम ऑक्साइड</p>",
                                "<p>मैग्नीशियम नाइट्रेट</p>", "<p>मैग्नीशियम कार्बोनेट</p>"],
                    solution_en: "<p>46.(b)<strong> Magnesium Oxide.</strong> When magnesium ribbon is burnt in air, magnesium (Mg) reacts with the oxygen (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub></math>) present in the air to form magnesium oxide (MgO). The reaction is -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>Mg</mi><mo>+</mo><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub><mo>&#8594;</mo><mn>2</mn><mi>MgO</mi></math>. <strong>Magnesium Nitride</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Mg</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub></math>) powder is a well-known solid catalyst and is used for fabricating special alloys and ceramics. Magnesium Nitrate&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Mg</mi><msub><mrow><mo>(</mo><msub><mi>NO</mi><mn>3</mn></msub><mo>)</mo></mrow><mn>2</mn></msub><mo>.</mo></math> Magnesium carbonate (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>MgCO</mi><mn>3</mn></msub></math>).</p>",
                    solution_hi: "<p>46.(b) <strong>मैग्नीशियम ऑक्साइड। </strong>जब मैग्नीशियम रिबन को हवा में जलाया जाता है, तो मैग्नीशियम (Mg) हवा में मौजूद ऑक्सीजन (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub></math>) के साथ अभिक्रिया करके मैग्नीशियम ऑक्साइड (MgO) बनाता है। अभिक्रिया है -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>Mg</mi><mo>+</mo><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub><mo>&#8594;</mo><mn>2</mn><mi>MgO</mi></math>. <strong>मैग्नीशियम नाइट्राइड</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>Mg</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">N</mi><mn>2</mn></msub></math>) पाउडर एक प्रसिद्ध ठोस उत्प्रेरक है और इसका उपयोग विशेष मिश्र धातुओं और सिरेमिक (चीनी मिट्टी की वस्तु) के निर्माण के लिए किया जाता है। मैग्नीशियम नाइट्रेट <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Mg</mi><msub><mrow><mo>(</mo><msub><mi>NO</mi><mn>3</mn></msub><mo>)</mo></mrow><mn>2</mn></msub></math> मैग्नीशियम कार्बोनेट<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msub><mi>MgCO</mi><mn>3</mn></msub><mo>)</mo></math>।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. The total surface area of a cylinder of diameter 10 cm is 330 square centimeters. Find the height (cm) of the cylinder?</p>",
                    question_hi: "<p>47. 10 सेमी व्यास वाले एक बेलन का कुल पृष्ठीय क्षेत्रफल 330 वर्ग सेंटीमीटर है। इसकी ऊँचाई ज्ञात कीजिये ।</p>",
                    options_en: ["<p>10.5</p>", "<p>2.5</p>", 
                                "<p>5.5</p>", "<p>6.5</p>"],
                    options_hi: ["<p>10.5</p>", "<p>2.5</p>",
                                "<p>5.5</p>", "<p>6.5</p>"],
                    solution_en: "<p>47.(c)<br>Diameter = 10 cm , radius = 5 cm<br>Total surface area of cylinder&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>&#960;R</mi><mo>(</mo><mi mathvariant=\"normal\">R</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi><mo>)</mo></math></p>\n<p>= 2&pi; &times; 5 &times; (5 + h) = 330<br><math display=\"inline\"><mo>&#8658;</mo></math>10 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; (5 + h) = 330<br><math display=\"inline\"><mo>&#8658;</mo></math>5 + h =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>330</mn><mo>&#215;</mo><mn>7</mn></mrow><mn>220</mn></mfrac></math>&rArr; 5 + h = 10.5</p>\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi mathvariant=\"normal\">h</mi><mo>=</mo><mn>10</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>5</mn><mo>=</mo><mn>5</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cm</mi></math></p>",
                    solution_hi: "<p>47.(c) व्यास = 10 cm , त्रिज्या = 5 cm<br>बेलन का कुल पृष्ठीय क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi>&#960;R</mi><mo>(</mo><mi mathvariant=\"normal\">R</mi><mo>+</mo><mi mathvariant=\"normal\">h</mi><mo>)</mo></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mi mathvariant=\"normal\">&#960;</mi><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><mo>(</mo><mn>5</mn><mo>+</mo><mi mathvariant=\"normal\">h</mi><mo>)</mo><mo>=</mo><mn>330</mn></math><br><math display=\"inline\"><mo>&#8658;</mo></math>10 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; (5 + h) = 330<br><math display=\"inline\"><mo>&#8658;</mo></math>5 + h =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>330</mn><mo>&#215;</mo><mn>7</mn></mrow><mn>220</mn></mfrac></math> &rArr; 5 + h = 10.5<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi mathvariant=\"normal\">h</mi><mo>=</mo><mn>10</mn><mo>.</mo><mn>5</mn><mo>-</mo><mn>5</mn><mo>=</mo><mn>5</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cm</mi></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. What is the name of the scheme launched in 2022 for the welfare measures of the transgeneder community and for persons who are engaged in the act of begging?</p>",
                    question_hi: "<p>48. ट्रांसजेंडर समुदाय और भीख मांगने के कार्य में लगे व्यक्तियों के कल्याण के उपायों के लिए, वर्ष 2022 में शुरू की गई योजना का नाम क्या है?</p>",
                    options_en: ["<p>SMILE</p>", "<p>SHINE</p>", 
                                "<p>SHRI</p>", "<p>RISE</p>"],
                    options_hi: ["<p>स्माइल (SMILE)</p>", "<p>शाइन (SHINE)</p>",
                                "<p>श्री (SHRI)</p>", "<p>राइज़ ( RISE)</p>"],
                    solution_en: "<p>48.(a)<strong> SMILE</strong> (Support for Marginalized Individuals for livelihood and Enterprise). Launched by Virendra Singh (union minister of Social justice and Empowerment) in February 2022. The Ministry has allocated Rs. 365 Crore for the scheme from 2021-22 to 2025-26. Components: Scholarships for Transgender Students, Skill Development and Livelihood, Composite Medical Health. Other Schemes for Transgenders: Sweekruti Scheme (Odisha), Shelter Homes for Transgender Persons (Gujrat).</p>",
                    solution_hi: "<p>48.(a) <strong>स्माइल</strong> (Support for Marginalized Individuals for livelihood and Enterprise). फरवरी 2022 में वीरेंद्र सिंह (केंद्रीय सामाजिक न्याय और अधिकारिता मंत्री) द्वारा लॉन्च किया गया। मंत्रालय ने 2021-22 से 2025-26 तक इस योजना के लिए 365 करोड़ रुपये आवंटित किए हैं। घटक : किन्नर (Transgenders छात्रों के लिए छात्रवृत्ति, कौशल विकास और आजीविका, समग्र चिकित्सा स्वास्थ्य। किन्नरों (Transgenders) के लिए अन्य योजनाएं : सप्ताह रुति योजना (ओडिशा), ट्रांसजेंडर व्यक्तियों के लिए आश्रय स्थल (गुजरात)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. In the given figure, AB || QP, AB = x , PQ = x + 10, RB = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> , BP = x + 1. Find PQ<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254755.png\" alt=\"rId23\" width=\"330\" height=\"141\"></p>",
                    question_hi: "<p>49. दी गई आकृति में , AB || QP , AB = x , PQ = x + 10, RB = <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></math> , BP = x + 1 PQ का मान ज्ञात कीजिए |<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254755.png\" alt=\"rId23\" width=\"330\" height=\"141\"></p>",
                    options_en: ["<p>13 units</p>", "<p>18 units</p>", 
                                "<p>12 units</p>", "<p>14 units</p>"],
                    options_hi: ["<p>13 इकाई</p>", "<p>18 इकाई</p>",
                                "<p>12 इकाई</p>", "<p>14 इकाई</p>"],
                    solution_en: "<p>49.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254755.png\" alt=\"rId23\" width=\"330\" height=\"141\"><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mi>x</mi><mn>2</mn></mfrac><mrow><mfrac><mi>x</mi><mn>2</mn></mfrac><mo>+</mo><mi>&#160;</mi><mi>x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>x</mi><mrow><mi>x</mi><mo>+</mo><mn>10</mn></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mn>3</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>x</mi><mrow><mi>x</mi><mo>+</mo><mn>10</mn></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mi>x</mi><mo>+</mo><mn>10</mn><mo>=</mo><mn>3</mn><mi>x</mi><mo>+</mo><mn>2</mn><mo>&#8658;</mo></math> x=4<br>PQ = 4 + 10 = 4 + 10 = 14 units.</p>",
                    solution_hi: "<p>49.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254755.png\" alt=\"rId23\" width=\"330\" height=\"141\"><br><math display=\"inline\"><mfrac><mrow><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac></mrow><mrow><mfrac><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></mfrac><mo>+</mo><mi>&#160;</mi><mi>x</mi><mo>+</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>x</mi><mrow><mi>x</mi><mo>+</mo><mn>10</mn></mrow></mfrac></mstyle></math></p>\n<p>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>x</mi><mrow><mn>3</mn><mi>x</mi><mo>+</mo><mn>2</mn></mrow></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>x</mi><mrow><mi>x</mi><mo>+</mo><mn>10</mn></mrow></mfrac></mstyle></math><br>= <math display=\"inline\"><mi>x</mi><mo>+</mo><mn>10</mn><mo>=</mo><mn>3</mn><mi>x</mi><mo>+</mo><mn>2</mn><mo>&#8658;</mo><mi>x</mi><mo>=</mo><mn>4</mn></math><br>PQ = 4 + 10 = 4 + 10 = 14 इकाई</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. In the following Venn diagram which letters represents all those who play Cricket as well as Kabaddi and also all the three games?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254862.png\" alt=\"rId24\" width=\"168\" height=\"166\"></p>",
                    question_hi: "<p>50. निम्नलिखित वेन आरेख में कौन-सा अक्षर उन सभी लोगों को दर्शाता है जो क्रिकेट के साथ कबड्डी खेलते हैं और वो जो तीनों खेल भी खेलते हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679254963.png\" alt=\"rId25\" width=\"168\" height=\"166\"> <br>Kabaddi - कबड्डी<br>Football - फुटबॉल<br>Cricket - क्रिकेट</p>",
                    options_en: ["<p>S + Q</p>", "<p>S + T</p>", 
                                "<p>S + R</p>", "<p>S + V</p>"],
                    options_hi: ["<p>S + Q</p>", "<p>S + T</p>",
                                "<p>S + R</p>", "<p>S + V</p>"],
                    solution_en: "<p>50.(b) <strong>S+T</strong> <br>those who play Cricket as well as Kabaddi and also all the three games are S+T.</p>",
                    solution_hi: "<p>50.(b) <strong>S+T</strong><br>जो क्रिकेट के साथ-साथ कबड्डी और तीनों खेल भी खेलते हैं, वे S+T हैं</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. Which one of the following is a cation?</p>",
                    question_hi: "<p>51. निम्नलिखित में से कौन सा एक धनायन है?</p>",
                    options_en: ["<p>Hydroxide</p>", "<p>Nitrate</p>", 
                                "<p>Ammonium</p>", "<p>Carbonate</p>"],
                    options_hi: ["<p>हाइड्रोक्साइड</p>", "<p>नाइट्रेट</p>",
                                "<p>अमोनियम</p>", "<p>कार्बोनेट</p>"],
                    solution_en: "<p>51.(c) <strong>Ammonium. Cation</strong> is an ion (charged particles) that has a positive charge on it whereas an anion has a negative charge on it.<strong> Ammonium </strong>ion (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>NH</mi><mn>4</mn></msub><mo>+</mo></msup></math>) - The cation has a charge of +1. <strong>Carbonate ion</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>CO</mi><mn>3</mn></msub><mrow><mn>2</mn><mo>-</mo></mrow></msup></math>) - The anion has a charge of -2.&nbsp;<strong>Nitrate ion</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">N</mi><msup><msub><mi mathvariant=\"normal\">O</mi><mn>3</mn></msub><mo>-</mo></msup></math>) - The anion has a charge of -1.&nbsp;<strong>Hydroxide ion</strong> (OH- ) - The anion has a charge of -1.</p>",
                    solution_hi: "<p>51.(c) <strong>अमोनियम। धनायन</strong> एक आयन (आवेशित कण) है जिस पर धनात्मक आवेश होता है जबकि ऋणायन पर ऋणात्मक आवेश होता है। <strong>अमोनियम आयन </strong>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>NH</mi><mn>4</mn></msub><mo>+</mo></msup></math>) - धनायन का आवेश +1 होता है। <strong>कार्बोनेट आयन</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>CO</mi><mn>3</mn></msub><mrow><mn>2</mn><mo>-</mo></mrow></msup></math>) - ऋणायन का आवेश -2 होता है। <strong>नाइट्रेट आयन</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>NO</mi><mn>3</mn></msub><mo>-</mo></msup></math>) - ऋणायन का आवेश -1 होता है। <strong>हाइड्रॉक्साइड आयन</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>OH</mi><mo>-</mo></msup></math>) - ऋणायन का आवेश -1 होता है</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. A bag contains Rs 3,000, the notes of which are in the denomination of Rs 50 , Rs 100 and Rs10. If the notes of Rs 100, Rs 50 and Rs 10 are in the ratio of 5 : 8 : 10 , then the numbers of the notes of Rs 50 in the bag will be ?</p>",
                    question_hi: "<p>52. एक बैग में 3,000 रुपये हैं, जिसके नोट 50 रुपये, 100 रुपये और 10 रुपये के मूल्यवर्ग में हैं। यदि 100 रुपये, 50 रुपये और 10 रुपये के नोटों का अनुपात 5 : 8 : 10 है, तो बैग में 50 रुपये के नोटों की संख्या क्या होगी ?</p>",
                    options_en: ["<p>16</p>", "<p>32</p>", 
                                "<p>24</p>", "<p>8</p>"],
                    options_hi: ["<p>16</p>", "<p>32</p>",
                                "<p>24</p>", "<p>8</p>"],
                    solution_en: "<p>52.(c) The notes of Rs 100, Rs. 50 and Rs. 10 = 5 : 8 : 10<br>Rs 50 in the bag = 3000 &times; <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 24</p>",
                    solution_hi: "<p>52.(c) 100 रुपये, 50 रुपये और 10 रुपये के नोट = 5 : 8 : 10<br>बैग में 50 रुपये = 3000 &times; <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> = 24</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. What is the suffix in the name Propanoic Acid?</p>",
                    question_hi: "<p>53. प्रोपेनॉइक (Propanoic ) अम्ल के नाम में कौन सा प्रत्यय होता है?</p>",
                    options_en: ["<p>oic acid</p>", "<p>Propane</p>", 
                                "<p>ane</p>", "<p>Prop</p>"],
                    options_hi: ["<p>ऑईक अम्ल (oic acid)</p>", "<p>प्रोपेन (Propane)</p>",
                                "<p>ऐन (ane)</p>", "<p>प्रोप (Prop)</p>"],
                    solution_en: "<p>53.(a) <strong>oic acid. </strong>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>CH</mi><mn>3</mn></msub><msub><mi>CH</mi><mn>2</mn></msub><mi>COOH</mi></math>): A naturally occurring carboxylic acid. <strong>Uses</strong> - antifungal agent in food. It is present naturally at low levels in dairy products; (Crystal structure:- Monoclinic). <strong>Propane </strong>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>8</mn></msub></math>) an alkane gas . It is stored under pressure inside a tank as a colorless, odorless liquid.&nbsp;<strong>Uses - </strong>home heating, gas fireplaces and clothes drying.</p>",
                    solution_hi: "<p>53.(a) <strong>ओईक अम्ल (oic acid)।</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"bold\">CH</mi><mn mathvariant=\"bold\">3</mn></msub><msub><mi mathvariant=\"bold\">CH</mi><mn mathvariant=\"bold\">2</mn></msub><mi mathvariant=\"bold\">COOH</mi></math>): प्राकृतिक रूप से पाया जाने वाला कार्बोक्जिलिक अम्ल है। <strong>उपयोग -</strong> भोजन में कवकरोधी कारक के रूप में । यह डेयरी उत्पादों में स्वाभाविक रूप से निम्न स्तर पर मौजूद होता है; (क्रिस्टल संरचना:- मोनोक्लिनिक)। <strong>प्रोपेन</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>8</mn></msub></math>) एल्केन गैस है जो रंगहीन, गंधहीन तरल के रूप में एक टैंक के अंदर दबाव में एकत्र होती है।&nbsp;<strong>उपयोग </strong>- घरो को गर्म करने वाली गैस, गैस चिमनियाँ और कपड़े सुखाने में ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. L, M, N, O, P, Q, R and S are playing a game standing in a circle facing outwards. N is neither the neighbour of L nor of R. O is the neighbour of L but not of S. P is the neighbour of S and is third to the right of Q . M is the neighbour of Q and fourth to the left of O. <br>Who among the following stands third to the left of O?</p>",
                    question_hi: "<p>54. L, M, N, O, P, Q, R और S एक घेरे में बाहर की ओर मुंह करके खड़े होकर एक खेल खेल रहे हैं। N, L और R का पड़ोसी नहीं है। O, L का पड़ोसी है, लेकिन S का नहीं। P, S का पड़ोसी है और Q के दाईं ओर तीसरे स्थान पर है। M, Q का पड़ोसी है और O के बाईं ओर चौथे स्थान पर है।<br>निम्नलिखित में से कौन सा O के बाएं से तीसरे स्थान पर खड़ा है?</p>",
                    options_en: ["<p>S</p>", "<p>P</p>", 
                                "<p>Q</p>", "<p>M</p>"],
                    options_hi: ["<p>S</p>", "<p>P</p>",
                                "<p>Q</p>", "<p>M</p>"],
                    solution_en: "<p>54.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679255076.png\" alt=\"rId26\" width=\"224\" height=\"170\"><br>From the above diagram it is clearly seen that S stands third to the left of<strong> </strong>O.</p>",
                    solution_hi: "<p>54.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679255076.png\" alt=\"rId26\" width=\"224\" height=\"170\"><br>उपरोक्त आरेख से यह स्पष्ट रूप से देखा जा सकता है कि S, O के बायें से तीसरे स्थान पर खड़ा है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. Maddy reads three - fifth of a chapter consisting of 75 pages. How&nbsp;many more pages does he need to read to complete the chapter ?</p>",
                    question_hi: "<p>55. मैडी 75 पृष्ठों वाले एक अध्याय का तीन-पांचवां भाग पढ़ता है। अध्याय को पूरा करने के लिए उसे और कितने पृष्ठ पढ़ने होंगे ?</p>",
                    options_en: ["<p>15</p>", "<p>25</p>", 
                                "<p>20</p>", "<p>30</p>"],
                    options_hi: ["<p>15</p>", "<p>25</p>",
                                "<p>20</p>", "<p>30</p>"],
                    solution_en: "<p>55.(d)<br>Maddy reads = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 75= 45 pages <br>Remaining pages = 75 - 45 = 30</p>",
                    solution_hi: "<p>55.(d) मैडी पढ़ता है = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times;75= 45 पृष्ठ<br>शेष पृष्ठ = 75 <math display=\"inline\"><mo>-</mo></math> 45 = 30</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. Six letters M, N, O, P, Q and R are written on different faces of a dice. Two positions of this dice ace shown in the figure. Find the letter on the face opposite to a vowel.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679255181.png\" alt=\"rId27\" width=\"155\" height=\"75\"></p>",
                    question_hi: "<p>56. एक पासे के विभिन्न फलकों पर छह अक्षर M, N, O, P, Q और R लिखे गए हैं। चित्र में इस पासे की दो स्थितियाँ दिखाई गई हैं। स्वर के विपरीत फलक पर अक्षर ढूंढें.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679255181.png\" alt=\"rId27\" width=\"155\" height=\"75\"></p>",
                    options_en: ["<p>M</p>", "<p>N</p>", 
                                "<p>P</p>", "<p>R</p>"],
                    options_hi: ["<p>M</p>", "<p>N</p>",
                                "<p>P</p>", "<p>R</p>"],
                    solution_en: "<p>56.(d) From dice 1 and 2 <br>Opposite faces:- M &harr; N , R &harr; O , P &harr; Q<br>R letter on the face opposite to the vowel</p>",
                    solution_hi: "<p>56.(d) पासे 1 और 2 से<br>विपरीत फलक:- M &harr; N , R &harr; O , P &harr; Q<br>स्वर के विपरीत फलक पर R अक्षर है</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57.Which of the following is a surface phenomenon?</p>",
                    question_hi: "<p>57. निम्न में से कौन सी एक सतहीय घटना है?</p>",
                    options_en: ["<p>Freezing</p>", "<p>Melting</p>", 
                                "<p>Evaporation</p>", "<p>Boiling</p>"],
                    options_hi: ["<p>जमना</p>", "<p>पिघलना</p>",
                                "<p>वाष्पीकरण</p>", "<p>उबलना</p>"],
                    solution_en: "<p>57.(c) <strong>Evaporation</strong> - A process by which water is transformed from liquid to gaseous state. <strong>Surface phenomenon</strong> occur at liquid (highly mobile) and solid interphase boundaries; Examples - Surface tension, Adsorption, Heterogeneous Catalysis, Corrosion. <strong>Freezing </strong>- Phase transition where a liquid turns into a solid when its temperature is lowered below its freezing point. <strong>Melting -</strong> Change of a solid into a liquid when heat is applied. <strong>Boiling</strong> is the process by which a liquid turns into a vapor when it is heated to its boiling point.</p>",
                    solution_hi: "<p>57.(c) <strong>वाष्पीकरण</strong> - एक प्रक्रिया जिसके द्वारा पानी तरल से गैसीय अवस्था में परिवर्तित हो जाता है। <strong>सतही घटनाएँ</strong> तरल (अत्यधिक गतिशील) और ठोस अंतरावस्था सीमाओं पर घटित होती हैं; उदाहरण - सतह तनाव, सोखना, विषम उत्प्रेरण, संक्षारण। <strong>जमना (हिमीकरण) </strong>- संक्रमण चरण जहां एक तरल एक ठोस में बदल जाता है जब उसका तापमान हिमांक बिंदु से नीचे चला जाता है। <strong>पिघलना - </strong>गर्म करने पर ठोस का द्रव में बदलना। <strong>उबलना </strong>वह प्रक्रिया है जिसके द्वारा एक तरल अपने क्वथनांक तक गर्म होने पर वाष्प में बदल जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. A man starts from point \'O\' travels 20 km towards East to reach point \'A\', turns right and travels 10 km to reach point \'B\', turns right and travels 9 km to reach point \'C\', turns right and travels 5 km to reach point \'D\', turns left and travels 12 km to reach point \'E\' and then turns right and travels 6 km to reach point &lsquo;F&rsquo;. In which direction is the man facing now?</p>",
                    question_hi: "<p>58. एक व्यक्ति \'O\' बिंदु से शुरुआत करते हुए बिंदु \'A\' तक पूर्व की ओर 20 किलोमीटर की यात्रा करता है, वह फिर दाईं ओर मुड़ता है तथा बिंदु \'B\' तक पहुँचने के लिए 10 किलोमीटर की यात्रा करता है, तथा फिर दाईं ओर मुड़कर बिंदु \'C\' तक पहुँचने के लिए 9 किलोमीटर की यात्रा करता है और फिर दाईं ओर मुड़कर बिंदु \'D\' तक पहुँचने के लिए 5 किलोमीटर की यात्रा करता है। तत्पश्चात्, बाईं ओर मुड़कर 12 किलोमीटर की यात्रा करके \'बिंदु E\' तक पहुँचता है और फिर दाईं ओर मुड़कर बिंदु \'F\' तक पहुँचने के लिए 6 किलोमीटर की यात्रा करता है। अब उस व्यक्ति का मुख किस दिशा की ओर है ?</p>",
                    options_en: ["<p>East</p>", "<p>West</p>", 
                                "<p>North</p>", "<p>South</p>"],
                    options_hi: ["<p>पूर्व</p>", "<p>पश्चिम</p>",
                                "<p>उत्तर</p>", "<p>दक्षिण</p>"],
                    solution_en: "<p>58.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679255433.png\" alt=\"rId29\" width=\"213\" height=\"170\"><br>Now, the man is facing in the North direction.</p>",
                    solution_hi: "<p>58.(c) <br><strong id=\"docs-internal-guid-46d45426-7fff-85ec-5f93-51ae89ca008e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfzpuxObo6ThHARGLnYGFDeknwUZOJ4ZD0YHbOz6f65j_VG0UV1QuRDTNVmRsR-ppQ2iY0osXNG0-gicdoVcm3Ns9ujpSgxfL_TGCa4m8DL7kwmeBKfGph8f_GcaRw3T-kAwxVpiPvkh4AfaqAhzWFIZjpt?key=173xHP_0HTfmOuXxTPd7aQ\" width=\"223\" height=\"178\"></strong><br>अब उस व्यक्ति का मुख उत्तर दिशा की ओर है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. To do a certain work , A and B work on alternate days, with B beginning the work on the first day. A can finish the work alone in 48 days. If the work gets completed in 11<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>&nbsp;days, then B alone can finish 4 times the same work in :</p>",
                    question_hi: "<p>59. किसी निश्चित कार्य को करने के दौरान A और B एक - एक (एक के बाद एक) दिन कार्य करते हैं तथा पहले दिन कार्य की शुरुआत B करता है | A इस कार्य को अकेले 48 दिनों में कर सकता है | यदि कार्य 11<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिनों में समाप्त होता है, तो B अकेले 4 गुना कार्य कितने दिनों में करेगा ?</p>",
                    options_en: ["<p>24 days</p>", "<p>32 days</p>", 
                                "<p>27 days</p>", "<p>30 days</p>"],
                    options_hi: ["<p>24 दिन</p>", "<p>32 दिन</p>",
                                "<p>27 दिन</p>", "<p>30 दिन</p>"],
                    solution_en: "<p>59.(c) Let the efficiency of A = 1 unit<br>Total work = <math display=\"inline\"><mn>48</mn><mo>&#215;</mo><mn>1</mn></math>= 48 unit<br>From 11<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>days, A worked for 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math>days and B worked for 6 days &hellip;...(B started the work)<br>Work done by A in 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>days<br>= <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#160;</mi><mo>&#215;</mo></math> 1 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>16</mn><mn>3</mn></mfrac></mstyle></math> unit<br>Work done by B = 48 - <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>128</mn><mn>3</mn></mfrac></mstyle></math> unit<br>Efficiency of B = <math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>64</mn><mn>9</mn></mfrac></mstyle></math> unit<br>Time taken by B to finish 4 times of total work = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>48</mn></mrow><mrow><mfrac><mrow><mn>64</mn></mrow><mrow><mn>9</mn></mrow></mfrac></mrow></mfrac></math> = 27 days<br><strong>Alternate :-</strong><br>From 11<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> days, A worked for 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math>days and B worked for 6 days &hellip;...(B started the work)<br>B &times; 6 + A &times; 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 48A<br>6B = 48A - <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>A<br>18B = 128A = <math display=\"inline\"><mfrac><mrow><mi>B</mi></mrow><mrow><mi>A</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>64</mn><mn>9</mn></mfrac></mstyle></math><br>Total work = 9 &times; 48 = 432 units<br>Time taken by B to finish 4 times the same work = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>432</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math> = 27 days</p>",
                    solution_hi: "<p>59.(c)<br>माना , A की क्षमता = 1 इकाई <br>कुल कार्य = <math display=\"inline\"><mn>48</mn><mo>&#215;</mo><mn>1</mn></math>= 48 इकाई <br>11<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिनों में , A ने 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math> दिनों के लिए कार्य किया और B ने 6 दिनों के लिए कार्य किया &hellip;&hellip;(B ने कार्य प्रारंभ किया)<br>A द्वारा 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिनों में किया गया कार्य <br>= <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>&#215;</mo></math>1 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>16</mn><mn>3</mn></mfrac></mstyle></math> इकाई <br>B द्वारा किया गया कार्य = 48 - <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>128</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> इकाई <br>B की क्षमता =<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>128</mn></mrow><mrow><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>64</mn><mn>9</mn></mfrac></mstyle></math> इकाई&nbsp;<br>B द्वारा कुल कार्य का 4 गुना पूरा करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>48</mn></mrow><mrow><mfrac><mrow><mn>64</mn></mrow><mrow><mn>9</mn></mrow></mfrac></mrow></mfrac></math> = 27 दिन<br><strong>वैकल्पिक विधि :-</strong> <br>11<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> दिनों से, A ने 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math> दिनों के लिए काम किया और B ने 6 दिनों के लिए काम किया......(B ने काम शुरू किया)<br>B &times; 6 + A &times; 5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 48A<br>6B = 48A - <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>A &rArr; 18B = 128A<br><math display=\"inline\"><mfrac><mrow><mi>B</mi></mrow><mrow><mi>A</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>64</mn><mn>9</mn></mfrac></mstyle></math><br>कुल काम = 9 &times; 48 = 432 यूनिट<br>B द्वारा उसी कार्य का 4 गुना समय पूरा करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>432</mn></mrow><mrow><mn>64</mn></mrow></mfrac></math> = 27 दिन</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. Select the correct water image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679255629.png\" alt=\"rId31\" width=\"164\" height=\"52\"></p>",
                    question_hi: "<p>60. जब दर्पण को MN पर रखा जाए तो दी गई आकृति की सही जल छवि का चयन करें।</p>\n<p><strong id=\"docs-internal-guid-95c6bc99-7fff-daa4-2f3c-7d3ab424a569\"></strong><br><strong id=\"docs-internal-guid-8331ffcc-7fff-c881-f330-a95d6e6a50d8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcGagS-sr3GTU8sYFwu16L5bGQtzqI5o-ALdZK03RY3K3HuMkDVKEUorX33Tz8JA1m6LfUQOMepdAVJRjtRsljsAQIwrt8QJTVi4L23v7wMOypkGxMYJZPb078n2HrCWYMZuCSeB13PvoUprsPqTIj0ojm7?key=173xHP_0HTfmOuXxTPd7aQ\" width=\"140\" height=\"40\"></strong></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679255857.png\" alt=\"rId33\" width=\"127\" height=\"39\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256064.png\" alt=\"rId35\" width=\"136\" height=\"40\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256306.png\" alt=\"rId37\" width=\"149\" height=\"40\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256560.png\" alt=\"rId39\" width=\"140\" height=\"40\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679255857.png\" alt=\"rId33\" width=\"127\" height=\"39\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256064.png\" alt=\"rId35\" width=\"136\" height=\"40\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256306.png\" alt=\"rId37\" width=\"149\" height=\"40\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256560.png\" alt=\"rId39\" width=\"140\" height=\"40\"></p>"],
                    solution_en: "<p>60.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256560.png\" alt=\"rId39\" width=\"140\" height=\"40\"></p>",
                    solution_hi: "<p>60.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256560.png\" alt=\"rId39\" width=\"140\" height=\"40\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. &lsquo;Triticum aestivum&rsquo; is the scientific name of ________.</p>",
                    question_hi: "<p>61. \'ट्रिटिकम एस्टिवम\' ________ का वैज्ञानिक नाम है।</p>",
                    options_en: ["<p>Monkey</p>", "<p>Wheat</p>", 
                                "<p>Mango</p>", "<p>Human</p>"],
                    options_hi: ["<p>बंदर</p>", "<p>गेहूं</p>",
                                "<p>आम</p>", "<p>मानव</p>"],
                    solution_en: "<p>61.(b)<strong> Wheat.</strong> Other scientific names - Mangifera Indica (Mango), Homo Sapiens (Humans), Cercopithecidae (Monkey), Ocimum sanctum (Tulsi), Gossypium herbaceum (Cotton), Azadirachta indica (Neem), Ficus benghalensis (Banyan), Oryza sativa (Rice), Triticum aestivum (Wheat), Bos indicus (Cattle), Rana tigrina (Frog), Elephas maximus (Asian Elephant), Pavo cristatus (Peacock).</p>",
                    solution_hi: "<p>61.(b)<strong> गेहूं।</strong> अन्य वैज्ञानिक नाम - मैंगीफेरा इंडिका (आम), होमो सेपियन्स (मनुष्य), सर्कोपिथेसिडे (बंदर), ओसीमम सैंक्टम (तुलसी), गॉसिपियम हर्बेशियम (कपास), अजाडिराक्टा इंडिका (नीम), फाइकस बेंघालेंसिस (बरगद), ओरिजा सैटिवा (चावल) , ट्रिटिकम एस्टिवम (गेहूं), बोस इंडिकस (मवेशी), राणा टाइग्रीना (मेंढक), एलिफस मैक्सिमस (एशियाई हाथी), पावो क्रिस्टेटस (मोर)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. Select the figure from the options that can replace the question mark (?) and complete the pattern. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256667.png\" alt=\"rId40\"></p>",
                    question_hi: "<p>62. विकल्पों में से उस आकृति का चयन करें जो प्रश्न-चिह्न (?) के स्थान पर आ सकती है और पैटर्न को पूर्ण करती है। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256667.png\" alt=\"rId40\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256778.png\" alt=\"rId41\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256906.png\" alt=\"rId42\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257013.png\" alt=\"rId43\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257211.png\" alt=\"rId44\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256778.png\" alt=\"rId41\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679256906.png\" alt=\"rId42\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257013.png\" alt=\"rId43\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257211.png\" alt=\"rId44\"></p>"],
                    solution_en: "<p>62.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257013.png\" alt=\"rId43\"></p>",
                    solution_hi: "<p>62.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257013.png\" alt=\"rId43\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. A large tanker can be filled by pipes A and B in 60 minutes and 30 minutes, respectively. How much time will it take to fill the empty tanker, if for half the time pipe B is opened and for the remaining time pipes A and B are opened ?</p>",
                    question_hi: "<p>63. एक बड़े टैंकर को पाइप A और B द्वारा क्रमश: 60 मिनट और 30 मिनट में भरा जा सकता है। यदि पाइप B को आधे समय के लिए खोला जाता है, और शेष समय के लिए पाइप A और B दोनों को खोल दिया जाता है, तो खाली टैंकर को भरने में कितना समय लगेगा ?</p>",
                    options_en: ["<p>20 minutes</p>", "<p>15 minutes</p>", 
                                "<p>24 minutes</p>", "<p>28 minutes</p>"],
                    options_hi: ["<p>20 मिनट</p>", "<p>15 मिनट</p>",
                                "<p>24 मिनट</p>", "<p>28 मिनट</p>"],
                    solution_en: "<p>63.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257325.png\" alt=\"rId45\" width=\"192\" height=\"192\"><br>B works for whole time and A works for half time , then efficiency = 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>5</mn><mn>2</mn></mfrac></mstyle></math><br>Time taken by both to complete the work = <math display=\"inline\"><mfrac><mrow><mn>60</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 24 min</p>",
                    solution_hi: "<p>63.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257422.png\" alt=\"rId46\" width=\"198\" height=\"192\"><br>B पूरे समय के लिए कार्य करता है और A आधे समय के लिए कार्य करता है, तो दक्षता&nbsp;= 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>5</mn><mn>2</mn></mfrac></mstyle></math><br>दोनों को कार्य पूरा करने में लगा समय&nbsp;= <math display=\"inline\"><mfrac><mrow><mn>60</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 24 मिनट</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. If &lsquo;&gt;&rsquo; denotes &lsquo;+&rsquo;, &lsquo;&lt;&rsquo; denotes &lsquo;-&rsquo;, &lsquo;+&rsquo; denotes &lsquo;&divide;&rsquo;, &lsquo;^&rsquo; denotes &lsquo;&times;&rsquo;, &lsquo;-&rsquo; denotes &lsquo;=&rsquo;, &lsquo;=&rsquo; denotes &lsquo;&lt;&rsquo; and &lsquo;&times;&rsquo; denotes &lsquo;&gt;&rsquo;, then which of the options is correct?</p>",
                    question_hi: "<p>64. यदि \'&gt;\' का अर्थ \'+\', \'&lt;\' का अर्थ \'-\', \'+&rsquo; का अर्थ \'&divide;\', \'^\' का अर्थ \'&times;\', \'-\' का अर्थ \'=\', \'=\' का अर्थ \'&lt;\' और \'&times;\' का अर्थ \'&gt;\' है। तो कौन सा विकल्प सही है?</p>",
                    options_en: ["<p>4 &gt; 6 + 2 &times; 32 + 4 &lt; 1</p>", "<p>14 + 7 &gt; 3 = 6 + 3 &gt; 2</p>", 
                                "<p>8 &lt; 4 + 2 = 6 &gt; 3</p>", "<p>6 + 3 &gt; 8 = 4 + 2 &lt; 1</p>"],
                    options_hi: ["<p>4 &gt; 6 + 2 &times; 32 + 4 &lt; 1</p>", "<p>14 + 7 &gt; 3 = 6 + 3 &gt; 2</p>",
                                "<p>8 &lt; 4 + 2 = 6 &gt; 3</p>", "<p>6 + 3 &gt; 8 = 4 + 2 &lt; 1</p>"],
                    solution_en: "<p>64.(c) If &lsquo;&gt;&rsquo; denotes &lsquo;+&rsquo;, &lsquo;&lt;&rsquo; denotes &lsquo;-&rsquo;, &lsquo;+&rsquo; denotes &lsquo;&divide;&rsquo;, &lsquo;^&rsquo; denotes &lsquo;&times;&rsquo;, &lsquo;-&rsquo; denotes &lsquo;=&rsquo;, &lsquo;=&rsquo; denotes &lsquo;&lt;&rsquo; and &lsquo;&times;&rsquo; denotes &lsquo;&gt;&rsquo;, <br>Then from the option : <br>8 &lt; 4 + 2 = 6 &gt; 3 <br>&rArr; 8 - 4 &divide; 2 &lt; 6 + 3 &rArr; 6 &lt; 9 is correct.</p>",
                    solution_hi: "<p>64.(c) यदि \'&gt;\' का अर्थ \'+\', \'&lt;\' का अर्थ \'-\',&nbsp;\'+\' का अर्थ \'&divide;\', \'^\' का अर्थ \'&times;\', \'-\' का अर्थ \'=\', \'=\'&nbsp;का अर्थ \'&lt;\' और \' &times;\' का अर्थ है \'&gt;\',<br>फिर विकल्प से:<br>8 &lt; 4 + 2 = 6 &gt; 3 <br>&rArr; 8 - 4 &divide; 2 &lt; 6 + 3 &rArr; 6 &lt; 9 सही है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. In a 200 m race, P beats Q by 35 m or 7 seconds. What is P\'s time over the course?</p>",
                    question_hi: "<p>65. एक 200 मीटर की दौड़ में, P, Q को 35 मीटर और 7 सेकेंड से हरा देता है। दौड़ के दौरान P द्वारा लिया गया समय ज्ञात कीजिए ।</p>",
                    options_en: ["<p>36 seconds</p>", "<p>33 seconds</p>", 
                                "<p>40 seconds</p>", "<p>47 seconds</p>"],
                    options_hi: ["<p>36 सेकेंड</p>", "<p>33 सेकेंड</p>",
                                "<p>40 सेकेंड</p>", "<p>47 सेकेंड</p>"],
                    solution_en: "<p>65.(b) Distance of the course = 200 m Now, we can say that,&nbsp;Q covers 35 m in 7 sec.<br>Time taken by Q to covers 200 m<math display=\"inline\"><mo>&#8594;</mo></math> 735 &times; 200 = 40 sec.<br>Hence, P covers 200 m<math display=\"inline\"><mo>&#8594;</mo></math>40 -7 = 33 sec.</p>",
                    solution_hi: "<p>65.(b) दौड़ की दूरी = 200 मीटर&nbsp;अब, हम कह सकते हैं कि, Q ,7 सेकंड में 35 मीटर की दूरी तय करता है।<br>200 मीटर की दूरी तय करने में Q द्वारा लिया गया समय <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8594;</mo><mfrac><mn>7</mn><mn>35</mn></mfrac></math>&times; 200 = 40 सेकंड .<br>इसलिए, P ,200 मीटर तय करता है <math display=\"inline\"><mo>&#8594;</mo></math>40 - 7 = 33 सेकंड .</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. Our body works within the pH range of:</p>",
                    question_hi: "<p>66. हमारा शरीर निम्न की pH श्रेणी के बीच कार्य करता है:</p>",
                    options_en: ["<p>8 to 8.7</p>", "<p>7 to 7.8</p>", 
                                "<p>7.8 to 8.0</p>", "<p>6 to 7</p>"],
                    options_hi: ["<p>8 से 8.7</p>", "<p>7 से 7.8</p>",
                                "<p>7.8 से 8.0</p>", "<p>6 से 7</p>"],
                    solution_en: "<p>66.(b) <strong>7 to 7.8.</strong> The normal pH range for our arterial blood is 7.35 - 7.45. The pH of saliva is 6.7, and Pure alcohol is 7.33. <strong>Sorensen </strong>was a Dutch scientist, who for the first time introduced the concept of pH. pH stands for the <strong>potential of hydrogen</strong>, which refers to the concentration of Hydrogen ions in a solution.</p>",
                    solution_hi: "<p>66.(b) <strong>7 से 7.8 ।</strong> हमारे धमनियों के रक्त के लिए सामान्य pH रेंज 7.35 - 7.45 है। लार का pH 6.7 है, और शुद्ध एल्कोहल का pH 7.33 है। <strong>सोरेनसेन</strong> एक डच वैज्ञानिक थे, जिन्होंने पहली बार pH की अवधारणा पेश की थी। pH का अर्थ <strong>पोटेंशियल ऑफ हाइड्रोजन</strong> है, जो विलयन में हाइड्रोजन आयनों की एकाग्रता को दर्शाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. Select the figure that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257553.png\" alt=\"rId47\" width=\"270\" height=\"58\"></p>",
                    question_hi: "<p>67. उस आकृति का चयन कीजिए जो नीचे दी गयी श्रृंखला में प्रश्नचिन्ह ( ? ) के स्थान पर आ सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257553.png\" alt=\"rId47\" width=\"279\" height=\"60\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257683.png\" alt=\"rId48\" width=\"74\" height=\"61\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257777.png\" alt=\"rId49\" width=\"73\" height=\"60\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257870.png\" alt=\"rId50\" width=\"73\" height=\"60\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257963.png\" alt=\"rId51\" width=\"73\" height=\"60\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257683.png\" alt=\"rId48\" width=\"73\" height=\"60\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257777.png\" alt=\"rId49\" width=\"73\" height=\"60\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257870.png\" alt=\"rId50\" width=\"73\" height=\"60\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257963.png\" alt=\"rId51\" width=\"73\" height=\"60\"></p>"],
                    solution_en: "<p>67.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257683.png\" alt=\"rId48\"></p>",
                    solution_hi: "<p>67.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679257683.png\" alt=\"rId48\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. After offering successive discounts of 15% and 12%, there was still a 10% profit on the sale of the item. What is the ratio of the marked price to the cost price of the item?</p>",
                    question_hi: "<p>68. 15% और 12% की क्रमिक छूट देने के बाद भी वस्तु की बिक्री पर 10% का लाभ था। अंकित मूल्य का वस्तु के क्रय मूल्य से अनुपात कितना है?</p>",
                    options_en: ["<p>2 : 1</p>", "<p>25 : 17</p>", 
                                "<p>90 : 73</p>", "<p>110 : 73</p>"],
                    options_hi: ["<p>2 : 1</p>", "<p>25 : 17</p>",
                                "<p>90 : 73</p>", "<p>110 : 73</p>"],
                    solution_en: "<p>68.(b) Let MP = 100 <br>SP = 100<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#160;</mi><mo>&#215;</mo><mfrac><mn>85</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>88</mn><mn>100</mn></mfrac></math> = 74.80<br>CP = <math display=\"inline\"><mfrac><mrow><mn>74</mn><mo>.</mo><mn>80</mn></mrow><mrow><mn>110</mn></mrow></mfrac></math> &times; 100 = 68<br>MP : CP = 100 : 68 = 25 : 17</p>",
                    solution_hi: "<p>68.(b) माना अंकित मूल्य = 100<br>विक्रय मूल्य = 100&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mo>&#215;</mo><mfrac><mn>85</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>88</mn><mn>100</mn></mfrac></mstyle></math>&nbsp;= 74.80<br>लागत मूल्य = <math display=\"inline\"><mfrac><mrow><mn>74</mn><mo>.</mo><mn>80</mn></mrow><mrow><mn>110</mn></mrow></mfrac></math> &times; 100 = 68<br>अंकित मूल्य : क्रय मूल्य = 100 : 68 = 25 : 17</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. How many such digits are there in the following sequence, each of which is immediately preceded by as well as immediately followed by a letter?<br>A B 7 C D 9 Z Y * P 2 M &copy; K S 3 <math display=\"inline\"><mo>&#8593;</mo></math>5 N T</p>",
                    question_hi: "<p>69. निम्नलिखित क्रम में ऐसे कितने अंक हैं, जिनमें से प्रत्येक के ठीक पहले और ठीक बाद में एक अक्षर है?<br>A B 7 C D 9 Z Y * P 2 M &copy; K S 3 <math display=\"inline\"><mo>&#8593;</mo></math>5 N T</p>",
                    options_en: ["<p>One</p>", "<p>Two</p>", 
                                "<p>Four</p>", "<p>Three</p>"],
                    options_hi: ["<p>एक</p>", "<p>दो</p>",
                                "<p>चार</p>", "<p>तीन</p>"],
                    solution_en: "<p>69.(d)<br>A <span style=\"text-decoration: underline;\">B 7 C</span> <span style=\"text-decoration: underline;\">D 9 Z</span> Y * <span style=\"text-decoration: underline;\">P 2 M</span> &copy; K S 3 <math display=\"inline\"><mo>&#8593;</mo></math>5 N T <br>In the above sequence, the number of digits, each of which is immediately preceded by as well as immediately followed by a letter is = 3</p>",
                    solution_hi: "<p>69.(d)<br>A <span style=\"text-decoration: underline;\">B 7 C</span> <span style=\"text-decoration: underline;\">D 9 Z</span> Y * <span style=\"text-decoration: underline;\">P 2 M</span> &copy; K S 3 <math display=\"inline\"><mo>&#8593;</mo></math>5 N T <br>उपरोक्त क्रम में, अंकों की संख्या, जिनमें से प्रत्येक के ठीक पहले और ठीक बाद में एक अक्षर है = 3</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. At certain rate of simple interest per annum a sum of money amounts to <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> of itself in 10 years. What is the rate of simple interest per annum ?</p>",
                    question_hi: "<p>70. साधारण ब्याज की एक निश्चित वार्षिक दर पर कोई धनराशि 10 वर्ष में स्वयं के <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> के बराबर हो जाती है। साधारण ब्याज की वार्षिक दर कितनी है ?</p>",
                    options_en: ["<p>6.25 %</p>", "<p>7.5%</p>", 
                                "<p>5%</p>", "<p>7.25%</p>"],
                    options_hi: ["<p>6.25 %</p>", "<p>7.5%</p>",
                                "<p>5%</p>", "<p>7.25%</p>"],
                    solution_en: "<p>70.(a) Rate % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>&#160;</mi><mi>n</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>13</mn><mn>8</mn></mfrac></mstyle><mi>&#160;</mi><mo>-</mo><mn>1</mn><mo>)</mo><mo>&#215;</mo><mn>100</mn></mrow><mn>10</mn></mfrac></mstyle></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>500</mn><mn>80</mn></mfrac></mstyle></math>= 6.25 %</p>",
                    solution_hi: "<p>70.(a) दर % = <math display=\"inline\"><mfrac><mrow><mo>(</mo><mi>&#160;</mi><mi>n</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mi>&#160;</mi><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>=</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>13</mn><mn>8</mn></mfrac></mstyle><mi>&#160;</mi><mo>-</mo><mn>1</mn><mo>)</mo><mo>&#215;</mo><mn>100</mn></mrow><mn>10</mn></mfrac></mstyle></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>500</mn><mn>80</mn></mfrac></mstyle></math>= 6.25 %</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. Litmus is an natural acid-base indicator which is extracted from:</p>",
                    question_hi: "<p>71. लिटमस एक प्राकृतिक अम्ल-क्षार संकेतक है जिसे निम्न से निकाला जाता है:</p>",
                    options_en: ["<p>rose petals</p>", "<p>turmeric</p>", 
                                "<p>lemon grass</p>", "<p>Lichen</p>"],
                    options_hi: ["<p>गुलाब की पंखुड़ियाँ</p>", "<p>हल्दी</p>",
                                "<p>नींबू घास</p>", "<p>लाइकेन</p>"],
                    solution_en: "<p>71.(d) <strong>Lichen.</strong> Litmus - purple coloured dye which is used as a visual indicator to identify acids and bases. Natural Indicators - These indicators are obtained from natural sources like plants. Examples - Turmeric, litmus, china rose petals, etc. Man-made indicators - Phenolphthalein, Methyl orange. Some indicators and their color change - Litmus Paper (Acidic - Red, Basic - Blue) ,Turmeric (Acidic - Yellow, Basic - Red), China Rose (Acidic - Dark pink (Magenta), Basic - Green), Methyl Orange (Acidic - Red or Orange, Basic - Yellow), Phenolphthalein (Acidic - colourless, Basic - Light Pink). Lichens are used as bio - indicators for air pollution.</p>",
                    solution_hi: "<p>71.(d) <strong>लाइकेन।</strong> लिटमस - बैंगनी रंग की डाई जिसका उपयोग अम्ल और क्षार की पहचान करने के लिए एक दृश्य संकेतक के रूप में किया जाता है। प्राकृतिक संकेतक - ये संकेतक पौधों जैसे प्राकृतिक स्रोतों से प्राप्त किए जाते हैं। उदाहरण - हल्दी, लिटमस, चीनी गुलाब की पंखुड़ियाँ आदि। मानव निर्मित संकेतक - फेनोल्फथेलिन, मिथाइल ऑरेंज। कुछ संकेतक और उनका रंग परिवर्तन - लिटमस पेपर (अम्लीय - लाल, क्षारकीय - नीला), हल्दी (अम्लीय - पीला, क्षारकीय - लाल), चीनी गुलाब (अम्लीय - गहरा गुलाबी (मैजेंटा), क्षारक - हरा), मिथाइल ऑरेंज (अम्लीय) - लाल या नारंगी, बेसिक - पीला), फेनोल्फथेलिन (अम्लीय - रंगहीन, बेसिक - हल्का गुलाबी)। लाइकेन का उपयोग वायु प्रदूषण के लिए जैव-सूचक के रूप में किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72.A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258067.png\" alt=\"rId52\" width=\"213\" height=\"103\"></p>",
                    question_hi: "<p>72. एक कागज को मोड़कर नीचे दिखाया गया है। खोलने पर यह कैसे दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258067.png\" alt=\"rId52\" width=\"213\" height=\"103\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258176.png\" alt=\"rId53\" width=\"88\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258292.png\" alt=\"rId54\" width=\"95\" height=\"87\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258403.png\" alt=\"rId55\" width=\"89\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258506.png\" alt=\"rId56\" width=\"88\" height=\"86\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258176.png\" alt=\"rId53\" width=\"88\" height=\"86\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258292.png\" alt=\"rId54\" width=\"95\" height=\"87\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258403.png\" alt=\"rId55\" width=\"89\" height=\"89\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258506.png\" alt=\"rId56\" width=\"88\" height=\"86\"></p>"],
                    solution_en: "<p>72.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258292.png\" alt=\"rId54\" width=\"113\" height=\"103\"></p>",
                    solution_hi: "<p>72.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727679258292.png\" alt=\"rId54\" width=\"113\" height=\"103\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. The mean weight of 18 jackfruits is 7.2 kg. If the weights of 2 more jackfruits whose weights are equal are added, the mean weight decreases by 20 g. What is the weight of each jackfruit added later?</p>",
                    question_hi: "<p>73. 18 कटहल का माध्य भार 7.2 kg है। यदि समान भार 2 और कटहल उनमे शामिल कर लिए जाए, तो माध्य भार 20 gm कम हो जाता है। बाद में जोड़े गए प्रत्येक कटहल का भार कितना है?</p>",
                    options_en: ["<p>7.18 kg</p>", "<p>7.22 kg</p>", 
                                "<p>7.00 kg</p>", "<p>7.40 kg</p>"],
                    options_hi: ["<p>7.18 kg</p>", "<p>7.22 kg</p>",
                                "<p>7.00 kg</p>", "<p>7.40 kg</p>"],
                    solution_en: "<p>73.(c) Total weight of 18 jackfruits = 7.2 &times; 18 = 129.6<br>When two more jackfruits are added then new average = 7.2 - 0.02 = 7.18<br>Total weight of 20 jackfruits = 7.18 &times; 20 = 143.6<br>Weight of the each jackfruit added&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>143</mn><mo>.</mo><mn>6</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>129</mn><mo>.</mo><mn>6</mn></mrow><mn>2</mn></mfrac></math> = 7.00 kg</p>",
                    solution_hi: "<p>73.(c)<br>18 कटहल का कुल वजन = 7.2 &times; 18 = 129.6<br>जब दो और कटहल जोड़े जाते हैं तो नया औसत = 7.2 - 0.02 = 7.18<br>20 कटहल का कुल वजन = 7.18 &times; 20 = 143.6<br>जोड़े गए प्रत्येक कटहल का भार =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>143</mn><mo>.</mo><mn>6</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>129</mn><mo>.</mo><mn>6</mn></mrow><mn>2</mn></mfrac></mstyle></math>= 7.00 kg</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. Who coined the term &lsquo;protoplasm&rsquo;?</p>",
                    question_hi: "<p>74. प्रोटोप्लाज्म शब्द का प्रतिपादन किसने किया ?</p>",
                    options_en: ["<p>Johann Evangelist Purkinje</p>", "<p>Robert Hooke</p>", 
                                "<p>G.J. Mendel</p>", "<p>Charles Darwin</p>"],
                    options_hi: ["<p>जोहान इंजीलवादी पर्किनजे</p>", "<p>रॉबर्ट हुक</p>",
                                "<p>जी.जे0. मेंडेल</p>", "<p>चार्ल्स डार्विन</p>"],
                    solution_en: "<p>74.(a) <strong>Johann Evangelist Purkinje.</strong> Protoplasm is the living part of a cell that is surrounded by a plasma membrane. It includes cytoplasm, nucleus and other organelle. <strong>Robert Hooke</strong> is known for his discovery of the law of elasticity and cell (1665). <strong>G.J. Mendel</strong> (The Father of Genetics) - studied the inheritance of traits . <strong>Charles Darwin</strong> gave the theory of evolution and was famous for natural selection.</p>",
                    solution_hi: "<p>74.(a) <strong>जोहान इंजीलवादी पर्किनजे।</strong> प्रोटोप्लाज्म कोशिका का जीवित भाग है जो प्लाज्मा झिल्ली से घिरा होता है। इसमें साइटोप्लाज्म, न्यूक्लियस और अन्य अंग शामिल हैं। <strong>रॉबर्ट हुक</strong> को लोच और कोशिका के नियम (1665) की खोज के लिए जाना जाता है। <strong>G.J. मेंडल</strong> (जेनेटिक्स के जनक) - लक्षणों की विरासत का अध्ययन किया। <strong>चार्ल्स डार्विन </strong>ने विकासवाद का सिद्धांत दिया और प्राकृतिक चयन के लिए प्रसिद्ध थे।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "misc",
                    question_en: "<p>75. A, B, C, D, E and F are six school-going children. A and B are from the Red House and the others are from the Green House. D and F are tall, unlike the others. A, C and D are boys. Which of the following is a tall girl from the Green House?</p>",
                    question_hi: "<p>75. A, B, C, D, E और F छह स्कूल जाने वाले बच्चे हैं। A और B रेड हाउस से हैं और अन्य ग्रीन हाउस से हैं। D और F दूसरों के तुलना में लंबे हैं | A, C और D लड़के हैं। निम्नलिखित में से कौन ग्रीन हाउस की एक लंबी लड़की है?</p>",
                    options_en: ["<p>E</p>", "<p>F</p>", 
                                "<p>D</p>", "<p>B</p>"],
                    options_hi: ["<p>E</p>", "<p>F</p>",
                                "<p>D</p>", "<p>B</p>"],
                    solution_en: "<p>75.(b)<br>A &amp; B - Red house , C to F - Green house <br>D &amp; F are tall ,unlike the others A .</p>\n<p>C and D - BOYSo F - girl ( Tall )</p>",
                    solution_hi: "<p>75.(b) <br>A और B - रेड हाउस, C से F - ग्रीन हाउस<br>D और F अन्य के विपरीत लम्बे हैं।</p>\n<p>A, C और D - लड़का तो F - लड़की (लम्बी )</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>