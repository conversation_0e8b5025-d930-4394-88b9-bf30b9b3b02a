<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 50</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">50</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 49,
                end: 49
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. The Directive Principles of State Policy are similar to the \'Instruments of Directives\' given in ___________.</p>",
                    question_hi: "<p>1. राज्य के नीति-निर्देशक सिद्धांत ___________ में दिए गए \'निर्देशों के लिखत\' के समान हैं।</p>",
                    options_en: ["<p>Government of India Act, 1935</p>", "<p>Government of India Act, 1919</p>", 
                                "<p>Indian Independence Act, 1947</p>", "<p>Government of India Act, 1909</p>"],
                    options_hi: ["<p>भारत सरकार अधिनियम, 1935</p>", "<p>भारत सरकार अधिनियम, 1919</p>",
                                "<p>भारतीय स्वतंत्रता अधिनियम, 1947</p>", "<p>भारत सरकार अधिनियम, 1909</p>"],
                    solution_en: "<p><strong>1.(a) Government of India Act (GOI),</strong> 1935. Directive Principles of State Policy (DPSP) - Covers the Articles 36-51 in Part IV of the constitution. Provisions of GOI Act of 1935 - All India Federation; Provincial Autonomy.</p>",
                    solution_hi: "<p><strong>1.(a) भारत सरकार अधिनियम (GOI), 1</strong>935। राज्य के नीति निर्देशक सिद्धांत (DPSP) - संविधान के भाग IV में अनुच्छेद 36-51 शामिल है। भारत सरकार अधिनियम 1935 के प्रावधान - अखिल भारतीय महासंघ; प्रांतीय स्वायत्तता ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "2. In terms of total number of passengers, which was the largest airport in India in the year 2022?",
                    question_hi: "<p>2. यात्रियों की कुल संख्या की दृष्टि से, वर्ष 2022 में भारत का सबसे बड़ा हवाई अड्डा कौन-सा था?</p>",
                    options_en: [" Rajiv Gandhi International Airport (Hyderabad)", "<p>Kempegowda International Airport (Bengaluru)</p>", 
                                "<p>Chhatrapati Shivaji Maharaj International Airport (Mumbai)</p>", "<p>Indira Gandhi International Airport (Delhi)</p>"],
                    options_hi: ["<p>राजीव गांधी अंतर्राष्ट्रीय हवाई अड्डा (हैदराबाद)</p>", "<p>केंपेगौड़ा अंतर्राष्ट्रीय हवाई अड्डा (बेंगलुरु)</p>",
                                "<p>छत्रपति शिवाजी महाराज अंतर्राष्ट्रीय हवाई अड्डा (मुंबई)</p>", "<p>इंदिरा गांधी अंतर्राष्ट्रीय हवाई अड्डा (दिल्ली)</p>"],
                    solution_en: "<p><strong>2.(d) Indira Gandhi International Airport (Delhi) - </strong>The first airport in India to become free of single-use plastics. Other International Airport in India: Chhatrapati Shivaji Maharaj (Mumbai); Netaji Subhas Chandra Bose (Kolkata); Lokpriya Gopinath Bordoloi (Guwahati) and Guru Ram Dass Jee ( Amritsar).</p>",
                    solution_hi: "<p><strong>2.(d) इंदिरा गांधी अंतर्राष्ट्रीय हवाई अड्डा (दिल्ली) -</strong> सिंगल-यूज प्लास्टिक से मुक्त होने वाला भारत का पहला हवाई अड्डा है। भारत में अन्य अंतर्राष्ट्रीय हवाई अड्डे: छत्रपति शिवाजी महाराज (मुंबई); नेताजी सुभाष चंद्र बोस (कोलकाता); लोकप्रिय गोपीनाथ बोरदोलोई (गुवाहाटी) और गुरु राम दास जी (अमृतसर)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. In which of the following years was the twelfth plan completed?</p>",
                    question_hi: "<p>3. बारहवीं योजना निम्नलिखित में से किस वर्ष में पूरी हुई थी?</p>",
                    options_en: ["<p>2014</p>", "<p>2016</p>", 
                                "<p>2017</p>", "<p>2015</p>"],
                    options_hi: ["<p>2014</p>", "<p>2016</p>",
                                "<p>2017</p>", "<p>2015</p>"],
                    solution_en: "<p><strong>3.(c) 2017. </strong>Twelfth five year plan: Aim - Faster, More Inclusive and Sustainable Growth. Its growth rate target was 8%. The Five Year Plans were formulated, implemented and regulated by the Planning Commission. The Planning Commission was replaced by NITI (National Institution for Transforming India) AAYOG in 2015.</p>",
                    solution_hi: "<p><strong>3.(c) 2017.</strong> बारहवीं पंचवर्षीय योजना: उद्देश्य - तीव्र, अधिक समावेशी और सतत विकास। इसका विकास लक्ष्य दर 8% था। पंचवर्षीय योजनाएँ योजना आयोग द्वारा तैयार, कार्यान्वित और विनियमित की गईं है। योजना आयोग को 2015 में NITI (नेशनल इंस्टीट्यूशन फॉर ट्रांसफॉर्मिंग इंडिया) आयोग द्वारा प्रतिस्थापित किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "4. Which of the following pairs is NOT correctly matched with respect to the player and<br />sport?",
                    question_hi: "4. खिलाड़ी और उससे संबंधित खेल का निम्नलिखित में से कौन-सा युग्म सुमेलित नहीं है?",
                    options_en: [" Yogesh Kathuniya - Discus throw ", " Bhavina Patel - Para-shooting ", 
                                " Nishad Kumar - High jump  ", " LY Suhas - Para-badminton"],
                    options_hi: [" योगेश कथुनिया - डिस्कस थ्रो ", " भाविना पटेल - पैरा शूटिंग ",
                                " निषाद कुमार - ऊँची कूद", " एल.वाई. सुहास - पैरा बैडमिंटन"],
                    solution_en: "4.(b) Bhavina Patel is an Indian para athlete and table tennis player from Mehsana, Gujarat. Other Athletes and their sports - Swapnil Kusale (shooting), Akshdeep Singh (Athletics), Nikhat Zareen (Boxing), Dhiraj Bommadevara (Archery), Vishnu Saravanan (sailing).",
                    solution_hi: "4.(b) भाविना पटेल मेहसाणा, गुजरात की एक भारतीय पैरा एथलीट और टेबल टेनिस खिलाड़ी हैं। अन्य एथलीट और उनके खेल - स्वप्निल कुसाले (निशानेबाजी), अक्षदीप सिंह (एथलेटिक्स), निखत ज़रीन (मुक्केबाजी), धीरज बोम्मदेवरा (तीरंदाजी), विष्णु सरवनन (नौकायन)।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Under which function of the Union Budget does the Government of India build roads<br>and hospitals?</p>",
                    question_hi: "<p>5. केंद्रीय बजट के किस कार्य के तहत भारत सरकार सड़कों और अस्पतालों का निर्माण करती है?</p>",
                    options_en: ["<p>Stabilization</p>", "<p>Redistribution</p>", 
                                "<p>Surplus generation</p>", "<p>Allocation</p>"],
                    options_hi: ["<p>स्थिरीकरण</p>", "<p>पुनर्वितरण</p>",
                                "<p>अधिशेष निर्माण</p>", "<p>आवंटन</p>"],
                    solution_en: "<p><strong>5.(d) Allocation. </strong>Stabilization: This function of the Union Budget involves measures to stabilize the economy by controlling inflation. Surplus generation: This function aims at generating a surplus in the budget by increasing revenue or reducing expenditures.</p>",
                    solution_hi: "<p><strong>5.(d) आवंटन। </strong>स्थिरीकरण: केंद्रीय बजट के इस कार्य में मुद्रास्फीति को नियंत्रित करके अर्थव्यवस्था को स्थिर करने के उपाय शामिल हैं। अधिशेष सृजन: इस फ़ंक्शन का उद्देश्य राजस्व में वृद्धि या व्यय को कम करके बजट में अधिशेष उत्पन्न करना है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. As per the CMIE\'s report in December 2022, what was the unemployment rate of the<br>urban population in India?</p>",
                    question_hi: "<p>6. दिसंबर 2022 में सी.एम.आई.ई. की रिपोर्ट के अनुसार, भारत में नगरीय जनसंख्या की बेरोजगारी दर<br>क्या थी?</p>",
                    options_en: ["<p>8.12%</p>", "<p>10.09%</p>", 
                                "<p>11.09%</p>", "<p>12.03%</p>"],
                    options_hi: ["<p>8.12%</p>", "<p>10.09%</p>",
                                "<p>11.09%</p>", "<p>12.03%</p>"],
                    solution_en: "<p><strong>6.(b) 10.09%. </strong>CMIE (Center for Monitoring Indian Economy) is an independent economic think tank and research organization based in Mumbai. It was established in 1976. It analyzes the data to decipher trends in the economy. CMIE has built India\'s largest database on the financial performance of individual companies.</p>",
                    solution_hi: "<p>6<strong>.(b) 10.09% </strong>. CMIE (सेंटर फॉर मॉनिटरिंग इंडियन इकोनॉमी) मुंबई में स्थित एक स्वतंत्र आर्थिक थिंक टैंक और अनुसंधान संगठन है। इसकी स्थापना 1976 में हुई थी। यह अर्थव्यवस्था में रुझानों को समझने के लिए डेटा का विश्लेषण करता है। CMIE ने व्यक्तिगत कंपनियों के वित्तीय प्रदर्शन पर भारत का सबसे बड़ा डेटाबेस बनाया है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Belgium won the Men\'s International Federation Hockey (FIH) 2018 World Cup, which<br>was held in ___________.</p>",
                    question_hi: "<p>7. बेल्जियम ने पुरुषों का अंतरराष्ट्रीय फेडरेशन हॉकी (FIH) 2018 विश्व कप जीता, जो _______ में आयोजित किया गया था।</p>",
                    options_en: ["<p>Australia</p>", "<p>India</p>", 
                                "<p>Germany</p>", "<p>Belgium</p>"],
                    options_hi: ["<p>ऑस्ट्रेलिया</p>", "<p>भारत</p>",
                                "<p>जर्मनी</p>", "<p>बेल्जियम</p>"],
                    solution_en: "<p><strong>7.(b) India. </strong>The Federation Internationale de Hockey (FIH) was founded on 7 January 1924 by Paul Leautey. Headquarter - Lausanne; Switzerland. The International Federation of Women&rsquo;s Hockey Associations (IFWHA) was established in 1927. India hosted the 2023 FIH Men\'s Hockey World Cup. The inaugural men&rsquo;s hockey world cup was held in 1971( Barcelona, Spain) while the women\'s tournament started in 1974.</p>",
                    solution_hi: "<p><strong>7.(b) भारत। </strong>फेडरेशन इंटरनेशनेल डी हॉकी (FIH) की स्थापना 7 जनवरी 1924 को पॉल लेउटी द्वारा की गई थी। मुख्यालय - लॉज़ेन; स्विट्जरलैंड. महिला हॉकी संघों का अंतर्राष्ट्रीय महासंघ (IFWHA) की स्थापना 1927 में हुई थी। भारत ने 2023 FIH पुरुष हॉकी विश्व कप की मेजबानी की। पहला पुरुष हॉकी विश्व कप 1971 (बार्सिलोना, स्पेन) में आयोजित किया गया था जबकि महिला टूर्नामेंट 1974 में शुरू हुआ था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who among the following became the first Indian musician to be honored with the<br>Ramon Magsaysay award, which is considered to be Asia\'s Nobel Prize?</p>",
                    question_hi: "<p>8. एशिया के नोबेल पुरस्कार माने जाने वाले रेमन मैग्सेसे (Ramon Magsaysay) पुरस्कार से सम्मानित<br>होने वाला/वाली पहला/पहली भारतीय संगीतकार कौन था/थी?</p>",
                    options_en: ["<p>Lata Mangeshkar</p>", "<p>MS Subbulakshmi</p>", 
                                "<p>Pandit Ravi Shankar</p>", "<p>Pandit Kumar Gandharva</p>"],
                    options_hi: ["<p>लता मंगेशकर</p>", "<p>एम.एस. सुब्बुलक्ष्मी</p>",
                                "<p>पंडित रविशंकर</p>", "<p>पंडित कुमार गंधर्व</p>"],
                    solution_en: "<p><strong>8.(b) MS Subbulakshmi (</strong>Carnatic singer): Place of Birth: Madurai, Tamil Nadu and the first musician to be awarded the Bharat Ratna (1998). Other Ramon Magsaysay awardees - Vinoba Bhave (1958), Mother Teresa (1962), Satyajit Ray (1967), Arvind Kejriwal (2006), Ravish Kumar (2019).</p>",
                    solution_hi: "<p>8.(b) <strong>एम.एस. सुब्बुलक्ष्मी</strong> (कर्नाटक गायिका): जन्म स्थान: मदुरै, तमिलनाडु और भारत रत्न (1998) से सम्मानित होने वाली पहली संगीतकार थी। अन्य रेमन मैग्सेसे पुरस्कार विजेता - विनोबा भावे (1958), मदर टेरेसा (1962), सत्यजीत रे (1967), अरविंद केजरीवाल (2006), रवीश कुमार (2019)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following organizations regulates international trade?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन-सा संगठन अंतरराष्ट्रीय व्यापार को नियंत्रित करता है?</p>",
                    options_en: ["<p>UNICEF</p>", "<p>UNESCO</p>", 
                                "<p>IMF</p>", "<p>WTO</p>"],
                    options_hi: ["<p>यूनिसेफ</p>", "<p>यूनेस्को</p>",
                                "<p>अंतरराष्ट्रीय मुद्रा कोष</p>", "<p>विश्व व्यापार संगठन</p>"],
                    solution_en: "<p><strong>9.(d) World Trade Organization (WTO) -</strong> The only global international organization dealing with the rules of trade between nations. The goal is to ensure that trade flows as smoothly, predictably and freely as possible. Formed - 1995; Headquarters - Geneva, Switzerland. UNICEF (United Nations Children\'s Fund): Headquarters - New York City; Established - 1946. UNESCO (United Nations Educational, Scientific and Cultural Organization): Established - 1945; Headquarters - Paris (France).</p>",
                    solution_hi: "<p><strong>9.(d) विश्व व्यापार संगठन (WTO) </strong>- राष्ट्रों के बीच व्यापार के नियमों से निपटने वाला एकमात्र वैश्विक अंतर्राष्ट्रीय संगठन है। इसका लक्ष्य यह सुनिश्चित करना है कि व्यापार यथासंभव सुचारू, पूर्वानुमानित और स्वतंत्र रूप से संचालित हो। गठित - 1995; मुख्यालय - जिनेवा, स्विट्जरलैंड। UNICEF (संयुक्त राष्ट्र बाल कोष): मुख्यालय - न्यूयॉर्क शहर; स्थापना - 1946 । UNESCO (संयुक्त राष्ट्र शैक्षिक, वैज्ञानिक और सांस्कृतिक संगठन): स्थापना - 1945; मुख्यालय - पेरिस (फ्रांस)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which festival is celebrated on the last day of the Hindu month of Shravan to mark the end of the monsoon season in the western coastal areas of India?</p>",
                    question_hi: "<p>10. भारत के पश्चिमी तटीय क्षेत्रों में मानसून का मौसम समाप्त होने के उपलक्ष्य में हिंदू माह श्रावण के<br>आखिरी दिन कौन सा पर्व मनाया जाता है?</p>",
                    options_en: ["<p>Narali Purnima</p>", "<p>Kojagiri Purnima</p>", 
                                "<p>Samba Dashami</p>", "<p>Guru Purnima</p>"],
                    options_hi: ["<p>नराली पूर्णिमा</p>", "<p>कोजागिरी पूर्णिमा</p>",
                                "<p>साम्बा दशमी</p>", "<p>गुरु पूर्णिमा</p>"],
                    solution_en: "<p><strong>10.(a) Narali Purnima festival i</strong>s majorly celebrated by fisher community Hindus. It is dedicated to the Sea God Varun and is observed in the month of Shravana. Kojagiri Purnima (Sharad Purnima) - It is celebrated on the full moon day of Ashwin, marking the end of monsoon. Guru Purnima - Celebrated in the month of Ashadha.</p>",
                    solution_hi: "<p><strong>10.(a) नराली पूर्णिमा उत्सव प्र</strong>मुख रूप से मछुआरा समुदाय के हिंदुओं द्वारा मनाया जाता है। यह समुद्र देवता वरुण को समर्पित है और श्रावण के महीने में मनाया जाता है। कोजागिरी पूर्णिमा (शरद पूर्णिमा) - यह अश्विन की पूर्णिमा के दिन मनाई जाती है, जो मानसून के अंत का प्रतीक है। गुरु पूर्णिमा - आषाढ़ माह में मनाई जाती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. From where do most of the refracted light rays enter the eyes?</p>",
                    question_hi: "<p>11. अधिकांश अपवर्तित प्रकाश किरणें आँखों में कहाँ से प्रवेश करती हैं?</p>",
                    options_en: ["<p>From the outer surface of the cornea</p>", "<p>From the outer surface of the retina</p>", 
                                "<p>From the outer surface of the iris</p>", "<p>From the inner surface of the cornea</p>"],
                    options_hi: ["<p>कॉर्निया की बाहरी सतह से</p>", "<p>रेटिना की बाहरी सतह से</p>",
                                "<p>आईरिस की बाहरी सतह से</p>", "<p>कॉर्निया की भीतरी सतह से</p>"],
                    solution_en: "<p><strong>11.(a)<em> From the outer surface of the cornea.</em></strong> Cornea: It is the clear, transparent, anterior portion of the external coat of the eyeball. The rays of light enter this layer and it accounts for two-thirds of the total optical power of the eye. Three layers of human eye - External fibrous coat, Middle vascular coat, and Internal nervous coat. Parts of the human eye - Pupil, Iris, Lens, Retina, Sclera.</p>",
                    solution_hi: "<p><strong>11.(a) कॉर्निया की बाहरी पटल से ।</strong> कॉर्निया: यह नेत्रगोलक के बाहरी आवरण का स्पष्ट, पारदर्शी, अग्रिम भाग है। प्रकाश की किरणें इस परत में प्रवेश करती हैं और यह आंख की कुल ऑप्टिकल शक्ति का दो-तिहाई हिस्सा होती है। मानव आँख की तीन परतें - बाहरी रेशेदार आवरण, मध्य संवहनी आवरण और आंतरिक तंत्रिका आवरण। मानव आँख के भाग - पुतली, आइरिस, लेंस, रेटिना, स्केलेरा।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Sahitya Akademi Award winner Maya Khutegaonkar is a famous __________ dancer.</p>",
                    question_hi: "<p>12. साहित्य अकादमी पुरस्कार विजेता माया खुटेगांवकर एक प्रसिद्ध __________ नृत्यांगना हैं।</p>",
                    options_en: ["<p>Bihu</p>", "<p>Suggi</p>", 
                                "<p>Lavani</p>", "<p>chandelier</p>"],
                    options_hi: ["<p>बिहु</p>", "<p>सुग्गी</p>",
                                "<p>लावणी</p>", "<p>झूमर</p>"],
                    solution_en: "<p>12.(c) <strong>Lavani </strong>dance was derived from &lsquo;lavanya&rsquo; or beauty and is a popular Marathi dance form from Maharashtra. It attained popularity in the Peshwa era. Maya Khutegaonkar received sangeet natak academy award in 2015. Other folk dances and state: Bihu dance (Assam), Suggi dance (Karnataka), and Jhumar dance (Haryana).</p>",
                    solution_hi: "<p>12.(c) <strong>लावणी </strong>नृत्य \'लावण्या\' या सौंदर्य से लिया गया है और यह महाराष्ट्र का एक लोकप्रिय मराठी नृत्य है। पेशवा काल में इसे लोकप्रियता प्राप्त हुई। माया खुटेगांवकर को 2015 में संगीत नाटक अकादमी पुरस्कार मिला। अन्य लोक नृत्य एवं राज्य: बिहू नृत्य (असम), सुग्गी नृत्य (कर्नाटक), और झूमर नृत्य (हरियाणा)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Parliament can create or abolish the State Legislative Council on the recommendation of _______.</p>",
                    question_hi: "<p>13. संसद _______ की सिफारिश पर राज्य विधान-परिषद बना या समाप्त कर सकती है।</p>",
                    options_en: ["<p>State Assembly</p>", "<p>Governor of the state</p>", 
                                "<p>Chief Minister</p>", "<p>President of India</p>"],
                    options_hi: ["<p>राज्य विधानसभा</p>", "<p>राज्य के राज्यपाल</p>",
                                "<p>मुख्यमंत्री</p>", "<p>भारत के राष्ट्रपति</p>"],
                    solution_en: "<p><strong>13.(a) State Assembly.</strong> Article 171 - Composition of the Legislative Councils. The size of the legislative council cannot be more than one-third of the total strength of the legislative assembly and not less than 40 members. Article 169 - The Parliament can abolish a Legislative Council (where it already exists) or create it (where it does not exist) by a simple majority. Six States having a Legislative Council: Andhra Pradesh, Telangana, Uttar Pradesh, Bihar, Maharashtra, Karnataka.</p>",
                    solution_hi: "<p><strong>13.(a) राज्य विधानसभा ।</strong> अनुच्छेद 171 - विधान परिषदों की संरचना। विधान परिषद का आकार विधान सभा की कुल संख्या के एक तिहाई से अधिक तथा 40 सदस्यों से कम नहीं हो सकता। अनुच्छेद 169 - संसद साधारण बहुमत से विधान परिषद को समाप्त कर सकती है (जहां यह पहले से मौजूद है) या इसे बना सकती है (जहां यह मौजूद नहीं है)। विधान परिषद वाले छः राज्य : आंध्र प्रदेश, तेलंगाना, उत्तर प्रदेश, बिहार, महाराष्ट्र, कर्नाटक।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following articles enshrines the Directive Principles as \'fundamental in the governance of the country\' and requires the State to \'apply these principles in making laws\'?</p>",
                    question_hi: "<p>14. निम्नलिखित में से कौन-सा अनुच्छेद निर्देशक सिद्धांतों को \'देश के शासन में मौलिक\' के रूप में निहित<br>करता है और \'कानून बनाने में इन सिद्धांतों को लागू\' करने की राज्य से अपेक्षा करता है?</p>",
                    options_en: ["<p>Article 37</p>", "<p>Article 38</p>", 
                                "<p>Article 39</p>", "<p>Article 36</p>"],
                    options_hi: ["<p>अनुच्छेद 37</p>", "<p>अनुच्छेद 38</p>",
                                "<p>अनुच्छेद 39</p>", "<p>अनुच्छेद 36</p>"],
                    solution_en: "<p><strong>14.(a) Article 37. </strong>Directive Principles of State Policy (DPSP, Part IV, Article 36&ndash;51). Important articles of DPSP - Article 36: &ldquo;Definition of the State&rdquo; has the same meaning as in Part III. Article 38: State to secure a social order for the promotion of the welfare of the people. Article 39A: Equal justice and free legal aid. Article 40: The organisation of village panchayats. Article 50: Separation of judiciary from the executive.</p>",
                    solution_hi: "<p><strong>14.(a) अनुच्छेद 37. </strong>राज्य के नीति निर्देशक सिद्धांत (DPSP , भाग IV, अनुच्छेद 36-51)। DPSP के महत्वपूर्ण अनुच्छेद - अनुच्छेद 36: \"राज्य की परिभाषा\" का वही अर्थ है जो भाग III में है। अनुच्छेद 38: राज्य लोगों के कल्याण को बढ़ावा देने के लिए एक सामाजिक व्यवस्था सुरक्षित करेगा। अनुच्छेद 39A: समान न्याय और नि:शुल्क विधिक सहायता। अनुच्छेद 40: ग्राम पंचायतों का गठन । अनुच्छेद 50: न्यायपालिका को कार्यपालिका से अलग करना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Which agricultural technology was promoted by the Green Revolution against traditional farming?</p>",
                    question_hi: "<p>15. हरित क्रांति द्वारा पारंपरिक खेती के विरुद्ध किस कृषि तकनीक को बढ़ावा दिया गया?</p>",
                    options_en: ["<p>Organic farming</p>", "<p>Labor intensive farming</p>", 
                                "<p>Scientific farming</p>", "<p>indigenous farming</p>"],
                    options_hi: ["<p>जैविक खेती</p>", "<p>श्रम प्रधान खेती</p>",
                                "<p>वैज्ञानिक खेती</p>", "<p>स्वदेशी खेती</p>"],
                    solution_en: "<p><strong>15.(c) Scientific farming. </strong>Organic farming: A practice of the cultivation of crops and rearing of animals without the use of any synthetic farm inputs such as fertilizer and pesticides, but by the use of traditional inputs such as green manure, compost manure, crop rotation. Labor intensive farming: The system of farming under which small farms are cultivated intensively using large inputs of manual labour.</p>",
                    solution_hi: "<p><strong>15.(c) वैज्ञानिक खेती। </strong>जैविक खेती: यह फसलों की खेती और पशुओं को पालने की एक प्रथा है, जिसमें रासायनिक खाद और कीटनाशकों जैसे कृत्रिम कृषि इनपुटस (synthetic farm inputs) का उपयोग नहीं किया जाता है, बल्कि हरी खाद, कम्पोस्ट खाद, फसल चक्र जैसी प्राकृतिक चीजों (traditional inputs) का उपयोग किया जाता है। श्रम प्रधान खेती: यह खेती की वह प्रणाली है जिसके तहत छोटे खेतों पर बहुत अधिक श्रम लगाकर गहन खेती की जाती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Who has the power to grant clemency, relief from punishment, moratorium or remission?</p>",
                    question_hi: "<p>16. क्षमादान देने, सजा में राहत देने, स्थगन देने या छूट देने की शक्ति किसके पास है?</p>",
                    options_en: ["<p>President</p>", "<p>Prime Minister</p>", 
                                "<p>Vice-President</p>", "<p>Speaker</p>"],
                    options_hi: ["<p>राष्ट्रपति</p>", "<p>प्रधान मंत्री</p>",
                                "<p>उप-राष्ट्रपति</p>", "<p>अध्यक्ष (स्पीकर)</p>"],
                    solution_en: "<p><strong>16.(a) President. </strong>Article 72 - Power of President to grant pardons, etc., and to suspend, remit or commute sentences in certain cases. Other Articles related to the president : Article 56 - The President shall hold office for a term of five years. Article 58 - Qualification for election as President. Article 61 - Procedure for impeachment of the president.</p>",
                    solution_hi: "<p>1<strong>6.(a) राष्ट्रपति। </strong>अनुच्छेद 72 - क्षमा आदि देने और कुछ मामलों में सजा को निलंबित करने या कम करने की राष्ट्रपति की शक्ति। राष्ट्रपति से संबंधित अन्य अनुच्छेद : अनुच्छेद 56 - राष्ट्रपति पांच वर्ष की अवधि के लिए पद पर रहेगा। अनुच्छेद 58 - राष्ट्रपति निर्वाचित होने के लिए अर्हताएं । अनुच्छेद 61 - राष्ट्रपति पर महाभियोग चलाने की प्रक्रिया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "17. In which year did Lord Curzon arrive in India as Viceroy?",
                    question_hi: "<p>17. लार्ड कर्जन वायसराय के रूप में किस वर्ष भारत आया था?</p>",
                    options_en: [" 1898 ", " 1891 ", 
                                "<p>1899</p>", "<p>1892</p>"],
                    options_hi: ["<p>1898 में</p>", "<p>1891 में</p>",
                                "<p>1899 में</p>", "<p>1892 में </p>"],
                    solution_en: "<p><strong>17.(c)1899. </strong>Lord Curzon Viceroy between 1899 and 1905. He was the youngest viceroy of India. Partition of Bengal (1905) was announced during his tenure. Significant Acts passed during his tenure: Calcutta Corporation Act, 1899; Indian universities Act, 1904; Ancient monument preservation Act,1904.</p>",
                    solution_hi: "<p><strong>17.(c) 1899.</strong> लॉर्ड कर्जन 1899 और 1905 के बीच वायसराय था। वह भारत के सबसे कम उम्र के वायसराय थे। उनके कार्यकाल के दौरान बंगाल विभाजन (1905) की घोषणा की गई। उनके कार्यकाल के दौरान पारित महत्वपूर्ण अधिनियम: कलकत्ता निगम अधिनियम, 1899; भारतीय विश्वविद्यालय अधिनियम, 1904; प्राचीन स्मारक संरक्षण अधिनियम, 1904।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. In 1882, the headquarters of the Theosophical Society was established in _________.</p>",
                    question_hi: "<p>18. 1882 में, थियोसोफिकल सोसायटी का मुख्यालय _________ में स्थापित किया गया था।</p>",
                    options_en: ["<p>Gwalior</p>", "<p>Calcutta</p>", 
                                "<p>Adyar</p>", "<p>Allahabad</p>"],
                    options_hi: ["<p>ग्वालियर</p>", "<p>कलकत्ता</p>",
                                "<p>अड्यार</p>", "<p>इलाहाबाद</p>"],
                    solution_en: "<p><strong>18.(c) Adyar </strong>near Madras (now Chennai). The Theosophical Society was founded in New York (USA) in 1875 by Madam H.P. Blavatsky and Henry Steel Olcott. They arrived in India and established their headquarters at Adyar in Madras in 1882. Later, Annie Besant took over the leadership of the Society.</p>",
                    solution_hi: "<p><strong>18.(c) अडयार, </strong>मद्रास (अब चेन्नई) के पास । थियोसोफिकल सोसायटी की स्थापना 1875 में मैडम एच.पी. ब्लावात्स्की और हेनरी स्टील ओल्कोट के द्वारा न्यूयॉर्क (अमेरिका) में की गई थी। वे भारत पहुंचे और 1882 में मद्रास के अडयार में अपना मुख्यालय स्थापित किया। बाद में, एनी बेसेंट ने सोसायटी का नेतृत्व संभाला था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. The amphibians of the plant kingdom are ___________ .</p>",
                    question_hi: "<p>19. पादप जगत के उभयचर ____________ हैं।</p>",
                    options_en: ["<p>Pteridophytes</p>", "<p>Bryophytes</p>", 
                                "<p>Algae</p>", "<p>Fungi</p>"],
                    options_hi: ["<p>टेरिडोफाइट्स</p>", "<p>ब्रायोफाइट्स</p>",
                                "<p>शैवाल</p>", "<p>कवक</p>"],
                    solution_en: "<p><strong>19.(b) Bryophytes </strong>are small, non-vascular plants, such as mosses, liverworts, and hornworts. They are amphibious in nature and called &ldquo;amphibians of the plant kingdom\'\' because these plants can live in soil but are dependent on water for sexual reproduction. Pteridophytes: they are the first terrestrial plants to possess vascular tissues &ndash; xylem and phloem. Algae are chlorophyll-bearing, simple, thalloid, autotrophic and largely aquatic (both freshwater and marine) organisms. Fungi are eukaryotic organisms such as yeasts, and mushrooms.</p>",
                    solution_hi: "<p><strong>19.(b) ब्रायोफाइट्स </strong>छोटे, गैर-संवहनी पौधे हैं, जैसे मॉस, लिवरवॉर्ट्स और हॉर्नवॉर्ट्स। वे प्रकृति में उभयचर हैं और उन्हें \"पादप जगत के उभयचर\" कहा जाता है क्योंकि ये पौधे मिट्टी में रह सकते हैं लेकिन यौन प्रजनन के लिए जल पर निर्भर होते हैं। टेरिडोफाइट्स: वे पहले स्थलीय पौधे हैं जिनमें संवहनी ऊतक - जाइलम और फ्लोएम होते हैं। शैवाल क्लोरोफिल युक्त, सरल, थैलॉइड, स्वपोषी और बड़े पैमाने पर जलीय (मीठे पानी और समुद्री दोनों) जीव हैं। कवक यूकेरियोटिक जीव हैं जैसे खमीर और मशरूम।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Which unusual flavor is observed in dairy products due to the formation of ethyl<br>esters catalyzed by esterases produced by lactic acid bacteria?</p>",
                    question_hi: "<p>20. लैक्टिक अम्ल जीवाणु द्वारा उत्पादित एस्टरेस (esterases) द्वारा उत्प्रेरित एथिल एस्टर के निर्माण के<br>कारण डेयरी उत्पादों में कौन सा असामान्य स्वाद महसूस किया जाता है?</p>",
                    options_en: ["<p>Fishy flavor</p>", "<p>Musty flavor</p>", 
                                "<p>Fruity flavor</p>", "<p>Bitty flavor</p>"],
                    options_hi: ["<p>मछली जैसा स्वाद</p>", "<p>बासी स्वाद</p>",
                                "<p>फल का स्वाद</p>", "<p>कड़वा स्वाद</p>"],
                    solution_en: "<p><strong>20.(c) Fruity flavour. </strong>Lactic acid bacteria, such as certain strains of Lactobacillus, are commonly used in the fermentation of dairy products like yogurt and cheese. These bacteria produce esterases, which are enzymes that catalyze the formation of esters. Esters are compounds responsible for various flavors and aromas in foods.</p>",
                    solution_hi: "<p><strong>20.(c) फल का स्वाद । </strong>लैक्टिक एसिड बैक्टीरिया, जैसे लैक्टोबैसिलस के कुछ उपभेद, आमतौर पर दही और पनीर जैसे डेयरी उत्पादों के किण्वन में उपयोग किए जाते हैं। ये बैक्टीरिया एस्टरेज़ का उत्पादन करते हैं, जो ऐसे एंजाइम होते हैं जो एस्टर के निर्माण को उत्प्रेरित करते हैं। एस्टर खाद्य पदार्थों में विभिन्न स्वादों और सुगंधों के लिए उत्तरदायी यौगिक हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. Which of the following does not come under the definition of a census town of India?",
                    question_hi: "21. निम्नलिखित में से क्या, भारत की जनगणना वाले किसी नगर की परिभाषा के अंतर्गत नहीं आता है?",
                    options_en: [" Population density 400 persons per km2", " Presence of Municipality, Municipal Corporation etc.", 
                                " More than 75% of the population engaged in primary sector", " Minimum population of 5000 persons"],
                    options_hi: [" जनसंख्या घनत्व 400 व्यक्ति प्रति km2 ", " नगर पालिका, नगर निगम आदि की उपस्थिति ",
                                " प्राथमिक क्षेत्र में लगी हुई 75% से अधिक जनसंख्या ", " 5000 व्यक्तियों की न्यूनतम जनसंख्या"],
                    solution_en: "21.(c) Criteria of census town - Minimum population of 5,000 persons and at least 75% of the male main working force engaged in non-agricultural activities and population density of at least 400 persons per sq.km. As per the 2011 census, there were 3,784  census towns. Top State-wise distribution of Census Towns : West Bengal-780, Kerala-461, Tamil Nadu-376. A Statutory Town is one with a municipality, corporation, cantonment board or notified town area committee. As per 2011 Census, there are 4,041 of such types of towns.",
                    solution_hi: "21.(c) जनगणना शहर के मानदंड - न्यूनतम जनसंख्या 5,000 व्यक्ति और कम से कम 75% पुरुष मुख्य कार्यबल गैर-कृषि गतिविधियों में लगे हुए और जनसंख्या घनत्व कम से कम 400 व्यक्ति प्रति वर्ग किमी हो। 2011 की जनगणना के अनुसार, 3,784 जनगणना शहर थे। जनगणना शहरों का शीर्ष राज्य-वार वितरण: पश्चिम बंगाल-780, केरल-461, तमिलनाडु-376। एक वैधानिक शहर एक नगर पालिका, निगम, छावनी बोर्ड या अधिसूचित नगर क्षेत्र समिति वाला होता है। 2011 की जनगणना के अनुसार, इस प्रकार के 4,041 शहर हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "22. Which crop is also known as \'golden fiber\'?",
                    question_hi: "<p>22. किस फसल को \'गोल्डन फाइबर\' भी कहा जाता है?</p>",
                    options_en: [" Cotton", " Wheat ", 
                                "<p>Rice</p>", "<p>Jute</p>"],
                    options_hi: ["<p>कपास</p>", "<p>गेहूँ</p>",
                                "<p>चावल</p>", "<p>जूट</p>"],
                    solution_en: "<p><strong>22.(d) Jute, </strong>a stem or bast fibre, is cultivated in West Bengal. Jute cloth is brittle and deteriorates with exposure to sun and rain. It has been popular as inexpensive packaging material. It is a natural fibre with a golden, soft, long, and silky shine. The first jute mill was set up near Kolkata in 1855 at Rishra. Cotton - The most widely used fibre for apparel and home textiles. Cotton fibres are obtained from the seed pod of the cotton plant. Wheat - A Rabi crop. Rice - A Kharif crop.</p>",
                    solution_hi: "<p><strong>22.(d) जूट, </strong>एक तना या बास्ट फाइबर, की खेती पश्चिम बंगाल में की जाती है। जूट का कपड़ा भंगुर होता है और धूप और बारिश के संपर्क में आने से खराब हो जाता है। यह सस्ती पैकेजिंग सामग्री के रूप में लोकप्रिय रही है। यह सुनहरा, मुलायम, लंबा और रेशमी चमक वाला एक प्राकृतिक फाइबर है। पहली जूट मिल 1855 में कोलकाता के पास रिशरा में स्थापित की गई थी। कपास - परिधान और घरेलू वस्त्रों के लिए सबसे व्यापक रूप से उपयोग किया जाने वाला फाइबर है। कपास के रेशे कपास के पौधे की बीज फली से प्राप्त होते हैं। गेहूँ - रबी फसल। चावल - ख़रीफ़ फसल।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. The \'Infant Mortality Rate\' is defined as the number of deaths of infants aged less than<br>one year per _____________ live births in a given year.</p>",
                    question_hi: "<p>23. \'शिशु मृत्यु दर (Infant mortality rate)\' को किसी दिए गए वर्ष में प्रति _________ जीवित जन्मों पर एक वर्ष से कम आयु के शिशुओं की मृत्यु की संख्या के रूप में परिभाषित किया गया है।</p>",
                    options_en: ["<p>5,000</p>", "<p>1,000</p>", 
                                "<p>100</p>", "<p>500</p>"],
                    options_hi: ["<p>5,000</p>", "<p>1,000</p>",
                                "<p>100</p>", "<p>500</p>"],
                    solution_en: "<p><strong>23.(b) 1000.</strong> Infant mortality is the death of an infant before the infant\'s first birthday. The under-five mortality rate refers to the probability a newborn would die before reaching exactly 5 years of age, expressed per 1,000 live births.</p>",
                    solution_hi: "<p><strong>23.(b) 1000.</strong> शिशु मृत्यु दर शिशु के पहले जन्मदिन से पहले उसकी मृत्यु को दर्शाता है। पांच वर्ष से कम उम्र की मृत्यु दर उस संभावना को दर्शाती है कि एक नवजात शिशु ठीक 5 वर्ष की आयु तक पहुंचने से पहले उसकी मृत्यु हो जाती है, जो प्रति 1,000 जीवित जन्मों पर व्यक्त किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. In which of the following styles of temple architecture the spire is called Deul?</p>",
                    question_hi: "<p>24. मंदिर वास्तुकला की निम्नलिखित में से किस शैली में शिखर को देउल कहा जाता है?</p>",
                    options_en: ["<p>Odisha</p>", "<p>Dravid</p>", 
                                "<p>Nagar</p>", "<p>Vesara</p>"],
                    options_hi: ["<p>ओडिशा</p>", "<p>द्रविड़</p>",
                                "<p>नागर</p>", "<p>वेसरा</p>"],
                    solution_en: "<p><strong>24.(a) Odisha. O</strong>disha&rsquo;s temple architecture falls under the Kalinga style of temple architecture, named after the state\'s Kalinga dynasty. Deuls are preceded, as usual, by mandapas called jagamohana in Odisha. The main architectural features of Odisha temples are classified in three orders - Rekha deul, Pidhadeul and Khakra deul. The basic form of the Hindu temple: Sanctum, Mandapa, Shikhar, Vimana, Vahan.</p>",
                    solution_hi: "<p><strong>24.(a) ओडिशा। </strong>ओडिशा की मंदिर वास्तुकला मंदिर वास्तुकला की कलिंग शैली के अंतर्गत आती है, जिसका नाम राज्य के कलिंग राजवंश के नाम पर रखा गया है। देउल के पहले, हमेशा की तरह, मंडप होते हैं जिन्हें ओडिशा में जगमोहन कहा जाता है। ओडिशा के मंदिरों की मुख्य स्थापत्य विशेषताओं को तीन क्रमों में वर्गीकृत किया गया है- रेखा देउल, पिधा देउल और खाकरा देउल । हिंदू मंदिर का मूल स्वरूप: गर्भगृह, मंडप, शिखर, विमान, वाहन ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "25. Which of the following is NOT an amphibian animal?",
                    question_hi: "<p>25. निम्नलिखित में से कौन एक उभयचर प्राणी नहीं है?</p>",
                    options_en: ["<p>Salamander</p>", "<p>Toad</p>", 
                                "<p>Tortoise</p>", "<p>Frog</p>"],
                    options_hi: ["<p>सैलामैंडर</p>", "<p>टोड</p>",
                                "<p>कछुआ</p>", "<p>मेंढक</p>"],
                    solution_en: "<p><strong>25.(c)Tortoise. </strong>Amphibians are small vertebrates that need water, or a moist environment, to survive. The species in this group include frogs, toads, salamanders, and newts. All can breathe and absorb water through their very thin skin. They have special skin glands that produce useful proteins. Turtles and terrapins are reptiles.</p>",
                    solution_hi: "<p><strong>25.(c) कछुआ। </strong>उभयचर छोटे कशेरुकी प्राणी हैं जिन्हें जीवित रहने के लिए जल या नम वातावरण की आवश्यकता होती है। इस समूह की प्रजातियों में मेंढक, टोड, सैलामैंडर और न्यूट्स शामिल हैं। सभी अपनी त्वचा के माध्यम से सांस ले सकते हैं और जल को अवशोषित कर सकते हैं। उनके पास विशेष त्वचा ग्रंथियां होती हैं जो उपयोगी प्रोटीन का उत्पादन करती हैं। कछुआ और टेरापिन सरीसृप हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. Which of the following authors has written the famous novel \'Gulliver\'s Travels\'?",
                    question_hi: "<p>26. निम्नलिखित में से किस लेखक ने प्रसिद्ध उपन्यास \'गुलिवर्स ट्रेवल्स\' (Gulliver\'s Travels) लिखा है?</p>",
                    options_en: ["<p>Oscar Wilde</p>", "<p>Samueal Richardson</p>", 
                                "<p>Jonathan Swift</p>", "<p>Ruskin Bond</p>"],
                    options_hi: ["<p>ऑस्कर वाइल्ड</p>", "<p>सैमुअल रिचर्डसन</p>",
                                "<p>जोनाथन स्विफ्ट</p>", "<p>रस्किन बॉन्ड</p>"],
                    solution_en: "<p><strong>26.(c) Jonathan Swift. </strong>His other books: &ldquo;A Modest Proposal&rdquo;, &ldquo;A Tale of a Tub \'\', &ldquo;The Battle of the Books&rdquo;. Authors and books: Oscar Wilde - &ldquo;The Picture of Dorian Gray&rdquo;, &ldquo;The Happy Prince&rdquo;. Samueal Richardson - &ldquo;The History of Sir Charles Grandison&rdquo;. Ruskin Bond - &ldquo;The Blue Umbrella&rdquo; , &ldquo;The Room on the Roof&rdquo;.</p>",
                    solution_hi: "<p><strong>26.(c) जोनाथन स्विफ़्ट। </strong>उनकी अन्य पुस्तकें: \"ए मॉडेस्ट प्रपोज़ल\", \"ए टेल ऑफ़ ए टब\", \"द बैटल ऑफ़ द बुक्स\"। लेखक एवं पुस्तकें: ऑस्कर वाइल्ड - \"द पिक्चर ऑफ़ डोरियन ग्रे\", \"द हैप्पी प्रिंस\"। सैमुअल रिचर्डसन - \" द हिस्ट्री ऑफ सर चार्ल्स ग्रैंडिसन\"। रस्किन बॉन्ड - \"द ब्लू अम्ब्रेला\", \"द रूम ऑन द रूफ\"।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Among the various sources of Aryan culture, ________ are prominent.</p>",
                    question_hi: "<p>27. आर्य संस्कृति के विभिन्न स्रोतों में ________ प्रमुख हैं।</p>",
                    options_en: ["<p>ritual texts</p>", "<p>Monument</p>", 
                                "<p>inscription</p>", "<p>archaeological artifacts</p>"],
                    options_hi: ["<p>अनुष्ठान ग्रंथ</p>", "<p>स्मारक</p>",
                                "<p>शिलालेख</p>", "<p>पुरातात्विक कलाकृतियां</p>"],
                    solution_en: "<p><strong>27.(a) Ritual texts. A</strong>ryan is a designation originally meaning &ldquo;civilized&rdquo;, &ldquo;noble&rdquo;, or &ldquo;free&rdquo; without reference to any ethnicity. It was first applied as a self-identifying term by a migratory group of people from Central Asia later known as Indo-Iranians (who settled on the Iranian Plateau) and, later, applied to Indo-Aryans (who traveled south to settle in northern India).</p>",
                    solution_hi: "<p><strong>27.(a) अनुष्ठान ग्रंथ। </strong>आर्यन एक पदनाम है जिसका मूल अर्थ बिना किसी जातीयता के संदर्भ के \"सभ्य\", \"कुलीन\" या \"स्वतंत्र\" है। इसे सबसे पहले मध्य एशिया के लोगों के एक प्रवासी समूह द्वारा एक स्व-पहचान शब्द के रूप में लागू किया गया था, जिन्हें बाद में इंडो-ईरानी (जो ईरानी पठार पर बस गए) के रूप में जाना जाता था और बाद में, इसे इंडो-आर्यन (जिन्होंने उत्तरी भारत में बसने के लिए दक्षिण की यात्रा की) के लिए लागू किया।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who was the author of Badshah Nama?</p>",
                    question_hi: "<p>28. बादशाह नामा के लेखक कौन थे ?</p>",
                    options_en: ["<p>Sadullah Khan</p>", "<p>Abul Fazal</p>", 
                                "<p>Abdul Hameed Lahori</p>", "<p>Bairam Khan</p>"],
                    options_hi: ["<p>सादुल्लाह खान</p>", "<p>अबुल फजल</p>",
                                "<p>अब्दुल हमीद लाहौरी</p>", "<p>बैरम खान</p>"],
                    solution_en: "<p><strong>28.(c) Abdul Hamid Lahori </strong>(A pupil of Abu\'l Fazl) was court historian of Shah Jahan. Other famous Authors and Books: Abul Fazl - &ldquo;Akbarnama&rdquo;, Abdul Qadir Badauni - &ldquo;Muntakhab-ut-Tawarikh&rdquo;, Ziauddin Barani - &ldquo;Tarikh-I Firoz Shahi&rdquo;. Sadullah Khan was Shah Jahan&rsquo;s wazir.</p>",
                    solution_hi: "<p>28.(c) <strong>अब्दुल हमीद लाहौरी</strong> (अबूल फज़ल का शिष्य) शाहजहाँ का दरबारी इतिहासकार था। अन्य प्रसिद्ध लेखक एवं पुस्तकें: अबुल फज़ल - \"अकबरनामा\", अब्दुल कादिर बदायूँनी - \"मुंतखब-उत-तवारीख\", ज़ियाउद्दीन बरनी - \"तारीख-ए फ़िरोज़ शाही\"। सादुल्लाह खान शाहजहाँ का वजीर था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who was the author of \'Raag Darbari\'?</p>",
                    question_hi: "<p>29. \'राग दरबारी\' के लेखक कौन थे?</p>",
                    options_en: ["<p>Dharamvir Bharati</p>", "<p>Suryakant Tripathi \'Nirala\'</p>", 
                                "<p>Shrilal Shukla</p>", "<p>Harivansh Rai Bachchan</p>"],
                    options_hi: ["<p>धर्मवीर भारती</p>", "<p>सूर्यकांत त्रिपाठी \'निराला\'</p>",
                                "<p>श्रीलाल शुक्ल</p>", "<p>हरिवंश राय बच्चन</p>"],
                    solution_en: "<p><strong>29.(c) Shrilal Shukla. </strong>His other books: &ldquo;Suni Ghati Ka Suraj&rdquo;, &ldquo;Makaan&rdquo;, &ldquo;Aadami Ka Jahar&rdquo;. Other famous Authors and Books: Dharamvir Bharati - &ldquo;Gunahon Ka Devta&rdquo;, &ldquo;Thanda Loha&rdquo;; Suryakant Tripathi - &ldquo;Kukurmutta&rdquo;; Harivansh Rai Bachchan - &ldquo;Madhushala&rdquo;, &ldquo;Need Ka Nirman Phir&rdquo;.</p>",
                    solution_hi: "<p><strong>29.(c) श्रीलाल शुक्ल।</strong> उनकी अन्य पुस्तकें: &ldquo;सूनी घाटी का सूरज&rdquo;, &ldquo;मकान&rdquo;, &ldquo;आदमी का जहर&rdquo;। अन्य प्रसिद्ध लेखक एवं पुस्तकें: धर्मवीर भारती - \"गुनाहों का देवता\", \"ठंडा लोहा\"; सूर्यकान्त त्रिपाठी - कुकुरमुत्ता\"; हरिवंश राय बच्चन - \"मधुशाला\", &ldquo;नीड़ का निर्माण फिर&rdquo;।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. What is 25-Ponders with reference to the celebration at the Republic Day Parade?</p>",
                    question_hi: "<p>30. गणतंत्र दिवस परेड में उत्सव के संदर्भ में 25-पॉन्डर्स क्या है?</p>",
                    options_en: ["<p>March-past by the Cavalry</p>", "<p>Salute by seven canons of the Indian Army</p>", 
                                "<p>Salute to the National Flag by the President\'s bodyguards</p>", "<p>Presentation of the tableaus from different states</p>"],
                    options_hi: ["<p>घुड़सवार सेना द्वारा मार्च पास्ट</p>", "<p>भारतीय सेना की सात तोपों की सलामी</p>",
                                "<p>राष्ट्रपति के अंगरक्षकों द्वारा राष्ट्रीय ध्वज को सलामी</p>", "<p>विभिन्न राज्यों की झांकियों की प्रस्तुति</p>"],
                    solution_en: "<p><strong>30.(b) Salute by seven canons of the Indian Army.</strong> When the Republic Day parade starts on 26th January with the arrival of the President, the cavalier bodyguards of the President salute the National Flag and the National Anthem is played but the 21 Guns Salute is not done by firing 21 canons. Instead, 7- canons of the Indian army are used. These canons are known as &ldquo;25- Ponders&rdquo;.</p>",
                    solution_hi: "<p><strong>30.(b) भारतीय सेना की सात तोपों की सलामी।</strong> 26 जनवरी को जब राष्ट्रपति के आगमन के साथ गणतंत्र दिवस परेड शुरू होती है, तो राष्ट्रपति के घुड़सवार अंगरक्षक राष्ट्रीय ध्वज को सलामी देते हैं और राष्ट्रीय गान बजाया जाता है, लेकिन 21 तोपों की सलामी 21 तोपों को फायर करके नहीं की जाती है। इसके स्थान पर भारतीय सेना की 7-तोपों का प्रयोग किया जाता है। इन कैनन को \"25-पॉन्डर्स\" के नाम से जाना जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Amaravati Stupa is located in which of these states?</p>",
                    question_hi: "<p>31. अमरावती स्तूप निम्न में से किस राज्य में स्थित है?</p>",
                    options_en: ["<p>West Bengal</p>", "<p>Andhra Pradesh</p>", 
                                "<p>Gujarat</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>पश्चिम बंगाल</p>", "<p>आंध्र प्रदेश</p>",
                                "<p>गुजरात</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p><strong>31.(b) Andhra Pradesh. </strong>Amaravati Stupa: A ruined Buddhist stupa, it was built by Ashoka. The site is under the protection of the Archaeological Survey of India. It has a brick built circular Vedika or drum with projecting rectangular Ayaka platforms in four cardinal directions measuring 7.20 X 2.10 meters each.</p>",
                    solution_hi: "<p><strong>31.(b) आंध्र प्रदेश। </strong>अमरावती स्तूप: एक खंडहर बौद्ध स्तूप, इसे अशोक ने बनवाया था। यह स्थल भारतीय पुरातत्व सर्वेक्षण के संरक्षण में है। इसमें ईंट से निर्मित गोलाकार वेदिका या ड्रम है जिसमें चार प्रमुख दिशाओं में 7.20 X 2.10 मीटर मापन वाले आयताकार अयाका मंच हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which female singer got the \'Best Playback Singer (Female)\' award at the Filmfare<br>Awards 2022?</p>",
                    question_hi: "<p>32. फिल्मफेयर अवार्ड्स 2022 में किस गायिका को \'सर्वश्रेष्ठ पार्श्व गायिका\' पुरस्कार मिला?</p>",
                    options_en: ["<p>Shreya Ghoshal</p>", "<p>Priya Sariya</p>", 
                                "<p>Neha Kakkar</p>", "<p>Asees Kaur</p>"],
                    options_hi: ["<p>श्रेया घोषाल</p>", "<p>प्रिया सरिया</p>",
                                "<p>नेहा कक्कड़</p>", "<p>असीस कौर</p>"],
                    solution_en: "<p><strong>32.(d) Asees Kaur. </strong>Her Awards - Filmfare Award (2021, 2022 for Shershaah film), Mirchi Music Award (2017). The Filmfare awards were introduced in 1954. 69th Filmfare Awards 2024: Best Film -12TH Fail; Best Director - Vidhu Vinod Chopra; Best Female Actor In A Leading Role - Alia Bhatt and (Male) - Ranbir Kapoor; Best Female Playback Singer - Shilpa Rao; Best Male Playback Singer - Bhupinder Babbal.</p>",
                    solution_hi: "<p><strong>32.(d) असीस कौर। </strong>पुरस्कार - फिल्मफेयर पुरस्कार (शेरशाह फिल्म के लिए 2021, 2022), मिर्ची म्यूजिक अवार्ड (2017)। फ़िल्मफ़ेयर पुरस्कारों की शुरुआत 1954 में हुई थी। 69वें फ़िल्मफ़ेयर पुरस्कार 2024: सर्वश्रेष्ठ फ़िल्म -12वीं फ़ेल; सर्वश्रेष्ठ निर्देशक - विधु विनोद चोपड़ा; अग्रणी भूमिका में सर्वश्रेष्ठ महिला अभिनेता - आलिया भट्ट और (पुरुष) - रणबीर कपूर; सर्वश्रेष्ठ महिला पार्श्व गायिका - शिल्पा राव; सर्वश्रेष्ठ पुरुष पार्श्व गायक - भूपिंदर बब्बल।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following is related to a glacial landform?</p>",
                    question_hi: "<p>33. निम्नलिखित में से किसका संबंध किसी हिमनदीय स्थलरूप से है?</p>",
                    options_en: ["<p>Alluvial fan</p>", "<p>Playas</p>", 
                                "<p>Yardangs</p>", "<p>Horns</p>"],
                    options_hi: ["<p>जलोढ़ पंखा (Alluvial fan)</p>", "<p>प्लाया (Playas)</p>",
                                "<p>यारडांग (Yardangs)</p>", "<p>हॉर्न (Horns)</p>"],
                    solution_en: "<p><strong>33.(d) Horns a</strong>re pointed peaks that are bounded on at least three sides by glaciers. Most of today\'s glacial landforms were created by the movement of large ice sheets during the Quaternary glaciations. Glacial Landforms - Erosional (Cirque or Corrie, Glacial Valley, Arete, Horn) and Depositional (Esker, Drumlin, Moraine). Playas and Yardangs Landforms are formed by Wind.</p>",
                    solution_hi: "<p><strong>33.(d) हार्न </strong>हिमनदों से कम से कम तीन तरफ से घिरे हुए नुकीले शिखर होते हैं। अधिकांश वर्तमान हिमनद स्थलरूपों का निर्माण चतुर्धातुक हिमनदों के दौरान बड़ी बर्फ की चादरों की गति से हुआ था। हिमनद भू-आकृतियाँ - अपरदनात्मक (सिर्क या कोरी, हिमनद घाटी, अरेटे, हॉर्न) और निक्षेपणात्मक (एस्कर, ड्रमलिन, मोराइन)। प्लायास और यार्डैंग्स पवन द्वारा निर्मित भू-आकृतियाँ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following Constitutional Amendment Acts made it a fundamental duty of<br>parents to educate their child?</p>",
                    question_hi: "<p>34. निम्नलिखित में से किस संविधान संशोधन अधिनियम ने अपने बच्चे को शिक्षित करना माता-पिता का<br>मौलिक कर्तव्य बना दिया है?</p>",
                    options_en: ["<p>88th Amendment Act</p>", "<p>87th Amendment Act</p>", 
                                "<p>86th Amendment Act</p>", "<p>85th Amendment Act</p>"],
                    options_hi: ["<p>88 वां संशोधन अधिनियम</p>", "<p>87 वां संशोधन अधिनियम</p>",
                                "<p>86 वां संशोधन अधिनियम</p>", "<p>85 वां संशोधन अधिनियम</p>"],
                    solution_en: "<p><strong>34.(c) 86th Amendment Act</strong> (2002): Provided the right to education as a fundamental right. A new article 21A was added which made the right to education a fundamental right for children between 6-14 years. Changes in Article 45 of the Directive Principle of State Policies were made. It made education for all children below 6 years a Directive Principle of State Policies. It made the opportunities for education to the child a Fundamental Duty {Article 51A (k)} of parents of children.</p>",
                    solution_hi: "<p>3<strong>4.(c) 86वां संशोधन अधिनियम (2002):</strong> शिक्षा के अधिकार को मौलिक अधिकार के रूप में प्रदान किया गया। एक नया अनुच्छेद 21A जोड़ा गया जिसने 6-14 वर्ष के बच्चों के लिए शिक्षा के अधिकार को मौलिक अधिकार बना दिया। राज्य के नीति निर्देशक सिद्धांत के अनुच्छेद 45 में परिवर्तन किये गये। इसने 6 वर्ष से कम उम्र के सभी बच्चों के लिए शिक्षा को राज्य नीतियों का निदेशक सिद्धांत बना दिया। इसने बच्चों को शिक्षा के अवसर देना बच्चों के माता-पिता का मौलिक कर्तव्य {अनुच्छेद 51A (K)} बना दिया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. \'Samagra Shiksha\', a scheme in the area of education subsumes the earlier schemes<br />pertaining to _____________. ",
                    question_hi: "<p>35. शिक्षा के क्षेत्र में एक योजना \'समग्र शिक्षा\', _____________ से संबंधित पहले की योजनाओं को समाहित करती है।</p>",
                    options_en: [" Sarva Shiksha Abhiyan, Rashtriya Madhyamik Shiksha Abhiyan and Teacher<br />      Education only ", "<p>Sarva Shiksha Abhiyan and Rashtriya Madhyamik Shiksha Abhiyan only</p>", 
                                "<p>Sarva Shiksha Abhiyan only</p>", "<p>Sarva Shiksha Abhiyan, Teacher Education and Rashtria Avishkar Abhiyan only</p>"],
                    options_hi: ["<p>केवल सर्व शिक्षा अभियान, राष्ट्रीय माध्यमिक शिक्षा अभियान और शिक्षक शिक्षा</p>", "<p>केवल सर्व शिक्षा अभियान और राष्ट्रीय माध्यमिक शिक्षा अभियान</p>",
                                "<p>केवल सर्व शिक्षा अभियान</p>", "<p>केवल सर्व शिक्षा अभियान, शिक्षक शिक्षा और राष्ट्रीय आविष्कार अभियान</p>"],
                    solution_en: "<p>35.(a) Samagra Shiksha (Launched - 2018-19) - An overarching programme for the school education sector extending from pre-school to class 12. Major Features: Support for Rashtriya Avishkar Abhiyan to promote Science and Maths learning in schools; Focus on Quality of Education; Focus on Digital Education and Focus on Girl Education.</p>",
                    solution_hi: "<p>35.(a) समग्र शिक्षा (लॉन्च - 2018-19) - प्री-स्कूल से लेकर कक्षा 12 तक स्कूली शिक्षा क्षेत्र के लिए एक व्यापक कार्यक्रम। प्रमुख विशेषताएं: स्कूलों में विज्ञान और गणित सीखने को बढ़ावा देने के लिए राष्ट्रीय अविष्कार अभियान के लिए समर्थन; शिक्षा की गुणवत्ता पर ध्यान दें; डिजिटल शिक्षा पर फोकस और बालिका शिक्षा पर फोकस।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. In which year was the Right to Education (RTE) Act implemented?</p>",
                    question_hi: "<p>36. शिक्षा का अधिकार (RTE) अधिनियम किस वर्ष लागू किया गया था?</p>",
                    options_en: ["<p>2009</p>", "<p>2010</p>", 
                                "<p>2015</p>", "<p>2012</p>"],
                    options_hi: ["<p>2009</p>", "<p>2010</p>",
                                "<p>2015</p>", "<p>2012</p>"],
                    solution_en: "<p><strong>36.(b) 2010. Th</strong>e Right to Education (RTE) Act enacted on 4 August 2009, describes the modalities of the importance of free and compulsory education for children between 6 and 14 in India mentioned under Article 21a of the Indian Constitution&rsquo;. Provisions: It provides for appointment of appropriately trained teachers, i.e. teachers with the requisite entry and academic qualifications; etc.</p>",
                    solution_hi: "<p><strong>36.(b) 2010. </strong>4 अगस्त 2009 को अधिनियमित शिक्षा का अधिकार (RTE) अधिनियम, भारतीय संविधान के अनुच्छेद 21a के तहत भारत में 6 से 14 वर्ष के बच्चों के लिए मुफ्त और अनिवार्य शिक्षा के महत्व के तौर-तरीकों का वर्णन करता है। प्रावधान: यह उचित रूप से प्रशिक्षित शिक्षकों, यानी अपेक्षित प्रवेश और शैक्षणिक योग्यता वाले शिक्षकों की नियुक्ति का प्रावधान करता है; इत्यादि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. India\'s first women\'s organization, Bharat Stree Mahamandal, was founded by ___________.</p>",
                    question_hi: "<p>37. भारत का पहला महिला संगठन, भारत स्त्री महामंडल की स्थापना ___________ के द्वारा की गई थी।</p>",
                    options_en: ["<p>Annie Besant</p>", "<p>Sarala Devi Choudharani</p>", 
                                "<p>Sarojini Naidu</p>", "<p>Savitribai Phule</p>"],
                    options_hi: ["<p>एनी बेसेंट</p>", "<p>सरला देवी चौधरानी</p>",
                                "<p>सरोजिनी नायडू</p>", "<p>सावित्रीबाई फुले</p>"],
                    solution_en: "<p><strong>37.(b) Sarala Devi Chaudhurani.</strong> She played an important role during the Swadeshi Movement. Bharat Stree Mahamandal was founded in 1910. Aim: To promote female education. Annie Besant became the president of Theosophical Society in 1907, headquartered in Adyar, Madras (Chennai). Sarojini Naidu was appointed the President of the Indian National Congress in 1925 and later became the Governor of the United Provinces in 1947.</p>",
                    solution_hi: "<p><strong>37.(b) सरला देवी चौधुरानी ।</strong> उन्होंने स्वदेशी आंदोलन के दौरान महत्वपूर्ण भूमिका निभाई। भारत स्त्री महामंडल की स्थापना 1910 में हुई। उद्देश्य: महिला शिक्षा को बढ़ावा देना। एनी बेसेंट 1907 में थियोसोफिकल सोसाइटी की अध्यक्ष बनीं, जिसका मुख्यालय अड्यार, मद्रास (चेन्नई) में था। सरोजिनी नायडू को 1925 में भारतीय राष्ट्रीय कांग्रेस का अध्यक्ष नियुक्त किया गया और बाद में 1947 में संयुक्त प्रांत की राज्यपाल बनीं थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following is a famous folk dance of Bihar?</p>",
                    question_hi: "<p>38. निम्न में से कौन सा बिहार का एक प्रसिद्ध लोकनृत्य है?</p>",
                    options_en: ["<p>Jhijhiya</p>", "<p>Naga dance</p>", 
                                "<p>Maharas</p>", "<p>Khel gopal</p>"],
                    options_hi: ["<p>झिझिया</p>", "<p>नागा नृत्य</p>",
                                "<p>महारास</p>", "<p>खेल गोपाल</p>"],
                    solution_en: "<p>38.(a) <strong>Jhijhiya </strong>- It was performed in the mythological town of Mithila, by women only. The whole Navaratri &lsquo;nine nights&rsquo; festival is celebrated to worship the three forms of Lord Lakshmi, Parvati, and Saraswati. Other folk dances of Bihar: Jat- Jatin, Jhumeri, Karma and Magahi Jhumar Dance.</p>",
                    solution_hi: "<p>38.(a)<strong> झिझिया - </strong>यह पौराणिक नगरी मिथिला में केवल महिलाओं द्वारा किया जाता था। संपूर्ण नवरात्रि \'नौ रातों\' का उत्सव भगवान लक्ष्मी, पार्वती और सरस्वती के तीन रूपों की पूजा करने के लिए मनाया जाता है। बिहार के अन्य लोक नृत्य: जट-जतिन, झुमेरी, करमा और मगही झूमर नृत्य।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "39.  Who among the following is associated with Kathak?",
                    question_hi: "<p>39. निम्नलिखित में से कौन कथक से संबंधित है?</p>",
                    options_en: [" Kalamandalam Krishnan Nair ", " Nahid Siddiqui ", 
                                "<p>V Satyanarayana Sarma</p>", "<p>Anokhelal Mishra</p>"],
                    options_hi: ["<p>कलामंडलम कृष्णन नायर</p>", "<p>नाहिद सिद्दीक़ी</p>",
                                "<p>वी सत्यनारायण शर्मा</p>", "<p>अनोखेलाल मिश्रा</p>"],
                    solution_en: "<p><strong>39.(b) Nahid Siddiqui. </strong>Kathak is the classical dance form of Uttar Pradesh. The word Kathak has been derived from the word Katha which means a story. Other famous exponents of Kathak : Pandit Birju Maharaj, Lachu Maharaj, Shambu Maharaj, Shovna Narayan and Sitara Devi. Kalamandalam Krishnan Nair, the Mozart of Kathakali. Vedantam Satyanarayana Sarma - Kuchipudi dancer. </p>",
                    solution_hi: "<p><strong>39.(b) नाहिद सिद्दीकी। </strong>कथक उत्तर प्रदेश का शास्त्रीय नृत्य है। कथक शब्द की उत्पत्ति कथा शब्द से हुई है जिसका अर्थ है कहानी। कथक के अन्य प्रसिद्ध प्रतिपादक: पंडित बिरजू महाराज, लच्छू महाराज, शंभू महाराज, शोवना नारायण और सितारा देवी। कलामंडलम कृष्णन नायर, कथकली के मोजार्ट। वेदांतम सत्यनारायण सरमा - कुचिपुड़ी नर्तक।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "40. Which of the following Rashtrakuta rulers defeated Dharmapala of the Pala dynasty?",
                    question_hi: "<p>40. पाल वंश के धर्मपाल को निम्नलिखित में से किस राष्ट्रकूट शासक ने पराजित किया था?</p>",
                    options_en: [" Amoghavarsha II", " Govind II", 
                                "<p>Krishna I</p>", "<p>Dhruv</p>"],
                    options_hi: ["<p>अमोघवर्ष द्वितीय</p>", "<p>गोविंद द्वितीय</p>",
                                "<p>कृष्ण प्रथम</p>", "<p>ध्रुव</p>"],
                    solution_en: "<p><strong>40.(d) Dhruv. </strong>He assumed the titles: Nirupama, Kali-vallabha, Dharavarsha, Shrivallabha. Rashtrakuta Empire: Founder - Dantidurga, fixed his capital at Manyakheta or Malkhed near modern Sholapur. The Pala empire was founded by Gopala. In the tripartite struggle, Dharmapala (the Pala king) was defeated by Vatsaraja (Gurjar pratihara king), who was then defeated by Dhruv (the Rashtrakuta king).</p>",
                    solution_hi: "<p>40.(d) <strong>ध्रुव</strong>। उन्होंने उपाधियाँ धारण कीं: निरुपमा, काली-वल्लभ, धारावर्ष, श्रीवल्लभ। राष्ट्रकूट साम्राज्य: संस्थापक - दंतिदुर्ग, ने अपनी राजधानी आधुनिक शोलापुर के निकट मान्यखेता या मालखेड में तय की। पाल साम्राज्य की स्थापना गोपाल ने की थी। त्रिपक्षीय संघर्ष में, धर्मपाल (पाल राजा) को वत्सराज (गुर्जर प्रतिहार राजा) ने हराया था, जिसे बाद में ध्रुव (राष्ट्रकूट राजा) ने हराया था।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following dancers learned ballet at the request of the famous ballerina Anna Pavlova?</p>",
                    question_hi: "<p>41. निम्नलिखित में से किस नृत्यांगना ने प्रसिद्ध बैलेरीना अन्ना पावलोवा (Anna Pavlova) के अनुरोध पर<br>बैले सीखा?</p>",
                    options_en: ["<p>Sitara Devi</p>", "<p>Rukmini Devi Arundale</p>", 
                                "<p>Sonal Mansingh</p>", "<p>Yamini Krishnamurthy</p>"],
                    options_hi: ["<p>सितारा देवी</p>", "<p>रुक्मिणी देवी अरुंडेल</p>",
                                "<p>सोनल मानसिंह</p>", "<p>यामिनी कृष्णमूर्ति</p>"],
                    solution_en: "<p>41.(b) <strong>Rukmini Devi Arundale</strong> was a dancer and choreographer of Bharatnatyam. She was the first woman to be nominated to the Rajya Sabha. She established the Kalakshetra Foundation at Adyar (Chennai) in 1936. Her Awards: Padma Bhushan (1956) and Sangeet Natak Akademi Fellowship (1967).</p>",
                    solution_hi: "<p>41.(b) <strong>रुक्मिणी देवी अरुंडेल </strong>भरतनाट्यम की नृत्यांगना और कोरियोग्राफर थीं। वह राज्यसभा के लिए नामांकित होने वाली पहली महिला थीं। उन्होंने 1936 में अड्यार (चेन्नई) में कलाक्षेत्र फाउंडेशन की स्थापना की। पुरस्कार: पद्म भूषण (1956) और संगीत नाटक अकादमी फ़ेलोशिप (1967)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. In which of the following years was Sati, the practice of widow-burning, banned in<br>India ?</p>",
                    question_hi: "<p>42. निम्नलिखित में से किस वर्ष में भारत में विधवाओं को जला देने की प्रथा, सती-प्रथा पर प्रतिबंध लगा दिया<br>गया था?</p>",
                    options_en: ["<p>1875</p>", "<p>1819</p>", 
                                "<p>1897</p>", "<p>1829</p>"],
                    options_hi: ["<p>1875</p>", "<p>1819</p>",
                                "<p>1897</p>", "<p>1829</p>"],
                    solution_en: "<p><strong>42.(d) 1829. </strong>Sati was a historical practice in which a widow sacrifices herself by sitting atop her deceased husband\'s funeral pyre. Governor-General of India Lord William Bentinck passed Bengal Sati Regulation 1829 which made the practice of Sati illegal in all of British India. Sati was banned with the efforts of Raja Ram Mohan Roy.</p>",
                    solution_hi: "<p>42.(d) <strong>1829</strong>. सती एक ऐतिहासिक प्रथा थी जिसमें एक विधवा अपने मृत पति की चिता पर बैठकर खुद को बलिदान कर देती थी। भारत के गवर्नर-जनरल लॉर्ड विलियम बेंटिक ने बंगाल सती विनियमन 1829 पारित किया, जिसने पूरे ब्रिटिश भारत में सती प्रथा को अवैध बना दिया। राजा राम मोहन राय के प्रयासों से सती प्रथा पर रोक लगायी गयी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "43. The thickness of the continental crust beneath the Central Himalayas is of the order of _____, marking the end of volcanic activity in the region.",
                    question_hi: "<p>43. मध्य हिमालय के नीचे महाद्वीपीय भूपर्पटी की मोटाई ___________ की कोटि की है, जो इस क्षेत्र में ज्वालामुखी गतिविधि के अंत को चिह्रित करती है।</p>",
                    options_en: ["<p>35-37 km</p>", "<p>60-65 km</p>", 
                                "<p>50-63 km</p>", "<p>70-72 km</p>"],
                    options_hi: ["<p>35-37 km</p>", "<p>60-65 km</p>",
                                "<p>50-63 km</p>", "<p>70-72 km</p>"],
                    solution_en: "<p><strong>43.(d) 70-72 km. </strong>The thickening of the continental crust marked the end of volcanic activity in the central Himalayan region as any magma moving upwards would solidify before it could reach the surface. The Himalayas are still rising by more than 1 cm per year as India continues to move northwards into Asia. The structure of the earth is divided into three major components: the crust, the mantle, the core.</p>",
                    solution_hi: "<p><strong>43.(d) 70-72 km. </strong>महाद्वीपीय परत के मोटे होने से मध्य हिमालय क्षेत्र में ज्वालामुखी गतिविधि का अंत हो गया क्योंकि ऊपर की ओर बढ़ने वाला कोई भी मैग्मा सतह तक पहुंचने से पहले ही जम जाता था। हिमालय अभी भी प्रति वर्ष 1 सेमी से अधिक बढ़ रहा है क्योंकि भारत एशिया में उत्तर की ओर बढ़ रहा है। पृथ्वी की संरचना तीन प्रमुख घटकों में विभाजित है: क्रस्ट, मेंटल, कोर।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "44. The Iron Pillar in Mehrauli was built by the rulers of which of the following dynasties?",
                    question_hi: "<p>44. महरौली में लौह स्तंभ का निर्माण निम्नलिखित में से किस राजवंश के शासकों द्वारा करवाया गया था?</p>",
                    options_en: [" Moukhari", "<p>Gupta</p>", 
                                "<p>Kushan</p>", "<p>Maurya</p>"],
                    options_hi: ["<p>मौखरी</p>", "<p>गुप्त</p>",
                                "<p>कुषाण</p>", "<p>मौर्य</p>"],
                    solution_en: "<p><strong>44.(b) Gupta. </strong>Mehrauli Iron Pillar inscription describes the military exploits of Gupta king Chandragupta II. It is famous for its non-rusted state. The Gupta dynasty was established by Shri Gupta in the 3rd century AD. Chandragupta II was the first Gupta ruler to issue silver coins which were called Rupaka. He was the son of Samudragupta and also known as the Chandragupta Vikramaditya.</p>",
                    solution_hi: "<p><strong>44.(b) गुप्ता। </strong>मेहरौली लौह स्तंभ का शिलालेख गुप्त राजा चंद्रगुप्त द्वितीय के सैन्य कारनामों का वर्णन करता है। यह अपनी जंग-रोधक अवस्था के लिए प्रसिद्ध है। गुप्त वंश की स्थापना श्रीगुप्त द्वारा तीसरी शताब्दी ईस्वी में की गई थी। चंद्रगुप्त द्वितीय रुपक नामक रजत सिक्के जारी करने वाले प्रथम गुप्त शासक थे। वह समुद्रगुप्त के पुत्र थे और चंद्रगुप्त विक्रमादित्य के नाम से भी जाने जाते थे।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following measures is NOT related to the tax reform measures<br>undertaken during the liberalization phase?</p>",
                    question_hi: "<p>45. निम्नलिखित में से कौन सा उपाय उदारीकरण चरण के दौरान किए गए कर सुधार उपायों से संबंधित नहीं है ?</p>",
                    options_en: ["<p>Reduction in rates of income tax</p>", "<p>Devaluation of Rupee</p>", 
                                "<p>Reforms related to indirect taxes</p>", "<p>Simplification of tax procedures</p>"],
                    options_hi: ["<p>आयकर की दरों में कमी</p>", "<p>रुपये का अवमूल्यन</p>",
                                "<p>अप्रत्यक्ष करों से संबंधित सुधार</p>", "<p>कर प्रक्रियाओं का सरलीकरण</p>"],
                    solution_en: "<p><strong>45.(b) Devaluation of Rupee</strong>. The first phase of liberalization in India began in 1991 under the regime of Prime Minister P.V Narasimha Rao. Goal - Making the economy more market and service-oriented and expanding the role of private and foreign investment. Changes - Increased foreign investment, lower import tariffs, deregulation of markets, and lower taxes.</p>",
                    solution_hi: "<p><strong>45.(b) रुपये का अवमूल्यन।</strong> भारत में उदारीकरण का पहला चरण 1991 में प्रधान मंत्री पी.वी नरसिम्हा राव के कार्यकाल में शुरू हुआ। लक्ष्य - अर्थव्यवस्था को अधिक बाजार और सेवा-उन्मुख बनाना और निजी और विदेशी निवेश की भूमिका का विस्तार करना। परिवर्तन - विदेशी निवेश में वृद्धि, कम आयात शुल्क, बाज़ारों का विनियमन और कम कर।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "46. Which of the following is the mascot of the 37th National Games?",
                    question_hi: "<p>46. निम्नलिखित में से कौन-सा 37वें राष्ट्रीय खेलों का शुभंकर है?</p>",
                    options_en: [" Moga", "<p>Ollie</p>", 
                                "<p>Savaj</p>", "<p>Bhima</p>"],
                    options_hi: ["<p>मोगा</p>", "<p>ऑली</p>",
                                "<p>सावज</p>", "<p>भीमा</p>"],
                    solution_en: "<p><strong>46.(a) Moga. </strong>The mascot is a representation of Indian Bison. The word Moga is derived from Konkani language, meaning Love. The 37th National Games were held in five cities of Goa - Mapusa, Margao, Panjim, Ponda and Vasco. Motto - &ldquo;Get Set Goa&rdquo;. Maharashtra topped the medals tally with 228 medals. The 38th National Games (2024) will be held in the state of Uttarakhand.</p>",
                    solution_hi: "<p><strong>46.(a) मोगा।</strong> शुभंकर भारतीय बाइसन का प्रतिनिधित्व करता है। मोगा शब्द कोंकणी भाषा से लिया गया है, जिसका अर्थ प्रेम होता है। 37वें राष्ट्रीय खेल गोवा के पांच शहरों - मापुसा, मडगांव, पणजी, पोंडा और वास्को में आयोजित किए गए थे। आदर्श वाक्य - \"गेट सेट गोवा\"। महाराष्ट्र 228 पदकों के साथ पदक तालिका में शीर्ष पर है। 38वें राष्ट्रीय खेल (2024) उत्तराखंड राज्य में आयोजित किये जायेंगे।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which month marks the approximate arrival of the monsoon in India?</p>",
                    question_hi: "<p>47. भारत में मानसून का अनुमानित आगमन कौन-से महीने में होता है?</p>",
                    options_en: ["<p>Early January</p>", "<p>Early June</p>", 
                                "<p>Early February</p>", "<p>Early October</p>"],
                    options_hi: ["<p>जनवरी के आरंभ में</p>", "<p>जून के आरंभ में</p>",
                                "<p>फरवरी के आरंभ में</p>", "<p>अक्टूबर के आरंभ में</p>"],
                    solution_en: "<p><strong>47.(b) Early June. </strong>The word monsoon is derived from the Arabic word &lsquo;mausim&rsquo; which literally means season. The climate of India is described as the &lsquo;monsoon&rsquo; type. The southwest monsoon season - Rainfall received from the southwest monsoons is seasonal in character, which occurs between June and September and the retreating monsoon season occurs in the months of October and November.</p>",
                    solution_hi: "<p><strong>47.(b) जून के आरंभ में। </strong>मानसून शब्द अरबी शब्द \'मौसिम\' से बना है जिसका शाब्दिक अर्थ मौसम होता है। भारत की जलवायु को \'मानसून\' प्रकार के रूप में वर्णित किया गया है। दक्षिण-पश्चिम मानसून का मौसम - दक्षिण-पश्चिम मानसून से प्राप्त वर्षा प्रकृति में मौसमी होती है, जो जून और सितंबर के बीच होती है और लौटते हुए मानसून का मौसम अक्टूबर और नवंबर के महीनों में होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which system was prevalent before the invention of money?</p>",
                    question_hi: "<p>48. मुद्रा के आविष्कार से पहले कौन-सी प्रणाली प्रचलित थी?</p>",
                    options_en: ["<p>Bretton woods system</p>", "<p>Liquidity system</p>", 
                                "<p>Fiscal system</p>", "<p>Barter system</p>"],
                    options_hi: ["<p>ब्रेटन वुड्स सिस्टम (Bretton woods system)</p>", "<p>तरलता प्रणाली (Liquidity system)</p>",
                                "<p>राजकोषीय प्रणाली (Fiscal system)</p>", "<p>वस्तु विनिमय प्रणाली (Barter system)</p>"],
                    solution_en: "<p><strong>48.(d) Barter system -</strong> When the goods and services of equal value are exchanged between two or more parties without using any form of monetary exchange. The only way to buy goods was to exchange them with personal belongings of similar value. For example- A farmer gives his cattle in exchange for some land, and so on.</p>",
                    solution_hi: "<p><strong>48.(d) वस्तु विनिमय प्रणाली -</strong> जब किसी भी प्रकार के मौद्रिक विनिमय का उपयोग किए बिना दो या दो से अधिक पक्षों के बीच समान मूल्य की वस्तुओं और सेवाओं का आदान-प्रदान किया जाता है। सामान खरीदने का एकमात्र तरीका उन्हें समान मूल्य के व्यक्तिगत सामान के साथ विनिमय करना था। उदाहरण के लिए- किसान कुछ ज़मीन के बदले में अपने मवेशी देता है, इत्यादि।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Who among the following presides over the joint sitting of the Parliament?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन संसद की संयुक्त बैठक की अध्यक्षता करता है?</p>",
                    options_en: ["<p>President of India</p>", "<p>Chairman of Rajya Sabha</p>", 
                                "<p>Prime Minister of India</p>", "<p>Lok Sabha Speaker</p>"],
                    options_hi: ["<p>भारत के राष्ट्रपति</p>", "<p>राज्यसभा के सभापति</p>",
                                "<p>भारत के प्रधानमंत्री</p>", "<p>लोकसभा अध्यक्ष</p>"],
                    solution_en: "<p><strong>49.(d) Speaker of Lok Sabha. </strong>The joint sitting of the Parliament is called by the President of India (Article 108) and is presided over by the Speaker of the Lok Sabha or, in their absence, by the Deputy Speaker of the Lok Sabha, or in their absence, the Deputy Chairman of the Rajya Sabha.</p>",
                    solution_hi: "<p><strong>49.(d) लोकसभा अध्यक्ष। </strong>संसद की संयुक्त बैठक भारत के राष्ट्रपति द्वारा बुलाई जाती है (अनुच्छेद 108) और इसकी अध्यक्षता लोकसभा अध्यक्ष या उनकी अनुपस्थिति में लोकसभा के उपाध्यक्ष या उनकी अनुपस्थिति में राज्य सभा के उपसभापति इस दायित्व को निभाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
                questions.push({
                    id: "50",
                    section: "misc",
                    question_en: "50. In which Olympics did Khashaba Jadhav, the first Indian win an individual Olympic<br />medal for India?",
                    question_hi: "<p>50. खाशाबा जाधव किस ओलंपिक में भारत के लिए व्यक्तिगत ओलंपिक पदक जीतने वाले पहले भारतीय बने ?</p>",
                    options_en: [" 1972 Munich Olympics  ", "<p>1948 London Olympics</p>", 
                                "<p>1952 Helsinki Olympics</p>", "<p>1936 Berlin Olympics</p>"],
                    options_hi: ["<p>1972 म्यूनिख ओलंपिक</p>", "<p>1948 लंदन ओलंपिक</p>",
                                "<p>1952 हेलसिंकी ओलंपिक</p>", "<p>1936 बर्लिन ओलंपिक</p>"],
                    solution_en: "<p><strong>50.(c) 1952 Helsinki Olympics.</strong> Khashaba Dadasaheb Jadhav (wrestler). He was known for winning a bronze medal at the 1952 Summer Olympics in Helsinki. The first World Wrestling Championships were held in 1904. Bajrang Punia has won the most medals for India at the world championships with four - one silver and three bronze.</p>",
                    solution_hi: "<p><strong>50.(c) 1952 हेलसिंकी ओलंपिक। </strong>खाशाबा दादासाहेब जाधव (पहलवान)। उन्हें हेलसिंकी में 1952 के ग्रीष्मकालीन ओलंपिक में कांस्य पदक जीतने के लिए जाना जाता है। पहली विश्व कुश्ती चैंपियनशिप 1904 में आयोजित की गई थी। बजरंग पुनिया ने विश्व चैंपियनशिप में भारत के लिए चार पदक (एक रजत और तीन कांस्य) जीते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.25
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>