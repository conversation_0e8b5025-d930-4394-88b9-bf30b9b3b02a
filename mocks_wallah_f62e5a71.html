<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Where has India&rsquo;s first floating elementary school been inaugurated?</p>",
                    question_hi: "<p>1. भारत के पहले तैरते हुए प्राथमिक विद्यालय का उद्घाटन कहाँ किया गया है?</p>",
                    options_en: ["<p>Naini Jheel , Nainital</p>", "<p>Dal Lake, Srinagar</p>", 
                                "<p>Lake Pichola, Udaipur</p>", "<p>Loktak Lake, Manipur</p>"],
                    options_hi: ["<p>नैनी झील, नैनीताल</p>", "<p>डल झील, श्रीनगर</p>",
                                "<p>पिछोला झील, उदयपुर</p>", "<p>लोकटक झील, मणिपुर</p>"],
                    solution_en: "<p>1.(d) <strong>Loktak Lake, Manipur -</strong> One of the largest freshwater lakes in the country. Keibul Lamjao National Park is the only floating national park in the world<strong>. Lake Pichola, Udaipur</strong> is an artificial freshwater lake, created in 1362.<strong> Dal Lake, Srinagar -</strong> Second largest lake in Jammu and Kashmir.</p>",
                    solution_hi: "<p>1.(d) <strong>लोकटक झील, मणिपुर - </strong>देश की सबसे बड़ी मीठे पानी की झीलों में से एक है। कैबुल लामजाओ राष्ट्रीय उद्यान दुनिया का एकमात्र तैरता हुआ राष्ट्रीय अभ्यारण है। <strong>पिछोला झील, उदयपुर </strong>एक कृत्रिम मीठे पानी की झील है, जो 1362 में निर्माण करवाया गया था । <strong>डल झील, श्रीनगर - </strong>जम्मू और कश्मीर की दूसरी सबसे बड़ी झील है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Who among the following joined the 969 Railway Engineer Regiment of Territorial Army (TA) in 2011? She was the first female jawan in the Indian Army.</p>",
                    question_hi: "<p>2. निम्नलिखित में से कौन 2011 में प्रादेशिक सेना (TA) की 969 रेलवे इंजीनियर रेजिमेंट में शामिल हुआ था? वह भारतीय सेना में पहली महिला जवान थीं।</p>",
                    options_en: ["<p>Amrita Devi</p>", "<p>Sapper Shanti Tigga</p>", 
                                "<p>Chhavi Rajawat</p>", "<p>Sita Sahu</p>"],
                    options_hi: ["<p>अमृता देवी</p>", "<p>सैपर शांति तिग्गा</p>",
                                "<p>छवी राजावत</p>", "<p>सीता साहू</p>"],
                    solution_en: "<p>2.(b) <strong>Sapper Shanti Tigga.</strong> Railway Territorial Army (TA) units were raised as an auxiliary force in 1949 under the Territorial Army Act, 1948 for maintaining rail communication.<strong> Amrita Devi Bishnoi</strong> first sacrificed her life by hugging a Khejri tree and preventing it from being cut down by the king\'s men.</p>",
                    solution_hi: "<p>2.(b) <strong>सैपर शांति तिग्गा। </strong>रेलवे प्रादेशिक सेना (TA) इकाइयों को रेल संचार बनाए रखने के लिए प्रादेशिक सेना अधिनियम, 1948 के तहत 1949 में एक सहायक बल के रूप में स्थापित किया गया था। अमृता देवी बिश्नोई ने सबसे पहले एक खेजड़ी के वृक्ष को गले लगाकर और उसे राजा के आदमियों द्वारा काटे जाने से रोककर अपने जीवन का बलिदान दिया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The world&rsquo;s first official airmail was sent from ______ to ________ in February 1911.</p>",
                    question_hi: "<p>3. फरवरी 1911 में दुनिया का पहला आधिकारिक एयरमेल ______ से ________ को भेजा गया था</p>",
                    options_en: ["<p>Allahabad, Naini</p>", "<p>Andhra, Delhi</p>", 
                                "<p>Allahabad, Agra</p>", "<p>Hyderabad, Tirupati</p>"],
                    options_hi: ["<p>इलाहाबाद, नैनी</p>", "<p>आंध्र, दिल्ली</p>",
                                "<p>इलाहाबाद, आगरा</p>", "<p>हदराबाद, तिरुपति</p>"],
                    solution_en: "<p>3.(a) <strong>Allahabad, Naini.</strong> This was the first commercial flight in India,This flight was made by a French pilot named <strong>Henri Peque</strong>. The flight took just 13 minutes (over a distance of 6 miles) to reach its destination. First railway line between Bombay and Thane (1853).</p>",
                    solution_hi: "<p>3.(a) <strong>इलाहाबाद, नैनी।</strong> यह भारत की पहली व्यावसायिक उड़ान थी, इस उड़ान को <strong>हेनरी पेक </strong>नामक एक फ्रांसीसी पायलट ने बनाया था। उड़ान को अपने गंतव्य तक पहुँचने में केवल 13 मिनट (6 मील से अधिक की दूरी) लगे। बम्बई और ठाणे के बीच पहली रेलवे लाइन (1853)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. In which of the following states the Electronic Voting Machine (EVMs) were used for the first time in India?</p>",
                    question_hi: "<p>4. निम्नलिखित में से किस राज्य में भारत में पहली बार इलेक्ट्रॉनिक वोटिंग मशीन (EVMs) का उपयोग किया गया था?</p>",
                    options_en: ["<p>Tamil Nadu</p>", "<p>West Bengal</p>", 
                                "<p>Kerala</p>", "<p>Karnataka</p>"],
                    options_hi: ["<p>तमिलनाडु</p>", "<p>पश्चिम बंगाल</p>",
                                "<p>केरल</p>", "<p>कर्नाटक</p>"],
                    solution_en: "<p>4.(c)<strong> Kerala (1982). Electronic Voting Machine </strong>(EVM) is designed with two units: control unit and balloting unit. First used - in1982 in the by-election to North Paravur Assembly Constituency in Kerala. A VVPAT (voter-verified paper audit trail) was first used in Noksen (Assembly Constituency) of Nagaland in September 2013.</p>",
                    solution_hi: "<p>4.(c)<strong> केरल (1982)। इलेक्ट्रॉनिक वोटिंग मशीन (EVM)</strong> को दो यूनिट्स के साथ डिज़ाइन किया गया है: कंट्रोल यूनिट और बैलट यूनिट । प्रथम बार इस्तेमाल किया गया - 1982 में केरल में उत्तरी परवूर विधानसभा क्षेत्र के उपचुनाव में। VVPAT (वोटर वेरीफाईड पेपर ऑडिट ट्रेल) का प्रथम बार उपयोग सितंबर 2013 में नागालैंड के नोकसेन (विधानसभा निर्वाचन क्षेत्र) में किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Who was the first female judge of the Supreme Court in India?</p>",
                    question_hi: "<p>5. भारत में सर्वोच्च न्यायालय की पहली महिला न्यायाधीश कौन थी?</p>",
                    options_en: ["<p>R Banumathi</p>", "<p>M Fathima Beevi</p>", 
                                "<p>Sujatha V Manohar</p>", "<p>Indira Banerjee</p>"],
                    options_hi: ["<p>आर भानुमति</p>", "<p>एम फथिमा बीवी</p>",
                                "<p>सुजाता वी मनोहर</p>", "<p>इंदिरा बनर्जी</p>"],
                    solution_en: "<p>5.(b) <strong>M Fathima Beevi.</strong> On her retirement from the court, she served as a member of the National Human Rights Commission and later as the Governor of Tamil Nadu from 1997 to 2001. <strong>Sujatha V Manohar</strong> was the second woman judge of the Supreme Court in India. <strong>Ruma Pal </strong>- longest-serving female judge of the Supreme Court of India.<strong> R Banumathi </strong>- sixth woman to be a Judge of the Indian Supreme Court. <strong>Indira Banerjee </strong>- 8th female Judge in history of the Supreme Court of India.</p>",
                    solution_hi: "<p>5.(b) <strong>एम. फातिमा बीवी। </strong>न्यायालय से सेवानिवृत्त होने पर, उन्होंने राष्ट्रीय मानवाधिकार आयोग के सदस्य और बाद में 1997 से 2001 तक तमिलनाडु के राज्यपाल के रूप में कार्य किया। <strong>सुजाता वी मनोहर</strong> भारत में सर्वोच्च न्यायालय की दूसरी महिला न्यायाधीश थीं। <strong>रूमा पाल - </strong>भारत के सर्वोच्च न्यायालय की सबसे लंबे समय तक सेवारत महिला न्यायाधीश।<strong> आर. भानुमति - </strong>भारतीय सर्वोच्च न्यायालय की न्यायाधीश बनने वाली छठी महिला।<strong> इंदिरा बनर्जी -</strong> भारत के सर्वोच्च न्यायालय के इतिहास में 8वीं महिला न्यायाधीश।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Who was the first Indian to join the Indian Civil Service?</p>",
                    question_hi: "<p>6. भारतीय सिविल सेवा में शामिल होने वाले पहले भारतीय कौन थे?</p>",
                    options_en: ["<p>Lakshmi Kant Jha</p>", "<p>Shyamji Krishna Varma</p>", 
                                "<p>Haribhai Patel</p>", "<p>Satyendranath Tagore</p>"],
                    options_hi: ["<p>लक्ष्मी कांत झा</p>", "<p>श्यामजी कृष्ण वर्मा</p>",
                                "<p>हरिभाई पटेल</p>", "<p>सत्येंद्रनाथ टैगोर</p>"],
                    solution_en: "<p>6.(d) <strong>Satyendranath Tagore. </strong>He became an ICS officer in 1863. He was a member of Bramho Samaj. Charles Cornwallis is known as \'the Father of civil service in India\'. Lakshmi Kant Jha was the eighth Governor of the Reserve Bank of India from 1 July 1967 to 3 May 1970. Shyamji Krishna Varma founded the Indian Home Rule Society, India House and The Indian Sociologist in London. Haribhai Patel - From 1977 to 1979, he served as the Finance Minister and later Home Minister of India.</p>",
                    solution_hi: "<p>6.(d) <strong>सत्येन्द्रनाथ टैगोर। </strong>वह 1863 में ICS अधिकारी बने। वह ब्रम्ह समाज के सदस्य थे। चार्ल्स कॉर्नवालिस को \'भारत में सिविल सेवा का जनक\' कहा जाता है। लक्ष्मी कांत झा 1 जुलाई 1967 से 3 मई 1970 तक भारतीय रिज़र्व बैंक के आठवें गवर्नर थे। श्यामजी कृष्ण वर्मा ने लंदन में इंडियन होम रूल सोसाइटी, इंडिया हाउस और द इंडियन सोशियोलॉजिस्ट की स्थापना की। हरिभाई पटेल - 1977 से 1979 तक, उन्होंने भारत के वित्त मंत्री और बाद में गृह मंत्री के रूप में कार्य किया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Who was the first winner of the Jnanpith Award?</p>",
                    question_hi: "<p>7. ज्ञानपीठ पुरस्कार के प्रथम विजेता कौन थे?</p>",
                    options_en: ["<p>Ashapurna Devi</p>", "<p>G Sankara Kurup</p>", 
                                "<p>Amrita Pritam</p>", "<p>Uma Shankar Joshi</p>"],
                    options_hi: ["<p>आशापूर्णा देवी</p>", "<p>जी शंकर कुरुप</p>",
                                "<p>अमृता प्रीतम</p>", "<p>उमा शंकर जोशी</p>"],
                    solution_en: "<p>7.(b) <strong>G Sankara Kurup (Malayalam poet). </strong>He won the first Jnanpith Award in 1965, the year of its inception, for his anthology of poems, titled &ldquo;Odakkuzhal&rdquo;. The Jnanpith Award is the oldest and the highest Indian literary award presented annually by the Bharatiya Jnanpith to an author for their outstanding contribution towards literature. Other Jnanpith awardees - Ashapurna Devi (1976), Amrita Pritam (1981), Uma Shankar Joshi (1967).</p>",
                    solution_hi: "<p>7.(b) <strong>जी शंकर कुरुप (मलयालम कवि)। </strong>उन्होंने 1965 में, इसकी स्थापना के वर्ष, पहला ज्ञानपीठ पुरस्कार, उनकी कविताओं के संकलन, जिसका शीर्षक था \"ओडक्कुझल\" के लिए जीता। ज्ञानपीठ पुरस्कार सबसे पुराना और सर्वोच्च भारतीय साहित्यिक पुरस्कार है जो भारतीय ज्ञानपीठ द्वारा हर साल किसी लेखक को साहित्य के प्रति उनके उत्कृष्ट योगदान के लिए दिया जाता है। अन्य ज्ञानपीठ पुरस्कार विजेता - आशापूर्णा देवी (1976), अमृता प्रीतम (1981), उमा शंकर जोशी (1967)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who is the first women Indian Police Service officer to reach the South pole?</p>",
                    question_hi: "<p>8. दक्षिणी ध्रुव पर पहुंचने वाली पहली महिला भारतीय पुलिस सेवा अधिकारी कौन है?</p>",
                    options_en: ["<p>Aparna Kumar</p>", "<p>Kiran Bedi</p>", 
                                "<p>Raisina Dialogue</p>", "<p>Kavita Desai</p>"],
                    options_hi: ["<p>अपर्णा कुमार</p>", "<p>किरण बेदी</p>",
                                "<p>रायसीना डायलॉग</p>", "<p>कविता देसाई</p>"],
                    solution_en: "<p>8.(a)<strong> Aparna Kumar.</strong> She is a 2002-batch Indian Police Service officer of the Uttar Pradesh cadre. The first person to reach the south pole was Roald Amundsen. First Indian to reach the South Pole - Colonel J.K. Bajaj. First Women IPS in India - <strong>Kiran Bedi.</strong></p>",
                    solution_hi: "<p>8.(a) <strong>अपर्णा कुमार। </strong>उत्तर प्रदेश कैडर की 2002-बैच की भारतीय पुलिस सेवा अधिकारी हैं। दक्षिणी ध्रुव पर पहुंचने वाले प्रथम व्यक्ति रोनाल्ड एमंडसेन थे। दक्षिणी ध्रुव पर पहुँचने वाले प्रथम भारतीय - कर्नल जे.के. बजाज। भारत की प्रथम महिला IPS - किरण बेदी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which one among the following is the Asia\'s first stock exchange?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन एशिया का पहला स्टॉक एक्सचेंज है?</p>",
                    options_en: ["<p>Shanghai Stock Exchange</p>", "<p>National Stock Exchange</p>", 
                                "<p>Bombay Stock Exchange</p>", "<p>Tokyo Stock Exchange</p>"],
                    options_hi: ["<p>शंघाई स्टॉक एक्सचेंज</p>", "<p>नेशनल स्टॉक एक्सचेंज</p>",
                                "<p>बॉम्बे स्टॉक एक्सचेंज</p>", "<p>टोक्यो स्टॉक एक्सचेंज</p>"],
                    solution_en: "<p>9.(c) <strong>Bombay Stock Exchange </strong>(BSE): Established on 9 July 1875 at Dalal Street, Mumbai. As per the Securities Contracts Regulation Act, BSE became the first stock exchange to be recognized by the Government of India in 1956. <strong>Shanghai Stock Exchange - </strong>November 26, 1990. <strong>The National Stock Exchange </strong>(NSE) was established in 1992. It was recognised as a stock exchange by SEBI (Securities and Exchange Board of India) in April 1993. <strong>Tokyo Stock Exchange -</strong> Established in 1878.</p>",
                    solution_hi: "<p>9.(c) <strong>बॉम्बे स्टॉक एक्सचेंज</strong> (BSE): 9 जुलाई 1875 को दलाल स्ट्रीट, मुंबई में स्थापित किया गया। प्रतिभूति अनुबंध विनियमन अधिनियम के अनुसार, BSE 1956 में भारत सरकार द्वारा मान्यता प्राप्त प्रथम स्टॉक एक्सचेंज बन गया। <strong>शंघाई स्टॉक एक्सचेंज - </strong>26 नवंबर, 1990। <strong>नेशनल स्टॉक एक्सचेंज </strong>(NSE) की स्थापना 1992 में की गयी थी। इसे अप्रैल 1993 में SEBI (भारतीय प्रतिभूति और विनिमय बोर्ड) द्वारा <strong>स्टॉक एक्सचेंज</strong> के रूप में मान्यता दी गई थी। <strong>टोक्यो स्टॉक एक्सचेंज </strong>- 1878 में स्थापित।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. In which year was the World Happiness Report published for the first time?</p>",
                    question_hi: "<p>10. विश्व प्रसन्नता रिपोर्ट पहली बार किस वर्ष प्रकाशित हुई थी?</p>",
                    options_en: ["<p>2012</p>", "<p>2015</p>", 
                                "<p>2010</p>", "<p>2017</p>"],
                    options_hi: ["<p>2012</p>", "<p>2015</p>",
                                "<p>2010</p>", "<p>2017</p>"],
                    solution_en: "<p>10.(a) <strong>2012. World Happiness Report :</strong> Released annually around March 20th as part of the International Day of Happiness celebration by the United Nations Sustainable Development Solutions Network. <strong>Factors </strong>- Social support, income, health, freedom, generosity, and absence of corruption.</p>",
                    solution_hi: "<p>10.(a) <strong>2012 । विश्व प्रसन्नता रिपोर्ट :&nbsp;</strong>संयुक्त राष्ट्र सतत विकास समाधान नेटवर्क द्वारा अंतर्राष्ट्रीय प्रसन्नता दिवस समारोह के भाग के रूप में प्रतिवर्ष 20 मार्च को जारी की जाती है। <strong>कारक </strong>- सामाजिक समर्थन, आय, स्वास्थ्य, स्वतंत्रता, उदारता और भ्रष्टाचार का अभाव।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Who was the first Indian to travel to Space?</p>",
                    question_hi: "<p>11. अंतरिक्ष की यात्रा करने वाले पहले भारतीय कौन थे?</p>",
                    options_en: ["<p>Sunita Williams</p>", "<p>Vikram Sarabhaib</p>", 
                                "<p>APJ Abdul Kalam</p>", "<p>Rakesh Sharma</p>"],
                    options_hi: ["<p>सुनीता विलियम्स</p>", "<p>विक्रम साराभाई</p>",
                                "<p>ए पी जे अब्दुल कलाम</p>", "<p>राकेश शर्मा</p>"],
                    solution_en: "<p>11.(d)<strong> Rakesh Sharma - </strong>A former Indian Air Force pilot who flew aboard Soyuz T-11 on 3 April 1984 as part of the Soviet Intercosmos program. <strong>Sunita Williams - </strong>An Indian American astronaut and United States Navy officer who formerly held the records for most spacewalks by a woman and most spacewalk time by a woman. <strong>Vikram Sarabhai - </strong>An Indian physicist and astronomer who initiated space research and helped develop nuclear power in India. <strong>APJ Abdul Kalam</strong> (Missile Man of India) - An Indian aerospace scientist and statesman who served as the 11th president of India from 2002 to 2007.</p>",
                    solution_hi: "<p>11.(d) <strong>राकेश शर्मा -</strong> एक पूर्व भारतीय वायु सेना पायलट जिन्होंने 3 अप्रैल 1984 को सोवियत इंटरकॉसमॉस कार्यक्रम के हिस्से के रूप में &lsquo;सोयुज टी-11&rsquo; पर उड़ान भरी थी। <strong>सुनीता विलियम्स -</strong> एक भारतीय अमेरिकी महिला अंतरिक्ष यात्री और संयुक्त राज्य अमेरिका की नौसेना अधिकारी, जिन्होंने सबसे अधिक स्पेस मे रहने का और सबसे अधिक स्पेसवॉक का रिकॉर्ड बनाया। <strong>विक्रम साराभाई - </strong>एक भारतीय भौतिक विज्ञानी और खगोलशास्त्री जिन्होंने अंतरिक्ष अनुसंधान की शुरुआत की और भारत में परमाणु ऊर्जा विकसित करने में मदद की। <strong>APJ अब्दुल कलाम </strong>(भारत के मिसाइल मैन) - एक भारतीय एयरोस्पेस वैज्ञानिक और राजनेता जिन्होंने 2002 से 2007 तक भारत के 11वें राष्ट्रपति के रूप में कार्य किया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Who was the first person to fly into space?</p>",
                    question_hi: "<p>12.अंतरिक्ष में जाने वाला प्रथम व्यक्ति कौन था ?</p>",
                    options_en: ["<p>Yelena Serova</p>", "<p>Valentina Goryacheva</p>", 
                                "<p>Yuri Gagarin</p>", "<p>Neil Armstrong</p>"],
                    options_hi: ["<p>येलेना सेरोवा</p>", "<p>वेलेंटीना गोरीचेवा</p>",
                                "<p>यूरी गगारिन</p>", "<p>नील आर्मस्ट्रांग</p>"],
                    solution_en: "<p>12.(c) <strong>Yuri Gagarin</strong> (in1961). Russian cosmonaut Yuri Gagarin made a 108-minute orbital journey in his Vostok 1 spacecraft. First Indian person to travel in space - Rakesh Sharma (3 April 1984). First Indian-born woman to fly in space - Kalpana Chawla (1997). First woman to reach space - Valentina Tereshkova from Russia (16 June 1963).</p>",
                    solution_hi: "<p>12.(c) <strong>यूरी गगारिन </strong>(1961 में)। रूसी अंतरिक्ष यात्री यूरी गगारिन ने अपने वोस्तोक-1 एयरक्राफ्ट में 108 मिनट की कक्षीय यात्रा की। अंतरिक्ष में यात्रा करने वाले पहले भारतीय व्यक्ति - राकेश शर्मा (3 अप्रैल 1984)। अंतरिक्ष में उड़ान भरने वाली पहली भारतीय मूल की महिला - कल्पना चावला (1997)। अंतरिक्ष में पहुंचने वाली प्रथम महिला - रूस की वेलेंटीना टेरेश्कोवा (16 जून 1963)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Who among the following was the first Indian Governor of the Reserve Bank of India?</p>",
                    question_hi: "<p>13. निम्नलिखित में से कौन भारतीय रिजर्व बैंक के पहले भारतीय गवर्नर थे?</p>",
                    options_en: ["<p>Manmohan Singh</p>", "<p>IG Patel</p>", 
                                "<p>CD Deshmukh</p>", "<p>Raghuram Rajan</p>"],
                    options_hi: ["<p>मनमोहन सिंह</p>", "<p>आईजी पटेल</p>",
                                "<p>सीडी देशमुख</p>", "<p>रघुराम राजन</p>"],
                    solution_en: "<p>13.(c) <strong>CD Deshmukh </strong>(after independence). First Governor of the RBI - Sir Osborne Smith. The Reserve Bank of India (RBI) - Founded on 1 April 1935 in Calcutta. Headquarter - Mumbai. It was set up based on the recommendations of the 1926 Royal Commission on Indian Currency and Finance (Hilton Young Commission). <strong>Nationalization </strong>- 1st January 1949. Work - To regulate the issue of Bank notes, to operate the currency and credit system.</p>",
                    solution_hi: "<p>13.(c) <strong>सीडी देशमुख</strong> (स्वतंत्रता के बाद)। RBI के प्रथम गवर्नर - सर ओसबोर्न स्मिथ। भारतीय रिज़र्व बैंक (RBI) - 1 अप्रैल 1935 को कलकत्ता में स्थापित किया गया। मुख्यालय - मुंबई। इसकी स्थापना 1926 में भारतीय मुद्रा और वित्त पर रॉयल कमीशन (हिल्टन यंग कमीशन) की सिफारिशों के आधार पर की गई थी। <strong>राष्ट्रीयकरण </strong>- 1 जनवरी 1949 । कार्य - बैंक नोटों के निर्गम को विनियमित करना, मुद्रा एवं ऋण प्रणाली का संचालन करना।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following beaches has become the first in Asia to get the Blue Flag Certification?</p>",
                    question_hi: "<p>14.निम्नलिखित में से कौन सा समुद्र तट ब्लू फ्लैग प्रमाणन प्राप्त करने वाला एशिया का पहला समुद्र तट बन गया है?</p>",
                    options_en: ["<p>Kovalam beach</p>", "<p>Bega beach</p>", 
                                "<p>Chandrabhaga beach</p>", "<p>Marina beach</p>"],
                    options_hi: ["<p>कोवलम बीच</p>", "<p>बेगा बीच</p>",
                                "<p>चंद्रभागा बीच</p>", "<p>मरीना बीच</p>"],
                    solution_en: "<p>14.(c)<strong> Chandrabhaga beach.</strong> It is an internationally recognised eco - label that is accorded based on 33 criterias. Other Beaches Received the Certification - Shivrajpur (Gujarat), Ghoghla (Daman &amp; Diu), Kasarkod (Karnataka), Kappad (Kerala), Rushikonda (Andhra Pradesh), Golden beach (Odisha), Radhanagar beach (Andaman and Nicobar), Kovalam (Tamil Nadu). Baga beach - Goa, Marina beach - Tamil Nadu.</p>",
                    solution_hi: "<p>14.(c) <strong>चंद्रभागा बीच।</strong> यह एक अंतरराष्ट्रीय स्तर पर मान्यता प्राप्त इको-लेबल है जिसे 33 मानदंडों के आधार पर प्रदान किया जाता है। प्रमाणन प्राप्त करने वाले अन्य समुद्र तट - शिवराजपुर (गुजरात), घोघला (दमन और दीव), कासरकोड (कर्नाटक), कप्पड़ (केरल), ऋषिकोंडा (आंध्र प्रदेश), गोल्डन बीच (ओडिशा), राधानगर बीच (अंडमान और निकोबार), कोवलम ( तमिलनाडु)। बागा बीच - गोवा, मरीना बीच - तमिलनाडु।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following has developed the world\'s first malaria vaccine for the World Health Organization?</p>",
                    question_hi: "<p>15. निम्नलिखित में से किसने विश्व स्वास्थ्य संगठन के लिए विश्व का पहला मलेरिया टीका विकसित किया है?</p>",
                    options_en: ["<p>Johnson &amp; Johnson</p>", "<p>Pfizer</p>", 
                                "<p>GlaxoSmithKline (GSK)</p>", "<p>Merck</p>"],
                    options_hi: ["<p>जॉनसन एंड जॉनसन</p>", "<p>फाइजर</p>",
                                "<p>ग्लैक्सोस्मिथक्लाइन (GSK)</p>", "<p>मर्क</p>"],
                    solution_en: "<p>15.(c) <strong>GlaxoSmithKline</strong> (GSK). He developed the RTS,S or Mosquirix vaccine in 1987. Johnson and <strong>Johnson is an American</strong> multinational corporation founded in 1886 that develops medical devices, pharmaceuticals, and consumer packaged goods. Pfizer Inc. is an American multinational pharmaceutical and biotechnology corporation. Merck is a German multinational science and technology company.</p>",
                    solution_hi: "<p>15.(c) <strong>ग्लैक्सोस्मिथक्लाइन</strong> (जीएसके)। उन्होंने 1987 में RTS,S या मॉस्किरिक्स वैक्सीन विकसित की। <strong>जॉनसन एंड जॉनसन</strong> 1886 में स्थापित एक अमेरिकी बहुराष्ट्रीय निगम है जो चिकित्सा उपकरणों, फार्मास्यूटिकल्स और उपभोक्ता पैकेज्ड सामान विकसित करता है। फाइजर इंक एक अमेरिकी बहुराष्ट्रीय फार्मास्युटिकल और जैव प्रौद्योगिकी निगम है। मर्क एक जर्मन बहुराष्ट्रीय विज्ञान और प्रौद्योगिकी कंपनी है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>