<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. The following sentence has been split into four segments. Identify the segment that&nbsp;contains a grammatical error.<br>Since childhood, Monty / ate his food / fast and he / will not change.</p>",
                    question_hi: "<p>1. The following sentence has been split into four segments. Identify the segment that&nbsp;contains a grammatical error.<br>Since childhood, Monty / ate his food / fast and he / will not change.</p>",
                    options_en: ["<p>ate his food</p>", "<p>will not change</p>", 
                                "<p>Since childhood, Monty</p>", "<p>fast and he</p>"],
                    options_hi: ["<p>ate his food</p>", "<p>will not change</p>",
                                "<p>Since childhood, Monty</p>", "<p>fast and he</p>"],
                    solution_en: "<p>1.(a) ate his food<br>Monty will not change. This means Monty has been eating his food fast and he will keep eating. Therefore, the first part of the sentence will be written in present perfect continuous tense. &lsquo;Monty&rsquo; is a singular subject that will take &lsquo;has been&rsquo; as a singular helping verb. Hence, &lsquo;has been eating his food&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>1.(a) ate his food<br>Monty नहीं बदलेगा। इसका अर्थ है कि मॉंटी जल्दी-जल्दी खाना खाता आया है और वह अब नहीं बदलेगा। इसलिए, Sentence का First part, Present perfect continuous tense में लिखा जाएगा। &lsquo;Monty&rsquo; एक Singular subject है जिसके साथ &lsquo;has been&rsquo; Singular helping verb का प्रयोग होगा। अतः, &lsquo;has been eating his food&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. The following sentence has been divided into four parts. Identify the part that contains&nbsp;an error.<br>(A) It is sad / (B) that you / (C) are believing / (D) in superstitious rituals.</p>",
                    question_hi: "<p>2. The following sentence has been divided into four parts. Identify the part that contains&nbsp;an error.<br>(A) It is sad / (B) that you / (C) are believing / (D) in superstitious rituals.</p>",
                    options_en: [" B", " C", 
                                " A", " D"],
                    options_hi: [" B", " C",
                                " A", " D"],
                    solution_en: "2.(b) C<br />Stative verbs are those verbs which express a state or condition. They do not refer to a physical action. Sense and emotions like hear, smell, see, love, hate etc are different types of stative verbs. Verbs denoting thoughts and opinions include know, believe, think, understand, consider, etc. However, these verbs can’t be used in (-ing form) for example, Hear, Smell(Correct) and Hearing, Smelling(Incorrect). Hence, ‘you believe’ is the most appropriate answer.",
                    solution_hi: "2.(b) C<br />Stative verb वे Verb हैं जो किसी स्थिति (State) या दशा (Condition) को Express करते हैं। वे किसी Physical action को refer नहीं करते। Hear, smell, see, love, hate आदि जैसे Sense और emotions, stative verb के विभिन्न प्रकार हैं। Thought तथा Opinion को Denote करने वाले Verb में know, believe, think, understand, consider आदि शामिल हैं। हालाँकि, इन Verbs का प्रयोग(-ing form) में नहीं किया जा सकता है, For example - Hear, Smell(Correct) और Hearing, Smelling(Incorrect)। अतः ‘you believe’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.<br>After your insistence, / my sister / checked / a mailbox again.</p>",
                    question_hi: "<p>3. The following sentence has been divided into four segments. Identify the segment that contains a grammatical error.<br>After your insistence, / my sister / checked / a mailbox again.</p>",
                    options_en: [" After your insistence, ", " my sister ", 
                                " checked ", " a mailbox again."],
                    options_hi: [" After your insistence, ", " my sister ",
                                " checked ", " a mailbox again."],
                    solution_en: "3.(d)  a mailbox again.<br />‘Mailbox’ mentioned in the given sentence is specific and we generally use the definite article ‘the’ before any specific or particular noun. Hence, ‘the mailbox again’ is the most appropriate answer.",
                    solution_hi: "3.(d)  a mailbox again.<br />दिए गए Sentence में उल्लिखित (mentioned) ‘Mailbox’ Specific है तथा हम आमतौर पर किसी भी Specific या Particular noun से पहले Definite article ‘the’ का प्रयोग करते हैं। अतः, ‘the mailbox again’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. The following sentence has been split into four segments. Identify the segment that&nbsp;contains a grammatical error.<br>In order to connect / these themes to a wider / historical context, Rahul go /beyond aesthetic analysis.</p>",
                    question_hi: "<p>4. The following sentence has been split into four segments. Identify the segment that&nbsp;contains a grammatical error.<br>In order to connect / these themes to a wider / historical context, Rahul go /beyond aesthetic analysis.</p>",
                    options_en: [" these themes to a wider", " historical context, Rahul go", 
                                " In order to connect", " beyond aesthetic analysis"],
                    options_hi: [" these themes to a wider", " historical context, Rahul go",
                                " In order to connect", " beyond aesthetic analysis"],
                    solution_en: "4.(b) historical context, Rahul go<br />According to the “Subject-Verb Agreement Rule”, a singular subject always takes a singular verb and a plural subject always takes a plural verb. In the given sentence, ‘Rahul’ is a singular subject that will take ‘goes’ as a singular verb. Hence, ‘historical context, Rahul goes’  is the most appropriate answer.",
                    solution_hi: "4.(b) historical context, Rahul go<br />“Subject-Verb Agreement Rule” के अनुसार, एक Singular subject के साथ हमेशा Singular verb का तथा एक Plural subject के साथ हमेशा Plural verb का प्रयोग होता है। दिए गए Sentence में, ‘Rahul’ एक Singular subject है जिसके साथ ‘goes’ singular verb का प्रयोग होगा। अतः, ‘historical context, Rahul goes’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <strong>Cloze Test:</strong><br>There is nothing more (5)__________than when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts (6)______ However, there are certain techniques that you can use to (7)_________your concentration. To begin with, one should (8)_________to create the physical environment that is conducive to (9)_________thought.<br>Select the most appropriate option to fill in blank no.5</p>",
                    question_hi: "<p>5. <strong>Cloze Test:</strong><br>There is nothing more (5)__________than when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts (6)______ However, there are certain techniques that you can use to (7)_________your concentration. To begin with, one should (8)_________to create the physical environment that is conducive to (9)_________thought.<br>Select the most appropriate option to fill in blank no.5</p>",
                    options_en: [" frustrating", " exciting   ", 
                                " interesting  ", " surprising   "],
                    options_hi: [" frustrating", " exciting   ",
                                " interesting  ", " surprising   "],
                    solution_en: "5.(a) frustrating<br />‘Frustrating’ means causing feelings of anger and annoyance. The given passage states that there is nothing more frustrating that when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts wandering. Hence, ‘frustrating’ is the most appropriate answer.",
                    solution_hi: "5.(a) frustrating<br />‘Frustrating’ का अर्थ है क्रोध (anger) एवं झुँझलाहट (annoyance) की भावनाएँ उत्पन्न करना। दिए गए Passage में कहा गया है कि इससे अधिक निराशाजनक (frustrating) कुछ नहीं हो सकता कि आप अपनी मेज पर बहुत ही ईमानदारी से अध्ययन करने के लिए बैठते हैं और अपना कार्य पूरा करने के बजाय आप पाते हैं कि आपके विचार भटक रहे हैं। अतः, ‘frustrating’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <strong>Cloze Test:</strong><br>There is nothing more (5)__________than when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts (6)______ However, there are certain techniques that you can use to (7)_________your concentration. To begin with, one should (8)_________to create the physical environment that is conducive to (9)_________thought.<br>Select the most appropriate option to fill in blank no. 6.</p>",
                    question_hi: "<p>6. <strong>Cloze Test:</strong><br>There is nothing more (5)__________than when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts (6)______ However, there are certain techniques that you can use to (7)_________your concentration. To begin with, one should (8)_________to create the physical environment that is conducive to (9)_________thought.<br>Select the most appropriate option to fill in blank no. 6.</p>",
                    options_en: ["<p>wandering</p>", "<p>interesting</p>", 
                                "<p>tiring</p>", "<p>worrying</p>"],
                    options_hi: ["<p>wandering</p>", "<p>interesting</p>",
                                "<p>tiring</p>", "<p>worrying</p>"],
                    solution_en: "<p>6.(a) wandering<br>&lsquo;Wandering&rsquo; means thinking about subjects other than the one that needs attention. The given passage states that there is nothing more frustrating that when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts wandering. Hence, &lsquo;wandering&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>6.(a) wandering<br>&lsquo;Wandering&rsquo; का अर्थ है किसी विषय पर ध्यान केंद्रित करने के बजाय अन्य विषयों के बारे में सोचना। दिए गए Passage में कहा गया है कि इससे अधिक निराशाजनक (frustrating) कुछ नहीं हो सकता कि आप अपनी मेज पर बहुत ही ईमानदारी से अध्ययन करने के लिए बैठते हैं और अपना कार्य पूरा करने के बजाय आप पाते हैं कि आपके विचार भटक रहे हैं। अतः, &lsquo;wandering&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. <strong>Cloze Test:</strong><br>There is nothing more (5)__________than when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts (6)______ However, there are certain techniques that you can use to (7)_________your concentration. To begin with, one should (8)_________to create the physical environment that is conducive to (9)_________thought.<br>Select the most appropriate option to fill in blank no. 7.</p>",
                    question_hi: "<p>7. <strong>Cloze Test:</strong><br>There is nothing more (5)__________than when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts (6)______ However, there are certain techniques that you can use to (7)_________your concentration. To begin with, one should (8)_________to create the physical environment that is conducive to (9)_________thought.<br>Select the most appropriate option to fill in blank no. 7.</p>",
                    options_en: ["<p>enhance</p>", "<p>commit</p>", 
                                "<p>discover</p>", "<p>convince</p>"],
                    options_hi: ["<p>enhance</p>", "<p>commit</p>",
                                "<p>discover</p>", "<p>convince</p>"],
                    solution_en: "<p>7.(a) enhance<br>&lsquo;Enhance&rsquo; means to increase the value, quality or extent of something. The given passage states that there are certain techniques that you can use to enhance your concentration. Hence, &lsquo;enhance&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>7.(a) enhance<br>&lsquo;Enhance&rsquo; का अर्थ है किसी चीज़ का मूल्य, गुणवत्ता या सीमा बढ़ाना। दिए गए Passage में कहा गया है कि कुछ ऐसी तकनीकें (techniques) हैं जिनका उपयोग करके आप अपनी एकाग्रता (concentration) बढ़ा सकते हैं। अतः &lsquo;enhance&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <strong>Cloze Test:</strong><br>There is nothing more (5)__________than when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts (6)______ However, there are certain techniques that you can use to (7)_________your concentration. To begin with, one should (8)_________to create the physical environment that is conducive to (9)_________thought.<br>Select the most appropriate option to fill in blank no. 8.</p>",
                    question_hi: "<p>8.<strong>Cloze&nbsp; Test:</strong></p>\n<p>There is nothing more (5)__________than when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts (6)______ However, there are certain techniques that you can use to (7)_________your concentration. To begin with, one should (8)_________to create the physical environment that is conducive to (9)_________thought.<br>Select the most appropriate option to fill in blank no. 8.</p>",
                    options_en: [" attempt ", " imagine  ", 
                                " announce ", " lead"],
                    options_hi: [" attempt ", " imagine  ",
                                " announce ", " lead"],
                    solution_en: "8.(a) attempt<br />‘Attempt’ means to make an effort to achieve something. The given passage states that one should attempt to create the physical environment conducive to focussed thought. Hence, ‘attempt’ is the most appropriate answer.",
                    solution_hi: "8.(a) attempt<br />‘Attempt’ का अर्थ है किसी चीज़ को प्राप्त करने के लिए प्रयास करना। दिए गए Passage में कहा गया है कि व्यक्ति को ऐसे भौतिक वातावरण (physical environment) को बनाने का प्रयास करना चाहिए जो केंद्रित विचार (focussed thought) के लिए अनुकूल हो। अतः, ‘attempt’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <strong>Cloze Test:</strong><br>There is nothing more (5)__________than when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts (6)______ However, there are certain techniques that you can use to (7)_________your concentration. To begin with, one should (8)_________to create the physical environment that is conducive to (9)_________thought.<br>Select the most appropriate option to fill in blank no.9.</p>",
                    question_hi: "<p>9. <strong>Cloze Test:</strong><br>There is nothing more (5)__________than when you sit down at your table to study with the most sincere of intentions and instead of being able to finish the task at hand you find your thoughts (6)______ However, there are certain techniques that you can use to (7)_________your concentration. To begin with, one should (8)_________to create the physical environment that is conducive to (9)_________thought.<br>Select the most appropriate option to fill in blank no.9.</p>",
                    options_en: [" focussed ", " joyful  ", 
                                " cautious ", " dazzling"],
                    options_hi: [" focussed ", " joyful  ",
                                " cautious ", " dazzling"],
                    solution_en: "9.(a)  focussed<br />‘Focussed’ means having a clear and definite purpose. The given passage states that one should attempt to create the physical environment conducive to focussed thought. Hence, ‘focussed’ is the most appropriate answer.",
                    solution_hi: "9.(a)  focussed<br />‘Focussed’ का अर्थ है स्पष्ट (clear) एवं निश्चित उद्देश्य (definite purpose) होना। दिए गए Passage में कहा गया है कि व्यक्ति को ऐसे भौतिक वातावरण (physical environment) को बनाने का प्रयास करना चाहिए जो केंद्रित विचार (focussed thought) के लिए अनुकूल हो। अतः, ‘focussed’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10.  Select the most appropriate ANTONYM of the given word.<br />Innovate",
                    question_hi: "10.  Select the most appropriate ANTONYM of the given word.<br />Innovate",
                    options_en: [" Solve", " Finance ", 
                                " Invent  ", " Copy "],
                    options_hi: [" Solve", " Finance ",
                                " Invent  ", " Copy "],
                    solution_en: "<p>10.(d) <strong>Copy-</strong> to do something that is same as an original piece of work.<br><strong>Innovate-</strong> to introduce changes and new ideas.<br><strong>Solve</strong>- to find an answer to a problem.<br><strong>Finance</strong>- to provide the money needed for something to happen.<br><strong>Invent-</strong> to think of or make something for the first time.</p>",
                    solution_hi: "<p>10.(d) <strong>Copy</strong> (नकल करना) - to do something that is same as an original piece of work.<br><strong>Innovate </strong>(नवाचार) - to introduce changes and new ideas.<br><strong>Solve</strong> (समाधान करना) - to find an answer to a problem.<br><strong>Finance</strong> (वित्तीय सहायता करना) - to provide the money needed for something to happen.<br><strong>Invent</strong> (आविष्कार करना) - to think of or make something for the first time.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the correct spelling of the underlined word in the given sentence.<br>She decided to file a complaint with HR after being <span style=\"text-decoration: underline;\">harrasad</span> by her supervisor for&nbsp;several weeks.</p>",
                    question_hi: "<p>11. Select the correct spelling of the underlined word in the given sentence.<br>She decided to file a complaint with HR after being <span style=\"text-decoration: underline;\">harrasad</span> by her supervisor for&nbsp;several weeks.</p>",
                    options_en: ["<p>harrased</p>", "<p>harassed</p>", 
                                "<p>harrassd</p>", "<p>harased</p>"],
                    options_hi: ["<p>harrased</p>", "<p>harassed</p>",
                                "<p>harrassd</p>", "<p>harased</p>"],
                    solution_en: "<p>11.(b) harassed<br>&lsquo;Harassed&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>11.(b) harassed<br>&lsquo;Harassed&rsquo; सही spelling है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the most appropriate option that can substitute the underlined word in the following sentence.<br>They studied <span style=\"text-decoration: underline;\">during</span> three hours.</p>",
                    question_hi: "<p>12. Select the most appropriate option that can substitute the underlined word in the following sentence.<br>They studied <span style=\"text-decoration: underline;\">during</span> three hours.</p>",
                    options_en: [" for ", " by ", 
                                "  at", " from<br /> "],
                    options_hi: [" for ", " by ",
                                "  at", " from"],
                    solution_en: "12.(a) for<br />“For” is used for a fixed period of time(countable) like 2 years/3 months/4 days/8 hours and for an activity that happened in a recent time. Hence, ‘for three hours’ is the most appropriate answer.",
                    solution_hi: "12.(a) for<br />\"For\" का प्रयोग Fixed period of time(countable), जैसे 2 years/3 months/4 days/8 hours तथा हाल ही में हुई किसी गतिविधि (activity) के लिए किया जाता है। अतः, ‘for three hours’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "13. Select the most appropriate synonym of the given word.<br />Estimate",
                    question_hi: "13. Select the most appropriate synonym of the given word.<br />Estimate",
                    options_en: [" Appropriate ", " Evaluate  ", 
                                " Profit ", " Verify"],
                    options_hi: [" Appropriate ", " Evaluate  ",
                                " Profit ", " Verify"],
                    solution_en: "<p>13.(b) <strong>Evaluate-</strong> to judge or calculate the quality, importance, amount of sth.<br><strong>Estimate-</strong> a guess or judgement about something.<br><strong>Appropriate-</strong> suitable or right for a particular situation.<br><strong>Profit-</strong> financial gain.<br><strong>Verify-</strong> to prove that something exists or is true.</p>",
                    solution_hi: "<p>13.(b) <strong>Evaluate</strong> (मूल्यांकन करना) - to judge or calculate the quality, importance, amount of sth.<br><strong>Estimate</strong> (आकलन करना) - a guess or judgement about something.<br><strong>Appropriate</strong> (उपयुक्त) - suitable or right for a particular situation.<br><strong>Profit </strong>(लाभ) - financial gain.<br><strong>Verify</strong> (सत्यापित करना) - to prove that something exists or is true.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>We shall rehearse thrice before the final performance <span style=\"text-decoration: underline;\">at least we make errors</span> in the dialogues.</p>",
                    question_hi: "<p>14. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>We shall rehearse thrice before the final performance <span style=\"text-decoration: underline;\">at least we make errors</span> in the dialogues.</p>",
                    options_en: [" can we make errors ", " for we make errors", 
                                " lest we make errors  ", " but we make errors"],
                    options_hi: [" can we make errors ", " for we make errors",
                                " lest we make errors  ", " but we make errors"],
                    solution_en: "14.(c)  lest we make errors<br />‘Lest’ is used in order to prevent something from happening. The given sentence talks about rehearsing thrice in order to prevent errors in the dialogues. Hence, ‘lest we make errors’ is the most appropriate answer.",
                    solution_hi: "14.(c)  lest we make errors<br />‘Lest’ का प्रयोग किसी चीज़ को होने से रोकने के लिए किया जाता है। दिया गया Sentence, संवादों (dialogues) में त्रुटियों को रोकने के लिए तीन बार पूर्वाभ्यास (rehearsing) करने की बात करता है। अतः, ‘lest we make errors’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the most appropriate idiom to fill the blank in the given situation.<br>They were waiting for their family members who were returning from a pilgrimage.<br>Therefore, they were_______________on the platform.</p>",
                    question_hi: "<p>15. Select the most appropriate idiom to fill the blank in the given situation.<br>They were waiting for their family members who were returning from a pilgrimage.<br>Therefore, they were_______________on the platform.</p>",
                    options_en: ["<p>running behind time</p>", "<p>playing a joke</p>", 
                                "<p>rolling up their sleeves</p>", "<p>pacing up and down</p>"],
                    options_hi: ["<p>running behind time</p>", "<p>playing a joke</p>",
                                "<p>rolling up their sleeves</p>", "<p>pacing up and down</p>"],
                    solution_en: "<p>15.(d) <strong>Pacing up and down</strong>- walking back and forth due to anxiety or impatience.</p>",
                    solution_hi: "<p>15.(d)<strong> Pacing up and down-</strong> walking back and forth due to anxiety or impatience./चिंता या अधीरता के कारण आगे-पीछे चलना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "16. Select the INCORRECTLY spelt word.",
                    question_hi: "16. Select the INCORRECTLY spelt word.",
                    options_en: [" Stretch ", " Appraisal  ", 
                                " Genuine ", " Definate"],
                    options_hi: [" Stretch ", " Appraisal  ",
                                " Genuine ", " Definate"],
                    solution_en: "16.(d) Definate<br />‘Definite’ is the correct spelling.",
                    solution_hi: "16.(d) Definate<br />‘Definite’ सही spelling है। ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the most appropriate synonym of the underlined word.<br>The removal of <span style=\"text-decoration: underline;\">petty</span> restrictions has made life easier.</p>",
                    question_hi: "<p>17. Select the most appropriate synonym of the underlined word.<br>The removal of <span style=\"text-decoration: underline;\">petty</span> restrictions has made life easier.</p>",
                    options_en: [" Consequential ", " Small ", 
                                " Large ", " Big<br /> "],
                    options_hi: [" Consequential ", " Small ",
                                " Large ", " Big"],
                    solution_en: "<p>17.(b) <strong>Small</strong><br><strong>Petty</strong>- small or of little importance.<br><strong>Consequential-</strong> happening as a result of something.</p>",
                    solution_hi: "<p>17.(b) <strong>Small</strong><br><strong>Petty</strong> (क्षुद्र) - small or of little importance.<br><strong>Consequential </strong>(परिणामी) - happening as a result of something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "<p>18. Select the most appropriate option that can substitute the underlined words in the&nbsp;given sentence.<br>This newly arisen evidence <span style=\"text-decoration: underline;\">could have proved</span> to finally put the culprit behind bars.</p>",
                    question_hi: "<p>18. Select the most appropriate option that can substitute the underlined words in the given sentence.<br>This newly arisen evidence <span style=\"text-decoration: underline;\">could have proved</span> to finally put the culprit behind bars.</p>",
                    options_en: ["<p>could have been proven</p>", "<p>can be proven</p>", 
                                "<p>is proof</p>", "<p>can proof</p>"],
                    options_hi: ["<p>could have been proven</p>", "<p>can be proven</p>",
                                "<p>is proof</p>", "<p>can proof</p>"],
                    solution_en: "<p>18.(c) is proof<br>Evidence does not need to be proved. Evidence itself is proof that something happened or is true. Hence, &lsquo;is proof&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>18.(c) is proof<br>Evidence को सिद्ध करने की आवश्यकता नहीं होती। Evidence स्वयं ही इस बात का प्रमाण (proof) है कि कुछ हुआ है या सच है। अतः, &lsquo;is proof&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the most appropriate ANTONYM for the underlined word in the given sentence.<br>Because the politician made a <span style=\"text-decoration: underline;\">candid</span> speech, he earned the respect of the voters.</p>",
                    question_hi: "<p>19. Select the most appropriate ANTONYM for the underlined word in the given sentence.<br>Because the politician made a <span style=\"text-decoration: underline;\">candid</span> speech, he earned the respect of the voters.</p>",
                    options_en: [" Reckless", " Reticent", 
                                " Deceitful", " Prudent"],
                    options_hi: [" Reckless", " Reticent",
                                " Deceitful", " Prudent"],
                    solution_en: "<p>19.(c) <strong>Deceitful</strong>- misleading or dishonest.<br><strong>Candid-</strong> truthful and straightforward.<br><strong>Reckless-</strong> acting without thinking about consequences.<br><strong>Reticent</strong>- not revealing one\'s thoughts or feelings readily.<br><strong>Prudent</strong>- acting with care and thought for the future.</p>",
                    solution_hi: "<p>19.(c) <strong>Deceitful</strong> (धोखेबाज़) - misleading or dishonest.<br><strong>Candid </strong>(निष्कपट) - truthful and straightforward.<br><strong>Reckless</strong> (लापरवाह) - acting without thinking about consequences.<br><strong>Reticent</strong> (मितभाषी) - not revealing one\'s thoughts or feelings readily.<br><strong>Prudent</strong> (विवेकशील) - acting with care and thought for the future.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the most appropriate ANTONYM of the underlined word.<br>The government is planning to <span style=\"text-decoration: underline;\">abolish</span> child care leave for female employees.</p>",
                    question_hi: "<p>20. Select the most appropriate ANTONYM of the underlined word.<br>The government is planning to <span style=\"text-decoration: underline;\">abolish</span> child care leave for female employees.</p>",
                    options_en: [" Ensure ", " Eradicate ", 
                                " Encourage ", " Establish "],
                    options_hi: [" Ensure ", " Eradicate ",
                                " Encourage ", " Establish "],
                    solution_en: "<p>20.(d) <strong>Establish-</strong> to begin something.<br><strong>Abolish</strong>- to end a law or system officially.<br><strong>Ensure-</strong> to make something certain to happen.<br><strong>Eradicate</strong>- to get rid of something completely.<br><strong>Encourage-</strong> to give support, confidence, or motivation to someone.</p>",
                    solution_hi: "<p>20.(d) <strong>Establish </strong>(स्थापित करना) - to begin something.<br><strong>Abolish</strong> (समाप्त करना) - to end a law or system officially.<br><strong>Ensure</strong> (सुनिश्चित करना) - to make something certain to happen.<br><strong>Eradicate </strong>(जड़ से मिटाना) - to get rid of something completely.<br><strong>Encourage</strong> (प्रोत्साहित करना) - to give support, confidence, or motivation to someone.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. Select the most appropriate meaning of the underlined idiom in the following&nbsp;sentence.<br>Manish told Rounak <span style=\"text-decoration: underline;\">time and again</span> of the ill consequences of his habit of being late to&nbsp;the college.</p>",
                    question_hi: "<p>21. Select the most appropriate meaning of the underlined idiom in the following&nbsp;sentence.<br>Manish told Rounak time and again of the ill consequences of his habit of being late to&nbsp;the college.</p>",
                    options_en: [" Not often ", " Hardly ever", 
                                " Repeatedly", " At intervals"],
                    options_hi: [" Not often ", " Hardly ever",
                                " Repeatedly", " At intervals"],
                    solution_en: "<p>21.(c) <strong>Time and again-</strong> repeatedly.</p>",
                    solution_hi: "<p>21.(c) <strong>Time and again</strong>- repeatedly./बार-बार।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "22. Select the most appropriate option to fill in the blank.<br />I am doubtful about his story, though you seem to be_____",
                    question_hi: "22. Select the most appropriate option to fill in the blank.<br />I am doubtful about his story, though you seem to be_____",
                    options_en: [" pleasant ", " uncertain ", 
                                " regretting ", " convinced"],
                    options_hi: [" pleasant ", " uncertain ",
                                " regretting ", " convinced"],
                    solution_en: "22.(d) convinced<br />‘Convinced’ means to be completely certain or assured about something. The given sentence states that I am doubtful about his story, though you seem to be convinced. Hence, ‘convinced’ is the most appropriate answer.",
                    solution_hi: "22.(d) convinced<br />‘Convinced’ का अर्थ है किसी बात को लेकर पूरी तरह से आश्वस्त होना। दिए गए Sentence में कहा गया है कि मुझे उसकी कहानी पर संदेह है, यद्यपि आप आश्वस्त प्रतीत होते हैं। अतः, ‘convinced’ सबसे उपयुक्त उत्तर है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. Select the most appropriate option that can substitute the underlined segment in the&nbsp;given sentence. If there is no need to substitute it, select \'No substitution required\'.<br>If you <span style=\"text-decoration: underline;\">give away</span> smoking, your health will improve.</p>",
                    question_hi: "<p>23. Select the most appropriate option that can substitute the underlined segment in the&nbsp;given sentence. If there is no need to substitute it, select \'No substitution required\'.<br>If you <span style=\"text-decoration: underline;\">give away</span> smoking, your health will improve.</p>",
                    options_en: ["<p>give off</p>", "<p>give out</p>", 
                                "<p>No substitution required</p>", "<p>give up</p>"],
                    options_hi: ["<p>give off</p>", "<p>give out</p>",
                                "<p>No substitution required</p>", "<p>give up</p>"],
                    solution_en: "<p>23.(d) give up<br>The phrasal verb &lsquo;give up&rsquo; means to stop doing something. The given sentence states that if you give up smoking, your health will improve. Hence, &lsquo;give up&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(d) give up<br>Phrasal verb &lsquo;give up&rsquo; का अर्थ है कुछ करना बंद करना। दिए गए Sentence में कहा गया है कि यदि आप धूम्रपान (smoking) छोड़ देते हैं, तो आपका स्वास्थ्य (health) बेहतर हो जाएगा। अतः, &lsquo;give up&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "24.  Select the most appropriate option to fill in the blank.<br />Seema has a dominating nature, so she wants to_________of everything in her life.",
                    question_hi: "24.  Select the most appropriate option to fill in the blank.<br />Seema has a dominating nature, so she wants to_________of everything in her life.",
                    options_en: [" keep control ", " keep record", 
                                " keep calm ", " keep touch"],
                    options_hi: [" keep control ", " keep record",
                                " keep calm ", " keep touch"],
                    solution_en: "24.(a) keep control<br />The given sentence states that Seema has a dominant nature, so she wants to keep control of everything in her life. Hence, ‘keep control’ is the most appropriate answer.",
                    solution_hi: "24.(a) keep control<br />दिए गए Sentence में कहा गया है कि सीमा का स्वभाव प्रभुत्वशाली (dominant) है, इसलिए वह अपने जीवन में हर चीज पर नियंत्रण रखना चाहती है। अतः, ‘keep control’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "25. Select the most appropriate ANTONYM of the given word.<br />Powerful",
                    question_hi: "25. Select the most appropriate ANTONYM of the given word.<br />Powerful",
                    options_en: [" Strong ", " Manly  ", 
                                " Impotent ", " Wild"],
                    options_hi: [" Strong ", " Manly  ",
                                " Impotent ", " Wild"],
                    solution_en: "<p>25.(c) <strong>Impotent</strong>- helpless or powerless.<br><strong>Powerful</strong>- having great strength or force.<br><strong>Strong-</strong> powerful.<br><strong>Manly-</strong> having the qualities that people think man should have.<br><strong>Wild</strong>- living in a state of nature.</p>",
                    solution_hi: "<p>25.(c) <strong>Impotent</strong> (शक्तिहीन) - helpless or powerless.<br><strong>Powerful </strong>(शक्तिशाली) - having great strength or force.<br><strong>Strong </strong>(मजबूत) - powerful.<br><strong>Manly </strong>(मर्दाना) - having the qualities that people think man should have.<br><strong>Wild</strong> (जंगली) - living in a state of nature.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>