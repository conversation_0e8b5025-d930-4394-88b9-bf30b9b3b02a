<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. 17 is related to 136 following a certain logic. Following the same logic, 23 is related to&nbsp;184. To which of the following is 29 related following the same logic ?<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as&nbsp;adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1&nbsp;and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>1. एक निश्चित तर्क का अनुसरण करते हुए 17, 136 से संबंधित है। उसी तर्क का अनुसरण करते हुए 23,&nbsp;184 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 29 निम्नलिखित में से किस से संबंधित है ?<br>(<strong>ध्यान दें :</strong> संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की&nbsp;जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा&nbsp;करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर&nbsp;गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>228</p>",
                        "<p>234</p>",
                        "<p>230</p>",
                        "<p>232</p>"
                    ],
                    options_hi: [
                        "<p>228</p>",
                        "<p>234</p>",
                        "<p>230</p>",
                        "<p>232</p>"
                    ],
                    solution_en: "<p>1.(d) <strong>Logic :-</strong> (1st number) &times; 8 = 2nd number<br>(17, 136) :- (17) &times; 8 = 136<br>(23, 184) :- (23) &times; 8 = 184<br>Similarly,<br>(29, ?) :- (29) &times; 8 = 232</p>",
                    solution_hi: "<p>1.(d) <strong>तर्क :-</strong> (पहली संख्या) &times; 8 = दूसरी संख्या<br>(17, 136) :- (17) &times; 8 = 136<br>(23, 184) :- (23) &times; 8 = 184<br>इसी प्रकार,<br>(29, ?) :- (29) &times; 8 = 232</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Which of the following numbers will replace the question mark (?) in the given series ?<br>19, ?, 43, 55, 67</p>",
                    question_hi: "<p>2. दी गई श्रृंखला में निम्नलिखित में से कौन-सी संख्या प्रश्न चिन्ह (?) के स्थान पर आएगी ?<br>19, ?, 43, 55, 67</p>",
                    options_en: [
                        "<p>34</p>",
                        "<p>33</p>",
                        "<p>31</p>",
                        "<p>41</p>"
                    ],
                    options_hi: [
                        "<p>34</p>",
                        "<p>33</p>",
                        "<p>31</p>",
                        "<p>41</p>"
                    ],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478878900.png\" alt=\"rId4\" width=\"298\" height=\"67\"></p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478878900.png\" alt=\"rId4\" width=\"298\" height=\"67\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;;<br>&lsquo;A &ndash; B&rsquo; means &lsquo;A is the brother of B&rsquo;;<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo; and<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the father of B&rsquo;.<br>Based on the above, if M + Q &ndash; R + K &ndash; Y, then how is M related to Y ?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में,<br>&lsquo;A + B&rsquo; का अर्थ है \'A, B की मां है\';<br>&lsquo;A &ndash; B&rsquo; का अर्थ है \'A, B का भाई है\';<br>&lsquo;A &times; B&rsquo; का अर्थ है \'A, B की पत्नी है\' और<br>&lsquo;A &divide; B&rsquo; का अर्थ है \'A, B का पिता है\'।<br>उपरोक्त के आधार पर, यदि M + Q &ndash; R + K &ndash; Y है, तो M का Y से क्&zwj;या संबंध है ?</p>",
                    options_en: [
                        "<p>Father&rsquo;s mother</p>",
                        "<p>Brother&rsquo;s wife</p>",
                        "<p>Mother&rsquo;s mother</p>",
                        "<p>Mother</p>"
                    ],
                    options_hi: [
                        "<p>पिता की मां (दादी)</p>",
                        "<p>भाई की पत्&zwj;नी (भाभी)</p>",
                        "<p>मां की मां (नानी)</p>",
                        "<p>मां</p>"
                    ],
                    solution_en: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478879133.png\" alt=\"rId5\" width=\"191\" height=\"117\"><br>Hence, M is the mother&rsquo;s mother of Y.</p>",
                    solution_hi: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478879133.png\" alt=\"rId5\" width=\"191\" height=\"117\"><br>अतः, M, Y की नानी है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Two different positions of the same dice are given below. Which is the number on the face opposite the face containing 5 ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478879415.png\" alt=\"rId6\" width=\"175\" height=\"70\"></p>",
                    question_hi: "<p>4. नीचे एक ही पासे की दो अलग-अलग स्थितियाँ दर्शाई गई हैं। \'5\' दर्शाने वाले फलक के विपरीत वाले फलक पर कौन-सी संख्या होगी ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478879415.png\" alt=\"rId6\" width=\"175\" height=\"70\"></p>",
                    options_en: [
                        "<p>6</p>",
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>6</p>",
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>4.(b) From the two dice the opposite faces are <br>2 &harr; 6, 1 &harr; 4, 3 &harr; 5</p>",
                    solution_hi: "<p>4.(b) दो पासों के विपरीत फलक हैं<br>2 &harr; 6, 1 &harr; 4, 3 &harr; 5</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. What will come in the place of &lsquo;?&rsquo; in the following equation if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are&nbsp;interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged ?<br>18 &divide; 9 + 24 &times; 3 &ndash; 16 = ?</p>",
                    question_hi: "<p>5. यदि \'+\' और \'-\' को आपस में बदल दिया जाए तथा \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए तो निम्नलिखित समीकरण में (?) के स्थान पर क्या आएगा ?<br>18 &divide; 9 + 24 &times; 3 &ndash; 16 = ?</p>",
                    options_en: [
                        "<p>678</p>",
                        "<p>980</p>",
                        "<p>450</p>",
                        "<p>170</p>"
                    ],
                    options_hi: [
                        "<p>678</p>",
                        "<p>980</p>",
                        "<p>450</p>",
                        "<p>170</p>"
                    ],
                    solution_en: "<p>5.(d) <strong>Given :-</strong> 18 &divide; 9 + 24 &times; 3 - 16<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo;<br>18 &times; 9 - 24 &divide; 3 + 16<br>162 - 8 + 16 = 170</p>",
                    solution_hi: "<p>5.(d) <strong>दिया गया :- </strong>18 &divide; 9 + 24 &times; 3 - 16<br>दिए गए निर्देश के अनुसार \'+\' और \'-\' और \'&times;\' और \'&divide;\' को आपस में बदलने के बाद<br>18 &times; 9 - 24 &divide; 3 + 16<br>162 - 8 + 16 = 170</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Identify the figure from among the given options which when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478879631.png\" alt=\"rId7\" width=\"348\" height=\"73\"></p>",
                    question_hi: "<p>6. दिए गए विकल्पों में से उस आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478879631.png\" alt=\"rId7\" width=\"348\" height=\"73\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880100.png\" alt=\"rId8\" width=\"75\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880286.png\" alt=\"rId9\" width=\"75\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880435.png\" alt=\"rId10\" width=\"75\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880598.png\" alt=\"rId11\" width=\"75\" height=\"77\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880100.png\" alt=\"rId8\" width=\"76\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880286.png\" alt=\"rId9\" width=\"75\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880435.png\" alt=\"rId10\" width=\"75\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880598.png\" alt=\"rId11\" width=\"76\" height=\"78\"></p>"
                    ],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880435.png\" alt=\"rId10\" width=\"76\" height=\"77\"></p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880435.png\" alt=\"rId10\" width=\"76\" height=\"77\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. How many rectangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880754.png\" alt=\"rId12\" width=\"133\" height=\"91\"></p>",
                    question_hi: "<p>7. दी गई आकृति में कितने आयत हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880754.png\" alt=\"rId12\" width=\"133\" height=\"91\"></p>",
                    options_en: [
                        " 31 ",
                        " 32 ",
                        " 33 ",
                        " 34"
                    ],
                    options_hi: [
                        " 31 ",
                        " 32 ",
                        " 33 ",
                        " 34"
                    ],
                    solution_en: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880938.png\" alt=\"rId13\" width=\"173\" height=\"131\"><br>There are 31 rectangle <br>ABFE, BCGF, CDHG, ACGE, BDHF, ADHE, ABJI, BCKJ, CDLK, ACKI, BDLJ, ADLI, EFJI, FGKJ, GHLK, EGKI, FHLJ, EHLI, KLNM , GHNM, CDNM, CDPO, KLPO, MNPO, GHPO, ILPQ, IKOQ, ACOQ, EGOQ, EHPQ, ADPQ</p>",
                    solution_hi: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478880938.png\" alt=\"rId13\" width=\"173\" height=\"131\"><br>31 आयत हैं<br>ABFE, BCGF, CDHG, ACGE, BDHF, ADHE, ABJI, BCKJ, CDLK, ACKI, BDLJ, ADLI, EFJI, FGKJ, GHLK, EGKI, FHLJ, EHLI, KLNM , GHNM, CDNM, CDPO, KLPO, MNPO, GHPO, ILPQ, IKOQ, ACOQ, EGOQ, EHPQ, ADPQ</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881164.png\" alt=\"rId14\" width=\"118\" height=\"117\"></p>",
                    question_hi: "<p>8. जब दर्पण को MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881164.png\" alt=\"rId14\" width=\"118\" height=\"117\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881305.png\" alt=\"rId15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881470.png\" alt=\"rId16\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881629.png\" alt=\"rId17\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881769.png\" alt=\"rId18\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881305.png\" alt=\"rId15\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881470.png\" alt=\"rId16\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881629.png\" alt=\"rId17\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881769.png\" alt=\"rId18\"></p>"
                    ],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881470.png\" alt=\"rId16\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478881470.png\" alt=\"rId16\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the set in which the numbers are related in the same way as are the numbers&nbsp;of the followingsets.<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as&nbsp;adding /subtracting /multiplying to 13 can be performed. Breaking down 13 into 1 and&nbsp;3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(7, 11, 33)<br>(14, 18, 54)</p>",
                    question_hi: "<p>9. उस सेट का चयन कीजिए, जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित सेटों की संख्याएं संबंधित हैं।<br>(<strong>नोट:</strong> संख्याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 को लीजिए 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>(7, 11, 33)<br>(14, 18, 54)</p>",
                    options_en: [
                        "<p>(16, 20, 40)</p>",
                        "<p>(5, 9, 36)</p>",
                        "<p>(12, 16, 48)</p>",
                        "<p>(18, 21, 63)</p>"
                    ],
                    options_hi: [
                        "<p>(16, 20, 40)</p>",
                        "<p>(5, 9, 36)</p>",
                        "<p>(12, 16, 48)</p>",
                        "<p>(18, 21, 63)</p>"
                    ],
                    solution_en: "<p>9.(c) <strong>Logic:-</strong> (2nd number) = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>3</mn><mi>r</mi><mi>d</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math>)<br>(11) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>33</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>4</mn></mfrac></math> = 11<br>(18) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>54</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>72</mn><mn>4</mn></mfrac></math>= 18<br>Similarly,<br>(16) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>48</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>4</mn></mfrac></math> = 16</p>",
                    solution_hi: "<p>9.(c) <strong>तर्क:-</strong> (दूसरी संख्या) = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>&#2340;&#2368;&#2360;&#2352;&#2368;</mi><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math>)<br>(11) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>33</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>44</mn><mn>4</mn></mfrac></math> = 11<br>(18) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>54</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>72</mn><mn>4</mn></mfrac></math> = 18<br>इसी प्रकार,<br>(16) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>48</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>4</mn></mfrac></math> = 16</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different.&nbsp;<br>(<strong>Note : </strong>The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>10. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है ?<br>(<strong>ध्यान दें :</strong> असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>OMJ</p>",
                        "<p>PNK</p>",
                        "<p>BZW</p>",
                        "<p>RTQ</p>"
                    ],
                    options_hi: [
                        "<p>OMJ</p>",
                        "<p>PNK</p>",
                        "<p>BZW</p>",
                        "<p>RTQ</p>"
                    ],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478882021.png\" alt=\"rId19\" width=\"113\" height=\"67\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478882255.png\" alt=\"rId20\" width=\"110\" height=\"65\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478882510.png\" alt=\"rId21\" width=\"114\" height=\"65\"><br>But, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478882715.png\" alt=\"rId22\" width=\"108\" height=\"63\"></p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478882021.png\" alt=\"rId19\" width=\"113\" height=\"67\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478882255.png\" alt=\"rId20\" width=\"110\" height=\"65\"> ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478882510.png\" alt=\"rId21\" width=\"114\" height=\"65\"><br>लेकिन, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478882715.png\" alt=\"rId22\" width=\"108\" height=\"63\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language, SAD is coded as 48 and DOG is coded as 52. What is the code for HIT in that language ?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में, SAD को 48 के रूप में कूटबद्ध किया जाता है और DOG को 52 के रूप में कूटबद्ध किया जाता है। उसी भाषा में HIT के लिए क्या कूट है ?</p>",
                    options_en: [
                        "<p>73</p>",
                        "<p>74</p>",
                        "<p>47</p>",
                        "<p>37</p>"
                    ],
                    options_hi: [
                        "<p>73</p>",
                        "<p>74</p>",
                        "<p>47</p>",
                        "<p>37</p>"
                    ],
                    solution_en: "<p>11.(b) <strong>Logic :-</strong> (Sum of the place value of letter) &times; 2 <br>SAD :- (19 + 1 + 4) &times; 2 &rArr; (24) &times; 2 = 48<br>DOG :- (4 + 15 + 7) &times; 2 &rArr; (26) &times; 2 = 52<br>Similarly,<br>HIT :- (8 + 9 + 20) &times; 2 &rArr; (37) &times; 2 = 74</p>",
                    solution_hi: "<p>11.(b) <strong>तर्क :-</strong> (अक्षर के स्थानीय मान का योग) &times; 2<br>SAD :- (19 + 1 + 4) &times; 2 &rArr; (24) &times; 2 = 48<br>DOG :- (4 + 15 + 7) &times; 2 &rArr; (26) &times; 2 = 52<br>इसी प्रकार,<br>HIT :- (8 + 9 + 20) &times; 2 &rArr; (37) &times; 2 = 74</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Which two numbers should be interchanged to make the given equation correct ?<br>(86 &divide; 43) + (66 &divide; 33) &times; 2 + 72 &ndash; 6 &times; 4 = 177<br>(<strong>NOTE : </strong>Numbers must be interchanged and not the constituent digits e.g. if 2 and 3&nbsp;are to be interchanged in the equation 43 &times; 3 + 4 &divide; 2, then interchanged equation is 43&nbsp;&times; 2 + 4 &divide; 3)</p>",
                    question_hi: "<p>12. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं को आपस में बदला जाना चाहिए?<br>(86 &divide; 43) + (66 &divide; 33) &times; 2 + 72 &ndash; 6 &times; 4 = 177<br>(<strong>ध्यान दें : </strong>संख्याओं को आपस में बदला जाना चाहिए और घटक अंकों को नहीं बदला जाना चाहिए।<br>उदाहरण: यदि समीकरण 43 &times; 3 + 4 &divide; 2 में 2 और 3 को आपस में बदलना है, तो बदला हुआ समीकरण 43 &times; 2 + 4 &divide; 3 होगा)</p>",
                    options_en: [
                        "<p>43 and 2</p>",
                        "<p>2 and 72</p>",
                        "<p>2 and 6</p>",
                        "<p>2 and 4</p>"
                    ],
                    options_hi: [
                        "<p>43 और 2</p>",
                        "<p>2 और 72</p>",
                        "<p>2 और 6</p>",
                        "<p>2 और 4</p>"
                    ],
                    solution_en: "<p>12.(a) <strong>Given :-</strong> (86 &divide; 43) + (66 &divide; 33) &times; 2 + 72 - 6 &times; 4 = 177<br>After checking all the options, option (a) satisfies. After interchanging 43 and 2 we get<br>(86 &divide; 2) + (66 &divide; 33) &times; 43 + 72 - 6 &times; 4&nbsp;<br>43 + (2) &times; 43 + 72 - 24<br>43 + 86 + 72 - 24<br>201 - 24 = 177<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>12.(a) <strong>दिया गया :-</strong> (86 &divide; 43) + (66 &divide; 33) &times; 2 + 72 - 6 &times; 4 = 177<br>सभी विकल्पों की जांच करने पर विकल्प (a) संतुष्ट करता है। 43 और 2 को आपस में बदलने पर हमें प्राप्त होता है<br>(86 &divide; 2) + (66 &divide; 33) &times; 43 + 72 - 6 &times; 4&nbsp;<br>43 + (2) &times; 43 + 72 - 24<br>43 + 86 + 72 - 24<br>201 - 24 = 177<br>L.H.S. = R.H.S.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.&nbsp;<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.) <br>Antibiotic : Infection</p>",
                    question_hi: "<p>13. उस शब्द-युग्म का चयन कीजिए जो दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है। (शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या /व्यंजन/ स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।) <br>प्रतिजैविक : संक्रमण</p>",
                    options_en: [
                        "<p>Anticlimax : Episode</p>",
                        "<p>Purgative : Eating</p>",
                        "<p>Antidote : Poisoning</p>",
                        "<p>Antibody : Braise</p>"
                    ],
                    options_hi: [
                        "<p>दुखांत : एपिसोड</p>",
                        "<p>शोधक : खाना</p>",
                        "<p>विषहर : जहर देना</p>",
                        "<p>रोग-प्रतिकारक : दम देना</p>"
                    ],
                    solution_en: "<p>13.(c) As Antibiotic is medicine of Infection similarly Antidote is the medicine of Poisoning.</p>",
                    solution_hi: "<p>13.(c) जिस प्रकार प्रतिजैविक संक्रमण की दवा है उसी प्रकार विषहर जहर की दवा है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, \'BOOK\' is coded as \'DMRH\' and \'WELL\' is coded as \'YCOI\'. How will \'TREE\' be coded in the same language ?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में, \'BOOK\' को \'DMRH\' के रूप में कूटबद्ध किया जाता है और \'WELL\' को \'YCOI\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'TREE\' को किस प्रकार कूटबद्ध किया जाएगा ?</p>",
                    options_en: [
                        "<p>VSHC</p>",
                        "<p>UPHC</p>",
                        "<p>VPHB</p>",
                        "<p>UQHB</p>"
                    ],
                    options_hi: [
                        "<p>VSHC</p>",
                        "<p>UPHC</p>",
                        "<p>VPHB</p>",
                        "<p>UQHB</p>"
                    ],
                    solution_en: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478882919.png\" alt=\"rId23\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478883085.png\" alt=\"rId24\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478883274.png\" alt=\"rId25\"></p>",
                    solution_hi: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478882919.png\" alt=\"rId23\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478883085.png\" alt=\"rId24\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478883274.png\" alt=\"rId25\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the option that represents the letters that when sequentially placed from left to right in the blanks below will complete the letter series.<br>_ B _ _ _ A L _ _ L _ _</p>",
                    question_hi: "<p>15. उस विकल्प का चयन कीजिए जो उन अक्षरों को प्रदर्शित करता है जिन्हें नीचे दिए गए रिक्&zwj;त स्&zwj;थानों में बाएं से दाएं क्रमिक रूप से रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी।<br>_ B _ _ _ A L _ _ L _ _</p>",
                    options_en: [
                        "<p>LALBBABA</p>",
                        "<p>BBALLBAA</p>",
                        "<p>LBAALLBA</p>",
                        "<p>BLABBALL</p>"
                    ],
                    options_hi: [
                        "<p>LALBBABA</p>",
                        "<p>BBALLBAA</p>",
                        "<p>LBAALLBA</p>",
                        "<p>BLABBALL</p>"
                    ],
                    solution_en: "<p>15.(a)<br><span style=\"text-decoration: underline;\"><strong>L</strong></span> B <span style=\"text-decoration: underline;\"><strong>A</strong></span>/ <span style=\"text-decoration: underline;\"><strong>L</strong></span> <span style=\"text-decoration: underline;\"><strong>B</strong></span> A /L <span style=\"text-decoration: underline;\"><strong>B</strong></span> <span style=\"text-decoration: underline;\"><strong>A</strong></span>/ L <span style=\"text-decoration: underline;\"><strong>B</strong></span> <span style=\"text-decoration: underline;\"><strong>A</strong></span></p>",
                    solution_hi: "<p>15.(a)<br><span style=\"text-decoration: underline;\"><strong>L</strong></span> B <span style=\"text-decoration: underline;\"><strong>A</strong></span>/ <span style=\"text-decoration: underline;\"><strong>L</strong></span> <span style=\"text-decoration: underline;\"><strong>B</strong></span> A /L <span style=\"text-decoration: underline;\"><strong>B</strong></span> <span style=\"text-decoration: underline;\"><strong>A</strong></span>/ L <span style=\"text-decoration: underline;\"><strong>B</strong></span> <span style=\"text-decoration: underline;\"><strong>A</strong></span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In a certain code language,<br>\'A &amp; B\' means &lsquo; A is the brother of B&rsquo;,<br>\'A &times; B\' means &lsquo;A is the father of B&rsquo;,<br>\'A ? B\' means &lsquo;A is the mother of B&rsquo; and<br>\'A ! B\' means &lsquo;A is the wife of B&rsquo;.<br>Which of the following means that W is the mother\'s mother of P ?</p>",
                    question_hi: "<p>16. एक निश्चित कूट भाषा में,<br>\'A &amp; B\' का अर्थ है कि &lsquo;A, B का भाई है&rsquo;,<br>\'A &times; B\' का अर्थ है कि &lsquo;A, B का पिता है&rsquo;,<br>\'A ? B\' का अर्थ है कि &lsquo;A, B की माता है&rsquo; और<br>\'A ! B\' का अर्थ है कि &lsquo;A, B की पत्नी है&rsquo;,<br>निम्नलिखित में से किसका अर्थ यह है कि W, P की नानी है ?</p>",
                    options_en: [
                        "<p>W ? R ! Y &amp; O &times; P</p>",
                        "<p>W ? R &times; Y ! O &amp; P</p>",
                        "<p>W &amp; R ! Y &times; O ? P</p>",
                        "<p>W ! R &times; Y ? O &amp; P</p>"
                    ],
                    options_hi: [
                        "<p>W ? R ! Y &amp; O &times; P</p>",
                        "<p>W ? R &times; Y ! O &amp; P</p>",
                        "<p>W &amp; R ! Y &times; O ? P</p>",
                        "<p>W ! R &times; Y ? O &amp; P</p>"
                    ],
                    solution_en: "<p>16.(d)<br>On checking option one by one option (d) satisfies the condition.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478883419.png\" alt=\"rId26\" width=\"196\" height=\"145\"><br>Hence, option (d) shows that &lsquo;W&rsquo; is the mother&rsquo;s mother of P.</p>",
                    solution_hi: "<p>16.(d)<br>एक-एक करके विकल्प की जांच करने पर विकल्प (d) संतुष्ट करता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478883419.png\" alt=\"rId26\" width=\"196\" height=\"145\"><br>अतः, विकल्प (d) दर्शाता है कि &lsquo;W&rsquo;, P की मां की मां (नानी) है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. In a certain code language, \'right from wrong\' is written as \'jn bh yt\', and \'take the right\' is coded as \'dn yt gd&rsquo;. How will \'right\' be coded in that language ?</p>",
                    question_hi: "<p>17. एक निश्चित कूट भाषा में \'right from wrong\' को \'jn bh yt\' के रूप में कूटबद्ध किया जाता है और \'take the right\' को \'dn yt gd\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'right\' को किस प्रकार कूटबद्ध किया जाएगा ?</p>",
                    options_en: [
                        "<p>gd</p>",
                        "<p>bh</p>",
                        "<p>yt</p>",
                        "<p>jn</p>"
                    ],
                    options_hi: [
                        "<p>gd</p>",
                        "<p>bh</p>",
                        "<p>yt</p>",
                        "<p>jn</p>"
                    ],
                    solution_en: "<p>17.(c) right from wrong &rarr; jn bh yt&hellip;&hellip;.. (i)<br>take the right &rarr; dn yt gd&hellip;&hellip;. (ii)<br>From (i) and (ii) &lsquo;right&rsquo; and &lsquo;yt&rsquo; are common. The code of &lsquo;right&rsquo; = &lsquo;yt&rsquo;</p>",
                    solution_hi: "<p>17.(c) right from wrong &rarr; jn bh yt&hellip;&hellip;.. (i)<br>take the right &rarr; dn yt gd&hellip;&hellip;. (ii)<br>(i) और (ii) से \'right\' और \'yt\' उभयनिष्ठ हैं। \'right\' का कोड = \'yt\'</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Which of the following numbers will replace the question mark (?) in the given series ?&nbsp;<br>800 400 480 240 320 ?</p>",
                    question_hi: "<p>18. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आएगी ? <br>800 400 480 240 320 ?</p>",
                    options_en: [
                        "<p>160</p>",
                        "<p>180</p>",
                        "<p>200</p>",
                        "<p>120</p>"
                    ],
                    options_hi: [
                        "<p>160</p>",
                        "<p>180</p>",
                        "<p>200</p>",
                        "<p>120</p>"
                    ],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478883713.png\" alt=\"rId27\" width=\"338\" height=\"84\"></p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478883713.png\" alt=\"rId27\" width=\"338\" height=\"84\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Study the given diagram carefully and answer the question that follows. The numbers in different sections indicate the number of persons travelling by different modes of transport.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478884090.png\" alt=\"rId28\" width=\"187\" height=\"218\"> <br>How many people travel by bus and car both, but NOT by train ?&nbsp;</p>",
                    question_hi: "<p>19. दिए गए आरेख का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। विभिन्न वर्गों वाली संख्याएँ परिवहन के विभिन्न साधनों से यात्रा करने वाले व्यक्तियों की संख्या को दर्शाती हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478884284.png\" alt=\"rId29\" width=\"172\" height=\"204\"> <br>कितने व्यक्ति बस और कार दोनों से यात्रा करते हैं, लेकिन ट्रेन से यात्रा नहीं करते ?</p>",
                    options_en: [
                        "<p>19</p>",
                        "<p>27</p>",
                        "<p>9</p>",
                        "<p>17</p>"
                    ],
                    options_hi: [
                        "<p>19</p>",
                        "<p>27</p>",
                        "<p>9</p>",
                        "<p>17</p>"
                    ],
                    solution_en: "<p>19.(c)<br>People travel by bus and car both, but Not by train = 9</p>",
                    solution_hi: "<p>19.(c)<br>लोग बस और कार दोनों से यात्रा करते हैं, लेकिन ट्रेन से नहीं = 9</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. If 19 July 2012 is Thursday, then what will be the day of the week on 11 January 2018 ?</p>",
                    question_hi: "<p>20. यदि 19 जुलाई 2012 को गुरुवार है, तो 11 जनवरी 2018 को सप्ताह का कौन-सा दिन होगा ?</p>",
                    options_en: [
                        "<p>Wednesday</p>",
                        "<p>Saturday</p>",
                        "<p>Monday</p>",
                        "<p>Thursday</p>"
                    ],
                    options_hi: [
                        "<p>बुधवार</p>",
                        "<p>शनिवार</p>",
                        "<p>सोमवार</p>",
                        "<p>गुरुवार</p>"
                    ],
                    solution_en: "<p>20.(d) 19 July 2012 is thursday.<br>On moving to 2018 the number of odd days = +1 + 1 + 1 + 2 + 1 + 1 = 7. <br>On dividing 7 by 7 the remainder = 0. <br>Thursday + 0 = Thursday . <br>We have reached till 19 July, but we have to go back to 11 January,<br>the number of days between = 19 + 30 + 31 + 30 + 31 + 28 + 20 = 189. <br>On dividing 189 by 7 the remainder is 0. <br>Thursday - 0 = Thursday.</p>",
                    solution_hi: "<p>20.(d) 19 जुलाई 2012 को गुरुवार है। <br>2018 में जाने पर विषम दिनों की संख्या = +1 + 1 + 1 + 2 + 1 + 1 = 7. <br>7 को 7 से विभाजित करने पर शेषफल = 0. <br>गुरुवार + 0 = गुरुवार। <br>हम 19 जुलाई तक पहुंच गए हैं, लेकिन हमें 11 जनवरी तक वापस जाना है, <br>बीच में दिनों की संख्या = 19 + 30 + 31 + 30 + 31 + 28 + 20 = 189. <br>189 को 7 से विभाजित करने पर शेषफल 0 आता है. <br>गुरुवार - 0 = गुरूवार.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the option that is related to the fifth number in the same way as the second number is related to the first number and the fourth number is related to the third number.&nbsp;<br>4 : 68 :: 8 : 136 :: 9 : ? <br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>21. उस विकल्प का चयन करें जो पांचवीं संख्या से उसी प्रकार संबंधित हो जिस प्रकार दूसरी संख्या पहली संख्या से संबंधित है और चौथी संख्या तीसरी संख्या से संबंधित है। <br>4 : 68 :: 8 : 136 :: 9 : ? <br><strong>नोट:</strong> संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: [
                        "<p>152</p>",
                        "<p>149</p>",
                        "<p>147</p>",
                        "<p>153</p>"
                    ],
                    options_hi: [
                        "<p>152</p>",
                        "<p>149</p>",
                        "<p>147</p>",
                        "<p>153</p>"
                    ],
                    solution_en: "<p>21.(d)<br><strong>Logic:- </strong>1<sup>st</sup>no. &times; 17 = 2<sup>nd</sup>no.<br>(4 : 68) :- 4 &times; 17 = 68<br>(8 : 136) :- 8 &times; 17 = 136<br>(9 : ?) :- 9 &times; 17 = 153</p>",
                    solution_hi: "<p>21.(d)<br><strong>तर्क :-</strong> पहली संख्या &times; 17 = दूसरी संख्या <br>(4 : 68) :- 4 &times; 17 = 68<br>(8 : 136) :- 8 &times; 17 = 136<br>(9 : ?) :- 9 &times; 17 = 153</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>Some laptops are computers.<br>No computer is a robot.<br>Some robots are humans.<br><strong>Conclusions :</strong><br>(I) All humans can never be computers.<br>(II) Some robots are laptops</p>",
                    question_hi: "<p>22. दिए गए कथनों और निष्कर्षों को ध्यान पूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्णय लें कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>कुछ लैपटॉप, कंप्यूटर हैं।<br>कोई कंप्यूटर, रोबोट नहीं है।<br>कुछ रोबोट, मनुष्य हैं।<br><strong>निष्कर्ष :</strong><br>(I) सभी मनुष्य कभी भी कंप्यूटर नहीं हो सकते।<br>(II) कुछ रोबोट, लैपटॉप हैं।</p>",
                    options_en: [
                        "<p>Both conclusions I and II follow</p>",
                        "<p>Only conclusion I follows</p>",
                        "<p>Only conclusion II follows</p>",
                        "<p>None of the conclusions follow</p>"
                    ],
                    options_hi: [
                        "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>"
                    ],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478884658.png\" alt=\"rId30\" width=\"369\" height=\"49\"><br>Only conclusion I follows.</p>",
                    solution_hi: "<p>22.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478884938.png\" alt=\"rId31\" width=\"377\" height=\"50\"><br>केवल निष्कर्ष I अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the figure from among the given options that can replace the question mark (?) and logically complete the series. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885215.png\" alt=\"rId32\" width=\"376\" height=\"78\"></p>",
                    question_hi: "<p>23. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है और शृंखला को तार्किक रूप से पूरा कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885215.png\" alt=\"rId32\" width=\"376\" height=\"78\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885379.png\" alt=\"rId33\" width=\"85\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885541.png\" alt=\"rId34\" width=\"85\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885708.png\" alt=\"rId35\" width=\"85\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885899.png\" alt=\"rId36\" width=\"85\" height=\"86\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885379.png\" alt=\"rId33\" width=\"86\" height=\"88\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885541.png\" alt=\"rId34\" width=\"85\" height=\"86\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885708.png\" alt=\"rId35\" width=\"84\" height=\"85\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885899.png\" alt=\"rId36\" width=\"85\" height=\"86\"></p>"
                    ],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885899.png\" alt=\"rId36\" width=\"85\" height=\"86\"></p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478885899.png\" alt=\"rId36\" width=\"85\" height=\"86\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the correct mirror image of the given figure when the mirror is placed at MN as shown.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886101.png\" alt=\"rId37\" width=\"141\" height=\"147\"></p>",
                    question_hi: "<p>24. दर्पण को MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिम्ब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886101.png\" alt=\"rId37\" width=\"141\" height=\"147\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886286.png\" alt=\"rId38\" width=\"97\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886499.png\" alt=\"rId39\" width=\"97\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886733.png\" alt=\"rId40\" width=\"97\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886930.png\" alt=\"rId41\" width=\"99\" height=\"100\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886286.png\" alt=\"rId38\" width=\"95\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886499.png\" alt=\"rId39\" width=\"97\" height=\"96\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886733.png\" alt=\"rId40\" width=\"95\" height=\"93\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886930.png\" alt=\"rId41\" width=\"97\" height=\"98\"></p>"
                    ],
                    solution_en: "<p>24.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886499.png\" alt=\"rId39\" width=\"97\" height=\"96\"></p>",
                    solution_hi: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478886499.png\" alt=\"rId39\" width=\"97\" height=\"96\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Each of the letters in the word &lsquo;MUSICAL&rsquo; is arranged in the English alphabetical order. How many letters are there in the English alphabetical series between the letter which is third from the left end and the one which is third from the right end in the new letter-cluster thus formed ?</p>",
                    question_hi: "<p>25. शब्द \'MUSICAL\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाता है। इस प्रकार बने नए अक्षर-समूह में बाएँ छोर से तीसरे अक्षर और दाएँ छोर से तीसरे अक्षर के बीच अँग्रेजी वर्णमाला शृंखला में कितने अक्षर हैं ?</p>",
                    options_en: [
                        "<p>Three</p>",
                        "<p>Two</p>",
                        "<p>Five</p>",
                        "<p>Four</p>"
                    ],
                    options_hi: [
                        "<p>तीन</p>",
                        "<p>दो</p>",
                        "<p>पाँच</p>",
                        "<p>चार</p>"
                    ],
                    solution_en: "<p>25.(a)<strong> Given:</strong> \'MUSICAL\' <br>After rearranged the alphabetical order - ACILMSU<br>The letter third from the left is &lsquo;I&rsquo; and third from the right end is &lsquo;M&rsquo;<br>Letter between &lsquo;I&rsquo; and &lsquo;M&rsquo; in english alphabet = 3</p>",
                    solution_hi: "<p>25.(a) <strong>दिया गया: </strong>\'MUSICAL\' <br>वर्णमाला क्रम को पुनर्व्यवस्थित करने के बाद - ACILMSU<br>बायीं ओर से तीसरा अक्षर \'I\' है और दायें छोर से तीसरा अक्षर \'M\' है<br>अंग्रेजी वर्णमाला में \'I\' और \'M\' के बीच अक्षर = 3</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following statements are correct regarding festivals followed in Christianity ?<br>A. Easter is the day celebrated for the \'Resurrection of Jesus Christ\'.<br>B. According to the Bible, \'Three days after Jesus was crucified, he was resurrected\'.&nbsp;<br>C. Good Friday is to commemorate the day of crucifixion of Jesus Christ.</p>",
                    question_hi: "<p>26. ईसाई धर्म में मनाए जाने वाले त्योहारों के संबंध में निम्नलिखित में से कौन-से कथन सही हैं ?&nbsp;<br>A. ईस्टर \'यीशु मसीह के पुनरुत्थान\' के लिए मनाया जाने वाला दिन है।<br>B. बाइबिल के अनुसार \'यीशु को सूली पर चढ़ाए जाने के तीन दिन बाद वह फिर से जीवित हो गए थे&rsquo;। <br>C. गुड फ्राइडे, ईसा मसीह के सूली पर चढ़ने के दिन को मनाने के लिए है।</p>",
                    options_en: [
                        "<p>A and C only</p>",
                        "<p>B and C only</p>",
                        "<p>A, B and C</p>",
                        "<p>A and B only</p>"
                    ],
                    options_hi: [
                        "<p>केवल A और C</p>",
                        "<p>केवल B और C</p>",
                        "<p>A, B और C</p>",
                        "<p>केवल A और B</p>"
                    ],
                    solution_en: "<p>26.(c) <strong>A, B and C.</strong> Christianity is the faith tradition that focuses on the figure of Jesus Christ. Christmas, Good Friday, and Easter are the three major Christian festivals observed in India. Easter is celebrated as a joyous occasion and the Sunday prior is called Palm Sunday which marks the arrival of Jesus in Jerusalem.<br><strong>Festivals of Christians</strong><br><strong>1. Christmas</strong> (25th December)<br>Celebrates the birth of Jesus Christ.<br>One of the most widely celebrated Christian festivals worldwide.<br>Symbolizes love, joy, and peace.<br><strong>2. Easter</strong><br>Celebrates the Resurrection of Jesus Christ three days after His crucifixion.<br>Falls on a Sunday between March and April (date varies).<br>Preceded by Good Friday and followed by Easter Monday in some countries.<br><strong>3. Good Friday</strong><br>Observed to commemorate the crucifixion of Jesus Christ.<br>Falls on the Friday before Easter Sunday.<br>A solemn day of fasting and prayer.<br><strong>4. Palm Sunday</strong><br>Marks the beginning of Holy Week.<br>Celebrates Jesus\' entry into Jerusalem before His crucifixion.<br>People carry palm leaves as a symbol of victory.<br><strong>5. Lent</strong><br>A 40-day period of fasting, repentance, and prayer before Easter.<br>Begins on Ash Wednesday and ends on Holy Saturday.<br>Symbolizes Jesus&rsquo; 40 days of fasting in the wilderness.<br><strong>6. Ash Wednesday</strong><br>Marks the start of the Lent season.<br>Ash is placed on the forehead in the shape of a cross as a sign of repentance.<br><strong>7. Pentecost</strong><br>Celebrates the descent of the Holy Spirit on Jesus&rsquo; disciples.<br>Falls 50 days after Easter.<br>Considered the \"birthday of the Christian Church.\"<br><strong>8. All Saints\' Day </strong>(1st November)<br>Honors all Christian saints and martyrs.<br>Observed by both Catholic and Protestant Christians.<br><strong>9. Epiphany </strong>(6th January)<br>Celebrates the visit of the Three Wise Men (Magi) to baby Jesus. <br>Also known as Three Kings&rsquo; Day.<br><strong>10. Ascension Day</strong><br>Observed 40 days after Easter.<br>Marks Jesus\' ascension into heaven.</p>",
                    solution_hi: "<p>26.(c) A, B और C । ईसाई धर्म एक आस्था परंपरा है जो ईसा मसीह की स्वरूप पर केंद्रित है। क्रिसमस, गुड फ्राइडे और ईस्टर भारत में मनाए जाने वाले तीन प्रमुख ईसाई त्योहार हैं। ईस्टर को एक खुशी के अवसर के रूप में मनाया जाता है और इससे पहले के रविवार को पाम संडे (Palm Sunday) कहा जाता है जो येरुशलम में ईसा मसीह के आगमन का प्रतीक है।<br><strong>ईसाई त्योहार</strong><br><strong>1. क्रिसमस </strong>(25 दिसंबर)<br>यीशु मसीह के जन्म का उत्सव मनाया जाता है।<br>यह दुनिया भर में सबसे व्यापक रूप से मनाया जाने वाला ईसाई त्योहार है।<br>प्रेम, आनंद और शांति का प्रतीक है।<br><strong>2. ईस्टर</strong><br>यीशु मसीह के तीन दिन बाद पुनरुत्थान (पुनर्जीवन) का उत्सव।<br>मार्च और अप्रैल के बीच रविवार को पड़ता है (तिथि परिवर्तनीय)।<br>इससे पहले गुड फ्राइडे और कई देशों में इसके बाद ईस्टर सोमवार मनाया जाता है।<br><strong>3. गुड फ्राइडे</strong><br>यीशु मसीह के क्रूस पर चढ़ाए जाने की स्मृति में मनाया जाता है।<br>ईस्टर संडे से पहले शुक्रवार को पड़ता है।<br>यह एक उपवास और प्रार्थना का दिन होता है।<br><strong>4. पाम संडे</strong><br>होली वीक (पवित्र सप्ताह) की शुरुआत का प्रतीक है।<br>यीशु के यरूशलेम में प्रवेश का उत्सव, जो उनके क्रूस पर चढ़ाए जाने से पहले हुआ था।<br>इस दिन लोग खजूर की पत्तियाँ लेकर चलते हैं, जो विजय का प्रतीक है।<br><strong>5. लेंट</strong> (व्रत का समय)<br>40 दिन तक चलने वाला उपवास, पश्चाताप और प्रार्थना का विशेष काल।<br>एश वेडनसडे (राख बुधवार) से शुरू होकर होली सैटरडे (पवित्र शनिवार) तक चलता है।<br>यीशु मसीह के 40 दिनों के उपवास का प्रतीक है।<br><strong>6. एश वेडनसडे </strong>(राख बुधवार)<br>लेंट की शुरुआत का दिन।<br>पश्चाताप के प्रतीक के रूप में माथे पर राख से क्रॉस का चिन्ह लगाया जाता है।<br><strong>7. पेंटेकोस्ट</strong><br>यीशु मसीह के शिष्यों पर पवित्र आत्मा के अवतरण का उत्सव।<br>ईस्टर के 50 दिन बाद मनाया जाता है।<br>इसे \"ईसाई चर्च का जन्मदिन\" भी कहा जाता है।<br><strong>8. ऑल सेंट्स डे</strong> (1 नवंबर)<br>सभी ईसाई संतों और शहीदों को सम्मान देने का दिन।<br>यह कैथोलिक और प्रोटेस्टेंट ईसाइयों द्वारा मनाया जाता है।<br><strong>9. एपिफनी</strong> (6 जनवरी)<br>जब तीन ज्योतिषी (थ्री वाइज मेन/मागी) बाल यीशु से मिलने आए, इस घटना का उत्सव।<br>इसे थ्री किंग्स डे (तीन राजाओं का दिन) भी कहा जाता है।<br><strong>10. एसेन्शन डे</strong> (स्वर्गारोहण दिवस)<br>ईस्टर के 40 दिन बाद मनाया जाता है।<br>यह दिन यीशु मसीह के स्वर्गारोहण (स्वर्ग जाने) का प्रतीक है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Libero, a special player, plays in which of the following sports ?</p>",
                    question_hi: "<p>27. लिबरो, एक विशेष खिलाड़ी, निम्नलिखित में से किस खेल में खेलता है ?</p>",
                    options_en: [
                        "<p>Volleyball</p>",
                        "<p>Football</p>",
                        "<p>Basketball</p>",
                        "<p>Handball</p>"
                    ],
                    options_hi: [
                        "<p>वॉलीबॉल</p>",
                        "<p>फुटबॉल</p>",
                        "<p>बास्केटबॉल</p>",
                        "<p>हैंडबॉल</p>"
                    ],
                    solution_en: "<p>27.(a) <strong>Volleyball</strong> is a team sport in which two teams of six players are separated by a net. Libero is a specialized defensive player especially adept at digging who always stays in the back row. The libero wears a different colored jersey from the rest of the team. Other Special Players: Setter - A player who excels in setting up teammates to attack. Front-row player - Any of three players positioned closest to the net.</p>",
                    solution_hi: "<p>27.(a) <strong>वॉलीबॉल </strong>एक टीम खेल है जिसमें छह खिलाड़ियों की दो टीमें एक नेट से अलग होती हैं। लिबरो, डीगिंग करने में माहिर एक रक्षात्मक खिलाड़ी होता है, जो हमेशा पिछली पंक्ति में रहता है। लिबरो टीम के अन्य खिलाड़ियों से अलग रंग की जर्सी पहनता है। अन्य विशेष खिलाड़ी: सेटर - वह खिलाड़ी जो टीम के साथियों को अटैक करने के लिए तैयार करने में माहिर होता है। फ्रंट-रो खिलाड़ी - नेट के सबसे करीब स्थित तीन खिलाड़ियों में से कोई भी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. A novel based on the story of a family named \'Fasting, Feasting\' was written by whom among the following ?</p>",
                    question_hi: "<p>28. निम्नलिखित में से किसके द्वारा \'फास्टिंग, फीस्टिंग\' नामक एक परिवार की कहानी पर आधारित उपन्यास लिखा गया था ?</p>",
                    options_en: [
                        "<p>Anita Desai</p>",
                        "<p>Arundhati Roy</p>",
                        "<p>Arvind Adiga</p>",
                        "<p>Kiran Desai</p>"
                    ],
                    options_hi: [
                        "<p>अनीता देसाई</p>",
                        "<p>अरुंधति रॉय</p>",
                        "<p>अरविंद अडिगा</p>",
                        "<p>किरण देसाई</p>"
                    ],
                    solution_en: "<p>28.(a) <strong>Anita Desai</strong> is an Indian novelist. As a writer, she has been shortlisted for the Booker Prize three times. Her Awards: Padma Bhushan (2014), Padma Shri (1989), Sahitya Akademi Award (1978), Neil Gunn Prize (1993). His other books : &ldquo;Clear Light of Day&rdquo;, &ldquo;In Custody&rdquo;, &ldquo;Fire on the Mountain&rdquo;.</p>",
                    solution_hi: "<p>28.(a) <strong>अनीता देसाई</strong> एक भारतीय उपन्यासकार हैं। एक लेखिका के रूप में, उन्हें तीन बार बुकर पुरस्कार के लिए चुना गया है। उनके पुरस्कार: पद्म भूषण (2014), पद्म श्री (1989), साहित्य अकादमी पुरस्कार (1978), नील गुन पुरस्कार (1993)। उनकी अन्य पुस्तकें: \"क्लियर लाइट ऑफ़ डे\", \"इन कस्टडी\", \"फ़ायर ऑन द माउंटेन\"।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who was the founder of Jaipur-Atrauli gharana ?</p>",
                    question_hi: "<p>29. जयपुर-अतरौली घराने के संस्थापक कौन थे ?</p>",
                    options_en: [
                        "<p>Abdul Karim Khan</p>",
                        "<p>Ali Baksh Khan</p>",
                        "<p>Alladiya Khan</p>",
                        "<p>Omkarnath Thakur</p>"
                    ],
                    options_hi: [
                        "<p>अब्दुल करीम खान</p>",
                        "<p>अली बख्श खान</p>",
                        "<p>अल्लादिया खान</p>",
                        "<p>ओंकारनाथ ठाकुर</p>"
                    ],
                    solution_en: "<p>29.(c) <strong>Alladiya Khan.</strong> The Jaipur-Atrauli Gharana is a Hindustani music tradition renowned for its unique style of khayal singing. It stands out for its distinctive vocal aesthetics, raga repertoire, and technical skill. Gharana of Hindustani classical music and its Founders : Gwalior Gharana (Nanthan Khan), Agra Gharana (Hajisujan Khan), Rangeela Gharana (Faiyyaz Khan), Kirana Gharana (Abdul Wahid Khan).</p>",
                    solution_hi: "<p>29.(c) <strong>अल्लादिया खान। </strong>जयपुर-अतरौली घराना एक हिंदुस्तानी संगीत परंपरा है जो ख़याल गायन की अपनी अनूठी शैली के लिए प्रसिद्ध है। यह अपने विशिष्ट गायन सौंदर्य, राग प्रदर्शनों की सूची और तकनीकी कौशल के लिए जाना जाता है। हिंदुस्तानी शास्त्रीय संगीत का घराना और इसके संस्थापक : ग्वालियर घराना (नानथन खान), आगरा घराना (हाजीसुजान खान), रंगीला घराना (फैयाज खान), किराना घराना (अब्दुल वाहिद खान)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. When a large amount of carbon dioxide dissolves in a water body, the water becomes acidic because of the formation of which of the following acids ?</p>",
                    question_hi: "<p>30. जब किसी जल निकाय में बड़ी मात्रा में कार्बन डाइऑक्साइड घुलता है, तो निम्नलिखित में से किस अम्ल के बनने के कारण पानी अम्लीय हो जाता है ?</p>",
                    options_en: [
                        "<p>Carboxylic acid</p>",
                        "<p>Carbamic acid</p>",
                        "<p>Carbolic acid</p>",
                        "<p>Carbonic acid</p>"
                    ],
                    options_hi: [
                        "<p>कार्बोक्सिलिक अम्ल (Carboxylic acid)</p>",
                        "<p>कार्बामिक अम्ल (Carbamic acid)</p>",
                        "<p>कार्बोलिक अम्ल (Carbolic acid)</p>",
                        "<p>कार्बोनिक अम्ल (Carbonic acid)</p>"
                    ],
                    solution_en: "<p>30.(d) <strong>Carbonic acid. </strong>When a large amount of carbon dioxide (CO₂) dissolves in a water body, it forms carbonic acid (H₂CO₃), which is a weak acid, because its ionization in water is incomplete. Carbon dioxide reacts with limewater (a solution of calcium hydroxide, Ca(OH)<sub>2</sub>, to form a white precipitate (appears milky) of calcium carbonate, CaCO<sub>3</sub>.</p>",
                    solution_hi: "<p>30.(d) <strong>कार्बोनिक अम्ल। </strong>जब कार्बन डाइऑक्साइड (CO<sub>2</sub>) की एक बड़ी मात्रा जल निकाय में घुल जाती है, तो यह कार्बोनिक अम्ल (H<sub>2</sub>CO<sub>3</sub>) बनाती है, जो एक दुर्बल अम्ल है, क्योंकि जल में इसका आयनीकरण अपूर्ण होता है। कार्बन डाइऑक्साइड, चूने के पानी (कैल्शियम हाइड्रॉक्साइड, Ca(OH)<sub>2</sub> का एक विलयन) के साथ अभिक्रिया करके कैल्शियम कार्बोनेट, CaCO<sub>3</sub> का एक सफ़ेद अवक्षेप (दूधिया) बनाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Who among the following was awarded the Pandit Bhimsen Joshi Lifetime Achievement Award, 2014 ?</p>",
                    question_hi: "<p>31. निम्नलिखित में से किसे पंडित भीमसेन जोशी लाइफटाइम अचीवमेंट अवार्ड, 2014 से सम्मानित किया गया ?</p>",
                    options_en: [
                        "<p>Pandit Ravi Shankar</p>",
                        "<p>Saraswati Abdul Rane</p>",
                        "<p>Pandit Kumar Gandharv</p>",
                        "<p>Prabha Atre</p>"
                    ],
                    options_hi: [
                        "<p>पंडित रवि शंकर</p>",
                        "<p>सरस्वती अब्दुल राणे</p>",
                        "<p>पंडित कुमार गंधर्व</p>",
                        "<p>प्रभा अत्रे</p>"
                    ],
                    solution_en: "<p>31.(d) <strong>Prabha Atre </strong>(vocalist). Her Awards : Padma Shri (1990), Padma Bhushan (2002), Padma Vibhushan (2022), Sangeet Natak Akademi Award (1991) for her contribution to Hindustani vocal music. Pandit Ravi Shankar - sitarist and composer. Pandit Kumar Gandharva - Hindustani classical singer.</p>",
                    solution_hi: "<p>31.(d) <strong>प्रभा अत्रे </strong>(गायक)। उनके पुरस्कार: पद्म श्री (1990), पद्म भूषण (2002), पद्म विभूषण (2022), संगीत नाटक अकादमी पुरस्कार (1991) हिंदुस्तानी गायन में उनके योगदान के लिए। पंडित रविशंकर - सितारवादक और संगीतकार। पंडित कुमार गंधर्व - हिंदुस्तानी शास्त्रीय गायक।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. How many types of makeup (vesham) are used for the performers of Kathakali dance ?</p>",
                    question_hi: "<p>32. कथकली नृत्य के कलाकारों के लिए कितने प्रकार के श्रृंगार (वेषम) का उपयोग किया जाता है ?</p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>7</p>",
                        "<p>5</p>",
                        "<p>11</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>7</p>",
                        "<p>5</p>",
                        "<p>11</p>"
                    ],
                    solution_en: "<p>32.(c) <strong>5.</strong> The five major Veshams in Kathakali (Kerala), are Kathi (Knife), Pacha (Green), Thaadi (Beard), Minukku (Radiant), and Kari (Black). The Pacha Veshams represent noble and divine characters. Kathi represents characters that are arrogant and evil, although they possess a vein of gallantry.</p>",
                    solution_hi: "<p>32.(c) <strong>5.</strong> कथकली (केरल) में पाँच प्रमुख वेषम हैं - काठी (चाकू), पाचा (हरा), थाडी (दाढ़ी), मिनुक्कु (चमकता हुआ) और कारी (काला)। पाचा वेषम महान और दिव्य चरित्रों का प्रतिनिधित्व करते हैं। काठी उन चरित्रों का प्रतिनिधित्व करता है जो अभिमानी और दुष्ट होते हैं, हालाँकि उनमें वीरता की भावना होती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. In which year was the first underground section (Vishwavidyalaya - Kashmere Gate) of&nbsp;the Golden Route opened in New Delhi ?</p>",
                    question_hi: "<p>33. नई दिल्ली में गोल्डन रूट (Golden Route) का पहला भूमिगत खंड(विश्वविद्यालय-कश्मीर गेट)किस&nbsp;वर्ष में खोला गया था ?</p>",
                    options_en: [
                        "<p>2004</p>",
                        "<p>2000</p>",
                        "<p>1998</p>",
                        "<p>1997</p>"
                    ],
                    options_hi: [
                        "<p>2004</p>",
                        "<p>2000</p>",
                        "<p>1998</p>",
                        "<p>1997</p>"
                    ],
                    solution_en: "<p>33.(a) <strong>2004.</strong> The Red Line, the inaugural line of the Delhi Metro, was officially opened in December 2002, by the then Prime Minister of India, Atal Bihari Vajpayee.</p>",
                    solution_hi: "<p>33.(a) <strong>2004.</strong> रेड लाइन, दिल्ली मेट्रो की पहली लाइन है, जिसका आधिकारिक उद्घाटन दिसंबर 2002 में तत्कालीन प्रधानमंत्री अटल बिहारी वाजपेयी द्वारा किया गया था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following statements best defines the monoecious ?</p>",
                    question_hi: "<p>34. निम्नलिखित में से कौन सा कथन उभयलिंगाश्रयी (monoecious) को सर्वोत्तम रूप से परिभाषित करता है ?</p>",
                    options_en: [
                        "<p>A flower with both androecium and gynoecium</p>",
                        "<p>A flower with dithecous</p>",
                        "<p>A flower with gynoecium only</p>",
                        "<p>A flower with androecium only</p>"
                    ],
                    options_hi: [
                        "<p>पुमंग और जायांग दोनों से युक्&zwj;त फूल</p>",
                        "<p>द्विकोष्ठी फूल</p>",
                        "<p>केवल जायांग वाला फूल</p>",
                        "<p>केवल पुमंग वाला फूल</p>"
                    ],
                    solution_en: "<p>34.(a) Monoecious plants bear both purely male and purely female flowers. Examples include castor, cucumber, and maize. In contrast, dioecious plants have male and female flowers on separate plants, such as papaya.</p>",
                    solution_hi: "<p>34.(a) उभयलिंगाश्रयी पौधों में नर और मादा दोनों प्रकार के फूल होते हैं। इसके उदाहरणों में अरंडी, खीरा, और मक्का शामिल हैं। इसके विपरीत, द्विलिंगी पौधों में नर और मादा फूल अलग-अलग पौधों पर होते हैं, जैसे कि पपीता।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which country&rsquo;s national game is Chinlone (Caneball) ?</p>",
                    question_hi: "<p>35. चिनलोन (केन बॉल) किस देश का राष्ट्रीय खेल है ?</p>",
                    options_en: [
                        "<p>Myanmar</p>",
                        "<p>China</p>",
                        "<p>Afghanistan</p>",
                        "<p>Bhutan</p>"
                    ],
                    options_hi: [
                        "<p>म्यांमार</p>",
                        "<p>चीन</p>",
                        "<p>अफ़गानिस्तान</p>",
                        "<p>भूटान</p>"
                    ],
                    solution_en: "<p>35.(a)<strong> Myanmar. </strong>The Chinlone game is non-competitive, with typically six people playing together as one team. Other countries\' National Game : China - Table tennis; Afghanistan - Buzkashi; Bhutan - Archery; Pakistan - Field Hockey; Bangladesh - Kabaddi; Nepal - Volleyball. India had not declared any sport or game as the national sport.</p>",
                    solution_hi: "<p>35.(a) <strong>म्यांमार।</strong> चिनलोन खेल गैर-प्रतिस्पर्धी है, जिसमें सामान्यतः छह व्यक्ति एक टीम के रूप में एक साथ खेलते हैं। अन्य देशों के राष्ट्रीय खेल: चीन - टेबल टेनिस; अफ़गानिस्तान - बुज़कशी; भूटान - तीरंदाजी; पाकिस्तान - फ़ील्ड हॉकी; बांग्लादेश - कबड्डी; नेपाल - वॉलीबॉल। भारत ने किसी भी खेल को राष्ट्रीय खेल घोषित नहीं किया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Ranjana Prakash Desai who became the first woman chairperson of the Press Council of India originally worked in which of the following organs of the government ?</p>",
                    question_hi: "<p>36. रंजना प्रकाश देसाई, जो भारतीय प्रेस परिषद की पहली महिला अध्यक्ष बनीं, मूल रूप से सरकार के निम्नलिखित में से किस अंग में कार्य करती थी ?</p>",
                    options_en: [
                        "<p>Legislature</p>",
                        "<p>Judiciary</p>",
                        "<p>Vigilance</p>",
                        "<p>Executive</p>"
                    ],
                    options_hi: [
                        "<p>विधान मंडल (Legislature)</p>",
                        "<p>न्यायपालिका (Judiciary)</p>",
                        "<p>सतर्कता (Vigilance)</p>",
                        "<p>कार्यपालिका (Executive)</p>"
                    ],
                    solution_en: "<p>36.(b) <strong>Judiciary. </strong>Ranjana Prakash Desai was a retired Judge of the Supreme Court of India and the head of the Delimitation Commission of India. Press Council of India (PCI): It is a statutory body created by the Press Council Act of 1978. It acts as an apex body for regulation of the press in the country.</p>",
                    solution_hi: "<p>36.(b) <strong>न्यायपालिका </strong>(Judiciary)। रंजना प्रकाश देसाई भारत के सर्वोच्च न्यायालय की सेवानिवृत्त न्यायाधीश और भारत के परिसीमन आयोग की प्रमुख थीं। भारतीय प्रेस परिषद (PCI): यह प्रेस परिषद अधिनियम 1978 द्वारा निर्मित एक वैधानिक निकाय है। यह देश में प्रेस के नियमन के लिए एक शीर्ष निकाय के रूप में कार्य करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Valabhi type of Nagara temples are generally _____in shape, with a roof that rises into a vaulted chamber.</p>",
                    question_hi: "<p>37. वल्लभी प्रकार (Valabhi type) के नागर मंदिर सामान्यतः आकार में______होते हैं, जिनकी छत, महारावी कक्ष (vaulted chamber) में उठी होती है।</p>",
                    options_en: [
                        "<p>square</p>",
                        "<p>triangular</p>",
                        "<p>circular</p>",
                        "<p>rectangular</p>"
                    ],
                    options_hi: [
                        "<p>वर्गाकार</p>",
                        "<p>त्रिभुजाकार</p>",
                        "<p>वृत्ताकार</p>",
                        "<p>आयताकार</p>"
                    ],
                    solution_en: "<p>37.(d) <strong>rectangular.</strong> North Indian temples are built on stone platforms with steps, featuring śikharas over the garbhagṛha. Early temples had one śikhara, while later ones had multiple. The latina or rekha-prasada sikhara has curving walls, and the phamsana type is stepped. Odisha temples, classified as rekhapiḍa, pidhadeula, and khakara, are prominent in Puri, Bhubaneswar, and Konark. Notable marble Jain temples are found in Mount Abu and Ranakpur.</p>",
                    solution_hi: "<p>37.(d) <strong>आयताकार।</strong> उत्तर भारतीय मंदिर सीढ़ियों वाले पत्थर के चबूतरे पर बने होते हैं, जिनमें गर्भगृह के ऊपर शिखर होते हैं। प्रारंभिक मन्दिरों में एक शिखर होता था, जबकि बाद के मंदिरों में कई शिखर होते थे। लैटिना या रेखा-प्रसाद शिखर में घुमावदार दीवारें होती हैं, जबकि फामसन (phamsana) में सीढ़ियाँ जैसी होती हैं। ओडिशा के मंदिर, जिन्हें रेखापीड़ा, पिधादेउला और खाकरा के रूप में वर्गीकृत किया गया है, पुरी, भुवनेश्वर और कोणार्क में प्रसिद्ध हैं। माउंट आबू और रणकपुर में उल्लेखनीय संगमरमर के जैन मंदिर प्रसिद्ध हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. On December 21, 2024, which city was targeted by explosive-laden drones as part of the ongoing conflict between Ukraine and Russia ?</p>",
                    question_hi: "<p>38. 21 दिसंबर 2024 को, यूक्रेन और रूस के&nbsp;बीच चल रहे संघर्ष के तहत किस शहर को विस्फोटक-लोडेड ड्रोन से लक्ष्य बनाया गया ?</p>",
                    options_en: [
                        "<p>Moscow</p>",
                        "<p>Kazan</p>",
                        "<p>St. Petersburg</p>",
                        "<p>Sochi</p>"
                    ],
                    options_hi: [
                        "<p>मॉस्को</p>",
                        "<p>कजान</p>",
                        "<p>सेंट पीटर्सबर्ग</p>",
                        "<p>सोची</p>"
                    ],
                    solution_en: "<p>38.(b) <strong>Kazan. </strong>Several explosive - laden drones targeted residential high-rises in Kazan, the capital of Russia&rsquo;s Republic of Tatarstan. The attack involved at least eight kamikaze drones, leading to fires across multiple buildings.</p>",
                    solution_hi: "<p>38.(b) <strong>कजान।</strong> कजान, रूस के तातारस्तान गणराज्य की राजधानी में कई विस्फोटक-लोडेड ड्रोन द्वारा आवासीय ऊंची इमारतों को निशाना बनाया गया। हमले में कम से कम आठ कमीकाजी ड्रोन शामिल थे, जिससे कई इमारतों में आग लग गई।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which of the following statements is INCORRECT ?</p>",
                    question_hi: "<p>39. निम्नलिखित में से कौन-सा कथन गलत है ?</p>",
                    options_en: [
                        "<p>Fats and oils get reduced over time and smell bad</p>",
                        "<p>Fats and oils are oxidised, they become rancid.</p>",
                        "<p>Antioxidants are added to foods containing fats and oil to prevent oxidation.</p>",
                        "<p>Chips manufacturers usually flush bags of chips with Nitrogen to prevent the chips from getting ranicid.</p>"
                    ],
                    options_hi: [
                        "<p>वसा और तेल, समय के साथ अपचयित हो जाते हैं और उनमें से दुर्गंध आने लगती है।</p>",
                        "<p>वसा और तेल उपचयित होने के कारण बासी हो जाते हैं।</p>",
                        "<p>उपचयन को रोकने के लिए वसा और तेल युक्त खाद्य पदार्थों में एंटीऑक्सीडेंट मिलाए जाते हैं।</p>",
                        "<p>चिप्स को खराब होने से बचाने के लिए, चिप्स निर्माता आमतौर पर चिप्स के बैगों को नाइट्रोजन से भर देते हैं।</p>"
                    ],
                    solution_en: "<p>39.(a) When fats and oils are oxidized, they undergo a process known as rancidity, which results in an unpleasant smell and taste. This occurs because the oxidation of fats leads to the formation of compounds like aldehydes and ketones, which give rancid products their off-flavor.</p>",
                    solution_hi: "<p>39.(a) जब वसा और तेल का ऑक्सीकरण होता है, तो वे विकृतीकरण (रन्सिडिटी) नामक प्रक्रिया से गुजरते हैं, जिसके परिणामस्वरूप खराब गंध और स्वाद उत्पन्न होता है। ऐसा इसलिए होता है, क्योंकि वसा के ऑक्सीकरण से एल्डीहाइड और कीटोन्स जैसे यौगिकों का निर्माण होता है, जो सड़े हुए (बासी ) उत्पादों को उनका खराब स्वाद देते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. What is the name of the space centre from which ISRO\'s 100th mission will be launched ?</p>",
                    question_hi: "<p>40. उस अंतरिक्ष केंद्र का नाम क्या है, जहां से इसरो का 100वां मिशन लॉन्च किया जाएगा ?</p>",
                    options_en: [
                        "<p>National Space Centre</p>",
                        "<p>Satish Dhawan Space Centre</p>",
                        "<p>Chandrayaan Space Centre</p>",
                        "<p>Vikram Space Centre</p>"
                    ],
                    options_hi: [
                        "<p>राष्ट्रीय अंतरिक्ष केंद्र</p>",
                        "<p>सतीश धवन अंतरिक्ष केंद्र</p>",
                        "<p>चंद्रयान अंतरिक्ष केंद्र</p>",
                        "<p>विक्रम अंतरिक्ष केंद्र</p>"
                    ],
                    solution_en: "<p>40.(b) <strong>Satish Dhawan Space Centre. </strong>India&rsquo;s space agency, ISRO (Indian Space Research Organisation), is preparing to make history with the launch of its 100th satellite. This milestone mission is set to take place on January 29 from the Satish Dhawan Space Centre in Sriharikota, Andhra Pradesh, with the GSLV-F15 NVS-02 mission. This achievement marks a significant step for India in space exploration and satellite technology, reinforcing the country&rsquo;s growing space capabilities.</p>",
                    solution_hi: "<p>40.(b)<strong> सतीश धवन अंतरिक्ष केंद्र।</strong> भारत की अंतरिक्ष एजेंसी इसरो (भारतीय अंतरिक्ष अनुसंधान संगठन) अपने 100वें उपग्रह के प्रक्षेपण के साथ इतिहास रचने की तैयारी कर रही है। यह मील का पत्थर मिशन 29 जनवरी को आंध्र प्रदेश के श्रीहरिकोटा में सतीश धवन अंतरिक्ष केंद्र से GSLV-F15 NVS-02 मिशन के साथ होने वाला है। यह उपलब्धि अंतरिक्ष अन्वेषण और उपग्रह प्रौद्योगिकी में भारत के लिए एक महत्वपूर्ण कदम है, जो देश की बढ़ती अंतरिक्ष क्षमताओं को मजबूत करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Match the army officers of the Mauryan empire in column A with the respective salaries given to them in column B. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478887224.png\" alt=\"rId42\" width=\"408\" height=\"116\"></p>",
                    question_hi: "<p>41. स्तंभ A में दिए गए मौर्य साम्राज्य के सैन्य अधिकारियों को स्तंभ B में उनको दिए जाने वाले वेतन के साथ सुमेलित कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478887534.png\" alt=\"rId43\" width=\"362\" height=\"160\"></p>",
                    options_en: [
                        "<p>a-iii, b-i, c-iv, d-ii</p>",
                        "<p>a-iii, b-iv, c-i, d-ii</p>",
                        "<p>a-iv, b-iii, c-ii, d-i</p>",
                        "<p>a-i, b-ii, c-iii, d-iv</p>"
                    ],
                    options_hi: [
                        "<p>a-iii, b-i, c-iv, d-ii</p>",
                        "<p>a-iii, b-iv, c-i, d-ii</p>",
                        "<p>a-iv, b-iii, c-ii, d-i</p>",
                        "<p>a-i, b-ii, c-iii, d-iv</p>"
                    ],
                    solution_en: "<p>41.(c) <strong>a-iv, b-iii, c-ii, d-i. </strong>Mauryan Administrative System: The Mauryan army was well organised and was under the control of a &lsquo;Senapati.&rsquo; The Chief Justice was called &lsquo;Dharmathikarin.&rsquo; Subordinate courts under &lsquo;Amatyas.&rsquo; Cases were settled by &lsquo;Nagara Vyavaharika Mahamantra&rsquo;. At the Village level cases were settled by the &lsquo;Gramavradha&rsquo;.</p>",
                    solution_hi: "<p>41.(c)<strong> a-iv, b-iii, c-ii, d-i. </strong>मौर्य प्रशासनिक व्यवस्था: मौर्य सेना अच्छी तरह से संगठित थी और सेनापति के नियंत्रण में थी। मुख्य न्यायाधीश को धर्माथिकारिन कहा जाता था। अधीनस्थ न्यायालय अमात्य के अधीन थे। मामलों का निपटारा नागर व्यवहारिक महामंत्र द्वारा किया जाता था। गांव स्तर पर मामलों का निपटारा ग्रामवृद्ध द्वारा किया जाता था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. In which part of India is the cool mountain climate observed ?</p>",
                    question_hi: "<p>42. भारत के किस हिस्से में ठंडी पर्वतीय जलवायु पाई जाती है ?</p>",
                    options_en: [
                        "<p>West</p>",
                        "<p>South</p>",
                        "<p>North</p>",
                        "<p>East</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिमी</p>",
                        "<p>दक्षिणी</p>",
                        "<p>उत्तरी</p>",
                        "<p>पूर्वी</p>"
                    ],
                    solution_en: "<p>42.(c) <strong>North. </strong>The cool mountain climate is found in the North of India, particularly in the Himalayan region, covering states like Jammu &amp; Kashmir, Himachal Pradesh, Uttarakhand, and parts of Sikkim and Arunachal Pradesh. Due to high altitudes, temperatures drop, with heavy snowfall in winter and mild summers. The climate varies from subtropical in lower altitudes to alpine in higher regions.</p>",
                    solution_hi: "<p>42.(c) <strong>उत्तरी।</strong> भारत के उत्तरी भाग, विशेषकर हिमालयी क्षेत्र में, ठंडी पर्वतीय जलवायु पायी जाती है, जिसमें जम्मू और कश्मीर, हिमाचल प्रदेश, उत्तराखंड और सिक्किम तथा अरुणाचल प्रदेश के कुछ हिस्से शामिल हैं। उच्च उचाइयों के कारण, तापमान काफी कम हो जाता है, सर्दियों में भारी बर्फबारी और गर्मियों में हल्की बर्फबारी होती है। जलवायु कम ऊँचाई वाले क्षेत्रों में उपोष्णकटिबंधीय से लेकर उच्च क्षेत्रों में अल्पाइन तक भिन्न होती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. What is the retirement age of Judges serving at the Supreme Court of India ?</p>",
                    question_hi: "<p>43. भारत के सर्वोच्च न्यायालय में सेवारत न्यायाधीशों की सेवानिवृत्ति की आयु क्या है ?</p>",
                    options_en: [
                        "<p>68 years</p>",
                        "<p>60 years</p>",
                        "<p>65 years</p>",
                        "<p>62 years</p>"
                    ],
                    options_hi: [
                        "<p>68 वर्ष</p>",
                        "<p>60 वर्ष</p>",
                        "<p>65 वर्ष</p>",
                        "<p>62 वर्ष</p>"
                    ],
                    solution_en: "<p>43.(c) <strong>65 years. </strong>The Judges of the Supreme Court are appointed by the President under clause (2) of Article 124 of the Constitution. <strong>Removal: </strong>A judge of the Supreme Court can only be removed from office by an order of the President. The removal process requires an address by each House of Parliament, supported by a special majority i.e., a majority of the total membership of that House and a majority of not less than two-thirds of the members present and voting. The grounds for removal are proven misbehaviour or incapacity.</p>",
                    solution_hi: "<p>43.(c)<strong> 65 वर्ष।</strong> संविधान के अनुच्छेद 124 के खंड (2) के तहत राष्ट्रपति द्वारा सर्वोच्च न्यायालय के न्यायाधीशों की नियुक्ति की जाती है। <strong>निष्कासन : </strong>सर्वोच्च न्यायालय के न्यायाधीश को केवल राष्ट्रपति के आदेश से ही पद से हटाया जा सकता है। हटाने की प्रक्रिया के लिए संसद के प्रत्येक सदन द्वारा विशेष बहुमत अर्थात उस सदन की कुल सदस्यता के बहुमत और उपस्थित और मतदान करने वाले सदस्यों के कम से कम दो-तिहाई बहुमत द्वारा समर्थित एक संबोधन की आवश्यकता होती है। निष्कासन का आधार सिद्ध दुर्व्यवहार या अक्षमता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. With the Theory of Relativity, ______transformed the landscape of physics, this theory majorly contributed to designing the theory of Quantum Mechanics.</p>",
                    question_hi: "<p>44. सापेक्षता के सिद्धांत के साथ, ______ने भौतिकी के परिदृश्य को बदल दिया, इस सिद्धांत ने क्वांटम यांत्रिकी के सिद्धांत को डिजाइन करने में प्रमुख योगदान दिया।</p>",
                    options_en: [
                        "<p>Albert Einstein</p>",
                        "<p>Galileo Galilei</p>",
                        "<p>Paul G. Hewitt</p>",
                        "<p>Isaac Newton</p>"
                    ],
                    options_hi: [
                        "<p>अल्बर्ट आइंस्टीन (Albert Einstein)</p>",
                        "<p>गैलीलियो गैलीली (Galileo Galilei)</p>",
                        "<p>पॉल जी. हेविट (Paul G. Hewitt)</p>",
                        "<p>आइजैक न्यूटन (Isaac Newton)</p>"
                    ],
                    solution_en: "<p>44.(a) <strong>Albert Einstein&rsquo;s</strong> important works include the Special Theory of Relativity (1905), Relativity (English translations in 1920 and 1950), the General Theory of Relativity (1916), Investigations on the Theory of Brownian Movement (1926), and The Evolution of Physics (1938). Other discoveries: Galileo Galilei - Law of Falling Bodies. Isaac Newton - The universal law of gravitation.</p>",
                    solution_hi: "<p>44.(a) <strong>अल्बर्ट आइंस्टीन </strong>के महत्वपूर्ण कार्यों में विशेष सापेक्षता का सिद्धांत (1905), सापेक्षता (अंग्रेजी अनुवाद 1920 और 1950 में), सामान्य सापेक्षता सिद्धांत (1916), ब्राउनी गति के सिद्धांत पर जांच (1926), और भौतिकी का विकास (1938) शामिल हैं। अन्य खोजें: गैलीलियो गैलीली - गिरते हुए पिंडों का नियम। आइजैक न्यूटन - गुरुत्वाकर्षण का सार्वभौमिक नियम।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Bal Gangadhar described ______ as a diamond of India.</p>",
                    question_hi: "<p>45. बाल गंगाधर ने ______को भारत का हीरा कह कर वर्णित किया है।</p>",
                    options_en: [
                        "<p>Baba Saheb Ambedkar</p>",
                        "<p>Mahatma Gandhi</p>",
                        "<p>Gopal Krishna Gokhale</p>",
                        "<p>Motilal Nehru</p>"
                    ],
                    options_hi: [
                        "<p>बाबा साहेब अम्बेडकर</p>",
                        "<p>महात्मा गाँधी</p>",
                        "<p>गोपाल कृष्ण गोखले</p>",
                        "<p>मोतीलाल नेहरू</p>"
                    ],
                    solution_en: "<p>45.(c) <strong>Gopal Krishna Gokhale.</strong> Bal Gangadhar Tilak described Gopal Krishna Gokhale as the \"diamond of India\" due to Gokhale\'s exemplary work in the fields of social reform and his role as a leader in the Indian independence movement. Apart from being a senior leader of the Indian National Congress, he was the founder of the Servants of India Society (1905).</p>",
                    solution_hi: "<p>45.(c)<strong> गोपाल कृष्ण गोखले। </strong>बाल गंगाधर तिलक ने गोपाल कृष्ण गोखले को सामाजिक सुधार के क्षेत्र में उनके अनुकरणीय कार्य और भारतीय स्वतंत्रता आंदोलन में एक नेता के रूप में उनकी भूमिका के कारण \"भारत का हीरा\" कहा। भारतीय राष्ट्रीय कांग्रेस के एक वरिष्ठ नेता होने के अलावा, वे सर्वेंट्स ऑफ़ इंडिया सोसाइटी (1905) के संस्थापक भी थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. How was the growth in all food grains after the Green Revolution ?</p>",
                    question_hi: "<p>46. हरित क्रांति के बाद सभी खाद्यान्नों में वृद्धि किस प्रकार थी ?</p>",
                    options_en: [
                        "<p>Unitary</p>",
                        "<p>Proportionate</p>",
                        "<p>Unpredictable</p>",
                        "<p>Disproportionate</p>"
                    ],
                    options_hi: [
                        "<p>ऐकिक</p>",
                        "<p>समानुपाती</p>",
                        "<p>अप्रत्याशित</p>",
                        "<p>असमानुपाती</p>"
                    ],
                    solution_en: "<p>46.(d) <strong>Disproportionate.</strong> Larger farmers with more resources were able to afford the necessary inputs, like high-yield variety (HYV) seeds, fertilizers, and irrigation, and thus reaped greater benefits. Smaller farmers often lacked access to these resources and faced higher risks, as the HYV crops were more vulnerable to pests, potentially causing severe losses. About 65 percent of the country&rsquo;s population continued to be employed in agriculture even as late as 1990.</p>",
                    solution_hi: "<p>46.(d) <strong>असमानुपाती।</strong> अधिक संसाधनों वाले बड़े किसान उच्च उपज वाली किस्म (HYV) के बीज, उर्वरक और सिंचाई जैसे आवश्यक निवेश का खर्च उठाने में सक्षम थे, और इस प्रकार अधिक लाभ प्राप्त करते थे। छोटे किसानों के पास अक्सर इन संसाधनों तक पहुँच नहीं होती थी और उन्हें अधिक जोखिम का सामना करना पड़ता था, क्योंकि HYV फसलें कीटों के प्रति अधिक संवेदनशील होती थीं, जिससे संभावित रूप से गंभीर नुकसान होता था। देश की लगभग 65 प्रतिशत आबादी 1990 के अंत तक भी कृषि में कार्यरत थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which of the following pairs is INCORRECTLY matched ?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन-सा मिलान युग्म गलत है ?</p>",
                    options_en: [
                        "<p>Rule of Law - Britain</p>",
                        "<p>Procedure Established by Law - Japan</p>",
                        "<p>Concurrent List - Australia</p>",
                        "<p>Function of President - France</p>"
                    ],
                    options_hi: [
                        "<p>कानून का शासन - ब्रिटेन</p>",
                        "<p>कानून द्वारा स्थापित प्रक्रिया - जापान</p>",
                        "<p>समवर्ती सूची - ऑस्ट्रेलिया</p>",
                        "<p>राष्ट्रपति के कार्य - फ्रांस</p>"
                    ],
                    solution_en: "<p>47.(d) <strong>Function of President - France. </strong>The French President has a unique role in a semi-presidential system, where executive powers are shared with the Prime Minister. The functions of president and vice-president are borrowed from the USA. Other features borrowed from USA - Removal of Supreme Court and High court judges, Fundamental Rights, Judicial review, Independence of judiciary, The preamble of the constitution.</p>",
                    solution_hi: "<p>47.(d)<strong> राष्ट्रपति का कार्य - फ्रांस। </strong>फ्रांस के राष्ट्रपति का अर्ध-अध्यक्षीय प्रणाली में एक विशिष्ट भूमिका होती है, जहाँ कार्यकारी शक्तियाँ प्रधानमंत्री के साथ साझा की जाती हैं। राष्ट्रपति और उपराष्ट्रपति के कार्य अमेरिका से अपनाया गया हैं। अमेरिका से अपनाई गई अन्य विशेषताएँ - सर्वोच्च न्यायालय और उच्च न्यायालय के न्यायाधीशों को हटाना, मौलिक अधिकार, न्यायिक समीक्षा, न्यायपालिका की स्वतंत्रता, संविधान की प्रस्तावना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. In the context of microfinance, what does &lsquo;S&rsquo; stand for in SHG ?</p>",
                    question_hi: "<p>48. सूक्ष्म वित्त (microfinance) के संदर्भ में SHG में \'S\' क्या दर्शाता है ?</p>",
                    options_en: [
                        "<p>Social</p>",
                        "<p>Steady</p>",
                        "<p>Sustainable</p>",
                        "<p>Self</p>"
                    ],
                    options_hi: [
                        "<p>Social (सोशल)</p>",
                        "<p>Steady (स्टीडी)</p>",
                        "<p>Sustainable (सस्टेनेबल)</p>",
                        "<p>Self (सेल्फ़)</p>"
                    ],
                    solution_en: "<p>48.(d) <strong>Self. </strong>Self-Help Groups (SHGs) are informal, self-governed associations of individuals from similar socio-economic backgrounds who come together to collectively improve their living conditions by addressing shared financial or social needs through mutual support and peer-controlled activities. Functions : To enhance the functional capacity of the poor and marginalized by promoting employment and income-generating activities. SHGs provide collateral-free loans at market-driven interest rates, with terms decided by the group.</p>",
                    solution_hi: "<p>48.(d) <strong>Self (सेल्फ़)। </strong>स्वयं-सहायता समूह (SHG) समान सामाजिक-आर्थिक पृष्ठभूमि वाले व्यक्तियों के अनौपचारिक, स्व-शासित संघ होते हैं जो पारस्परिक समर्थन और सहकर्मी-नियंत्रित गतिविधियों के माध्यम से साझा वित्तीय या सामाजिक जरूरतों को संबोधित करके सामूहिक रूप से अपने जीवन की स्थिति में सुधार करने के लिए एक साथ आते हैं। कार्य: रोजगार और आय-उत्पादक गतिविधियों को बढ़ावा देकर गरीबों और हाशिए पर रहने वालों की कार्यात्मक क्षमता को बढ़ाना। SHG समूह द्वारा तय की गई शर्तों के साथ, बाजार संचालित ब्याज दरों पर संपार्श्विक-मुक्त ऋण प्रदान करते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. What is the frequency (f) of a wave with a period of 0.04 seconds ?</p>",
                    question_hi: "<p>49. 0.04 सेकंड की आवर्त काल वाली तरंग की आवृत्ति (f) क्या होगी ?</p>",
                    options_en: [
                        "<p>50 Hz</p>",
                        "<p>25 Hz</p>",
                        "<p>40 Hz</p>",
                        "<p>100 Hz</p>"
                    ],
                    options_hi: [
                        "<p>50 Hz</p>",
                        "<p>25 Hz</p>",
                        "<p>40 Hz</p>",
                        "<p>100 Hz</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>25 Hz. </strong>We know that f = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>T</mi></mfrac></math> <br>where f&nbsp;= frequency and T = time period of the vibration<br>f = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>0</mn><mo>.</mo><mn>04</mn></mrow></mfrac></math> = 25 Hz.</p>",
                    solution_hi: "<p>49.(b) <strong>25 Hz.</strong> हम जानते हैं कि f = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>T</mi></mfrac></math><br>जहाँ f = आवृत्ति और T = कंपन की समयावधि<br>f = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>0</mn><mo>.</mo><mn>04</mn></mrow></mfrac></math> = 25 Hz.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. The term &lsquo;Microfinancing&rsquo; was first used in the 1970s during the development of Grameen Bank of Bangladesh, which was founded by _______.</p>",
                    question_hi: "<p>50. माइक्रोफाइनेंसिंग\' शब्द का पहली बार उपयोग 1970 के दशक में बांग्लादेश के ग्रामीण बैंक के विकास के दौरान किया गया था, जिसकी स्थापना _____द्वारा की गई थी।</p>",
                    options_en: [
                        "<p>Muhammad Yunus</p>",
                        "<p>Adam Smith</p>",
                        "<p>Jeremy Bentham</p>",
                        "<p>Alfred Marshall</p>"
                    ],
                    options_hi: [
                        "<p>मुहम्मद यूनुस</p>",
                        "<p>एडम स्मिथ</p>",
                        "<p>जेरेमी बेंथम</p>",
                        "<p>अल्फ्रेड मार्शल</p>"
                    ],
                    solution_en: "<p>50.(a) <strong>Muhammad Yunus.</strong> Muhammad Yunus is a Bangladeshi economist, entrepreneur, politician, and civil society leader. He was awarded the Nobel Peace Prize in 2006 for founding the Grameen Bank and pioneering microcredit and microfinance. Microfinance provides banking services to low-income individuals or groups who typically lack access to traditional financial institutions.</p>",
                    solution_hi: "<p>50.(a) <strong>मुहम्मद यूनुस। </strong>मुहम्मद यूनुस एक बांग्लादेशी अर्थशास्त्री, उद्यमी, राजनीतिज्ञ और नागरिक समाज के नेता हैं। उन्हें ग्रामीण बैंक की स्थापना और माइक्रोक्रेडिट और माइक्रोफाइनेंस में अग्रणी भूमिका निभाने के लिए 2006 में नोबेल शांति पुरस्कार से सम्मानित किया गया था। माइक्रोफाइनेंस कम आय वाले व्यक्तियों या समूहों को बैंकिंग सेवाएँ प्रदान करता है, जिनकी आम तौर पर पारंपरिक वित्तीय संस्थानों तक पहुँच नहीं होती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. An 8 digit number is divisible by 99. If the digits are shuffled, then the number is always divisible by ________.</p>",
                    question_hi: "<p>51. एक 8 अंकीय संख्या 99 से विभाज्य है। यदि अंकों में फेरबदल (shuffled) कर दिया जाए, तो संख्या हमेशा _______ से विभाज्य होती है।</p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>11</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>11</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>51.(a)<br>Divisibility rule of 9 :- sum of digits is divisible by 9<br>When any 8 digit number is divisible by 99 it means that number is also divisible by 9 and 11.<br>Even if we shuffle the digits the number will be divisible by 9 but not by 11</p>",
                    solution_hi: "<p>51.(a)<br>9 की विभाज्यता नियम :- अंकों का योग 9 से विभाज्य होता है<br>जब कोई 8 अंकों की संख्या 99 से विभाज्य होती है तो इसका मतलब है कि वह संख्या 9 और 11 से भी विभाज्य है।<br>यदि हम अंकों में फेरबदल करें तो भी संख्या 9 से विभाज्य होगी लेकिन 11 से नहीं</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The value of the expression sin x + cosec x = 2, then the value of sin<sup>7</sup> x + cosec<sup>7</sup>x is:</p>",
                    question_hi: "<p>52. व्यंजक sin <math display=\"inline\"><mi>x</mi></math> + cosec x = 2 हो, तो sin<sup>7</sup>x + cosec<sup>7</sup>x का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>52.(c) sin x&nbsp;+ cosec x = 2<br>Put x&nbsp;= 90&deg; <br>&rArr; 1 + 1 = 2<br>&rArr; 2 = 2 (LHS = RHS)<br>Now , sin<sup>7</sup> x + cosec<sup>7</sup>x = 1<sup>7</sup> + 1<sup>7</sup> = 2</p>",
                    solution_hi: "<p>52.(c) Sin x&nbsp;+ Cosec x = 2<br>x = 90&deg; रखे <br>&rArr; 1 + 1 = 2<br>&rArr; 2 = 2 (बायाँ पक्ष = दायाँ पक्ष)<br>अब , sin<sup>7</sup> x + cosec<sup>7</sup>x = 1<sup>7</sup> + 1<sup>7</sup> = 2</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. R can finish a work in 6 days if he works 10 hours a day. S can finish it in 5 days if he works 8 hours a day. If both start a work and work daily for 6 hours, the work will be finished in _____ days.</p>",
                    question_hi: "<p>53. यदि R प्रतिदिन 10 घंटे काम करता है तो वह एक काम 6 दिनों में पूरा कर सकता है। यदि S प्रतिदिन 8 घंटे काम करता है तो वह इसे 5 दिनों में पूरा कर सकता है। यदि दोनों एक साथ मिलकर काम शुरू करते हैं और प्रतिदिन 6 घंटे काम करते हैं, तो काम कितने दिनों में पूरा हो जाएगा ?</p>",
                    options_en: [
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>5</p>",
                        "<p>4</p>",
                        "<p>3</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>53.(b) <br>According to the question,<br>R &times; 10 &times; 6 = S &times; 8 &times; 5<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mi>R</mi><mi>S</mi></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>60</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>Total work = 2 &times; 10 &times; 6 = 120 units<br>Required days = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math>&nbsp;= 4 days</p>",
                    solution_hi: "<p>53.(b) <br>प्रश्न के अनुसार,<br>R &times; 10 &times; 6 = S &times; 8 &times; 5<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mi>R</mi><mi>S</mi></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>60</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>कुल कार्य = 2 &times; 10 &times; 6 = 120 इकाई<br>आवश्यक दिन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>3</mn><mo>)</mo><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math> = 4 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. With a uniform speed, a car covers a distance in 16 hours. Had the speed been increased by 20 km/h, the same distance would have been covered in 12 hours. The total distance covered by the car is:</p>",
                    question_hi: "<p>54. एक कार एक समान चाल से किसी दूरी को 16 घंटे में तय करती है। यदि चाल 20 km/h बढ़ा दी जाती, तो यही दूरी 12 घंटे में तय की जा सकती है। कार द्वारा तय की गई कुल दूरी कितनी है ?</p>",
                    options_en: [
                        "<p>1080 km</p>",
                        "<p>480 km</p>",
                        "<p>720 km</p>",
                        "<p>960 km</p>"
                    ],
                    options_hi: [
                        "<p>1080 km</p>",
                        "<p>480 km</p>",
                        "<p>720 km</p>",
                        "<p>960 km</p>"
                    ],
                    solution_en: "<p>54.(d) <br>As we know, Time &prop; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>S</mi><mi>p</mi><mi>e</mi><mi>e</mi><mi>d</mi></mrow></mfrac></math> (when distance is constant)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Original&nbsp; &nbsp; &nbsp;New<br>Time&nbsp; &nbsp; &nbsp; &nbsp;16&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp;12 = 4 : 3<br>Speed&nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 4<br>4 - 3 = 1 unit = 20 km/hr<br>3 unit = 20 &times; 3 = 60 km/hr<br>Total distance covered by the car = 60 &times; 16 = 960 km</p>",
                    solution_hi: "<p>54.(d) <br>जैसा कि हम जानते हैं, समय &prop; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>&#2327;&#2340;&#2367;</mi></mfrac></math> (जब दूरी स्थिर हो)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; मूल&nbsp; :&nbsp; नया<br>समय - 16&nbsp; :&nbsp; &nbsp; 12 = 4 : 3<br>गति&nbsp; -&nbsp; &nbsp;3&nbsp; &nbsp;:&nbsp; &nbsp; 4<br>4 - 3 = 1 इकाई = 20 km/hr<br>3 इकाई = 20 &times; 3 = 60 km/hr<br>कार द्वारा तय की गई कुल दूरी = 60 &times; 16 = 960 km</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Find the average of all prime numbers between 42 and 75.</p>",
                    question_hi: "<p>55. 42 और 75 के बीच की सभी अभाज्य संख्याओं का औसत ज्ञात करें।</p>",
                    options_en: [
                        "<p>57.65</p>",
                        "<p>59.25</p>",
                        "<p>55.75</p>",
                        "<p>60.25</p>"
                    ],
                    options_hi: [
                        "<p>57.65</p>",
                        "<p>59.25</p>",
                        "<p>55.75</p>",
                        "<p>60.25</p>"
                    ],
                    solution_en: "<p>55.(b)<br>Prime no&rsquo;s between 42 and 75 = 43, 47, 53, 59, 61, 67, 71, 73<br>Required average =<math display=\"inline\"><mfrac><mrow><mn>43</mn><mo>+</mo><mn>47</mn><mo>+</mo><mn>53</mn><mo>+</mo><mn>59</mn><mo>+</mo><mn>61</mn><mo>+</mo><mn>67</mn><mo>+</mo><mn>71</mn><mo>+</mo><mn>73</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>474</mn><mn>48</mn></mfrac></math>&nbsp;= 59.25</p>",
                    solution_hi: "<p>55.(b)<br>42 और 75 के बीच अभाज्य संख्यायें = 43, 47, 53, 59, 61, 67, 71, 73<br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>43</mn><mo>+</mo><mn>47</mn><mo>+</mo><mn>53</mn><mo>+</mo><mn>59</mn><mo>+</mo><mn>61</mn><mo>+</mo><mn>67</mn><mo>+</mo><mn>71</mn><mo>+</mo><mn>73</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>474</mn><mn>48</mn></mfrac></math> = 59.25</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Ajit walks around a circular field at the speed of two rounds per hour, while Mohit runs around it at the speed of 10 rounds per hour. They start in the same direction from the same point at 6.30 a.m. Find the time at which they cross each other.</p>",
                    question_hi: "<p>56. अजीत एक वृत्ताकार मैदान के चारों ओर दो चक्कर प्रति घंटे की चाल से पैदल चलता है, जबकि मोहित इसके चारों ओर 10 चक्कर प्रति घंटे की चाल से दौड़ता है। वे 6.30 a.m. पर एक ही बिंदु से एक ही दिशा में प्रस्थान करते हैं। वह समय ज्ञात करें, जिस समय वे एक दूसरे को पार करते हैं।</p>",
                    options_en: [
                        "<p>06 : 37 : 50</p>",
                        "<p>06 : 37 : 20</p>",
                        "<p>06 : 37 : 30</p>",
                        "<p>06 : 40 : 00</p>"
                    ],
                    options_hi: [
                        "<p>06 : 37 : 50</p>",
                        "<p>06 : 37 : 20</p>",
                        "<p>06 : 37 : 30</p>",
                        "<p>06 : 40 : 00</p>"
                    ],
                    solution_en: "<p>56.(c) Time taken by Ajit to complete one round = 30 min.<br>Time taken by Mohit to complete one round = 6 min.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478887884.png\" alt=\"rId44\" width=\"270\" height=\"137\"><br>Relative efficiency = (5 - 1) = 4 {They are going in same direction}<br>Time taken = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = 7 min 30 second<br>Hence, time when they meet = 6 : 30 + 6 min 30 second = 6 : 37 : 30</p>",
                    solution_hi: "<p>56.(c) अजीत द्वारा एक चक्कर पूरा करने में लिया गया समय = 30 मिनट<br>मोहित द्वारा एक चक्कर पूरा करने में लगा समय = 6 मिनट<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478888099.png\" alt=\"rId45\" width=\"268\" height=\"139\"><br>सापेक्ष दक्षता = (5 - 1) = 4 {वे एक ही दिशा में जा रहे हैं}<br>लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = 7 मिनट 30 सेकंड<br>अत:, उनके मिलने का समय = 6 : 30 + 6 मिनट 30 सेकंड = 6 : 37 : 30</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If 855 &divide; 15 &minus; k + 32 &times; 5 = 1152 &divide; 16 &times; 111 &divide; 37, then the value of k is:</p>",
                    question_hi: "<p>57. यदि 855 &divide; 15 &minus; k + 32 &times; 5 = 1152 &divide; 16 &times; 111 &divide; 37 हो, तो k का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>3</p>"
                    ],
                    options_hi: [
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>3</p>"
                    ],
                    solution_en: "<p>57.(b)<br>855 &divide; 15 &minus; k + 32 &times; 5 = 1152 &divide; 16 &times; 111 &divide; 37<br>57 &minus; k + 160 = 72 &times; 3<br>57 &minus; k + 160 = 216<br>&minus; k = 216 - 217<br>&nbsp;k = 1</p>",
                    solution_hi: "<p>57.(b)<br>855 &divide; 15 &minus; k + 32 &times; 5 = 1152 &divide; 16 &times; 111 &divide; 37<br>57 &minus; k + 160 = 72 &times; 3<br>57 &minus; k + 160 = 216<br>&minus; k = 216 - 217<br>&nbsp;k = 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. If asin<sup>3</sup>x + bcos<sup>3</sup>x = sin x cos x and a sinx = b cosx , then find the value of a<sup>2</sup> + b<sup>2</sup>, provided that x is neither 0&deg; nor 90&deg;.</p>",
                    question_hi: "<p>58. यदि asin<sup>3</sup>x + bcos<sup>3</sup>x = sinxcosx और a sinx = b cosx हो, तो a<sup>2</sup> + b<sup>2</sup> का मान ज्ञात कीजिए, बशर्ते कि x न तो 0&deg; है और न ही 90&deg; है।</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>a<sup>2 </sup>- b<sup>2</sup></p>",
                        "<p>a<sup>2</sup> + b<sup>2</sup></p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>a<sup>2</sup> - b<sup>2</sup></p>",
                        "<p>a<sup>2</sup> + b<sup>2</sup></p>"
                    ],
                    solution_en: "<p>58.(b) <strong>Given: </strong>asin<sup>3</sup>x + bcos<sup>3</sup>x = sinx cosx and asinx = bcosx<br>asin<sup>3</sup>x + bcos<sup>3</sup>x = sinx cosx<br>asinx sin<sup>2</sup>x + bcos<sup>3</sup>x = sinx cosx<br>bcosx sin<sup>2</sup>x + bcos<sup>3</sup>x = sinx cosx<br>bcosx (sin<sup>2</sup>x + cos<sup>2</sup>x) = sinx cosx<br>bcosx = sinx cosx<br>b = sinx<br>asinx = bcosx<br>on putting value of b<br>asinx = sinx cosx<br>a = cosx<br>Putting value of a and b <br>a<sup>2</sup> + b<sup>2</sup> = sin<sup>2</sup>x + cos<sup>2</sup>x = 1</p>",
                    solution_hi: "<p>58.(b) <strong>दिया गया :</strong> asin<sup>3</sup>x + bcos<sup>3</sup>x = sinx cosx और asinx = bcosx<br>asin<sup>3</sup>x + bcos<sup>3</sup>x = sinx cosx<br>asinx sin<sup>2</sup>x + bcos<sup>3</sup>x = sinx cosx<br>bcosx sin<sup>2</sup>x + bcos<sup>3</sup>x = sinx cosx<br>bcosx (sin<sup>2</sup>x + cos<sup>2</sup>x) = sinx cosx<br>bcosx = sinx cosx<br>b = sinx<br>asinx = bcosx<br>b का मान रखने पर<br>asinx = sinx cosx<br>a = cosx<br>a और b के मान का उपयोग करने पर <br>a<sup>2</sup> + b<sup>2</sup> = sin<sup>2</sup>x + cos<sup>2</sup>x = 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Which of the following statements is INCORRECT about the circle ?</p>",
                    question_hi: "<p>59. वृत्त के संबंध निलिखित में से कौन-सा कथन असत्य है ?</p>",
                    options_en: [
                        "<p>The area of a sector = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> (arc length &times; R)</p>",
                        "<p>The radius of an incircle of an equilateral triangle of side \'s\' is <math display=\"inline\"><mfrac><mrow><mi>s</mi></mrow><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math></p>",
                        "<p>The length of an arc = <math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>e</mi><mi>n</mi><mi>t</mi><mi>r</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>a</mi><mi>n</mi><mi>g</mi><mi>l</mi><mi>e</mi><mi>&#160;</mi><mi>m</mi><mi>a</mi><mi>d</mi><mi>e</mi><mi>&#160;</mi><mi>b</mi><mi>y</mi><mi>&#160;</mi><mi>t</mi><mi>h</mi><mi>e</mi><mi>&#160;</mi><mi>a</mi><mi>r</mi><mi>c</mi></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; &pi;r<sup>2</sup>.</p>",
                        "<p>Circles are congruent if they have same radii while similar circles may have different radii.</p>"
                    ],
                    options_hi: [
                        "<p>त्रिज्यखंड का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> (चाप की लंबाई &times; R)</p>",
                        "<p>\'s\' भुजा वाले एक समबाहु त्रिभुज के अन्तर्वृत्त की त्रिज्या <math display=\"inline\"><mfrac><mrow><mi>s</mi></mrow><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> होगी।</p>",
                        "<p>चाप की लंबाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2330;&#2366;&#2346;</mi><mo>&#160;</mo><mi>&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</mi><mo>&#160;</mo><mi>&#2348;&#2344;&#2366;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2327;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2351;</mi><mo>&#160;</mo><mi>&#2325;&#2379;&#2339;</mi></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; &pi;r<sup>2</sup></p>",
                        "<p>यदि वृत्तों की त्रिज्याएँ समान हों तो वृत्त सर्वांगसम होते हैं जबकि समरूप वृत्तों की त्रिज्याएँ भिन्न-भिन्न हो सकती हैं।</p>"
                    ],
                    solution_en: "<p>59.(c) <br>Option &lsquo;c&rsquo; is false<br>Length of an arc = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 2&pi;r</p>",
                    solution_hi: "<p>59.(c) <br>विकल्प \'c\' गलत है<br>चाप की लंबाई = <math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; 2&pi;r</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. For what value of k will the lines 2x&nbsp;+ 7ky &ndash; 8 = 0 and x + y &ndash; 9 = 0 have no solution ?</p>",
                    question_hi: "<p>60. k के कौन-से मान के लिए समीकरण 2x + 7ky &ndash; 8 = 0 और x + y &ndash; 9 = 0 का कोई हल नहीं होगा ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>60.(a)<br>If the lines have no solution<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math>,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>k</mi></mrow><mn>1</mn></mfrac></math> <br>&rArr; k = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math></p>",
                    solution_hi: "<p>60.(a)<br>यदि रेखाओं का कोई हल नहीं है,<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math>,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>1</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>k</mi></mrow><mn>1</mn></mfrac></math><br>&rArr; k = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>7</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. Among the ratios 3 : 4, 5 : 8, 1 : 2, 6 : 9 which is the greatest ?</p>",
                    question_hi: "<p>61. 3 : 4, 5 : 8, 1 : 2, 6 : 9 अनुपात में सबसे बड़ा कौन-सा अनुपात होगा ?</p>",
                    options_en: [
                        "<p>5 : 8</p>",
                        "<p>6 : 9</p>",
                        "<p>1 : 2</p>",
                        "<p>3 : 4</p>"
                    ],
                    options_hi: [
                        "<p>5 : 8</p>",
                        "<p>6 : 9</p>",
                        "<p>1 : 2</p>",
                        "<p>3 : 4</p>"
                    ],
                    solution_en: "<p>61.(d)<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 0.75<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 0.625<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 0.5<br><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 0.666<br>We can clearly see that the greatest ratio is 3 : 4</p>",
                    solution_hi: "<p>61.(d)<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 0.75<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 0.625<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 0.5<br><math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = 0.666<br>हम स्पष्ट रूप से देख सकते हैं कि सबसे बड़ा अनुपात 3 : 4 है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Two inlet pipes, P1 and P2, can fill a cistern in 20 hours and 30 hours, respectively. They were opened at the same time, but pipe P1 had to be closed 5 hours before the cistern was full. How many hours in total did it take for the two pipes to fill the cistern ?</p>",
                    question_hi: "<p>62. दो इनलेट पाइप, P1 और P2, एक टंकी को क्रमश: 20 घंटे और 30 घंटे में भर सकते हैं। उन्हें एक ही समय पर खोला गया, लेकिन टंकी के पूरा भरने से 5 घंटे पहले पाइप P1 को बंद करना पड़ा। दोनों पाइपों के द्वारा टंकी को भरने में कुल कितने घंटे लगे ?</p>",
                    options_en: [
                        "<p>15 hours</p>",
                        "<p>9 hours</p>",
                        "<p>12 hours</p>",
                        "<p>10 hours</p>"
                    ],
                    options_hi: [
                        "<p>15 घंटे</p>",
                        "<p>9 घंटे</p>",
                        "<p>12 घंटे</p>",
                        "<p>10 घंटे</p>"
                    ],
                    solution_en: "<p>62.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478888440.png\" alt=\"rId46\" width=\"211\" height=\"125\"><br>Total efficiency of pipe (P1 + P2) = 3 + 2 = 5 units<br>Water filled by pipe P2 in 5 hours = 2 &times; 5 = 10 units<br>amount of water to be filled by both pipe = 60 - 10 = 50 unit<br>Time taken to fill 50 unit water by both pipe = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 10 hours<br>Hence, total time taken = 5 hrs + 10 hrs = 15 hrs</p>",
                    solution_hi: "<p>62.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478888731.png\" alt=\"rId47\" width=\"188\" height=\"127\"><br>पाइप (P1 +P2) की कुल दक्षता = 3 + 2 = 5 इकाई<br>पाइप P2 द्वारा 5 घंटे में भरा गया पानी = 2 &times; 5 = 10 इकाई<br>दोनों पाइपों द्वारा भरे जाने वाले पानी की मात्रा = 60 - 10 = 50 इकाई<br>दोनों पाइप द्वारा 50 इकाई पानी भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>50</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 10 घंटे<br>अतः, लिया गया कुल समय = 5 घंटे + 10 घंटे = 15 घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. Last year, Geeta\'s monthly salary was ₹12,000 and Seeta\'s monthly salary was ₹10,000. This year, Geeta\'s monthly salary is ₹14,400, while Seeta\'s monthly salary is ₹12,500. If the percentage increase in Seeta\'s monthly salary this year over her monthly salary last year is denoted by x%, and the percentage increase in Geeta\'s monthly salary this year over her monthly salary last year is denoted by y%, then what is the value of (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow><mi>y</mi></mfrac></math> &times; 100)%?</p>",
                    question_hi: "<p>63. गत वर्ष, गीता का मासिक वेतन ₹12,000 था और सीता का मासिक वेतन ₹10,000 था। इस वर्ष, गीता का मासिक वेतन ₹14,400 है, जबकि सीता का मासिक वेतन ₹12,500 है। यदि इस वर्ष सीता के मासिक वेतन में पिछले वर्ष के मासिक वेतन की तुलना में प्रतिशत वृद्धि को x% से निरुपित किया जाता है, और इस वर्ष गीता के मासिक वेतन में पिछले वर्ष के मासिक वेतन की तुलना में प्रतिशत वृद्धि को y% से निरुपित किया जाता है, तो (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow><mi>y</mi></mfrac></math> &times; 100)% का मान क्या है ?</p>",
                    options_en: [
                        "<p>22</p>",
                        "<p>20</p>",
                        "<p>24</p>",
                        "<p>25</p>"
                    ],
                    options_hi: [
                        "<p>22</p>",
                        "<p>20</p>",
                        "<p>24</p>",
                        "<p>25</p>"
                    ],
                    solution_en: "<p>63.(d) <br>According to question,<br>y % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14400</mn><mo>-</mo><mn>12000</mn></mrow><mn>12000</mn></mfrac></math> &times; 100 = 20 %<br>x % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12500</mn><mo>-</mo><mn>10000</mn></mrow><mn>10000</mn></mfrac></math> &times; 100 = 25 %<br>So, (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow><mi>y</mi></mfrac></math> &times; 100)% = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>20</mn></mrow><mn>20</mn></mfrac></math> &times; 100)% = 25%</p>",
                    solution_hi: "<p>63.(d) <br>प्रश्न के अनुसार,<br>y % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14400</mn><mo>-</mo><mn>12000</mn></mrow><mn>12000</mn></mfrac></math> &times; 100 = 20 %<br>x % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12500</mn><mo>-</mo><mn>10000</mn></mrow><mn>10000</mn></mfrac></math> &times; 100 = 25 %<br>तो, (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow><mi>y</mi></mfrac></math> &times; 100)% = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>-</mo><mn>20</mn></mrow><mn>20</mn></mfrac></math> &times; 100)% = 25%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A shopkeeper buys 1800 kg wheat for ₹32,400. If 20% of this wheat is spoiled due to&nbsp;rain, at what rate (₹/kg) should he sell the rest to earn 20% ?</p>",
                    question_hi: "<p>64. एक दुकानदार ₹32,400 में 1800 kg गेहूं खरीदता है। यदि इस गेहूं का 20% बारिश के कारण खराब हो जाता है, तो उसे 20% लाभ अर्जित करने के लिए शेष गेहूं को किस दर (₹/kg) पर बेचना चाहिए ?</p>",
                    options_en: [
                        "<p>26</p>",
                        "<p>29</p>",
                        "<p>27</p>",
                        "<p>25</p>"
                    ],
                    options_hi: [
                        "<p>26</p>",
                        "<p>29</p>",
                        "<p>27</p>",
                        "<p>25</p>"
                    ],
                    solution_en: "<p>64.(c)<br>Remaining wheats = 1800 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 1440 kg<br>Required rate = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32400</mn><mo>&#215;</mo><mn>120</mn><mi>%</mi></mrow><mn>1440</mn></mfrac></math> = 27 ₹/kg</p>",
                    solution_hi: "<p>64.(c)<br>शेष गेहूं = 1800 &times; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 1440 kg<br>आवश्यक दर = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>32400</mn><mo>&#215;</mo><mn>120</mn><mi>%</mi></mrow><mn>1440</mn></mfrac></math> = 27 ₹/kg</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Two successive percentage decrease of 25% each is by what percentage less than two successive percentage increase of 25% each ? (Round to two decimal places.)</p>",
                    question_hi: "<p>65. 25% प्रत्येक की दो क्रमिक प्रतिशत कमी, 25% प्रत्येक की दो क्रमिक प्रतिशत वृद्धि से कितने प्रतिशत कम है ? (दशमलव के दो स्थानों तक पूर्णांकित करें।)</p>",
                    options_en: [
                        "<p>50%</p>",
                        "<p>43.75%</p>",
                        "<p>22.22%</p>",
                        "<p>56.25%</p>"
                    ],
                    options_hi: [
                        "<p>50%</p>",
                        "<p>43.75%</p>",
                        "<p>22.22%</p>",
                        "<p>56.25%</p>"
                    ],
                    solution_en: "<p>65.(c)<br>Net decrease = 25 + 25 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>100</mn></mfrac></math> = 43.75 %<br>Net Increase = 25 + 25 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>100</mn></mfrac></math> = 56.25 %<br>Required % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>56</mn><mo>.</mo><mn>25</mn><mo>-</mo><mn>43</mn><mo>.</mo><mn>75</mn><mo>)</mo></mrow><mrow><mn>56</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>56</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> &times; 100 = 22.22%</p>",
                    solution_hi: "<p>65.(c)<br>शुद्ध कमी = 25 + 25 - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>100</mn></mfrac></math> = 43.75 %<br>शुद्ध वृद्धि = 25 + 25 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>&#215;</mo><mn>25</mn></mrow><mn>100</mn></mfrac></math> = 56.25 %<br>आवश्यक % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>56</mn><mo>.</mo><mn>25</mn><mo>-</mo><mn>43</mn><mo>.</mo><mn>75</mn><mo>)</mo></mrow><mrow><mn>56</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> &times; 100<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>56</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> &times; 100 = 22.22%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A borrows ₹89,500 for 3 years at 7% p.a. simple interest. B borrows the same amount at simple interest of 9% for the first year, 8% for the second year, and 7% for the third year. What is the total simple interest (in ₹) paid by A and B ?</p>",
                    question_hi: "<p>66. A ने 3 वर्षों के लिए 7% वार्षिक साधारण ब्याज पर ₹89,500 की धनराशि उधार ली। B ने साधारण ब्याज पर समान धनराशि पहले वर्ष के लिए 9%, दूसरे वर्ष के लिए 8% और तीसरे वर्ष के लिए 7% की दर पर उधार ली। A और B द्वारा भुगतान किए गए कुल साधारण ब्याज (₹ में) की गणना कीजिए।</p>",
                    options_en: [
                        "<p>45,027</p>",
                        "<p>47,025</p>",
                        "<p>42,075</p>",
                        "<p>40,275</p>"
                    ],
                    options_hi: [
                        "<p>45,027</p>",
                        "<p>47,025</p>",
                        "<p>42,075</p>",
                        "<p>40,275</p>"
                    ],
                    solution_en: "<p>66.(d)<br>S.I. for A = 89500 &times; 21% = ₹18,795<br>S.I. for B = 89500 &times; (9 + 8 + 7)% = ₹21,480<br>Hence, total S.I. paid by A and B = 18,795 + 21,480 = ₹40,275</p>",
                    solution_hi: "<p>66.(d)<br>A के लिए साधारण ब्याज = 89500 &times; 21% = ₹18,795<br>B के लिए साधारण ब्याज = 89500 &times; (9 + 8 + 7)% = ₹21,480<br>इसलिए, A और B द्वारा भुगतान किया गया कुल साधारण ब्याज = 18,795 + 21,480 = ₹40,275</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The given table shows the percentage of marks obtained by three students in three different subjects in an institute. (maximum marks are given in brackets)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478889006.png\" alt=\"rId48\" width=\"355\" height=\"113\"> <br>If in order to pass the exam, a minimum of 105 marks are needed in History, how many students pass in the exam ?</p>",
                    question_hi: "<p>67. दी गई तालिका में एक संस्थान में तीन अलग-अलग विषयों में तीन विद्यार्थियों द्वारा प्राप्त अंकों का प्रतिशत दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478889321.png\" alt=\"rId49\" width=\"313\" height=\"123\"> <br>यदि परीक्षा उत्तीर्ण करने के लिए इतिहास में न्यूनतम 105 अंक चाहिए, तो कितने विद्यार्थी परीक्षा में उत्तीर्ण हुए हैं ?</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>1</p>",
                        "<p>0</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>67.(b) According to the question,<br>Passing marks of Ram in History = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 94.5<br>Passing marks of Mohan in History = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 89.25<br>Passing marks of Shyam in History = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 131.25<br>Hence, only 1 student passed the examination.</p>",
                    solution_hi: "<p>67.(b) प्रश्न के अनुसार,<br>इतिहास विषय में राम के उत्तीर्ण अंक = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 94.5<br>इतिहास विषय में मोहन के उत्तीर्ण अंक = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>51</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 89.25<br>इतिहास विषय में श्याम के उत्तीर्ण अंक = 175 &times; <math display=\"inline\"><mfrac><mrow><mn>75</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 131.25<br>अतः, केवल 1 छात्र परीक्षा में उत्तीर्ण हुआ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. In a class of 150 students (boys and girls only), the girls are 60. The average weight of boys is 52 kg and that of girls is 48 kg. What is the average weight (in kg) of the whole class ?</p>",
                    question_hi: "<p>68. 150 विद्यार्थियों (यों केवल लड़के और लड़कियाँ) की एक कक्षा में 60 लड़कियाँ हैं। लड़कों का औसत वजन 52 kg है और लड़कियों का औसत वजन 48 kg है। पूरी कक्षा का औसत वजन (kg में) क्या है ?</p>",
                    options_en: [
                        "<p>50.4</p>",
                        "<p>48.8</p>",
                        "<p>49.6</p>",
                        "<p>51.2</p>"
                    ],
                    options_hi: [
                        "<p>50.4</p>",
                        "<p>48.8</p>",
                        "<p>49.6</p>",
                        "<p>51.2</p>"
                    ],
                    solution_en: "<p>68.(a)<br>Total = 150<br>Girls = 60<br>Boys = 150 - 60 = 90<br>Average weight of whole class = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>&#215;</mo><mn>52</mn><mo>+</mo><mn>60</mn><mo>&#215;</mo><mn>48</mn></mrow><mn>150</mn></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>52</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>48</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>156</mn><mo>+</mo><mn>96</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>252</mn><mn>5</mn></mfrac></math> = 50.4</p>",
                    solution_hi: "<p>68.(a)<br>कुल = 150<br>लड़कियाँ = 60<br>लड़के = 150 - 60 = 90<br>पूरी कक्षा का औसत वजन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mo>&#215;</mo><mn>52</mn><mo>+</mo><mn>60</mn><mo>&#215;</mo><mn>48</mn></mrow><mn>150</mn></mfrac></math><br>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>&#215;</mo><mn>52</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>48</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>156</mn><mo>+</mo><mn>96</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>252</mn><mn>5</mn></mfrac></math> = 50.4</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The population of a town first decreased by 16% due to migration to a big city for&nbsp;better job opportunities. The next year, the population of the town increased by 21% as&nbsp;there were better facilities for jobs. What is the net percentage change (correct to 2&nbsp;decimal places) in the population ?</p>",
                    question_hi: "<p>69. बेहतर नौकरी के अवसरों के लिए एक बड़े शहर में प्रवास के कारण पहले एक कस्बे की आबादी में 16% की कमी आई। अगले वर्ष, कस्बे की आबादी में 21% की वृद्धि हुई क्योंकि वहां नौकरियों के लिए बेहतर&nbsp;अवसर मिलने लगे। जनसंख्या में निवल प्रतिशत परिवर्तन (दशमलव के 2 स्थानों तक सही) कितना है ?</p>",
                    options_en: [
                        "<p>Decrease by 2.54%</p>",
                        "<p>Increase by 2.54%</p>",
                        "<p>Decrease by 1.64%</p>",
                        "<p>Increase by 1.64%</p>"
                    ],
                    options_hi: [
                        "<p>2.54% की कमी</p>",
                        "<p>2.54% की वृद्धि</p>",
                        "<p>1.64% की कमी</p>",
                        "<p>1.64% की वृद्धि</p>"
                    ],
                    solution_en: "<p>69.(d) Let the population of a town = 100 <br>According to question,<br>Population after two year = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>121</mn><mn>100</mn></mfrac></math> = 101.64 <br>Net increase change % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>101</mn><mo>.</mo><mn>64</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math> &times; 100 = 1.64%</p>",
                    solution_hi: "<p>69.(d) माना कि एक कस्बे की जनसंख्या = 100 है <br>प्रश्न के अनुसार,<br>दो वर्ष बाद जनसंख्या = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>121</mn><mn>100</mn></mfrac></math> = 101.64<br>शुद्ध वृद्धि परिवर्तन % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>101</mn><mo>.</mo><mn>64</mn><mo>-</mo><mn>100</mn></mrow><mn>100</mn></mfrac></math> &times; 100 = 1.64%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The given bar graph shows the production of bikes by a company (in thousands) over the years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478889566.png\" alt=\"rId50\" width=\"374\" height=\"234\"> <br>In how many of the given years was the production of bikes more than the average production of bikes over the years ?</p>",
                    question_hi: "<p>70. निम्नांकित बार ग्राफ दिए गए वर्षों में एक कंपनी द्वारा बाइक के उत्पादन (हजारों में) को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478889790.png\" alt=\"rId51\" width=\"330\" height=\"236\"> <br>Production of bikes by a company (in thousands)-कंपनी द्वारा बाइक का उत्पादन (हजारों में)<br>दिए गए वर्षों में से कितने वर्षों में बाइक का उत्पादन, दिए गए वर्षों में बाइक के औसत उत्पादन से अधिक था ?</p>",
                    options_en: [
                        "<p>1 year</p>",
                        "<p>2 year</p>",
                        "<p>3 year</p>",
                        "<p>4 year</p>"
                    ],
                    options_hi: [
                        "<p>1 वर्ष</p>",
                        "<p>2 वर्ष</p>",
                        "<p>3 वर्ष</p>",
                        "<p>4 वर्ष</p>"
                    ],
                    solution_en: "<p>70.(c) Average production of bikes all over the years = <math display=\"inline\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>50</mn><mo>+</mo><mn>70</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>60</mn><mo>+</mo><mn>30</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>375</mn><mn>7</mn></mfrac></math> = 53.57 <br>Bike production of 3 years (2010, 2011 and 2013) more than the average marks.</p>",
                    solution_hi: "<p>70.(c) कंपनी द्वारा सभी वर्षो में बाइक औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>35</mn><mo>+</mo><mn>50</mn><mo>+</mo><mn>70</mn><mo>+</mo><mn>90</mn><mo>+</mo><mn>40</mn><mo>+</mo><mn>60</mn><mo>+</mo><mn>30</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>375</mn><mn>7</mn></mfrac></math> = 53.57&nbsp;<br>3 वर्षों (2010, 2011 और 2013) में बाइक उत्पादन औसत अंक से अधिक है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. A motorboat travelling at some speed can cover 24 km upstream and 40 km downstream in 17 hours. At the same speed it can travel 32 km downstream and 12 km upstream in 10 hours. The speed of the stream is:</p>",
                    question_hi: "<p>71. किसी चाल से चलती हुई एक मोटरबोट धारा की विपरीत दिशा में 24 km और धारा की दिशा में 40 km की दूरी 17 घंटे में तय कर सकती है। समान चाल से यह 10 घंटे में धारा की दिशा में 32 km और धारा की विपरीत दिशा में 12 km की यात्रा कर सकती है। धारा की चाल क्या है ?</p>",
                    options_en: [
                        "<p>5 km/h</p>",
                        "<p>4 km/h</p>",
                        "<p>2 km/h</p>",
                        "<p>3 km/h</p>"
                    ],
                    options_hi: [
                        "<p>5 km/h</p>",
                        "<p>4 km/h</p>",
                        "<p>2 km/h</p>",
                        "<p>3 km/h</p>"
                    ],
                    solution_en: "<p>71.(d) Let speed of boat in still water = x km/h&nbsp;<br>Speed of stream = y km/h <br>According to question , <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 17 &hellip;&hellip; (i)<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 10 &hellip;.(ii)<br>Multiply eq (ii) by 2&nbsp;<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 20 &hellip;.(iii)<br>Subtract eq (i) from eq .(iii) we get <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 3<br>&rArr; x + y = 8 &hellip;.(iv)<br>Put this value in eqn (ii) we get <br>&rArr; x - y = 2 &hellip;.(v)<br>After solving eqn (iv) and (v) we get <br>x = 5 km/h , y = 3 km/h <br>speed of the stream = 3km/h</p>",
                    solution_hi: "<p>71.(d) माना , शांत पानी में नाव की गति = x km/h&nbsp;<br>धारा की गति = y km/h <br>प्रश्न के अनुसार , <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 17 &hellip;&hellip; (i)<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 10 &hellip;.(ii)<br>समीकरण (ii) को 2 से गुणा करने पर <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 20 &hellip;.(iii)<br>समीकरण (iii) से समीकरण (i) घटाने पर <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> = 3<br>&rArr; x + y = 8 &hellip;.(iv)<br>इस मान को समीकरण (ii) में रखने पर हमें प्राप्त होता है <br><math display=\"inline\"><mo>&#8658;</mo></math> x -y = 2 &hellip;.(v)<br>समीकरण (iv) और (v) को हल करने के बाद<br>x = 5 km/h , y = 3 km/h <br>धारा की गति = 3km/h</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The least number which is exactly divisible by 27, 36, 56, 12 and 10 ?</p>",
                    question_hi: "<p>72. निम्न में से वह न्यूनतम संख्या कौन-सी है, जो 27, 36, 56, 12 और 10 से पूरी तरह विभाज्य हो ?</p>",
                    options_en: [
                        "<p>7560</p>",
                        "<p>8584</p>",
                        "<p>5953</p>",
                        "<p>7694</p>"
                    ],
                    options_hi: [
                        "<p>7560</p>",
                        "<p>8584</p>",
                        "<p>5953</p>",
                        "<p>7694</p>"
                    ],
                    solution_en: "<p>72.(a)<br>Least no = LCM of (27, 36, 56, 12, 10) = 3<sup>3</sup>&nbsp;&times; 2<sup>3</sup> &times; 7 &times; 5 = 7560</p>",
                    solution_hi: "<p>72.(a) न्यूनतम संख्या = (27, 36, 56, 12, 10) का LCM = 3<sup>3</sup>&nbsp;&times; 2<sup>3</sup> &times; 7 &times; 5 = 7560</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The centers of two circles of radii 25 cm and 35 cm are 80 cm apart. What is the ratio of the lengths of the transverse common tangent to the direct common tangent to these circles ?</p>",
                    question_hi: "<p>73. 25 cm और 35 cm त्रिज्या वाले दो वृत्तों के केंद्र एक-दूसरे से 80 cm दूरी पर हैं। इन वृत्तों की अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा की लंबाई और सीधी उभयनिष्ठ स्पर्श रेखा की लंबाई का अनुपात क्या है ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 3</p>",
                        "<p>2 : 3</p>",
                        "<p>2 : <math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 2</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 3</p>",
                        "<p>2 : 3</p>",
                        "<p>2 : <math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>",
                        "<p>3<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 2</p>"
                    ],
                    solution_en: "<p>73.(b) <br><strong>Formula:-</strong><br>Lengths of the transverse common tangent = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>R</mi><mo>+</mo><mi>r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>Lengths of the direct common tangent = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>R</mi><mo>-</mo><mi>r</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>Required ratio = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>35</mn><mo>+</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>35</mn><mo>-</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>60</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>140</mn><mo>&#215;</mo><mn>20</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>90</mn><mo>&#215;</mo><mn>70</mn></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>&#215;</mo><mn>20</mn><mo>&#215;</mo><mn>20</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>100</mn></msqrt></math><br>= 20<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 30<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math><br>= 2 : 3</p>",
                    solution_hi: "<p>73.(b) <br><strong>सूत्र:-</strong><br>अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा की लंबाई = <math display=\"inline\"><msqrt><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>R</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>r</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>सीधी उभयनिष्ठ स्पर्शरेखा की लंबाई = <math display=\"inline\"><msqrt><msup><mrow><mi>d</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>R</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>r</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>आवश्यक अनुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>35</mn><mo>+</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>35</mn><mo>-</mo><mn>25</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>60</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>80</mn><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>140</mn><mo>&#215;</mo><mn>20</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>90</mn><mo>&#215;</mo><mn>70</mn></msqrt></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>&#215;</mo><mn>20</mn><mo>&#215;</mo><mn>20</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>100</mn></msqrt></math><br>= 20<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : 30<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math><br>= 2 : 3</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. If the point of intersection of lines x + y = 2 and 2x - y = 1 lies on y = Kx + 5, what is the value of K ?</p>",
                    question_hi: "<p>74. यदि रेखाओं x + y = 2 और 2x - y = 1 का प्रतिच्छेद बिंदु y = Kx + 5 पर स्थित है, तो K का मान ज्ञात कीजिए</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>-4</p>",
                        "<p>4</p>",
                        "<p>-3</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>-4</p>",
                        "<p>4</p>",
                        "<p>-3</p>"
                    ],
                    solution_en: "<p>74.(b) <br>x + y = 2&hellip;&hellip;(i)<br>2x - y = 1&hellip;&hellip;.(ii)<br>On solving both equation we get,<br>x = 1, y = 1<br>According to the question,<br>1 = K &times;&nbsp;1 + 5<br>K = 1 - 5 = - 4</p>",
                    solution_hi: "<p>74.(b) <br>x + y = 2&hellip;&hellip;(i)<br>2x - y = 1&hellip;&hellip;.(ii)<br>दोनों समीकरणों को हल करने पर हमें प्राप्त होता है,<br>x = 1, y = 1<br>प्रश्न के अनुसार,<br>1 = K &times; 1 + 5<br>K = 1 - 5 = - 4</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. In an isosceles triangle, the vertex angle is four times the sum of the base angles. The measure of the square root of the vertex angle is:</p>",
                    question_hi: "<p>75. एक समद्विबाहु त्रिभुज में, शीर्ष कोण, आधार कोणों के योग का चार गुना है। शीर्ष कोण के वर्गमूल का&nbsp;माप ______है।</p>",
                    options_en: [
                        "<p>10&deg;</p>",
                        "<p>12&deg;</p>",
                        "<p>18&deg;</p>",
                        "<p>16&deg;</p>"
                    ],
                    options_hi: [
                        "<p>10&deg;</p>",
                        "<p>12&deg;</p>",
                        "<p>18&deg;</p>",
                        "<p>16&deg;</p>"
                    ],
                    solution_en: "<p>75.(b)<br>ATQ, we have given triangle : <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478890081.png\" alt=\"rId52\" width=\"182\" height=\"120\"><br>Now, x + x + 8x = 180&deg; (sum of the angles of triangle is 180&deg;)<br>10x = 180&deg;<br>&nbsp;x = 18&deg;<br>Vertex angle(A) = 8x = 8 &times; 18&deg; = 144&deg;<br>Then, square root of the vertex angle = <math display=\"inline\"><msqrt><mn>144</mn></msqrt></math> = 12&deg;</p>",
                    solution_hi: "<p>75.(b)<br>प्रश्न के अनुसार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1743478890081.png\" alt=\"rId52\" width=\"182\" height=\"120\"><br>अब, x + x + 8x = 180&deg; (त्रिभुज के कोणों का योग 180&deg; होता है)<br>10x = 180&deg;<br>&nbsp;x = 18&deg;<br>शीर्ष कोण(A) = 8x = 8 &times; 18&deg; = 144&deg;<br>फिर शीर्ष कोण का वर्गमूल = <math display=\"inline\"><msqrt><mn>144</mn></msqrt></math> = 12&deg;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the word which means the same as the group of words given. <br>One who denies oneself ordinary bodily pleasures</p>",
                    question_hi: "<p>76. Select the word which means the same as the group of words given. <br>One who denies oneself ordinary bodily pleasures</p>",
                    options_en: [
                        "<p>Atheist</p>",
                        "<p>Theist</p>",
                        "<p>Aesthetic</p>",
                        "<p>Ascetic</p>"
                    ],
                    options_hi: [
                        "<p>Atheist</p>",
                        "<p>Theist</p>",
                        "<p>Aesthetic</p>",
                        "<p>Ascetic</p>"
                    ],
                    solution_en: "<p>76.(d) <strong>Ascetic</strong>-One who denies oneself ordinary bodily pleasures<br>(a) <strong>Atheist</strong>-a person who disbelieves or lacks belief in the existence of God or gods.<br>(b) <strong>Theist</strong>-a person who believes in the existence of a god or gods, specifically of a creator who intervenes in the universe.<br>(c) <strong>Aesthetic</strong>- concerned with beauty or the appreciation of beauty.</p>",
                    solution_hi: "<p>76.(d) <strong>Ascetic</strong>-तपस्वी-One who&nbsp;denies oneself ordinary bodily pleasures<br>(a) <strong>Atheist</strong>-नास्तिक-a person who disbelieves or lacks belief in the existence of God or gods.<br>(b) <strong>Theist</strong>-आस्तिक-a person who believes in the existence of a god or gods, specifically of a creator who intervenes in the universe.<br>(c) <strong>Aesthetic</strong>-सौंदर्यबोध- concerned with beauty or the appreciation of beauty.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. The following sentence has been divided into four segments. Identify the segment that contains an error. <br>Mr. Abhilash and his family / have received / no informations / about the incident.</p>",
                    question_hi: "<p>77. The following sentence has been divided into four segments. Identify the segment that contains an error. <br>Mr. Abhilash and his family / have received / no informations / about the incident.</p>",
                    options_en: [
                        "<p>have received</p>",
                        "<p>no informations</p>",
                        "<p>about the incident</p>",
                        "<p>Mr. Abhilash and his family</p>"
                    ],
                    options_hi: [
                        "<p>have received</p>",
                        "<p>no informations</p>",
                        "<p>about the incident</p>",
                        "<p>Mr. Abhilash and his family</p>"
                    ],
                    solution_en: "<p>77.(b) no informations<br>&lsquo;Informations&rsquo; must be replaced with &lsquo;information&rsquo; as &lsquo;information&rsquo; is an uncountable noun and its plural cannot be formed. Hence, \'no information\' is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(b) no informations<br>&lsquo;Informations&rsquo; के स्थान पर &lsquo;information&rsquo; का प्रयोग होगा क्योंकि &lsquo;information&rsquo; एक uncountable noun है और इसका plural नहीं बनाया जा सकता है। अतः, \'no information\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate idiom that can substitute the underlined segment in the given sentence. <br>Read the books of APJ Abdul Kalam, who was a <span style=\"text-decoration: underline;\">scholar</span>.</p>",
                    question_hi: "<p>78. Select the most appropriate idiom that can substitute the underlined segment in the given sentence. <br>Read the books of APJ Abdul Kalam, who was a <span style=\"text-decoration: underline;\">scholar</span>.</p>",
                    options_en: [
                        "<p>a dare devil</p>",
                        "<p>a queer fish</p>",
                        "<p>a man of straw</p>",
                        "<p>a man of letters</p>"
                    ],
                    options_hi: [
                        "<p>a dare devil</p>",
                        "<p>a queer fish</p>",
                        "<p>a man of straw</p>",
                        "<p>a man of letters</p>"
                    ],
                    solution_en: "<p>78.(d) <strong>A man of letters - </strong>a scholar.</p>",
                    solution_hi: "<p>78.(d) <strong>A man of letters -</strong> a scholar./विद्यार्थी</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79.&nbsp;A part of the sentence is underlined. Below are given alternatives part a, b and c which may improve the sentence. Choose the correct alternative .In case no improvement is needed your answer is d.<br>All the people are supposed to <span style=\"text-decoration: underline;\"><strong>take out</strong></span> their shoes when they enter a place of worship.</p>",
                    question_hi: "<p>79.&nbsp;A part of the sentence is underlined. Below are given alternatives part a, b and c which may improve the sentence. Choose the correct alternative .In case no improvement is needed your answer is d.<br>All the people are supposed to <span style=\"text-decoration: underline;\"><strong>take out</strong></span> their shoes when they enter a place of worship.</p>",
                    options_en: [
                        " to take off      ",
                        " to put off",
                        " to put away      ",
                        " no improvement"
                    ],
                    options_hi: [
                        " to take off      ",
                        " to put off",
                        " to put away      ",
                        " no improvement"
                    ],
                    solution_en: "79.(a) to take off  <br />‘To take off’ is a phrasal verb. It means removing something from our body. For example - He took off his cap before he entered the principal’s room. Similarly, all the people in the given sentence are supposed to take off their shoes. Hence, ‘to take off’ is the most appropriate answer.",
                    solution_hi: "79.(a) to take off  <br />\'To take off\' एक phrasal verb है जिसका अर्थ है - हमारे शरीर से कुछ निकालना/ हटाना / उतारना । उदाहरण के लिए- उसने principal के कमरे में प्रवेश करने से पहले अपनी टोपी उतार दी। इसी तरह, दिए गए वाक्य में सभी लोगों को अपने जूते उतारने हैं। इसलिए, \'to take off\' सबसे उपयुक्त उत्तर है।<br />                 ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that best explains the underlined idiom in the given sentence.<br>A team\'s captain always <strong><span style=\"text-decoration: underline;\">keeps a level head</span></strong>, even in the most challenging situations.</p>",
                    question_hi: "<p>80. Select the option that best explains the underlined idiom in the given sentence.<br>A team\'s captain always <span style=\"text-decoration: underline;\"><strong>keeps a level head</strong></span>, even in the most challenging situations.</p>",
                    options_en: [
                        "<p>Keeps his head protected with a helmet</p>",
                        "<p>Prefers to keep his head covered with a cap</p>",
                        "<p>Maintains an equal number of players on the field</p>",
                        "<p>Remains calm and composed</p>"
                    ],
                    options_hi: [
                        "<p>Keeps his head protected with a helmet</p>",
                        "<p>Prefers to keep his head covered with a cap</p>",
                        "<p>Maintains an equal number of players on the field</p>",
                        "<p>Remains calm and composed</p>"
                    ],
                    solution_en: "<p>80.(d) <strong>Keep a level head-</strong> remain calm and composed.</p>",
                    solution_hi: "<p>80.(d) <strong>Keep a level head -</strong> remain calm and composed./शांत एवं संयमित रहना</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate antonym of the given word. <br>Wax</p>",
                    question_hi: "<p>81. Select the most appropriate antonym of the given word. <br>Wax</p>",
                    options_en: [
                        "<p>Wane</p>",
                        "<p>Mourn</p>",
                        "<p>Error</p>",
                        "<p>Insolent</p>"
                    ],
                    options_hi: [
                        "<p>Wane</p>",
                        "<p>Mourn</p>",
                        "<p>Error</p>",
                        "<p>Insolent</p>"
                    ],
                    solution_en: "<p>81.(a) Wane<br><strong>Wane- </strong>to become gradually weaker or less important<br><strong>Wax -</strong> Increase , rise , growth<br><strong>Mourn-</strong> to feel and show great sadness, especially because somebody has died<br><strong>Error- </strong>a mistake<br><strong>Insolent</strong>- lacking respect</p>",
                    solution_hi: "<p>81.(a) Wane<br><strong>Wane - </strong>धीरे-धीरे कमजोर या कम महत्वपूर्ण हो जाना<br><strong>Wax -</strong> वृद्धि<br><strong>Mourn -</strong> बहुत दुख महसूस करना और दिखाना, खासकर क्योंकि किसी की मृत्यु हो गई है<br><strong>Error- </strong>गलती<br><strong>Insolent - </strong>सम्मान की कमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;Rajeev, when is the next train ?&rdquo;</p>",
                    question_hi: "<p>82. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>He said, &ldquo;Rajeev, when is the next train ?&rdquo;</p>",
                    options_en: [
                        "<p>He asked Rajeev when the next train was.</p>",
                        "<p>He asked Rajeev when was the next train.</p>",
                        "<p>He asks Rajeev when the next train was.</p>",
                        "<p>He asked when the next train is.</p>"
                    ],
                    options_hi: [
                        "<p>He asked Rajeev when the next train was.</p>",
                        "<p>He asked Rajeev when was the next train.</p>",
                        "<p>He asks Rajeev when the next train was.</p>",
                        "<p>He asked when the next train is.</p>"
                    ],
                    solution_en: "<p>82.(a) He asked Rajeev when the next train was.<br>(b) He asked Rajeev when<strong> was the next train.</strong> (Interrogative pattern)<br>(c) He <strong>asks </strong>Rajeev when the next train was. (Incorrect Reporting Verb)<br>(d) He asked when the next train <strong>is.</strong> (Incorrect change of tense)</p>",
                    solution_hi: "<p>82.(a) He asked Rajeev when the next train was.<br>(b) He asked Rajeev when <strong>was the next train.</strong> (Interrogative पैटर्न है)<br>(c) He <strong>asks </strong>Rajeev when the next train was. (ग़लत Reporting Verb)<br>(d) He asked when the next train<strong> is.</strong> (Tense का गलत परिवर्तन)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the option that is the passive form of the sentence<br>The sub-committee is presenting its report in a week&rsquo;s time.</p>",
                    question_hi: "<p>83. Select the option that is the passive form of the sentence<br>The sub-committee is presenting its report in a week&rsquo;s time.</p>",
                    options_en: [
                        "<p>The report of the sub-committee has been presented in a week&rsquo;s time.</p>",
                        "<p>The report of the sub-committee is being presented in a week&rsquo;s time.</p>",
                        "<p>The report of the sub-committee is presenting in a week&rsquo;s time.</p>",
                        "<p>The report of the sub-committee had been presented in a week&rsquo;s time.</p>"
                    ],
                    options_hi: [
                        "<p>The report of the sub-committee has been presented in a week&rsquo;s time.</p>",
                        "<p>The report of the sub-committee is being presented in a week&rsquo;s time.</p>",
                        "<p>The report of the sub-committee is presenting in a week&rsquo;s time.</p>",
                        "<p>The report of the sub-committee had been presented in a week&rsquo;s time.</p>"
                    ],
                    solution_en: "<p>83.(b) The report of the sub-committee is being presented in a week&rsquo;s time. (Correct)<br>(a) The report of the sub-committee <span style=\"text-decoration: underline;\">has been</span> presented in a week&rsquo;s time. (Tense has changed)<br>(c) The report of the sub-committee <span style=\"text-decoration: underline;\">is presenting</span> in a week&rsquo;s time. (Incorrect Verb)<br>(d) The report of the sub-committee <span style=\"text-decoration: underline;\">had been</span> presented in a week&rsquo;s time. (Tense has changed)</p>",
                    solution_hi: "<p>83.(b) The report of the sub-committee is being presented in a week&rsquo;s time. (Correct)<br>(a) The report of the sub-committee <span style=\"text-decoration: underline;\">has been</span> presented in a week&rsquo;s time. (गलत tense (present perfect) का प्रयोग किया गया है। is being presented (present continuous) का प्रयोग होगा ।) <br>(c) The report of the sub-committee <span style=\"text-decoration: underline;\">is presenting</span> in a week&rsquo;s time. (गलत verb (is presenting) का प्रयोग किया गया | is being presented) का प्रयोग होगा ।)&nbsp;<br>(d) The report of the sub-committee <span style=\"text-decoration: underline;\">had been</span> presented in a week&rsquo;s time. (गलत tense (past perfect) का प्रयोग किया गया है।) is being presented (present continuous) का प्रयोग होगा ।)&nbsp;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Find a word that is the synonym of &lsquo;Robust&rdquo;</p>",
                    question_hi: "<p>84. Find a word that is the synonym of &lsquo;Robust&rdquo;</p>",
                    options_en: [
                        "<p>sturdy</p>",
                        "<p>small</p>",
                        "<p>round</p>",
                        "<p>weak</p>"
                    ],
                    options_hi: [
                        "<p>sturdy</p>",
                        "<p>small</p>",
                        "<p>round</p>",
                        "<p>weak</p>"
                    ],
                    solution_en: "<p>84.(a) <strong>Sturdy</strong>- strong and healthy. <br><strong>Small</strong>- not large in size, number, amount, etc<br><strong>Round</strong>- having the shape of a circle<br><strong>Weak</strong>- having little strength or energy or not strong</p>",
                    solution_hi: "<p>84.(a) <strong>Sturdy</strong>- मजबूत और स्वस्थ <br><strong>Small</strong>- आकार, संख्या, राशि आदि में छोटा<br><strong>Round </strong>- एक चक्र(circle) का आकार होना<br><strong>Weak</strong>- कम ताकत या ऊर्जा होना या मजबूत ना होना</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>He fell from a running bus and would have died if the passers-by did not get him admitted in the nearby hospital immediately.</p>",
                    question_hi: "<p>85. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct. <br>He fell from a running bus and would have died if the passers-by did not get him admitted in the nearby hospital immediately.</p>",
                    options_en: [
                        "<p>He fell from a running bus and</p>",
                        "<p>would have died if the passers-by</p>",
                        "<p>did not get him admitted in the nearby hospital immediately</p>",
                        "<p>No error</p>"
                    ],
                    options_hi: [
                        "<p>He fell from a running bus and</p>",
                        "<p>would have died if the passers-by</p>",
                        "<p>did not get him admitted in the nearby hospital immediately</p>",
                        "<p>No error</p>"
                    ],
                    solution_en: "<p>85.(c) did not get him admitted in the nearby hospital immediately.<br>We will use the past perfect tense (had + V<sub>3</sub>) in the latter part of the given sentence. Hence, &lsquo;had not got him admitted (V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>85.(c) did not get him admitted in the nearby hospital immediately.<br>हम दिए गए वाक्य के बाद के भाग में past perfect tense (had + V<sub>3</sub>) का उपयोग करेंगे। इसलिए, &lsquo;had not got him admitted (V<sub>3</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the option that is similar in meaning to the underlined word in the following sentence. <br>Chameleon usually <span style=\"text-decoration: underline;\"><strong>disguises</strong></span> itself to avoid predators</p>",
                    question_hi: "<p>86. Select the option that is similar in meaning to the underlined word in the following sentence. <br>Chameleon usually <span style=\"text-decoration: underline;\"><strong>disguises</strong></span> itself to avoid predators</p>",
                    options_en: [
                        "<p>transposes</p>",
                        "<p>changes</p>",
                        "<p>swaps</p>",
                        "<p>camouflages</p>"
                    ],
                    options_hi: [
                        "<p>transposes</p>",
                        "<p>changes</p>",
                        "<p>swaps</p>",
                        "<p>camouflages</p>"
                    ],
                    solution_en: "<p>86.(d) <strong>Camouflages </strong>- to blend in with surroundings or conceal.<br><strong>Disguises</strong>- to change appearance to hide identity or avoid detection.<br><strong>Transposes</strong>- to switch or interchange.<br><strong>Changes</strong>- to make something different.<br><strong>Swaps-</strong> to exchange one thing for another.</p>",
                    solution_hi: "<p>86.(d) <strong>Camouflages </strong>(छद्मवेश) - to blend in with surroundings or conceal.<br><strong>Disguises </strong>(भेष बदलना) - to change appearance to hide identity or avoid detection.<br><strong>Transposes </strong>(स्थानांतरित करना) - to switch or interchange.<br><strong>Changes </strong>(बदलना) - to make something different.<br><strong>Swaps </strong>(अदला-बदली) - to exchange one thing for another.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) When you see these signs, you know that winter is near.<br>(B) Birds begin to fly south.<br>(C )There are certain signs to show that winter is coming.<br>(D) People also want new warm coats.</p>",
                    question_hi: "<p>87. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) When you see these signs, you know that winter is near.<br>(B) Birds begin to fly south.<br>(C )There are certain signs to show that winter is coming.<br>(D) People also want new warm coats.</p>",
                    options_en: [
                        "<p>BDCA</p>",
                        "<p>ACBD</p>",
                        "<p>CBDA</p>",
                        "<p>DBCA</p>"
                    ],
                    options_hi: [
                        "<p>BDCA</p>",
                        "<p>ACBD</p>",
                        "<p>CBDA</p>",
                        "<p>DBCA</p>"
                    ],
                    solution_en: "<p>87.(c) CBDA<br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e. signs that winter is coming. Sentence B states the sign that birds begin to fly south . So, B will follow C . Further, Sentence D states another sign of winter and Sentence A concludes that when we see these signs we can conclude that winter is coming . So, A will follow D . Going through the options, <br>option (c) CBDA has the correct sequence.</p>",
                    solution_hi: "<p>87.(c) CBDA<br>Sentence C starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;signs that winter is coming&rsquo; शामिल है। Sentence B संकेत बताता है कि पक्षी दक्षिण की ओर उड़ना शुरू करते हैं। अतः C के बाद B आएगा। इसके अलावा, Sentence D सर्दी का एक और संकेत बताता है और Sentence A का निष्कर्ष है कि जब हम इन संकेतों को देखते हैं तो हम निष्कर्ष निकाल सकते हैं कि सर्दी आ रही है। अत: D के बाद A आएगा। options के माध्यम से जाने पर, option (c) CBDA का सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Find the one which is correctly/wrongly spelled word out of the four alternatives.</p>",
                    question_hi: "<p>88. Find the one which is correctly/wrongly spelled word out of the four alternatives.</p>",
                    options_en: [
                        "<p>Endeavour</p>",
                        "<p>Endaevour</p>",
                        "<p>Endevour</p>",
                        "<p>Endeavore</p>"
                    ],
                    options_hi: [
                        "<p>Endeavour</p>",
                        "<p>Endaevour</p>",
                        "<p>Endevour</p>",
                        "<p>Endeavore</p>"
                    ],
                    solution_en: "<p>88.(a) Endeavour</p>",
                    solution_hi: "<p>88.(a) Endeavour</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "89. Select the most appropriate meaning of idiom in the sentence.<br />Hold the key",
                    question_hi: "89. Select the most appropriate meaning of idiom in the sentence.<br />Hold the key",
                    options_en: [
                        "<p>To own a property</p>",
                        "<p>To keep a secret</p>",
                        "<p>To have control of something</p>",
                        "<p>To have the right of succession</p>"
                    ],
                    options_hi: [
                        "<p>To own a property</p>",
                        "<p>To keep a secret</p>",
                        "<p>To have control of something</p>",
                        "<p>To have the right of succession</p>"
                    ],
                    solution_en: "89.(c)  to have control of something.<br />Example .- The two main parties have won almost the same votes in the recent election, and now the minority group holds the key to the result. ",
                    solution_hi: "<p>89.(c) to have control of something./ किसी चीज पर नियंत्रण रखना।<br>Example - The two main parties have won almost the same votes in the recent election, and now the minority group holds the key to the result.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) For instance, genes from a bacteria have been introduced into cotton species, making it resistant to a major pest. <br>(B) Genetic modification may also be done to shorten growing time.<br>(C) New techniques allow scientists to import genes from one species into another.<br>(D) The other benefits of genetic modifications are an increased size and the shelf-life of crops.</p>",
                    question_hi: "<p>90. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) For instance, genes from a bacteria have been introduced into cotton species, making it resistant to a major pest. <br>(B) Genetic modification may also be done to shorten growing time.<br>(C) New techniques allow scientists to import genes from one species into another.<br>(D) The other benefits of genetic modifications are an increased size and the shelf-life of crops.</p>",
                    options_en: [
                        "<p>CDBA</p>",
                        "<p>DACB</p>",
                        "<p>CABD</p>",
                        "<p>DCBA</p>"
                    ],
                    options_hi: [
                        "<p>CDBA</p>",
                        "<p>DACB</p>",
                        "<p>CABD</p>",
                        "<p>DCBA</p>"
                    ],
                    solution_en: "<p>90.(c) CABD<br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e. New techniques allow scientists genetic modification. Sentence A states that genes from a bacteria has enabled making cotton resistant to a major pest . So, A will follow C. Further, Sentence B states that it may also be done to shorten growing time and Sentence D states the other benefit of genetic modification. So, D will follow B. Going through the options, option (c) CABD has the correct sequence.</p>",
                    solution_hi: "<p>90.(c) CABD<br>Sentence C starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;New techniques allow scientists genetic modification&rsquo; शामिल है। Sentence A कहता है कि एक बैक्टीरिया के जीन ने कपास को एक प्रमुख कीट के लिए प्रतिरोधी बनाने में सक्षम बनाया है। तो, C के बाद A आएगा। आगे, Sentence B कहता है कि यह बढ़ते समय को कम करने के लिए भी किया जा सकता है और Sentence D आनुवंशिक संशोधन के अन्य लाभों को बताता है। तो, B के बाद D आएगा। options के माध्यम से जाने पर, option (c) CABD में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the option that can be used as a one-word substitute for the given group of words.<br>The original inhabitants of a country</p>",
                    question_hi: "<p>91. Select the option that can be used as a one-word substitute for the given group of words.<br>The original inhabitants of a country</p>",
                    options_en: [
                        "<p>Aborigines</p>",
                        "<p>Primitive</p>",
                        "<p>Citizens</p>",
                        "<p>Aliens</p>"
                    ],
                    options_hi: [
                        "<p>Aborigines</p>",
                        "<p>Primitive</p>",
                        "<p>Citizens</p>",
                        "<p>Aliens</p>"
                    ],
                    solution_en: "<p>91.(a) <strong>Aborigines</strong>- the original inhabitants of a country.<br><strong>Primitive</strong>- very simple and not developed.<br><strong>Citizen</strong>- a person who legally belongs to a country and has the rights and protection of that country.<br><strong>Aliens</strong>- a creature from outer space.</p>",
                    solution_hi: "<p>91.(a) <strong>Aborigines</strong> (मूलनिवाशी) - the original inhabitants of a country.<br><strong>Primitive </strong>(प्राचीन) - very simple and not developed.<br><strong>Citizen </strong>(नागरिक) - a person who legally belongs to a country and has the rights and protection of that country.<br><strong>Aliens </strong>(अजनबी) - a creature from outer space.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Switch off the television.</p>",
                    question_hi: "<p>92. Switch off the television.</p>",
                    options_en: [
                        "<p>Can you switch off the television?</p>",
                        "<p>May I switch off the television?</p>",
                        "<p>Let the television be switched off.</p>",
                        "<p>Let the television being switch off.</p>"
                    ],
                    options_hi: [
                        "<p>Can you switch off the television?</p>",
                        "<p>May I switch off the television?</p>",
                        "<p>Let the television be switched off.</p>",
                        "<p>Let the television being switch off.</p>"
                    ],
                    solution_en: "<p>92.(c) Let the television be switched off. (Correct)<br>(a) <span style=\"text-decoration: underline;\">Can you switch</span> off the television? (Incorrect Meaning and Structure)<br>(b) <span style=\"text-decoration: underline;\">May I switch</span> off the television? (Incorrect Meaning and Structure)<br>(d) Let the television <span style=\"text-decoration: underline;\">being switch</span> off. (Incorrect Tense and Verb)</p>",
                    solution_hi: "<p>92.(c) Let the television be switched off. (Correct)<br>(a) <span style=\"text-decoration: underline;\">Can you switch</span> off the television ? (Sentence Structure सही नहीं है)<br>(b) <span style=\"text-decoration: underline;\">May I switch</span> off the television? (Sentence Structure सही नहीं है)<br>(d) Let the television <span style=\"text-decoration: underline;\">being switc</span>h off. (गलत verb (<span style=\"text-decoration: underline;\">being switch</span>) का प्रयोग किया गया है | (be switched) का प्रयोग होगा ।)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate option to fill in the blanks. <br>(&Oslash; = No article).<br>_____ road to Taj Mahal passes through _____forest.</p>",
                    question_hi: "<p>93. Select the most appropriate option to fill in the blanks. <br>(&Oslash; = No article).<br>_____ road to Taj Mahal passes through _____forest.</p>",
                    options_en: [
                        "<p>A; a</p>",
                        "<p>A; the</p>",
                        "<p>&Oslash;; &Oslash;</p>",
                        "<p>The; a</p>"
                    ],
                    options_hi: [
                        "<p>A; a</p>",
                        "<p>A; the</p>",
                        "<p>&Oslash;; &Oslash;</p>",
                        "<p>The; a</p>"
                    ],
                    solution_en: "<p>93.(d) The, a<br>Definite article &lsquo;the&rsquo; will be used before &lsquo;road&rsquo; because it specifies a particular road leading to the Taj Mahal. Article &lsquo;a&rsquo; will be used before &lsquo;forest&rsquo; as the forest is not specific or particular. Hence, &lsquo;The; a&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>93.(d) The, a<br>&lsquo;Road&rsquo; से पहले definite article &lsquo;the&rsquo; का प्रयोग किया जाएगा क्योंकि यह Taj Mahal तक जाने वाली एक particular road को specify करता है। &lsquo;Forest&rsquo; से पहले article &lsquo;a&rsquo; का प्रयोग किया जाएगा क्योंकि यह किसी specific forest की बात नहीं कर रहा है। अतः, &lsquo;The; a&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Pick a word opposite in meaning to the bold word.<br>They <strong>bestow </strong>upon him whatever he wishes</p>",
                    question_hi: "<p>94. Pick a word opposite in meaning to the bold word.<br>They <strong>bestow </strong>upon him whatever he wishes</p>",
                    options_en: [
                        "<p>intensify</p>",
                        "<p>gift</p>",
                        "<p>deny</p>",
                        "<p>classify</p>"
                    ],
                    options_hi: [
                        "<p>intensify</p>",
                        "<p>gift</p>",
                        "<p>deny</p>",
                        "<p>classify</p>"
                    ],
                    solution_en: "<p>94.(c) <strong>Deny </strong>- to refuse to allow somebody to have something. <br><strong>Bestow </strong>- to give something to somebody, especially to show how much he/she is respected<br><strong>Intensify </strong>- to become or to make something greater or stronger<br><strong>Gift </strong>- something that you give to somebody<br><strong>Classify </strong>- to put somebody/something into a group with other people or things of a similar type</p>",
                    solution_hi: "<p>94.(c) <strong>Deny </strong>- किसी को कुछ लेने की अनुमति देने से इंकार करना <br><strong>Bestow </strong>- किसी को कुछ प्रदान करना, विशेष रूप से यह दिखाने के लिए कि उसका कितना सम्मान है<br><strong>Intensify </strong>- कुछ बड़ा या मजबूत बनना या बनाना<br><strong>Gift </strong>- कुछ ऐसा जो आप किसी को देते हैं<br><strong>Classify </strong>- किसी को/कुछ को समान लोगों या समान प्रकार की चीजों के एक समूह में रखना</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate homophones to fill in the blanks. <br>It was against her ______to earn more ______ than that decided by the board of directors.</p>",
                    question_hi: "<p>95. Select the most appropriate homophones to fill in the blanks. <br>It was against her ______ to earn more ______ than that decided by the board of directors.</p>",
                    options_en: [
                        "<p>principle; profit</p>",
                        "<p>principal; prophet</p>",
                        "<p>principal; profit</p>",
                        "<p>principle; prophet</p>"
                    ],
                    options_hi: [
                        "<p>principle; profit</p>",
                        "<p>principal; prophet</p>",
                        "<p>principal; profit</p>",
                        "<p>principle; prophet</p>"
                    ],
                    solution_en: "95.(a) principle; profit <br />‘Principle’ means a moral rule of good behaviour. ‘Profit’ is a financial gain. The given sentence states that it was against her principle to earn more profit than that decided by the board of directors. Hence, ‘principle; profit’ is the most appropriate answer.",
                    solution_hi: "95.(a) principle; profit <br />‘Principle’ का अर्थ है अच्छे व्यवहार का एक नैतिक नियम (moral rule)। ‘Profit’ एक वित्तीय लाभ (financial gain) है। दिए गए sentence में कहा गया है कि निदेशक मंडल (board of directors) द्वारा तय किए गए लाभ (profit) से अधिक लाभ कमाना उसके सिद्धांत (principle) के विरुद्ध था। इसलिए, ‘principle; profit’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :-</strong><br>Why do people yawn? No one seems to know the exact (96)______. Some people believe that we yawn (97)_____we do not have enough oxygen in our bodies. In (98)_____words, we yawn to breathe in (99)______oxygen. People also believe that yawning is associated (100)______boredom.<br>Select the correct option to fill blank 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test :-</strong><br>Why do people yawn? No one seems to know the exact (96)______. Some people believe that we yawn (97)_____we do not have enough oxygen in our bodies. In (98)_____words, we yawn to breathe in (99)______oxygen. People also believe that yawning is associated (100)______boredom.<br>Select the correct option to fill blank 96.</p>",
                    options_en: [
                        "<p>reason</p>",
                        "<p>basis</p>",
                        "<p>explanation</p>",
                        "<p>reality</p>"
                    ],
                    options_hi: [
                        "<p>reason</p>",
                        "<p>basis</p>",
                        "<p>explanation</p>",
                        "<p>reality</p>"
                    ],
                    solution_en: "<p>96.(a) reason<br>&lsquo;Reason&rsquo; means a cause, explanation, or justification for an action or event. The given passage states that no one seems to know the exact reason. Hence, &lsquo;reason&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(a) reason<br>&lsquo;Reason&rsquo; का अर्थ है किसी action या event के लिए कारण, स्पष्टीकरण या औचित्य। दिए गए passage में कहा गया है कि कोई भी व्यक्ति exact reason नहीं जानता है। इसलिए, &lsquo;reason&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :-</strong><br>Why do people yawn? No one seems to know the exact (96)______. Some people believe that we yawn (97)_____we do not have enough oxygen in our bodies. In (98)_____words, we yawn to breathe in (99)______oxygen. People also believe that yawning is associated (100)______boredom.<br>Select the correct option to fill blank 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test :-</strong><br>Why do people yawn? No one seems to know the exact (96)______. Some people believe that we yawn (97)_____we do not have enough oxygen in our bodies. In (98)_____words, we yawn to breathe in (99)______oxygen. People also believe that yawning is associated (100)______boredom.<br>Select the correct option to fill blank 97.</p>",
                    options_en: [
                        "<p>why</p>",
                        "<p>while</p>",
                        "<p>when</p>",
                        "<p>as</p>"
                    ],
                    options_hi: [
                        "<p>why</p>",
                        "<p>while</p>",
                        "<p>when</p>",
                        "<p>as</p>"
                    ],
                    solution_en: "<p>97.(c) when<br>&lsquo;When&rsquo; is used to indicate the time of an action or event. The given passage states that some people believe that we yawn when we do not have enough oxygen in our bodies. Hence, &lsquo;when&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(c) when<br>&lsquo;When&rsquo; का प्रयोग किसी action या event के समय को indicate करने के लिए किया जाता है। दिए गए passage में कहा गया है कि some people का मानना ​​है कि जब हमारे bodies में पर्याप्त oxygen नहीं होती है तो हम जम्हाई (yawn) लेते हैं। इसलिए, &lsquo;When&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :-</strong><br>Why do people yawn? No one seems to know the exact (96)______. Some people believe that we yawn (97)_____we do not have enough oxygen in our bodies. In (98)_____words, we yawn to breathe in (99)______oxygen. People also believe that yawning is associated (100)______boredom.<br>Select the correct option to fill blank 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test :-</strong><br>Why do people yawn? No one seems to know the exact (96)______. Some people believe that we yawn (97)_____we do not have enough oxygen in our bodies. In (98)_____words, we yawn to breathe in (99)______oxygen. People also believe that yawning is associated (100)______boredom.<br>Select the correct option to fill blank 98.</p>",
                    options_en: [
                        "<p>new</p>",
                        "<p>other</p>",
                        "<p>rather</p>",
                        "<p>some</p>"
                    ],
                    options_hi: [
                        "<p>new</p>",
                        "<p>other</p>",
                        "<p>rather</p>",
                        "<p>some</p>"
                    ],
                    solution_en: "<p>98.(b) other<br>&lsquo;In other words&rsquo; is the correct phrase used to tell the same meaning in different words. Hence, &lsquo;other&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(b) other<br>&lsquo;In other words&rsquo; एक ही अर्थ को अलग-अलग शब्दों में बताने के लिए प्रयोग किया जाने वाला सही phrase है। इसलिए, &lsquo;other&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :-</strong><br>Why do people yawn? No one seems to know the exact (96)______. Some people believe that we yawn (97)_____we do not have enough oxygen in our bodies. In (98)_____words, we yawn to breathe in (99)______oxygen. People also believe that yawning is associated (100)______boredom.<br>Select the correct option to fill blank 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test :-</strong><br>Why do people yawn? No one seems to know the exact (96)______. Some people believe that we yawn (97)_____we do not have enough oxygen in our bodies. In (98)_____words, we yawn to breathe in (99)______oxygen. People also believe that yawning is associated (100)______boredom.<br>Select the correct option to fill blank 99.</p>",
                    options_en: [
                        "<p>less</p>",
                        "<p>more</p>",
                        "<p>little</p>",
                        "<p>much</p>"
                    ],
                    options_hi: [
                        "<p>less</p>",
                        "<p>more</p>",
                        "<p>little</p>",
                        "<p>much</p>"
                    ],
                    solution_en: "<p>99.(b) more <br>The given passage states that we yawn to breathe in more oxygen. Hence, &lsquo;more&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) more <br>दिए गए passage में कहा गया है कि हम अधिक oxygen लेने के लिए जम्हाई (yawn) लेते हैं। इसलिए, &lsquo;more&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :-</strong><br>Why do people yawn? No one seems to know the exact (96)______. Some people believe that we yawn (97)_____we do not have enough oxygen in our bodies. In (98)_____words, we yawn to breathe in (99)______oxygen. People also believe that yawning is associated (100)______boredom.<br>Select the correct option to fill blank 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test :-</strong><br>Why do people yawn? No one seems to know the exact (96)______. Some people believe that we yawn (97)_____we do not have enough oxygen in our bodies. In (98)_____words, we yawn to breathe in (99)______oxygen. People also believe that yawning is associated (100)______boredom.<br>Select the correct option to fill blank 100.</p>",
                    options_en: [
                        "<p>in</p>",
                        "<p>with</p>",
                        "<p>by</p>",
                        "<p>on</p>"
                    ],
                    options_hi: [
                        "<p>in</p>",
                        "<p>with</p>",
                        "<p>by</p>",
                        "<p>on</p>"
                    ],
                    solution_en: "<p>100.(b) with<br>The phrase \"associated with\" is used to describe a connection or relationship between two or more things. Hence, &lsquo;with&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) with<br>Phrase \"associated with\" का प्रयोग दो या दो से अधिक things के बीच connection या relationship का वर्णन करने के लिए किया जाता है। इसलिए, &lsquo;with&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>