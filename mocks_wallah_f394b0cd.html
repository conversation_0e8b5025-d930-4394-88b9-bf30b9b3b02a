<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 40</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">40</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 38
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 39,
                end: 39
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. The cause of Hepatitis A is a</p>",
                    question_hi: "<p>1. हेपेटाइटिस A का कारण क्या है ?</p>",
                    options_en: ["<p>virus</p>", "<p>bacteria</p>", 
                                "<p>protozoa</p>", "<p>mosquito bite</p>"],
                    options_hi: ["<p>विषाणु</p>", "<p>जीवाणु</p>",
                                "<p>प्रोटोजोआ</p>", "<p>मच्छर काटने से</p>"],
                    solution_en: "<p>1.(a) Hepatitis A is an inflammation of the liver caused by the hepatitis A virus (HAV). Hepatitis A is an inflammation of the liver caused by Hepatitis A virus. The virus is primarily spread when an uninfected (and unvaccinated) person ingests food or water that is contaminated with the faeces of an infected person.</p>",
                    solution_hi: "<p>1.(a) हेपेटाइटिस A रोग में, हेपेटाइटिस A वायरस (HIV) के कारण लीवर में सूजन आ जाती है। वायरस मुख्य रूप से तब फैलता है जब एक असंक्रमित (और बिना टीकाकरण वाला) व्यक्ति संक्रमित व्यक्ति के मल से दूषित भोजन या पानी का सेवन करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which organ in the human body produces bile juice ?</p>",
                    question_hi: "<p>2. मानव शरीर में कौन सा अंग पित्त रस का उत्पादन करता है ?</p>",
                    options_en: ["<p>Small intestine</p>", "<p>Pancreas</p>", 
                                "<p>Liver</p>", "<p>Stomach</p>"],
                    options_hi: ["<p>छोटी आंत</p>", "<p>अग्न्याशय</p>",
                                "<p>यकृत</p>", "<p>आमाशय</p>"],
                    solution_en: "<p>2.(c) Bile juice is produced by the liver in the human body. Bile juice is a fluid that is made and released by the liver and stored in the gallbladder. It helps in digestion and breaks down fats into fatty acids, which can be taken into the body by the digestive tract.</p>",
                    solution_hi: "<p>2.(c) पित्त रस मानव शरीर में यकृत द्वारा निर्मित होता है। पित्त रस एक तरल पदार्थ है जो यकृत द्वारा निर्मित और छोड़ा जाता है और पित्ताशय में जमा हो जाता है। यह पाचन में मदद करता है और वसा को फैटी एसिड में तोड़ देता है, जिसे पाचन तंत्र द्वारा शरीर में ले जाया जा सकता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. How many non-permanent members does the UN Council have ?</p>",
                    question_hi: "<p>3. संयुक्त राष्ट्र परिषद के कितने अस्थायी सदस्य हैं ?</p>",
                    options_en: ["<p>10</p>", "<p>14</p>", 
                                "<p>15</p>", "<p>12</p>"],
                    options_hi: ["<p>10</p>", "<p>14</p>",
                                "<p>15</p>", "<p>12</p>"],
                    solution_en: "<p>3.(a) There are 10 non-permanent members and 5 permanent members in the UN Council.<br>Five permanent members are China, France, Russia, the United Kingdom, and the USA. The Security Council consists of ten elected members for two-year terms elected by the General Assembly and five permanent members.</p>",
                    solution_hi: "<p>3.(a) संयुक्त राष्ट्र परिषद में 10 अस्थायी सदस्य और 5 स्थायी सदस्य हैं। पांच स्थायी सदस्य में चीन, फ्रांस, रूस, यूनाइटेड किंगडम और यूएसए हैं। सुरक्षा परिषद में महासभा द्वारा चुने गए दो साल के कार्यकाल के लिए दस निर्वाचित सदस्य और पांच स्थायी सदस्य होते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. When did the RTI Act come into effect ?</p>",
                    question_hi: "<p>4. RTI अधिनियम कब लागू हुआ ?</p>",
                    options_en: ["<p>September 2005</p>", "<p>December 2005</p>", 
                                "<p>November 2006</p>", "<p>October 2005</p>"],
                    options_hi: ["<p>सितंबर 2005</p>", "<p>दिसंबर 2005</p>",
                                "<p>नवंबर 2006</p>", "<p>अक्टूबर 2005</p>"],
                    solution_en: "<p>4.(d) The RTI (Right to Information) Bill was passed by Parliament of India on 15 June 2005 and came into force with effect from 12 October 2005. The basic objective of the RTI Act is to empower the citizens, promote transparency and accountability in the working of the government, contain corruption and make our democracy work for the people in a real sense.</p>",
                    solution_hi: "<p>4.(d) RTI (सूचना का अधिकार) विधेयक 15 जून 2005 को भारत की संसद द्वारा पारित किया गया था और 12 अक्टूबर 2005 से प्रभावी हुआ। RTI अधिनियम का मूल उद्देश्य नागरिकों को सशक्त बनाना, पारदर्शिता और जवाबदेही को बढ़ावा देना है। सरकार का काम करना, भ्रष्टाचार को रोकना और हमारे लोकतंत्र को वास्तविक अर्थों में लोगों के लिए काम करना है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Where was the ﬁrst nuclear power plant set up in India ?</p>",
                    question_hi: "<p>5. भारत में पहला परमाणु ऊर्जा संयंत्र कहाँ स्थापित किया गया था ?</p>",
                    options_en: ["<p>Kakrapar</p>", "<p>Kaiga</p>", 
                                "<p>Tarapur</p>", "<p>Kalpakkam</p>"],
                    options_hi: ["<p>काकरापार</p>", "<p>कैगा</p>",
                                "<p>तारापुर</p>", "<p>कलपक्कम</p>"],
                    solution_en: "<p>5.(c) The Tarapur Atomic Power Station (TAPS) is located near Boisar in the Thane District of Maharashtra. On 8 May 1964, a contract between the Government of India and the United States began as India\'s first atomic power project. <br>Other power plants, Kakrapar- Gujarat, Kaiga- Karnataka, Kalpakkam- Chennai, Tamil Nadu.</p>",
                    solution_hi: "<p>5.(c) तारापुर परमाणु ऊर्जा स्टेशन (TAPS) महाराष्ट्र के ठाणे जिले में बोईसर के पास स्थित है। यह 8 मई 1964 को, भारत सरकार और संयुक्त राज्य अमेरिका के बीच एक अनुबंध में भारत की पहली परमाणु ऊर्जा परियोजना के रूप में शुरू हुआ। <br>अन्य ऊर्जा संयंत्र, काकरापार- गुजरात, कैगा- कर्नाटक, कलपक्कम- चेन्नई, तमिलनाडु।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Who launched the Sukanya Samridhi Yojana ?",
                    question_hi: "6. सुकन्या समृद्धि योजना किसने शुरू की ?",
                    options_en: [" ID Deve Gowda", " Atal Bihari Vajpayee", 
                                " Manmohan Singh", " Narendra Modi"],
                    options_hi: [" आईडी देवेगौड़ा", " बिहारी वाजपेयी",
                                " मनमोहन सिंह", " नरेंद्र मोदी"],
                    solution_en: "6.(d) Sukanya Samridhi Yojana was launched by Prime Minister Narendra Modi on 22 January 2015 as a part of the Beti Bachao, Beti Padhao campaign.The scheme encourages parents to build a fund for the future education and marriage expenses for their female child.",
                    solution_hi: "6.(d) सुकन्या समृद्धि योजना 22 जनवरी 2015 को बेटी बचाओ, बेटी पढाओ अभियान के एक भाग के रूप में प्रधान मंत्री नरेंद्र मोदी द्वारा शुरू की गई थी। यह योजना माता-पिता को उनकी बेटी के लिए भविष्य की शिक्षा और शादी के खर्च के लिए एक फंड बनाने के लिए प्रोत्साहित करती है।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. When did Akbar become the emperor ?",
                    question_hi: "7. अकबर सम्राट कब बना था ?",
                    options_en: [" 1560 AD", " 1550 AD", 
                                " 1552 AD", " 1556 AD"],
                    options_hi: [" 1560 AD", " 1550 AD",
                                " 1552 AD", " 1556 AD"],
                    solution_en: "7.(d) Akbar became the emperor in 1556 AD.",
                    solution_hi: "7.(d) 1556 ई. में अकबर बादशाह बना।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "8. When was the revolt of 1857 ﬁnally suppressed by the British ?",
                    question_hi: "8. 1857 के विद्रोह को अंततः अंग्रेजों ने कब दबा दिया था ? ",
                    options_en: [" 1861", " 1859", 
                                " 1860", " 1857"],
                    options_hi: [" 1861", " 1859",
                                " 1860", " 1857"],
                    solution_en: "8.(b) The revolt of 1857 lasted for more than a year. The rebellion began on 10 May 1857 in the form of a mutiny of sepoys of the Company\'s army in the garrison town of Meerut. It was suppressed by 1859, British rule was once again established in India, peace was finally proclaimed by Lord Canning.",
                    solution_hi: "8.(b) 1857 का विद्रोह एक वर्ष से अधिक समय तक चला। विद्रोह 10 मई 1857 को मेरठ के गैरीसन शहर में कंपनी की सेना के सिपाहियों के विद्रोह के रूप में शुरू हुआ। इसे 1859 तक दबा दिया गया था, भारत में एक बार फिर से ब्रिटिश शासन की स्थापना हुई थी, अंततः लॉर्ड कैनिंग द्वारा शांति की घोषणा की गई थी।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which branch of physics deals with properties of ﬂuids at rest ?</p>",
                    question_hi: "<p>9. भौतिकी की कौन सी शाखा विराम अवस्था में द्रवों के गुणों से संबंधित है ?</p>",
                    options_en: ["<p>Optics</p>", "<p>Thermodynamics</p>", 
                                "<p>Hydrostatics</p>", "<p>Astrophysics</p>"],
                    options_hi: ["<p>प्रकाशिकी</p>", "<p>ऊष्मप्रवैगिकी</p>",
                                "<p>हाइड्रोस्टैटिक्स</p>", "<p>खगोल भौतिकी</p>"],
                    solution_en: "<p>9.(c) Hydrostatics is the branch of physics that deals with the characteristics of fluids at rest, particularly with the pressure in a fluid or exerted by a fluid (gas or liquid) on an immersed body. <br>Optics is the branch of physics that studies the behavior and properties of light. Thermodynamics is the branch of physics that deals with the relationships between heat and other forms of energy. <br>Astrophysics is a science that employs the methods and principles of physics in the study of astronomical objects and phenomena.</p>",
                    solution_hi: "<p>9.(c) हाइड्रोस्टैटिक्स भौतिकी की वह शाखा है जो की स्थिर तरल पदार्थ की विशेषताओं से संबंधित है, विशेष रूप से एक तरल पदार्थ में दबाव के साथ या एक डूबी हुई वस्तु पर तरल पदार्थ (गैस या तरल) द्वारा लगाया जाता है। <br>प्रकाशिकी भौतिकी की वह शाखा है जो प्रकाश के व्यवहार और गुणों का अध्ययन करती है। <br>ऊष्मप्रवैगिकी भौतिकी की वह शाखा है जो ऊष्मा और ऊर्जा के अन्य रूपों के बीच संबंधों से संबंधित है। <br>खगोल भौतिकी एक ऐसा विज्ञान है जो खगोलीय पिंडों के अध्ययन में भौतिकी के तरीकों और सिद्धांतों को नियोजित करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Programming language Java was developed by______.</p>",
                    question_hi: "<p>10. प्रोग्रामिंग भाषा जावा को _____द्वारा विकसित किया गया था।</p>",
                    options_en: ["<p>Paul Allen</p>", "<p>Jaap Haartsen</p>", 
                                "<p>James Gosling</p>", "<p>Charles Simonyi</p>"],
                    options_hi: ["<p>पॉल एलन</p>", "<p>जाप हार्टसेन</p>",
                                "<p>जेम्स गोस्लिंग</p>", "<p>चार्ल्स सिमोनी</p>"],
                    solution_en: "<p>10.(c) The programming language Java was developed by James Gosling in 1995. Java is a high-level, class-based, object-oriented programming language and it is a general-purpose programming language intended to let programmers write once, run anywhere.</p>",
                    solution_hi: "<p>10.(c) प्रोग्रामिंग भाषा जावा को 1995 में जेम्स गोस्लिंग द्वारा विकसित किया गया था। जावा एक उच्च-स्तरीय, वर्ग-आधारित, वस्तु-उन्मुख प्रोग्रामिंग भाषा है और यह एक सामान्य-उद्देश्य वाली प्रोग्रामिंग भाषा है जिसका उद्देश्य प्रोग्रामर को एक बार लिखने और कहीं भी चलाने देता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "11. When did the Simon Commission arrive in India ?",
                    question_hi: "11. साइमन कमीशन भारत में कब आया ?",
                    options_en: [" 1927", " 1930", 
                                " 1928", " 1931"],
                    options_hi: [" 1927", " 1930",
                                " 1928", " 1931"],
                    solution_en: "11.(c) The Indian Statutory Commission, also known as Simon Commission arrived in British India in 1928, was a group of seven Members of Parliament under the chairmanship of Sir John Simon. The commission arrived in British India in 1928 to study constitutional reform in Britain\'s largest and most important possession.",
                    solution_hi: "11.(c) भारतीय सांविधिक आयोग को साइमन कमीशन के रूप में भी जाना जाता है जो 1928 में ब्रिटिश भारत में आया था। यह सर जॉन साइमन की अध्यक्षता में संसद के सात सदस्यों का एक समूह था और 1928 में ब्रिटेन भारत के सबसे बड़े और महत्वपूर्ण  संवैधानिक सुधार का अध्ययन करने के लिए ब्रिटिश भारत आया था। ",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "12. In which year were the Women Transforming India Awards started by NITI Aayog ?",
                    question_hi: "12. नीति आयोग द्वारा ‘वुमन ट्रांसफॉर्मिंग इंडिया अवार्ड’ किस वर्ष शुरू किए गए थे ?",
                    options_en: [" 2014", " 2017", 
                                " 2015", " 2016"],
                    options_hi: [" 2014", " 2017",
                                " 2015", " 2016"],
                    solution_en: "12.(d) WTI awards were launched by NITI Aayog, in collaboration with the United Nations, in India in 2016, to honor the women entrepreneurs, who are breaking the glass ceiling and challenging stereotypes, through businesses, enterprises and innovative initiatives.",
                    solution_hi: "12.(d) WTI पुरस्कार नीति आयोग और संयुक्त राष्ट्र के सहयोग से भारत में 2016 में शुरू किए गए, ताकि उन महिला उद्यमियों को सम्मानित किया जा सके, जो व्यवसायों, उद्यमों और नवीन पहलों के माध्यम से पुरानी सीमाओं को तोड़ रही हैं और रूढ़िवादी सोच को चुनौती दे रही हैं।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. When was the Hindustan Republican Association formed ?</p>",
                    question_hi: "<p>13. हिंदुस्तान रिपब्लिकन एसोसिएशन की स्थापना कब हुई थी ?</p>",
                    options_en: ["<p>1920</p>", "<p>1924</p>", 
                                "<p>1922</p>", "<p>1926</p>"],
                    options_hi: ["<p>1920</p>", "<p>1924</p>",
                                "<p>1922</p>", "<p>1926</p>"],
                    solution_en: "<p>13.(b) Hindustan Republican Association was a revolutionary organization of India established in 1924 in East Bengal by Sachindra Nath Sanyal, Narendra Mohan Sen and Pratul Ganguly as an offshoot of Anushilan Samiti.<br>Members: Bhagat Singh, Chandra Shekhar Azad, Sukhdev, Ram Prasad Bismil, Roshan Singh, Ashfaqulla Khan, Rajendra Lahiri.</p>",
                    solution_hi: "<p>13.(b) हिंदुस्तान रिपब्लिकन एसोसिएशन, 1924 में पूर्वी बंगाल में सचिंद्र नाथ सान्याल, नरेंद्र मोहन सेन और प्रतुल गांगुली द्वारा अनुशीलन समिति की एक शाखा के रूप में स्थापित भारत का एक क्रांतिकारी संगठन था।<br>सदस्य - भगत सिंह, चंद्र शेखर आज़ाद, सुखदेव, राम प्रसाद बिस्मिल, रोशन सिंह, अशफाकउल्ला खान, राजेंद्र लाहिड़ी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. The ﬁrst national ﬂag of India is said to have been hoisted at _____ in 1906.</p>",
                    question_hi: "<p>14. भारत का पहला राष्ट्रीय ध्वज 1906 में कहाँ पर फहराया गया था ?</p>",
                    options_en: ["<p>Kolkata</p>", "<p>Patna</p>", 
                                "<p>Ahmedabad</p>", "<p>New Delhi</p>"],
                    options_hi: ["<p>कोलकाता</p>", "<p>पटना</p>",
                                "<p>अहमदाबाद</p>", "<p>नई दिल्ली</p>"],
                    solution_en: "<p>14.(a) The first national flag in India is said to have been hoisted on August 7, 1906, in the Parsee Bagan Square in Calcutta, which is now known as Kolkata. The flag had three horizontal stripes of red, yellow, and green. Vande Mataram in the Hindi language was written on a yellow strip.</p>",
                    solution_hi: "<p>14.(a) भारत में पहला राष्ट्रीय ध्वज 7 अगस्त, 1906 को कलकत्ता के पारसी बागान स्क्वायर में फहराया गया था, जिसे अब कोलकाता के नाम से जाना जाता है। झंडे में लाल, पीले और हरे रंग की तीन क्षैतिज धारियां थीं। पीले रंग की पट्टी पर हिंदी भाषा में वंदे मातरम लिखा गया था।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "15. How many environmental activists got the Goldman Environmental Prize 2019 ?",
                    question_hi: "15. कितने पर्यावरण कार्यकर्ताओं को गोल्डमैन पर्यावरण पुरस्कार 2019 मिला ?",
                    options_en: [" 4", " 6", 
                                " 5", " 3"],
                    options_hi: [" 4", " 6",
                                " 5", " 3"],
                    solution_en: "15.(b) Six grassroots environmental activists received the prestigious Goldman Environmental Prize. It is a prize awarded annually to grassroots environmental activists, one from each of the world\'s six geographic regions.",
                    solution_hi: "15.(b) छह जमीनी स्तर के पर्यावरण कार्यकर्ताओं को प्रतिष्ठित गोल्डमैन पर्यावरण पुरस्कार मिला। यह पुरस्कार दुनिया के छह भौगोलिक क्षेत्रों में से प्रत्येक के जमीनी स्तर के पर्यावरण कार्यकर्ताओं को प्रतिवर्ष दिया जाने वाला पुरस्कार है।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. As per Nov 2020, how many countries have membership in the World Trade Organisation ?</p>",
                    question_hi: "<p>16. नवंबर 2020 के अनुसार विश्व व्यापार संगठन में कितने देशों की सदस्यता है ?</p>",
                    options_en: ["<p>164</p>", "<p>165</p>", 
                                "<p>160</p>", "<p>168</p>"],
                    options_hi: ["<p>164</p>", "<p>165</p>",
                                "<p>160</p>", "<p>168</p>"],
                    solution_en: "<p>16.(a) The WTO (World Trade Organization) has 164 members and 25 observer governments. <br>Membership: 164 member states, <br>Director-General-Ngozi Okonjo-Iweala. <br>Headquarters- Geneva, Switzerland.</p>",
                    solution_hi: "<p>16.(a) WTO (विश्व व्यापार संगठन) में 164 सदस्य और 25 पर्यवेक्षक सरकारें हैं। <br>सदस्यता: 164 सदस्य राज्य, <br>महानिदेशक-नोगोज़ी ओकोन्जो-इवेला, <br>मुख्यालय- जिनेवा, स्विट्जरलैंड।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. The main focus of the First Five-Year Plan was on the ______.</p>",
                    question_hi: "<p>17. प्रथम पंचवर्षीय योजना का मुख्य फोकस किस पर था ?</p>",
                    options_en: ["<p>service sector</p>", "<p>agricultural sector</p>", 
                                "<p>agricultural and industrial sector</p>", "<p>industrial sector</p>"],
                    options_hi: ["<p>सेवा क्षेत्र</p>", "<p>कृषि क्षेत्र</p>",
                                "<p>कृषि और औद्योगिक क्षेत्र</p>", "<p>औद्योगिक क्षेत्र</p>"],
                    solution_en: "<p>17.(b) The First Five-Year Plan was introduced in 1951. It focused primarily on the development of the primary sector, specifically agriculture and irrigation. <br>The main focus of 2nd five-year plans- Industrial Development, <br>Third Five Year plan- Self Sufficiency in food, self-sufficiency in the economy, <br>Fourth- Self-reliance and sustained growth, <br>Fifth- Removable of poverty.</p>",
                    solution_hi: "<p>17.(b) पहली पंचवर्षीय योजना 1951 में शुरू की गई थी। यह प्राथमिक रूप से प्राथमिक क्षेत्र, विशेष रूप से कृषि और सिंचाई के विकास पर केंद्रित थी। <br>द्वितीय पंचवर्षीय योजनाओं का मुख्य फोकस- औद्योगिक विकास, <br>तीसरी पंचवर्षीय योजना- भोजन में आत्मनिर्भरता, अर्थव्यवस्था में आत्मनिर्भरता, <br>चौथी- आत्मनिर्भरता और सतत विकास, <br>पांचवीं- गरीबी दूर करने योग्य।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. The pH range of a human body is</p>",
                    question_hi: "<p>18. मानव शरीर का pH परास कितना है ?</p>",
                    options_en: ["<p>2.35 - 4.45</p>", "<p>8.35 - 9.45</p>", 
                                "<p>5.35 - 6.45</p>", "<p>7.35 - 7.45</p>"],
                    options_hi: ["<p>2.35 - 4.45</p>", "<p>8.35 - 9.45</p>",
                                "<p>5.35 - 6.45</p>", "<p>7.35 - 7.45</p>"],
                    solution_en: "<p>18.(d) The pH range of a human body is 7.35 to 7.45. Our body needs to maintain a healthy balance of acidity and alkalinity to work properly. Our body works within the pH range of 7.35 to 7.45. Therefore, we can say that the pH of our body is slightly alkaline.</p>",
                    solution_hi: "<p>18.(d) मानव शरीर की pH रेंज 7.35 से 7.45 तक होती है। हमारे शरीर को ठीक से काम करने के लिए अम्लता और क्षारीयता का स्वस्थ संतुलन बनाए रखने की आवश्यकता होती है। हमारा शरीर 7.35 से 7.45 के pH रेंज के भीतर काम करता है। इसलिए हम कह सकते हैं कि हमारे शरीर का pH थोड़ा क्षारीय होता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. The pistil in the ﬂower is ______.</p>",
                    question_hi: "<p>19. पुष्प स्त्रीकेसर क्या है ?</p>",
                    options_en: ["<p>a male reproductive part</p>", "<p>unisexual</p>", 
                                "<p>bisexual</p>", "<p>a female reproductive part</p>"],
                    options_hi: ["<p>एक पुरुष प्रजनन अंग</p>", "<p>एक लिंगीय</p>",
                                "<p>उभयलिंगी</p>", "<p>मादा प्रजनन अंग</p>"],
                    solution_en: "<p>19.(d) The ​pistil​ is the female reproductive part of a flower and the function of a pistil is to receive pollen and produce seeds. A stamen consists of an anther (which produces pollen, the male reproductive cell) and a filament. Pollination is the act of transferring pollen grains from the male anther of a flower to the female stigma.</p>",
                    solution_hi: "<p>19.(d) स्त्रीकेसर एक फूल का मादा प्रजनन अंग है और स्त्रीकेसर का कार्य पराग प्राप्त करना और बीज उत्पन्न करना है। पुंकेसर में एक परागकोश (जो पराग पैदा करता है, नर प्रजनन कोशिका) और एक रेशा होता है। परागण एक फूल के नर एंथर से परागकणों को मादा वर्तिकाग्र में स्थानांतरित करने की क्रिया है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. URL stands for</p>",
                    question_hi: "<p>20. URL का पूर्ण रूप क्या है ?</p>",
                    options_en: ["<p>Uniform Resource Locator</p>", "<p>Universal Remote Land</p>", 
                                "<p>Uniform Remote Locator</p>", "<p>Universal Resource Locator</p>"],
                    options_hi: ["<p>Uniform Resource Locator</p>", "<p>Universal Remote Land</p>",
                                "<p>Uniform Remote Locator</p>", "<p>Universal Resource Locator</p>"],
                    solution_en: "<p>20.(a) URL stands for Uniform Resource Locator. A URL has two main components,<br>1. Protocol identifier, (for the URL http://example.com, the protocol identifier is http) <br>2. Resource name: (for the URL http://example.com, the resource name is example.com).</p>",
                    solution_hi: "<p>20.(a) URL का अर्थ Uniform Resource Locator है। URL के दो मुख्य घटक होते हैं: <br>1. प्रोटोकॉल पहचानकर्ता: (URL http://example.com के लिए, प्रोटोकॉल पहचानकर्ता http है) <br>2. संसाधन का नाम: (URL http://example.com के लिए, संसाधन का नाम example.com है)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. In which state is the Gandhi Sagar Dam constructed ?</p>",
                    question_hi: "<p>21. गांधी सागर बांध किस राज्य में बनाया गया है ?</p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Madhya Pradesh</p>", 
                                "<p>Rajasthan</p>", "<p>Himachal Pradesh</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>मध्य प्रदेश</p>",
                                "<p>राजस्थान</p>", "<p>हिमाचल प्रदेश</p>"],
                    solution_en: "<p>21.(b) The Gandhi Sagar Dam is one of the four major dams built on India\'s Chambal river. The dam is located in the Mandsaur, districts of the state of Madhya Pradesh. It is a masonry gravity dam, standing 62.17 meters (204.0 ft) high, with a gross storage capacity of 7.322 billion cubic meters from a catchment area of 22,584 km<sup>2</sup> (8,720 sq mi).</p>",
                    solution_hi: "<p>21.(b) गांधी सागर बांध भारत की चंबल नदी पर बने चार प्रमुख बांधों में से एक है। यह बांध मध्य प्रदेश राज्य के मंदसौर जिले में स्थित है। यह एक चिनाई वाला गुरुत्वाकर्षण बांध है, जो 62.17 मीटर (204.0 फीट) ऊंचा है, जिसकी कुल भंडारण क्षमता 22,584 किमी<sup>2</sup> (8,720 वर्ग मील) के जलग्रहण क्षेत्र से 7.322 बिलियन क्यूबिक मीटर है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. _____ is the largest bauxite producing state of India.</p>",
                    question_hi: "<p>22. _____भारत का सबसे बड़ा बॉक्साइट उत्पादक राज्य है।</p>",
                    options_en: ["<p>Gujarat</p>", "<p>Jharkhand</p>", 
                                "<p>Andhra Pradesh</p>", "<p>Odisha</p>"],
                    options_hi: ["<p>गुजरात</p>", "<p>झारखंड</p>",
                                "<p>आंध्र प्रदेश</p>", "<p>उड़ीसा</p>"],
                    solution_en: "<p>22.(d) Odisha is the largest bauxite producing state of India. Odisha alone constitutes almost 50% of India&rsquo;s bauxite production. Odisha, Gujarat, Jharkhand, Maharashtra, Chhattisgarh, Tamil Nadu, and Madhya Pradesh are the main bauxite-producing states in India.</p>",
                    solution_hi: "<p>22.(d) ओडिशा भारत का सबसे बड़ा बॉक्साइट उत्पादक राज्य है। भारत के बॉक्साइट उत्पादन का लगभग 50% उत्पादन अकेले ओडिशा करता है। ओडिशा, गुजरात, झारखंड, महाराष्ट्र, छत्तीसगढ़, तमिलनाडु और मध्य प्रदेश भारत में मुख्य बॉक्साइट उत्पादक राज्य हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "23. In which form is data stored in a computer ?",
                    question_hi: "23. कंप्यूटर में डाटा को किस रूप में भंडारित किया जाता है ?",
                    options_en: [" Binary", " Magnetic", 
                                " Picture", " Alphabets"],
                    options_hi: [" बायनरी", " चुंबकीय",
                                " चित्र", " अक्षर"],
                    solution_en: "23.(a) Computers store data in binary form as it is the language that is understood by the computer. The processor converts the commands and information given by the user into binary data for the computer to execute them. Binary data is numerically represented by 0s and 1s.",
                    solution_hi: "23.(a) कंप्यूटर डेटा को बाइनरी रूप में संग्रहीत करता है क्योंकि यह कंप्यूटर द्वारा समझी जाने वाली भाषा है। प्रोसेसर उपयोगकर्ता द्वारा दिए गए आदेशों और सूचनाओं को कंप्यूटर के लिए उन्हें निष्पादित करने के लिए बाइनरी डेटा में परिवर्तित करता है। बाइनरी डेटा को संख्यात्मक रूप से 0s और 1s द्वारा दर्शाया जाता है।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. The ﬁrst high court of India was established in _______.</p>",
                    question_hi: "<p>24. भारत का पहला उच्च न्यायालय कहाँ स्थापित किया गया था ?</p>",
                    options_en: ["<p>Punjab</p>", "<p>Kolkata</p>", 
                                "<p>Mumbai</p>", "<p>Delhi</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>कोलकाता</p>",
                                "<p>मुंबई</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p>24.(b) The first high court in India, &lsquo;The High Court of Judicature at Fort William\', now called the High Court of Calcutta, was brought into existence by the Letters Patent dated 14 May 1862, issued under the Indian High Courts Act, 1861 and was formally opened on 1 July 1862.</p>",
                    solution_hi: "<p>24.(b) भारत में पहला उच्च न्यायालय, \'द हाई कोर्ट ऑफ ज्यूडिकेचर एट फोर्ट विलियम\', जिसे अब कलकत्ता का उच्च न्यायालय कहा जाता है, को 14 मई 1862 को जारी किए गए लेटर्स पेटेंट द्वारा अस्तित्व में लाया गया था। इसे भारतीय उच्च न्यायालय अधिनियम, 1861 और औपचारिक रूप से 1 जुलाई 1862 को खोला गया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. The ability of metals to be drawn into thin wires is called</p>",
                    question_hi: "<p>25. धातुओं के पतले तारों में खींचे जाने की क्षमता______कहलाती है।</p>",
                    options_en: ["<p>ductility</p>", "<p>solubility</p>", 
                                "<p>reactivity</p>", "<p>malleability</p>"],
                    options_hi: ["<p>तन्यता</p>", "<p>विलेयता</p>",
                                "<p>अपक्रांतिकता</p>", "<p>आघातवर्धनीयता</p>"],
                    solution_en: "<p>25.(a) Ductility is the ability of a material to be drawn or plastically deformed without fracture. It is therefore an indication of how soft or malleable the material is. The ductility of steel varies depending on the types and levels of alloying elements present. <br>Malleability describes the property of a metal\'s ability to be distorted below compression.</p>",
                    solution_hi: "<p>25.(a) नमनीयता किसी सामग्री को बिना फ्रैक्चर के खींचे जाने या प्लास्टिक रूप से विकृत करने की क्षमता है। इसलिए यह इस बात का संकेत है कि सामग्री कितनी नरम या लचीली है। स्टील की लचीलापन मौजूद मिश्र धातु तत्वों के प्रकार और स्तरों के आधार पर भिन्न होती है। <br>लचीलापन संपीड़न के नीचे विकृत होने की धातु की क्षमता की संपत्ति का वर्णन करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "26. In which year did India first participate in the Olympic games ?",
                    question_hi: "26. भारत ने पहली बार ओलंपिक खेलों में किस वर्ष भाग लिया था ?",
                    options_en: [" 1925", " 1923", 
                                " 1924", " 1900"],
                    options_hi: [" 1925", " 1923",
                                " 1924", " 1900"],
                    solution_en: "26.(d) India first participated at the Olympic Games in 1900, with athlete Norman Pritchard winning two medals, both silver – in athletics, and became the first asian nation to win an olympic medal.",
                    solution_hi: "26.(d) भारत ने पहली बार 1900 में ओलंपिक खेलों में भाग लिया, एथलीट नॉर्मन प्रिचर्ड ने दो पदक जीते, दोनों रजत - एथलेटिक्स में, और ओलंपिक पदक जीतने वाला पहला एशियाई राष्ट्र बन गया।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Raja Ravi Varma was a famous ______</p>",
                    question_hi: "<p>27. राजा रवि वर्मा एक प्रसिद्ध _____थे</p>",
                    options_en: ["<p>poet</p>", "<p>painter</p>", 
                                "<p>singer</p>", "<p>mathematician</p>"],
                    options_hi: ["<p>कवि</p>", "<p>चित्रकार</p>",
                                "<p>गायक</p>", "<p>गणितज्ञ</p>"],
                    solution_en: "<p>27.(b) Raja Ravi Varma (29 April 1848 &ndash; 2 October 1906) was an Indian painter and artist. Additionally, he was notable for making affordable lithographs of his paintings available to the public, which greatly enhanced his reach and influence as a painter and public figure.</p>",
                    solution_hi: "<p>27.(b) राजा रवि वर्मा (29 अप्रैल 1848 - 2 अक्टूबर 1906) एक भारतीय चित्रकार और कलाकार थे। इसके अतिरिक्त, वह अपने चित्रों की सस्ती लिथोग्राफ को जनता के लिए उपलब्ध कराने के लिए उल्लेखनीय थे, जिसने एक चित्रकार और सार्वजनिक व्यक्ति के रूप में उनकी पहुंच और प्रभाव को काफी बढ़ाया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "28. When was INSAT-1B commissioned ?",
                    question_hi: "28. इन्सैट-1B कब प्रक्षेपित किया गया था ?",
                    options_en: [" 1985", " 1983", 
                                " 1987", " 1980"],
                    options_hi: [" 1985", " 1983",
                                " 1987", " 1980"],
                    solution_en: "28.(b) INSAT-1B was commissioned in 1983. INSAT-1B was an Indian communications satellite that formed part of the Indian National Satellite System.",
                    solution_hi: "28.(b) INSAT-1B को 1983 में कमीशन किया गया था। INSAT-1B एक भारतीय संचार उपग्रह था जो भारतीय राष्ट्रीय उपग्रह प्रणाली का हिस्सा था।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. In which of the following does the river Godavari originate ?</p>",
                    question_hi: "<p>29. गोदावरी नदी निम्नलिखित में से किससे निकलती है ?</p>",
                    options_en: ["<p>Brahmagiri Hills</p>", "<p>Gangotri</p>", 
                                "<p>Hills of Coong</p>", "<p>Yamunotri</p>"],
                    options_hi: ["<p>ब्रह्मगिरी की पहाड़ियां</p>", "<p>गंगोत्री</p>",
                                "<p>कूंग की पहाड़ियाँ</p>", "<p>यमुनोत्री</p>"],
                    solution_en: "<p>29.(a) River Godavari originates from Brahmagiri Hills. The Godavari is India\'s second-longest river after the Ganga and third largest in India, drains about 10% of India\'s total geographical area. The Godavari River is known as \'Dakshin Ganga\'. It is the second-largest river in India and the largest river in southern India.</p>",
                    solution_hi: "<p>29.(a) गोदावरी नदी ब्रह्मगिरी पहाड़ियों से निकलती है। गोदावरी गंगा के बाद भारत की दूसरी सबसे लंबी नदी है और भारत में तीसरी सबसे बड़ी नदी है, जो भारत के कुल भौगोलिक क्षेत्र का लगभग 10% है। गोदावरी नदी को \'दक्षिण गंगा\' के नाम से जाना जाता है। यह भारत की दूसरी सबसे बड़ी नदी और दक्षिण भारत की सबसे बड़ी नदी है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which industry uses limestone as raw material ?</p>",
                    question_hi: "<p>30. कौन सा उद्योग चूना पत्थर को कच्चे माल के रूप में उपयोग करता है ?</p>",
                    options_en: ["<p>Cement</p>", "<p>Automobile</p>", 
                                "<p>Plastic</p>", "<p>Utensils</p>"],
                    options_hi: ["<p>सीमेंट</p>", "<p>ऑटोमोबाइल</p>",
                                "<p>प्लास्टिक</p>", "<p>बर्तन</p>"],
                    solution_en: "<p>30.(a) The cement industry uses limestone as raw material. Limestone is a sedimentary rock made of calcium carbonate(CaCO<sub>3</sub>). The four essential elements are calcium, silicon, aluminum and iron.</p>",
                    solution_hi: "<p>30.(a) सीमेंट उद्योग कच्चे माल के रूप में चूना पत्थर का उपयोग करता है। चूना पत्थर कैल्शियम कार्बोनेट (CaCO<sub>3</sub>) से बनी अवसादी चट्टान है। इसके चार आवश्यक तत्व कैल्शियम, सिलिकॉन, एल्युमिनियम और आयरन हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. How many world heritage sites have been protected by UNESCO as of June 2020 ?</p>",
                    question_hi: "<p>31. जून 2020 तक UNESCO द्वारा कितने विश्व धरोहर स्थलों को संरक्षित किया गया है ?</p>",
                    options_en: ["<p>1273</p>", "<p>1256</p>", 
                                "<p>1056</p>", "<p>1121</p>"],
                    options_hi: ["<p>1273</p>", "<p>1256</p>",
                                "<p>1056</p>", "<p>1121</p>"],
                    solution_en: "<p>31.(d) UNESCO- United Nations Educational Scientific and Cultural Organization.<br>There are 1121 world heritage sites that have been protected by UNESCO as of June 2020. The number of world heritage sites in India stands to be 40. Its headquarters are located in Paris, France.</p>",
                    solution_hi: "<p>31.(d) यूनेस्को- संयुक्त राष्ट्र शैक्षिक वैज्ञानिक और सांस्कृतिक संगठन। इसमें 1121 विश्व धरोहर स्थल हैं जिन्हें जून 2020 तक यूनेस्को द्वारा संरक्षित किया गया है। भारत में विश्व धरोहर स्थलों की संख्या 40 है। इसका मुख्यालय पेरिस, फ्रांस में स्थित है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "32. Who built the Sanchi Stupa ?",
                    question_hi: "32. सांची स्तूप का निर्माण किसने करवाया था ?",
                    options_en: [" Chandra gupta", " Chanakya", 
                                " Bindusar", " Ashoka"],
                    options_hi: [" चंद्रगुप्त", " चाणक्य",
                                " बिंदुसार", " अशोक"],
                    solution_en: "32.(d) Sanchi Stupa was built by Ashoka. Sanchi is a buddhist complex, famous for its Great Stupa, on a hilltop at Sanchi Town in Raisen District of the State of Madhya Pradesh. Sanchi was designated a World Heritage site in 1989.",
                    solution_hi: "32.(d) सांची स्तूप अशोक द्वारा बनवाया गया था। सांची मध्य प्रदेश राज्य के रायसेन जिले के सांची टाउन में एक पहाड़ी की चोटी पर एक बौद्ध परिसर है, जो अपने महान स्तूप के लिए प्रसिद्ध है। सांची को 1989 में विश्व धरोहर स्थल नामित किया गया था।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. With which state is the Nabakalebara festival associated ?</p>",
                    question_hi: "<p>33. नवकलेबारा उत्सव किस राज्य से संबंधित है ?</p>",
                    options_en: ["<p>West Bengal</p>", "<p>Odisha</p>", 
                                "<p>Assam</p>", "<p>Sikkim</p>"],
                    options_hi: ["<p>पश्चिम बंगाल</p>", "<p>उड़ीसा</p>",
                                "<p>असम</p>", "<p>सिक्किम</p>"],
                    solution_en: "<p>33.(b) Nabakalebara festival is associated with Odisha. It is an important festival in the Hindu Odia calendar, observed in the Jagannath Temple, Puri. It was first organized in 1575 A.D by Yaduvanshi Bhoi King Ramachandra Deva.</p>",
                    solution_hi: "<p>33.(b) नवकलेबरा उत्सव ओडिशा से जुड़ा हुआ है। यह हिंदू ओडिया कैलेंडर में एक महत्वपूर्ण त्योहार है, जिसे जगन्नाथ मंदिर, पुरी में मनाया जाता है। यह पहली बार 1575 ई. में यदुवंशी भोई राजा रामचंद्र देव द्वारा आयोजित किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "34. On which river is the Sardar Sarovar Dam constructed ?",
                    question_hi: "34. सरदार सरोवर बांध किस नदी पर बनाया गया है ?",
                    options_en: [" Yamuna", " Narmada", 
                                " Ganga", " Brahmaputra"],
                    options_hi: [" यमुना", " नर्मदा",
                                " गंगा", " ब्रह्मपुत्र"],
                    solution_en: "34.(b) The Sardar Sarovar Dam was constructed on river Narmada in Navagam near Kevadiya, Narmada District, Gujarat in India.",
                    solution_hi: "34.(b) सरदार सरोवर बांध भारत में गुजरात राज्य के नर्मदा जिले के केवडिया के पास नवागाम में नर्मदा नदी पर बनाया गया था।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "35. Who wrote the famous Hindi novel \'Tamas\' ?",
                    question_hi: "35. प्रसिद्ध हिंदी उपन्यास \'तमस\' किसने लिखा था ?",
                    options_en: [" Nagendra", " Bhisham Sahni", 
                                " Yashpal", " Trilochan"],
                    options_hi: [" नागेन्द्र", " भीष्म साहनी",
                                " यशपाल", " त्रिलोचन"],
                    solution_en: "35.(b) Tamas is written by Bhisham Sahni. More notable works are Chief ki Dawat and Amritsar aa gaya hai. He won the Sahitya academy award in 1975 .",
                    solution_hi: "35.(b) \'तमस’ भीष्म साहनी द्वारा लिखा गया है। चीफ की दावत और अमृतसर आ गया है, उनके अधिक उल्लेखनीय कार्य हैं। उन्होंने 1975 में साहित्य अकादमी पुरस्कार भी जीता।",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. The ﬁrst Amendment to the constitution of India was made on ______</p>",
                    question_hi: "<p>36. भारत के संविधान में पहला संशोधन ______को किया गया था।</p>",
                    options_en: ["<p>1953</p>", "<p>1951</p>", 
                                "<p>1952</p>", "<p>1950</p>"],
                    options_hi: ["<p>1953</p>", "<p>1951</p>",
                                "<p>1952</p>", "<p>1950</p>"],
                    solution_en: "<p>36.(b) The ﬁrst Amendment to the constitution of India was made in 1951. It inserted the Ninth Schedule to the Constitution to protect the land reform and other laws present in it from judicial review.</p>",
                    solution_hi: "<p>36.(b) भारत के संविधान में पहला संशोधन 1951 में किया गया था। इसने भूमि सुधार और इसमें मौजूद अन्य कानूनों को न्यायिक समीक्षा से बचाने के लिए संविधान की नौवीं अनुसूची को सम्मिलित किया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. What was the code-name for Pokhran Nuclear Test 2 ?</p>",
                    question_hi: "<p>37. पोखरण परमाणु परीक्षण 2 का कोड नाम क्या था ?</p>",
                    options_en: ["<p>Operation Research</p>", "<p>Laughing Buddha</p>", 
                                "<p>Operation Shakti</p>", "<p>Smiling Buddha</p>"],
                    options_hi: ["<p>ऑपरेशन रिसर्च</p>", "<p>लाफिंग बुद्धा</p>",
                                "<p>ऑपरेशन शक्ति</p>", "<p>स्माइलिंग बुद्धा</p>"],
                    solution_en: "<p>37.(c) Operation Shakti(1998) is the code-name for Pokhran Nuclear Test 2. The first Pokhran test, code-named Smiling Buddha, was conducted in May 1974.</p>",
                    solution_hi: "<p>37.(c) ऑपरेशन शक्ति (1998) पोखरण परमाणु परीक्षण 2 का कोडनेम है। पहला पोखरण परीक्षण, कोड-नाम स्माइलिंग बुद्धा, मई 1974 में आयोजित किया गया था।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "38. Pradhan Mantri Swasthya Suraksha Yojana (PMSSY) was launched in the year",
                    question_hi: "38. प्रधान मंत्री स्वास्थ्य सुरक्षा योजना (PMSSY) किस वर्ष में शुरू की गई थी ?",
                    options_en: [" 2004", " 2005", 
                                " 2006", " 2003"],
                    options_hi: [" 2004", " 2005",
                                " 2006", " 2003"],
                    solution_en: "38.(d) PMSSY(Pradhan Mantri Swasthya Suraksha Yojana) was introduced in 2003 to correct imbalances in the availability of tertiary healthcare services and improve the quality of medical education. The scheme has two components (i) setting up of new AIIMS, and (ii) upgradation of selected Government Medical College Institutions (GMCIs).",
                    solution_hi: "38.(d) PMSSY (प्रधान मंत्री स्वास्थ्य सुरक्षा योजना) को 2003 में तृतीयक स्वास्थ्य सेवाओं की उपलब्धता में असंतुलन को ठीक करने और चिकित्सा शिक्षा की गुणवत्ता में सुधार के लिए शुरू किया गया था। इस योजना के दो घटक हैं (i) नए एम्स की स्थापना, और (ii) चयनित सरकारी मेडिकल कॉलेज संस्थानों (जीएमसीआई) का उन्नयन।",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. According to the World Development Report, countries having per capita income of more than US$12,000 per annum as on 2016 are called</p>",
                    question_hi: "<p>39. विश्व विकास रिपोर्ट के अनुसार, जिन देशों की प्रति व्यक्ति आय 2016 की स्थिति के अनुसार 12,000 अमेरिकी डॉलर प्रति वर्ष से अधिक है, वे ______कहलाते हैं।</p>",
                    options_en: ["<p>low income countries</p>", "<p>low middle income countries</p>", 
                                "<p>rich countries</p>", "<p>poor countries</p>"],
                    options_hi: ["<p>कम आय वाले देश</p>", "<p>निम्न मध्यम आय वाले देश</p>",
                                "<p>अमीर देश</p>", "<p>गरीब देश</p>"],
                    solution_en: "<p>39.(c) According to the World Development Report, countries having per capita income of more than US$12,000 per annum, as of 2016, are called rich countries.</p>",
                    solution_hi: "<p>39.(c) विश्व विकास रिपोर्ट के अनुसार, 2016 तक प्रति व्यक्ति आय 12,000 अमेरिकी डॉलर से अधिक की प्रति व्यक्ति आय वाले देश अमीर देश कहलाते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "<p>40. Who among the following is the youngest Nobel Laureate ?</p>",
                    question_hi: "<p>40. निम्नलिखित में से कौन सबसे कम उम्र का नोबेल पुरस्कार विजेता है ?</p>",
                    options_en: ["<p>Malala Yousafzai</p>", "<p>Tsung Dao Lee</p>", 
                                "<p>Nadia Murad</p>", "<p>Lawrence Bragg</p>"],
                    options_hi: ["<p>मलाला यूसूफ़जई</p>", "<p>त्सुंग दाओ ली</p>",
                                "<p>नादिया मुराद</p>", "<p>लॉरेंस ब्रैग</p>"],
                    solution_en: "<p>40.(a) The youngest Nobel (Peace prize winner) Laureate is Malala Yousafzai (17). John B. Goodenough (97) is the oldest Nobel Laureate.</p>",
                    solution_hi: "<p>40.(a) सबसे कम उम्र की नोबेल (शांति पुरस्कार विजेता) विजेता मलाला यूसुफजई (17) हैं। जॉन बी गुडइनफ (97) सबसे उम्रदराज नोबेल पुरस्कार विजेता हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>