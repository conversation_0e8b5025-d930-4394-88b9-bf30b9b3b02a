<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A and B can do a work in 10 days and 40 days, respectively. How long will it take together to complete the work?</p>",
                    question_hi: "<p>1. A और B एक काम को क्रमश: 10 दिन और 40 दिन में पूरा कर सकते हैं। उन्हें साथ मिलकर काम को पूरा करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>15 days</p>", "<p>20 days</p>", 
                                "<p>8 days</p>", "<p>12 days</p>"],
                    options_hi: ["<p>15 दिन</p>", "<p>20 दिन</p>",
                                "<p>8 दिन</p>", "<p>12 दिन</p>"],
                    solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732793486879.png\" alt=\"rId6\" width=\"274\" height=\"162\"><br>Required time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mrow><mn>4</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 8 days</p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1732793487002.png\" alt=\"rId7\" width=\"266\" height=\"168\"><br>आवश्यक समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mrow><mn>4</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math>&nbsp;= 8 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A motorcycle covered the first 60 km of its journey at an average speed of 40 km/h.&nbsp;The speed of the motorcycle for covering the rest of the journey, i.e. 90 km, was 45&nbsp;km/h. During the whole journey, the overall average speed of the motorcycle was:</p>",
                    question_hi: "<p>2. एक मोटरसाइकिल अपनी यात्रा के पहले 60 km की दूरी को 40 km/h की औसत चाल से तय करती है।&nbsp;शेष यात्रा अर्थात 90 km की दूरी तय करने के लिए मोटरसाइकिल की चाल 45 km/h थी। पूरी यात्रा के&nbsp;दौरान मोटरसाइकिल की कुल औसत चाल कितनी थी?</p>",
                    options_en: ["<p>42<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>km/h</p>", "<p>42 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>km/h</p>", 
                                "<p>42 km/h</p>", "<p>42<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>km/h</p>"],
                    options_hi: ["<p>42<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>km/h</p>", "<p>42 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>km/h</p>",
                                "<p>42 km/h</p>", "<p>42<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>km/h</p>"],
                    solution_en: "<p>2.(d)<br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>distance</mi></mrow><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>time</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math><br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>+</mo><mn>90</mn></mrow><mrow><mfrac><mn>60</mn><mn>40</mn></mfrac><mo>+</mo><mfrac><mn>90</mn><mn>45</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>7</mn></mfrac></math> = 42<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>7</mn></mfrac></math>km/h&nbsp;</p>",
                    solution_hi: "<p>2.(d)<br>औसत गति = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi><mo>&#160;</mo></mrow></mfrac></mstyle></math><br>औसत गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>+</mo><mn>90</mn></mrow><mrow><mfrac><mn>60</mn><mn>40</mn></mfrac><mo>+</mo><mfrac><mn>90</mn><mn>45</mn></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>150</mn><mrow><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle><mo>+</mo><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>7</mn></mfrac></math> = 42<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mn>7</mn></mfrac></math>km/h&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The ratio of the diameters of two spheres is given as 1 : 4. The larger sphere is melted&nbsp;and 125 identical spheres are made out of the molten material. The smaller sphere is&nbsp;melted and 27 identical spheres are made out of the molten material. If the ratio of the&nbsp;volume of each of the 125 identical spheres to the volume of each of the 27 identical&nbsp;spheres is given as 1 : m, what is the value of m ?</p>",
                    question_hi: "<p>3. दो गोलों के व्यासों का अनुपात 1 : 4 दिया गया है। बड़े गोले को पिघलाया जाता है और पिघले हुए पदार्थ से 125 समरूप गोले बनाए जाते हैं। छोटे गोले को पिघलाया जाता है और पिघले हुए पदार्थ से 27 समरूप गोले बनाए जाते हैं। यदि 125 समरूप गोलों में से प्रत्येक के आयतन का, 27 समरूप गोलों में से प्रत्येक के आयतन से अनुपात 1 : m दिया गया है, तो m का मान क्या होगा?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1728</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>1728</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>3375</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1728</mn></mrow><mrow><mn>125</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>1728</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>8000</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>3375</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>3.(b)<br>Let the radius of larger and smaller spheres 2x&nbsp;and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>2</mn></mfrac></math> respectively.<br><strong>1st condition :-</strong><br>Volume of larger spheres = 125 &times; volume of identical spheres<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><msubsup><mi>&#960;R</mi><mn>1</mn><mn>3</mn></msubsup></math> = 125 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>1</mn><mn>3</mn></msubsup></math><br>(2x)<sup>3</sup> = 125 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>1</mn><mn>3</mn></msubsup></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>1</mn><mn>3</mn></msubsup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>3</mn></msup><mn>125</mn></mfrac></math><br><strong>2nd condition : -</strong><br>Volume of smaller spheres = 27 &times; volume of identical spheres<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><mrow><mo>(</mo><msubsup><mi mathvariant=\"normal\">R</mi><mn>2</mn><mn>3</mn></msubsup><mo>)</mo></mrow></math> = 27 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>2</mn><mn>3</mn></msubsup></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><mrow><mo>(</mo><msup><mrow><mfrac><mi mathvariant=\"normal\">x</mi><mn>2</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup></mrow></math> = 27 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>2</mn><mn>3</mn></msubsup></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>2</mn><mn>3</mn></msubsup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>3</mn></msup><mn>27</mn></mfrac></math><br>Ratio of of the volume = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>3</mn></msup><mn>125</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>3</mn></msup><mn>27</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>3</mn></msup><mn>125</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>3</mn></msup><mrow><mn>27</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math>&nbsp;= 1728 : 125<br>According to question<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">m</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1728</mn><mn>125</mn></mfrac></math><br>Hence the value of m = <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>1728</mn></mrow></mfrac></math><br><strong>Alternate method :</strong> <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><mi>volume</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>lager</mi><mi mathvariant=\"normal\">&#160;</mi><mi>sphere</mi></mrow><mn>125</mn></mfrac><mfrac><mrow><mi>volume</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>smaller</mi><mi mathvariant=\"normal\">&#160;</mi><mi>sphere</mi></mrow><mn>27</mn></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">m</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><mo>&#215;</mo><mo>(</mo><mn>2</mn><msup><mo>)</mo><mn>3</mn></msup></mrow><mn>125</mn></mfrac><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><mo>&#215;</mo><mo>(</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><msup><mo>)</mo><mn>3</mn></msup></mrow><mn>27</mn></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">m</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>&#215;</mo><mn>27</mn></mrow><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>&#215;</mo><mn>125</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">m</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>27</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>125</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">m</mi></mfrac></math><br>m = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>1728</mn></mfrac></math></p>",
                    solution_hi: "<p>3.(b)<br>माना बड़े और छोटे गोले का त्रिज्या क्रमशः 2x और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>2</mn></mfrac></math> है।<br><strong>पहली शर्त :-</strong><br>बड़े गोले का आयतन = 125 &times; समरूप गोले का आयतन<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><msubsup><mi>&#960;R</mi><mn>1</mn><mn>3</mn></msubsup></math> = 125 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>1</mn><mn>3</mn></msubsup></math><br>(2x)<sup>3</sup> = 125 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>1</mn><mn>3</mn></msubsup></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>1</mn><mn>3</mn></msubsup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>3</mn></msup><mn>125</mn></mfrac></math><br><strong>दूसरी शर्त :-</strong><br>छोटे गोले का आयतन = 27 &times; समरूप गोले का आयतन<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><msup><mrow><mo>(</mo><msubsup><mi mathvariant=\"normal\">R</mi><mn>2</mn><mn>3</mn></msubsup><mo>)</mo></mrow><mrow></mrow></msup></math> = 27 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>2</mn><mn>3</mn></msubsup></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><mrow><mo>(</mo><msup><mrow><mfrac><mi mathvariant=\"normal\">x</mi><mn>2</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup></mrow></math> = 27 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>2</mn><mn>3</mn></msubsup></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msubsup><mi mathvariant=\"normal\">r</mi><mn>2</mn><mn>3</mn></msubsup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>3</mn></msup><mn>27</mn></mfrac></math><br>आयतन का अनुपात = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>3</mn></msup><mn>125</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow><mn>3</mn></msup><mn>27</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>3</mn></msup><mn>125</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>3</mn></msup><mrow><mn>27</mn><mo>&#215;</mo><mn>8</mn></mrow></mfrac></math> = 1728 : 125<br>प्रश्न के अनुसार <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">m</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1728</mn><mn>125</mn></mfrac></math><br>अतः m का मान = <math display=\"inline\"><mfrac><mrow><mn>125</mn></mrow><mrow><mn>1728</mn></mrow></mfrac></math><br><strong>वैकल्पिक विधि :</strong> <br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><mi>&#2348;&#2337;&#2364;&#2375;</mi><mo>&#160;</mo><mi>&#2327;&#2379;&#2354;&#2375;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow><mn>125</mn></mfrac><mfrac><mrow><mi>&#2331;&#2379;&#2335;&#2375;</mi><mo>&#160;</mo><mi>&#2327;&#2379;&#2354;&#2375;</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2310;&#2351;&#2340;&#2344;</mi></mrow><mn>27</mn></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">m</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><mo>&#215;</mo><mo>(</mo><mn>2</mn><msup><mo>)</mo><mn>3</mn></msup></mrow><mn>125</mn></mfrac><mfrac><mrow><mfrac><mn>4</mn><mn>3</mn></mfrac><mi mathvariant=\"normal\">&#960;</mi><mo>&#215;</mo><mo>(</mo><mfrac><mn>1</mn><mn>2</mn></mfrac><msup><mo>)</mo><mn>3</mn></msup></mrow><mn>27</mn></mfrac></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">m</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>&#215;</mo><mn>27</mn></mrow><mrow><mo>(</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>&#215;</mo><mn>125</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">m</mi></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>27</mn><mo>&#215;</mo><mn>8</mn></mrow><mn>125</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">m</mi></mfrac></math><br>m = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>125</mn><mn>1728</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If equation of line p is x + y = 5 and that of line q is x - y = 3, what are the coordinates&nbsp;of the point common to both the lines?</p>",
                    question_hi: "<p>4. यदि रेखा p का समीकरण x+y = 5 है और रेखा q का समीकरण x - y = 3 है, तो दोनों रेखाओं में&nbsp;उभयनिष्ठ बिंदु के निर्देशांक हैं ?</p>",
                    options_en: ["<p>(1, 4)</p>", "<p>(2, 3)</p>", 
                                "<p>(2, 1)</p>", "<p>(4, 1)</p>"],
                    options_hi: ["<p>(1, 4)</p>", "<p>(2, 3)</p>",
                                "<p>(2, 1)</p>", "<p>(4, 1)</p>"],
                    solution_en: "<p>4.(d)<br>According to the question,<br>x + y = 5&hellip;..(i)<br>x - y = 3&hellip;&hellip;(ii)<br>On adding both equations we get,<br>2x&nbsp;= 8 &rArr; x = 4 <br>On putting the value of <math display=\"inline\"><mi>x</mi></math> in e.q. (i) we get<br>4 + y = 5<br>y = 1<br>Required coordinates = (4, 1)</p>",
                    solution_hi: "<p>4.(d)<br>प्रश्न के अनुसार,<br>x + y = 5&hellip;..(i)<br>x - y = 3&hellip;&hellip;(ii)<br>दोनों समीकरणों को जोड़ने पर,<br>2x&nbsp;= 8 &rArr; x = 4 <br><math display=\"inline\"><mi>x</mi></math> के मान को समीकरण (i) में रखने पर, हमें मिलता है,<br>4 + y = 5<br>y = 1<br>आवश्यक निर्देशांक = (4, 1)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A man spent 60% of his salary on household expenses, 20% of his salary on rent and&nbsp;out of remaining salary, he donated 80% to a trust. If he is left with ₹20,000, then what&nbsp;is his salary?</p>",
                    question_hi: "<p>5. एक व्यक्ति ने अपने वेतन का 60% घर के खर्च पर, अपने वेतन का 20% किराए पर और शेष वेतन में से&nbsp;80% एक ट्रस्ट को दान कर दिया। यदि उसके पास ₹20,000 शेष हैं, तो उसका वेतन कितना है?</p>",
                    options_en: ["<p>₹5,50,000</p>", "<p>₹5,00,000</p>", 
                                "<p>₹4,00,000</p>", "<p>₹50,000</p>"],
                    options_hi: ["<p>₹5,50,000</p>", "<p>₹5,00,000</p>",
                                "<p>₹4,00,000</p>", "<p>₹50,000</p>"],
                    solution_en: "<p>5.(b)<br>Let the total salary = 100 units<br>Remaining amount = 100 - [{60 + 20} + 20 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)] = 4 units<br>4 units = ₹ 20,000 <br>(Required salary) 100 units = <math display=\"inline\"><mfrac><mrow><mn>20000</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = ₹ 5,00,000</p>",
                    solution_hi: "<p>5.(b)<br>माना कुल वेतन = 100 इकाई<br>शेष राशि = 100 - [{60 + 20} + 20 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)] = 4 इकाई<br>4 इकाई = ₹ 20,000 <br>(आवश्यक वेतन) 100 इकाई = <math display=\"inline\"><mfrac><mrow><mn>20000</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 100 = ₹ 5,00,000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Two parallel chords on the same side of the centre of a circle are 12 cm and 20 cm long and the radius of the circle is 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm.What is the distance between the chords?</p>",
                    question_hi: "<p>6. एक वृत्त के केंद्र के एक ही ओर 12 cm और 20 cm लंबाई की दो समान्तर जीवाएँ हैं और वृत्त की त्रिज्या&nbsp;5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm है। इन जीवाओं के बीच की दूरी कितनी होगी?</p>",
                    options_en: ["<p>2 cm</p>", "<p>3 cm</p>", 
                                "<p>4 cm</p>", "<p>5 cm</p>"],
                    options_hi: ["<p>2 cm</p>", "<p>3 cm</p>",
                                "<p>4 cm</p>", "<p>5 cm</p>"],
                    solution_en: "<p>6.(a)<br><strong id=\"docs-internal-guid-476553f8-7fff-159a-2a60-51b6e4965684\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdBjFaTR87bc0QXUATMIU48ftKFciBkGbQhhdoU7hL8LBJ6_POTT2r-KnsXv8UP9BoSQNAzacvKREMVt3IpwUPGRUB38Q16PtL_GMd7rQdXExqu-pjwBeay7jTgQ-4WcNSGQsfAsw?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"186\" height=\"165\"></strong><br>Radius of the circle(OB) = 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm<br>In triangle OAB,<br>AO = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>OB</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>AB</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>AO = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>5</mn><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>6</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>AO = <math display=\"inline\"><msqrt><mn>325</mn><mo>-</mo><mn>36</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>289</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>17</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>AO = 17 cm<br>In triangle OCD,<br>CO =&nbsp;<math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>5</mn><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>CO = <math display=\"inline\"><msqrt><mn>325</mn><mo>-</mo><mn>100</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>15</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>CO = 15 cm<br>AC = AO - CO = 17 - 15 = 2 cm</p>",
                    solution_hi: "<p>6.(a)<br><strong id=\"docs-internal-guid-998939c8-7fff-5ca1-54b9-4ddc0fe69e70\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdBjFaTR87bc0QXUATMIU48ftKFciBkGbQhhdoU7hL8LBJ6_POTT2r-KnsXv8UP9BoSQNAzacvKREMVt3IpwUPGRUB38Q16PtL_GMd7rQdXExqu-pjwBeay7jTgQ-4WcNSGQsfAsw?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"186\" height=\"165\"></strong><br>वृत्त की त्रिज्या (OB) = 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm<br>त्रिभुज OAB में,<br>AO = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>OB</mi><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mi>AB</mi><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>AO = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>5</mn><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>6</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>AO = <math display=\"inline\"><msqrt><mn>325</mn><mo>-</mo><mn>36</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>289</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>17</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>AO = 17 cm<br>त्रिभुज OCD में,<br>CO =&nbsp;<math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>5</mn><msqrt><mn>13</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>CO = <math display=\"inline\"><msqrt><mn>325</mn><mo>-</mo><mn>100</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>15</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>CO = 15 cm<br>AC = AO - CO = 17 - 15 = 2 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. If PQ and PR are the two tangents to a circle with center O, and &ang;QOR = 150&deg;, then &ang;QPR is equal to :</p>",
                    question_hi: "<p>7. यदि PQ और PR केद्र O वाले वृत्त की दो स्पर्श रेखाएँ हैं, और &ang;QOR = 150&deg; है, तो &ang;QPR किसके बराबर है?</p>",
                    options_en: ["<p>60&deg;</p>", "<p>90&deg;</p>", 
                                "<p>45&deg;</p>", "<p>30&deg;</p>"],
                    options_hi: ["<p>60&deg;</p>", "<p>90&deg;</p>",
                                "<p>45&deg;</p>", "<p>30&deg;</p>"],
                    solution_en: "<p>7.(d)<br><strong id=\"docs-internal-guid-29c3aa37-7fff-f227-98f1-2ffd1d390cb5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfLuwkR5FwZo3bhmYWQd_ui_QkuPzFuEQaUk-ti_BPaWSlu_h_U3tQ_soKtTZbk-X-Q32LiTLU5p_rn8k7WEUJEe8VcqUWkE40i9uvOkVKwbYhdMAD10Xu6VYEQrXb-XxTfOEquuQ?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"267\" height=\"171\"></strong><br>the angle between the radius and the tangent always be exactly 90&deg;.<br>So, &ang;OQP = &ang;ORP = 90&deg;<br>&ang;QPR = 360&deg; - (150&deg; + 90&deg; + 90&deg;) = 30&deg;</p>",
                    solution_hi: "<p>7.(d)<br><strong id=\"docs-internal-guid-29c3aa37-7fff-f227-98f1-2ffd1d390cb5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfLuwkR5FwZo3bhmYWQd_ui_QkuPzFuEQaUk-ti_BPaWSlu_h_U3tQ_soKtTZbk-X-Q32LiTLU5p_rn8k7WEUJEe8VcqUWkE40i9uvOkVKwbYhdMAD10Xu6VYEQrXb-XxTfOEquuQ?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"267\" height=\"171\"></strong><br>त्रिज्या और स्पर्शरेखा के बीच का कोण हमेशा 90&deg; होता है।<br>तो, &ang;OQP = &ang;ORP = 90&deg;<br>&ang;QPR = 360&deg; - (150&deg; + 90&deg; + 90&deg;) = 30&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. In how many years will the simple interest on a sum of money be equal to the principal at the rate of 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>% per annum?</p>",
                    question_hi: "<p>8. कितने वर्षों में किसी धनराशि पर 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>% वार्षिक की दर से साधारण ब्याज मूलधन के बराबर हो जायेगा?</p>",
                    options_en: ["<p>34 years</p>", "<p>37 years</p>", 
                                "<p>35 years</p>", "<p>33 years</p>"],
                    options_hi: ["<p>34 वर्ष</p>", "<p>37 वर्ष</p>",
                                "<p>35 वर्ष</p>", "<p>33 वर्ष</p>"],
                    solution_en: "<p>8.(c) S.I. = Principal &hellip; (given)<br>Let the simple interest and principal be x<br>S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">p</mi><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mi mathvariant=\"normal\">t</mi></mrow><mn>100</mn></mfrac></math><br>x =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mn>20</mn><mo>&#215;</mo><mi mathvariant=\"normal\">t</mi></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>7</mn><mo>&#160;</mo></mrow></mfrac></math><br>t = 35 years</p>",
                    solution_hi: "<p>8.(c) साधारण ब्याज = मूलधन &hellip; (दिया गया है)<br>माना साधारण ब्याज और मूलधन <math display=\"inline\"><mi>x</mi></math> है<br>साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>100</mn></mfrac></math><br>x =<math style=\"font-family:\'Times New Roman\'\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>&#215;</mo><mn>20</mn><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi><mo>&#160;</mo></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>7</mn><mo>&#160;</mo></mrow></mfrac></math><br>समय = 35 वर्ष</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. If Arun\'s salary was decreased by 50% and subsequently increased by 50%, then how&nbsp;much percent did he lose?</p>",
                    question_hi: "<p>9. यदि अरुण के वेतन में 50% की कमी की जाती है और बाद में 50% की वृद्धि की जाती है, तो उसे कितने प्रतिशत की हानि हुई?</p>",
                    options_en: ["<p>25%</p>", "<p>10%</p>", 
                                "<p>40%</p>", "<p>36%</p>"],
                    options_hi: ["<p>25%</p>", "<p>10%</p>",
                                "<p>40%</p>", "<p>36%</p>"],
                    solution_en: "<p>9.(a)<br>loss% = - 50% + 50% + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>50</mn><mi>%</mi><mo>&#215;</mo><mn>50</mn><mi>%</mi></mrow><mn>100</mn></mfrac></math> = - 25% (- ve sign show loss)</p>",
                    solution_hi: "<p>9.(a)<br>हानि% = - 50% + 50% + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>50</mn><mi>%</mi><mo>&#215;</mo><mn>50</mn><mi>%</mi></mrow><mn>100</mn></mfrac></math>&nbsp;= - 25% (- चिन्ह हानि को दर्शाते है)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. A company has five plants for manufacturing spare parts. Each plant manufactures&nbsp;local quality and export quality. What is the ratio of the production of local quality and&nbsp;export quality units of the highest manufacturing plant?<br><strong id=\"docs-internal-guid-161bcffe-7fff-afbe-a3bb-90c3601203fe\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdAP2xPMmDdgG2VnuKKLVrrpYAit4WM_rhRZc5-IDzANYKFrGyEIXgV-lblqFhfodUa7Xo_Hn2hTOSzGe1ZbGE7vA7nM1NttnoPsGfHxmqySOF43fBJR-vY4tbThI-yCG3lBamE?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"465\" height=\"214\"></strong></p>",
                    question_hi: "<p>10. किसी कंपनी का स्पेयर पार्ट विनिर्माण के लिए पाँच संयंत्र हैं। प्रत्येक संयंत्र स्थानीय गुणवत्ता और निर्यात गुणवत्ता का विनिर्माण करता है। उच्चतम विनिर्माण संयंत्र की स्थानीय गुणवत्ता और निर्यात गुणवत्ता इकाइयों के उत्पादन का अनुपात क्या है?<br><strong id=\"docs-internal-guid-79906e03-7fff-4f4c-66d6-98c8c924ebe2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfuZsnp4eLEj3e8zPKyisKYI1cF8YhWOFR-UY5h4bLKK1QcS0bEMRVu8geROXf3vXV4zaeZN0v9b2FapeSyIEQtFpGGl8QwoWZZl_zcvSU3lrmPpnPCu0EJrOWWyPMWwsnydbFt?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"453\" height=\"202\"></strong></p>",
                    options_en: ["<p>4 : 1</p>", "<p>3 : 1</p>", 
                                "<p>2 : 11</p>", "<p>7 : 3</p>"],
                    options_hi: ["<p>4 : 1</p>", "<p>3 : 1</p>",
                                "<p>2 : 11</p>", "<p>7 : 3</p>"],
                    solution_en: "<p>10.(b)<br>Total production of Plant A = 420 + 140 = 560<br>Total production of Plant B = 350 + 150 = 500<br>Total production of Plant C = 360 + 120 = 480<br>Total production of Plant D = 440 + 80 = 520<br>Total production of Plant E = 440 + 110 = 550<br>We can see that the highest manufacturing plant is A<br>So Required ratio = 420 : 140 = 3 : 1</p>",
                    solution_hi: "<p>10.(b)<br>संयंत्र A का कुल उत्पादन = 420 + 140 = 560<br>संयंत्र B का कुल उत्पादन = 350 + 150 = 500<br>संयंत्र C का कुल उत्पादन = 360 + 120 = 480<br>संयंत्र D का कुल उत्पादन = 440 + 80 = 520<br>संयंत्र E का कुल उत्पादन = 440 + 110 = 550<br>हम देख सकते हैं कि उच्चतम विनिर्माण संयंत्र A है<br>तो अभीष्ट अनुपात = 420 : 140 = 3 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A shopkeeper marks up the price of the oil by 40% and uses a faulty machine which measures 15% less. If the shopkeeper gives a discount of 32%, the profit/loss percentage is:</p>",
                    question_hi: "<p>11. एक दुकानदार तेल का मूल्य 40% अधिक अंकित करता है और एक खराब तराजू का उपयोग करता है जो 15% कम मापती है। यदि दुकानदार 32% की छूट देता है, तो उसका लाभ/हानि प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>12% Profit</p>", "<p>25% Profit</p>", 
                                "<p>15% Loss</p>", "<p>18% Loss</p>"],
                    options_hi: ["<p>12% लाभ</p>", "<p>25% लाभ</p>",
                                "<p>15% हानि</p>", "<p>18% हानि</p>"],
                    solution_en: "<p>11.(a)<br>Ratio - CP&nbsp; :&nbsp; SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; :&nbsp; 140<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;850&nbsp; :&nbsp; 1000<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; :&nbsp; 68<br>&mdash;--------------------------<br>Final - 850 : 14 &times; 68 = 50 : 56<br>profit% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>56</mn><mo>-</mo><mn>50</mn></mrow><mn>50</mn></mfrac></math> &times; 100 = 12</p>",
                    solution_hi: "<p>11.(a)<br>अनुपात - क्रय मूल्य&nbsp; :&nbsp; विक्रय मूल्य<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100&nbsp; :&nbsp; 140<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;850&nbsp; :&nbsp; 1000<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; :&nbsp; 68<br>-------------------------------------<br>अंतिम - 850 : 14 &times; 68 = 50 : 56<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>56</mn><mo>-</mo><mn>50</mn></mrow><mrow><mn>50</mn></mrow></mfrac></math> &times; 100 = 12</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. The percentage of students enrolled in different activities in a school which are displayed in the left side pie chart. The total number of students is 5000.<br>In the right-side pie chart, the total number of girls is 1550 and their percentage breakup enrolled in these activities.<br><strong id=\"docs-internal-guid-ad4b979b-7fff-25c6-94a6-e83f50519dd4\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdR-bCZGN2w6bk-gZcsoDeWsqmaDxHMLPoCozT8fRQHwIMeOVnlgwyfDljEFEGpZTqQqJXUPZraJwfeicyrB2QuwwyznKs4Q2vpEOuDTmA7UT3aJFcWbMRJziyN8IB4pNILKuIqDA?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"208\" height=\"202\">&nbsp; &nbsp; &nbsp; &nbsp; </strong><strong id=\"docs-internal-guid-b8e6717a-7fff-32b1-0529-5119aeb1ac56\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdPKyT30ryIG6qdoIq_qcho-B_HrebkYvMwyRM8WB07trg6V9rrU4vP23jlIu4QoO26hq-xlSKTc5AaGUi5aRO6Fd-xmdjpuQlz-4L4W_PsJwPBMHcNBCGVv8yHegJr93ngHZMdew?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"206\" height=\"207\"></strong><br>The number of girls enrolled in Craft forms what percent of the total number of&nbsp;students in the school?</p>",
                    question_hi: "<p>12. एक स्कूल में विभिन्न गतिविधियों में नामांकित विद्यार्थियों का प्रतिशत जो बाईं ओर पाई चार्ट में प्रदर्शित किया गया है। विद्यार्थियों की कुल संख्या 5000 है।<br>दायीं ओर के पाई चार्ट में, लड़कियों की कुल संख्या 1550 है और इन गतिविधियों में नामांकित उनका ब्रेकअप प्रतिशत है।<br><strong id=\"docs-internal-guid-074b37b4-7fff-c239-2228-5681596b2dae\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe3Yaf38JC2gfdcfUewCaKN7TQLPn8Tm4YgCCAl38XmLTdcvJiiI2UvDcu_DWXZCZT2o0ivAGLKrr1Tp0vSd5PnOupiNLehYJaaq2w30CpR575m9fjzsfDzbNPWzdaQPVclj10gNw?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"214\" height=\"203\">&nbsp; &nbsp; &nbsp; &nbsp; </strong><strong id=\"docs-internal-guid-aa491b62-7fff-cb89-b6d4-76a5c5702eb5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXebPKmGvgFK7nlopevPG54RgSMeUqAOF8YVyNJM8tx5NwRYkQDPGfN9gaeWqeMoatHRxJmgiUsixPGW3I4b6eKayrwTrb1fmcXJaT6oGbwJfntgcdA-V3lHHrOOt8vPxAipZBjfSg?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"195\" height=\"205\"></strong><br>क्राफ्ट में नामांकित लड़कियों की संख्या, स्कूल में विद्यार्थियों की कुल संख्या का कितना प्रतिशत है?</p>",
                    options_en: ["<p>6.82%</p>", "<p>7.11%</p>", 
                                "<p>5.82%</p>", "<p>5.81%</p>"],
                    options_hi: ["<p>6.82%</p>", "<p>7.11%</p>",
                                "<p>5.82%</p>", "<p>5.81%</p>"],
                    solution_en: "<p>12.(a)<br>Girls enrolled in craft = 1550 &times; 22% = 341<br>required% = <math display=\"inline\"><mfrac><mrow><mn>341</mn></mrow><mrow><mn>5000</mn></mrow></mfrac></math> &times; 100 = 6.82</p>",
                    solution_hi: "<p>12.(a)<br>क्राफ्ट में नामांकित लड़कियाँ = 1550 &times; 22% = 341<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>341</mn></mrow><mrow><mn>5000</mn></mrow></mfrac></math> &times; 100 = 6.82</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Radhika requested the cashier of a bank to get her coins in lieu of her cheque worth ₹1,845. Cashier gave her a packet containing ₹5, ₹10 and ₹20 coins in the ratio of 3 : 5 : 7. What is the total worth of the smallest coin received by her?</p>",
                    question_hi: "<p>13. राधिका ने एक बैंक के कैशियर से अनुरोध किया कि वह उसके ₹1,845 के चेक के बदले में उसे सिक्के दे दे। कैशियर ने उसे ₹5, ₹10 और ₹20 के सिक्कों का एक पैकेट 3 : 5 : 7 के अनुपात में दिया। उसके द्वारा प्राप्त सबसे छोटे सिक्के का कुल मूल्य कितना है?</p>",
                    options_en: ["<p>₹135</p>", "<p>₹125</p>", 
                                "<p>₹145</p>", "<p>₹105</p>"],
                    options_hi: ["<p>₹135</p>", "<p>₹125</p>",
                                "<p>₹145</p>", "<p>₹105</p>"],
                    solution_en: "<p>13.(a)<br>Ratio - 5 : 10 : 20<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; :&nbsp; 5&nbsp; :&nbsp; 7<br>&mdash;--------------------------<br>Final - 15 : 50 : 140<br>Total amount = 15 + 50 + 140 = 205 units<br>205 units = ₹ 1845<br>(required worth) 15 units = <math display=\"inline\"><mfrac><mrow><mn>1845</mn></mrow><mrow><mn>205</mn></mrow></mfrac></math> &times; 15 = ₹ 135</p>",
                    solution_hi: "<p>13.(a)<br>अनुपात - 5 : 10 : 20<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; :&nbsp; 5&nbsp; :&nbsp; 7<br>-----------------------------<br>अंतिम - 15 : 50 : 140<br>कुल राशि = 15 + 50 + 140 = 205 इकाई<br>205 इकाई = ₹ 1845<br>(आवश्यक मूल्य) 15 इकाई= <math display=\"inline\"><mfrac><mrow><mn>1845</mn></mrow><mrow><mn>205</mn></mrow></mfrac></math> &times; 15 = ₹ 135</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. In right-angled triangle ABC, &ang;B = 90&deg; and angle A and angle C are acute angles.&nbsp;If cosecA = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mi>&#160;</mi></math>&nbsp;, then find the value of sinA.cosC + cosA.sinC</p>",
                    question_hi: "<p>14. समकोण त्रिभुज ABC में, &ang;B = 90&deg; तथा कोण A एवं कोण C न्यून कोण हैं।&nbsp;यदि cosecA = 2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt><mi>&#160;</mi></math> हो, तो sinA.cosC + cosA.sinC का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>1</p>", 
                                "<p>0</p>", "<p>2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>&nbsp;</p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>1</p>",
                                "<p>0</p>", "<p>2<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math>&nbsp;</p>"],
                    solution_en: "<p>14.(b)<br><strong id=\"docs-internal-guid-af432214-7fff-a65b-9105-3fe6fa832b92\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcJhP2jMRjNof2N2I944QqcfXT48kJWdASadMk2wLir0zP2ROjoWZornM1r0TGQ1qKmj39xOnd9cei6-zSjS7r6er9sVQzaNZ6D-dPYCgEqts6E_EoXT7P1G6-9SDP2Cu9P6yOtNA?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"158\" height=\"169\"></strong><br>cosecA = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mi>BC</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>1</mn></mfrac></math><br>AB = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>8</mn><mo>-</mo><mn>1</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math><br>sinA.cosC + cosA.sinC = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mi>AC</mi></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mi>AC</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>AC</mi></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>AC</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>7</mn></msqrt><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>7</mn></msqrt><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math> <br>= 1</p>",
                    solution_hi: "<p>14.(b)<br><strong id=\"docs-internal-guid-af432214-7fff-a65b-9105-3fe6fa832b92\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcJhP2jMRjNof2N2I944QqcfXT48kJWdASadMk2wLir0zP2ROjoWZornM1r0TGQ1qKmj39xOnd9cei6-zSjS7r6er9sVQzaNZ6D-dPYCgEqts6E_EoXT7P1G6-9SDP2Cu9P6yOtNA?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"158\" height=\"169\"></strong><br>cosecA = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AC</mi><mi>BC</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow><mn>1</mn></mfrac></math><br>AB = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>2</mn><msqrt><mn>2</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>8</mn><mo>-</mo><mn>1</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math><br>sinA.cosC + cosA.sinC = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mi>AC</mi></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mi>AC</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>AC</mi></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>AC</mi></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>7</mn></msqrt><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>7</mn></msqrt><mrow><mn>2</mn><msqrt><mn>2</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math> <br>= 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. Given that the perimeters of two similar triangles, &Delta;ABC and &Delta;DEF, are 24 cm and 9&nbsp;cm, respectively, and DE = 3 cm, what is the ratio between the area of &Delta;ABC and that&nbsp;of &Delta;DEF ?</p>",
                    question_hi: "<p>15. यदि दो समरूप त्रिभुजों , &Delta;ABC और &Delta;DEF के परिमाप क्रमशः 24 cm और 9 cm हैं, और DE = 3 cm&nbsp;है, तो &Delta;ABC और &Delta;DEF के क्षेत्रफल के बीच का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>54 : 9</p>", "<p>54 : 7</p>", 
                                "<p>64 : 9</p>", "<p>64 : 7</p>"],
                    options_hi: ["<p>54 : 9</p>", "<p>54 : 7</p>",
                                "<p>64 : 9</p>", "<p>64 : 7</p>"],
                    solution_en: "<p>15.(c)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>perimeter</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mo>&#160;</mo><mi>&#916;ABC</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>perimeter</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mo>&#160;</mo><mi>&#916;DEF</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>9</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>DE</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mi>EF</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mn>3</mn></mfrac></math>, AB = 8 cm<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>area</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>&#916;ABC</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>area</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mo>&#160;</mo><mi>&#916;DEF</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>DE</mi></mfrac></math>)<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>area</mi><mo>&#160;</mo><mi>of</mi><mo>&#160;</mo><mi>&#916;ABC</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>area</mi><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mo>&#160;</mo><mi>&#916;DEF</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math>)<sup>2</sup><br>Required ratio = 64 : 9</p>",
                    solution_hi: "<p>15.(c)<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mo>&#160;</mo><mo>&#8710;</mo><mi>ABC</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi mathvariant=\"normal\">&#160;</mi><mo>&#8710;</mo><mo>&#160;</mo><mi>DEF</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2352;&#2367;&#2350;&#2366;&#2346;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>9</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>DE</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>BC</mi><mi>EF</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mn>3</mn></mfrac></math>, AB = 8 cm<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mi>&#916;ABC</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;DEF</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AB</mi><mi>DE</mi></mfrac></math>)<sup>2</sup><br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mi>&#916;ABC</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;DEF</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>3</mn></mfrac></math>)<sup>2</sup><br>अभीष्ट अनुपात = 64 : 9</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. A largest possible sphere is carved from a cube of side 14 cm. What is its volume in cm<sup>3</sup>?</p>",
                    question_hi: "<p>16. 14 cm भुजा के एक घन से एक सबसे बड़ा संभाव्य गोला निकालकर बनाया जाता है। इसका&nbsp;आयतन ( cm<sup>3</sup> में )कितना है?</p>",
                    options_en: ["<p>205<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>1707<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>1437<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>1600<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>205<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>1707<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>1437<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>1600<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>16.(c) <br>Radius of the sphere = <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 7 cm<br>Volume of the sphere = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;r<sup>3</sup>&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; (7)<sup>3</sup><br>= 1437<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> cm<sup>3</sup></p>",
                    solution_hi: "<p>16.(c) <br>गोले की त्रिज्या = <math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 7 cm<br>गोले का आयतन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;r<sup>3</sup>&nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; (7)<sup>3</sup><br>= 1437<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> cm<sup>3</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. The surface area of a cube is 486 cm<sup>2</sup> Find the volume.</p>",
                    question_hi: "<p>17. एक घन का पृष्ठीय क्षेत्रफल 486 cm<sup>2</sup> है, उसका आयतन ज्ञात करें।</p>",
                    options_en: ["<p>625 cm<sup>3</sup></p>", "<p>512 cm<sup>3</sup></p>", 
                                "<p>729 cm<sup>3</sup></p>", "<p>486 cm<sup>3</sup></p>"],
                    options_hi: ["<p>625 cm<sup>3</sup></p>", "<p>512 cm<sup>3</sup></p>",
                                "<p>729 cm<sup>3</sup></p>", "<p>486 cm<sup>3</sup></p>"],
                    solution_en: "<p>17.(c)<br>Surface area of the cube = 6a<sup>2</sup><br>486 = 6a<sup>2</sup><br>a = 9 cm<br>Volume of the cube = a<sup>3</sup><br>= (9)<sup>3</sup><br>= 729 cm<sup>3</sup></p>",
                    solution_hi: "<p>17.(c)<br>घन का पृष्ठीय क्षेत्रफल = 6a<sup>2</sup><br>486 = 6a<sup>2</sup><br>a = 9 cm<br>घन का आयतन = a<sup>3</sup><br>= (9)<sup>3</sup><br>= 729 cm<sup>3</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. A man walking at the speed of 5 km/h covers a certain distance in 3 hours 45 minutes.&nbsp;If he covers the same distance by a cycle in 2 hours 10 minutes, then the speed of the&nbsp;cycle in km/h is:</p>",
                    question_hi: "<p>18. एक व्यक्ति 5 km/h की चाल से चलते हुए एक निश्चित दूरी को 3 घंटे 45 मिनट में तय करता है। यदि वह समान दूरी को एक साइकिल द्वारा 2 घंटे 10 मिनट में तय करता है, तो साइकिल की km/h में चाल क्या है?</p>",
                    options_en: ["<p>7<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>", "<p>8<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>", 
                                "<p>5<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>7<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>", "<p>8<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>",
                                "<p>5<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>", "<p>9<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>26</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>18.(b)<br>Distance = speed &times; time<br>Distance covered by walking = 5 &times; 3<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> km = 18.75 km<br>Time taken by cycle = 2 hours 10 min. = 130 min.<br>Required speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>.</mo><mn>75</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>130</mn></mfrac></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>26</mn></mfrac></math> km/h</p>",
                    solution_hi: "<p>18.(b)<br>दूरी = गति &times; समय<br>पैदल चलकर तय की गई दूरी = 5 &times; 3<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> km = 18.75 km<br>साइकिल द्वारा लिया गया समय = 2 घंटे 10 मिनट = 130 मिनट<br>आवश्यक गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>18</mn><mo>.</mo><mn>75</mn><mo>&#215;</mo><mn>60</mn></mrow><mn>130</mn></mfrac></math> = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>26</mn></mfrac></math> km/h</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. The given table shows the percentage of marks obtained by 5 students in different subjects. Study the table and answer the question that follows. (Maximum marks are given beside subject)<br><strong id=\"docs-internal-guid-3311fedb-7fff-e748-350f-c3c9103efa32\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdzqYuUc1k_c7xGqpXBBcqJP0qDpVBPzQXiRa10ejRaJnljrR3QGE9O_gCV92oknZwzX5oD6kYQzopqD7iSMVo9h_egrLMnhw3kPVHXqVGKrAk6FhPN8fMR4mL6GPOUgdn9sErt?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"329\" height=\"130\"></strong><br>What are the marks (in percentage) obtained by Mohan?</p>",
                    question_hi: "<p>19. दी गई तालिका 5 छात्रों द्वारा विभिन्न विषयों में प्राप्त अंकों का प्रतिशत दर्शाती है। तालिका का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें। (अधिकतम अंक विषय के नीचे दिए गए हैं)<br><strong id=\"docs-internal-guid-79875f9f-7fff-2021-55b8-0da9169060f6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdtwO4h-GQuqinfjnFc9xRCHO3ZKHjTmW65p5ilsK7fALK9V0tzn0sddvjBhcHnnKBKrhlnGHZzktuF08_NwTXgnj0l-lJ1fifVpEyG8W_EDTICgVX-DcT81kIUDsoKXqEWekZpcQ?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"378\" height=\"142\"></strong><br>मोहन द्वारा प्राप्त अंक (प्रतिशत में) कितने हैं?</p>",
                    options_en: ["<p>76.5%</p>", "<p>77.5%</p>", 
                                "<p>75.5%</p>", "<p>74.5%</p>"],
                    options_hi: ["<p>76.5%</p>", "<p>77.5%</p>",
                                "<p>75.5%</p>", "<p>74.5%</p>"],
                    solution_en: "<p>19.(d)&nbsp;Total marks of mohan</p>\n<p>= 300 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + 300 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math> + 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>100</mn></mfrac></math> + 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>95</mn><mn>100</mn></mfrac></math> + 200 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac></math><br>= 240 + 180 + 70 + 95 + 160<br>= 745<br>Maximum marks = 300 + 300 + 100 + 100 + 200 = 1000<br>required% = <math display=\"inline\"><mfrac><mrow><mn>745</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 100 = 74.5</p>",
                    solution_hi: "<p>19.(d)&nbsp;मोहन के कुल अंक</p>\n<p>= 300 &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> + 300 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>100</mn></mfrac></math> + 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>70</mn><mn>100</mn></mfrac></math> + 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>95</mn><mn>100</mn></mfrac></math> + 200 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac></math><br>= 240 + 180 + 70 + 95 + 160<br>= 745<br>अधिकतम अंक = 300 + 300 + 100 + 100 + 200 = 1000<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>745</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; 100 = 74.5</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. What will be the least value of x&nbsp;so that the 5-digit number 627x5 becomes divisible by&nbsp;9?</p>",
                    question_hi: "<p>20. x&nbsp;का वह न्यूनतम मान क्या होगा जिससे 5 अंकों की संख्या 627x 5, 9 से विभाज्य हो जाए?</p>",
                    options_en: ["<p>3</p>", "<p>9</p>", 
                                "<p>4</p>", "<p>7</p>"],
                    options_hi: ["<p>3</p>", "<p>9</p>",
                                "<p>4</p>", "<p>7</p>"],
                    solution_en: "<p>20.(d) <br>Divisibility rule of 9 :- sum of digits is divisible by 9<br>627x5 = 6 + 2 + 7 + x + 5 = 20 + x<br>x = 7, 27 is completely divisible by 9<br>Hence the value of the x will be 7.</p>",
                    solution_hi: "<p>20.(d) <br>9 का विभाज्यता नियम : - अंकों का योग 9 से विभाज्य होता है<br>627x5 = 6 + 2 + 7 + x + 5 = 20 + x<br>x = 7<br>20 + 7 = 27 , 9 से पूर्णतः विभाज्य है<br>अतः x का मान 7 होगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. Simplify the following expression.<br>3 + 28 &divide; 35 &times; 15 + 4 - 3 &times; 4</p>",
                    question_hi: "<p>21. निम्नलिखित व्यंजक को सरल कीजिए।<br>3 + 28 &divide; 35 &times; 15 + 4 - 3 &times; 4</p>",
                    options_en: ["<p>26</p>", "<p>7</p>", 
                                "<p>64</p>", "<p>10</p>"],
                    options_hi: ["<p>26</p>", "<p>7</p>",
                                "<p>64</p>", "<p>10</p>"],
                    solution_en: "<p>21.(b) <br>3 + <math display=\"inline\"><mfrac><mrow><mn>28</mn><mi>&#160;</mi></mrow><mrow><mn>35</mn></mrow></mfrac></math> &times; 15 + 4 - 3 &times; 4<br>3 + <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 15 + 4 - 12<br>3 + 12 + 4 - 12 = 7</p>",
                    solution_hi: "<p>21.(b) <br>3 + <math display=\"inline\"><mfrac><mrow><mn>28</mn><mi>&#160;</mi></mrow><mrow><mn>35</mn></mrow></mfrac></math> &times; 15 + 4 - 3 &times; 4<br>3 + <math display=\"inline\"><mfrac><mrow><mn>4</mn><mi>&#160;</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 15 + 4 - 12<br>3 + 12 + 4 - 12 = 7</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. Study the given pie-charts and answer the question that follows.<br>The pie-charts show the data of students\' progress after graduation.<br><strong id=\"docs-internal-guid-a585fc1d-7fff-a921-a645-b7187bd86e9e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcmgL_3TyiiXh4ifC0_jhtZLewyxmB_ASg8c5c3dsW-jng9XZGl4JtGukx4vBGzQx7YMn4vPF7CxD76m4xmQflsCg_lKFEs9OzfQRdVUQHnv3uGIKabebtXa2fZtII1kiMSmlQG?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"322\" height=\"219\"> &nbsp;</strong><strong id=\"docs-internal-guid-51eaf756-7fff-b924-1725-6d4b9c3765a5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXejxJeeeteNuqHmdjw33-NlZoMJY3yzJwbLWe-HKJbL6dMzeVmkj_6HrBOdFT8hTXQidTf7IV7r9uAOoYscl3g2lvjkiOTicvD4t8_CWg-3zarjtzzl4gTOpOWexOpikOmcAAHHwQ?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"213\" height=\"191\"></strong><br>Which two combined have the same number of students as the other?</p>",
                    question_hi: "<p>22. दिए गए पाई-चार्ट का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br>पाई-चार्ट स्नातक स्तर की पढ़ाई के बाद विद्यार्थियों की प्रगति के आंकड़े दर्शाते हैं।<br><strong id=\"docs-internal-guid-6a3dc534-7fff-2dae-7f25-16e2b62082a9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd5pDYkVMOS1JL1GinmQSdvfDoJWGevQCycsocRwuqkcNTj4KeVWX-L3d4wT7YlUoMEno68k3A0wY_fje_n1ouKsIIbk05zfcXmxLNOsjyrUgfo3Al_9quddM6ykFFxoyiB59-tBw?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"319\" height=\"208\">&nbsp; &nbsp; &nbsp;</strong><strong id=\"docs-internal-guid-7817b0d9-7fff-396c-d5c6-6237542c7c25\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfn5efsdkpWJpItjaTS4AxcchqJQaXfwS4UHFnSdDMbRMYznO-f-VZwPjOIvU-hDjdugt-UuPGSsKrVsvrBy5OKUEpYFIeoS_rg2VtuNN8bGP_hpqndOJROmRdDuLB294n1FC2X?key=SVzFhHGAESnk9qwEJ_KPOMcg\" width=\"192\" height=\"183\"></strong><br>किन दो को मिलाकर विद्यार्थियों की संख्या अन्य (Other) के समान है?</p>",
                    options_en: ["<p>P.G. + M.Phil.</p>", "<p>P.G. + Ph.D.</p>", 
                                "<p>Entrepreneur + Ph.D.</p>", "<p>Entrepreneur + P.G.</p>"],
                    options_hi: ["<p>पी.जी. + एम.फिल.</p>", "<p>पी.जी.+ पीएच.डी</p>",
                                "<p>उद्यमी + पीएच.डी</p>", "<p>उद्यमी + पी.जी.</p>"],
                    solution_en: "<p>22.(b)&nbsp;By checking option one by one option (b) satisfied the given condition<br>P.G. + Ph.D. = other<br>39% + 9% = 48%<br>48% = 48%<br>RHS = LHS</p>",
                    solution_hi: "<p>22.(b)&nbsp;एक-एक करके विकल्प की जाँच करके विकल्प (b) दी गई शर्त को पूरा करता है<br>पी.जी.+ पीएच.डी = अन्य <br>39% + 9% = 48%<br>48% = 48%<br>RHS = LHS</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. A shopkeeper decides to raise the marked price of an article by 40%. How much percentage discount should he allow to be able to sell the article at the original marked price?</p>",
                    question_hi: "<p>23. एक दुकानदार एक वस्तु के अंकित मूल्य में 40% की वृद्धि करने का निर्णय लेता है। वस्तु को मूल अंकित मूल्य पर बेच पाने के लिए उसे कितने प्रतिशत की छूट देनी चाहिए?</p>",
                    options_en: ["<p>28<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> %</p>", "<p>31<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>", 
                                "<p>24<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> %</p>", "<p>26<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>28<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> %</p>", "<p>31<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>",
                                "<p>24<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> %</p>", "<p>26<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>23.(a)<br>Let the MP = 100 units<br>MP after increment = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>140</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 140 units<br>Required discount = <math display=\"inline\"><mfrac><mrow><mn>140</mn><mo>-</mo><mn>100</mn></mrow><mrow><mn>140</mn></mrow></mfrac></math> &times; 100 = 2847 %</p>",
                    solution_hi: "<p>23.(a)<br>माना अंकित मूल्य = 100 इकाई <br>वृद्धि के बाद अंकित मूल्य = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>140</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 140 इकाई <br>आवश्यक छूट = <math display=\"inline\"><mfrac><mrow><mn>140</mn><mo>-</mo><mn>100</mn></mrow><mrow><mn>140</mn></mrow></mfrac></math> &times; 100 = 2847 %</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. Which of the following is a correct statement?</p>",
                    question_hi: "<p>24. निम्नलिखित में से कौन-सा कथन सही है?</p>",
                    options_en: ["<p>If two circles touch each other, the point of contact lies on the line joining the two&nbsp;centres.</p>", "<p>Equal chords are equidistant from the centre of the circle but not always subtend&nbsp;equal angle at centre of the circle</p>", 
                                "<p>The sum of the angles of a cyclic quadrilateral is always 180&deg;.</p>", "<p>Angles subtended by the arc in the same segment of the circle are in ratio of 2 : 1.</p>"],
                    options_hi: ["<p>यदि दो वृत्त एक-दूसरे को स्पर्श करते हैं, तो संपर्क बिंदु दोनों केंद्रों को जोड़ने वाली रेखा पर स्थित होता है।</p>", "<p>बराबर लंबाई वाली जीवाएं वृत्त के केंद्र से समान दूरी पर होती हैं लेकिन सदैव वृत्त के केंद्र पर समान कोण अंतरित नहीं करती हैं।</p>",
                                "<p>चक्रीय चतुर्भुज के कोणों का योग सदैव 180&deg; होता है।</p>", "<p>वृत्त के एक ही खंड में चाप द्वारा अंतरित कोणों का अनुपात 2 : 1 होता है।</p>"],
                    solution_en: "<p>24.(a)&nbsp;Only option (a) is the correct option.<br>If two circles touch each other, the point of contact lies on the line joining the two<br>centres.</p>",
                    solution_hi: "<p>24.(a)&nbsp;केवल विकल्प (a) सही विकल्प है।<br>यदि दो वृत्त एक-दूसरे को स्पर्श करते हैं, तो संपर्क बिंदु दोनों केंद्रों को जोड़ने वाली रेखा पर स्थित होता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Mohan gets 12% increase in his sale amount in the first year and 15% increase in the&nbsp;second year, with that his present sale is ₹1,28,800. What was his sale two years ago?</p>",
                    question_hi: "<p>25. मोहन को पहले वर्ष में उसकी बिक्री राशि में 12% की वृद्धि प्राप्त होती है और दूसरे वर्ष में 15% की वृद्धि प्राप्त होती है, और अब उसकी वर्तमान बिक्री ₹1,28,800 है। दो वर्ष पूर्व उसकी बिक्री कितनी थी?</p>",
                    options_en: ["<p>₹1,75,000</p>", "<p>₹1,25,000</p>", 
                                "<p>₹1,00,000</p>", "<p>₹1,50,000</p>"],
                    options_hi: ["<p>₹1,75,000</p>", "<p>₹1,25,000</p>",
                                "<p>₹1,00,000</p>", "<p>₹1,50,000</p>"],
                    solution_en: "<p>25.(c) 12 % = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, 15 % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>20</mn></mfrac></math><br>Let the sale of 2 years ago = ₹ x<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> = 128800<br>x = 100,000</p>",
                    solution_hi: "<p>25.(c) 12 % = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, 15 % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>20</mn></mfrac></math><br>माना 2 वर्ष पहले की बिक्री = ₹ x<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>25</mn></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>20</mn></mfrac></math> = 128800<br>x = 100,000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>