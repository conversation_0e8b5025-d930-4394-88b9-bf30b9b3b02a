<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following terms best describes the biological study of animal behaviour?</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन सा शब्द पशु व्यवहार के जैविक अध्ययन का सबसे अच्छा वर्णन करता है?</p>",
                    options_en: ["<p>Etiology</p>", "<p>Ethnology</p>", 
                                "<p>Entomology</p>", "<p>Ethology</p>"],
                    options_hi: ["<p>इटियोलोजी</p>", "<p>इथनोलोजी</p>",
                                "<p>एंटोमोलोजी</p>", "<p>एथोलॉजी</p>"],
                    solution_en: "<p>1.(d) <strong>Ethology</strong>. When a cause of a disease is determined then it is called <strong>etiology</strong>. The study of disease is called <strong>pathology. Entomology</strong> is the study of insects. <strong>Ethnology </strong>is the scientific study and comparison of human races.</p>",
                    solution_hi: "<p>1.(d) <strong>एथोलॉजी</strong>। जब किसी रोग का कारण निर्धारित हो जाता है तो इसे इटियोलोजी (etiology) कहा जाता है। रोग के अध्ययन को <strong>पैथोलॉजी</strong> (pathology) कहा जाता है। <strong>एंटोमोलोजी (Entomology)</strong> कीटों का अध्ययन है। <strong>इथनोलोजी (Ethnology) </strong>मानव जातियों का वैज्ञानिक अध्ययन और तुलना है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. For which of the following diseases has the U.S. FDA approved the first vaccine Dengvaxia in 2019?</p>",
                    question_hi: "<p>2. निम्नलिखित में से किस बीमारी के लिए U.S. FDA ने 2019 में पहली वैक्सीन डेंगवैक्सिया को मंजूरी दी है?</p>",
                    options_en: ["<p>Chikungunya</p>", "<p>Dengue</p>", 
                                "<p>Swine flu</p>", "<p>Cholera</p>"],
                    options_hi: ["<p>चिकनगुनिया</p>", "<p>डेंगू</p>",
                                "<p>स्वाइन फ्लू</p>", "<p>हैज़ा</p>"],
                    solution_en: "<p>2.(b) <strong>Dengue. Dengvaxia</strong> is approved for use in individuals aged 9&ndash;16 years of age with laboratory confirmed previous <strong>dengue </strong>infection and living in endemic areas. Dengue viruses are spread to people through the bite of an infected Aedes species mosquito. <strong>Chikungunya </strong>virus is spread to people by the bite of an infected mosquito. <strong>Swine flu (H1N1 virus): </strong>A human respiratory infection caused by an influenza strain that started in pigs. <strong>Cholera </strong>is a bacterial disease usually spread through contaminated water.</p>",
                    solution_hi: "<p>2.(b) <strong>डेंगू । डेंगवैक्सिया</strong> को 9-16 वर्ष की आयु के उन व्यक्तियों में उपयोग के लिए अनुमोदित किया गया है, जिनकी प्रयोगशाला में पिछले डेंगू संक्रमण की पुष्टि हुई है और जो स्थानिक क्षेत्रों में रहते हैं। <strong>डेंगू </strong>वायरस संक्रमित एडीज प्रजाति के मच्छर के काटने से लोगों में फैलता है। चिकनगुनिया वायरस संक्रमित मच्छर के काटने से लोगों में फैलता है। <strong>स्वाइन फ़्लू (H1N1 वायरस):</strong> इन्फ्लूएंजा स्ट्रेन के कारण होने वाला एक मानव श्वसन संक्रमण जो सूअरों में शुरू हुआ। <strong>हैजा </strong>एक जीवाणुजन्य रोग है जो आमतौर पर दूषित जल से फैलता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The study of birds is called _______.</p>",
                    question_hi: "<p>3. पक्षियों के अध्ययन को क्या कहते हैं?</p>",
                    options_en: ["<p>Herpetology</p>", "<p>Ornithology</p>", 
                                "<p>Anthropology</p>", "<p>Ophthalmology</p>"],
                    options_hi: ["<p>सरीसृप विज्ञान (Herpetology)</p>", "<p>पक्षीविज्ञान (Ornithology)</p>",
                                "<p>नृविज्ञान (Anthropology)</p>", "<p>नेत्र विज्ञान (Ophthalmology)</p>"],
                    solution_en: "<p>3.(b) <strong>Ornithology. Herpetology - </strong>The study of reptiles and amphibians. <strong>Anthropology </strong>- The study of the origin and development of human societies and cultures. <strong>Ophthalmology </strong>- The study of medical conditions relating to the eye.</p>",
                    solution_hi: "<p>3.(b) <strong>पक्षीविज्ञान(Ornithology)</strong>। <strong>सरीसृप विज्ञान (Herpetology) - </strong>सरीसृपों और उभयचरों का अध्ययन। <strong>मानवशास्त्र (Anthropology) -</strong> मानव समाज और संस्कृतियों की उत्पत्ति और विकास का अध्ययन।<strong> नेत्र विज्ञान(Ophthalmology) - </strong>आँख से संबंधित चिकित्सा स्थितियों का अध्ययन।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The approach based on the idea that &lsquo;organisms which are more distantly related will accumulate a greater number of differences in their DNA&rsquo; is called?</p>",
                    question_hi: "<p>4. इस विचार पर आधारित दृष्टिकोण कि \'जो जीव अधिक दूर से संबंधित हैं, उनके DNA में अधिक संख्या में अंतर जमा हो जाएंगे\' क्या कहलाता है?</p>",
                    options_en: ["<p>The DNA study</p>", "<p>Morphological approach</p>", 
                                "<p>Molecular phylogeny</p>", "<p>Metamorphosis</p>"],
                    options_hi: ["<p>DNA अध्ययन</p>", "<p>रूपात्मक दृष्टिकोण</p>",
                                "<p>आणविक जातिवृत्त</p>", "<p>कायांतरण</p>"],
                    solution_en: "<p>4.(c) <strong>Molecular phylogeny -</strong> The branch of phylogeny that uses hereditary molecular differences in Deoxyribonucleic acid (DNA) sequences and genetics to determine the evolutionary relationship of an organism. <strong>Metamorphosis</strong> - A biological process by which animals undergo extreme, rapid physical changes some time after birth. <strong>Deoxyribonucleic acid</strong> (DNA) - The molecule that carries genetic information for the development and functioning of an organism.</p>",
                    solution_hi: "<p>4.(c) <strong>आणविक जातिवृत्त - </strong>फ़ाइलोजेनी की शाखा जो किसी जीव के विकासवादी संबंध को निर्धारित करने के लिए डीऑक्सीराइबोन्यूक्लिक एसिड (DNA) अनुक्रमों और आनुवंशिकी में वंशानुगत आणविक अंतर का उपयोग करती है। <strong>कायांतरण</strong>- एक जैविक प्रक्रिया, जिसके द्वारा जानवरों में जन्म के कुछ समय बाद अत्यधिक तीव्र शारीरिक परिवर्तन होते हैं। <strong>डीऑक्सीराइबोन्यूक्लिक एसिड</strong> (DNA) - वह अणु जो किसी जीव के विकास और कार्य पद्धति के लिए आनुवंशिक जानकारी रखता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. ___________ is the branch of science that aims at improving the genetic quality of the human population.</p>",
                    question_hi: "<p>5. ___________ विज्ञान की वह शाखा है जिसका उद्देश्य मानव जनसंख्या की आनुवंशिक गुणवत्ता में सुधार करना है।</p>",
                    options_en: ["<p>Biotechnology</p>", "<p>Genetics</p>", 
                                "<p>Eugenics</p>", "<p>Epigenetics</p>"],
                    options_hi: ["<p>जैव प्रौद्योगिकी</p>", "<p>आनुवंशिकी</p>",
                                "<p>सुजनन विज्ञान</p>", "<p>एपिजेनेटिक्स</p>"],
                    solution_en: "<p>5.(c) <strong>Eugenics</strong>. Biotechnology - utilizes biological systems, living organisms or parts of this to develop or create different products. Genetics - the study of heredity, the process in which a. parents pass certain genes onto their children.&rdquo; Epigenetics - a process involving changes in the function and expression of the genes.</p>",
                    solution_hi: "<p>5.(c) <strong>सुजनन विज्ञान</strong> (Eugenics)। जैव प्रौद्योगिकी - विभिन्न उत्पादों को विकसित करने या बनाने के लिए जैविक प्रणालियों, जीवित जीवों या इसके कुछ हिस्सों का उपयोग करती है। आनुवंशिकी - आनुवंशिकता का अध्ययन, वह प्रक्रिया जिसमें माता-पिता अपने बच्चों में कुछ जीन जारी करते हैं।&rdquo; एपिजेनेटिक्स - जीन के कार्य और अभिव्यक्ति में परिवर्तन से जुड़ी एक प्रक्रिया है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. &lsquo;Hydroponics&rsquo; refers to the kind of farming in which:</p>",
                    question_hi: "<p>6. \'हाइड्रोपोनिक्स\' उस प्रकार की खेती को संदर्भित करता है जिसमें ______।</p>",
                    options_en: ["<p>Farming is done using machines</p>", "<p>Crops are planted on large estates</p>", 
                                "<p>Plants are grown in mineral nutrient solutions and without soil</p>", "<p>Cultivation of crops and rearing of animals are done together.</p>"],
                    options_hi: ["<p>मशीनों का उपयोग करके खेती की जाती है</p>", "<p>बड़े-बड़े भू-भागों में फसलें बोई जाती हैं।</p>",
                                "<p>पौधों को खनिज पोषक तत्वों के घोल में और बिना मिट्टी के उगाया जाता है</p>", "<p>फसलों की खेती और पशुओं का पालन-पोषण एक साथ किया जाता है</p>"],
                    solution_en: "<p>6.(c) <strong>Hydroponics </strong>refers to the kind of farming in which plants are grown in mineral nutrient solutions and without soil. Plants grown by Hydroponics: Tomatoes, Lettuce, Cucumber, Kale, Spinach, Strawberries, Peppers (Capsicum).</p>",
                    solution_hi: "<p>6.(c) <strong>हाइड्रोपोनिक्स </strong>उस प्रकार की खेती को संदर्भित करता है जिसमें पौधों को खनिज पोषक तत्वों के घोल में और बिना मिट्टी के उगाया जाता है। हाइड्रोपोनिक्स द्वारा उगाए गए पौधे: टमाटर, सलादपत्ता , ककड़ी, केल, पालक, स्ट्रॉबेरी, मिर्च (शिमला मिर्च)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. The technology that uses the reflection of ultrasound waves to produce images of the heart is known as :</p>",
                    question_hi: "<p>7. दिल की छवियों को बनाने के लिए अल्ट्रासाउंड तरंगों के प्रतिबिंब का उपयोग करने वाली तकनीक को कहा जाता है</p>",
                    options_en: ["<p>echocardiogram</p>", "<p>echocardiography</p>", 
                                "<p>ultrasonography</p>", "<p>sonography</p>"],
                    options_hi: ["<p>इकोकार्डियोग्राम</p>", "<p>इकोकार्डियोग्राफी</p>",
                                "<p>अल्ट्रासोनोग्राफी</p>", "<p>सोनोग्राफ़ी</p>"],
                    solution_en: "<p>7.(b) <strong>Echocardiography. Ultrasonography</strong> is a diagnostic imaging technique that uses high-frequency sound waves to produce images of internal body structures. <strong>Sonography </strong>is a medical imaging technique that uses ultrasound waves to create visual images of the body\'s internal structures.</p>",
                    solution_hi: "<p>7.(b) <strong>इकोकार्डियोग्राफी। अल्ट्रासोनोग्राफी</strong> एक नैदानिक ​​इमेजिंग तकनीक है जो आंतरिक शरीर संरचनाओं की छवियां बनाने के लिए उच्च आवृत्ति ध्वनि तरंगों का उपयोग करती है। सोनोग्राफी एक चिकित्सा इमेजिंग तकनीक है जो शरीर की आंतरिक संरचनाओं की दृश्य छवियां बनाने के लिए अल्ट्रासाउंड तरंगों का उपयोग करती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which antibiotic is considered the first miracle drug, used to treat throat infections, meningitis, syphilis and other bacterial infections?</p>",
                    question_hi: "<p>8. किस एंटीबायोटिक को पहली चमत्कारिक दवा माना जाता है, जिसका उपयोग गले के संक्रमण, मस्तिष्क ज्वर (meningitis), सिफलिस (syphilis) और अन्य जीवाणु संक्रमण के इलाज के लिए किया जाता है?</p>",
                    options_en: ["<p>Aciclovir</p>", "<p>Nifuratel</p>", 
                                "<p>Penicillin</p>", "<p>Metronidazole</p>"],
                    options_hi: ["<p>असिक्लोविर ( Aciclovir)</p>", "<p>निफुराटेल (Nifuratel)</p>",
                                "<p>पेनिसिलिन (Penicillin)</p>", "<p>मेट्रोनिडाज़ोल (Metronidazole)</p>"],
                    solution_en: "<p>8.(c) <strong>Penicillin </strong>was discovered by Alexander Fleming. <strong>Acyclovir </strong>is used for the treatment of herpes simplex virus infections, chickenpox, and shingles. <strong>Nifuratel</strong> is a drug used in gynecology, used in the treatment of leucorrhea, vulvovaginal infections, and urinary tract infections. <strong>Metronidazole </strong>is used to treat trichomoniasis, amebiasis, inflammatory lesions of rosacea, and bacterial infections and prevent postoperative infections.</p>",
                    solution_hi: "<p>8.(c) <strong>पेनिसिलिन </strong>(Penicillin) की खोज अलेक्जेंडर फ्लेमिंग ने की थी। <strong>एसाइक्लोविर </strong>का उपयोग हर्पीस सिम्प्लेक्स वायरस संक्रमण, चेचक और दाद के उपचार के लिए किया जाता है। <strong>निफुराटेल </strong>स्त्री रोग में उपयोग की जाने वाली एक दवा है, जिसका उपयोग लिकोरिया, योनि संक्रमण और मूत्र मार्ग के संक्रमण के उपचार में किया जाता है। <strong>मेट्रोनिडाजोल </strong>का उपयोग ट्राइकोमोनिएसिस, अमीबियासिस, रोसैसिया के सूजन वाले घावों और जीवाणु संक्रमण को रोकने के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. In 1869, who named a new term &lsquo;Oecologie&rsquo; that defined the study of the relationship of organisms with their environment?</p>",
                    question_hi: "<p>9. 1869 में, एक नए शब्द \'ओकोलॉजी\' (Oecologie) का नाम किसने रखा, जो जीवों के उनके पर्यावरण के साथ संबंधों के अध्ययन को परिभाषित करता है ?</p>",
                    options_en: ["<p>Charles Elton</p>", "<p>Ernst Haeckel</p>", 
                                "<p>Eugene Odum</p>", "<p>Arthur Tansley</p>"],
                    options_hi: ["<p>चार्ल्स एल्टन</p>", "<p>अर्न्स्ट हेकेल</p>",
                                "<p>यूजीन ओडुम</p>", "<p>आर्थर टैन्सले</p>"],
                    solution_en: "<p>9.(b) <strong>Ernst Haeckel.</strong> Eugene Odum is the father of modern ecology.</p>",
                    solution_hi: "<p>9.(b) <strong>अर्नस्ट हेकेल।</strong> यूजीन ओडुम आधुनिक पारिस्थितिकी के जनक हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following is the study of insects and their relationship to humans, the environment, and other organisms?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन सा कीड़ों और उनके इंसानों, पर्यावरण और अन्य जीवों से संबंध का अध्ययन है?</p>",
                    options_en: ["<p>Ornithology</p>", "<p>Entomology</p>", 
                                "<p>Ethology</p>", "<p>Mycology</p>"],
                    options_hi: ["<p>पक्षीविज्ञान</p>", "<p>कीटविज्ञान</p>",
                                "<p>आचारविज्ञान</p>", "<p>माइकोलॉजी</p>"],
                    solution_en: "<p>10.(b)<strong> Entomology. Ornithology, </strong>a branch of zoology dealing with the study of birds. <strong>Ethology </strong>is the study of animal behavior under natural conditions.</p>",
                    solution_hi: "<p>10.(b) <strong>एंटोमोलॉजी </strong>(Entomology)। <strong>ऑर्निथोलॉजी </strong>(Ornithology), पक्षियों के अध्ययन से संबंधित प्राणी विज्ञान की एक शाखा है। <strong>एथोलॉजी </strong>(Ethology) प्राकृतिक परिस्थितियों में जानवरों के व्यवहार का अध्ययन है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. The branch of horticulture which deals with the production, storage, processing and marketing of vegetables is called_____.</p>",
                    question_hi: "<p>11. बागवानी की वह शाखा जो सब्जियों के उत्पादन, भंडारण, प्रसंस्करण और विपणन से संबंधित है, _____ कहलाती है।</p>",
                    options_en: ["<p>Pomology</p>", "<p>agronomy</p>", 
                                "<p>olericulture</p>", "<p>apiculture</p>"],
                    options_hi: ["<p>पोमोलॉजी</p>", "<p>कृषि विज्ञान</p>",
                                "<p>ओलेरीकल्चर</p>", "<p>मधुमक्खी पालन</p>"],
                    solution_en: "<p>11.(c) <strong>Olericulture. Pomology</strong> is the science that deals with fruits and fruit growing.</p>",
                    solution_hi: "<p>11.(c) <strong>ओलेरीकल्चर। पोमोलॉजी</strong> वह विज्ञान है जो फल और फल उगाने से संबंधित है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. What does Ichthyology deal with?</p>",
                    question_hi: "<p>12. इचिथोलॉजी किससे संबंधित है?</p>",
                    options_en: ["<p>Study of reptiles and amphibians</p>", "<p>Study of fish</p>", 
                                "<p>Study of birds</p>", "<p>Study of insects</p>"],
                    options_hi: ["<p>सरीसृप का अध्ययन</p>", "<p>मछली का अध्ययन</p>",
                                "<p>पक्षियों का अध्ययन</p>", "<p>कीड़ों का अध्ययन</p>"],
                    solution_en: "<p>12.(b) <strong>Study of fish.</strong> Study of reptiles and amphibians : Herpetology. Study of birds : Ornithology. Study of insects : Entomology.</p>",
                    solution_hi: "<p>12.(b)<strong> मछली का अध्ययन। </strong>सरीसृपों और उभयचरों का अध्ययन : हर्पेटोलॉजी। पक्षियों का अध्ययन : पक्षीविज्ञान। कीड़ों का अध्ययन : कीटविज्ञान।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. What is Agraphia?</p>",
                    question_hi: "<p>13. एग्रफिया क्या है?</p>",
                    options_en: ["<p>Loss of the ability to hear</p>", "<p>Loss of the ability to speak</p>", 
                                "<p>Loss of the ability to write</p>", "<p>Loss of the ability to understand</p>"],
                    options_hi: ["<p>सुनने की क्षमता का अभाव</p>", "<p>बोलने की क्षमता का अभाव</p>",
                                "<p>लिखने की क्षमता का अभाव</p>", "<p>समझने की क्षमता का अभाव</p>"],
                    solution_en: "<p>13.(c) <strong>Loss of the ability to write.</strong> Loss of the ability to hear - Presbycusis. Loss of the ability to speak - Aphasia .</p>",
                    solution_hi: "<p>13.(c) <strong>लिखने की क्षमता का अभाव। </strong>सुनने की क्षमता की कमी - प्रेस्बीकोसिस (Presbycusis) । बोलने की क्षमता की कमी -&nbsp;एफसीआ (Aphasia).</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. ______ is the field of study concentrated on pregnancy, childbirth and the postpartum period.</p>",
                    question_hi: "<p>14. ______ गर्भावस्था, प्रसव और प्रसवोत्तर अवधि पर केंद्रित अध्ययन का क्षेत्र है।</p>",
                    options_en: ["<p>Ophthalmology</p>", "<p>Oncology</p>", 
                                "<p>Obstetrics</p>", "<p>Orthopedics</p>"],
                    options_hi: ["<p>नेत्र विज्ञान</p>", "<p>ऑन्कोलॉजी</p>",
                                "<p>प्रसूति विज्ञान</p>", "<p>विकलदंत विज्ञान</p>"],
                    solution_en: "<p>14.(c) <strong>Obstetrics. Ophthalmology</strong> is a branch of medicine and surgery which deals with the diagnosis and treatment of eye disorders. <strong>Oncology </strong>is the branch of medicine that researches, identifies and treats cancer.</p>",
                    solution_hi: "<p>14.(c) <strong>प्रसूति विज्ञान।</strong> <strong>नेत्र विज्ञान</strong> चिकित्सा और सर्जरी की एक शाखा है जो नेत्र विकारों के निदान और उपचार से संबंधित है। <strong>ऑन्कोलॉजी </strong>चिकित्सा की वह शाखा है जो कैंसर पर शोध, पहचान और उपचार करती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The scientific study of dreams is called ______.</p>",
                    question_hi: "<p>15. सपनों के वैज्ञानिक अध्ययन को _________ कहा जाता है।</p>",
                    options_en: ["<p>morphology</p>", "<p>oneirology</p>", 
                                "<p>kalology</p>", "<p>entomology</p>"],
                    options_hi: ["<p>आकारिकी</p>", "<p>ओनेइरोलोजी</p>",
                                "<p>कैलोलॉजी</p>", "<p>कीटविज्ञान</p>"],
                    solution_en: "<p>15.(b) <strong>Oneirology. Morphology - </strong>The study of the form and structure of organisms and their specific structural features. <strong>Kalology </strong>: Study of facial beauty.</p>",
                    solution_hi: "<p>15.(b) <strong>ओनेइरोलोजी। आकारिकी - </strong>जीवों के स्वरूप एवं संरचना तथा उनकी विशिष्ट संरचनात्मक विशेषताओं का अध्ययन। <strong>कैलोलॉजी </strong>: चेहरे की सुंदरता का अध्ययन।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>