<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Who discovered the law of planetary motion?</p>",
                    question_hi: "<p>1. ग्रहों की गति के नियम की खोज किसने की थी ?</p>",
                    options_en: ["<p>Newton</p>", "<p>Kepler</p>", 
                                "<p>Einstein</p>", "<p>Galileo</p>"],
                    options_hi: ["<p>न्यूटन</p>", "<p>केप्लर</p>",
                                "<p>आइंस्टीन</p>", "<p>गैलीलियो</p>"],
                    solution_en: "<p>1.(b) <strong>Kepler</strong>. Kepler&rsquo;s three laws of planetary motion describes the motion of planets around the sun. First law (law of orbits) - All the planets revolve around the sun in elliptical orbits having the sun at one of the foci, Second law (law of equal areas) - The radius vector drawn from the sun to the planet sweeps out equal areas in equal intervals of time, Third law (law of periods) - The square of the time period of revolution of a planet around the sun in an elliptical orbit is directly proportional to the cube of its semi-major axis.</p>",
                    solution_hi: "<p>1.(b) <strong>केप्लर। केप्लर</strong> के ग्रहों की गति के तीन नियम सूर्य के चारों ओर ग्रहों की गति का वर्णन करते हैं। प्रथम नियम (कक्षाओं का नियम) - सभी ग्रह सूर्य के चारों ओर दीर्घवृत्ताकार कक्षा में चक्कर लगाते हैं, जिसमें सूर्य एक फोकस पर होता है, द्वितीय नियम (समान क्षेत्रों का नियम) - सूर्य से ग्रह तक खींचा गया त्रिज्य वेक्टर समय के समान अंतराल में समान क्षेत्रों को विस्तारित करता है, तृतीय नियम (समयावधि का नियम) - एक दीर्घवृत्ताकार कक्षा में सूर्य के चारों ओर किसी ग्रह की परिक्रमा की समयावधि का वर्ग उसके अर्ध-प्रमुख धूरी के घन के अनुक्रमानुपाती होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Who among the following noticed the deflection of the compass needle every time current was passed through the wire?</p>",
                    question_hi: "<p>2. निम्नलिखित में से किसने हर बार तार से करंट प्रवाहित होने पर कंपास सुई के विक्षेपण को देखा?</p>",
                    options_en: ["<p>C V Raman</p>", "<p>William Sturgeon</p>", 
                                "<p>Thomas Alva Edison</p>", "<p>Hans Christian Oersted</p>"],
                    options_hi: ["<p>सी. वी. रमन</p>", "<p>विलियम स्टर्जन</p>",
                                "<p>थॉमस अल्वा एडिसन</p>", "<p>हैन्स क्रिश्चियन ओर्स्टेड</p>"],
                    solution_en: "<p>2.(d) <strong>Hans Christian Oersted.</strong> The deflection in the needle becomes opposite when the direction of the current is reversed. He discovered electromagnetism in 1820. <strong>Oersted\'s Law -</strong> Moving charges or currents produced a magnetic field in the surrounding space. SI units: Current - Ampere (A), Magnetic field - Tesla (T).</p>",
                    solution_hi: "<p>2.(d) <strong>हैन्स क्रिश्चियन ओर्स्टेड।</strong> धारा की दिशा विपरीत करने पर सुई में विक्षेपण विपरीत हो जाता है। उन्होंने 1820 में विद्युत चुंबकत्व की खोज की। <strong>ओर्स्टेड का नियम -</strong> गतिमान आवेशों या धाराओं के आसपास के स्थान में एक चुंबकीय क्षेत्र उत्पन्न होता है। SI मात्रक : धारा - एम्पीयर (A), चुंबकीय क्षेत्र - टेस्ला (T)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. Which device was invented by Carl Friedrich Gauss and Wilhelm Eduard Weber in 1833?</p>",
                    question_hi: "<p>3. 1833 में, कार्ल फ्रेडरिक गॉस और विल्हेम एडवर्ड वेबर ने किस उपकरण का आविष्कार किया था?</p>",
                    options_en: ["<p>Transistor</p>", "<p>electromagnetic telegraph</p>", 
                                "<p>Optical maser</p>", "<p>Particle accelerator</p>"],
                    options_hi: ["<p>ट्रांजिस्टर</p>", "<p>विद्युत चुम्बकीय टेलीग्राफ</p>",
                                "<p>प्रकाशीय मेसर</p>", "<p>कण त्वरक</p>"],
                    solution_en: "<p>3.(b) <strong>Electromagnetic telegraph.</strong> Inventions and discoveries : Henry Moseley - The atomic battery. Pierre and Marie Curie - Radium. Percy Spencer - Microwave. David Hughes and Thomas Edison - Carbon Microphone. John Bardeen, Walter Brattain and William Shockley - Transistor. Wilhelm Conrad Roentgen - X Ray. Victor Francis Hess - Cosmic radiation.</p>",
                    solution_hi: "<p>3.(b) <strong>विद्युत चुम्बकीय टेलीग्राफ।</strong> आविष्कार और खोजें : हेनरी मोजले - परमाणु बैटरी। पियरे और मैरी क्यूरी - रेडियम। पर्सी स्पेंसर - माइक्रोवेव। डेविड ह्यूजेस और थॉमस एडिसन - कार्बन माइक्रोफोन। जॉन बार्डीन, वाल्टर ब्रैटन और विलियम शॉक्ले - ट्रांजिस्टर। विल्हेम कॉन्राड रोएंटजेन-एक्स-रे। विक्टर फ्रांसिस हेस - ब्रह्मांडीय विकिरण।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. When did Heinrich Hertz discover the photoelectric effect and observe that shining ultraviolet light on the electrodes caused a change in voltage between them?</p>",
                    question_hi: "<p>4. हेनरिक हेटर्स (Heinrich Hertz) ने कब फोटोइलेक्ट्रिक प्रभाव की खोज की, और यह पाया कि इलेक्ट्रोडों पर चमकदार पराबैंगनी प्रकाश, उनके बीच वोल्टेज में परिवर्तन का कारण बनता है ?</p>",
                    options_en: ["<p>1990</p>", "<p>1916</p>", 
                                "<p>1902</p>", "<p>1887</p>"],
                    options_hi: ["<p>1990 में</p>", "<p>1916 में</p>",
                                "<p>1902 में</p>", "<p>1887 में</p>"],
                    solution_en: "<p>4.(d) <strong>1887</strong>. Photoelectric effect - The emission of electrons from the surface of a metal when light is incident on it. Scientists and their discoveries : Michael Faraday - Electromagnetic Induction. Thomas Edison - Incandescent light bulb, phonograph. Albert Einstein - Theory of Relativity.</p>",
                    solution_hi: "<p>4.(d) <strong>1887 में ।</strong> फोटोइलेक्ट्रिक प्रभाव - किसी धातु पर प्रकाश पड़ने पर उसकी सतह से इलेक्ट्रॉनों का उत्सर्जन होता है। वैज्ञानिक और उनकी खोजें : माइकल फैराडे - विद्युत चुम्बकीय प्रेरण। थॉमस एडिसन - तापदीप्त प्रकाश बल्ब, फोनोग्राफ। अल्बर्ट आइंस्टीन - सापेक्षता का सिद्धांत।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Who among the following established a unit of horsepower that is equal to one horse doing 33,000 foot-pounds of work in one minute?</p>",
                    question_hi: "<p>5. निम्नलिखित में से किसने अश्वशक्ति (horsepower) की एक इकाई स्थापित की जो एक घोड़े द्वारा एक मिनट में 33,000 फुट-पाउंड काम (33,000 foot-pounds of work) करने के बराबर है?</p>",
                    options_en: ["<p>James Black</p>", "<p>James Croll</p>", 
                                "<p>James Watt</p>", "<p>James Dwight Dana</p>"],
                    options_hi: ["<p>जेम्स ब्लैक</p>", "<p>जेम्स क्रॉल</p>",
                                "<p>जेम्स वॉट</p>", "<p>जेम्स ड्वाइट डाना</p>"],
                    solution_en: "<p>5.(c) <strong>James Watt</strong> (Inventor of steam Engine). <strong>Horsepower </strong>: Unit of measurement of power, or the rate at which work is done, usually in reference to the output of engines or motors. It is calculated by multiplying the amount of force (in pounds) by the speed (in feet per second). 1 Hp - 746 watts (W) or 0.746 kilowatts (kW).</p>",
                    solution_hi: "<p>5.(c) <strong>जेम्स वाट</strong> (भाप इंजन के आविष्कारक)। <strong>अश्वशक्ति </strong>: शक्ति के माप की इकाई, या वह दर जिस पर काम किया जाता है, आमतौर पर इंजन या मोटर के आउटपुट के संदर्भ में। इसकी गणना बल की मात्रा (पाउंड में) को गति (फीट प्रति सेकंड में) से गुणा करके की जाती है। 1 Hp - 746 वाट (W) या 0.746 किलोवाट (kW)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which physicist\'s experiment during a lecture in 1820 showed the connection between electricity and magnetism ?</p>",
                    question_hi: "<p>6. 1820 में एक प्रयोग के दौरान किस भौतिक विज्ञानी के प्रयोग ने विद्युत (electricity) और चुंबकत्व (magnetism) के बीच संबंध को दर्शाया?</p>",
                    options_en: ["<p>John Cockcroft</p>", "<p>William Gilbert</p>", 
                                "<p>Heinrich Hertz</p>", "<p>Hans Christian Oersted</p>"],
                    options_hi: ["<p>जॉन कॉकक्रॉफ्ट (John Cockcroft)</p>", "<p>विलियम गिल्बर्ट (William Gilbert)</p>",
                                "<p>हेनरिक हर्ट्ज़ (Heinrich Hertz)</p>", "<p>हैंस क्रिश्चियन ऑस्टैंड (Hans Christian Oersted)</p>"],
                    solution_en: "<p>6.(d) <strong>Hans Christian Oersted</strong>. John Cockcroft and Ernest Walton shared the Nobel Prize (1951) in Physics for the &ldquo;transmutation of atomic nuclei by artificially accelerated atomic particles&rdquo;. William Gilbert - Invented the Electroscope. Heinrich Hertz - Discovered the photoelectric effect.</p>",
                    solution_hi: "<p>6.(d) <strong>हैंस क्रिश्चियन ऑस्टैंड</strong> (Hans Christian Oersted)। जॉन कॉकक्रॉफ्ट और अर्नेस्ट वाल्टन को \"कृत्रिम रूप से त्वरित परमाणु कणों द्वारा परमाणु नाभिक के रूपांतरण\" के लिए भौतिकी में नोबेल पुरस्कार (1951) को साझा किया गया था। विलियम गिल्बर्ट - इलेक्ट्रोस्कोप का आविष्कार किया था। हेनरिक हर्ट्ज़ - फोटोइलेक्ट्रिक प्रभाव की खोज की।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Who was famous for publishing a formula in 1827 that shows the mathematical relationship between current, resistance and voltage?</p>",
                    question_hi: "<p>7. कौन 1827 में एक सूत्र प्रकाशित करने के लिए प्रसिद्ध था जो विद्युत धारा, प्रतिरोध और वोल्टता के बीच एक गणितीय संबंध दर्शाता है?</p>",
                    options_en: ["<p>William Gilbert</p>", "<p>William Watson</p>", 
                                "<p>Gustav Kirchhoff</p>", "<p>Georg Simon Ohm</p>"],
                    options_hi: ["<p>विलियम गिल्बर्ट (William Gilbert)</p>", "<p>विलियम वॉटसन (William Watson)</p>",
                                "<p>गुस्ताव किरचॉफ (Gustav Kirchhoff)</p>", "<p>जॉर्ज साइमन ओम (Georg Simon Ohm)</p>"],
                    solution_en: "<p>7.(d) <strong>Georg Simon Ohm.</strong> Ohm\'s Law : Voltage (V) = Current (I) x Resistance (R). William Gilbert created the science of magnetism and discovered that the Earth is a magnet. William Watson introduced the Leyden jar. Gustav Kirchhoff and Robert Bunsen discovered two alkali metals, caesium and rubidium.</p>",
                    solution_hi: "<p>7.(d)<strong> जॉर्ज साइमन ओम। </strong>ओम का नियम : विभवान्तर (V) = धारा (I) x प्रतिरोध (R)। विलियम गिल्बर्ट ने चुंबकत्व के विज्ञान का पता लगाया कि पृथ्वी एक चुंबक है। विलियम वॉटसन ने लेडेन जार (Leyden jar) पेश किया। गुस्ताव किरचॉफ और रॉबर्ट बुन्सन ने दो क्षार धातुओं, सीज़ियम और रुबिडियम की खोज की।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which physicist is known for discovering that any periodic wave can be represented as an infinite number of weighted sinusoids, i.e., the sum of sine and cosine waves?</p>",
                    question_hi: "<p>8. किस भौतिक विज्ञानी को यह पता लगाने के लिए जाना जाता है कि किसी भी आवर्ती तरंग को भारित आवृतियों की अनंत संख्या, अर्थात ज्या और कोज्या तरंगों के योग के रूप में व्यक्त किया जा सकता है</p>",
                    options_en: ["<p>Louis de Broglie</p>", "<p>Jean Baptiste Joseph Fourier</p>", 
                                "<p>Joseph-Louis Lagrange</p>", "<p>Ernest Walton</p>"],
                    options_hi: ["<p>लुईस डी ब्रोग्ली</p>", "<p>जीन बापटिस्ट जोसेफ फूरिए</p>",
                                "<p>जोसेफ लुई लैग्रेंज</p>", "<p>अर्नेस्ट वाल्टन</p>"],
                    solution_en: "<p>8.(b) <strong>Jean Baptiste Joseph Fourier :</strong> Discovered &ldquo;Green house effect&rdquo; in 1824.<strong> Louis de Broglie</strong> : He was awarded the 1929 Nobel Prize in Physics &ldquo;for his discovery of the wave nature of electrons&rdquo;. <strong>Joseph-Louis Lagrange :</strong> Contributed to the development of celestial mechanics, calculus, algebra, number theory, and group theory. <strong>Ernest Walton </strong>: Won the 1951 Nobel Prize in Physics with his colleague John Cockcroft for producing the first artificial nuclear disintegration in history.</p>",
                    solution_hi: "<p>8.(b) <strong>जीन बापटिस्ट जोसेफ फूरिए :</strong> 1824 में \"ग्रीन हाउस प्रभाव\" की खोज की। <strong>लुईस डी ब्रोग्ली : </strong>इन्हें \"इलेक्ट्रॉनों की तरंग प्रकृति की खोज के लिए\" भौतिकी में 1929 के नोबेल पुरस्कार से सम्मानित किया गया था। <strong>जोसेफ-लुई लैग्रेंज : </strong>आकाशीय यांत्रिकी, कैलकुलस, बीजगणित, संख्या सिद्धांत और समूह सिद्धांत के विकास में योगदान दिया। <strong>अर्नेस्ट वाल्टन :</strong> इतिहास में पहला कृत्रिम परमाणु विघटन उत्पन्न करने के लिए अपने सहयोगी जॉन कॉक्रॉफ़्ट के साथ भौतिकी में 1951 का नोबेल पुरस्कार जीता।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. In 1830, which American scientist created the world\'s most powerful electromagnet, the Albany magnet, which could lift up to 750 pounds of metal at a time?</p>",
                    question_hi: "<p>9. 1830 में, किस अमेरिकी वैज्ञानिक ने दुनिया का सबसे शक्तिशाली विद्युत चुंबक, अल्बानी चुंबक (Albany magnet) बनाया, जो एक समय में 750 पाउंड तक धातु उठा सकता था?</p>",
                    options_en: ["<p>John Cockcroft</p>", "<p>William Gilbert</p>", 
                                "<p>Joseph Henry</p>", "<p>Edward Purcell</p>"],
                    options_hi: ["<p>जॉन कॉक्रॉफ़्ट (John Cockcroft)</p>", "<p>विलियम गिल्बर्ट (William Gilbert)</p>",
                                "<p>जोसेफ हेनरी (Joseph Henry)</p>", "<p>एडवर्ड परसेल (Edward Purcell)</p>"],
                    solution_en: "<p>9.(c) <strong>Joseph Henry</strong>, renowned for his discovery of self-inductance in electric circuits and his work on electromagnets like Albany and Yale magnets. <strong>John Cockcroft </strong>was a British physicist, who won the Nobel Prize (1951) in Physics for splitting the atomic nucleus. <strong>William Gilbert -</strong> the inventor of the term \"electricity\".<strong> Edward Purcell,</strong> an American physicist, won the Nobel Prize (1952) for Physics for discovery of nuclear magnetic resonance in liquids and solids.</p>",
                    solution_hi: "<p>9.(c) <strong>जोसेफ हेनरी,</strong> विद्युत् परिपथ में स्व-प्रेरकत्व की खोज और अल्बानी तथा येल मैग्नेट जैसे विद्युत् चुम्बकत्व पर अपने काम के लिए प्रसिद्ध हैं। <strong>जॉन कॉक्रॉफ़्ट </strong>एक ब्रिटिश भौतिक विज्ञानी थे, जिन्होंने परमाणु नाभिक को विभाजित करने के लिए भौतिकी में नोबेल पुरस्कार (1951) जीता था। <strong>विलियम गिल्बर्ट -</strong> \"विद्युत\" शब्द के आविष्कारक। <strong>एडवर्ड परसेल</strong>, एक अमेरिकी भौतिक विज्ञानी, जिन्होंने द्रव और ठोस पदार्थों में परमाणु चुंबकीय अनुनाद की खोज के लिए भौतिकी में नोबेल पुरस्कार (1952) जीता।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. In 1851, who discovered the phenomenon of Eddy currents which flow in closed loops within conductors in planes perpendicular to the magnetic field?</p>",
                    question_hi: "<p>10. 1851 में, किसने भंवर धाराओं की परिघटना की खोज की जो चुंबकीय क्षेत्र के लंबवत तलों में चालकों के भीतर बंद लूप में प्रवाहित होती हैं?</p>",
                    options_en: ["<p>David Hughes</p>", "<p>Charles Townes</p>", 
                                "<p>Nicola Tesla</p>", "<p>Leon Foucault</p>"],
                    options_hi: ["<p>डेविड ह्यूजेस</p>", "<p>चार्ल्स टाउन्स</p>",
                                "<p>निकोला टेस्ला</p>", "<p>लियोन फौकॉल्ट</p>"],
                    solution_en: "<p>10.(d) <strong>Leon Foucault </strong>also discovered the Gyroscope. Famous scientists and discoveries: <strong>Nikola Tesla -</strong> AC Power (alternating current), Tesla Coil, Magnifying Transmitter. <strong>Albert Einstein </strong>- Theory of relativity and the concept of mass-energy equivalence (E = mc<sup>2</sup>); <strong>J.J Thomson -</strong> Electron.<strong> Ernest Rutherford </strong>(father of nuclear physics) - Discovered alpha and beta rays, set forth the laws of radioactive decay and identified alpha particles as helium nuclei.<strong> John Dalton -</strong> Theory on atoms.</p>",
                    solution_hi: "<p>10.(d) <strong>लियोन फौकॉल्ट</strong> ने जाइरोस्कोप की भी खोज की। प्रसिद्ध वैज्ञानिक और खोज: <strong>निकोला टेस्ला</strong> - ए.सी. पावर (प्रत्यावर्ती धारा), टेस्ला कॉइल (कुंडली), आवर्धक ट्रांसमीटर। <strong>अल्बर्ट आइंस्टीन -</strong> सापेक्षता का सिद्धांत और द्रव्यमान-ऊर्जा समतुल्यता का सिद्धांत (E = mc<sup>2</sup>); <strong>जे. जे. थॉमसन -</strong> इलेक्ट्रॉन। <strong>अर्नेस्ट रदरफोर्ड </strong>(नाभिकीय भौतिकी के जनक) - अल्फा और बीटा किरणों की खोज की, रेडियोधर्मी क्षय के नियम बताए और अल्फा कणों को हीलियम नाभिक के रूप में पहचाना। <strong>जॉन डाल्टन</strong> - परमाणु सिद्धांत।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Who was awarded the Nobel Prize in Physics in 1909 along with Karl Ferdinand Braun for the development of Practical wireless telegraphy?</p>",
                    question_hi: "<p>11. निम्नलिखित में से किसे 1909 में व्यावहारिक बेतार के तार (practical wireless telegraphy) के विकास के लिए कार्ल फर्डिनेंड ब्रोन (Karl Ferdinand Braun) के साथ भौतिकी में नोबेल पुरस्कार से सम्मानित किया गया था?</p>",
                    options_en: ["<p>Heinrich Rudolf Hertz</p>", "<p>John Michell</p>", 
                                "<p>Wilhelm Rontgen</p>", "<p>Guglielmo Marconi</p>"],
                    options_hi: ["<p>हेनरिक रुडोल्फ हर्ट्ज़ (Heinrich Rudolf Hertz)</p>", "<p>जॉन मिशेल (John Michell)</p>",
                                "<p>विल्हेम रॉन्टजेन (Wilhelm Rontgen)</p>", "<p>गुगलीओं मार्कोनी (Guglielmo Marconi)</p>"],
                    solution_en: "<p>11.(d) <strong>Guglielmo Marconi</strong> (Father of Radio). His Inventions - Radio (1895), Monopole Antenna, Magnetic Detector (1902). <strong>Awards </strong>- Matteucci Medal (1901), Albert Medal (1914), IEEE Medal of Honor (1920). Other engineers who got nobel prize - Wilhelm Rontgen (1901) - The discovery of X-Rays. Pieter zeeman and Hendrik Lorentz (1902) - They discovered the influence of Magnetic upon Radiation phenomena. Albert Einstein (1921) - Discovered the Law of Photoelectric Effect.</p>",
                    solution_hi: "<p>11.(d) <strong>गुगलीओं मार्कोनी </strong>(रेडियो के जनक)। इनके आविष्कार - रेडियो (1895), मोनोपोल एंटीना, चुंबकीय डिटेक्टर (1902)। <strong>पुरस्कार </strong>- मैटेयुकी मेडल (1901), अल्बर्ट मेडल (1914), IEEE मेडल ऑफ ऑनर (1920)। नोबेल पुरस्कार पाने वाले अन्य इंजीनियर - विलहम रॉन्टजेन (1901) - एक्स-रे की खोज। पीटर ज़ीमैन और हेंड्रिक लोरेंज़ (1902) - इन्होंने विकिरण घटना पर चुंबकीय प्रभाव की खोज की। अल्बर्ट आइंस्टीन (1921) - प्रकाश विद्युत प्रभाव के नियम की खोज की।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which experiment was designed to trace the motion of the earth through the \'luminiferous aether\", a theoretical substance necessary for the transmission of light?</p>",
                    question_hi: "<p>12. प्रकाश संचरण के लिए आवश्यक सैद्धांतिक पदार्थ \'प्रकाशवाही ईथर&rsquo; के माध्यम से पृथ्वी की गति का पता लगाने के लिए किस प्रयोग की रूपरेखा तैयार की गई थी ?</p>",
                    options_en: ["<p>Maxwell and Hertz experiment</p>", "<p>Faraday and Henry experiment</p>", 
                                "<p>Michelson and Morley experiment</p>", "<p>Marconi and Thomson experiment</p>"],
                    options_hi: ["<p>मैक्सवेल और हर्ट्ज प्रयोग</p>", "<p>फैराडे और हेनरी प्रयोग</p>",
                                "<p>माइकल्सन और मोरले प्रयोग</p>", "<p>मार्कोनी और थॉमसन प्रयोग</p>"],
                    solution_en: "<p>12.(c) <strong>Michelson and Morley experiment - </strong>They tried to explain that Earth moved around the sun on its orbit, and the flow of substances like ether across the Earth&rsquo;s surface could produce a detectable &lsquo;ether wind&rsquo;<strong>. Maxwell and Hertz experiment -</strong> Maxwell had proved that light was an electromagnetic wave and Hertz measured Maxwell\'s waves and explained that the velocity of these waves was equal to the velocity of light. <strong>Faraday and Henry Experiments - </strong>Electromagnetic Induction. Marconi used radio waves to transmit signals over several kilometers. <strong>J.J. Thomson</strong> discovered the negatively charged part of the atom (the electron).</p>",
                    solution_hi: "<p>12.(c) <strong>माइकल्सन और मोरले प्रयोग - </strong>इन्होंने यह समझाने की कोशिश किया कि पृथ्वी अपनी कक्षा में सूर्य के चारों ओर घूमती है, और पृथ्वी की सतह पर ईथर जैसे पदार्थों का प्रवाह एक पता लगाने योग्य \'ईथर हवा\' उत्पन्न कर सकता है। <strong>मैक्सवेल और हर्ट्ज़ प्रयोग - </strong>मैक्सवेल ने सिद्ध किया था कि प्रकाश एक विद्युत चुम्बकीय तरंग है और हर्ट्ज़ ने मैक्सवेल की तरंगों को मापा और बताया कि इन तरंगों का वेग प्रकाश के वेग के बराबर था। <strong>फैराडे और हेनरी प्रयोग -</strong> विद्युत चुम्बकीय प्रेरण। मार्कोनी ने कई किलोमीटर तक सिग्नल प्रसारित करने के लिए रेडियो तरंगों का उपयोग किया। <strong>जे.जे. थॉमसन</strong> ने परमाणु के ऋणावेशित भाग (इलेक्ट्रॉन) की खोज की थी।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. In which year did an English Scientist named Michael Faraday discover benzene in the illuminating gas?</p>",
                    question_hi: "<p>13. माइकल फैराडे नाम के एक अंग्रेज वैज्ञानिक ने किस वर्ष में प्रदीपक (इलुमिनेटिंग) गैस में बेंजीन की खोज की थी?</p>",
                    options_en: ["<p>1827</p>", "<p>1820</p>", 
                                "<p>1822</p>", "<p>1825</p>"],
                    options_hi: ["<p>1827</p>", "<p>1820</p>",
                                "<p>1822</p>", "<p>1825</p>"],
                    solution_en: "<p>13.(d) <strong>1825</strong>. Michael Faraday also known as Father of Electricity. Inventions and Discoveries: Electric motor (1822), Electromagnetic induction (1831), Laws of electrolysis (1834), Liquefaction of gasses and their refrigeration (1823).</p>",
                    solution_hi: "<p>13.(d) <strong>1825 </strong>। माइकल फैराडे को विद्युत का जनक भी कहा जाता है। आविष्कार और खोज: विद्युत मोटर (1822), विद्युतचुंबकीय प्रेरण (1831), विद्युत अपघटन के नियम (1834), गैसों का द्रवीकरण और उनका प्रशीतन (1823)।।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. As per physicists and their major contributions/discoveries, which of the following pairs is INCORRECT ?</p>",
                    question_hi: "<p>14. भौतिकविदों और उनके प्रमुख योगदानों/खोजों के संबंध में, निम्नलिखित में से कौन सा युग्म गलत है?</p>",
                    options_en: ["<p>John Bardeen &ndash; Theory of superconductivity</p>", "<p>Louis Victor de Broglie &ndash; Wave nature of matter</p>", 
                                "<p>Victor Francis Hess &ndash; Cosmic radiation</p>", "<p>Paul Dirac &ndash; Liquid helium</p>"],
                    options_hi: ["<p>जॉन बार्डीन - अतिचालकता का सिद्धांत</p>", "<p>लुई विक्टर डी ब्रोगली - पदार्थ की तरंग प्रकृति</p>",
                                "<p>विक्टर फ्रांसिस हेस - ब्रह्मांडीय विकिरण</p>", "<p>पॉल डिराक - तरल हीलियम</p>"],
                    solution_en: "<p>14.(d) <strong>Paul Dirac</strong> Known for &ldquo;Dirac equation&rdquo; related to quantum theory. Physicist Heike Kamerlingh Onnes discovered liquid Helium. Albert Einstein - Theory of relativity. Isaac Newton - Laws of motion, Gravity. Edwin Hubble - Law of Cosmic Expansion. Kepler - Laws of Planetary Motion. Archimedes - Buoyancy Principle. Heisenberg - Uncertainty Principle.</p>",
                    solution_hi: "<p>14.(d) <strong>पॉल डिराक</strong> क्वांटम सिद्धांत से संबंधित \"डिराक समीकरण\" (Dirac Equation) के लिए प्रसिद्ध हैं। भौतिक विज्ञानी हेइके कामेरलिंग ओन्स ने &lsquo;तरल हीलियम&rsquo; की खोज की। अल्बर्ट आइंस्टीन - सापेक्षता का सिद्धांत। आइजैक न्यूटन - गति के नियम, गुरुत्वाकर्षण। एडविन हबल - ब्रह्मांडीय विस्तार का नियम। केप्लर - ग्रहों की गति के नियम। आर्किमिडीज़ - उत्प्लावन सिद्धांत। हाइजेनबर्ग - अनिश्चितता का सिद्धांत।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. In the early nineteenth century, who demonstrated that there are fourteen space lattices, or regularly repeating arrangements of points in space, that differ in symmetry and geometry?</p>",
                    question_hi: "<p>15. उन्नीसवीं शताब्दी की शुरुआत में, किसने यह प्रमाणित किया कि चौदह आकाशीय जालक (लेटिस), या अंतरिक्ष में बिंदुओं की नियमित पुनरावर्तक व्यवस्थाएं हैं, जो समरूपता और ज्यामिति में भिन्न होते हैं?</p>",
                    options_en: ["<p>Auguste Bravais</p>", "<p>William Bragg</p>", 
                                "<p>Jerome Karle</p>", "<p>Charles Frank</p>"],
                    options_hi: ["<p>ऑगस्टे ब्रावैस</p>", "<p>विलियम ब्रैग</p>",
                                "<p>जेरोम कार्ले</p>", "<p>चार्ल्स फ्रैंक</p>"],
                    solution_en: "<p>15.(a) <strong>Auguste Bravais. William Bragg :</strong> Won a Nobel Prize with his son Lawrence Bragg (1915) for their services in the analysis of crystal structure by means of X-rays. <strong>Jerome Karle : </strong>He was awarded the Nobel Prize in Chemistry in 1985, for the direct analysis of crystal structures using X-ray scattering techniques. <strong>Charles Frank :</strong> known for his work on crystal growth, liquid crystals and on the role of dislocations in crystals.</p>",
                    solution_hi: "<p>15.(a) <strong>ऑगस्टे ब्रावैस। विलियम ब्रैग : </strong>एक्स-रे के माध्यम से क्रिस्टल संरचना के विश्लेषण में उनकी सेवाओं के लिए अपने पुत्र लॉरेंस ब्रैग (1915) के साथ नोबेल पुरस्कार प्राप्त किया। <strong>जेरोम कार्ले :</strong> एक्स-रे स्कैटरिंग तकनीक का उपयोग करके क्रिस्टल संरचनाओं के प्रत्यक्ष विश्लेषण के लिए उन्हें 1985 में रसायन विज्ञान में नोबेल पुरस्कार से सम्मानित किया गया था। <strong>चार्ल्स फ्रैंक : </strong>क्रिस्टल विकास, तरल क्रिस्टल और क्रिस्टल में अव्यवस्था की भूमिका पर अपने काम के लिए जाने जाते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>