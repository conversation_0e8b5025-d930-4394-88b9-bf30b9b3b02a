<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 26</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">26</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 25,
                end: 25
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Read the given statement and conclusion carefully and decide which of the conclusions logically follows from the statement.<br><strong>Statement </strong>:<br>India based industrialist Mukesh Ambani is Asia\'s richest person this year.<br><strong>Conclusion</strong> <br>1. He is also the richest person of India. <br>2. He is not the richest person in India.</p>",
                    question_hi: "<p>1. दिए गए कथन और निष्कर्ष को ध्यान से पढिये और बताइये कि कौन सा निष्कर्ष कथन का तार्किक रूप से अनुसरण करता है।<br><strong>कथन :</strong> <br>भारत के उद्योगपति मुकेश अंबानी इस साल एशिया के सबसे अमीर व्यक्ति हैं।<br><strong>निष्कर्ष :</strong> <br>1. वह भारत के सबसे अमीर व्यक्ति भी हैं<br>2. वह भारत के सबसे अमीर व्यक्ति नहीं हैं</p>",
                    options_en: ["<p>Either conclusion I or II follows</p>", "<p>Only conclusion II follows</p>", 
                                "<p>Only conclusion I follows</p>", "<p>Neither conclusion I and II follows</p>"],
                    options_hi: ["<p>या तो निष्कर्ष I या II अनुसरण करता है</p>", "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                                "<p>केवल निष्कर्ष I अनुसरण करता है</p>", "<p>न तो निष्कर्ष I और II अनुसरण करता है</p>"],
                    solution_en: "<p>1.(c)<br>Mukesh Ambani is Asia\'s richest person this year and India is also a country of Asia continent so he is also the richest person of India. Thus only conclusion 1 follows.</p>",
                    solution_hi: "<p>1.(c)<br>मुकेश अंबानी इस साल एशिया के सबसे अमीर व्यक्ति हैं और भारत भी एशिया महाद्वीप का देश है इसलिए वे भारत के सबसे अमीर व्यक्ति भी हैं। इस प्रकार केवल निष्कर्ष 1 अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "2. Ram pointed to Sohan and said, “He is the only son of my mother’s father”.How is Sohan related to Ram’s mother? ",
                    question_hi: "2. राम ने सोहन की ओर इशारा करते हुए कहा, \"वह मेरी माँ के पिता का इकलौता पुत्र है।\" सोहन का राम की माँ से क्या संबंध है?",
                    options_en: [" Husband ", " Brother ", 
                                " Nephew ", " Maternal uncle "],
                    options_hi: [" पति", " भाई",
                                " भतीजा", " मामा"],
                    solution_en: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467280760.png\" alt=\"rId4\" width=\"116\" height=\"121\"><br>Sohan is Ram\'s mother&rsquo;s brother.</p>",
                    solution_hi: "<p>2.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467280880.png\" alt=\"rId5\" width=\"117\" height=\"120\"><br>सोहन, राम की माता का भाई है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Five friends Sohan, Mohan, Rohan, Ali and Salim like 5 different games i.e. Cricket, Kho-Kho, badminton, football and hockey, but not in the same order. Sohan likes football but does not like playing cricket. Ali and Rohan are best friends. Ali likes badminton and cricket is liked by Mohan. Who likes hockey?</p>",
                    question_hi: "<p>3. पांच दोस्त सोहन, मोहन, रोहन, अली और सलीम को 5 अलग-अलग खेल पसंद हैं जैसे क्रिकेट, खो-खो, बैडमिंटन, फुटबॉल और हॉकी, लेकिन इसी क्रम में नहीं। सोहन को फुटबॉल पसंद है लेकिन क्रिकेट खेलना पसंद नहीं है। अली और रोहन सबसे अच्छे दोस्त हैं। अली को बैडमिंटन पसंद है और मोहन को क्रिकेट पसंद है। हॉकी किसे पसंद है?</p>",
                    options_en: ["<p>Neither Rohan nor Salim</p>", "<p>Rohan Only</p>", 
                                "<p>Salim Only</p>", "<p>Either Rohan or Salim</p>"],
                    options_hi: ["<p>न रोहन न सलीम</p>", "<p>केवल रोहन</p>",
                                "<p>केवल सलीम</p>", "<p>या तो रोहन या सलीम</p>"],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467281153.png\" alt=\"rId6\" width=\"250\" height=\"118\"></p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467281426.png\" alt=\"rId7\" width=\"230\" height=\"164\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. From the given options, select the term that can replace the question mark (?) in the following series.<br>A1, B27, C125, ?</p>",
                    question_hi: "<p>4. दिए गए विकल्पों में से उस पद का चयन कीजिये जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिह्न (?) को प्रतिस्थापित कर सके<br>A1, B27, C125, ?</p>",
                    options_en: ["<p>D216</p>", "<p>D343</p>", 
                                "<p>C343</p>", "<p>D516</p>"],
                    options_hi: ["<p>D216</p>", "<p>D343</p>",
                                "<p>C343</p>", "<p>D516</p>"],
                    solution_en: "<p>4.(b)<br>Logic - <br>A + 1 = B , B + 1 = C , C + 1 = D<br><math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></msup></math> = 1 , 3<sup>3 </sup>= 27 , 5<sup>3</sup> = 125 ,7<sup>3</sup> = 343 (cube of odd numbers )</p>",
                    solution_hi: "<p>4.(b)<br>तर्क -<br>A + 1 = B , B + 1 = C , C+ 1 = D<br><math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></msup></math> = 1 , 3<sup>3 </sup>= 27 , 5<sup>3</sup> = 125 ,7<sup>3</sup> = 343 (विषम संख्याओं का घन)</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. From the given options, select the option that can replace the question mark in the series.<br>A, D , I, ?, Y</p>",
                    question_hi: "<p>5. दिए गए विकल्पों में से उस विकल्प का चयन कीजिये जो श्रृंखला में प्रश्नवाचक चिह्न को प्रतिस्थापित कर सके।<br>A, D , I, ?, Y</p>",
                    options_en: ["<p>Q</p>", "<p>R</p>", 
                                "<p>P</p>", "<p>N</p>"],
                    options_hi: ["<p>Q</p>", "<p>R</p>",
                                "<p>P</p>", "<p>N</p>"],
                    solution_en: "<p>5.(c)<br>Logic - addition with odd numbers<br>A + 3 = D , D + 5 = I , I + 7 = P , P + 9 = Y</p>",
                    solution_hi: "<p>5.(c)<br>तर्क - विषम संख्याओं के साथ जोड़<br>A + 3 = D , D + 5 = I , I + 7 = P , P + 9 = Y</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. A, B, C, and D are four persons in a group who have to choose one number from 1 to 10. No two persons can take the same number. The Cube of the number chosen by A and the Square of the number chosen by C are both within the permitted number range. B has chosen a number that is just one greater than the number chosen by C and D has chosen the smallest number amongst the four persons. What number has D chosen?</p>",
                    question_hi: "<p>6. A, B, C, और D एक समूह में चार व्यक्ति हैं जिन्हें 1 से 10 तक एक संख्या चुननी है। कोई भी दो व्यक्ति समान संख्या नहीं ले सकते। A द्वारा चुनी गई संख्या का घन और C द्वारा चुनी गई संख्या का वर्ग दोनों ही अनुमत संख्या सीमा के भीतर हैं। B ने एक संख्या चुनी है जो C द्वारा चुनी गई संख्या से सिर्फ एक बड़ी है और D ने चार व्यक्तियों में से सबसे छोटी संख्या को चुना है। D ने कौन सी संख्या चुनी है?</p>",
                    options_en: ["<p>4</p>", "<p>9</p>", 
                                "<p>8</p>", "<p>1</p>"],
                    options_hi: ["<p>4</p>", "<p>9</p>",
                                "<p>8</p>", "<p>1</p>"],
                    solution_en: "<p>6.(d)<br>(i) A choice cube of the number = 8<br>(ii) C choice square of the numbers and both within the permitted number range = 4<br>(iii) B chooses a number which is just greater than the number chosen by C . = 5<br>(iv) D has chosen the smallest number amongst the four persons. = 1</p>",
                    solution_hi: "<p>6.(d)<br>(i) A ने एक संख्या का घन चुना = 8<br>(ii) C के द्वारा चुने गए संख्या के वर्ग और दोनों अनुमत संख्या सीमा अंतर = 4<br>(iii) B एक संख्या चुनता है जो C द्वारा चुनी गई संख्या से ठीक बड़ी है = 5<br>(iv) D ने चार व्यक्तियों में से सबसे छोटी संख्या को चुना है। = 1</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the number from the given options that can replace the question mark (?) in the following series.<br>100, 64, 36, ?</p>",
                    question_hi: "<p>7. दिए गए विकल्पों में से वह संख्या चुनिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सके।<br>100, 64, 36, ?</p>",
                    options_en: ["<p>16</p>", "<p>4</p>", 
                                "<p>81</p>", "<p>25</p>"],
                    options_hi: ["<p>16</p>", "<p>4</p>",
                                "<p>81</p>", "<p>25</p>"],
                    solution_en: "<p>7.(a)<br>Logic - decreasing the square of consecutive even numbers.<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>&#160;</mi><mn>10</mn><mi>&#160;</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>100</mn></math> , ( 8 )<sup>2 </sup>= 64 , ( 6 )<sup>2 </sup>= 36 , (4 )<sup>2 </sup>= 16</p>",
                    solution_hi: "<p>7.(a)<br>तर्क - क्रमागत सम संख्याओं का वर्ग घटाना।<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>&#160;</mi><mn>10</mn><mi>&#160;</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>100</mn></math> , ( 8 )<sup>2 </sup>= 64 , ( 6 )<sup>2 </sup>= 36 , (4 )<sup>2 </sup>= 16</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the option which is different from the rest.</p>",
                    question_hi: "<p>8. उस विकल्प का चयन कीजिये जो बाकी से अलग है।</p>",
                    options_en: ["<p>256</p>", "<p>512</p>", 
                                "<p>729</p>", "<p>343</p>"],
                    options_hi: ["<p>256</p>", "<p>512</p>",
                                "<p>729</p>", "<p>343</p>"],
                    solution_en: "<p>8.(a)<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>343</mn></math>, ( 8 )<sup>3 </sup>= 512, ( 9)<sup>3</sup> = 729,( 16)<sup>2</sup> = 256<br>256 is different from others because 256 is a square of 16.<br>But others are cubes of numbers.</p>",
                    solution_hi: "<p>8.(a)<br><math display=\"inline\"><msup><mrow><mo>(</mo><mi>&#160;</mi><mn>7</mn><mi>&#160;</mi><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>343</mn></math>, ( 8 )<sup>3 </sup>= 512, ( 9)<sup>3</sup> = 729,( 16)<sup>2</sup> = 256<br>256 अन्य से भिन्न है क्योंकि 256, 16 का एक वर्ग है।<br>लेकिन अन्य संख्याओं के घन हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. According to the time by Nihaal&rsquo;s watch its half past one and the hour hand is pointing towards the north-east. Assuming that there is no change in Nihaal&rsquo;s position, in which direction would the minute hand point after 30 minutes?</p>",
                    question_hi: "<p>9. निहाल की घड़ी से समय के अनुसार 1 बजकर 30 मिनट हुई है और घंटे की सुई उत्तर-पूर्व की ओर इशारा कर रही है। यह मानते हुए कि निहाल की स्थिति में कोई परिवर्तन नहीं हुआ है, 30 मिनट के बाद मिनट की सुई किस दिशा में इंगित करेगी?</p>",
                    options_en: ["<p>South</p>", "<p>East</p>", 
                                "<p>West</p>", "<p>North</p>"],
                    options_hi: ["<p>दक्षिण</p>", "<p>पूर्व</p>",
                                "<p>पश्चिम</p>", "<p>उत्तर</p>"],
                    solution_en: "<p>9.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467281553.png\" alt=\"rId8\" width=\"86\" height=\"94\"></p>",
                    solution_hi: "<p>9.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467281553.png\" alt=\"rId8\" width=\"94\" height=\"103\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. If <strong>SUDDEN </strong>is coded as <strong>RVCEDO</strong>, then what will <strong>GROWTH</strong> be coded as?</p>",
                    question_hi: "<p>10. यदि <strong>SUDDEN </strong>को <strong>RVCEDO </strong>के रूप में कोडित किया जाता है, तो <strong>GROWTH </strong>को किस रूप में कोडित किया जाएगा?</p>",
                    options_en: ["<p>FSNXSG</p>", "<p>HQPXUG</p>", 
                                "<p>FSNXUG</p>", "<p>FSNXSI</p>"],
                    options_hi: ["<p>FSNXSG</p>", "<p>HQPXUG</p>",
                                "<p>FSNXUG</p>", "<p>FSNXSI</p>"],
                    solution_en: "<p>10.(d)<br>S - 1 = R , U + 1 = V , D -1 = C , D + 1 = E , N + 1 = O<br>Similarly , GROWTH is written as FSNXSI .</p>",
                    solution_hi: "<p>10.(d)<br>S - 1 = R , U + 1 = V , D -1 = C , D + 1 = E , N + 1 = O<br>इसी तरह, GROWTH को FSNXSI के रूप में लिखा जाता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the option that can replace the question mark (?) in the following series.<br>2, 0, 10, 2, 30, 6, 68, 12, ?,?</p>",
                    question_hi: "<p>11. उस विकल्प का चयन कीजिये जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकता है<br>2, 0, 10, 2, 30, 6, 68, 12, ?,?</p>",
                    options_en: ["<p>120 ; 20</p>", "<p>98 ; 24</p>", 
                                "<p>130 ; 20</p>", "<p>130 ; 30</p>"],
                    options_hi: ["<p>120 ; 20</p>", "<p>98 ; 24</p>",
                                "<p>130 ; 20</p>", "<p>130 ; 30</p>"],
                    solution_en: "<p>11.(c)<br>Logic - alternate series <br>2, 0, 10, 2, 30, 6, 68, 12, ? , ?<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 2 = 8 + 2 = 10 <br><math display=\"inline\"><msup><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 3 = 27 + 3 = 30<br><math display=\"inline\"><msup><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 4 = 64 + 4 = 68<br><math display=\"inline\"><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 5 = 125 + 5 = 130<br>And <br>0 + 2 = 2 , 2 + 4 = 6 , 6 + 6 = 12 , 12 + 8 = 20</p>",
                    solution_hi: "<p>11.(c)<br>तर्क - वैकल्पिक श्रृंखला<br>2, 0, 10, 2, 30, 6, 68, 12, ? , ?<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 2 = 8 + 2 = 10 <br><math display=\"inline\"><msup><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 3 = 27 + 3 = 30<br><math display=\"inline\"><msup><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 4 = 64 + 4 = 68<br><math display=\"inline\"><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 5 = 125 + 5 = 130<br>और<br>0 + 2 = 2 , 2 + 4 = 6 , 6 + 6 = 12 , 12 + 8 = 20</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. 8 students Ani, Bini, Cina, Dia, Eva, Fin, Gim and Haz are sitting in a row facing towards North ( not necessarily in the same order). Only four students are sitting between Cina and Bini and one among Cina and Bini is sitting at the end of row. Only three students are sitting between Bini and Ani. Only two students are sitting between Gim and Haz. Dia is sitting to the immediate right of Ani and Ani is fifth to the left of Haz.<br>Who among the given options could be seated to the immediate left of Bini?</p>",
                    question_hi: "<p>12. 8 छात्र अनी, बिनी, सीना, दीया, ईवा, फिन, गिम और हाज उत्तर की ओर एक पंक्ति में बैठे हैं (जरूरी नहीं कि एक ही क्रम में) । सीना और बिनी के बीच केवल चार छात्र बैठे हैं और एक सीना और बिनी के बीच पंक्ति के अंत में बैठा है । बिनी और अनी के बीच सिर्फ तीन छात्र बैठे हैं। गिम और हाज के बीच सिर्फ दो छात्र बैठे हैं। दीया, अनी के दाएँ ओर है और अनी, हाज के बाईं ओर पांचवें स्थान पर है ।<br>दिए गए विकल्पों में से किसे बिनी के बाईं ओर बैठाया जा सकता है?</p>",
                    options_en: ["<p>Haz</p>", "<p>Eva</p>", 
                                "<p>Gim</p>", "<p>Ani</p>"],
                    options_hi: ["<p>हाज़</p>", "<p>ईवा</p>",
                                "<p>गिम</p>", "<p>अनी</p>"],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467281683.png\" alt=\"rId9\" width=\"236\" height=\"52\"><br>One who can be seated to the immediate left of Bini = E or F<br>According to the options Eva can be seated to the immediate left of Bini.</p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467281683.png\" alt=\"rId9\" width=\"281\" height=\"62\"><br>वह व्यक्ति जो बीनी के ठीक बायें बैठा है = E या F<br>विकल्पों के अनुसार Eva को बिनी के ठीक बायीं ओर बैठाया जा सकता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the number that can replace the question mark(?) in the following series.<br>2, 12, 36, 80, ?</p>",
                    question_hi: "<p>13. उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है<br>2, 12, 36, 80, ?</p>",
                    options_en: ["<p>150</p>", "<p>140</p>", 
                                "<p>120</p>", "<p>144</p>"],
                    options_hi: ["<p>150</p>", "<p>140</p>",
                                "<p>120</p>", "<p>144</p>"],
                    solution_en: "<p>13.(a)<br>Logic -<br>2 = <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>2</mn></math><br>12 = <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>12</mn></math><br>36 = <math display=\"inline\"><msup><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>=</mo><mn>27</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>9</mn><mi>&#160;</mi></math><br>80 = <math display=\"inline\"><msup><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>80</mn><mi>&#160;</mi></math><br>Similarly , <math display=\"inline\"><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>125</mn></math> + 25 = 150</p>",
                    solution_hi: "<p>13.(a)<br>तर्क -<br>2 = <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>2</mn></math><br>12 = <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>12</mn></math><br>36 = <math display=\"inline\"><msup><mrow><mn>3</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>=</mo><mn>27</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>9</mn><mi>&#160;</mi></math><br>80 = <math display=\"inline\"><msup><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mn>4</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>80</mn><mi>&#160;</mi></math><br>इसी प्रकार, <math display=\"inline\"><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>125</mn></math> + 25 = 150</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Assuming P &amp; Q were Arithmetic Operators, if 24 P 12 Q 13 = 15 and 16 Q 56 P 14 = 20, then 87 P 29 Q 22 = ?</p>",
                    question_hi: "<p>14. मान लें कि P &amp; Q अंकगणितीय प्रचालन हैं, यदि 24 P 12 Q 13 = 15 और 16 Q 56 P 14 = 20, तो 87 P 29 Q 22 = ?</p>",
                    options_en: ["<p>30</p>", "<p>27</p>", 
                                "<p>25</p>", "<p>28</p>"],
                    options_hi: ["<p>30</p>", "<p>27</p>",
                                "<p>25</p>", "<p>28</p>"],
                    solution_en: "<p>14.(c)<br>Let , P = <math display=\"inline\"><mo>&#247;</mo></math> , Q = + <br>THEN , 24 P 12 Q 13 <br>= 24 <math display=\"inline\"><mo>&#247;</mo></math> 12 + 13 = 15 (satisfied)<br>Now for 87 P 29 Q 22 = 87 <math display=\"inline\"><mo>&#247;</mo></math> 29 + 22 = 3 + 22 = 25</p>",
                    solution_hi: "<p>14.(c)<br>माना, P = <math display=\"inline\"><mo>&#247;</mo></math> , Q = + <br>तब, 24 P 12 Q 13 <br>= 24 <math display=\"inline\"><mo>&#247;</mo></math> 12 + 13 = 15 (संतुष्ट)<br>अब 87 P 29 Q 22 के लिए = 87 <math display=\"inline\"><mo>&#247;</mo></math> 29 + 22 = 3 + 22 = 25</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. 8 students P, Q, R, S, T, X, Y and Z are sitting in a row facing towards south (not necessarily in the same order). Only four students are sitting between P and T. Z is fourth to the left of S. R is neighbour of Q. P is sitting to the right of T and T is sitting second to the left of Z. T is not the neighbour of X. If T is sitting at one end of the row, then who will sit at seventh place from the other end?</p>",
                    question_hi: "<p>15. 8 विद्यार्थी P, Q, R, S, T, X, Y और Z एक पंक्ति में दक्षिण की ओर मुख करके बैठे हैं (जरूरी नहीं कि इसी क्रम में हों)। P और T के बीच केवल चार छात्र बैठे हैं। Z, S के बाएं से चौथे स्थान पर है। R, Q का पड़ोसी है। P, T के दाईं ओर बैठा है और T, Z के बाएं से दूसरे स्थान पर बैठा है। T, का पड़ोसी नहीं है X. यदि T पंक्ति के एक छोर पर बैठा है, तो दूसरे छोर से सातवें स्थान पर कौन बैठेगा?</p>",
                    options_en: ["<p>Y</p>", "<p>P</p>", 
                                "<p>R</p>", "<p>Z</p>"],
                    options_hi: ["<p>Y</p>", "<p>P</p>",
                                "<p>R</p>", "<p>Z</p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467281791.png\" alt=\"rId10\" width=\"256\" height=\"43\"></p>",
                    solution_hi: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467281791.png\" alt=\"rId10\" width=\"268\" height=\"45\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. If <strong>def </strong>is coded as <strong>222120 </strong>and <strong>qtv </strong>is coded as <strong>964</strong>, then which of the given options follows the same code?</p>",
                    question_hi: "<p>16. यदि <strong>def </strong>को <strong>222120 </strong>के रूप में और <strong>qtv </strong>को <strong>964 </strong>के रूप में कोडित किया जाता है, तो दिए गए विकल्पों में से कौन सा समान कोड का अनुसरण करता है?</p>",
                    options_en: ["<p>abc = 222324</p>", "<p>qpr = 24587</p>", 
                                "<p>hik = 181715</p>", "<p>xyz = 2156</p>"],
                    options_hi: ["<p>abc = 222324</p>", "<p>qpr = 24587</p>",
                                "<p>hik = 181715</p>", "<p>xyz = 2156</p>"],
                    solution_en: "<p>16.(c)<br>Logic - <br>Using ( opposite face value - 1 )<br>d - w - 23 - 1 = 22<br>e - v - 22 - 1 = 21 <br>f - u - 21 - 1 = 20&nbsp;<br>Similarly , <br>hik is written as 181715</p>",
                    solution_hi: "<p>16.(c)<br>तर्क -<br>उपयोग करना (विपरीत अंकित मूल्य - 1 )<br>d - w - 23 - 1 = 22<br>e - v - 22 - 1 = 21 <br>f - u - 21 - 1 = 20&nbsp;<br>इसी तरह,<br>hik को 181715 के रूप में लिखा जाता है</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. In a certain language, &lsquo;Delhi is capital&rsquo; is written as &lsquo;753 852 159&rsquo;, &lsquo; Ram lives in Delhi&rsquo; is written as, &lsquo;153 253 159 157&rsquo; and &lsquo;Ram is teacher&rsquo; is written as &lsquo;753 157 954&rsquo;. What is capital coded as?</p>",
                    question_hi: "<p>17. एक निश्चित भाषा में, \'Delhi is capital\' को \'753 852 159\', \'Ram lives in Delhi\' को \'153 253 159 157\' और \'Ram is teacher\' को \'753 157 954\' लिखा जाता है। capital को किस रूप में कूटबद्ध किया जायेगा ?</p>",
                    options_en: ["<p>253</p>", "<p>753</p>", 
                                "<p>852</p>", "<p>159</p>"],
                    options_hi: ["<p>253</p>", "<p>753</p>",
                                "<p>852</p>", "<p>159</p>"],
                    solution_en: "<p>17.(c)<br>Delhi is capital&rsquo; - &lsquo;753 852 159 ----- (i)<br>Ram lives in Delhi&rsquo; - &lsquo;153 253 159 157&rsquo; ----------(ii)<br>Ram is teacher&rsquo; - &lsquo;753 157 954 ----------(iii)<br>Delhi is common in (i) and (ii) so , delhi = 159<br>&ldquo;Is&rdquo; common in (i) and (iii) so , is = 753<br>Hence , capital = 852</p>",
                    solution_hi: "<p>17.(c)<br>दिल्ली राजधानी है\' - \'753 852 159 ----- (i)<br>राम दिल्ली में रहता है\' - \'153 253 159 157\' ----------(ii)<br>राम शिक्षक हैं\' - \'753 157 954 ----------(iii)<br>दिल्ली (i) और (ii) में समान है, इसलिए दिल्ली = 159<br>(i) और (iii) में \"Is\" उभयनिष्ठ है, इसलिए = 753<br>अत: capital = 852</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Six persons- A, B, C, D, E and F are sitting on a round table facing each other. Two persons are sitting between B and E. A is sitting second to the right of B. D is sitting second to the left of E. Who is sitting between D and E?</p>",
                    question_hi: "<p>18. छह व्यक्ति- A, B, C, D, E और F एक गोल मेज पर एक दूसरे की ओर मुख करके बैठे हैं। B और E के बीच दो व्यक्ति बैठे हैं। A, B के दायें से दूसरे स्थान पर बैठा है। D, E के बायें से दूसरे स्थान पर बैठा है। D और E के बीच में कौन बैठा है?</p>",
                    options_en: ["<p>A</p>", "<p>C</p>", 
                                "<p>F</p>", "<p>B</p>"],
                    options_hi: ["<p>A</p>", "<p>C</p>",
                                "<p>F</p>", "<p>B</p>"],
                    solution_en: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467281909.png\" alt=\"rId11\" width=\"110\" height=\"105\"><br>A is sitting between D and E .</p>",
                    solution_hi: "<p>18.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467281909.png\" alt=\"rId11\" width=\"105\" height=\"101\"><br>A, D और E के बीच बैठा है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. If <strong>STYLE </strong>is <strong>81</strong>, ACE is <strong>9 </strong>and <strong>BOLD</strong> is <strong>33</strong>, then what will be the difference between <strong>SMART </strong>and <strong>GOLD </strong>if these are coded in the same manner?</p>",
                    question_hi: "<p>19. यदि <strong>STYLE </strong>को <strong>81 </strong>लिखा जाता है, <strong>ACE </strong>को <strong>9 </strong>है और <strong>BOLD </strong>को <strong>33 </strong>लिखा जाता है, तो <strong>SMART </strong>और <strong>GOLD </strong>के बीच क्या अंतर होगा यदि इन्हें इसी तरीके से कोडित किया जाता है?</p>",
                    options_en: ["<p>72</p>", "<p>35</p>", 
                                "<p>39</p>", "<p>33</p>"],
                    options_hi: ["<p>72</p>", "<p>35</p>",
                                "<p>39</p>", "<p>33</p>"],
                    solution_en: "<p>19.(d)<br>Logic - <br>Sum of the place value of the algebraic digit.<br>STYLE = 20 + 19 + 25 + 12 + 5 = 81 <br>So that GOLD = 7 + 15 + 12 + 4 = 38 and SMART = 19 + 13 + 1 + 18 + 20 = 71<br>Hence , difference between the SMART and GOLD = 71 - 38 = 33</p>",
                    solution_hi: "<p>19.(d)<br>तर्क -<br>बीजीय अंक के स्थानीय मान का योग।<br>STYLE = 20 + 19 + 25 + 12 + 5 = 81&nbsp;<br>ताकि GOLD = 7 + 15 + 12 + 4 = 38 और SMART = 19 + 13 + 1 + 18 + 20 = 71<br>इसलिए, SMART और GOLD के बीच का अंतर = 71 - 38 = 33</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. If <strong>LIGHT </strong>is coded as <strong>KHFGS</strong>, then what will <strong>SHORT </strong>be coded as?</p>",
                    question_hi: "<p>20. यदि <strong>LIGHT </strong>को <strong>KHFGS </strong>के रूप में कोडित किया जाता है, तो <strong>SHORT </strong>को किस रूप में कोडित किया जाएगा?</p>",
                    options_en: ["<p>TINQS</p>", "<p>TIPSU</p>", 
                                "<p>RGNQS</p>", "<p>RGNSU</p>"],
                    options_hi: ["<p>TINQS</p>", "<p>TIPSU</p>",
                                "<p>RGNQS</p>", "<p>RGNSU</p>"],
                    solution_en: "<p>20.(c)<br>Logic - <br>L - 1 = K , I - 1 = H , G - 1 = F , H - 1 = G , T - 1 = S&nbsp;<br>So that SHORT is written as RGNQS.</p>",
                    solution_hi: "<p>20.(c)<br>तर्क -<br>L - 1 = K , I - 1 = H , G - 1 = F , H - 1 = G , T - 1 = S&nbsp;<br>ताकि SHORT को RGNQS लिखा जाए।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. In a certain code language, &lsquo;<strong>JUNIOR</strong>&rsquo; is written as &lsquo;<strong>6</strong>&rsquo;, &lsquo;<strong>LABOURER</strong>&rsquo; is written as &lsquo;<strong>8</strong>&rsquo;. What is the code for &lsquo;<strong>JOSEPHINE</strong>&rsquo; in that code language?</p>",
                    question_hi: "<p>21. एक निश्चित कोड भाषा में, \'<strong>JUNIOR</strong>\' को \'<strong>6</strong>\' लिखा जाता है, \'<strong>LABOURER</strong>\' को \'<strong>8</strong>\' लिखा जाता है। उसी कूट भाषा में &lsquo;<strong>JOSEPHINE</strong>&rsquo; के लिए कूट क्या है?</p>",
                    options_en: ["<p>10</p>", "<p>9</p>", 
                                "<p>11</p>", "<p>12</p>"],
                    options_hi: ["<p>10</p>", "<p>9</p>",
                                "<p>11</p>", "<p>12</p>"],
                    solution_en: "<p>21.(b)<br>Logic - no.of digit <br>In &ldquo;JUNIOR&rdquo; number of digit = 6<br>Similarly &ldquo;JOSEPHINE&rdquo; has 9 digits.</p>",
                    solution_hi: "<p>21.(b)<br>तर्क - अंकों की संख्या<br>&ldquo;जूनियर&rdquo; में अंकों की संख्या = 6<br>इसी तरह \"Josephine\" में 9 अंक होते हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the letter that can replace the question mark (?) in the following series.<br>a, z, b, y, c, ?</p>",
                    question_hi: "<p>22. उस अक्षर का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है<br>a, z, b, y, c, ?</p>",
                    options_en: ["<p>e</p>", "<p>x</p>", 
                                "<p>d</p>", "<p>c</p>"],
                    options_hi: ["<p>e</p>", "<p>x</p>",
                                "<p>d</p>", "<p>c</p>"],
                    solution_en: "<p>22.(b)<br>Logic - <br>Alternet series<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467282047.png\" alt=\"rId12\" width=\"222\" height=\"78\"></p>",
                    solution_hi: "<p>22.(b)<br>तर्क -<br>वैकल्पिक श्रृंखला<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467282047.png\" alt=\"rId12\" width=\"235\" height=\"83\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Read the given statement and assumptions carefully and decide which of the assumptions is/are implied from the statement.<br><strong>Statement </strong>:<br>Pollution is one of the major causes leading to the weather imbalances throughout the world. It must be checked by combined efforts of all.<br><strong>Assumptions </strong>:<br>I. Weather imbalances affects everyone throughout the world. <br>II. Water pollution is causing many diseases. <br>choose the correct option</p>",
                    question_hi: "<p>23. दिए गए कथनों और पूर्वधारणाओं को ध्यानपूर्वक पढ़िए और निर्णय कीजिए कि कौन-सी धारणा कथन से निहित हैं।<br><strong>कथन :</strong><br>प्रदूषण दुनिया भर में मौसम के असंतुलन के प्रमुख कारणों में से एक है। सभी के संयुक्त प्रयासों से इसकी जांच होनी चाहिए।<br><strong>अवधारणा</strong> <strong>:</strong><br>I. मौसम असंतुलन दुनिया भर में सभी को प्रभावित करता है<br>II. जल प्रदूषण कई बीमारियों का कारण बन रहा है<br>सही विकल्प चुनिए</p>",
                    options_en: ["<p>None assumption is implied</p>", "<p>Both assumptions are implied</p>", 
                                "<p>Only assumption II is implied</p>", "<p>Only assumption I is implied</p>"],
                    options_hi: ["<p>कोई अवधारणा निहित नहीं है</p>", "<p>दोनों अवधारणाएँ निहित हैं</p>",
                                "<p>केवल अवधारणा II निहित है</p>", "<p>केवल अवधारणा I निहित है</p>"],
                    solution_en: "<p>23.(d)<br>Imbalances Weather affects everyone throughout the world <br>This assumption is directly related to the given statement.</p>",
                    solution_hi: "<p>23.(d)<br>असंतुलन मौसम दुनिया भर में सभी को प्रभावित करता है<br>यह धारणा सीधे दिए गए कथन से संबंधित है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Which of the given options is related to the fourth term in the same way as the first term is related to the second term.<br>Ink : Pen :: ? : Car</p>",
                    question_hi: "<p>24. दिए गए विकल्पों में से कौन चौथे पद से उसी प्रकार संबंधित है जैसे पहला पद दूसरे पद से संबंधित है<br>स्याही: पेन :: ? : कार</p>",
                    options_en: ["<p>Gear</p>", "<p>Wheels</p>", 
                                "<p>Petrol</p>", "<p>Body</p>"],
                    options_hi: ["<p>गियर</p>", "<p>पहिया</p>",
                                "<p>पेट्रोल</p>", "<p>बॉडी</p>"],
                    solution_en: "<p>24.(c)<br>In pen run through ink and similarly fuel of the car is petrol .</p>",
                    solution_hi: "<p>24.(c) पेन स्याही से चलती है और इसी तरह कार का (ईंधन) पेट्रोल से चलती है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25. If <strong>BASE</strong> = <strong>27</strong>, then what is <strong>COLLECTION</strong>?</p>",
                    question_hi: "<p>25. यदि <strong>BASE</strong> = <strong>27 </strong>तो <strong>COLLECTION </strong>क्या होगा ?</p>",
                    options_en: ["<p>108</p>", "<p>107</p>", 
                                "<p>111</p>", "<p>110</p>"],
                    options_hi: ["<p>108</p>", "<p>107</p>",
                                "<p>111</p>", "<p>110</p>"],
                    solution_en: "<p>25.(a)<br>B + A + S + E = 2 + 1 + 19 + 5 = 27<br>Similarly , COLLECTION is written as 108.</p>",
                    solution_hi: "<p>25.(a)<br>B + A + S + E = 2 + 1 + 19 + 5 = 27<br>इसी तरह, COLLECTION को 108 के रूप में लिखा जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "misc",
                    question_en: "<p>26. In a workshop, there are five workers- A, V, N, J, and S, who each go to work only once in a week. The workshop operates for 5 days a week. N goes on Monday. A and V go on consecutive days. A goes the next day to that of N. There is a gap of two days between the days on which A and J go to work. On which of the given days would S be going to the workshop?</p>",
                    question_hi: "<p>26. एक कार्यशाला में, पाँच कर्मचारी- A, V, N, J, और S हैं, जो प्रत्येक सप्ताह में केवल एक बार काम पर जाते हैं। कार्यशाला सप्ताह में 5 दिन चलती है। N सोमवार को जाता है। A और V लगातार दिनों पर चलते हैं। A अगले दिन N के पास जाता है। A और J के काम पर जाने के दिनों के बीच दो दिनों का अंतर है। दिए गए दिनों में से S किस दिन कार्यशाला में जाएगा?</p>",
                    options_en: ["<p>Friday</p>", "<p>Thursday</p>", 
                                "<p>Monday</p>", "<p>Wednesday</p>"],
                    options_hi: ["<p>शुक्रवार</p>", "<p>गुरूवार</p>",
                                "<p>सोमवार</p>", "<p>बुधवार</p>"],
                    solution_en: "<p>26.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467282210.png\" alt=\"rId13\" width=\"115\" height=\"116\"></p>",
                    solution_hi: "<p>26.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728467282351.png\" alt=\"rId14\" width=\"119\" height=\"128\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>