<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The children did not like the </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">abominable</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"> </span>table manners of old aunt.</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The children did not like the<span style=\"text-decoration: underline;\"> </span></span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">abominable</span></span><span style=\"font-family: Cambria Math;\"> table manners of old aunt.</span></p>\n",
                    options_en: ["<p>delectable</p>\n", "<p>stringent</p>\n", 
                                "<p>obnoxious</p>\n", "<p>inflexible</p>\n"],
                    options_hi: ["<p>delectable</p>\n", "<p>stringent</p>\n",
                                "<p>obnoxious</p>\n", "<p>inflexible</p>\n"],
                    solution_en: "<p>1.(c)<strong> Obnoxious</strong><span style=\"font-family: Cambria Math;\">- extremely unp</span><span style=\"font-family: Cambria Math;\">leasant or offensive.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Abominable</span></strong><span style=\"font-family: Cambria Math;\">- extremely unpleasant or loathsome.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Delectable</span></strong><span style=\"font-family: Cambria Math;\">- highly enjoyable or delicious.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Stringent</span></strong><span style=\"font-family: Cambria Math;\"><strong>- </strong>strict, precise, or demanding in requirements.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Inflexible</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> not easily bent, changed, or persuaded.</span></p>\n",
                    solution_hi: "<p>1.(c) <strong>Obnoxious </strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2381;&#2352;&#2367;&#2351;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">extremely unpleasant or offensive.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Abominable</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2328;&#2371;&#2339;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - extremely unpleasant or loathsome.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Delectable</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2350;&#2344;&#2379;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\">/ </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2366;&#2342;&#2367;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">) - highly enjoyable or delicious.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Stringent</span></strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2360;&#2326;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\">) - strict, precise, or demanding in requirements.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Inflexible</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2350;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) - not easily bent</span><span style=\"font-family: Cambria Math;\">, changed, or persuaded.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Makeshift</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Makeshift</span></p>\n",
                    options_en: ["<p>Resourceful</p>\n", "<p>Provisional</p>\n", 
                                "<p>Perpetual</p>\n", "<p>Inconsiderate</p>\n"],
                    options_hi: ["<p>Resourceful</p>\n", "<p>Provisional</p>\n",
                                "<p>Perpetual</p>\n", "<p>Inconsiderate</p>\n"],
                    solution_en: "<p>2.(b) <strong>Provisional-</strong> <span style=\"font-family: Cambria Math;\">arranged or existing for the present, possibly to be changed later.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Makeshift-</strong> </span><span style=\"font-family: Cambria Math;\">something created temporarily and quickly, using whatever is available.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Resourceful- </span></strong><span style=\"font-family: Cambria Math;\">having t</span><span style=\"font-family: Cambria Math;\">he ability to find quick and clever ways to overcome difficulties.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Perpetual- </span></strong><span style=\"font-family: Cambria Math;\">never ending or changing.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Inconsiderate-</strong> </span><span style=\"font-family: Cambria Math;\">not caring about other people or their feelings.</span></p>\n",
                    solution_hi: "<p>2.(b) <strong>Provisional</strong> <span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2341;&#2366;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">arranged or existing for the present, possibly to b</span><span style=\"font-family: Cambria Math;\">e changed later.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Makeshift</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2341;&#2366;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">something created temporarily and quickly, using whatever is available.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Resourceful</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2346;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">having the ability to find quick and clever ways to overcome difficulties.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Perpetual</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2344;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">never ending or chan</span><span style=\"font-family: Cambria Math;\">ging.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Inconsiderate</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2367;&#2357;&#2375;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">not caring about other people or their feelings.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word from the following sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Ambitious</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He aspires to be a future diplomat but he will have to improve his decision-making skills. </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word from the following sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> Ambitious</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He aspires </span><span style=\"font-family: Cambria Math;\">to be a future diplomat but he will have to improve his decision-making skills. </span></p>\n",
                    options_en: ["<p>aspires</p>\n", "<p>improve</p>\n", 
                                "<p>decision</p>\n", "<p>skills</p>\n"],
                    options_hi: ["<p>aspires</p>\n", "<p>improve</p>\n",
                                "<p>decision</p>\n", "<p>skills</p>\n"],
                    solution_en: "<p>3.(a)<strong> Aspires</strong> -<span style=\"font-family: Cambria Math;\"> to have a strong hope or goal to achieve something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Ambitious </strong>- </span><span style=\"font-family: Cambria Math;\">having a strong desire to ach</span><span style=\"font-family: Cambria Math;\">ieve something big or important.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Improve -</span></strong><span style=\"font-family: Cambria Math;\"> to make something better or get better at doing something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Decision -</span></strong><span style=\"font-family: Cambria Math;\"> a choice or judgment made after considering options.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Skills -</strong> </span><span style=\"font-family: Cambria Math;\">abilities and knowledge to do something well.</span></p>\n",
                    solution_hi: "<p>3.(a) <strong>Aspires</strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2310;&#2325;&#2366;&#2306;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\">) - t</span><span style=\"font-family: Cambria Math;\">o have a strong hope or goal to achieve something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Ambitious</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2340;&#2381;&#2357;&#2366;&#2325;&#2366;&#2306;&#2325;&#2381;&#2359;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">having a strong desire to achieve something big or important.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Improve </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to make something better or get better at doing something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Decision</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2381;&#2339;&#2351;</span><span style=\"font-family: Cambria Math;\">) - a choice or judgment made after considering options.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Skills</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2380;&#2358;&#2354;</span><span style=\"font-family: Cambria Math;\">) - abilities and knowledge to do something well.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">During lockdown, the government and NGOs tried their best to look after people who </span><span style=\"font-family: Cambria Math;\">were </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">destitute</span><span style=\"font-family: Cambria Math;\">.</span></span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">During lock</span><span style=\"font-family: Cambria Math;\">down, the government and NGOs tried their best to look after people who </span><span style=\"font-family: Cambria Math;\">were </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">destitute</span><span style=\"font-family: Cambria Math;\">.</span></span></p>\n",
                    options_en: ["<p>impoverished</p>\n", "<p>elite</p>\n", 
                                "<p>haughty</p>\n", "<p>patriotic</p>\n"],
                    options_hi: ["<p>impoverished</p>\n", "<p>elite</p>\n",
                                "<p>haughty</p>\n", "<p>patriotic</p>\n"],
                    solution_en: "<p>4.(a)<strong> Impoverished-</strong><span style=\"font-family: Cambria Math;\"> very poor.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Destitute- </span></strong><span style=\"font-family: Cambria Math;\">extremely poor and lacking the means to provide for one</span><span style=\"font-family: Cambria Math;\">self.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Elite- </span></strong><span style=\"font-family: Cambria Math;\">a select group that is superior in terms of ability or qualities.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Haughty- </span></strong><span style=\"font-family: Cambria Math;\">behaving in a proud, arrogant manner.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Patriotic</strong>- </span><span style=\"font-family: Cambria Math;\">having or expressing devotion to and vigorous support for one\'s country.</span></p>\n",
                    solution_hi: "<p>4.(a)<strong> Impoverished</strong> <span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2381;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> )</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> very poor.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Destitute</span></strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2366;&#2358;&#2381;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">extremely poor and lacking the means to provide for oneself.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Elite</span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2369;&#2354;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">a select group that is superior in terms of ability or qualities.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Haughty</span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2349;&#2367;&#2350;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">behaving in a proud, arrogant manner.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Patriotic</span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2358;&#2349;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">having or expressing devotion to and vigorous support for one\'s country.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He was put behind bars for his act of </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">for</span><span style=\"font-family: Cambria Math;\">g</span><span style=\"font-family: Cambria Math;\">ery</span><span style=\"font-family: Cambria Math;\">.</span></span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">He was put behind bars for his act of </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">for</span><span style=\"font-family: Cambria Math;\">g</span><span style=\"font-family: Cambria Math;\">ery</span><span style=\"font-family: Cambria Math;\">.</span></span></p>\n",
                    options_en: ["<p>murdering</p>\n", "<p>counterfeiting</p>\n", 
                                "<p>coaxing</p>\n", "<p>embezzling</p>\n"],
                    options_hi: ["<p>murdering</p>\n", "<p>counterfeiting</p>\n",
                                "<p>coaxing</p>\n", "<p>embezzling</p>\n"],
                    solution_en: "<p>5.(b)<strong> Counterfeiting</strong>-<span style=\"font-family: Cambria Math;\"> imitating something fraudulently.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Forgery</strong>- </span><span style=\"font-family: Cambria Math;\">the crime of illegally copying something in order to deceive someone.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Murdering-</strong> </span><span style=\"font-family: Cambria Math;\">intentionally killing someone.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Coaxing-</strong> </span><span style=\"font-family: Cambria Math;\">gently and persistently persuasive.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Embezzling-</strong> </span><span style=\"font-family: Cambria Math;\">secretly taking money that is in your care.</span></p>\n",
                    solution_hi: "<p>5.(b) <strong>Counterfeiting</strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2354;&#2360;&#2366;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> imitating something fraudulently.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Forgery</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2354;&#2360;&#2366;&#2332;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">the crime of illegally copying something in order to deceive someone.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Murdering</span></strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2361;&#2340;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">intentionally killing someone.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Coaxing</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2347;&#2369;&#2360;&#2354;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2350;&#2344;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">gently and persistently persuasive.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Embezzling</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2327;&#2348;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> )-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">secretly taking money that is in your care.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Indispensable</span></p>\n",
                    question_hi: "<p>6. <span style=\"font-family: Cambria Math;\">Select the </span><span style=\"font-family: Cambria Math;\">most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Indispensable</span></p>\n",
                    options_en: ["<p>Memorable</p>\n", "<p>Eternal</p>\n", 
                                "<p>Essential</p>\n", "<p>Forceful</p>\n"],
                    options_hi: ["<p>Memorable</p>\n", "<p>Eternal</p>\n",
                                "<p>Essential</p>\n", "<p>Forceful</p>\n"],
                    solution_en: "<p>6.(c)<strong> Essential</strong>- <span style=\"font-family: Cambria Math;\">absolutely necessary; extremely important.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Indispensabl</strong>e- </span><span style=\"font-family: Cambria Math;\">absolutely necessary.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Memorable-</strong> </span><span style=\"font-family: Cambria Math;\">worth remembering, especially because of being special or unusual.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Eternal</strong>- </span><span style=\"font-family: Cambria Math;\">lasting or existing forever.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Forceful- </span></strong><span style=\"font-family: Cambria Math;\">vigorous and powerful.</span></p>\n",
                    solution_hi: "<p>6.(c) <strong>Essential</strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2367;&#2357;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">absolutely necessary; extremely important.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Indispensable</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">absolutely ne</span><span style=\"font-family: Cambria Math;\">cessary.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Memorable</span></strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2351;&#2366;&#2342;&#2327;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">)- worth remembering, especially because of being special or unusual.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Eternal</span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2366;&#2358;&#2381;&#2357;&#2340;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">lasting or existing forever.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Forceful</span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2325;&#2381;&#2340;&#2367;&#2358;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">vigorous and powerful.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">transient </span></span><span style=\"font-family: Cambria Math;\">rains have caused harm to the crops in the north.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">transient</span> </span><span style=\"font-family: Cambria Math;\">rains have caused harm to the crops in the north.</span></p>\n",
                    options_en: ["<p>Perpetual</p>\n", "<p>Long</p>\n", 
                                "<p>Fleeting</p>\n", "<p>Stable</p>\n"],
                    options_hi: ["<p>Perpetual</p>\n", "<p>Long</p>\n",
                                "<p>Fleeting</p>\n", "<p>Stable</p>\n"],
                    solution_en: "<p>7.(c) <strong>Fleeting</strong><span style=\"font-family: Cambria Math;\"><strong>- </strong>lasting for a very short time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Transient</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> lasting only for a short time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Perpetual</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> never ending or changing.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Long</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> taking a great amount of time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Stable</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> not easily upse</span><span style=\"font-family: Cambria Math;\">t or disturbed.</span></p>\n",
                    solution_hi: "<p>7.(c) <strong>Fleeting (<span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2339;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2341;&#2366;&#2351;&#2368;</span></strong><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">- lasting for a very short time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Transient (</span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2339;&#2367;&#2325;</span></strong><span style=\"font-family: Cambria Math;\"><strong>)</strong> </span><span style=\"font-family: Cambria Math;\">- lasting only for a short time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Perpetual (</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2344;&#2381;&#2340;&#2352;</span></strong><span style=\"font-family: Cambria Math;\"><strong>)</strong> </span><span style=\"font-family: Cambria Math;\">- never ending or changing.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Long ( </span><span style=\"font-family: Cambria Math;\">&#2342;&#2368;&#2352;&#2381;&#2328;&#2325;&#2366;&#2354;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2354;&#2306;&#2348;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2340;&#2325;</span></strong><span style=\"font-family: Cambria Math;\"><strong>)</strong> </span><span style=\"font-family: Cambria Math;\">- taking a great amount of time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Stab</span><span style=\"font-family: Cambria Math;\">le (</span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2341;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2342;&#2371;&#2338;&#2364;</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\">- not easily upset or disturbed.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Prolific</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Prolific</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Productive</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Lazy</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Unproductive</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Unsuccessful</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">Productive</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Lazy</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">Unproductive</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Unsuccessful</span></p>\n"],
                    solution_en: "<p>8.(a)<strong> Productive</strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> being effective and achieving a lot of tasks or goals.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Prolific</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> producing or creating a large quantity of something, often in a successful or impressive way.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Lazy</span></strong><span style=\"font-family: Cambria Math;\"><strong>- </strong>avoiding work or being unwilling to put in effort.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Unproductive</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> not being effective in getting things done.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Unsuccessful</span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> failing to achieve the desired goals or outcomes.</span></p>\n",
                    solution_hi: "<p>8.(a<strong>) Productive (<span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span></strong><span style=\"font-family: Cambria Math;\">- being effective and achieving a lot of</span><span style=\"font-family: Cambria Math;\"> tasks or goals.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Prolific (</span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> producing or creating a large quantity of something, often in a successful or impressive way.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Lazy (</span><span style=\"font-family: Cambria Math;\">&#2310;&#2354;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> avoiding work or being unwilling to put in effort.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Unproductive (</span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2369;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\">- not being effective in g</span><span style=\"font-family: Cambria Math;\">etting things done.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Unsuccessful (</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2347;&#2354;</span></strong><span style=\"font-family: Cambria Math;\"><strong>)</strong> </span><span style=\"font-family: Cambria Math;\">- failing to achieve the desired goals or outcomes.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">His </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">conceit</span></span><span style=\"font-family: Cambria Math;\"> prevented him from accepting any criticism, even if it was meant to be </span><span style=\"font-family: Cambria Math;\">constructive.</span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">His </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">conceit </span></span><span style=\"font-family: Cambria Math;\">prevented </span><span style=\"font-family: Cambria Math;\">him from accepting any criticism, even if it was meant to be</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">constructive.</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">prejudice</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">modesty</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">cowardice</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">egotism</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">prejudice</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">modesty</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">cowardice</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">egotism</span></p>\n"],
                    solution_en: "<p>9.(d)</p>\r\n<p><strong>Egotism </strong><span style=\"font-family: Cambria Math;\"><strong>- </strong>excessive belief in one\'s own importance.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Conceit </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> excessive pride in oneself or one\'s abiliti</span><span style=\"font-family: Cambria Math;\">es.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Prejudice </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> an unfair feeling of dislike for a person or group.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Modesty </span></strong><span style=\"font-family: Cambria Math;\"><strong>- </strong>the quality of not being too proud or confident about yourself.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Cowardice </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> a lack of courage or bravery.</span></p>\n",
                    solution_hi: "<p>9.(d) <strong>Egotism (<span style=\"font-family: Cambria Math;\">&#2309;&#2361;&#2306;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> excessive belief in one\'s own importance.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">C</span><span style=\"font-family: Cambria Math;\">onceit (</span><span style=\"font-family: Cambria Math;\">&#2328;&#2350;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> excessive pride in oneself or one\'s abilities.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Prejudice (</span><span style=\"font-family: Cambria Math;\">&#2346;&#2325;&#2381;&#2359;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> an unfair feeling of dislike for a person or group.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Modesty (</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2344;&#2350;&#2381;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> the quality of not being too proud or confident about yourself.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Cowardice (</span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2351;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> a lack of courage or bravery.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Education gives people the knowledge and skills they need to stay healthy, get jobs and foster </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">tolerance.</span></span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Education gives people the knowledge and skills they need to stay healthy, get jobs and foster </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">tolerance.</span></span></p>\n",
                    options_en: ["<p>entrench</p>\n", "<p>distrust</p>\n", 
                                "<p>lethargy</p>\n", "<p>endurance</p>\n"],
                    options_hi: ["<p>entrench</p>\n", "<p>distrust</p>\n",
                                "<p>lethargy</p>\n", "<p>endurance</p>\n"],
                    solution_en: "<p>10.(d)<strong> Endurance- </strong><span style=\"font-family: Cambria Math;\">ability to withstand challenges, difficulties, or hardships.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Tolerance-</strong> </span><span style=\"font-family: Cambria Math;\">the ability to deal with something unpleasant or annoying.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Entrench-</span></strong><span style=\"font-family: Cambria Math;\"> to establish something firmly, making it difficult to change or remove.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Distrust-</span></strong><span style=\"font-family: Cambria Math;\"> lack of trust.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Lethargy- </span></strong><span style=\"font-family: Cambria Math;\">lack of energy or enthusiasm.</span></p>\n",
                    solution_hi: "<p>10.(d) <strong>Endurance</strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2344;&#2358;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ability to withstand challenges, difficulties, or hardships.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Tolerance</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2361;&#2344;&#2358;&#2368;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">)- the ability to deal with something unpleasant or annoying.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Entrench </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2350;&#2332;&#2348;&#2370;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;</span><span style=\"font-family: Cambria Math;\">&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)- to establish something firmly, making it difficult to change or remove.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Distrust</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2357;&#2367;&#2358;&#2381;&#2357;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\">)- lack of trust.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Lethargy</span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2354;&#2360;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">lack of energy or enthusiasm.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Precise</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Precise</span></p>\n",
                    options_en: ["<p>Vague</p>\n", "<p>General</p>\n", 
                                "<p>Accurate</p>\n", "<p>Ambiguous</p>\n"],
                    options_hi: ["<p>Vague</p>\n", "<p>General</p>\n",
                                "<p>Accurate</p>\n", "<p>Ambiguous</p>\n"],
                    solution_en: "<p>11.(c) <strong>Accurate </strong>- <span style=\"font-family: Cambria Math;\">correct or true.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Precise -</strong> </span><span style=\"font-family: Cambria Math;\">exact or accurate.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Vague </strong>- </span><span style=\"font-family: Cambria Math;\">not clear or specific.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>General -</strong> </span><span style=\"font-family: Cambria Math;\">applying to most or everyone.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Ambiguous </strong>- </span><span style=\"font-family: Cambria Math;\">having more than one possible meaning.</span></p>\n",
                    solution_hi: "<p>11.(c) <strong>Accurate</strong> <span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2342;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">correct or true.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Precise</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2319;&#2325;&#2342;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">exact or accurate.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Vague </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">)- not clear or specific.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>General</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">applying to most or everyone.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Ambiguous</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2375;&#2325;&#2366;&#2352;&#2381;&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> )-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">having more than one possible meaning.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym for the underlined word in the following sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">She\'s so<span style=\"text-decoration: underline;\"> </span></span><strong><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">benevolent</span></span></strong><span style=\"font-family: Cambria Math;\"> that she can\'t even harm a</span><span style=\"font-family: Cambria Math;\"> fly, let alone hurting someone.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym for the underlined word in the following sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">She\'s so </span><span style=\"text-decoration: underline;\"><strong><span style=\"font-family: Cambria Math;\">benevolent</span></strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>that she can\'t even harm a fly, let alone hurting someone.</span></p>\n",
                    options_en: ["<p>Gloomy</p>\n", "<p>Unfortunate</p>\n", 
                                "<p>Grudging</p>\n", "<p>Compassionate</p>\n"],
                    options_hi: ["<p>Gloomy</p>\n", "<p>Unfortunate</p>\n",
                                "<p>Grudging</p>\n", "<p>Compassionate</p>\n"],
                    solution_en: "<p>12.(d) <strong>Compassionate-</strong> <span style=\"font-family: Cambria Math;\">feeling or showing sympathy and concern for others.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Benevolent-</strong> </span><span style=\"font-family: Cambria Math;\">kind and helpful.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Gloomy-</span></strong><span style=\"font-family: Cambria Math;\"> dark </span><span style=\"font-family: Cambria Math;\">and depressing.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Unfortunate-</strong> </span><span style=\"font-family: Cambria Math;\">unlucky, unsuccessful or unhappy.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Grudging-</strong> </span><span style=\"font-family: Cambria Math;\">unwilling to give or allow something.</span></p>\n",
                    solution_hi: "<p>12.(d) <strong>Compassionate</strong> <span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2351;&#2366;&#2354;&#2369;</span><span style=\"font-family: Cambria Math;\">) - feeling or showing sympathy and concern for others.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Benevolent</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2379;&#2346;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">) - kind and helpful.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Gloomy</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2366;&#2358;</span><span style=\"font-family: Cambria Math;\">&#2366;&#2332;&#2344;&#2325;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2313;&#2342;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> ) - dark and depressing.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Unfortunate</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2352;&#2381;&#2349;&#2366;&#2327;&#2381;&#2351;&#2358;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">unlucky, unsuccessful or unhappy.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Grudging</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2344;&#2376;&#2330;&#2381;&#2331;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">unwilling to give or allow something.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the underlined word in the given sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I am i</span><span style=\"font-family: Cambria Math;\">n a difficult </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">predicament</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"> </span>as I have two offers at a time.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the underlined word in the given sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I am in a difficult </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">predicament</span></span><span style=\"font-family: Cambria Math;\"> as I have two offers at a time.</span></p>\n",
                    options_en: ["<p>Dilemma</p>\n", "<p>Success</p>\n", 
                                "<p>Offer</p>\n", "<p>Advantage</p>\n"],
                    options_hi: ["<p>Dilemma</p>\n", "<p>Success</p>\n",
                                "<p>Offer</p>\n", "<p>Advantage</p>\n"],
                    solution_en: "<p>13.(a) <strong>Dilemma </strong>- <span style=\"font-family: Cambria Math;\">a difficult situation requiring to choose between two or more equally bad options.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Predicament </strong>- </span><span style=\"font-family: Cambria Math;\">a difficult or dangerous situation.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Success </span></strong><span style=\"font-family: Cambria Math;\"><strong>-</strong> </span><span style=\"font-family: Cambria Math;\">the achievement of something that you have been trying to do.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Offer -</strong> </span><span style=\"font-family: Cambria Math;\">to give or propose something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Advantage </strong>- </span><span style=\"font-family: Cambria Math;\">a benefit or superiority.</span></p>\n",
                    solution_hi: "<p>13.(a)<strong> Dilemm</strong>a <span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2357;&#2367;&#2343;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a difficult situation requiring to choose between two or more equally bad options.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Predicamen</strong>t </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2336;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2352;&#2367;&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a difficult or dangerous situation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Success</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2347;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">the achievement of something that you have been trying to do.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Offer </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">to give or propose something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Advantage</span></strong><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a benefit or superiority.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the underlined word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Demand for water is also </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">rapidly.</span></span><span style=\"font-family: Cambria Math;\"> rising due to population growth, urbanisation and increasing pressures from the agriculture and energy sector.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the underlined word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Demand for water is also </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">rapidly.</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\"> </span>rising due to population growth, urbanisation and increasing pressures from the agriculture and energy sector.</span></p>\n",
                    options_en: ["<p>promptly</p>\n", "<p>luxuriously</p>\n", 
                                "<p>leisurely</p>\n", "<p><span style=\"font-family: Cambria Math;\"> hopefully</span></p>\n"],
                    options_hi: ["<p>promptly</p>\n", "<p>luxuriously</p>\n",
                                "<p>leisurely</p>\n", "<p>hopefully</p>\n"],
                    solution_en: "<p>14.(a) <strong>Promptly </strong>- <span style=\"font-family: Cambria Math;\">quickly and without delay.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Rapidly -</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>quickly.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Luxuriously -</strong> </span><span style=\"font-family: Cambria Math;\">in a way that is very comfortable and expensive.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Leisurely - </span></strong><span style=\"font-family: Cambria Math;\">done slowly and without hurry.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Hopefully -</strong> </span><span style=\"font-family: Cambria Math;\">with the hope that something go</span><span style=\"font-family: Cambria Math;\">od will happen.</span></p>\n",
                    solution_hi: "<p>14.(a) <strong>Promptly</strong> <span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2340;&#2340;&#2381;&#2325;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">quickly and without delay.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Rapidly</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2340;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">) - quickly.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Luxuriously</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2354;&#2366;&#2360;&#2367;&#2340;&#2366;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">in a way that is very comfortable and expensive.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Leisurely</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">) - done slowly and without hurry.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Hopefully</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2358;&#2366;&#2346;&#2370;&#2352;&#2381;&#2357;&#2325;</span><span style=\"font-family: Cambria Math;\">) - with the hope that something good will happen.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The Sustainable Development Goals are a call for action by all countries-poor, rich and middle-income-to </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">promote </span></span><span style=\"font-family: Cambria Math;\">prosperity w</span><span style=\"font-family: Cambria Math;\">hile protecting the planet.</span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The Sustainable Development Goals are a call for action by all countries-poor, rich and middle-income-to </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">promote</span> </span><span style=\"font-family: Cambria Math;\">prosperity while protecting the planet.</span></p>\n",
                    options_en: ["<p>conceal</p>\n", "<p>holistic</p>\n", 
                                "<p>growl</p>\n", "<p>boost</p>\n"],
                    options_hi: ["<p>conceal</p>\n", "<p>holistic</p>\n",
                                "<p>growl</p>\n", "<p>boost</p>\n"],
                    solution_en: "<p>15.(d)<strong> Boost -</strong> <span style=\"font-family: Cambria Math;\">to increase or improve something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Promote </strong>-</span><span style=\"font-family: Cambria Math;\"> to support or encourage something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Conceal - </span></strong><span style=\"font-family: Cambria Math;\">to hide something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Holistic </strong>-</span><span style=\"font-family: Cambria Math;\"> relating to o</span><span style=\"font-family: Cambria Math;\">r concerned with the whole of something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Growl -</span></strong><span style=\"font-family: Cambria Math;\"> make a low, deep sound like a dog.</span></p>\n",
                    solution_hi: "<p>15.(d) <strong>Boost</strong> <span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2348;&#2338;&#2364;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to increase or improve something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Promote</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2348;&#2338;&#2364;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to support or encourage something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Conceal</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2331;&#2367;&#2346;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to hide something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Holistic</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2327;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\">) - relating to or concerned with the whole of something.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Growl</strong> (</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2369;&#2352;&#2381;&#2352;&#2366;&#2361;&#2335;</span><span style=\"font-family: Cambria Math;\">) - make a low, deep sound like a dog.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>