<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">135:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 150</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">150</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 135 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["misc"] = {
                name: "Miscellaneous",
                start: 0,
                end: 149
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "misc",
                    question_en: "1. While tabulation of marks scored in an examination by the students of a class, by mistake the marks scored by one student got recovered as 93 in place of 63, and thereby the average marks increased by 0.5. What was the number of students in the class? ",
                    question_hi: "1. किसी कक्षा के छात्रों के द्वारा एक परीक्षा में प्राप्त किये गए अंकों को तालिकाबद्ध करने के दौरान, भूलवश एक छात्र के अंक को 63 के स्थान पर 93 लिख लिया गया, जिससे औसत अंक में 0.5 की वृद्धि हो गयी । इस कक्षा में छात्रों की संख्या कितनी थी ?  ",
                    options_en: [" 20", " 30", 
                                " 15", " 60"],
                    options_hi: [" 20", " 30",
                                " 15", " 60<br /> "],
                    solution_en: "1.(d)   The total difference of marks recorded when 93 is observed as 63 = 30 marks<br />And the average marks increased by 0.5. <br />So, the total number of students =  <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 60",
                    solution_hi: "1.(d) <br />93 को 63 के रूप में देखे जाने पर दर्ज किए गए अंकों का कुल अंतर = 30 अंक<br />और औसत अंकों में 0.5 की वृद्धि हुई।<br />अत: विद्यार्थियों की कुल संख्या = <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 60",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "misc",
                    question_en: "<p>2.The length of the shadow of a vertical pole on the ground is 36 m. If the angle of elevation of the sum at that time is &theta;, such that sec&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>12</mn></mfrac></math>, then what is the height (in cm ) of the pole ?</p>",
                    question_hi: "<p>2. भूतल पर एक अधोलंब खम्भे की परछाईं की लंबाई 36 मी है। यदि उस समय सूर्य की ऊँचाई का उन्नयन &theta; कोण इस प्रकार है कि sec&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>12</mn></mfrac></math>है, तो खम्भे की ऊँचाई (cm में) क्या है?</p>",
                    options_en: ["<p>12</p>", "<p>18</p>", 
                                "<p>9</p>", "<p>15</p>"],
                    options_hi: ["<p>12</p>", "<p>18</p>",
                                "<p>9</p>", "<p>15</p>"],
                    solution_en: "<p>2.(d) sec<math display=\"inline\"><mi>&#952;</mi><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>12</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">H</mi><mi mathvariant=\"normal\">B</mi></mfrac></math><br>If &ang; C = <math display=\"inline\"><mi>&#952;</mi><mo>&#176;</mo></math> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642930880.png\" alt=\"rId4\" width=\"150\" height=\"102\"><br>If shadow = 36 m<br>Then height of the pole = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 5 = 15 m</p>",
                    solution_hi: "<p>2.(d) दिया गया, sec<math display=\"inline\"><mi>&#952;</mi><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>12</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">H</mi><mi mathvariant=\"normal\">B</mi></mfrac></math><br>यदि &ang;C = <math display=\"inline\"><mi>&#952;</mi></math>&deg; <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642930880.png\" alt=\"rId4\" width=\"150\" height=\"102\"><br>यदि छाया = 36 m<br>तब खम्भे की ऊँचाई = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> &times; 5 = 15 m</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "misc",
                    question_en: "<p>3. The cost price of an article is ₹2800. Profit as a percentage of selling price is 20 percent. What is the actual profit (in ₹)?</p>",
                    question_hi: "<p>3. एक वस्तु का क्रय मूल्य ₹2800 है। विक्रय मूल्य के प्रतिशत के रूप में लाभ 20 प्रतिशत है। वास्तविक लाभ का मान (₹ में) क्या है?</p>",
                    options_en: ["<p>₹ 616</p>", "<p>₹ 504</p>", 
                                "<p>₹ 700</p>", "<p>₹ 560</p>"],
                    options_hi: ["<p>₹ 616</p>", "<p>₹ 504</p>",
                                "<p>₹ 700</p>", "<p>₹ 560</p>"],
                    solution_en: "<p>3.(c) Let the SP of an article = 100 unit <br>Then CP of an article = 100 - 20 = 80 unit<br>Since, 80 unit corresponds to ₹2800<br>Then, the actual profit = <math display=\"inline\"><mfrac><mrow><mn>2800</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> &times; 20&nbsp;= 35 &times; 20 = ₹700</p>",
                    solution_hi: "<p>3.(c) माना, एक वस्तु का विक्रय मूल्य = 100 इकाई <br>तो एक वस्तु का क्रय मूल्य = 100 - 20 = 80 इकाई <br>चूंकि, 80 इकाई ₹2800 से मेल खाती है। <br>फिर , वास्तविक लाभ = <math display=\"inline\"><mfrac><mrow><mn>2800</mn></mrow><mrow><mn>80</mn></mrow></mfrac></math> &times; 20&nbsp;= 35 &times; 20 = ₹700</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "misc",
                    question_en: "<p>4. ABC is an equilateral triangle with side 12 cm. What is the length of the radius of the circle inscribed in it ?</p>",
                    question_hi: "<p>4. ABC एक समबाहु त्रिभुज है जिसकी भुजा 12 सेमी है। इसमें उत्कीर्णित अंतर्वृत्त की त्रिज्या की लंबाई कितनी है?</p>",
                    options_en: ["<p>2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>8<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", 
                                "<p>4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p>2<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>8<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>",
                                "<p>4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>6<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>4.(a) Length of radius of the circle inscribed in it = <math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac><mo>=</mo><mfrac><mn>6</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>=</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>4.(a) इसमें अंतर्वृत्त की त्रिज्या की लंबाई = <math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mrow><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow></mfrac><mo>=</mo><mfrac><mn>6</mn><msqrt><mn>3</mn></msqrt></mfrac><mo>=</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></math> cm</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "misc",
                    question_en: "<p>5. The numbers 25, 34, 46, 48, 2<math display=\"inline\"><mi>x</mi></math> + 1, 3x + 3, 102, 110, 114, 122 are written in ascending order and their mean is 73. The value of x is:</p>",
                    question_hi: "<p>5. संख्याएँ 25, 34, 46, 48, 2<math display=\"inline\"><mi>x</mi></math> + 1, 3x + 3, 102, 110, 114, 122 आरोही क्रम में लिखी गई हैं और उनका माध्य 73 है। x का मान है:</p>",
                    options_en: ["<p>22</p>", "<p>24</p>", 
                                "<p>28</p>", "<p>25</p>"],
                    options_hi: ["<p>22</p>", "<p>24</p>",
                                "<p>28</p>", "<p>25</p>"],
                    solution_en: "<p>5.(d) Here total number of observations = 10 (even)<br>Mean = <math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>u</mi><mi>m</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>o</mi><mi>b</mi><mi>s</mi><mi>e</mi><mi>r</mi><mi>v</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mi>s</mi></mrow><mrow><mi>N</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>o</mi><mi>b</mi><mi>s</mi><mi>e</mi><mi>r</mi><mi>v</mi><mi>a</mi><mi>t</mi><mi>i</mi><mi>o</mi><mi>n</mi><mi>s</mi></mrow></mfrac></math> <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>34</mn><mo>+</mo><mn>46</mn><mo>+</mo><mn>48</mn><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>1</mn><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>3</mn><mo>+</mo><mn>102</mn><mo>+</mo><mn>110</mn><mo>+</mo><mn>114</mn><mo>+</mo><mn>122</mn></mrow><mn>10</mn></mfrac></math><br>73 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>605</mn><mo>)</mo></mrow><mn>10</mn></mfrac></math><br>730 = 5<math display=\"inline\"><mi>x</mi></math> + 605 &rArr; 125 = 5x &rArr; x = 25</p>",
                    solution_hi: "<p>5.(d)&nbsp;यहाँ प्रेक्षणों की कुल संख्या = 10 (सम)<br>माध्य = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2357;&#2354;&#2379;&#2325;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2351;&#2379;&#2327;</mi></mrow><mrow><mi>&#2309;&#2357;&#2354;&#2379;&#2325;&#2344;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>25</mn><mo>+</mo><mn>34</mn><mo>+</mo><mn>46</mn><mo>+</mo><mn>48</mn><mo>+</mo><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>1</mn><mo>+</mo><mn>3</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>3</mn><mo>+</mo><mn>102</mn><mo>+</mo><mn>110</mn><mo>+</mo><mn>114</mn><mo>+</mo><mn>122</mn></mrow><mn>10</mn></mfrac></math><br>73 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>5</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>605</mn><mo>)</mo></mrow><mn>10</mn></mfrac></math><br>730 = 5<math display=\"inline\"><mi>x</mi></math> + 605 &rArr; 125 = 5x &rArr; x = 25</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "misc",
                    question_en: "<p>6. Two persons A and B invested in a business with Rs.154000 and Rs.110000 respectively. They agree that 40% of the profit should be divided equally among them and rest is divided between them according to their investment. If A got 2520 rupee more than B, then find the total profit. (In Rs.)</p>",
                    question_hi: "<p>6. दो व्यक्ति A और B एक व्यापार में क्रमश: Rs.154000 और Rs. 110000 की राशि निवेश करते हैं। वे सहमत होते हैं कि लाभ के 40% को उनके बीच बराबर हिस्सों में बांटा जाएगा और शेष लाभ को उनके निवेश के अनुसार बांटा जाएगा। यदि A को B की तुलना में Rs.2520 अधिक प्राप्त होते हैं, तो कुल लाभ ज्ञात कीजिए। (Rs. में)</p>",
                    options_en: ["<p>25200</p>", "<p>25215</p>", 
                                "<p>25205</p>", "<p>25225</p>"],
                    options_hi: ["<p>25200</p>", "<p>25215</p>",
                                "<p>25205</p>", "<p>25225</p>"],
                    solution_en: "<p>6.(a)<br>Ratio &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;A &nbsp; &nbsp; &nbsp; : &nbsp; &nbsp; B<br><strong id=\"docs-internal-guid-53e87fd5-7fff-54a2-eda3-e51173fe4088\"></strong>Amount&nbsp;<math display=\"inline\"><mo>&#8594;</mo></math> 154000 : 110000 &rArr; (7 : 5)<br>Remaining 60% of profit,<math display=\"inline\"><mo>&#8594;</mo></math> 12 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 7.2<br>A&rsquo;s share = 7.2 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mrow><mn>7</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = 4.2 units<br>B&rsquo;s share = 7.2 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>7</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = 3 units<br>Difference between A and B profit <br>(1.2 units ) = 2520 ₹<br>Total profit (7 + 5 units) = <math display=\"inline\"><mfrac><mrow><mn>2520</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math> &times; 12&nbsp;= 25200 ₹</p>",
                    solution_hi: "<p>6.(a) <br>अनुपात &rarr;&nbsp; &nbsp;A &nbsp; &nbsp; : &nbsp; &nbsp; B<br><strong id=\"docs-internal-guid-4b338fc4-7fff-a9f8-72a8-92e4adae8b6f\"></strong>राशि &rarr; 154000 : 110000 (7 : 5)<br>शेष लाभ का 60% <math display=\"inline\"><mo>&#8594;</mo></math> 12 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math> = 7.2<br>A का हिस्सा = 7.2 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mrow><mn>7</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = 4.2 इकाई <br>B का हिस्सा = 7.2 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>7</mn><mo>+</mo><mn>5</mn></mrow></mfrac></math> = 3 इकाई <br>A और B के लाभ का अंतर (1.2 इकाई ) = 2520<br>कुल लाभ (7 + 5 इकाई) = <math display=\"inline\"><mfrac><mrow><mn>2520</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>2</mn></mrow></mfrac></math> &times; 12 = 25200 ₹</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "misc",
                    question_en: "<p>7. Volume of a cuboid is 4800 cm&sup3;. If the height of this cuboid is 20 cm, then what will be the area of the base of the cuboid ?</p>",
                    question_hi: "<p>7. एक घनाभ का आयतन 4800 cm&sup3; है। यदि इस घनाभ की ऊंचाई 20 cm है, तो घनाभ के आधार का क्षेत्रफल कितना होगा?</p>",
                    options_en: ["<p>150 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>240 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", 
                                "<p>480 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>120 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    options_hi: ["<p>150 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>240 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>",
                                "<p>480 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>120 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    solution_en: "<p>7.(b) Volume of cuboid = length <math display=\"inline\"><mo>&#215;</mo></math> breadth &times; height<br>4800 = length <math display=\"inline\"><mo>&#215;</mo></math> breadth &times; 20<br>length <math display=\"inline\"><mo>&#215;</mo></math> breadth = 240<br>So, area of base of cuboid <br>= length <math display=\"inline\"><mo>&#215;</mo></math> breadth = 240 cm&sup2;</p>",
                    solution_hi: "<p>7.(b) घनाभ का आयतन = लंबाई &times; चौड़ाई &times; ऊंचाई<br>4800 = लंबाई &times; चौड़ाई &times; 20<br>लंबाई &times; चौड़ाई = 240<br>अत: घनाभ के आधार का क्षेत्रफल = लंबाई &times; चौड़ाई = 240 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "misc",
                    question_en: "<p>8. The marked price of an article is 40% above the cost price. If its selling price is 73<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% of the marked price, then the profit percentage is:</p>",
                    question_hi: "<p>8. एक वस्तु का अंकित मूल्य लागत मूल्य से 40% अधिक है। यदि इसका विक्रय मूल्य अंकित मूल्य का 73<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% है, तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>2.7%</p>", "<p>2.4%</p>", 
                                "<p>2.9%</p>", "<p>3.1%</p>"],
                    options_hi: ["<p>2.7%</p>", "<p>2.4%</p>",
                                "<p>2.9%</p>", "<p>3.1%</p>"],
                    solution_en: "<p>8.(c) Let CP = <math display=\"inline\"><mi>x</mi></math> , MP = 1.4x<br>SP = 1.4<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>73</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = 1.029x<br>Profit = 0.029<math display=\"inline\"><mi>x</mi></math><br>Profit percentage = 2.9%</p>",
                    solution_hi: "<p>8.(c) माना क्र.मू. = x<br>अंकित मूल्य = 1.4x<br>वि.मू.= 1.4<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>73</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = 1.029x<br>लाभ = 0.029x<br>लाभ प्रतिशत = 2.9%</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "misc",
                    question_en: "<p>9. If <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + x = 1, then find <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3000</mn></msup></mfrac><mo>+</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3000</mn></msup></math>.</p>",
                    question_hi: "<p>9. यदि <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + x = 1,है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">x</mi><mn>3000</mn></msup></mfrac><mo>+</mo><msup><mi mathvariant=\"normal\">x</mi><mn>3000</mn></msup></math> ज्ञात कीजिए</p>",
                    options_en: ["<p>2</p>", "<p>-2</p>", 
                                "<p>4</p>", "<p>0</p>"],
                    options_hi: ["<p>2</p>", "<p>-2</p>",
                                "<p>4</p>", "<p>0</p>"],
                    solution_en: "<p>9.(a) <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + x = 1<br>Then <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math> = - 1<br>Now,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3000</mn></msup></mfrac><mo>+</mo><msup><mi>x</mi><mn>3000</mn></msup><mo>=</mo><mfrac><mn>1</mn><msup><mrow><mo>(</mo><msup><mi>x</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>1000</mn></msup></mfrac><mo>+</mo><msup><mrow><mo>(</mo><msup><mi>x</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>1000</mn></msup></math> = 1 + 1 = 2</p>",
                    solution_hi: "<p>9.(a) <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> + x = 1<br>तो, <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></math> = - 1<br>अब,<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3000</mn></msup></mfrac><mo>+</mo><msup><mi>x</mi><mn>3000</mn></msup><mo>=</mo><mfrac><mn>1</mn><msup><mrow><mo>(</mo><msup><mi>x</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>1000</mn></msup></mfrac><mo>+</mo><msup><mrow><mo>(</mo><msup><mi>x</mi><mn>3</mn></msup><mo>)</mo></mrow><mn>1000</mn></msup></math> = 1 + 1 = 2</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "<p>10. A speaks truth in 75% cases and B in 80% cases. What is the probability that they contradict each other in stating the same fact ?</p>",
                    question_hi: "<p>10. A 75% मामलों में और B 80% मामलों में सच बोलते है किसी समान तथ्य को बताते समय प्रायिकता क्या होगी कि दोनों एक-दूसरे का विरोधाभास करे ?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>10.(a) A speak truth in 75% cases<br>so, P(A) = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &rArr; <img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAWCAYAAAChWZ5EAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAALXSURBVEhL7ZQ9SLJRFMf/fkU12FBZEWVLBNESBCVJWxi4ZC0FDi3R1OTS1O7qUlRDQw6FzREiOBQkDSVBKAZ9ihpFGkYfave953q1zKd6fd+hpR9cvM/fe597nnv+56gYBz+IWv7+GL8B/HgA/21ClUolZ5/z1RG/VfDPAYRCIbhcLuRyOal8zuHhIZaXl5HNZqXyRjGA29tbjIyMiJwWhl6vh81mw97eXkkeI5EI5ubmMDw8DI1GI9XP6enpwd3dHTY2Nsr9QB4gbm5umMViYUtLS2JOIx6Ps4WFBdbV1cV2dnbEuqenJ+ZwONjm5qZ4VuL09JQNDg4W9xBXV1dsdHSUBYNBqeQpS0FtbS3q6+vFaGpqwvT0NMbGxuD1ekX0/AXi+k0mk9xRCl3z6uoq+AdIJU9ra6u4YY/HU5K2bz2gVqtRVVWFl5cXvL6+Ynd3F319fWhubpYrSqF8NzY2YmhoCGdnZ1LNQ0EfHBwgkUhI5ZsA6MBAIIDt7W309/fj+fkZx8fH6OzsVKz/+/t7bG1tYXx8HA0NDVJ9o6WlBZlMBhcXF1JRCMButxdNSAabmJjA1NQUrFYrHh8fcXl5iY6ODrm6FL/fj4GBAXE7NTU1SKfT8p881dXVMBgMOD8/l4pCANyEIn80kskkuKEwMzMDrVYrVygTjUaFP8xms1SA6+trOctD7yCPvS/HL01YV1eneNUfIXOSuebn58V+2kPzj9A6MuD7j/nWhO+ha21raysz19HREcLhMFKplDiExtraGngZg5etXAU8PDyIFBqNRqlUGAB9XXd3t2hEdAhBvlhZWRHGo8ZFkHmp8RQqp0AsFoNOp0N7e7tUKgyA4A1GlBp1TqqKxcVFrK+v4+TkRDwTPp8Ps7OzomSpixbY399Hb2+v6C9F+JdUxN90QiV47bPJycmyTlhxAAS1Wt4dGe8JUvkaXvvM6XQyt9vNeEqkSjD2B8Urc/A4JV+BAAAAAElFTkSuQmCC\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>B speak truth in 80 % cases<br>so,&nbsp;P(B) = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &rArr; <img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAUCAYAAACaq43EAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAALTSURBVEhL1VW9S/JRFH58NcMiKjIKIiqiLLCPpaFaBNcWx7bAwaU/oiWIJmkqs6kEh8jFoYaGpqIQqcwKCRKXUPv+wr7ue8/x/qQyzAreeB+4+Dvndy7Pved5fkedkMAv4I/6/ef48Y11Op16Koz3NP9fqzOZDDweDyKRiMoUxtzcHDY2NlT0jtjn83HrXq+Ojg5MTk7i+vpaVQHPz8+YmprC+fk5vy8GQ0ND8Hq92N/fzyao1RoWFhaE3W4XsVhMpNNpXuFwWDgcDjE6Oiru7u64bmtrSwwPD4uzszOOCQcHB6Knp4dky62mpiYxNjYmbm5uuGZlZUWMjIyIy8tLkddqg8GA6upq1NTU8Ort7YXcjO3tbcTjcW7x/Pw85AG5TkN7eztcLhcveUC8vLxgeXkZq6urCAaDXNPf3895efDiNDYajfz78PCAk5MTHB0dYXBwkHMaSJaLiwv09fXBZDJx3NDQgObmZo4JFRUVGBgY4MN8Snx1dQW/34/6+no0NjbyrZ+enlBbW6sqsiAPHB8fMzGB9s3MzECv1785ZGdnJ3Z2dvKJpQ4wm805c1VWVnLh+Pg4tzaRSPAtysrK1I4spN7scKlzbl9VVRWmp6dZMg2UIynyiEk7aS5IY/EiTZeWltDW1qYqPgZ1oqurK6cveYK+kt3dXVWRRWlpKUpKSvKJ35tL0/czhEIhWCyWnL50UIoPDw9VRRbS3Py+KHO9BulMWtLNNNzf3zOB1WpVGbDRyIR0kddIJpOc+zKx/DZ5YyqVUhnwIDk9PUVLSwvH9EwDpry8HDabjXMaaIB0d3d/nZjc3drayq0lkAcCgQAWFxe5vdRGcjHpODs7i7q6Oq4j3N7eYnNzk330ZnIVi48mVzFYW1sTTqeTJ9e3iKVrhdvtFhMTE+Lx8VFlC0MOHiaNRqMcf4uYQIRy6Iu9vT2VKQyqXV9fV5EQv/R/DPwFq4an0FKpJiEAAAAASUVORK5CYII=\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>Probability = <img src=\"data:image/png;base64,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\"><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>20</mn></mfrac></math></p>",
                    solution_hi: "<p>10.(a) A 75% मामलों में सच बोलता है | <br>इसलिए , P(A) = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &rArr; <img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAWCAYAAAChWZ5EAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAALXSURBVEhL7ZQ9SLJRFMf/fkU12FBZEWVLBNESBCVJWxi4ZC0FDi3R1OTS1O7qUlRDQw6FzREiOBQkDSVBKAZ9ihpFGkYfave953q1zKd6fd+hpR9cvM/fe597nnv+56gYBz+IWv7+GL8B/HgA/21ClUolZ5/z1RG/VfDPAYRCIbhcLuRyOal8zuHhIZaXl5HNZqXyRjGA29tbjIyMiJwWhl6vh81mw97eXkkeI5EI5ubmMDw8DI1GI9XP6enpwd3dHTY2Nsr9QB4gbm5umMViYUtLS2JOIx6Ps4WFBdbV1cV2dnbEuqenJ+ZwONjm5qZ4VuL09JQNDg4W9xBXV1dsdHSUBYNBqeQpS0FtbS3q6+vFaGpqwvT0NMbGxuD1ekX0/AXi+k0mk9xRCl3z6uoq+AdIJU9ra6u4YY/HU5K2bz2gVqtRVVWFl5cXvL6+Ynd3F319fWhubpYrSqF8NzY2YmhoCGdnZ1LNQ0EfHBwgkUhI5ZsA6MBAIIDt7W309/fj+fkZx8fH6OzsVKz/+/t7bG1tYXx8HA0NDVJ9o6WlBZlMBhcXF1JRCMButxdNSAabmJjA1NQUrFYrHh8fcXl5iY6ODrm6FL/fj4GBAXE7NTU1SKfT8p881dXVMBgMOD8/l4pCANyEIn80kskkuKEwMzMDrVYrVygTjUaFP8xms1SA6+trOctD7yCPvS/HL01YV1eneNUfIXOSuebn58V+2kPzj9A6MuD7j/nWhO+ha21raysz19HREcLhMFKplDiExtraGngZg5etXAU8PDyIFBqNRqlUGAB9XXd3t2hEdAhBvlhZWRHGo8ZFkHmp8RQqp0AsFoNOp0N7e7tUKgyA4A1GlBp1TqqKxcVFrK+v4+TkRDwTPp8Ps7OzomSpixbY399Hb2+v6C9F+JdUxN90QiV47bPJycmyTlhxAAS1Wt4dGe8JUvkaXvvM6XQyt9vNeEqkSjD2B8Urc/A4JV+BAAAAAElFTkSuQmCC\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>B 80% मामलों में सच बोलता है |<br>इसलिए, P(B) =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &rArr; <img src=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAUCAYAAACaq43EAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAALTSURBVEhL1VW9S/JRFH58NcMiKjIKIiqiLLCPpaFaBNcWx7bAwaU/oiWIJmkqs6kEh8jFoYaGpqIQqcwKCRKXUPv+wr7ue8/x/qQyzAreeB+4+Dvndy7Pved5fkedkMAv4I/6/ef48Y11Op16Koz3NP9fqzOZDDweDyKRiMoUxtzcHDY2NlT0jtjn83HrXq+Ojg5MTk7i+vpaVQHPz8+YmprC+fk5vy8GQ0ND8Hq92N/fzyao1RoWFhaE3W4XsVhMpNNpXuFwWDgcDjE6Oiru7u64bmtrSwwPD4uzszOOCQcHB6Knp4dky62mpiYxNjYmbm5uuGZlZUWMjIyIy8tLkddqg8GA6upq1NTU8Ort7YXcjO3tbcTjcW7x/Pw85AG5TkN7eztcLhcveUC8vLxgeXkZq6urCAaDXNPf3895efDiNDYajfz78PCAk5MTHB0dYXBwkHMaSJaLiwv09fXBZDJx3NDQgObmZo4JFRUVGBgY4MN8Snx1dQW/34/6+no0NjbyrZ+enlBbW6sqsiAPHB8fMzGB9s3MzECv1785ZGdnJ3Z2dvKJpQ4wm805c1VWVnLh+Pg4tzaRSPAtysrK1I4spN7scKlzbl9VVRWmp6dZMg2UIynyiEk7aS5IY/EiTZeWltDW1qYqPgZ1oqurK6cveYK+kt3dXVWRRWlpKUpKSvKJ35tL0/czhEIhWCyWnL50UIoPDw9VRRbS3Py+KHO9BulMWtLNNNzf3zOB1WpVGbDRyIR0kddIJpOc+zKx/DZ5YyqVUhnwIDk9PUVLSwvH9EwDpry8HDabjXMaaIB0d3d/nZjc3drayq0lkAcCgQAWFxe5vdRGcjHpODs7i7q6Oq4j3N7eYnNzk330ZnIVi48mVzFYW1sTTqeTJ9e3iKVrhdvtFhMTE+Lx8VFlC0MOHiaNRqMcf4uYQIRy6Iu9vT2VKQyqXV9fV5EQv/R/DPwFq4an0FKpJiEAAAAASUVORK5CYII=\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math><br>प्रायिकता = <img src=\"data:image/png;base64,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\"><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>=</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>=</mo><mfrac><mn>7</mn><mn>20</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "misc",
                    question_en: "<p>11. What will be the remainder when <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>265</mn><mo>)</mo></mrow><mn>4081</mn></msup></math> + 9 is divided by 266 ?</p>",
                    question_hi: "<p>11. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>265</mn><mo>)</mo></mrow><mn>4081</mn></msup></math> + 9 को 266 से विभाजित करने पर प्राप्त शेषफल ज्ञात कीजिए ।</p>",
                    options_en: ["<p>8</p>", "<p>6</p>", 
                                "<p>1</p>", "<p>9</p>"],
                    options_hi: ["<p>8</p>", "<p>6</p>",
                                "<p>1</p>", "<p>9</p>"],
                    solution_en: "<p>11.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mfenced separators=\"|\"><mn>265</mn></mfenced><mn>4081</mn></msup><mo>+</mo><mn>9</mn></mrow><mn>266</mn></mfrac></math> = Rem. <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mfenced separators=\"|\"><mrow><mn>266</mn><mo>-</mo><mn>1</mn></mrow></mfenced><mn>4081</mn></msup><mo>+</mo><mn>9</mn></mrow><mn>266</mn></mfrac></math> = Rem. <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mfenced separators=\"|\"><mrow><mo>-</mo><mn>1</mn></mrow></mfenced><mn>4081</mn></msup><mo>+</mo><mn>9</mn></mrow><mn>266</mn></mfrac></math> = Rem. <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfenced separators=\"|\"><mrow><mo>-</mo><mn>1</mn></mrow></mfenced><mo>+</mo><mn>9</mn></mrow><mn>266</mn></mfrac></math> = 8 Rem.</p>",
                    solution_hi: "<p>11.(a) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mfenced separators=\"|\"><mn>265</mn></mfenced><mn>4081</mn></msup><mo>+</mo><mn>9</mn></mrow><mn>266</mn></mfrac></math>&nbsp;= शेषफल <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mfenced separators=\"|\"><mrow><mn>266</mn><mo>-</mo><mn>1</mn></mrow></mfenced><mn>4081</mn></msup><mo>+</mo><mn>9</mn></mrow><mn>266</mn></mfrac></math> = शेषफल <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mfenced separators=\"|\"><mrow><mo>-</mo><mn>1</mn></mrow></mfenced><mn>4081</mn></msup><mo>+</mo><mn>9</mn></mrow><mn>266</mn></mfrac></math> = शेषफल <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfenced separators=\"|\"><mrow><mo>-</mo><mn>1</mn></mrow></mfenced><mo>+</mo><mn>9</mn></mrow><mn>266</mn></mfrac></math> = 8 शेषफल</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "misc",
                    question_en: "<p>12. A and B start moving from places X to Y and Y to X, respectively, at the same time on the same day. After crossing each other, A and B take <math display=\"inline\"><mn>5</mn><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math>hours and 9 hours, respectively, to reach their respective destination. If the speed of A is 33 km/hr, then the speed (in km/hr) of B is:</p>",
                    question_hi: "<p>12. A और B, एक ही दिन में एक ही समय पर क्रमशः स्थान X से Y और Y से X की ओर चलना शुरू करते हैं। एक-दूसरे को पार करने के बाद, A और B को अपने-अपने गंतव्य तक पहुंचने में क्रमशः <math display=\"inline\"><mn>5</mn><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math> घंटे और 9 घंटे लगते हैं। यदि A की गति 33 किमी/घंटा है, तो B की गति (किमी/घंटा में) है:</p>",
                    options_en: ["<p>22</p>", "<p>25<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p>24<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>2</p>"],
                    options_hi: ["<p>22</p>", "<p>25<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p>24<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>2</p>"],
                    solution_en: "<p>12.(b)<br><strong>Formula:-</strong> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>s</mi><mn>1</mn></msub><msub><mi>s</mi><mn>2</mn></msub></mfrac><mo>=</mo><msqrt><mfrac><msub><mi>t</mi><mn>2</mn></msub><msub><mi>t</mi><mn>1</mn></msub></mfrac></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><msub><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>9</mn></mrow><mn>49</mn></mfrac></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><msub><mi mathvariant=\"normal\">s</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>7</mn></mfrac><mo>,</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">s</mi><mn>2</mn></msub><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mo>&#215;</mo><mn>33</mn><mo>=</mo><mn>25</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mi>km</mi><mo>/</mo><mi mathvariant=\"normal\">h</mi></math></p>",
                    solution_hi: "<p>12.(b)<br><strong>सूत्र:-</strong> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>s</mi><mn>1</mn></msub><msub><mi>s</mi><mn>2</mn></msub></mfrac><mo>=</mo><msqrt><mfrac><msub><mi>t</mi><mn>2</mn></msub><msub><mi>t</mi><mn>1</mn></msub></mfrac></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><msub><mrow><mi>s</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>9</mn><mo>&#215;</mo><mn>9</mn></mrow><mn>49</mn></mfrac></msqrt></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>33</mn><msub><mi mathvariant=\"normal\">s</mi><mn>2</mn></msub></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>7</mn></mfrac><mo>,</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">s</mi><mn>2</mn></msub><mo>=</mo><mfrac><mn>7</mn><mn>9</mn></mfrac><mo>&#215;</mo><mn>33</mn><mo>=</mo><mn>25</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mi>km</mi><mo>/</mo><mi mathvariant=\"normal\">h</mi></math></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "misc",
                    question_en: "<p>13. The distribution of 50 workmen employed in a factory according to their monthly salary, showed an average salary of ₹2250 per month. If the median salary is found to be ₹2450, find the mode salary.</p>",
                    question_hi: "<p>13. एक कारखाने में कार्यरत 50 श्रमिकों का उनके मासिक वेतन के अनुसार वितरण, औसत वेतन ₹2250 प्रति माह दर्शाता है। यदि माध्यिका वेतन ₹2450 पाया जाता है, तो बहुलक वेतन ज्ञात करें।</p>",
                    options_en: ["<p>₹2,800</p>", "<p>₹2,750</p>", 
                                "<p>₹2,850</p>", "<p>₹2,900</p>"],
                    options_hi: ["<p>₹2,800</p>", "<p>₹2,750</p>",
                                "<p>₹2,850</p>", "<p>₹2,900</p>"],
                    solution_en: "<p>13.(c)&nbsp;Mean(average) = 2250, Median = 2450<br>We know that,<br>Mode = 3 &times; Median - 2 &times; Mean <br>Mode = 3 &times; 2450 - 2 &times; 2250<br>Mode = 2850</p>",
                    solution_hi: "<p>13.(c)&nbsp;माध्य (औसत) = 2250, माध्यिका = 2450<br>हम जानते हैं कि,<br>बहुलक = 3 &times; माध्यिका - 2 &times; माध्य<br>बहुलक = 3 &times; 2450 - 2 &times; 2250<br>बहुलक = 2850</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "misc",
                    question_en: "<p>14. A milkman have mixture of 240 ℓ, which contains milk and water in the ratio of 5 : 3. Milkman sold 64ℓ of mixture and added 14ℓ water in remaining mixture and again sold 76ℓ of mixture. Find milk percentage in the final mixture ?</p>",
                    question_hi: "<p>14. एक दूधवाले के पास 240ℓ का मिश्रण है, जिसमें दूध और पानी 5 : 3 के अनुपात में है। दूधवाले ने 64ℓ मिश्रण बेचा और शेष मिश्रण में 14 ℓ पानी मिलाया और फिर से 76ℓ मिश्रण बेचा। अंतिम मिश्रण में दूध का प्रतिशत ज्ञात कीजिये?</p>",
                    options_en: ["<p>52<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>", "<p>55<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>", 
                                "<p>57<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>", "<p>53<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>52<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>", "<p>55<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>",
                                "<p>57<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>", "<p>53<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>19</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>14.(c)&nbsp;Remaining mixture = 240 - 64 = 176 ℓ<br>Then, quantity of milk = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> &times; 176 = 110 ℓ<br>And quantity of water = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> &times; 176 = 66 ℓ<br>Final quantity of water = 66ℓ + 14ℓ = 80 ℓ<br>Now the ratio of milk and water = 110 : 80 = 11 : 8<br>Again, 76 ℓ of mixture is taken out from the remaining mixture.<br>So, the remaining mixture = (110 + 80) - 76 = 114ℓ<br>Now, quantity of milk = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mrow><mn>11</mn><mo>+</mo><mn>8</mn></mrow></mfrac></math> &times; 114 = 66 ℓ<br>And quantity of water = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mrow><mn>11</mn><mo>+</mo><mn>8</mn></mrow></mfrac></math> &times; 114 = 48 ℓ<br>Required % = <math display=\"inline\"><mfrac><mrow><mn>66</mn></mrow><mrow><mn>114</mn></mrow></mfrac></math> &times; 100 = 57<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>19</mn></mfrac></math>%</p>",
                    solution_hi: "<p>14.(c)&nbsp;शेष मिश्रण = 240 - 64 = 176ℓ<br>फिर, दूध की मात्रा = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> &times; 176 = 110 ℓ<br>और पानी की मात्रा = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mn>5</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> &times; 176 = 66 ℓ<br>पानी की अंतिम मात्रा = 66ℓ + 14ℓ = 80ℓ<br>अब दूध और पानी का अनुपात = 110 : 80 = 11 : 8<br>पुनः शेष मिश्रण से 76ℓ मिश्रण निकाल लिया जाता है।<br>तो, बचा हुआ मिश्रण = (110 + 80) - 76 = 114ℓ<br>अब, दूध की मात्रा = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mrow><mn>11</mn><mo>+</mo><mn>8</mn></mrow></mfrac></math> &times; 114 = 66 ℓ<br>और पानी की मात्रा = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mrow><mn>11</mn><mo>+</mo><mn>8</mn></mrow></mfrac></math> &times; 114 = 48 ℓ<br>आवश्यक % = <math display=\"inline\"><mfrac><mrow><mn>66</mn></mrow><mrow><mn>114</mn></mrow></mfrac></math> &times; 100 = 57<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>17</mn><mn>19</mn></mfrac></math>%</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Find the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">k</mi><mo>+</mo><mn>19</mn></msqrt></math>, where k is average of 11, 20, 13, 16 and 15.</p>",
                    question_hi: "<p>15. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">k</mi><mo>+</mo><mn>19</mn></msqrt></math>&nbsp;का मान ज्ञात कीजिये, जहां 11, 20, 13, 16 और 15 का औसत K है।</p>",
                    options_en: ["<p>10</p>", "<p>8</p>", 
                                "<p>7</p>", "<p>9</p>"],
                    options_hi: ["<p>10</p>", "<p>8</p>",
                                "<p>7</p>", "<p>9</p>"],
                    solution_en: "<p>15.(c) According to the question,<br>k = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>20</mn><mo>+</mo><mn>13</mn><mo>+</mo><mn>16</mn><mo>+</mo><mn>15</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>5</mn></mfrac></math> = 15<br>So, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">k</mi><mo>+</mo><mn>19</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mo>(</mo><mn>15</mn><mo>)</mo><mo>+</mo><mn>19</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>30</mn><mo>+</mo><mn>19</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> = 7</p>",
                    solution_hi: "<p>15.(c) प्रश्न के अनुसार,<br>k = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mo>+</mo><mn>20</mn><mo>+</mo><mn>13</mn><mo>+</mo><mn>16</mn><mo>+</mo><mn>15</mn></mrow><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>75</mn><mn>5</mn></mfrac></math> = 15<br>इसलिए , <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">k</mi><mo>+</mo><mn>19</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mo>(</mo><mn>15</mn><mo>)</mo><mo>+</mo><mn>19</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mn>30</mn><mo>+</mo><mn>19</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> = 7</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "16",
                    section: "misc",
                    question_en: "<p>16. The value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mfrac><mn>6</mn><mn>7</mn></mfrac><mi>of</mi><mn>4</mn><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>&#247;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#215;</mo><mn>1</mn><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mn>2</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mi>of</mi><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>)</mo></math> is:</p>",
                    question_hi: "<p>16. (4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>6</mn><mn>7</mn></mfrac><mo>&#247;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math>) &times; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math> का 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math>) का मान है :</p>",
                    options_en: ["<p>5</p>", "<p>8</p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>5</p>", "<p>8</p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>16.(a) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>2</mn><mfrac><mn>6</mn><mn>7</mn></mfrac><mi>of</mi><mn>4</mn><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>&#247;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#215;</mo><mn>1</mn><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mn>2</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mi>of</mi><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>)</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>20</mn><mn>7</mn></mfrac><mi>of</mi><mfrac><mn>21</mn><mn>5</mn></mfrac><mo>&#247;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>10</mn><mn>9</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>)</mo></math><br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>12</mn><mo>&#247;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>10</mn><mn>9</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>)</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 18 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math> &divide; 4 = 5</p>",
                    solution_hi: "<p>16.(a) <br>(4<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> का <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>2</mn><mfrac><mn>6</mn><mn>7</mn></mfrac><mo>&#247;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac></math>) &times; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac></math> &divide; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math> का 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math>)<br><math display=\"inline\"><mo>&#8658;</mo></math> <math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mfrac><mn>21</mn><mn>5</mn></mfrac><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mfrac><mn>20</mn><mn>7</mn></mfrac><mo>&#247;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>10</mn><mn>9</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>4</mn><mn>3</mn></mfrac><mo>&#247;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac><mo>)</mo></math><br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>12</mn><mo>&#247;</mo><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>)</mo><mo>&#215;</mo><mfrac><mn>10</mn><mn>9</mn></mfrac><mo>&#247;</mo><mo>(</mo><mfrac><mn>3</mn><mn>4</mn></mfrac><mo>&#215;</mo><mfrac><mn>16</mn><mn>3</mn></mfrac><mo>)</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 18 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math> &divide; 4 = 5</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "17",
                    section: "misc",
                    question_en: "<p>17. If (a&nbsp;+ b) : (b + c) : (c + a) = 7 : 6 : 5 and a + b + c = 27, then what will be the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>:</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">b</mi></mfrac><mo>:</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">c</mi></mfrac></math> ?</p>",
                    question_hi: "<p>17. यदि (a + b) : (b + c) : (c + a) = 7 : 6 : 5 तथा a + b + c = 27 है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>:</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">b</mi></mfrac><mo>:</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">c</mi></mfrac></math>&nbsp;का मान क्या होगा ?</p>",
                    options_en: ["<p>3 : 6 : 4</p>", " 3 : 2 : 4     ", 
                                " 4 : 3 : 6  ", " 3 : 4 : 2"],
                    options_hi: [" 3 : 6 : 4 ", " 3 : 2 : 4",
                                " 4 : 3 : 6", " 3 : 4 : 2"],
                    solution_en: "<p>17.(c) Let (a + b) = 7 unit<br>(b + c) = 6 unit <br>(c + a) = 5 unit<br>Adding all three<br>(a + b) + (b + c) + (c + a) = (7 + 6 + 5) unit<br>(a + b + c) = 9 unit<br>According to the question<br>9 unit = 27 <math display=\"inline\"><mo>&#8658;</mo></math> 1 unit = 3<br><math display=\"inline\"><mo>&#8658;</mo></math> a = (a + b + c) - (b + c) = 9 - 6 = 3 unit <br><math display=\"inline\"><mo>&#8658;</mo></math> 3 (3) = 9<br>Similarly , b = 12 and c = 6 <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>:</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">b</mi></mfrac><mo>:</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">c</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>:</mo><mfrac><mn>1</mn><mn>12</mn></mfrac><mo>:</mo><mfrac><mn>1</mn><mn>6</mn></mfrac></math> = 4 : 3 : 6</p>",
                    solution_hi: "<p>17.(c) माना (a + b) = 7 unit<br>(b + c) = 6 unit <br>(c + a) = 5 unit<br>तीनों को जोड़ने पर,<br>(a + b) + (b + c) + (c + a) = (7 + 6 + 5) unit<br>(a + b + c) = 9 unit<br>प्रश्न के अनुसार<br><math display=\"inline\"><mo>&#8658;</mo></math> a = (a + b + c) - (b + c) = 9 - 6 = 3 unit <br><math display=\"inline\"><mo>&#8658;</mo></math> 3 (3) = 9<br>उसी प्रकार<br>b = 12 और c = 6 <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac><mo>:</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">b</mi></mfrac><mo>:</mo><mfrac><mn>1</mn><mi mathvariant=\"normal\">c</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>9</mn></mfrac><mo>:</mo><mfrac><mn>1</mn><mn>12</mn></mfrac><mo>:</mo><mfrac><mn>1</mn><mn>6</mn></mfrac></math> = 4 : 3 : 6</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "18",
                    section: "misc",
                    question_en: "<p>18. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#981;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin&#981;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math>, then secϕ is equal to :</p>",
                    question_hi: "<p>18. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#981;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin&#981;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math>, है, तो secϕ किसके बराबर है ?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mrow><mo>+</mo><mi mathvariant=\"normal\">q</mi></mrow><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">q</mi><mi mathvariant=\"normal\">p</mi></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac></math>)</p>", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup></mfrac><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mrow><mo>+</mo><mi mathvariant=\"normal\">q</mi></mrow><mn>2</mn></msup></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mrow><mo>+</mo><mi mathvariant=\"normal\">q</mi></mrow><mn>2</mn></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">q</mi><mi mathvariant=\"normal\">p</mi></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac></math>)</p>",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup></mfrac><mo>+</mo><mfrac><mn>1</mn><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mrow><mo>+</mo><mi mathvariant=\"normal\">q</mi></mrow><mn>2</mn></msup></mrow></mfrac></math></p>"],
                    solution_en: "<p>18.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#981;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin&#981;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#981;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin&#981;</mi></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#981;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>sin&#981;</mi></mrow></mfrac><mo>=</mo><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin&#981;</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#981;</mi></mrow></mfrac><mo>=</mo><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#981;</mi><mo>+</mo><mn>2</mn><mi>sin&#981;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#981;</mi></mrow></mfrac><mo>=</mo><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> sec&sup2;ϕ + tan&sup2;ϕ + 2secϕ.tanϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> (secϕ + tanϕ)&sup2; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> secϕ + tanϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac></math>&hellip;.(1)<br><math display=\"inline\"><mo>&#8658;</mo></math> secϕ - tanϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">q</mi><mi mathvariant=\"normal\">p</mi></mfrac></math>&hellip;(2) &hellip;..<br>{If<math display=\"inline\"><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#981;</mi></math> + tanϕ = k, then secϕ-tanϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac></math>}<br>Add (1) and (2)<br><math display=\"inline\"><mo>&#8658;</mo></math> 2secϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">q</mi><mi mathvariant=\"normal\">p</mi></mfrac></math>&rArr; secϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mi mathvariant=\"normal\">q</mi><mi mathvariant=\"normal\">p</mi></mfrac><mo>)</mo></math></p>",
                    solution_hi: "<p>18.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#981;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin&#981;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#981;</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>sin&#981;</mi></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>1</mn><mo>+</mo><mi>sin&#981;</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>sin&#981;</mi></mrow></mfrac><mo>=</mo><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mn>1</mn><mo>+</mo><mi>sin&#981;</mi><mo>)</mo></mrow><mn>2</mn></msup><mrow><mn>1</mn><mo>-</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#981;</mi></mrow></mfrac><mo>=</mo><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#981;</mi><mo>+</mo><mn>2</mn><mi>sin&#981;</mi></mrow><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#981;</mi></mrow></mfrac><mo>=</mo><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> sec&sup2;ϕ + tan&sup2;ϕ + 2secϕ.tanϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> (secϕ + tanϕ)&sup2; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">p</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">q</mi><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> secϕ + tanϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac></math>&hellip;.(1)<br><math display=\"inline\"><mo>&#8658;</mo></math> secϕ - tanϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">q</mi><mi mathvariant=\"normal\">p</mi></mfrac></math>&hellip;(2) &hellip;..<br>{यदि&nbsp;<math display=\"inline\"><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#981;</mi></math> + tanϕ = k, तो secϕ - tanϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">k</mi></mfrac></math>}<br>समीकरण (1) और (2) को जोड़ने पर,<br><math display=\"inline\"><mo>&#8658;</mo></math> 2secϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac><mo>+</mo><mfrac><mi mathvariant=\"normal\">q</mi><mi mathvariant=\"normal\">p</mi></mfrac></math>&rArr; secϕ = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac><mo>(</mo><mfrac><mi mathvariant=\"normal\">p</mi><mi mathvariant=\"normal\">q</mi></mfrac><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mi mathvariant=\"normal\">q</mi><mi mathvariant=\"normal\">p</mi></mfrac><mo>)</mo></math></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "19",
                    section: "misc",
                    question_en: "<p>19. A sum of Rs 10,500 amounts to Rs 13,825 in 3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> years at a certain rate per cent per annum simple interest. What will be the simple interest on the same sum for 5 years at double the earlier rate?</p>",
                    question_hi: "<p>19.10,500 रुपये की एक राशि प्रति वर्ष साधारण ब्याज के एक निश्चित दर प्रतिशत पर 3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> वर्षों में 13,825 रुपये हो जाती है । इसी राशि पर पहली दर से दोगुनी दर पर 5 वर्षों का साधारण ब्याज कितना होगा ?</p>",
                    options_en: ["<p>Rs 8,470</p>", "<p>Rs 8,750</p>", 
                                "<p>Rs 8,670</p>", "<p>Rs 8,560</p>"],
                    options_hi: ["<p>Rs 8,470</p>", "<p>Rs 8,750</p>",
                                "<p>Rs 8,670</p>", "<p>Rs 8,560</p>"],
                    solution_en: "<p>19.(b) Let r be the rate of interest&nbsp;<br>According to the question<br>3325 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10500</mn><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mfrac><mn>19</mn><mn>5</mn></mfrac></mrow><mn>100</mn></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> r = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%<br><math display=\"inline\"><mo>&#8658;</mo></math> New rate of interest = 16<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%<br>Now, 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math><br>Let amount = 6 unit and interest earned in one year = 1 unit<br>Interest earned in 5 years = 5 unit <br>According to the question<br>6 unit = 10500 <br>1 unit = 1750<br>5 unit = 8750<br><strong>Short-trick:</strong><br>Interest earned in 3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> years <br>= 13825 - 10500 = 3325<br>Interest earned in 1 year = 875<br><math display=\"inline\"><mo>&#8658;</mo></math> Interest earned in 5 year at double the rate of interest = 875 &times; 5 &times; 2 = 8750</p>",
                    solution_hi: "<p>19.(b)<br>मान लीजिए r ब्याज दर है। <br>प्रश्न के अनुसार,<br>3325 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10500</mn><mo>&#215;</mo><mi mathvariant=\"normal\">r</mi><mo>&#215;</mo><mfrac><mn>19</mn><mn>5</mn></mfrac></mrow><mn>100</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> r = 8<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%<br>ब्याज की नई दर = 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% <br>अब, = 16<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> <br>माना राशि = 6 इकाई और एक वर्ष में अर्जित ब्याज = 1 इकाई <br>5 वर्षों में अर्जित ब्याज = 5 इकाई<br>प्रश्न के अनुसार<br>6 इकाई = 10500<br>1 इकाई = 1750<br>5 इकाई = 8750<br><strong>वैकल्पिक विधि:</strong><br>3<math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>वर्षों में अर्जित ब्याज <br>= 13825 - 10500 = 3325<br>1 वर्ष में अर्जित ब्याज = 875<br>5 साल में दोगुना ब्याज दर पर अर्जित ब्याज = 875 &times; 5 &times; 2 = 8750</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20. A and B can do a piece of work in 6 days and 8 days, respectively. With help of C, they completed the work in 3 days and earned Rs. 1848. What was the share of C ?</p>",
                    question_hi: "<p>20. A और B किसी कार्य को क्रमशः 6 और 8 दिन में कर सकते हैं । C की सहायता से, उन्होंने इस कार्य को 3 दिनों में पूरा किया और 1848 रुपये कमाए । C का हिस्सा ज्ञात करें ।</p>",
                    options_en: ["<p>Rs. 231</p>", "<p>Rs. 924</p>", 
                                "<p>Rs. 462</p>", "<p>Rs. 693</p>"],
                    options_hi: ["<p>Rs.231</p>", "<p>Rs.924</p>",
                                "<p>Rs.462</p>", "<p>Rs.693</p>"],
                    solution_en: "<p>20.(a) Let the total work = 24 unit<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931000.png\" alt=\"rId5\" width=\"150\" height=\"110\"><br>Efficiency of C = 8 - 4 - 3 = 1 unit<br>Since, all worked for the same time period, Share will be distributed according to the efficiency of worker <br>According to the question <br>8 unit = Rs.1848<br>1 unit = Rs.231</p>",
                    solution_hi: "<p>20.(a)&nbsp;माना की कुल कार्य = 24 इकाई <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931000.png\" alt=\"rId5\" width=\"150\" height=\"110\"><br>C की क्षमता = 8 - 4 - 3 = 1 इकाई <br>चूंकि, सभी एक ही समय अवधि के लिए काम करते हैं, तो उनके दक्षता के अनुसार उनका हिस्सा वितरित किया जाएगा<br>प्रश्न के अनुसार<br>8 इकाई = Rs.1848, <br>1 इकाई = Rs.231</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "21",
                    section: "misc",
                    question_en: "<p>21. Find the sum of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>+</mo><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><msup><mn>3</mn><mn>3</mn></msup><mo>+</mo><mo>&#8230;</mo><mo>&#8230;</mo><mo>.</mo><mo>+</mo><msup><mn>3</mn><mn>8</mn></msup></math>.</p>",
                    question_hi: "<p>21. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>+</mo><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><msup><mn>3</mn><mn>3</mn></msup><mo>+</mo><mo>&#8230;</mo><mo>&#8230;</mo><mo>.</mo><mo>+</mo><msup><mn>3</mn><mn>8</mn></msup></math> का योग ज्ञात कीजिये?</p>",
                    options_en: ["<p>6561</p>", "<p>6560</p>", 
                                "<p>9840</p>", "<p>3280</p>"],
                    options_hi: ["<p>6561</p>", "<p>6560</p>",
                                "<p>9840</p>", "<p>3280</p>"],
                    solution_en: "<p>21.(c) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>+</mo><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><msup><mn>3</mn><mn>3</mn></msup><mo>+</mo><mo>&#8230;</mo><mo>&#8230;</mo><mo>.</mo><mo>+</mo><msup><mn>3</mn><mn>8</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 3 + 9 + 27 +....... + 6561<br>Common ratio (r) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Second</mi><mi mathvariant=\"normal\">&#160;</mi><mi>term</mi></mrow><mrow><mi>First</mi><mi mathvariant=\"normal\">&#160;</mi><mi>term</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>3</mn></mfrac></math> = 3<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">S</mi><mi mathvariant=\"normal\">n</mi></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>(</mo><msup><mi mathvariant=\"normal\">r</mi><mi mathvariant=\"normal\">n</mi></msup><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">r</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>&#8658;</mo><msub><mi mathvariant=\"normal\">S</mi><mi mathvariant=\"normal\">n</mi></msub><mo>=</mo><mfrac><mrow><mn>3</mn><mo>(</mo><msup><mn>3</mn><mn>8</mn></msup><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>3</mn><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math> = 9840</p>",
                    solution_hi: "<p>21.(c) <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mo>+</mo><msup><mn>3</mn><mn>2</mn></msup><mo>+</mo><msup><mn>3</mn><mn>3</mn></msup><mo>+</mo><mo>&#8230;</mo><mo>&#8230;</mo><mo>.</mo><mo>+</mo><msup><mn>3</mn><mn>8</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 3 + 9 + 27 +....... + 6561<br>सामान्य अनुपात (r) = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2360;&#2352;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2342;</mi></mrow><mrow><mi>&#2346;&#2361;&#2354;&#2366;</mi><mo>&#160;</mo><mi>&#2346;&#2342;</mi></mrow></mfrac></math> <strong id=\"docs-internal-guid-bc960805-7fff-1797-2b83-1ad57fca4f82\"></strong>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>3</mn></mfrac></math> = 3<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">S</mi><mi mathvariant=\"normal\">n</mi></msub></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">a</mi><mo>(</mo><msup><mi mathvariant=\"normal\">r</mi><mi mathvariant=\"normal\">n</mi></msup><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">r</mi><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac><mo>&#8658;</mo><msub><mi mathvariant=\"normal\">S</mi><mi mathvariant=\"normal\">n</mi></msub><mo>=</mo><mfrac><mrow><mn>3</mn><mo>(</mo><msup><mn>3</mn><mn>8</mn></msup><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><mn>3</mn><mo>-</mo><mn>1</mn><mo>)</mo></mrow></mfrac></math> = 9840</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "22",
                    section: "misc",
                    question_en: "<p>22. What approximate value should come in the place of the question mark in the following question ?<br>349.98 + (4.99<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - ? + 500.03 = 569.93</p>",
                    question_hi: "<p>22. निम्नलिखित प्रश्न में प्रश्न चिन्ह के स्थान पर कौन सा अनुमानित मान आना चाहिए ?<br>349.98 + (4.99<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - ? + 500.03 = 569.93</p>",
                    options_en: ["<p>305</p>", "<p>315</p>", 
                                "<p>320</p>", "<p>325</p>"],
                    options_hi: ["<p>305</p>", "<p>315</p>",
                                "<p>320</p>", "<p>325</p>"],
                    solution_en: "<p>22.(a)&nbsp;349.98 + (4.99<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - ? + 500.03 = 569.93<br>Using approximation, we have ;<br>350 + (<math display=\"inline\"><msup><mrow><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - ? + 500 = 570<br>375 - ? = 70 <math display=\"inline\"><mo>&#8658;</mo></math> ? = 305</p>",
                    solution_hi: "<p>22.(a)&nbsp;349.98 + (4.99<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - ? + 500.03 = 569.93<br>सन्निकटन का उपयोग करने पर, हमारे पास है;<br>350 + (<math display=\"inline\"><msup><mrow><mn>5</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - ? + 500 = 570<br>375 - ? = 70 <math display=\"inline\"><mo>&#8658;</mo></math> ? = 305</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "23",
                    section: "misc",
                    question_en: "<p>23. AB is the chord of a circle such that AB = 10 cm. If the diameter of the circle is 20 cm, then the angle subtended by the chord at the centre is______.</p>",
                    question_hi: "<p>23. AB एक वृत्त की जीवा इस प्रकार है कि AB = 10 सेमी. यदि वृत्त का व्यास 20 सेमी है, तो जीवा द्वारा केंद्र पर अंतरित कोण ______ है।</p>",
                    options_en: ["<p>45 degree</p>", "<p>60 degree</p>", 
                                "<p>30 degree</p>", "<p>90 degree</p>"],
                    options_hi: ["<p>45 डिग्री</p>", "<p>60 डिग्री</p>",
                                "<p>30 डिग्री</p>", "<p>90 डिग्री</p>"],
                    solution_en: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931135.png\" alt=\"rId6\" width=\"150\" height=\"130\"><br>Since, OA = OB = AB = 10 cm. Then, <math display=\"inline\"><mi>&#916;</mi></math>AOB is an equilateral triangle.<br>So, the angle(<math display=\"inline\"><mi>&#952;</mi></math>) subtended by circle at the centre = 60&deg;</p>",
                    solution_hi: "<p>23.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931135.png\" alt=\"rId6\" width=\"150\" height=\"130\"><br>चूँकि, OA = OB = AB = 10 cm तब, <math display=\"inline\"><mi>&#916;</mi></math>AOB एक समबाहु त्रिभुज है।<br>अत: वृत्त द्वारा केंद्र पर बनाया गया कोण (<math display=\"inline\"><mi>&#952;</mi></math>) = 60&deg;</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "24",
                    section: "misc",
                    question_en: "<p>24. Average age of 7 students of a class is 28 years. Average age of first three students is 30 years. Age of the fourth student is 4 years less than the age of fifth student. Ages of last two students is same and is 5 more than the average age of first three students. What is the average age of fourth and fifth student?</p>",
                    question_hi: "<p>24. एक कक्षा के 7 विद्यार्थियों की औसत आयु 28 वर्ष है। पहले तीन छात्रों की औसत आयु 30 वर्ष है। चौथे छात्र की आयु पांचवें छात्र की आयु से 4 वर्ष कम है। अंतिम दो छात्रों की आयु समान है और पहले तीन छात्रों की औसत आयु से 5 वर्ष अधिक है। चौथे और पांचवें छात्र की औसत आयु क्या है?</p>",
                    options_en: ["<p>20 years</p>", "<p>36 years</p>", 
                                "<p>16 years</p>", "<p>18 years</p>"],
                    options_hi: ["<p>20 वर्ष</p>", "<p>36 वर्ष</p>",
                                "<p>16 वर्ष</p>", "<p>18 वर्ष</p>"],
                    solution_en: "<p>24.(d) Let the age of fifth student be x<br>Then, the age of fourth student = x - 4<br>The age of last two students = 30 + 5 = 35 yrs <br>Average age of 7 students of the class = 28 yrs<br>Average of first three students = 30 yrs<br>Total deviation in the average = +2 &times; 3 = +6<br>As we know that net deviation should be zero then we have,<br>x + (x - 4) + 35 &times; 2 = 28 &times; 4 - 6<br>2x - 4 + 70 = 112 - 6 &rArr; 2x + 66 = 106<br>2x = 106 - 66 = 40 <math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math>= 20<br>Now, the average age of fourth and fifth student = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>4</mn></mrow><mn>2</mn></mfrac></math> = x - 2 = 20 - 2 = 18 yrs</p>",
                    solution_hi: "<p>24.(d) माना, पाँचवे विद्यार्थी की आयु x है।<br>तो, चौथे छात्र की आयु = x - 4<br>अंतिम दो विद्यार्थियों की आयु = 30 + 5 = 35 वर्ष<br>कक्षा के 7 छात्रों की औसत आयु = 28 वर्ष<br>पहले तीन छात्रों का औसत = 30 वर्ष<br>औसत में कुल विचलन = +2 &times; 3 = +6<br>जैसा कि हम जानते हैं कि शुद्ध विचलन शून्य होना चाहिए तो हमारे पास है: -<br>x + (x - 4) + 35 &times; 2 = 28 &times; 4 - 6<br>2x - 4 + 70 = 112 - 6 &rArr; 2x + 66 = 106<br>2x = 106 - 66 = 40 <math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math> = 20<br>अब, चौथे और पांचवें छात्र की औसत आयु = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = x - 2 = 20 - 2 = 18 वर्ष</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. In a village, 85% registered voters cast their votes in the election. Only two candidates (A and B) contested the election. A won the election by 510 votes. If A had received 15% less votes, the result would have been tie. How many registered voters are there in the village?</p>",
                    question_hi: "<p>25. एक गाँव में, 85% पंजीकृत मतदाताओं ने चुनाव में अपना वोट डाला। केवल दो उम्मीदवारों (A और B) ने चुनाव लड़ा। A ने 510 वोटों से चुनाव जीता। यदि A को 15% कम वोट मिले होते, तो परिणाम टाई होता। गांव में कितने पंजीकृत मतदाता हैं?</p>",
                    options_en: ["<p>4200</p>", "<p>3400</p>", 
                                "<p>4000</p>", "<p>4250</p>"],
                    options_hi: ["<p>4200</p>", "<p>3400</p>",
                                "<p>4000</p>", "<p>4250</p>"],
                    solution_en: "<p>25.(b) Let the total <math display=\"inline\"><mi>n</mi></math>umbers of registered votes be n. <br>the no. of votes received by A and B be <math display=\"inline\"><mi>x</mi></math> and (x - 510) respectively.<br>ATQ,<br>85% of <math display=\"inline\"><mi>x</mi></math> = (x - 510) + 15% of x<br>70% of <math display=\"inline\"><mi>x</mi></math> = x - 510 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math>x = (x - 510)<br>7<math display=\"inline\"><mi>x</mi></math> = 10x - 5100 &rArr; x = 1700<br>No. of votes received by A = 1700<br>No. of votes received by B <br>= 1700 <math display=\"inline\"><mo>-</mo></math> 510 = 1190<br>Then<br><math display=\"inline\"><mi>n</mi></math> &times; 85% = 1700 + 1190 = 2890<br><math display=\"inline\"><mi>n</mi></math> = 2890 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>5</mn></mfrac></math> = 3400</p>",
                    solution_hi: "<p>25.(b)&nbsp;माना कि पंजीकृत मतों की कुल संख्या n है । <br>A और B को प्राप्त वोटों की संख्या क्रमशः x और (x - 510) है।<br>प्रश्न के अनुसार,<br>x का 85% = (x - 510) + x का 15%<br><math display=\"inline\"><mi>x</mi></math> का 70% = x - 510 &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>10</mn></mfrac></math>x = (x - 510)<br>7<math display=\"inline\"><mi>x</mi></math> = 10x - 5100 &rArr; x = 1700<br>A को प्राप्त वोटों की संख्या = 1700<br>B को प्राप्त मतों की संख्या = 1700 - 510 = 1190<br>तब<br><math display=\"inline\"><mi>n</mi></math> &times; 85% = 1700 + 1190 = 2890<br><math display=\"inline\"><mi>n</mi></math> = 2890 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>5</mn></mfrac></math> = 3400</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "26",
                    section: "misc",
                    question_en: "<p>26. What is the simplified value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>(</mo><mi>xy</mi><mo>+</mo><mi>yz</mi><mo>+</mo><mi>zx</mi><mo>)</mo><mo>-</mo><mi>xyz</mi></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">z</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow></mfrac></math> ?</p>",
                    question_hi: "<p>26. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>(</mo><mi>xy</mi><mo>+</mo><mi>yz</mi><mo>+</mo><mi>zx</mi><mo>)</mo><mo>-</mo><mi>xyz</mi></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">z</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow></mfrac></math>&nbsp;का सरलीकृत मान क्या है?</p>",
                    options_en: ["<p>y</p>", "<p>z</p>", 
                                "<p>1</p>", "<p>x</p>"],
                    options_hi: ["<p>y</p>", "<p>z</p>",
                                "<p>1</p>", "<p>x</p>"],
                    solution_en: "<p>26.(c) Let <math display=\"inline\"><mi>x</mi></math> = y = z = 2<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>(</mo><mi>xy</mi><mo>+</mo><mi>yz</mi><mo>+</mo><mi>zx</mi><mo>)</mo><mo>-</mo><mi>xyz</mi></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">z</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>6</mn><mo>)</mo><mo>(</mo><mn>4</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>4</mn><mo>)</mo><mo>-</mo><mn>8</mn></mrow><mrow><mo>(</mo><mn>4</mn><mo>)</mo><mo>(</mo><mn>4</mn><mo>)</mo><mo>(</mo><mn>4</mn><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>6</mn><mo>)</mo><mo>(</mo><mn>12</mn><mo>)</mo><mo>-</mo><mn>8</mn></mrow><mn>64</mn></mfrac></math> &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>72</mn><mo>-</mo><mn>8</mn></mrow><mn>64</mn></mfrac><mo>&#8658;</mo><mfrac><mn>64</mn><mn>64</mn></mfrac></math> = 1</p>",
                    solution_hi: "<p>26.(c) माना , x = y = z = 2<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>(</mo><mi>xy</mi><mo>+</mo><mi>yz</mi><mo>+</mo><mi>zx</mi><mo>)</mo><mo>-</mo><mi>xyz</mi></mrow><mrow><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">z</mi><mo>)</mo><mo>(</mo><mi mathvariant=\"normal\">z</mi><mo>+</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>)</mo><mo>-</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mo>(</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>)</mo><mo>(</mo><mn>2</mn><mo>+</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>6</mn><mo>)</mo><mo>(</mo><mn>4</mn><mo>+</mo><mn>4</mn><mo>+</mo><mn>4</mn><mo>)</mo><mo>-</mo><mn>8</mn></mrow><mrow><mo>(</mo><mn>4</mn><mo>)</mo><mo>(</mo><mn>4</mn><mo>)</mo><mo>(</mo><mn>4</mn><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>6</mn><mo>)</mo><mo>(</mo><mn>12</mn><mo>)</mo><mo>-</mo><mn>8</mn></mrow><mn>64</mn></mfrac></math> &rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>72</mn><mo>-</mo><mn>8</mn></mrow><mn>64</mn></mfrac><mo>&#8658;</mo><mfrac><mn>64</mn><mn>64</mn></mfrac></math> = 1</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "27",
                    section: "misc",
                    question_en: "<p>27. A well of diameter 3m and depth 14m is dug. The earth, taken out of it, has been evenly spread all around it in the shape of a circular ring of width 4m to form an embankment. Find the height of embankment?</p>",
                    question_hi: "<p>27. 3 मी व्यास वाला तथा 14 मी गहरा एक कुआँ खोदा जाता है | इस दौरान निकली मिट्टी को इसके चारो ओर 4 मी चौड़े एक वृत्ताकार छल्ले के आकार में समान रूप से फैला दिया जाता है ताकि एक तटबंध का निर्माण किया जा सके | तटबंध की ऊंचाई ज्ञात करें |</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>m</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mi>m</mi></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>m</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>m</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>m</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mi>m</mi></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>m</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math>m</p>"],
                    solution_en: "<p>27.(b) Let the height of embankment be h.<br>According to the question,<br><math display=\"inline\"><mi>&#960;</mi></math> &times; (1.5)&sup2; &times; 14 = &pi;[(5.5)&sup2; - (1.5)&sup2;] &times; h<br><math display=\"inline\"><mo>&#8658;</mo><mi>h</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>14</mn></mrow><mrow><mn>7</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>8</mn></mfrac></math> m</p>",
                    solution_hi: "<p>27.(b) माना , तटबंध की ऊंचाई = h <br>प्रश्न के अनुसार,<br><math display=\"inline\"><mi>&#960;</mi></math> &times; (1.5)&sup2; &times; 14 = &pi;[(5.5)&sup2; - (1.5)&sup2;] &times; h<br><math display=\"inline\"><mo>&#8658;</mo><mi>h</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>1</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>14</mn></mrow><mrow><mn>7</mn><mo>&#215;</mo><mn>4</mn></mrow></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>8</mn></mfrac></math> m</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "28",
                    section: "misc",
                    question_en: "<p>28. A 9 - digit number 846523X7Y is divisible by 9, and Y - X = 6. Find the value of <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">X</mi><mo>+</mo><mn>4</mn><mi mathvariant=\"normal\">Y</mi></msqrt></math>.</p>",
                    question_hi: "<p>28. 846523X7Y एक ऐसी 9 अंकीय संख्या है जो 9 से विभाज्य है, और Y - X = 6 है। <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">X</mi><mo>+</mo><mn>4</mn><mi mathvariant=\"normal\">Y</mi></msqrt></math> का मान ज्ञात कीजिए ।</p>",
                    options_en: ["<p>4</p>", "<p>2</p>", 
                                "<p>6</p>", "<p>8</p>"],
                    options_hi: ["<p>4</p>", "<p>2</p>",
                                "<p>6</p>", "<p>8</p>"],
                    solution_en: "<p>28.(c)&nbsp;Given number = 846523X7Y <br>Divisibility of 9 : sum of all digits should be divisible by 9.<br>Hence, Y + X = 10 &hellip;&hellip;.(1) and <br>Y - X = 6 &hellip;&hellip;..(2)<br>By solving equation (1) and (2) we get, <br>X = 2 and Y = 8 <br>Now, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">X</mi><mo>+</mo><mn>4</mn><mi mathvariant=\"normal\">Y</mi></msqrt><mo>=</mo><msqrt><mn>4</mn><mo>+</mo><mn>32</mn></msqrt><mo>=</mo><msqrt><mn>36</mn></msqrt><mo>=</mo><mn>6</mn></math></p>",
                    solution_hi: "<p>28.(c) दी गई संख्या = 846523X7Y<br>9 की विभाज्यता : <br>सभी अंकों का योग 9 से विभाज्य होना चाहिए।<br>अत: Y + X = 10 &hellip;&hellip;.(1) और<br>Y - X = 6 &hellip;&hellip;..(2)<br>समीकरण (1) और (2) को हल करने पर हमें मिलता है , X = 2 और Y = 8<br>अब, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn><mi mathvariant=\"normal\">X</mi><mo>+</mo><mn>4</mn><mi mathvariant=\"normal\">Y</mi></msqrt><mo>=</mo><msqrt><mn>4</mn><mo>+</mo><mn>32</mn></msqrt><mo>=</mo><msqrt><mn>36</mn></msqrt><mo>=</mo><mn>6</mn></math></p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "29",
                    section: "misc",
                    question_en: "<p>29. When an article is sold for Rs 355, there is a loss of 29%. To gain 21%, it should be sold for Rs:</p>",
                    question_hi: "<p>29. जब कोई वस्तु 355 रुपये में बेची जाती है, तो 29% की हानि होती है । 21% का लाभ कमाने के लिए, इसे किस कीमत पर बेचा जाना चाहिए ?</p>",
                    options_en: ["<p>629.20</p>", "<p>580.80</p>", 
                                "<p>605</p>", "<p>635</p>"],
                    options_hi: ["<p>629.20</p>", "<p>580.80</p>",
                                "<p>605</p>", "<p>635</p>"],
                    solution_en: "<p>29.(c) Let the CP = 100 unit <br>SP of the article = 100 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>29</mn></mrow><mn>100</mn></mfrac></math> = 71 unit<br>According to the question <br>71 unit = 355 <math display=\"inline\"><mo>&#8658;</mo></math> 1 unit = 5<br>100 unit = 500<br>Desired Sale price = 500 &times; <math display=\"inline\"><mfrac><mrow><mn>121</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 605</p>",
                    solution_hi: "<p>29.(c)&nbsp;माना, क्रय मूल्य = 100 इकाई&nbsp;<br>वस्तु का विक्रय मूल्य = 100 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>29</mn></mrow><mn>100</mn></mfrac></math> = 71 इकाई <br>प्रश्न के अनुसार, <br>71 इकाई = 355 <math display=\"inline\"><mo>&#8658;</mo></math> 1 इकाई = 5<br>100 इकाई = 500<br>वांछित बिक्री मूल्य = 500 &times; <math display=\"inline\"><mfrac><mrow><mn>121</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 605</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "30",
                    section: "misc",
                    question_en: "<p>30. The sides of a triangle are 12 cm, 35 cm and 37 cm. What is the circumradius of the triangle ?</p>",
                    question_hi: "<p>30. एक त्रिभुज की भुजाएं 12 सेमी, 35 सेमी और 37 सेमी की हैं । इस त्रिभुज की परित्रिज्या ज्ञात करें ।</p>",
                    options_en: ["<p>19</p>", "<p>17.5</p>", 
                                "<p>17</p>", "<p>18.5</p>"],
                    options_hi: ["<p>19</p>", "<p>17.5</p>",
                                "<p>17</p>", "<p>18.5</p>"],
                    solution_en: "<p>30.(d) 12, 35 and 37 are triplet pairs, clearly a given triangle is a right angle triangle. <br>Circumradius of a right angle triangle = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>Hypotenuse</mi><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>2</mn></mfrac></math> = 18.5 cm</p>",
                    solution_hi: "<p>30.(d) 12, 35 और 37 त्रिक युग्म हैं, स्पष्ट रूप से दिया गया त्रिभुज एक समकोण त्रिभुज है। <br>समकोण त्रिभुज की परिधि त्रिज्या = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2325;&#2352;&#2381;&#2339;</mi><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>2</mn></mfrac></math> = 18.5 cm</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "31",
                    section: "misc",
                    question_en: "<p>31. Read the given statement and conclusions carefully. Decide which of the given conclusions is/are definitely true from the statement.<br><strong>Statement: </strong>Q &gt; S &lt; R &gt; T = U &le; V &lt; P<br><strong>Conclusions:</strong> <br>I. T = V <br>II. P &gt; U <br>III. V &gt; T</p>",
                    question_hi: "<p>31. दिए गए कथन और निष्कर्ष को ध्यानपूर्वक पढ़ें। निर्णय लें कि कथन से कौन सा/से निष्कर्ष निश्चित रूप से सत्य है/हैं।<br><strong>कथन: </strong>Q &gt; S &lt; R &gt; T = U &le; V &lt; P<br><strong>निष्कर्ष:</strong> <br>I. T = V <br>II. P &gt; U <br>III. V &gt; T</p>",
                    options_en: ["<p>All conclusions are true</p>", "<p>Only conclusion II and either I or III are true.</p>", 
                                "<p>Neither conclusion I nor II is true</p>", "<p>Only conclusion II is true</p>"],
                    options_hi: ["<p>सभी निष्कर्ष सत्य हैं</p>", "<p>केवल निष्कर्ष II और या तो I या III सत्य हैं।</p>",
                                "<p>न तो निष्कर्ष I और न ही II सत्य है</p>", "<p>केवल निष्कर्ष II सत्य है</p>"],
                    solution_en: "<p>31.(b) <strong>Given statement: </strong>Q &gt; S &lt; R &gt; T = U &le; V &lt; P<br>I. T = V&rarr; False (as T = U &le; V, thus clear relation between T and V cannot be determined)<br>II. P &gt; U True (as P &gt; V U)<br>III. V &gt; T False (as V T, thus clear relation between T and V cannot be determined)<br>Therefore, the conclusion I and III forms a complementary pair<br>Hence, Only conclusion II and either I or III are true.</p>",
                    solution_hi: "<p>31.(b) <strong>दिया गया कथन: </strong>Q &gt; S &lt; R &gt; T = U &le; V &lt; P<br>I. T = V&rarr; असत्य (चूंकि T = U &le; V, इस प्रकार T और V के बीच स्पष्ट संबंध स्पष्ट नहीं है)<br>II. P &gt; Uसत्य (चूंकि P &gt; V U)<br>III. V &gt; T असत्य (चूंकि V T, इस प्रकार T और V के बीच स्पष्ट संबंध स्पष्ट नहीं है)<br>इसलिए, निष्कर्ष I और III एक पूरक जोड़ी बनाते हैं<br>अतः, केवल निष्कर्ष II और या तो I या III सत्य हैं।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "32",
                    section: "misc",
                    question_en: "<p>32. A statement is given followed by two possible conclusions numbered I and II. You have to assume everything in the statement to be true and on the basis of it, decide which of the conclusions logically follow(s).<br><strong>Statement: </strong>Playing with mud and butterflies in a garden is more beneficial for small children than watching academic videos on computers or mobile phones.<br><strong>Conclusions:</strong><br>I. Mobile phones and computers are harmful for small children.<br>II. Real experiences are more beneficial than virtual experiences for children\'s development.</p>",
                    question_hi: "<p>32. एक कथन तथा उसके दो संभावित निष्कर्ष क्रमांक I और II दिए गए हैं। कथनों में दी गई जानकारी को सत्य मानते हुए विचार करें, और बताएं कि कौन से निष्कर्ष तार्किक रूप से कथन का पालन करते हैं?<br><strong>कथनः </strong>छोटे बच्चों के लिए, कंप्यूटर या मोबाइल फोन पर शिक्षाप्रद विडियो देखने के बजाए, बगीचे में मिट्टी और तितलियों से खेलना अधिक लाभदायक होता है।<br><strong>निष्कर्षः</strong><br>I. मोबाइल फोन और कंप्यूटर, छोटे बच्चों के लिए हानिकारक हैं।<br>II. बच्चों के विकास के लिए वास्तविक अनुभव, आभासी अनुभवों की अपेक्षा अधिक लाभकारी होते हैं।</p>",
                    options_en: ["<p>Only conclusion II follows</p>", "<p>Neither conclusion follows</p>", 
                                "<p>Both the conclusions follow</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष II अनुसरण करता है</p>", "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>",
                                "<p>दोनों निष्कर्ष अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष I अनुसरण करता है</p>"],
                    solution_en: "<p>32.(a) Conclusion (i) Mobile phones and computers are harmful for small children. This is not always true because this device is also useful to get some information so this conclusion is not strengthens.<br>Conclusion (ii) Real experiences are more beneficial than virtual experiences for children\'s development this conclusion strengthens the statement.</p>",
                    solution_hi: "<p>32.(a) निष्कर्षः(i) मोबाइल फोन और कंप्यूटर छोटे बच्चों के लिए हानिकारक हैं यह हमेशा सच नहीं होता है क्योंकि यह उपकरण कुछ जानकारी प्राप्त करने के लिए भी उपयोगी होता है इसलिए यह निष्कर्ष पुष्ट नहीं होता है।<br>निष्कर्षः(ii) आभासी अनुभवों की तुलना में वास्तविक अनुभव बच्चों के विकास के लिए अधिक लाभदायक होते हैं। यह निष्कर्ष कथन को पुष्ट करता है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "33",
                    section: "misc",
                    question_en: "<p>33. What number should come next?<br>3, 9, 45, 261, 1557, ?</p>",
                    question_hi: "<p>33. अगला कौन सा संख्या आना चाहिए?<br>3, 9, 45, 261, 1557, ?</p>",
                    options_en: ["<p>9555</p>", "<p>8572</p>", 
                                "<p>9433</p>", "<p>9333</p>"],
                    options_hi: ["<p>9555</p>", "<p>8572</p>",
                                "<p>9433</p>", "<p>9333</p>"],
                    solution_en: "<p>33.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931219.png\" alt=\"rId7\" width=\"280\" height=\"56\"></p>",
                    solution_hi: "<p>33.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931219.png\" alt=\"rId7\" width=\"280\" height=\"56\"></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "34",
                    section: "misc",
                    question_en: "<p>34. Identify the figure given in the options which when put in place of (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931351.png\" alt=\"rId8\" width=\"350\" height=\"76\"></p>",
                    question_hi: "<p>34. विकल्पों में दी गई उस आकृति की पहचान कीजिए, जिसे (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931351.png\" alt=\"rId8\" width=\"350\" height=\"76\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931452.png\" alt=\"rId9\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931569.png\" alt=\"rId10\" width=\"90\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931692.png\" alt=\"rId11\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931833.png\" alt=\"rId12\" width=\"90\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931452.png\" alt=\"rId9\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931569.png\" alt=\"rId10\" width=\"90\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931692.png\" alt=\"rId11\" width=\"90\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931833.png\" alt=\"rId12\" width=\"90\"></p>"],
                    solution_en: "<p>34.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931833.png\" alt=\"rId12\" width=\"90\"></p>",
                    solution_hi: "<p>34.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931833.png\" alt=\"rId12\" width=\"90\"></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "35",
                    section: "misc",
                    question_en: "<p>35. If &lsquo;+&rsquo; means &lsquo;&divide;&rsquo;, &lsquo; ̶ &rsquo; means &lsquo;+&rsquo;, &lsquo;&times;&rsquo; means &lsquo;&minus;&rsquo; and &lsquo;&divide;&rsquo; means &lsquo;&times;&rsquo;, what will be the value of the following expression?<br>[{(14 &times; 6) - (4 &divide; 3)} + (6 - 4)] &divide; 3</p>",
                    question_hi: "<p>35. यदि \'+\' का अर्थ &lsquo;&divide;&rsquo;, &lsquo; ̶ &rsquo; का अर्थ \'+\', \'&times;\' का अर्थ &lsquo;&minus;&rsquo; और \'&divide;\' का अर्थ \'&times;\' है, तो निम्नलिखित व्यंजक का मान क्या होगा?<br>[{(14 &times; 6) - (4 &divide; 3)} + (6 - 4)] &divide; 3</p>",
                    options_en: ["<p>1</p>", "<p>9</p>", 
                                "<p>6</p>", "<p>3</p>"],
                    options_hi: ["<p>1</p>", "<p>9</p>",
                                "<p>6</p>", "<p>3</p>"],
                    solution_en: "<p>35.(c)&nbsp;[{(14 &times; 6) - (4 &divide; 3)} + (6 - 4)] 3<br>As per given instructions after interchanging the sign we get<br>[{(14 - 6) + (4 &times; 3) &divide; (6 + 4)}] &times; 3&nbsp;<br>[(8 + 12) &divide; 10] &times; 3 = 2 &times; 3 = 6</p>",
                    solution_hi: "<p>35.(c) <strong>दिया गया :-</strong> <br>[{(14 &times; 6) - (4 &divide; 3)} + (6 - 4)] 3<br>दिए गए निर्देशों के अनुसार चिन्ह की अदला-बदली करने के बाद हमें प्राप्त होता है<br>[{(14 - 6) + ( 4 &times; 3) &divide; (6 + 4)}] &times; 3&nbsp;<br>[(8 + 12) &divide; 10] &times; 3 = 2 &times; 3 = 6</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "36",
                    section: "misc",
                    question_en: "<p>36. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits.)<br>(10, 49, 9) <br>(25, 484, 324)</p>",
                    question_hi: "<p>36. उस समुच्चय का चयन करें जिसमें संख्याएँ उसी प्रकार से संबंधित हैं जिस प्रकार दिए गए समुच्चय की संख्याएँ संबंधित हैं।<br>(<strong>नोट: </strong>संख्याओं को उसके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाओं को किया जाना चाहिए।)<br>(10, 49, 9) <br>(25, 484, 324)</p>",
                    options_en: ["<p>(26, 361, 529)</p>", "<p>(27, 400, 576)</p>", 
                                "<p>(28, 625, 441)</p>", "<p>(24, 289, 441)</p>"],
                    options_hi: ["<p>(26, 361, 529)</p>", "<p>(27, 400, 576)</p>",
                                "<p>(28, 625, 441)</p>", "<p>(24, 289, 441)</p>"],
                    solution_en: "<p>36.(c)<strong> Logic: </strong>n, (n - 3)&sup2;, (n - 7)&sup2;<br>(10, 49, 9) &rArr; 10, (10 - 3)&sup2;, (10 - 7)&sup2; &rArr; 10, (7)&sup2;, (3)&sup2;&nbsp;<br>(25, 484, 324) &rArr; 25, (25 - 3)&sup2;, (25 - 7)&sup2; &rArr; 25, (22)&sup2;, (18)&sup2;&nbsp;<br>Similarly,<br>(28, 625, 441) &rArr; 28, (28 - 3)&sup2;, (28 - 7)&sup2; &rArr; 28, (25)&sup2;, (21)&sup2;</p>",
                    solution_hi: "<p>36.(c) <strong>तर्क: </strong>n, (n - 3)&sup2;, (n - 7)&sup2;<br>(10, 49, 9) &rArr; 10, (10 - 3)&sup2;, (10 - 7)&sup2; &rArr; 10, (7)&sup2;, (3)&sup2;&nbsp;<br>(25, 484, 324) &rArr; 25, (25 - 3)&sup2;, (25 - 7)&sup2; &rArr; 25, (22)&sup2;, (18)&sup2;&nbsp;<br>इसी प्रकार,<br>(28, 625, 441) &rArr; 28, (28 - 3)&sup2;, (28 - 7)&sup2; &rArr; 28, (25)&sup2;, (21)&sup2;</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "37",
                    section: "misc",
                    question_en: "<p>37. Select the option that arranges the following words in a logical and meaningful order.<br>1. Open email account, <br>2. Compose email, <br>3. Start computer, <br>4. Enter email address, <br>5. Write the content, <br>6. Send the mail</p>",
                    question_hi: "<p>37. उस विकल्प का चयन करें जो निम्&zwj;नलिखित शब्दों की व्&zwj;यवस्&zwj;था को तार्किक और सार्थक क्रम में दर्शाता है।<br>1. ईमेल खाता खोलना, <br>2. ईमेल लिखना,<br>3. कंप्यूटर शुरू करना, <br>4. ईमेल पता दर्ज करना<br>5.सामग्री लिखना, <br>6. मेल भेजना</p>",
                    options_en: ["<p>3, 1, 2, 4, 5, 6</p>", "<p>2, 5, 1, 3, 4, 6</p>", 
                                "<p>3, 2, 6, 1, 5, 4</p>", "<p>3, 1, 2, 6, 5, 4</p>"],
                    options_hi: ["<p>3, 1, 2, 4, 5, 6</p>", "<p>2, 5, 1, 3, 4, 6</p>",
                                "<p>3, 2, 6, 1, 5, 4</p>", "<p>3, 1, 2, 6, 5, 4</p>"],
                    solution_en: "<p>37.(a) Correct order will be :<br>Start computer - Open email account - Compose email - Enter email address - Write the content - Send the mail</p>",
                    solution_hi: "<p>37.(a) सही क्रम होगा :- कंप्यूटर शुरू करना - ईमेल खाता खोलना - ईमेल लिखना - ईमेल पता दर्ज करना - सामग्री लिखना मेल भेजना</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "38",
                    section: "misc",
                    question_en: "<p>38. Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series. <br>Z _ XW _ ZYXXW _YYXW _</p>",
                    question_hi: "<p>38. अक्षरों के उस संयोजन का चयन कीजिए, जिसे दी गई शृंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर शृंखला पूरी हो जाएगी। <br>Z _ XW _ ZYXXW _YYXW _</p>",
                    options_en: ["<p>ZYWZ</p>", "<p>YWZZ</p>", 
                                "<p>ZZYY</p>", "<p>WYZY</p>"],
                    options_hi: ["<p>ZYWZ</p>", "<p>YWZZ</p>",
                                "<p>ZZYY</p>", "<p>WYZY</p>"],
                    solution_en: "<p>38.(b)&nbsp;Z<span style=\"text-decoration: underline;\"><strong>Y</strong></span>XW / <span style=\"text-decoration: underline;\"><strong>W</strong></span>ZYX / XW<span style=\"text-decoration: underline;\"><strong>Z</strong></span>Y / YXW<span style=\"text-decoration: underline;\"><strong>Z</strong></span><br>Explanation : the last letter of group shifted to the first position for their next group, and all rest letters get shifted to the right.</p>",
                    solution_hi: "<p>38.(b)&nbsp;Z<span style=\"text-decoration: underline;\"><strong>Y</strong></span>XW / <span style=\"text-decoration: underline;\"><strong>W</strong></span>ZYX / XW<span style=\"text-decoration: underline;\"><strong>Z</strong></span>Y / YXW<span style=\"text-decoration: underline;\"><strong>Z</strong></span><br>स्पष्टीकरण: समूह का अंतिम अक्षर उनके अगले समूह के लिए पहले स्थान पर आ जाता है, और बाकी सभी अक्षर दाईं ओर स्थानांतरित हो जाते हैं।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "39",
                    section: "misc",
                    question_en: "39. After arranging the given words according to dictionary order, which word will come at \'Third\' position?<br />1. Overwork   <br />2. Overwrought<br />3. Overwhelming   <br />4. Overweight<br />5. Overweening",
                    question_hi: "39. दिए गए शब्दों को शब्दकोश क्रम के अनुसार व्यवस्थित करने के बाद, कौन सा शब्द \'तीसरे\' स्थान पर आएगा?<br />1. Overwork   <br />2. Overwrought<br />3. Overwhelming   <br />4. Overweight<br />5. Overweening",
                    options_en: [" Overwhelming  ", " Overwrought ", 
                                " Overweight        ", " Overweening"],
                    options_hi: [" Overwhelming  ", " Overwrought ",
                                " Overweight        ", " Overweening"],
                    solution_en: "39.(a) The correct order will be<br />Overweening <math display=\"inline\"><mo>→</mo></math> Overweight → Overwhelming → Overwork → Overwrought",
                    solution_hi: "39.(a) शब्दों को अंग्रेजी शब्दकोश के अनुसार व्यवस्थित करने के बाद,हमें प्राप्त होता है <br />Overweening <math display=\"inline\"><mo>→</mo></math> Overweight → Overwhelming → Overwork → Overwrought",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "40",
                    section: "misc",
                    question_en: "<p>40. After interchanging the given two numbers and two signs what will be the values of equation (I) and (II) respectively?<br>&ndash; and &times;, 6 and 4<br>I. 7 &times; 6 + 8 &divide; 2 &ndash; 4<br>II. 4 &ndash; 7 &times; 6 + 8 &divide; 2</p>",
                    question_hi: "<p>40. दी गई दो संख्याओं तथा दो चिह्नों को आपस में परस्पर बदलने के पश्चात् क्रमशः समीकरण (I) तथा (II) के मान क्या होंगे?<br>&ndash; तथा &times; , 6 तथा 4<br>I. 7 &times; 6 + 8 &divide; 2 &ndash; 4<br>II. 4 &ndash; 7 &times; 6 + 8 &divide; 2</p>",
                    options_en: ["<p>27, 42</p>", "<p>27, 47</p>", 
                                "<p>27, 49</p>", "<p>25, 40</p>"],
                    options_hi: ["<p>27, 42</p>", "<p>27, 47</p>",
                                "<p>27, 49</p>", "<p>25, 40</p>"],
                    solution_en: "<p>40.(a) on Interchanging &times; and - , 4 and 6 and putting them in both the equations, we get,<br>I. 7 - 4 + 8 &divide; 2 &times; 6 = 27<br>II. 6 &times; 7 - 4 + 8 &divide; 2 = 42</p>",
                    solution_hi: "<p>40.(a) &times; और - तथा 4 और 6 को आपस में बदलकर और उन्हें दोनों समीकरणों में रखने पर, हम पाते हैं,<br>I. 7 - 4 + 8 &divide; 2 &times; 6 = 27<br>II. 6 &times; 7 - 4 + 8 &divide; 2 = 42</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "41",
                    section: "misc",
                    question_en: "<p>41. J, K, L, M and N are sitting on a bench. J is the immediate neighbour of K, M is the immediate neighbour of N, and J is not sitting with L who sits on the right end of the bench. J sits to the right of M and N. M sits to the right of N. In which position is J sitting?</p>",
                    question_hi: "<p>41. J, K, L, M और N एक बेंच पर बैठे हैं। J, K का निकटतम पडोसी है; M, N का निकटतम पडोसी है, और J, L के साथ नहीं बैठा है जो बेंच के दाएँ छोर पर बैठा है। J, M और N के दाएँ बैठा है। M, N के दाएँ बैठा है। J किस स्थिति में बैठा है?</p>",
                    options_en: ["<p>Third from the left</p>", "<p>Fourth from the left</p>", 
                                "<p>Second from the left</p>", "<p>Second from the right</p>"],
                    options_hi: ["<p>बाएं से तीसरा</p>", "<p>बाएं से चौथा</p>",
                                "<p>बाएं से दूसरा</p>", "<p>दाएं से दूसरा</p>"],
                    solution_en: "<p>41.(a) Applying directions given in the question, we get following arrangement :<br>Clearly, we can see that J is sitting at third from the left.</p>",
                    solution_hi: "<p>41.(a) प्रश्न में दिए गए निर्देशों को लागू करने पर हमें निम्नलिखित व्यवस्था प्राप्त होती है:-<br>स्पष्ट रूप से, हम देख सकते हैं कि J बायें से तीसरे स्थान पर बैठा है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "42",
                    section: "misc",
                    question_en: "42. Pick the odd pair of words from the below options",
                    question_hi: "42. नीचे दिए गए विकल्पों में से विषम शब्द युग्म चुनिए?",
                    options_en: [" Steadfast : irresolute ", " Monotonous  : exciting ", 
                                " Stringent  : ﬂexible ", " Docile : submissive "],
                    options_hi: [" स्थिर : अनिश्चित   ", " नीरस : उत्तेजक",
                                " कठोर : लचीला", " विनम्र : आज्ञाकारी"],
                    solution_en: "42.(d) In every pair of words the 2nd word is opposite of the first except option (d).",
                    solution_hi: "42.(d) विकल्प (d) को छोड़कर प्रत्येक जोड़े में दूसरा शब्द पहले के विपरीत है।",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "43",
                    section: "misc",
                    question_en: "<p>43. Three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statement :</strong> <br>All uncles are relatives. <br>Some relatives are neighbours. <br>All neighbours are employed. <br><strong>Conclusions :</strong> <br>I. Some neighbours are uncles. <br>II. Some uncles are employed. <br>III. Some relatives are employed.</p>",
                    question_hi: "<p>43. तीन कथन दिए गए हैं, जिसके बाद तीन निष्कर्ष I और II दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय करें कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/ करते हैं।<br><strong>कथन :</strong><br>1. सभी अंकल, रिलेटिव हैं।<br>2. कुछ रिलेटिव, नेबर हैं।<br>3. सभी नेबर, एम्प्लॉयड हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ नेबर, अंकल हैं।<br>II. कुछ अंकल, एम्प्लॉयड हैं।<br>III. कुछ रिलेटिव, एम्प्लॉयड हैं।</p>",
                    options_en: ["<p>Only conclusions I and II follow</p>", "<p>Only conclusions II and III follow</p>", 
                                "<p>Only conclusion III follows</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I और II अनुसरण करते हैं</p>", "<p>केवल निष्कर्ष II और III अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष III अनुसरण करता है</p>", "<p>केवल निष्कर्ष I अनुसरण करता है</p>"],
                    solution_en: "<p>43.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642931952.png\" alt=\"rId13\" width=\"300\" height=\"101\"><br>Clearly, only conclusion III follows.</p>",
                    solution_hi: "<p>43.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932091.png\" alt=\"rId14\" width=\"300\"><br>स्पष्ट रूप से, केवल निष्कर्ष III अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "44",
                    section: "misc",
                    question_en: "44. If A denotes ‘addition’, B denotes ‘multiplication’, C denotes \'subtraction\', and D denotes division\', then what will be the value of the following equation?<br />7 B 5 A 31 C (48 D 6)= ?",
                    question_hi: "44. यदि A का अर्थ \'जोड़\', B का अर्थ \'गुणा\', C का अर्थ \'घटाव\' और D का अर्थ \'भाग\' हो, तो निम्नलिखित समीकरण का मान क्या होगा?<br />7 B 5 A 31 C (48 D 6) = ?",
                    options_en: [" 62", " 38", 
                                " 58", " 46"],
                    options_hi: [" 62", " 38",
                                " 58", " 46"],
                    solution_en: "44.(c) Given that :- 7 B 5 A 31 C (48 D 6) = ? <br />As per given instruction after interchanging the letters with symbol we get<br />7 <math display=\"inline\"><mo>×</mo></math> 5 + 31 - (48 ÷ 6) ⇒  35 + 31 - 8 = 58",
                    solution_hi: "44.(c) दिया गया है:-  7 B 5 A 31 C (48 D 6) = ? <br />दिए गए निर्देश के अनुसार अक्षरों को प्रतीक से बदलने पर हमें प्राप्त होता है<br />7 <math display=\"inline\"><mo>×</mo></math> 5 + 31 - (48 ÷ 6) ⇒  35 + 31 - 8 = 58",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "45",
                    section: "misc",
                    question_en: "45. Select the option that does NOT belong in the following figure series.<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932204.png\" alt=\"rId15\" /> ",
                    question_hi: "45. उस विकल्प का चयन करें जो निम्न समूह से संबंधित नहीं है।<br /> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932204.png\" alt=\"rId15\" /> ",
                    options_en: [" D", " B", 
                                " C", " A"],
                    options_hi: [" D", " B",
                                " C", " A"],
                    solution_en: "45.(a) Logic: Each figure has 1 symbol or letter repeated once but in figure ‘D’ two symbols (star and circle) repeated. <br />The correct answer is ",
                    solution_hi: "45.(a) तर्क: प्रत्येक आकृति में 1 प्रतीक या अक्षर एक बार दोहराया गया है लेकिन आकृति ‘D’ में दो प्रतीक (तारा और वृत्त) दोहराए गए हैं।<br />सही उत्तर है",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "46",
                    section: "misc",
                    question_en: "<p>46. Read the given statement and conclusions carefully and decide which of the conclusions logically follow(s) from the statement.<br><strong>Statement : </strong>The GDP of India has increased in the modern era.<br><strong>Conclusions :</strong><br>1. There is increase in economic activity in the country.<br>2. The government is not vital for the economic activity in the country.</p>",
                    question_hi: "<p>46. दिए गए कथन और निष्कर्षों को ध्यान से पढ़ें और तय करें कि कौन सा निष्कर्ष कथन का तार्किक रूप से अनुसरण करता है।<br><strong>कथन : </strong>आधुनिक युग में भारत की GDP बढ़ी है।<br><strong>निष्कर्ष :</strong><br>1. देश में आर्थिक गतिविधियों में वृद्धि हो रही है<br>2. सरकार देश में आर्थिक गतिविधियों के लिए महत्वपूर्ण नहीं है।</p>",
                    options_en: ["<p>Neither conclusion 1 nor 2 follows.</p>", "<p>Both conclusions 1 and 2 follow.</p>", 
                                "<p>Conclusion 1 follows but 2 does not follow.</p>", "<p>Conclusion 1 does not follow but 2 follows.</p>"],
                    options_hi: ["<p>न तो निष्कर्ष 1 और न ही 2 अनुसरण करता है।</p>", "<p>निष्कर्ष 1 और 2 दोनों अनुसरण करते हैं।</p>",
                                "<p>निष्कर्ष 1 अनुसरण करता है लेकिन 2 अनुसरण नहीं करता है।</p>", "<p>निष्कर्ष 1 अनुसरण नहीं करता है लेकिन 2 अनुसरण करता है।</p>"],
                    solution_en: "<p>46.(c) From the given statement we can observe There is an increase in economic activity in the country. But the government also has a part in GDP. hence Conclusion 1 follows but 2 does not follow.</p>",
                    solution_hi: "<p>46.(c) दिए गए समीकरण से हम देख सकते हैं कि देश में आर्थिक गतिविधियों में वृद्धि हुई है। लेकिन जीडीपी में सरकार का भी एक हिस्सा है। इसलिए निष्कर्ष 1 अनुसरण करता है लेकिन 2 अनुसरण नहीं करता है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "47",
                    section: "misc",
                    question_en: "<p>47. F is the brother of J. E is the sister of D. K is the sister of L. D is the son of L. J is the father of E. How is D related to F ?</p>",
                    question_hi: "<p>47. F, J का भाई है I E, D की बहन है। K, L की बहन है I D, L का पुत्र है। J, E का पिता हैI D का F से क्या संबंध है ?</p>",
                    options_en: ["<p>Wife</p>", "<p>Brother\'s daughter</p>", 
                                "<p>Brother\'s son</p>", "<p>Sister\'s husband</p>"],
                    options_hi: ["<p>पत्नी</p>", "<p>भतीजी</p>",
                                "<p>भतीजा</p>", "<p>बहनोई</p>"],
                    solution_en: "<p>47.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932325.png\" alt=\"rId16\" width=\"200\" height=\"87\"><br>D is the Brother&rsquo;s son of F.</p>",
                    solution_hi: "<p>47.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932325.png\" alt=\"rId16\" width=\"200\" height=\"87\"><br>D, F का भतीजा है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "48",
                    section: "misc",
                    question_en: "<p>48. In a certain code language, DIABETES is coded as SETEBAID, HOSPITAL coded as LATIPSOH. How will INSULIN be written in that code language ?</p>",
                    question_hi: "<p>48. एक निश्चित कोड भाषा में, DIABETES को SETEBAID के रूप में कोडित किया जाता है, HOSPITAL को LATIPSOH के रूप में कोडित किया जाता है। उस कोड भाषा में INSULIN को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>NILUSIN</p>", "<p>NLIUSNI</p>", 
                                "<p>NILUSNI</p>", "<p>NIULSNI</p>"],
                    options_hi: ["<p>NILUSIN</p>", "<p>NLIUSNI</p>",
                                "<p>NILUSNI</p>", "<p>NIULSNI</p>"],
                    solution_en: "<p>48.(c) <strong>Logic :</strong> letters are written in their reverse order.<br>therefore<br>DIABETES will be SETEBAID<br>HOSPITAL will be LATIPSOH<br>INSULIN will be NILUSNI.</p>",
                    solution_hi: "<p>48.(c) <strong>तर्क:</strong>अक्षरों को उलटे क्रम में लिखा गया है <br>तब <br>DIABETES होगा SETEBAID<br>HOSPITAL होगा LATIPSOH<br>INSULIN होगा NILUSNI.</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "49",
                    section: "misc",
                    question_en: "<p>49. Select the option figure that will replace the question mark (?) in the figure given below to complete the pattern.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932424.png\" alt=\"rId17\" width=\"120\" height=\"119\"></p>",
                    question_hi: "<p>49. उस विकल्प आकृति का चयन कीजिए, जो नीचे दिए गए चित्र में प्रश्न-चिह्न (?) के स्थान पर आकर पैटर्न को पूर्ण करेगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932424.png\" alt=\"rId17\" width=\"120\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932535.png\" alt=\"rId18\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932636.png\" alt=\"rId19\" width=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932745.png\" alt=\"rId20\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932852.png\" alt=\"rId21\" width=\"80\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932535.png\" alt=\"rId18\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932636.png\" alt=\"rId19\" width=\"80\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932745.png\" alt=\"rId20\" width=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932852.png\" alt=\"rId21\" width=\"80\"></p>"],
                    solution_en: "<p>49.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932636.png\" alt=\"rId19\" width=\"80\"></p>",
                    solution_hi: "<p>49.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932636.png\" alt=\"rId19\" width=\"80\"></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "50",
                    section: "misc",
                    question_en: "<p>50. In a certain code language RACE is coded as 152, LACE is coded as 104. How will FACE be written in that code language ?</p>",
                    question_hi: "<p>50. एक निश्चित कोड भाषा में RACE को 152 के रूप में कोडित किया जाता है, LACE को 104 के रूप में कोडित किया जाता है। उस कोड भाषा में FACE को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>56</p>", "<p>52</p>", 
                                "<p>60</p>", "<p>46</p>"],
                    options_hi: ["<p>56</p>", "<p>52</p>",
                                "<p>60</p>", "<p>46</p>"],
                    solution_en: "<p>50.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932962.png\" alt=\"rId22\" width=\"100\" height=\"97\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933081.png\" alt=\"rId23\" width=\"100\"><br>similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933235.png\" alt=\"rId24\" width=\"100\"></p>",
                    solution_hi: "<p>50.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642932962.png\" alt=\"rId22\" width=\"100\" height=\"97\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933081.png\" alt=\"rId23\" width=\"100\"><br>उसी प्रकार&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933235.png\" alt=\"rId24\" width=\"100\"></p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "51",
                    section: "misc",
                    question_en: "<p>51. If the given sheet is folded to form a cube, which among the given figures is NOT possible?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933348.png\" alt=\"rId25\" width=\"100\" height=\"99\"></p>",
                    question_hi: "<p>51. यदि दी गई शीट को मोड़कर एक घन बनाया जाता है, तो दी गई आकृतियों में से कौन-सी आकृति बनना संभव नहीं है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933348.png\" alt=\"rId25\" width=\"100\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933464.png\" alt=\"rId26\" width=\"50\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933548.png\" alt=\"rId27\" width=\"50\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933664.png\" alt=\"rId28\" width=\"50\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933762.png\" alt=\"rId29\" width=\"50\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933464.png\" alt=\"rId26\" width=\"50\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933548.png\" alt=\"rId27\" width=\"50\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933664.png\" alt=\"rId28\" width=\"50\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933762.png\" alt=\"rId29\" width=\"50\"></p>"],
                    solution_en: "<p>51.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933855.png\" alt=\"rId30\" width=\"100\"><br>Opposite faces:- # &harr; ! , $ &harr; @ , &amp; &harr; % <br>We know that opposite faces can not be adjacent</p>",
                    solution_hi: "<p>51.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933855.png\" alt=\"rId30\" width=\"100\"><br>विपरीत फलक :- # &harr; ! , $ &harr; @ , &amp; &harr; % <br>हम जानते हैं कि विपरीत फलक आसन्न नहीं हो सकते</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "52",
                    section: "misc",
                    question_en: "<p>52. Three of the following four number-pairs are alike in a certain way and one is different. Find the odd one out.</p>",
                    question_hi: "<p>52. निम्नलिखित चार में से तीन संख्या-युग्म किसी निश्चित प्रकार से एक जैसे हैं तथा एक युग्म अलग है | उस अलग युग्म की पहचान करें |</p>",
                    options_en: ["<p>14 : 16</p>", "<p>18 : 9</p>", 
                                "<p>11 : 4</p>", "<p>17 : 18</p>"],
                    options_hi: ["<p>14 : 16</p>", "<p>18 : 9</p>",
                                "<p>11 : 4</p>", "<p>17 : 18</p>"],
                    solution_en: "<p>52.(d) <strong>Logic :- </strong>(Digit sum of the square of first number) = Second number<br>14 : 16 :- (14)&sup2; = 196 &rArr; 1 + 9 + 6 = 16<br>18 : 9 :- (18)&sup2; = 324 &rArr; 3 + 2 + 4 = 9<br>11 : 4 :- (11)&sup2; = 121 &rArr; 1 + 2 + 1 = 4<br>But,<br>17 : 18 : - (17)&sup2; = 289 &rArr; 2 + 8 + 9 = 19 &ne; 18</p>",
                    solution_hi: "<p>52.(d)<br>तर्क :- (पहली संख्या के वर्ग के अंको का योग) = दूसरी संख्या<br>14 : 16 :- (14)&sup2; = 196 &rArr; 1 + 9 + 6 = 16<br>18 : 9 :- (18)&sup2; = 324 &rArr; 3 + 2 + 4 = 9<br>11 : 4 :- (11)&sup2; = 121 &rArr; 1 + 2 + 1 = 4<br>लेकिन,<br>17 : 18 : - (17)&sup2; = 289 &rArr; 2 + 8 + 9 = 19 &ne; 18</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "53",
                    section: "misc",
                    question_en: "<p>53. Read the given statements and conclusions carefully. You have to take the given statements to be true even if they seem to be at variance from commonly known facts. You have to decide which conclusions logically follow(s) from the given statements. <br><strong>Statements :</strong><br>I. All Z are Y<br>II. Some Y are B<br><strong>Conclusion :</strong><br>I. Some B are not Z<br>II. Some Y are not Z <br>III. Some Z are Y</p>",
                    question_hi: "<p>53. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। आपको दिए गए कथनों को सत्य मानना है, भले ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों। आपको यह तय करना है कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/ करते हैं। <br><strong>कथन :</strong><br>I. सभी Z, Y हैं।<br>II. कुछ Y, B हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ B, Z नहीं हैं।<br>II. कुछ Y, Z नहीं हैं।<br>III. कुछ Z, Y हैं।</p>",
                    options_en: ["<p>Both conclusions II and III follows</p>", "<p>Both conclusions I and III follows</p>", 
                                "<p>Only conclusion III follows</p>", "<p>All conclusion follows</p>"],
                    options_hi: ["<p>दोनों निष्कर्ष II और III कथनों के अनुसार हैं</p>", "<p>दोनों निष्कर्ष I और III कथनों के अनुसार हैं</p>",
                                "<p>केवल निष्कर्ष III कथनों के अनुसार है</p>", "<p>सभी निष्कर्ष कथनों के अनुसार हैं</p>"],
                    solution_en: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933970.png\" alt=\"rId31\" width=\"180\" height=\"95\"><br>Only conclusion III follows.</p>",
                    solution_hi: "<p>53.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642933970.png\" alt=\"rId31\" width=\"180\" height=\"95\"><br>केवल निष्कर्ष III अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "54",
                    section: "misc",
                    question_en: "<p>54. What should come in place of the question mark (?) in the given series?<br>YIQ, AKR, CMS, EOT, ?</p>",
                    question_hi: "<p>54. दी गई शृंखला में प्रश्न-चिह्न (?) के स्थान पर क्या आना चाहिए?<br>YIQ, AKR, CMS, EOT, ?</p>",
                    options_en: ["<p>HPV</p>", "<p>GQU</p>", 
                                "<p>FPU</p>", "<p>FRV</p>"],
                    options_hi: ["<p>HPV</p>", "<p>GQU</p>",
                                "<p>FPU</p>", "<p>FRV</p>"],
                    solution_en: "<p>54.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934084.png\" alt=\"rId32\" width=\"250\" height=\"81\"></p>",
                    solution_hi: "<p>54.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934084.png\" alt=\"rId32\" width=\"250\" height=\"81\"></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "55",
                    section: "misc",
                    question_en: "<p>55. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants /vowels in the word.)<br>Horse : Mare</p>",
                    question_hi: "<p>55. उस शब्द-युग्म का चयन कीजिए जिसके शब्दों के मध्य वही संबंध है जो नीचे दिए गए शब्द-युग्म के शब्दों के मध्य है।<br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या / व्यंजनों की संख्या / स्वरों की संख्या के आधार पर एक-दूसरे से संबद्ध नहीं किया जाना चाहिए।)<br>घोड़ा : घोड़ी</p>",
                    options_en: ["<p>Bull : Cow</p>", "<p>Duck : Chick</p>", 
                                "<p>Dog : Calf</p>", "<p>Butterfly : Larva</p>"],
                    options_hi: ["<p>सांड : गाय</p>", "<p>बतख : चूजा</p>",
                                "<p>कुत्ता : बछड़ा</p>", "<p>तितली : लार्वा</p>"],
                    solution_en: "<p>55.(a) As Horse is a adult male and Mare is a adult female , similarly Bull is a adult male and Cow is a adult female</p>",
                    solution_hi: "<p>55.(a) जैसे घोड़ा एक वयस्क नर है और घोड़ी एक वयस्क मादा है, उसी प्रकार सांड एक वयस्क नर है और गाय एक वयस्क मादा है</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "56",
                    section: "misc",
                    question_en: "<p>56. Six friends are sitting in a circle facing the center. Sumit sits second to the right of Amit. Karan is an immediate neighbor of Param. Tony sits third to the right of Amit. Dharam is an immediate neighbor of Amit and Sumit. Karan sits second to the right of Sumit. Who sits fourth to the right of Param ?</p>",
                    question_hi: "<p>56. छह मित्र एक वृत्ताकार स्थिति में केंद्र की ओर मुख करके बैठे हुए हैं। सुमित, अमित के दाई ओर दूसरे स्थान पर बैठा है। करण, परम के ठीक बगल में है। टोनी, अमित के दाई ओर तीसरे स्थान पर बैठा है। धरम, अमित और सुमित के ठीक बगल में बैठा है। करण, सुमित के दाईं ओर दूसरे स्थान पर बैठा है।&nbsp;परम के दाईं ओर चौथे स्थान पर कौन बैठा है ?</p>",
                    options_en: ["<p>Karan</p>", "<p>Tony</p>", 
                                "<p>Dharam</p>", "<p>Sumit</p>"],
                    options_hi: ["<p>करण</p>", "<p>टोनी</p>",
                                "<p>धरम</p>", "<p>सुमित</p>"],
                    solution_en: "<p>56.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934193.png\" alt=\"rId33\" width=\"200\" height=\"143\"></p>",
                    solution_hi: "<p>56.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934305.png\" alt=\"rId34\" width=\"200\"></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "57",
                    section: "misc",
                    question_en: "<p>57. A @ B means &lsquo;A is the husband of B&rsquo;<br>A &amp; B means &lsquo;A is the mother of B&rsquo;<br>A # B means &lsquo;A is the daughter of B&rsquo;<br>If Q @ M &amp; Y @ J # R &amp; Z, then how is Y related to Z?</p>",
                    question_hi: "<p>57. A @ B का अर्थ है \"A, B का पति है\"<br>A &amp; B का अर्थ है \"A, B की माता है\"<br>A # B का अर्थ है \"A, B की पुत्री है\"<br>यदि Q @ M &amp; Y @ J # R &amp; Z है, तो Y का Z से क्या संबंध है?</p>",
                    options_en: ["<p>Father</p>", "<p>Father&rsquo;s sister</p>", 
                                "<p>Brother&rsquo;s wife</p>", "<p>Sister&rsquo;s husband</p>"],
                    options_hi: ["<p>पिता</p>", "<p>बुआ</p>",
                                "<p>भाभी</p>", "<p>बहनोई</p>"],
                    solution_en: "<p>57.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934422.png\" alt=\"rId35\" width=\"250\" height=\"108\"><br>Y is sister&rsquo;s husband of Z.</p>",
                    solution_hi: "<p>57.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934422.png\" alt=\"rId35\" width=\"250\"><br>Y, Z का बहनोई है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "58",
                    section: "misc",
                    question_en: "<p>58. Sona goes to a park every day for walking. One morning, she started walking from her home and walked 40 km towards the north. Then she turned to the right and walked 60 km. Again, she turned right and walked 40 km. Lastly, she took a left turn and walked 60 km. In which direction and how far is she from her home?</p>",
                    question_hi: "<p>58. सोना प्रतिदिन पार्क में टहलने जाती है। एक सुबह, वह अपने घर से टहलना आरंभ करती है, और उत्तर की ओर 40 km चलती है। फिर वह दाईं ओर मुड़ती है, और 60 km चलती है। वह फिर से दाईं ओर मुड़ती है, और 40 km चलती है। अंत में, वह बाईं ओर मुड़ती है, और 60 km चलती है। वह अपने घर से किस दिशा में और कितनी दूरी पर है?</p>",
                    options_en: ["<p>East, 90 km</p>", "<p>West, 60 km</p>", 
                                "<p>East, 120 km</p>", "<p>West, 120 km</p>"],
                    options_hi: ["<p>पूर्व, 90 km</p>", "<p>पश्चिम, 60 km</p>",
                                "<p>पूर्व, 120 km</p>", "<p>पश्चिम, 120 km</p>"],
                    solution_en: "<p>58.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934562.png\" alt=\"rId36\" width=\"250\" height=\"101\"><br>She is 120 km in the East direction from her home.</p>",
                    solution_hi: "<p>58.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934674.png\" alt=\"rId37\" width=\"250\"><br>वह अपने घर से 120 किमी दूर पूर्व दिशा में है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "59",
                    section: "misc",
                    question_en: "<p>59. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>GOOD : HSXT :: BEST : CIBJ :: COOL : ?</p>",
                    question_hi: "<p>59. उस विकल्प का चयन कीजिए जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है, जिस प्रकार से दूसरा अक्षर-समूह, पहले अक्षर-समूह से संबंधित हैऔर चौथा अक्षर-समूह, तीसरेअक्षर-समूह से संबंधित है।<br>GOOD : HSXT :: BEST : CIBJ :: COOL : ?</p>",
                    options_en: ["<p>DSSB</p>", "<p>DSXZ</p>", 
                                "<p>DSZB</p>", "<p>DSXB</p>"],
                    options_hi: ["<p>DSSB</p>", "<p>DSXZ</p>",
                                "<p>DSZB</p>", "<p>DSXB</p>"],
                    solution_en: "<p>59.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934785.png\" alt=\"rId38\" width=\"100\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934889.png\" alt=\"rId39\" width=\"100\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935004.png\" alt=\"rId40\" width=\"100\"></p>",
                    solution_hi: "<p>59.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934785.png\" alt=\"rId38\" width=\"100\"> , <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642934889.png\" alt=\"rId39\" width=\"100\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935004.png\" alt=\"rId40\" width=\"100\"></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "60",
                    section: "misc",
                    question_en: "<p>60. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935116.png\" alt=\"rId41\" width=\"90\"></p>",
                    question_hi: "<p>60. जब दर्पण को नीचे दिखाए अनुसार MN पर रखा जाए तो दी गई आकृति की सही दर्पण छवि का चयन करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935116.png\" alt=\"rId41\" width=\"100\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935236.png\" alt=\"rId42\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935333.png\" alt=\"rId43\" width=\"70\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935440.png\" alt=\"rId44\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935543.png\" alt=\"rId45\" width=\"70\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935236.png\" alt=\"rId42\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935333.png\" alt=\"rId43\" width=\"70\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935440.png\" alt=\"rId44\" width=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935543.png\" alt=\"rId45\" width=\"70\"></p>"],
                    solution_en: "<p>60.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935543.png\" alt=\"rId45\" width=\"70\"></p>",
                    solution_hi: "<p>60.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935543.png\" alt=\"rId45\" width=\"70\"></p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "61",
                    section: "misc",
                    question_en: "<p>1. Select the correct spelling from the given options to fill in the blank.<br>Jaysmita is one of the most __________ persons I have ever met as she has many talents.</p>",
                    question_hi: "<p>1. Select the correct spelling from the given options to fill in the blank.<br>Jaysmita is one of the most __________ persons I have ever met as she has many talents.</p>",
                    options_en: ["<p>versataile</p>", "<p>versatele</p>", 
                                "<p>versateile</p>", "<p>versatile</p>"],
                    options_hi: ["<p>versataile</p>", "<p>versatele</p>",
                                "<p>versateile</p>", "<p>versatile</p>"],
                    solution_en: "<p>1.(d) Versatile<br>&lsquo;Versatile&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>1.(d) Versatile<br>&lsquo;Versatile&rsquo; सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "62",
                    section: "misc",
                    question_en: "<p>2. Select the most appropriate ANTONYM of the word given in brackets to fill in the blank.<br>The gradual______(<strong>curtail</strong>) in the expenditures made them tensed.</p>",
                    question_hi: "<p>2. Select the most appropriate ANTONYM of the word given in brackets to fill in the blank.<br>The gradual______(<strong>curtail</strong>) in the expenditures made them tensed.</p>",
                    options_en: ["<p>indent</p>", "<p>innate</p>", 
                                "<p>increase</p>", "<p>insinuate</p>"],
                    options_hi: ["<p>indent</p>", "<p>innate</p>",
                                "<p>increase</p>", "<p>insinuate</p>"],
                    solution_en: "<p>2.(c) <strong>Increase </strong>- to become larger in size or quantity.<br><strong>Curtail </strong>- to reduce or limit the extent or amount of something.<br><strong>Indent </strong>- to create a space at the beginning of a line to set it apart from the margin.<br><strong>Innate </strong>- naturally present or inherent in someone from birth.<br><strong>Insinuate </strong>- to suggest or hint indirectly.</p>",
                    solution_hi: "<p>2.(c) <strong>Increase </strong>(वृद्धि करना/बढ़ाना) - to become larger in size or quantity.<br><strong>Curtail </strong>(घटाना/संक्षिप्त करना) - to reduce or limit the extent or amount of something.<br><strong>Indent </strong>(मांगपत्र) - to create a space at the beginning of a line to set it apart from the margin.<br><strong>Innate</strong> (जन्मजात) - naturally present or inherent in someone from birth.<br><strong>Insinuate </strong>(इशारा करना) - to suggest or hint indirectly.</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "63",
                    section: "misc",
                    question_en: "<p>3. Select the most appropriate synonym of the given word.<br><strong>Trousseau</strong></p>",
                    question_hi: "<p>3. Select the most appropriate synonym of the given word.<br><strong>Trousseau</strong></p>",
                    options_en: ["<p>Being true to oneself</p>", "<p>Single leg side of a trouser</p>", 
                                "<p>A large cabinet full of expensive clothes</p>", "<p>The clothes, linen, and other belongings collected by a bride for her marriage</p>"],
                    options_hi: ["<p>Being true to oneself</p>", "<p>Single leg side of a trouser</p>",
                                "<p>A large cabinet full of expensive clothes</p>", "<p>The clothes, linen, and other belongings collected by a bride for her marriage</p>"],
                    solution_en: "<p>3.(d) Trousseau - the clothes, linen, and other belongings collected by a bride for her marriage.</p>",
                    solution_hi: "<p>3.(d) Trousseau (दुल्हन का साज सामान) - the clothes, linen, and other belongings collected by a bride for her marriage.</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "64",
                    section: "misc",
                    question_en: "<p>4. Select the option that can be used as a one-word substitute for the given group of words<br>Waver between different opinions or actions</p>",
                    question_hi: "<p>4. Select the option that can be used as a one-word substitute for the given group of words<br>Waver between different opinions or actions</p>",
                    options_en: ["<p>Sway</p>", "<p>Viaduct</p>", 
                                "<p>Thrifty</p>", "<p>Vacillate</p>"],
                    options_hi: ["<p>Sway</p>", "<p>Viaduct</p>",
                                "<p>Thrifty</p>", "<p>Vacillate</p>"],
                    solution_en: "<p>4.(d) <strong>Vacillate </strong>- waver between different opinions or actions, indecisive.<br><strong>Sway </strong>- to move slowly from side to side.<br><strong>Viaduct </strong>- a bridge-like structure carrying a road or railway over a valley or other low ground.<br><strong>Thrifty </strong>- careful with money, frugal.</p>",
                    solution_hi: "<p>4.(d) <strong>Vacillate </strong>(दुविधा/अनिश्चितता) - waver between different opinions or actions, indecisive.<br><strong>Sway </strong>(झूमना) - to move slowly from side to side.<br><strong>Viaduct </strong>(पुल/सेतु) - a bridge-like structure carrying a road or railway over a valley or other low ground.<br><strong>Thrifty </strong>(मितव्ययी) - careful with money, frugal.</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "65",
                    section: "misc",
                    question_en: "<p>5. In the following question, out of the four alternatives, select the alternative which best expresses the meaning of the idiom/phrase.<br><strong>a mare&rsquo;s nest</strong></p>",
                    question_hi: "<p>5. In the following question, out of the four alternatives, select the alternative which best expresses the meaning of the idiom/phrase.<br><strong>a mare&rsquo;s nest</strong></p>",
                    options_en: ["<p>a familiar situation</p>", "<p>a new situation</p>", 
                                "<p>a refreshing situation</p>", "<p>a confused situation</p>"],
                    options_hi: ["<p>a familiar situation</p>", "<p>a new situation</p>",
                                "<p>a refreshing situation</p>", "<p>a confused situation</p>"],
                    solution_en: "<p>5.(d) <strong>A mare&rsquo;s nest</strong> - a confused situation.<br>E.g.- Finding a solution to the complex math problem turned out to be a mare\'s nest for the students.</p>",
                    solution_hi: "<p>5.(d) <strong>A mare&rsquo;s nest</strong> - a confused situation. / भ्रमित स्थिति में होना <br>E.g.- Finding a solution to the complex math problem turned out to be a mare\'s nest for the students.</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "66",
                    section: "misc",
                    question_en: "<p>6. The question consists of four statements labelled P, Q, R, ans S which when logically ordered form a coherent passage. Choose the option that represents the most logical order.<br>Mount Everest is a peak in the Himalaya mountain range.<br>P. It is located between Nepal and Tibet, an autonomous region of China. <br>Q . At 8,849 meters (29,032 feet), it is considered the tallest point on Earth.<br>R. In the nineteenth century, the mountain was named after George Everest, a former Surveyor General of India.<br>S. The Tibetan name is Chomolungma, which means \"Mother Goddess of the World.\" The Nepali name is Sagarmatha, which has various meanings.</p>",
                    question_hi: "<p>6. The question consists of four statements labelled P, Q, R, ans S which when logically ordered form a coherent passage. Choose the option that represents the most logical order.<br>Mount Everest is a peak in the Himalaya mountain range.<br>P. It is located between Nepal and Tibet, an autonomous region of China. <br>Q . At 8,849 meters (29,032 feet), it is considered the tallest point on Earth.<br>R. In the nineteenth century, the mountain was named after George Everest, a former Surveyor General of India.<br>S. The Tibetan name is Chomolungma, which means \"Mother Goddess of the World.\" The Nepali name is Sagarmatha, which has various meanings.</p>",
                    options_en: ["<p>PQRS</p>", "<p>SPQR</p>", 
                                "<p>SRQP</p>", "<p>RPSQ</p>"],
                    options_hi: ["<p>PQRS</p>", "<p>SPQR</p>",
                                "<p>SRQP</p>", "<p>RPSQ</p>"],
                    solution_en: "<p>6.(a) <strong>PQRS</strong><br>Sentence P will be the starting line as it introduces the main idea of the parajumble i.e. location of Mount Everest. However, Sentence Q states that it is considered the tallest point on Earth. So, Q will follow P. Further, Sentence R states that in the nineteenth century, the mountain was named after George Everest and Sentence S states that the Tibetan name is Chomolungma, which means Mother Goddess of the World. So, S will follow R. Going through the options, option a has the correct sequence.</p>",
                    solution_hi: "<p>6.(a) <strong>PQRS</strong><br>Sentence P प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;location of Mount Everest&rsquo; शामिल है । हालाँकि, Sentence Q कहता है कि इसे पृथ्वी का सबसे ऊँचा चोटी माना जाता है। तो, P के बाद Q आएगा। आगे, Sentence R कहता है कि 19वीं शताब्दी में, पहाड़ का नाम जॉर्ज एवरेस्ट के नाम पर रखा गया था और Sentence S कहता है कि तिब्बती नाम चोमोलुंगमा है, जिसका अर्थ है विश्व की देवी। तो, R, के बाद S आएगा। Options के माध्यम से जाने पर , option a में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "67",
                    section: "misc",
                    question_en: "<p>7. Select the option that expresses the given sentence in active voice<br>Let the fire be kept burning at night.</p>",
                    question_hi: "<p>7. Select the option that expresses the given sentence in active voice<br>Let the fire be kept burning at night.</p>",
                    options_en: ["<p>You must burn the fire at night.</p>", "<p>The fire should be burnt at night.</p>", 
                                "<p>Let the fire burning at night</p>", "<p>Keep the fire burning at night.</p>"],
                    options_hi: ["<p>You must burn the fire at night.</p>", "<p>The fire should be burnt at night.</p>",
                                "<p>Let the fire burning at night</p>", "<p>Keep the fire burning at night.</p>"],
                    solution_en: "<p>7.(d) Keep the fire burning at night.(Correct)<br>(a) You must burn the fire at night.(Incorrect Sentence Structure)<br>(b) The fire should be burnt at night.(Incorrect Sentence Structure)<br>(c) Let the fire burning at night.(Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>7.(d) Keep the fire burning at night.(Correct)<br>(a) You must burn the fire at night.(गलत Sentence Structure)<br>(b) The fire should be burnt at night.(गलत Sentence Structure)<br>(c) Let the fire burning at night.(गलत Sentence Structure)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "68",
                    section: "misc",
                    question_en: "<p>8. Select the option that can be used as a one-word substitute for the given group of words<br>Enjoying or affording warm secure shelter or cover and opportunity for ease and contentment</p>",
                    question_hi: "<p>8. Select the option that can be used as a one-word substitute for the given group of words<br>Enjoying or affording warm secure shelter or cover and opportunity for ease and contentment</p>",
                    options_en: ["<p>Undisturbed</p>", "<p>Easeful</p>", 
                                "<p>Untroubled</p>", "<p>Snug</p>"],
                    options_hi: ["<p>Undisturbed</p>", "<p>Easeful</p>",
                                "<p>Untroubled</p>", "<p>Snug</p>"],
                    solution_en: "<p>8.(d) <strong>Snug </strong>- Enjoying or affording warm secure shelter or cover and opportunity for ease and contentment<br><strong>Undisturbed </strong>- not disrupted or interrupted; calm and peaceful.<br><strong>Easeful </strong>- providing comfort, relaxation, or a sense of ease.<br><strong>Untroubled </strong>- free from worries, problems, or disturbances.</p>",
                    solution_hi: "<p>8.(d) <strong>Snug </strong>(आरामदायक) - Enjoying or affording warm secure shelter or cover and opportunity for ease andcontentment<br><strong>Undisturbed </strong>(अबाधित) - not disrupted or interrupted; calm and peaceful.<br><strong>Easeful </strong>(सुखद) - providing comfort, relaxation, or a sense of ease.<br><strong>Untroubled </strong>(निश्चिंत) - free from worries, problems, or disturbances.</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "69",
                    section: "misc",
                    question_en: "<p>9. Select the option that expresses the given sentence in indirect speech. <br>Arya said, &ldquo;I am very busy now.&rdquo;</p>",
                    question_hi: "<p>9. Select the option that expresses the given sentence in indirect speech. <br>Arya said, &ldquo;I am very busy now.&rdquo;</p>",
                    options_en: ["<p>Arya said that she is being very busy now.</p>", "<p>Arya said that she is be very busy now.</p>", 
                                "<p>Arya said that she was very busy then.</p>", "<p>Arya said that she was being very busy then.</p>"],
                    options_hi: ["<p>Arya said that she is being very busy now.</p>", "<p>Arya said that she is be very busy now.</p>",
                                "<p>Arya said that she was very busy then.</p>", "<p>Arya said that she was being very busy then.</p>"],
                    solution_en: "<p>9.(c) Arya said that she was very busy then.(Correct)<br>(a) Arya said that she <span style=\"text-decoration: underline;\">is being</span> very busy now.(Incorrect Tense)<br>(b) Arya said that she <span style=\"text-decoration: underline;\">is be</span> very busy now.(Incorrect Tense)<br>(d) Arya said that she <span style=\"text-decoration: underline;\">was being</span> very busy then.(Incorrect Tense structure)</p>",
                    solution_hi: "<p>9.(c) Arya said that she was very busy then.(Correct)<br>(a) Arya said that she <span style=\"text-decoration: underline;\">is being</span> very busy now.(गलत Tense)<br>(b) Arya said that she <span style=\"text-decoration: underline;\">is be</span> very busy now.(गलत Tense)<br>(d) Arya said that she <span style=\"text-decoration: underline;\">was being</span> very busy then.(गलत Tense structure)</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "70",
                    section: "misc",
                    question_en: "<p>10. Select the most appropriate option to fill in the blank.<br>A loud thud woke up everybody and nobody knew what all the _________ was about.</p>",
                    question_hi: "<p>10. Select the most appropriate option to fill in the blank.<br>A loud thud woke up everybody and nobody knew what all the _________ was about.</p>",
                    options_en: ["<p>exoneration</p>", "<p>commotion</p>", 
                                "<p>abomination</p>", "<p>culmination</p>"],
                    options_hi: ["<p>exoneration</p>", "<p>commotion</p>",
                                "<p>abomination</p>", "<p>culmination</p>"],
                    solution_en: "<p>10.(b) <strong>Commotion</strong><br>&lsquo;Commotion&rsquo; means a state of noisy and chaotic disturbance or tumultuous activity. The given sentence states that something unexpected and noisy occurred, causing confusion or disturbance. Hence, &lsquo;Commotion&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>10.(b) <strong>Commotion</strong><br>Commotion का अर्थ शोरगुल और अशांति या उथल-पुथल वाली गतिविधि की स्थिति है। दिए गए वाक्य में कहा गया है कि कुछ अप्रत्याशित शोर-शराबा हुआ, जिससे अव्यवस्था या अशांति हो गई। अतः, &lsquo;Commotion&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "71",
                    section: "misc",
                    question_en: "<p>11. Parts of the following sentence have been given as options. Select the option that contains an error.<br>It was Ravi, not his friends, / who were shouting / at late hours last night.</p>",
                    question_hi: "<p>11. Parts of the following sentence have been given as options. Select the option that contains an error.<br>It was Ravi, not his friends, / who were shouting / at late hours last night.</p>",
                    options_en: ["<p>It was Ravi, not his friends,</p>", "<p>No error</p>", 
                                "<p>who were shouting</p>", "<p>at late hours last night.</p>"],
                    options_hi: ["<p>It was Ravi, not his friends,</p>", "<p>No error</p>",
                                "<p>who were shouting</p>", "<p>at late hours last night.</p>"],
                    solution_en: "<p>11.(c) <strong>who were shouting</strong><br>&lsquo;Were&rsquo; must be replaced with &lsquo;was.&rsquo; According to the &ldquo;Subject-Verb Agreement Rule&rdquo;, a singular subject always takes a singular verb and a plural subject always takes a plural verb. <br>&lsquo;Ravi&rsquo; is a singular subject that will take &lsquo;was&rsquo; as a singular verb. Hence, &lsquo;who was shouting&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>11.(c) <strong>who were shouting</strong><br>&lsquo;Were&rsquo; के स्थान पर &lsquo;was&rsquo; का प्रयोग होगा। <br>\"Subject- verb agreement&rdquo; नियम के अनुसार, singular subject के साथ हमेशा singular verb का प्रयोग होता है और plural subject के साथ हमेशा plural verb का प्रयोग होता है। &lsquo;Ravi&rsquo; एक singular subject है जिसके साथ singular verb &lsquo;was&rsquo; का प्रयोग होगा । इसलिए, &lsquo;who was shouting&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "72",
                    section: "misc",
                    question_en: "<p>12. Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.<br>He brought these sweets <span style=\"text-decoration: underline;\">for you and I</span>.</p>",
                    question_hi: "<p>12. Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.<br>He brought these sweets <span style=\"text-decoration: underline;\"><strong>for you and I</strong></span>.</p>",
                    options_en: ["<p>for I and you</p>", "<p>for you and I both</p>", 
                                "<p>for you and me</p>", "<p>No substitution required</p>"],
                    options_hi: ["<p>for I and you</p>", "<p>for you and I both</p>",
                                "<p>for you and me</p>", "<p>No substitution required </p>"],
                    solution_en: "<p>12.(c) <strong>For you and me</strong><br>Replace &lsquo;I&rsquo; with &lsquo;me&rsquo; in the sentence. Because we never use subjective cases in objective places. Hence, &lsquo;he brought these sweets for you and me&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>12.(c)<strong> For you and me</strong><br>Sentence में &lsquo;I&rsquo; के स्थान पर &lsquo;me&rsquo; होगा । क्योंकि हम कभी भी objective places में subjective cases का प्रयोग नहीं करते हैं। इसलिए, &lsquo;he brought these sweets for you and me&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "73",
                    section: "misc",
                    question_en: "<p>13. Select the correct indirect narration of the given sentence.<br>Aravind said, &ldquo;Let us wait for the cab.&rdquo;</p>",
                    question_hi: "<p>13. Select the correct indirect narration of the given sentence.<br>Aravind said, &ldquo;Let us wait for the cab.&rdquo;</p>",
                    options_en: ["<p>Aravind proposed that they should wait for the cab.</p>", "<p>Aravind proposed that they should has wait for the cab.</p>", 
                                "<p>Aravind proposes that let we waited for the cab.</p>", "<p>Aravind propose that they should been wait for the cab.</p>"],
                    options_hi: ["<p>Aravind proposed that they should wait for the cab.</p>", "<p>Aravind proposed that they should has wait for the cab.</p>",
                                "<p>Aravind proposes that let we waited for the cab.</p>", "<p>Aravind propose that they should been wait for the cab.</p>"],
                    solution_en: "<p>13.(a) Aravind proposed that they should wait for the cab. (Correct)<br>(b) Aravind proposed that they <span style=\"text-decoration: underline;\">should has</span> wait for the cab. (Incorrect use of &lsquo;has&rsquo;)<br>(c) Aravind proposes that <span style=\"text-decoration: underline;\">let we waited</span> for the cab. (Incorrect use of &lsquo;let&rsquo; and tense)<br>(d) Aravind propose that <span style=\"text-decoration: underline;\">they should been</span> wait for the cab. (Incorrect use of &lsquo;been&rsquo;)</p>",
                    solution_hi: "<p>13.(a) Aravind proposed that they should wait for the cab. (Correct)<br>(b) Aravind proposed that they <span style=\"text-decoration: underline;\">should has</span> wait for the cab. (&lsquo;has&rsquo; का प्रयोग गलत है)<br>(c) Aravind proposes that <span style=\"text-decoration: underline;\">let we waited</span> for the cab. (&lsquo;let&rsquo; और tense का प्रयोग गलत है)<br>(d) Aravind propose that <span style=\"text-decoration: underline;\">they should been</span> wait for the cab. (&lsquo;been&rsquo; का प्रयोग गलत है)</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "74",
                    section: "misc",
                    question_en: "<p>14. Select the correct passive form of the given sentence.<br>One can achieve nothing without hard work.</p>",
                    question_hi: "<p>14. Select the correct passive form of the given sentence.<br>One can achieve nothing without hard work.</p>",
                    options_en: ["<p>Nothing is achieved without hard work.</p>", "<p>Nothing will be achieved without hard work</p>", 
                                "<p>Nothing has been achieved without hard work.</p>", "<p>Nothing can be achieved without hard work.</p>"],
                    options_hi: ["<p>Nothing is achieved without hard work.</p>", "<p>Nothing will be achieved without hard work</p>",
                                "<p>Nothing has been achieved without hard work.</p>", "<p>Nothing can be achieved without hard work.</p>"],
                    solution_en: "<p>14.(d) Nothing can be achieved without hard work.(Correct)<br>(a) Nothing <span style=\"text-decoration: underline;\">is</span> achieved without hard work.(Incorrect Helping Verb)<br>(b) Nothing <span style=\"text-decoration: underline;\">will</span> be achieved without hard work.(Incorrect Modal)<br>(c) Nothing <span style=\"text-decoration: underline;\">has been</span> achieved without hard work.(Incorrect Helping Verb)</p>",
                    solution_hi: "<p>14.(d) Nothing can be achieved without hard work.(Correct)<br>(a) Nothing <span style=\"text-decoration: underline;\">is</span> achieved without hard work.(गलत Helping Verb)<br>(b) Nothing <span style=\"text-decoration: underline;\">will</span> be achieved without hard work.(गलत Modal)<br>(c) Nothing <span style=\"text-decoration: underline;\">has been</span> achieved without hard work.(गलत Helping Verb)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "75",
                    section: "misc",
                    question_en: "<p>15. The question consists of four statements labelled P, Q, R, ans S which when logically ordered form a coherent passage. Choose the option that represents the most logical order.<br>P. Amongst all the cities of Japan, Hiroshima is the one that experiences earliest mornings.<br>Q . It experiences the earliest sun rise amongst all the countries.<br>R. According to the words of the Japanese envoy himself, that name was chosen because the country was so close to where the sun rises.<br>S. Japan is also known as the land of the rising sun.</p>",
                    question_hi: "<p>15. The question consists of four statements labelled P, Q, R, ans S which when logically ordered form a coherent passage. Choose the option that represents the most logical order.<br>P. Amongst all the cities of Japan, Hiroshima is the one that experiences earliest mornings.<br>Q . It experiences the earliest sun rise amongst all the countries.<br>R. According to the words of the Japanese envoy himself, that name was chosen because the country was so close to where the sun rises.<br>S. Japan is also known as the land of the rising sun.</p>",
                    options_en: ["<p>SRQP</p>", "<p>PSQR</p>", 
                                "<p>QPRS</p>", "<p>SQRP</p>"],
                    options_hi: ["<p>SRQP</p>", "<p>PSQR</p>",
                                "<p>QPRS</p>", "<p>SQRP</p>"],
                    solution_en: "<p>15.(d) <strong>SQRP</strong><br>Sentence S will be the starting line as it introduces the main idea of the parajumble i.e. Japan, the land of the rising sun. However, Sentence Q states that it experiences the earliest sun rise amongst all the countries. So, Q will follow S. Further, Sentence R states that the name was chosen because the country was so close to where the sun rises and Sentence P states that Hiroshima in Japan is the one that experiences earliest mornings. So, P will follow R. Going through the options, option d has the correct sequence.</p>",
                    solution_hi: "<p>15.(d) <strong>SQRP</strong><br>Sentence S प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;Japan, the land of the rising sun&rsquo; शामिल है। हालाँकि, Sentence Q कहता है कि यह सभी देशों में सबसे पहले सूर्य उदय का अनुभव करता है। तो, S के बाद Q आएगा । आगे, Sentence R कहता है कि नाम चुना गया था क्योंकि देश सूरज उगने के करीब था और Sentence P कहता है कि जापान में हिरोशिमा वह है जो सबसे पहले सुबह का अनुभव करता है। तो, R,के बाद P आएगा। Options के माध्यम से जाने पर, option d में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "76",
                    section: "misc",
                    question_en: "<p>16. The question consists of four statements labelled P, Q, R, ans S which when logically ordered form a coherent passage. Choose the option that represents the most logical order.<br>P. little free time like a single parent nurse caring for <br>Q . more than one child and working long hours are <br>R. we showed that people on low incomes with<br>S. set to lose out most as energy prices rise</p>",
                    question_hi: "<p>16. The question consists of four statements labelled P, Q, R, ans S which when logically ordered form a coherent passage. Choose the option that represents the most logical order.<br>P. little free time like a single parent nurse caring for <br>Q . more than one child and working long hours are <br>R. we showed that people on low incomes with<br>S. set to lose out most as energy prices rise</p>",
                    options_en: ["<p>SPQR</p>", "<p>RPQS</p>", 
                                "<p>PQSR</p>", "<p>RQSP</p>"],
                    options_hi: ["<p>SPQR</p>", "<p>RPQS</p>",
                                "<p>PQSR</p>", "<p>RQSP</p>"],
                    solution_en: "<p>16.(b) <strong>RPQS</strong> <br>Sentence R will be the starting line as it introduces the main idea of the parajumble i.e. people of low incomes. However, Sentence P states that they have little time like a single parent. So, P will follow R. Further, Sentence Q talks about a nurse caring for more than one child and Sentence S states that they are set to lose out most as energy prices rise. So, S will follow Q . Going through the options, option b has the correct sequence.</p>",
                    solution_hi: "<p>16.(b) <strong>RPQS</strong> <br>Sentence R प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;people of low incomes&rsquo; शामिल है। हालाँकि, Sentence P बताता है कि उनके पास एकल अभिभावक की तरह बहुत कम समय है। तो,R, के बाद P आएगा। आगे, Sentence Q एक से अधिक बच्चों की देखभाल करने वाली नर्स के बारे में बात करता है और Sentence S कहता है कि ऊर्जा की कीमतों में वृद्धि के रूप में वे सबसे अधिक खोने के लिए तैयार हैं। तो, Q, के बाद S आएगा। Options के माध्यम से जाने पर , विकल्प b में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "77",
                    section: "misc",
                    question_en: "<p>17. In the following question, out of the four alternatives, select the alternative which best expresses the meaning of the underlined idiom/phrase.<br>The new routine will suit you <span style=\"text-decoration: underline;\"><strong>to a T</strong></span>.</p>",
                    question_hi: "<p>17. In the following question, out of the four alternatives, select the alternative which best expresses the meaning of the underlined idiom/phrase.<br>The new routine will suit you <span style=\"text-decoration: underline;\"><strong>to a T</strong></span>.</p>",
                    options_en: ["<p>nicely</p>", "<p>a little bit</p>", 
                                "<p>very poorly</p>", "<p>exactly</p>"],
                    options_hi: ["<p>nicely</p>", "<p>a little bit</p>",
                                "<p>very poorly</p>", "<p>exactly</p>"],
                    solution_en: "<p>17.(d) <strong>to a T OR to a tee</strong> - exactly</p>",
                    solution_hi: "<p>17.(d) <strong>to a T OR to a tee</strong> - exactly / बिल्कुल सही।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "78",
                    section: "misc",
                    question_en: "<p>18. Select the option that expresses the given sentence in indirect speech.<br>She asked, &ldquo; Where do you live ?&rdquo;</p>",
                    question_hi: "<p>18. Select the option that expresses the given sentence in indirect speech.<br>She asked, &ldquo; Where do you live ?&rdquo;</p>",
                    options_en: ["<p>She asked me where I had lived.</p>", "<p>She told me where I lived.</p>", 
                                "<p>She asked me where I had been living.</p>", "<p>She asked me where I lived.</p>"],
                    options_hi: ["<p>She asked me where I had lived.</p>", "<p>She told me where I lived.</p>",
                                "<p>She asked me where I had been living.</p>", "<p>She asked me where I lived.</p>"],
                    solution_en: "<p>18.(d) She asked me where I lived. (Correct)<br>(a) She asked me where I <span style=\"text-decoration: underline;\">had</span> lived. (Incorrect Helping Verb)<br>(b) She <span style=\"text-decoration: underline;\">told</span> me where I lived. (Incorrect Reporting Verb)<br>(c) She asked me where I <span style=\"text-decoration: underline;\">had been</span> living. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>18.(d) She asked me where I lived. (Correct)<br>(a) She asked me where I <span style=\"text-decoration: underline;\">had</span> lived. (गलत Helping Verb)<br>(b) She <span style=\"text-decoration: underline;\">told</span> me where I lived. (गलत Reporting Verb)<br>(c) She asked me where I <span style=\"text-decoration: underline;\">had been </span>living. (गलत Helping Verb)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "79",
                    section: "misc",
                    question_en: "<p>19. Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.<br>The apple tree was <span style=\"text-decoration: underline;\"><strong>loaded of</strong></span> fruit.</p>",
                    question_hi: "<p>19. Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.<br>The apple tree was <strong>loaded of</strong> fruit.</p>",
                    options_en: ["<p>No Improvement</p>", "<p>loaded with</p>", 
                                "<p>loaded from</p>", "<p>laden with</p>"],
                    options_hi: ["<p>No Improvement</p>", "<p>loaded with</p>",
                                "<p>loaded from</p>", "<p>laden with</p>"],
                    solution_en: "<p>19.(d) <strong>Laden with</strong><br>&lsquo;Laden&rsquo; means heavily loaded with something. The given sentence states that the apple tree was laden (heavily loaded) with fruits. Hence, &lsquo;Laden with&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>19.(d)<strong> Laden with</strong><br>\'Laden\' का अर्थ है किसी चीज से लदा हुआ। दिए गए sentence में कहा गया है कि सेब का पेड़ फलों से लदा (heavily loaded) हुआ था। इसलिए, \'Laden with\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "80",
                    section: "misc",
                    question_en: "<p>20. Select the most appropriate antonym of the given word.<br><strong>Falter</strong></p>",
                    question_hi: "<p>20. Select the most appropriate antonym of the given word.<br><strong>Falter</strong></p>",
                    options_en: ["<p>Stabilise</p>", "<p>Suspect</p>", 
                                "<p>Haunt</p>", "<p>Transfer</p>"],
                    options_hi: ["<p>Stabilise</p>", "<p>Suspect</p>",
                                "<p>Haunt</p>", "<p>Transfer</p>"],
                    solution_en: "<p>20.(a) <strong>Stabilise </strong>- to make something firm, steady, or more stable.<br><strong>Falter </strong>- to move unsteadily.<br><strong>Suspect </strong>- to have doubts about someone\'s involvement in wrongdoing.<br><strong>Haunt </strong>- to repeatedly come to mind or trouble someone.<br><strong>Transfer </strong>- to move something or someone from one place or position to another.</p>",
                    solution_hi: "<p>20.(a) <strong>Stabilise </strong>(स्थिर करना) - to make something firm, steady, or more stable.<br><strong>Falter </strong>(लड़खड़ाना) - to move unsteadily.<br><strong>Suspect </strong>(संदिग्ध व्यक्ति) - to have doubts about someone\'s involvement in wrongdoing.<br><strong>Haunt </strong>(सताना) - to repeatedly come to mind or trouble someone.<br><strong>Transfer </strong>(स्थानांतरण) - to move something or someone from one place or position to another.</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "81",
                    section: "misc",
                    question_en: "<p>21. Select the most appropriate SYNONYM of the word given in brackets to fill in the blank.<br>She\'s reading a _______ (<strong>secrecy</strong>) novel and can\'t wait to find out who the killer is.</p>",
                    question_hi: "<p>21. Select the most appropriate SYNONYM of the word given in brackets to fill in the blank.<br>She\'s reading a _______ (secrecy) novel and can\'t wait to find out who the killer is.</p>",
                    options_en: ["<p>crisis</p>", "<p>complication</p>", 
                                "<p>difficulty</p>", "<p>mystery</p>"],
                    options_hi: ["<p>crisis</p>", "<p>complication</p>",
                                "<p>difficulty</p>", "<p>mystery</p>"],
                    solution_en: "<p>21.(d) <strong>Mystery </strong>- something not understood or known.<br><strong>Secrecy </strong>- keeping information hidden or not disclosing it.<br><strong>Crisis</strong>- a crucial and difficult situation or turning point.<br><strong>Complication </strong>- a complex factor making a situation harder to understand.</p>",
                    solution_hi: "<p>21.(d) <strong>Mystery </strong>(रहस्य) - something not understood or known.<br><strong>Secrecy </strong>(गोपनीयता) - keeping information hidden or not disclosing it.<br><strong>Crisis </strong>(संकट-काल) - a crucial and difficult situation or turning point.<br><strong>Complication </strong>(जटिल स्थिति) - a complex factor making a situation harder to understand.</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "82",
                    section: "misc",
                    question_en: "<p>22. Parts of the following sentence have been given as options. Select the option that contains an error.<br>The current state of the literature / on the efficacy under cognitive-behavioural therapy / for individuals with generalised anxiety disorder / suggests that it is a promising treatment option.</p>",
                    question_hi: "<p>22. Parts of the following sentence have been given as options. Select the option that contains an error.<br>The current state of the literature / on the efficacy under cognitive-behavioural therapy / for individuals with generalised anxiety disorder / suggests that it is a promising treatment option.</p>",
                    options_en: ["<p>The current state of the literature</p>", "<p>suggests that it is a promising treatment option</p>", 
                                "<p>on the efficacy under cognitive-behavioural therapy</p>", "<p>for individuals with generalised anxiety disorder.</p>"],
                    options_hi: ["<p>The current state of the literature</p>", "<p>suggests that it is a promising treatment option</p>",
                                "<p>on the efficacy under cognitive-behavioural therapy</p>", "<p>for individuals with generalised anxiety disorder.</p>"],
                    solution_en: "<p>22.(c) <strong>on the efficacy under cognitive-behavioural therapy</strong><br>&lsquo;Under&rsquo; must be replaced with &lsquo;of&rsquo;. &lsquo;Under&rsquo; is used to denote the position of something/somebody at a lower level. &lsquo;Of&rsquo; is used to mention the parts or characteristics of something. Hence, &lsquo;on the efficacy of cognitive-behavioural therapy&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(c) <strong>on the efficacy under cognitive-behavioural therapy</strong><br>&lsquo;Under&rsquo; को &lsquo;of&rsquo; से बदला जाना चाहिए। &lsquo;Under&rsquo; का प्रयोग निचले स्तर पर किसी चीज़/व्यक्ति की स्थिति को दर्शाने के लिए किया जाता है। &lsquo;Of&rsquo; का प्रयोग किसी चीज़ के हिस्सों या विशेषताओं का उल्लेख करने के लिए किया जाता है। इसलिए, &lsquo;on the efficacy of cognitive-behavioural therapy&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "83",
                    section: "misc",
                    question_en: "23. Select the most appropriate option to fill in the blank.<br />I envy the way she ______  all tasks with such ease and speed.",
                    question_hi: "23. Select the most appropriate option to fill in the blank.<br />I envy the way she ______  all tasks with such ease and speed.",
                    options_en: [" carries out      ", " dwindles out  ", 
                                " bring about         ", " account for   "],
                    options_hi: [" carries out      ", " dwindles out  ",
                                " bring about         ", " account for   "],
                    solution_en: "23.(a) ‘Carries out’ means to bring to a successful issue. The given sentence states that I envy the way she carries out all tasks with such ease and speed. Hence, ‘carries out’ is the most appropriate answer.",
                    solution_hi: "23.(a) ‘Carries out’ का अर्थ है एक सफल मुद्दे पर लाना। दिए गए sentence में कहा गया है कि जिस तरह से वह सभी कार्यों को इतनी आसानी और गति से करती है उससे मैं ईर्ष्या करता हूं। इसलिए, ‘carries out’\' सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "84",
                    section: "misc",
                    question_en: "<p>24. Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.<br>Many families are <span style=\"text-decoration: underline;\"><strong>so poor</strong></span> to bear the cost of educating all their children.</p>",
                    question_hi: "<p>24. Improve the underlined part of the sentence. Choose \'No improvement\' as an answer if the sentence is grammatically correct.<br>Many families are <span style=\"text-decoration: underline;\"><strong>so poor</strong></span> to bear the cost of educating all their children.</p>",
                    options_en: ["<p>No improvement</p>", "<p>too poor</p>", 
                                "<p>to poor</p>", "<p>such poor</p>"],
                    options_hi: ["<p>No improvement</p>", "<p>too poor</p>",
                                "<p>to poor</p>", "<p>such poor</p>"],
                    solution_en: "<p>24.(b) <strong>&ldquo;Too&hellip;.to&rdquo;</strong> is a fixed conjunction pair that is used in a sentence giving a negative sense. Similarly, the given sentence gives a negative sense of not having enough money to provide education. Hence, &lsquo;too poor to&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(b)<strong> &ldquo;Too&hellip;.to&rdquo;</strong> एक fixed conjunction pair है जिसका प्रयोग नकारात्मक अर्थ देने वाले वाक्य में किया जाता है।<br>इसी प्रकार, दिया गया वाक्य शिक्षा प्रदान करने के लिए पर्याप्त धन न होने का नकारात्मक बोध कराता है। इसलिए, &lsquo;too poor to&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "85",
                    section: "misc",
                    question_en: "<p>25. Select the most appropriate option to fill in the blank.<br>Be it a chaat or a refreshing lemonade, lemon is the need of the hour in_____ summers.</p>",
                    question_hi: "<p>25. Select the most appropriate option to fill in the blank.<br>Be it a chaat or a refreshing lemonade, lemon is the need of the hour in_____ summers.</p>",
                    options_en: ["<p>banging</p>", "<p>freezing</p>", 
                                "<p>scorching</p>", "<p>alleviating</p>"],
                    options_hi: ["<p>banging</p>", "<p>freezing</p>",
                                "<p>scorching</p>", "<p>alleviating</p>"],
                    solution_en: "<p>25.(c) <strong>scorching</strong>.<br>&lsquo;Scorching&rsquo; means very hot. The given sentence states that lemon is the need of the hour in very hot summers. Hence, &lsquo;scorching&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(c) <strong>scorching</strong><br>&lsquo;Scorching&rsquo; का अर्थ है बहुत गर्म। दिए गए sentence में कहा गया है कि बहुत तेज गर्मी के समय में नींबू की जरूरत होती हैं। इसलिए, &lsquo;Scorching&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "86",
                    section: "misc",
                    question_en: "<p>26. Choose the incorrectly spelt word.</p>",
                    question_hi: "<p>26. Choose the incorrectly spelt word.</p>",
                    options_en: ["<p>Separate</p>", "<p>Accommodate</p>", 
                                "<p>Definitely</p>", "<p>Embarass</p>"],
                    options_hi: ["<p>Separate</p>", "<p>Accommodate</p>",
                                "<p>Definitely</p>", "<p>Embarass</p>"],
                    solution_en: "<p>26.(d) Embarass<br>&lsquo;Embarrass&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>26.(d) Embarass<br>&lsquo;Embarrass&rsquo; सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "87",
                    section: "misc",
                    question_en: "<p>27. Parts of the following sentence have been given as options. Select the option that contains an error.<br>They decided to / travel in train / instead of driving / to New Delhi.</p>",
                    question_hi: "<p>27. Parts of the following sentence have been given as options. Select the option that contains an error.<br>They decided to / travel in train / instead of driving / to New Delhi.</p>",
                    options_en: ["<p>They decided to</p>", "<p>travel in train</p>", 
                                "<p>to New Delhi</p>", "<p>instead of driving</p>"],
                    options_hi: ["<p>They decided to</p>", "<p>travel in train</p>",
                                "<p>to New Delhi</p>", "<p>instead of driving</p>"],
                    solution_en: "<p>27.(b) <strong>travel in train</strong><br>Preposition &lsquo;in&rsquo; must be replaced with &lsquo;by&rsquo; as preposition &lsquo;by&rsquo; is used with the means or method of transportation. For example- by train, by bus, by car, etc. Hence, &lsquo;travel by train&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>27.(b) <strong>travel in train</strong><br>Preposition &lsquo;in&rsquo; के स्थान पर &lsquo;by&rsquo; का प्रयोग होगा क्योंकि preposition &lsquo;by&rsquo; का प्रयोग transportation के means या method के साथ किया जाता है। जैसे- by train, by bus, by car,आदि। अतः, &lsquo;travel by train&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "88",
                    section: "misc",
                    question_en: "<p>28. Select the correct active/passive form of the given sentence.<br>He is eating an apple.</p>",
                    question_hi: "<p>28. Select the correct active/passive form of the given sentence.<br>He is eating an apple.</p>",
                    options_en: ["<p>An apple was eaten by him.</p>", "<p>He was eating an apple.</p>", 
                                "<p>An apple is being eaten by him.</p>", "<p>He has been eating an apple.</p>"],
                    options_hi: ["<p>An apple was eaten by him.</p>", "<p>He was eating an apple.</p>",
                                "<p>An apple is being eaten by him.</p>", "<p>He has been eating an apple. </p>"],
                    solution_en: "<p>28.(c) An apple is being eaten by him.(Correct)<br>(a) An apple <span style=\"text-decoration: underline;\">was eaten</span> by him.(Incorrect Tense)<br>(b) He was eating an apple.(Incorrect Sentence Structure)<br>(d) He has been eating an apple. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>28.(c) An apple is being eaten by him.(Correct)<br>(a) An apple was eaten by him.(गलत Tense)<br>(b) He <span style=\"text-decoration: underline;\">was eating</span> an apple.(गलत Sentence Structure)<br>(d) He has been eating an apple.(गलत Sentence Structure)</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "89",
                    section: "misc",
                    question_en: "<p>29. <strong>Cloze Test:</strong><br>The greenhouse effect is a natural process that warms the Earth\'s surface. It occurs when certain gases in the atmosphere (29) _______ heat from the sun and trap it, preventing it from escaping back into space. These gases, known as greenhouse gases, include carbon dioxide, methane, and water vapor. While the greenhouse effect is essential for maintaining a (30) _______ temperature on Earth, human activities such as burning fossil fuels and deforestation have (31) _______ the concentration of greenhouse gases in the atmosphere, leading to enhanced global warming. This phenomenon, known as anthropogenic climate change, poses significant challenges for ecosystems, weather patterns, and sea levels. Addressing the greenhouse effect requires (32) _______ efforts to reduce greenhouse gas emissions and transition to renewable energy sources. By taking action to mitigate the greenhouse effect, we can work towards a more sustainable and resilient future for our planet.<br>Select the most appropriate option for blank 29.</p>",
                    question_hi: "<p>29. <strong>Cloze Test:</strong><br>The greenhouse effect is a natural process that warms the Earth\'s surface. It occurs when certain gases in the atmosphere (29) _______ heat from the sun and trap it, preventing it from escaping back into space. These gases, known as greenhouse gases, include carbon dioxide, methane, and water vapor. While the greenhouse effect is essential for maintaining a (30) _______ temperature on Earth, human activities such as burning fossil fuels and deforestation have (31) _______ the concentration of greenhouse gases in the atmosphere, leading to enhanced global warming. This phenomenon, known as anthropogenic climate change, poses significant challenges for ecosystems, weather patterns, and sea levels. Addressing the greenhouse effect requires (32) _______ efforts to reduce greenhouse gas emissions and transition to renewable energy sources. By taking action to mitigate the greenhouse effect, we can work towards a more sustainable and resilient future for our planet.<br>Select the most appropriate option for blank 29.</p>",
                    options_en: ["<p>absorb</p>", "<p>reflect</p>", 
                                "<p>release</p>", "<p>emit</p>"],
                    options_hi: ["<p>absorb</p>", "<p>reflect</p>",
                                "<p>release</p>", "<p>emit</p>"],
                    solution_en: "<p>29.(a) absorb <br>&lsquo;Absorb&rsquo; means to take in or soak up by chemical or physical action. The given passage states that the greenhouse effect occurs when certain gases in the atmosphere absorb heat from the sun and trap it, preventing it from escaping back into space. Hence, &lsquo;absorb&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>29.(a) absorb <br>&lsquo;Absorb&rsquo; means to take in or soak up by chemical or physical action. The given passage states that the greenhouse effect occurs when certain gases in the atmosphere absorb heat from the sun and trap it, preventing it from escaping back into space. Hence, &lsquo;absorb&rsquo; is the most appropriate answer. </p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "90",
                    section: "misc",
                    question_en: "<p>30. <strong>Cloze Test:</strong><br>The greenhouse effect is a natural process that warms the Earth\'s surface. It occurs when certain gases in the atmosphere (29) _______ heat from the sun and trap it, preventing it from escaping back into space. These gases, known as greenhouse gases, include carbon dioxide, methane, and water vapor. While the greenhouse effect is essential for maintaining a (30) _______ temperature on Earth, human activities such as burning fossil fuels and deforestation have (31) _______ the concentration of greenhouse gases in the atmosphere, leading to enhanced global warming. This phenomenon, known as anthropogenic climate change, poses significant challenges for ecosystems, weather patterns, and sea levels. Addressing the greenhouse effect requires (32) _______ efforts to reduce greenhouse gas emissions and transition to renewable energy sources. By taking action to mitigate the greenhouse effect, we can work towards a more sustainable and resilient future for our planet.<br>Select the most appropriate option for blank 30.</p>",
                    question_hi: "<p>30. <strong>Cloze Test:</strong><br>The greenhouse effect is a natural process that warms the Earth\'s surface. It occurs when certain gases in the atmosphere (29) _______ heat from the sun and trap it, preventing it from escaping back into space. These gases, known as greenhouse gases, include carbon dioxide, methane, and water vapor. While the greenhouse effect is essential for maintaining a (30) _______ temperature on Earth, human activities such as burning fossil fuels and deforestation have (31) _______ the concentration of greenhouse gases in the atmosphere, leading to enhanced global warming. This phenomenon, known as anthropogenic climate change, poses significant challenges for ecosystems, weather patterns, and sea levels. Addressing the greenhouse effect requires (32) _______ efforts to reduce greenhouse gas emissions and transition to renewable energy sources. By taking action to mitigate the greenhouse effect, we can work towards a more sustainable and resilient future for our planet.<br>Select the most appropriate option for blank 30.</p>",
                    options_en: ["<p>adaptable</p>", "<p>habitable</p>", 
                                "<p>inhabitable</p>", "<p>comfortable</p>"],
                    options_hi: ["<p>adaptable</p>", "<p>habitable</p>",
                                "<p>inhabitable</p>", "<p>comfortable</p>"],
                    solution_en: "<p>30.(b) habitable<br>&lsquo;Habitable&rsquo; means suitable or good enough to live in. The given passage states that the greenhouse effect is essential for maintaining a habitable temperature on Earth. Hence, &lsquo;habitable&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>30.(b) habitable<br>&lsquo;Habitable&rsquo; means suitable or good enough to live in. The given passage states that the greenhouse effect is essential for maintaining a habitable temperature on Earth. Hence, &lsquo;habitable&rsquo; is the most appropriate answer.</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "91",
                    section: "misc",
                    question_en: "<p>31. <strong>Cloze Test:</strong><br>The greenhouse effect is a natural process that warms the Earth\'s surface. It occurs when certain gases in the atmosphere (29) _______ heat from the sun and trap it, preventing it from escaping back into space. These gases, known as greenhouse gases, include carbon dioxide, methane, and water vapor. While the greenhouse effect is essential for maintaining a (30) _______ temperature on Earth, human activities such as burning fossil fuels and deforestation have (31) _______ the concentration of greenhouse gases in the atmosphere, leading to enhanced global warming. This phenomenon, known as anthropogenic climate change, poses significant challenges for ecosystems, weather patterns, and sea levels. Addressing the greenhouse effect requires (32) _______ efforts to reduce greenhouse gas emissions and transition to renewable energy sources. By taking action to mitigate the greenhouse effect, we can work towards a more sustainable and resilient future for our planet.<br>Select the most appropriate option for blank 31.</p>",
                    question_hi: "<p>31.<strong> Cloze Test:</strong><br>The greenhouse effect is a natural process that warms the Earth\'s surface. It occurs when certain gases in the atmosphere (29) _______ heat from the sun and trap it, preventing it from escaping back into space. These gases, known as greenhouse gases, include carbon dioxide, methane, and water vapor. While the greenhouse effect is essential for maintaining a (30) _______ temperature on Earth, human activities such as burning fossil fuels and deforestation have (31) _______ the concentration of greenhouse gases in the atmosphere, leading to enhanced global warming. This phenomenon, known as anthropogenic climate change, poses significant challenges for ecosystems, weather patterns, and sea levels. Addressing the greenhouse effect requires (32) _______ efforts to reduce greenhouse gas emissions and transition to renewable energy sources. By taking action to mitigate the greenhouse effect, we can work towards a more sustainable and resilient future for our planet.<br>Select the most appropriate option for blank 31.</p>",
                    options_en: ["<p>decreased</p>", "<p>increased</p>", 
                                "<p>stabilized</p>", "<p>maintained</p>"],
                    options_hi: ["<p>decreased</p>", "<p>increased</p>",
                                "<p>stabilized</p>", "<p>maintained</p>"],
                    solution_en: "<p>31.(b) increased<br>&lsquo;Increase&rsquo; means to become or make greater in size, amount, or degree. The given passage states that human activities such as burning fossil fuels and deforestation have increased the concentration of greenhouse gases in the atmosphere, leading to enhanced global warming. Hence, &lsquo;increased&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>31.(b) increased<br>&lsquo;Increase&rsquo; means to become or make greater in size, amount, or degree. The given passage states that human activities such as burning fossil fuels and deforestation have increased the concentration of greenhouse gases in the atmosphere, leading to enhanced global warming. Hence, &lsquo;increased&rsquo; is the most appropriate answer.</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "92",
                    section: "misc",
                    question_en: "<p>32. <strong>Cloze Test:</strong><br>The greenhouse effect is a natural process that warms the Earth\'s surface. It occurs when certain gases in the atmosphere (29) _______ heat from the sun and trap it, preventing it from escaping back into space. These gases, known as greenhouse gases, include carbon dioxide, methane, and water vapor. While the greenhouse effect is essential for maintaining a (30) _______ temperature on Earth, human activities such as burning fossil fuels and deforestation have (31) _______ the concentration of greenhouse gases in the atmosphere, leading to enhanced global warming. This phenomenon, known as anthropogenic climate change, poses significant challenges for ecosystems, weather patterns, and sea levels. Addressing the greenhouse effect requires (32) _______ efforts to reduce greenhouse gas emissions and transition to renewable energy sources. By taking action to mitigate the greenhouse effect, we can work towards a more sustainable and resilient future for our planet.<br>Select the most appropriate option for blank 32.</p>",
                    question_hi: "<p>32. <strong>Cloze Test:</strong><br>The greenhouse effect is a natural process that warms the Earth\'s surface. It occurs when certain gases in the atmosphere (29) _______ heat from the sun and trap it, preventing it from escaping back into space. These gases, known as greenhouse gases, include carbon dioxide, methane, and water vapor. While the greenhouse effect is essential for maintaining a (30) _______ temperature on Earth, human activities such as burning fossil fuels and deforestation have (31) _______ the concentration of greenhouse gases in the atmosphere, leading to enhanced global warming. This phenomenon, known as anthropogenic climate change, poses significant challenges for ecosystems, weather patterns, and sea levels. Addressing the greenhouse effect requires (32) _______ efforts to reduce greenhouse gas emissions and transition to renewable energy sources. By taking action to mitigate the greenhouse effect, we can work towards a more sustainable and resilient future for our planet.<br>Select the most appropriate option for blank 32.</p>",
                    options_en: ["<p>collective</p>", "<p>individual</p>", 
                                "<p>sporadic</p>", "<p>concerted</p>"],
                    options_hi: ["<p>collective</p>", "<p>individual</p>",
                                "<p>sporadic</p>", "<p>concerted</p>"],
                    solution_en: "<p>32.(d) concerted<br>&lsquo;Concert&rsquo; means to arrange something by mutual agreement or coordination. The given passage states that addressing the greenhouse effect requires concerted efforts to reduce greenhouse gas emissions and transition to renewable energy sources. Hence, &lsquo;concerted&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>32.(d) concerted<br>&lsquo;Concert&rsquo; means to arrange something by mutual agreement or coordination. The given passage states that addressing the greenhouse effect requires concerted efforts to reduce greenhouse gas emissions and transition to renewable energy sources. Hence, &lsquo;concerted&rsquo; is the most appropriate answer.</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "93",
                    section: "misc",
                    question_en: "<p>33. <strong>Comprehension:</strong><br>Aryabhatta, an ancient Indian mathematician and astronomer, made significant contributions to the fields of mathematics and astronomy during the 5th century. Born in 476 CE in Kusumapura (present-day Patna, India), Aryabhatta\'s work laid the foundation for many mathematical concepts that are still relevant today.<br>One of Aryabhatta\'s most notable achievements was his compilation of the Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit. In this work, he introduced the concept of zero, a revolutionary idea that had a profound impact on mathematics. Aryabhatta\'s understanding of the decimal system and place value greatly influenced mathematical development.<br>In astronomy, Aryabhatta accurately calculated the duration of a year, the Earth\'s circumference, and the positions of various celestial bodies. His insights paved the way for future astronomers and contributed to the understanding of the solar system.<br>Aryabhatta\'s legacy extends beyond his specific contributions. His works influenced Islamic and European mathematicians and astronomers, and his insights continue to be studied and revered in the field of mathematics and astronomy.<br>What is Aryabhatta\'s most notable contribution to mathematics, as mentioned in the passage?</p>",
                    question_hi: "<p>33. <strong>Comprehension:</strong><br>Aryabhatta, an ancient Indian mathematician and astronomer, made significant contributions to the fields of mathematics and astronomy during the 5th century. Born in 476 CE in Kusumapura (present-day Patna, India), Aryabhatta\'s work laid the foundation for many mathematical concepts that are still relevant today.<br>One of Aryabhatta\'s most notable achievements was his compilation of the Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit. In this work, he introduced the concept of zero, a revolutionary idea that had a profound impact on mathematics. Aryabhatta\'s understanding of the decimal system and place value greatly influenced mathematical development.<br>In astronomy, Aryabhatta accurately calculated the duration of a year, the Earth\'s circumference, and the positions of various celestial bodies. His insights paved the way for future astronomers and contributed to the understanding of the solar system.<br>Aryabhatta\'s legacy extends beyond his specific contributions. His works influenced Islamic and European mathematicians and astronomers, and his insights continue to be studied and revered in the field of mathematics and astronomy.<br>What is Aryabhatta\'s most notable contribution to mathematics, as mentioned in the passage?</p>",
                    options_en: ["<p>Introduction of Sanskrit language</p>", "<p>Calculation of the Earth\'s circumference</p>", 
                                "<p>Introduction of the concept of zero</p>", "<p>Discovery of celestial bodies</p>"],
                    options_hi: ["<p>Introduction of Sanskrit language</p>", "<p>Calculation of the Earth\'s circumference</p>",
                                "<p>Introduction of the concept of zero</p>", "<p>Discovery of celestial bodies</p>"],
                    solution_en: "<p>33.(c) Introduction of the concept of zero. <br>As mentioned in the passage, Aryabhatta\'s most notable contribution to mathematics is the introduction of the concept of zero. Hence, option (c) is the most appropriate answer.</p>",
                    solution_hi: "<p>33.(c) Introduction of the concept of zero. <br>जैसा कि passage में बताया गया है, आर्यभट्ट का गणित में सबसे उल्लेखनीय योगदान(notable contribution) शून्य(zero) की अवधारणा का परिचय है। अतः, option (c) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "94",
                    section: "misc",
                    question_en: "<p>34. <strong>Comprehension:</strong><br>Aryabhatta, an ancient Indian mathematician and astronomer, made significant contributions to the fields of mathematics and astronomy during the 5th century. Born in 476 CE in Kusumapura (present-day Patna, India), Aryabhatta\'s work laid the foundation for many mathematical concepts that are still relevant today.<br>One of Aryabhatta\'s most notable achievements was his compilation of the Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit. In this work, he introduced the concept of zero, a revolutionary idea that had a profound impact on mathematics. Aryabhatta\'s understanding of the decimal system and place value greatly influenced mathematical development.<br>In astronomy, Aryabhatta accurately calculated the duration of a year, the Earth\'s circumference, and the positions of various celestial bodies. His insights paved the way for future astronomers and contributed to the understanding of the solar system.<br>Aryabhatta\'s legacy extends beyond his specific contributions. His works influenced Islamic and European mathematicians and astronomers, and his insights continue to be studied and revered in the field of mathematics and astronomy.<br>What was Aryabhatta\'s major work in mathematics mentioned in the passage?</p>",
                    question_hi: "<p>34. <strong>Comprehension:</strong><br>Aryabhatta, an ancient Indian mathematician and astronomer, made significant contributions to the fields of mathematics and astronomy during the 5th century. Born in 476 CE in Kusumapura (present-day Patna, India), Aryabhatta\'s work laid the foundation for many mathematical concepts that are still relevant today.<br>One of Aryabhatta\'s most notable achievements was his compilation of the Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit. In this work, he introduced the concept of zero, a revolutionary idea that had a profound impact on mathematics. Aryabhatta\'s understanding of the decimal system and place value greatly influenced mathematical development.<br>In astronomy, Aryabhatta accurately calculated the duration of a year, the Earth\'s circumference, and the positions of various celestial bodies. His insights paved the way for future astronomers and contributed to the understanding of the solar system.<br>Aryabhatta\'s legacy extends beyond his specific contributions. His works influenced Islamic and European mathematicians and astronomers, and his insights continue to be studied and revered in the field of mathematics and astronomy.<br>What was Aryabhatta\'s major work in mathematics mentioned in the passage?</p>",
                    options_en: ["<p>Calculation of the Earth\'s circumference</p>", "<p>Discovery of celestial bodies</p>", 
                                "<p>Introduction of the decimal system</p>", "<p>Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit</p>"],
                    options_hi: ["<p>Calculation of the Earth\'s circumference</p>", "<p>Discovery of celestial bodies</p>",
                                "<p>Introduction of the decimal system</p>", "<p>Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit </p>"],
                    solution_en: "<p>34.(d) Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit.<br>(Line/s from the passage - One of Aryabhatta\'s most notable achievements was his compilation of the Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit.)</p>",
                    solution_hi: "<p>34.(d) Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit.<br>( Passage से ली गयी line/s - One of Aryabhatta\'s most notable achievements was his compilation of the Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit. / आर्यभट्ट की सबसे उल्लेखनीय उपलब्धियों(notable achievements) में से एक आर्यभटीय(Aryabhatiya) का संकलन था, जो संस्कृत में लिखा गया एक गणितीय एवं खगोलीय(astronomical) ग्रंथ है।)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "95",
                    section: "misc",
                    question_en: "<p>35. <strong>Comprehension:</strong><br>Aryabhatta, an ancient Indian mathematician and astronomer, made significant contributions to the fields of mathematics and astronomy during the 5th century. Born in 476 CE in Kusumapura (present-day Patna, India), Aryabhatta\'s work laid the foundation for many mathematical concepts that are still relevant today.<br>One of Aryabhatta\'s most notable achievements was his compilation of the Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit. In this work, he introduced the concept of zero, a revolutionary idea that had a profound impact on mathematics. Aryabhatta\'s understanding of the decimal system and place value greatly influenced mathematical development.<br>In astronomy, Aryabhatta accurately calculated the duration of a year, the Earth\'s circumference, and the positions of various celestial bodies. His insights paved the way for future astronomers and contributed to the understanding of the solar system.<br>Aryabhatta\'s legacy extends beyond his specific contributions. His works influenced Islamic and European mathematicians and astronomers, and his insights continue to be studied and revered in the field of mathematics and astronomy.<br>What is Aryabhatta\'s significant contribution to astronomy mentioned in the passage?</p>",
                    question_hi: "<p>35. <strong>Comprehension:</strong><br>Aryabhatta, an ancient Indian mathematician and astronomer, made significant contributions to the fields of mathematics and astronomy during the 5th century. Born in 476 CE in Kusumapura (present-day Patna, India), Aryabhatta\'s work laid the foundation for many mathematical concepts that are still relevant today.<br>One of Aryabhatta\'s most notable achievements was his compilation of the Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit. In this work, he introduced the concept of zero, a revolutionary idea that had a profound impact on mathematics. Aryabhatta\'s understanding of the decimal system and place value greatly influenced mathematical development.<br>In astronomy, Aryabhatta accurately calculated the duration of a year, the Earth\'s circumference, and the positions of various celestial bodies. His insights paved the way for future astronomers and contributed to the understanding of the solar system.<br>Aryabhatta\'s legacy extends beyond his specific contributions. His works influenced Islamic and European mathematicians and astronomers, and his insights continue to be studied and revered in the field of mathematics and astronomy.<br>What is Aryabhatta\'s significant contribution to astronomy mentioned in the passage?</p>",
                    options_en: ["<p>Introduction of the decimal system</p>", "<p>Calculation of the Earth\'s circumference</p>", 
                                "<p>Accurate calculation of the duration of a year and positions of celestial bodies</p>", "<p>Discovery of the solar system</p>"],
                    options_hi: ["<p>Introduction of the decimal system</p>", "<p>Calculation of the Earth\'s circumference</p>",
                                "<p>Accurate calculation of the duration of a year and positions of celestial bodies</p>", "<p>Discovery of the solar system</p>"],
                    solution_en: "<p>35.(c) Accurate calculation of the duration of a year and positions of celestial bodies. <br>As highlighted in the passage, Aryabhatta\'s significant contribution to astronomy includes the accurate calculation of the duration of a year and the positions of celestial bodies.</p>",
                    solution_hi: "<p>35.(c) Accurate calculation of the duration of a year and positions of celestial bodies. <br>जैसा कि passage में रेखांकित किया गया है, खगोल विज्ञान(astronomy) में आर्यभट्ट के महत्वपूर्ण योगदान(significant contribution) में एक वर्ष की अवधि और आकाशीय पिंडों(celestial bodies) की स्थिति की सटीक गणना शामिल है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "96",
                    section: "misc",
                    question_en: "<p>36. <strong>Comprehension:</strong><br>Aryabhatta, an ancient Indian mathematician and astronomer, made significant contributions to the fields of mathematics and astronomy during the 5th century. Born in 476 CE in Kusumapura (present-day Patna, India), Aryabhatta\'s work laid the foundation for many mathematical concepts that are still relevant today.<br>One of Aryabhatta\'s most notable achievements was his compilation of the Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit. In this work, he introduced the concept of zero, a revolutionary idea that had a profound impact on mathematics. Aryabhatta\'s understanding of the decimal system and place value greatly influenced mathematical development.<br>In astronomy, Aryabhatta accurately calculated the duration of a year, the Earth\'s circumference, and the positions of various celestial bodies. His insights paved the way for future astronomers and contributed to the understanding of the solar system.<br>Aryabhatta\'s legacy extends beyond his specific contributions. His works influenced Islamic and European mathematicians and astronomers, and his insights continue to be studied and revered in the field of mathematics and astronomy.<br>Select the correct meaning for the phrase &lsquo;paved the way&rsquo;</p>",
                    question_hi: "<p>36. <strong>Comprehension:</strong><br>Aryabhatta, an ancient Indian mathematician and astronomer, made significant contributions to the fields of mathematics and astronomy during the 5th century. Born in 476 CE in Kusumapura (present-day Patna, India), Aryabhatta\'s work laid the foundation for many mathematical concepts that are still relevant today.<br>One of Aryabhatta\'s most notable achievements was his compilation of the Aryabhatiya, a mathematical and astronomical treatise written in Sanskrit. In this work, he introduced the concept of zero, a revolutionary idea that had a profound impact on mathematics. Aryabhatta\'s understanding of the decimal system and place value greatly influenced mathematical development.<br>In astronomy, Aryabhatta accurately calculated the duration of a year, the Earth\'s circumference, and the positions of various celestial bodies. His insights paved the way for future astronomers and contributed to the understanding of the solar system.<br>Aryabhatta\'s legacy extends beyond his specific contributions. His works influenced Islamic and European mathematicians and astronomers, and his insights continue to be studied and revered in the field of mathematics and astronomy.<br>Select the correct meaning for the phrase &lsquo;paved the way&rsquo;</p>",
                    options_en: ["<p>to be a pioneer in a particular activity</p>", "<p>willing to consider something</p>", 
                                "<p>making a judgment</p>", "<p>to make it easier or possible for someone to follow</p>"],
                    options_hi: ["<p>to be a pioneer in a particular activity</p>", "<p>willing to consider something</p>",
                                "<p>making a judgment</p>", "<p>to make it easier or possible for someone to follow</p>"],
                    solution_en: "<p>36.(a) Paved the way- to make it possible or easier for someone to follow. <br>E.g.- The partnership will pave the way for more performances by world-class dancers in the town.</p>",
                    solution_hi: "<p>36.(a) Paved the way- to make it possible or easier for someone to follow / किसी के लिए अनुसरण करना संभव या आसान बनाना<br>उदाहरण - The partnership will pave the way for more performances by world-class dancers in the town.</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "97",
                    section: "misc",
                    question_en: "<p>37. <strong>Comprehension:</strong><br>Mental health is an integral aspect of overall well-being, encompassing emotional, psychological, and social factors. It is not merely the absence of mental disorders but the presence of positive mental attributes. Maintaining good mental health contributes to one\'s ability to cope with stress, maintain fulfilling relationships, work productively and make informed decisions.<br>Promoting mental health involves fostering resilience and creating environments that support well-being. Strategies include developing coping skills, nurturing a strong social support system, and seeking professional help when needed. However, mental health challenges persist globally, often due to stigma, lack of awareness, and limited access to mental health services.<br>Efforts to address mental health issues require collaboration across sectors, including healthcare, education, and community support. Mental health education plays a crucial role in raising awareness, reducing stigma, and empowering individuals to prioritize their mental well-being.<br>While progress has been made in recognizing the importance of mental health, ongoing advocacy is essential for breaking down barriers and ensuring that mental health is prioritized at individual, societal, and policy levels.<br>What does mental health encompass, as mentioned in the passage?</p>",
                    question_hi: "<p>37.<strong> Comprehension:</strong><br>Mental health is an integral aspect of overall well-being, encompassing emotional, psychological, and social factors. It is not merely the absence of mental disorders but the presence of positive mental attributes. Maintaining good mental health contributes to one\'s ability to cope with stress, maintain fulfilling relationships, work productively and make informed decisions.<br>Promoting mental health involves fostering resilience and creating environments that support well-being. Strategies include developing coping skills, nurturing a strong social support system, and seeking professional help when needed. However, mental health challenges persist globally, often due to stigma, lack of awareness, and limited access to mental health services.<br>Efforts to address mental health issues require collaboration across sectors, including healthcare, education, and community support. Mental health education plays a crucial role in raising awareness, reducing stigma, and empowering individuals to prioritize their mental well-being.<br>While progress has been made in recognizing the importance of mental health, ongoing advocacy is essential for breaking down barriers and ensuring that mental health is prioritized at individual, societal, and policy levels.<br>What does mental health encompass, as mentioned in the passage?</p>",
                    options_en: ["<p>Only the absence of mental disorders</p>", "<p>Presence of positive mental attributes</p>", 
                                "<p>Physical well-being</p>", "<p>Coping with stress</p>"],
                    options_hi: ["<p>Only the absence of mental disorders</p>", "<p>Presence of positive mental attributes</p>",
                                "<p>Physical well-being</p>", "<p>Coping with stress</p>"],
                    solution_en: "<p>37.(b) Presence of positive mental attributes. <br>As stated in the passage, mental health encompasses more than just the absence of mental disorders. It includes the presence of positive mental attributes, contributing to overall well-being by fostering emotional, psychological, and social factors.</p>",
                    solution_hi: "<p>37.(b) Presence of positive mental attributes. <br>जैसा कि passage में कहा गया है, मानसिक स्वास्थ्य में मानसिक विकारों(mental disorders) की अनुपस्थिति से कहीं अधिक बातें शामिल हैं। इसमें सकारात्मक मानसिक विशेषताओं(positive mental attributes) की उपस्थिति शामिल है, जो भावनात्मक(emotional), psychological(मनोवैज्ञानिक), एवं सामाजिक कारकों(social factors) को बढ़ावा देकर समग्र कल्याण(overall well-being) में योगदान करती है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "98",
                    section: "misc",
                    question_en: "<p>38. <strong>Comprehension:</strong><br>Mental health is an integral aspect of overall well-being, encompassing emotional, psychological, and social factors. It is not merely the absence of mental disorders but the presence of positive mental attributes. Maintaining good mental health contributes to one\'s ability to cope with stress, maintain fulfilling relationships, work productively and make informed decisions.<br>Promoting mental health involves fostering resilience and creating environments that support well-being. Strategies include developing coping skills, nurturing a strong social support system, and seeking professional help when needed. However, mental health challenges persist globally, often due to stigma, lack of awareness, and limited access to mental health services.<br>Efforts to address mental health issues require collaboration across sectors, including healthcare, education, and community support. Mental health education plays a crucial role in raising awareness, reducing stigma, and empowering individuals to prioritize their mental well-being.<br>While progress has been made in recognizing the importance of mental health, ongoing advocacy is essential for breaking down barriers and ensuring that mental health is prioritized at individual, societal, and policy levels.<br>What is the main focus of the passage?</p>",
                    question_hi: "<p>38. <strong>Comprehension:</strong><br>Mental health is an integral aspect of overall well-being, encompassing emotional, psychological, and social factors. It is not merely the absence of mental disorders but the presence of positive mental attributes. Maintaining good mental health contributes to one\'s ability to cope with stress, maintain fulfilling relationships, work productively and make informed decisions.<br>Promoting mental health involves fostering resilience and creating environments that support well-being. Strategies include developing coping skills, nurturing a strong social support system, and seeking professional help when needed. However, mental health challenges persist globally, often due to stigma, lack of awareness, and limited access to mental health services.<br>Efforts to address mental health issues require collaboration across sectors, including healthcare, education, and community support. Mental health education plays a crucial role in raising awareness, reducing stigma, and empowering individuals to prioritize their mental well-being.<br>While progress has been made in recognizing the importance of mental health, ongoing advocacy is essential for breaking down barriers and ensuring that mental health is prioritized at individual, societal, and policy levels.<br>What is the main focus of the passage?</p>",
                    options_en: ["<p>The absence of mental disorders</p>", "<p>Positive mental attributes</p>", 
                                "<p>Challenges in global mental health</p>", "<p>Strategies and collaboration for promoting mental health</p>"],
                    options_hi: ["<p>The absence of mental disorders</p>", "<p>Positive mental attributes</p>",
                                "<p>Challenges in global mental health</p>", "<p>Strategies and collaboration for promoting mental health </p>"],
                    solution_en: "<p>38.(d) Strategies and collaboration for promoting mental health. <br>It can be inferred that the main focus of the passage is on the Strategies and collaboration for promoting mental health.</p>",
                    solution_hi: "<p>38.(d) Strategies and collaboration for promoting mental health. <br>दिए गए से passage यह अनुमान लगाया जा सकता है कि passage का मुख्य focus मानसिक स्वास्थ्य(mental health) को बढ़ावा देने के लिए रणनीतियों और सहयोग पर है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "99",
                    section: "misc",
                    question_en: "<p>39.<strong> Comprehension:</strong><br>Mental health is an integral aspect of overall well-being, encompassing emotional, psychological, and social factors. It is not merely the absence of mental disorders but the presence of positive mental attributes. Maintaining good mental health contributes to one\'s ability to cope with stress, maintain fulfilling relationships, work productively and make informed decisions.<br>Promoting mental health involves fostering resilience and creating environments that support well-being. Strategies include developing coping skills, nurturing a strong social support system, and seeking professional help when needed. However, mental health challenges persist globally, often due to stigma, lack of awareness, and limited access to mental health services.<br>Efforts to address mental health issues require collaboration across sectors, including healthcare, education, and community support. Mental health education plays a crucial role in raising awareness, reducing stigma, and empowering individuals to prioritize their mental well-being.<br>While progress has been made in recognizing the importance of mental health, ongoing advocacy is essential for breaking down barriers and ensuring that mental health is prioritized at individual, societal, and policy levels.<br>What challenges persist in addressing mental health globally, according to the passage?</p>",
                    question_hi: "<p>39. <strong>Comprehension:</strong><br>Mental health is an integral aspect of overall well-being, encompassing emotional, psychological, and social factors. It is not merely the absence of mental disorders but the presence of positive mental attributes. Maintaining good mental health contributes to one\'s ability to cope with stress, maintain fulfilling relationships, work productively and make informed decisions.<br>Promoting mental health involves fostering resilience and creating environments that support well-being. Strategies include developing coping skills, nurturing a strong social support system, and seeking professional help when needed. However, mental health challenges persist globally, often due to stigma, lack of awareness, and limited access to mental health services.<br>Efforts to address mental health issues require collaboration across sectors, including healthcare, education, and community support. Mental health education plays a crucial role in raising awareness, reducing stigma, and empowering individuals to prioritize their mental well-being.<br>While progress has been made in recognizing the importance of mental health, ongoing advocacy is essential for breaking down barriers and ensuring that mental health is prioritized at individual, societal, and policy levels.<br>What challenges persist in addressing mental health globally, according to the passage?</p>",
                    options_en: ["<p>Lack of mental health education</p>", "<p>Limited access to mental health services</p>", 
                                "<p>Stigma, lack of awareness, and limited access to mental health services</p>", "<p>Presence of positive mental attributes</p>"],
                    options_hi: ["<p>Lack of mental health education</p>", "<p>Limited access to mental health services</p>",
                                "<p>Stigma, lack of awareness, and limited access to mental health services</p>", "<p>Presence of positive mental attributes</p>"],
                    solution_en: "<p>39.(c) Stigma, lack of awareness, and limited access to mental health services. <br>(Line/s from the passage - However, mental health challenges persist globally, due to stigma, lack of awareness, and limited access to mental health services.)</p>",
                    solution_hi: "<p>39.(c) Stigma, lack of awareness, and limited access to mental health services. <br>(Passage से ली गयी line/s - However, mental health challenges persist globally, due to stigma, lack of awareness and limited access to mental health services. / हालाँकि, निंदा(Stigma), जागरूकता की कमी और मानसिक स्वास्थ्य सेवाओं तक सीमित पहुँच के कारण मानसिक स्वास्थ्य चुनौतियाँ वैश्विक स्तर पर बनी हुई हैं।)</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>40. <strong>Comprehension:</strong><br>Mental health is an integral aspect of overall well-being, encompassing emotional, psychological, and social factors. It is not merely the absence of mental disorders but the presence of positive mental attributes. Maintaining good mental health contributes to one\'s ability to cope with stress, maintain fulfilling relationships, work productively and make informed decisions.<br>Promoting mental health involves fostering resilience and creating environments that support well-being. Strategies include developing coping skills, nurturing a strong social support system, and seeking professional help when needed. However, mental health challenges persist globally, often due to stigma, lack of awareness, and limited access to mental health services.<br>Efforts to address mental health issues require collaboration across sectors, including healthcare, education, and community support. Mental health education plays a crucial role in raising awareness, reducing stigma, and empowering individuals to prioritize their mental well-being.<br>While progress has been made in recognizing the importance of mental health, ongoing advocacy is essential for breaking down barriers and ensuring that mental health is prioritized at individual, societal, and policy levels.<br>What sectors need to collaborate to address mental health issues, as mentioned in the passage?</p>",
                    question_hi: "<p>40. <strong>Comprehension:</strong><br>Mental health is an integral aspect of overall well-being, encompassing emotional, psychological, and social factors. It is not merely the absence of mental disorders but the presence of positive mental attributes. Maintaining good mental health contributes to one\'s ability to cope with stress, maintain fulfilling relationships, work productively and make informed decisions.<br>Promoting mental health involves fostering resilience and creating environments that support well-being. Strategies include developing coping skills, nurturing a strong social support system, and seeking professional help when needed. However, mental health challenges persist globally, often due to stigma, lack of awareness, and limited access to mental health services.<br>Efforts to address mental health issues require collaboration across sectors, including healthcare, education, and community support. Mental health education plays a crucial role in raising awareness, reducing stigma, and empowering individuals to prioritize their mental well-being.<br>While progress has been made in recognizing the importance of mental health, ongoing advocacy is essential for breaking down barriers and ensuring that mental health is prioritized at individual, societal, and policy levels.<br>What sectors need to collaborate to address mental health issues, as mentioned in the passage?</p>",
                    options_en: ["<p>Only healthcare</p>", "<p>Only education</p>", 
                                "<p>Only community support</p>", "<p>Healthcare, education, and community support</p>"],
                    options_hi: ["<p>Only healthcare</p>", "<p>Only education</p>",
                                "<p>Only community support</p>", "<p>Healthcare, education, and community support </p>"],
                    solution_en: "<p>40.(d) Healthcare, education, and community support<br>As highlighted in the passage, mental health issues require collaboration across multiple sectors.This includes not only the healthcare sector but also education and community support. Hence, option (d) is the most appropriate answer.</p>",
                    solution_hi: "<p>40.(d) Healthcare, education, and community support<br>जैसा कि passage में रेखांकित किया गया है, मानसिक स्वास्थ्य(mental health) के मुद्दों के लिए कई क्षेत्रों में सहयोग की आवश्यकता होती है। इसमें न केवल स्वास्थ्य सेवा क्षेत्र बल्कि शिक्षा और सामुदायिक सहायता(community support) भी शामिल है। अतः, option (d) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "101",
                    section: "misc",
                    question_en: "<p>41. <strong>Comprehension:</strong><br>Ensuring access to education for girls is a critical step toward achieving gender equality and fostering societal development. Despite progress, many girls around the world still face barriers that hinder their education. These barriers include socio-economic factors, cultural norms, and inadequate infrastructure.<br>Educating girls has far-reaching benefits, positively impacting not only individual lives but entire communities. Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. When girls receive an education, they are more likely to make informed choices about their lives, contribute to their communities, and break the cycle of poverty.<br>To overcome barriers to girl education, concerted efforts are needed, including policy changes, community engagement, and targeted interventions. Providing a safe and supportive environment for girls to learn is crucial. Additionally, addressing societal attitudes that perpetuate gender stereotypes and discrimination is vital for creating an inclusive education system.<br>Empowering girls through education is an investment in the future, as educated girls become empowered women who contribute significantly to building a more equitable and prosperous society.<br>Select the most appropriate title for the given passage.</p>",
                    question_hi: "<p>41. <strong>Comprehension:</strong><br>Ensuring access to education for girls is a critical step toward achieving gender equality and fostering societal development. Despite progress, many girls around the world still face barriers that hinder their education. These barriers include socio-economic factors, cultural norms, and inadequate infrastructure.<br>Educating girls has far-reaching benefits, positively impacting not only individual lives but entire communities. Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. When girls receive an education, they are more likely to make informed choices about their lives, contribute to their communities, and break the cycle of poverty.<br>To overcome barriers to girl education, concerted efforts are needed, including policy changes, community engagement, and targeted interventions. Providing a safe and supportive environment for girls to learn is crucial. Additionally, addressing societal attitudes that perpetuate gender stereotypes and discrimination is vital for creating an inclusive education system.<br>Empowering girls through education is an investment in the future, as educated girls become empowered women who contribute significantly to building a more equitable and prosperous society.<br>Select the most appropriate title for the given passage.</p>",
                    options_en: ["<p>Economic Growth Through Education</p>", "<p>Challenges Faced by Girls in Society</p>", 
                                "<p>Empowering Societal Development Through Girl Education</p>", "<p>Cultural Norms and Their Impact on Education</p>"],
                    options_hi: ["<p>Economic Growth Through Education</p>", "<p>Challenges Faced by Girls in Society</p>",
                                "<p>Empowering Societal Development Through Girl Education</p>", "<p>Cultural Norms and Their Impact on Education</p>"],
                    solution_en: "<p>41.(c) Empowering Societal Development Through Girl Education.<br>Most appropriate title for the given passage is \"Empowering Societal Development Through Girl Education\". Educating girls impacts not only individual lives but also builds a more equitable and prosperous society.</p>",
                    solution_hi: "<p>41.(c) Empowering Societal Development Through Girl Education.<br>दिए गए passage के लिए सबसे उपयुक्त title \"Empowering Societal Development Through Girl Education\" है। लड़कियों को शिक्षित करने से न केवल व्यक्तिगत जीवन(individual lives) पर प्रभाव पड़ता है बल्कि एक अधिक न्यायसंगत(equitable) और समृद्ध समाज(prosperous society) का निर्माण भी होता है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "102",
                    section: "misc",
                    question_en: "<p>42. <strong>Comprehension:</strong><br>Ensuring access to education for girls is a critical step toward achieving gender equality and fostering societal development. Despite progress, many girls around the world still face barriers that hinder their education. These barriers include socio-economic factors, cultural norms, and inadequate infrastructure.<br>Educating girls has far-reaching benefits, positively impacting not only individual lives but entire communities. Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. When girls receive an education, they are more likely to make informed choices about their lives, contribute to their communities, and break the cycle of poverty.<br>To overcome barriers to girl education, concerted efforts are needed, including policy changes, community engagement, and targeted interventions. Providing a safe and supportive environment for girls to learn is crucial. Additionally, addressing societal attitudes that perpetuate gender stereotypes and discrimination is vital for creating an inclusive education system.<br>Empowering girls through education is an investment in the future, as educated girls become empowered women who contribute significantly to building a more equitable and prosperous society.<br>Which of the following statements is true, based on the passage?</p>",
                    question_hi: "<p>42. <strong>Comprehension:</strong><br>Ensuring access to education for girls is a critical step toward achieving gender equality and fostering societal development. Despite progress, many girls around the world still face barriers that hinder their education. These barriers include socio-economic factors, cultural norms, and inadequate infrastructure.<br>Educating girls has far-reaching benefits, positively impacting not only individual lives but entire communities. Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. When girls receive an education, they are more likely to make informed choices about their lives, contribute to their communities, and break the cycle of poverty.<br>To overcome barriers to girl education, concerted efforts are needed, including policy changes, community engagement, and targeted interventions. Providing a safe and supportive environment for girls to learn is crucial. Additionally, addressing societal attitudes that perpetuate gender stereotypes and discrimination is vital for creating an inclusive education system.<br>Empowering girls through education is an investment in the future, as educated girls become empowered women who contribute significantly to building a more equitable and prosperous society.<br>Which of the following statements is true, based on the passage?</p>",
                    options_en: ["<p>Educating girls only impacts individual lives.</p>", "<p>Socio-economic factors do not pose barriers to girls\' education.</p>", 
                                "<p>Educated girls are less likely to make informed choices.</p>", "<p>Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates.</p>"],
                    options_hi: ["<p>Educating girls only impacts individual lives.</p>", "<p>Socio-economic factors do not pose barriers to girls\' education.</p>",
                                "<p>Educated girls are less likely to make informed choices.</p>", "<p>Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates.</p>"],
                    solution_en: "<p>42.(d) Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. <br>(Line/s from the passage - Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates.)</p>",
                    solution_hi: "<p>42.(d) Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. <br>(Passage से ली गयी line/s - Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. / लड़कियों की शिक्षा, बेहतर स्वास्थ्य परिणामों, आर्थिक विकास और गरीबी दर में कमी से जुड़ी है।)</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "103",
                    section: "misc",
                    question_en: "<p>43. <strong>Comprehension:</strong><br>Ensuring access to education for girls is a critical step toward achieving gender equality and fostering societal development. Despite progress, many girls around the world still face barriers that hinder their education. These barriers include socio-economic factors, cultural norms, and inadequate infrastructure.<br>Educating girls has far-reaching benefits, positively impacting not only individual lives but entire communities. Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. When girls receive an education, they are more likely to make informed choices about their lives, contribute to their communities, and break the cycle of poverty.<br>To overcome barriers to girl education, concerted efforts are needed, including policy changes, community engagement, and targeted interventions. Providing a safe and supportive environment for girls to learn is crucial. Additionally, addressing societal attitudes that perpetuate gender stereotypes and discrimination is vital for creating an inclusive education system.<br>Empowering girls through education is an investment in the future, as educated girls become empowered women who contribute significantly to building a more equitable and prosperous society.<br>Select the correct synonym for the word &lsquo;perpetuate&rsquo;</p>",
                    question_hi: "<p>43.<strong> Comprehension:</strong><br>Ensuring access to education for girls is a critical step toward achieving gender equality and fostering societal development. Despite progress, many girls around the world still face barriers that hinder their education. These barriers include socio-economic factors, cultural norms, and inadequate infrastructure.<br>Educating girls has far-reaching benefits, positively impacting not only individual lives but entire communities. Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. When girls receive an education, they are more likely to make informed choices about their lives, contribute to their communities, and break the cycle of poverty.<br>To overcome barriers to girl education, concerted efforts are needed, including policy changes, community engagement, and targeted interventions. Providing a safe and supportive environment for girls to learn is crucial. Additionally, addressing societal attitudes that perpetuate gender stereotypes and discrimination is vital for creating an inclusive education system.<br>Empowering girls through education is an investment in the future, as educated girls become empowered women who contribute significantly to building a more equitable and prosperous society.<br>Select the correct synonym for the word &lsquo;perpetuate&rsquo;</p>",
                    options_en: ["<p>Terminate</p>", "<p>Perpetrate</p>", 
                                "<p>Reveal</p>", "<p>Continue</p>"],
                    options_hi: ["<p>Terminate</p>", "<p>Perpetrate</p>",
                                "<p>Reveal</p>", "<p>Continue</p>"],
                    solution_en: "<p>43.(d) <strong>Continue</strong>- to keep going. <br><strong>Perpetuate</strong>- to cause something to continue.<br><strong>Terminate</strong>- to bring to an end or conclude.<br><strong>Perpetrate</strong>- to commit or carry out (a harmful, illegal, or immoral action).<br><strong>Reveal</strong>- to make something known or disclose information.</p>",
                    solution_hi: "<p>43.(d) <strong>Continue </strong>(जारी रखना) - to keep going. <br><strong>Perpetuate </strong>(स्थिर रखना) - to cause something to continue.<br><strong>Terminate </strong>(समाप्त करना) - to bring to an end or conclude.<br><strong>Perpetrate </strong>(अपराध करना) - to commit or carry out (a harmful, illegal, or immoral action).<br><strong>Reveal </strong>(प्रकट करना) - to make something known or disclose information.</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "104",
                    section: "misc",
                    question_en: "<p>44.<strong> Comprehension:</strong><br>Ensuring access to education for girls is a critical step toward achieving gender equality and fostering societal development. Despite progress, many girls around the world still face barriers that hinder their education. These barriers include socio-economic factors, cultural norms, and inadequate infrastructure.<br>Educating girls has far-reaching benefits, positively impacting not only individual lives but entire communities. Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. When girls receive an education, they are more likely to make informed choices about their lives, contribute to their communities, and break the cycle of poverty.<br>To overcome barriers to girl education, concerted efforts are needed, including policy changes, community engagement, and targeted interventions. Providing a safe and supportive environment for girls to learn is crucial. Additionally, addressing societal attitudes that perpetuate gender stereotypes and discrimination is vital for creating an inclusive education system.<br>Empowering girls through education is an investment in the future, as educated girls become empowered women who contribute significantly to building a more equitable and prosperous society.<br>What is the overarching goal of providing a safe and supportive environment for girls to learn, according to the passage?</p>",
                    question_hi: "<p>44. <strong>Comprehension:</strong><br>Ensuring access to education for girls is a critical step toward achieving gender equality and fostering societal development. Despite progress, many girls around the world still face barriers that hinder their education. These barriers include socio-economic factors, cultural norms, and inadequate infrastructure.<br>Educating girls has far-reaching benefits, positively impacting not only individual lives but entire communities. Girls\' education is linked to improved health outcomes, economic growth, and reduced poverty rates. When girls receive an education, they are more likely to make informed choices about their lives, contribute to their communities, and break the cycle of poverty.<br>To overcome barriers to girl education, concerted efforts are needed, including policy changes, community engagement, and targeted interventions. Providing a safe and supportive environment for girls to learn is crucial. Additionally, addressing societal attitudes that perpetuate gender stereotypes and discrimination is vital for creating an inclusive education system.<br>Empowering girls through education is an investment in the future, as educated girls become empowered women who contribute significantly to building a more equitable and prosperous society.<br>What is the overarching goal of providing a safe and supportive environment for girls to learn, according to the passage?</p>",
                    options_en: ["<p>Encouraging perpetuation of gender stereotypes</p>", "<p>Reducing access to educational resources</p>", 
                                "<p>Hindering academic progress</p>", "<p>Empowering girls and fostering their academic success</p>"],
                    options_hi: ["<p>Encouraging perpetuation of gender stereotypes</p>", "<p>Reducing access to educational resources</p>",
                                "<p>Hindering academic progress</p>", "<p>Empowering girls and fostering their academic success</p>"],
                    solution_en: "<p>44.(d) Empowering girls and fostering their academic success. <br>\"Empower girls\" and \"foster their academic success\", is the primary goal for providing a safe and supportive environment for girls to learn. Hence, option (d) is the most appropriate answer.</p>",
                    solution_hi: "<p>44.(d) Empowering girls and fostering their academic success. <br>\"Empower girls\" और \"foster their academic success\", लड़कियों को सीखने के लिए एक सुरक्षित और सहायक वातावरण प्रदान करना प्राथमिक लक्ष्य है। अतः, option (d) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "105",
                    section: "misc",
                    question_en: "45. Select the correct homonym from the given options to fill in the blank.<br />Use the ______ to stop the truck or it will kill the dog",
                    question_hi: "45. Select the correct homonym from the given options to fill in the blank.<br />Use the ______ to stop the truck or it will kill the dog",
                    options_en: [" brace", " brake ", 
                                " break", " brak"],
                    options_hi: [" brace", " brake ",
                                " break", " brak "],
                    solution_en: "45.(b) brake - A device for slowing or stopping a vehicle, break - Separate suddenly or violently into two or more pieces.",
                    solution_hi: "45.(b) brake -(रोक) A device for slowing or stopping a vehicle, break - (तोड़ना) Separate suddenly or violently into two or more pieces.",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "106",
                    section: "misc",
                    question_en: "46. Which of the following statement is incorrect about the “Vivad se Vishwas II” Scheme?",
                    question_hi: "46.“विवाद से विश्वास II” योजना के बारे में निम्नलिखित में से कौन सा कथन गलत है?",
                    options_en: [" The Department of Consumer Affairs has launched this scheme.", " Its aim is to settle the pending contractual disputes of government and government undertakings.", 
                                " The scheme was announced in the Union Budget 2023-24.", " The scheme was announced by the Union Finance Minister."],
                    options_hi: [" उपभोक्ता मामलों के विभाग ने यह योजना शुरू की है।", " इसका उद्देश्य सरकार और सरकारी उपक्रमों के लंबित संविदात्मक विवादों का निपटारा करना है।",
                                " इस योजना की घोषणा केंद्रीय बजट 2023-24 में की गई थी।", " इस योजना की घोषणा केंद्रीय वित्त मंत्री ने की थी।"],
                    solution_en: "46.(a) “Vivad se Vishwas II” Scheme launched by the Department of Expenditure, Ministry of Finance. It will apply to all domestic contractual disputes where one of the parties is either the Government of India or an organization working under its control. ",
                    solution_hi: "46.(a) “विवाद से विश्वास II\'\' योजना वित्त मंत्रालय के व्यय विभाग द्वारा शुरू की गई। यह उन सभी घरेलू संविदात्मक विवादों पर लागू होगी, जिनमें से एक पक्ष भारत सरकार या उसके नियंत्रण में काम करने वाला कोई संगठन है।",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "107",
                    section: "misc",
                    question_en: "<p>47. Which of the following reactions is an endothermic reaction?</p>",
                    question_hi: "<p>47. निम्नलिखित में से कौन सी अभिक्रिया ऊष्माशोषी अभिक्रिया है?</p>",
                    options_en: ["<p>Burning of coal</p>", "<p>Decomposition of vegetable matter into compost</p>", 
                                "<p>Process of respiration</p>", "<p>Decomposition of calcium carbonate to form quick lime and carbon dioxide.</p>"],
                    options_hi: ["<p>कोयले का जलना</p>", "<p>वनस्पति पदार्थ का खाद में अपघटन</p>",
                                "<p>श्वसन की प्रक्रिया</p>", "<p>कैल्शियम कार्बोनेट का अपघटन करके बिना बुझा हुआ चूना और कार्बन डाइऑक्साइड बनाना।</p>"],
                    solution_en: "<p>47.(d) An endothermic reaction is a chemical reaction that absorbs heat from its surroundings, resulting in a decrease in temperature. In the decomposition of calcium carbonate (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CaCO</mi><mn>3</mn></msub></math>) to form quicklime (CaO) and carbon dioxide (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CO</mi><mn>2</mn></msub></math>), heat energy is absorbed from the surroundings to break the bonds within the calcium carbonate molecule, making it an endothermic reaction.</p>",
                    solution_hi: "<p>47.(d) ऊष्माशोषी अभिक्रिया एक रासायनिक अभिक्रिया है जो अपने आस-पास से ऊष्मा को अवशोषित करती है, जिसके परिणामस्वरूप तापमान में कमी आती है। कैल्शियम कार्बोनेट (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CaCO</mi><mn>3</mn></msub></math>) के अपघटन से बिना बुझा हुआ चूना (CaO) और कार्बन डाइऑक्साइड (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CO</mi><mn>2</mn></msub></math>) बनता है, कैल्शियम कार्बोनेट अणु के बीच के बंध को तोड़ने के लिए आस-पास से ऊष्मा ऊर्जा को अवशोषित किया जाता है, जिससे यह एक ऊष्माशोषी अभिक्रिया बन जाती है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "108",
                    section: "misc",
                    question_en: "48. Which of the following statements about privatisation is FALSE ?",
                    question_hi: "48. निजीकरण के बारे में निम्नलिखित में से कौन-सा कथन गलत है ?",
                    options_en: [" There was an increase in the cases of disinvestment after the economic reforms of 1991 as compared to the pre-1991 period.", " Improved performance is one of the advantages of privatisation.", 
                                " Maruti Udyog Limited was a public sector enterprise before the 1991 economic reforms.", " There is no possibility of exploitation of monopoly power by private companies after privatisation. "],
                    options_hi: [" 1991 के पूर्व की अवधि की तुलना में 1991 के आर्थिक सुधारों के बाद विनिवेश के मामलों में वृद्धि हुई।", " बेहतर प्रदर्शन निजीकरण के लाभों में से एक है।",
                                " मारुति उद्योग लिमिटेड 1991 के आर्थिक सुधारों से पहले एक सार्वजनिक क्षेत्र का उद्यम था।", " निजीकरण के बाद निजी कंपनियों द्वारा एकाधिकार शक्ति के दोहन की कोई संभावना नहीं है।"],
                    solution_en: "48.(d) After privatisation, there is a possibility that private companies could exploit monopoly power if they dominate the market without sufficient competition or regulation. So privatisation with competition can be a big win for policy makers. Privatisation : It implies shedding of the ownership or management of a government owned enterprise. ",
                    solution_hi: "48.(d) निजीकरण के बाद, ऐसी संभावना है कि निजी कम्पनियां पर्याप्त प्रतिस्पर्धा या विनियमन के बिना बाजार पर अपना प्रभुत्व स्थापित कर लें, तो वे अपनी एकाधिकार शक्ति का फायदा उठा सकती हैं। अत: प्रतिस्पर्धा के साथ निजीकरण नीति निर्माताओं के लिए एक बड़ी जीत हो सकती है। निजीकरण: इसका तात्पर्य सरकारी स्वामित्व वाले उद्यम के स्वामित्व या प्रबंधन को समाप्त करना है।",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "109",
                    section: "misc",
                    question_en: "<p>49. When a non-minister proposes a bill, it is called:</p>",
                    question_hi: "<p>49. जब कोई गैर मंत्री किसी विधेयक का प्रस्ताव करता है तो उसे क्या कहा जाता है ?</p>",
                    options_en: ["<p>Money Bill</p>", "<p>Government Bill</p>", 
                                "<p>Private Member&rsquo;s Bill</p>", "<p>Non-Money Bill</p>"],
                    options_hi: ["<p>धन विधेयक</p>", "<p>सरकारी विधेयक</p>",
                                "<p>निजी सदस्य विधेयक</p>", "<p>गैर-धन विधेयक</p>"],
                    solution_en: "<p>49.(c) <strong>Private Member&rsquo;s Bill: </strong>When a bill is proposed by a non-minister (a Member of Parliament who is not part of the government), it is called a Private Member\'s Bill. One month\'s notice is required before its introduction in the House. It can be introduced and discussed only on Fridays. Money Bills can only be introduced in the Lok Sabha (House of the People) and must be returned by the Rajya Sabha (Council of States) within 14 days. The Rajya Sabha can only make recommendations, which the Lok Sabha may or may not accept.</p>",
                    solution_hi: "<p>49.(c) <strong>निजी सदस्य विधेयक :</strong> जब कोई विधेयक किसी गैर-मंत्री (एक संसद सदस्य जो सरकार का हिस्सा नहीं है) द्वारा प्रस्तावित किया जाता है, तो इसे निजी सदस्य का विधेयक कहा जाता है। सदन में इसे पेश करने के लिए एक महीने पहले नोटिस देना आवश्यक है। इसे केवल शुक्रवार को ही पेश और चर्चा की जा सकती है। धन विधेयक केवल लोकसभा (लोगों का सदन) में पेश किया जा सकता है और इसे 14 दिनों के भीतर राज्यसभा (राज्यों की परिषद) द्वारा वापस किया जाना चाहिए। राज्यसभा केवल सिफारिशें कर सकती है, जिसे लोकसभा स्वीकार भी कर सकती है और नहीं भी।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "110",
                    section: "misc",
                    question_en: "<p>50. According to _______ law, heat produced by an electric current is directly proportional to the resistance of the conductor, the square of the current, and the time for which it flows.</p>",
                    question_hi: "<p>50. ______के नियम के अनुसार, किसी विद्युत धारा द्वारा उत्पन्न ऊष्मा चालक के प्रतिरोध, धारा के वर्ग और उसके प्रवाहित होने के समय के अनुक्रमानुपाती होता है।</p>",
                    options_en: ["<p>Hooke\'s</p>", "<p>Charles\'s</p>", 
                                "<p>Faraday\'s</p>", "<p>Joule\'s</p>"],
                    options_hi: ["<p>हुक</p>", "<p>चार्ल्स</p>",
                                "<p>फैराडे</p>", "<p>जूल</p>"],
                    solution_en: "<p>50.(d) <strong>Joule\'s</strong>. Joule\'s law of heating, H = I2Rt, where, H - Heat, I - Current, R - Resistance, t - time. Hooke\'s law (law of elasticity) - Discovered by the English scientist Robert Hooke in 1660, which states that, for relatively small deformations of an object, the displacement or size of the deformation is directly proportional to the deforming force or load. Faraday\'s law - The magnitude of induced emf is equal to the rate of change of flux linkages with the coil.</p>",
                    solution_hi: "<p>50.(d) <strong>जूल</strong>। जूल का तापन नियम, H = I2Rt, जहाँ, H - ऊष्मा, I - धारा, R - प्रतिरोध, t - समय। हुक का नियम (प्रत्यास्थता का नियम) - 1660 में अंग्रेज वैज्ञानिक रॉबर्ट हुक द्वारा खोजा गया, जो बताता है कि, किसी वस्तु के अपेक्षाकृत छोटे विरूपण के लिए, विरूपण का विस्थापन या आकार , विरूपण बल या भार के अनुक्रमानुपाती होता है। फैराडे का नियम - प्रेरित विद्युतवाहक बल (emf) का परिमाण कुण्डली के साथ, फ्लक्सबद्धता (flux linkages) के परिवर्तन की दर के बराबर होता है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "111",
                    section: "misc",
                    question_en: "<p>51. Banking regulation Act was passed in:</p>",
                    question_hi: "<p>51. बैंकिंग विनियमन अधिनियम कब पारित किया गया था ?</p>",
                    options_en: ["<p>1959</p>", "<p>1999</p>", 
                                "<p>1949</p>", "<p>1967</p>"],
                    options_hi: ["<p>1959</p>", "<p>1999</p>",
                                "<p>1949</p>", "<p>1967</p>"],
                    solution_en: "<p>51.(c) <strong>1949</strong>. The Banking Regulation Act, 1949 is a legislation in India that regulates all banking companies in India. It came into force on 16 March 1949 and changed to Banking Regulation Act 1949 from 1 March 1966. It is applicable in Jammu and Kashmir from 1956.</p>",
                    solution_hi: "<p>51.(c)<strong> 1949.</strong> बैंकिंग विनियमन अधिनियम, 1949 भारत का एक ऐसा कानून है जो भारत में सभी बैंकिंग कंपनियों को विनियमित करता है। यह अधिनियम 16 मार्च 1949 को लागू हुआ और 1 मार्च 1966 से बैंकिंग विनियमन अधिनियम 1949 में बदल गया। यह 1956 से जम्मू और कश्मीर में लागू है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "112",
                    section: "misc",
                    question_en: "<p>52. The Provincial elections were held for the first time in ______ under the Government of India Act, 1935.</p>",
                    question_hi: "<p>52. भारत सरकार अधिनियम, 1935 के अंतर्गत ______में पहली बार प्रांतीय चुनाव हुए थे।</p>",
                    options_en: ["<p>1935</p>", "<p>1937</p>", 
                                "<p>1936</p>", "<p>1938</p>"],
                    options_hi: ["<p>1935</p>", "<p>1937</p>",
                                "<p>1936</p>", "<p>1938</p>"],
                    solution_en: "<p>52.(b) <strong>1937. Indian provincial elections:</strong> Elections were held in eleven provinces - Madras, Central Provinces, Bihar, Orissa, the United Provinces, the Bombay Presidency, Assam, the North-West Frontier Province, Bengal, Punjab and Sind. The Indian National Congress (INC) won the majority of seats in the six provincial elections, and formed governments in eight provinces. Government of India Act, 1935 : It provided for the establishment of all India federations consisting of provinces and princely states as units.</p>",
                    solution_hi: "<p>52.(b) <strong>1937. भारतीय प्रांतीय चुनाव :</strong> ग्यारह प्रांतों में चुनाव हुए - मद्रास, मध्य प्रांत, बिहार, उड़ीसा, संयुक्त प्रांत, बॉम्बे प्रेसीडेंसी, असम, उत्तर-पश्चिम सीमांत प्रांत, बंगाल, पंजाब और सिंध। भारतीय राष्ट्रीय कांग्रेस (INC) ने छह प्रांतीय चुनावों में अधिकांश सीटें जीतीं और आठ प्रांतों में सरकार बनाई। भारत सरकार अधिनियम, 1935 : इसमें प्रांतों और रियासतों को मिलाकर सभी भारतीय संघों की स्थापना का प्रावधान किया गया।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "113",
                    section: "misc",
                    question_en: "<p>53. Match List-I with List-II.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935671.png\" alt=\"rId46\" width=\"300\" height=\"137\"></p>",
                    question_hi: "<p>53. सूची-I का सूची-II से मिलान करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642935816.png\" alt=\"rId47\" width=\"300\"></p>",
                    options_en: ["<p>A - iv, B - iii, C - ii, D - i</p>", "<p>A - iii, B - i, C - iv, D - ii.</p>", 
                                "<p>A - iii, B - i, C - ii, D - iv</p>", "<p>A - i, B - iv, C - iii, D - ii</p>"],
                    options_hi: ["<p>A - iv, B - iii, C - ii, D - i</p>", "<p>A - iii, B - i, C - iv, D - ii.</p>",
                                "<p>A - iii, B - i, C - ii, D - iv</p>", "<p>A - i, B - iv, C - iii, D - ii</p>"],
                    solution_en: "<p>53.(b) A - iii, B - i, C - iv, D - ii.</p>",
                    solution_hi: "<p>53.(b) A - iii, B - i, C - iv, D - ii.</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "114",
                    section: "misc",
                    question_en: "<p>54. Who will cause every year, annual financial statement to be laid before the Legislative Assembly of the State?</p>",
                    question_hi: "<p>54. राज्य की विधान सभा के समक्ष प्रतिवर्ष वार्षिक वित्तीय विवरण कौन प्रस्तुत करवाएगा?</p>",
                    options_en: ["<p>President</p>", "<p>Governor</p>", 
                                "<p>Deputy Chief Minister</p>", "<p>Chief Minister</p>"],
                    options_hi: ["<p>राष्ट्रपति</p>", "<p>राज्यपाल</p>",
                                "<p>उप मुख्यमंत्री</p>", "<p>मुख्यमंत्री</p>"],
                    solution_en: "<p>54.(b) <strong>Governor</strong>. Governors of States - Article 153. Money Bills: Can only be introduced in the State Legislature with the Governor&rsquo;s prior recommendation. Contingency Fund: The Governor can make advances from the State Contingency Fund for urgent expenses. Grants: No demand for grants can be made except on the Governor&rsquo;s recommendation. State Finance Commission: The Governor appoints the State Finance Commission to review financial arrangements between the State and its local bodies. Annual Financial Statement: Article 202 requires the Governor to present the Annual Financial Statement (State Budget) to the State Legislature.</p>",
                    solution_hi: "<p>54.(b) <strong>राज्यपाल</strong>। राज्यों के राज्यपाल - अनुच्छेद 153। धन विधेयक : राज्यपाल की पूर्व संस्तुति से ही राज्य विधानमंडल में पेश किए जा सकते हैं। आकस्मिकता निधि : राज्यपाल तत्काल व्यय के लिए राज्य आकस्मिकता निधि से अग्रिम राशि दे सकते हैं। अनुदान : राज्यपाल की संस्तुति के अलावा अनुदान की कोई मांग नहीं की जा सकती। राज्य वित्त आयोग : राज्यपाल राज्य और उसके स्थानीय निकायों के बीच वित्तीय व्यवस्था की समीक्षा करने के लिए राज्य वित्त आयोग की नियुक्ति करता है। वार्षिक वित्तीय विवरण: अनुच्छेद 202 के अनुसार राज्यपाल को राज्य विधानमंडल के समक्ष वार्षिक वित्तीय विवरण (राज्य बजट) प्रस्तुत करना आवश्यक है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "115",
                    section: "misc",
                    question_en: "<p>55. Choose the correct statements about the islands of India.<br>a. Lakshadweep Islands are composed of coral islands.<br>b. Andaman and Nicobar islands are composed of coral islands.<br>c. It is believed that Lakshadweep islands are an elevated portion of submarine mountains.<br>d. It is believed that Andaman and Nicobar islands are an elevated portion of submarine mountains.</p>",
                    question_hi: "<p>55. भारत के द्वीपों के संबंध में सही कथनों का चयन कीजिए।<br>a. लक्षद्वीप द्वीपसमूह प्रवाल द्वीपों से बने हैं।<br>b. अंडमान और निकोबार द्वीप समूह प्रवाल द्वीपों से बने हैं।<br>c. ऐसा माना जाता है कि लक्षद्वीप द्वीपसमूह अंतःसमुद्री पर्वतों का एक उच्चस्तरीय भाग है।<br>d. ऐसा माना जाता है कि अंडमान और निकोबार द्वीप समूह अंतःसमुद्री पर्वतों का एक उच्चस्तरीय भाग है।</p>",
                    options_en: ["<p>b and c</p>", "<p>a and b</p>", 
                                "<p>a and d</p>", "<p>c and d </p>"],
                    options_hi: ["<p>b और c</p>", "<p>a और b</p>",
                                "<p>a और d</p>", "<p>c और d</p>"],
                    solution_en: "<p>55.(c)<strong> a and d. </strong>India has two main groups of Islands. Lakshadweep Islands are located in the Arabian Sea.<br>These are coral islands located off the coast of Kerala. The Andaman and the Nicobar Islands lie to the southeast of the Indian mainland in the Bay of Bengal. India&rsquo;s only active volcano is located on Barren Island, part of the Andaman and Nicobar Islands. Important mountain peaks in Andaman and Nicobar Islands: Saddle peak (732 m, North Andaman), Mount Diavolo (Middle Andaman), and Mount Koyob (South Andaman).</p>",
                    solution_hi: "<p>55.(c) <strong>a और d </strong>। भारत में द्वीपों के दो मुख्य समूह हैं। लक्षद्वीप द्वीप अरब सागर में स्थित हैं। ये केरल के तट पर स्थित प्रवाल द्वीप हैं। अंडमान और निकोबार द्वीप समूह बंगाल की खाड़ी में भारतीय मुख्य भूमि के दक्षिण-पूर्व में स्थित हैं। भारत का एकमात्र सक्रिय ज्वालामुखी बैरन द्वीप पर स्थित है, जो अंडमान और निकोबार द्वीप समूह का हिस्सा है। अंडमान और निकोबार द्वीप समूह में महत्वपूर्ण पर्वत शिखर : सैडल पीक (732 मीटर, उत्तरी अंडमान), माउंट डियावोलो (मध्य अंडमान) और माउंट कोयोब (दक्षिणी अंडमान)।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "116",
                    section: "misc",
                    question_en: "<p>56. Which of the following causes enlargement and extension growth of cells?</p>",
                    question_hi: "<p>56. निम्नलिखित में से कौन-सा कोशिकाओं के विस्तार और फैलाव का कारण बनता है?</p>",
                    options_en: ["<p>Pressure potential</p>", "<p>Osmotic pressure</p>", 
                                "<p>Imbibition</p>", "<p>Turgor pressure</p>"],
                    options_hi: ["<p>दाब विभव (Pressure potential)</p>", "<p>परासरण दाब (Osmotic pressure)</p>",
                                "<p>अंतःशोषण (Imbibition)</p>", "<p>स्फीति दाब (Turgor pressure)</p>"],
                    solution_en: "<p>56.(d) <strong>Turgor pressure.</strong> It is the force exerted by water entering plant cells, causing them to swell and expand. This pressure is responsible for - Enlargement growth: Increase in cell size due to water uptake. Extension growth: Increase in cell length due to continuous water uptake and cell expansion. Turgor pressure is essential for plant growth and maintenance of plant structure.</p>",
                    solution_hi: "<p>56.(d) <strong>स्फीति दाब </strong>(Turgor pressure)। यह पौधों की कोशिकाओं में जल के प्रवेश द्वारा लगाया जाने वाला बल है, जिससे उसमे सूजन तथा फैलाव हो जाती हैं। यह दाब निम्नलिखित के लिए जिम्मेदार है - विस्तार वृद्धि: जल अवशोषण के कारण कोशिका के आकार में वृद्धि। फैलाव वृद्धि : निरंतर जल के अवशोषण और कोशिका विस्तार के कारण कोशिका की लंबाई में वृद्धि। स्फीति दाब पौधों की वृद्धि और पौधों की संरचना के रखरखाव के लिए आवश्यक है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "117",
                    section: "misc",
                    question_en: "<p>57. In which year was the Marris College of Music established in India ?</p>",
                    question_hi: "<p>57. भारत में मैरिस कॉलेज ऑफ़ म्यूज़िक (Marris College of Music) की स्थापना किस वर्ष हुई थी?</p>",
                    options_en: ["<p>1922</p>", "<p>1926</p>", 
                                "<p>1901</p>", "<p>1919</p>"],
                    options_hi: ["<p>1922</p>", "<p>1926</p>",
                                "<p>1901</p>", "<p>1919</p>"],
                    solution_en: "<p>57.(b) <strong>1926</strong>. Bhatkhande Music Institute University, formerly known as Marris College of Hindustani Music, was established by Pt. Vishnu Narayan Bhatkhande. The purpose of its establishment was to liberate music from the monopoly of Gharanedar Musicians and make music education accessible to all.</p>",
                    solution_hi: "<p>57.(b) <strong>1926</strong>. भातखंडे संगीत संस्थान विश्वविद्यालय, जिसे पहले मैरिस कॉलेज ऑफ हिंदुस्तानी म्यूजिक के नाम से जाना जाता था, की स्थापना पंडित विष्णु नारायण भातखंडे ने की थी। इसकी स्थापना का उद्देश्य संगीत को घरानेदार संगीतकारों के एकाधिकार से मुक्त कराना और संगीत शिक्षा को सभी के लिए सुलभ बनाना था।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "118",
                    section: "misc",
                    question_en: "<p>58. Match List I with List II and choose your answer from the code below:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642936013.png\" alt=\"rId48\" width=\"380\" height=\"124\"></p>",
                    question_hi: "<p>58. सूची I को सूची II से सुमेलित करें और नीचे दिए गए कोड से अपना उत्तर चुनें:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642936174.png\" alt=\"rId49\" width=\"350\" height=\"186\"></p>",
                    options_en: ["<p>i-d, ii-c, iii-b, iv-a</p>", "<p>i-c, ii-b, iii-a, iv-d</p>", 
                                "<p>i-d, ii-c, iii-a, iv-b</p>", "<p>i-d, ii-a, iii-c, iv-b</p>"],
                    options_hi: ["<p>i-d, ii-c, iii-b, iv-a</p>", "<p>i-c, ii-b, iii-a, iv-d</p>",
                                "<p>i-d, ii-c, iii-a, iv-b</p>", "<p>i-d, ii-a, iii-c, iv-b</p>"],
                    solution_en: "<p>58.(c) <strong>i-d, ii-c, iii-a, iv-b. </strong>Tax revenue : The revenues collected from taxes, for example Income tax, Custom duty, GST, etc,. Non-tax revenue : It includes dividends, interest, profits, fines, fees, etc,. Revenue expenditure : Expenditure that neither creates any asset nor reduces any liability, for example salaries, interest payments, pension, and administrative expenses. Capital receipt : Disinvestment, Money received in the form of borrowings or repayment of loans by states.</p>",
                    solution_hi: "<p>58.(c) <strong>i-d, ii-c, iii-a, iv-b. </strong>कर राजस्व: करों से एकत्रित राजस्व, उदाहरण के लिए आयकर, सीमा शुल्क, GST, आदि। गैर-कर राजस्व: इसमें लाभांश, ब्याज, लाभ, जुर्माना, शुल्क आदि शामिल हैं। राजस्व व्यय: ऐसा व्यय जो न तो कोई संपत्ति बनाता है और न ही किसी देयता को कम करता है, उदाहरण के लिए वेतन, ब्याज भुगतान, पेंशन और प्रशासनिक व्यय। पूंजीगत प्राप्ति: विनिवेश, राज्यों द्वारा उधार या ऋण के पुनर्भुगतान के रूप में प्राप्त धन।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "119",
                    section: "misc",
                    question_en: "<p>59. Mameluke style of architecture belongs to which dynasty?</p>",
                    question_hi: "<p>59. मामलुक शैली की स्थापत्य कला किस वंश से संबंधित है ?</p>",
                    options_en: ["<p>Lodi dynasty</p>", "<p>Slave dynasty</p>", 
                                "<p>Khilji dynasty</p>", "<p>Tughlaq dynasty</p>"],
                    options_hi: ["<p>लोदी वंश</p>", "<p>गुलाम वंश</p>",
                                "<p>खिलजी वंश</p>", "<p>तुगलक वंश</p>"],
                    solution_en: "<p>59.(b) <strong>Slave dynasty </strong>(1206 - 1290) : It is also known as the Mamluk Dynasty. It was established by Qutbuddin Aibak. Capital - Lahore (1206 - 1210). Famous monuments built by rulers of the slave dynasty: Quwwat-ul-Islam Mosque, Qutub Minar, Adhai Din ka Jhonpra, Tomb of Nasir-ud-Din Mohammed, Tomb of Shams-ud-Din Iltutmish.</p>",
                    solution_hi: "<p>59.(b) <strong>गुलाम वंश </strong>(1206 - 1290) : इसे मामलुक वंश के नाम से भी जाना जाता है। इसकी स्थापना कुतुबुद्दीन ऐबक ने की थी। राजधानी - लाहौर (1206 - 1210)। गुलाम वंश के शासकों द्वारा निर्मित प्रसिद्ध इमारतें : कुव्वत-उल-इस्लाम मस्जिद, कुतुब मीनार, अढ़ाई दिन का झोपड़ा, नासिर-उद-दीन मोहम्मद का मकबरा, शम्स-उद-दीन इल्तुतमिश का मकबरा।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "120",
                    section: "misc",
                    question_en: "<p>60. Trade liberalisation has helped India improve its _____________ in industries with medium-to-high technology content.</p>",
                    question_hi: "<p>60. व्यापार उदारीकरण ने भारत को मध्यम से उच्च प्रौद्योगिकी सामग्री वाले उद्योगों में अपने _____________ को बेहतर बनाने में मदद की है।</p>",
                    options_en: ["<p>imports</p>", "<p>comparative advantage</p>", 
                                "<p>fuel efficiency</p>", "<p>labour intensity</p>"],
                    options_hi: ["<p>आयात</p>", "<p>तुलनात्मक लाभ</p>",
                                "<p>ईंधन दक्षता</p>", "<p>श्रम तीव्रता</p>"],
                    solution_en: "<p>60.(b) <strong>Comparative advantage </strong>refers to the ability of an economy to produce goods or services at a lower opportunity cost compared to others. This concept, introduced by David Ricardo, demonstrates how trade liberalization (such as the reduction of barriers like tariffs and quotas) allows countries to specialize in industries where they have a comparative advantage. This specialization promotes more efficient global production and trade, leading to overall economic growth.</p>",
                    solution_hi: "<p>60.(b) <strong>तुलनात्मक लाभ </strong>से तात्पर्य किसी अर्थव्यवस्था की दूसरों की तुलना में कम अवसर लागत पर वस्तुओं या सेवाओं का उत्पादन करने की क्षमता से है। डेविड रिकार्डो द्वारा प्रस्तुत यह अवधारणा दर्शाती है कि कैसे व्यापार उदारीकरण (जैसे टैरिफ और कोटा जैसी बाधाओं में कमी) देशों को उन उद्योगों में विशेषज्ञता हासिल करने की अनुमति देता है जहां उन्हें तुलनात्मक लाभ होता है। यह विशेषज्ञता अधिक कुशल वैश्विक उत्पादन और व्यापार को बढ़ावा देती है, जिससे समग्र आर्थिक विकास होता है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "121",
                    section: "misc",
                    question_en: "<p>61. Sea ice helps determine Earth\'s climate. According to this, what percentage of the sunlight falling on sea ice is reflected back into space ?</p>",
                    question_hi: "<p>61. समुद्री बर्फ पृथ्वी की जलवायु को निर्धारित करने में सहायक होती है। इसके अनुसार, समुद्री बर्फ पर पड़ने वाले सूर्य के प्रकाश का कितना प्रतिशत वापस अंतरिक्ष में परावर्तित हो जाता है ?</p>",
                    options_en: ["<p>80%</p>", "<p>70%</p>", 
                                "<p>75%</p>", "<p>85%</p>"],
                    options_hi: ["<p>80%</p>", "<p>70%</p>",
                                "<p>75%</p>", "<p>85%</p>"],
                    solution_en: "<p>61.(a) <strong>80%</strong>. Sea ice has a very bright surface, or albedo. Albedo is a measure of how much solar energy is reflected back into space from the Earth\'s surface. The dark surface of the liquid ocean, however, absorbs about 90% of solar radiation.</p>",
                    solution_hi: "<p>61.(a)<strong> 80%</strong>. समुद्री बर्फ की सतह बहुत चमकीली होती है, जिसे अल्बेडो कहते हैं। अल्बेडो इस बात का माप है कि पृथ्वी की सतह से कितनी सौर ऊर्जा, अंतरिक्ष में परावर्तित होती है। हालाँकि, तरल महासागर की गहरी सतह लगभग 90% सौर विकिरण को अवशोषित कर लेती है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "122",
                    section: "misc",
                    question_en: "<p>62. Match the following 2024 Padma award recipients with their respective awards and fields:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642936294.png\" alt=\"rId50\" width=\"450\" height=\"86\"></p>",
                    question_hi: "<p>62. निम्नलिखित 2024 में पद्म पुरस्कार प्राप्तकर्ताओं को उनके संबंधित पुरस्कारों और क्षेत्रों से सुमेलित कीजिए:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642936397.png\" alt=\"rId51\" width=\"400\" height=\"108\"></p>",
                    options_en: ["<p>A-iii, B-iv, C-i, D-ii</p>", "<p>A-ii, B-iv, C-i, D-iii</p>", 
                                "<p>A-iii, B-ii, C-iv, D-i</p>", "<p>A-ii, B-iv, C-ii, D-iv</p>"],
                    options_hi: ["<p>A-iii, B-iv, C-i, D-ii</p>", "<p>A-ii, B-iv, C-i, D-iii</p>",
                                "<p>A-iii, B-ii, C-iv, D-i</p>", "<p>A-ii, B-iv, C-ii, D-iv</p>"],
                    solution_en: "<p>62.(b) <strong>A-ii, B-iv, C-i, D-iii. </strong>Padma Awards are conferred by the President of India at ceremonial functions held at Rashtrapati Bhawan, usually around March or April every year. The President of India approved 132 Padma Awards for 2024, including 5 Padma Vibhushan, 17 Padma Bhushan, and 110 Padma Shri awards. This list includes 30 women, 8 Foreigners/NRI/PIO/OCI recipients, and 9 posthumous awards.</p>",
                    solution_hi: "<p>62.(b) <strong>A-ii, B-iv, C-i, D-iii. </strong>पद्म पुरस्कार भारत के राष्ट्रपति द्वारा राष्ट्रपति भवन में आयोजित औपचारिक समारोह में प्रदान किए जाते हैं, जो आमतौर पर प्रत्येक वर्ष मार्च या अप्रैल के आसपास होता है। भारत की राष्ट्रपति ने 2024 के लिए 132 पद्म पुरस्कारों को मंजूरी दी, जिनमें 5 पद्म विभूषण, 17 पद्म भूषण और 110 पद्म श्री पुरस्कार शामिल हैं। इस सूची में 30 महिलाएं, 8 विदेशी/NRI/PIO/OCI प्राप्तकर्ता और 9 मरणोपरांत पुरस्कार शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "123",
                    section: "misc",
                    question_en: "<p>63. Match the points under List I with those under List II.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642936512.png\" alt=\"rId52\" width=\"520\" height=\"130\"></p>",
                    question_hi: "<p>63. सूची I में दिए गए बिंदुओं को सूची ॥ में दिए गए बिंदुओं से सुमेलित कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1728642936663.png\" alt=\"rId53\" width=\"450\" height=\"146\"></p>",
                    options_en: ["<p>1-b, 2-d, 3-c, 4-a</p>", "<p>1-a, 2-d, 3-b, 4-c</p>", 
                                "<p>1-c, 2-d, 3-b, 4-a</p>", "<p>1-c, 2-b, 3-d, 4-a</p>"],
                    options_hi: ["<p>1-b, 2-d, 3-c, 4-a</p>", "<p dir=\"ltr\">&nbsp;1-a, 2-d, 3-b, 4-c</p>",
                                "<p>1-c, 2-d, 3-b, 4-a</p>", "<p>1-c, 2-b, 3-d, 4-a</p>"],
                    solution_en: "<p>63.(b) <strong>1-a, 2-d, 3-b, 4-c.</strong> The first sextant was produced by John Bird. Thermopile was developed by Leopoldo Nobili and Macedonio Melloni. Pyrometer was invented by Josiah Wedgwood.</p>",
                    solution_hi: "<p>63.(b) <strong>1-a, 2-d, 3-b, 4-c.</strong> पहला सेक्सटैंट जॉन बर्ड द्वारा बनाया गया था। थर्मोपाइल को लियोपोल्डो नोबिली और मैसेडोनियो मेलोनी द्वारा विकसित किया गया था। पाइरोमीटर का आविष्कार जोशिया वेजवुड ने किया था।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "124",
                    section: "misc",
                    question_en: "64. Which of the following statements is NOT correct about alluvial soil? ",
                    question_hi: "64. जलोढ़ मिट्टी के बारे में निम्नलिखित में से कौन सा कथन सही नहीं है?",
                    options_en: [" Deltas of Mahanadi, the Godavari and the Krishna are made from alluvial soil. ", " Old alluvial is called ‘bangar’ and new alluvial is called ‘khadar’. ", 
                                " In the Himalayan region we find  alluvial soil. ", " Northern plains are made of alluvial soil. "],
                    options_hi: [" महानदी, गोदावरी और कृष्णा के डेल्टा जलोढ़ मिट्टी से बने हैं।", " पुराने जलोढ़ को \'बांगर\' और नए जलोढ़ को \'खादर\' कहा जाता है।",
                                " हिमालयी क्षेत्र में हमें जलोढ़ मिट्टी मिलती है।", " उत्तरी मैदान जलोढ़ मिट्टी से बने हैं।"],
                    solution_en: "64.(c) Alluvial soil is mainly found in Northern Plains and valleys, not in Himalayan region. The major soils in the Himalayas are brown hill soil, sub-mountain soils, mountain meadow soil and red loamy soils. Alluvial soils are rich in humus as they are deposited by three important rivers of Himalayas, Indus river, Ganges and Brahmaputra River. ",
                    solution_hi: "64.(c) जलोढ़ मिट्टी मुख्य रूप से उत्तरी मैदानों और घाटियों में पाई जाती है, हिमालय क्षेत्र में नहीं। हिमालय की प्रमुख मिट्टी भूरी पहाड़ी मिट्टी, उप-पर्वतीय मिट्टी, पहाड़ी मैदानी मिट्टी और लाल दोमट मिट्टी हैं। जलोढ़ मिट्टी में ह्यूमस प्रचुर मात्रा में होता है क्योंकि यह हिमालय की तीन महत्वपूर्ण नदियों सिंधु नदी, गंगा और ब्रह्मपुत्र नदी द्वारा जमा की जाती है।",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "125",
                    section: "misc",
                    question_en: "<p>65. Select the correct alternative on the basis of the given statements on the cities of Harappan civilisation.<br><strong>Statement I:</strong> Most of the Harappan cities were divided into two parts : Higher Town and Lower Town. <br><strong>Statement II: </strong>The archaeologists describe the lower part of the towns as Citadel.</p>",
                    question_hi: "<p>65. हड़प्पा सभ्यता के नगरों के बारे में दिए गए कथनों के आधार पर सही विकल्प का चयन कीजिए।<br><strong>कथन I:</strong> हड़प्पा के अधिकांश नगर दो भागों में विभाजित थे: ऊपरी नगर और निचला नगर । <br><strong>कथन II: </strong>पुरातत्वविद्, नगरों के निचले हिस्से को गढ़ (Citadel) के रूप में वर्णित करते हैं।</p>",
                    options_en: ["<p>Both Statement I and Statement II are true.</p>", "<p>Only Statement II is true.</p>", 
                                "<p>Only Statement I is true.</p>", "<p>Neither Statement I nor Statement II is true.</p>"],
                    options_hi: ["<p>कथन I और कथन II दोनों सत्य हैं।</p>", "<p>केवल कथन II सत्य है।</p>",
                                "<p>केवल कथन I सत्य है।</p>", "<p>न तो कथन I सत्य है और न ही कथन II सत्य है।</p>"],
                    solution_en: "<p>65.(c) <strong>Only Statement I is true. </strong>The Harappan civilization was one of the oldest civilizations in the world. It flourished in the Indus River Valley region from around 2500 - 1500 BC. Harappan civilization: Excavated by - Daya Ram Sahni in 1921. Location - Situated on the bank of river Ravi in Punjab (Pakistan). Important Findings - Granaries, Bullock carts, Coffin burial, Mother goddess.</p>",
                    solution_hi: "<p>65.(c) <strong>केवल कथन I सत्य है।</strong> हड़प्पा सभ्यता दुनिया की सबसे पुरानी सभ्यताओं में से एक थी। यह लगभग 2500 - 1500 ईसा पूर्व के दौरान सिंधु घाटी क्षेत्र में विकसित हुआ। हड़प्पा सभ्यता : 1921 में दया राम साहनी द्वारा खुदाई की गई। स्थान - पंजाब (पाकिस्तान) में रावी नदी के तट पर स्थित है। महत्वपूर्ण खोज - अन्न भंडार, बैलगाड़ियाँ, ताबूत दफन, मातृ देवी।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "126",
                    section: "misc",
                    question_en: "<p>66. Read the following about Commercial Banks in India.<br>A. Commercial banks are the other type of institutions which are a part of the money-creating system of the economy.<br>B. They accept deposits from the public and lend out part of these funds to those who want to borrow.<br>Identify the INCORRECT statement/statements.</p>",
                    question_hi: "<p>66. भारत की व्यावसायिक बैंकों के बारे में निम्नलिखित कथनों को पढ़िए।<br>A. व्यावसायिक बैंक दूसरे प्रकार के संस्थान हैं जो द्रव्य निर्माण अर्थव्यवस्था का एक भाग हैं।<br>B. वे जनता से जमा स्वीकार करते हैं और इस रकम का एक भाग उन लोगों को उधार देते हैं जो उधार लेना चाहते हैं। <br>गलत कथन/कथनों की पहचान कीजिए।</p>",
                    options_en: ["<p>Neither A nor В</p>", "<p>Only A</p>", 
                                "<p>Both A and B</p>", "<p>Only B</p>"],
                    options_hi: ["<p>न तो A और न ही B</p>", "<p>केवल A</p>",
                                "<p>A और B दोनों</p>", "<p>केवल B</p>"],
                    solution_en: "<p>66.(a) <strong>Neither A nor В</strong>. Commercial banks are key players in the money-creating system. They accept deposits from the public and lend part of these funds to borrowers. The difference between the lower interest rate paid to depositors and the higher rate charged to borrowers, known as the \'spread,\' constitutes the bank\'s profit.</p>",
                    solution_hi: "<p>66.(a) <strong>न तो A और न ही B</strong>। व्यावसायिक बैंक मुद्रा-सृजन प्रणाली में प्रमुख भूमिका निभाते हैं। वे जनता से जमा स्वीकार करते हैं और इस धनराशि का कुछ हिस्सा उधारकर्ताओं को उधार देते हैं। जमाकर्ताओं को दी जाने वाली कम ब्याज दर और उधारकर्ताओं से ली जाने वाली उच्च दर के बीच का अंतर, जिसे \'स्प्रेड\' के रूप में जाना जाता है, बैंक के लाभ का गठन करता है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "127",
                    section: "misc",
                    question_en: "<p>67. Recently, Neeraj Chopra won which medal in the men&rsquo;s javelin throw event at the Paris Olympics 2024?</p>",
                    question_hi: "<p>67. हाल ही में, नीरज चोपड़ा ने पेरिस ओलंपिक 2024 में पुरुषों की भाला फेंक स्पर्धा में कौन सा पदक जीता?</p>",
                    options_en: ["<p>Gold</p>", "<p>Silver</p>", 
                                "<p>Bronze</p>", "<p>None of the Above</p>"],
                    options_hi: ["<p>स्वर्ण</p>", "<p>रजत</p>",
                                "<p>कांस्य</p>", "<p>उपरोक्त में से कोई नहीं</p>"],
                    solution_en: "<p>67.(b) <strong>Silver</strong>. Neeraj Chopra is a javelin thrower who threw 89.45 meters in the finals. Pakistan\'s Arshad Nadeem claimed the top spot with a throw of 92.97 meters, while Anderson Peters of Grenada earned the bronze with an attempt of 88.54 meters. India clinched six medals&mdash;one silver and five bronze&mdash;and finished 71st in the final medal tally at the Paris Olympics.</p>",
                    solution_hi: "<p>67.(b) <strong>रजत</strong>। नीरज चोपड़ा भाला फेंक खिलाड़ी हैं जिन्होंने फाइनल में 89.45 मीटर भाला फेंका। पाकिस्तान के अरशद नदीम ने 92.97 मीटर की थ्रो के साथ शीर्ष स्थान हासिल किया, जबकि ग्रेनेडा के एंडरसन पीटर्स ने 88.54 मीटर के प्रयास के साथ कांस्य पदक जीता। भारत ने कुल छह पदक जीते - एक रजत और पांच कांस्य पदक । इसी के साथ पेरिस ओलंपिक की पदक तालिका में भारत 71वें स्थान पर रहा।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "128",
                    section: "misc",
                    question_en: "<p>68. Who discovered a principle that states that the buoyant force on an object is equal to the weight of the fluid displaced by the object?</p>",
                    question_hi: "<p>68. निम्नलिखित में से किसने एक सिद्धांत की खोज की, जो बताता है कि किसी वस्तु पर उत्प्लावन बल वस्तु द्वारा विस्थापित द्रव के भार के बराबर होता है?</p>",
                    options_en: ["<p>Isaac Newton</p>", "<p>Niels Bohr</p>", 
                                "<p>Archimedes</p>", "<p>Galileo Galilei</p>"],
                    options_hi: ["<p>आइजैक न्यूटन</p>", "<p>नील्स बोहर</p>",
                                "<p>आर्किमिडीज</p>", "<p>गैलीलियो गैलीली</p>"],
                    solution_en: "<p>68.(c) <strong>Archimedes</strong>. This principle is known as Archimedes\' Principle, which has many practical applications. This principle is used in designing ships and submarines. It is also the basis for devices like lactometers, which determine the purity of milk, and hydrometers, which measure the density of liquids. Isaac Newton formulated the Laws of Motion and the Universal Law of Gravitation. Niels Bohr, a Danish physicist, explained the hydrogen atom spectrum using quantum ideas and developed a theory of nuclear fission based on the liquid drop model of the nucleus.</p>",
                    solution_hi: "<p>68.(c) <strong>आर्किमिडीज</strong>। इस सिद्धांत को आर्किमिडीज सिद्धांत के नाम से जाना जाता है, जिसके कई व्यावहारिक अनुप्रयोग हैं। इस सिद्धांत का उपयोग जहाजों और पनडुब्बियों को डिजाइन करने में किया जाता है। यह लैक्टोमीटर जैसे उपकरणों का आधार भी है, जो दूध की शुद्धता निर्धारित करते हैं, और हाइड्रोमीटर, जो तरल पदार्थों के घनत्व को मापते हैं। आइजैक न्यूटन ने गति के नियम और गुरुत्वाकर्षण के सार्वभौमिक नियम तैयार किए। डेनमार्क के भौतिक विज्ञानी नील्स बोहर ने क्वांटम विचारों का उपयोग करके हाइड्रोजन परमाणु स्पेक्ट्रम की व्याख्या की और नाभिक के तरल बूंद मॉडल के आधार पर परमाणु विखंडन का सिद्धांत विकसित किया।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "129",
                    section: "misc",
                    question_en: "<p>69. Which of the following pairs is/are correctly matched regarding the terminology used in the Parliament?<br>A. Appropriation Bill - A Money Bill passed for providing for the withdrawal or appropriation from and out of the Consolidated Fund of India<br>B. Demand for Grants - Earmarking of budgetary allocation for meeting only planned expenditure of a Ministry/Department <br>C. Short Duration Discussion - For raising a discussion on a matter of urgent public importance for which a notice has to be given by a Member and supported by 50 Members.</p>",
                    question_hi: "<p>69. संसद में प्रयुक्त शब्दावली के संबंध में निम्नलिखित में से कौन सा/से युग्म सही सुमेलित है/हैं?<br>A. विनियोग विधेयक - भारत की संचित निधि से निकासी या विनियोग के लिए पारित किया गया धन विधेयक<br>B. अनुदानों की मांग - किसी मंत्रालय/विभाग के केवल नियोजित व्यय को पूरा करने के लिए बजटीय आवंटन निर्धारित करना<br>C. अल्पकालिक चर्चा - तत्काल सार्वजनिक महत्व के किसी मामले पर चर्चा शुरू करने के लिए जिसके लिए किसी सदस्य द्वारा नोटिस दिया जाना चाहिए और 50 सदस्यों द्वारा समर्थित होना चाहिए।</p>",
                    options_en: ["<p>A, B and C</p>", "<p>B and C only</p>", 
                                "<p>A only</p>", "<p>A and C only</p>"],
                    options_hi: ["<p>A, B और C</p>", "<p>केवल B और C</p>",
                                "<p>केवल A</p>", "<p>केवल A और C</p>"],
                    solution_en: "<p>69.(c) <strong>A only. </strong>Demand for Grants: The estimates of expenditure from the Consolidated Fund, as part of the annual financial statement, are presented in the form of Demand for Grants, which require voting in the Lok Sabha pursuant to Article 113 of the Constitution. Short Duration Discussion: This is a parliamentary procedure allowing members to raise urgent matters of public importance for discussion without a formal motion. The Speaker can allocate two days a week for such discussions.</p>",
                    solution_hi: "<p>69.(c) <strong>केवल A </strong>। अनुदान की मांग: वार्षिक वित्तीय विवरण के भाग के रूप में समेकित निधि से व्यय का अनुमान अनुदान की मांग के रूप में प्रस्तुत किया जाता है, जिसके लिए संविधान के अनुच्छेद 113 के अनुसार लोक सभा में मतदान की आवश्यकता होती है। अल्पकालिक चर्चा: यह एक संसदीय प्रक्रिया है जो सदस्यों को औपचारिक प्रस्ताव के बिना चर्चा के लिए सार्वजनिक महत्व के तत्काल मामलों को उठाने की अनुमति देती है। अध्यक्ष ऐसी चर्चाओं के लिए सप्ताह में दो दिन आवंटित कर सकते हैं।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "130",
                    section: "misc",
                    question_en: "<p>70. In which year was the National Human Rights Commission established in India?</p>",
                    question_hi: "<p>70. भारत में राष्ट्रीय मानवाधिकार आयोग की स्थापना किस वर्ष में हुई थी?</p>",
                    options_en: ["<p>2003</p>", "<p>1995</p>", 
                                "<p>1993</p>", "<p>2005</p>"],
                    options_hi: ["<p>2003</p>", "<p>1995</p>",
                                "<p>1993</p>", "<p>2005</p>"],
                    solution_en: "<p>70.(c) <strong>1993</strong>. The National Human Rights Commission (NHRC) is an embodiment of India&rsquo;s commitment to the promotion and protection of human rights. It consists of a Chairperson, five full-time Members, and seven deemed Members. The Chairperson must be a former Chief Justice of India or a Judge of the Supreme Court. The United Nations Commission on Human Rights was established in 1946.</p>",
                    solution_hi: "<p>70.(c) <strong>1993</strong>. राष्ट्रीय मानवाधिकार आयोग (NHRC) मानवाधिकारों के संवर्धन और संरक्षण के प्रति भारत की प्रतिबद्धता का प्रतीक है। इसमें एक अध्यक्ष, पाँच पूर्णकालिक सदस्य और सात मानद सदस्य होते हैं। अध्यक्ष भारत का पूर्व मुख्य न्यायाधीश या सर्वोच्च न्यायालय का न्यायाधीश होना चाहिए। संयुक्त राष्ट्र मानवाधिकार आयोग की स्थापना 1946 में हुई थी।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "131",
                    section: "misc",
                    question_en: "<p>1. In how many different combinations do the standard ASCII codes come?</p>",
                    question_hi: "<p>1. मानक ASCII कोड कितने भिन्न संयोजनों में आते हैं?</p>",
                    options_en: ["<p>100</p>", "<p>64</p>", 
                                "<p>256</p>", "<p>128</p>"],
                    options_hi: ["<p>100</p>", "<p>64</p>",
                                "<p>256</p>", "<p>128</p>"],
                    solution_en: "<p>1.(d) <strong>128</strong>. Standard ASCII (American Standard Code for Information Interchange) uses 7 bits to represent characters, which allows for 128 different combinations (0 to 127). ASCII was developed in the early 1960s and published in 1968 by the American National Standards Institute (ANSI). It was designed to standardize the representation of text in computers and other devices.</p>",
                    solution_hi: "<p>1.(d) <strong>128</strong>. मानक ASCII (अमेरिकन स्टैंडर्ड कोड फॉर इंफॉर्मेशन इंटरचेंज) 7 बिट्स का उपयोग करके अक्षरों का प्रतिनिधित्व करता है, जिससे 128 विभिन्न संयोजन (0 से 127 तक) संभव होते हैं। ASCII का विकास 1960 के दशक की शुरुआत में हुआ था और इसे 1968 में अमेरिकन नेशनल स्टैंडर्ड्स इंस्टीट्यूट (ANSI) द्वारा प्रकाशित किया गया था। इसे कंप्यूटरों और अन्य उपकरणों में पाठ का प्रतिनिधित्व मानकीकृत करने के लिए डिजाइन किया गया था।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "132",
                    section: "misc",
                    question_en: "<p>2. Which of the following is used to remove formatting from a selected paragraph in many word processing programs?</p>",
                    question_hi: "<p>2. कई वर्ड प्रोसेसिंग प्रोग्रामों में सेलेक्&zwj;टेड पैराग्राफ से फ़ॉर्मेटिंग हटाने के लिए निम्नलिखित में से किसका उपयोग किया जाता है?</p>",
                    options_en: ["<p>Clear Formatting</p>", "<p>Delete Paragraph</p>", 
                                "<p>Format Painter</p>", "<p>Remove Paragraph</p>"],
                    options_hi: ["<p>क्लीयर फॉर्मेटिंग (Clear Formatting)</p>", "<p>डिलीट पैराग्राफ (Delete Paragraph)</p>",
                                "<p>फॉर्मेट पेंटर (Format Painter)</p>", "<p>रिमूव पैराग्राफ (Remove Paragraph)</p>"],
                    solution_en: "<p>2.(a) <strong>Clear Formatting. </strong>Formatting refers to the process of altering the appearance and layout of text, documents, or data to enhance readability, presentation, and organization. In word processing and other software, formatting can include various elements such as: Text Style, Text Effects, Alignment, Spacing, Lists, Borders and Shading, Headers and Footers, Tables, Page Layout.</p>",
                    solution_hi: "<p>2.(a) <strong>क्लीयर फॉर्मेटिंग</strong> । फॉर्मेटिंग उस प्रक्रिया को संदर्भित करता है जिसमें टेक्स्ट, दस्तावेज़, या डेटा की उपस्थिति और लेआउट को बदलकर पढ़ने में आसानी, प्रस्तुति, और संगठन को बढ़ाया जाता है। वर्ड प्रोसेसिंग और अन्य सॉफ़्टवेयर में, फॉर्मेटिंग में विभिन्न तत्व शामिल हो सकते हैं जैसे: टेक्स्ट स्टाइल, टेक्स्ट इफेक्ट्स, अलाइनमेंट, स्पेसिंग, लिस्ट्स, बॉर्डर्स और शेडिंग, हेडर्स और फुटर्स, टेबल्स, पेज लेआउट।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "133",
                    section: "misc",
                    question_en: "<p>3. Which of the following parts of an email is used to indicate the sender and recipient details, as well as the date and time?</p>",
                    question_hi: "<p>3. ईमेल के निम्नलिखित में से किस भाग का उपयोग प्रेषक (sender) और प्राप्तकर्ता (recipient) के विवरण, साथ ही दिनांक और समय को इंगित करने के लिए किया जाता है?</p>",
                    options_en: ["<p>Subject line</p>", "<p>Body</p>", 
                                "<p>Email header</p>", "<p>Attachment</p>"],
                    options_hi: ["<p>सब्जेक्ट लाइन (Subject line)</p>", "<p>बॉडी (Body)</p>",
                                "<p>ईमेल हेडर (Email header)</p>", "<p>अटैचमेंट (Attachment)</p>"],
                    solution_en: "<p>3.(c) <strong>Email header</strong> is crucial for routing the email and providing context about its transmission. The subject line is a brief summary or title of the email\'s content. The body is the main content of the email where the message is written. An attachment is a file sent along with the email, such as documents, images, or other types of files.</p>",
                    solution_hi: "<p>3.(c)&nbsp;<strong>ईमेल हेडर </strong>ईमेल को रूट करने और इसके प्रसारण के बारे में संदर्भ प्रदान करने के लिए महत्वपूर्ण होता है। सब्जेक्ट लाइन ईमेल की सामग्री का संक्षिप्त सारांश या शीर्षक होती है। बॉडी ईमेल की मुख्य सामग्री होती है जहां संदेश लिखा जाता है। एटैचमेंट एक फ़ाइल होती है जो ईमेल के साथ भेजी जाती है, जैसे दस्तावेज़, चित्र, या अन्य प्रकार की फ़ाइलें।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "134",
                    section: "misc",
                    question_en: "<p>4. In MS Word, which option allows you to align text in a way that it appears vertically at the bottom of a cell or text box?</p>",
                    question_hi: "<p>4. एमएस वर्ड (MS Word) में, कौन-सा ऑशन आपको टेक्स्ट को इस तरह से अलाइन करने की सुविधा देता है कि वह सेल या टेक्स्ट बॉक्स के नीचे लंबवत दिखाई दे?</p>",
                    options_en: ["<p>Center Alignment</p>", "<p>Justify Alignment</p>", 
                                "<p>Bottom Alignment</p>", "<p>Distributed Alignment</p>"],
                    options_hi: ["<p>सेंटर अलाइनमेंट (Center Alignment)</p>", "<p>जस्टिफाई अलाइनमेंट (Justify Alignment)</p>",
                                "<p>बॉटम अलाइनमेंट (Bottom Alignment)</p>", "<p>डिस्ट्रिब्यूटेड अलाइनमेंट (Distributed Alignment)</p>"],
                    solution_en: "<p>4.(c) <strong>Bottom Alignment</strong>. Alignment in text formatting refers to how text is positioned relative to the margins, edges, or container boundaries in a document.</p>",
                    solution_hi: "<p>4.(c) <strong>बॉटम अलाइनमेंट</strong> (Bottom Alignment) । टेक्स्ट फॉर्मेटिंग में एलाइनमेंट उस तरीके को संदर्भित करता है जिसमें टेक्स्ट को दस्तावेज़ में मार्जिन, किनारों, या कंटेनर की सीमाओं के सापेक्ष स्थित किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "135",
                    section: "misc",
                    question_en: "<p>5. Which technology is associated with providing high-speed wireless data transmission for mobile devices ?</p>",
                    question_hi: "<p>5. कौन-सी तकनीक मोबाइल डिवाइसों के लिए हाई-स्पीड वायरलेस डेटा ट्रांसमिशन (high-speed wireless data transmission) प्रदान करने से संबंधित है?</p>",
                    options_en: ["<p>GPS</p>", "<p>Wi-Max</p>", 
                                "<p>GPRS</p>", "<p>LTE</p>"],
                    options_hi: ["<p>GPS</p>", "<p>Wi-Max</p>",
                                "<p>GPRS</p>", "<p>LTE</p>"],
                    solution_en: "<p>5.(d) <strong>LTE </strong>(Long-Term Evolution) is commonly known as 4G LTE and is widely used for faster internet connectivity on smartphones and other mobile devices. GPS (Global Positioning System) is a satellite-based navigation system that provides location and time information to GPS receivers anywhere on Earth. Wi-Max (Worldwide Interoperability for Microwave Access) is a wireless communication technology designed for providing high-speed broadband internet access over long distances. GPRS (General Packet Radio Service) is a 2.5G mobile data service that provides packet-switched data transmission over cellular networks.</p>",
                    solution_hi: "<p>5.(d) <strong>LTE </strong>(Long-Term Evolution) को आमतौर पर 4G LTE के रूप में जाना जाता है और इसे स्मार्टफ़ोन और अन्य मोबाइल उपकरणों पर तेज़ इंटरनेट कनेक्टिविटी के लिए व्यापक रूप से उपयोग किया जाता है। GPS (Global Positioning System) एक उपग्रह-आधारित नेविगेशन प्रणाली है जो पृथ्वी पर कहीं भी GPS रिसीवर्स को स्थान और समय की जानकारी प्रदान करती है। Wi-Max (Worldwide Interoperability for Microwave Access) एक वायरलेस संचार तकनीक है जिसे लंबी दूरी पर उच्च गति ब्रॉडबैंड इंटरनेट एक्सेस प्रदान करने के लिए डिज़ाइन किया गया है। GPRS (General Packet Radio Service) एक 2.5G मोबाइल डेटा सेवा है जो सेल्युलर नेटवर्क पर पैकेट-स्विच्ड डेटा ट्रांसमिशन प्रदान करती है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "136",
                    section: "misc",
                    question_en: "<p>6. Symmetric encryption is also known as:</p>",
                    question_hi: "<p>6. सममित एन्क्रिप्शन (Symmetric encryption)_____ भी कहलाता है।</p>",
                    options_en: ["<p>public-key encryption</p>", "<p>hybrid encryption</p>", 
                                "<p>conventional encryption</p>", "<p>asymmetric encryption</p>"],
                    options_hi: ["<p>सार्वजनिक-कुंजी एन्क्रिप्शन (public-key encryption)</p>", "<p>हाइब्रिड एन्क्रिप्शन (hybrid encryption)</p>",
                                "<p>कन्वेन्शनल एन्क्रिप्शन (conventional encryption)</p>", "<p>असममित एन्क्रिप्शन (asymmetric encryption)</p>"],
                    solution_en: "<p>6.(c) <strong>conventional encioryptn </strong>. In this method, the same key is used for both encryption and decryption of the data. Public-key encryption, also known as asymmetric encryption, uses two different keys: a public key for encryption and a private key for decryption. Hybrid encryption combines both symmetric and asymmetric encryption methods.</p>",
                    solution_hi: "<p>6.(c) <strong>कन्वेन्शनल एन्क्रिप्शन</strong> (conventional encryption)। इस विधि में, डेटा के एन्क्रिप्शन और डिक्रिप्शन दोनों के लिए एक ही कुंजी का उपयोग किया जाता है। पब्लिक-की एन्क्रिप्शन, जिसे असिमेट्रिक एन्क्रिप्शन के नाम से भी जाना जाता है, दो अलग-अलग कुंजियों का उपयोग करता है: एन्क्रिप्शन के लिए एक पब्लिक की और डिक्रिप्शन के लिए एक प्राइवेट की। हाइब्रिड एन्क्रिप्शन, सिमेट्रिक और असिमेट्रिक एन्क्रिप्शन विधियों दोनों को मिलाता है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "137",
                    section: "misc",
                    question_en: "<p>7. IBM 360 was developed in which of the following generations of computer?</p>",
                    question_hi: "<p>7. IBM 360 (IBM 360) निम्नलिखित में से कम्प्यूटर की किस पीढ़ी में विकसित किया गया था?</p>",
                    options_en: ["<p>2nd Generation</p>", "<p>1st Generation</p>", 
                                "<p>3rd Generation</p>", "<p>4th Generation</p>"],
                    options_hi: ["<p>द्वितीय पीढ़ी</p>", "<p>प्रथम पीढ़ी</p>",
                                "<p>तृतीय पीढ़ी</p>", "<p>चतुर्थ पीढ़ी</p>"],
                    solution_en: "<p>7.(c) <strong>Third generation. </strong>First Generation (1940 - 56): Vacuum tubes were used. Example: ENIAC (Electronic Numerical Integrator And Computer), EDVAC (Electronic Discrete Variable Automatic Computer), EDSAC (Electronic Delay Storage Automatic Calculator), UNIVAC I (Universal Automatic Computer I), IBM 701, IBM 650 etc. Second generation (1956 - 63): Transistors were used. Example: IBM 1620, IBM 7094, CDC 1604, CDC 3600, UNIVAC 1108, Honeywell 400 etc. Third Generation (1964 - 71): Integrated Circuits (IC) were used. Examples: IBM 370, PDP-11, Honeywell - 6000, PDP8 , ICL 2900 and DEC series. Fourth Generation (1971 - 2010): very large scale integration technology was used. Examples: DEC 10, STAR 1000, IBM 4341 and PUP 11.</p>",
                    solution_hi: "<p>7.(c) <strong>तीसरी पीढ़ी</strong>। पहली पीढ़ी (1940 - 56): इसमें वैक्यूम ट्यूब का इस्तेमाल किया गया था । उदाहरण: ENIAC (इलेक्ट्रॉनिक न्यूमेरिकल इंटीग्रेटर एंड कंप्यूटर), EDVAC (इलेक्ट्रॉनिक डिस्क्रीट वेरिएबल ऑटोमैटिक कंप्यूटर), EDSAC (इलेक्ट्रॉनिक डिले स्टोरेज ऑटोमैटिक कैलकुलेटर), UNIVAC I (यूनिवर्सल ऑटोमैटिक कंप्यूटर I), IBM 701, IBM 650 आदि। दूसरी पीढ़ी (1956 - 63): इसमें ट्रांजिस्टर का प्रयोग किया जाता था। उदाहरण: IBM 1620, IBM 7094, CDC 1604, CDC 3600, UNIVAC 1108, हनीवेल 400 आदि। तीसरी पीढ़ी (1964 - 71): इसमें इंटीग्रेटेड सर्किट (IC) का उपयोग किया गया। उदाहरण: IBM 370, PDP-11, हनीवेल - 6000, PDP8, ICL 2900 और DEC सीरीज। चौथी पीढ़ी (1971 - 2010): बहुत बड़े पैमाने पर एकीकरण तकनीक का इस्तेमाल किया गया। उदाहरण: DEC 10, STAR 1000, IBM 4341 और PUP 11।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "138",
                    section: "misc",
                    question_en: "8. What is the purpose of the \'Quick Access\' section in Windows Explorer (File Explorer)?",
                    question_hi: "8. विंडोज एक्सप्लोरर (File Explorer) में \'क्विक एक्सेस\' सेक्शन का उद्देश्य क्या है?",
                    options_en: [" To provide quick access to the Internet.", " To display shortcuts to frequently  used folders and files.", 
                                " To play multimedia files, such as  videos and music.", " To view recently visited websites."],
                    options_hi: [" इंटरनेट तक त्वरित (quick) पहुंच प्रदान  करना।", " उपयोग किए जाने वाले फ़ोल्डरों और फ़ाइलों के शॉर्टकट प्रदर्शित करने के लिए।",
                                " वीडियो और संगीत जैसी मल्टीमीडिया फ़ाइलें चलाने के लिए।", " हाल ही में देखी गई वेबसाइटों को देखने के लिए।"],
                    solution_en: "8.(b) Quick access makes it simple to find your frequently used storage locations, including recently used shared libraries, channels, and folders.",
                    solution_hi: "8.(b) क्विक एक्सेस (Quick access) अक्सर उपयोग किए जाने वाले स्टोरेज लोकेशन (storage locations) को फाइन्ड (find) करना आसान बनाती है, जिसमें हाल ही में उपयोग की गई साझा लाइब्रेरी (shared libraries), चैनल और फ़ोल्डर्स भी शामिल हैं।",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "139",
                    section: "misc",
                    question_en: "<p>9. In MS Word 365 how can you&nbsp;select a paragraph in Microsoft Word?</p>",
                    question_hi: "<p>9. एमएस वर्ड 365 में, आप माइक्रोसॉफ्ट वर्ड (Microsoft Word) में पैराग्राफ को कैसे सेलेक्&zwj;ट कर सकते हैं?</p>",
                    options_en: ["<p>Double-click anywhere within the paragraph.</p>", "<p>Right-click anywhere within the paragraph and choose \'Select Paragraph\'.</p>", 
                                "<p>Press Ctrl+B to select the entire paragraph.</p>", "<p>Triple-click anywhere within the paragraph</p>"],
                    options_hi: ["<p>पैराग्राफ के भीतर कहीं भी डबल-क्लिक करें।</p>", "<p>पैराग्राफ के भीतर कहीं भी राइट-क्लिक करें और &lsquo;Select Paragraph&rsquo; चुनें।</p>",
                                "<p>संपूर्णपैराग्राफ़ को सेलेक्&zwj;ट करने के लिए Ctrl + B दबाएं।</p>", "<p>पैराग्राफ के भीतर कहीं भी ट्रिपल-क्लिक करें।</p>"],
                    solution_en: "<p>9.(a) We can select a word by double clicking on the word. Ctrl + B - Bold. Ctrl + A - all select.</p>",
                    solution_hi: "<p>9.(a) वर्ड (word) पर डबल क्लिक करके किसी वर्ड को सेलेक्ट कर सकते हैं। Ctrl + B - बोल्ड (Bold)। Ctrl + A - ऑल सेलेक्ट (all select) कर सकते हैं।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "140",
                    section: "misc",
                    question_en: "<p>10. Which of the following option best describes the truthfulness of given statement with reference to working in edit mode in MS-Excel 365?<br>(i) Click the cell that contains data you want to edit and then click anywhere in the Formula bar.<br>(ii) Click the cell that contains the data that you want to edit and then press F6.</p>",
                    question_hi: "<p>10. एमएस-एक्सेल 365 (MS-Excel 365) में संपादन मोड (edit mode) में काम करने के संदर्भ में निम्नलिखित में से कौन सा विकल्प दिए गए कथन की सत्यता (truthfulness) का सबसे अच्छा वर्णन करता है?<br>(i) उस सेल पर क्लिक करें जिसमें वह डेटा है जिसे आप एडिट करना चाहते हैं और फिर फॉर्मूला बार में कहीं भी क्लिक करें।<br>(ii) उस सेल पर क्लिक करें जिसमें वह डेटा है जिसे आप एडिट करना चाहते हैं और फिर F6 दबाएँ।</p>",
                    options_en: ["<p>(i)-TRUE (ii) - FALSE</p>", "<p>(i)-FALSE (ii) - FALSE</p>", 
                                "<p>(i)-TRUE (ii) - TRUE</p>", "<p>(i)- FALSE (ii) - TRUE</p>"],
                    options_hi: ["<p>(i) - सत्य (ii)-असत्य</p>", "<p>(i) - असत्य (ii)-असत्य</p>",
                                "<p>(i) - सत्य (ii)-सत्य</p>", "<p>(i) - असत्य (ii) - सत्य</p>"],
                    solution_en: "<p>10.(a)<strong> (i) - True (ii) - False. </strong>Ctrl + F6 or Ctrl + Shift + F6 - Move between the cells, sheet tabs, status bar, list of services, and the ribbon.</p>",
                    solution_hi: "<p>10.(a)<strong> (i) - सत्य (ii)-असत्य</strong> । Ctrl + F6 या Ctrl + Shift + F6 - सेल, शीट टैब, स्टेटस बार, list of services और रिबन के बीच जाएँ।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "141",
                    section: "misc",
                    question_en: "<p>11. Sir Tim Berners - Lee __ a British computer scientist invented the revolutionary World Wide Web in _______</p>",
                    question_hi: "<p>11. सर टिम बर्नर्स-ली (Sir Tim Berners Lee) &ndash; एक ब्रिटिश कंप्यूटर वैज्ञानिक ने वर्ल्ड वाइड वेब का आविष्कार में किया था।</p>",
                    options_en: ["<p>1982</p>", "<p>1995</p>", 
                                "<p>1990</p>", "<p>1985</p>"],
                    options_hi: ["<p>1982</p>", "<p>1995</p>",
                                "<p>1990</p>", "<p>1985</p>"],
                    solution_en: "<p>11.(c) <strong>1990</strong>. Tim Berners-Lee submitted a proposal to The European Organization for Nuclear Research (CERN) in May 1989, without giving the system a name. He got a working system implemented by the end of 1990, including a browser called WorldWideWeb. Other Invention of Tim Berners-Lee HTML, HTTP, web browser.</p>",
                    solution_hi: "<p>11.(c) <strong>1990 </strong>। टिम बर्नर्स-ली ने सिस्टम को कोई नाम दिए बिना, मई 1989 में यूरोपीय नाभिकीय अनुसंधान संगठन (CERN) को एक प्रस्ताव प्रस्तुत किया। उन्होंने 1990 के अंत तक एक कार्य प्रणाली लागू करवाई, जिसमें WorldWideWeb नामक एक ब्राउज़र भी शामिल था । टिम बर्नर्स-ली के अन्य आविष्कार HTML, HTTP, वेब ब्राउज़र ।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "142",
                    section: "misc",
                    question_en: "<p>12. Which of the following is used to receive and send computer files over telephone lines?</p>",
                    question_hi: "<p>12. निम्नलिखित में से किस यंत्र को टेलीफोन लाइन (telephone lines) पर कंप्यूटर फाइल (computer files) लेने और भेजने (receive and send) के लिए इस्तेमाल (used) किया जाता है ?</p>",
                    options_en: ["<p>Modem</p>", "<p>MICR device</p>", 
                                "<p>Floppy disk</p>", "<p>Light pen</p>"],
                    options_hi: ["<p>मॉडेम (Modem)</p>", "<p>MICR उपकरण (MICR device)</p>",
                                "<p>फ्लॉपी डिस्क (floppy disk)</p>", "<p>लाईट पेन (light pen)</p>"],
                    solution_en: "<p>12.(a)<strong> A modulator Demodulator</strong> (Modem) is a hardware component that allows a computer or another device, such as a router or switch, to connect to the Internet. It converts or &ldquo;modulates&rdquo; an analog signal from a telephone or cable wire to digital data that a computer can recognize. Similarly, it converts digital data from a computer or other device into an analog signal that can be sent over standard telephone lines.</p>",
                    solution_hi: "<p>12.(a) <strong>मॉड्यूलेटर डेमोडुलेटर </strong>(मॉडेम) एक हार्डवेयर कॉम्पोनेन्ट है जो कंप्यूटर या किसी अन्य डिवाइस , जैसे राउटर (router) या स्विच (switch) को इंटरनेट से कनेक्ट (connect) करने की अनुमति देता है। यह एक टेलीफोन या केबल तार (cable wire) से एक एनालॉग सिग्नल (analog signal) को डिजिटल डेटा (digital data) में परिवर्तित या \"मॉड्यूलेट\" करता है जिसे कंप्यूटर पहचान सकता है। इसी तरह, यह कंप्यूटर या अन्य डिवाइस से डिजिटल डेटा को एनालॉग सिग्नल में परिवर्तित करता है जिसे मानक टेलीफोन लाइनों पर भेजा जा सकता है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "143",
                    section: "misc",
                    question_en: "<p>13. In computer system, the memory is fundamentally divided into ________ memory.</p>",
                    question_hi: "<p>13. कंप्यूटर सिस्टम में, मेमोरी को मूलतः ________ मेमोरी में विभाजित किया जाता है।</p>",
                    options_en: ["<p>subjective and objective</p>", "<p>computational and operational</p>", 
                                "<p>logical and conceptual</p>", "<p>primary and secondary</p>"],
                    options_hi: ["<p>सब्जेक्टिव और ऑब्जेक्टिव</p>", "<p>कम्प्यूटेशनल और ऑपरेशनल</p>",
                                "<p>लॉजिकल और कॉन्सेप्टुअल</p>", "<p>प्राइमेरी और सेकेंडरी</p>"],
                    solution_en: "<p>13.(d) <strong>Primary and secondary. </strong>Memory in a computer system is divided into two fundamental types:Primary Memory (Internal Memory): This includes Main Memory (RAM), Cache Memory, and CPU registers. It is directly accessible by the processor and is used for storing data and instructions that are in active use.Secondary Memory (External Memory): This consists of storage devices like magnetic disks (e.g., hard drives), optical disks (e.g., CDs/DVDs), and magnetic tapes. These are peripheral storage devices accessed by the processor via an I/O module.</p>",
                    solution_hi: "<p>13.(d) <strong>प्राइमेरी और सेकेंडरी </strong>। कंप्यूटर सिस्टम में मेमोरी दो प्रमुख प्रकारों में विभाजित होती है: प्राइमरी मेमोरी (आंतरिक मेमोरी): इसमें मुख्य मेमोरी (RAM), कैश मेमोरी, और CPU रजिस्टर शामिल हैं। यह प्रोसेसर द्वारा सीधे पहुंच योग्य होती है और उन डाटा और निर्देशों को संग्रहीत करती है जो सक्रिय उपयोग में होते हैं। सेकेंडरी मेमोरी (बाहरी मेमोरी): इसमें स्टोरेज डिवाइस जैसे मैग्नेटिक डिस्क (जैसे हार्ड ड्राइव), ऑप्टिकल डिस्क (जैसे CD/DVD), और मैग्नेटिक टेप शामिल हैं। ये पेरीफेरल स्टोरेज डिवाइस होते हैं जो I/O मॉड्यूल के माध्यम से प्रोसेसर द्वारा एक्सेस किए जाते हैं।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "144",
                    section: "misc",
                    question_en: "<p>14. A ______ card is a circuit board that provides additional capabilities to the computer system.</p>",
                    question_hi: "<p>14. ______ कार्ड एक सर्किट बोर्ड है जो कंप्यूटर सिस्टम को अतिरिक्त क्षमताएं (additional capabilities) प्रदान करता है।</p>",
                    options_en: ["<p>expansion</p>", "<p>hybrid</p>", 
                                "<p>portable</p>", "<p>parallel</p>"],
                    options_hi: ["<p>एक्सपेंशन (expansion)</p>", "<p>हाइब्रिड (hybrid)</p>",
                                "<p>पोर्टेबल (portable)</p>", "<p>पैरेलल (parallel)</p>"],
                    solution_en: "<p>14.(a) <strong>Expansion</strong>. An expansion card, also known as an expansion board or add-on card, is a hardware component that we can insert into a computer\'s expansion slot to enhance its functionality. It is a PCB (Printed Circuit Board) that plugs into an expansion slot on a computer motherboard.</p>",
                    solution_hi: "<p>14.(a) <strong>एक्सपेंशन </strong>। एक एक्सपेंशन कार्ड, जिसे एक्सपेंशन बोर्ड या ऐड-ऑन कार्ड के रूप में भी जाना जाता है, एक हार्डवेयर घटक है जिसे हम कंप्यूटर की कार्यक्षमता बढ़ाने के लिए उसके एक्सपेंशन स्लॉट में इन्सर्ट कर सकते हैं। यह एक PCB (Printed Circuit Board) है जिसे कंप्यूटर मदरबोर्ड पर एक एक्सपेंशन स्लॉट में प्लग किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "145",
                    section: "misc",
                    question_en: "<p>15. In least frequently used page - replacement algorithm of operating system, when a page must be replaced, then which page is chosen ?</p>",
                    question_hi: "<p>15. ऑपरेटिंग सिस्टम के कम से कम बार उपयोग किए जाने वाले पेज-रिप्लेसमेंट एल्गोरिदम (page-replacement algorithm) में, जब किसी पेज को बदलना होता है, तो कौन सा पेज चुना जाता है?</p>",
                    options_en: ["<p>a page with the zero count</p>", "<p>page with the largest count</p>", 
                                "<p>page with the average count</p>", "<p>page with the smallest count</p>"],
                    options_hi: ["<p>शून्य गिनती वाला एक पेज (a page with the zero count)</p>", "<p>सबसे बड़ी संख्या वाला पेज (page with the largest count)</p>",
                                "<p>औसत गिनती वाला पेज (page with the average count)</p>", "<p>सबसे छोटी संख्या वाला पेज (page with the smallest count)</p>"],
                    solution_en: "<p>15.(d) <strong>page with the smallest count. </strong>Two Types of Counting-Based Algorithms - Most Frequently Used (MFU) Algorithm: It replaces the page with a count greater than other pages i.e. which is accessed a maximum number of times in the past. Least Frequently Used (LFU) Algorithm: It replaces the page with a count lesser than other pages i.e. which is accessed a minimum number of times in the past.</p>",
                    solution_hi: "<p>15.(d)<strong> सबसे छोटी संख्या वाला पेज</strong>।<strong> </strong>Counting-Based Algorithms के दो प्रकार है- मोस्ट फ्रिक्वेंटली यूज़्ड (Most Frequently Used) एल्गोरिदम (Algorithm): यह उस पेज को अन्य पेजों की तुलना में अधिक गिनती के साथ बदल देता है यानी जिसे अतीत (past) में अधिकतम संख्या में एक्सेस किया गया है। लीस्ट फ्रिक्वेंटली यूज़्ड (Least Frequently Used) एल्गोरिदम: यह उस पेज को अन्य पेजों की तुलना में कम गिनती के साथ बदल देता है यानी जिस पेज को अतीत (past) में न्यूनतम संख्या में एक्सेस किया गया हो।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "146",
                    section: "misc",
                    question_en: "<p>16. What is the purpose of an email signature?</p>",
                    question_hi: "<p>16. ईमेल हस्ताक्षर (email signature) का उद्देश्य क्या है?</p>",
                    options_en: ["<p>To add decorative elements to the email.</p>", "<p>To encrypt the email for added security.</p>", 
                                "<p>To automatically send a copy of the email to the sender.</p>", "<p>To provide contact information and personal details of the sender.</p>"],
                    options_hi: ["<p>ईमेल में डेकोरेटिव एलिमेंट जोड़ने के लिए। (To add decorative elements to the email)</p>", "<p>अतिरिक्त सुरक्षा के लिए ईमेल को एन्क्रिप्ट करना। (To encrypt the email for added security)</p>",
                                "<p>प्रेषक को ईमेल की एक प्रति स्वचालित रूप से भेजने के लिए। (To automatically send a copy of the email to the sender)</p>", "<p>प्रेषक की संपर्क (contact) जानकारी और व्यक्तिगत विवरण प्रदान करना। (To provide contact information and personal details of the sender)</p>"],
                    solution_en: "<p>16.(d) An <strong>email signature</strong> is a block of text at the end of an email which includes professional contact details and company branding.</p>",
                    solution_hi: "<p>16.(d) <strong>ईमेल हस्ताक्षर </strong>(email signature) ईमेल के अंत में टेक्स्ट का एक ब्लॉक होता है जिसमें पेशेवर संपर्क विवरण (professional contact details) और कंपनी ब्रांडिंग शामिल होती है।</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "147",
                    section: "misc",
                    question_en: "17.  Which architecture connects individual devices directly, with equal responsibilities and powers, and without a central authority? ",
                    question_hi: "17.  निम्नलिखित में से कौन-सा आर्किटेक्चर समान जिम्मेदारियों और शक्तियों के साथ और किसी केंद्रीय प्राधिकरण के बिना व्यक्तिगत उपकरणों को सीधे जोड़ता है? ",
                    options_en: [" Peer-to-Peer architecture", " Distributed architecture", 
                                " Client-Server architecture", " Centralised architecture"],
                    options_hi: [" पीयर-टू-पीयर आर्किटेक्चर (Peer-to-Peer architecture)", " वितरित आर्किटेक्चर (Distributed architecture)",
                                " क्लाइंट-सर्वर आर्किटेक्चर (Client-Server architecture)", " केंद्रीकृत आर्किटेक्चर (Centralised architecture)"],
                    solution_en: "17.(a) Peer-to-Peer (P2P) architecture allows devices to communicate directly with each other, sharing resources and responsibilities equally. Distributed architecture involves multiple interconnected devices or systems working together to achieve a common goal, but it does not necessarily imply equal responsibilities or a lack of central control. Client-Server architecture involves a centralized server providing resources or services to multiple client devices. Centralized architecture relies on a single central authority or server that manages and controls all resources and data.",
                    solution_hi: "17.(a) Peer-to-Peer (P2P) आर्किटेक्चर: यह आर्किटेक्चर डिवाइसों को सीधे आपस में संवाद (communicate) करने की अनुमति देता है, संसाधनों (resources) और जिम्मेदारियों को समान रूप से साझा करता है। Distributed आर्किटेक्चर: इसमें कई इंटरकनेक्टेड डिवाइस या सिस्टम मिलकर एक सामान्य लक्ष्य को प्राप्त करने के लिए काम करते हैं, लेकिन यह समान जिम्मेदारियों या केंद्रीय नियंत्रण की कमी का संकेत नहीं देता। Client-Server आर्किटेक्चर: इसमें एक केंद्रीकृत सर्वर कई क्लाइंट डिवाइसों को संसाधन या सेवाएँ प्रदान करता है। Centralized आर्किटेक्चर: इसमें एक एकल केंद्रीय प्राधिकरण (single central authority ) या सर्वर सभी संसाधनों और डेटा को प्रबंधित और नियंत्रित करता है।",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "148",
                    section: "misc",
                    question_en: "<p>18. How many types of HTTP (HyperText Transfer Protocol) messages are there?</p>",
                    question_hi: "<p>18. HTTP (हाइपर टेक्स्ट ट्रांसफर प्रोटोकॉल) संदेश कितने प्रकार के होते हैं?</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>3</p>", "<p>4</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>3</p>", "<p>4</p>"],
                    solution_en: "<p>18.(b) <strong>2</strong>. HTTP messages are of two types: request and response. Request Message: The request message is sent by the client that consists of a request line, headers, and sometimes a body. Response Message: The response message is sent by the server to the client that consists of a status line, headers, and sometimes a body.</p>",
                    solution_hi: "<p>18.(b) <strong>2</strong>. HTTP मैसेज दो प्रकार के होते हैं: रिक्वेस्ट और रिस्पांस । रिक्वेस्ट मैसेज (Request Message) : रिक्वेस्ट मैसेज क्लाइंट द्वारा भेजा जाता है जिसमें एक रिक्वेस्ट लाइन, हेडर और कभी-कभी एक मुख्य भाग होता है। रिस्पांस मैसेज (Response Message): रिस्पांस मैसेज सर्वर द्वारा क्लाइंट को भेजा जाता है जिसमें एक स्टेटस लाइन, हेडर और कभी-कभी एक बॉडी होती है।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "149",
                    section: "misc",
                    question_en: "<p>19. In Windows 10, _______ allows you to sync the files between your computer and the cloud, so that you can access the files from anywhere.</p>",
                    question_hi: "<p>19. विंडोज़ 10 में, ______आपको अपने कंप्यूटर और क्लाउड के बीच फ़ाइलों को सिंक (sinkj) करने की अनुमति देता है, ताकि आप फ़ाइलों को कहीं से भी एक्सेस कर सकें।</p>",
                    options_en: ["<p>Google Drive</p>", "<p>MyDrive</p>", 
                                "<p>OneDrive</p>", "<p>Class Drive</p>"],
                    options_hi: ["<p>गूगल ड्राइव (Google Drive)</p>", "<p>माईड्राइव (MyDrive)</p>",
                                "<p>वनड्राइव (OneDrive)</p>", "<p>क्लास ड्राइव (Class Drive)</p>"],
                    solution_en: "<p>19.(c) <strong>One Drive</strong>. Google Drive is a cloud-based storage service that enables users to store and access files online.</p>",
                    solution_hi: "<p>19.(c) <strong>वनड्राइव</strong>। गूगल ड्राइव (Google Drive) एक क्लाउड-आधारित स्टोरेज सर्विस है जो यूजर को फ़ाइलों को ऑनलाइन संग्रहीत (store) करने और उन तक पहुंचने (access) में सक्षम बनाती है।</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "150",
                    section: "misc",
                    question_en: "<p>20. JPEG is a commonly used method of lossy compression for digital images. What is the full form of JPEG?</p>",
                    question_hi: "<p>20. JPEG डिजिटल छवियों (digital images) के लिए लोस्सी कम्प्रेशन (lossy compression)<br>का एक सामान्य रूप से इस्तेमाल किया जाने वाला तरीका है। JPEG का पूर्ण रूप क्या है?</p>",
                    options_en: ["<p>Jet Prints Editing Graphics</p>", "<p>Joint Photographic Experts Group</p>", 
                                "<p>Jet Protocol Experts Group</p>", "<p>Joint Pages Enhanced Graphics</p>"],
                    options_hi: ["<p>Jet Prints Editing Graphics</p>", "<p>Joint Photographic Experts Group</p>",
                                "<p>Jet Protocol Experts Group</p>", "<p>Joint Pages Enhanced Graphics</p>"],
                    solution_en: "<p>20.(b) JPEG stands for &ldquo;Joint Photographic Experts Group&rdquo;. It\'s a standard image format for containing lossy and compressed image data. Despite the huge reduction in file size, JPEG images maintain reasonable image quality.</p>",
                    solution_hi: "<p>20.(b) JPEG का अर्थ है \"Joint Photographic Experts Group&rdquo;\"। यह lossy compression छवि डेटा (digital image) रखने के लिए एक मानक छवि प्रारूप (standard image format) है। फ़ाइल आकार में भारी कमी के बावजूद, JPEG छवियां (images) उचित छवि गुणवत्ता बनाए रखती (reasonable image quality)हैं।</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>