<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. 72 is related to 96 following a certain logic. Following the same logic, 66 is related to 88. Which of the following numbers is related to 104 using the same logic ? <br>(<strong>NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding / subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>1. एक निश्चित तर्क का अनुसरण करते हुए 72, 96 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 66, 88 से संबंधित है। उसी तर्क का उपयोग करते हुए निम्नलिखित में से कौन सी संख्या 104 से संबंधित है ?&nbsp;</p>\n<p>(<strong>ध्यान दें</strong>: संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>80</p>", "<p>77</p>", 
                                "<p>78</p>", "<p>75</p>"],
                    options_hi: ["<p>80</p>", "<p>77</p>",
                                "<p>78</p>", "<p>75</p>"],
                    solution_en: "<p>1.(c) <strong>Logic</strong> :- The ratio between 1st and 2nd number = 3 : 4<br>(72, 96) : - (72 : 96) = 3 : 4<br>(66, 88) :- (66 : 88) = 3 : 4<br>Similarly,<br>(78 , 104) :- (78 : 104) = 3 : 4</p>",
                    solution_hi: "<p>1.(c) <strong>तर्क</strong> :- पहली और दूसरी संख्या के बीच का अनुपात = 3 : 4<br>(72, 96) : - (72 : 96) = 3 : 4<br>(66, 88) :- (66 : 88) = 3 : 4<br>इसी प्रकार, <br>(78 , 104) :- (78 : 104) = 3 : 4</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the option in which the given figure X is embedded (i.e. contains figure X in the same form).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593108.png\" alt=\"rId4\" width=\"107\" height=\"121\"></p>",
                    question_hi: "<p>2. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति x सन्निहित है (अर्थात आकृति x उसी रूप में समाहित है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593108.png\" alt=\"rId4\" height=\"121\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593212.png\" alt=\"rId5\" height=\"95\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593321.png\" alt=\"rId6\" height=\"95\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593427.png\" alt=\"rId7\" height=\"98\"></p>", "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593525.png\" alt=\"rId8\" height=\"95\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593212.png\" alt=\"rId5\" height=\"95\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593321.png\" alt=\"rId6\" height=\"95\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593427.png\" alt=\"rId7\" height=\"98\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593525.png\" alt=\"rId8\" height=\"95\"></p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593636.png\" alt=\"rId9\" height=\"100\"></p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593636.png\" alt=\"rId9\" height=\"100\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Three of the following four options are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?<br>(<strong>NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 - Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>3. निम्नलिखित चार विकल्पों में से तीन किसी न किसी रूप में समान हैं और इस प्रकार, एक समूह बनाते हैं। कौन-सा विकल्प उस समूह से संबंधित नहीं है?<br>(<strong>ध्यान दें</strong> : संख्याओं को उनके घटक अंकों में तोड़े बिना, संक्रियाएं पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लें-संख्या 13 में की जाने वाली संक्रिया, जैसे कि जोड़ना/घटाना/गुणा करना केवल 13 में किया जा सकता है। 13 को अंक 1 और 3 में तोड़ना और फिर 1 और 3 के साथ गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>68 &ndash; 54 &ndash; 94</p>", "<p>36 &ndash; 88 &ndash; 80</p>", 
                                "<p>23 &ndash; 56 - 51</p>", "<p>34 &ndash; 56 &ndash; 62</p>"],
                    options_hi: ["<p>68 &ndash; 54 &ndash; 94</p>", "<p>36 &ndash; 88 &ndash; 80</p>",
                                "<p>23 &ndash; 56 &ndash; 51</p>", "<p>34 &ndash; 56 &ndash; 62</p>"],
                    solution_en: "<p>3.(a)<br><strong>Logic:-</strong> (3rd no. -&nbsp; 1st no.) &times; 2 = 2nd no.<br>(36 - 88 - 80) :- (80 - 36) &times; 2 &rArr; 44 &times; 2 = 88<br>(23 - 56 - 51) :- (51 - 23) &times; 2 &rArr; 28 &times; 2 = 56<br>(34 - 56 - 62) :- (62 - 34) &times; 2 &rArr; 28 &times; 2 = 56<br>But<br>(68 - 54 - 94) :- (94 - 68) &times; 2 &rArr; 26 &times; 2 = 52 &ne; 54</p>",
                    solution_hi: "<p>3.(a)<br><strong>तर्क:<math display=\"inline\"><mo>-</mo></math></strong> (तीसरी संख्या - पहली संख्या) &times; 2 = दूसरी संख्या <br>(36 - 88 - 80) :- (80 - 36) &times; 2 &rArr; 44 &times; 2 = 88<br>(23 - 56 - 51) :- (51 - 23) &times; 2 &rArr; 28 &times; 2 = 56<br>(34 - 56 - 62) :- (62 - 34) &times; 2 &rArr; 28 &times; 2 = 56<br>लेकिन <br>(68 - 54 - 94) :- (94 - 68) &times; 2 &rArr; 26 &times; 2 = 52 &ne; 54</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. How many triangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593813.png\" alt=\"rId10\" width=\"147\" height=\"133\"></p>",
                    question_hi: "<p>4. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593813.png\" alt=\"rId10\" width=\"147\" height=\"133\"></p>",
                    options_en: ["<p>14</p>", "<p>12</p>", 
                                "<p>9</p>", "<p>16</p>"],
                    options_hi: ["<p>14</p>", "<p>12</p>",
                                "<p>9</p>", "<p>16</p>"],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593976.png\" alt=\"rId11\" width=\"183\" height=\"170\"><br>There are 16 triangle<br>ABD, ACD, DEF, CDE, ADE , GJH , JHI, HMF, BHF, BDH, DHF,HIG, MLN, LNO , NOQ , PQO.</p>",
                    solution_hi: "<p>4.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182593976.png\" alt=\"rId11\" width=\"183\" height=\"170\"><br>16 त्रिभुज हैं<br>ABD, ACD, DEF, CDE, ADE , GJH , JHI, HMF, BHF, BDH, DHF,HIG, MLN, LNO , NOQ , PQO.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a code language, &lsquo;GOOD&rsquo; is written as &lsquo;HMPB&rsquo;, and &lsquo;HALL&rsquo; is written as &lsquo;IYMJ&rsquo;. How will &lsquo;TOOL&rsquo; be written in that language ?</p>",
                    question_hi: "<p>5. एक कूट भाषा में &lsquo;GOOD&rsquo; को &lsquo;HMPB&rsquo; के रूप में लिखा जाता है और &lsquo;HALL&rsquo; को &lsquo;IYMJ&rsquo; के रूप में लिखा जाता है। इसी कूट भाषा में &lsquo;TOOL&rsquo; को कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>UPJM</p>", "<p>JUMP</p>", 
                                "<p>UMPJ</p>", "<p>PUMJ</p>"],
                    options_hi: ["<p>UPJM</p>", "<p>JUMP</p>",
                                "<p>UMPJ</p>", "<p>PUMJ</p>"],
                    solution_en: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594178.png\" alt=\"rId12\" height=\"100\">,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594273.png\" alt=\"rId13\" height=\"100\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594396.png\" alt=\"rId14\" height=\"100\"></p>",
                    solution_hi: "<p>5.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594178.png\" alt=\"rId12\" height=\"100\">,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594273.png\" alt=\"rId13\" height=\"100\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594396.png\" alt=\"rId14\" height=\"100\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language, \'god is great\' is coded as \'tg so rf\' and \'we love god\' is coded as \'ae rf xd\'. How is \'god\' coded in that language ?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में, \'god is great\' को \'tg so rf\' के रूप में कूटबद्ध किया जाता है और \'we love god\' को \'ae rf xd\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'god\' को किस रूप में कूटबद्ध किया जाता है ?</p>",
                    options_en: ["<p>tg</p>", "<p>xd</p>", 
                                "<p>ae</p>", "<p>rf</p>"],
                    options_hi: ["<p>tg</p>", "<p>xd</p>",
                                "<p>ae</p>", "<p>rf</p>"],
                    solution_en: "<p>6.(d) god is great &rarr; tg so rf&hellip;..(i)<br>we love god &rarr; ae rf xd&hellip;..(ii)<br>From (i) and (ii) &lsquo;god&rsquo; and &lsquo;rf&rsquo; are common. The code of &lsquo;god&rsquo; = &lsquo;rf&rsquo;.</p>",
                    solution_hi: "<p>6.(d) god is great &rarr; tg so rf&hellip;..(i)<br>we love god &rarr; ae rf xd&hellip;..(ii)<br>(i) और (ii) से &lsquo;god\' और \'rf\' उभय-निष्ठ हैं। \'god\' का कूट = \'rf\'.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What should come in place of ? in the given series based on the English alphabetical order ?<br>JMP UZE FMT QZI ?</p>",
                    question_hi: "<p>7. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में ?\' के स्थान पर क्या आना चाहिए?<br>JMP UZE FMT QZI ?</p>",
                    options_en: ["<p>SLP</p>", "<p>XHN</p>", 
                                "<p>BMX</p>", "<p>LNP</p>"],
                    options_hi: ["<p>SLP</p>", "<p>XHN</p>",
                                "<p>BMX</p>", "<p>LNP</p>"],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594536.png\" alt=\"rId15\" width=\"331\" height=\"120\"></p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594536.png\" alt=\"rId15\" width=\"331\" height=\"120\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Which figure should replace the question mark (?) if the following series were to be continued ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594659.png\" alt=\"rId16\" width=\"458\" height=\"100\"></p>",
                    question_hi: "<p>8. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594659.png\" alt=\"rId16\" height=\"100\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594828.png\" alt=\"rId17\" height=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594929.png\" alt=\"rId18\" height=\"100\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595030.png\" alt=\"rId19\" height=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595131.png\" alt=\"rId20\" height=\"100\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594828.png\" alt=\"rId17\" height=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182594929.png\" alt=\"rId18\" height=\"100\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595030.png\" alt=\"rId19\" height=\"100\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595131.png\" alt=\"rId20\" height=\"100\"></p>"],
                    solution_en: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595030.png\" alt=\"rId19\" height=\"100\"></p>",
                    solution_hi: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595030.png\" alt=\"rId19\" height=\"100\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. What should come in place of &lsquo;?&rsquo; in the given series ? <br>33 32 35 34 37 ?</p>",
                    question_hi: "<p>9. दी गई श्रृंखला में प्रश्न चिह्न \'?\' के स्थान पर क्या आना चाहिए ?<br>33 32 35 34 37 ?</p>",
                    options_en: ["<p>40</p>", "<p>38</p>", 
                                "<p>37</p>", "<p>36</p>"],
                    options_hi: ["<p>40</p>", "<p>38</p>",
                                "<p>37</p>", "<p>36</p>"],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595347.png\" alt=\"rId22\" height=\"65\"></p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595347.png\" alt=\"rId22\" height=\"65\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. The second number in the given number-pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) is/are followed in all the number-pairs, except one. Find that odd number-pair. <br>(NOTE : The relation should be found without breaking down the numbers into its constituent digits)</p>",
                    question_hi: "<p>10. दिए गए संख्या-युग्मों में दूसरी संख्या, पहली संख्या पर कुछ गणितीय संक्रिया/संक्रियाएँ करके प्राप्त की गई है। एक को छोड़कर सभी संख्या-युग्मों में समान संक्रिया/संक्रियाएं की गई है/हैं। वह असंगत संख्या-युग्म ज्ञात कीजिए।<br>(नोट: संख्याओं को उसके घटक अंकों में विभाजित किए बिना संबंध ज्ञात किया जाना चाहिए)</p>",
                    options_en: ["<p>784 : 961</p>", "<p>1764 : 1849</p>", 
                                "<p>1156 : 1225</p>", "<p>1521 : 1600</p>"],
                    options_hi: ["<p>784 : 961</p>", "<p>1764 : 1849</p>",
                                "<p>1156 : 1225</p>", "<p>1521 : 1600</p>"],
                    solution_en: "<p>10.(a) <strong>Logic</strong> : square of consecutive number<br>1764 : 1849 :- 42<sup>2</sup> : 43<sup>2</sup><br>1156 : 1225 :- 34<sup>2</sup> : 35<sup>2</sup><br>1521 : 1600 :- 39<sup>2</sup> : 40<sup>2</sup><br>But<br>784 : 961 :- 28<sup>2</sup> : 31<sup>2</sup></p>",
                    solution_hi: "<p>10.(a) <strong>तर्क</strong> : क्रमागत संख्याओं का वर्ग<br>1764 : 1849 :- 42<sup>2</sup> : 43<sup>2</sup><br>1156 : 1225 :- 34<sup>2</sup> : 35<sup>2</sup><br>1521 : 1600 :- 39<sup>2</sup> : 40<sup>2</sup><br>लेकिन<br>784 : 961 :- 28<sup>2</sup> : 31<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Read the given statements and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follow(s) from the statements.&nbsp;<br><strong>Statements:</strong> <br>No pillow is a cushion. <br>All cushions are sheets. <br>No sheet is a blanket. <br><strong>Conclusions:</strong> <br>(I) No cushion is a blanket. <br>(II) Some pillows are sheets.</p>",
                    question_hi: "<p>11. दिए गए कथनों और निष्कर्षों को ध्यान पूर्वक पढ़ें। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही यह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, निर्णय लें कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन:</strong> <br>कोई भी तकिया, गद्दा नहीं है। <br>सभी गद्दे, चादर हैं। <br>कोई भी चादर, कंबल नहीं है। <br><strong>निष्कर्ष:</strong> <br>(I) कोई भी गद्दा , कंबल नहींहै। <br>(II) कुछ तकिए, चादर हैं।</p>",
                    options_en: ["<p>Only conclusion I follows</p>", "<p>Only conclusion II follows</p>", 
                                "<p>None of the conclusions follow</p>", "<p>Both conclusions I and II follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष I अनुसरण करता है</p>", "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                                "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>", "<p>दोनों निष्कर्ष I और II अनुसरण करते हैं</p>"],
                    solution_en: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595464.png\" alt=\"rId23\" width=\"391\" height=\"89\"><br>Only conclusion I follow.</p>",
                    solution_hi: "<p>11.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595594.png\" alt=\"rId24\" height=\"89\"><br>केवल निष्कर्ष I अनुसरण करता है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below.<br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/consonants/vowels in the word.)<br>Pleasure : Delight</p>",
                    question_hi: "<p>12. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है।<br>(शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>आनंद : प्रसन्नता</p>",
                    options_en: ["<p>Silly : Serious</p>", "<p>Rain : Sky</p>", 
                                "<p>Demolish : Destroy</p>", "<p>Knowledge : Book</p>"],
                    options_hi: ["<p>मूर्ख : गंभीर</p>", "<p>वर्षा : आकाश</p>",
                                "<p>ध्वस्त करना : नष्ट करना</p>", "<p>ज्ञान : किताब</p>"],
                    solution_en: "<p>12.(c) As Pleasure and Delight are synonyms of each other, similarly Demolish and Destroy are synonyms of each other.</p>",
                    solution_hi: "<p>12.(c) जैसे आनंद और प्रसन्नता एक दूसरे के पर्यायवाची हैं, वैसे ही ध्वस्त करना और नष्ट करना एक दूसरे के पर्यायवाची हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain language, <br>A &amp; B means A is the wife of B, <br>A &divide; B means A is the daughter of B, <br>A &times; B means A is the son of B, <br>A + B means A is the sister of B and <br>A @ B means A is the father of B. <br>P &amp; Q @ L + T &amp; U @ V<br>Based on the above, how is Q related to V ?</p>",
                    question_hi: "<p>13. एक निश्चित भाषा में,<br>A &amp; B का अर्थ A, B की पत्नी है, <br>A &divide; B का अर्थ A, B की बेटी है, <br>A &times; B का अर्थ A, B का बेटा है, <br>A + B का अर्थ A, B की बहन है और <br>A @ B का अर्थ A, B का पिता है।<br>P &amp; Q @ L + T &amp; U @ V<br>उपरोक्त के आधार पर, Q का V से क्या संबंध है ?</p>",
                    options_en: ["<p>Mother\'s father</p>", "<p>Sister</p>", 
                                "<p>Mother</p>", "<p>Father\'s mother</p>"],
                    options_hi: ["<p>माँ के पिता</p>", "<p>बहन</p>",
                                "<p>माँ</p>", "<p>पिता की माँ</p>"],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595699.png\" alt=\"rId25\" width=\"217\" height=\"178\"><br>Q is the father of V&rsquo;s mother.</p>",
                    solution_hi: "<p>13.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182595699.png\" alt=\"rId25\" width=\"217\" height=\"178\"><br>Q, V की माँ का पिता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the Venn diagram that best illustrates the relationship between the following classes.<br>Dogs, Cats, Labradors</p>",
                    question_hi: "<p>14. उस वेन आरेख का चयन कीजिए जो निम्नलिखित वर्गों के बीच संबंध को सर्वोत्तम रूप से दर्शाता है।<br>कुत्ता, बिल्ली, लैब्राडोर</p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-688c8719-7fff-2ecc-cfb1-ac36cc0b7fd5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeD2ApJ0mCMsGtZZH-BhKmM2I2tcpt0COeKNjpI-Wg2kknHfBF-fWVibIQI0Qidu8GNk4Q3KKipAvOJr_Uexor2Gmzlux62c8IT9S31cu7fn7j2lHumJQYxByQH7y1vNLIVRPb1gg?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"150\" height=\"94\"></strong></p>", "<p><strong id=\"docs-internal-guid-3d0f68c1-7fff-56f5-3479-f4ffe634b778\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdMssDfye3Y5Axzo0sgXIOBm6vEdZMkgl3ZcdoRmI-ZouHq-PtKD1ypHWKiusVNBS4B_ZAf_Aupx_VXtMeuAiJOwvlU95TnecSyE8m_wTzVVf3qedwWd1zWaGY7E62uORpq5f2V3w?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"104\" height=\"99\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-6097a0a6-7fff-7932-eb4c-636c849490f0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcAhAfWYe_qUCNla4RXGYPe9kMl0fF6qjvUe3n6CM27W2J8Q6Y4FORaDevbaHbNArTE7na3dA9esMarcWMJ5hnM-PJsqjbHlPBTtJkwwjIQOUMEjKrXndz2OK0AqdajfpFq2hpj0g?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"123\" height=\"121\"></strong></p>", "<p><strong id=\"docs-internal-guid-a7f59a69-7fff-9d69-5fd1-b0cb0b1ece76\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc34cSpGMvIEcoETi9cqEJIxqbxTacF3eecS4BgvDymk_dejecIqfmZLHJ0cbuU127gFAgTyEM8DoargBny6p4qBQEgoEVQz-LqClVTskBUDRnhCKbWWWuglXD-FWpfFN1XECHwIw?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"174\" height=\"93\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-4afd26ef-7fff-e052-23aa-14b6178f673f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeD2ApJ0mCMsGtZZH-BhKmM2I2tcpt0COeKNjpI-Wg2kknHfBF-fWVibIQI0Qidu8GNk4Q3KKipAvOJr_Uexor2Gmzlux62c8IT9S31cu7fn7j2lHumJQYxByQH7y1vNLIVRPb1gg?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"150\" height=\"94\"></strong></p>", "<p><strong id=\"docs-internal-guid-e5b98745-7fff-f9dd-74a6-055813aa8c80\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdMssDfye3Y5Axzo0sgXIOBm6vEdZMkgl3ZcdoRmI-ZouHq-PtKD1ypHWKiusVNBS4B_ZAf_Aupx_VXtMeuAiJOwvlU95TnecSyE8m_wTzVVf3qedwWd1zWaGY7E62uORpq5f2V3w?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"104\" height=\"99\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-a9663b6b-7fff-1524-393d-59fd6e898666\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcAhAfWYe_qUCNla4RXGYPe9kMl0fF6qjvUe3n6CM27W2J8Q6Y4FORaDevbaHbNArTE7na3dA9esMarcWMJ5hnM-PJsqjbHlPBTtJkwwjIQOUMEjKrXndz2OK0AqdajfpFq2hpj0g?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"123\" height=\"121\"></strong></p>", "<p><strong id=\"docs-internal-guid-bea3ea85-7fff-3f44-e3d3-cdd9ff5b6edb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc34cSpGMvIEcoETi9cqEJIxqbxTacF3eecS4BgvDymk_dejecIqfmZLHJ0cbuU127gFAgTyEM8DoargBny6p4qBQEgoEVQz-LqClVTskBUDRnhCKbWWWuglXD-FWpfFN1XECHwIw?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"174\" height=\"93\"></strong></p>"],
                    solution_en: "<p>14.(a)<br><strong id=\"docs-internal-guid-dd2e7964-7fff-6134-6794-1d3d357c2fde\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeD2ApJ0mCMsGtZZH-BhKmM2I2tcpt0COeKNjpI-Wg2kknHfBF-fWVibIQI0Qidu8GNk4Q3KKipAvOJr_Uexor2Gmzlux62c8IT9S31cu7fn7j2lHumJQYxByQH7y1vNLIVRPb1gg?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"150\" height=\"94\"></strong></p>",
                    solution_hi: "<p>14.(a)<br><strong id=\"docs-internal-guid-dd2e7964-7fff-6134-6794-1d3d357c2fde\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeD2ApJ0mCMsGtZZH-BhKmM2I2tcpt0COeKNjpI-Wg2kknHfBF-fWVibIQI0Qidu8GNk4Q3KKipAvOJr_Uexor2Gmzlux62c8IT9S31cu7fn7j2lHumJQYxByQH7y1vNLIVRPb1gg?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"150\" height=\"94\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Two different positions of the same dice with faces 9, 2, 5, 3, 8 and 6 are shown below. Select the number that will be on the face opposite to the one having 2.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182596324.png\" alt=\"rId30\" width=\"249\" height=\"113\"></p>",
                    question_hi: "<p>15. 9, 2, 5, 3, 8 और 6 फलकों वाले एक ही पासे की दो अलग-अलग स्थितियाँ नीचे दिखाई गई हैं। उस संख्या का चयन कीजिए जो 2 संख्&zwj;या वाले फलक के विपरीत फलक पर होगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182596324.png\" alt=\"rId30\" width=\"249\" height=\"113\"></p>",
                    options_en: ["<p>6</p>", "<p>9</p>", 
                                "<p>3</p>", "<p>5</p>"],
                    options_hi: ["<p>6</p>", "<p>9</p>",
                                "<p>3</p>", "<p>5</p>"],
                    solution_en: "<p>15.(c)&nbsp;From both the dice the opposite face are <br>2 &harr; 3 , 5 &harr; 6, 9 &harr; 8</p>",
                    solution_hi: "<p>15.(c)&nbsp;दोनों पासों के विपरीत फलक हैं <br>2 &harr; 3 , 5 &harr; 6, 9 &harr; 8</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. In a certain code language, &lsquo;COOL&rsquo; is written as &lsquo;45&rsquo; and &lsquo;TREE&rsquo; is written as &lsquo;48&rsquo;. How will &lsquo;WELL&rsquo; be written in that language ?</p>",
                    question_hi: "<p>16. एक निश्चित कूट भाषा में \'COOL\' को \'45\' और \'TREE\' को \'48\' के रूप में लिखा जाता है। उस भाषा में \'WELL\' कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>46</p>", "<p>51</p>", 
                                "<p>36</p>", "<p>52</p>"],
                    options_hi: ["<p>46</p>", "<p>51</p>",
                                "<p>36</p>", "<p>52</p>"],
                    solution_en: "<p>16.(d) <strong>Logic</strong> :- (Sum of the place value of letters)<br>COOL :- (3 + 15 + 15 + 12) = 45<br>TREE :- (20 + 18 + 5 + 5) = 48<br>Similarly,<br>WELL :- (23 + 5 + 12 + 12) = 52</p>",
                    solution_hi: "<p>16.(d) <strong>तर्क</strong> :- (अक्षर के स्थानीय मान का योग) <br>COOL :- (3 + 15 + 15 + 12) = 45<br>TREE :- (20 + 18 + 5 + 5) = 48<br>इसी प्रकार,<br>WELL :- (23 + 5 + 12 + 12) = 52</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. A, B, L, N, S, T and Z are seven candidates sitting around a circular table, facing the centre (but not necessarily in the same order), for a group discussion. L is sitting third to the left of N. N is sitting exactly between T and B. Z is an immediate neighbour of B and is sitting third to the left of S. Z is also an immediate neighbour of A. Who is sitting second to the left of N ?</p>",
                    question_hi: "<p>17. सात उम्मीदवार A, B, L, N, S, T और Z समूह चर्चा के लिए एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। L, N के बाईं ओर तीसरे स्थान पर बैठा है। T और B के ठीक बीच में N बैठा है। Z, B का निकटतम पड़ोसी है और S के बाईं ओर तीसरे स्थान पर बैठा है। Z भी A का निकटतम पड़ोसी है। N के बाईं ओर दूसरे स्थान पर कौन बैठा है ?</p>",
                    options_en: ["<p>S</p>", "<p>L</p>", 
                                "<p>A</p>", "<p>Z</p>"],
                    options_hi: ["<p>S</p>", "<p>L</p>",
                                "<p>A</p>", "<p>Z</p>"],
                    solution_en: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182596521.png\" alt=\"rId31\" width=\"185\" height=\"176\"></p>",
                    solution_hi: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182596521.png\" alt=\"rId31\" width=\"185\" height=\"176\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. If \'A\' stands for \'&divide;\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for \'-\', then the resultant of which of the following will be 338 ?</p>",
                    question_hi: "<p>18. यदि &lsquo;A&rsquo; का अर्थ &lsquo;&divide;&rsquo;, &lsquo;B&rsquo; का अर्थ &lsquo;&times;&rsquo;, &lsquo;C&rsquo; का अर्थ &lsquo;+&rsquo; और &lsquo;D&rsquo; का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित में से किसका परिणाम 338 होगा ?</p>",
                    options_en: ["<p>32 B 11 D 80 A 4 C 6</p>", "<p>32 C 11 D 80 A 4 B 6</p>", 
                                "<p>32 D 11 B 80 A 4 C 6</p>", "<p>32 A 11 D 80 B 4 C 6</p>"],
                    options_hi: ["<p>32 B 11 D 80 A 4 C 6</p>", "<p>32 C 11 D 80 A 4 B 6</p>",
                                "<p>32 D 11 B 80 A 4 C 6</p>", "<p>32 A 11 D 80 B 4 C 6</p>"],
                    solution_en: "<p>18.(a) After checking all the options one by one, only option (a) satisfies<br>32 B 11 D 80 A 4 C 6<br>After interchanging the letter with sign we get<br>32 &times; 11 - 80 &divide;&nbsp;4 + 6<br>352 - 20 + 6 = 338</p>",
                    solution_hi: "<p>18.(a) सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (a) ही संतुष्ट करता है<br>32 B 11 D 80 A 4 C 6<br>अक्षर को चिह्न से बदलने पर हमें प्राप्त होता है<br>32 &times; 11 - 80 &divide;&nbsp;4 + 6<br>352 - 20 + 6 = 338</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the correct option that indicates the arrangement of the following words in a logical and meaningful order.<br>1. Manufacturing<br>2. Raw material<br>3. Consumption<br>4. Material processing<br>5. Distribution</p>",
                    question_hi: "<p>19. उस सही विकल्प का चयन कीजिए जो नीचे दिए गए शब्दों के एक तार्किक क्रम-विन्यास को दर्शाता है।<br>1. उत्पादन<br>2. कच्चा माल<br>3. खपत<br>4. सामग्री प्रसंस्करण<br>5. वितरण</p>",
                    options_en: ["<p>4, 5, 2, 1, 3</p>", "<p>4, 2, 1, 5, 3</p>", 
                                "<p>2, 1, 4, 5, 3</p>", "<p>2, 4, 1, 5, 3</p>"],
                    options_hi: ["<p>4, 5, 2, 1, 3</p>", "<p>4, 2, 1, 5, 3</p>",
                                "<p>2, 1, 4, 5, 3</p>", "<p>2, 4, 1, 5, 3</p>"],
                    solution_en: "<p>19.(d) The correct order is <br>Raw material(2) &rarr; Material processing(4) &rarr; Manufacturing(1) &rarr; Distribution(5) &rarr; Consumption(3)</p>",
                    solution_hi: "<p>19.(d) The correct order is <br>कच्चा माल(2) &rarr; सामग्री प्रसंस्करण(4) &rarr; उत्पादन(1) &rarr; वितरण(5) &rarr; खपत(3)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. If 18 August 2000 was Friday, then what was the day of the week on 21 August 2005 ?</p>",
                    question_hi: "<p>20. यदि 18 अगस्त 2000 को शुक्रवार था, तो 21 अगस्त 2005 को सप्ताह का कौन-सा दिन रहा होगा ?</p>",
                    options_en: ["<p>Wednesday</p>", "<p>Sunday</p>", 
                                "<p>Monday</p>", "<p>Friday</p>"],
                    options_hi: ["<p>बुधवार</p>", "<p>रविवार</p>",
                                "<p>सोमवार</p>", "<p>शुक्रवार</p>"],
                    solution_en: "<p>20.(b) 18 August 2000 was Friday. On moving to 2005 the number of odd days are <br>+1 + 1 + 1 + 2 + 1 = 6. We have reached till 18 August 2005, but we have to reach till 21 August, the number of days between = 3. Total number of days = 6 + 3 = 9.On dividing 9 by 7 the remainder = 2. Friday + 2 = Sunday.</p>",
                    solution_hi: "<p>20.(b) 18 अगस्त 2000 को शुक्रवार था. 2005 में जाने पर विषम दिनों की संख्या है =<br>+1 + 1 + 1 + 2 + 1 = 6. हम 18 अगस्त 2005 तक पहुँच चुके हैं, लेकिन हमें 21 अगस्त तक पहुँचना है, बीच के दिनों की संख्या = 3. कुल दिनों की संख्या = 6 + 3 = 9. 9 को 7 से विभाजित करने पर शेषफल = 2. शुक्रवार + 2 = रविवार।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Three of the following four are alike in a certain way and thus form a group. Which is the one that does NOT belong to that group?</p>\n<p>(<strong>Note</strong>: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster.)</p>",
                    question_hi: "<p>21. निम्नलिखित चार अक्षर-समूहों में से तीन अक्षर-समूह एक निश्चित प्रकार से समान हैं और इस प्रकार एक समूह बनाते हैं। वह कौन-सा अक्षर-समूह है जो उस समूह से संबंधित नहीं है? <br>(<strong>ध्यान दें</strong> : असंगत अक्षर-समूह, उस अक्षर-समूह में व्यंजनों/स्वरों की संख्या या उनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: ["<p>RUX</p>", "<p>KMP</p>", 
                                "<p>NQT</p>", "<p>BEH</p>"],
                    options_hi: ["<p>RUX</p>", "<p>KMP</p>",
                                "<p>NQT</p>", "<p>BEH</p>"],
                    solution_en: "<p>21.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182596640.png\" alt=\"rId32\" height=\"65\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182596780.png\" alt=\"rId33\" height=\"65\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182596955.png\" alt=\"rId34\" height=\"65\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182597087.png\" alt=\"rId35\" height=\"65\"></p>",
                    solution_hi: "<p>21.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182596640.png\" alt=\"rId32\" height=\"65\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182596780.png\" alt=\"rId33\" height=\"65\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182596955.png\" alt=\"rId34\" height=\"65\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182597087.png\" alt=\"rId35\" height=\"65\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. A paper is folded and cut as shown below. How will it appear when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182597280.png\" alt=\"rId36\" height=\"121\"></p>",
                    question_hi: "<p>22. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182597280.png\" alt=\"rId36\" height=\"121\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182597650.png\" alt=\"rId37\" height=\"121\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182597830.png\" alt=\"rId38\" height=\"121\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182598006.png\" alt=\"rId39\" height=\"121\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182598269.png\" alt=\"rId40\" height=\"121\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182597650.png\" alt=\"rId37\" height=\"121\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182597830.png\" alt=\"rId38\" height=\"121\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182598006.png\" alt=\"rId39\" height=\"121\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182598269.png\" alt=\"rId40\" height=\"121\"></p>"],
                    solution_en: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182597650.png\" alt=\"rId37\" height=\"121\"></p>",
                    solution_hi: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182597650.png\" alt=\"rId37\" height=\"121\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. HJKN is related to EGHK in a certain way based on the English alphabetical order. In the same way, QSTW is related to NPQT. To which of the following is IKLO related, following the same logic ?</p>",
                    question_hi: "<p>23. अंग्रेजी वर्णमाला क्रम के आधार पर HJKN, EGHK से एक निश्चित प्रकार से संबंधित है। उसी प्रकार, QSTW, NPQT से संबंधित है। समान तर्क का अनुसरण करते हुए, IKLO निम्नलिखित में से किससे संबंधित है ?</p>",
                    options_en: ["<p>FHJL</p>", "<p>FHIK</p>", 
                                "<p>FGIL</p>", "<p>FHIL</p>"],
                    options_hi: ["<p>FHJL</p>", "<p>FHIK</p>",
                                "<p>FGIL</p>", "<p>FHIL</p>"],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182598501.png\" alt=\"rId41\" height=\"100\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182598723.png\" alt=\"rId42\" height=\"100\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182598878.png\" alt=\"rId43\" height=\"100\"></p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182598501.png\" alt=\"rId41\" height=\"100\">,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182598723.png\" alt=\"rId42\" height=\"100\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182598878.png\" alt=\"rId43\" height=\"100\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599094.png\" alt=\"rId44\"></p>",
                    question_hi: "<p>24. दी गई आकृति के उस सही दर्पण प्रतिबिंब का चयन कीजिए जो दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599094.png\" alt=\"rId44\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599209.png\" alt=\"rId45\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599314.png\" alt=\"rId46\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599414.png\" alt=\"rId47\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599499.png\" alt=\"rId48\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599209.png\" alt=\"rId45\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599314.png\" alt=\"rId46\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599414.png\" alt=\"rId47\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599499.png\" alt=\"rId48\"></p>"],
                    solution_en: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599414.png\" alt=\"rId47\"></p>",
                    solution_hi: "<p>24.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599414.png\" alt=\"rId47\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the option that represents the letters that, when sequentially placed from left to right in the following blanks, will complete the letter-series.<br>P_ _SH_BK_HPB_S_PB_ _H</p>",
                    question_hi: "<p>25. उस विकल्प का चयन कीजिए जो उन अक्षरों का प्रतिनिधित्व करता है, जिन्हें निम्नलिखित रिक्त स्थानों में बाएं से दाएं क्रमिक रूप से रखने पर अक्षर-श्रृंखला पूर्ण हो जाएगी।<br>P_ _SH_BK_HPB_S_PB_ _H</p>",
                    options_en: ["<p>BKPSKHKS</p>", "<p>BKPSKSKS</p>", 
                                "<p>KBSKPHKS</p>", "<p>KSBKPHKS</p>"],
                    options_hi: ["<p>BKPSKHKS</p>", "<p>BKPSKSKS</p>",
                                "<p>KBSKPHKS</p>", "<p>KSBKPHKS</p>"],
                    solution_en: "<p>25.(a) P<strong><span style=\"text-decoration: underline;\">BK</span></strong>SH / <strong><span style=\"text-decoration: underline;\">P</span></strong>BK<strong><span style=\"text-decoration: underline;\">S</span></strong>H / PB<strong><span style=\"text-decoration: underline;\">K</span></strong>S<strong><span style=\"text-decoration: underline;\">H</span></strong> / PB<strong><span style=\"text-decoration: underline;\">KS</span></strong>H</p>",
                    solution_hi: "<p>25.(a) P<strong><span style=\"text-decoration: underline;\">BK</span></strong>SH / <strong><span style=\"text-decoration: underline;\">P</span></strong>BK<strong><span style=\"text-decoration: underline;\">S</span></strong>H / PB<strong><span style=\"text-decoration: underline;\">K</span></strong>S<strong><span style=\"text-decoration: underline;\">H</span></strong> / PB<strong><span style=\"text-decoration: underline;\">KS</span></strong>H</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which album earned Ricky Kej his third Grammy Award?</p>",
                    question_hi: "<p>26. रिकी केज ने अपना तीसरा ग्रैमी पुरस्कार निम्नलिखित में से किस एल्बम के लिए जीता?</p>",
                    options_en: ["<p>Harmony of Elements</p>", "<p>Divine Tides</p>", 
                                "<p>Melodies of the Universe</p>", "<p>Nature\'s Symphony</p>"],
                    options_hi: ["<p>हार्मोनी ऑफ एलिमेंट्स (Harmony of Elements)</p>", "<p>डिवाइन टाइड्स (Divine Tides)</p>",
                                "<p>मेलोडिस ऑफ द यूनिवर्स (Melodies of the Universe)</p>", "<p>नेचर्स सिम्फनी (Nature\'s Symphony)</p>"],
                    solution_en: "<p>26.(b) <strong>Divine Tides.</strong> Ram Gyan \"Ricky\" Kej is an Indian-American music composer and environmentalist of Indian descent and a three-time Grammy Award winner. The Grammy Awards: Awarded for - Outstanding achievements in the music industry, Country - United States, Presented by - The Recording Academy, First awarded - May 4, 1959.</p>",
                    solution_hi: "<p>26.(b)<strong> डिवाइन टाइड्स (Divine Tides)</strong>। राम ज्ञान \"रिकी\" केज भारतीय मूल के एक भारतीय-अमेरिकी संगीतकार और पर्यावरणविद् हैं और तीन बार ग्रैमी पुरस्कार विजेता हैं। ग्रैमी पुरस्कार: संगीत उद्योग में उत्कृष्ट उपलब्धियों के लिए सम्मानित किया जाता है, देश - संयुक्त राज्य अमेरिका, प्रस्तुतकर्ता - रिकॉर्डिंग अकादमी, प्रथम पुरस्कार - 4 मई, 1959 ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Who was appointed as the Director in the Ministry of Steel with an extended tenure up to March 15, 2026 ?</p>",
                    question_hi: "<p>27. 15 मार्च, 2026 तक विस्तारित कार्यकाल के साथ इस्पात मंत्रालय में निदेशक के रूप में किसे नियुक्त किया गया ?</p>",
                    options_en: ["<p>Nitin Jain</p>", "<p>Rajesh Kumar</p>", 
                                "<p>Sanjay Gupta</p>", "<p>Amitabh Chaudhry</p>"],
                    options_hi: ["<p>नितिन जैन</p>", "<p>राजेश कुमार</p>",
                                "<p>संजय गुप्ता</p>", "<p>अमिताभ चौधरी</p>"],
                    solution_en: "<p>27. (a) <strong>Nitin Jain. </strong>The Ministry of Steel: Headquarter - New Delhi. <strong>Steel Plants and Establishment Year</strong>: Tata Steel Plant (Jamshedpur): 1907. Bhilai Steel Plant (Chhattisgarh): 1959. Rourkela Steel Plant (Odisha): 1959. Durgapur Steel Plant (West Bengal): 1959. Bokaro Steel Plant (Jharkhand): 1964. Visakhapatnam Steel Plant (Andhra Pradesh): 1971. Steel Authority of India Limited (SAIL): 1973</p>",
                    solution_hi: "<p>27.(a) <strong>नितिन जैन। </strong>इस्पात मंत्रालय: मुख्यालय - नई दिल्ली। इस्पात संयंत्र और स्थापना वर्ष: टाटा स्टील प्लांट (जमशेदपुर): 1907। भिलाई स्टील प्लांट (छत्तीसगढ़): 1959। राउरकेला स्टील प्लांट (ओडिशा): 1959। दुर्गापुर स्टील प्लांट (पश्चिम बंगाल): 1959। बोकारो स्टील प्लांट (झारखंड): 1964। विशाखापत्तनम स्टील प्लांट (आंध्र प्रदेश): 1971। स्टील अथॉरिटी ऑफ इंडिया लिमिटेड (SAIL): 1973।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. The wrestler must be at least ___________ years old, and must be sponsored by the national federation, to compete at the World Championships or Olympic Games.</p>",
                    question_hi: "<p>28. विश्व चैंपियनशिप या ओलंपिक खेलों में भाग लेने के लिए पहलवान (wrestler) की आयु कम से कम _________ वर्ष होनी चाहिए, और उसे राष्ट्रीय महासंघ द्वारा प्रायोजित किया जाना चाहिए।</p>",
                    options_en: ["<p>25</p>", "<p>21</p>", 
                                "<p>17</p>", "<p>23</p>"],
                    options_hi: ["<p>25</p>", "<p>21</p>",
                                "<p>17</p>", "<p>23</p>"],
                    solution_en: "<p>28.(c) <strong>17.</strong> There are eight weight divisions in men&rsquo;s international wrestling and six for women. Athletes are weighed-in prior to the competition, and must be at the weight level or below in order to participate in the competition.</p>",
                    solution_hi: "<p>28.(c) <strong>17</strong> । पुरुषों की अंतरराष्ट्रीय कुश्ती में आठ भार वर्ग और महिलाओं के लिए छह भार वर्ग हैं। प्रतियोगिता से पहले एथलीटों का वजन लिया जाता है और प्रतियोगिता में भाग लेने के लिए उनका वजन बराबर या उससे कम होना चाहिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. ______choreographed famous song \"Kahe Chhed Mohe\' from the movie Devdas and &lsquo;Mohe Rang Do Laal\' from Bajirao Mastani</p>",
                    question_hi: "<p>29. _____ ने फिल्म देवदास के प्रसिद्ध गीत \'काहे छेड़ मोहे और बाजीराव मस्तानी के गीत \'मोहे रंग दो लाल\' को कोरियोग्राफ किया है।</p>",
                    options_en: ["<p>Pandit Birju Maharaj</p>", "<p>Uday Shankar</p>", 
                                "<p>Rukmini Devi</p>", "<p>Kelucharan Mohapatra</p>"],
                    options_hi: ["<p>पंडित बिरजू महाराज</p>", "<p>उदय शंकर</p>",
                                "<p>रुक्मिणी देवी</p>", "<p>केलुचरण महापात्र</p>"],
                    solution_en: "<p>29.(a) <strong>Pandit Birju Maharaj </strong>was an Indian dancer and exponent of the Lucknow \"Kalka-Bindadin\" Gharana of Kathak dance. He received the Sangeet Natak Akademi Award in 1964, Padma Vibhushan in 1986, and the National Film Award for Best Choreography in 2013 for Vishwaroopam. He also won the Filmfare Award for Best Choreography in 2016 for Bajirao Mastani. Famous Kathak exponents: Shambhu Maharaj, Durga Lal, Nandkishore Kapote, Munna Lal Shukla, Shovana Narayan.</p>",
                    solution_hi: "<p>29.(a) <strong>पंडित बिरजू महाराज </strong>एक भारतीय नर्तक और कथक नृत्य के लखनऊ \"कालका-बिंदादीन\" घराने के प्रतिपादक थे। उन्हें 1964 में संगीत नाटक अकादमी पुरस्कार, 1986 में पद्म विभूषण और 2013 में विश्वरूपम के लिए सर्वश्रेष्ठ कोरियोग्राफी के लिए राष्ट्रीय फिल्म पुरस्कार मिला। उन्होंने 2016 में बाजीराव मस्तानी के लिए सर्वश्रेष्ठ कोरियोग्राफी के लिए फिल्मफेयर पुरस्कार भी जीता। प्रसिद्ध कथक प्रतिपादक: शंभू महाराज, दुर्गा लाल, नंद किशोर कपोते, मुन्ना लाल शुक्ला, शोवना नारायण।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which type of muscles do the uterus, iris of the eye, and bronchi contain?</p>",
                    question_hi: "<p>30. गर्भाशय, आंख की पुतली और श्वसन नली में किस प्रकार की पेशियां होती हैं?</p>",
                    options_en: ["<p>Smooth muscles</p>", "<p>Striated muscles</p>", 
                                "<p>Skeletal muscles</p>", "<p>Cardiac muscles</p>"],
                    options_hi: ["<p>अरेखित पेशियां / चिकनी पेशियां</p>", "<p>रेखित पेशियां</p>",
                                "<p>कंकाल पेशियां</p>", "<p>हृदय पेशियां</p>"],
                    solution_en: "<p>30.(a) <strong>Smooth muscles </strong>(involuntary muscles) are found in various organs and tissues that require automatic contraction and relaxation. The uterus, iris of the eye, and bronchi all contain smooth muscles. Characteristics of smooth muscles : Involuntary (not under conscious control), Non-striated (no visible striations), Found in internal organs (hollow organs). Cardiac muscles: Found exclusively in the heart, these muscles pump blood.</p>",
                    solution_hi: "<p>[30.(a) <strong>अरेखित पेशियां / चिकनी पेशियां </strong>(अनैच्छिक मांसपेशियाँ) विभिन्न अंगों और ऊतकों में पाई जाती हैं जिन्हें स्वचालित संकुचन और विश्राम की आवश्यकता होती है। गर्भाशय, आँख की परितारिका (पुतली) और श्वासनलियाँ सभी में अरेखित मांसपेशियाँ पायी जाती हैं। अरेखित मांसपेशियों की विशेषताएँ: अनैच्छिक (चेतन नियंत्रण में न होना), गैर-धारीदार (कोई दृश्यमान धारियाँ न होना), आंतरिक अंगों (खोखले अंगों) में पाई जाती हैं। हृदय पेशियाँ: विशेष रूप से हृदय में पाई जाने वाली ये मांसपेशियाँ जो रक्त को पंप करती हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Each game in table tennis consists of how many points?</p>",
                    question_hi: "<p>31. टेबल टेनिस में, प्रत्येक खेल में कितने अंक होते हैं?</p>",
                    options_en: ["<p>12</p>", "<p>11</p>", 
                                "<p>14</p>", "<p>21</p>"],
                    options_hi: ["<p>12</p>", "<p>11</p>",
                                "<p>14</p>", "<p>21</p>"],
                    solution_en: "<p>31.(b) <strong>11.</strong> Table tennis is played on a 2.74 x 1.525 meter rectangular table, made of fiberwood, divided into two halves by a net. The net, suspended between two poles, stands 15.25 centimeters high. The table tennis racquet measures approximately 17 cm in length and 15 cm in width. The table tennis ball weighs 2.7 grams and has a diameter of 40 millimeters.</p>",
                    solution_hi: "<p>31.(b) <strong>11.</strong> टेबल टेनिस 2.74 x 1.525 मीटर की आयताकार मेज पर खेला जाता है, जो फाइबर वुड से बनी होती है, जिसे नेट द्वारा दो भागों में विभाजित किया जाता है। दो खंभों के बीच लटका हुआ नेट 15.25 सेंटीमीटर ऊँचा होता है। टेबल टेनिस रैकेट की लंबाई लगभग 17 सेमी और चौड़ाई 15 सेमी होती है। टेबल टेनिस बॉल का वजन 2.7 ग्राम होता है और इसका व्यास 40 मिलीमीटर होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Alarippu, Jatiswaram, Shabdam, Varnam and Tillana are regular patterns of which of the following classical dance forms of India ?</p>",
                    question_hi: "<p>32. अलारिप्पु, जातिस्वरम, शब्दम, वर्णम और तिल्लाना भारत की निम्नलिखित शास्त्रीय नृत्य शैलियों में से किस शैली के नियमित प्रतिरूप हैं ?</p>",
                    options_en: ["<p>Sattriya</p>", "<p>Bharatanatyam</p>", 
                                "<p>Manipuri</p>", "<p>Kathak</p>"],
                    options_hi: ["<p>सत्त्रिया</p>", "<p>भरतनाट्यम</p>",
                                "<p>मणिपुरी</p>", "<p>कथक</p>"],
                    solution_en: "<p>32.(b) <strong>Bharatanatyam,</strong> a classical dance form originating from Tamil Nadu, follows a structured pattern - Alarippu: Invocation, introducing the dancer and setting the tone. Jatiswaram: Pure dance, showcasing rhythmic patterns. Shabdam: Combination of music and dance, with emphasis on expression. Varnam: Central piece, highlighting the dancer\'s skill and technique. Tillana: Concluding piece, characterized by rapid movements.</p>",
                    solution_hi: "<p>32.(b) <strong>भरतनाट्यम,</strong> तमिलनाडु से उत्पन्न एक शास्त्रीय नृत्य शैली है, जो एक संरचित पैटर्न का अनुसरण करती है - अलारिप्पु: आह्वान, नर्तक का परिचय और स्वर स्थापित करना। जतिस्वरम: शुद्ध नृत्य, लयबद्ध पैटर्न प्रदर्शित करना। शब्दम: अभिव्यक्ति पर जोर देने के साथ संगीत और नृत्य का संयोजन। वर्णम: केंद्रीय खंड, जो नर्तक के कौशल और तकनीक पर प्रकाश डालता है। तिल्लना: समापन खंड, जिसमें तीव्र गति की विशेषता होती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. As per Article 163 of the Constitution of India, any advice tendered by Ministers to the ________ shall not be inquired into in any court.</p>",
                    question_hi: "<p>33. भारतीय संविधान के अनुच्छेद 163 के अनुसार मंत्रियों द्वारा ________ को दी गई किसी भी सलाह की किसी भी अदालत में पूछताछ नहीं की जाएगी।</p>",
                    options_en: ["<p>President</p>", "<p>Chief Minister</p>", 
                                "<p>Chief Justice</p>", "<p>Governor</p>"],
                    options_hi: ["<p>राष्ट्रपति</p>", "<p>मुख्यमंत्री</p>",
                                "<p>मुख्य न्यायाधीश</p>", "<p>राज्यपाल</p>"],
                    solution_en: "<p>33.(d) <strong>Governor.</strong> Article 163(1) : The Governor is advised by a Council of Ministers, headed by the Chief Minister, except when the Governor acts at his discretion. Article 163(3) : Court cannot inquire into the advice tendered by Ministers to the Governor. Article 74(1) : There shall be a Council of Ministers with the Prime Minister at the head to aid and advise the President who shall, in the exercise of his functions, act in accordance with such advice. Article 74(2) : The question whether any, and if so what, advice was tendered by Ministers to the President shall not be inquired into in any court.</p>",
                    solution_hi: "<p>33.(d) <strong>राज्यपाल।</strong> अनुच्छेद 163(1): राज्यपाल को मंत्रिपरिषद द्वारा सलाह दी जाती है, जिसका नेतृत्व मुख्यमंत्री करता है, सिवाय इसके कि जब राज्यपाल अपने विवेक से कार्य करता है। अनुच्छेद 163(3): न्यायालय राज्यपाल को मंत्रियों द्वारा दी गई सलाह की जांच नहीं कर सकता। अनुच्छेद 74(1): राष्ट्रपति की सहायता और सलाह के लिए प्रधानमंत्री की अध्यक्षता में एक मंत्रिपरिषद होगी, जो अपने कार्यों के निर्वहन में ऐसी सलाह के अनुसार कार्य करेगा। अनुच्छेद 74(2): यह प्रश्न कि क्या मंत्रियों द्वारा राष्ट्रपति को कोई सलाह दी गई थी, और यदि दी गई थी, तो क्या, इसकी जांच किसी भी न्यायालय में नहीं की जाएगी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who is the author of the poetry \'Chand Pukhraj Ka\' ?</p>",
                    question_hi: "<p>34. \'चांद पुखराज का (Chand Pukhraj Ka)\' काव्य के लेखक कौन हैं?</p>",
                    options_en: ["<p>Anand Bakshi</p>", "<p>Gulzar</p>", 
                                "<p>Gurmel Singh</p>", "<p>Shailendra</p>"],
                    options_hi: ["<p>आनंद बख्शी</p>", "<p>गुलजार</p>",
                                "<p>गुरमेल सिंह</p>", "<p>शैलेंद्र</p>"],
                    solution_en: "<p>34.(b) <strong>Gulzar.</strong> His awards : Dadasaheb Phalke Award in 2013; Padma Bhushan in 2004; Sahitya Akademi Award for Urdu in 2002. His other Poems - Triveni, Raat Pashmine Ki, Suspected Poems. Anand Bakshi - Nagme, Kisse, Baatein, Yaadein. Gurmel Singh - Economic Liberalisation and Indian Agriculture. Shailendra - Na Bairi Na Koi Begana.</p>",
                    solution_hi: "<p>34.(b) <strong>गुलज़ार।</strong> उनके पुरस्कार: 2013 में दादा साहब फाल्के पुरस्कार; 2004 में पद्म भूषण; 2002 में उर्दू के लिए साहित्य अकादमी पुरस्कार। उनकी अन्य कविताएँ - त्रिवेणी, रात पश्मीने की, सस्पेक्टेड पोयम्स। आनंद बख्शी - नग्मे, किस्से, बातें, यादें। गुरमेल सिंह - इकॉनोमिक लिबरलाइजेशन एंड इंडियन एग्रीकल्चर। शैलेन्द्र - ना बैरी ना कोई बेगाना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Amaravati stupa is situated in which of the following states?</p>",
                    question_hi: "<p>35. अमरावती स्तूप निम्नलिखित में से किस राज्य में स्थित है ?</p>",
                    options_en: ["<p>Tamil Nadu</p>", "<p>Odisha</p>", 
                                "<p>Andhra Pradesh</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>तमिलनाडु</p>", "<p>ओडिशा</p>",
                                "<p>आंध्र प्रदेश</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p>35.(c) <strong>Andhra Pradesh.</strong> Amaravati Stupa is situated in the Guntur district of Andhra Pradesh. It was built by the Satavahana dynasty. Life and teaching of Buddha are carved out in the stupa. It was discovered by Colin Mackenzie in 1797. It was established in the 3rd-2nd century BC and expanded in the 1st-4th century AD under the patronage of the Satavahanas and Ikshvaku. Other Buddhist stupa : Sanchi (Madhya pradesh), Dhamek (Sarnath, Uttar Pradesh), Kesaria stupa (East Champaran, Bihar), Shanti stupa (Ladakh), etc,.</p>",
                    solution_hi: "<p>35.(c) <strong>आंध्र प्रदेश।</strong> अमरावती स्तूप आंध्र प्रदेश के गुंटूर जिले में स्थित है। इसे सातवाहन वंश ने बनवाया था। स्तूप में बुद्ध के जीवन और शिक्षाओं को उत्कीर्ण किया गया है। इसकी खोज 1797 में कॉलिन मैकेंजी ने की थी। इसकी स्थापना तीसरी-दूसरी शताब्दी ईसा पूर्व में हुई थी और सातवाहन तथा इक्ष्वाकु के संरक्षण में पहली-चौथी शताब्दी ईस्वी में इसका विस्तार किया गया था। अन्य बौद्ध स्तूप: सांची (मध्य प्रदेश), धामेक (सारनाथ, उत्तर प्रदेश), केसरिया स्तूप (पूर्वी चंपारण, बिहार), शांति स्तूप (लद्दाख), आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. In which of the following liquids would anthracene dissolve easily ?</p>",
                    question_hi: "<p>36. निम्नलिखित में से किस द्रव में एन्थ्रेसीन (anthracene) आसानी से घुल जाएगा ?</p>",
                    options_en: ["<p>Methane</p>", "<p>Water</p>", 
                                "<p>Benzene</p>", "<p>Sodium chloride</p>"],
                    options_hi: ["<p>मीथेन</p>", "<p>पानी</p>",
                                "<p>बेंजीन</p>", "<p>सोडियम क्लोराइड</p>"],
                    solution_en: "<p>36.(c) <strong>Benzene</strong> (C<sub>6</sub>H<sub>6</sub>) - It is a colorless or light yellow liquid at room temperature. It is widely used in the production of plastics, resins, synthetic fibers, rubber, and lubricants. The benzene molecule consists of alternating single and double bonds between carbon atoms, forming a total of 9 single bonds and 3 double bonds. The structure is planar, with a regular hexagonal shape and bond angles of 120&ordm;. Additionally, benzene is used as an additive in gasoline.</p>",
                    solution_hi: "<p>36.(c) <strong>बेंजीन</strong> (C<sub>6</sub>H<sub>6</sub>) - यह कमरे के तापमान पर रंगहीन या हल्का पीला द्रव होता है। इसका व्यापक रूप से प्लास्टिक, रेजिन, सिंथेटिक फाइबर, रबर और स्नेहक के उत्पादन में उपयोग किया जाता है। बेंजीन अणु में कार्बन परमाणुओं के बीच बारी-बारी से एकल और द्विबंध होते हैं, जो कुल 9 एकल बंध और 3 द्विबंध बनाते हैं। यह एक नियमित षट्कोणीय आकार और 120&ordm; के बंध कोण के साथ इसकी समतल संरचना होती है। इसके अतिरिक्त, बेंजीन का उपयोग गैसोलीन में एक योजक के रूप में किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Who was the founder of the Bahmani Sultanate ?</p>",
                    question_hi: "<p>37. बहमनी सल्तनत के संस्थापक कौन थे?</p>",
                    options_en: ["<p>Muhammad Shah</p>", "<p>Feroz Shah Bahmani</p>", 
                                "<p>Muhammad Bin Tughlaq</p>", "<p>Alauddin Hasan Bahman Shah</p>"],
                    options_hi: ["<p>मुहम्मद शाह</p>", "<p>फिरोज शाह बहमनी</p>",
                                "<p>मुहम्मद बिन तुगलक</p>", "<p>अलाउद्दीन हसन बहमन शाह</p>"],
                    solution_en: "<p>37.(d) <strong>Alauddin Hasan Bahman Shah</strong> founded the Bahmani Sultanate in 1347. Its capital was Ahsanabad (Gulbarga) from 1347 to 1425, later shifted to Muhammadabad (Bidar). Qutub ud-din Aibak was the first ruler and founder of the Slave Dynasty. Muhammad Bin Tughlaq ruled the Tughlaq Dynasty from 1325 to 1351. His cousin Feroz Shah Tughluq, ascended the throne in 1351 and ruled until 1388.</p>",
                    solution_hi: "<p>37.(d) <strong>अलाउद्दीन हसन बहमन शाह</strong> ने 1347 में बहमनी सल्तनत की स्थापना की। इसकी राजधानी 1347 से 1425 तक अहसानाबाद (गुलबर्गा) थी, जिसे बाद में मुहम्मदाबाद (बीदर) में स्थानांतरित कर दिया गया। कुतुबुद्दीन ऐबक, गुलाम वंश का पहला शासक और संस्थापक था। मुहम्मद बिन तुगलक ने 1325 से 1351 ई. तक तुगलक वंश पर शासन किया। उनके चचेरे भाई फ़िरोज़ शाह तुगलक 1351 में सिंहासन पर बैठे और 1388 तक शासन किया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Sea ice helps determine Earth\'s climate. According to this, what percentage of the sunlight falling on sea ice is reflected back into space ?</p>",
                    question_hi: "<p>38. समुद्री बर्फ पृथ्वी की जलवायु को निर्धारित करने में सहायक होती है। इसके अनुसार, समुद्री बर्फ पर पड़ने वाले सूर्य के प्रकाश का कितना प्रतिशत वापस अंतरिक्ष में परावर्तित हो जाता है ?</p>",
                    options_en: ["<p>80%</p>", "<p>70%</p>", 
                                "<p>75%</p>", "<p>85%</p>"],
                    options_hi: ["<p>80%</p>", "<p>70%</p>",
                                "<p>75%</p>", "<p>85%</p>"],
                    solution_en: "<p>38.(a) <strong>80%.</strong> Sea ice reflects a significant portion of sunlight, which is essential for regulating Earth&rsquo;s temperature. As sea ice diminishes, more solar energy is absorbed by the ocean, resulting in higher temperatures. This change affects global climate patterns and contributes to the acceleration of climate change. Albedo is the percentage of solar radiation reflected by a given surface.</p>",
                    solution_hi: "<p>38.(a) <strong>80%.</strong> समुद्री बर्फ सूर्य के प्रकाश का एक महत्वपूर्ण भाग परावर्तित करती है, जो पृथ्वी के तापमान को नियंत्रित करने के लिए आवश्यक है। जैसे-जैसे समुद्री बर्फ कम होती जाती है, समुद्र द्वारा अधिक सौर ऊर्जा अवशोषित की जाती है, जिसके परिणामस्वरूप तापमान बढ़ता है। यह परिवर्तन वैश्विक जलवायु पैटर्न को प्रभावित करता है और जलवायु परिवर्तन की गति को बढ़ाने में योगदान देता है। एल्बेडो किसी सतह द्वारा परावर्तित सौर विकिरण का प्रतिशत है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Match the following renewable sources of energy with their producing regions in India correctly. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599632.png\" alt=\"rId49\" width=\"415\" height=\"89\"></p>",
                    question_hi: "<p>39. ऊर्जा के निम्नलिखित नवीकरणीय स्रोतों का भारत में उनके उत्पादक क्षेत्रों के साथ सही मिलान कीजिये।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599797.png\" alt=\"rId50\" width=\"296\" height=\"106\"></p>",
                    options_en: ["<p>1-c, 2-b, 3-a</p>", "<p>1-a, 2-b, 3-c</p>", 
                                "<p>1-b, 2-a, 3-c</p>", "<p>1-b, 2-c, 3-a</p>"],
                    options_hi: ["<p>1-c, 2-b, 3-a</p>", "<p>1-a, 2-b, 3-c</p>",
                                "<p>1-b, 2-a, 3-c</p>", "<p>1-b, 2-c, 3-a</p>"],
                    solution_en: "<p>39.(b) <strong>1-a, 2-b, 3-c</strong>. Renewable energy comes from natural sources that are regenerated more quickly than they are used up. They are natural and self-replenishing, and usually have a low- or zero-carbon footprint. Examples of renewable energy sources include wind power, solar power, bioenergy (organic matter burned as a fuel) and hydroelectric.</p>",
                    solution_hi: "<p>39.(b) <strong>1-a, 2-b, 3-c .</strong> नवीकरणीय ऊर्जा प्राकृतिक स्रोतों से प्राप्त ऊर्जा है, जो खपत की तुलना में अधिक तेजी से पुनर्निर्मित हो जाती है। वे प्राकृतिक और स्व-पुनःपूर्ति करने वाली होती हैं, और आमतौर पर उनका कार्बन फुटप्रिंट कम या शून्य होता है। नवीकरणीय ऊर्जा स्रोतों के उदाहरणों में पवन ऊर्जा, सौर ऊर्जा, जैव ऊर्जा (ईंधन के रूप में जलाए जाने वाले कार्बनिक पदार्थ) और जलविद्युत ऊर्जा शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. What is the adulterant of hot paprika?</p>",
                    question_hi: "<p>40. गर्म लाल मिर्च की बुकनी (hot paprika) का अपमिश्रक क्या है?</p>",
                    options_en: ["<p>Starch powder</p>", "<p>Chalk powder</p>", 
                                "<p>Saw dust</p>", "<p>Sudan dye</p>"],
                    options_hi: ["<p>स्टार्च पाउडर</p>", "<p>चाक पाउडर</p>",
                                "<p>बुरादा</p>", "<p>सूडान रंजक</p>"],
                    solution_en: "<p>40.(d) <strong>Sudan dye.</strong> They are synthetic chemical dyes with similar structures, characterized by an azo group (-N=N-). Adulterants are substances secretly added to compromise the safety or quality of products like food, cosmetics, and pharmaceuticals. Common examples include sand and filth to increase weight, water to dilute milk or alcohol, high fructose corn syrup in honey, roasted chicory in coffee, urea and melamine in protein products, cheaper fats replacing dairy fats, and palm or soybean oils adulterating olive oil. Even papaya seeds may be added to black pepper.</p>",
                    solution_hi: "<p>40.(d) <strong>सूडान रंजक।</strong> वे समान संरचनाओं वाले कृत्रिम रसायन रंग हैं, जिनकी विशेषता एज़ो समूह (-N=N-) है। मिलावट करने वाले पदार्थ गुप्त रूप से खाद्य, सौंदर्य प्रसाधन और दवाइयों जैसे उत्पादों की सुरक्षा या गुणवत्ता से संकलित करने के लिए मिलाए जाते हैं। सामान्य उदाहरणों में वजन बढ़ाने के लिए रेत और कूड़ा-कर्कट, दूध या शराब को पतला करने के लिए पानी, शहद में उच्च फ्रुक्टोज कॉर्न सिरप, कॉफी में भुना हुआ चिकोरी, प्रोटीन उत्पादों में यूरिया और मेलामाइन, डेयरी वसा की जगह सस्ते वसा और जैतून के तेल में मिलावट करने वाले ताड़ या सोयाबीन तेल शामिल हैं। यहां तक ​​कि काली मिर्च में पपीते के बीज भी मिलाए जा सकते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which executive system is present in India?</p>",
                    question_hi: "<p>41. भारत में कौन सी कार्यपालिका प्रणाली विद्यमान है?</p>",
                    options_en: ["<p>Collegiate System</p>", "<p>Parliamentary System</p>", 
                                "<p>Both Parliamentary and Presidential System</p>", "<p>Presidential System</p>"],
                    options_hi: ["<p>कॉलेजियम प्रणाली</p>", "<p>संसदीय प्रणाली</p>",
                                "<p>संसदीय और राष्ट्रपति प्रणाली दोनों</p>", "<p>राष्ट्रपति प्रणाली</p>"],
                    solution_en: "<p>41.(b) <strong>Parliamentary System.</strong> This system involves a Prime Minister as the head of government and a President as the ceremonial head of state. The executive is accountable to the legislature, and the government is formed by the majority party or coalition in the Lok Sabha (Lower House of Parliament).</p>",
                    solution_hi: "<p>41.(b) <strong>संसदीय प्रणाली।</strong> इस प्रणाली में सरकार के प्रमुख के रूप में प्रधानमंत्री और राष्ट्र के औपचारिक प्रमुख के रूप में राष्ट्रपति शामिल होते हैं। कार्यपालिका विधायिका के प्रति जवाबदेह होती है, और सरकार लोकसभा (संसद के निम्न सदन) में बहुमत वाली पार्टी या गठबंधन द्वारा बनाई जाती है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. If an object of mass 2 kg is dropped from a height of 10 metres, what will be the ratio of its potential energy and kinetic energy at the height of 5 metres (g = 10 m/sec<sup>2</sup> )</p>",
                    question_hi: "<p>42. यदि 2 किलोग्राम द्रव्यमान की कोई वस्तु को 10 मीटर की ऊंचाई से गिराया जाए, तो 5 मीटर की ऊंचाई पर उसकी स्थितिज ऊर्जा और गतिज ऊर्जा का अनुपात क्या होगा? (g = 10 m/sec<sup>2</sup>)</p>",
                    options_en: ["<p>1 : 2</p>", "<p>4 : 1</p>", 
                                "<p>1 : 1</p>", "<p>1 : 4</p>"],
                    options_hi: ["<p>1 : 2</p>", "<p>4 : 1</p>",
                                "<p>1 : 1</p>", "<p>1 : 4</p>"],
                    solution_en: "<p>42.(c) 1 : 1. When the object is dropped from a height of 10 meters, its potential energy (PE) is converted to kinetic energy (KE) as it falls. At the height of 5 meters, the object has lost half of its initial potential energy and has gained an equal amount of kinetic energy.<br>Initial Potential energy (PE) = mgh <br>= 2 kg &times; 10 m/s&sup2; &times; 10 m = 200 J<br>At 5 meters, Potential energy (PE) = mgh <br>= 2 kg &times; 10 m/s&sup2; &times; 5 m = 100 J<br>Kinetic energy (KE) = Initial PE - PE <br>= 200 J - 100 J = 100 J<br>Ratio of PE to KE = 100 J : 100 J = 1 : 1</p>",
                    solution_hi: "<p>42.(c) 1 : 1. जब वस्तु को 10 मीटर की ऊंचाई से गिराया जाता है, तो गिरते समय इसकी स्थितिज ऊर्जा (PE) गतिज ऊर्जा (KE) में बदल जाती है। 5 मीटर की ऊंचाई पर, वस्तु अपनी प्रारंभिक स्थितिज ऊर्जा का आधा हिस्सा खो देती है और गतिज ऊर्जा की समान मात्रा प्राप्त कर लेती है। प्रारंभिक स्थितिज ऊर्जा (PE) = mgh <br>= 2 kg &times; 10 m/s&sup2; &times; 10 m = 200 J <br>5 मीटर पर, स्थितिज ऊर्जा (PE) = mgh <br>= 2 kg &times; 10 m/s&sup2; &times; 5 m = 100 J <br>गतिज ऊर्जा (KE) = प्रारंभिक PE - PE <br>= 200 J - 100 J = 100 J <br>PE से KE का अनुपात = 100 J : 100 J = 1:1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. What are the two main forms of protection used to shield domestic industries from foreign competition in an inward- looking trade strategy</p>",
                    question_hi: "<p>43. अंतर्मुखी व्यापार रणनीति में घरेलू उद्योगों को विदेशी प्रतिस्पर्धा से बचाने के लिए उपयोग की जाने वाली सुरक्षा के दो मुख्य रूप क्या है ?</p>",
                    options_en: ["<p>Tariffs and subsidies</p>", "<p>Quotas and subsidies</p>", 
                                "<p>Tariffs and price controls</p>", "<p>Tariffs and quotas</p>"],
                    options_hi: ["<p>प्रशुल्क और सब्सिडी</p>", "<p>कोटा और सब्सिडी</p>",
                                "<p>प्रशुल्क और मूल्य नियंत्रण</p>", "<p>प्रशुल्क और कोटा</p>"],
                    solution_en: "<p>43.(d) <strong>Tariffs and quotas.</strong> Tariff - It is a tax on imported goods. Quota - It refers to fixing the maximum limit on the imports of a commodity by a domestic producer. Inward looking trade strategy is a government policy that aims to reduce a country\'s reliance on imports and build up its domestic industries. An outward-looking trade strategy is an economic development strategy that focuses on promoting exports and international targets.</p>",
                    solution_hi: "<p>43.(d) <strong>प्रशुल्क और कोटा।</strong> प्रशुल्क (टैरिफ) - यह आयातित वस्तुओं पर लगाया जाने वाला कर है। कोटा - यह किसी घरेलू उत्पादक द्वारा किसी वस्तु के आयात पर अधिकतम सीमा तय करने को संदर्भित करता है। अंतर्मुखी व्यापार रणनीति एक सरकारी नीति है जिसका उद्देश्य किसी देश की आयात पर निर्भरता को कम करना और अपने घरेलू उद्योगों का निर्माण करना है। एक बहिर्मुखी व्यापार रणनीति एक आर्थिक विकास रणनीति है जो निर्यात और अंतर्राष्ट्रीय लक्ष्यों को बढ़ावा देने पर केंद्रित है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. What is Statutory Liquidity Ratio (SLR) ?</p>",
                    question_hi: "<p>44. वैधानिक तरलता अनुपात (Statutory Liquidity Ratio-SLR) क्या है ?</p>",
                    options_en: ["<p>It\'s the percentage of deposits that banks must keep in liquid form in the short term</p>", "<p>It\'s the interest rate at which banks borrow from the central bank</p>", 
                                "<p>It\'s the ratio of government bonds to total assets held by a bank</p>", "<p>It\'s the ratio of a bank\'s capital to its total assets</p>"],
                    options_hi: ["<p>यह जमाओं का वह प्रतिशत है जो किसी बैंक में अल्पकाल में तरल रूप में रखना होता है</p>", "<p>यह वह ब्याज दर है जिस पर बैंक केंद्रीय बैंक से ऋण लेते हैं</p>",
                                "<p>यह सरकारी बांड और किसी बैंक द्वारा धारित कुल परिसंपत्तियों का अनुपात है</p>", "<p>यह किसी बैंक की पूंजी और उसकी कुल परिसंपत्तियों का अनुपात है</p>"],
                    solution_en: "<p>44.(a) <strong>Statutory Liquidity Ratio.</strong> Other economics terms: Cash Reserve Ratio (CRR) - The fraction of their deposits which the commercial banks are required to keep with RBI. Currency deposit ratio - The ratio of money held by the public in currency to that held as deposits in commercial banks. Reserve deposit ratio - The fraction of their total deposits which commercial banks keep as reserves.</p>",
                    solution_hi: "<p>44.(a) <strong>वैधानिक तरलता अनुपात।</strong> अन्य अर्थशास्त्र संबंधी शब्द: नकद आरक्षित अनुपात (CRR) - वाणिज्यिक बैंकों को अपने जमा का वह अंश जो RBI के पास रखने की आवश्यकता होती है। मुद्रा जमा अनुपात - जनता द्वारा मुद्रा के रूप में रखे गए धन और वाणिज्यिक बैंकों में जमा के रूप में रखे गए धन का अनुपात है। आरक्षित जमा अनुपात - वाणिज्यिक बैंक अपनी कुल जमा राशि का वह अंश जो आरक्षित निधि के रूप में रखते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Who among the following discovered that a wire carrying electric current can attract or repel another wire next to it that\'s also carrying electric current ?</p>",
                    question_hi: "<p>45. निम्नलिखित में से किसने यह खोज की थी कि विद्युत धारा प्रवाहित करने वाली तार अपने बगल में विद्युत धारा को प्रवाहित करने वाली एक अन्य तार को आकर्षित या प्रतिकर्षित कर सकती है?</p>",
                    options_en: ["<p>James Maxwell</p>", "<p>Michael Faraday</p>", 
                                "<p>Guglielmo Marconi</p>", "<p>Andre-Marie Ampere</p>"],
                    options_hi: ["<p>जेम्स मैक्सवेल (James Maxwell)</p>", "<p>माइकल फैराडे (Michael Faraday)</p>",
                                "<p>गुग्लील्मो मार्कोनी (Guglielmo Marconi)</p>", "<p>आंद्रे-मैरी एम्पीयर (Andre-Marie Ampere)</p>"],
                    solution_en: "<p>45.(d) <strong>Andre-Marie Ampere.</strong> He was a French mathematician and physicist who discovered the fundamental principles of electromagnetism in 1820. James Maxwell formulated the equations that unified electricity and magnetism into a single force, electromagnetism. Michael Faraday discovered electromagnetic induction and the principles of electromagnetic rotation. Guglielmo Marconi pioneered radio communication.</p>",
                    solution_hi: "<p>45.(d) <strong>आंद्रे-मैरी एम्पीयर</strong> (Andre-Marie Ampere) एक फ्रांसीसी गणितज्ञ और भौतिक वैज्ञानिक थे जिन्होंने 1820 में विद्युत चुंबकत्व के मूलभूत सिद्धांतों की खोज की थी। जेम्स मैक्सवेल ने ऐसे समीकरण तैयार किए जो विद्युत और चुंबकत्व को एक ही बल , विद्युत चुंबकत्व में एकीकृत करते हैं। माइकल फैराडे ने विद्युत चुम्बकीय प्रेरण और विद्युत चुम्बकीय घूर्णन के सिद्धांतों की खोज की। गुग्लिल्मो मार्कोनी ने रेडियो संचार के क्षेत्र में अग्रणी भूमिका निभाई थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who among the following founded the \'Satyashodhak Samaj\' in 1873 ?</p>",
                    question_hi: "<p>46. निम्नलिखित में से किसने 1873 में सत्यशोधक समाज की स्थापना की थी ?</p>",
                    options_en: ["<p>Jyotiba Phule</p>", "<p>Gopal Ganesh Agarkar</p>", 
                                "<p>Vinoba Bhave</p>", "<p>Vinayak Damodar Savarkar</p>"],
                    options_hi: ["<p>ज्योतिबा फुले</p>", "<p>गोपाल गणेश आगरकर</p>",
                                "<p>विनोबा भावे</p>", "<p>विनायक दामोदर सावरकर</p>"],
                    solution_en: "<p>46.(a) <strong>Jyotiba Phule.</strong> Satyashodhak Samaj worked to uplift Dalits and women by promoting education, social rights, and political access. Other Organizations/Movements and Founder: Abhinav Bharat (1904) - Vinayak Damodar Savarkar. Deccan Education Society (1884) - Gopal Ganesh Agarkar and Lokmanya Tilak. Bhoodan movement (1951) - Vinoba Bhave.</p>",
                    solution_hi: "<p>46.(a) <strong>ज्योतिबा फुले।</strong> सत्यशोधक समाज ने शिक्षा, सामाजिक अधिकारों और राजनीतिक पहुंच को बढ़ावा देकर दलितों और महिलाओं के उत्थान के लिए कार्य किया। अन्य संगठन/आंदोलन और संस्थापक: अभिनव भारत (1904) - विनायक दामोदर सावरकर। डेक्कन शिक्षा समिति (1884) - गोपाल गणेश अगरकर और लोकमान्य तिलक। भूदान आंदोलन (1951) - विनोबा भावे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. What is the interaction of a species with all the biotic and abiotic factors affecting it called ?</p>",
                    question_hi: "<p>47. किसी प्रजाति का उसको प्रभावित करने वाले सभी जैविक और अजैविक कारकों के साथ उसके अंतर्संबंध को क्या कहा जाता है ?</p>",
                    options_en: ["<p>Ecological niche</p>", "<p>Food web</p>", 
                                "<p>Food chain</p>", "<p>Natural habitat</p>"],
                    options_hi: ["<p>पारिस्थितिक ताक (Ecological niche)</p>", "<p>खाद्य जाल (Food web)</p>",
                                "<p>खाद्य श्रृंखला (Food chain)</p>", "<p>प्राकृतिक वास (Habitat)</p>"],
                    solution_en: "<p>47.(a) <strong>Ecological niche.</strong> Food web consists of all the food chains in a single ecosystem. All of the interconnected and overlapping food chains in an ecosystem make up a food web. Food chain - A linear sequence of organisms through which nutrients and energy pass as one organism eats another. Natural habitat - An ecological or environmental area where a specific species lives.</p>",
                    solution_hi: "<p>47.(a) <strong>पारिस्थितिक ताक</strong> । खाद्य जाल में एक ही पारिस्थितिकी तंत्र की सभी खाद्य श्रृंखलाएँ शामिल होती हैं। एक पारिस्थितिकी तंत्र में सभी परस्पर जुड़ी और अतिव्यापी खाद्य श्रृंखलाएं एक खाद्य जाल बनाती हैं। खाद्य श्रृंखला - जीवों का एक रैखिक अनुक्रम जिसके माध्यम से पोषक तत्व और ऊर्जा गुजरती है जब एक जीव दूसरे को खाता है। प्राकृतिक आवास - एक पारिस्थितिक या पर्यावरणीय क्षेत्र जहां एक विशिष्ट प्रजाति रहती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. How many Gram Panchayats competed in the National Panchayat Awards 2024 ?</p>",
                    question_hi: "<p>48. राष्ट्रीय पंचायत पुरस्कार 2024 में कितनी ग्राम पंचायतों ने भाग लिया?</p>",
                    options_en: ["<p>1 lakh</p>", "<p>1.5 lakh</p>", 
                                "<p>1.94 lakh</p>", "<p>2 lakh</p>"],
                    options_hi: ["<p>1 लाख</p>", "<p>1.5 लाख</p>",
                                "<p>1.94 लाख</p>", "<p>2 लाख</p>"],
                    solution_en: "<p>48.(c) <strong>1.94</strong> lakh. Notably, 42% of the award-winning Panchayats were led by women, highlighting the growing role of female leadership in rural governance.</p>",
                    solution_hi: "<p>48. (c) <strong>1.94</strong> लाख। विशेष रूप से, पुरस्कार विजेता पंचायतों में से 42% का नेतृत्व महिलाओं द्वारा किया गया था, जो ग्रामीण शासन में महिला नेतृत्व की बढ़ती भूमिका को दर्शाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. When a shuttle is fired high into the air, the player has time to make a powerful overhead shot (overhand shot) directly on the opposing court\'s command. It is known as __________.</p>",
                    question_hi: "<p>49. जब एक शटल को हवा में ऊंचा उड़ाया जाता है, तब खिलाड़ी के पास सीधे विरोधी कोर्ट के फर्श पर एक शक्तिशाली ओवरहेड शॉट (overhand shot) लगाने का समय होता है। इसे ______ के नाम से जाना जाता है।</p>",
                    options_en: ["<p>Long service</p>", "<p>Rally</p>", 
                                "<p>Short service</p>", "<p>Smash</p>"],
                    options_hi: ["<p>लॉन्ग सर्विस (Long service)</p>", "<p>रैली (Rally)</p>",
                                "<p>शॉर्ट सर्विस (Short service)</p>", "<p>स्मैश (Smash)</p>"],
                    solution_en: "<p>49.(d) <strong>Smash</strong> is a shot that is hit above the hitter\'s head with a serve-like motion in tennis. Some terms used in tennis: Ace, Backhand, Volley, Forehand, Advantage, Fault, Break point, Doubles, Drop shot. Grand Slam: Australian Open: Started in 1905, French Open: Started in 1891, Wimbledon: Started in 1877, US Open: Started in 1881.</p>",
                    solution_hi: "<p>49.(d) <strong>स्मैश</strong> (Smash) एक शॉट है जो टेनिस में सर्व-जैसी गति से हिटर के सिर के ऊपर मारा जाता है। टेनिस में प्रयोग किए जाने वाले कुछ शब्द: ऐस, बैकहैंड, वॉली, फोरहैंड, एडवांटेज, फॉल्ट, ब्रेक पॉइंट, डबल्स, ड्रॉप शॉट। ग्रैंड स्लैम: ऑस्ट्रेलियन ओपन: 1905 में शुरू हुआ, फ्रेंच ओपन: 1891 में शुरू हुआ, विंबलडन: 1877 में शुरू हुआ, यूएस ओपन: 1881 में शुरू हुआ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which initiative was launched by the Ministry of Panchayati Raj to enhance the capacity building of Panchayati Raj Institution (PRI) members ?</p>",
                    question_hi: "<p>50. पंचायत राज संस्थाओं (PRI) के सदस्यों की क्षमता निर्माण को बढ़ाने के लिए पंचायत राज मंत्रालय द्वारा कौन सा पहल शुरू किया गया था ?</p>",
                    options_en: ["<p>Gram Swaraj Abhiyan</p>", "<p>Viksit Panchayat Karmayogi</p>", 
                                "<p>Rashtriya Gram Vikas Yojana</p>", "<p>Digital Panchayat Mission</p>"],
                    options_hi: ["<p>ग्राम स्वराज अभियान</p>", "<p>विकसित पंचायत कर्मयोगी</p>",
                                "<p>राष्ट्रीय ग्राम विकास योजना</p>", "<p>डिजिटल पंचायत मिशन</p>"],
                    solution_en: "<p>50.(b) <strong>Viksit Panchayat Karmayogi. </strong>This initiative aims to empower local bodies and enhance their role in achieving sustainable development goals at the grassroots level.</p>",
                    solution_hi: "<p>50.(b)<strong> विकसित पंचायत कर्मयोगी। </strong>इस पहल का उद्देश्य स्थानीय निकायों को सशक्त बनाना और उन्हें जमीनी स्तर पर सतत विकास लक्ष्यों की प्राप्ति में उनकी भूमिका को बढ़ाना है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> = 2(a + c -1), then the value of a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = ?</p>",
                    question_hi: "<p>51. यदि a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> = 2(a + c - 1) है, तो a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = ?</p>",
                    options_en: ["<p>0</p>", "<p>2</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>0</p>", "<p>2</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>51.(b) <strong>Given</strong> , a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> = 2(a + c - 1)&nbsp;<br>Here, a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> - 2a - 2c + 2 = 0<br>&rArr; a<sup>2</sup> - 2a + 1 + b<sup>2</sup> + c<sup>2</sup> - 2c + 1 = 0<br>&rArr;&nbsp; (a - 1)<sup>2</sup> + b<sup>2</sup> + (c - 1)<sup>2</sup> = 0<br>Now , (a - 1)<sup>2</sup>&nbsp;= 0 , a = 1<br>b<sup>2</sup> = 0 , b = 0 and (c - 1)<sup>2</sup>&nbsp;= 0 , c = 1<br>&there4; a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = 2</p>",
                    solution_hi: "<p>51.(b) <strong>दिया गया है</strong>, a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> = 2(a + c - 1)<br>अब, a<sup>2</sup> + b<sup>2</sup> + c<sup>2</sup> - 2a - 2c + 2 = 0<br>&rArr; a<sup>2</sup> - 2a + 1 + b<sup>2</sup> + c<sup>2</sup> - 2c + 1 = 0<br>&rArr;&nbsp; (a - 1)<sup>2</sup> + b<sup>2</sup> + (c - 1)<sup>2</sup> = 0<br>अतः, (a - 1)<sup>2</sup> = 0 , a = 1<br>b<sup>2</sup> = 0 , b =0 and (c - 1)<sup>2</sup> = 0 , c = 1<br>&there4; a<sup>3</sup> + b<sup>3</sup> + c<sup>3</sup> = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. A circular arc whose radius is 12 cm makes an angle of 30&deg; at the centre. Find the perimeter (in cm) of the sector formed. (Use &pi; = 3.14)</p>",
                    question_hi: "<p>52. एक वृत्ताकार चाप जिसकी त्रिज्या 12 cm है, केंद्र पर 30&deg; का कोण बनाता है। बने हुए त्रिज्यखंड का परिमाप (cm में) ज्ञात कीजिए। (&pi; = 3.14 का उपयोग कीजिए)</p>",
                    options_en: ["<p>32.38</p>", "<p>30.28</p>", 
                                "<p>28.64</p>", "<p>26.24</p>"],
                    options_hi: ["<p>32.38</p>", "<p>30.28</p>",
                                "<p>28.64</p>", "<p>26.24</p>"],
                    solution_en: "<p>52.(b) Length of arc = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mn>360</mn></mfrac></math> &times; 2&pi;r<br>= <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2 &times; 3.14 &times; 12 <br>= 6.28cm<br>Perimeter of the sector = 6.28 + 12 + 12 = 30.28cm</p>",
                    solution_hi: "<p>52.(b) चाप की लंबाई = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mn>360</mn></mfrac></math>&nbsp;&times; 2&pi;r<br>= <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>360</mn></mrow></mfrac></math> &times; 2 &times; 3.14 &times; 12 <br>= 6.28cm<br>त्रिज्यखंड का परिमाप = 6.28 + 12 + 12 = 30.28cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. R pays ₹100 to P with ₹5, ₹2 and ₹1 coins. The total number of coins used for paying are 40. What is the number of coins of denomination ₹5 in the payment ?</p>",
                    question_hi: "<p>53. R, P को ₹5, ₹2 और ₹1 के सिक्कों के साथ ₹100 का भुगतान करता है। भुगतान के लिए उपयोग किए गए सिक्कों की कुल संख्या 40 है। भुगतान में ₹5 मूल्यवर्ग के सिक्कों की संख्या कितनी है ?</p>",
                    options_en: ["<p>16</p>", "<p>17</p>", 
                                "<p>18</p>", "<p>13</p>"],
                    options_hi: ["<p>16</p>", "<p>17</p>",
                                "<p>18</p>", "<p>13</p>"],
                    solution_en: "<p>53.(d)&nbsp;Let ₹5, ₹2, ₹1 coins be x, y, z&nbsp;respectively.<br>According to question, <br>x + y + z&nbsp;= 40 &hellip;.. (i)<br>5x + 2y + z = 100 &hellip; (ii)<br>On subtracting equation (i) from (ii) we get<br>4x + y = 60<br>Now on satisf<math display=\"inline\"><mi>y</mi></math>ing the options we find that only option (d) gives positive value of y and no. of coins must be positive.<br>Hence, no. of ₹5 coins = 13</p>",
                    solution_hi: "<p>53.(d)&nbsp;माना ₹5, ₹2, ₹1 के सिक्के क्रमशः x, y, z हैं।<br>प्रश्न के अनुसार, <br>x + y + z&nbsp;= 40 &hellip;.. (i)<br>5x + 2y + z = 100 &hellip; (ii)<br>समीकरण (i) को (ii) से घटाने पर हमें प्राप्त होता है<br>4x + y = 60<br>अब विकल्पों से संतुष्ट करने पर हम पाते हैं कि केवल विकल्प (d) y&nbsp;का सकारात्मक मान देता है और सिक्कों का मान सकारात्मक होना चाहिए । <br>अत:, ₹5 के सिक्कों की संख्या = 13</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. An observer at the top of the tower observes that two cars are moving towards the foot of the tower at a distance of 120 m from each other making angle of depression &alpha; and &beta; such that &alpha; &gt; &beta; and tan &alpha; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> and tan &beta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math>, Find the height of the tower ?</p>",
                    question_hi: "<p>54. टावर के शीर्ष पर एक पर्यवेक्षक देखता है कि दो कारें टावर के पैर की तरफ एक दूसरे से 120 मीटर की दूरी पर &alpha; और &beta; अवनमन कोण इस प्रकार बना रही हैं, कि &alpha; &gt; &beta; और tan&alpha; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> और tan &beta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math>, टावर की ऊंचाई ज्ञात कीजिये ?</p>",
                    options_en: ["<p>120<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>", "<p>40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>", 
                                "<p>80<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>", "<p>60<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>"],
                    options_hi: ["<p>120<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>", "<p>40<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>",
                                "<p>80<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>", "<p>60<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>m</p>"],
                    solution_en: "<p>54.(d) Given that, <br>tan &alpha; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> &rarr; &alpha; = 60&deg;<br>tan &beta;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> &rarr; &beta; = 30&deg;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599935.png\" alt=\"rId51\" width=\"274\" height=\"176\"><br><br>From above diagram, <br>2 unit = 120 m<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> unit (Height) = 60<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> m</p>",
                    solution_hi: "<p>54.(d) दिया गया है की, <br>tan &alpha; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> &rarr; &alpha; = 60&deg;<br>tan &beta;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>3</mn></msqrt></mfrac></math> &rarr; &beta; = 30&deg;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182599935.png\" alt=\"rId51\" width=\"274\" height=\"176\"><br>उपरोक्त आकृति से,<br>2 इकाई = 120 मीटर<br><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> इकाई (ऊंचाई) = 60<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> मीटर</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. What is the number of digits required for numbering a book with 428 pages ?</p>",
                    question_hi: "<p>55. 428 पृष्ठों वाली एक पुस्तक को क्रमांकित करने के लिए आवश्यक अंकों की संख्या कितनी है ?</p>",
                    options_en: ["<p>1500</p>", "<p>2000</p>", 
                                "<p>988</p>", "<p>1176</p>"],
                    options_hi: ["<p>1500</p>", "<p>2000</p>",
                                "<p>988</p>", "<p>1176</p>"],
                    solution_en: "<p>55.(d)&nbsp;According to the question,<br>Number of digits required = (9 &times; 1) + (90 &times; 2) + (329 &times; 3) = 1176</p>",
                    solution_hi: "<p>55.(d)&nbsp;प्रश्न के अनुसार,<br>आवश्यक अंकों की संख्या = (9 &times; 1) + (90 &times; 2) + (329 &times; 3) = 1176</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. A chemistry laboratory requests for a 25% solution of ferrous sulphate. A supplier has 40 millilitres of 20% solution. How many millilitres of 40% solution should be added to make it a 25% solution (correct to two decimal places) ?</p>",
                    question_hi: "<p>56. एक रसायन प्रयोगशाला फेरस सल्फेट के 25% विलयन के लिए अनुरोध करती है। एक आपूर्तिकर्ता के पास 20% विलयन का 40 मिलीलीटर है। इसे 25% विलयन बनाने के लिए 40% विलयन के कितने मिलीलीटर (दो दशमलव स्थानों तक सही) मिलाए जाने चाहिए ?</p>",
                    options_en: ["<p>16.40</p>", "<p>15.20</p>", 
                                "<p>13.33</p>", "<p>14.30</p>"],
                    options_hi: ["<p>16.40</p>", "<p>15.20</p>",
                                "<p>13.33</p>", "<p>14.30</p>"],
                    solution_en: "<p>56.(c)&nbsp;Using Alligation method, we have ;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182600120.png\" alt=\"rId52\" width=\"124\" height=\"170\"><br>&rArr; 3 unit = 40 mililitres<br>&rArr; 1 unit = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>3</mn></mfrac></math> = 13.33 milliliters</p>",
                    solution_hi: "<p>56.(c)&nbsp;मिश्रण विधि का उपयोग करके :-<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182600120.png\" alt=\"rId52\" width=\"124\" height=\"170\"><br>&rArr; 3 इकाई = 40 मिलीमीटर<br>&rArr; 1 इकाई = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>3</mn></mfrac></math> = 13.33 मिलीमीटर</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If 6 engineers can complete a work in 5 days and 5 skilled workers can complete the same work in 7 days, then the time taken (in days) to complete the same work by 7 engineers and 4 skilled workers will be:</p>",
                    question_hi: "<p>57. यदि 6 इंजीनियर एक काम को 5 दिनों में पूरा कर सकते हैं और 5 कुशल श्रमिक उसी काम को 7 दिनों में पूरा कर सकते हैं, तो उसी काम को 7 इंजीनियरों और 4 कुशल श्रमिकों द्वारा पूरा करने में कितना समय (दिनों में) लगेगा ?</p>",
                    options_en: ["<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>70</mn></mfrac></math></p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math></p>", 
                                "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>73</mn></mfrac></math></p>", "<p>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math></p>"],
                    options_hi: ["<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>70</mn></mfrac></math></p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math></p>",
                                "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>73</mn></mfrac></math></p>", "<p>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math></p>"],
                    solution_en: "<p>57.(b)&nbsp;According to the question.<br>6 engineers &times; 5 = 5 skilled workers &times; 7<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>engineers</mi><mrow><mi>skilled</mi><mi mathvariant=\"normal\">&#160;</mi><mi>workers</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math><br>Total works = 6 &times; 7 &times; 5 units<br>Required time = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mo>(</mo><mn>7</mn><mo>&#215;</mo><mn>7</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>4</mn><mo>&#215;</mo><mn>6</mn><mo>)</mo></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math> days</p>",
                    solution_hi: "<p>57.(b)&nbsp;प्रश्न के अनुसार.<br>6 इंजीनियर &times; 5 = 5 कुशल श्रमिक &times; 7<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2311;&#2306;&#2332;&#2368;&#2344;&#2367;&#2351;&#2352;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2325;&#2369;&#2358;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2358;&#2381;&#2352;&#2350;&#2367;&#2325;</mi><mo>&#160;&#160;</mo><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math><br>कुल कार्य = 6 &times; 7 &times; 5 इकाई<br>आवश्यक समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>6</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>5</mn></mrow><mrow><mo>(</mo><mn>7</mn><mo>&#215;</mo><mn>7</mn><mo>)</mo><mo>+</mo><mo>(</mo><mn>4</mn><mo>&#215;</mo><mn>6</mn><mo>)</mo></mrow></mfrac></math> = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>64</mn><mn>73</mn></mfrac></math> दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Three circles each of radius 5 cm touch one another. The area (in cm<sup>2</sup>) subtended between them is:</p>",
                    question_hi: "<p>58. 5 सेमी की त्रिज्या वाले तीन वृत्त एक दूसरे को स्पर्श करते हैं। उनके बीच अंतरित क्षेत्रफल (सेमी<sup>2</sup> में) कितना है ?</p>",
                    options_en: ["<p>50 (<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math>)</p>", "<p>25 (<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math>)</p>", 
                                "<p>25 (2<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math>)</p>", "<p>25 (<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math>)</p>"],
                    options_hi: ["<p>50 (<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math>)</p>", "<p>25 (<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math>)</p>",
                                "<p>25 (2<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math>)</p>", "<p>25 (<math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math>)</p>"],
                    solution_en: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182600255.png\" alt=\"rId53\" width=\"193\" height=\"176\"><br>Center distance between circle = side of triangle = 2 &times;&nbsp;5 = 10 cm<br>Since all sides are of 10 cm so, <strong id=\"docs-internal-guid-80fdf87a-7fff-afba-58bd-5b4db9c3bb84\">∆</strong>ABC is an equilateral triangle<br>Area subtended between three circle = area of triangle - 3 &times;&nbsp;area of sector<br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 10 &times; 10 - 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; &pi;r<sup>2</sup> <br>= 25<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; &pi; &times; 5 &times; 5&nbsp;<br>= 25<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math> &times; 25&nbsp;<br>= 25 ( <math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math>)</p>",
                    solution_hi: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182600255.png\" alt=\"rId53\" width=\"193\" height=\"176\"><br>वृत्त के केंद्र की दूरी = त्रिभुज की भुजा = 2 &times;&nbsp;5 = 10 cm<br>चूँकि सभी भुजाएँ 10 सेमी हैं, इसलिए , <strong id=\"docs-internal-guid-80fdf87a-7fff-afba-58bd-5b4db9c3bb84\">∆</strong>ABC एक समबाहु त्रिभुज है<br>तीन वृत्तों के बीच का क्षेत्रफल = त्रिभुज का क्षेत्रफल - 3 &times;&nbsp;त्रिज्यखंड का क्षेत्रफल<br>= <math display=\"inline\"><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 10 &times; 10 - 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; &pi;r<sup>2</sup> <br>= 25<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - 3 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mo>&#176;</mo></mrow><mrow><mn>360</mn><mo>&#176;</mo></mrow></mfrac></math> &times; &pi; &times; 5 &times; 5&nbsp;<br>= 25<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math> &times; 25&nbsp;<br>= 25 ( <math display=\"inline\"><msqrt><mn>3</mn></msqrt><mi>&#160;</mi></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#960;</mi><mn>2</mn></mfrac></math>)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Tom travelled 285 km in 6 hours. In his total travelling, he travelled the first part by bus at 40 km/hr and the remaining part by train at 55 km/hr. How much distance has he travelled by train ?</p>",
                    question_hi: "<p>59. टॉम ने 6 घंटे में 285 km की यात्रा की। अपनी कुल यात्रा में, वह पहले भाग की यात्रा बस द्वारा 40 km/hr की चाल से और शेष भाग की यात्रा रेलगाड़ी द्वारा 55 km/hr की चाल से तय करता है। वह रेलगाड़ी द्वारा कितनी दूरी तय करता है ?</p>",
                    options_en: ["<p>155 km</p>", "<p>120 km</p>", 
                                "<p>135 km</p>", "<p>165 km</p>"],
                    options_hi: ["<p>155 km</p>", "<p>120 km</p>",
                                "<p>135 km</p>", "<p>165 km</p>"],
                    solution_en: "<p>59.(d)&nbsp;Let distance travel by bus = x&nbsp;km<br>Distance travel by train = (285 - x) km<br>According to question,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>40</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>285</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi></mrow><mn>55</mn></mfrac></math> = 6<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>8</mn><mo>(</mo><mn>285</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>440</mn></mfrac></math> = 6<br>11x + 2280 - 8x = 6 &times; 440<br>3x&nbsp;= 2640 - 2280<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>3</mn></mfrac></math> = 120 km<br>Hence, distance travel by train = 285 - x<br>= 285 - 120 = 165km</p>",
                    solution_hi: "<p>59.(d)&nbsp;माना बस से तय की गई दूरी = x&nbsp;km<br>ट्रेन से तय की गई दूरी = (285 - x) km<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mn>40</mn></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>285</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi></mrow><mn>55</mn></mfrac></math> = 6<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>11</mn><mi mathvariant=\"normal\">x</mi><mo>+</mo><mn>8</mn><mo>(</mo><mn>285</mn><mo>-</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>440</mn></mfrac></math> = 6<br>11x + 2280 - 8x = 6 &times; 440<br>3x&nbsp;= 2640 - 2280<br>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>3</mn></mfrac></math> = 120 km<br>अत: ट्रेन से यात्रा की दूरी= 285 - x<br>= 285 - 120 = 165km</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. If p sin A - cos A = 1, then p<sup>2</sup> - (1 + p<sup>2</sup>) cos A equals:</p>",
                    question_hi: "<p>60. यदि p sin A - cos A = 1 है, तो p<sup>2</sup> - (1 + p<sup>2</sup>) cos A का मान ज्ञात करें।</p>",
                    options_en: ["<p>1</p>", "<p>-1</p>", 
                                "<p>2</p>", "<p>0</p>"],
                    options_hi: ["<p>1</p>", "<p>-1</p>",
                                "<p>2</p>", "<p>0</p>"],
                    solution_en: "<p>60.(a)&nbsp;p sin A - cos A = 1<br>Put A = 90&deg;, we have ;<br>p - 0 = 1<br>p = 1<br>Now, p<sup>2</sup> - (1 + p<sup>2</sup>) cos A = 1 - (1 + 1) cos 90&deg; = 1 - 0 = 1</p>",
                    solution_hi: "<p>60.(a)&nbsp;p sin A - cos A = 1<br>A = 90&deg; रखने पर,<br>p - 0 = 1<br>p = 1<br>इसलिए, p<sup>2</sup> - (1 + p<sup>2</sup>) cos A = 1 - (1 + 1) cos 90&deg; = 1 - 0 = 1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. A man rows 48 km and back in 48 hours. He can row 4 km with the stream in the same time as 3 km against the stream. The speed of the stream (in km/h) is:</p>",
                    question_hi: "<p>61. एक आदमी 48 km नाव चलाता है और 48 घंटे में वापस आता है। वह धारा के अनुकूल 4 km की दूरी उतने ही समय में तय कर सकता है जितने समय में धारा के विपरीत 3 km की दूरी तय करता है। धारा की चाल (km/h में) ज्ञात करें।</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>21</mn></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>29</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>61.(c)&nbsp;Let the speed of a boat be x&nbsp;km/h and the speed of a stream be y km/h<br>According to the question,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 48 &hellip;&hellip;..(i)<br>And, <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math><br>&rArr; 4x - 4y = 3x + 3y<br>&rArr; x = 7y &hellip;&hellip;.(ii)<br>Put the value of x&nbsp;in equation (i)<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mn>7</mn><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mn>7</mn><mi mathvariant=\"normal\">y</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 48<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mn>8</mn><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mn>6</mn><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 48<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mi mathvariant=\"normal\">y</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mi mathvariant=\"normal\">y</mi></mfrac></math> = 48<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mi mathvariant=\"normal\">y</mi></mfrac></math> = 48 &rArr; y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>24</mn></mfrac></math> km/h</p>",
                    solution_hi: "<p>61.(c)&nbsp;माना नाव की गति x&nbsp;किमी/घंटा है और धारा की गति y किमी/घंटा है<br>प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 48 &hellip;&hellip;..(i)<br>और, <br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math><br>&rArr; 4x - 4y = 3x + 3y<br>&rArr; x = 7y &hellip;&hellip;.(ii)<br>x का मान समीकरण (i) में रखने पर,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mn>7</mn><mi mathvariant=\"normal\">y</mi><mo>+</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mn>7</mn><mi mathvariant=\"normal\">y</mi><mo>-</mo><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 48<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mn>8</mn><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mrow><mn>6</mn><mi mathvariant=\"normal\">y</mi></mrow></mfrac></math> = 48<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6</mn><mi mathvariant=\"normal\">y</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mi mathvariant=\"normal\">y</mi></mfrac></math> = 48<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mi mathvariant=\"normal\">y</mi></mfrac></math> = 48 &rArr; y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>24</mn></mfrac></math> km/h</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. Let ABCD be a quadrilateral and AC, BD be diagonals. The length of BD = 20 cm and the heights of the triangles ABD and BCD are, respectively, 6 cm, 8 cm. Find the area of the quadrilateral ABCD (in cm<sup>2</sup>).</p>",
                    question_hi: "<p>62. माना ABCD एक चतुर्भुज है और AC, BD उसके विकर्ण है। BD की लंबाई 20 cm है और त्रिभुजों ABD और BCD की ऊँचाइयाँ क्रमशः 6 cm, 8 cm हैं। चतुर्भुज ABCD का क्षेत्रफल (cm<sup>2</sup> में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>140</p>", "<p>1282</p>", 
                                "<p>148</p>", "<p>120</p>"],
                    options_hi: ["<p>140</p>", "<p>1282</p>",
                                "<p>148</p>", "<p>120</p>"],
                    solution_en: "<p>62.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182600441.png\" alt=\"rId54\" width=\"204\" height=\"154\"><br>Area of quadrilateral ABCD = ar(<strong id=\"docs-internal-guid-38c2ba3b-7fff-e805-2539-c5e6de45e73f\">∆</strong>ABD) + ar(BCD)<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>(CM + AN) &times; BD<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>(8 + 6) &times; 20 = 140 cm<sup>2</sup></p>",
                    solution_hi: "<p>62.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182600441.png\" alt=\"rId54\" width=\"204\" height=\"154\"><br>चतुर्भुज ABCD का क्षेत्रफल = (<strong id=\"docs-internal-guid-38c2ba3b-7fff-e805-2539-c5e6de45e73f\">∆</strong>ABD) का क्षेत्रफल + (BCD) का क्षेत्रफल<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>(CM + AN) &times; BD<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>(8 + 6) &times; 20 = 140 cm<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. In an election between two candidates, 10% of the registered voters did not cast their vote. The winning candidate got 60% of the total votes cast and defeated the other candidate by 1242 votes. Find the total number of registered voters.</p>",
                    question_hi: "<p>63. दो उम्मीदवारों के बीच एक चुनाव में पंजीकृत मतदाताओं में से 10% ने अपना मत नहीं डाला। जीतने वाले उम्मीदवार ने डाले गए कुल मतों का 60% प्राप्त किया और दूसरे उम्मीदवार को 1242 मतों से हराया। पंजीकृत मतदाताओं की कुल संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>6200</p>", "<p>8640</p>", 
                                "<p>6900</p>", "<p>9600</p>"],
                    options_hi: ["<p>6200</p>", "<p>8640</p>",
                                "<p>6900</p>", "<p>9600</p>"],
                    solution_en: "<p>63.(c)&nbsp;Let total registered voters be x.<br>According to the question,<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times; (60 - 40)% = 1242<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 1242<br>x = 1242 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math> &times; 5 = 138 &times; 50 = 6900</p>",
                    solution_hi: "<p>63.(c)&nbsp;माना कुल पंजीकृत मतदाता x&nbsp;हैं<br>प्रश्न के अनुसार,<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times; (60 - 40)% = 1242<br>x &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>10</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> = 1242<br>x = 1242 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>9</mn></mfrac></math> &times; 5 = 138 &times; 50 = 6900</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. By selling an article at 66% of its marked price, a merchant makes a profit of 10%. If he sells the article at 80% of its marked price, then the profit percentage will be:</p>",
                    question_hi: "<p>64. एक वस्तु को उसके अंकित मूल्य के 66% पर बेचकर, एक व्यापारी 10% का लाभ अर्जित करता है। यदि वह वस्तु को उसके अंकित मूल्य के 80% पर बेचता है, तो उसका लाभ प्रतिशत कितना होगा ?</p>",
                    options_en: ["<p>33<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</p>", "<p>25%</p>", 
                                "<p>33<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</p>", "<p>20%</p>"],
                    options_hi: ["<p>33<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</p>", "<p>25%</p>",
                                "<p>33<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</p>", "<p>20%</p>"],
                    solution_en: "<p>64.(a)&nbsp;According to the question,<br>MP &times; 66% = CP &times; 110%<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>MP</mi><mi>CP</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math><br>New SP = 5 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 4 units<br>Profit% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>-</mo><mn>3</mn></mrow><mn>3</mn></mfrac></math> &times; 100 = 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</p>",
                    solution_hi: "<p>64.(a)&nbsp;प्रश्न के अनुसार,<br>अंकित मूल्य &times; 66% = क्रय मूल्य &times; 110%<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math><br>नया विक्रय मूल्य = 5 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 4 इकाई<br>लाभ% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>-</mo><mn>3</mn></mrow><mn>3</mn></mfrac></math> &times; 100 = 33<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A manufacturer announces a trade discount of 30% on his products for his dealers. One of the dealers purchased 6 smart phones manufactured by the company at the rate of Rs.25,000 each. What is the amount of trade discount ?</p>",
                    question_hi: "<p>65. एक निर्माता अपने डीलरों को अपने उत्पाद पर 30% की व्यापार छूट प्रदान करता है। एक डीलर Rs.25,000 प्रत्येक की दर से कंपनी द्वारा निर्मित 6 स्मार्ट फ़ोन खरीदता है। व्यापार छूट की राशि क्या होगी ?</p>",
                    options_en: ["<p>Rs.50,000</p>", "<p>Rs.55,000</p>", 
                                "<p>Rs.1,05,000</p>", "<p>Rs.45,000</p>"],
                    options_hi: ["<p>Rs.50,000</p>", "<p>Rs.55,000</p>",
                                "<p>Rs.1,05,000</p>", "<p>Rs.45,000</p>"],
                    solution_en: "<p>65.(d) <br>Amount of trade discount = 25000 &times; 6 &times; <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 45000</p>",
                    solution_hi: "<p>65.(d) <br>व्यापार छूट की राशि = 25000 &times; 6 &times; <math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 45000</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. Rohan buys a bike priced at ₹95,000. He pays ₹25,000 at once and the rest after 18 months, on which he is charged a simple interest at the rate of 10% per annum. The total amount (in ₹) he pays for the bike is:</p>",
                    question_hi: "<p>66. रोहन एक बाइक खरीदता है जिसकी कीमत ₹95,000 है। वह एक बार में ₹25,000 का भुगतान करता है और शेष 18 महीने के बाद, जिस पर उसे 10% की वार्षिक दर से साधारण ब्याज लगाया जाता है। उसके द्वारा बाइक के लिए भुगतान की गई कुल राशि (₹ में) क्&zwj;या है ?</p>",
                    options_en: ["<p>1,23,200</p>", "<p>1,02,320</p>", 
                                "<p>1,03,500</p>", "<p>1,05,500</p>"],
                    options_hi: ["<p>1,23,200</p>", "<p>1,02,320</p>",
                                "<p>1,03,500</p>", "<p>1,05,500</p>"],
                    solution_en: "<p>66.(d)&nbsp;Principal = 95000 - 25000 = ₹70,000<br>SI for 18 months = 70,000 &times;10% &times; <math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = ₹10500<br>Total amount paid for the bike = 95,000 + 10500 = ₹1,05,500</p>",
                    solution_hi: "<p>66.(d)&nbsp;मूलधन = 95000 - 25000 = ₹70,000<br>18 महीने के लिए साधारण ब्याज = 70,000 &times; 10% &times;&nbsp;<math display=\"inline\"><mfrac><mrow><mn>18</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = ₹10500<br>बाइक के लिए भुगतान की गई कुल राशि = 95,000 +10500 = ₹1,05,500</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The sum of two consecutive even numbers is 174. Find the smaller number.</p>",
                    question_hi: "<p>67. दो क्रमागत सम संख्याओं का योग 174 है। छोटी संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>84</p>", "<p>90</p>", 
                                "<p>86</p>", "<p>88</p>"],
                    options_hi: ["<p>84</p>", "<p>90</p>",
                                "<p>86</p>", "<p>88</p>"],
                    solution_en: "<p>67.(c)&nbsp;Let two consecutive even no. = x, x + 2<br>According to question,<br>x + x + 2 = 174<br>2x&nbsp;= 172<br>x = 86</p>",
                    solution_hi: "<p>67.(c)&nbsp;माना x, और x + 2 दो क्रमागत सम संख्याएं है ।&nbsp;<br>प्रश्न के अनुसार,<br>x + x + 2 = 174<br>2x&nbsp;= 172<br>x = 86</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. In an examination, the average marks of the student in class were 85.4. If the student had got 8 more marks in History, 12 more marks in Science, 10 more marks in Computer Science, 15 less marks in Hindi and retained the same marks in other subjects, then the average would have been 87.9. How many subjects were there in the examination ?</p>",
                    question_hi: "<p>68. किसी परीक्षा में, एक कक्षा में विद्यार्थी के औसत अंक 85.4 थे। यदि विद्यार्थी को इतिहास में 8 अंक अधिक मिलते, विज्ञान में 12 अंक अधिक मिलते, कंप्यूटर विज्ञान में 10 अंक अधिक मिलते, हिंदी में 15 अंक कम मिलते और अन्य विषयों में समान अंक बरकरार रहते, तो औसत 87.9 होता। परीक्षा में कितने विषय थे ?</p>",
                    options_en: ["<p>8</p>", "<p>6</p>", 
                                "<p>5</p>", "<p>7</p>"],
                    options_hi: ["<p>8</p>", "<p>6</p>",
                                "<p>5</p>", "<p>7</p>"],
                    solution_en: "<p>68.(b)<br>New average = old average + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>+</mo><mn>12</mn><mo>+</mo><mn>10</mn><mo>-</mo><mn>15</mn></mrow><mrow><mi>no</mi><mo>.</mo><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>subjects</mi></mrow></mfrac></math><br>87.9 = 85.4 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mrow><mi>no</mi><mo>.</mo><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>subjects</mi></mrow></mfrac></math><br>87.9 <math display=\"inline\"><mo>-</mo></math> 85.4 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mrow><mi>no</mi><mo>.</mo><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>subjects</mi></mrow></mfrac></math><br>2.5 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mrow><mi>no</mi><mo>.</mo><mi mathvariant=\"normal\">&#160;</mi><mi>of</mi><mi mathvariant=\"normal\">&#160;</mi><mi>subjects</mi></mrow></mfrac></math><br>No. of subjects = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 6 subjects</p>",
                    solution_hi: "<p>68.(b)<br>नया औसत = पुराना औसत + <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>+</mo><mn>12</mn><mo>+</mo><mn>10</mn><mo>-</mo><mn>15</mn></mrow><mrow><mi>&#2357;&#2367;&#2359;&#2351;&#2379;&#2306;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math><br>87.9 = 85.4 + <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mrow><mi>&#2357;&#2367;&#2359;&#2351;&#2379;&#2306;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow></mfrac></math><br>87.9 <math display=\"inline\"><mo>-</mo></math> 85.4 = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mrow><mi>&#2357;&#2367;&#2359;&#2351;&#2379;&#2306;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow></mfrac></math><br>2.5 = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mrow><mi>&#2357;&#2367;&#2359;&#2351;&#2379;&#2306;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow></mfrac></math><br>विषयों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>2</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 6 विषय</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The centres of two circles of radii 20 cm and 32 cm are 60 cm apart. What is the ratio of the length of the direct common tangent to the length of the transverse common tangent to these circles ?</p>",
                    question_hi: "<p>69. 20 cm और 32 cm त्रिज्याओं वाले दो वृत्तों के केंद्र एक-दूसरे से 60 cm की दूरी पर हैं। इन वृत्तों की सीधी उभयनिष्ठ स्पर्शरेखा की लंबाई और अनुप्रस्थ उभयनिष्ठ स्पर्शरेखा की लंबाई का अनुपात क्या है ?</p>",
                    options_en: ["<p>3<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> : <math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", 
                                "<p>3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> : <math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>7<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> : 3</p>"],
                    options_hi: ["<p>3<math display=\"inline\"><msqrt><mn>7</mn></msqrt></math> : <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p>3<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> : <math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>",
                                "<p>3<math display=\"inline\"><msqrt><mn>2</mn></msqrt></math> : <math display=\"inline\"><msqrt><mn>7</mn></msqrt></math></p>", "<p>7<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> : 3</p>"],
                    solution_en: "<p>69.(b) <br>Direct common tangent(DCT) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn><msup><mn>0</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>32</mn><mo>-</mo><mn>20</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3600</mn><mo>-</mo><mn>144</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3456</mn></msqrt></math><br>Transverse common tangent(TCT) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn><msup><mn>0</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>32</mn><mo>+</mo><mn>20</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3600</mn><mo>-</mo><mn>2704</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>896</mn></msqrt></math> <br>Required ratio = <math display=\"inline\"><msqrt><mfrac><mrow><mn>3456</mn></mrow><mrow><mn>896</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>27</mn><mn>7</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><msqrt><mn>7</mn></msqrt></mfrac></math> or 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>",
                    solution_hi: "<p>69.(b) <br>प्रत्यक्ष उभयनिष्ठ स्पर्शज्या(DCT) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn><msup><mn>0</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>32</mn><mo>-</mo><mn>20</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3600</mn><mo>-</mo><mn>144</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3456</mn></msqrt></math><br>अनुप्रस्थ उभयनिष्ठ स्पर्शज्या (TCT) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn><msup><mn>0</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>32</mn><mo>+</mo><mn>20</mn><msup><mo>)</mo><mn>2</mn></msup></msqrt></math>&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3600</mn><mo>-</mo><mn>2704</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>896</mn></msqrt></math><br>आवश्यक अनुपात = <math display=\"inline\"><msqrt><mfrac><mrow><mn>3456</mn></mrow><mrow><mn>896</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>27</mn><mn>7</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><msqrt><mn>3</mn></msqrt></mrow><msqrt><mn>7</mn></msqrt></mfrac></math> या 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>7</mn></msqrt></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The following pie chart shows the percentage distribution of the expenditure incurred in publishing a book. Study the pie chart and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182600616.png\" alt=\"rId55\" height=\"254\"> <br>If the expenditure on royalty in publishing certain number of books be <math display=\"inline\"><mi>&#8377;</mi></math>43,500, then what is the combined expenditure (in ₹) on transportation, binding and printing ?</p>",
                    question_hi: "<p>70. निम्नलिखित पाई चार्ट किसी पुस्तक के प्रकाशन में किए गए व्यय का प्रतिशत वितरण दर्शाता है। पाई चार्ट का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742182600775.png\" alt=\"rId56\" height=\"254\"><br>यदि निश्चित संख्या में पुस्तकों के प्रकाशन में रॉयल्टी पर व्यय रु. 43,500 है, तो परिवहन, बाइंडिंग और मुद्रण&nbsp;पर संयुक्त व्यय (रुपये में) कितना है ?</p>",
                    options_en: ["<p>1,97,900</p>", "<p>1,79,400</p>", 
                                "<p>1,79,900</p>", "<p>1,47,900</p>"],
                    options_hi: ["<p>1,97,900</p>", "<p>1,79,400</p>",
                                "<p>1,79,900</p>", "<p>1,47,900</p>"],
                    solution_en: "<p>70.(d) According to the question,<br>15 % = 43500<br>(10 + 16 + 25) % = 51% = <math display=\"inline\"><mfrac><mrow><mn>43500</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 51 = ₹147900</p>",
                    solution_hi: "<p>70.(d) प्रश्न के अनुसार,<br>15 % = 43500<br>(10 + 16 + 25) % = 51% = <math display=\"inline\"><mfrac><mrow><mn>43500</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math> &times; 51 = ₹147900</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Simplify the following expression.<br>(6 + 6 of 5 &divide; 5) &divide; 6 of 5 + (24 &divide; 2 of 3 - 6 of 5 + 4)</p>",
                    question_hi: "<p>71. निम्नलिखित व्यंजक को सरल कीजिए।<br>(6 + 6 का 5 &divide; 5) &divide; 6 का 5 + (24 &divide; 2 का 3 - 6 का 5 + 4)</p>",
                    options_en: ["<p>-26 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>-17 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", 
                                "<p>-21<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>-23 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>-26 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>-17 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                                "<p>-21<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p>-23 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>71.(c)<br>(6 + 6 of 5 &divide; 5) &divide; 6 of 5+ (24 &divide; 2 of 3 - 6 of 5 + 4)<br>(6+ 30 &divide; 5) &divide; 30 + (24 &divide; 6 - 30 + 4)<br>(6 + 6) &divide; 30 + (4 - 30 + 4)<br>12 &divide; 30 - 22<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> - 22 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>108</mn></mrow><mn>5</mn></mfrac></math> = -21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>71.(c)<br>(6 + 6 का 5 &divide; 5) &divide; 6 का 5 + (24 &divide; 2 का 3 - 6 का 5 + 4)<br>(6+ 30 &divide; 5) &divide; 30 + (24 &divide; 6 - 30 + 4)<br>(6 + 6) &divide; 30 + (4 - 30 + 4)<br>12 &divide; 30 - 22<br><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> - 22 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mn>108</mn></mrow><mn>5</mn></mfrac></math> = -21<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. In grade 11 of a school, 40 students opted for Physics, 17 opted for Biology, and 20 opted for Chemistry. If the total number of students in grade 11 was 60, all of these students opted for at least one of the three subjects mentioned here, and exactly five of these students opted for all these three subjects, what is the probability that a randomly selected student of grade 11 of this school would have opted for exactly one of these three subjects ?</p>",
                    question_hi: "<p>72. एक विद्यालय की कक्षा 11 में, 40 छात्रों ने भौतिक विज्ञान, 17 ने जीव विज्ञान और 20 ने रसायन विज्ञान विषय चुना। यदि कक्षा 11 में छात्रों की कुल संख्या 60 थी, और इन सभी छात्रों ने यहां वर्णित तीन विषयों में से कम से कम एक विषय चुना, और इनमें से ठीक पांच छात्रों ने सभी तीन विषय चुने, तो इस बात की क्या प्रायिकता है कि इस स्कूल के कक्षा 11 के एक यादृच्छिक रूप से चुने गए छात्र ने इन तीन विषयों में से ठीक एक विषय चुना होगा ?</p>",
                    options_en: ["<p>0.75</p>", "<p>0.85</p>", 
                                "<p>0.90</p>", "<p>0.80</p>"],
                    options_hi: ["<p>0.75</p>", "<p>0.85</p>",
                                "<p>0.90</p>", "<p>0.80</p>"],
                    solution_en: "<p>72.(d)<br><strong id=\"docs-internal-guid-c1681a3d-7fff-8029-f5cf-c90acaba8bcd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXefExc7nI8QbT4XLA1YaQf6rtFhTjEbqiBuSu3OeiI9Go4jd-VBMqmqknonviiwjE-NIUwHHESOIUkvu_cN1Mog3I6Wg2am1xknZtxcoLsdIsW22ZEyhnUssHf_zCAM_41x3xHbFg?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"192\" height=\"160\"></strong><br>&rArr; a + d + f + g = 40 <br>&rArr; b + d + g + e = 17<br>&rArr; c + f + g + e = 20<br>On adding above equation , we get <br>a + b + c + 2d + 2e + 2f + 3g = 77...e.q .(1)<br>and a + b + c + d + e + f + g = 60 &hellip;.e.q .(2)<br>on subtracting e.q .(2) from e.q .(1) , we get<br>d + e + f + 2g = 17<br>d + e + f + g = 17<math display=\"inline\"><mo>-</mo></math> 5 = 12<br>Number of students who opted exactly one subject = 60 -&nbsp;12 = 48 students<br>Required probability = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 0.80</p>",
                    solution_hi: "<p>72.(d)<br><strong id=\"docs-internal-guid-07bd1b74-7fff-f3c4-9125-310a717144f8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcZHeHdUqhMj9ge5bkldI_pVCrXy4VRAUb3vlpMsFjsxPvLD-f28Iu76N8-Lhvox2fK667JqUj_6gcI_E1qOzZAWs8n-kc8SO8sTx5Y7cIdJ5ZZHp3_WumEORGmemSVTPIABTpP?key=SD_iAeFXesHi0_rpwTMkljh3\" width=\"195\" height=\"143\"></strong><br>&rArr; a + d + f + g = 40 <br>&rArr; b + d + g + e = 17<br>&rArr; c + f + g + e = 20<br>उपरोक्त समीकरण को जोड़ने पर, हमें प्राप्त होता है<br>a + b + c + 2d + 2e + 2f + 3g = 77..e.q .(1)<br>और a + b + c + d + e + f + g = 60...e.q .(2)<br>e. q .(1) से e.q .(2) घटाने पर, हमें प्राप्त होता है<br>d + e + f + 2g = 17<br>d + e + f + g = 17 -&nbsp;5 = 12<br>सिर्फ एक विषय चुनने वाले विद्यार्थियों की संख्या <br>= 60 -&nbsp;12 = 48 छात्र<br>अभीष्ट प्रायिकता = <math display=\"inline\"><mfrac><mrow><mn>48</mn></mrow><mrow><mn>60</mn></mrow></mfrac></math> = 0.80</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msup><mi>tan</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cot</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> is equal to ___________.</p>",
                    question_hi: "<p>73. <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>t</mi><mi>a</mi><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup><mn>2</mn><mi>A</mi></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi><mo>.</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mn>2</mn><mi>A</mi></mrow></mfrac></math> का मान ____________ के बराबर है।</p>",
                    options_en: ["<p>sec2A + tan2A</p>", "<p>sec2A + cosec2A</p>", 
                                "<p>cot2A - cosec2A</p>", "<p>sec2A.cosec2A</p>"],
                    options_hi: ["<p>sec2A + tan2A</p>", "<p>sec2A + cosec2A</p>",
                                "<p>cot2A - cosec2A</p>", "<p>sec2A.cosec2A</p>"],
                    solution_en: "<p>73.(d)<strong>&nbsp;Formula used :</strong> <br>sec<sup>2</sup>&theta; = 1 + tan<sup>2</sup>&theta; and cosec<sup>2</sup>&theta; = 1 + cot<sup>2</sup>&theta;<br>now,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msup><mi>tan</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cot</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mi>tan</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>1</mn><mo>+</mo><msup><mi>cot</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cosec</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo></mrow><mrow><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mn>1</mn><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac><mfrac><mn>1</mn><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></mstyle></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> = sec2A.cosec2A</p>",
                    solution_hi: "<p>73.(d)&nbsp;<strong>सूत्र का प्रयोग करने पर :</strong> <br>sec<sup>2</sup>&theta; = 1 + tan<sup>2</sup>&theta; and cosec<sup>2</sup>&theta; = 1 + cot<sup>2</sup>&theta;<br>अब, </p>\n<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>+</mo><msup><mi>tan</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cot</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><msup><mi>tan</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>+</mo><mn>1</mn><mo>+</mo><msup><mi>cot</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cosec</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sec</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>cosec</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo></mrow><mrow><mo>.</mo><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cosec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sec</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mfrac><mn>1</mn><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac><mfrac><mn>1</mn><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></mstyle></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>+</mo><msup><mi>cos</mi><mn>2</mn></msup><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mi>cos</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi><mo>.</mo><mi>sin</mi><mn>2</mn><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math> = sec2A.cosec2A</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A police travelling at 65 km/h is chasing a thief who is 1200 m away from him and is travelling at 41 km/h. Find the time taken by the policeman to catch the thief.</p>",
                    question_hi: "<p>74. 65 km/h की चाल से दौड़ रहा एक पुलिसकर्मी एक चोर का पीछा कर रहा है जो उससे 1200 m दूर है और 41 km/h की चाल से दौड़ रहा है। पुलिसकर्मी द्वारा चोर को पकड़ने में लगने वाला समय ज्ञात कीजिए।</p>",
                    options_en: ["<p>4 min</p>", "<p>1 min</p>", 
                                "<p>2 min</p>", "<p>3 min</p>"],
                    options_hi: ["<p>4 मिनट</p>", "<p>1 मिनट</p>",
                                "<p>2 मिनट</p>", "<p>3 मिनट</p>"],
                    solution_en: "<p>74.(d)&nbsp;Relative speed, when travelling in same direction = 65 - 41 = 24 km/hr<br>Time taken by the policeman to catch the thief = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math> &times; 60 = 3 min</p>",
                    solution_hi: "<p>74.(d)&nbsp;समान दिशा में यात्रा करते समय सापेक्ष गति = 65 - 41 = 24 किमी/घंटा<br>पुलिसकर्मी द्वारा चोर को पकड़ने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>2</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>20</mn></mfrac></math> &times; 60 = 3 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. The least common multiple of 6 and 29 is X, the greatest common multiple of 6 and 29 is Y. So what is the value of (X + 4Y) ?</p>",
                    question_hi: "<p>75. 6 और 29 का लघुत्तम समापवर्त्य X है, 6 और 29 का महत्तम समापवर्तक Y है। तो (X + 4Y) का मान कितना है ?</p>",
                    options_en: ["<p>180</p>", "<p>190</p>", 
                                "<p>244</p>", "<p>178</p>"],
                    options_hi: ["<p>180</p>", "<p>190</p>",
                                "<p>244</p>", "<p>178</p>"],
                    solution_en: "<p>75.(d) LCM of (6, 29) = 174 = X<br>HCM of (6, 29) = 1 = Y<br>Now,<br>(X + 4Y) = (174 + 4 &times; 1) = 178</p>",
                    solution_hi: "<p>75.(d) (6, 29) का LCM = 174 = X<br>(6, 29) का HCM = 1 = Y<br>अब,<br>(X + 4Y) = (174 + 4 &times; 1) = 178</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate ANTONYM of the underlined word.<br>The brave knight set out to <span style=\"text-decoration: underline;\">vanquish</span> the fearsome dragon and protect the kingdom from its menace.</p>",
                    question_hi: "<p>76. Select the most appropriate ANTONYM of the underlined word.<br>The brave knight set out to <span style=\"text-decoration: underline;\">vanquish</span> the fearsome dragon and protect the kingdom from its menace.</p>",
                    options_en: ["<p>Engage</p>", "<p>Instill</p>", 
                                "<p>Measure</p>", "<p>Liberate</p>"],
                    options_hi: ["<p>Engage</p>", "<p>Instill</p>",
                                "<p>Measure</p>", "<p>Liberate</p>"],
                    solution_en: "<p>76.(d) <strong>Liberate-</strong> to set free or release.<br><strong>Vanquish-</strong> to defeat thoroughly or conquer.<br><strong>Engage-</strong> to participate or become involved.<br><strong>Instill-</strong> to gradually impart or introduce an idea or feeling.<br><strong>Measure-</strong> to discover the exact size or amount of something.</p>",
                    solution_hi: "<p>76.(d) <strong>Liberate</strong> (आजाद कराने) - to set free or release.<br><strong>Vanquish</strong> (जीतना) - to defeat thoroughly or conquer.<br><strong>Engage</strong> (काम पर लगाना) - to participate or become involved.<br><strong>Instill</strong> (स्थापित करना) - to gradually impart or introduce an idea or feeling.<br><strong>Measure</strong> (मापना) - to discover the exact size or amount of something.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If you don&rsquo;t find any error, mark &lsquo;No error&rsquo; as your answer.<br>When Ramu was working / as a clerk, / he asked me money.</p>",
                    question_hi: "<p>77. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error&nbsp;from the given options. If you don&rsquo;t find any error, mark &lsquo;No error&rsquo; as your answer.<br>When Ramu was working / as a clerk, / he asked me money.</p>",
                    options_en: ["<p>No error</p>", "<p>he asked me money</p>", 
                                "<p>as a clerk</p>", "<p>When Ramu was working</p>"],
                    options_hi: ["<p>No error</p>", "<p>he asked me money</p>",
                                "<p>as a clerk</p>", "<p>When Ramu was working</p>"],
                    solution_en: "<p>77.(b) he asked me money<br>&lsquo;Asked me money&rsquo; must be replaced with &lsquo;asked me for money&rsquo;. The phrasal verb &lsquo;ask for&rsquo; means to try to obtain something by requesting. Similarly, Ramu tried to obtain some money by requesting. Hence, &lsquo;he asked me for money&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(b) he asked me money<br>&lsquo;Asked me money&rsquo; के स्थान पर &lsquo;asked me for money&rsquo; का प्रयोग करना चाहिए। Phrasal verb &lsquo;ask for&rsquo; का अर्थ है अनुरोध करके कुछ प्राप्त करने का प्रयास करना। इसी तरह, रामू ने अनुरोध करके कुछ पैसे प्राप्त करने का प्रयास किया। इसलिए, &lsquo;he asked me for money&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Enough money will have been saved by me for a new house by next year</p>",
                    question_hi: "<p>78. Enough money will have been saved by me for a new house by next year</p>",
                    options_en: ["<p>I will have saved enough money for a new house by next year.</p>", "<p>I will be saving enough money for a new house by next year.</p>", 
                                "<p>I will have been saving enough money for a new house by next year.</p>", "<p>I will save enough money for a new house by next year.</p>"],
                    options_hi: ["<p>I will have saved enough money for a new house by next year.</p>", "<p>I will be saving enough money for a new house by next year.</p>",
                                "<p>I will have been saving enough money for a new house by next year.</p>", "<p>I will save enough money for a new house by next year.</p>"],
                    solution_en: "<p>78.(a) I will have saved enough money for a new house by&nbsp;next year.<br>(a) I will have saved enough money for a new house by next year. (Correct)<br>(b) I will be <span style=\"text-decoration: underline;\">saving</span> enough money for a new house by next year. (Incorrect Verb)<br>(c) I will <span style=\"text-decoration: underline;\">have been saving</span> enough money for a new house by next year. (Incorrect Tense)<br>(d) I will <span style=\"text-decoration: underline;\">save</span> enough money for a new house by next year. (Incorrect Verb)</p>",
                    solution_hi: "<p>78.(a) I will have saved enough money for a new house by next year. (Correct)<br>(b) I will be <span style=\"text-decoration: underline;\">saving</span> enough money for a new house by next Year. (गलत verb (will be <span style=\"text-decoration: underline;\">saving)</span> का प्रयोग किया गया है । (will have saved) का प्रयोग होगा। )<br>(c) I will <span style=\"text-decoration: underline;\">have been saving</span> enough money for a new house by next year. (गलत verb (<span style=\"text-decoration: underline;\">have been saving</span>) का प्रयोग किया गया है | (will have saved) का प्रयोग होगा I ) <br>(d) I will <span style=\"text-decoration: underline;\">save</span> enough money for a new house by next Year. (गलत verb <span style=\"text-decoration: underline;\">(save)</span> का प्रयोग किया गया है। (will have saved) का प्रयोग होगा । )</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>One day the hare began to <strong><span style=\"text-decoration: underline;\">made fun of</span></strong> the tortoise as it moved slowly.</p>",
                    question_hi: "<p>79. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>One day the hare began to <strong><span style=\"text-decoration: underline;\">made fun of</span></strong> the tortoise as it moved slowly.</p>",
                    options_en: ["<p>mock</p>", "<p>shout at</p>", 
                                "<p>No Improvement</p>", "<p>torture</p>"],
                    options_hi: ["<p>mock</p>", "<p>shout at</p>",
                                "<p>No Improvement</p>", "<p>torture</p>"],
                    solution_en: "<p>79.(a) mock<br>Mock (Verb) - to laugh at somebody/something in an unkind way ; make fun of.</p>",
                    solution_hi: "<p>79.(a) mock<br>Mock (Verb) - किसी पर / किसी चीज पर द्वेषपूर्ण तरीके से हंसना ; मज़ाक उड़ाना</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Walking zones have been demarcated using paints and cones by the municipal corporation.</p>",
                    question_hi: "<p>80. Walking zones have been demarcated using paints and cones by the municipal corporation.</p>",
                    options_en: ["<p>Walking zones are demarcating the municipal corporation using paints and cones.</p>", "<p>The municipal corporation has demarcated walking zones using paints and cones.</p>", 
                                "<p>The municipal corporation will demarcate walking zones using paints and cones.</p>", "<p>The municipal corporation is demarcating walking zones using paints and cones.</p>"],
                    options_hi: ["<p>Walking zones are demarcating the municipal corporation using paints and cones.</p>", "<p>The municipal corporation has demarcated walking zones using paints and cones.</p>",
                                "<p>The municipal corporation will demarcate walking zones using paints and cones.</p>", "<p>The municipal corporation is demarcating walking zones using paints and cones.</p>"],
                    solution_en: "<p>80.(b) The municipal corporation has demarcated walking zones using paints and cones.<br>(a) Walking zones are demarcating the <span style=\"text-decoration: underline;\">municipal corporation</span> using paints and cones. (Incorrect placement of Subject)<br>(b) The municipal corporation has demarcated walking zones using paints and cones. (Correct)<br>(c) The municipal corporation <span style=\"text-decoration: underline;\">will demarcate</span> walking zones using paints and cones. (Incorrect Tense)<br>(d) The municipal corporation <span style=\"text-decoration: underline;\">is demarcating</span> walking zones using paints and cones. (Incorrect Tense)</p>",
                    solution_hi: "<p>80.(b) The municipal corporation has demarcated walking zones using paints and cones.<br>(a) Walking zones are demarcating the <span style=\"text-decoration: underline;\">municipal corporation</span> using paints and cones. (&lsquo;subject&rsquo; का प्रयोग गलत स्थान पर किया गया है | (The municipal corporation Walking zones are) का प्रयोग होगा | ) <br>(c) The municipal corporation <span style=\"text-decoration: underline;\">will demarcate</span> walking zones using paints and cones.(गलत tense (simple future) का प्रयोग किया गया है | has demarcated (present perfect) का प्रयोग होगा I ) <br>(d) The municipal corporation <span style=\"text-decoration: underline;\">is demarcating</span> walking zones using paints and cones. (गलत tense (Present continuous) का प्रयोग किया गया है | has demarcated (present perfect) का प्रयोग होगा | )</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the option that can be used as a one-word substitute for the given group of words.<br>A list of books</p>",
                    question_hi: "<p>81. Select the option that can be used as a one-word substitute for the given group of words.<br>A list of books</p>",
                    options_en: ["<p>Bibliography</p>", "<p>Catalogue</p>", 
                                "<p>Lexicon</p>", "<p>Manuscript</p>"],
                    options_hi: ["<p>Bibliography</p>", "<p>Catalogue</p>",
                                "<p>Lexicon</p>", "<p>Manuscript</p>"],
                    solution_en: "<p>81.(b) <strong>Catalogue-</strong> a list of books.<br><strong>Bibliography-</strong> a list of the books referred to in a scholarly work, typically printed as an appendix.<br><strong>Lexicon-</strong> a list of all the words used in a particular language or subject.<br><strong>Manuscript-</strong> a book, document, or piece of music written by hand rather than typed or printed.</p>",
                    solution_hi: "<p>81.(b) <strong>Catalogue</strong> (सूची) - a list of books.<br><strong>Bibliography</strong> (ग्रन्थसूची) - a list of the books referred to in a scholarly work, typically printed as an appendix.<br><strong>Lexicon</strong> (शब्दकोश) - a list of all the words used in a particular language or subject.<br><strong>Manuscript</strong> (हस्तलिपि) - a book, document, or piece of music written by hand rather than typed or printed.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. Bepin Babu didn&rsquo;t beat about the bush. &ldquo;Listen, Chuni - I want to ask you something. <br>Q. Bepin Babu hurried down the stairs and into the living room.<br>R. It struck Bepin Babu that Chuni might remember something about the &rsquo;1958 trip. <br>S. Chuni was about to leave, but seeing Bepin Babu appear, he turned round hopefully.</p>",
                    question_hi: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. Bepin Babu didn&rsquo;t beat about the bush. &ldquo;Listen, Chuni - I want to ask you something. <br>Q. Bepin Babu hurried down the stairs and into the living room.<br>R. It struck Bepin Babu that Chuni might remember something about the &rsquo;1958 trip. <br>S. Chuni was about to leave, but seeing Bepin Babu appear, he turned round hopefully.</p>",
                    options_en: ["<p>QRSP</p>", "<p>RQSP</p>", 
                                "<p>SRQP</p>", "<p>SRPQ</p>"],
                    options_hi: ["<p>QRSP</p>", "<p>RQSP</p>",
                                "<p>SRQP</p>", "<p>SRPQ</p>"],
                    solution_en: "<p>82.(b)RQSP<br>Sentence R will be the starting line as it contains the main idea of the parajumble i.e. Bepin&rsquo;s worry about the trip of 1958. And, Sentence Q states Bepin hurried down the stairs to talk to Chuni. So, Q will follow R. Further, Sentence S states that Chuni was about to leave and Sentence P states that Bepin directly said to him that he wanted to ask something. So, P will follow S. Going through the options, option (b) has the correct sequence.</p>",
                    solution_hi: "<p>82.(b)RQSP<br>Sentence R starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;Bepin&rsquo;s worry about the trip of 1958&rsquo; शामिल है। हालांकि, Sentence Q बताता है कि बिपिन चुन्नी से बात करने के लिए सीढ़ियों से नीचे उतरा। तो, R के बाद Q आएगा। आगे,Sentence S बताता है कि चुन्नी जाने वाली थी और Sentence P बताता है कि बिपिन ने सीधे उससे कहा कि वह कुछ पूछना चाहता है। तो, S के बाद P आएगा । Options के माध्यम से जाने पर , option (b) में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate option to fill in the blank.<br>The grass on my lawn is glistening with the morning__________.</p>",
                    question_hi: "<p>83. Select the most appropriate option to fill in the blank.<br>The grass on my lawn is glistening with the morning__________.</p>",
                    options_en: ["<p>dew</p>", "<p>do</p>", 
                                "<p>duo</p>", "<p>due</p>"],
                    options_hi: ["<p>dew</p>", "<p>do</p>",
                                "<p>duo</p>", "<p>due</p>"],
                    solution_en: "<p>83.(a) dew<br>&lsquo;Dew&rsquo; means drops of water that form on surfaces during the night. The given sentence states that the grass on my lawn is glistening with the morning dew. Hence, &lsquo;dew&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>83.(a) dew<br>&lsquo;Dew&rsquo; का अर्थ है रात्रि के समय सतहों (surfaces) पर बनने वाली जल की बूंदें (drops)। दिए गए sentence में कहा गया है कि मेरे लॉन की घास सुबह की ओस से चमक (glistening) रही है। अतः, &lsquo;dew&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Rectify the sentence by selecting the correct spelling of the underlined word from the options. <br>The baby smiled and laughed at the <span style=\"text-decoration: underline;\">pickaboo</span> game.</p>",
                    question_hi: "<p>84. Rectify the sentence by selecting the correct spelling of the underlined word from the options. <br>The baby smiled and laughed at the <span style=\"text-decoration: underline;\">pickaboo</span> game.</p>",
                    options_en: ["<p>pekaboo</p>", "<p>peakaboo</p>", 
                                "<p>pikaboo</p>", "<p>peekaboo</p>"],
                    options_hi: ["<p>pekaboo</p>", "<p>peakaboo</p>",
                                "<p>pikaboo</p>", "<p>peekaboo</p>"],
                    solution_en: "<p>84.(d) peekaboo<br>&lsquo;Peekaboo&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>84.(d) peekaboo<br>&lsquo;Peekaboo&rsquo; सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate meaning of the given idiom. <br>Ignoring his father&rsquo;s warnings, he said, &ldquo;<span style=\"text-decoration: underline;\">an elephant in the room</span>&rdquo; to his mother.</p>",
                    question_hi: "<p>85. Select the most appropriate meaning of the given idiom. <br>Ignoring his father&rsquo;s warnings, he said, &ldquo;<span style=\"text-decoration: underline;\">an elephant in the room</span>&rdquo; to his mother.</p>",
                    options_en: ["<p>There is an obvious problem</p>", "<p>Someone hiding in the room</p>", 
                                "<p>The elephant got stuck in the room</p>", "<p>Someone begins to suspect</p>"],
                    options_hi: ["<p>There is an obvious problem</p>", "<p>Someone hiding in the room</p>",
                                "<p>The elephant got stuck in the room</p>", "<p>Someone begins to suspect</p>"],
                    solution_en: "<p>85.(a) <strong>An elephant in the room</strong> - there is an obvious problem.</p>",
                    solution_hi: "<p>85.(a) <strong>An elephant in the room </strong>- there is an obvious problem./ स्पष्ट समस्या होना</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Identify the segment in the sentence, which contains error.<br>When Sanathan asked Mr. Jignesh / for a fresh evaluation of his performance / in the last financial year, / little did him know that the results will come out worse than expected.</p>",
                    question_hi: "<p>86. Identify the segment in the sentence, which contains error.<br>When Sanathan asked Mr. Jignesh / for a fresh evaluation of his performance / in the last financial year, / little did him know that the results will come out worse than expected.</p>",
                    options_en: ["<p>When Sanathan asked Mr. Jignesh</p>", "<p>for a fresh evaluation of his performance</p>", 
                                "<p>in the last financial year</p>", "<p>little did him know that the results will come out worse than expected</p>"],
                    options_hi: ["<p>When Sanathan asked Mr. Jignesh</p>", "<p>for a fresh evaluation of his performance</p>",
                                "<p>in the last financial year</p>", "<p>little did him know that the results will come out worse than expected</p>"],
                    solution_en: "<p>86.(d) We will replace the pronoun &lsquo;him&rsquo; with the correct pronoun &lsquo;he&rsquo; after the verb &lsquo;did&rsquo; in the given sentence. Hence, &lsquo;little did he know that the results will come out worse than expected&rsquo; is the mostm appropriate structure.</p>",
                    solution_hi: "<p>86.(d) Him के स्थान पर he का प्रयोग होगा । इसलिए, &lsquo;little did he know that the results will come out worse than expected&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>The chairman said, &ldquo;Ladies and gentlemen I should thank you all&rdquo;.</p>",
                    question_hi: "<p>87. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>The chairman said, &ldquo;Ladies and gentlemen I should thank you all&rdquo;.</p>",
                    options_en: ["<p>The chairman thanked the ladies and gentlemen.</p>", "<p>The chairman addressed public that they should thank him.</p>", 
                                "<p>The chairman addressed the public as ladies and gentlemen and said that he should thank them all.</p>", "<p>Addressing the public as ladies and gentlemen, the chairman told them that they should thank them all.</p>"],
                    options_hi: ["<p>The chairman thanked the ladies and gentlemen.</p>", "<p>The chairman addressed public that they should thank him.</p>",
                                "<p>The chairman addressed the public as ladies and gentlemen and said that he should thank them all.</p>", "<p>Addressing the public as ladies and gentlemen, the chairman told them that they should thank them all.</p>"],
                    solution_en: "<p>87.(c) The chairman addressed the public as ladies and gentlemen and said that he should thank them all.<br>(a) The chairman <strong>thanked</strong> the ladies and gentlemen. (Incorrect Reporting Verb)<br>(b) The chairman addressed <strong>public</strong> that they should thank him. (Listener isincorrect)<br>(d) Addressing the public as ladies and gentlemen, the chairman <strong>told</strong> that heshould thank them all. (Incorrect word)</p>",
                    solution_hi: "<p>87.(c) The chairman addressed the public as ladies and gentlemen and said that he should thank them all.<br>(a) The chairman <strong>thanked</strong> the ladies and gentlemen. (गलत Reporting Verb )<br>(b) The chairman addressed <strong>public</strong> that they should thank him. ( गलत Listener )<br>(d) Addressing the public as ladies and gentlemen, the chairman <strong>told</strong> that he should thank them all. (गलत word)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88.&nbsp;Four alternatives are given for the idiom/phrase underlined in the sentence. Choose the alternative which best expresses the meaning&nbsp; of the idiom/phrase.<br>Caesar was <strong><span style=\"text-decoration: underline;\">done to death</span></strong> by the conspirators.</p>",
                    question_hi: "<p>88.&nbsp;Four alternatives are given for the idiom/phrase underlined in the sentence. Choose the alternative which best expresses the meaning&nbsp; of the idiom/phrase.<br>Caesar was <strong><span style=\"text-decoration: underline;\">done to death</span></strong> by the conspirators.</p>",
                    options_en: ["<p>attacked</p>", "<p>removed</p>", 
                                "<p>eliminated</p>", "<p>murdered</p>"],
                    options_hi: ["<p>attacked</p>", "<p>removed</p>",
                                "<p>eliminated</p>", "<p>murdered</p>"],
                    solution_en: "<p>88.(d) <strong>Done to death</strong> - murdered</p>",
                    solution_hi: "<p>88.(d) <strong>Done to death</strong> - murdered/ हत्या करना</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. Her geography teacher had shown her class the video, and told them that tsunamis can be caused by earthquakes, volcanoes and landslides.<br>Q. Tilly saw the sea slowly rise, and start to foam, bubble and form whirlpools.<br>R. Tilly started to scream at her family to get off the beach. <br>S. She remembered that she had seen this in class in a video of a tsunami that had hit Japan.</p>",
                    question_hi: "<p>89. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>P. Her geography teacher had shown her class the video, and told them that tsunamis can be caused by earthquakes, volcanoes and landslides.<br>Q. Tilly saw the sea slowly rise, and start to foam, bubble and form whirlpools.<br>R. Tilly started to scream at her family to get off the beach. <br>S. She remembered that she had seen this in class in a video of a tsunami that had hit Japan.</p>",
                    options_en: ["<p>RQSP</p>", "<p>RSQP</p>", 
                                "<p>QSPR</p>", "<p>SPRQ</p>"],
                    options_hi: ["<p>RQSP</p>", "<p>RSQP</p>",
                                "<p>QSPR</p>", "<p>SPRQ</p>"],
                    solution_en: "<p>89. (c) QSPR<br>Sentence Q will be the starting line as it contains the main subject of the parajumble i.e.Tilly,who saw the sea slowly rise. And, Sentence S states what she remembered after seeing that. So, S will follow Q. Further, Sentence P states about the videos which she had seen in the class and Sentence R states how she reacted in that situation. So, R will follow P. Going through the options, option (c) has the correct sequence.</p>",
                    solution_hi: "<p>89. (c) QSPR<br>Sentence Q starting line होगी क्योंकि इसमें parajumble का मुख्य विषय &lsquo;Tilly,who saw the sea slowly rise&rsquo; शामिल है । Sentence S बताता है कि उसे देखने के बाद उसे क्या याद आया। तो, Q के बाद S आएगा । आगे, Sentence P उन वीडियो के बारे में बताता है जो उसने कक्षा में देखे थे और Sentence R बताता है कि उसने उस स्थिति में कैसे प्रतिक्रिया दी। इसलिए, P के बाद R आएगा। Options के माध्यम से जाने पर , option (c) में सही sequence है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the word which means the same as the group of words given.<br>A gentle song sung to put a child to sleep</p>",
                    question_hi: "<p>90. Select the word which means the same as the group of words given.<br>A gentle song sung to put a child to sleep</p>",
                    options_en: ["<p>Lullaby</p>", "<p>Chorus</p>", 
                                "<p>Prayer</p>", "<p>Hymn</p>"],
                    options_hi: ["<p>Lullaby</p>", "<p>Chorus</p>",
                                "<p>Prayer</p>", "<p>Hymn</p>"],
                    solution_en: "<p>90.(a) Lullaby<br><strong>Lullaby</strong> - A gentle song sung to put a child to sleep <br><strong>Chorus</strong> - to sing or say something together <br><strong>Prayer</strong> - the act of speaking to God<br><strong>Hymn-</strong> a religious song that Christians sing together in church</p>",
                    solution_hi: "<p>90.(a) Lullaby<br><strong>Lullaby</strong> (लोरी)- A gentle song sung to put a child to sleep <br><strong>Chorus</strong> (सहगान)- to sing or say something together <br><strong>Prayer</strong> (प्रार्थना)- the act of speaking to God<br><strong>Hymn</strong> (भजन)- a religious song that Christians sing together in church</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym of the word in brackets to fill in the blank.<br>Emotional stimulation provides the necessary __________ for creative imagination. (fecundity)</p>",
                    question_hi: "<p>91. Select the most appropriate synonym of the word in brackets to fill in the blank.<br>Emotional stimulation provides the necessary __________ for creative imagination. (fecundity)</p>",
                    options_en: ["<p>freedom</p>", "<p>fertility</p>", 
                                "<p>fulcrum</p>", "<p>force</p>"],
                    options_hi: ["<p>freedom</p>", "<p>fertility</p>",
                                "<p>fulcrum</p>", "<p>force</p>"],
                    solution_en: "<p>91.(b) <strong>Fertility-</strong> the ability to produce or create abundantly.<br><strong>Fecundity-</strong> the ability to produce an abundance of ideas or offspring.<br><strong>Freedom-</strong> the state of being free or unrestricted.<br><strong>Fulcrum-</strong> a point of support or balance.<br><strong>Force-</strong> strength or energy as an attribute of physical action or movement.</p>",
                    solution_hi: "<p>91.(b) <strong>Fertility</strong> (उर्वरता) - the ability to produce or create abundantly.<br><strong>Fecundity</strong> (प्रजनन क्षमता) - the ability to produce an abundance of ideas or offspring.<br><strong>Freedom</strong> (स्वतंत्रता) - the state of being free or unrestricted.<br><strong>Fulcrum</strong> (आधार) - a point of support or balance.<br><strong>Force</strong> (बल) - strength or energy as an attribute of physical action or movement.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Had I known that you were planning to visit me, <span style=\"text-decoration: underline;\">I should have done</span> all the necessary arrangements.</p>",
                    question_hi: "<p>92. Select the most appropriate option that can substitute the underlined segment in the given sentence.<br>Had I known that you were planning to visit me, <span style=\"text-decoration: underline;\">I should have done</span> all the necessary arrangements.</p>",
                    options_en: ["<p>I would have do</p>", "<p>I would have made</p>", 
                                "<p>I must have made</p>", "<p>I should have made</p>"],
                    options_hi: ["<p>I would have do</p>", "<p>I would have made</p>",
                                "<p>I must have made</p>", "<p>I should have made</p>"],
                    solution_en: "<p>92.(b) I would have made<br>The given sentence is an example of the third conditional sentence and &lsquo;&rsquo;(if + Sub. + had + V<sub>3</sub>)/(Had + Sub. + V<sub>3</sub>) &hellip; would have + V<sub>3</sub>&rsquo; is the correct grammatical structure for it. Therefore, &lsquo;should&rsquo; must be replaced with &lsquo;would&rsquo;. Hence, &lsquo;I would have made(V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>92.(b) I would have made<br>दिया गया sentence third conditional sentence का उदाहरण है और &lsquo;&rsquo;(if + Sub. + had + V<sub>3</sub>)/(Had + Sub. + V<sub>3</sub>) &hellip; would have + V<sub>3</sub>&rsquo; इसके लिए सही grammatical structure है। इसलिए, &lsquo;should&rsquo; के स्थान पर &lsquo;would&rsquo; का प्रयोग करना चाहिए। इसलिए, &lsquo;I would have made(V<sub>3</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93.Find a word that is the synonym of :<br>SQUANDER</p>",
                    question_hi: "<p>93.Find a word that is the synonym of :<br>SQUANDER</p>",
                    options_en: ["<p>expensive</p>", "<p>waste</p>", 
                                "<p>litter</p>", "<p>economical</p>"],
                    options_hi: ["<p>expensive</p>", "<p>waste</p>",
                                "<p>litter</p>", "<p>economical</p>"],
                    solution_en: "<p>93.(b) <strong>waste</strong><br><strong>Squander</strong> - Waste (something, especially money or time) in a reckless and foolish manner.</p>",
                    solution_hi: "<p>93.(b) <strong>waste</strong><br><strong>Squander</strong> - बेकार (विशेष रूप से पैसा या समय) लापरवाह और मूर्ख तरीके से।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Choose the word opposite in meaning to the given word as your answer.<br>Primitive</p>",
                    question_hi: "<p>94. Choose the word opposite in meaning to the given word as your answer.<br>Primitive</p>",
                    options_en: ["<p>Technical</p>", "<p>Racial</p>", 
                                "<p>Sophisticated</p>", "<p>Agricultural</p>"],
                    options_hi: ["<p>Technical</p>", "<p>Racial</p>",
                                "<p>Sophisticated</p>", "<p>Agricultural</p>"],
                    solution_en: "<p>94.(c) <strong>Primitive-</strong> very simple and not developed<br><strong>Sophisticated-</strong> advanced and able to understand difficult or complicated things<br><strong>Technical-</strong> connected with the practical use of machines, methods, etc. in science and industry<br><strong>Racial-</strong> connected with people&rsquo;s race<br><strong>Agricultural-</strong> the science, art, or practice of cultivating the soil, producing crop</p>",
                    solution_hi: "<p>94.(c) <strong>Primitive</strong> (प्राचीन) - very simple and not developed<br><strong>Sophisticated</strong> (जटिल) - advanced and able to understand difficult or complicated things<br><strong>Technical</strong> (तकनीकी) - connected with the practical use of machines, methods, etc. in science and industry<br><strong>Racial(जातीय)-Racial</strong> describes things relating to people\'s race.<br><strong>Agricultural</strong> ( कृषि संबंधी ) - the science, art, or practice of cultivating the soil, producing crop</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>Health is too important to be ______.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>Health is too important to be ______.</p>",
                    options_en: ["<p>neglected</p>", "<p>despised</p>", 
                                "<p>detested</p>", "<p>discarded</p>"],
                    options_hi: ["<p>neglected</p>", "<p>despised</p>",
                                "<p>detested</p>", "<p>discarded</p>"],
                    solution_en: "<p>95. (a) neglected<br>&lsquo;Neglected&rsquo; means to give little attention or respect to somebody/something. The given sentence states that health is too important to be neglected. Hence, &lsquo;neglected&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(a) neglected/नजरअंदाज करना, उपेक्षा करना<br>\'Neglected\' का अर्थ है की किसी को थोड़ा ध्यान या सम्मान देना। दिया गया वाक्य यह बताता है &lsquo;health is too important to be neglected&rsquo;/स्वास्थ्य इतना जरुरी है कि उसकी उपेक्षा नहीं कि जा सकती। इसलिए, \'neglected\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test :</strong><br>During the days of slavery, there lived (96)_____ very hard-working slave. One day, (97)_____ freedom, he ran away from his master\'s house. As he passed a forest, he saw a lion (98)____ with pain, for a huge thorn had pierced its paw. The slave felt sorry for the lion and took out the thorn gently. The grateful lion licked the slave\'s hand. But just then, both of (99)_____ were captured by the soldiers. The king ordered, &ldquo;Let the runaway slave (100)_____ thrown in front of a hungry lion.&rdquo; But, when the slave was thrown in front of the lion, the lion licked him and quietly sat beside him. The amazed king asked the reason. The slave told the king everything. The king pardoned the slave. The lion was also freed and sent to the forest.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test :</strong><br>During the days of slavery, there lived (96)_____ very hard-working slave. One day, (97)_____ freedom, he ran away from his master\'s house. As he passed a forest, he saw a lion (98)____ with pain, for a huge thorn had pierced its paw. The slave felt sorry for the lion and took out the thorn gently. The grateful lion licked the slave\'s hand. But just then, both of (99)_____ were captured by the soldiers. The king ordered, &ldquo;Let the runaway slave (100)_____ thrown in front of a hungry lion.&rdquo; But, when the slave was thrown in front of the lion, the lion licked him and quietly sat beside him. The amazed king asked the reason. The slave told the king everything. The king pardoned the slave. The lion was also freed and sent to the forest.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: ["<p>a</p>", "<p>two</p>", 
                                "<p>the</p>", "<p>an</p>"],
                    options_hi: ["<p>a</p>", "<p>two</p>",
                                "<p>the</p>", "<p>an</p>"],
                    solution_en: "<p>96.(a) a<br>Indefinite article &lsquo;a/an&rsquo; is used before a singular noun. Article &lsquo;a&rsquo; is used before a word beginning with a consonant sound. For example:- a cat, a new car, etc. Similarly, in the given sentence, &lsquo;slave&rsquo; is a singular noun and the word &lsquo;very&rsquo; begins with the consonant sound. Hence, &lsquo;a&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(a) a<br>Indefinite article &lsquo;a/an&rsquo; का प्रयोग singular noun से पहले किया जाता है। Article &lsquo;a&rsquo; का प्रयोग consonant sound से प्रारंभ होने वाले word से पहले किया जाता है। For example:- a cat, a new car आदि। इसी तरह, दिए गए sentence में, &lsquo;slave&rsquo; एक singular noun है तथा consonant sound &lsquo;very&rsquo; word से प्रारंभ होता है। अतः, option (a) सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test :</strong><br>During the days of slavery, there lived (96)_____ very hard-working slave. One day, (97)_____ freedom, he ran away from his master\'s house. As he passed a forest, he saw a lion (98)____ with pain, for a huge thorn had pierced its paw. The slave felt sorry for the lion and took out the thorn gently. The grateful lion licked the slave\'s hand. But just then, both of (99)_____ were captured by the soldiers. The king ordered, &ldquo;Let the runaway slave (100)_____ thrown in front of a hungry lion.&rdquo; But, when the slave was thrown in front of the lion, the lion licked him and quietly sat beside him. The amazed king asked the reason. The slave told the king everything. The king pardoned the slave. The lion was also freed and sent to the forest.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test :</strong><br>During the days of slavery, there lived (96)_____ very hard-working slave. One day, (97)_____ freedom, he ran away from his master\'s house. As he passed a forest, he saw a lion (98)____ with pain, for a huge thorn had pierced its paw. The slave felt sorry for the lion and took out the thorn gently. The grateful lion licked the slave\'s hand. But just then, both of (99)_____ were captured by the soldiers. The king ordered, &ldquo;Let the runaway slave (100)_____ thrown in front of a hungry lion.&rdquo; But, when the slave was thrown in front of the lion, the lion licked him and quietly sat beside him. The amazed king asked the reason. The slave told the king everything. The king pardoned the slave. The lion was also freed and sent to the forest.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: ["<p>to gain</p>", "<p>gained</p>", 
                                "<p>gains</p>", "<p>gain</p>"],
                    options_hi: ["<p>to gain</p>", "<p>gained</p>",
                                "<p>gains</p>", "<p>gain</p>"],
                    solution_en: "<p>97.(a) to gain<br>The main verb (ran away) is already there in the sentence. So, we need a non-finite verb. Infinitive (to + V<sub>1</sub>) is non-finite that acts as a noun, adjective or adverb in a sentence. Similarly, in the given sentence, &lsquo;to gain&rsquo; is an infinitive acting as an adverb of reason. Hence, &lsquo;to gain&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) to gain<br>वाक्य में main verb (ran away) पहले से ही मौजूद है। इसलिए, हमें एक non-finite verb की आवश्यकता है। Infinitive (to + V<sub>1</sub>), non-finite है जो sentence में noun, adjective या adverb के रूप में कार्य करता है। इसी तरह, दिए गए sentence में, &lsquo;to gain&rsquo; एक infinitive है जो adverb of reason के रूप में कार्य करता है। अतः, &lsquo;to gain&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test :</strong><br>During the days of slavery, there lived (96)_____ very hard-working slave. One day, (97)_____ freedom, he ran away from his master\'s house. As he passed a forest, he saw a lion (98)____ with pain, for a huge thorn had pierced its paw. The slave felt sorry for the lion and took out the thorn gently. The grateful lion licked the slave\'s hand. But just then, both of (99)_____ were captured by the soldiers. The king ordered, &ldquo;Let the runaway slave (100)_____ thrown in front of a hungry lion.&rdquo; But, when the slave was thrown in front of the lion, the lion licked him and quietly sat beside him. The amazed king asked the reason. The slave told the king everything. The king pardoned the slave. The lion was also freed and sent to the forest.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test :</strong><br>During the days of slavery, there lived (96)_____ very hard-working slave. One day, (97)_____ freedom, he ran away from his master\'s house. As he passed a forest, he saw a lion (98)____ with pain, for a huge thorn had pierced its paw. The slave felt sorry for the lion and took out the thorn gently. The grateful lion licked the slave\'s hand. But just then, both of (99)_____ were captured by the soldiers. The king ordered, &ldquo;Let the runaway slave (100)_____ thrown in front of a hungry lion.&rdquo; But, when the slave was thrown in front of the lion, the lion licked him and quietly sat beside him. The amazed king asked the reason. The slave told the king everything. The king pardoned the slave. The lion was also freed and sent to the forest.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: ["<p>roaring</p>", "<p>roared</p>", 
                                "<p>roar</p>", "<p>roars</p>"],
                    options_hi: ["<p>roaring</p>", "<p>roared</p>",
                                "<p>roar</p>", "<p>roars</p>"],
                    solution_en: "<p>98.(a) roaring<br>Present participle (V-ing) is used to indicate an action happening at the same time as another. Similarly, the lion was roaring with pain when he saw it. Hence, &lsquo;roaring&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(a) roaring<br>Present participle (V-ing) का प्रयोग किसी action के उसी समय होने को इंगित करने के लिए किया जाता है। इसी तरह, शेर (lion) भी दर्द से दहाड़ रहा (roaring) था जब उसने इसे देखा। अतः, &lsquo;roaring&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test :</strong><br>During the days of slavery, there lived (96)_____ very hard-working slave. One day, (97)_____ freedom, he ran away from his master\'s house. As he passed a forest, he saw a lion (98)____ with pain, for a huge thorn had pierced its paw. The slave felt sorry for the lion and took out the thorn gently. The grateful lion licked the slave\'s hand. But just then, both of (99)_____ were captured by the soldiers. The king ordered, &ldquo;Let the runaway slave (100)_____ thrown in front of a hungry lion.&rdquo; But, when the slave was thrown in front of the lion, the lion licked him and quietly sat beside him. The amazed king asked the reason. The slave told the king everything. The king pardoned the slave. The lion was also freed and sent to the forest.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test :</strong><br>During the days of slavery, there lived (96)_____ very hard-working slave. One day, (97)_____ freedom, he ran away from his master\'s house. As he passed a forest, he saw a lion (98)____ with pain, for a huge thorn had pierced its paw. The slave felt sorry for the lion and took out the thorn gently. The grateful lion licked the slave\'s hand. But just then, both of (99)_____ were captured by the soldiers. The king ordered, &ldquo;Let the runaway slave (100)_____ thrown in front of a hungry lion.&rdquo; But, when the slave was thrown in front of the lion, the lion licked him and quietly sat beside him. The amazed king asked the reason. The slave told the king everything. The king pardoned the slave. The lion was also freed and sent to the forest.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: ["<p>him</p>", "<p>them</p>", 
                                "<p>us</p>", "<p>her</p>"],
                    options_hi: ["<p>him</p>", "<p>them</p>",
                                "<p>us</p>", "<p>her</p>"],
                    solution_en: "<p>99.(b) them<br>&lsquo;Them&rsquo; is a third person pronoun that is used to talk about two or more people or things other than the person speaking and the person listening. In the given sentence, &lsquo;them&rsquo; has been used for the lion and the slave. Hence, &lsquo;them&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) them<br>&lsquo;Them&rsquo; एक third person pronoun है जिसका प्रयोग बोलने वाले व्यक्ति और सुनने वाले व्यक्ति के अलावा दो या दो से अधिक लोगों या चीजों के बारे में बात करने के लिए किया जाता है। दिए गए sentence में, &lsquo;them&rsquo; का प्रयोग lion और slave के लिए किया गया है। अतः, &lsquo;them&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test :</strong><br>During the days of slavery, there lived (96)_____ very hard-working slave. One day, (97)_____ freedom, he ran away from his master\'s house. As he passed a forest, he saw a lion (98)____ with pain, for a huge thorn had pierced its paw. The slave felt sorry for the lion and took out the thorn gently. The grateful lion licked the slave\'s hand. But just then, both of (99)_____ were captured by the soldiers. The king ordered, &ldquo;Let the runaway slave (100)_____ thrown in front of a hungry lion.&rdquo; But, when the slave was thrown in front of the lion, the lion licked him and quietly sat beside him. The amazed king asked the reason. The slave told the king everything. The king pardoned the slave. The lion was also freed and sent to the forest.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test :</strong><br>During the days of slavery, there lived (96)_____ very hard-working slave. One day, (97)_____ freedom, he ran away from his master\'s house. As he passed a forest, he saw a lion (98)____ with pain, for a huge thorn had pierced its paw. The slave felt sorry for the lion and took out the thorn gently. The grateful lion licked the slave\'s hand. But just then, both of (99)_____ were captured by the soldiers. The king ordered, &ldquo;Let the runaway slave (100)_____ thrown in front of a hungry lion.&rdquo; But, when the slave was thrown in front of the lion, the lion licked him and quietly sat beside him. The amazed king asked the reason. The slave told the king everything. The king pardoned the slave. The lion was also freed and sent to the forest.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: ["<p>been</p>", "<p>be</p>", 
                                "<p>being</p>", "<p>is</p>"],
                    options_hi: ["<p>been</p>", "<p>be</p>",
                                "<p>being</p>", "<p>is</p>"],
                    solution_en: "<p>100.(b) be<br>The given sentence is in the passive voice and the correct structure is &lsquo;Let + be + V<sub>3</sub>&rsquo;. Hence, &lsquo;be&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) be<br>दिया गया वाक्य passive voice में है और &lsquo;Let + be + V<sub>3</sub>&rsquo; सही structure है। अतः, &lsquo;be&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>