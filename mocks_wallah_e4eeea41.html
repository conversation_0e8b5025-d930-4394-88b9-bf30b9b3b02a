<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 10</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">10</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 8
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 9,
                end: 9
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. A man walks to a viewpoint and returns to the starting point by his car maintaining constant speed and thus takes a total time of 9 hours 30 minutes. He would have gained 7 hours by driving both ways. How long would it have taken for him to walk both ways with the same walking speed?</p>",
                    question_hi: "<p>1. एक व्यक्ति एक व्यूपॉइंट तक पैदल जाता है और अपनी कार से अपरिवर्ती चाल बनाए रखते हुए वापस प्रारंभिक बिंदु पर लौटता है और इस प्रकार उसे कुल 9 घंटे 30 मिनट का समय लगता है। दोनों तरफ गाड़ी से यात्रा करने में उसे 7 घंटे कम लगते। दोनों ओर समान चाल से पैदल यात्रा करने में उसे कुल कितना समय लगता?</p>",
                    options_en: ["<p>16 hours 45 minutes</p>", "<p>16 hours 30 minutes</p>", 
                                "<p>17 hours 30 minutes</p>", "<p>15 hours 15 minutes</p>"],
                    options_hi: ["<p>16 घंटे 45 मिनट</p>", "<p>16 घंटे 30 मिनट</p>",
                                "<p>17 घंटे 30 मिनट</p>", "<p>15 घंटे 15 मिनट</p>"],
                    solution_en: "<p>1.(b) <br>Walks + car &rarr; 9 hours 30 minutes ---- (i)<br>car + car &rarr; (9 hours 30 minutes ) - 7 hours<br>= 2 hours 30 minutes <br>Car &rarr; 1 hour 15 minutes ---- (ii)<br>From equations (i) and (ii) :&ndash;<br>Walks &rarr; 8 hours 15 minutes <br>Therefore, the total time taken to travel on foot at the same speed on both sides = 2 &times; (8 hours 15 minutes) =16 hours 30 minutes</p>",
                    solution_hi: "<p>1.(b) <br>पैदल + कार &rarr; 9 घंटे 30 मिनट ---- (i)<br>कार + कार &rarr; ( 9 घंटे 30 मिनट ) - 7 घंटे<br>= 2 घंटे 30 मिनट <br>कार &rarr; 1 घंटे 15 मिनट ---- (ii)<br>समीकरण (i) और (ii) से -<br>पैदल &rarr; 8 घंटे 15 मिनट <br>अतः दोनों ओर समान चाल से पैदल यात्रा करने मे लगा कुल समय = 2 &times; (8 घंटे 15 मिनट ) <br>=16 घंटे 30 मिनट</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. What time (in seconds) is required for a 188 m long train to cross a 472 m long tunnel, if the train travels at a speed of 27 km/h?</p>",
                    question_hi: "<p>2. 27 km/h की चाल से चल रही 188m लंबी रेलगाड़ी को 472 m लंबी सुरंग पार करने में कितना समय (सेकंड में) लगेगा?</p>",
                    options_en: ["<p>98</p>", "<p>80</p>", 
                                "<p>88</p>", "<p>79</p>"],
                    options_hi: ["<p>98</p>", "<p>80</p>",
                                "<p>88</p>", "<p>79</p>"],
                    solution_en: "<p>2.(c) Total distance = 188 + 472 = 660 m<br>Required time = <math display=\"inline\"><mfrac><mrow><mn>660</mn></mrow><mrow><mn>27</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>660</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn></mrow><mrow><mn>27</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>5</mn></mrow></mfrac></math> = 88 seconds</p>",
                    solution_hi: "<p>2.(c) कुल दूरी = 188 + 472 = 660 m<br>आवश्यक समय = <math display=\"inline\"><mfrac><mrow><mn>660</mn></mrow><mrow><mn>27</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>660</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn></mrow><mrow><mn>27</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>5</mn></mrow></mfrac></math> = 88 सेकंड</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Two trains of equal length are running on parallel lines in the same direction at speeds of 97 km/h and 21 km/h. The faster train passes the slower train in 45 seconds. The length of each train is:</p>",
                    question_hi: "<p>3. समान लंबाई की दो ट्रेन समांतर पटरियों पर एक ही दिशा में 97 km/h और 21 km/h की चाल से चल रही हैं। तेज़ चलने वाली ट्रेन, धीमी चलने वाली ट्रेन को 45 सेकंड में पार करती है। प्रत्येक ट्रेन की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>475 meters</p>", "<p>472 meters</p>", 
                                "<p>466 meters</p>", "<p>477 meters</p>"],
                    options_hi: ["<p>475 meters</p>", "<p>472 meters</p>",
                                "<p>466 meters</p>", "<p>477 meters</p>"],
                    solution_en: "<p>3.(a) Length of each train be <math display=\"inline\"><mi>x</mi></math><br>Relative speed of train = 97 - 21 = 76 km/h <br>Length of each train = 76 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> &times; 45 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = 19 &times; 25 = 475 m</p>",
                    solution_hi: "<p>3.(a) प्रत्येक ट्रेन की लंबाई <math display=\"inline\"><mi>x</mi></math> है<br>ट्रेन की सापेक्ष गति = 97 - 21 = 76 km/h <br>प्रत्येक ट्रेन की लंबाई = 76 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> &times; 45 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> = 19 &times; 25 = 475 m</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. A man walks to a viewpoint and returns to the starting point by his car maintaining constant speed and thus takes a total time of 7 hours 15 minutes. He would have gained 6 hours by driving both ways. How long would it have taken for him to walk both ways with the same walking speed?</p>",
                    question_hi: "<p>4. एक व्यक्ति एक व्यूपॉइंट तक पैदल जाता है और अपनी कार से स्थिर चाल बनाए रखते हुए लौटता है और इस प्रकार उसे कुल 7 घंटे 15 मिनट का समय लगता है। दोनों तरफ कार से यात्रा करने में उसे 6 घंटे कम लगते। दोनों ओर समान चाल से पैदल यात्रा करने में उसे कुल कितना समय लगता?</p>",
                    options_en: ["<p>13 hours 45 minutes</p>", "<p>13 hours 15 minutes</p>", 
                                "<p>12 hours 15 minutes.</p>", "<p>14 hours 30 minutes</p>"],
                    options_hi: ["<p>13 घंटे 45 मिनट</p>", "<p>13 घंटे 15 मिनट</p>",
                                "<p>12 घंटे 15 मिनट</p>", "<p>14 घंटे 30 मिनट</p>"],
                    solution_en: "<p>4.(b) Let the speed of a car and walking be <math display=\"inline\"><mi>x</mi></math> and y respectively,<br>According to question,<br><math display=\"inline\"><mfrac><mrow><mi>d</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>d</mi><mi>y</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>4</mn></mfrac></math> = 7.25 &hellip;..(i)<br><math display=\"inline\"><mfrac><mrow><mi>d</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>d</mi><mi>x</mi></mfrac></math> = (7.25 - 6) = 1.25 <br><math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>d</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 1.25 &rArr;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>d</mi><mo>&#160;</mo></mrow><mi>x</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math><br>Put the value <math display=\"inline\"><mfrac><mrow><mi>d</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math> in equation (i)<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>d</mi><mi>y</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>4</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">d</mi></mrow><mrow><mi mathvariant=\"bold-italic\">y</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>4</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>58</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>5</mn></mrow><mn>8</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>53</mn><mn>8</mn></mfrac></math> hours<br>Hence, Both side walking to take time = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 534 = 13 hours 15 minutes</p>",
                    solution_hi: "<p>4.(b) माना कार और चलने की गति क्रमशः <math display=\"inline\"><mi>x</mi></math> और y है,<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mi>d</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>d</mi><mi>y</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>4</mn></mfrac></math> = 7.25 &hellip;..(i)<br><math display=\"inline\"><mfrac><mrow><mi>d</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>d</mi><mi>x</mi></mfrac></math> = (7.25 - 6) = 1.25 <br><math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>d</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 1.25 &rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>d</mi><mi>x</mi></mfrac></math> = 1.252 = 58 <br>समीकरण (i) में <math display=\"inline\"><mfrac><mrow><mi>d</mi></mrow><mrow><mi>x</mi></mrow></mfrac></math> का मान रखने पर,<br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>d</mi><mi>y</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>4</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>d</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>4</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>58</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>5</mn></mrow><mn>8</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>53</mn><mn>8</mn></mfrac></math> घंटे <br>इसलिए, दोनों तरफ पैदल चलने में लगा समय = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>53</mn><mn>4</mn></mfrac></math> = 13 घंटे 15 मिनट</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. A train runs at a speed of 92 kmph to cover a distance of 184 km and then at a speed of 102 kmph to cover a distance of 204 km. Find the average speed of the train for the entire distance.</p>",
                    question_hi: "<p>5. एक रेलगाड़ी 184 km की दूरी तय करने के लिए 92 kmph की चाल से चलती है और फिर 204 km की दूरी तय करने के लिए 102 kmph की चाल से चलती है। रेलगाड़ी द्वारा तय की गई कुल दूरी का औसत चाल ज्ञात कीजिए।</p>",
                    options_en: ["<p>98 kmph</p>", "<p>134 kmph</p>", 
                                "<p>92 kmph</p>", "<p>97 kmph</p>"],
                    options_hi: ["<p>98 kmph</p>", "<p>134 kmph</p>",
                                "<p>92 kmph</p>", "<p>97 kmph</p>"],
                    solution_en: "<p>5.(d)<br>Average speed = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>184</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>204</mn><mo>&#160;</mo><mo>&#160;</mo></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><mn>184</mn><mn>92</mn></mfrac></mstyle><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>204</mn><mn>102</mn></mfrac></mstyle></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>388</mn><mo>&#160;</mo></mrow><mrow><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>388</mn><mn>4</mn></mfrac></math> = 97 km/h</p>",
                    solution_hi: "<p>5.(d)<br>औसत गति =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>184</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>204</mn><mo>&#160;</mo><mo>&#160;</mo></mrow><mstyle displaystyle=\"true\"><mfrac><mn>184</mn><mn>92</mn></mfrac><mo>+</mo><mfrac><mn>204</mn><mn>102</mn></mfrac></mstyle></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>388</mn><mo>&#160;</mo></mrow><mrow><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>388</mn><mn>4</mn></mfrac></math> = 97 km/h</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. A man walks to a viewpoint and returns to the starting point by his car maintaining constant speed and thus takes a total time of 8 hours 45 minutes. He would have gained 5 hours by driving both ways. How long would it have taken for him to walk both ways with the same walking speed?</p>",
                    question_hi: "<p>6. एक व्यक्ति एक व्यूपॉइंट तक पैदल जाता है और अपनी कार से एक निश्चित चाल से वापस लौटता है और इस प्रकार उसे कुल 8 घंटे 45 मिनट का समय लगता है। दोनों तरफ कार से यात्रा करने पर उसे 5 घंटे कम लगते । दोनों ओर एकसमान चाल से पैदल यात्रा करने में उसे कुल कितना समय लगता?</p>",
                    options_en: ["<p>13 hours 45 minutes</p>", "<p>14 hours 15 minutes</p>", 
                                "<p>13 hours 30 minutes</p>", "<p>12 hours 45 minutes</p>"],
                    options_hi: ["<p>13 घंटे 45 मिनट</p>", "<p>14 घंटे 15 मिनट</p>",
                                "<p>13 घंटे 30 मिनट</p>", "<p>12 घंटे 45 मिनट </p>"],
                    solution_en: "<p>6.(a)<br>Let time taken by men on one side be x and car be y<br>According to the question,<br>x + y = 8 hr 45 min. = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hr.---------(i)<br>y + y = 3 hr 45 min. = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hr.---------(ii)<br>Now, 2y = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hr.<br>Then, y = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> hr.<br>Now, putting the value of y in equation (i) we get;<br>x + <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>4</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>4</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>15</mn><mo>&#160;</mo></mrow><mn>8</mn></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>8</mn></mfrac></math> hr<br>Time taken to men for walk both ways (2x) = <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 2 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>55</mn><mo>&#160;</mo></mrow><mn>4</mn></mfrac></math> hr<br><math display=\"inline\"><mo>&#8658;</mo></math> (13 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>)hr = 13 hour +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; 60 = 13 hour 45 min.</p>",
                    solution_hi: "<p>6.(a)<br>माना एक ओर व्यक्ति द्वारा पैदल और कार द्वारा लिया जाने वाला समय क्रमशः x और y घंटे है <br>प्रश्न के अनुसार,<br>x + y = 8 घंटे 45 मिनट = <math display=\"inline\"><mfrac><mrow><mn>35</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटे ---------(i)<br>y + y = 3 घंटे 45 मिनट = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटे ---------(ii)<br>अब, 2y = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटे <br>तो, y = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> घंटे <br>अब, y का मान समीकरण (i) में रखने पर हमे प्राप्त होता है;<br>x + <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>4</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>4</mn></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>8</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>8</mn></mfrac></math> घंटे <br>आदमी द्वारा दोनों और पैदल जाने में लगा समय (2x) = <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> &times; 2 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>55</mn><mn>4</mn></mfrac></math> घंटे <br><math display=\"inline\"><mo>&#8658;</mo></math> (13 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math>)घंटे = 13 घंटे +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &times; 60 = 13 घंटे 45 मिनट</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. A train runs at a speed of 114 kmph to cover a distance of 228 km and then at a speed of 93 kmph to cover a distance of 372 km. Find the average speed of the train for the entire distance.</p>",
                    question_hi: "<p>7. एक रेलगाड़ी 228 km की दूरी तय करने के लिए 114 kmph की चाल से चलती है और फिर 372 km की दूरी तय करने के लिए 93 kmph की चाल से चलती है। रेलगाड़ी द्वारा तय की गई कुल दूरी का औसत चाल ज्ञात कीजिए।</p>",
                    options_en: ["<p>60 kmph</p>", "<p>100 kmph</p>", 
                                "<p>113 kmph</p>", "<p>125 kmph</p>"],
                    options_hi: ["<p>60 kmph</p>", "<p>100 kmph</p>",
                                "<p>113 kmph</p>", "<p>125 kmph</p>"],
                    solution_en: "<p>7.(b) Average speed = <math display=\"inline\"><mfrac><mrow><mi>T</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>T</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>228</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>372</mn></mrow><mrow><mfrac><mrow><mn>228</mn></mrow><mrow><mn>114</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>372</mn></mrow><mrow><mn>93</mn></mrow></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mrow><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow></mfrac></math> = 100 km/h</p>",
                    solution_hi: "<p>7.(b) औसत गति =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>228</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>372</mn></mrow><mrow><mfrac><mrow><mn>228</mn></mrow><mrow><mn>114</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>372</mn></mrow><mrow><mn>93</mn></mrow></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>600</mn><mrow><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>4</mn></mrow></mfrac></math> = 100 km/h</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. A man walks to a viewpoint and returns to the starting point by his car maintaining constant speed and thus takes a total time of 8 hours 30 minutes. He would have gained 5 hours by driving both ways. How long would it have taken for him to walk both ways with the same walking speed?</p>",
                    question_hi: "<p>8. एक व्यक्ति एक व्यूपॉइंट तक पैदल जाता है और अपनी कार से स्थिर चाल बनाए रखते हुए लौटता है और इस प्रकार उसे कुल 8 घंटे 30 मिनट का समय लगता है। दोनों तरफ कार से यात्रा करने में उसे 5 घंटे कम लगते। दोनों ओर समान चाल से पैदल यात्रा करने में उसे कुल कितना समय लगता?</p>",
                    options_en: ["<p>12 hours 15 minutes</p>", "<p>13 hours 45 minutes</p>", 
                                "<p>14 hours 30 minutes</p>", "<p>13 hours 30 minutes</p>"],
                    options_hi: ["<p>12 घंटे 15 मिनट</p>", "<p>13 घंटे 45 मिनट</p>",
                                "<p>14 घंटे 30 मिनट</p>", "<p>13 घंटे 30 मिनट</p>"],
                    solution_en: "<p>8.(d) Walking (W) + Car (D) = 8&thinsp;hrs&thinsp;30&thinsp;mins <br>Car (D) + Car (D) = 8.5 - 5 = 3.5&thinsp;hrs <br>Time to drive one way (D) = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 1.75 hrs= 1 hr 45 mins.<br>Time to walk one way (W) = 8.5 &minus; 1.75 = 6.75 hrs = 6 hrs 45 mins. <br>Time to walk both ways:<br>2W = 2 &times; 6.75 = 13.5 hrs = 13 hrs 30 mins.</p>",
                    solution_hi: "<p>8.(d) पैदल (W) + कार (D) = 8&thinsp;घंटे&thinsp;30&thinsp;मिनट<br>कार (D) + कार (D) = 8.5 - 5 = 3.5&thinsp;घंटे<br>कार से एक तरफ जाने का समय (D) = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 1.75 घंटे = 1 घंटा 45 मिनट<br>पैदल एक तरफ जाने का समय (W): <br>W = 8.5 &minus; 1.75 = 6.75 घंटे = 6 घंटे 45मिनट<br>दोनों तरफ पैदल चलने का समय:<br>2W = 2 &times; 6.75 = 13.5 घंटे = 13 घंटे 30 मिनट</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. A train runs at a speed of 147 kmph to cover a distance of 294 km and then at a speed of 133 kmph to cover a distance of 266 km. Find the average speed of the train for the entire distance.</p>",
                    question_hi: "<p>9. एक रेलगाड़ी 147 kmph की चाल से 294 km की दूरी तय करती है और फिर 133 kmph की चाल से 266 km की दूरी तय करती है। रेलगाड़ी द्वारा तय की गई कुल दूरी का औसत चाल ज्ञात कीजिए।</p>",
                    options_en: ["<p>152 kmph</p>", "<p>187 kmph</p>", 
                                "<p>140 kmph</p>", "<p>132 kmph</p>"],
                    options_hi: ["<p>152 kmph</p>", "<p>187 kmph</p>",
                                "<p>140 kmph</p>", "<p>132 kmph</p>"],
                    solution_en: "<p>9.(c) Average speed of the train = <math display=\"inline\"><mfrac><mrow><mi>T</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>c</mi><mi>e</mi><mi>&#160;</mi></mrow><mrow><mi>T</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>294</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>266</mn></mrow><mrow><mfrac><mrow><mn>294</mn></mrow><mrow><mn>147</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>266</mn></mrow><mrow><mn>133</mn></mrow></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>560</mn><mrow><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn></mrow></mfrac></math> = 140kmph</p>",
                    solution_hi: "<p>9.(c) ट्रेन की औसत गति =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math> &nbsp;<br>= <math display=\"inline\"><mfrac><mrow><mn>294</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>266</mn></mrow><mrow><mfrac><mrow><mn>294</mn></mrow><mrow><mn>147</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>266</mn></mrow><mrow><mn>133</mn></mrow></mfrac></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>560</mn></mrow><mrow><mn>2</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo></mrow></mfrac></math>= 140kmph</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "misc",
                    question_en: "<p>10. Two trains of equal length are running on parallel lines in the same direction at speeds of 66 km/h and 39 km/h. The faster train passes the slower train in 24 seconds. The length of each train is:</p>",
                    question_hi: "<p>10. समान लंबाई की दो ट्रेन समांतर पटरियों पर एक ही दिशा में 66 किमी/घंटा और 39 किमी/घंटा की चाल से चल रही हैं। तेज़ चलने वाली ट्रेन, धीमी चलने वाली ट्रेन को 24 सेकंड में पार करती है। प्रत्येक ट्रेन की लंबाई <br>ज्ञात कीजिए।</p>",
                    options_en: ["<p>89 meters</p>", "<p>90 meters</p>", 
                                "<p>110 meters</p>", "<p>72 meters</p>"],
                    options_hi: ["<p>89 मीटर</p>", "<p>90 मीटर</p>",
                                "<p>110 मीटर</p>", "<p>72 मीटर</p>"],
                    solution_en: "<p>10.(b) Let length of each train = D meters<br>Total distance covered by trains = D + D = 2D<br>Relative speeds between two trains = <math display=\"inline\"><mo>(</mo><mn>66</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>39</mn><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>135</mn><mn>18</mn></mfrac></math> m/s<br>According to question , <br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>D</mi></mrow><mstyle displaystyle=\"true\"><mfrac><mn>135</mn><mn>18</mn></mfrac></mstyle></mfrac></math> = 24 &rArr; D =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>135</mn></mrow><mrow><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> D = 90 meters</p>",
                    solution_hi: "<p>10.(b) प्रत्येक ट्रेन की लंबाई = D मीटर<br>ट्रेनों द्वारा तय की गई कुल दूरी = D + D = 2D<br>दो ट्रेनों के बीच सापेक्ष गति = <math display=\"inline\"><mo>(</mo><mn>66</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>39</mn><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>135</mn><mn>18</mn></mfrac></math> m/s<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>D</mi></mrow><mstyle displaystyle=\"true\"><mfrac><mn>135</mn><mn>18</mn></mfrac></mstyle></mfrac></math> = 24 &rArr; D =&nbsp;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>135</mn></mrow><mrow><mn>2</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>18</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> D = 90 मीटर</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>