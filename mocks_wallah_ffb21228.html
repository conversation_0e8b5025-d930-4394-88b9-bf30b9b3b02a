<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">30:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 30</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">30</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 30 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 28
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 29,
                end: 29
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>[1. Among the following words, which one will come second if they are arranged as per their order in an English dictionary ?<br>Exit, Exercise, Excuse, Exempt</p>",
                    question_hi: "<p>1. निम्नलिखित शब्दों में से कौन-सा एक अंग्रेजी शब्दकोश में उनके क्रम के अनुसार व्यवस्थित होने पर दूसरे स्थान पर आएगा ?<br>Exit, Exercise, Excuse, Exempt</p>",
                    options_en: ["<p>Exit</p>", "<p>Excuse</p>", 
                                "<p>Exempt</p>", "<p>Exercise</p>"],
                    options_hi: ["<p>Exit</p>", "<p>Excuse</p>",
                                "<p>Exempt</p>", "<p>Exercise</p>"],
                    solution_en: "<p>1.(c)<br>Excuse , Exempt, Exercise, Exit</p>",
                    solution_hi: "<p>1.(c)<br>Excuse , Exempt, Exercise, Exit</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In a certain code language, <strong>SHANU </strong>is written as <strong>3969</strong>. How will <strong>TAPLG </strong>be written as in the language ?</p>",
                    question_hi: "<p>2. एक निश्चित कूट भाषा में <strong>SHANU </strong>को <strong>3969 </strong>लिखा जाता है। <strong>TAPLG </strong>को भाषा की तरह कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>3945</p>", "<p>3136</p>", 
                                "<p>3166</p>", "<p>3939</p>"],
                    options_hi: ["<p>3945</p>", "<p>3136</p>",
                                "<p>3166</p>", "<p>3939</p>"],
                    solution_en: "<p>2.(b)<br>SHANU = <math display=\"inline\"><msup><mrow><mo>(</mo><mn>19</mn><mo>+</mo><mn>8</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>14</mn><mo>+</mo><mn>21</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><msup><mrow><mo>(</mo><mn>63</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>3969</mn></math><br>TAPLG = <math display=\"inline\"><msup><mrow><mo>(</mo><mn>20</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>16</mn><mo>+</mo><mn>12</mn><mo>+</mo><mn>7</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><msup><mrow><mo>(</mo><mn>56</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>3136</mn></math></p>",
                    solution_hi: "<p>2.(b)<br>SHANU = <math display=\"inline\"><msup><mrow><mo>(</mo><mn>19</mn><mo>+</mo><mn>8</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>14</mn><mo>+</mo><mn>21</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><msup><mrow><mo>(</mo><mn>63</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>3969</mn></math><br>TAPLG = <math display=\"inline\"><msup><mrow><mo>(</mo><mn>20</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>16</mn><mo>+</mo><mn>12</mn><mo>+</mo><mn>7</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><msup><mrow><mo>(</mo><mn>56</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>3136</mn></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, <strong>PEN </strong>is written as <strong>8</strong>. How will <strong>PENCIL </strong>be written in that language ?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में <strong>PEN </strong>को <strong>8 </strong>लिखा जाता है। उसी भाषा में <strong>PENCIL </strong>को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>17</p>", "<p>15</p>", 
                                "<p>25</p>", "<p>14</p>"],
                    options_hi: ["<p>17</p>", "<p>15</p>",
                                "<p>25</p>", "<p>14</p>"],
                    solution_en: "<p>3.(d)<br>PEN = 16 + 5 + 14 = 35 = 3 + 5 = 8&nbsp;<br>PENCIL = 16 + 5 + 14 + 3 + 9 + 12 = 59 = 5 + 9 = 14</p>",
                    solution_hi: "<p>3.(d)<br>PEN = 16 + 5 + 14 = 35 = 3 + 5 = 8&nbsp;<br>PENCIL = 16 + 5 + 14 + 3 + 9 + 12 = 59 = 5 + 9 = 14</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. A question is given followed by two arguments. Decide which of the arguments is/are strong with respect to the question.<br><strong>Question :</strong><br>Is there a need for an employees union in every government institution ?<br><strong>Arguments :</strong><br>1. No, it hinders the smooth operation of the institution.<br>2. Yes, it empowers the rights of the employees.</p>",
                    question_hi: "<p>4. एक प्रश्न के बाद दो तर्क दिए गए हैं। निर्णय लें कि प्रश्न के संबंध में कौन-सा/से तर्क प्रबल है/हैं।<br><strong>प्रश्न :</strong><br>क्या हर सरकारी संस्थान में कर्मचारी संघ की आवश्यकता है ?<br><strong>तर्क :</strong><br>1. नहीं, यह संस्था के सुचारू संचालन में बाधक है।<br>2. हां, यह कर्मचारियों के अधिकारों को सशक्त बनाता है।</p>",
                    options_en: ["<p>Only 2 is strong.</p>", "<p>Only 1is strong.</p>", 
                                "<p>Neither 1 nor 2 is strong.</p>", "<p>1 and 2 both are strong.</p>"],
                    options_hi: ["<p>केवल 2 मजबूत है।</p>", "<p>केवल 1 मजबूत है।</p>",
                                "<p>न तो 1 और न ही 2 मजबूत है।</p>", "<p>1 और 2 दोनों मजबूत हैं।</p>"],
                    solution_en: "<p>4.(d)<br>For the question asked, both arguments 1 and 2 are strong.</p>",
                    solution_hi: "<p>4.(d)<br>पूछे गए प्रश्न के लिए, तर्क 1 और 2 दोनों ही प्रबल हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a certain code language, &lsquo;BDT&rsquo; means &lsquo;girls play toy&rsquo;, &lsquo;TSM&rsquo; means &lsquo;girls are angry&rsquo; and &lsquo;BTC&rsquo; means &lsquo;girls like toy&rsquo;. Which of the following letters will mean &lsquo;like&rsquo; in that language ?</p>",
                    question_hi: "<p>5. एक निश्चित कूट भाषा में, \'BDT\' का अर्थ है &lsquo;girls play toy\', \'TSM\' का अर्थ है \'girls are angry\' और \'BTC\' का अर्थ है &lsquo;girls like toy&rsquo;, उस भाषा में निम्नलिखित में से किस अक्षर का अर्थ &lsquo;like&rsquo; होगा ?</p>",
                    options_en: ["<p>T</p>", "<p>B</p>", 
                                "<p>C</p>", "<p>D</p>"],
                    options_hi: ["<p>T</p>", "<p>B</p>",
                                "<p>C</p>", "<p>D</p>"],
                    solution_en: "<p>5.(c)<br>Code for girls = T<br>Play = D<br>Toy = B<br>Like = C</p>",
                    solution_hi: "<p>5.(c)<br>लड़कियों के लिए कोड = T<br>Play = D<br>Toy = B<br>Like = C</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Pick the odd one out.</p>",
                    question_hi: "<p>6. विषम को चुनें।</p>",
                    options_en: ["<p>Goat</p>", "<p>Cat</p>", 
                                "<p>Buffalo</p>", "<p>Cow</p>"],
                    options_hi: ["<p>बकरी</p>", "<p>बिल्ली</p>",
                                "<p>भैंस</p>", "<p>गाय</p>"],
                    solution_en: "<p>6.(b)<br>All are pet animals except Cat</p>",
                    solution_hi: "<p>6.(b)<br>बिल्ली को छोड़कर सभी पालतू जानवर हैं</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. In a certain code language, &lsquo;Man is mortal&rsquo; is written as &lsquo;+ ga lp&rsquo;, James is Man&rsquo; is written as &lsquo;lp ga ku&rsquo;, &lsquo;James is mortal&rsquo; is written as &lsquo;ku + lp&rsquo;. What is the code for &lsquo;Man James&rsquo; in that code language ?</p>",
                    question_hi: "<p>7. एक निश्चित कूट भाषा में, \'Man is mortal\' को \'+ ga lp\' लिखा जाता है, James is Man\' को \'lp ga ku\' लिखा जाता है, \'James is mortal\' को \'ku + lp\' लिखा जाता है। उसी कूट भाषा में &lsquo;Man James&rsquo; के लिए कूट क्या है ?</p>",
                    options_en: ["<p>lp ku</p>", "<p>+ lp</p>", 
                                "<p>ku +</p>", "<p>ga ku</p>"],
                    options_hi: ["<p>lp ku</p>", "<p>+ lp</p>",
                                "<p>ku +</p>", "<p>ga ku</p>"],
                    solution_en: "<p>7.(d)<br>Code for mortal = +<br>is = lp<br>James = ku<br>Man = ga</p>",
                    solution_hi: "<p>7.(d)<br>mortal के लिए कोड = +<br>is = lp<br>James = ku<br>Man = ga</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language, &lsquo;<strong>DKVCTR</strong>&rsquo; is written as &lsquo;<strong>VXGZOH</strong>&rsquo;. What is the code for &lsquo;<strong>FRSTYD</strong>&rsquo; in that code language ?</p>",
                    question_hi: "<p>8. एक निश्चित कूट भाषा में, \'<strong>DKVCTR</strong>\' को \'<strong>VXGZOH</strong>\' लिखा जाता है। उसी कूट भाषा में &lsquo;<strong>FRSTYD</strong>&rsquo; के लिए कूट क्या है ?</p>",
                    options_en: ["<p>HBXVWK</p>", "<p>HCXWVJ</p>", 
                                "<p>HCXWVL</p>", "<p>HBXWMK</p>"],
                    options_hi: ["<p>HBXVWK</p>", "<p>HCXWVJ</p>",
                                "<p>HCXWVL</p>", "<p>HBXWMK</p>"],
                    solution_en: "<p>8.(b)<br><strong>Logic :</strong> Given place value of alphabets + 4 gives desired answer.<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930912998.png\" alt=\"rId4\" width=\"119\" height=\"54\">&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913143.png\" alt=\"rId5\" width=\"108\" height=\"56\"></p>",
                    solution_hi: "<p>8.(b)<br><strong>तर्क : </strong>अक्षर 4 का दिया गया स्थानीय मान वांछित उत्तर देता है।<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930912998.png\" alt=\"rId4\" width=\"119\" height=\"54\"> &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913143.png\" alt=\"rId5\" width=\"108\" height=\"56\"><br><br></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Read the given information carefully and answer the questions that follows.<br>There are 9 members M, N, O, P, Q, R, S, T and U, in a joint family. There are three married couples in the family. N is a doctor and the brother of O and P. Q is the mother of S. T is the wife of M, and M is the grandfather of S. O is the only daughter of M. U is the daughter of P and R.<br>Who is the wife of the doctor ?</p>",
                    question_hi: "<p>9. दी गई जानकारी को ध्यान से पढ़ें और नीचे दिए गए प्रश्नों के उत्तर दें।<br>एक संयुक्त परिवार में 9 सदस्य M, N, O, P, Q, R, S, T और U हैं। परिवार में तीन विवाहित जोड़े हैं। N एक डॉक्टर है और O और P का भाई है। Q, S की माँ है। T, M की पत्नी है, और M, S का दादा है। O, M की इकलौती बेटी है। U, P और R की पुत्री है। .<br>डॉक्टर की पत्नी कौन है ?</p>",
                    options_en: ["<p>T</p>", "<p>R</p>", 
                                "<p>Q</p>", "<p>O</p>"],
                    options_hi: ["<p>T</p>", "<p>R</p>",
                                "<p>Q</p>", "<p>O</p>"],
                    solution_en: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913271.png\" alt=\"rId6\" width=\"233\" height=\"118\"></p>",
                    solution_hi: "<p>9.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913271.png\" alt=\"rId6\" width=\"233\" height=\"118\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the option that is related to the third term in the same way as the second term is related to the first term.<br><strong>Plants : Garden :: Animals : ?</strong></p>",
                    question_hi: "<p>10. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br><strong>पौधे : बगीचा :: पशु : ?</strong></p>",
                    options_en: [" Cage", " Wild", 
                                " Zoo", " Pet"],
                    options_hi: [" पिंजरा", " जंगली",
                                " चिड़ियाघर", " पालतु"],
                    solution_en: "10.(c)<br />Plants are in the garden similarly Animals are found in zoos.",
                    solution_hi: "10.(c)<br />बगीचे में पौधे हैं वैसे ही चिड़ियाघरों में जानवर पाए जाते हैं।",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Pick the odd one out.</p>",
                    question_hi: "<p>11. विषम को चुनें।</p>",
                    options_en: ["<p>Table</p>", "<p>Cupboard</p>", 
                                "<p>Chair</p>", "<p>Glass </p>"],
                    options_hi: ["<p>मेज</p>", "<p>अलमारी</p>",
                                "<p>चेयर</p>", "<p>ग्लास</p>"],
                    solution_en: "<p>11.(d)<br>Table , Cupboard and Chair are articles made of glass or wood.</p>",
                    solution_hi: "<p>11.(d)<br>मेज, अलमारी और कुर्सी कांच या लकड़ी से बनी वस्तुएं हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Read the given statement and conclusions carefully. Assuming that the information given in the statements is true, even if it appears to be at variance with commonly known facts, decide which of the given conclusions logically follows from the statements.<br><strong>Statements :</strong><br>Some roses are pink. <br>All pink are pigs. <br>Some pigs are birds.<br><strong>Conclusion :</strong><br>I. Some birds are pink.<br>II. Some pigs are pink.<br>III. Some pigs are roses.<br>IV. Some birds are roses.</p>",
                    question_hi: "<p>12. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। यह मानते हुए कि कथनों में दी गई जानकारी सत्य है, भले ही वह सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होती हो, तय करें कि दिए गए निष्कर्षों में से कौन सा कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन :</strong><br>कुछ गुलाब गुलाबी हैं। <br>सभी गुलाबी सूअर हैं। <br>कुछ सूअर पक्षी हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ पक्षी गुलाबी हैं।<br>II. कुछ सूअर गुलाबी हैं।<br>III. कुछ सूअर गुलाब हैं।<br>IV. कुछ पक्षी गुलाब हैं।</p>",
                    options_en: ["<p>Only IV follows.</p>", "<p>Only I and II follow.</p>", 
                                "<p>Only II and III follow</p>", "<p>Only I follow.</p>"],
                    options_hi: ["<p>केवल IV अनुसरण करता है।</p>", "<p>केवल I और II अनुसरण करता है।</p>",
                                "<p>केवल II और III अनुसरण करता है।</p>", "<p>केवल I अनुसरण करता है।</p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913380.png\" alt=\"rId7\" width=\"218\" height=\"41\"><br>From the above diagram it is clear that Some pigs are pink and some pigs are roses.</p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913512.png\" alt=\"rId8\" width=\"213\" height=\"39\"><br>उपरोक्त आरेख से यह स्पष्ट है कि कुछ सूअर गुलाबी हैं और कुछ सूअर गुलाब हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. If R is the mother of F, G is the father of K, L is the brother of F and son of G, then how is R related to G ?</p>",
                    question_hi: "<p>13. यदि R, F की माता है, G, K का पिता है, L, F का भाई है और G का पुत्र है, तो R, G से किस प्रकार संबंधित है ?</p>",
                    options_en: [" Husband’s Sister  ", " Mother", 
                                " Sister", " Wife"],
                    options_hi: [" पति की बहन", " माँ",
                                " बहन", " पत्नी"],
                    solution_en: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913608.png\" alt=\"rId9\" width=\"137\" height=\"76\"></p>",
                    solution_hi: "<p>13.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913608.png\" alt=\"rId9\" width=\"137\" height=\"76\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. If &lsquo;*&rsquo; stand for &lsquo;addition&rsquo;, &lsquo;/&rsquo; stands for &lsquo;subtraction&rsquo;, &lsquo;+&rsquo; stands for &lsquo;multiplication&rsquo; and &lsquo;-&rsquo; stands for &lsquo;division&rsquo;, then find the value of <strong>22 * <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn mathvariant=\"bold\">10</mn><mn mathvariant=\"bold\">10</mn></mfrac></math> - 2 +</strong><strong>&nbsp;4.</strong></p>",
                    question_hi: "<p>14. यदि \'*\' का अर्थ \'जोड़\' है, \'/\' का अर्थ \'घटाव\' है, \'+\' का अर्थ \'गुणा\' है और \'-\' का अर्थ \'भाग\' है, तो <strong>22 * <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn mathvariant=\"bold\">10</mn><mn mathvariant=\"bold\">10</mn></mfrac></math> - 2 +</strong><strong>&nbsp;4.</strong>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>16</p>", "<p>10</p>", 
                                "<p>12</p>", "<p>14</p>"],
                    options_hi: ["<p>16</p>", "<p>10</p>",
                                "<p>12</p>", "<p>14</p>"],
                    solution_en: "<p>14.(c)<br><strong>22 * <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn mathvariant=\"bold\">10</mn><mn mathvariant=\"bold\">10</mn></mfrac></math> - 2 +</strong><strong>&nbsp;4.</strong><br>= 22 + 10 - 10<math display=\"inline\"><mo>&#247;</mo><mn>2</mn><mo>&#215;</mo><mn>4</mn></math><br>= 22 + 10 - 5<math display=\"inline\"><mo>&#215;</mo><mn>4</mn></math><br>= 22 + 10 - 20<br>= 32 - 20 <br>= 12</p>",
                    solution_hi: "<p>14.(c)<br><strong>22 * <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn mathvariant=\"bold\">10</mn><mn mathvariant=\"bold\">10</mn></mfrac></math> - 2 +</strong><strong>&nbsp;4.</strong><br>= 22 + 10 - 10<math display=\"inline\"><mo>&#247;</mo><mn>2</mn><mo>&#215;</mo><mn>4</mn></math><br>= 22 + 10 - 5<math display=\"inline\"><mo>&#215;</mo><mn>4</mn></math><br>= 22 + 10 - 20<br>= 32 - 20 <br>= 12</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. In a certain code language, <strong>ADG </strong>is written as <strong>ZWT</strong>. How will <strong>BEH </strong>be written as in that language ?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में <strong>ADG </strong>को <strong>ZWT </strong>लिखा जाता है। उसी भाषा में <strong>BEH </strong>को कैसे लिखा जाएगा ?</p>",
                    options_en: ["<p>VYS</p>", "<p>PQR</p>", 
                                "<p>YVS</p>", "<p>SVY</p>"],
                    options_hi: ["<p>VYS</p>", "<p>PQR</p>",
                                "<p>YVS</p>", "<p>SVY</p>"],
                    solution_en: "<p>15.(c)<br>Opposite of letters are written.<br>ADG = ZWT<br>BEH = YVS</p>",
                    solution_hi: "<p>15.(c)<br>विपरीत अक्षर लिखे जाते हैं।<br>ADG = ZWT<br>BEH = YVS</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "16. Out of the four numbers listed, three are alike in some manner and one is different. Select the odd one. <br />67, 41, 39, 17",
                    question_hi: "16. सूचीबद्ध चार संख्याओं में से तीन किसी न किसी रूप में समान हैं और एक भिन्न है। विजातीय का चयन करें।<br />67, 41, 39, 17",
                    options_en: [" 39", " 67", 
                                " 17", " 41"],
                    options_hi: [" 39", " 67",
                                " 17", " 41"],
                    solution_en: "16.(a)<br />All are prime numbers except 39",
                    solution_hi: "16.(a)<br />39 को छोड़कर सभी अभाज्य संख्याएँ हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Read the given statements and conclusions carefully and decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>Some printers are laser. <br>Some printers are inkjet. <br>No inkjet is a laser. <br><strong>Conclusion :</strong><br>I. Some inkjet are laser. <br>II. Some laser are inkjet. <br>III. Some laser are not inkjet.</p>",
                    question_hi: "<p>17. दिए गए कथनों और निष्कर्षों को ध्यान से पढ़ें औरबताइये कि कौन सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन :</strong><br>कुछ प्रिंटर लेजर हैं।<br>कुछ प्रिंटर इंकजेट हैं।<br>कोई इंकजेट लेजर नहीं है।<br><strong>निष्कर्ष :</strong><br>I. कुछ इंकजेट लेजर हैं<br>II. कुछ लेजर इंकजेट हैं।<br>III. कुछ लेजर इंकजेट नहीं हैं।</p>",
                    options_en: ["<p>Only I follows.</p>", "<p>Only II follows.</p>", 
                                "<p>None of I, II and III follows.</p>", "<p>Only III follows.</p>"],
                    options_hi: ["<p>केवल I अनुसरण करता है।</p>", "<p>केवल II अनुसरण करता है।</p>",
                                "<p>I, II और III में से कोई भी अनुसरण नहीं करता है।</p>", "<p>केवल III अनुसरण करता है</p>"],
                    solution_en: "<p>17.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913739.png\" alt=\"rId10\" width=\"190\" height=\"56\"><br>From the above diagram it is clear that no laser is inkjet so also some lasers are not inkjet.</p>",
                    solution_hi: "<p>17.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913857.png\" alt=\"rId11\" width=\"190\" height=\"59\"><br>उपरोक्त आरेख से यह स्पष्ट है कि कोई लेज़र इंकजेट नहीं है इसलिए कुछ लेज़र इंकजेट नहीं हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Six students, Maira, Amaira, Sara, Zara, Tara and Kiara, are made to sit against a wall. All of them are facing north. <br>Amaira is sitting 2nd to the left of Maira. <br>Sara is sitting 4th to the right of Tara. <br>Kiara is sitting to the immediate left of Amaira. <br>Tara is sitting at one of the ends. <br>Which 2 students are sitting at the extreme ends ?</p>",
                    question_hi: "<p>18. छह छात्रों, मायरा, अमायरा, सारा, ज़ारा, तारा और कियारा को एक दीवार के खिलाफ बैठाया जाता है। उन सभी का मुख उत्तर की ओर है।<br>अमायरा, मायरा के बायें से दूसरे स्थान पर बैठी है।<br>सारा, तारा के दायें चौथे स्थान पर बैठी है।<br>कियारा, अमायरा के ठीक बायें बैठी है।<br>तारा एक छोर पर बैठी है।<br>अंतिम छोर पर कौन से 2 छात्र बैठे हैं ?</p>",
                    options_en: ["<p>Tara and Zara</p>", "<p>Amaira and Kiara</p>", 
                                "<p>Tara and Sara</p>", "<p>Tara and Maira</p>"],
                    options_hi: ["<p>तारा और जारा</p>", "<p>अमेरा और कियारा</p>",
                                "<p>तारा और सारा</p>", "<p>तारा और मायरा</p>"],
                    solution_en: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913983.png\" alt=\"rId12\" width=\"226\" height=\"55\"></p>",
                    solution_hi: "<p>18.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930913983.png\" alt=\"rId12\" width=\"226\" height=\"55\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Abhishek is the brother of Ashu who is the son of Jai, Swati is the daughter of Jyoti. Malti is the mother of Ashu and Jyoti. How is Abhishek related to Swati ?</p>",
                    question_hi: "<p>19. अभिषेक आशु का भाई है जो जय का पुत्र है, स्वाति ज्योति की पुत्री है। मालती आशु और ज्योति की माता हैं। अभिषेक स्वाति से किस प्रकार संबंधित है ?</p>",
                    options_en: [" Brother’s Son ", " Mother’s Brother ", 
                                " Brother ", " Father "],
                    options_hi: [" भाई का बेटा ", " मा का भाई ",
                                " भाई ", " पिता "],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930914096.png\" alt=\"rId13\" width=\"185\" height=\"89\"></p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930914211.png\" alt=\"rId14\" width=\"233\" height=\"103\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Select the option that is related to the third number in the same way as the second number is related to the first number.<br>15 : 8 :: 25 : ?</p>",
                    question_hi: "<p>20. उस विकल्प का चयन करें जो तीसरी संख्या से उसी प्रकार संबंधित है जैसे दूसरी संख्या पहली संख्या से संबंधित है।<br>15 : 8 :: 25 : ?</p>",
                    options_en: ["<p>14</p>", "<p>10</p>", 
                                "<p>16</p>", "<p>12</p>"],
                    options_hi: ["<p>14</p>", "<p>10</p>",
                                "<p>16</p>", "<p>12</p>"],
                    solution_en: "<p>20.(b)<br>15 = 3<math display=\"inline\"><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>8</mn></math><br>25 = <math display=\"inline\"><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>5</mn><mo>+</mo><mn>5</mn><mo>=</mo><mn>10</mn></math></p>",
                    solution_hi: "<p>20.(b)<br>15 = 3<math display=\"inline\"><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>8</mn></math><br>25 = <math display=\"inline\"><mn>5</mn><mo>&#215;</mo><mn>5</mn><mo>=</mo><mn>5</mn><mo>+</mo><mn>5</mn><mo>=</mo><mn>10</mn></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. &lsquo;<strong>Cold</strong>&rsquo; is related to &lsquo;<strong>Refrigerator</strong>&rsquo; in the same way as &lsquo;<strong>Hot</strong>&rsquo; is related to&rsquo;____&rsquo;.</p>",
                    question_hi: "<p>21. <strong>ठंडे </strong>का सम्बन्ध \' <strong>रेफ्रिजरेटर</strong> \' से उसी तरह से <strong>गर्म </strong>का किस से संबंध है ?</p>",
                    options_en: ["<p>Oven</p>", "<p>Grinder</p>", 
                                "<p>Air conditioner</p>", "<p>Fire</p>"],
                    options_hi: ["<p>ओवन</p>", "<p>ग्राइंडर</p>",
                                "<p>एयर कंडीशनर</p>", "<p>आग</p>"],
                    solution_en: "<p>21.(a)<br>The refrigerator is used for cooling in the same way an oven is used for making things hot.</p>",
                    solution_hi: "<p>21.(a)<br>जिस तरह से चीजों को गर्म करने के लिए ओवन का इस्तेमाल किया जाता है, उसी तरह फ्रिज का इस्तेमाल ठंडा करने के लिए किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. O is the mother of A. B is the mother of C and E. C and D are the brothers and sons of A. G is the daughter of E and cousin of F. F is the son of D. How is B related to F ?</p>",
                    question_hi: "<p>22. O, A की माँ है। B, C और E की माँ है। C और D, A के भाई और पुत्र हैं। G, E की पुत्री और F का चचेरा भाई है। F, D का पुत्र है। B,F किस प्रकार से संबंधित है ?</p>",
                    options_en: ["<p>Mother</p>", "<p>Father&rsquo;s Mother</p>", 
                                "<p>Mother&rsquo;s Mother</p>", "<p>Son&rsquo;s Son</p>"],
                    options_hi: ["<p>माँ</p>", "<p>पिता की मां</p>",
                                "<p>मां की मां</p>", "<p>बेटे का बेटा</p>"],
                    solution_en: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930914349.png\" alt=\"rId15\" width=\"126\" height=\"149\"></p>",
                    solution_hi: "<p>22.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930914349.png\" alt=\"rId15\" width=\"126\" height=\"149\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "23. Four words have been given, out of which three are alike in some manner and one is different. Select the odd one.",
                    question_hi: "23. चार शब्द दिए गए हैं, जिनमें से तीन किसी तरह से एक जैसे हैं और एक अलग है। विजातीय का चयन करें",
                    options_en: [" Students ", " Dean ", 
                                " Principal ", " Director "],
                    options_hi: [" छात्र", " डीन",
                                " प्रिंसिपल", " डायरेक्टर "],
                    solution_en: "23.(a)<br />All are the heads of the institutions except students.",
                    solution_hi: "23.(a)<br />छात्रों को छोड़कर सभी संस्थानों के प्रमुख हैं।",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the number that can replace the question mark (?) in the following series.<br>1, 1, 2, 4, 6, 36, 42, ?</p>",
                    question_hi: "<p>24. उस संख्या का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है<br>1, 1, 2, 4, 6, 36, 42, ?</p>",
                    options_en: ["<p>1664</p>", "<p>1764</p>", 
                                "<p>168</p>", "<p>48</p>"],
                    options_hi: ["<p>1664</p>", "<p>1764</p>",
                                "<p>168</p>", "<p>48</p>"],
                    solution_en: "<p>24.(b)<br>1<math display=\"inline\"><mo>&#215;</mo><mn>1</mn><mo>=</mo><mn>1</mn></math><br>1 + 1 = 2<br>2<math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>4</mn></math><br>4 + 2 = 6<br>6 <math display=\"inline\"><mo>&#215;</mo><mn>6</mn><mo>=</mo><mn>36</mn></math><br>36 + 6 = 42<br>42 <math display=\"inline\"><mo>&#215;</mo><mn>42</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>1764</mn></math></p>",
                    solution_hi: "<p>24.(b)<br>1<math display=\"inline\"><mo>&#215;</mo><mn>1</mn><mo>=</mo><mn>1</mn></math><br>1 + 1 = 2<br>2<math display=\"inline\"><mo>&#215;</mo><mn>2</mn><mo>=</mo><mn>4</mn></math><br>4 + 2 = 6<br>6 <math display=\"inline\"><mo>&#215;</mo><mn>6</mn><mo>=</mo><mn>36</mn></math><br>36 + 6 = 42<br>42 <math display=\"inline\"><mo>&#215;</mo><mn>42</mn><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>1764</mn></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "6",
                    question_en: "<p>25. Select the number that will come next in the following series.<br>12, 15, 21, 30, 42, 57, ?</p>",
                    question_hi: "<p>25. निम्नलिखित श्रृंखला में आगे आने वाली संख्या का चयन करें<br>12, 15, 21, 30, 42, 57, ?</p>",
                    options_en: ["<p>65</p>", "<p>75</p>", 
                                "<p>80</p>", "<p>67</p>"],
                    options_hi: ["<p>65</p>", "<p>75</p>",
                                "<p>80</p>", "<p>67</p>"],
                    solution_en: "<p>25.(b)<br>12 + 3 = 15<br>15 + 6 = 21&nbsp;<br>21 + 9 = 30<br>30 + 12 = 42<br>42 + 15 = 57<br>57 + 18 = 75</p>",
                    solution_hi: "<p>25.(b)<br>12 + 3 = 15<br>15 + 6 = 21&nbsp;<br>21 + 9 = 30<br>30 + 12 = 42<br>42 + 15 = 57<br>57 + 18 = 75</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "6",
                    question_en: "<p>26. Read the given information carefully and answer the questions that follow.<br>There are six persons A, B, C, D, E, and F, in a family. There is one teacher, one business person and one accountant. B and D are unmarried males and are not employed. None of the ladies are in business or accountancy . A is the only child in the family. C is the wife of E, and F is the brother of C.<br>Who is the teacher ?</p>",
                    question_hi: "<p>26. दी गई जानकारी को ध्यानपूर्वक पढ़ें और उसके बाद आने वाले प्रश्नों के उत्तर दें। <br>एक परिवार में छह व्यक्ति A, B, C, D, E और F हैं। इनमें एक शिक्षक, एक व्यवसायी और एक लेखाकार है। B और D अविवाहित पुरुष हैं और कार्यरत नहीं हैं। कोई भी महिला व्यवसाय या एकाउंटेंसी में नहीं है। A परिवार में इकलौता बच्चा है। C, E की पत्नी है और F, C का भाई है।<br>शिक्षक कौन है ?</p>",
                    options_en: ["<p>F</p>", "<p>C</p>", 
                                "<p>D</p>", "<p>E</p>"],
                    options_hi: ["<p>F</p>", "<p>C</p>",
                                "<p>D</p>", "<p>E</p>"],
                    solution_en: "<p>26.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930914467.png\" alt=\"rId16\" width=\"266\" height=\"141\"></p>",
                    solution_hi: "<p>26.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1729930914586.png\" alt=\"rId17\" width=\"186\" height=\"148\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "6",
                    question_en: "<p>27. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>Author : Pen :: Surgeon : ?</p>",
                    question_hi: "<p>27. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br>लेखक : पेन :: शल्य चिकित्सक : ?</p>",
                    options_en: ["<p>Stitch</p>", "<p>Scalpel</p>", 
                                "<p>Operation</p>", "<p>Cut</p>"],
                    options_hi: ["<p>टांका</p>", "<p>छुरी</p>",
                                "<p>आपरेशन</p>", "<p>काटना</p>"],
                    solution_en: "<p>27.(b)<br>As Author uses pen to write similarly Surgeon uses Scalpel for surgery</p>",
                    solution_hi: "<p>27.(b)<br>जिस प्रकार लेखक लिखने के लिए पेन का उपयोग करता है उसी प्रकार सर्जन सर्जरी के लिए स्केलपेल का उपयोग करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "6",
                    question_en: "<p>28. Select the option that is related to the third letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster. <br><strong>RAM : PBO :: SPQ : ?</strong></p>",
                    question_hi: "<p>28. उस विकल्प का चयन करें जो तीसरे अक्षर-समूह से उसी प्रकार संबंधित है जैसे दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है।<br><strong>RAM : PBO :: SPQ : ?</strong></p>",
                    options_en: ["<p>QQR</p>", "<p>QQS</p>", 
                                "<p>QPS</p>", "<p>QQT</p>"],
                    options_hi: ["<p>QQR</p>", "<p>QQS</p>",
                                "<p>QPS</p>", "<p>QQT</p>"],
                    solution_en: "<p>28.(b)<br>R - 2 = P, A + 1 = B, M + 2 = O<br>S - 2 = Q, P + 1 = Q, Q + 2 = S<br>SPQ = QQS</p>",
                    solution_hi: "<p>28.(b)<br>R - 2 = P, A + 1 = B, M + 2 = O<br>S - 2 = Q, P + 1 = Q, Q + 2 = S<br>SPQ = QQS</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "6",
                    question_en: "<p>29. A question is given followed by two arguments. Decide which of the arguments is/are strong with respect to the question.<br><strong>Question :</strong><br>Should education at primary level be in mother tongue ?<br><strong>Arguments :</strong><br>1. Yes, it helps the students to understand the concepts easily. <br>2. No, finding tuition teachers in mother tongue is difficult.</p>",
                    question_hi: "<p>29. एक प्रश्न के बाद दो तर्क दिए गए हैं। निर्णय लें कि प्रश्न के संबंध में कौन-सा तर्क पुष्ट हैं।<br><strong>प्रश्न :</strong><br>क्या प्राथमिक स्तर पर शिक्षा मातृभाषा में होनी चाहिए ?<br><strong>तर्क :</strong><br>1. हां, यह छात्रों को अवधारणाओं को आसानी से समझने में मदद करता है।<br>2. नहीं, ट्यूशन शिक्षकों को मातृभाषा में खोजना कठिन है।</p>",
                    options_en: ["<p>Only 1 is strong.</p>", "<p>Only 2 is strong.</p>", 
                                "<p>Neither 1 nor 2 is strong.</p>", "<p>Bothe 1 and 2 are strong.</p>"],
                    options_hi: ["<p>केवल 1 पुष्ट है।</p>", "<p>केवल 2 पुष्ट है।</p>",
                                "<p>न तो 1 और न ही 2 पुष्ट है।</p>", "<p>दोनों 1 और 2 पुष्ट हैं।</p>"],
                    solution_en: "<p>29.(a)<br>For the given question, Argument 1 is strong. If education at primary level be in mother tongue it would help students to understand the concepts easily.</p>",
                    solution_hi: "<p>29.(a)<br>दिए गए प्रश्न के लिए, तर्क 1 मजबूत है। यदि प्राथमिक स्तर पर शिक्षा मातृभाषा में हो तो इससे छात्रों को अवधारणाओं को आसानी से समझने में मदद मिलेगी।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "misc",
                    question_en: "<p>30. Select the option that can replace the question mark(?) in the following series.<br>B7E, D11G, F15I, ?</p>",
                    question_hi: "<p>30. उस विकल्प का चयन करें जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है।<br>B7E, D11G, F15I, ?</p>",
                    options_en: ["<p>H19K</p>", "<p>H19L</p>", 
                                "<p>H18J</p>", "<p>G18I</p>"],
                    options_hi: ["<p>H19K</p>", "<p>H19L</p>",
                                "<p>H18J</p>", "<p>G18I</p>"],
                    solution_en: "<p>30.(a)<br>B + 2 = D, D + 2 = F<br>7 + 4 = 11, 11 + 4 = 15<br>E + 2 = G, G + 2 = I<br>Next wii be = F + 2 = H, 15 + 4 = 19, I + 2 = K <br>= H19K</p>",
                    solution_hi: "<p>30.(a)<br>B + 2 = D, D + 2 = F<br>7 + 4 = 11, 11 + 4 = 15<br>E + 2 = G, G + 2 = I<br>अगला होगा = F + 2 = H, 15 + 4 = 19, I + 2 = K <br>= H19K</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>