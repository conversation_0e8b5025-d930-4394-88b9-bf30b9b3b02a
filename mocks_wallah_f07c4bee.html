<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 75</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">75</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 74,
                end: 74
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. If the first and last letters in the word &lsquo;ZIGZAGGING&rsquo; were interchanged, and the same was done with the second and ninth letters, the third and eight letters, and so on, then which letter will be the fifth letter from the right end?</p>",
                    question_hi: "<p>1. यदि शब्द \'ZIGZAGGING\' के पहले और अंतिम अक्षरों को आपस में बदल दिया जाता है, और दूसरे और नौवें अक्षर, तीसरे और आठ अक्षरों के साथ भी ऐसा ही किया जाता है, तो दायें छोर से पाँचवाँ अक्षर कौन सा होगा?</p>",
                    options_en: ["<p>A</p>", "<p>N</p>", 
                                "<p>Z</p>", "<p>I</p>"],
                    options_hi: ["<p>A</p>", "<p>N</p>",
                                "<p>Z</p>", "<p>I</p>"],
                    solution_en: "<p>1.(a) ZIGZAGGING<br>We interchange A/Q , <br>Then we get <br>GNIGGAZGIZ<br>Hence ,The fifth letter from the right end = A</p>",
                    solution_hi: "<p>1.(a) ZIGZAGGING<br>प्रश्न के अनुसार, हम आदान-प्रदान करते हैं,<br>तब हम पाते हैं, GNIGGAZGIZ<br>अत: दायें छोर से पाँचवाँ अक्षर = A</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2.Three-fifths of my current age is the same as five-sixths of that of one of my cousins\'. My age ten years ago will be his age four years hence. My current age is ________ years.</p>",
                    question_hi: "<p>2. वर्तमान आयु का तीन - पांचवां उतना ही है जितना कि मेरे चचेरे भाइयों में से एक की आयु का पांचवां - छठा हिस्सा। मेरी दस साल पहले आयु, उनकी आज से चार साल बाद की आयु होगी। मेरी वर्तमान आयु ____ वर्ष है।</p>",
                    options_en: ["<p>60</p>", "<p>45</p>", 
                                "<p>50</p>", "<p>55</p>"],
                    options_hi: ["<p>60</p>", "<p>45</p>",
                                "<p>50</p>", "<p>55</p>"],
                    solution_en: "<p>2.(c)<br>Let my and my cousin&rsquo;s current age are x and y respectively<br>ATQ,<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>y<br>&rArr; <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>18</mn></mfrac></math><br>Also, (x - 10) = (y + 4)<br>x - y = 14<br>&rArr; (25 - 18) unit = 14<br>&rArr; 7 unit = 14<br>1 unit = 2<br>So, my current age is = 25 &times; 2 = 50 yrs</p>",
                    solution_hi: "<p>2.(c)<br>माना मेरी और मेरे चचेरे भाई की वर्तमान आयु क्रमशः x और y है<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>y<br>&rArr; <math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>3</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>18</mn></mfrac></math><br>और , (x - 10) = (y + 4)<br>x - y = 14<br>&rArr; (25 - 18) इकाई = 14<br>&rArr; 7 इकाई = 14<br>1 इकाई = 2<br>इसलिए, मेरी वर्तमान आयु = 25 &times; 2 = 50 वर्ष</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. Fleming\'s right-hand rule can be used to determine the direction of induced current when the angle between the magnetic field and the motion of the conductor is:</p>",
                    question_hi: "<p>3.जब चुंबकीय क्षेत्र और चालक की गति के बीच का कोण___________ होता है तो प्रेरित धारा की दिशा निर्धारित करने के लिए फ्लेमिंग के दाएं हाथ के नियम का उपयोग किया जा सकता है।,</p>",
                    options_en: ["<p>90&deg;</p>", "<p>30&deg;</p>", 
                                "<p>180&deg;</p>", "<p>0&deg;</p>"],
                    options_hi: ["<p>90&deg;</p>", "<p>30&deg;</p>",
                                "<p>180&deg;</p>", "<p>0&deg;</p>"],
                    solution_en: "<p>3.(a) <strong>90&deg;. Fleming\'s right-hand rule</strong> - It states that if we arrange our thumb, forefinger and middle finger of the right-hand perpendicular to each other, then the thumb points towards the direction of the motion of the conductor relative to the magnetic field, the forefinger points towards the direction of the magnetic field and the middle finger points towards the direction of the induced current. In electromagnetism, It shows the direction of induced current for generators.</p>",
                    solution_hi: "<p>3.(a) <strong>90&deg;। फ्लेमिंग के दाएँ हाथ का नियम</strong> - इसके अनुसार &lsquo;यदि हम अपने दाहिने हाथ के अंगूठे, तर्जनी और मध्यमा को एक दूसरे के लंबवत व्यवस्थित करते हैं, तो अंगूठा चुंबकीय क्षेत्र के सापेक्ष चालक की गति की दिशा की ओर इशारा करता है, तर्जनी चुंबकीय क्षेत्र की दिशा की ओर इशारा करती है और मध्यमा प्रेरित धारा की दिशा की ओर इशारा करती है। विद्युत चुंबकत्व में, यह जनरेटर के लिए प्रेरित धारा की दिशा दर्शाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. If 19<sup>th</sup>&nbsp;July 2019 was a Friday, what was the day on 21<sup>th</sup> September 2019 ?</p>",
                    question_hi: "<p>4. यदि 19 जुलाई 2019 को शुक्रवार था, तो 21 सितंबर 2019 को कौन सा दिन था?</p>",
                    options_en: ["<p>Thursday</p>", "<p>Tuesday</p>", 
                                "<p>Wednesday</p>", "<p>Saturday</p>"],
                    options_hi: ["<p>गुरुवार</p>", "<p>मंगलवार</p>",
                                "<p>बुधवार</p>", "<p>शनिवार</p>"],
                    solution_en: "<p>4.(d)</p>\n<p>Total number of days between 19th July 2019 and 21th September 2019</p>\n<p>= 12 + 31 + 21 &rArr; 64<br>Total odd remaining days = <math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &rArr; 1<br>Hence, friday + 1 = Saturday</p>",
                    solution_hi: "<p>4.(d)</p>\n<p>19 जुलाई 2019 और 21 सितंबर 2019 के बीच दिनों की कुल संख्या<br>= 12 + 31 + 21 &rArr; 64<br>कुल विषम शेष दिन = <math display=\"inline\"><mfrac><mrow><mn>64</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &rArr; 1<br>अत:, शुक्रवार + 1 = शनिवार</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. Megaloblastic anaemia is caused by the deficiency of which vitamin?</p>",
                    question_hi: "<p>5. मेगालोब्लास्टिक एनीमिया किस विटामिन की कमी से होता है?</p>",
                    options_en: ["<p>Vitamin - A</p>", "<p>Vitamin - B<sub>2</sub></p>", 
                                "<p>Vitamin - C</p>", "<p>Vitamin - B<sub>9</sub></p>"],
                    options_hi: ["<p>विटामिन A</p>", "<p>विटामिन - B<sub>2</sub></p>",
                                "<p>विटामिन - C</p>", "<p>विटामिन - B<sub>9</sub></p>"],
                    solution_en: "<p>5.(d) <strong>Vitamin - B<sub>9</sub>:</strong> Low levels of folic acid can cause megaloblastic anemia. In this condition, the red blood cells become larger than normal. <strong>Chemical name and Deficiency diseases</strong>: Vitamin A - Retinol, night blindness. Vitamin B1 - Thiamine, Beri-beri. Vitamin C (Ascorbic acid) - Scurvy. Vitamin D - Calciferol, Rickets. Vitamin B2 - Riboflavin. Vitamin B9 and Vitamin B12 - Megaloblastic Anemia.</p>",
                    solution_hi: "<p>5.(d) <strong>विटामिन-B<sub>9</sub></strong>: फोलिक अम्ल का निम्न स्तर मेगालोब्लास्टिक एनीमिया का कारण बन सकता है। इस स्थिति में लाल रक्त कोशिकाएं सामान्य से बड़ी हो जाती हैं। <strong>रासायनिक नाम एवं कमी से होने वाले रोग:</strong> विटामिन A- रेटिनोल, रतौंधी। विटामिन B1 - थायमिन, बेरी-बेरी। विटामिन C (एस्कॉर्बिक अम्ल) - स्कर्वी। विटामिन D - कैल्सीफेरॉल, रिकेट्स। विटामिन B2 - राइबोफ्लेविन। विटामिन B9 और विटामिन B12 - मेगालोब्लास्टिक एनीमिया।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. Find the compound interest on 62500 at 21% per annum for 1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> yrs.</p>",
                    question_hi: "<p>6. ₹62,500 पर 21% की वार्षिक दर से 1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष के लिए चक्रवृद्धि ब्याज क्या होगा ?</p>",
                    options_en: ["<p>₹19,687.5</p>", "<p>₹20,695</p>", 
                                "<p>₹21,638.5</p>", "<p>₹21,065.6</p>"],
                    options_hi: ["<p>₹ 19,687.5</p>", "<p>₹20,695</p>",
                                "<p>₹21,638.5</p>", "<p>₹21,065.6</p>"],
                    solution_en: "<p>6.(d)<br>Rate for 1 year = 21% = <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> , Rate for half year = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac></math>% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>200</mn></mfrac></math><br>&nbsp; Principal&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;Amount<br>&nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;121 <br>&nbsp; &nbsp; &nbsp; 200&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 221<br>_________________________<br>&nbsp; &nbsp; 20,000&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;26,741<br>CI = 26,741 - 20,000 = ₹6,741<br>Here, 20,000 unit = ₹62,500<br>Then, C.I (6741 unit ) =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>62</mn><mo>,</mo><mn>500</mn></mrow><mrow><mn>20</mn><mo>,</mo><mn>000</mn></mrow></mfrac></math> &times; 6741</p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>,</mo><mn>13</mn><mo>,</mo><mn>125</mn></mrow><mn>200</mn></mfrac></math> = ₹21,065.6</p>",
                    solution_hi: "<p>6.(d)<br>1 वर्ष के लिए दर = 21% = <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>, छमाही के लिए दर = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>2</mn></mfrac></math>% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>200</mn></mfrac></math><br>&nbsp; &nbsp; मूलधन&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;धनराशि<br>&nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;121 <br>&nbsp; &nbsp; &nbsp; 200&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 221<br>_________________________<br>&nbsp; &nbsp; 20,000&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;26,741<br>चक्रवृद्धि ब्याज = 26,741 &ndash; 20,000 = ₹6,741<br>यहां 20,000 इकाई = ₹62,500 <br>फिर, 6741 इकाई =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>62</mn><mo>,</mo><mn>500</mn></mrow><mrow><mn>20</mn><mo>,</mo><mn>000</mn></mrow></mfrac></math> &times; 6741</p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>42</mn><mo>,</mo><mn>13</mn><mo>,</mo><mn>125</mn></mrow><mn>200</mn></mfrac></math> = ₹21,065.6</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. In the given letter-cluster pairs, the first letter-cluster is related to the second letter-cluster following a certain logic. Study the given pairs carefully, and from the given options, select the pair that follows the same logic.<br>ASD : EWH<br>FGH : JKL</p>",
                    question_hi: "<p>7. दिए गए अक्षर समूह युग्म में, पहला अक्षर-समूह एक निश्चित तर्क के आधार पर दूसरे अक्षर समूह से संबंधित है। दिए गए युग्मों का ध्यानपूर्वक अध्ययन कीजिए, और दिए गए विकल्पों में से उस युग्म का चयन कीजिए, जो समान तर्क का पालन करता है।<br>ASD : EWH<br>FGH : JKL</p>",
                    options_en: ["<p>GYU : JCY</p>", "<p>JKL : NOP</p>", 
                                "<p>NSP : ORQ</p>", "<p>CBH : GFM</p>"],
                    options_hi: ["<p>GYU : JCY</p>", "<p>JKL : NOP</p>",
                                "<p>NSP : ORQ</p>", "<p>CBH : GFM</p>"],
                    solution_en: "<p>7.(b) ASD : EWH<br>FGH : JKL<br>Add (4) to each alphabet<br>JKL : NOP</p>",
                    solution_hi: "<p>7.(b) ASD : EWH<br>FGH : JKL<br>प्रत्येक वर्ण में (4) जोड़ने पर <br>JKL : NOP</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. Which of the following cities is famous for the prestigious \'Kumbh Mela\'?</p>",
                    question_hi: "<p>8. निम्नलिखित में से कौन सा शहर प्रतिष्ठित कुंभ मेला\' के लिए प्रसिद्ध है?</p>",
                    options_en: ["<p>Ahmedabad</p>", "<p>Surat</p>", 
                                "<p>Lucknow</p>", "<p>Prayagraj</p>"],
                    options_hi: ["<p>अहमदाबाद</p>", "<p>सूरत</p>",
                                "<p>लखनऊ</p>", "<p>प्रयागराज</p>"],
                    solution_en: "<p>8.(d) <strong>Prayagraj</strong>. <strong>Kumbh Mela</strong> is a major pilgrimage and festival in Hinduism. It is celebrated in a cycle of approximately 12 years, to celebrate every revolution <strong>Brihaspati</strong> (Jupiter) completes, at four river-bank pilgrimage sites - <strong>Prayagraj</strong> (Ganges-Yamuna-Sarasvati rivers confluence), <strong>Haridwar</strong> (Ganges), <strong>Nashik</strong> (Godavari), and <strong>Ujjain</strong> (Shipra).</p>",
                    solution_hi: "<p>8.(d) <strong>प्रयागराज</strong>। कुंभ मेला हिंदू धर्म में एक प्रमुख तीर्थ और त्योहार है। यह लगभग 12 वर्षों के चक्र में मनाया जाता है, हर क्रांति का जश्न मनाने के लिए बृहस्पति (Jupiter) चार नदी-तट तीर्थ स्थलों- <strong>प्रयागराज</strong> (गंगा-यमुना-सरस्वती नदियों का संगम), <strong>हरिद्वार</strong> (गंगा), <strong>नासिक</strong> (गोदावरी) और <strong>उज्जैन</strong> (शिप्रा) पर चक्र पूरा करता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. If P denotes \'&times;\', T denotes &lsquo;&ndash;&rsquo;, M denotes &lsquo;+&rsquo; and B denotes &lsquo;&divide;&rsquo;, what will come in place of the question mark (?) in the following equation?<br>54 B 9 P 11 T 13 M 17 = ?</p>",
                    question_hi: "<p>9. यदि P, \'&times;\' निरूपित करता है, T, &lsquo;-&rsquo; \' निरूपित करता है, M, \'+\' निरूपित करता है, और B, \'<math display=\"inline\"><mo>&#247;</mo></math>\' को निरूपित करता है, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा ?<br>54 B 9 P 11 T 13 M 17 = ?</p>",
                    options_en: ["<p>78</p>", "<p>70</p>", 
                                "<p>74</p>", "<p>73</p>"],
                    options_hi: ["<p>78</p>", "<p>70</p>",
                                "<p>74</p>", "<p>73</p>"],
                    solution_en: "<p>9.(b) 54 B 9 P 11 T 13 M 17 <br>After interchanging letters with mathematical operators<br>54 &divide; 9 &times; 11 - 13 + 17</p>\n<p>= 66 + 4</p>\n<p>= 70</p>",
                    solution_hi: "<p>9.(b) 54 B 9 P 11 T 13 M 17 <br>गणितीय संक्रियाओं के साथ अक्षरों को आपस में&nbsp;बदलने पर,<br>54 &divide; 9 &times; 11 - 13 + 17</p>\n<p>= 66 + 4</p>\n<p>= 70</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10. Study the given graph and table and answer the following question. <br>Data of different states regarding population of states in the year 1998<br><strong id=\"docs-internal-guid-a355cf87-7fff-e25a-6534-95b9a5bc0a07\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfPZluryHctEu_46EiJax6wVbeFOl7fPTnM0UJlH_xypWxF2nfCLoV2ZfESOu1kjJRA5uCHOxKLgwEWcTBbNayHCA3kNufJMkI9V9-VGosEffrKksPnaQJ3FlN67Jd5rAEZo9hjtNGfRuOPwJ6CJlqcVqc?key=FY2PB4WGVOjqZykVyvi7UQ\" width=\"209\" height=\"139\"></strong><br>Total population of the given states <br>= 32760000<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349474115.png\" alt=\"rId7\" width=\"231\" height=\"111\"> <br>If in the year 1998, there was an increase of 20% in the population of Goa and 10% in the population of Arunachal Pradesh compared to the previous year, then what was the ratio of populations of Goa and Arunachal Pradesh in 1997?</p>",
                    question_hi: "<p>10. दिए गए ग्राफ और तालिका का अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए <br>वर्ष 1998 में राज्यों की जनसंख्या से संबंधित विभिन्न राज्यों के आंकड़े दिए गए राज्यों की कुल जनसंख्या = 32760000<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349474252.png\" alt=\"rId8\" width=\"259\" height=\"178\"> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349474400.png\" alt=\"rId9\" width=\"260\" height=\"175\"> <br>यदि वर्ष 1998 में, पिछले वर्ष की तुलना में गोवा की जनसंख्या में 20% और अरुणाचल प्रदेश की जनसंख्या में 10% की वृद्धि हुई है, तो वर्ष 1997 में गोवा और अरुणाचल प्रदेश की जनसंख्या का अनुपात क्या था</p>",
                    options_en: ["<p>7 : 11</p>", "<p>11 : 25</p>", 
                                "<p>4 : 5</p>", "<p>25 : 11</p>"],
                    options_hi: ["<p>7 : 11</p>", "<p>11 : 25</p>",
                                "<p>4 : 5</p>", "<p>25 : 11</p>"],
                    solution_en: "<p>10.(b) Population of goa in 1998 <br>= 32760000 &times; <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 3931200<br>Population of Arunachal Pradesh in 1998 = 32760000 &times; <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 8190000<br>According to question , <br>Population of goa in 1997 : Population of Arunachal Pradesh in 1997<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3931200</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>120</mn></mfrac></math>&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8190000</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>110</mn></mfrac></math> <br>&nbsp; 32760 &times; 11&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;819000 <br>&nbsp; &nbsp; &nbsp; 36036&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;81900 <br>&nbsp;11 &times; 3276&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;25 &times; 3276 <br><math display=\"inline\"><mi>&#160;</mi></math>&nbsp; &nbsp; &nbsp; &nbsp;11&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;25</p>",
                    solution_hi: "<p>10.(b) 1998 में गोवा की जनसंख्या <br>= 32760000 &times; <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 3931200<br>1998 में अरुणाचल प्रदेश की जनसंख्या <br>= 32760000 &times; <math display=\"inline\"><mfrac><mrow><mn>25</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 8190000<br>प्रश्नअनुसार , <br>आवश्यक अनुपात :- <br>1997 में गोवा की जनसंख्या : 1997 में अरुणाचल प्रदेश की जनसंख्या<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3931200</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>120</mn></mfrac></math>&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8190000</mn><mo>&#215;</mo><mn>100</mn></mrow><mn>110</mn></mfrac></math> <br>&nbsp; 32760 &times; 11&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;819000 <br>&nbsp; &nbsp; &nbsp; 36036&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;81900 <br>&nbsp;11 &times; 3276&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;25 &times; 3276 <br><math display=\"inline\"><mi>&#160;</mi></math>&nbsp; &nbsp; &nbsp; &nbsp;11&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;25</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. Select the option which is related to the fifth number in the same way as the second number is related to the first number and the fourth number is related to the third number.<br>3 : 25 :: 7 : 81 :: 11 : ?</p>",
                    question_hi: "<p>11. उस विकल्प का चयन करें, जिसका पांचवीं संख्या से वही संबंध है, जो दूसरी संख्या का पहली संख्या से है, और चौथी संख्या का तीसरी संख्या से है।<br>3 : 25 :: 7 : 81 :: 11 : ?</p>",
                    options_en: ["<p>144</p>", "<p>121</p>", 
                                "<p>169</p>", "<p>196</p>"],
                    options_hi: ["<p>144</p>", "<p>121</p>",
                                "<p>169</p>", "<p>196</p>"],
                    solution_en: "<p>11(c)<br>Logic : (1st number + 2)<sup>2</sup> = 2nd number<br>In 3 : 25 &rArr; (3 + 2)<sup>2</sup> = 5<sup>2</sup> = 25<br>In 7 : 81 &rArr; (7 + 2)<sup>2</sup> = 9<sup>2</sup> = 81<br>Similarly,<br>In 11 : ? &rArr; (11 + 2)<sup>2</sup> = 13<sup>2</sup> = 169</p>",
                    solution_hi: "<p>11(c)<br><strong>तर्क : </strong>(पहली संख्या + 2)<sup>2</sup> = दूसरी संख्या<br>(3 : 25)में &rArr; (3 + 2)<sup>2</sup> = 5<sup>2&nbsp;</sup> = 25<br>(7 : 81) में &rArr; (7 + 2)<sup>2</sup> = 9<sup>2</sup>&nbsp; = 81&nbsp;<br>इसी प्रकार,<br>(11 : ?) में &rArr; (11 + 2)<sup>2</sup> = 13<sup>2</sup> = 169</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. A current of 5 A flows through a conductor having resistance 2 &Omega;. The potential difference (in volt) across the ends of the conductor is:</p>",
                    question_hi: "<p>12. 2&Omega; प्रतिरोध वाले एक चालक से 5 A की धारा प्रवाहित होती है। चालक के सिरों के बीच विभवांतर ( वोल्ट में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>3</p>", "<p>7</p>", 
                                "<p>2.5</p>", "<p>10</p>"],
                    options_hi: ["<p>3</p>", "<p>7</p>",
                                "<p>2.5</p>", "<p>10</p>"],
                    solution_en: "<p>12.(d) Given, Current = 5 A and Resistance = 2 &Omega; and V = ?<br>Using Ohm&rsquo;s Law, V = IR &rArr; V = (5)(2) = 10V.</p>",
                    solution_hi: "<p>12.(d) दिया गया है, धारा = 5 A और प्रतिरोध = 2 &Omega; और V= ? <br>ओम के नियम का उपयोग करते हुए, V = IR &rArr; V = (5)(2) = 10V |</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. Blood is a type of _____ tissue.</p>",
                    question_hi: "<p>13. रक्त एक प्रकार का _____ ऊतक है।</p>",
                    options_en: ["<p>epithelial</p>", "<p>muscular</p>", 
                                "<p>connective</p>", "<p>nervous</p>"],
                    options_hi: ["<p>उपकला</p>", "<p>मांसल</p>",
                                "<p>संयोजी</p>", "<p>स्नायविक</p>"],
                    solution_en: "<p>13.(c) <strong>Connective tissue:</strong> Provides support and connects body parts. Example - bone, cartilage. <strong>Epithelial tissue</strong>: Covers and lines body surfaces. Example - lining of the respiratory and digestive tracts. <strong>Muscular tissue:</strong> Responsible for movement. Example - muscle in arms and legs. <strong>Nervous tissue</strong>: Forms the nervous system, controls body functions. Example - brain, spinal cord, and nerves. <strong>Blood</strong> consists of various cellular components, such as red blood cells, white blood cells, and platelets, suspended in a fluid matrix called plasma.</p>",
                    solution_hi: "<p>13.(c) <strong>संयोजी ऊतक:</strong> शरीर के अंगों को सहारा प्रदान करता है और जोड़ता है। उदाहरण - हड्डी, उपास्थि। <strong>उपकला ऊतक: </strong>शरीर की सतहों को ढकता और रेखाबद्ध करता है। उदाहरण - श्वसन और पाचन तंत्र की परत। <strong>मांसपेशी ऊतक</strong>: गति के लिए जिम्मेदार। उदाहरण - हाथ और पैर की मांसपेशियाँ। <strong>तंत्रिका ऊतक:</strong> तंत्रिका तंत्र का निर्माण करता है, शरीर के कार्यों को नियंत्रित करता है। उदाहरण-मस्तिष्क, मेरूरज्जु एवं तंत्रिकाएँ। <strong>रक्त</strong> में विभिन्न सेलुलर घटक होते हैं, जैसे लाल रक्त कोशिकाएं, सफेद रक्त कोशिकाएं और प्लेटलेट्स, जो प्लाज्मा नामक द्रव मैट्रिक्स में निलंबित होते हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. Assuming P &amp; Q were Arithmetic&nbsp;Operators, if 24 P 12 Q 13 = 15 and 16 Q&nbsp;56 P 14 = 20, then 87 P 29 Q 22 =?</p>",
                    question_hi: "<p>14. मान लें कि P &amp; Q अंकगणितीय प्रचालन हैं, यदि 24 P 12 Q 13 = 15 और 16 Q 56 P 14 = 20, तो 87 P 29 Q 22 =?</p>",
                    options_en: ["<p>30</p>", "<p>27</p>", 
                                "<p>25</p>", "<p>28</p>"],
                    options_hi: ["<p>30</p>", "<p>27</p>",
                                "<p>25</p>", "<p>28</p>"],
                    solution_en: "<p>14.(c) Let , P = <math display=\"inline\"><mo>&#247;</mo></math> , Q = + <br>Then , 24 P 12 Q 13 <br>= 24 <math display=\"inline\"><mo>&#247;</mo></math> 12 + 13 = 15 (satisfied)<br>Now for 87 P 29 Q 22 = 87 <math display=\"inline\"><mo>&#247;</mo></math> 29 + 22<br>= 3 + 22 = 25</p>",
                    solution_hi: "<p>14.(c) माना, P = <math display=\"inline\"><mo>&#247;</mo></math> , Q = + <br>तब, 24 P 12 Q 13 <br>= 24 <math display=\"inline\"><mo>&#247;</mo></math> 12 + 13 = 15 (संतुष्ट)<br>अब 87 P 29 Q 22 के लिए = 87 <math display=\"inline\"><mo>&#247;</mo></math> 29 + 22 <br>= 3 + 22 = 25</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. Who was the first Indian citizen to receive the Nobel Prize in literature?</p>",
                    question_hi: "<p>15. साहित्य में नोबेल पुरस्कार पाने वाले पहले भारतीय नागरिक कौन थे?</p>",
                    options_en: ["<p>Swami VNaiduivekananda</p>", "<p>Sully Prudhomme</p>", 
                                "<p>Sarojini</p>", "<p>Rabindranath Tagore</p>"],
                    options_hi: ["<p>स्वामी विवेकानंद</p>", "<p>सुली प्रुधोमे</p>",
                                "<p>सरोजिनी नायडू</p>", "<p>रवींद्रनाथ टैगोर</p>"],
                    solution_en: "<p>15.(d) <strong>Rabindranath Tagore. Indian Nobel Prize winners:</strong> Rabindranath Tagore (Literature) 1913, CV Raman (Physics) 1930, Har Gobind Khurana (Medicine) 1968, Mother Teresa (Peace) 1979, Subrahmanyan Chandrasekhar (Physics) 1983, Amartya Sen (Economics) 1998, Venkatraman Ramakrishnan (Chemistry) 2009, Kailash Satyarthi (Peace) 2014, Abhijit Banerjee (Economics) 2019.</p>",
                    solution_hi: "<p>15.(d) <strong>रवींद्रनाथ टैगोर। भारतीय नोबेल पुरस्कार विजेता: </strong>रवींद्रनाथ टैगोर (साहित्य) 1913, सी वी रमन (भौतिकी) 1930, हरगोबिंद खुराना (चिकित्सा) 1968, मदर टेरेसा (शांति) 1979, सुब्रह्मण्यन चंद्रशेखर (भौतिकी) 1983, अमर्त्य सेन (अर्थशास्त्र) 1998, वेंकटरमन रामकृष्णन (रसायन विज्ञान) 2009, कैलाश सत्यार्थी (शांति) 2014, अभिजीत बनर्जी (अर्थशास्त्र) 2019।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. Select the appropriate picture from the alternatives to proceed in the given series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349474544.png\" alt=\"rId10\" width=\"327\" height=\"74\"></p>",
                    question_hi: "<p>16. दी गई श्रृंखला में आगे बढ़ने के लिए विकल्पों में से उपयुक्त चित्र का चयन करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349474544.png\" alt=\"rId10\" width=\"318\" height=\"72\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349474664.png\" alt=\"rId11\" width=\"64\" height=\"71\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349474786.png\" alt=\"rId12\" width=\"69\" height=\"76\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475045.png\" alt=\"rId13\" width=\"65\" height=\"72\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475163.png\" alt=\"rId14\" width=\"65\" height=\"72\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349474664.png\" alt=\"rId11\" width=\"69\" height=\"76\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349474786.png\" alt=\"rId12\" width=\"71\" height=\"78\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475045.png\" alt=\"rId13\" width=\"70\" height=\"77\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475163.png\" alt=\"rId14\" width=\"71\" height=\"78\"></p>"],
                    solution_en: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475045.png\" alt=\"rId13\" width=\"64\" height=\"71\"></p>",
                    solution_hi: "<p>16.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475045.png\" alt=\"rId13\" width=\"64\" height=\"71\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. Given that the mean of five numbers is 28. If one is excluded, the mean gets reduced by 5. Determine the excluded number.</p>",
                    question_hi: "<p>17. पाँच संख्याओं का माध्य 28 है। यदि एक को बाहर रखा जाता है, तो माध्य 5 से घट जाता है।&nbsp;बाहर रखी गयी संख्या निर्धारित कीजिए।</p>",
                    options_en: ["<p>46</p>", "<p>48</p>", 
                                "<p>47</p>", "<p>45</p>"],
                    options_hi: ["<p>46</p>", "<p>48</p>",
                                "<p>47</p>", "<p>45</p>"],
                    solution_en: "<p>17.(b)<br>Sum of five numbers = 5 &times; 28 = 140<br>sum of four numbers = 4 &times; 23 = 92<br>so, excluded number = 140 &ndash; 92 = 48</p>",
                    solution_hi: "<p>17.(b)<br>पांच संख्याओं का योग = 5 &times; 28 = 140<br>चार संख्याओं का योग = 4 &times; 23 = 92<br>अत: अपवर्जित संख्या = 140 &ndash; 92 = 48</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. A spring balance is a device commonly used for measuring the _____ acting on an object.</p>",
                    question_hi: "<p>18. स्प्रिंग बैलेंस एक उपकरण है जिसका उपयोग आमतौर पर किसी वस्तु पर कार्य करने वाले _____ को मापने के लिए किया जाता है।</p>",
                    options_en: ["<p>force</p>", "<p>momentum</p>", 
                                "<p>velocity</p>", "<p>mass</p>"],
                    options_hi: ["<p>बल</p>", "<p>गति</p>",
                                "<p>वेग</p>", "<p>द्रव्यमान</p>"],
                    solution_en: "<p>18.(a) <strong>force (F) </strong>- SI unit - Newton, F = m (mass) &times; a (acceleration), a vector quantity, <strong>Spring balance:-</strong> Hooke\'s Law, which states that the force needed to extend a spring is proportional to the distance that spring is extended from its rest position. <strong>Momentum (p):-</strong> SI unit: (kg&sdot;m/s), p = m (mass) &times; v (velocity), a vector quantity. <strong>Velocity (V):-</strong> It is the rate of change of distance. SI unit:- m/s. <strong>Mass (m):</strong>- Dimensionless quantity representing the amount of matter in a particle. SI unit:- kg, Mass = &rho; (density) &times; v (volume), a scalar quantity.</p>",
                    solution_hi: "<p>18.(a) <strong>बल (F)</strong> - SI मात्रक - न्यूटन, F = m (द्रव्यमान) &times; a (त्वरण), एक वेक्टर मात्रा, स्प्रिंग बैलेंस: - हुक का नियम, जो बताता है कि स्प्रिंग को फैलाने के लिए आवश्यक बल स्प्रिंग की दूरी के समानुपाती होता है इसकी विश्राम स्थिति से बढ़ाया गया है। <strong>संवेग (p):</strong>- SI मात्रक : (kg&sdot;m/s), p = m (द्रव्यमान) &times; v (वेग), सदिश राशि है। <strong>वेग (V):-</strong> यह दूरी के परिवर्तन की दर है। SI मात्रक :- मीटर/सेकंड। <strong>द्रव्यमान (m):-</strong> किसी कण में पदार्थ की मात्रा को दर्शाने वाली आयामहीन मात्रा (Dimensionless quantity) है। SI मात्रक :- किग्रा, द्रव्यमान = &rho; (घनत्व) &times; v (आयतन), अदिश राशि है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. Which is the least frictional force of the following?</p>",
                    question_hi: "<p>19. निम्नलिखित में से सबसे कम घर्षण बल कौन सा है?</p>",
                    options_en: ["<p>Sliding</p>", "<p>Fluid</p>", 
                                "<p>Static</p>", "<p>Rolling</p>"],
                    options_hi: ["<p>सर्पी</p>", "<p>द्रव</p>",
                                "<p>स्थैतिक</p>", "<p>बेल्लन</p>"],
                    solution_en: "<p>19.(d) <strong>Rolling friction </strong>is a type of kinetic friction; it comes into play when one body actually rolls on the surface of another body. Static &gt; Sliding &gt; Rolling. <strong>Static frictional</strong> force is the strongest form among all. <strong>Fluid friction</strong> is friction that acts on objects that are moving through a fluid. <strong>Friction</strong> is the force that resists motion when the surface of one object comes in contact with the surface of another.</p>",
                    solution_hi: "<p>19.(d) <strong>बेल्लन घर्षण</strong> एक प्रकार का गतिज घर्षण है; यह तब क्रियान्वित होता है जब एक पिंड वास्तव में दूसरे पिंड की सतह पर लुढ़कता है। स्थैतिक &gt; सर्पी &gt; बेल्लन। <strong>स्थैतिक घर्षण</strong> बल सभी में सबसे शक्तिशाली रूप है। <strong>द्रव घर्षण</strong> वह घर्षण है जो तरल पदार्थ के माध्यम से गति करने वाली वस्तुओं पर कार्य करता है। <strong>घर्षण</strong> वह बल है जो गति का विरोध करता है जब एक वस्तु की सतह दूसरी वस्तु की सतह के संपर्क में आती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. The given Problem Figure is embedded in one of the given Answer Figures. Which is that Answer Figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475304.png\" alt=\"rId15\" width=\"158\" height=\"124\"></p>",
                    question_hi: "<p>20. दिया गया समस्या चित्र (Problem figure) नीचे के उत्तर चित्रों (Answer Figures) में से किसी एक में निहित है। उस उत्तर चित्र (Answer Figure) की पहचान करें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475453.png\" alt=\"rId16\" width=\"178\" height=\"142\"></p>",
                    options_en: ["<p>D</p>", "<p>A</p>", 
                                "<p>C</p>", "<p>B</p>"],
                    options_hi: ["<p>D</p>", "<p>A</p>",
                                "<p>C</p>", "<p>B</p>"],
                    solution_en: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475596.png\" alt=\"rId17\" width=\"87\" height=\"128\"></p>",
                    solution_hi: "<p>20.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475596.png\" alt=\"rId17\" width=\"81\" height=\"119\"></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. The Digambara sect belongs to which of the following religions?</p>",
                    question_hi: "<p>21. दिगंबर संप्रदाय इनमें से किस धर्म से संबंधित है?</p>",
                    options_en: ["<p>Buddhism</p>", "<p>Islam</p>", 
                                "<p>Jainism</p>", "<p>Sikhism</p>"],
                    options_hi: ["<p>बौद्ध धर्म</p>", "<p>इस्लाम धर्म</p>",
                                "<p>जैन धर्म</p>", "<p>सिख धर्म</p>"],
                    solution_en: "<p>21.(c) <strong>Jainism</strong> is divided into two major schools of thought, Digambara and Svetambara. <strong>Digambara </strong>- Monks of this sect believe in complete nudity. <strong>Bhadrabahu </strong>was an exponent of this sect. Follow all five vows of Satya, Ahimsa, Asteya, Aparigraha and Brahmacharya. <strong>Svetambara - </strong>Monks wear white clothes. <strong>Sthulabhadra</strong> was an exponent of this sect. Follow only 4 vows except brahmacharya. The first Tirthankara was <strong>Rishabnatha. Vardhamana Mahavira</strong> was the 24th Tirthankara.</p>",
                    solution_hi: "<p>21.(c) <strong>जैन धर्म</strong> विचार के दो प्रमुख संप्रदाय में विभाजित है, दिगंबर और श्वेतांबर। <strong>दिगंबर - </strong>इस संप्रदाय के साधु पूर्ण नग्नता में विश्वास करते हैं। <strong>भद्रबाहु </strong>इस संप्रदाय के प्रतिपादक थे। सत्य, अहिंसा, अस्तेय, अपरिग्रह और ब्रह्मचर्य के सभी पांच व्रतों का पालन करें। <strong>श्वेतांबर -</strong> इस संप्रदाय के साधु सफेद वस्त्र धारण करते हैं। <strong>स्थूलभद्र </strong>इस संप्रदाय के प्रतिपादक थे। ब्रह्मचर्य को छोड़कर केवल 4 व्रतों का पालन करें। प्रथम तीर्थंकर <strong>ऋषभनाथ</strong> थे। <strong>वर्धमान महावीर</strong> 24वें तीर्थंकर थे।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. A group of people contains men, women and children. If 40% of them are men, 35% are women and the rest are children and their average weights are 70 kg, 60 kg and 30 kg, respectively. The average weight of the group is:</p>",
                    question_hi: "<p>22. किसी समूह में पुरुष, महिलाएं और बच्चे शामिल हैं। यदि उनमें से 40% पुरुष हैं, 35% महिलाएं हैं और शेष बच्चे हैं और उनका औसत वजन क्रमशः 70 किग्रा, 60 किग्रा और 30 किग्रा है। उस समूह का औसत वजन कितना है?</p>",
                    options_en: ["<p>45.5 gm</p>", "<p>56.5 gm</p>", 
                                "<p>56.5 kg</p>", "<p>45.5 kg</p>"],
                    options_hi: ["<p>45.5 gm</p>", "<p>56.5 gm</p>",
                                "<p>56.5 kg</p>", "<p>45.5 kg</p>"],
                    solution_en: "<p>22.(c)Let the group contain x people.<br>Number of men = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>x</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math><br><math display=\"inline\"><mi>&#160;</mi><mi>a</mi><mi>n</mi><mi>d</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>w</mi><mi>o</mi><mi>m</mi><mi>e</mi><mi>n</mi><mi>&#160;</mi><mo>=</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>20</mn></mfrac></math><br>Number of children = x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi></mrow><mn>5</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>20</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>4</mn></mfrac></math><br>Now, Men : Women : Children = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>20</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = 8 : 7 : 5<br>Now, Total average of the group = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>70</mn><mo>+</mo><mn>7</mn><mo>&#215;</mo><mn>60</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>30</mn></mrow><mn>20</mn></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1130</mn><mn>20</mn></mfrac></math> = 56.5 kg</p>",
                    solution_hi: "<p>22.(c) माना कि समूह में x लोग हैं।<br>पुरुषों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>x</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math><br>और महिलाओं की संख्या = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>20</mn></mfrac></math><br>बच्चों की संख्या = x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi></mrow><mn>5</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>x</mi></mrow><mn>20</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mn>4</mn></mfrac></math><br>अब, पुरुष : महिला : बच्चे = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>20</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> = 8 : 7 : 5<br>अब, समूह का कुल औसत&nbsp; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>70</mn><mo>+</mo><mn>7</mn><mo>&#215;</mo><mn>60</mn><mo>+</mo><mn>5</mn><mo>&#215;</mo><mn>30</mn></mrow><mn>20</mn></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1130</mn><mn>20</mn></mfrac></math> = 56.5 kg</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. Select the one that is different from the rest..</p>",
                    question_hi: "<p>23. उस विकल्प का चयन करें जो विषम है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>62</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>49</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>62</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>49</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>23.(a) <strong>Logic :</strong> Denominator &times; 4 + 1 = Numerator <br>But in option 1 pattern is not followed.</p>",
                    solution_hi: "<p>23.(a) <strong>तर्क : </strong>हर &times; 4 + 1 = अंश<br>लेकिन विकल्प 1 में पैटर्न का पालन नहीं किया गया है ।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. The S.I unit of resistivity is:</p>",
                    question_hi: "<p>24. प्रतिरोधकता की S. I. इकाई_________ है।</p>",
                    options_en: ["<p>ohm/m</p>", "<p>ohm</p>", 
                                "<p>mho</p>", "<p>ohm m</p>"],
                    options_hi: ["<p>ohm/m</p>", "<p>ohm</p>",
                                "<p>mho</p>", "<p>ohm m</p>"],
                    solution_en: "<p>24.(d) <strong>Resistivity</strong> (<math display=\"inline\"><mi>&#961;</mi></math>) (SI unit - ohm m) - a fundamental property of a material that measures how strongly it resists electric current. <strong>mho (℧) / siemens (S)</strong>- SI unit of electrical conductance . <strong>Ohm (&Omega;)</strong> - SI unit of electrical resistance (R).</p>",
                    solution_hi: "<p>24.(d) <strong>प्रतिरोधकता</strong> (<math display=\"inline\"><mi>&#961;</mi></math>) (SI मात्रक - ओम (&Omega;) मीटर) - किसी धातु का एक मौलिक गुण जो यह मापता है कि यह विद्युत धारा का कितनी दृढ़ता से प्रतिरोध करता है। <strong>mho (℧) / siemens (S) </strong>- विद्युत चालकता की SI मात्रक। <strong>ओम (&Omega;)</strong> - विद्युत प्रतिरोध (R) की SI मात्रक।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. A watch reads 4:30. If the minute hand points east, in what direction will the hour hand point?</p>",
                    question_hi: "<p>25.एक घड़ी 4:30 का समय दर्शाती है। यदि&nbsp;मिनट वाली सुई पूर्व की ओर इंगित करती है, तो घंटे वाली सुई किस दिशा में होगी?</p>",
                    options_en: ["<p>South east</p>", "<p>North west</p>", 
                                "<p>North</p>", "<p>North east</p>"],
                    options_hi: ["<p>दक्षिण पूर्व</p>", "<p>उत्तर पश्चिम</p>",
                                "<p>उत्तर</p>", "<p>उत्तर पूर्व</p>"],
                    solution_en: "<p>25.(d) From the diagram it is clearly seen that the hour hand points in the North east direction.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475785.png\" alt=\"rId18\" width=\"153\" height=\"121\"></p>",
                    solution_hi: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349475992.png\" alt=\"rId19\" width=\"139\" height=\"119\"><br>उपरोक्त आरेख से यह स्पष्ट रूप से देखा जाता है कि घंटे की सुई <strong>उत्तर पूर्व</strong> दिशा में इंगित करती है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. Under which article of the constitution of India is the village Panchayat organised?</p>",
                    question_hi: "<p>26. भारत के संविधान के किस अनुच्छेद के तहत ग्राम पंचायत का आयोजन किया गया है?</p>",
                    options_en: ["<p>Article 38</p>", "<p>Article 39</p>", 
                                "<p>Article 37</p>", "<p>Article 40</p>"],
                    options_hi: ["<p>अनुच्छेद 38</p>", "<p>अनुच्छेद 39</p>",
                                "<p>अनुच्छेद 37</p>", "<p>अनुच्छेद 40</p>"],
                    solution_en: "<p>26.(d) <strong>Article 40. Directive Principles of State Policy</strong> (DPSP) - Articles 36-51 and Part-IV of the Indian Constitution. <strong>Article 37</strong> - It states that the DPSP (Directive Principles of State Policy ) &ldquo;shall not be enforceable by any court, but the principles therein laid down are nevertheless fundamental in the governance of the country and it shall be the duty of the state to apply these principles in making laws&rdquo;. <strong>Article 39</strong> - It deals with the provisions or principles of policy that shall be undertaken by the state. <strong>Article 38</strong> - State to secure a social order for the promotion of the welfare of the people.</p>",
                    solution_hi: "<p>26.(d) <strong>अनुच्छेद 40। राज्य के नीति निदेशक सिद्धांत</strong> (DPSP) - भारतीय संविधान के भाग-IV और अनुच्छेद 36-51 में । <strong>अनुच्छेद 37</strong> - इसमें कहा गया है कि DPSP (राज्य नीति के निदेशक सिद्धांत) \"कानून बनाने में ये सिद्धांत &rdquo; किसी भी न्यायालय द्वारा लागू नहीं किए जाएंगे, लेकिन इसमें निर्धारित सिद्धांत देश के शासन में मौलिक हैं और इसे लागू करना राज्य का कर्तव्य होगा।<strong>अनुच्छेद 39</strong> - यह राज्य द्वारा अपनाए जाने वाले नीति के प्रावधानों या सिद्धांतों से संबंधित है। <strong>अनुच्छेद 38</strong> - राज्य लोगों के कल्याण को बढ़ावा देने के लिए एक सामाजिक व्यवस्था की सुरक्षा करेगा ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. Select the correct option from the given options that can make a full square.&nbsp;(3 of the 5 images given below are given)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349476418.png\" alt=\"rId20\" width=\"261\" height=\"81\"></p>",
                    question_hi: "<p>27. दिए गए विकल्पों में से वह सही विकल्प चुनिए जो एक पूर्ण वर्ग बना सके। (नीचे दी गई 5 छवियों में से 3 दी गई हैं)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349476418.png\" alt=\"rId20\" width=\"271\" height=\"84\"></p>",
                    options_en: ["<p>1,2,3</p>", "<p>1,4,2</p>", 
                                "<p>1,4,5</p>", "<p>2,3,4</p>"],
                    options_hi: ["<p>1,2,3</p>", "<p>1,4,2</p>",
                                "<p>1,4,5</p>", "<p>2,3,4</p>"],
                    solution_en: "<p>27.(d)<br><img src=\"data:image/png;base64,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\" width=\"112\" height=\"109\"></p>",
                    solution_hi: "<p>27.(d)<br><img src=\"data:image/png;base64,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\" width=\"112\" height=\"109\"></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. The simple interest on a certain sum for 4<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> years at the rate of 9.5% p.a. is ₹3,553. What will be the amount payable on the same sum at 8.4% p.a. simple interest in 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> years?</p>",
                    question_hi: "<p>28. एक निश्चित धनराशि पर 9.5% बार्षिक दर से 4<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> वर्ष में प्राप्त साधारण ब्याज 3,553 है। उसी धनराशि पर 8.4% वार्षिक साधारण ब्याज की दर से 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> वर्ष में देय मिश्रधन ज्ञात कीजिए।</p>",
                    options_en: ["<p>13,950</p>", "<p>13,855</p>", 
                                "<p>14,855</p>", "<p>13,850</p>"],
                    options_hi: ["<p>13,950</p>", "<p>13,855</p>",
                                "<p>14,855</p>", "<p>13,850</p>"],
                    solution_en: "<p>28.(b) S.I = ₹3553, Rate = 9.5%, Time = 4.4 years<br>Then, P = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mo>.</mo><mi>I</mi><mo>.</mo><mo>&#215;</mo><mn>100</mn></mrow><mrow><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3553</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>9</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>4</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = 8,500<br>Now, New S.I = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8500</mn><mo>&#215;</mo><mn>7</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>8</mn><mo>.</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math> = 5355<br>Amount = P + S.I = 8500 + 5355 = ₹13,855</p>",
                    solution_hi: "<p>28.(b) साधारण ब्याज = ₹3553, दर <br>= 9.5%, समय = 4.4 वर्ष<br>फिर, मूलधन (P) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>S</mi><mo>.</mo><mi>I</mi><mo>.</mo><mo>&#215;</mo><mn>100</mn></mrow><mrow><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3553</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>9</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>4</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = 8,500<br>अब, नया साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8500</mn><mo>&#215;</mo><mn>7</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>8</mn><mo>.</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math> = 5355<br>मिश्रधन = P + S.I = 8500 + 5355 = ₹13,855</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. In a certain code language, STUDFARM is written as FGHQSNEZ. How will NUCLEAR be written in that language?</p>",
                    question_hi: "<p>29. एक निश्चित कूट भाषा में STUDFARM को FGHQSNEZ लिखा जाता है। उस भाषा में NUCLEAR को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>FLQYVNE</p>", "<p>GSQUCNE</p>", 
                                "<p>AHPYRNE</p>", "<p>DKQTCNE</p>"],
                    options_hi: ["<p>FLQYVNE</p>", "<p>GSQUCNE</p>",
                                "<p>AHPYRNE</p>", "<p>DKQTCNE</p>"],
                    solution_en: "<p>29.(c) In a certain code language, STUDFARM is written as FGHQSNEZ. <br>Here the difference between the place values of the corresponding letters = 13;<br>So, NUCLEAR will be written in that language as :<br>14 - 13 = 1 = A<br>21 - 13 = 8 = H<br>3 - (-13) = 16 = P<br>12 - (-13) = 25 = Y<br>5 - (-13) = 18 = R<br>1 - (-13) = 14 = N<br>18 - 13 = 5 = E<br>i.e. NUCLEAR : AHPYRNE</p>",
                    solution_hi: "<p>29.(c) एक निश्चित कूट भाषा में STUDFARM को FGHQSNEZ लिखा जाता है।<br>यहाँ संगत अक्षरों के स्थानीय मानों के बीच का अंतर = 13;<br>तो, NUCLEAR को उस भाषा में इस प्रकार लिखा जाएगा:<br>14 - 13 = 1 = A<br>21 - 13 = 8 = H<br>3 - ( - 13) = 16 = P<br>12 - ( - 13) = 25 = Y<br>5 - ( - 13) = 18 = R<br>1 - ( - 13) = 14 = N<br>18 - 13 = 5 = E<br>अर्थात NUCLEAR : AHPYRNE</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. Which is the incorrect statement with regard to ultrasound waves?</p>",
                    question_hi: "<p>30. अल्ट्रासाउंड तरंगों के संबंध में कौन सा कथन गलत है?</p>",
                    options_en: ["<p>They are high frequency waves</p>", "<p>They are used in industries and for medical purposes</p>", 
                                "<p>They travel among well-defined paths even in the presence of obstacles</p>", "<p>Ultrasound cannot be reflected back from defective locations</p>"],
                    options_hi: ["<p>ये उच्च आवृत्ति तरंगें हैं</p>", "<p>इनका उपयोग उद्योगों और चिकित्सा उद्देश्यों के लिए किया जाता है</p>",
                                "<p>वे बाधाओं की उपस्थिति में भी अच्छी तरह से परिभाषित पथों के बीच यात्रा करते है</p>", "<p>अल्ट्रासाउंड को दोषपूर्ण स्थानों से वापस प्रतिबिंबित नहीं किया जा सकता है</p>"],
                    solution_en: "<p>30.(d) There are three types of sound waves: <strong>Infrasonic sounds</strong> - waves below the frequency of 20Hz. <strong>Sonic Waves</strong> - Human beings can only hear sounds whose frequency is ranging from 20Hz to 20kH. <strong>Ultrasonic sounds</strong> - above the frequency of 20kHz. It can be reflected back from defective locations. Example - Bats are good at flying at night because they use ultrasonic sound.</p>",
                    solution_hi: "<p>30.(d) ध्वनि तरंगें तीन प्रकार की होती हैं: <strong>अवश्रव्य ध्वनियाँ</strong> - 20Hz की आवृत्ति से नीचे की तरंगें हैं। <strong>ध्वनि तरंगें</strong> - मनुष्य केवल वही ध्वनियाँ सुन सकता है जिनकी आवृत्ति 20Hz से 20kH तक होती है।<strong> पराश्रव्य ध्वनियाँ</strong> - 20kHz की आवृत्ति से ऊपर। इसे दोषपूर्ण स्थानों से वापस परिलक्षित किया जा सकता है। उदाहरण - चमगादड़ रात में अच्छे से उड़ पाते है क्योंकि वे पराश्रव्य ध्वनि का उपयोग करते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. What is the approximate percentage of female literacy in India according to the 2011 Census?</p>",
                    question_hi: "<p>31. 2011 की जनगणना के अनुसार भारत में महिला साक्षरता लगभग कितना प्रतिशत है?</p>",
                    options_en: ["<p>65.46%</p>", "<p>46.89%</p>", 
                                "<p>59.23%</p>", "<p>29.32%</p>"],
                    options_hi: ["<p>65.46%</p>", "<p>46.89%</p>",
                                "<p>59.23%</p>", "<p>29.32%</p>"],
                    solution_en: "<p>31.(a) <strong>65.46%. According to the 2011 Census</strong>: The literacy rate in the country is 74.04 per cent and 82.14 for males. Bihar has the lowest literacy rate in India at 61.8% while Kerala has the highest at 94%. <strong>Density of population</strong>: 382 per km<sup>2</sup>. <strong>Sex ratio</strong>: 943, <strong>Child sex ratio (0&ndash;6 age group):</strong> 914.</p>",
                    solution_hi: "<p>31.(a) <strong>65.46%। 2011 की जनगणना के अनुसार: </strong>देश में साक्षरता दर 74.04 % और पुरुषों की साक्षरता दर 82.14 % है। भारत में बिहार की साक्षरता दर सबसे कम 61.8% है जबकि केरल की साक्षरता दर सबसे अधिक 94% है। जनसंख्या घनत्व: 382 प्रति वर्ग किमी । लिंगानुपात: 943, बाल लिंगानुपात (0-6 आयु वर्ग): 914।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. If 5A72B is divisible by 11, then what is the value of B - A?</p>",
                    question_hi: "<p>32. यदि 5A72B, 11 से भाज्य है, तो B - A का मान क्या है?</p>",
                    options_en: ["<p>3</p>", "<p>2</p>", 
                                "<p>1</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>2</p>",
                                "<p>1</p>", "<p>4</p>"],
                    solution_en: "<p>32.(c)<br>5A72B is divisible by 11 <br>So, 5 + 7 + B - (A + 2) = 0 or 11<br>B -&nbsp;A + 10 = 0 or 11<br>B -&nbsp;A = 11 - 10 = 1</p>",
                    solution_hi: "<p>32.(c)<br>5A72B , 11 से विभाज्य है<br>इसलिए , 5 + 7 + B - (A + 2) = 0 या 11<br>B -&nbsp;A&nbsp; + 10 = 0 या 11<br>B - &nbsp;A = 11 - 10 = 1</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. Select the option that represents the letters that, when placed from left to right in the blanks below, will complete the letter series.<br>P _ TP_ S _CT_ RSPC _ PR _ P _ T _ RS</p>",
                    question_hi: "<p>33. उस विकल्प का चयन करें जो अक्षरों का प्रतिनिधित्व करता है, जो नीचे दिए गए रिक्त स्थान में बाएं से दाएं रखे जाने पर अक्षर श्रृंखला को पूरा करेगा।<br>P_ TP_ S _CT_ RSPC_PR_P_T_RS</p>",
                    options_en: ["<p>CSPCRPTP</p>", "<p>CRPCS PPT</p>", 
                                "<p>CCPPPRST</p>", "<p>CRPPTSCP</p>"],
                    options_hi: ["<p>CSPCRPTP</p>", "<p>CRPCS PPT</p>",
                                "<p>CCPPPRST</p>", "<p>CRPPTSCP</p>"],
                    solution_en: "<p>33.(d)<br>P <span style=\"text-decoration: underline;\"><strong>C</strong></span> T P <span style=\"text-decoration: underline;\"><strong>R</strong></span> S / <span style=\"text-decoration: underline;\"><strong>P</strong></span> C T <span style=\"text-decoration: underline;\"><strong>P</strong></span> R S / P C <span style=\"text-decoration: underline;\"><strong>T</strong></span> P R <span style=\"text-decoration: underline;\"><strong>S</strong></span> / P <span style=\"text-decoration: underline;\"><strong>C</strong></span> T <span style=\"text-decoration: underline;\"><strong>P</strong></span> R S</p>",
                    solution_hi: "<p>33.(d)<br>P <span style=\"text-decoration: underline;\"><strong>C</strong></span> T P <span style=\"text-decoration: underline;\"><strong>R</strong></span> S / <span style=\"text-decoration: underline;\"><strong>P</strong></span> C T <span style=\"text-decoration: underline;\"><strong>P</strong></span> R S / P C <span style=\"text-decoration: underline;\"><strong>T</strong></span> P R <span style=\"text-decoration: underline;\"><strong>S</strong></span> / P <span style=\"text-decoration: underline;\"><strong>C</strong></span> T <span style=\"text-decoration: underline;\"><strong>P</strong></span> R S&nbsp;</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. An object is placed in front of a convex mirror at a point between infinity and the pole of&nbsp;the mirror. The image formed is:</p>",
                    question_hi: "<p>34. किसी वस्तु को एक उत्तल दर्पण के सामने अनंत और दर्पण के ध्रुव के बीच स्थित किसी बिंदु पर रखा गया है।निर्मित प्रतिबिंब ______ होगा।</p>",
                    options_en: ["<p>virtual and inverted</p>", "<p>real and inverted</p>", 
                                "<p>virtual and erect</p>", "<p>real and erect</p>"],
                    options_hi: ["<p>आभासी और उल्टा</p>", "<p>वास्तविक और उल्टा</p>",
                                "<p>आभासी और सीधा</p>", "<p>वास्तविक और सीधा</p>"],
                    solution_en: "<p>34.(c) <strong>Virtual and erect</strong>. Convex mirror (diverging mirror) diverges the rays of light, which fall on its reflecting surface. It is a curved mirror where the reflective surface bulges out toward the light source. The image formed in a convex mirror is always virtual and erect, whatever be the position of the object.</p>",
                    solution_hi: "<p>34.(c) <strong>आभासी और सीधा। </strong>उत्तल दर्पण (अपसारी दर्पण) प्रकाश की किरणों को अपसारित करता है, जो इसकी परावर्तक सतह पर पड़ती हैं। यह एक घुमावदार दर्पण है जहां परावर्तक सतह प्रकाश स्रोत की ओर उभरी हुई होती है। उत्तल दर्पण में बनने वाला प्रतिबिम्ब हमेशा आभासी और सीधा होता है, चाहे वस्तु की स्थिति कुछ भी हो।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. From the given four answer figures choose the correct water image of the problem figure. Problem Figure<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349477032.png\" alt=\"rId23\" width=\"213\" height=\"133\"></p>",
                    question_hi: "<p>35. दिए गए चार विकल्पों (Answer figures) में से नीचे दी गई प्रश्न आकृति (Problem figure) का जल में बनने वाला सही प्रतिबिंब चुनें।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349477192.png\" alt=\"rId24\" width=\"229\" height=\"147\"></p>",
                    options_en: ["<p>B</p>", "<p>D</p>", 
                                "<p>C</p>", "<p>A</p>"],
                    options_hi: ["<p>B</p>", "<p>D</p>",
                                "<p>C</p>", "<p>A</p>"],
                    solution_en: "<p>35.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349477366.png\" alt=\"rId25\" width=\"109\" height=\"108\"></p>",
                    solution_hi: "<p>35.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349477366.png\" alt=\"rId25\" width=\"109\" height=\"108\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. The Central Bank of Zimbabwe has been lowering the rate of interests of the economy to boost growth. The bank is being__________ in its monetary policy stance.</p>",
                    question_hi: "<p>36. सेंट्रल बैंक ऑफ जिम्बाब्वे विकास को बढ़ावा देने के लिए अर्थव्यवस्था में ब्याज दरों को कम कर रहा है। बैंक अपनी मौद्रिक नीति के रुख में _________ हो रहा है।</p>",
                    options_en: ["<p>Neutral</p>", "<p>Hawkish</p>", 
                                "<p>Dovish</p>", "<p>Accommodative</p>"],
                    options_hi: ["<p>उदासीन (Neutral)</p>", "<p>आक्रामक (hawkish)</p>",
                                "<p>शांतिवादी (dovish)</p>", "<p>समायोजक (Accommodative)</p>"],
                    solution_en: "<p>36.(c) <strong>Dovish Monetary Policy</strong>: It states the idea of low-interest rates which encourages economic growth and employment. <strong>Hawkish Monetary Policy: </strong>It seeks to reduce inflation by raising interest rates. <strong>Accommodative Monetary Policy: </strong>Central bank decreases the interest rates to inject money into the financial system. <strong>Neutral Monetary Policy:</strong> It states that interest rates are neither increased nor decreased.</p>",
                    solution_hi: "<p>36.(c) <strong>शांतिवादी मौद्रिक नीति -</strong> यह कम ब्याज दरों के विचार को बताता है जो आर्थिक विकास और रोजगार को प्रोत्साहित करता है। <strong>आक्रामक मौद्रिक नीति -</strong> यह ब्याज दरों को बढ़ाकर मुद्रास्फीति को कम करना चाहता है। <strong>समायोजक मौद्रिक नीति - </strong>केंद्रीय बैंक वित्तीय प्रणाली में पैसा डालने के लिए ब्याज दरों में कमी करता है। <strong>उदासीन मौद्रिक नीति -</strong> इसमें कहा गया है कि ब्याज दरें न तो बढ़ाई जाती हैं और न ही घटाई जाती हैं।।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. If the sum of three consecutive composite numbers is 36. then what is the product of the three numbers?</p>",
                    question_hi: "<p>37. यदि तीन क्रमबद्ध भाज्य संख्याओं का योग 36 है, तो तीनों संख्याओं का गुणनफल क्या है?</p>",
                    options_en: ["<p>1460</p>", "<p>1750</p>", 
                                "<p>1680</p>", "<p>1820</p>"],
                    options_hi: ["<p>1460</p>", "<p>1750</p>",
                                "<p>1680</p>", "<p>1820</p>"],
                    solution_en: "<p>37.(c)<br>The average of three consecutive composite numbers = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>= 12<br>so , the composite numbers = 10, 12,14<br>So , <br>product of the numbers = 10 &times; 12 &times; 14 = 1680</p>",
                    solution_hi: "<p>37.(c)<br>लगातार तीन संयुक्त संख्याओं का औसत = <math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>= 12<br>इसलिए, संयुक्त संख्या = 10, 12,14<br>इसलिए,<br>संख्याओं का गुणनफल = 10 &times; 12 &times; 14 = 1680</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. 3Y7, 8X9, 15W11, 24V13, 35U15, ?</p>",
                    question_hi: "<p>38. 3Y7, 8X9, 15W11, 24V13, 35U15, ?</p>",
                    options_en: ["<p>48T17</p>", "<p>44S16</p>", 
                                "<p>49T19</p>", "<p>47S14</p>"],
                    options_hi: ["<p>48T17</p>", "<p>44S16</p>",
                                "<p>49T19</p>", "<p>47S14</p>"],
                    solution_en: "<p>38.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349477550.png\" alt=\"rId26\" width=\"295\" height=\"73\"></p>",
                    solution_hi: "<p>38.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349477550.png\" alt=\"rId26\" width=\"295\" height=\"73\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39.What does the kinetic energy of an object increase with?</p>",
                    question_hi: "<p>39. किसके अनुरूप वस्तु की गतिज ऊर्जा की वृद्धि होती है ?</p>",
                    options_en: ["<p>Speed</p>", "<p>Acceleration</p>", 
                                "<p>Friction</p>", "<p>time</p>"],
                    options_hi: ["<p>गति</p>", "<p>द्रव्यमान</p>",
                                "<p>घर्षण</p>", "<p>समय</p>"],
                    solution_en: "<p>39.(a) Speed. The kinetic energy of an object is the energy it has because of its motion. Kinetic Energy = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> mv<sup>2</sup> (where m - Mass of the object, v - Velocity of the object). The kinetic energy of an object is directly proportional to the square of its speed. Apart from speed it also depends upon the mass of the object. SI unit for kinetic energy is Joules (kg m<sup>2</sup> s<sup>-2</sup>).</p>",
                    solution_hi: "<p>39.(a) गति। किसी वस्तु की गतिज ऊर्जा वह ऊर्जा होती है जो उसकी गति के कारण होती है। गतिज ऊर्जा = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> mv<sup>2 </sup>(जहाँ m - वस्तु का द्रव्यमान, v - वस्तु का वेग)। किसी वस्तु की गतिज ऊर्जा उसकी गति के वर्ग के समानुपाती होती है। गति के अतिरिक्त यह वस्तु के द्रव्यमान पर भी निर्भर करता है। गतिज ऊर्जा के लिए SI इकाई जूल (kg m<sup>2</sup> s<sup>-2</sup>) है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. If it was Tuesday on 21 February 2017, then what will be the day of the week on 5 February 2025? (2020 and 2024 are leap years.)</p>",
                    question_hi: "<p>40. यदि 21 फरवरी 2017 को मंगलवार था, तो 5 फरवरी 2025 को सप्ताह का कौन सा दिन होगा? (2020 और 2024 लीप वर्ष हैं।)</p>",
                    options_en: ["<p>Thursday</p>", "<p>Tuesday</p>", 
                                "<p>Monday</p>", "<p>Wednesday</p>"],
                    options_hi: ["<p>गुरुवार</p>", "<p>मंगलवार</p>",
                                "<p>सोमवार</p>", "<p>बुधवार</p>"],
                    solution_en: "<p>40.(d) We know that the same day comes after/before every 7 days<br>To reach 5 feb 2017 we have to go back 16 days.<br>21st feb - 14 days = 7th feb = Tuesday<br>7th feb - 2 days = 5th feb = Sunday<br>Number of odd days between 5 feb 2017 to 5 feb 2025</p>\n<p>=&nbsp;1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 = 10<br>10 &divide; 7 = Remainder = 3<br>Sunday + 3 days = Wednesday</p>",
                    solution_hi: "<p>40.(d) हम जानते हैं कि वही दिन हर 7 दिन के बाद/पहले आता है<br>5 फरवरी 2017 तक पहुंचने के लिए हमें 16 दिन पीछे जाना होगा.<br>21 फरवरी - 14 दिन = 7 फरवरी = मंगलवार<br>7 फरवरी - 2 दिन = 5 फरवरी = रविवार<br>5 फरवरी 2017 से 5 फरवरी 2025 के बीच विषम दिनों की संख्या</p>\n<p>=&nbsp;1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 = 10<br>10 &divide; 7 = शेष = 3<br>रविवार + 3 दिन = बुधवार</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. The LCM of two numbers is 84. If the numbers are in the ratio 2 : 3, then find the sum of the numbers.</p>",
                    question_hi: "<p>41. दो संख्याओं का लघुत्तम समापवर्त्य (LCM) 84 है। यदि संख्याओं का अनुपात 2 : 3 है, तो संख्याओं का योग ज्ञात कीजिए।</p>",
                    options_en: ["<p>40</p>", "<p>70</p>", 
                                "<p>25</p>", "<p>60</p>"],
                    options_hi: ["<p>40</p>", "<p>70</p>",
                                "<p>25</p>", "<p>60</p>"],
                    solution_en: "<p>41.(b) Let the numbers are 2x and 3x<br>H.C.F = x and L.C.M = 84<br>L.C.M &times; H.C.F = Product of two numbers<br>&rArr; 84x = 6x<sup>2</sup></p>\n<p>&rArr; x = 14<br>Sum of the numbers = 2x + 3x <br>= 28 + 42</p>\n<p>= 70</p>",
                    solution_hi: "<p>41.(b) माना संख्याएँ 2x और 3x हैं<br>H.C.F = x और L.C.M = 84<br>L.C.M &times; H.C.F = दो संख्याओं का गुणनफल</p>\n<p>&rArr; 84x = 6x<sup>2</sup></p>\n<p>&rArr; x = 14</p>\n<p>संख्याओं का योग = 2x + 3x</p>\n<p>= 28 + 42</p>\n<p>= 70</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. Washing soda is ____________ in nature.</p>",
                    question_hi: "<p>42. धावन सोडा प्रकृति में _________ होता है।</p>",
                    options_en: ["<p>sour</p>", "<p>acidic</p>", 
                                "<p>basic</p>", "<p>edible</p>"],
                    options_hi: ["<p>खट्टा</p>", "<p>अम्लीय</p>",
                                "<p>क्षारीय</p>", "<p>खाद्य</p>"],
                    solution_en: "<p>42.(c) <strong>Basic. Washing Soda</strong> (Sodium carbonate decahydrate - Na<sub>2</sub>CO<sub>3</sub>.10H<sub>2</sub>O) is an alkaline compound with a pH value of 11. The preparation of washing soda involves Solvay process. <strong>Some other Compounds</strong> - Baking Soda (Sodium bicarbonate - NaHCO<sub>3</sub>), Caustic Soda (Sodium hydroxide - NaOH), Salt (Sodium Chloride - NaCl).</p>",
                    solution_hi: "<p>42.(c) <strong>क्षारीय । वाशिंग सोडा</strong> (सोडियम कार्बोनेट डिकाहाइड्रेट - Na<sub>2</sub>CO<sub>3</sub>.10H<sub>2</sub>O) 11 के पीएच मान के साथ एक क्षारीय यौगिक है। वाशिंग सोडा की तैयारी में सॉल्वे प्रक्रिया शामिल है। <strong>कुछ अन्य यौगिक -</strong> बेकिंग सोडा (सोडियम बाइकार्बोनेट - NaHCO<sub>3</sub>), कास्टिक सोडा (सोडियम हाइड्रोक्साइड - NaOH), नमक (सोडियम क्लोराइड - NaCl)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. Which of the following gases is not generated in a biogas plant?</p>",
                    question_hi: "<p>43. निम्नलिखित गैसों में से कौन सी गैस बायोगैस संयंत्र में उत्पन्न नहीं होती?</p>",
                    options_en: ["<p>CO</p>", "<p>CO<sub>2</sub></p>", 
                                "<p>CH<sub>4</sub></p>", "<p>H<sub>2</sub>S</p>"],
                    options_hi: ["<p>CO</p>", "<p>CO<sub>2</sub></p>",
                                "<p>CH<sub>4</sub></p>", "<p>H<sub>2</sub>S</p>"],
                    solution_en: "<p>43.(a) <strong>CO </strong>(Carbon Monoxide). It is highly toxic in nature. It is produced by incomplete combustion of fuels. It combines with hemoglobin present in blood and forms carboxyhemoglobin complex. <strong>Biogas </strong>- The organic matter breakdown to produce the mixture of different gases. It is a renewable form of energy and can be produced from raw materials like agriculture waste, manure, municipal waste, plant waste etc. It produces Methane (CH<sub>4</sub>), Carbon dioxide (CO<sub>2</sub>), Hydrogen sulfide (H<sub>2</sub>S) and moisture (H<sub>2</sub>O).</p>",
                    solution_hi: "<p>43.(a) <strong>CO</strong> (कार्बन मोनोऑक्साइड)। यह प्रकृति में अत्यधिक विषैली होती है। यह ईंधन के अपूर्ण दहन से उत्पन्न होता है। यह रक्त में मौजूद हीमोग्लोबिन के साथ मिलकर कार्बोक्सीहीमोग्लोबिन कॉम्प्लेक्स बनाता है। <strong>बायोगैस</strong> - विभिन्न गैसों के मिश्रण का उत्पादन करने के लिए जैविक पदार्थ का विघटन होता है। यह ऊर्जा का एक नवीकरणीय रूप है और कच्चे माल जैसे कृषि अपशिष्ट, खाद, नगरपालिका अपशिष्ट, पौधों के अपशिष्ट आदि से उत्पादित किया जा सकता है। यह मीथेन (CH<sub>4</sub>), कार्बन डाइऑक्साइड (CO<sub>2</sub>), हाइड्रोजन सल्फाइड (H<sub>2</sub>S) और नमी (H<sub>2</sub>O) का उत्पन्न करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. Express 0.4<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>24</mn><mo>&#175;</mo></mover></math>&nbsp;in the form <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>q</mi></mfrac></math> , where p and q are integers and q &ne; 0.</p>",
                    question_hi: "<p>44. 0.4 <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>24</mn><mo>&#175;</mo></mover></math>&nbsp;को <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>q</mi></mfrac></math> के रूप में व्यक्त करें, जहाँ p और q पूर्णांक हैं और q &ne; 0 है।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>165</mn></mrow></mfrac><mi>&#160;</mi><mi>&#160;</mi></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>14</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>165</mn></mrow></mfrac><mi>&#160;</mi><mi>&#160;</mi></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>33</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>41</mn></mrow><mrow><mn>990</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>44.(a) 0.4<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>24</mn><mo>&#175;</mo></mover></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>424</mn><mo>-</mo><mn>4</mn></mrow><mn>990</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>420</mn><mn>990</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>33</mn></mfrac></math></p>",
                    solution_hi: "<p>44.(a) 0.4<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mover><mn>24</mn><mo>&#175;</mo></mover></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>424</mn><mo>-</mo><mn>4</mn></mrow><mn>990</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>420</mn><mn>990</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>14</mn><mn>33</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. Open Network for Digital Commerce (ONDC) is an initiative by ______.</p>",
                    question_hi: "<p>45. ओपन नेटवर्क फॉर डिजिटल कॉमर्स (Open Network for Digital Commerce - ONDC) की ______ एक पहल है।</p>",
                    options_en: ["<p>Ministry of Statistics and Program Implementation</p>", "<p>Ministry of Information and Broadcasting</p>", 
                                "<p>Department for Promotion of Industry and Internal Trade</p>", "<p>NITI Aayog</p>"],
                    options_hi: ["<p>सांख्यिकी और कार्यक्रम कार्यान्वयन मंत्रालय</p>", "<p>सूचना एवं प्रसारण मंत्रालय</p>",
                                "<p>उद्योग संवर्धन और आंतरिक व्यापार विभाग</p>", "<p>नीति आयोग</p>"],
                    solution_en: "<p>45.(c) Department for Promotion of Industry and Internal Trade (DPIIT) - A central government department under the Ministry of Commerce and Industry in India. Open Network for Digital Commerce (ONDC) : It is a tech-based initiative to transform the way e-commerce functions in the country by enabling e-commerce through an open protocol based on open-source specifications. Founded - 31 December 2021.</p>",
                    solution_hi: "<p>45.(c) उद्योग संवर्धन और आंतरिक व्यापार विभाग (DPIIT) - भारत में वाणिज्य और उद्योग मंत्रालय के तहत एक केंद्रीय सरकारी विभाग। ओपन नेटवर्क फॉर डिजिटल कॉमर्स (ONDC): यह ओपन-सोर्स विनिर्देशों के आधार पर एक ओपन प्रोटोकॉल के माध्यम से ई-कॉमर्स को सक्षम करके देश में ई-कॉमर्स के काम करने के तरीके को बदलने के लिए एक तकनीक-आधारित पहल है। स्थापना - 31 दिसंबर 2021 ।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. Read the given statement and conclusions carefully. Assuming that the information given in the statements is true, decide which of the given conclusions logically follow(s) from the <br>statement.<br><strong>Statement:</strong><br>Team A students lost 14 out of 20 games they played last season.<br><strong>Conclusions:</strong><br>I. Team A students are more interested in academics than sports.<br>II.Team A students are unlucky.</p>",
                    question_hi: "<p>46. दिए गए कथन और निष्कर्षों को ध्यानपूर्वक पढ़िए। कथनों में दी गई जानकारी को सत्य मानते हुए विचार करें, और बताएं कि कौन से निष्कर्ष तार्किक रूप से कथन का पालन करते हैं?<br><strong>कथन:</strong><br>टीम A के विद्यार्थियों ने पिछले सीजन में खेले गए 20 गेम में से 14 गेम हारे हैं।<br><strong>निष्कर्ष:</strong><br>I. टीम A के विद्यार्थी खेल की तुलना में शिक्षा में&nbsp;अधिक रुचि रखते हैं। <br>II. टीम A के विद्यार्थी दुर्भाग्यशाली हैं।</p>",
                    options_en: ["<p>Both conclusions I and II follow</p>", "<p>Neither conclusion I nor II follows</p>", 
                                "<p>Only conclusion II follows</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>", "<p>न तो निष्कर्ष । और न ही II अनुसरण करता है।</p>",
                                "<p>केवल निष्कर्ष || अनुसरण करता है।</p>", "<p>केवल निष्कर्ष । अनुसरण करता है।</p>"],
                    solution_en: "<p>46.(b)&nbsp;Neither conclusion I nor II follows.</p>",
                    solution_hi: "<p>46 (b) न तो निष्कर्ष I न ही II अनुसरण करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. The pH of milk of magnesia is about______.</p>",
                    question_hi: "<p>47. मिल्क ऑफ मैग्नीशिया का pH मान लगभग_______ होता है।</p>",
                    options_en: ["<p>4.7</p>", "<p>13.2</p>", 
                                "<p>10.4</p>", "<p>2.9</p>"],
                    options_hi: ["<p>4.7</p>", "<p>13.2</p>",
                                "<p>10.4</p>", "<p>2.9</p>"],
                    solution_en: "<p>47.(c) <strong>10.4. Milk of Magnesia</strong> {Magnesium hydroxide(Mg(OH)<sub>2</sub>)} - It is a white solid and one of the key components of antacids. It is an alkaline solution of Water and Magnesium hydroxide. <strong>pH value of some common substances - </strong>Baking Soda - 8.3, Ammonia - 11, Vinegar - 2.8, Milk - 6.8, Pure water - 7.0, Human blood - 7.4, Rainwater - 6.5.</p>",
                    solution_hi: "<p>47.(c) <strong>10.4 । </strong>मिल्क ऑफ मैग्नेशिया {मैग्नीशियम हाइड्रॉक्साइड (Mg(OH)<sub>2</sub>)} - यह एक सफेद ठोस और अमलरोधी के प्रमुख घटकों में से एक है। यह जल और मैग्नीशियम हाइड्रॉक्साइड का एक क्षारीय घोल है। <strong>कुछ सामान्य पदार्थों का pH मान:</strong> बेकिंग सोडा- 8.3, अमोनिया- 11, सिरका- 2.8, दूध- 6.8, शुद्ध जल- 7.0, मानव रक्त- 7.4, वर्षा जल- 6.5.</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. If tan&theta; = 4, then the value of <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>cos&#952;</mi><mo>+</mo><mn>2</mn><mi>sin&#952;</mi></mrow><mrow><mn>2</mn><mi>sin&#952;</mi><mo>-</mo><mi>cos&#952;</mi></mrow></mfrac></math> is :</p>",
                    question_hi: "<p>48. यदि tan&theta; = 4 है तो <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>cos&#952;</mi><mo>+</mo><mn>2</mn><mi>sin&#952;</mi></mrow><mrow><mn>2</mn><mi>sin&#952;</mi><mo>-</mo><mi>cos&#952;</mi></mrow></mfrac></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>48.(b) <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>cos&#952;</mi><mo>+</mo><mn>2</mn><mi>sin&#952;</mi></mrow><mrow><mn>2</mn><mi>sin&#952;</mi><mo>-</mo><mi>cos&#952;</mi></mrow></mfrac></math><br>Dividing numerator and denominator by cos&theta;, and put tan&theta;=4<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>4</mn></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>4</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>8</mn></mrow><mrow><mn>8</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>7</mn></mfrac></math></p>",
                    solution_hi: "<p>48.(b) <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mi>cos&#952;</mi><mo>+</mo><mn>2</mn><mi>sin&#952;</mi></mrow><mrow><mn>2</mn><mi>sin&#952;</mi><mo>-</mo><mi>cos&#952;</mi></mrow></mfrac></math><br>अंश और हर को cos&theta; से विभाजित करके,tan&theta; = 4 रखिये</p>\n<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mn>2</mn><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>2</mn><mo>&#215;</mo><mn>4</mn></mrow><mrow><mn>2</mn><mo>&#215;</mo><mn>4</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>8</mn></mrow><mrow><mn>8</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>7</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. There is an aqueous solution X, whose pH is 2.6. It represents that the solution is:</p>",
                    question_hi: "<p>49. X एक जलीय विलयन है, जिसका pH 2.6 है। यह दर्शाता है कि विलयन ______ का है।</p>",
                    options_en: ["<p>strongly basic in nature</p>", "<p>neutral in nature</p>", 
                                "<p>strongly acidic in nature</p>", "<p>weakly basic in nature</p>"],
                    options_hi: ["<p>प्रबल क्षारीय प्रकृति</p>", "<p>उदासीन प्रकृति</p>",
                                "<p>प्रबल अम्लीय प्रकृति</p>", "<p>दुर्बल क्षारीय प्रकृति</p>"],
                    solution_en: "<p>49.(c) <strong>strongly acidic in nature</strong>. pH stands for <strong>\'potential of Hydrogen</strong>\' which measures the acidity or alkalinity of aqueous solution developed in 1909 by Sorenson. The pH of a solution varies from 0 to 14. Solutions having a value of pH ranging from 0 to 7 are termed as acidic and the value of pH ranging from 7 to 14 are known as basic solutions. pH equal to 7 are known as neutral solutions. pH of some solutions Conic HCl (0), Saliva before meal (7.4), Toothpaste (8.0), Gastric Juice (1.4), Blood (7.4), Lemon Juice (2.5), Vinegar (4.0).</p>",
                    solution_hi: "<p>49.(c) <strong>प्रबल अम्लीय प्रकृति</strong> । pH का अर्थ \'पोटेंशियल आफ हाइड्रोजन\' है जिसे सोरेनसन द्वारा 1909 में विकसित किया गया था जो जलीय विलयन की <strong>अम्लता</strong> या <strong>क्षारीयता</strong> को मापता है। विलयन का pH , 0 से 14 तक बदलता रहता है। 0 से 7 तक के pH मान वाले विलयनों को अम्लीय कहा जाता है और 7 से 14 तक के pH के मान को क्षारीय विलयन के रूप में जाना जाता है। 7 के बराबर pH वाले विलयन को उदासीन विलयन कहलाते हैं। कुछ विलयनों का pH- सांद्र HCl (0), भोजन से पहले लार का pH (7.4), टूथपेस्ट (8.0), गैस्ट्रिक रस (1.4), रक्त (7.4), नींबू का रस (2.5), सिरका (4.0)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. Consider the given statement and decide which of the given assumptions is (are) implicit in the statement.<br><strong>Statement:</strong><br>During an exam, an invigilator said, &ldquo;if anyone tries to copy, I will cancel their exam&rdquo;.<br><strong>Assumptions:</strong><br>1. Some students copy during exams.<br>2. Students will not copy during exams.</p>",
                    question_hi: "<p>50. दिए गए कथन को सही मानते हुए विचार करें और तय करें कि कथन में कौन-सी दी गई धारणाएं अंतर्निहित है (हैं)।<br><strong>कथन :</strong><br>एक परीक्षा के दौरान, अन्वेषक ने कहा, \"यदि किसी ने नकल करने की कोशिश की तो मैं उसकी परीक्षा रद्द कर दूंगा\"।<br><strong>धारणाएं :</strong><br>1. कुछ छात्र, परीक्षा के दौरान नकल करते है।<br>2. छात्र परीक्षा के दौरान नकल नहीं करेंगे।</p>",
                    options_en: ["<p>Only assumption 1 is implicit.</p>", "<p>Only assumption 2 is implicit.</p>", 
                                "<p>Both assumptions 1 and 2 are implicit.</p>", "<p>Either assumption 1 or 2 is implicit.</p>"],
                    options_hi: ["<p>केवल धारणा 1 अंतर्निहित है।</p>", "<p>केवल धारणा 2 अंतर्निहित है।</p>",
                                "<p>धारणा 1 और 2 दोनों अंतर्निहित हैं।</p>", "<p>या तो धारणा 1 या 2 अंतर्निहित है।</p>"],
                    solution_en: "<p>50.(c) Both assumptions 1 and 2 are implicit.</p>",
                    solution_hi: "<p>50.(c) धारणा 1 और 2 दोनों अंतर्निहित हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. Who joined the Competition Commission of India (CCI) as Chairperson on 23rd May, 2023 ?</p>",
                    question_hi: "<p>51. 23 मई 2023 को, भारतीय प्रतिस्पर्धा आयोग (Competition Commission of India - CCI) में अध्यक्ष के रूप में किसने पद संभाला ?</p>",
                    options_en: ["<p>Shanti Ekambaram</p>", "<p>Ravneet Kaur</p>", 
                                "<p>Leena Nair</p>", "<p>Naina Lal Kidwai</p>"],
                    options_hi: ["<p>शांति एकंबरम</p>", "<p>रवनीत कौर</p>",
                                "<p>लीना नायर</p>", "<p>नैना लाल किदवई</p>"],
                    solution_en: "<p>51.(b) <strong>Ravneet Kaur.</strong> The Competition Commission of India (CCI) is a statutory body of the Government of India responsible for enforcing the Competition Act, 2002, it was duly constituted in March 2009.</p>",
                    solution_hi: "<p>51.(b) <strong>रवनीत कौर</strong>। भारतीय प्रतिस्पर्धा आयोग (CCI) भारत सरकार का एक वैधानिक निकाय है जो प्रतिस्पर्धा अधिनियम, 2002 को लागू करने के लिए जिम्मेदार है, इसका गठन मार्च 2009 में विधिवत किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. The top of two towers of heights x and y standing on level ground, subtend angles of 60&deg; and 30&deg; respectively at the midpoint of the line joining their feet. The value of x : y is</p>",
                    question_hi: "<p>52. समतल जमीन पर खड़े x और y ऊँचाई के दो टावरों के शीर्ष, उनके पाद बिंदु को मिलाने वाली रेखा के मध्य बिंदु पर क्रमशः 60&deg; और 30&deg; के कोण अंतरित करते हैं। x : y का मान क्या है?</p>",
                    options_en: ["<p>2 : 1</p>", "<p>1 : 2</p>", 
                                "<p>1 : 3</p>", "<p>3 : 1</p>"],
                    options_hi: ["<p>2 : 1</p>", "<p>1 : 2</p>",
                                "<p>1 : 3</p>", "<p>3 : 1</p>"],
                    solution_en: "<p>52.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349477899.png\" alt=\"rId28\" width=\"135\" height=\"79\"><br>Let distance from mid point to their feet = b<br>From the above diagram, we can see<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>b</mi></mrow></mfrac></math> = tan60&deg;, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mi>b</mi></mfrac></math> = tan30&deg;<br>The ratio of x : y = b &times; tan60&deg; : b &times; tan30&deg;<br>= 3 : 1</p>",
                    solution_hi: "<p>52.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349477899.png\" alt=\"rId28\" width=\"131\" height=\"77\"><br>माना मध्य बिंदु से उनके पादो की दूरी = b<br>उपरोक्त आरेख से हम देख सकते हैं,<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>b</mi></mrow></mfrac></math> = tan60&deg;, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>y</mi><mi>b</mi></mfrac></math> = tan30&deg;<br>x : y का अनुपात = b &times; tan60&deg; : b &times; tan30&deg;</p>\n<p>= 3 : 1</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. A positively charged ion is called a/an:</p>",
                    question_hi: "<p>53. एक धनावेशित आयन को _________कहा जाता है।</p>",
                    options_en: ["<p>anion</p>", "<p>cation</p>", 
                                "<p>molecule</p>", "<p>atom</p>"],
                    options_hi: ["<p>एनायन</p>", "<p>कैटाएन</p>",
                                "<p>अणु</p>", "<p>परमाणु</p>"],
                    solution_en: "<p>53.(b) <strong>Cation</strong> - Formed when a metal loses its electrons.<strong> Example</strong> - Calcium (Ca<sup>2+</sup>), Potassium (K<sup>+</sup>), Hydrogen (H<sup>+</sup>). <strong>Anions</strong> (Negatively charged ions) - Formed when non-metal gains the electrons. <strong>Examples</strong> - Chlorine (Cl<sup>&ndash;</sup>), Hydroxide (OH<sup>&ndash;</sup>), Iodide (I<sup>&ndash;</sup>). <strong>Molecule: </strong>The smallest unit of a compound that contains the chemical properties of the compound. <strong>Atom</strong> - Made up of particles called protons, neutrons, and electrons.</p>",
                    solution_hi: "<p>53.(b) <strong>कैटायन</strong> - इसका निर्माण तब होता है जब कोई धातु अपना इलेक्ट्रॉन त्याग देता है। <strong>उदाहरण -</strong> कैल्शियम (Ca<sup>2+</sup>), पोटैशियम (K<sup>+</sup>), हाइड्रोजन (H<sup>+</sup>)। <strong>एनायन - </strong>(ऋणात्मक रूप से आवेशित आयन) - इसका निर्माण तब होता है जब अधातु इलेक्ट्रॉन ग्रहण करते हैं। <strong>उदाहरण</strong> - क्लोरीन (Cl<sup>&ndash;</sup>), हाइड्रॉक्साइड (OH<sup>&ndash;</sup>), आयोडाइड (I<sup>&ndash;</sup>)। <strong>अणु: </strong>किसी यौगिक की सबसे छोटी इकाई जिसमें यौगिक के रासायनिक गुण सम्मिलित होते हैं। <strong>परमाणु </strong>- प्रोटॉन, न्यूट्रॉन और इलेक्ट्रॉन नामक कणों से बना होता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. Consider the given statements and decide which of the given assumptions is /are implicit in the statement.<br><strong>Statement:</strong><br>&ldquo;One of ways your child can do better is by getting private tuition&rdquo;, said the teacher.<br><strong>Assumptions:</strong><br>I. The child is not good in studies.<br>II The child cannot do better if he is not taking private tuition.</p>",
                    question_hi: "<p>54. दिए गए कथनों पर विचार करें और तय करें कि दिए गए अनुमानों में से कौन सा/से कथन में निहित है।<br><strong>कथन:</strong><br>शिक्षक ने कहा, \"निजी ट्यूशन प्राप्त करके आपका बच्चा बेहतर कर सकता है\"।<br><strong>धारणाएं:</strong><br>1. बच्चा पढ़ाई में अच्छा नहीं है।<br>2. निजी ट्यूशन न लेने पर बच्चा बेहतर नहीं कर सकता।</p>",
                    options_en: ["<p>Only I is implicit.</p>", "<p>Only II is implicit.</p>", 
                                "<p>Neither I nor II is implicit.</p>", "<p>Both I and II are implicit.</p>"],
                    options_hi: ["<p>केवल I निहित है।</p>", "<p>केवल II निहित है।</p>",
                                "<p>न तो I और न ही II निहित है।</p>", "<p>I और II दोनों निहित हैं।</p>"],
                    solution_en: "<p>54.(c) From the given statement it is clear that neither assumptions 1 nor 2 are implicit.</p>",
                    solution_hi: "<p>54.(c) दिए गए कथन से यह स्पष्ट है कि न तो धारणा 1 और न ही 2 निहित हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. Which State\'s Chief Minister has been awarded the Bharat Ratna Dr Ambedkar Award for fear free state, in May 2023 ?</p>",
                    question_hi: "<p>55. मई 2023 में, किस राज्य के मुख्यमंत्री को भय मुक्त राज्य बनाने के लिए भारत रत्न डॉ. अंबेडकर पुरस्कार से सम्मानित किया गया ?</p>",
                    options_en: ["<p>Chief Minister of UP</p>", "<p>Chief Minister of Jharkhand</p>", 
                                "<p>Chief Minister of Kerala</p>", "<p>Chief Minister of Bihar</p>"],
                    options_hi: ["<p>उत्तर प्रदेश के मुख्यमंत्री</p>", "<p>झारखंड के मुख्यमंत्री</p>",
                                "<p>केरल के मुख्यमंत्री</p>", "<p>बिहार के मुख्यमंत्री</p>"],
                    solution_en: "<p>55.(a) <strong>Chief Minister of UP</strong> (Yogi Adityanath). The Bharat Ratna Dr. Ambedkar Award is an annual award presented to individuals who have made significant contributions to society in the areas of education, social welfare, and economics, in the spirit of Dr. B.R. Ambedkar. The award was instituted in 1996 by the Government of Maharashtra, and it is presented every year on his birth anniversary. The award carries a cash prize and a citation.</p>",
                    solution_hi: "<p>55.(a) <strong>उत्तर प्रदेश के मुख्यमंत्री</strong> (योगी आदित्यनाथ)। भारत रत्न डॉ. अंबेडकर पुरस्कार एक वार्षिक पुरस्कार है, जो उन व्यक्तियों को दिया जाता है, जिन्होंने डॉ. बी.आर. अंबेडकर की भावना के अनुरूप शिक्षा, सामाजिक कल्याण और अर्थशास्त्र के क्षेत्रों में समाज के लिए महत्वपूर्ण योगदान दिया है। इस पुरस्कार की शुरुआत 1996 में महाराष्ट्र सरकार द्वारा की गई थी और यह प्रत्येक वर्ष उनकी जयंती पर दिया जाता है। इस पुरस्कार में नकद पुरस्कार और प्रशस्ति पत्र दिया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. In triangle ABC, P. Q, and R are the mid points of its sides BC, CA, and AB, respectively. If the area of triangle PQR is 6 cm&sup2;, then the area of triangle ABC is :</p>",
                    question_hi: "<p>56. त्रिभुज ABC में, P, Q, और R क्रमशः इसकी भुजाओं BC, CA और AB के मध्य बिंदु हैं। यदि त्रिभुज PQR का क्षेत्रफल 6 cm&sup2; है, तो त्रिभुज ABC का क्षेत्रफल ज्ञात कीजिए ।</p>",
                    options_en: ["<p>36 cm&sup2;</p>", "<p>12 cm&sup2;</p>", 
                                "<p>24 cm&sup2;</p>", "<p>18 cm&sup2;</p>"],
                    options_hi: ["<p>36 cm&sup2;</p>", "<p>12 cm&sup2;</p>",
                                "<p>24 cm&sup2;</p>", "<p>18 cm&sup2;</p>"],
                    solution_en: "<p>56.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349478198.png\" alt=\"rId29\" width=\"99\" height=\"112\"><br>Area of triangle PQR = 6 cm<sup>2</sup><br>Then, area of triangle ABC = 6 &times; 4 = 24 cm<sup>2</sup></p>",
                    solution_hi: "<p>56.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349478198.png\" alt=\"rId29\" width=\"88\" height=\"99\"><br>त्रिभुज PQR का क्षेत्रफल = 6 cm<sup>2</sup><br>तब, त्रिभुज ABC का क्षेत्रफल = 6 &times; 4 = 24 cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57. Elements in the modern periodic table are arranged in ________ vertical columns.</p>",
                    question_hi: "<p>57. आधुनिक आवर्त सारणी में तत्वों को ________ ऊर्ध्वाधर स्तंभों में व्यवस्थित किया जाता है।</p>",
                    options_en: ["<p>18</p>", "<p>16</p>", 
                                "<p>12</p>", "<p>9</p>"],
                    options_hi: ["<p>18</p>", "<p>16</p>",
                                "<p>12</p>", "<p>9</p>"],
                    solution_en: "<p>57.(a) <strong>18. Modern periodic table: </strong>Father - Demitri Mendeleev. Total Elements - 118, Arrangement based on their Atomic number. Elements are arranged in 7 periods (Horizontal rows) and 18 groups (Vertical Columns).<strong> Important Points</strong>: Lightest element - Hydrogen (Atomic Number - 1). Heaviest element (naturally occurring) - Uranium (Atomic Weight - 238). <strong>Noble Gases</strong> - Helium (He), Neon (Ne), Argon (Ar), krypton (Kr), Xenon (Xe), Radon (Rn) and Oganesson (Og).</p>",
                    solution_hi: "<p>57.(a) <strong>18। आधुनिक आवर्त सारणी:</strong> जनक - डेमित्री मेंडेलीव। कुल तत्व - 118, उनके परमाणु क्रमांक के आधार पर व्यवस्थित किया गया है। तत्वों को 7 आवर्तों (क्षैतिज पंक्तियों) और 18 समूहों (ऊर्ध्वाधर स्तंभों) में व्यवस्थित किया गया है। <strong>महत्वपूर्ण बिंदु</strong>: सबसे हल्का तत्व - हाइड्रोजन (परमाणु संख्या - 1)। सबसे भारी तत्व (प्राकृतिक रूप से पाया जाने वाला) - यूरेनियम (परमाणु भार - 238)। <strong>नोबल गैसें </strong>- हीलियम (He), नियॉन (Ne), आर्गन (Ar), क्रिप्टन (Kr), जीनॉन (Xe), रेडॉन (Rn) और ऑगनेसन(Og)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. <strong>Statements:</strong><br>All glasses are plates.<br>No plate is spoon.<br><strong>Conclusions:</strong><br>I. Some glasses are spoons.<br>II. No spoon is a glass.</p>",
                    question_hi: "<p>58. <strong>कथन:</strong><br>सभी गिलास प्लेटें हैं।<br>कोई प्लेट चम्मच नहीं है।<br><strong>निष्कर्ष:</strong><br>I. कुछ गिलास चम्मच हैं।<br>II. कोई चम्मच गिलास नहीं है।</p>",
                    options_en: ["<p>Either conclusion I or II follows</p>", "<p>Only conclusion II follows</p>", 
                                "<p>Neither conclusion I nor II follow</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>या तो निष्कर्ष । या II पालन करता है।</p>", "<p>केवल निष्कर्ष II पालन करता है।</p>",
                                "<p>न तो निष्कर्ष । और न ही II पालन करता है।</p>", "<p>केवल निष्कर्ष । पालन करता है।</p>"],
                    solution_en: "<p>58.(b) <br><strong id=\"docs-internal-guid-686df369-7fff-6ab7-fcca-a97d7e0e606b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcnXzPMRUDFULgVV2tuoXzolZdB7Oti0az0NK5F7orabtfZrxw6kiENSfYjDqvaDd_YkmPlAscQpfFDp07X_Y3Yy-SE079iFUgyDgfGK6R88bjOKpuTpWTvTKGO81QgfMIZ8-UR3qrTdzF3pE0SHzgwfkg?key=FY2PB4WGVOjqZykVyvi7UQ\" width=\"182\" height=\"86\"></strong><br>We can clearly see that no spoon is a glass so only II conclusion is followed.</p>",
                    solution_hi: "<p>58.(b) <br><strong id=\"docs-internal-guid-6a94012c-7fff-9668-61c5-ef6a00ecda01\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXczsL2ybg8CdCCFx_J6fA4O3HhaVPE7uDsZcRG7du1B4THWbZFtgG4THMKCheO8LypyR3Vmt96hovCv-p4sCqxFCIpBtfnvOY5JYzkvOBzKdrBe1uwqPHdHTr9kNpH-m3sSNx3_Q1fW0WVMv1wX4tUPxiTy?key=FY2PB4WGVOjqZykVyvi7UQ\" width=\"189\" height=\"89\"></strong><br>हम स्पष्ट रूप से देख सकते हैं कि कोई भी चम्मच गिलास नहीं है इसलिए केवल II निष्कर्ष का अनुसरण किया जाता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. _______ partners with DAY-NULM towards empowering women entrepreneurs in June 2023.</p>",
                    question_hi: "<p>59. जून 2023 में, _______ ने महिला उद्यमियों को सशक्त बनाने की दिशा में DAY-NULM के साथ साझेदारी की।</p>",
                    options_en: ["<p>UNGA</p>", "<p>UNESCO</p>", 
                                "<p>UNDP</p>", "<p>WHO</p>"],
                    options_hi: ["<p>UNGA</p>", "<p>UNESCO</p>",
                                "<p>UNDP</p>", "<p>WHO</p>"],
                    solution_en: "<p>59.(c) <strong>UNDP</strong>. The United Nations Development Programme (UNDP) and the Deendayal Antyodaya Yojana-National Urban Livelihoods Mission (DAY-NULM) entered into a collaborative partnership aimed at empowering women to make well-informed career choices in the field of entrepreneurship.</p>",
                    solution_hi: "<p>59.(c) <strong>UNDP.</strong> संयुक्त राष्ट्र विकास कार्यक्रम (UNDP) और दीनदयाल अंत्योदय योजना-राष्ट्रीय शहरी आजीविका मिशन (DAY-NULM) ने महिलाओं को उद्यमिता के क्षेत्र में सुविचारित करियर विकल्प चुनने के लिए सशक्त बनाने के उद्देश्य से एक सहयोगात्मक साझेदारी की है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. The ratio of the income of Seema and Darshan is 7 : 5. They save ₹12,000 and ₹9,000 respectively. If the ratio of their expenses is 17 : 12, then find the total expenditure (in Rs) of Seema and Darshan.</p>",
                    question_hi: "<p>60. सीमा और दर्शन की आय का अनुपात 7 : 5 है। वे क्रमशः ₹12,000 और ₹9,000 की बचत करते हैं। यदि उनके व्ययों का अनुपात 17 : 12 है, तो सीमा और दर्शन का कुल व्यय (₹ में) ज्ञात कीजिए ।</p>",
                    options_en: ["<p>93,000</p>", "<p>81,000</p>", 
                                "<p>87,000</p>", "<p>75,000</p>"],
                    options_hi: ["<p>93,000</p>", "<p>81,000</p>",
                                "<p>87,000</p>", "<p>75,000</p>"],
                    solution_en: "<p>60.(c) Ratio of income of Seema and Darshan = 7 : 5<br>Ratio of expenditure of Seema and Darshan = 17 : 12<br>As per the question,<br>7x - 17y = 12000 &hellip;(1) &times; 5<br>5x - 12y = 9000 &hellip;(2) &times; 7<br>Now subtracting equation (1) from (2)<br>35x - 84y = 63000<br><span style=\"text-decoration: underline;\">35x - 85y = 60000&nbsp;</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Y = 3000<br>Therefore, total expenditure<br>= (17 + 12) &times; 3000</p>\n<p>= ₹87000</p>",
                    solution_hi: "<p>60.(c) सीमा और दर्शन की आय का&nbsp;अनुपात = 7 : 5<br>सीमा और दर्शन के व्यय का अनुपात = 17 : 12<br>प्रश्न के अनुसार,<br>7x - 17 y = 12000 &hellip;(1) &times; 5<br>5x - 12y = 9000 &hellip;(2) &times; 7<br>अब समीकरण (1) को (2) से घटने पर,<br>35x - 84y = 63000<br><span style=\"text-decoration: underline;\">35x - 85y = 60000&nbsp;</span><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; Y = 3000<br>इसलिए, कुल व्यय = (17 + 12) &times; 3000 <br>= ₹87000</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. Carbon is able to form stable compounds because of:</p>",
                    question_hi: "<p>61. कार्बन ,____ के कारण स्थायी यौगिक बनाने में सक्षम होता है</p>",
                    options_en: ["<p>its smaller atomic size</p>", "<p>its allotropic forms</p>", 
                                "<p>Its ability to form covalent bond</p>", "<p>the presence of p-orbital</p>"],
                    options_hi: ["<p>अपने छोटे परमाणु आकार</p>", "<p>इसके अपररूपों</p>",
                                "<p>अपनी सहसंयोजक बंध बनाने की क्षमता</p>", "<p>P-कक्षक की उपस्थिति</p>"],
                    solution_en: "<p>61.(a) <strong>Its smaller atomic size</strong>. Carbon forms strong bonds with most other elements. Due to the <strong>small atomic size</strong> of carbon enables its nucleus to strongly hold on to the shared pairs of electrons. Allotropes of carbon - Diamond, graphite and buckminsterfullerene. A Covalent bond is formed by a shared pair of electrons from both the participating atoms. There are four basic types of orbitals: s, p, d, and f present in atoms.</p>",
                    solution_hi: "<p>61.(a) <strong>इसका छोटा परमाणु आकार।</strong> कार्बन अधिकांश अन्य तत्वों के साथ मजबूत बंधन बनाता है। कार्बन के <strong>छोटे परमाणु आकार </strong>के कारण इसके नाभिक को इलेक्ट्रॉनों के साझा जोड़े को मजबूती से पकड़ने में सक्षम बनाता है। कार्बन के अपरूप - हीरा, ग्रेफाइट और बकमिन्स्टरफुलरीन। एक सहसंयोजक बंधन दोनों भाग लेने वाले परमाणुओं से इलेक्ट्रॉनों की एक साझा जोड़ी द्वारा बनता है। चार मूल प्रकार के ऑर्बिटल्स हैं: s, p, d, और f परमाणुओं में मौजूद हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. <strong>Question</strong>:<br>How is damini related to Bhola?<br><strong>Statement:</strong><br>I. Tika has only one son, Bhola and only one daughter, kamala.<br>II. Damini is Roma&rsquo;s son&rsquo;s wife. Kamala is the only daughter of Roma. Meghan is the daughter of Bhola.</p>",
                    question_hi: "<p>62. <strong>प्रश्न:</strong><br>दामिनी का भोला से क्या संबंध है?<br><strong>कथन:</strong><br>I. टीका का केवल एक पुत्र, भोला और केवल एक पुत्री, कमला है।<br>II. दामिनी, रोमा के पुत्र की पत्नी है। कमला, रोमा की इकलौती पुत्री है। मेघन भोला की पुत्री है।</p>",
                    options_en: ["<p>Statement II alone is sufficient , while statement I alone is not sufficient to answer The question.</p>", "<p>Statements I alone is sufficient, while II is not sufficient to answer the question.</p>", 
                                "<p>Both statements I and II together are sufficient to answer the question.</p>", "<p>Statements I or II are not sufficient to answer the question.</p>"],
                    options_hi: ["<p>प्रश्न का उत्तर देने के लिए कथन । अकेले पर्याप्त है, जबकि कथन II अकेले पर्याप्त नहीं है</p>", "<p>प्रश्न का उत्तर देने के लिए कथन II अकेले पर्याप्त है, जबकि कथन I अकेले पर्याप्त नहीं है</p>",
                                "<p>कथन I और II दोनों एक साथ मिलकर प्रश्न का उत्तर देने के लिए पर्याप्त हैं।</p>", "<p>कथन I और II दोनों एक साथ मिलकर प्रश्न का उत्तर देने के लिए पर्याप्त नहीं हैं।</p>"],
                    solution_en: "<p>62.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349478709.png\" alt=\"rId32\" width=\"164\" height=\"95\"><br>So, Both statements I and II together are sufficient to answer the question.</p>",
                    solution_hi: "<p>62.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349478709.png\" alt=\"rId32\" width=\"184\" height=\"107\"><br>अतः, कथन I और II दोनों मिलकर प्रश्न का उत्तर देने के लिए पर्याप्त हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63. A and B invest some amount in the ratio 3 : 5 in a business. After 6 months, C joins them with an investment equal to that of B. In what ratio should the yearly profit be shared among A, B and C?</p>",
                    question_hi: "<p>63. A और B एक व्यवसाय में 3 : 5 के अनुपात में कुछ राशि निवेश करते हैं। 6 महीने के बाद, C व्यवसाय में B के बराबर निवेश करके जुड़ जाता है। वार्षिक लाभ को A, B और C के बीच किस अनुपात में साझा किया जाना चाहिए?</p>",
                    options_en: ["<p>3 : 5 : 6</p>", "<p>6 : 10 : 5</p>", 
                                "<p>6 : 7 : 5</p>", "<p>3 : 5 : 4</p>"],
                    options_hi: ["<p>3 : 5 : 6</p>", "<p>6 : 10 : 5</p>",
                                "<p>6 : 7 : 5</p>", "<p>3 : 5 : 4</p>"],
                    solution_en: "<p>63.(b) Ratio of profit is equal to the product of investment and time of investment .<br>Ratio&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; A&nbsp; &nbsp;:&nbsp; &nbsp;B&nbsp; &nbsp; :&nbsp; &nbsp;C<br>Investment&nbsp; &rarr;&nbsp; 3&nbsp; &nbsp;:&nbsp; &nbsp;5&nbsp; &nbsp; :&nbsp; &nbsp;5<br>Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr; 12&nbsp; :&nbsp; 12&nbsp; &nbsp;:&nbsp; &nbsp;6<br>--------------------------------------------<br>Profit&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; 36 :&nbsp; &nbsp;60&nbsp; &nbsp;:&nbsp; &nbsp;30 <br>=&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 6&nbsp; :&nbsp; &nbsp;10&nbsp; &nbsp;:&nbsp; &nbsp; 5</p>",
                    solution_hi: "<p>63.(b) लाभ का अनुपात , निवेश के उत्पाद और निवेश के समय के गुणज के बराबर है।<br>अनुपात&nbsp; &rarr;&nbsp; &nbsp;A&nbsp; &nbsp;:&nbsp; &nbsp;B&nbsp; &nbsp; :&nbsp; &nbsp;C<br>निवेश&nbsp; &nbsp; &rarr;&nbsp; &nbsp;3&nbsp; &nbsp; :&nbsp; &nbsp;5&nbsp; &nbsp; :&nbsp; &nbsp;5<br>समय&nbsp; &nbsp; &nbsp;&rarr;&nbsp; 12&nbsp; &nbsp;:&nbsp; 12&nbsp; &nbsp;:&nbsp; &nbsp;6<br>--------------------------------------<br>लाभ&nbsp; &nbsp; &nbsp; &rarr;&nbsp; 36&nbsp; &nbsp;:&nbsp; &nbsp;60&nbsp; &nbsp;:&nbsp; &nbsp;30 <br>=&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;6&nbsp; &nbsp; :&nbsp; 10&nbsp; &nbsp; :&nbsp; &nbsp; 5</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. Name the British Chemist who presented the atomic theory in 1808, on conservation of mass and law of definite proportions, which was a turning point in the study of motion.</p>",
                    question_hi: "<p>64. उस ब्रिटिश रसायनज्ञ का नाम बताइए जिसने 1808 में द्रव्यमान के संरक्षण और निश्चित अनुपात के नियम पर परमाणु सिद्धांत प्रस्तुत किया, जो गति के अध्ययन में एक महत्वपूर्ण बदलाव था।</p>",
                    options_en: ["<p>John Dalton</p>", "<p>Lavoisier</p>", 
                                "<p>Ernest Rutherford</p>", "<p>Proust</p>"],
                    options_hi: ["<p>जॉन डाल्टन</p>", "<p>लेवॉज़ियर</p>",
                                "<p>अर्नेस्ट रदरफोर्ड</p>", "<p>प्रॉस्ट</p>"],
                    solution_en: "<p>64.(a) <strong>John Dalton: Law of Conservation of Mass</strong> - Mass is neither created nor destroyed in chemical reactions.<strong>Law of definite proportions -</strong> Every chemical compound contains fixed and constant proportions (by mass) of its constituent elements. <strong>According to Dalton&rsquo;s atomic theory :- </strong>All matter is composed of small particles called atoms. <strong>Ernest Rutherford:</strong> Nuclear structure of the atom, discovered alpha and beta rays and proposed the laws of radioactive decay. <strong>Antoine Lavoisier: </strong>&ldquo;Father of modern chemistry&rdquo;.</p>",
                    solution_hi: "<p>64.(a) <strong>जॉन डाल्टन: द्रव्यमान संरक्षण का नियम - </strong>रासायनिक अभिक्रियाओं में द्रव्यमान को न तो उत्पन्न किया जा सकता है और न ही नष्ट किया जा सकता है। <strong>निश्चित अनुपात का नियम</strong> - प्रत्येक रासायनिक यौगिक में उसके घटक तत्वों के निश्चित और स्थिर अनुपात (द्रव्यमान द्वारा) होते हैं। <strong>डाल्टन के परमाणु सिद्धांत के अनुसार:-</strong> सभी पदार्थ छोटे-छोटे कणों से बने होते हैं जिन्हें परमाणु कहते हैं। <strong>अर्नेस्ट रदरफोर्ड:</strong> परमाणु की नाभिकीय संरचना, अल्फा और बीटा किरणों की खोज की और रेडियोधर्मी क्षय का नियम प्रस्तुत किया। <strong>एंटोनी लवॉज़ियर:</strong> \"आधुनिक रसायन विज्ञान के जनक\"।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. There are seven members H, I, J, K, L, M and N in a family. H is K&rsquo;s mother&rsquo;s mother. J and L are the only two children of H and M. J is the only daughter of M. N is the son of L and I. How is M related to N ?</p>",
                    question_hi: "<p>65 एक परिवार में सात सदस्य H, I, J, K, L, M और N हैं। H, K की मां की मां है। H और M के केवल दो बच्चे J और L हैं । J, M की इकलौती पुत्री है। N, L और । का पुत्र है। M का N से क्या संबंध है?</p>",
                    options_en: ["<p>Father&rsquo;s sister</p>", "<p>Mother&rsquo;s father</p>", 
                                "<p>Father&rsquo;s father</p>", "<p>Mother&rsquo;s brother</p>"],
                    options_hi: ["<p>पिता की बहन</p>", "<p>मां के पिता</p>",
                                "<p>पिता के पिता</p>", "<p>मां का भाई</p>"],
                    solution_en: "<p>65.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349478860.png\" alt=\"rId33\" width=\"134\" height=\"103\"><br>So, M is the father&rsquo;s father of N.</p>",
                    solution_hi: "<p>65.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349478860.png\" alt=\"rId33\" width=\"134\" height=\"103\"><br>अत: M, N के पिता के पिता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. A and B can do a work in 18 days, B and C can do the same work in 15 days, while A and C can do the work in 12 days. Working together, how much time will they take to complete the work?</p>",
                    question_hi: "<p>66. A और B किसी कार्य को 18 दिन में कर सकते हैं, B और C उसी कार्य को 15 दिन में कर सकते हैं, जबकि A और C कार्य को 12 दिन में कर सकते हैं। एक साथ मिलकर उन्हें कार्य को पूरा करने में कितना समय लगेगा?</p>",
                    options_en: ["<p>8<math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>", 
                                "<p>9<math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>", "<p>10<math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>8<math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>", "<p>11<math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>",
                                "<p>9<math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>", "<p>10<math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>37</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>66.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349479046.png\" alt=\"rId34\" width=\"175\" height=\"91\"><br>Efficiency of 2(A + B + C) = 37<br>Efficiency of (A + B + C) = <math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>So required days = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mfrac><mrow><mn>37</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>37</mn></mfrac></math> = 9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>37</mn></mfrac></math></p>",
                    solution_hi: "<p>66.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349479210.png\" alt=\"rId35\" width=\"162\" height=\"95\"><br>2(A + B + C) की क्षमता = 37<br>(A + B + C) की क्षमता = <math display=\"inline\"><mfrac><mrow><mn>37</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math><br>इसलिए, आवश्यक दिन&nbsp;= <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mfrac><mrow><mn>37</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math></p>\n<p>&rArr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>360</mn><mn>37</mn></mfrac></math> = 9<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>27</mn><mn>37</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. Carbonisation is a term used to describe the process of transforming dead plants into _______ over a long period of time.</p>",
                    question_hi: "<p>67. लंबी अवधि के दौरान मृत पौधों के _________ में परिवर्तित होने की प्रक्रिया कार्बनीकरण कहलाती है।</p>",
                    options_en: ["<p>petrol</p>", "<p>fossil</p>", 
                                "<p>coal</p>", "<p>diesel</p>"],
                    options_hi: ["<p>पेट्रोल</p>", "<p>जीवाश्म</p>",
                                "<p>कोयला</p>", "<p>डीजल</p>"],
                    solution_en: "<p>67.(c) <strong>coal. Carbonisation</strong>: conversion of organic matters (plants and dead animal remains) into coal through destructive distillation under high pressure and temperature. Coal is a complex mixture of carbon, hydrogen and oxygen compounds, found in coal mines deep under the surface of earth. The classification of coal depends on the <strong>carbon content</strong> of coal.<strong> Higher the temperature and pressure</strong> of the Earth and the longer time the coal has been buried under the Earth, the <strong>more is the carbon content</strong> in it. <strong>Peat</strong> (30-40%) &lt; <strong>Lignite</strong> (50%) &lt; <strong>Bituminous </strong>(60%) &lt; <strong>Anthracite</strong> (&gt;80%).</p>",
                    solution_hi: "<p>67.(c) <strong>कोयला। कार्बनीकरण </strong>- उच्च दबाव और उच्च तापमान के तहत विनाशकारी आसवन के माध्यम से पौधों और मृत जानवरों के अवशेषों जैसे कार्बनिक पदार्थों का कोयले में रूपांतरण । कोयला कार्बन, हाइड्रोजन और ऑक्सीजन यौगिकों का एक जटिल मिश्रण है जो पृथ्वी की सतह के नीचे गहरे कोयले की खानों में पाया जाता है। कोयले का वर्गीकरण, कोयले में उपस्थित कार्बन पदार्थ पर निर्भर करता है। पृथ्वी का तापमान और दबाव जितना अधिक होगा और जितना अधिक समय तक कोयला पृथ्वी के नीचे दबा रहेगा, उसमें कार्बन की मात्रा उतनी ही अधिक होगी। <strong>पीट</strong> (30-40%) &lt;<strong>लिग्नाइट </strong>(50%) &lt;<strong>बिटुमिनस</strong> (60%) &lt;<strong>एन्थ्रेसाइट</strong> (&gt;80%)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. Study the diagram and answer the question that follows. The numbers in different Sections indicate the number of persons. How many Indian industrialists make autos but are NOT steel producers?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349479394.png\" alt=\"rId36\" width=\"148\" height=\"119\"></p>",
                    question_hi: "<p>68. निम्न आरेख का अध्ययन करें, और उसके आधार पर पूछे गए प्रश्न का उत्तर दें। विभिन्न भागों में दी गई संख्याएं, उस भाग से संबंधित व्यक्तियों की संख्या को दर्शाती हैं। कितने भारतीय उद्योगपति ऑटो का निर्माण करते हैं, किंतु स्टील उत्पादक नहीं हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349479574.png\" alt=\"rId37\" width=\"174\" height=\"143\"></p>",
                    options_en: ["<p>6</p>", "<p>3</p>", 
                                "<p>8</p>", "<p>13</p>"],
                    options_hi: ["<p>6</p>", "<p>3</p>",
                                "<p>8</p>", "<p>13</p>"],
                    solution_en: "<p>68.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349479394.png\" alt=\"rId36\" width=\"150\" height=\"121\"><br>There are 6 such persons.</p>",
                    solution_hi: "<p>68.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349479574.png\" alt=\"rId37\" width=\"159\" height=\"131\"><br>ऐसे 6 व्यक्ति हैं जो भारतीय उद्योगपति ऑटो का निर्माण करते हैं, किंतु स्टील उत्पादक नहीं हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. Ratio of the time taken by three taps P, Q and R to individually fill a cistern is 2 : 1 : 6. Which tap is the fastest ?</p>",
                    question_hi: "<p>69. तीन नल P, Q और R द्वारा अलग-अलग एक टंकी को भरने में लिए गए समय का अनुपात 2 : 1 : 6 है। इनमें से कौन सा नल सबसे तेज है ?</p>",
                    options_en: ["<p>Cannot be determined</p>", "<p>P</p>", 
                                "<p>Q</p>", "<p>R</p>"],
                    options_hi: ["<p>निर्धारित नहीं किया जा सकता</p>", "<p>P</p>",
                                "<p>Q</p>", "<p>R</p>"],
                    solution_en: "<p>69.(c) efficiency<math display=\"inline\"><mi>&#160;</mi><mo>&#8733;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>time</mi></mfrac></math><br>Ratio of time of taps P,Q and R = 2 : 1 : 6<br>Efficiency of taps P, Q and R are<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> = 3 : 6 : 1<br>Clearly , the fastest tap is Q .</p>",
                    solution_hi: "<p>69.(c) दक्षता <math display=\"inline\"><mi>&#160;</mi><mo>&#8733;</mo></math> <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>&#2360;&#2350;&#2351;</mi></mfrac></math> <br>नल P, Q और R के समय का अनुपात = 2 : 1 : 6<br>नल P, Q और R की दक्षता <br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1</mn></mfrac></math> : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math> = 3 : 6 : 1<br>स्पष्ट रूप से, सबसे तेज नल Q है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. Which of the following is present in the center of a flower?</p>",
                    question_hi: "<p>70. निम्नलिखित में से कौन सा फूल के केंद्र में मौजूद होता है?</p>",
                    options_en: ["<p>Carpel</p>", "<p>Stamen</p>", 
                                "<p>Petals</p>", "<p>Sepals</p>"],
                    options_hi: ["<p>अंडप</p>", "<p>पुष्प केसर</p>",
                                "<p>पंखुड़ियों</p>", "<p>बाह्यदल</p>"],
                    solution_en: "<p>70.(a) <strong>Carpel</strong>. A typical flower has four main parts. The outermost whorl of the flowering plant is<strong> calyx</strong> and <strong>sepals</strong> are the floral leaves of this whorl. The second whorl is <strong>corolla</strong> and <strong>petals</strong> are floral leaves here. The third whorl is <strong>Androecium</strong>, which is the male reproductive organ. The floral leaves here are <strong>stamens.</strong> The fourth (central) whorl is <strong>gynoecium </strong>and it is the female reproductive organ of the flower. The floral leaves in it are carpels.</p>",
                    solution_hi: "<p>70.(a) <strong>अंडप।</strong> पुष्प पत्तियाँ चार चक्रों में व्यवस्थित चार प्रकार की संरचनाएँ हैं। फूल वाले पौधे का सबसे बाहरी चक्र <strong>बाह्यदलपुंज</strong> (calyx) होता है और बाह्यदल इस चक्र की पुष्पीय पत्तियाँ होती हैं। दूसरा चक्र दलपुंज (कोरोला) है और यहाँ की पंखुड़ियाँ पुष्प पत्तियाँ हैं। तीसरा चक्र पुमंग <strong>(एंड्रोकियम)</strong> है, जो नर प्रजनन अंग है। यहाँ की पुष्प पत्तियाँ पुंकेसर हैं। चौथा (केंद्रीय) चक्र जायांग है और यह फूल का मादा प्रजनन अंग है। इसमें लगे पुष्प के पत्ते अंडप होते हैं।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. Which cube given in the options CANNOT be made using the following sheet?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349479742.png\" alt=\"rId38\" width=\"126\" height=\"78\"></p>",
                    question_hi: "<p>71. निम्नलिखित शीट का प्रयोग करके विकल्पों में दिया गया कौन-सा घन नहीं बनाया जा सकता है?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349479742.png\" alt=\"rId38\" width=\"134\" height=\"83\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349479881.png\" alt=\"rId39\" width=\"71\" height=\"67\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480000.png\" alt=\"rId40\" width=\"66\" height=\"62\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480129.png\" alt=\"rId41\" width=\"74\" height=\"70\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480252.png\" alt=\"rId42\" width=\"74\" height=\"69\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349479881.png\" alt=\"rId39\" width=\"71\" height=\"67\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480000.png\" alt=\"rId40\" width=\"73\" height=\"69\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480129.png\" alt=\"rId41\" width=\"76\" height=\"71\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480252.png\" alt=\"rId42\" width=\"73\" height=\"68\"></p>"],
                    solution_en: "<p>71.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480000.png\" alt=\"rId40\" width=\"73\" height=\"69\"></p>",
                    solution_hi: "<p>71.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480000.png\" alt=\"rId40\" width=\"73\" height=\"69\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. A man travels 80 km in three hours. He further travels for two more hours. Find the distance travelled in the latter two hours, if his average speed for the entire journey is 30 km/h.</p>",
                    question_hi: "<p>72. एक आदमी तीन घंटे में 80 किमी की यात्रा करता है। वह आगे और दो घंटे की यात्रा करता है। यदि पूरी यात्रा के लिए उसकी औसत गति 30 किमी/घंटा है। तो बाद के दो घंटों में तय की गई दूरी ज्ञात कीजिए,</p>",
                    options_en: ["<p>70 km</p>", "<p>150 km</p>", 
                                "<p>120 km</p>", "<p>90 km</p>"],
                    options_hi: ["<p>70 किमी</p>", "<p>150 किमी</p>",
                                "<p>120 किमी</p>", "<p>90 किमी</p>"],
                    solution_en: "<p>72.(a) Let the distance travelled latter = x km., Average speed <br>= <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>d</mi><mi>i</mi><mi>s</mi><mi>t</mi><mo>.</mo></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>t</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>+</mo><mi>x</mi></mrow><mrow><mn>3</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> = 30<br>&rArr; 80 + x = 150</p>\n<p>&rArr; x = 70 km</p>",
                    solution_hi: "<p>72.(a)<br>माना बाद में तय की गई दूरी = x किमी <br>औसत चाल = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2342;&#2370;&#2352;&#2368;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mo>&#160;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>80</mn><mo>+</mo><mi>x</mi></mrow><mrow><mn>3</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> = 30<br>&rArr; 80 + x = 150</p>\n<p>&rArr; x = 70 km</p>\n<p><br><br></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. Six persons, A, B, C, D, E and F, are sitting around a circular table. All are facing the center of the table. Only two persons are sitting between D and A. F is to the immediate right of C. E is second to the right of F. E is second to the left of A. Who is sitting to the immediate right of F?</p>",
                    question_hi: "<p>73. छह व्यक्ति A, B, C, D, E और F एक वृत्ताकार मेज के चारों ओर बैठे हैं। सभी का मुख मेज के केंद्र की ओर है। D और A के बीच केवल दो व्यक्ति बैठे हैं। F, C के ठीक दायें बैठा है। E, F के दायें से दूसरे स्थान पर है। E, A के बायें से दूसरे स्थान पर है। F के ठीक दायें कौन बैठा है?</p>",
                    options_en: ["<p>E</p>", "<p>C</p>", 
                                "<p>A</p>", "<p>D</p>"],
                    options_hi: ["<p>E</p>", "<p>C</p>",
                                "<p>A</p>", "<p>D</p>"],
                    solution_en: "<p>73.(d) As per given instructions in the question, we can arrange all persons in the following way.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480387.png\" alt=\"rId43\" width=\"136\" height=\"87\"><br>We can clearly see in the above diagram that D is immediate right of F.</p>",
                    solution_hi: "<p>73.(d) प्रश्न में दिए गए निर्देशों के अनुसार, हम सभी व्यक्तियों को निम्न प्रकार से व्यवस्थित कर सकते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480519.png\" alt=\"rId44\" width=\"129\" height=\"86\"><br>उपरोक्त आरेख में हम स्पष्ट रूप से देख सकते हैं कि D, F के ठीक दायें है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. The percentage profit earned by James by selling an article for ₹ 1,920 is equal to the percentage loss incurred by selling the same article for ₹ 1,500. At what selling price should he sell the article if he wants to make a profit of 10%?</p>",
                    question_hi: "<p>74. एक वस्तु को ₹1,920 में बेचने पर जेम्स द्वारा अर्जित प्रतिशत लाभ, उस वस्तु को ₹1,500 में बेचने पर हुई प्रतिशत हानि के बराबर है। यदि वह 10% लाभ अर्जित करना चाहता है, तो उसे उस वस्तु को किस विक्रय मूल्य पर बेचना चाहिए?</p>",
                    options_en: ["<p>₹4,000</p>", "<p>₹7,000</p>", 
                                "<p>₹1,881</p>", "<p>₹2,000</p>"],
                    options_hi: ["<p>₹4,000</p>", "<p>₹7,000</p>",
                                "<p>₹1,881</p>", "<p>₹2,000</p>"],
                    solution_en: "<p dir=\"ltr\">74.(c) Let the C.P = ₹x</p>\n<p dir=\"ltr\">Profit gained at 1920</p>\n<p dir=\"ltr\">&nbsp;= loss incurred at 1500</p>\n<p dir=\"ltr\">1920 - x = x - 1500</p>\n<p dir=\"ltr\">&rArr; 2x = 1920 + 1500</p>\n<p dir=\"ltr\">&rArr; 2x = 3420&nbsp;</p>\n<p dir=\"ltr\">&rArr; x = 1710</p>\n<p dir=\"ltr\">At 10% percent profit, S.P&nbsp;</p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>100</mn></mfrac></math> &times; 1710</p>\n<p>=₹1,881</p>",
                    solution_hi: "<p dir=\"ltr\">74.(c) माना क्रय मूल्य = ₹ x&nbsp;</p>\n<p dir=\"ltr\">1920 पर हुआ लाभ = 1500 पर हुआ नुकसान</p>\n<p dir=\"ltr\">1920 - x = x - 1500</p>\n<p dir=\"ltr\">&rArr; 2x = 1920 + 1500</p>\n<p dir=\"ltr\">&rArr; 2x = 3420&nbsp;</p>\n<p dir=\"ltr\">&rArr; x = 1710</p>\n<p dir=\"ltr\">10% प्रतिशत लाभ पर,विक्रय मूल्य&nbsp;&nbsp;</p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>110</mn><mn>100</mn></mfrac></math> &times; 1710</p>\n<p>=₹1,881</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "misc",
                    question_en: "<p>75. Samarth heading towards north turns right after walking 10m, he turns right again walks for 4 meters and then he turns left and walks 6km. In which direction he is walking?</p>",
                    question_hi: "<p>75. समर्थ 10 मीटर उत्तर की ओर चलता है और फिर दाएं मुड़ता है। एक बार फिर वह दाएं मुड़ता है, 4 मीटर तक चलता है , बाएं मुड़ता है और 6 किलोमीटर तक चलता है। अब समर्थ का मुख किस दिशा की ओर है?</p>",
                    options_en: ["<p>West</p>", "<p>North</p>", 
                                "<p>East</p>", "<p>South</p>"],
                    options_hi: ["<p>पश्चिम</p>", "<p>उत्तर</p>",
                                "<p>पूर्व</p>", "<p>दक्षिण</p>"],
                    solution_en: "<p>75.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480705.png\" alt=\"rId45\" width=\"131\" height=\"117\"><br>He is walking in the East direction.</p>",
                    solution_hi: "<p>75.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1727349480849.png\" alt=\"rId46\" width=\"129\" height=\"118\"><br>वह पूर्व दिशा में चल रहा है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>