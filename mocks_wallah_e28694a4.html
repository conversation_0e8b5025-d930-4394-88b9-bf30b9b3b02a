<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">5:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 5 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">Which state is the largest in terms of the outstanding portfolio of microfinance loans, as per the MFIN Micrometer Q4 FY21-</span><span style=\"font-family: Cambria Math;\">22 ?</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Cambria Math;\">MFIN </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2350;&#2368;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> Q4 </span><span style=\"font-family: Cambria Math;\">FY21</span><span style=\"font-family: Cambria Math;\">-22 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2347;&#2366;&#2311;&#2344;&#2375;&#2306;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2315;&#2339;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2325;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2379;&#2352;&#2381;&#2335;&#2347;&#2379;&#2354;&#2367;&#2351;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2350;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> I </span></p>\n",
                    options_en: ["<p>Karnataka</p>\n", "<p>Uttar Pradesh</p>\n", 
                                "<p>Maharashtra</p>\n", "<p>Tamil Nadu</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Tamil Nadu. Microfinance</strong> </span><span style=\"font-family: Cambria Math;\">- A banking service provided to low-income individuals or groups who otherwise would have no other access to financial services. The modern use of the expression \"microfinancing\" has roots in the 1970s when Grameen Bank of Bangladesh, founded by microfinance pioneer Muhammad Yunus. </span><span style=\"font-family: Cambria Math;\"><strong>SEWA</strong> </span><span style=\"font-family: Cambria Math;\">(Self-Employed Women\'s Association) Bank, a cooperative bank, was established in Ahmedabad as one of the first modern-day microfinance institutions in the country.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">d) </span><strong><span style=\"font-family: Nirmala UI;\">&#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2347;&#2366;&#2311;&#2344;&#2366;&#2306;&#2360;</span></strong><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2370;&#2361;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2306;&#2325;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2381;&#2351;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2340;&#2381;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;&#2357;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2369;&#2306;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> \"</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2347;&#2366;&#2311;&#2344;&#2375;&#2306;&#2360;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\">\" </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2367;&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2369;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2337;&#2364;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 1970 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2347;&#2366;&#2311;&#2344;&#2375;&#2306;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2327;&#2381;&#2352;&#2339;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2361;&#2350;&#2381;&#2350;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2369;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2352;&#2366;&#2350;&#2368;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2321;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2306;&#2327;&#2381;&#2354;&#2366;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> <strong>SEWA</strong> (</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2332;&#2364;&#2327;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2328;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2369;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2347;&#2366;&#2311;&#2344;&#2375;&#2306;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2361;&#2350;&#2342;&#2366;&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">Which of the following statements is/are correct with respect to the demand for </span><span style=\"font-family: Cambria Math;\">money ?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">i. When the interest rate is high, the demand for money is low.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ii. When the interest rate is low, demand for money is also low.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">iii. When the interest rate is high, demand for money is also high. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">iv. When the interest rate is low, demand for money is high.</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2342;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">i</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2342;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ii. </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2342;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">iii. </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2342;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">iv. </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2342;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>Only ii and <span style=\"font-family: Cambria Math;\">iv</span></p>\n", "<p>Only i and <span style=\"font-family: Cambria Math;\">iv</span></p>\n", 
                                "<p>Only ii and iii</p>\n", "<p>Only i and ii</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> ii </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> iv</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> i </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> iv</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> ii </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> iii</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> i </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> ii</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b) </span><strong>Only i and iv. Motive of Money</strong><strong> - </strong><span style=\"font-weight: 400;\">Three motives: </span><strong>Transaction Motive</strong><span style=\"font-weight: 400;\"> - To meet the day to day needs of its business operations. Examples - Wages, interests, dividends, goods purchased. </span><strong>Precautionary Motive</strong><span style=\"font-weight: 400;\"> -</span><span style=\"font-weight: 400;\"> The contingencies or unforeseen circumstances arising in the course of business. Examples - Increase in the price of raw materials, labor strike, lockouts. </span><strong>Speculative motives </strong><span style=\"font-weight: 400;\">- The benefit of bargain purchases that may arise in the future.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>&#2325;&#2375;&#2357;&#2354; i &#2324;&#2352; iv</strong><span style=\"font-weight: 400;\"> &#2404; </span><strong>&#2350;&#2369;&#2342;&#2381;&#2352;&#2366; &#2325;&#2366; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; </strong><span style=\"font-weight: 400;\">- &#2340;&#2368;&#2344; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351;: </span><strong>&#2354;&#2375;&#2344;-&#2342;&#2375;&#2344; &#2325;&#2366; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; -</strong><span style=\"font-weight: 400;\"> &#2309;&#2346;&#2344;&#2375; &#2357;&#2381;&#2351;&#2357;&#2360;&#2366;&#2351; &#2360;&#2306;&#2330;&#2366;&#2354;&#2344; &#2325;&#2368; &#2342;&#2367;&#2344;-&#2346;&#2381;&#2352;&#2340;&#2367;&#2342;&#2367;&#2344; &#2325;&#2368; &#2332;&#2352;&#2370;&#2352;&#2340;&#2379;&#2306; &#2325;&#2379; &#2346;&#2370;&#2352;&#2366; &#2325;&#2352;&#2344;&#2366;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2357;&#2375;&#2340;&#2344;, &#2348;&#2381;&#2351;&#2366;&#2332;, &#2354;&#2366;&#2349;&#2366;&#2306;&#2358;, &#2326;&#2352;&#2368;&#2342;&#2366; &#2327;&#2351;&#2366; &#2360;&#2366;&#2350;&#2366;&#2344;&#2404; </span><strong>&#2346;&#2370;&#2352;&#2381;&#2357;&#2379;&#2346;&#2366;&#2351; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; -</strong><strong> </strong><span style=\"font-weight: 400;\">&#2357;&#2381;&#2351;&#2357;&#2360;&#2366;&#2351; &#2325;&#2375; &#2342;&#2380;&#2352;&#2366;&#2344; &#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2310;&#2325;&#2360;&#2381;&#2350;&#2367;&#2325;&#2340;&#2366;&#2319;&#2305; &#2351;&#2366; &#2309;&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2366;&#2358;&#2367;&#2340; &#2346;&#2352;&#2367;&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;&#2351;&#2366;&#2305;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2325;&#2330;&#2381;&#2330;&#2375; &#2350;&#2366;&#2354; &#2325;&#2368; &#2325;&#2368;&#2350;&#2340; &#2350;&#2375;&#2306; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367;, &#2350;&#2332;&#2342;&#2370;&#2352;&#2379;&#2306; &#2325;&#2368; &#2361;&#2337;&#2364;&#2340;&#2366;&#2354;, &#2340;&#2366;&#2354;&#2366;&#2348;&#2306;&#2342;&#2368;&#2404; </span><strong>&#2325;&#2366;&#2354;&#2381;&#2346;&#2344;&#2367;&#2325; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351;</strong><span style=\"font-weight: 400;\"> - &#2349;&#2357;&#2367;&#2359;&#2381;&#2351; &#2350;&#2375;&#2306; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2360;&#2380;&#2342;&#2375;&#2348;&#2366;&#2332;&#2368; &#2325;&#2368; &#2326;&#2352;&#2368;&#2342;&#2366;&#2352;&#2368; &#2325;&#2366; &#2354;&#2366;&#2349;&#2404;</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">Identify the INCORRECT equation</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Nirmala UI;\">&#2327;&#2354;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2330;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span></p>\n",
                    options_en: ["<p>Revenue receipts = Tax revenue (net of State\'s share) less non-tax revenue</p>\n", "<p><span style=\"font-weight: 400;\">Gross fiscal deficit = Total expenditure </span><span style=\"font-weight: 400;\">-</span><span style=\"font-weight: 400;\"> (Revenue receipts + Non-debt creating capital receipts)</span></p>\n", 
                                "<p>Gross primary deficit = Gross fiscal deficit <span style=\"font-family: Cambria Math;\"> Net interest liabilities</span></p>\n", "<p>Revenue deficit = Revenue expenditure <span style=\"font-family: Cambria Math;\"> revenue receipts</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\">) &ndash; </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2376;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2325;&#2354; &#2352;&#2366;&#2332;&#2325;&#2379;&#2359;&#2368;&#2351; &#2328;&#2366;&#2335;&#2366; = &#2325;&#2369;&#2354; &#2357;&#2381;&#2351;&#2351; - ( &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2366;&#2306; + &#2327;&#2376;&#2352;-&#2315;&#2339; &#2360;&#2371;&#2332;&#2344; &#2346;&#2370;&#2306;&#2332;&#2368; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2366;&#2306;)</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2341;&#2350;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2366;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2325;&#2379;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2366;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2342;&#2366;&#2351;&#2327;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2366;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2366;&#2306;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a).</span><strong>Revenue receipts</strong><span style=\"font-weight: 400;\"> - Money earned by a business through its day to day operational activities. </span><strong>Revenue Expenditure</strong><span style=\"font-weight: 400;\"> - An expense that is incurred by the business as a result of producing its products and services. </span><strong>Revenue Deficit </strong><span style=\"font-weight: 400;\">- It refers to the excess of revenue expenditure over revenue receipts.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a).&nbsp;</span><strong>&#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2366;&#2305; </strong><span style=\"font-weight: 400;\">- &#2325;&#2367;&#2360;&#2368; &#2357;&#2381;&#2351;&#2357;&#2360;&#2366;&#2351; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2309;&#2346;&#2344;&#2368; &#2342;&#2367;&#2344;-&#2346;&#2381;&#2352;&#2340;&#2367;&#2342;&#2367;&#2344; &#2325;&#2368; &#2346;&#2352;&#2367;&#2330;&#2366;&#2354;&#2344; &#2327;&#2340;&#2367;&#2357;&#2367;&#2343;&#2367;&#2351;&#2379;&#2306; &#2325;&#2375; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2360;&#2375; &#2309;&#2352;&#2381;&#2332;&#2367;&#2340; &#2343;&#2344;&#2404;</span><strong> &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2357;&#2381;&#2351;&#2351; - </strong><span style=\"font-weight: 400;\">&#2319;&#2325; &#2357;&#2381;&#2351;&#2351; &#2332;&#2379; &#2357;&#2381;&#2351;&#2357;&#2360;&#2366;&#2351; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2309;&#2346;&#2344;&#2375; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2379;&#2306; &#2324;&#2352; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2375; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2325;&#2375; &#2346;&#2352;&#2367;&#2339;&#2366;&#2350;&#2360;&#2381;&#2357;&#2352;&#2370;&#2346; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2328;&#2366;&#2335;&#2366; -</strong><span style=\"font-weight: 400;\"> &#2351;&#2361; &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306; &#2346;&#2352; &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2357;&#2381;&#2351;&#2351; &#2325;&#2368; &#2309;&#2343;&#2367;&#2325;&#2340;&#2366; &#2325;&#2379; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which of these is NOT one of the reasons why the public sector played a dominant role in establishing the Industrial sector in India post-</span><span style=\"font-family: Cambria Math;\">Independence ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p>4<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2332;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2370;&#2350;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2349;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Poor savings capacity of the public</p>\n", "<p>To avoid generation of private profit</p>\n", 
                                "<p>Absence of large enough market for output</p>\n", "<p>Lack of adequate capital with private industrialists</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2366;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2350;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2371;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2330;&#2344;&#2366;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2381;&#2351;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2332;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2366;&#2357;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2381;&#2351;&#2379;&#2327;&#2346;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2381;&#2351;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2306;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2349;&#2366;&#2357;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> In 1950, a Planning Commission was established by the government to formulate and implement effective economic development policies. The \"mixed economy\" model was embraced, where the State and private sector collaborated to boost production and employment. Second Five Year Plan (1956-61</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> Focussed on rapid </span><span style=\"font-family: Cambria Math;\">industrialization and the public sector and based on Mahalanobis model. Steel plants at Bhilai (Chattisgarh), Rourkela (Odisha) and Durgapur (West Bengal) were set up during this period. </span></p>\n",
                    solution_hi: "<p>4.(b)<span style=\"font-family: Cambria Math;\"> 1950 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2381;&#2341;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2340;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;&#2404;</span><span style=\"font-family: Cambria Math;\"> \"</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2358;&#2381;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">\" </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2377;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2361;&#2366;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2332;&#2327;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2338;&#2364;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> (1956-61)- </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2357;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2342;&#2381;&#2351;&#2379;&#2327;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2381;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2366;&#2354;&#2344;&#2379;&#2348;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2377;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2367;&#2354;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2331;&#2340;&#2381;&#2340;&#2368;&#2360;&#2327;&#2338;&#2364;</span><span style=\"font-family: Cambria Math;\">), </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2313;&#2352;&#2325;&#2375;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2323;&#2337;&#2367;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2352;&#2381;&#2327;&#2366;&#2346;&#2369;&#2352;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2358;&#2381;&#2330;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2327;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2381;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2351;&#2306;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which of the following is NOT included in \'Operating Surplus\'?</span></p>\n",
                    question_hi: "<p>5<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> \' </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2330;&#2366;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> (Operating Surplus)\' </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Rent</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Profit</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Interest</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Wages in cash</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2352;&#2366;&#2351;&#2366;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2325;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2332;&#2342;&#2370;&#2352;&#2368;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d) <strong>Wages in Cash.</strong></span><strong><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Cambria Math;\"><strong>Operating Surplus</strong>:</span><span style=\"font-family: Cambria Math;\"> concept used in national accounts statistics which refers to the measure of surplus accruing from production of the output before deducting property income from it like land, rent, and interest. It is a component of value added and gross domestic product. </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d)<strong> </strong></span><strong><span style=\"font-family: Nirmala UI;\">&#2344;&#2325;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span></strong><span style=\"font-family: Nirmala UI;\"><strong>&#2350;&#2332;&#2342;&#2370;&#2352;&#2368;</strong>&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2330;&#2366;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2358;&#2375;&#2359;</span></strong><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2305;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2343;&#2366;&#2352;&#2339;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2370;&#2350;&#2367;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2352;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2346;&#2340;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2335;&#2380;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2342;&#2352;&#2381;&#2349;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2352;&#2375;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which of the following is NOT a monetary policy instrument of </span><span style=\"font-family: Cambria Math;\">RBI ?</span></p>\n",
                    question_hi: "<p>6<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2348;&#2368;&#2310;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2380;&#2342;&#2381;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Government Spending</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Bank Rate </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Open Market Operations</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Cash Reserve Ratio </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2325;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2326;&#2369;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2332;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2319;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2325;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp; </span><strong>Government Spending. </strong><span style=\"font-weight: 400;\">The money spent by the public sector on the acquisition of goods and provision of services such as education, healthcare, social protection, and defense. </span><strong>Bank Rate</strong><span style=\"font-weight: 400;\">: Rate at which RBI gives loans to commercial banks without keeping any collateral (security). </span><strong>Open Market Operations:</strong><span style=\"font-weight: 400;\"> Buying and selling of government securities in the open market. </span><strong>Cash Reserve Ratio (CRR)</strong><span style=\"font-weight: 400;\">: It is the percentage of deposits that commercial banks are required to keep with the Reserve Bank of India in the form of cash reserves.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">a) </span><strong>&#2360;&#2352;&#2325;&#2366;&#2352;&#2368; &#2326;&#2352;&#2381;&#2330;&#2404;</strong><span style=\"font-weight: 400;\"> &#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2325;&#2375; &#2309;&#2343;&#2367;&#2327;&#2381;&#2352;&#2361;&#2339; &#2324;&#2352; &#2358;&#2367;&#2325;&#2381;&#2359;&#2366;, &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351; &#2342;&#2375;&#2326;&#2349;&#2366;&#2354;, &#2360;&#2366;&#2350;&#2366;&#2332;&#2367;&#2325; &#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366; &#2340;&#2341;&#2366; &#2352;&#2325;&#2381;&#2359;&#2366; &#2332;&#2376;&#2360;&#2368; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2375; &#2346;&#2381;&#2352;&#2366;&#2357;&#2343;&#2366;&#2344; &#2346;&#2352; &#2326;&#2352;&#2381;&#2330; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2343;&#2344;&#2404; </span><strong>&#2348;&#2376;&#2306;&#2325; &#2342;&#2352;:</strong><span style=\"font-weight: 400;\"> &#2357;&#2361; &#2342;&#2352; &#2332;&#2367;&#2360; &#2346;&#2352; RBI &#2357;&#2366;&#2339;&#2367;&#2332;&#2381;&#2351;&#2367;&#2325; &#2348;&#2376;&#2306;&#2325;&#2379;&#2306; &#2325;&#2379; &#2348;&#2367;&#2344;&#2366; &#2325;&#2379;&#2312; &#2360;&#2306;&#2346;&#2366;&#2352;&#2381;&#2358;&#2381;&#2357;&#2367;&#2325; (security) &#2352;&#2326;&#2375; &#2315;&#2339; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2326;&#2369;&#2354;&#2375; &#2348;&#2366;&#2332;&#2366;&#2352; &#2325;&#2366; &#2360;&#2306;&#2330;&#2366;&#2354;&#2344;: </strong><span style=\"font-weight: 400;\">&#2326;&#2369;&#2354;&#2375; &#2348;&#2366;&#2332;&#2364;&#2366;&#2352; &#2350;&#2375;&#2306; &#2360;&#2352;&#2325;&#2366;&#2352;&#2368; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367;&#2351;&#2379;&#2306; &#2325;&#2368; &#2326;&#2352;&#2368;&#2342; &#2324;&#2352; &#2348;&#2367;&#2325;&#2381;&#2352;&#2368;&#2404; </span><strong>&#2344;&#2325;&#2342; &#2310;&#2352;&#2325;&#2381;&#2359;&#2367;&#2340; &#2309;&#2344;&#2369;&#2346;&#2366;&#2340; (CRR)</strong><span style=\"font-weight: 400;\">: &#2351;&#2361; &#2332;&#2350;&#2366; &#2325;&#2366; &#2357;&#2361; &#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340; &#2361;&#2376; &#2332;&#2367;&#2360;&#2375; &#2357;&#2366;&#2339;&#2367;&#2332;&#2381;&#2351;&#2367;&#2325; &#2348;&#2376;&#2306;&#2325;&#2379;&#2306; &#2325;&#2379; &#2344;&#2325;&#2342;&#2368; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2352;&#2367;&#2332;&#2352;&#2381;&#2357; &#2348;&#2376;&#2306;&#2325; &#2325;&#2375; &#2346;&#2366;&#2360; &#2352;&#2326;&#2344;&#2366; &#2310;&#2357;&#2358;&#2381;&#2351;&#2325; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which of the following is NOT a component of </span><span style=\"font-family: Cambria Math;\">investment ?</span></p>\n",
                    question_hi: "<p>7<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Addition to residential construction</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Money spent on purchasing shares of a company</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Machinery and equipment employed by a firm</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Inventories of raw materials stocked by a firm</span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2366;&#2360;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2358;&#2368;&#2344;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2325;&#2352;&#2339;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2352;&#2381;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2335;&#2381;&#2336;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2330;&#2381;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2330;&#2368;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b)<strong> Buying shares</strong> <strong>of a</strong> <strong>company-</strong> </span><span style=\"font-family: Cambria Math;\">It is not deemed an investment as it does not directly enhance productive capacity. Share purchase grants ownership in the company but not in its capital goods, which the company might acquire with share-sale funds. Component of Investment: Fixed </span><span style=\"font-family: Cambria Math;\">Investment and</span><span style=\"font-family: Cambria Math;\"> Inventory Investment. </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">b) </span><strong><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2344;&#2366;</span></strong><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;&#2343;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2380;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2350;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2366;&#2350;&#2367;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2306;&#2332;&#2368;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2360;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;&#2325;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;&#2381;&#2357;&#2375;&#2306;&#2335;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">The estimation of poverty line in India is based on the survey conducted by which of the following?</span></p>\n",
                    question_hi: "<p>8<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2352;&#2368;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2375;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Ministry of Finance</p>\n", "<p>Reserve Bank of India (RBI)</p>\n", 
                                "<p>Ministry of Rural Development</p>\n", "<p>National Statistical Office (NSO)</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2340;&#2381;&#2352;&#2366;&#2306;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2367;&#2332;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> (RBI) </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2352;&#2366;&#2350;&#2368;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-weight: 400;\">National Statistical Office (NSO)</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d)<strong> National Statistical Office (NSO)</strong>. </span><span style=\"font-family: Cambria Math;\">Poverty estimation in India is carried out by NITI Aayog\'s task force through the calculation of the poverty line based on data captured by the National Sample Survey Office (NSSO) under the Ministry of Statistics and Programme Implementation (MOSPI). Post-Independence Estimation of Poverty</span><span style=\"font-family: Cambria Math;\">:-</span><span style=\"font-family: Cambria Math;\"> Alagh Committee (1979), Lakdawala Committee (1993), Tendulkar Committee (2005), C Rangarajan Committee (2012).</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d) </span><strong><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2306;&#2326;&#2381;&#2351;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> (NSO)</span></strong><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2352;&#2368;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> NITI </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2366;&#2360;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2379;&#2352;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2306;&#2326;&#2381;&#2351;&#2367;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2366;&#2344;&#2381;&#2357;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> (MOSPI) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2361;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2350;&#2370;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2375;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> (NSSO) </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2306;&#2325;&#2337;&#2364;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2352;&#2368;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2326;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2340;&#2306;&#2340;&#2381;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2352;&#2368;&#2348;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2325;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\">:- </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2328;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> (1979), </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2325;&#2337;&#2364;&#2366;&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> (1993), </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2375;&#2306;&#2342;&#2369;&#2354;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> (2005), </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2306;&#2327;&#2352;&#2366;&#2332;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> (2012)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9</span><span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\">The</span><span style=\"font-family: Cambria Math;\"> era of five year plans began in India with the establishment of_________.</span></p>\n",
                    question_hi: "<p>9<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2369;&#2327;</span><span style=\"font-family: Cambria Math;\"> _________</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2352;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2310;&#2404;</span></p>\n",
                    options_en: ["<p>The NITI Aayog</p>\n", "<p>The Parliament</p>\n", 
                                "<p>The Finance Commission</p>\n", "<p>The Planning Commission</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2360;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2340;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2327;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d) <strong>The Planning Commission</strong>: </span><span style=\"font-family: Cambria Math;\">Set up on 15 March 1950 and the plan era started from 1 April 1951 with the launch&shy;ing of the First Five Year Plan (1951-56). The Planning Commission was replaced by a think tank called NITI AAYOG in 2015 by Narendra Modi (Prime Minister). First Chairman of the Planning Commission - Pandit Jawaharlal Nehru. Last deputy chairman - Montek Singh Ahluwalia. First Five year plan was based on the Harrod Domar Model.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">d)</span><strong><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2327;</span></strong><span style=\"font-family: Cambria Math;\">: 15 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> 1950 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2349;&#2366;&#2352;&#2350;&#2381;&#2349;</span><span style=\"font-family: Cambria Math;\"> 1 </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2381;&#2352;&#2376;&#2354;</span><span style=\"font-family: Cambria Math;\"> 1951 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> (1951-56) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2369;&#2352;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2310;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 2015 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2379;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> NITI AAYOG </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2366;&#2350;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2367;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2337;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2357;&#2366;&#2361;&#2352;&#2354;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;&#2361;&#2352;&#2370;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2367;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2360;&#2349;&#2366;&#2346;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2379;&#2306;&#2335;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2306;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2361;&#2354;&#2370;&#2357;&#2366;&#2354;&#2367;&#2351;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2341;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2352;&#2379;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2379;&#2350;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2377;&#2337;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which of the following statements is/are true about currency notes?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">i. They do not have intrinsic value.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ii. They can be called fiat money.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">iii. They are not legal tenders.</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2379;&#2335;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2340;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">i. </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2376;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> (Intrinsic value) </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ii. </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2327;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2342;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">iii. </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2376;&#2343;&#2366;&#2344;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    options_en: ["<p>Only ii</p>\n", "<p>Only i</p>\n", 
                                "<p>Only i and ii</p>\n", "<p>Only ii and iii</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> ii</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">i</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">i</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ii</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">ii</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">iii</span></p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(c)<strong> Only i and ii. </strong></span><span style=\"font-family: Cambria Math;\">The Reserve Bank of India (RBI) prints and manages currency in India, whereas the Indian government regulates what denominations to circulate. The Indian government is solely responsible for minting coins. The symbol (&#8377;) of Indian Rupee was accepted by the Government of India on 15th July 2010. The symbol of Indian Rupee is designed by Udaya Kumar.</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(c) </span><strong><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> i </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span></strong><span style=\"font-family: Cambria Math;\"><strong> ii</strong> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2367;&#2332;&#2364;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> (RBI) </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2342;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2346;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2348;&#2306;&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;&#2357;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2360;&#2366;&#2352;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2325;&#2381;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2338;&#2366;&#2354;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;&#2342;&#2366;&#2351;&#2367;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2340;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2368;&#2325;</span><span style=\"font-family: Cambria Math;\"> (&#8377;) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 15 </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2369;&#2354;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 2010 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2368;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2368;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2350;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2367;&#2332;&#2366;&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Net domestic product is calculated as________.</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2352;</span><span style=\"font-family: Nirmala UI;\">&#2375;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2339;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> ________ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    options_en: ["<p>GDP- Depreciation</p>\n", "<p>GNP - Depreciation</p>\n", 
                                "<p>GDP-Net income earned from abroad</p>\n", "<p>GNP-Net income earned from abroad</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2337;&#2368;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;&#2361;&#2381;&#2352;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2319;&#2344;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;&#2361;&#2381;&#2352;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2337;&#2368;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2319;&#2344;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> - </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span></p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(a)&nbsp;</span><strong>GDP- Depreciation. Net domestic product (NDP)</strong><span style=\"font-weight: 400;\">: A yearly measure of a country\'s economic output. </span><strong>GDP (Gross Domestic Product) </strong><span style=\"font-weight: 400;\">- The monetary measurement of the total market value of all the finished goods and services that nations manufacture within their borders and sell over a given time period. </span><strong>Net National Product </strong><span style=\"font-weight: 400;\">(NNP): An economy is the GNP after deducting the loss due to depreciation. NNP = GNP &ndash; Depreciation. Gross National Product (GNP): The value of all final goods and services produced by the residents of a country in a financial year.&nbsp;</span></p>\n",
                    solution_hi: "<p>11.(a)<span style=\"font-weight: 400;\">&nbsp;</span><strong>GDP - &#2350;&#2370;&#2354;&#2381;&#2351;&#2361;&#2381;&#2352;&#2366;&#2360;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2358;&#2369;&#2342;&#2381;&#2343; &#2328;&#2352;&#2375;&#2354;&#2370; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342; (NDP)</strong><span style=\"font-weight: 400;\">: &#2325;&#2367;&#2360;&#2368; &#2342;&#2375;&#2358; &#2325;&#2375; &#2310;&#2352;&#2381;&#2341;&#2367;&#2325; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2325;&#2366; &#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325; &#2350;&#2366;&#2346; &#2361;&#2376; &#2404; </span><strong>GDP (&#2360;&#2325;&#2354; &#2328;&#2352;&#2375;&#2354;&#2370; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;) </strong><span style=\"font-weight: 400;\">- &#2360;&#2349;&#2368; &#2340;&#2376;&#2351;&#2366;&#2352; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2324;&#2352; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2375; &#2325;&#2369;&#2354; &#2348;&#2366;&#2332;&#2366;&#2352; &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2366; &#2350;&#2380;&#2342;&#2381;&#2352;&#2367;&#2325; &#2350;&#2366;&#2346; &#2332;&#2379; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352; &#2309;&#2346;&#2344;&#2368; &#2360;&#2368;&#2350;&#2366;&#2323;&#2306; &#2325;&#2375; &#2349;&#2368;&#2340;&#2352; &#2348;&#2344;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2319;&#2325; &#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2360;&#2350;&#2351; &#2309;&#2357;&#2343;&#2367; &#2350;&#2375;&#2306; &#2348;&#2375;&#2330;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2358;&#2369;&#2342;&#2381;&#2343; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</strong><span style=\"font-weight: 400;\"> (NNP): &#2350;&#2370;&#2354;&#2381;&#2351;&#2361;&#2381;&#2352;&#2366;&#2360; &#2325;&#2375; &#2325;&#2366;&#2352;&#2339; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2344;&#2369;&#2325;&#2360;&#2366;&#2344; &#2325;&#2379; &#2328;&#2335;&#2366;&#2344;&#2375; &#2325;&#2375; &#2348;&#2366;&#2342; &#2319;&#2325; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; GNP &#2361;&#2376;&#2404; NNP = GNP - &#2350;&#2370;&#2354;&#2381;&#2351;&#2361;&#2381;&#2352;&#2366;&#2360;&#2404; &#2360;&#2325;&#2354; &#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342; (GNP ): &#2319;&#2325; &#2357;&#2367;&#2340;&#2381;&#2340;&#2368;&#2351; &#2357;&#2352;&#2381;&#2359; &#2350;&#2375;&#2306; &#2325;&#2367;&#2360;&#2368; &#2342;&#2375;&#2358; &#2325;&#2375; &#2344;&#2367;&#2357;&#2366;&#2360;&#2367;&#2351;&#2379;&#2306; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340; &#2360;&#2349;&#2368; &#2309;&#2306;&#2340;&#2367;&#2350; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2324;&#2352; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2366; &#2350;&#2370;&#2354;&#2381;&#2351;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Which of these is a component of the tertiary sector of an economy?</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2371;&#2340;&#2368;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Mining and quarrying</p>\n", "<p>Water supply</p>\n", 
                                "<p>Electricity</p>\n", "<p>Hotels and restaurants</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2326;&#2344;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2326;&#2344;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2346;&#2370;&#2352;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2332;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2335;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2344;&#2381;&#2335;</span></p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(d)</span><strong>&nbsp;Hotels and restaurants. </strong><span style=\"font-weight: 400;\">Sectors of Indian Economy: </span><strong>Primary sector </strong><span style=\"font-weight: 400;\">- Depends on the availability of natural resources to manufacture goods and execute various processes. Examples - Agriculture, Forestry, Daily, Mining, etc. </span><strong>Secondary sector</strong><span style=\"font-weight: 400;\"> - The sector of the economy that revolves around manufacturing. Examples - Construction, fabrication, assembly, distillation, and purification. </span><strong>Tertiary Sector</strong><span style=\"font-weight: 400;\"> (service sector) - It creates services rather than finished items. Examples - Trade, transport, communication, banking, education, health, tourism, insurance, etc.</span></p>\n",
                    solution_hi: "<p>12.(d)&nbsp;<strong>&#2361;&#2379;&#2335;&#2354; &#2324;&#2352; &#2352;&#2375;&#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2306;&#2335;</strong><span style=\"font-weight: 400;\">&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2375; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;: </span><strong>&#2346;&#2381;&#2352;&#2366;&#2341;&#2350;&#2367;&#2325; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2325;&#2375; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2324;&#2352; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2346;&#2381;&#2352;&#2325;&#2381;&#2352;&#2367;&#2351;&#2366;&#2323;&#2306; &#2325;&#2379; &#2344;&#2367;&#2359;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325; &#2360;&#2306;&#2360;&#2366;&#2343;&#2344;&#2379;&#2306; &#2325;&#2368; &#2313;&#2346;&#2354;&#2348;&#2381;&#2343;&#2340;&#2366; &#2346;&#2352; &#2344;&#2367;&#2352;&#2381;&#2349;&#2352; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2325;&#2371;&#2359;&#2367;, &#2357;&#2366;&#2344;&#2367;&#2325;&#2368;, &#2342;&#2376;&#2344;&#2367;&#2325;, &#2326;&#2344;&#2344;, &#2310;&#2342;&#2367;&#2404; </span><strong>&#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351;&#2325; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2325;&#2366; &#2357;&#2361; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2332;&#2379; &#2357;&#2367;&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2325;&#2375; &#2310;&#2360;&#2346;&#2366;&#2360; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339;, &#2360;&#2306;&#2352;&#2330;&#2344;&#2366;, &#2360;&#2306;&#2351;&#2379;&#2332;&#2344;, &#2310;&#2360;&#2357;&#2344; &#2324;&#2352; &#2358;&#2369;&#2342;&#2381;&#2343;&#2367;&#2325;&#2352;&#2339;&#2404; </span><strong>&#2340;&#2371;&#2340;&#2368;&#2351;&#2325; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</strong><span style=\"font-weight: 400;\"> (&#2360;&#2375;&#2357;&#2366; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;) - &#2351;&#2361; &#2340;&#2376;&#2351;&#2366;&#2352; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2325;&#2375; &#2348;&#2332;&#2366;&#2351; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;, &#2346;&#2352;&#2367;&#2357;&#2361;&#2344;, &#2360;&#2306;&#2330;&#2366;&#2352;, &#2348;&#2376;&#2306;&#2325;&#2367;&#2306;&#2327;, &#2358;&#2367;&#2325;&#2381;&#2359;&#2366;, &#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351;, &#2346;&#2352;&#2381;&#2351;&#2335;&#2344;, &#2348;&#2368;&#2350;&#2366; &#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Which of the following can be considered a part of parallel economy?</span></p>\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2360;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Under declared income.</p>\n", "<p>Value of domestic work done by the housewife.</p>\n", 
                                "<p>Payments made to domestic helpers or housekeepers.</p>\n", "<p>Payments for private tuition.</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2379;&#2359;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2404;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2371;&#2361;&#2339;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2352;&#2375;&#2354;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2328;&#2352;&#2375;&#2354;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2366;&#2351;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2341;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2313;&#2360;&#2325;&#2368;&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2351;&#2369;&#2358;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;&#2404;</span></p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(a) <strong>Under declared Income</strong>. Parallel Economy - </span><span style=\"font-family: Cambria Math;\">The functioning of an unsanctioned sector in the economy whose objectives run in opposite to the objectives of the official, sanctioned or legitimate sector. </span><strong><span style=\"font-family: Cambria Math;\">Types of Economic systems</span></strong><span style=\"font-family: Cambria Math;\"><strong>:</strong> Capitalist, Socialist and Mixed. </span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(a) </span><strong><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2379;&#2359;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">: </span></strong><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2360;&#2381;&#2357;&#2368;&#2325;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2367;&#2325;&#2366;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2368;&#2325;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2376;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2346;&#2352;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2354;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><strong><span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2381;&#2341;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2339;&#2366;&#2354;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span></strong><span style=\"font-family: Cambria Math;\"><strong>:</strong> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2306;&#2332;&#2368;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2332;&#2357;&#2366;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2358;&#2381;&#2352;&#2367;&#2340;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Match the following items with respect to money.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><img src=\"https://ssccglpinnacle.com/images/mceu_46185447411699255999237.png\" width=\"472\" height=\"131\"></span></p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2342;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/images/mceu_18349207111699258305301.png\" width=\"318\" height=\"179\"></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">i</span><span style=\"font-family: Cambria Math;\">-d, ii-c, iii-a, </span><span style=\"font-family: Cambria Math;\">iv</span><span style=\"font-family: Cambria Math;\">-b</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">i</span><span style=\"font-family: Cambria Math;\">-a, ii-d, iii-b, </span><span style=\"font-family: Cambria Math;\">iv</span><span style=\"font-family: Cambria Math;\">-c</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">i</span><span style=\"font-family: Cambria Math;\">-c, ii-b, iii-a, </span><span style=\"font-family: Cambria Math;\">iv</span><span style=\"font-family: Cambria Math;\">-d</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">i</span><span style=\"font-family: Cambria Math;\">-b, ii-c, iii-d, </span><span style=\"font-family: Cambria Math;\">iv</span><span style=\"font-family: Cambria Math;\">-a</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">i</span><span style=\"font-family: Cambria Math;\">-d, ii-c, iii-a, </span><span style=\"font-family: Cambria Math;\">iv</span><span style=\"font-family: Cambria Math;\">-b</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">i</span><span style=\"font-family: Cambria Math;\">-a, ii-d, iii-b, </span><span style=\"font-family: Cambria Math;\">iv</span><span style=\"font-family: Cambria Math;\">-c</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">i</span><span style=\"font-family: Cambria Math;\">-c, ii-b, iii-a, </span><span style=\"font-family: Cambria Math;\">iv</span><span style=\"font-family: Cambria Math;\">-d</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">i</span><span style=\"font-family: Cambria Math;\">-b, ii-c, iii-d, </span><span style=\"font-family: Cambria Math;\">iv</span><span style=\"font-family: Cambria Math;\">-a </span></p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(d)</span><strong>&nbsp;Money: </strong><span style=\"font-weight: 400;\">A medium of exchange that is centralized, generally accepted, recognized, and facilitates transactions of goods and services. </span><strong>Characteristics:</strong><span style=\"font-weight: 400;\"> Durable, easily recognizable, stability, portable. </span><strong>Functions: </strong><span style=\"font-weight: 400;\">Medium of exchange, A measure of Value, Standard of deferred payments, Store of value, Distribution of social income, Basic of Credit Creation, Liquidity.</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(d)&nbsp; </span><strong>&#2350;&#2369;&#2342;&#2381;&#2352;&#2366; :</strong><span style=\"font-weight: 400;\">&#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2325;&#2366; &#2319;&#2325; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2332;&#2379; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352;&#2368;&#2325;&#2371;&#2340; &#2361;&#2376;, &#2310;&#2350; &#2340;&#2380;&#2352; &#2346;&#2352; &#2360;&#2381;&#2357;&#2368;&#2325;&#2371;&#2340;, &#2350;&#2366;&#2344;&#2381;&#2351;&#2340;&#2366; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2361;&#2376; &#2324;&#2352; &#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306; &#2324;&#2352; &#2360;&#2375;&#2357;&#2366;&#2323;&#2306; &#2325;&#2375; &#2354;&#2375;&#2344;&#2342;&#2375;&#2344; &#2325;&#2379; &#2360;&#2369;&#2357;&#2367;&#2343;&#2366;&#2332;&#2344;&#2325; &#2348;&#2344;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366;&#2319;&#2305;</strong><span style=\"font-weight: 400;\">: &#2360;&#2381;&#2341;&#2367;&#2352;, &#2310;&#2360;&#2366;&#2344;&#2368; &#2360;&#2375; &#2346;&#2361;&#2330;&#2366;&#2344;&#2344;&#2375; &#2351;&#2379;&#2327;&#2381;&#2351;, &#2344;&#2367;&#2351;&#2350;&#2367;&#2340;&#2340;&#2366;, &#2310;&#2360;&#2366;&#2344;&#2368; &#2360;&#2375; &#2354;&#2375; &#2332;&#2366;&#2344;&#2375; &#2351;&#2379;&#2327;&#2381;&#2351;&#2404;</span><strong> &#2325;&#2366;&#2352;&#2381;&#2351;: </strong><span style=\"font-weight: 400;\">&#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2325;&#2366; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350;, &#2350;&#2370;&#2354;&#2381;&#2351; &#2325;&#2366;&nbsp; &#2350;&#2366;&#2344;, &#2310;&#2360;&#2381;&#2341;&#2327;&#2367;&#2340; &#2349;&#2369;&#2327;&#2340;&#2366;&#2344; &#2325;&#2366; &#2350;&#2366;&#2344;&#2325;, &#2350;&#2370;&#2354;&#2381;&#2351;&nbsp; &#2325;&#2366; &#2360;&#2306;&#2330;&#2351;, &#2360;&#2366;&#2350;&#2366;&#2332;&#2367;&#2325; &#2310;&#2351; &#2325;&#2366; &#2357;&#2367;&#2340;&#2352;&#2339;, &#2315;&#2339; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2325;&#2366; &#2350;&#2370;&#2354;, &#2340;&#2352;&#2354;&#2340;&#2366;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">During a boom, the deficit of the government will reduce automatically due to _______.</span></p>\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2375;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2366;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> _______ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>increase in tax revenue</p>\n", "<p>increase in GDP</p>\n", 
                                "<p>decrease in tax revenue</p>\n", "<p>decrease in GDP</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2352;&#2375;&#2354;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> (GDP) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2352;&#2375;&#2354;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> (GDP) </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2350;&#2368;</span></p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(a) <strong>increase in tax revenue. Budget Deficit: </strong><span style=\"font-weight: 400;\">When the government spends more than the revenue received, then this situation is called budgetary deficit, There are three types. </span><strong>Fiscal deficit</strong><span style=\"font-weight: 400;\"> = Total expenditures - Total receipts (exc</span><span style=\"font-weight: 400;\">luding borrowings). </span><strong>Revenue deficit </strong><span style=\"font-weight: 400;\">= Total revenue expenditure - Total revenue receipts. </span><strong>Primary deficit</strong><span style=\"font-weight: 400;\"> = Fiscal deficit - Interest payments.</span></span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(a) <strong>&#2325;&#2352; &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2350;&#2375;&#2306; &#2357;&#2371;&#2342;&#2381;&#2343;&#2367;&#2404; &#2348;&#2332;&#2335; &#2328;&#2366;&#2335;&#2366;: </strong><span style=\"font-weight: 400;\">&#2332;&#2348; &#2360;&#2352;&#2325;&#2366;&#2352; &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367; &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2357;&#2381;&#2351;&#2351; &#2325;&#2352;&#2340;&#2368; &#2340;&#2379; &#2311;&#2360; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2325;&#2379; &#2348;&#2332;&#2335;&#2368;&#2351; &#2328;&#2366;&#2335;&#2366; &#2325;&#2361;&#2366;&#2305; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2306;, &#2351;&#2361; &#2340;&#2368;&#2344; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2352;&#2366;&#2332;&#2325;&#2379;&#2359;&#2368;&#2351; &#2328;&#2366;&#2335;&#2366;</strong><span style=\"font-weight: 400;\"> = &#2325;&#2369;&#2354; &#2357;&#2381;&#2351;&#2351; - &#2325;&#2369;&#2354; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2366;&#2305; (&#2313;&#2343;&#2366;&#2352; &#2325;&#2379; &#2331;&#2379;&#2337;&#2364;&#2325;&#2352;)&#2404; </span><strong>&#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2328;&#2366;&#2335;&#2366;</strong><span style=\"font-weight: 400;\"> = &#2325;&#2369;&#2354; &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2357;&#2381;&#2351;&#2351; - &#2325;&#2369;&#2354; &#2352;&#2366;&#2332;&#2360;&#2381;&#2357; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340;&#2367;&#2351;&#2366;&#2305;&#2404; </span><strong>&#2346;&#2381;&#2352;&#2366;&#2341;&#2350;&#2367;&#2325; &#2328;&#2366;&#2335;&#2366;</strong><span style=\"font-weight: 400;\"> = &#2352;&#2366;&#2332;&#2325;&#2379;&#2359;&#2368;&#2351; &#2328;&#2366;&#2335;&#2366; - &#2348;&#2381;&#2351;&#2366;&#2332; &#2349;&#2369;&#2327;&#2340;&#2366;&#2344;&#2404;</span></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>