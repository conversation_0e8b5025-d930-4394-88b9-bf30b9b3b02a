<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. A sheet of paper is folded along the dotted line successively along the directions shown and is then punched in the last. How would this paper look when unfolded?<br><strong id=\"docs-internal-guid-49e9a91e-7fff-5a4d-7445-c78996cbb71d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcVNHvKyG6c_q_rC2Q8ifHw-fYdi0EVUYvCe3Q3W1lKx5d8K27jV8KHPtzI6dHs2DZXKFlunIKXMpwBSaqm1ataqjLLNWH37xsrHnpDZ09FSaDHqIskKDuBI3rKqcroXcj4O2htyw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"244\" height=\"75\"></strong></p>",
                    question_hi: "<p>1. कागज के एक टुकड़े को निर्देशों के अनुसार क्रमिक रूप से बिंदुदार रेखा पर से मोड़ा जाता है और फिर उस पर <br><strong id=\"docs-internal-guid-53bcb766-7fff-dc78-b461-b515052b6b5e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcVNHvKyG6c_q_rC2Q8ifHw-fYdi0EVUYvCe3Q3W1lKx5d8K27jV8KHPtzI6dHs2DZXKFlunIKXMpwBSaqm1ataqjLLNWH37xsrHnpDZ09FSaDHqIskKDuBI3rKqcroXcj4O2htyw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"244\" height=\"75\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-c9a6d442-7fff-73ff-6db9-25ed658ecfc6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf4_I08-b7T1HDL-8FMm9GIlAfvN0EuLERWtQ9Gf1VCTTljPh0zwZRlQqOO_o3eiFiGtU3lMd4py5g_SeqlnF4orAAXnfU6XfwAKEL7DL96JPhpFU74K5kumbjJGkT81p0sQ7AyRw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-cc94aeaf-7fff-8aa3-58d1-62eee697496d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddFt_rVsPrUzxyJVCWX9E3XG0WVuQ2kTRurBSrZDh1LQOVQaQ370cEkmsBs4ilmmod3f5ZqIFs_dzeAWpf68DNwY4ACzgpXdBEfEmD5qTfGdhNmdLtLgLzp3FxkmH8PvkJHqyWVQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"73\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-d1f5c594-7fff-abae-a7f5-195b27fdd2fb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcTVSK9KlCUeMPbX3FalJq2-lgbPSBQRvJsCOj5U1jeP3Uk-ijgyf3FuS3xvFJ02fxstOxbRA9UiwsOBbJVJT_LCfjoAXdOXo6tOXIhmzCdjvGAACWjsc1V4vvh_xZ0aCc4YoqSaQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-08960e61-7fff-c23e-115c-65b685d78a03\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdU77OfLOyMXdNmhsVHc-oYdY5XDsY4lu1e1WPhSliUM7B2-oPwknziXWm_RC53ZCHLr6JofhmUzfdCl6foyEKrCePaO2CN8IB2AlcxXRNPitUXZep_3u-bCB2IbbNfrh9lX8Bodg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"73\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-b6d560bb-7fff-f4b5-6a5c-266f880dbc89\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf4_I08-b7T1HDL-8FMm9GIlAfvN0EuLERWtQ9Gf1VCTTljPh0zwZRlQqOO_o3eiFiGtU3lMd4py5g_SeqlnF4orAAXnfU6XfwAKEL7DL96JPhpFU74K5kumbjJGkT81p0sQ7AyRw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-c9c29d9e-7fff-0a97-9e22-ea559547ab4e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddFt_rVsPrUzxyJVCWX9E3XG0WVuQ2kTRurBSrZDh1LQOVQaQ370cEkmsBs4ilmmod3f5ZqIFs_dzeAWpf68DNwY4ACzgpXdBEfEmD5qTfGdhNmdLtLgLzp3FxkmH8PvkJHqyWVQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"73\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-8677eaeb-7fff-2105-2913-be1cf4c844f7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcTVSK9KlCUeMPbX3FalJq2-lgbPSBQRvJsCOj5U1jeP3Uk-ijgyf3FuS3xvFJ02fxstOxbRA9UiwsOBbJVJT_LCfjoAXdOXo6tOXIhmzCdjvGAACWjsc1V4vvh_xZ0aCc4YoqSaQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-522e6a12-7fff-3911-521d-5e70791f50c6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdU77OfLOyMXdNmhsVHc-oYdY5XDsY4lu1e1WPhSliUM7B2-oPwknziXWm_RC53ZCHLr6JofhmUzfdCl6foyEKrCePaO2CN8IB2AlcxXRNPitUXZep_3u-bCB2IbbNfrh9lX8Bodg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"73\"></strong></p>"],
                    solution_en: "<p>1.(b)<br><strong id=\"docs-internal-guid-efbe4879-7fff-519c-58e8-900d487a0fa1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddFt_rVsPrUzxyJVCWX9E3XG0WVuQ2kTRurBSrZDh1LQOVQaQ370cEkmsBs4ilmmod3f5ZqIFs_dzeAWpf68DNwY4ACzgpXdBEfEmD5qTfGdhNmdLtLgLzp3FxkmH8PvkJHqyWVQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"73\"></strong></p>",
                    solution_hi: "<p>1.(b)<br><strong id=\"docs-internal-guid-efbe4879-7fff-519c-58e8-900d487a0fa1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXddFt_rVsPrUzxyJVCWX9E3XG0WVuQ2kTRurBSrZDh1LQOVQaQ370cEkmsBs4ilmmod3f5ZqIFs_dzeAWpf68DNwY4ACzgpXdBEfEmD5qTfGdhNmdLtLgLzp3FxkmH8PvkJHqyWVQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"73\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the letter cluster that is different. <br>Note: The odd one out is not based on the number of consonants/vowels or their position in the letter cluster</p>",
                    question_hi: "<p>2. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एकसमान हैं और एक उनसे असंगत है। उस असंगत अक्षर-समूह का चयन कीजिए। <br>नोट: अक्षर समूह में, असंगत व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: ["<p>ORTU</p>", "<p>XZCH</p>", 
                                "<p>MORW</p>", "<p>PRUZ</p>"],
                    options_hi: ["<p>ORTU</p>", "<p>XZCH</p>",
                                "<p>MORW</p>", "<p>PRUZ</p>"],
                    solution_en: "<p>2.(a)<br><strong id=\"docs-internal-guid-64efe895-7fff-f3dc-a253-8f6cab3dcb2c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcCDQUC3VpWTo2ZqYwP93AgKdf7nIeO7OVrj0NgHDapS1I2XhaAHivd8C3401XNJzaBQHoW_aHk1GNzVXV9DeWo1SnWlTblS-XsmGTSA2VTEhKaEfh0lo-ye8qMq-_uEbZ5_hDF5g?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"109\" height=\"50\"></strong><br><strong id=\"docs-internal-guid-febc9625-7fff-fdc1-70c4-f2ca43d2fad7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdd4jm6Xbn6U9aNVEQgqsUYr3Q2-cDbw30aVPLM-hwqgdQm_DSFYfc4db9bWGajdfAqyYlkdyLyVfZm7X2SeXq13-t3das-lq5Xgu8xwzXxBrxeArGYBD7hV5L1UTeJjL5RVdvPKQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"109\" height=\"50\"></strong><br><strong id=\"docs-internal-guid-f709deb0-7fff-05b3-7646-8238bede8336\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe2vVzw7AC7Q7N8t_LCuRkhjIydcZD2En9ofamy01Y5ncxv3n4ANnglrMZXVOv7wIJFC8ERnd8N5mZ3Mtmnm38jkT6PIiptbM3nOTdplfIfanVswira2awJiySOgyWDLj8ReXEk_w?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"110\" height=\"50\"></strong><br>but<br><strong id=\"docs-internal-guid-61e5a166-7fff-10d6-c74b-fc9c61da0402\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc3yQ0nOx5L1di01LSzgbOA0mb_hwJayTMDbt9RsVhdPslUAvyxMb-59w9icegvFYdPpBYuPDp4Udf_vZFq6Uje4xoWP_XdBznfxPOgs05A4a7wgoYzZ8RuFbW4VX_DsM9W1c8e-A?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"109\" height=\"50\"></strong></p>",
                    solution_hi: "<p>2.(a)<br><strong id=\"docs-internal-guid-d1f3f2bd-7fff-ca14-70b7-c830d11e9cc9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcCDQUC3VpWTo2ZqYwP93AgKdf7nIeO7OVrj0NgHDapS1I2XhaAHivd8C3401XNJzaBQHoW_aHk1GNzVXV9DeWo1SnWlTblS-XsmGTSA2VTEhKaEfh0lo-ye8qMq-_uEbZ5_hDF5g?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"109\" height=\"50\"></strong><br><strong id=\"docs-internal-guid-96b3af69-7fff-d15c-af5c-a178f31fbd70\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdd4jm6Xbn6U9aNVEQgqsUYr3Q2-cDbw30aVPLM-hwqgdQm_DSFYfc4db9bWGajdfAqyYlkdyLyVfZm7X2SeXq13-t3das-lq5Xgu8xwzXxBrxeArGYBD7hV5L1UTeJjL5RVdvPKQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"109\" height=\"50\"></strong><br><strong id=\"docs-internal-guid-ba3d0f8c-7fff-d80d-f574-13c753ad4e71\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe2vVzw7AC7Q7N8t_LCuRkhjIydcZD2En9ofamy01Y5ncxv3n4ANnglrMZXVOv7wIJFC8ERnd8N5mZ3Mtmnm38jkT6PIiptbM3nOTdplfIfanVswira2awJiySOgyWDLj8ReXEk_w?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"110\" height=\"50\"></strong><br>लेकिन <br><strong id=\"docs-internal-guid-b158b231-7fff-df9e-9341-d360a843f04e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc3yQ0nOx5L1di01LSzgbOA0mb_hwJayTMDbt9RsVhdPslUAvyxMb-59w9icegvFYdPpBYuPDp4Udf_vZFq6Uje4xoWP_XdBznfxPOgs05A4a7wgoYzZ8RuFbW4VX_DsM9W1c8e-A?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"109\" height=\"50\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. In a certain code language, &lsquo;D&rsquo; is coded as &lsquo;16&rsquo;, &lsquo;B&rsquo; is coded as &lsquo;4&rsquo; and &lsquo;T&rsquo; is coded as &lsquo;400&rsquo;. How will &lsquo;H&rsquo; be coded in the same language?</p>",
                    question_hi: "<p>3. एक निश्चित कूट भाषा में, \'D\' को \'16\' के रूप में कूटबद्ध किया जाता है, \'B\' को \'4\' के रूप में कूटबद्ध किया जाता है और \'T\' को \'400\' के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'H\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>8</p>", "<p>64</p>", 
                                "<p>25</p>", "<p>36</p>"],
                    options_hi: ["<p>8</p>", "<p>64</p>",
                                "<p>25</p>", "<p>36</p>"],
                    solution_en: "<p>3.(b)<br><strong>Logic</strong> :- number = (alphabetical place value)<sup>2</sup><br>D &rarr; (4)<sup>2</sup> = 16<br>B &rarr; (2)<sup>2</sup> = 4<br>T &rarr; (20)<sup>2</sup> = 400<br>Similarly<br>H &rarr; (8)<sup>2</sup> = 64</p>",
                    solution_hi: "<p>3.(b)<br><strong>तर्क</strong> :- संख्या = (वर्णमाला में स्थानीय मान)<sup>2</sup><br>D &rarr; (4)<sup>2</sup> = 16<br>B &rarr; (2)<sup>2</sup> = 4<br>T &rarr; (20)<sup>2</sup> = 400<br>इसी प्रकार <br>H &rarr; (8)<sup>2</sup> = 64</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, <br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;; <br>&lsquo;A &minus; B&rsquo; means &lsquo;A is the brother of B&rsquo;; <br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo; and <br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the father of B&rsquo;. <br>Based on the above, how is I related to H if &lsquo;B + R &times; I &minus; T &divide; H&rsquo;?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में, <br>&lsquo;A + B&rsquo; का अर्थ है &lsquo;A, B की माता है&rsquo;; <br>&lsquo;A &minus; B&rsquo; का अर्थ है &lsquo;A, B का भाई है&rsquo;; <br>&lsquo;A &times; B&rsquo; का अर्थ है &lsquo;A,&nbsp; B&rsquo; की पत्नी है&rsquo; और <br>&lsquo;A &divide; B&rsquo; का अर्थ है &lsquo;A,&nbsp; B&rsquo; का पिता है&rsquo; <br>उपरोक्त के आधार पर, यदि &lsquo;B + R &times; I &minus; T &divide; H&rsquo; है, तो I का H से क्या सम्बन्ध है ?</p>",
                    options_en: ["<p>Father&rsquo;s father</p>", "<p>Father&rsquo;s sister</p>", 
                                "<p>Brother</p>", "<p>Father&rsquo;s brother</p>"],
                    options_hi: ["<p>पिता के पिता</p>", "<p>पिता की बहन</p>",
                                "<p>भाई</p>", "<p>पिता का भाई</p>"],
                    solution_en: "<p>4.(d)<br><strong id=\"docs-internal-guid-b8c81963-7fff-bc31-2a3b-fa7e3752163b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd0GZITp__LpAEoN9Fq0sq9rXxpPJ53cXefZPBtGnVZCqYFSNdR8tWNNO58BuWHuclbqKQfSo1s4P6g62Dr9tJiFKaNRvJD-8rAHoTxVp1Viw5vCOK9ieEXcKnbuynSefHWVRGq?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"194\" height=\"137\"></strong><br>Hence, &lsquo;I&rsquo; is the father&rsquo;s brother of H.</p>",
                    solution_hi: "<p>4.(d)<br><strong id=\"docs-internal-guid-b8c81963-7fff-bc31-2a3b-fa7e3752163b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd0GZITp__LpAEoN9Fq0sq9rXxpPJ53cXefZPBtGnVZCqYFSNdR8tWNNO58BuWHuclbqKQfSo1s4P6g62Dr9tJiFKaNRvJD-8rAHoTxVp1Viw5vCOK9ieEXcKnbuynSefHWVRGq?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"194\" height=\"137\"></strong><br>अतः, \'I\' H के पिता का भाई है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Each of the letters in the word &lsquo;DARKEN&rsquo; is arranged in the English alphabetical order. How many letters are there in the English alphabetical series between the letter which is second from the left end and the one which is third from the right end in the new letter-cluster thus formed?</p>",
                    question_hi: "<p>5. शब्द \'DARKEN\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाता है। इस प्रकार बने नए अक्षर-समूह में बाएँ छोर से दूसरे अक्षर और दाएँ छोर से तीसरे अक्षर के बीच अँग्रेजी वर्णमाला श्रृंखला में कितने अक्षर हैं?</p>",
                    options_en: ["<p>Six</p>", "<p>Eight</p>", 
                                "<p>Five</p>", "<p>Seven</p>"],
                    options_hi: ["<p>छ:</p>", "<p>आठ</p>",
                                "<p>पाँच</p>", "<p>सात</p>"],
                    solution_en: "<p>5.(a)<br><strong>Given</strong> : - D A R K E N <br>After arranging alphabetically :- A D E K N R<br>Second alphabet from left end = D<br>Second alphabet from right end = K<br>No. of alphabet between D and K in alphabetical order = 6</p>",
                    solution_hi: "<p>5.(a)<br><strong>दिया गया है: </strong>- D A R K E N <br>वर्णानुक्रम से व्यवस्थित करने के बाद :- A D E K N R<br>बाएं छोर से दूसरा अक्षर = D<br>दाएं छोर से दूसरा अक्षर = K<br>वर्णमाला क्रम में D और K के बीच अक्षरों की संख्या = 6</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the number from among the given options that can replace the question mark (?) in the following series. <br>176, 167, 156, 147, 136, ?</p>",
                    question_hi: "<p>6. दिए गए विकल्पों में से उस संख्या का चयन कीजिए, जो निम्नलिखित श्रृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकती है।<br>176, 167, 156, 147, 136, ?</p>",
                    options_en: ["<p>127</p>", "<p>119</p>", 
                                "<p>126</p>", "<p>124</p>"],
                    options_hi: ["<p>127</p>", "<p>119</p>",
                                "<p>126</p>", "<p>124</p>"],
                    solution_en: "<p>6.(a)<br><strong id=\"docs-internal-guid-7485663b-7fff-2203-f846-b11f56d61ca3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXflcrU8zwo20mv3SX5navjDUwBp_VkdD6GsZt0xNWknpzjo6HgATApgSAsPepq7hpSf7hn5iYObNduN7f5R96wJreqCfbnD_oaHF47tVr8GMZDjFW-Lm2LjSUHWQbjoPsKo1zYe?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"268\" height=\"62\"></strong></p>",
                    solution_hi: "<p>6.(a)<br><strong id=\"docs-internal-guid-7485663b-7fff-2203-f846-b11f56d61ca3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXflcrU8zwo20mv3SX5navjDUwBp_VkdD6GsZt0xNWknpzjo6HgATApgSAsPepq7hpSf7hn5iYObNduN7f5R96wJreqCfbnD_oaHF47tVr8GMZDjFW-Lm2LjSUHWQbjoPsKo1zYe?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"268\" height=\"62\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<strong id=\"docs-internal-guid-24626fa2-7fff-9948-92c0-67cff05451af\"> </strong><br>(16, 7, 112) <br>(8, 12, 96)</p>",
                    question_hi: "<p>7. उस समुच्&zwj;चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार संबंधित हैं जिस प्रकार निम्नलिखित समुच्&zwj;चय की संख्याएँ संबंधित हैं। <br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।) <br>(16, 7, 112) <br>(8, 12, 96)</p>",
                    options_en: ["<p>(12, 5, 65)</p>", "<p>(14, 9, 128)</p>", 
                                "<p>(13, 7, 96)</p>", "<p>(15, 8, 120)</p>"],
                    options_hi: ["<p>(12, 5, 65)</p>", "<p>(14, 9, 128)</p>",
                                "<p>(13, 7, 96)</p>", "<p>(15, 8, 120)</p>"],
                    solution_en: "<p>7.(d)<br><strong>Logic:-</strong> 1<sup>st</sup>no. &times;&nbsp;2<sup>nd</sup>no. = 3<sup>rd</sup> no.<br>(16, 7, 112) :- 16 &times; 7 = 112<br>(8, 12, 96) :- 8 &times; 12 = 96<br>Similarly, <br>(15, 8, 120) :- 15 &times; 8 = 120</p>",
                    solution_hi: "<p>7.(d)<br><strong>तर्क:-</strong> पहली संख्या &times; दूसरी संख्या = तीसरी संख्या <br>(16, 7, 112) :- 16 &times; 7 = 112<br>(8, 12, 96) :- 8 &times; 12 = 96<br>इसी प्रकार, <br>(15, 8, 120) :- 15 &times; 8 = 120</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Select the combination of letters that when sequentially placed in the blanks of the given series will complete the series. <br>ABC_FG_JK_MOPQ_TUV_Y</p>",
                    question_hi: "<p>8. अक्षरों के उस संयोजन का चयन कीजिए, जो दी गई श्रृंखला के रिक्त स्थान में क्रमिक रूप से रखे जाने पर श्रृंखला को पूर्ण करेगा।<br>ABC_FG_JK_MOPQ_TUV_Y</p>",
                    options_en: ["<p>EHLRW</p>", "<p>DHLSX</p>", 
                                "<p>DHLRW</p>", "<p>EHLRX</p>"],
                    options_hi: ["<p>EHLRW</p>", "<p>DHLSX</p>",
                                "<p>DHLRW</p>", "<p>EHLRX</p>"],
                    solution_en: "<p>8.(a)<br><strong>Series</strong> <math display=\"inline\"><mo>&#8658;</mo></math> ABC<strong><span style=\"text-decoration: underline;\">E</span></strong>FG<strong><span style=\"text-decoration: underline;\">H</span></strong>JK<strong><span style=\"text-decoration: underline;\">L</span></strong>MOPQ<strong><span style=\"text-decoration: underline;\">R</span></strong>TUV<strong><span style=\"text-decoration: underline;\">W</span></strong>Y</p>",
                    solution_hi: "<p>8.(a)<br><strong>श्रृंखला</strong> <math display=\"inline\"><mo>&#8658;</mo></math> ABC<strong><span style=\"text-decoration: underline;\">E</span></strong>FG<strong><span style=\"text-decoration: underline;\">H</span></strong>JK<strong><span style=\"text-decoration: underline;\">L</span></strong>MOPQ<strong><span style=\"text-decoration: underline;\">R</span></strong>TUV<strong><span style=\"text-decoration: underline;\">W</span></strong>Y</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. How many triangles are there in the figure given below?<br><strong id=\"docs-internal-guid-6ec75559-7fff-9b5e-9865-d64f5e2861c7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcYtAaM5rBFWIWS-AVspW7LNJkat1z33BOvtRelJvr5K5eIoel5LyL58rH4Y0RmImvMzG71c55qOgQNQzbbxqP-XfkMuZZjuG0GuttBvJLeC_Mw8NEPoAtHDIYgqftLiKHUSnNmHw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"106\" height=\"97\"></strong></p>",
                    question_hi: "<p>9. नीचे दी गई आकृति में कितने त्रिभुज हैं?<br><strong id=\"docs-internal-guid-6ec75559-7fff-9b5e-9865-d64f5e2861c7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcYtAaM5rBFWIWS-AVspW7LNJkat1z33BOvtRelJvr5K5eIoel5LyL58rH4Y0RmImvMzG71c55qOgQNQzbbxqP-XfkMuZZjuG0GuttBvJLeC_Mw8NEPoAtHDIYgqftLiKHUSnNmHw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"106\" height=\"97\"></strong></p>",
                    options_en: ["<p>12</p>", "<p>8</p>", 
                                "<p>14</p>", "<p>10</p>"],
                    options_hi: ["<p>12</p>", "<p>8</p>",
                                "<p>14</p>", "<p>10</p>"],
                    solution_en: "<p>9.(d)<br><strong id=\"docs-internal-guid-9f1df1a6-7fff-0d9a-b18b-cfcf1e66061a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcO2B8PrLO2kA3alX8K3FZe6SQSZmVbJnyOKQaVccb4vDjL2Ns7N8wsyBikjOtC-rgGE5dDemgf221eg9JZt7TjsrYXOkZyZ_2Xf-aO1F65zIJRtDBAPcVSkS3-dzaEi3CykhQF?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"176\" height=\"173\"></strong><br>There are 10 triangles = ABC, AED, AFG, AIH, HKJ, KLQ, KMP, KMJ, PMJ, JON.</p>",
                    solution_hi: "<p>9.(d)<br><strong id=\"docs-internal-guid-9f1df1a6-7fff-0d9a-b18b-cfcf1e66061a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcO2B8PrLO2kA3alX8K3FZe6SQSZmVbJnyOKQaVccb4vDjL2Ns7N8wsyBikjOtC-rgGE5dDemgf221eg9JZt7TjsrYXOkZyZ_2Xf-aO1F65zIJRtDBAPcVSkS3-dzaEi3CykhQF?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"176\" height=\"173\"></strong><br>10 त्रिभुज हैं = ABC, AED, AFG, AIH, HKJ, KLQ, KMP, KMJ, PMJ, JON.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain code language, &lsquo;FUTURE&rsquo; is written as &lsquo;IXWXUH&rsquo; and &lsquo;ISLAND&rsquo; is written as &lsquo;LVODQG&rsquo;. How will &lsquo;JERSEY&rsquo; be written in that language?</p>",
                    question_hi: "<p>10. एक निश्चित कूट भाषा में, \'FUTURE\' को \'IXWXUH\' के रूप में लिखा जाता है और \'ISLAND\' को \'LVODQG\' के रूप में लिखा जाता है। उसी भाषा में &lsquo;JERSEY&rsquo; को किस प्रकार लिखा जाएगा?</p>",
                    options_en: ["<p>KGTVGA</p>", "<p>KHTUHA</p>", 
                                "<p>MHTVGB</p>", "<p>MHUVHB</p>"],
                    options_hi: ["<p>KGTVGA</p>", "<p>KHTUHA</p>",
                                "<p>MHTVGB</p>", "<p>MHUVHB</p>"],
                    solution_en: "<p>10.(d)<br><strong id=\"docs-internal-guid-59011a44-7fff-b175-5725-33ba4a8e3522\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcbVUFvYcXgMfokdI3g7g-GyDN8MWAbbqjdheQCfp1S47gGKhCFtZXHaFn_017tqtCs8OKcNOwjpuWMMZX1uUUIFTzUUvDoLJJt1N9IqMzs4PSTQddc3bYErvmjW51QAzr2ds0s?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"179\" height=\"79\"></strong><br>and <br><strong id=\"docs-internal-guid-1b14a514-7fff-bc9c-b513-0b84753b9245\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfVr2FgWMYELYcToF9_wCUxwGR4OWYN4KipsYvXU_kdXc0JViC0tLSMVZEcCjTRBmQuz8hsJuPEjiZ58oBQg-Qb0jPmUJ7nBIYAN8XtpOUURKkwW_9FwWItHQ6xQzGl_-pOp7pTZg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"180\" height=\"79\"></strong><br>Similarly<br><strong id=\"docs-internal-guid-bdd95ea8-7fff-cccc-be73-cc0dd6573d76\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfw8ZsQSpkxr2oqKnU6scqdgXj9pk5TiPYgFY1pLHzEPVw4z0r7Rnwr_J4qI6x6ivQIXAXblDwoGGsmvKf5Mt6E5UBVyNKibRJG3UGFL9sQ1wwceettp5tfgPFKSVerWBDnNJEoLA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"180\" height=\"94\"></strong></p>",
                    solution_hi: "<p>10.(d)<br><strong id=\"docs-internal-guid-91bf062b-7fff-f7c0-9b8f-7e38b5909e6b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcbVUFvYcXgMfokdI3g7g-GyDN8MWAbbqjdheQCfp1S47gGKhCFtZXHaFn_017tqtCs8OKcNOwjpuWMMZX1uUUIFTzUUvDoLJJt1N9IqMzs4PSTQddc3bYErvmjW51QAzr2ds0s?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"179\" height=\"79\"></strong><br>और<br><strong id=\"docs-internal-guid-14737b9d-7fff-07e3-d9d5-f5d0dc452c63\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfVr2FgWMYELYcToF9_wCUxwGR4OWYN4KipsYvXU_kdXc0JViC0tLSMVZEcCjTRBmQuz8hsJuPEjiZ58oBQg-Qb0jPmUJ7nBIYAN8XtpOUURKkwW_9FwWItHQ6xQzGl_-pOp7pTZg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"180\" height=\"79\"></strong><br>इसी प्रकार<br><strong id=\"docs-internal-guid-35e4b330-7fff-131f-925a-4fda7ad60078\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfw8ZsQSpkxr2oqKnU6scqdgXj9pk5TiPYgFY1pLHzEPVw4z0r7Rnwr_J4qI6x6ivQIXAXblDwoGGsmvKf5Mt6E5UBVyNKibRJG3UGFL9sQ1wwceettp5tfgPFKSVerWBDnNJEoLA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"180\" height=\"94\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Which of the four options will replace the question mark (?) in the following series? <br>FX32, EV38, DT44, CR50, ?</p>",
                    question_hi: "<p>11. चार विकल्पों में से कौन सा विकल्प निम्नलिखित श्रृंखला में प्रश्नचिह्न (?) के स्&zwj;थान पर आएगा?<br>FX32, EV38, DT44, CR50, ?</p>",
                    options_en: ["<p>BP56</p>", "<p>CR54</p>", 
                                "<p>CQ55</p>", "<p>DP55</p>"],
                    options_hi: ["<p>BP56</p>", "<p>CR54</p>",
                                "<p>CQ55</p>", "<p>DP55</p>"],
                    solution_en: "<p>11.(a)<br><strong id=\"docs-internal-guid-0d0319ad-7fff-5d6d-bb10-9d7d34466da9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdQvQvH468tmYW2BHxJJNpwwbTstfAE8V48u7Eubz5r8cOnyeqFf6ABhqonfqyS-jYdz8-LfY174EqOsZIDT92haGeht7UROkHG5To9beYoFHpabYjBFY0qM2equ1rbMwn3PftqmQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"301\" height=\"93\"></strong></p>",
                    solution_hi: "<p>11.(a)<br><strong id=\"docs-internal-guid-0d0319ad-7fff-5d6d-bb10-9d7d34466da9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdQvQvH468tmYW2BHxJJNpwwbTstfAE8V48u7Eubz5r8cOnyeqFf6ABhqonfqyS-jYdz8-LfY174EqOsZIDT92haGeht7UROkHG5To9beYoFHpabYjBFY0qM2equ1rbMwn3PftqmQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"301\" height=\"93\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the option figure in which the given figure (X) is embedded (rotation is NOT allowed).<br><strong id=\"docs-internal-guid-06e68954-7fff-cb3f-8493-3bf3e55f8da2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe0is09OpQe6xI5bjYWuFv3Fc41HZFS-YddkHYOKVn-EwtKQBaF2AY-iHGtQa21WprNl8jh4QDqLvGpuYqrxy7NPbA9INBY3Fwtrbh3iiuGDwsOicst2Au8Kb-OKQZVMRtlWQMF4w?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"89\" height=\"112\"></strong></p>",
                    question_hi: "<p>12. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति (X) सन्निहित है (आकृति को घुमाने की अनुमति नहीं है)।<br><strong id=\"docs-internal-guid-dfaf3444-7fff-3f7b-530c-b842829da2d1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXe0is09OpQe6xI5bjYWuFv3Fc41HZFS-YddkHYOKVn-EwtKQBaF2AY-iHGtQa21WprNl8jh4QDqLvGpuYqrxy7NPbA9INBY3Fwtrbh3iiuGDwsOicst2Au8Kb-OKQZVMRtlWQMF4w?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"89\" height=\"112\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-081c1dc8-7fff-e609-85b0-710a0ee94850\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdmT0cU72F-_ruxu55Njeq38MbGBNEI1xE3aD_B57__5FsiF42ooTq0WnQqUillM2y3Y6mKLagSUlABzxe3Ef1K7bv95IXHYVBfZdl1BiqqQEr6IXhMNsuqBYRcyfj1xX1-QXXQDw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-3f80d8b3-7fff-f99e-8f27-46a9aa17984e\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd6sCZOKgRtFcQ0nhcR7gVJ4k2TPXoyQWWCwokFC9GIhW73gRhtali4ApCN8r6fWKZPR2A7X-QXZucvR9uX2ffL646i-dihu-kasdTQEmOb78cmM9LipJNrfcl7NyK7-xduOth2uw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"72\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-487bf339-7fff-118d-f253-ad3f28343031\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXclu5tTkT3xLVZ_irlN8x3hJuRRPCOngiUcEnIE5kTe1XTHj-QFCgi4DDgBNJ1m22iQ6XQAVu9aw7nAoz3luNr8RGWwmAn-sBmTEpu77x3NcvBlTsjrsGdYUBMFLySO8wxX43eKlw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-ad007434-7fff-255e-2261-de48abd54f50\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXffHLMJFIhOGO0TGC5A5ZBgufRYMCsl_R6MGCexUKphtPtRPH0jNb3qViHiAvo_uXXLi17WGqedB2CVrevoI6GXHrkj1uAlRKkSATdLCFUwM_kRzVAkiACUl0qJdEzOPluDwVtRWA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-4dc0204f-7fff-5732-21c6-25d825a1e067\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdmT0cU72F-_ruxu55Njeq38MbGBNEI1xE3aD_B57__5FsiF42ooTq0WnQqUillM2y3Y6mKLagSUlABzxe3Ef1K7bv95IXHYVBfZdl1BiqqQEr6IXhMNsuqBYRcyfj1xX1-QXXQDw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-3eb831ca-7fff-6e8b-fab3-089d6e23d8ba\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd6sCZOKgRtFcQ0nhcR7gVJ4k2TPXoyQWWCwokFC9GIhW73gRhtali4ApCN8r6fWKZPR2A7X-QXZucvR9uX2ffL646i-dihu-kasdTQEmOb78cmM9LipJNrfcl7NyK7-xduOth2uw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"72\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-9ecaa9f7-7fff-96b4-f871-46614c20cd35\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXclu5tTkT3xLVZ_irlN8x3hJuRRPCOngiUcEnIE5kTe1XTHj-QFCgi4DDgBNJ1m22iQ6XQAVu9aw7nAoz3luNr8RGWwmAn-sBmTEpu77x3NcvBlTsjrsGdYUBMFLySO8wxX43eKlw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-f343023a-7fff-ac5e-afb2-7596de92f9cd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXffHLMJFIhOGO0TGC5A5ZBgufRYMCsl_R6MGCexUKphtPtRPH0jNb3qViHiAvo_uXXLi17WGqedB2CVrevoI6GXHrkj1uAlRKkSATdLCFUwM_kRzVAkiACUl0qJdEzOPluDwVtRWA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"72\"></strong></p>"],
                    solution_en: "<p>12.(b) <br><strong id=\"docs-internal-guid-03e65623-7fff-ca4f-fe63-cb637222df64\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd6sCZOKgRtFcQ0nhcR7gVJ4k2TPXoyQWWCwokFC9GIhW73gRhtali4ApCN8r6fWKZPR2A7X-QXZucvR9uX2ffL646i-dihu-kasdTQEmOb78cmM9LipJNrfcl7NyK7-xduOth2uw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"72\"></strong></p>",
                    solution_hi: "<p>12.(b) <br><strong id=\"docs-internal-guid-03e65623-7fff-ca4f-fe63-cb637222df64\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd6sCZOKgRtFcQ0nhcR7gVJ4k2TPXoyQWWCwokFC9GIhW73gRhtali4ApCN8r6fWKZPR2A7X-QXZucvR9uX2ffL646i-dihu-kasdTQEmOb78cmM9LipJNrfcl7NyK7-xduOth2uw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"72\"></strong></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language, &lsquo;bright colour rainbow&rsquo; is coded as &lsquo;mq bj st&rsquo; and &lsquo;sunny bright day&rsquo; is coded as &lsquo;nv bj fm&rsquo;. How is &lsquo;bright&rsquo; coded in that language?</p>",
                    question_hi: "<p>13. एक निश्चित कूट भाषा में, \'bright colour rainbow\' को \'mq bj st\' के रूप में कूटबद्ध किया जाता है और \'sunny bright day\' को \'nv bj fm\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'bright\' को किस प्रकार कूटबद्ध किया है?</p>",
                    options_en: ["<p>bj</p>", "<p>nv</p>", 
                                "<p>st</p>", "<p>mq</p>"],
                    options_hi: ["<p>bj</p>", "<p>nv</p>",
                                "<p>st</p>", "<p>mq</p>"],
                    solution_en: "<p>13.(a)<br><strong id=\"docs-internal-guid-652dc762-7fff-6be7-e55e-03ffe394e9f5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd2hw9696aZFuXlFNG0kWh8GWi3Q4nx3C-KXLW-Pv1REuxiQC-82c4XPL6BvmOtZUqRhPM3ll_Ur_XM8S5IWMgQeYpdmvs3qxuCC4SUSG-6XPN1ou395vdQxjn0saQl5vCj1mAR?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"305\" height=\"62\"></strong><br>Hence, the code for bright is bj.</p>",
                    solution_hi: "<p>13.(a)<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731555506374.png\" alt=\"rId17\"><br><strong id=\"docs-internal-guid-652dc762-7fff-6be7-e55e-03ffe394e9f5\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd2hw9696aZFuXlFNG0kWh8GWi3Q4nx3C-KXLW-Pv1REuxiQC-82c4XPL6BvmOtZUqRhPM3ll_Ur_XM8S5IWMgQeYpdmvs3qxuCC4SUSG-6XPN1ou395vdQxjn0saQl5vCj1mAR?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"305\" height=\"62\"></strong><br>अतः, Bright के लिए कूट bj है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series?<br><strong id=\"docs-internal-guid-a6121234-7fff-b1dc-4852-0ec9a25197e6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLN6_TQe9_lYA2JDuZjTz1FrGrBOa3_FkdBSpvcFPfuGAdt2CaCTQ0_YmywjEtd33Ko36_BWQpSXsSPQ1UqsrNs4nbNby7R3d6Fdt8tmUbXilo_rO2zZ3tnx03BVbtMSO6Z4v55w?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"362\" height=\"75\"></strong></p>",
                    question_hi: "<p>14. विकल्पों में दिए गए उस चित्र की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूर्ण हो जाएगी?<br><strong id=\"docs-internal-guid-106c6792-7fff-3096-47cb-c004a3e23459\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLN6_TQe9_lYA2JDuZjTz1FrGrBOa3_FkdBSpvcFPfuGAdt2CaCTQ0_YmywjEtd33Ko36_BWQpSXsSPQ1UqsrNs4nbNby7R3d6Fdt8tmUbXilo_rO2zZ3tnx03BVbtMSO6Z4v55w?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"362\" height=\"75\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-e409b2ab-7fff-cbdd-9193-58a964c6e2f9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdTlAlgfRQfyiaRk4JAK42p5RIT7dUZZz3E4YVm1T6xeHw7j4HgsT6DkRhtlt0grCxkD7iFRwQjnGK51rNsTlqmyrcDW-qhq_0H47zV3HXIw0MeyguRxgJbpNGI8mnjf7_6LJtU6A?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-25399691-7fff-e459-5022-15d8cb2359fd\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdS-WzEuXSERm4xFtnvAAJf672RC-wfNPDt2V_sjjK-w37dRT8r8xgM47qj1sH1erSyM_skbs4bWQ60vvX-DsFsAAY5ToMILfrzNayQ-Wl-oBtV6Priy0I0WIeAQo9P_KNEwnvjNg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"72\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-255b3dbf-7fff-f8ab-b568-600518671f11\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeB0qQFPFcwfwYDQDQUzAv1TZkvArxFU3Gx6Am3gKq1nGrQrkdX_7Cg0-vOF2oWzAkM5jHBsmma-JMYH2WdCLqxMhrA_m1FfxLs6wHlKF7_LmCiB_x9mYzOw45zUSRTzJf9sm7aSQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-9daa1a70-7fff-14d2-6b29-d5ef64ba6980\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfJziATEF3UU05ianb8-tWDK30bFOp67U4GH4-7KZFKwZLxEd_oWR-SHiYQccMthn7lGHBxnvKSdTfPOkPzqqGQE_tJSwkAYAj7aSwpW6PV0PdCEC2fgowN_vWQWqpR1nayTEYh0g?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"72\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-d1d954ce-7fff-6dd2-b329-80389ab9ec6f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdTlAlgfRQfyiaRk4JAK42p5RIT7dUZZz3E4YVm1T6xeHw7j4HgsT6DkRhtlt0grCxkD7iFRwQjnGK51rNsTlqmyrcDW-qhq_0H47zV3HXIw0MeyguRxgJbpNGI8mnjf7_6LJtU6A?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-a354a745-7fff-871f-5ef0-7cbe3fb235b1\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdS-WzEuXSERm4xFtnvAAJf672RC-wfNPDt2V_sjjK-w37dRT8r8xgM47qj1sH1erSyM_skbs4bWQ60vvX-DsFsAAY5ToMILfrzNayQ-Wl-oBtV6Priy0I0WIeAQo9P_KNEwnvjNg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"72\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-b6aa107f-7fff-9dfb-6224-9ca6218cfaf3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeB0qQFPFcwfwYDQDQUzAv1TZkvArxFU3Gx6Am3gKq1nGrQrkdX_7Cg0-vOF2oWzAkM5jHBsmma-JMYH2WdCLqxMhrA_m1FfxLs6wHlKF7_LmCiB_x9mYzOw45zUSRTzJf9sm7aSQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-82c9faae-7fff-b6ce-3803-ab97a25d7f12\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfJziATEF3UU05ianb8-tWDK30bFOp67U4GH4-7KZFKwZLxEd_oWR-SHiYQccMthn7lGHBxnvKSdTfPOkPzqqGQE_tJSwkAYAj7aSwpW6PV0PdCEC2fgowN_vWQWqpR1nayTEYh0g?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"72\"></strong></p>"],
                    solution_en: "<p>14.(d)<br><strong id=\"docs-internal-guid-82c9faae-7fff-b6ce-3803-ab97a25d7f12\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfJziATEF3UU05ianb8-tWDK30bFOp67U4GH4-7KZFKwZLxEd_oWR-SHiYQccMthn7lGHBxnvKSdTfPOkPzqqGQE_tJSwkAYAj7aSwpW6PV0PdCEC2fgowN_vWQWqpR1nayTEYh0g?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"72\"></strong></p>",
                    solution_hi: "<p>14.(d)<br><strong id=\"docs-internal-guid-82c9faae-7fff-b6ce-3803-ab97a25d7f12\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfJziATEF3UU05ianb8-tWDK30bFOp67U4GH4-7KZFKwZLxEd_oWR-SHiYQccMthn7lGHBxnvKSdTfPOkPzqqGQE_tJSwkAYAj7aSwpW6PV0PdCEC2fgowN_vWQWqpR1nayTEYh0g?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"72\" height=\"72\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. The second number in the given number pairs is obtained by performing certain mathematical operation(s) on the first number. The same operation(s) are followed in all the number pairs except one. Find that odd number pair. <br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>15. दिए गए संख्या युग्मों में दूसरी संख्या, पहली संख्या पर कुछ गणितीय संक्रिया/संक्रियाएँ करके प्राप्त की गई है। एक संख्या-युग्म को छोड़कर, शेष सभी पर उसी संक्रिया/संक्रियाओं का अनुपालन किया गया है। उस असंगत संख्या-युग्म का चयन कीजिए। <br>(नोट : संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: ["<p>34 : 44</p>", "<p>23 : 25</p>", 
                                "<p>13 : 16</p>", "<p>35 : 36</p>"],
                    options_hi: ["<p>34 : 44</p>", "<p>23 : 25</p>",
                                "<p>13 : 16</p>", "<p>35 : 36</p>"],
                    solution_en: "<p>15.(a)<br><strong>Logic:-</strong> 2<sup>nd </sup>no.&nbsp;is next perfect square to the 1<sup>st </sup>no.<br>(23 : 25) :- perfect square just after 23 = 25<br>(13 : 16) :- perfect square just after 13 = 16<br>(35 : 36) :- perfect square just after 35 = 36<br>but,<br>(34 : 44) :- perfect square just after 34 = 36 not 44</p>",
                    solution_hi: "<p>15.(a)<br><strong>तर्क:-</strong> दूसरी संख्या, पहली संख्या के तुरंत बाद का पूर्ण वर्ग है । <br>(23 : 25) :- 23 के ठीक बाद का पूर्ण वर्ग = 25<br>(13 : 16) :- 13 के ठीक बाद का पूर्ण वर्ग = 16<br>(35 : 36) :- 35 के ठीक बाद का पूर्ण वर्ग = 36<br>लेकिन, <br>(34 : 44) :- 34 के ठीक बाद का पूर्ण वर्ग = 36 होगा 44 नहीं ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Six numbers 5, 3, 6, 7, 9 and 2 are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the number on the face opposite to the one having 5.<br><strong id=\"docs-internal-guid-180df5be-7fff-9cce-bd6f-e93f89d8138f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeBYBxXzh7bh8-6RhkflE-zno4MQK7EOtRwMRkpIPsoBGnApoaBymwynrEXah3HpglQfi47ElyuapY1xnz4dzDnKjtPtHdGyqKcLlJgNFLCq_kA67CuVHYaCRBEF4Hr0fZFO5BYkw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"164\" height=\"98\"></strong></p>",
                    question_hi: "<p>16. एक पासे के विभिन्न फलकों पर छह संख्याएँ 5, 3, 6, 7, 9 और 2 अंकित हैं। इस पासे की दो स्थितियाँ नीचे दी गई आकृति में दर्शाई गई हैं। 5 वाले फलक के विपरीत फलक पर संख्या ज्ञात कीजिए। <br><strong id=\"docs-internal-guid-180df5be-7fff-9cce-bd6f-e93f89d8138f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeBYBxXzh7bh8-6RhkflE-zno4MQK7EOtRwMRkpIPsoBGnApoaBymwynrEXah3HpglQfi47ElyuapY1xnz4dzDnKjtPtHdGyqKcLlJgNFLCq_kA67CuVHYaCRBEF4Hr0fZFO5BYkw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"164\" height=\"98\"></strong></p>",
                    options_en: ["<p>9</p>", "<p>6</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>9</p>", "<p>6</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>16.(d)<br>From both the dice the opposite faces are -<br>3 &harr; 9 , 5 &harr; 2, 6 &harr; 7</p>",
                    solution_hi: "<p>16.(d)<br>दोनों पासों के विपरीत फलक हैं -<br>3 &harr; 9 , 5 &harr; 2, 6 &harr; 7</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Which of the following terms will replace the question mark (?) in the given series? <br>QGMW, VCPU, AYSS, ? , KQYO</p>",
                    question_hi: "<p>17. निम्नलिखित में से कौन-सा पद दी गई श्रृंखला में प्रश्&zwj;न चिह्न (?) का स्थान लेगा?<br>QGMW, VCPU, AYSS, ? , KQYO</p>",
                    options_en: ["<p>FUVQ</p>", "<p>FUXO</p>", 
                                "<p>FVQO</p>", "<p>FTUO</p>"],
                    options_hi: ["<p>FUVQ</p>", "<p>FUXO</p>",
                                "<p>FVQO</p>", "<p>FTUO</p>"],
                    solution_en: "<p>17.(a)<br><strong id=\"docs-internal-guid-f3a752c6-7fff-1d1e-1604-8b6639b772bc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJnZ9Zhqaodg5IDsUAyvktWLI5bupPRb0krgYdR6pybth7UNgxOknyBo5NSuqNMIONN6YxBFGu75uAUbxSzwhciL9oS1QjVcd30K-h0UF2BBnQcG0Xf24zHP57zaYuvezc9oujoA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"343\" height=\"117\"></strong></p>",
                    solution_hi: "<p>17.(a)<br><strong id=\"docs-internal-guid-f3a752c6-7fff-1d1e-1604-8b6639b772bc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdJnZ9Zhqaodg5IDsUAyvktWLI5bupPRb0krgYdR6pybth7UNgxOknyBo5NSuqNMIONN6YxBFGu75uAUbxSzwhciL9oS1QjVcd30K-h0UF2BBnQcG0Xf24zHP57zaYuvezc9oujoA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"343\" height=\"117\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets? <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>48 &ndash; 50 &ndash; 46 &ndash; 52; 37 &ndash; 39 &ndash; 35 &ndash; 41</p>",
                    question_hi: "<p>18. नीचे संख्याओं के दो समुच्चय दिए गए हैं। संख्याओं के प्रत्येक समुच्चय में, पहली संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर दूसरी संख्या प्राप्त होती है। इसी प्रकार, दूसरी संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर तीसरी संख्या प्राप्त होती है और इसी तरह आगे भी होती है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए समुच्चयों की तरह संक्रियाओं के समान समुच्चय का अनुसरण करता है? <br>(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 लीजिए - 13 पर की जाने वाली संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि केवल 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना, तथा 1 और 3 पर गणितीय संक्रिया करने की अनुमति नहीं है।) <br>48 &ndash; 50 &ndash; 46 &ndash; 52; 37 &ndash; 39 &ndash; 35 &ndash; 41</p>",
                    options_en: ["<p>21 &ndash; 23 &ndash; 29 &ndash; 27</p>", "<p>19 &ndash; 17 &ndash; 21 &ndash; 25</p>", 
                                "<p>26 &ndash; 28 &ndash; 24 &ndash; 30</p>", "<p>15 &ndash; 13 &ndash; 9 &ndash; 19</p>"],
                    options_hi: ["<p>21 &ndash; 23 &ndash; 29 &ndash; 27</p>", "<p>19 &ndash; 17 &ndash; 21 &ndash; 25</p>",
                                "<p>26 &ndash; 28 &ndash; 24 &ndash; 30</p>", "<p>15 &ndash; 13 &ndash; 9 &ndash; 19</p>"],
                    solution_en: "<p>18.(c)<br><strong>Logic;-</strong> 1<sup>st </sup>no.&nbsp;+ 2 = 2<sup>nd</sup> no. , 2<sup>nd</sup> no. - 4 = 3<sup>rd</sup> no., 3<sup>rd</sup> no. + 6 = 4<sup>th</sup> no.&nbsp;<br>(48 <math display=\"inline\"><mo>-</mo></math> 50 - 46 - 52) :-&nbsp; 48 + 2 = 50, 50 - 4 = 46, 46 + 6 = 52<br>(37 <math display=\"inline\"><mo>-</mo></math> 39 - 35 - 41) :-&nbsp; 37 + 2 = 39, 39 - 4 = 35, 35 + 6 = 41<br>Similarly<br>(26 <math display=\"inline\"><mo>-</mo></math> 28 - 24 - 30) :-&nbsp; 26 + 2 = 28, 28 - 4 = 24, 24 + 6 = 30</p>",
                    solution_hi: "<p>18.(c)<br><strong>तर्क</strong> ;<math display=\"inline\"><mo>-</mo></math> पहली संख्या + 2 = दूसरी संख्या , दूसरी संख्या - 4 = तीसरी संख्या , तीसरी संख्या + 6 = चौथी संख्या <br>(48 <math display=\"inline\"><mo>-</mo></math> 50 - 46 - 52) :- 48 + 2 = 50, 50 - 4 = 46, 46 + 6 = 52<br>(37 <math display=\"inline\"><mo>-</mo></math> 39 - 35 - 41) :- 37 + 2 = 39, 39 - 4 = 35, 35 + 6 = 41<br>इसी प्रकार <br>(26 <math display=\"inline\"><mo>-</mo></math> 28 - 24 - 30) :- 26 + 2 = 28, 28 - 4 = 24, 24 + 6 = 30</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Which figure should replace the question mark (?) if the following series were to be continued?<br><strong id=\"docs-internal-guid-bf03e0e3-7fff-c67d-3423-37a141342ea0\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd5bKwln-uTIdNh2drFUk5ozqSFcIg-K1j3ec4NEuo-IFO2iOkenVmJ2xzXWD2Vz9JivMEtTZXUUqZrm8fp6AF1TXKk0nvA66nC1n4YLJRzgA5Etc9J-dg-0_PNwOhooErkBllR6A?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"372\" height=\"76\"></strong></p>",
                    question_hi: "<p>19. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए? <br><strong id=\"docs-internal-guid-6fb294d0-7fff-5627-bf45-4751f599490d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd5bKwln-uTIdNh2drFUk5ozqSFcIg-K1j3ec4NEuo-IFO2iOkenVmJ2xzXWD2Vz9JivMEtTZXUUqZrm8fp6AF1TXKk0nvA66nC1n4YLJRzgA5Etc9J-dg-0_PNwOhooErkBllR6A?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"372\" height=\"76\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-8e0cdc56-7fff-f020-fa4f-2e8d770eba2c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXedFnaUqaDUQY13we5yqhDTBkw2BCUG-AVRJuCFdN7HG6TZo1k9nf2-UJH5dJhgW8G7xVUEli9Cj4823LXpQv6hN-uVIwUeGRCgbH2qoJ11NmNBodLy_a-pK1du-QvWGCJXB_fKoA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-1d7344f3-7fff-066f-b9f3-935ce6606521\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfYI__pHeJxFtIFI70yIlJpyAa5hJYBLny0OVXScU7lpWV9jOXRCYdnuCT3oqlcK2YuJcxnY1fV73kINJACDNSR4dOOu1b7rA2i7Oa7Zzcy5UITX5kamYaoNiFUdPPKvxBVT_xFJg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-65bf18c7-7fff-1ff4-71f8-0a457e3e133f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcACw6fRshbIXBpSNO5biONErMK6b5JVbzDorD0qvwyvhUtv6-JR17fSN9_YBxrh_1tOmgqBDdIIxYbbQeXNbxA9B9TO_Ohq01YQbOmomovLYKu4j-gqUgnLL-6M4-Evm1wZk7VOw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-c220edcc-7fff-3874-35eb-bb0fcd0cb127\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_zIVp-hRV5cbIl_ODDC1Y_Z6huz23i8EubeixNqSSxiijLGX-h448gPUPT3EaD81xCkQRFcVe1nMsU93rnIu2cJdEZo9L5nuFvyEsuo40mvNEZ2a7QBjiMTAH8joTrRk5jDxynw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-fa1af947-7fff-6bd6-69ef-6b2bb0066a0c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXedFnaUqaDUQY13we5yqhDTBkw2BCUG-AVRJuCFdN7HG6TZo1k9nf2-UJH5dJhgW8G7xVUEli9Cj4823LXpQv6hN-uVIwUeGRCgbH2qoJ11NmNBodLy_a-pK1du-QvWGCJXB_fKoA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-48a1560d-7fff-81b8-6c8b-8bfddb4815b9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfYI__pHeJxFtIFI70yIlJpyAa5hJYBLny0OVXScU7lpWV9jOXRCYdnuCT3oqlcK2YuJcxnY1fV73kINJACDNSR4dOOu1b7rA2i7Oa7Zzcy5UITX5kamYaoNiFUdPPKvxBVT_xFJg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-f1ec76da-7fff-97ce-95bd-c4f88d4d4a46\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcACw6fRshbIXBpSNO5biONErMK6b5JVbzDorD0qvwyvhUtv6-JR17fSN9_YBxrh_1tOmgqBDdIIxYbbQeXNbxA9B9TO_Ohq01YQbOmomovLYKu4j-gqUgnLL-6M4-Evm1wZk7VOw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>", "<p><strong id=\"docs-internal-guid-938c57a8-7fff-ae29-55e6-b3c1730926d6\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd_zIVp-hRV5cbIl_ODDC1Y_Z6huz23i8EubeixNqSSxiijLGX-h448gPUPT3EaD81xCkQRFcVe1nMsU93rnIu2cJdEZo9L5nuFvyEsuo40mvNEZ2a7QBjiMTAH8joTrRk5jDxynw?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>"],
                    solution_en: "<p>19.(a)<br><strong id=\"docs-internal-guid-78a85a4e-7fff-c298-5bee-20fea25a93dc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXedFnaUqaDUQY13we5yqhDTBkw2BCUG-AVRJuCFdN7HG6TZo1k9nf2-UJH5dJhgW8G7xVUEli9Cj4823LXpQv6hN-uVIwUeGRCgbH2qoJ11NmNBodLy_a-pK1du-QvWGCJXB_fKoA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>",
                    solution_hi: "<p>19.(a)<br><strong id=\"docs-internal-guid-78a85a4e-7fff-c298-5bee-20fea25a93dc\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXedFnaUqaDUQY13we5yqhDTBkw2BCUG-AVRJuCFdN7HG6TZo1k9nf2-UJH5dJhgW8G7xVUEli9Cj4823LXpQv6hN-uVIwUeGRCgbH2qoJ11NmNBodLy_a-pK1du-QvWGCJXB_fKoA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"73\" height=\"73\"></strong></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. &lsquo;Conceal&rsquo; is related to &lsquo;Disclose&rsquo; in the same way as &lsquo;Identical&rsquo; is related to &lsquo;________&rsquo;. <br>(The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word.)</p>",
                    question_hi: "<p>20. \'छिपाना\' (Conceal), \'प्रकट करना\' (Disclose) से उसी प्रकार संबंधित है, जिस प्रकार \'समरूप\' (Identical) \'________\' से संबंधित है। <br>(शब्दों को सार्थक हिंदी शब्द माना जाना चाहिए और शब्&zwj;दों को अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।</p>",
                    options_en: ["<p>Analogous</p>", "<p>Homogeneous</p>", 
                                "<p>Similar</p>", "<p>Distinct</p>"],
                    options_hi: ["<p>अनुरूप (Analogous)</p>", "<p>सजातीय (Homogeneous)</p>",
                                "<p>समान (Similar)</p>", "<p>भिन्&zwj;न (Distinct)</p>"],
                    solution_en: "<p>20.(d)<br>As &lsquo;Conceal&rsquo; is the antonym of &lsquo;Disclose&rsquo; similarly &lsquo;Identical&rsquo; is the antonym of &lsquo;Distinct&rsquo;</p>",
                    solution_hi: "<p>20.(d)<br>जिस प्रकार \'छिपाना\' &lsquo; (Conceal), \'प्रकट करने \' (&lsquo;Disclose&rsquo;) का विपरीतार्थी है उसी प्रकार \'समरूप\' (&lsquo;Identical&rsquo;), भिन्&zwj;न &lsquo;Distinct&rsquo;का विपरीतार्थी है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. What will come in the place of &lsquo;?&rsquo; in the following equation, if &lsquo;&divide;&rsquo; and &lsquo;&ndash;&lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; are interchanged? <br>7 + 4 &divide; 18 &ndash; 3 &times; 2 = ?</p>",
                    question_hi: "<p>21. निम्नलिखित समीकरण में, यदि \'&divide;\' और \'&ndash;\' को आपस में बदल दिया जाए तथा \'&times;\' और \'+\' को आपस में बदल दिया जाए, तो \'?\' के स्थान पर क्या आएगा?<br>7 + 4 &divide; 18 &ndash; 3 &times; 2 = ?</p>",
                    options_en: ["<p>16</p>", "<p>20</p>", 
                                "<p>18</p>", "<p>24</p>"],
                    options_hi: ["<p>16</p>", "<p>20</p>",
                                "<p>18</p>", "<p>24</p>"],
                    solution_en: "<p>21.(d)<br><strong>Given:-</strong> 7 + 4 &divide;&nbsp;18 - 3 &times; 2 = ?<br>As per given instruction after interchanging &lsquo;&divide;&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;+&rsquo; we get,<br>7 &times;&nbsp;4 - 18 &divide; 3 + 2 <br>28 -&nbsp;6 + 2 = 24</p>",
                    solution_hi: "<p>21.(d)<br><strong>दिया गया है:</strong>- 7 + 4 &divide;&nbsp;18 - 3 &times; 2 = ?<br>दिए गए निर्देश के अनुसार &lsquo;&divide;&rsquo; और &lsquo;-&rsquo; तथा &lsquo;&times;&rsquo; और &lsquo;+&rsquo; को आपस में बदलने के बाद हमें प्राप्त होता है,<br>7 &times;&nbsp;4 - 18 &divide; 3 + 2 <br>28 -&nbsp;6 + 2 = 24</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the set in which the numbers are related in the same way as are the numbers of the following set. <br>3 &mdash; 20 &mdash; 5 <br>4 &mdash; 17 &mdash; 3<br>NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>&nbsp;</p>\n<p>22. उस समुच्चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार से संबंधित हैं जिस प्रकार निम्नलिखित समुच्चय की संख्याएँ संबंधित हैं।&nbsp;<br>3 &mdash; 20 &mdash; 5 <br>4 &mdash; 17 &mdash; 3 <br>नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 को लें - 13 पर संक्रियाएं जैसे 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>8 &mdash; 36 &mdash; 11</p>", "<p>9 &mdash; 77 &mdash; 8</p>", 
                                "<p>11 &mdash; 28 &mdash; 5</p>", "<p>5 &mdash; 28 &mdash; 9</p>"],
                    options_hi: ["<p>8 &mdash; 36 &mdash; 11</p>", "<p>9 &mdash; 77 &mdash; 8</p>",
                                "<p>11 &mdash; 28 &mdash; 5</p>", "<p>5 &mdash; 28 &mdash; 9</p>"],
                    solution_en: "<p>22.(b)<br><strong>Logic:-</strong> (1<sup>st</sup> no. &times; 3<sup>rd</sup> no.) + 5 = 2<sup>nd</sup> no.<br>(3 - 20 - 5) :- (3 &times; 5) + 5 &rArr; 15 + 5 = 20<br>(4 - 17 - 3) :- (4 &times; 3) + 5 &rArr; 12 + 5 = 17<br>Similarly <br>(9 - 77 - 8) :- (9 &times; 8) + 5 &rArr; 72 + 5 = 77</p>",
                    solution_hi: "<p>22.(b)<br><strong>तर्क</strong> :- (पहली संख्या &times;&nbsp;तीसरी संख्या) + 5 = दूसरी संख्या <br>(3 - 20 - 5) :- (3 &times; 5) + 5 &rArr; 15 + 5 = 20<br>(4 - 17 - 3) :- (4 &times; 3) + 5 &rArr; 12 + 5 = 17<br>इसी प्रकार, <br>(9 - 77 - 8) :- (9 &times; 8) + 5 &rArr; 72 + 5 = 77</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the number that will come in the place of the question mark(?), if &lsquo;+&rsquo; and &lsquo; &ndash; &lsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged <br>11 &divide; 8 &ndash; 78 &times; 6 + 37 = ?</p>",
                    question_hi: "<p>23. यदि निम्&zwj;नलिखित समीकरण में \'+\' और \'-\' को आपस में बदल दिया जाए और \'&times;\' और \'&divide;\' को आपस में बदल दिया जाए, तो प्रश्न चिह्न (?) के स्थान पर कौन-सी संख्या आएगी? <br>11 &divide; 8 &ndash; 78 &times; 6 + 37 = ?</p>",
                    options_en: ["<p>64</p>", "<p>58</p>", 
                                "<p>62</p>", "<p>71</p>"],
                    options_hi: ["<p>64</p>", "<p>58</p>",
                                "<p>62</p>", "<p>71</p>"],
                    solution_en: "<p>23.(a)<br><strong>Given:-</strong> 11 &divide;&nbsp;8 - 78 &times; 6 + 37 = ?<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;-&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; we get,<br>11 &times;&nbsp;8 + 78 &divide; 6 - 37 <br>88 +&nbsp;13 - 37 = 64</p>",
                    solution_hi: "<p>23.(a)<br><strong>दिया गया है:</strong>- 11 &divide;&nbsp;8 - 78 &times; 6 + 37 = ?<br>दिए गए निर्देश के अनुसार &lsquo;+&rsquo; और &lsquo;-&rsquo; तथा &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदलने के बाद हमें प्राप्त होता है,<br>11 &times;&nbsp;8 + 78 &divide; 6 - 37 <br>88 + 13 - 37 = 64</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Read the given statements and conclusions carefully. You have to take the given statements to be true even if they seem to be at variance from commonly known facts. You have to decide which conclusion(s) logically follow(s) from the given statements. <br><strong>Statements:</strong> <br>All Grapes are Berries. <br>Some Berries are Juices. <br>All Juices are Sodas. <br><strong>Conclusions:</strong> <br>(I) Some Berries are Grapes. <br>(II) Some Sodas are Berries</p>",
                    question_hi: "<p>24. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़िए। आपको दिए गए कथनों को सत्य मानना है, भले ही वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों।आपको यह तय करना है कि कौन-सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता है/करते हैं। <br><strong>कथन:</strong> <br>सभी अंगूर, जामुन हैं। <br>कुछ जामुन, जूस हैं। <br>सभी जूस, सोडा हैं। <br><strong>निष्कर्ष:</strong> <br>(I) कुछ जामुन, अंगूर हैं। <br>(II) कुछ सोडा, जामुन हैं।</p>",
                    options_en: ["<p>Only conclusion (II) follows</p>", "<p>Both the conclusions (I) and (II) follow</p>", 
                                "<p>Only conclusion (I) follows</p>", "<p>Both the conclusions (I) and (II) do not follow</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (II) अनुसरण करता हैं</p>", "<p>निष्कर्ष (I) और (II) दोनों अनुसरण करते हैं</p>",
                                "<p>केवल निष्कर्ष (I) अनुसरण करता हैं</p>", "<p>निष्कर्ष (I) और (II) दोनों अनुसरण नहीं करते हैं</p>"],
                    solution_en: "<p>24.(b)<br><strong id=\"docs-internal-guid-87c6f3ad-7fff-a61c-aa42-1cedb28978c8\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeYCoHbancl4kOejFMU-A_wK3wvyj3b1orGbSYlRNM6axhUfygC5EH00C0mwivOEoBNkQEk7_F6E7kMyZ1XZRqTqtj2YRVJPqbqqsZrmSpnbPsuIg7cQlBnlaL4x5y01hN8uRBOHQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"258\" height=\"144\"></strong><br>Hence, both the conclusion (I) and (II) follow.</p>",
                    solution_hi: "<p>24.(b)<br><strong id=\"docs-internal-guid-f13a4769-7fff-b35f-b8cb-f865613e857f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdU5CUpxZHOO7L08yLhCcYMmvv1274TbPq67szIwlTDyhxyBo-dpfssUZC5yzZFcqR_jbNHgd2cC8s_Mw2_x7vJ8Eva8dXAZ3BTNmX14x9tljD5F8xd7rjaQqcim4kV2nv3I5mpEg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"252\" height=\"109\"></strong><br>अतः, निष्कर्ष (I) और (II) दोनों अनुसरण करते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Two different positions of the same dice with faces Z, G, M, F, H and S are shown below. Select the letter that will be on the face opposite to the one having G. <br><strong id=\"docs-internal-guid-6b85abc9-7fff-2831-a231-8f01ff7631ab\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcoopDfvFID6Ppb5oMz1Ql6a_9X6-4sXprrae0ZmZYJgODtI6CARPrbX5XIoOg0WiwItNx1tasCpRcF0WL2SAiET5sE00J_Hwq4a9XBiKUsvxF44UQNVSC2WjLSV5PBTrqvIhj0?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"159\" height=\"95\"></strong></p>",
                    question_hi: "<p>25. Z, G, M, F, H और S फलकों वाले एक ही पासे की दो अलग-अलग स्थितियाँ नीचे दिखाई गई हैं। उस अक्षर का चयन कीजिए जो G अक्षर वाले फलक के विपरीत फलक पर होगा। <br><strong id=\"docs-internal-guid-6b85abc9-7fff-2831-a231-8f01ff7631ab\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcoopDfvFID6Ppb5oMz1Ql6a_9X6-4sXprrae0ZmZYJgODtI6CARPrbX5XIoOg0WiwItNx1tasCpRcF0WL2SAiET5sE00J_Hwq4a9XBiKUsvxF44UQNVSC2WjLSV5PBTrqvIhj0?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"159\" height=\"95\"></strong></p>",
                    options_en: ["<p>H</p>", "<p>M</p>", 
                                "<p>S</p>", "<p>F</p>"],
                    options_hi: ["<p>H</p>", "<p>M</p>",
                                "<p>S</p>", "<p>F</p>"],
                    solution_en: "<p>25.(d)<br>From both the dice the opposite faces are <br>Z &harr; S , M &harr; H, G &harr; F</p>",
                    solution_hi: "<p>25.(d)<br>दोनों पासों के विपरीत फलक हैं <br>Z &harr; S , M &harr; H, G &harr; F</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. The concept of &lsquo;Independence of judiciary&rsquo; in the Indian Constitution is taken from the Constitution of:</p>",
                    question_hi: "<p>26. भारतीय संविधान में \'न्यायपालिका की स्वतंत्रता\' की अवधारणा _________ के संविधान से ली गई है|</p>",
                    options_en: ["<p>Ireland</p>", "<p>France</p>", 
                                "<p>The USA</p>", "<p>Britain</p>"],
                    options_hi: ["<p>आयरलैंड</p>", "<p>फ्रांस</p>",
                                "<p>संयुक्त राज्य अमेरिका</p>", "<p>ब्रिटेन</p>"],
                    solution_en: "<p>26.(c) <strong>The USA.</strong> Some features of the Indian Constitution borrowed - From USA : Fundamental Rights, President&rsquo;s impeachment process, Office of the Vice-President. From Britain: Parliamentary system, Single citizenship, Bicameralism. From Ireland: Directive Principles of State Policy, Election method for the President, Rajya Sabha nominations. From France: Ideals of Liberty, Equality, and Fraternity in the Preamble, Concept of a Republic.</p>",
                    solution_hi: "<p>26.(c) <strong>संयुक्त राज्य अमेरिका।</strong> भारतीय संविधान द्वारा अपनाई गई विशेषताएँ - अमेरिका : मौलिक अधिकार, राष्ट्रपति की महाभियोग प्रक्रिया, उपराष्ट्रपति का कार्यालय। ब्रिटेन: संसदीय प्रणाली, एकल नागरिकता, द्विसदनीयता। आयरलैंड: राज्य के नीति निर्देशक सिद्धांत, राष्ट्रपति के लिए चुनाव पद्धति, राज्यसभा नामांकन। फ्रांस: प्रस्तावना में स्वतंत्रता, समानता और बंधुत्व के आदर्श, गणतंत्र की अवधारणा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which state in India was the most transformed by the Green Revolution?</p>",
                    question_hi: "<p>27. हरित क्रांति से भारत के किस राज्य का सबसे अधिक रूपांतरण हुआ?</p>",
                    options_en: ["<p>Kerala</p>", "<p>Maharashtra</p>", 
                                "<p>West Bengal</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>केरल</p>", "<p>महाराष्ट्र</p>",
                                "<p>पश्चिम बंगाल</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>27.(d) <strong>Punjab.</strong> The Green Revolution primarily benefited Punjab, Haryana, and western Uttar Pradesh in the north, and Andhra Pradesh and Tamil Nadu in the south. It had minimal impact on the eastern region, including Assam, Bihar, West Bengal, Orissa, and the arid regions of western and southern India. Initiated by Norman Borlaug in the 1960s, known as the \'Father of the Green Revolution,\' his development of High Yielding Varieties (HYVs) of wheat earned him the Nobel Peace Prize in 1970.</p>",
                    solution_hi: "<p>27.(d) <strong>पंजाब।</strong> हरित क्रांति से मुख्य रूप से उत्तर में पंजाब, हरियाणा और पश्चिमी उत्तर प्रदेश तथा दक्षिण में आंध्र प्रदेश और तमिलनाडु को लाभ हुआ। असम, बिहार, पश्चिम बंगाल, उड़ीसा और पश्चिमी तथा दक्षिणी भारत के शुष्क क्षेत्रों सहित पूर्वी क्षेत्र पर इसका न्यूनतम प्रभाव पड़ा। 1960 के दशक में नॉर्मन बोरलॉग द्वारा शुरू की गई इस क्रांति को \'हरित क्रांति के जनक\' के रूप में जाना जाता है, तथा गेहूँ की उच्च उपज देने वाली किस्मों (HYV) के विकास के लिए उन्हें 1970 में नोबेल शांति पुरस्कार मिला।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Match the points under Column A with those under Column B.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731555510623.png\" alt=\"rId46\"></p>",
                    question_hi: "<p>28. कॉलम A में दिए गए बिंदुओं का कॉलम B के बिंदुओं से मिलान कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731555510750.png\" alt=\"rId47\"></p>",
                    options_en: ["<p>i-b, ii-c, iii-a, iv-d</p>", "<p>i-a, ii-b, iii-c, iv-d</p>", 
                                "<p>i-d, ii-c, iii-b, iv-a</p>", "<p>i-b, ii-a, iii-d, iv-c</p>"],
                    options_hi: ["<p>i-b, ii-c, iii-a, iv-d</p>", "<p>i-a, ii-b, iii-c, iv-d</p>",
                                "<p>i-d, ii-c, iii-b, iv-a</p>", "<p>i-b, ii-a, iii-d, iv-c</p>"],
                    solution_en: "<p>28.(d) <strong>i-b, ii-a, iii-d, iv-c.</strong> Hexapods, the largest clade of arthropods, include most existing arthropod species. Crustaceans, part of the subphylum Crustacea, are invertebrates with about 45,000 species worldwide. Myriapods, from the subphylum Myriapoda, consist of terrestrial arthropods like millipedes and centipedes, with around 13,000 species. Chelicerata, another arthropod subphylum, includes both terrestrial and marine species.</p>",
                    solution_hi: "<p>28.(d) <strong>i-b, ii-a, iii-d, iv-c</strong>. हेक्सापोड्स, आर्थ्रोपोड्स का सबसे बड़ा समूह है, जिसमें अधिकांश मौजूदा आर्थ्रोपोड प्रजातियां शामिल हैं। क्रस्टेशियन, उपसंघ क्रस्टेशिया का हिस्सा हैं, जो अकशेरुकी हैं जिनकी विश्व भर में लगभग 45,000 प्रजातियां हैं। माइरियापोडा उपसंघ से संबंधित माइरियापोड्स में मिलीपीड और सेंटीपीड जैसे स्थलीय आर्थ्रोपोड शामिल हैं, जिनकी लगभग 13,000 प्रजातियां हैं। चेलिसेराटा, एक अन्य आर्थ्रोपोड उपसंघ है, जिसमें स्थलीय और समुद्री दोनों प्रजातियां शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which of the following parts of the mountain will receive the most rainfall from moisture-laden winds?</p>",
                    question_hi: "<p>29. पर्वत के निम्नलिखित में से किस भाग में नमी युक्त हवाओं से सबसे अधिक वर्षा होगी?</p>",
                    options_en: ["<p>Windward side</p>", "<p>Leeward side</p>", 
                                "<p>Upward side</p>", "<p>Relief side</p>"],
                    options_hi: ["<p>हवा की दिशा में (Windward side)</p>", "<p>अनुवात दिशा में (Leeward side)</p>",
                                "<p>ऊपर की दिशा में (Upward side)</p>", "<p>उच्चावच दिशा में (Relief side)</p>"],
                    solution_en: "<p>29.(a) <strong>Windward side.</strong> The leeward side is the side facing away from the wind, while the windward side faces toward it. Orographic rain occurs on the windward side of mountains when moist air is forced to rise over a range, cooling and condensing into droplets. Mawsynram, a town in Meghalaya\'s East Khasi Hills, 60.9 kilometers from Shillong, holds the record for the highest rainfall in both India and the world.</p>",
                    solution_hi: "<p>29.(a) <strong>हवा की दिशा में (Windward side)।</strong> अनुवात दिशा वह दिशा है जो हवा से दूर की ओर होता है, जबकि हवा की दिशा हवा की ओर होता है। पर्वतीय वर्षा पहाड़ों के हवा की ओर वाले भाग में होती है, जब नम हवा को एक सीमा से ऊपर उठने के लिए बाध्य किया जाता है, तथा वह ठंडी होकर बूंदों में संघनित हो जाती है। शिलांग से 60.9 किलोमीटर दूर मेघालय के पूर्वी खासी हिल्स में स्थित मौसिनराम शहर भारत और विश्व दोनों में सबसे अधिक वर्षा का स्थान है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. In the context of vernier calliper, an internal jaw is used to measure:</p>",
                    question_hi: "<p>30. वर्नियर कैलीपर के संदर्भ में आंतरिक जबड़े (जॉ) का उपयोग निम्न के मापन के लिए किया जाता है:</p>",
                    options_en: ["<p>the length correct up to 1 mm</p>", "<p>the depth of a beaker</p>", 
                                "<p>the length of a rod and diameter of a sphere</p>", "<p>the internal diameter of a hollow cylinder and pipes</p>"],
                    options_hi: ["<p>लंबाई, जो 1 mm तक सटीक हो</p>", "<p>बीकर की गहराई</p>",
                                "<p>एक छड़ की लंबाई और एक गोले का व्यास</p>", "<p>एक खोखले बेलन और पाइप का आंतरिक व्यास</p>"],
                    solution_en: "<p>30.(d) A Vernier Caliper is a precision instrument used for accurate measurements in various applications. Key parts include- Depth Probe: Used for measuring the depth of objects or holes. Main Scale: Provides measurements in millimeters. Vernier Scale: Offers measurements with an accuracy of up to one decimal place in millimeters.</p>",
                    solution_hi: "<p>30.(d) वर्नियर कैलिपर एक सटीक उपकरण है जिसका उपयोग विभिन्न अनुप्रयोगों में सटीक माप के लिए किया जाता है। मुख्य भागों में शामिल हैं- गहराई की जांच: वस्तुओं या छिद्रों की गहराई मापने के लिए उपयोग किया जाता है। मुख्य पैमाना: मिलीमीटर में माप प्रदान करता है। वर्नियर स्केल: मिलीमीटर में एक दशमलव स्थान तक की सटीकता के साथ माप प्रदान करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Tsang Po and Jamuna are other names of which river?</p>",
                    question_hi: "<p>31. त्सांग पो और जमुना किस नदी के अन्य नाम हैं?</p>",
                    options_en: ["<p>Indus</p>", "<p>Ganga</p>", 
                                "<p>Narmada</p>", "<p>Brahmaputra</p>"],
                    options_hi: ["<p>सिंधु</p>", "<p>गंगा</p>",
                                "<p>नर्मदा</p>", "<p>ब्रह्मपुत्र</p>"],
                    solution_en: "<p>31.(d) <strong>The Brahmaputra</strong> River originates in Tibet as the Tsang Po, flows through Assam in India as the Brahmaputra, and merges with the Ganges in Bangladesh to form the Jamuna. The Indus River, also called Sindhu, has names like Sengge Chu in Tibetan, Abaseen in Pashto, and Shendu in Chinese, flowing through China, India, and Pakistan. The Ganga is known as Jahnavi, Bhagirathi, and Alaknanda. The Narmada, meaning \'Giver of Pleasure,\' is also called Reva or Narbada, and flows through Madhya Pradesh, Maharashtra, and Gujarat.</p>",
                    solution_hi: "<p>31.(d) <strong>ब्रह्मपुत्र</strong> नदी तिब्बत में त्सांग पो के नाम से निकलती है, भारत में असम में ब्रह्मपुत्र के नाम से बहती है और बांग्लादेश में गंगा के साथ मिलकर जमुना का निर्माण करती है। सिंधु नदी, जिसे सिंधु भी कहा जाता है, तिब्बती में सेंगगे चू, पश्तो में अबासीन और चीनी में शेंदु जैसे नाम हैं, जो चीन, भारत और पाकिस्तान से होकर बहती है। गंगा को जाह्नवी, भागीरथी और अलकनंदा के नाम से जाना जाता है। नर्मदा, जिसका अर्थ है \'सुख देने वाली\', को रेवा या नर्बदा भी कहा जाता है, और यह मध्य प्रदेश, महाराष्ट्र और गुजरात से होकर बहती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. What is the cost of the &lsquo;Nand Baba Milk Mission&rsquo; scheme launched by the Uttar Pradesh state government in 2023 to provide milk producers the facility of selling their milk at a reasonable price in villages through dairy co-operative societies?</p>",
                    question_hi: "<p>32. दुग्ध उत्पादकों को डेयरी सहकारी समितियों के माध्यम से गांवों में उचित मूल्य पर अपना दूध बेचने की सुविधा प्रदान करने के लिए उत्तर प्रदेश राज्य सरकार द्वारा 2023 में शुरू की गई \'नंद बाबा दुग्ध मिशन\' योजना की लागत क्या है?</p>",
                    options_en: ["<p>₹500 crore</p>", "<p>₹1,500 crore</p>", 
                                "<p>₹2,000 crore</p>", "<p>₹1,000 crore</p>"],
                    options_hi: ["<p>₹500 करोड़</p>", "<p>₹1,500 करोड़</p>",
                                "<p>₹2,000 करोड़</p>", "<p>₹1,000 करोड़</p>"],
                    solution_en: "<p>32.(d) <strong>₹1,000 crore.</strong> Under the Nand Baba Milk Mission, Mukhyamantri Swadeshi Gau Samvardhan Yojana is being run to encourage the purchase of improved indigenous breeds of cows from outside the state. The main objective of the scheme is to provide&nbsp;employment to the youth and women of rural areas in the state by engaging them in livestock farming, increasing the number of advanced breed cows in the state, and improving the breed of indigenous cows. The scheme was launched on June 6, 2023.</p>",
                    solution_hi: "<p>32.(d) <strong>₹1,000 करोड़।</strong> नंद बाबा दुग्ध मिशन के अंतर्गत राज्य के बाहर से उन्नत देशी नस्ल की गायों की खरीद को प्रोत्साहित करने के लिए मुख्यमंत्री स्वदेशी गौ संवर्धन योजना चलाई जा रही है। योजना का मुख्य उद्देश्य राज्य के ग्रामीण क्षेत्रों के युवाओं और महिलाओं को पशुधन पालन में लगाकर रोजगार प्रदान करना, राज्य में उन्नत नस्ल की गायों की संख्या में वृद्धि करना और देशी गायों की नस्ल में सुधार करना है। यह योजना 6 जून, 2023 को शुरू की गई थी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Medini Rai of Chanderi, Hasan Khan of Mewat and Mahmud Lodi joined Rana Sanga with their forces to fight against which of the following Mughal rulers?</p>",
                    question_hi: "<p>33. चंदेरी के मेदिनी राय, मेवात के हसन खान और महमूद लोदी निम्नलिखित में से किस मुग़ल शासक के खिलाफ लड़ने के लिए अपनी सेना सहित राणा सांगा के साथ मिल गए थे?</p>",
                    options_en: ["<p>Babur</p>", "<p>Akbar</p>", 
                                "<p>Aurangzeb</p>", "<p>Humayun</p>"],
                    options_hi: ["<p>बाबर</p>", "<p>अकबर</p>",
                                "<p>औरंगजेब</p>", "<p>हुमायूँ</p>"],
                    solution_en: "<p>33.(a) <strong>Babur.</strong> He was a descendant of Genghis Khan and Timur, founded the Mughal Empire in northern India, making Agra its power center. He achieved victories in these key battles: the Battle of Khanwa (1527), where he defeated Rana Sanga and the Rajput Confederation for dominance in northern India; the Battle of Chanderi (1528), where he defeated Medini Rai of Malwa; and the Battle of Ghagra (1529), where he defeated Mahmud Lodi.</p>",
                    solution_hi: "<p>33.(a) <strong>बाबर।</strong> वह चंगेज खान और तैमूर का वंशज था, उसने उत्तर भारत में मुगल साम्राज्य की स्थापना की, आगरा को अपना शक्ति केंद्र बनाया। उसने इन प्रमुख लड़ाइयों में जीत हासिल की: खानवा की लड़ाई (1527), जहाँ उसने उत्तर भारत में प्रभुत्व के लिए राणा सांगा और राजपूत संघ को हराया; चंदेरी की लड़ाई (1528), जहाँ उसने मालवा के मेदिनी राय को हराया; और घाघरा की लड़ाई (1529), जहाँ उसने महमूद लोदी को हराया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which singer from Assam was posthumously awarded the Bharat Ratna in 2019?</p>",
                    question_hi: "<p>34. वर्ष 2019 में असम के किस गायक/गायिका को मरणोपरांत भारत रत्न से सम्मानित किया गया था?</p>",
                    options_en: ["<p>Padmanav Bordoloi</p>", "<p>Rameshwar Pathak</p>", 
                                "<p>Archana Mahanta</p>", "<p>Bhupen Hazarika</p>"],
                    options_hi: ["<p>पद्मनाव बोरदोलोई</p>", "<p>रामेश्वर पाठक</p>",
                                "<p>अर्चना महंत</p>", "<p>भूपेन हजारिका</p>"],
                    solution_en: "<p>34.(d) <strong>Bhupen Hazarika</strong> was a renowned playback singer, lyricist, musician, poet, actor, and filmmaker from Assam, was affectionately known as Sudha Kontho (\"nectar-throated\"). Bharat Ratna 2019 awardees included Pranab Mukherjee, a senior Indian politician, and Nanaji Deshmukh, a social activist focused on education and rural self-reliance. The Non-Indians who receive the Bharat Ratna are -Khan Abdul Ghaffar Khan(1987) and Nelson Mandela (1990).</p>",
                    solution_hi: "<p>34.(d) <strong>भूपेन हजारिका</strong> असम के एक प्रसिद्ध पार्श्व गायक, गीतकार, संगीतकार, कवि, अभिनेता और फिल्म निर्माता थे, जिन्हें प्यार से सुधा कोंथो (\"अमृत-गले\") के नाम से जाना जाता था। भारत रत्न 2019 पुरस्कार विजेताओं में वरिष्ठ भारतीय राजनेता प्रणब मुखर्जी और शिक्षा और ग्रामीण आत्मनिर्भरता पर केंद्रित सामाजिक कार्यकर्ता नानाजी देशमुख शामिल थे। भारत रत्न पाने वाले गैर-भारतीय हैं - खान अब्दुल गफ्फार खान (1987) और नेल्सन मंडेला (1990)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Belur Math was founded by which of the following social reformers in British India?</p>",
                    question_hi: "<p>35. ब्रिटिश भारत में बेलूर मठ की स्थापना निम्नलिखित में से किस समाज सुधारक द्वारा की गई थी?</p>",
                    options_en: ["<p>Swami Vivekanand</p>", "<p>RG Bhandarkar</p>", 
                                "<p>VN Mandlik</p>", "<p>KT Telang</p>"],
                    options_hi: ["<p>स्वामी विवेकानन्द</p>", "<p>आर. जी. भंडारकर</p>",
                                "<p>वी. एन. मांडलिक</p>", "<p>के. टी. तेलंग</p>"],
                    solution_en: "<p>35.(a) <strong>Swami Vivekanand.</strong> Belur Math, situated in Belur on the west bank of the Hooghly River in West Bengal, India, serves as the headquarters for the Ramakrishna Mission and Ramakrishna Math. The architecture of this monastery showcases a blend of Hindu, Christian, and Islamic elements, symbolizing the unity of religions.</p>",
                    solution_hi: "<p>35.(a) <strong>स्वामी विवेकानंद।</strong> बेलूर मठ, भारत के पश्चिम बंगाल में हुगली नदी के पश्चिमी तट पर बेलूर में स्थित है, जो रामकृष्ण मिशन और रामकृष्ण मठ के मुख्यालय के रूप में कार्य करता है। इस मठ की वास्तुकला हिंदू, ईसाई और इस्लामी तत्वों का मिश्रण दर्शाती है, जो धर्मों की एकता का प्रतीक है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. &lsquo;Castling&rsquo; is related to which of the following sports?</p>",
                    question_hi: "<p>36. &lsquo;कैसलिंग (Castling) निम्नलिखित में से किस खेल से संबंधित है?</p>",
                    options_en: ["<p>Table Tennis</p>", "<p>Billiards</p>", 
                                "<p>Tennis</p>", "<p>Chess</p>"],
                    options_hi: ["<p>टेबल टेनिस</p>", "<p>बिलियर्ड्स</p>",
                                "<p>टेनिस</p>", "<p>शतरंज</p>"],
                    solution_en: "<p>36.(d) <strong>Chess.</strong> Castling is a unique chess move involving the king and one of the original rooks. Key chess terms include Bishop, Pawn, Knight, Rook, King, Checkmate, Queen, and Zugzwang.</p>",
                    solution_hi: "<p>36.(d) <strong>शतरंज।</strong> &lsquo;कैसलिंग एक अनोखी शतरंज चाल है जिसमें राजा और मूल हाथी में से एक का प्रयोग होता है। प्रमुख शतरंज शब्दावली में बिशप, पॉन, नाइट, रूक, किंग, चेकमेट, क्वीन और ज़ुगज़्वांग शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Who among the following was the shortest-serving Prime Minister of India?</p>",
                    question_hi: "<p>37. निम्नलिखित में से कौन भारत के सबसे कम समय तक सेवा करने वाले प्रधान मंत्री थे?</p>",
                    options_en: ["<p>Chandra Shekhar</p>", "<p>Gulzarilal Nanda</p>", 
                                "<p>Charan Singh</p>", "<p>Atal Bihari Vajpayee</p>"],
                    options_hi: ["<p>चंद्र शेखर</p>", "<p>गुलजारीलाल नंदा</p>",
                                "<p>चरण सिंह</p>", "<p>अटल बिहारी वाजपेयी</p>"],
                    solution_en: "<p>37.(b) <strong>Gulzarilal Nanda</strong> was an Indian politician and economist specializing in labor issues. He served as Acting Prime Minister of India for two 13-day tenures following the deaths of Jawaharlal Nehru in 1964 and Lal Bahadur Shastri in 1966. Chandra Shekhar, known as Jananayak, was Prime Minister from 10 November 1990 to 21 June 1991. Chaudhuri Charan Singh briefly served as Prime Minister from 1979 to 1980. Atal Bihari Vajpayee, an Indian politician and poet, was Prime Minister first for 13 days in 1996, then for 13 months from 1998 to 1999, and finally for a full term from 1999 to 2004.</p>",
                    solution_hi: "<p>37.(b) <strong>गुलजारीलाल नंदा</strong> श्रम मुद्दों के विशेषज्ञ भारतीय राजनेता तथा अर्थशास्त्री थे। 1964 में जवाहरलाल नेहरू और 1966 में लाल बहादुर शास्त्री की मृत्यु के बाद वे दो बार 13-दिवसीय कार्यकाल के लिए भारत के कार्यवाहक प्रधानमंत्री रहे। जननायक के रूप में जाने जाने वाले चंद्रशेखर 10 नवंबर 1990 से 21 जून 1991 तक प्रधानमंत्री रहे। चौधरी चरण सिंह ने 1979 से 1980 तक कुछ समय के लिए प्रधानमंत्री के रूप में कार्य किया। भारतीय राजनेता और कवि अटल बिहारी वाजपेयी पहली बार 1996 में 13 दिनों के लिए, पुनः 1998 से 1999 तक 13 महीनों के लिए और अंत में 1999 से 2004 तक पूर्ण कार्यकाल के लिए प्रधानमंत्री रहे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following organisations is NOT a winner of the $100,000 cash prize as &lsquo;Best Small Business: Good Food for All&rsquo;, announced by the United Nations for providing inspiring, diverse and impactful solutions in improving access to healthy and sustainable food?</p>",
                    question_hi: "<p>38. निम्न में से कौन-सा संगठन स्वस्थ और पोषणीय़ भोजन तक पहुँच में सुधार करने हेतु प्रेरणादायक, विविध और प्रभावशाली समाधान प्रदान करने के लिए संयुक्त राष्ट्र द्वारा घोषित \'सर्वश्रेष्ठ लघु व्यवसाय: सभी के लिए अच्छा भोजन\' के रूप में $100,000 के नकद पुरस्कार का विजेता नहीं है?</p>",
                    options_en: ["<p>Patanjali</p>", "<p>Edible Routes Private Limited</p>", 
                                "<p>Oorja Development Solutions India Private Limited</p>", "<p>Taru Naturals</p>"],
                    options_hi: ["<p>पतंजलि</p>", "<p>एडिबल रूट्स प्राइवेट लिमिटेड</p>",
                                "<p>ऊर्जा डेवलपमेंट सॉल्यूशंस इंडिया प्राइवेट लिमिटेड</p>", "<p>तरु नेचुरल्स</p>"],
                    solution_en: "<p>38.(a) <strong>Patanjali</strong> Ayurved is an Indian multinational conglomerate based in Haridwar, founded by Ramdev and Balkrishna in 2006. The United Nations, an international organization with 193 member states, is dedicated to maintaining global peace and security. It was established on 24 October 1945 and has its headquarters in New York, United States.</p>",
                    solution_hi: "<p>38.(a) <strong>पतंजलि</strong> आयुर्वेद हरिद्वार में स्थित एक भारतीय बहुराष्ट्रीय समूह है, जिसकी स्थापना 2006 में रामदेव और बालकृष्ण ने की थी। संयुक्त राष्ट्र, 193 सदस्य देशों वाला एक अन्तर्राष्ट्रीय संगठन है, जो वैश्विक शांति और सुरक्षा बनाए रखने के लिए समर्पित है। इसकी स्थापना 24 अक्टूबर 1945 को हुई थी और इसका मुख्यालय न्यूयॉर्क, संयुक्त राज्य अमेरिका में है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. At the 36th National Games, Hashika Ramchandra won six gold medals in which of the following sports?</p>",
                    question_hi: "<p>39. 36वें राष्ट्रीय खेलों में, हशिका रामचन्द्र ने निम्नलिखित में से किस खेल में छ: स्वर्ण पदक जीते?</p>",
                    options_en: ["<p>Boxing</p>", "<p>Wrestling</p>", 
                                "<p>Swimming</p>", "<p>Badminton</p>"],
                    options_hi: ["<p>मुक्केबाजी</p>", "<p>कुश्ती</p>",
                                "<p>तैराकी</p>", "<p>बैडमिंटन</p>"],
                    solution_en: "<p>39.(c) <strong>Swimming.</strong> The 2022 National Games of India, also known as the 36th National Games or Gujarat 2022, took place in Ahmedabad, Gandhinagar, Surat, Vadodara, Rajkot, and Bhavnagar from 29 September to 12 October 2022. The motto was \"Celebrating Unity Through Sports,\" and the main venue was the Narendra Modi Stadium in Ahmedabad. Notable Indian swimmers included Srihari Nataraj, Sajan Prakash, Sandeep Sejwal, Virdhawal Khade, Arati Saha, Bhakti Sharma, Prasanta Karmakar, and Sharath Gayakwad.</p>",
                    solution_hi: "<p>39.(c) <strong>तैराकी।</strong> भारत के 2022 राष्ट्रीय खेल, जिन्हें 36वें राष्ट्रीय खेल या गुजरात 2022 के नाम से भी जाना जाता है, 29 सितंबर से 12 अक्टूबर 2022 तक अहमदाबाद, गांधीनगर, सूरत, वडोदरा, राजकोट और भावनगर में आयोजित किए गए। इसका आदर्श वाक्य \"सेलिब्रेटिन्ग यूनिटी थ्रू स्पोर्ट्स\" था और मुख्य आयोजन स्थल अहमदाबाद का नरेंद्र मोदी स्टेडियम था। उल्लेखनीय भारतीय तैराकों में श्रीहरि नटराज, साजन प्रकाश, संदीप सेजवाल, वीरधवल खाड़े, आरती साहा, भक्ति शर्मा, प्रशांत करमाकर और शरत गायकवाड़ शामिल थे।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Umayalpuram K Sivaraman is an eminent musician associated with which of the following musical instruments?</p>",
                    question_hi: "<p>40. उमयालपुरम के. शिवरामन निम्नलिखित में से किस वाद्य यंत्र से जुड़े एक प्रसिद्ध संगीतकार हैं?</p>",
                    options_en: ["<p>Sitar</p>", "<p>Guitar</p>", 
                                "<p>Dhol</p>", "<p>Mridangam</p>"],
                    options_hi: ["<p>सितार</p>", "<p>गिटार</p>",
                                "<p>ढोल</p>", "<p>मृदंगम</p>"],
                    solution_en: "<p>40.(d) <strong>Mridangam.</strong> The mridangam is a percussion instrument from the Indian subcontinent and serves as the primary rhythmic accompaniment in Carnatic music ensembles. Dr. Umayalpuram Kashiviswanatha Sivaraman, a prominent Karnataka mridangam scholar, received the Sangeeta Kalanidhi award from the Madras Music Academy in 2001 and the Padma Vibhushan in 2010. Notable mridangam players include Palghat Mani Iyer, Karaikudi R. Mani, and Yella Venkateswara Rao.</p>",
                    solution_hi: "<p>40.(d) <strong>मृदंगम।</strong> मृदंगम भारतीय उपमहाद्वीप का एक ताल वाद्य है और यह कर्नाटक संगीत मंडलियों में प्राथमिक लयबद्ध संगत के रूप में कार्य करता है। कर्नाटक मृदंगम के एक प्रमुख विद्वान डॉ. उमयालपुरम काशीविश्वनाथ शिवरामन को 2001 में मद्रास संगीत अकादमी से संगीत कलानिधि पुरस्कार और 2010 में पद्म विभूषण से सम्मानित किया गया। उल्लेखनीय मृदंगम वादकों में पालघाट मणि अय्यर, कराईकुडी आर. मणि और येल्ला वेंकटेश्वर राव शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which of the following ports was developed as a Satellite port to relieve the pressure at the Mumbai port?</p>",
                    question_hi: "<p>41. मुंबई बंदरगाह पर दबाव कम करने के लिए निम्नलिखित में से किस बंदरगाह को उपग्रह बंदरगाह (Satellite port) के रूप में विकसित किया गया था?</p>",
                    options_en: ["<p>New Mangalore Port</p>", "<p>Marmagao Port</p>", 
                                "<p>Deendayal Port Authority</p>", "<p>Jawaharlal Nehru Port</p>"],
                    options_hi: ["<p>न्यू मैंगलोर बंदरगाह</p>", "<p>मारमागाओ बंदरगाह</p>",
                                "<p>दीनदयाल बंदरगाह प्राधिकरण</p>", "<p>जवाहरलाल नेहरू बंदरगाह</p>"],
                    solution_en: "<p>41.(d) <strong>Jawaharlal Nehru Port</strong> : Also known as Nhava Sheva Port, it was established on 26 May 1989 and is the second-largest container port in India, following Mundra Port. New Mangalore Port, located in Panambur, Mangalore, Karnataka, is operated by the New Mangalore Port Trust. Mormugao Port, commissioned in 1885, is situated on Goa\'s western coast, built on a natural harbor. Kandla Port, officially known as Deendayal Port, is a seaport in Kutch district, Gujarat.</p>",
                    solution_hi: "<p>41.(d) <strong>जवाहरलाल नेहरू बंदरगाह:</strong> इसे न्हावा शेवा बंदरगाह के नाम से भी जाना जाता है, इसकी स्थापना 26 मई 1989 को हुई थी और यह मुंद्रा बंदरगाह के बाद भारत का दूसरा सबसे बड़ा कंटेनर बंदरगाह है। कर्नाटक के मैंगलोर के पनाम्बुर में स्थित न्यू मैंगलोर बंदरगाह का संचालन न्यू मैंगलोर पोर्ट ट्रस्ट द्वारा किया जाता है। 1885 में शुरू किया गया मोरमुगाओ बंदरगाह गोवा के पश्चिमी तट पर स्थित है, जो एक प्राकृतिक बंदरगाह पर बना है। कांडला बंदरगाह, जिसे आधिकारिक तौर पर दीनदयाल बंदरगाह के नाम से जाना जाता है, गुजरात के कच्छ जिले में एक बंदरगाह है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. In which of the following situations does the government run a deficit budget?</p>",
                    question_hi: "<p>42. निम्नलिखित में से किस स्थिति में सरकार घाटे का बजट चलाती है?</p>",
                    options_en: ["<p>When the government expenditure and revenue both are zero.</p>", "<p>When the government expenditure exceeds revenue.</p>", 
                                "<p>When the government expenditure equals revenue.</p>", "<p>When the government revenue exceeds expenditure.</p>"],
                    options_hi: ["<p>जब सरकारी व्यय और राजस्व दोनों शून्य हो।</p>", "<p>जब सरकारी व्यय राजस्व से अधिक हो जाता है।</p>",
                                "<p>जब सरकारी व्यय राजस्व के बराबर होता है।</p>", "<p>जब सरकारी राजस्व व्यय से अधिक हो जाता है।</p>"],
                    solution_en: "<p>42.(b) <strong>When the government expenditure exceeds revenue.</strong> A budget deficit is a fiscal situation in which a government\'s total expenditures exceed its total revenues over a specific period, resulting in a negative balance. The Union Budget of India, also referred to as the Annual Financial Statement in Article 112 of the Constitution of India. It is the annual budget of the Republic of India set by the Ministry of Finance for the following financial year. The budget system was introduced during Lord Canning\'s viceroyalty, which lasted from 1856 to 1862.</p>",
                    solution_hi: "<p>42.(b) <strong>जब सरकारी व्यय राजस्व से अधिक हो जाता है।</strong>बजट घाटा एक राजकोषीय स्थिति है जिसमें किसी सरकार का कुल व्यय एक विशिष्ट अवधि में उसके कुल राजस्व से अधिक हो जाता है, जिसके परिणामस्वरूप ऋणात्मक शेष होता है। भारत का केंद्रीय बजट, जिसे भारत के संविधान के अनुच्छेद 112 में वार्षिक वित्तीय विवरण के रूप में भी संदर्भित किया जाता है। यह वित्त मंत्रालय द्वारा अगले वित्तीय वर्ष के लिए निर्धारित भारत गणराज्य का वार्षिक बजट है। बजट प्रणाली लॉर्ड कैनिंग के वायसराय काल के दौरान शुरू की गई थी, जो 1856 से 1862 तक चली।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Rottela Panduga or the Roti Festival is celebrated in which Indian state?</p>",
                    question_hi: "<p>43. रोट्टेला पांडुगा या रोटी महोत्सव किस भारतीय राज्य में मनाया जाता है?</p>",
                    options_en: ["<p>Karnataka</p>", "<p>Maharashtra</p>", 
                                "<p>Andhra Pradesh</p>", "<p>Tamil Nadu</p>"],
                    options_hi: ["<p>कर्नाटक</p>", "<p>महाराष्ट्र</p>",
                                "<p>आंध्र प्रदेश</p>", "<p>तमिलनाडु</p>"],
                    solution_en: "<p>43.(c) <strong>Andhra Pradesh.</strong> Rotiyaan Ki Eid, or Rottela Panduga, is an annual three-day festival held at Bara Shaheed Dargah in Nellore, Andhra Pradesh. This event is observed in Muharram to commemorate the urs (death anniversary) of the 12 martyrs buried in the shrine. Women visiting the shrine exchange rotis at Nellore Tank. Other notable festivals celebrated in Andhra Pradesh include Pongal, Ugadi, Sri Rama Navami, Ratha Saptami, Tirupati Ganga Jatara, and Visakha Utsav.</p>",
                    solution_hi: "<p>43.(c) <strong>आंध्र प्रदेश।</strong> रोटियाँ की ईद, या रोटेला पंडुगा, आंध्र प्रदेश के नेल्लोर में बारा शहीद दरगाह पर आयोजित होने वाला एक वार्षिक तीन दिवसीय उत्सव है। यह आयोजन मुहर्रम में दरगाह में दफन 12 शहीदों के उर्स (पुण्यतिथि) के उपलक्ष्य में मनाया जाता है। दरगाह पर आने वाली महिलाएँ नेल्लोर टैंक में रोटियाँ का आदान-प्रदान करती हैं। आंध्र प्रदेश में मनाए जाने वाले अन्य उल्लेखनीय त्योहारों में पोंगल, उगादी, श्री राम नवमी, रथ सप्तमी, तिरुपति गंगा जतरा और विशाखा उत्सव शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. In the context of Mathura school of Art, during which of the following periods were a number of sculptures of Brahmanical deities, such as Kartikeya, Vishnu and Kubera carved?</p>",
                    question_hi: "<p>44. मथुरा कला विद्यालय के संदर्भ में, निम्नलिखित में से किस काल के दौरान कार्तिकेय, विष्णु और कुबेर जैसे ब्राह्मण देवताओं की कई मूर्तियां उकेरी गई थी?</p>",
                    options_en: ["<p>Shaka</p>", "<p>Parthian</p>", 
                                "<p>Satavahana</p>", "<p>Kushana</p>"],
                    options_hi: ["<p>शक</p>", "<p>पार्थियन</p>",
                                "<p>सातवाहन</p>", "<p>कुषाण</p>"],
                    solution_en: "<p>44.(d) <strong>The Kushan</strong> Empire was a syncretic empire established by the Yuezhi in the Bactrian territories in the early 1st century. The Mathura School of Art flourished under the Kushana emperor Kanishka during the first century AD. Notable works include the Sarvatobhadrika image of four Jain Jinas standing back to back. The school produced numerous sculptures of the Buddha, Yakshas, Yakshinis, and Shaivite and Vaishnavite deities, primarily in red sandstone. In Mathura art, Buddha images feature long earlobes, thick lips, wide eyes, and prominent noses, modeled similarly to Yaksha images.</p>",
                    solution_hi: "<p>44.(d) <strong>कुषाण साम्राज्य</strong> पहली शताब्दी की शुरुआत में बैक्ट्रियन क्षेत्रों में यूझी द्वारा स्थापित एक समन्वित साम्राज्य था। मथुरा कला विद्यालय पहली शताब्दी ई. के दौरान कुषाण सम्राट कनिष्क के अधीन फला-फूला। उल्लेखनीय कार्यों में चार जैन जिनों की पीठ से पीठ मिलाकर खड़ी सर्वतोभद्रिका छवि शामिल है। इस विद्यालय ने बुद्ध, यक्ष, यक्षिणी और शैव तथा वैष्णव देवताओं की कई मूर्तियाँ बनाईं, जो मुख्य रूप से लाल बलुआ पत्थर में बनी थीं। मथुरा कला में बुद्ध की मूर्तियों में लम्बे कान, मोटे होंठ, बड़ी आंखें और उभरी हुई नाक होती है, जो यक्ष की मूर्तियों के समान होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Match the terms in column A with their respective properties in column B.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731555510862.png\" alt=\"rId48\"></p>",
                    question_hi: "<p>45. स्तम्भ A में दिए गए पदों को स्तम्भ B में उनके संबंधित गुणों के साथ सुमेलित कीजिये।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731555511046.png\" alt=\"rId49\"></p>",
                    options_en: ["<p>i-b, ii-a, iii-d, iv-c</p>", "<p>i-d, ii-c, iii-b, iv-a</p>", 
                                "<p>i-a, ii-b, iii-c, iv-d</p>", "<p>i-b, ii-d, iii-a, iv-c</p>"],
                    options_hi: ["<p>i-b, ii-a, iii-d, iv-c</p>", "<p>i-d, ii-c, iii-b, iv-a</p>",
                                "<p>i-a, ii-b, iii-c, iv-d</p>", "<p>i-b, ii-d, iii-a, iv-c</p>"],
                    solution_en: "<p>45.(b) <strong>i-d, ii-c, iii-b, iv-a. </strong>Glucose is a simple sugar and a major free sugar present in the blood of higher animals. Yeasts are eukaryotic, single-celled microorganisms classified within the fungus kingdom. Glycolysis is the metabolic process in which glucose is broken down to produce energy. Pyruvic acid is a 2-oxo monocarboxylic acid that is the 2-keto derivative of propionic acid. It is a metabolite obtained during glycolysis.</p>",
                    solution_hi: "<p>45.(b) <strong>i-d, ii-c, iii-b, iv-a.</strong> ग्लूकोज एक सरल शर्करा है तथा उच्च श्रेणी के जन्तुओं के रक्त में उपस्थित एक प्रमुख मुक्त शर्करा है। खमीर यूकेरियोटिक, एकल-कोशिका वाले सूक्ष्मजीव हैं जिन्हें कवक जगत के भीतर वर्गीकृत किया गया है। ग्लाइकोलाइसिस उपापचय प्रक्रिया है जिसमें ऊर्जा उत्पन्न करने के लिए ग्लूकोज को तोड़ा जाता है। पाइरुविक एसिड 2-ऑक्सो मोनोकार्बोक्सिलिक एसिड है जो प्रोपियोनिक एसिड का 2-कीटो व्युत्पन्न है। यह ग्लाइकोलाइसिस के दौरान प्राप्त एक मेटाबोलाइट है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. As per the UNDP, the rank of Sri Lanka in the Human Development Index (2022) is:</p>",
                    question_hi: "<p>46. यू.एन.डी.पी. (UNDP) के अनुसार, मानव विकास सूचकांक (2022) में, श्रीलंका का स्थान क्&zwj;या है?</p>",
                    options_en: ["<p>74/189</p>", "<p>73/189</p>", 
                                "<p>75/189</p>", "<p>72/189</p>"],
                    options_hi: ["<p>74/189</p>", "<p>73/189</p>",
                                "<p>75/189</p>", "<p>72/189</p>"],
                    solution_en: "<p>46.(b) <strong>73/189.</strong> The Human Development Index (HDI) is a statistical measure created and coordinated by the United Nations Development Programme (UNDP) to assess the social and economic development levels of various nations. The title of the Human Development Report (HDR) 2021-22 was \"Uncertain Times, Unsettled Lives: Shaping our Future in a Transforming World.\"</p>",
                    solution_hi: "<p>46.(b) <strong>73/189.</strong> मानव विकास सूचकांक (HDI) संयुक्त राष्ट्र विकास कार्यक्रम (UNDP) द्वारा विभिन्न देशों के सामाजिक और आर्थिक विकास स्तरों का आकलन करने के लिए बनाया और समन्वित किया गया एक सांख्यिकीय उपाय है। मानव विकास रिपोर्ट (HDR) 2022-23 का शीर्षक \"अनिश्चित समय, अस्थिर जीवन: एक बदलती दुनिया में हमारे भविष्य को आकार देना\" था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. If a bar magnet is hung from a string, in which direction does its north pole point?</p>",
                    question_hi: "<p>47. यदि एक छड़ चुंबक को एक डोरी से लटका दिया जाए, तो उसका उत्तरी ध्रुव किस दिशा में इंगित करेगा?</p>",
                    options_en: ["<p>West</p>", "<p>North</p>", 
                                "<p>East</p>", "<p>South</p>"],
                    options_hi: ["<p>पश्चिम</p>", "<p>उत्तर</p>",
                                "<p>पूर्व</p>", "<p>दक्षिण</p>"],
                    solution_en: "<p>47.(b) <strong>North.</strong> When a bar magnet is hung freely from a string, its north pole aligns with the Earth\'s magnetic field and points toward the Earth\'s magnetic north pole. This happens because opposite magnetic poles attract; since the Earth\'s magnetic north pole is actually a magnetic south pole, it attracts the north pole of the magnet, causing it to point north.</p>",
                    solution_hi: "<p>47.(b) <strong>उत्तर।</strong> जब एक छड़ चुंबक को एक डोरी से स्वतंत्र रूप से लटकाया जाता है, तो इसका उत्तरी ध्रुव पृथ्वी के चुंबकीय क्षेत्र के साथ संरेखित हो जाता है और पृथ्वी के चुंबकीय उत्तरी ध्रुव की ओर इंगित करता है। ऐसा इसलिए होता है क्योंकि विपरीत चुंबकीय ध्रुव आकर्षित होते हैं; चूँकि पृथ्वी का चुंबकीय उत्तरी ध्रुव वास्तव में एक चुंबकीय दक्षिणी ध्रुव है, यह चुंबक के उत्तरी ध्रुव को आकर्षित करता है, जिससे वह उत्तर की ओर निर्देशित होता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Fugdi dance is associated with which of the following states?</p>",
                    question_hi: "<p>48. फुगड़ी (Fugdi) नृत्य निम्नलिखित में से किस राज्य से संबंधित है?</p>",
                    options_en: ["<p>Goa</p>", "<p>Uttarakhand</p>", 
                                "<p>Uttar Pradesh</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>गोवा</p>", "<p>उत्तराखंड</p>",
                                "<p>उत्तर प्रदेश</p>", "<p>केरल</p>"],
                    solution_en: "<p>48.(a) <strong>Goa.</strong> Fugdi is a folk dance performed by women in the Konkan region of Goa and Maharashtra, especially during Hindu festivals like Ganesh Chaturthi, Vrata, and in the month of Bhadrapada. It is also performed at weddings and other celebrations. Other popular dances of Goa include Dhalo, Dekhni, Lamp dance, Mando, Goff, Musalam Khel, and Veerbhadra.</p>",
                    solution_hi: "<p>48.(a) <strong>गोवा।</strong> फुगड़ी एक लोक नृत्य है जो गोवा और महाराष्ट्र के कोंकण क्षेत्र में महिलाओं द्वारा किया जाता है, विशेष रूप से गणेश चतुर्थी, व्रत जैसे हिंदू त्योहारों और भाद्रपद के महीने में। इसे शादियों और अन्य समारोहों में भी किया जाता है। गोवा के अन्य लोकप्रिय नृत्यों में ढालो, देखनी, लैंप नृत्य, मांडो, गोफ, मुसलम खेल और वीरभद्र शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following groups/parties was started in San Francisco, United States of America during the Indian National Movement?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन-सा दल/पार्टी भारतीय राष्ट्रीय आंदोलन के दौरान सैन फ्रांसिस्को, संयुक्त राज्य अमेरिका में शुरू किया गया था /की गई थी?</p>",
                    options_en: ["<p>Hindustan Socialist Republican Association</p>", "<p>Swaraj Party</p>", 
                                "<p>Khilafat Committee</p>", "<p>Ghadar Party</p>"],
                    options_hi: ["<p>हिंदुस्तान सोशलिस्ट रिपब्लिकन एसोसिएशन</p>", "<p>स्वराज पार्टी</p>",
                                "<p>खिलाफत समिति</p>", "<p>ग़दर पार्टी</p>"],
                    solution_en: "<p>49.(d) <strong>Ghadar Party :</strong> Founded in 1913 in the U.S. as the Hindi Association of the Pacific Coast, it aimed to free India from British rule. It was founded by Sohan Singh Bhakna and Lala Har Dayal. Hindustan Socialist Republican Association (HSRA) : Founded by Ram Prasad Bismil, Sachindra Nath Bakshi, Sachindranath Sanyal, and Jogesh Chandra Chatterjee, it was previously known as the Hindustan Republican Army. Swaraj Party : Established on 1 January 1923, it opposed Mahatma Gandhi&rsquo;s suspension of civil resistance after the Chauri Chaura incident. Khilafat Committee : Led by Mohammed Ali, Shaukat Ali, and Maulana Azad, it was formed to defend the Ottoman Caliph\'s powers post-World War I.</p>",
                    solution_hi: "<p>49.(d) <strong>ग़दर पार्टी :</strong> 1913 में अमेरिका में प्रशांत तट के हिंदी एसोसिएशन के रूप में स्थापित, इसका उद्देश्य भारत को ब्रिटिश शासन से मुक्त करना था। इसकी स्थापना सोहन सिंह भकना और लाला हर दयाल ने की थी। हिंदुस्तान सोशलिस्ट रिपब्लिकन एसोसिएशन (HSRA) : राम प्रसाद बिस्मिल, सचिंद्र नाथ बख्शी, सचिंद्रनाथ सान्याल और जोगेश चंद्र चटर्जी द्वारा स्थापित, इसे पहले हिंदुस्तान रिपब्लिकन आर्मी के रूप में जाना जाता था। स्वराज पार्टी : 1 जनवरी 1923 को स्थापित, इसने चौरी चौरा की घटना के बाद महात्मा गांधी द्वारा नागरिक प्रतिरोध को स्थगित करने का विरोध किया। खिलाफत समिति : मोहम्मद अली, शौकत अली और मौलाना आज़ाद के नेतृत्व में, इसका गठन प्रथम विश्व युद्ध के बाद ओटोमन खलीफा की शक्तियों की रक्षा के लिए किया गया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which writ is issued by a high court or supreme court when a lower court has considered a case going beyond its jurisdiction?</p>",
                    question_hi: "<p>50. जब निचली अदालत ने अपने अधिकार क्षेत्र से बाहर जाकर किसी मामले पर विचार किया हो तो किसी उच्च न्यायालय या सर्वोच्च न्यायालय द्वारा कौन सी रिट जारी की जाती है?</p>",
                    options_en: ["<p>Quo Warrant</p>", "<p>Habeas Corpus</p>", 
                                "<p>Certiorari</p>", "<p>Prohibition</p>"],
                    options_hi: ["<p>अधिकार पृच्छा (Quo Warrant)</p>", "<p>बन्दी प्रत्यक्षीकरण (Habeas Corpus)</p>",
                                "<p>उत्प्रेषण लेख (Certiorari)</p>", "<p>निषेधाज्ञा (Prohibition)</p>"],
                    solution_en: "<p>50.(d) <strong>Prohibition.</strong> Writs are issued by the Supreme Court under Article 32 and by the High Court under Article 226 of the Indian Constitution. The five types of writs in India are: Habeas Corpus: Ensures that a detained person is brought before the court. Mandamus: Directs a public official to perform a duty. Certiorari: Transfers a case for review by a higher court. Prohibition: Prevents a lower court from exceeding its jurisdiction. Quo-Warranto: Questions the legal authority of someone holding a public office.</p>",
                    solution_hi: "<p>50.(d) <strong>निषेधाज्ञा (Prohibition)।</strong> भारतीय संविधान के अनुच्छेद 32 के तहत सर्वोच्च न्यायालय और अनुच्छेद 226 के तहत उच्च न्यायालय द्वारा रिट जारी की जाती हैं। भारत में पाँच प्रकार की रिट हैं: बंदी प्रत्यक्षीकरण: यह सुनिश्चित करता है कि हिरासत में लिए गए व्यक्ति को न्यायालय के समक्ष लाया जाए। परमादेश: किसी सार्वजनिक अधिकारी को कोई कर्तव्य निभाने का निर्देश देता है। उत्प्रेषण लेख : किसी मामले को उच्च न्यायालय द्वारा समीक्षा के लिए स्थानांतरित करता है। निषेधाज्ञा: किसी निचली अदालत को अपने अधिकार क्षेत्र से बाहर जाने से रोकता है। अधिकार-पृच्छा: किसी सार्वजनिक पद पर आसीन व्यक्ति के कानूनी अधिकार पर सवाल उठाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. The central angle of a sector is 80&deg; and whose length is 96&pi;. What is the radius of the circle?</p>",
                    question_hi: "<p>51. एक त्रिज्यखंड का केंद्रीय कोण 80&deg; है और जिसकी लंबाई 96&pi; है। वृत्त की त्रिज्या क्या है ?</p>",
                    options_en: ["<p>196 units</p>", "<p>204 units</p>", 
                                "<p>116 units</p>", "<p>216 units</p>"],
                    options_hi: ["<p>196 यूनिट</p>", "<p>204 यूनिट</p>",
                                "<p>116 यूनिट</p>", "<p>216 यूनिट</p>"],
                    solution_en: "<p>51.(d)<br>Length of sector = 96<math display=\"inline\"><mi>&#960;</mi></math><br><math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>36</mn><msup><mrow><mn>0</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></mrow></mfrac><mo>&#215;</mo></math> 2&pi;r = 96&pi;<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>80</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></mrow><mrow><mn>36</mn><msup><mrow><mn>0</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></mrow></mfrac><mo>&#215;</mo></math> 2&times;r = 96<br>r = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>96</mn><mo>&#215;</mo><mn>36</mn></mrow><mn>16</mn></mfrac></math> = 6 &times; 36 = 216 unit</p>",
                    solution_hi: "<p>51.(d)<br>त्रिज्यखंड की लंबाई = 96<math display=\"inline\"><mi>&#960;</mi></math><br><math display=\"inline\"><mfrac><mrow><mi>&#952;</mi></mrow><mrow><mn>36</mn><msup><mrow><mn>0</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></mrow></mfrac><mo>&#215;</mo></math> 2&pi;r = 96&pi;<br><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>80</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></mrow><mrow><mn>36</mn><msup><mrow><mn>0</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></mrow></mfrac><mo>&#215;</mo></math> 2 &times; r = 96<br>r = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>96</mn><mo>&#215;</mo><mn>36</mn></mrow><mn>16</mn></mfrac></math> = 6 &times; 36 = 216 इकाई</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. If a ∶ b = c ∶ d = e ∶ f = 5 ∶ 7, then what is the ratio (3a + 5c + 11e) ∶ (3b + 5d + 11f)?</p>",
                    question_hi: "<p>52. यदि a ∶ b = c ∶ d = e ∶ f = 5 ∶ 7 है, तो (3a + 5c + 11e) ∶ (3b + 5d + 11f) का अनुपात क्या है?</p>",
                    options_en: ["<p>7 : 11</p>", "<p>3 : 7</p>", 
                                "<p>5 : 7</p>", "<p>11 : 7</p>"],
                    options_hi: ["<p>7 : 11</p>", "<p>3 : 7</p>",
                                "<p>5 : 7</p>", "<p>11 : 7</p>"],
                    solution_en: "<p>52.(c) Given: a : b = c : d = e : f = 5 : 7<br>Now,<br>(3a + 5c + 11e) : (3b + 5d + 11f)<br>(3 &times; 5 + 5 &times; 5 + 11 &times; 5) : (3 &times; 7 + 5 &times; 7 + 11 &times; 7)<br>(15 + 25 + 55) : (21 + 35 + 77)<br>95 : 133<br>5 : 7</p>",
                    solution_hi: "<p>52.(c) दिया गया है : a : b = c : d = e : f = 5 : 7<br>अब,<br>(3a + 5c + 11e) : (3b + 5d + 11f)<br>(3 &times; 5 + 5 &times; 5 + 11 &times; 5) : (3 &times; 7 + 5 &times; 7 + 11 &times; 7)<br>(15 + 25 + 55) : (21 + 35 + 77)<br>95 : 133<br>5 : 7</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The given bar graph shows the turnover of five companies (in crores).<br><strong id=\"docs-internal-guid-71e9d04a-7fff-5c3f-6757-cfb93819bc39\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdq7kdcPK9scVI5FCVsU3qt9lZzn5tZpISArKzozvyJwoPol_IcFF9gTgCBkjlTc_uPWni5sXvv6wVMzK8GOKA6uKrQG_RnE48ZndvmEpWOlhA6DVydxKQUe8xxuy53sH4UytABRg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"357\" height=\"244\"></strong><br>What is the difference between the average sales turnover of all the companies put together between the years 2018-2019 and 2019-2020?.</p>",
                    question_hi: "<p>53. दिया गया बार ग्राफ पांच कंपनियों का टर्नओवर (करोड़ में) दर्शाता है।<br><strong id=\"docs-internal-guid-67980b0b-7fff-ba4a-c0ca-7a5358f179d9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcu-KP2MPROOYPM1pa_n3wZ24MBG-ChnxoW3LsGwzBwOFf17Cvj8kLYo4B88ljNawyCIOrd61JDHjy5aPy62rrRNm00A0K5G16qHRDNCI7GxPzcpkU6eCI9V_TjQk68vGGTl8mhtg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"393\" height=\"274\"></strong><br>वर्ष 2018-2019 और 2019-2020 के बीच सभी कंपनियों के कुल औसत बिक्री टर्नओवर के बीच कितना अंतर है?.</p>",
                    options_en: ["<p>₹6.5 crore</p>", "<p>₹2 crore</p>", 
                                "<p>₹5 crore</p>", "<p>₹3.5 crore</p>"],
                    options_hi: ["<p>₹6.5 करोड़</p>", "<p>₹2 करोड़</p>",
                                "<p>₹5 करोड़</p>", "<p>₹3.5 करोड़</p>"],
                    solution_en: "<p>53.(c)<br>Average sale of all companies in 2018 - 2019 = <math display=\"inline\"><mfrac><mrow><mn>250</mn><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>350</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>275</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>200</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>400</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1475</mn><mn>5</mn></mfrac></math> = 295 crore<br>Average sale of all companies in 2019 - 2020 = <math display=\"inline\"><mfrac><mrow><mn>300</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>250</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>300</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>300</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>350</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1500</mn><mn>5</mn></mfrac></math> = 300 crore<br>Required difference = 300 - 295 = 5 crore</p>",
                    solution_hi: "<p>53.(c)<br>2018 - 2019 में सभी कंपनियों की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>250</mn><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>350</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>275</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>200</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>400</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1475</mn><mn>5</mn></mfrac></math> = 295 करोड़<br>2019 - 2020 में सभी कंपनियों की औसत बिक्री = <math display=\"inline\"><mfrac><mrow><mn>300</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>250</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>300</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>300</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>350</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1500</mn><mn>5</mn></mfrac></math> = 300 करोड़<br>आवश्यक अंतर = 300 - 295 = 5 करोड़</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The five-digit number 45yz0 is divisible by 40. What is the maximum possible value of (y + z)?</p>",
                    question_hi: "<p>54. 45yz0 पांच अंकों की एक ऐसी संख्या है जो 40 से विभाज्य है। (y + z) का अधिकतम संभावित मान क्या है?</p>",
                    options_en: ["<p>15</p>", "<p>18</p>", 
                                "<p>16</p>", "<p>17</p>"],
                    options_hi: ["<p>15</p>", "<p>18</p>",
                                "<p>16</p>", "<p>17</p>"],
                    solution_en: "<p>54.(c) 45yz0<br>For divisibility of 40 number must be divisible by 8 and 5<br><strong>Divisibility of 5:</strong>- A number is divisible by 5 if its last digit is 0 or 5<br><strong>Divisibility of 8 </strong>:- if the last three digits of the number are either all zeros or are divisible by 8:<br>Maximum possible value of y and z be 8 and 8 respectively,<br>So, the value of (y + z) = 8 + 8 = 16</p>",
                    solution_hi: "<p>54.(c) 45yz0<br>40 की विभाज्यता के लिए संख्या 8 और 5 से विभाज्य होनी चाहिए<br><strong>5 की विभाज्यता:-</strong> कोई संख्या 5 से विभाज्य होती है यदि उसका अंतिम अंक 0 या 5 है<br><strong>8 की विभाज्यता :</strong>- यदि किसी संख्या के अंतिम तीन अंक या तो सभी शून्य हैं या 8 से विभाज्य हैं:<br>y और z का अधिकतम संभावित मान क्रमशः 8 और 8 है,<br>तो, (y + z) का मान = 8 + 8 = 16</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Simplify the following expression. <br>(60 + 64 &divide; 4 of 4) &times; {1000 &divide; ((10 of (3 + 2)) &times; 2 of 5)} &minus; 28 + (100 &times; 3 of 3).</p>",
                    question_hi: "<p>55. निम्नलिखित व्यंजक को सरल कीजिए।<br>(60 + 64 &divide; 4 of 4) &times; {1000 &divide; ((10 of (3 + 2)) &times; 2 of 5)} &minus; 28 + (100 &times; 3 of 3).</p>",
                    options_en: ["<p>600</p>", "<p>1000</p>", 
                                "<p>100</p>", "<p>500</p>"],
                    options_hi: ["<p>600</p>", "<p>1000</p>",
                                "<p>100</p>", "<p>500</p>"],
                    solution_en: "<p>55.(b)<br>(60 + 64 &divide;&nbsp;4 of 4) &times; [1000 &divide; {(10 of (3 + 2)) &times; 2 of 5}] - 28 + (100 &times; 3 of 3 )<br>= (60 + 64 &divide;&nbsp;16) &times; [1000 &divide; {10 of 5 &times; 10}] - 28 + (100 &times; 9 )<br>= (60 + 4) &times;&nbsp;[1000 &divide; 500] - 28 + 900<br>= 64 &times;&nbsp;2 - 28 + 900<br>= 128 - 28 + 900 = 1000</p>",
                    solution_hi: "<p>55.(b)<br>(60 + 64 &divide;&nbsp;4 of 4) &times; [1000 &divide; {(10 of (3 + 2)) &times; 2 of 5}] - 28 + (100 &times; 3 of 3 )<br>= (60 + 64 &divide;&nbsp;16) &times; [1000 &divide; {10 of 5 &times; 10}] - 28 + (100 &times; 9 )<br>= (60 + 4) &times;&nbsp;[1000 &divide; 500] - 28 + 900<br>= 64 &times;&nbsp;2 - 28 + 900<br>= 128 - 28 + 900 = 1000</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Varun and Raju, working together, can complete a job in 40 hours whereas Varun alone can complete the same job in 50 hours. The number of hours required by Raju to complete the job alone is:</p>",
                    question_hi: "<p>56. वरुण और राजू, एक साथ मिलकर कार्य करते हुए, किसी कार्य को 40 घंटे में पूरा कर सकते हैं, जबकि अकेले वरुण, उसी कार्य को 50 घंटे में पूरा कर सकता है। अकेले राजू, कितने घंटे में कार्य पूरा करेगा?</p>",
                    options_en: ["<p>200</p>", "<p>150</p>", 
                                "<p>170</p>", "<p>190</p>"],
                    options_hi: ["<p>200</p>", "<p>150</p>",
                                "<p>170</p>", "<p>190</p>"],
                    solution_en: "<p>56.(a)<br><strong id=\"docs-internal-guid-81c3daca-7fff-d00a-393f-f23d079bf7b9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd5ziaAfHoUtdGmqFoVqAdCRS0uYMGTin9vWGxUBWimJEHEKHrZfN29hqBEANDsR4izgM244fzeAhn9pVa3mOp6tFOxN7xpDY26PiK8ngPzQ9-TfPhURnEXLxhylhR5w6KWyxUC9g?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"226\" height=\"138\"></strong><br>Efficiency of raju = 5 - 4 = 1 unit<br>Time taken by Raju alone = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 200 hours</p>",
                    solution_hi: "<p>56.(a)<br><strong id=\"docs-internal-guid-b954d237-7fff-a1ba-95f1-16c40ee858e9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXda5Q0_KB_uF7k8zz8sUwIj5Sym5bDj3VmzXI4u4fm98_DXJb00F44N5qn0zOKdNXdCkOrR30w5i6vmZZoRQKiY4DGj9bj2gwwov8ulrGFyCz0eqvJL4p_HYozVV1oqVeCbeY7SuA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"191\" height=\"144\"></strong><br>राजू की क्षमता = 5 - 4 = 1 unit<br>अकेले राजू द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 200 घंटे</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. The marked price of an electronic watch in a store is ₹15,620 and it is available at a discount of 27%. What is the price (in ₹, to the nearest tens) that a customer pays if he buys from the store?</p>",
                    question_hi: "<p>57. किसी स्टोर में एक इलेक्ट्रॉनिक घड़ी का अंकित मूल्य ₹15,620 है और यह 27% की छूट पर उपलब्ध है। यदि कोई ग्राहक स्टोर से खरीदारी करता है, तो उसके द्वारा चुकाया गया मूल्य (₹ में, निकटतम दहाई स्थान तक) कितना है?</p>",
                    options_en: ["<p>12,500</p>", "<p>11,400</p>", 
                                "<p>9,880</p>", "<p>10,800</p>"],
                    options_hi: ["<p>12,500</p>", "<p>11,400</p>",
                                "<p>9,880</p>", "<p>10,800</p>"],
                    solution_en: "<p>57.(b)<br>Price customer pays = 15,620 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>73</mn><mn>100</mn></mfrac></math> = 11402.60 &sim; 11400</p>",
                    solution_hi: "<p>57.(b)<br>ग्राहक द्वारा भुगतान की गई कीमत = 15,620 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>73</mn><mn>100</mn></mfrac></math> = 11402.60 &sim; 11400</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. If p = <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi></mrow></mfrac></math> , then <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow></mfrac></math> = ?</p>",
                    question_hi: "<p>58. यदि p = <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi></mrow></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>-</mo><mi>cosA</mi></mrow></mfrac></math> का मान किसके बराबर है?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>p</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>-</mo><mi>&#160;</mi><mi>p</mi></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>p</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math></p>", "<p>p - 1</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>p</mi></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>-</mo><mi>&#160;</mi><mi>p</mi></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>p</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math></p>", "<p>p - 1</p>"],
                    solution_en: "<p>58.(a)<br><math display=\"inline\"><mo>&#8658;</mo></math> p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>+</mo><mi>cosA</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>+</mo><mi>cosA</mi></mrow></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cosA</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cosA</mi><mo>)</mo></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cosA</mi><mo>)</mo></mrow><mi>sinA</mi></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cosA</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">P</mi></mfrac></math></p>",
                    solution_hi: "<p>58.(a)<br><math display=\"inline\"><mo>&#8658;</mo></math> p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>+</mo><mi>cosA</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mn>1</mn><mo>+</mo><mi>cosA</mi></mrow></mfrac></math>&nbsp;&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cosA</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sinA</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cosA</mi><mo>)</mo></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">A</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cosA</mi><mo>)</mo></mrow><mi>sinA</mi></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cosA</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">P</mi></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Half litre of a solution contains 15% of alcohol. To change the alcohol concentration to 50%, find the quantity of alcohol to be mixed.</p>",
                    question_hi: "<p>59. एक घोल के आधे लीटर में 15% एल्कोहॉल है। एल्कोहॉल की सांद्रता को 50% में बदलने के लिए, मिश्रित की जाने वाली एल्कोहॉल की मात्रा ज्ञात कीजिए।</p>",
                    options_en: ["<p>250 ml</p>", "<p>350 ml</p>", 
                                "<p>175 ml</p>", "<p>400 ml</p>"],
                    options_hi: ["<p>250 ml</p>", "<p>350 ml</p>",
                                "<p>175 ml</p>", "<p>400 ml</p>"],
                    solution_en: "<p>59.(b)<br>Ratio &rarr;&nbsp; &nbsp; &nbsp;Alcohol&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;Water<br>Initial &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;3&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 17&nbsp; &nbsp; &nbsp; &rarr; total = 20 unit<br>Final &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; 1) &times; 17 = 17 : 17<br>Part of the alcohol that was added = 17 - 3 = 14 units<br>20 unit = 500 ml<br>14 unit = <math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 14 = 25 &times; 14 = 350 ml</p>",
                    solution_hi: "<p>59.(b)<br>अनुपात &rarr;&nbsp; &nbsp; &nbsp;अल्कोहल&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;पानी<br>आरंभिक &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 3&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;17&nbsp; &nbsp; &nbsp; &rarr; कुल = 20 इकाई<br>अंतिम &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 1) &times; 17 = 17 : 17<br>मिलाए गए अल्कोहल का भाग = 17 - 3 = 14 इकाई<br>20 इकाई = 500 ml<br>14 इकाई = <math display=\"inline\"><mfrac><mrow><mn>500</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> &times; 14 = 25 &times; 14 = 350 ml</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. The side BC of ∆ABC is produced to a point D. If AC = BC and &ang;BAC = 70&deg;, then find the value of 2.5&ang;ACD &minus; 1.5&ang;ABC.</p>",
                    question_hi: "<p>60. ∆ABC की भुजा BC को बिंदु D तक बढ़ाया गया है। यदि AC = BC और &ang;BAC = 70&deg; है, तो 2.5&ang;ACD &minus; 1.5&ang;ABC का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>245&deg;</p>", "<p>235&deg;</p>", 
                                "<p>225&deg;</p>", "<p>230&deg;</p>"],
                    options_hi: ["<p>245&deg;</p>", "<p>235&deg;</p>",
                                "<p>225&deg;</p>", "<p>230&deg;</p>"],
                    solution_en: "<p>60.(a)<br><strong id=\"docs-internal-guid-70db9d94-7fff-55b9-693b-de240e7df746\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdp1aM-F7YmfKW0g1ERxyPZBfjKWdi1oWKFH5u60Le_sjItPjJaZhCNQgVVSP2Nvhq8QfdeZ-6Gh7kxcL-X16zwjYpdUKHtB58xmC_sPcRo3J6I41Cf8y5zKct02oeDzIBml6zW?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"213\" height=\"131\"></strong><br>In <strong id=\"docs-internal-guid-9f1113f4-7fff-5dae-aad4-0156c8ade524\">∆</strong>ABC&nbsp;<br>&ang;A + &ang;B + &ang;C = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math><br>&ang;ACD = &ang;A + &ang;B &hellip; (exterior angle property)<br>&ang;ACD = <math display=\"inline\"><msup><mrow><mn>140</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> <br>now,<br>2.5 &times;&nbsp;&ang;ACD - 1.5 &ang;ABC<br>2.5 &times;&nbsp;140&deg; - 1.5 &times; 70&deg; = 350&deg; - 105&deg; = 245&deg;</p>",
                    solution_hi: "<p>60.(a)<br><strong id=\"docs-internal-guid-dfbf352d-7fff-504a-095f-bf35bb417f06\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdp1aM-F7YmfKW0g1ERxyPZBfjKWdi1oWKFH5u60Le_sjItPjJaZhCNQgVVSP2Nvhq8QfdeZ-6Gh7kxcL-X16zwjYpdUKHtB58xmC_sPcRo3J6I41Cf8y5zKct02oeDzIBml6zW?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"213\" height=\"131\"></strong><br><strong id=\"docs-internal-guid-9f1113f4-7fff-5dae-aad4-0156c8ade524\">∆</strong>ABC&nbsp; में<br>&ang;A + &ang;B + &ang;C = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math><br>&ang;ACD = &ang;A + &ang;B &hellip; (बाहरी कोण गुण)<br>&ang;ACD = <math display=\"inline\"><msup><mrow><mn>140</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> <br>अब,<br>2.5 &times;&nbsp;&ang;ACD - 1.5 &ang;ABC<br>2.5 &times;&nbsp;140&deg; - 1.5 &times; 70&deg; = 350&deg; - 105&deg; = 245&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. If 8cot&theta; = 7, then the value of <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> is :</p>",
                    question_hi: "<p>61. यदि 8cot&theta; = 7 है, तो <math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msqrt><mn>113</mn></msqrt></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>61.(a)<br>8 cot &theta; = 7&nbsp;<br>cot &theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>Base</mi><mi>Perpendicular</mi></mfrac></math><br>Hence, Hypotenuse = <math display=\"inline\"><msqrt><mn>113</mn></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mi>perpendicular</mi><mi>Hypotenuse</mi></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mi>Base</mi><mi>Hypotenuse</mi></mfrac></mstyle></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><msqrt><mn>113</mn></msqrt><mo>+</mo><mn>8</mn></mrow><msqrt><mn>113</mn></msqrt></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>113</mn></msqrt><mo>+</mo><mn>8</mn></mrow><mn>7</mn></mfrac></math></p>",
                    solution_hi: "<p>61.(a)<br>8 cot &theta; = 7&nbsp;<br>cot &theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>8</mn></mfrac></math> = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>&#2310;&#2343;&#2366;&#2352;</mi></mrow><mrow><mi>&#2354;&#2306;&#2348;</mi><mo>&#160;</mo></mrow></mfrac></math><br>अत:, कर्ण = <math display=\"inline\"><msqrt><mn>113</mn></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#952;</mi></mrow><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#952;</mi></mrow></mfrac></math> = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mi>&#2354;&#2306;&#2348;</mi><mi>&#2325;&#2352;&#2381;&#2339;</mi></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2354;&#2306;&#2348;</mi></mfrac></mstyle></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>8</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mrow><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><msqrt><mn>113</mn></msqrt><mo>+</mo><mn>8</mn></mrow><msqrt><mn>113</mn></msqrt></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><msqrt><mn>113</mn></msqrt></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>113</mn></msqrt><mo>+</mo><mn>8</mn></mrow><mn>7</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. If x = <math display=\"inline\"><msqrt><mn>6</mn><mi>&#160;</mi></msqrt></math>+ 2 and y = <math display=\"inline\"><msqrt><mn>6</mn><mi>&#160;</mi></msqrt></math> - 2, then what is the value of (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">x</mi></mfrac></math>)<sup>2</sup> - 3 ?</p>",
                    question_hi: "<p>62. यदि x = <math display=\"inline\"><msqrt><mn>6</mn><mi>&#160;</mi></msqrt></math>+ 2 और y = <math display=\"inline\"><msqrt><mn>6</mn><mi>&#160;</mi></msqrt></math> - 2, है, तो (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">y</mi></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">y</mi><mi mathvariant=\"normal\">x</mi></mfrac></math>)<sup>2</sup> - 3 का मान क्या होगा?</p>",
                    options_en: ["<p>42</p>", "<p>97</p>", 
                                "<p>35</p>", "<p>22</p>"],
                    options_hi: ["<p>42</p>", "<p>97</p>",
                                "<p>35</p>", "<p>22</p>"],
                    solution_en: "<p>62.(b) <strong>Given:</strong> x&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math> + 2 and y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math> - 2<br>= <math display=\"inline\"><mo>(</mo><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>y</mi></mrow><mrow><mi>x</mi></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - 3 <br>= <math display=\"inline\"><mo>(</mo><mfrac><mrow><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow></mfrac><mo>+</mo><mfrac><mrow><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - 3 <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>[</mo><mfrac><mrow><msup><mrow><mo>(</mo><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><msqrt><mn>6</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac><mo>]</mo></mrow><mn>2</mn></msup></math> - 3<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>[</mo><mfrac><mrow><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>6</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac><mo>]</mo></mrow><mn>2</mn></msup></math> - 3 <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>[</mo><mfrac><mn>20</mn><mn>2</mn></mfrac><mo>]</mo></mrow><mn>2</mn></msup></math> - 3<br>= 100 - 3 = 97</p>",
                    solution_hi: "<p>62.(b)<strong> दिया गया है: </strong>x&nbsp;= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math> + 2 and y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>6</mn></msqrt></math> - 2<br>= <math display=\"inline\"><mo>(</mo><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mi>y</mi></mrow><mrow><mi>x</mi></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - 3 <br>= <math display=\"inline\"><mo>(</mo><mfrac><mrow><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow></mfrac><mo>+</mo><mfrac><mrow><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn></mrow><mrow><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> - 3 <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>[</mo><mfrac><mrow><msup><mrow><mo>(</mo><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow><mrow><msup><mrow><mo>(</mo><msqrt><mn>6</mn></msqrt><mo>)</mo></mrow><mn>2</mn></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></mrow></mfrac><mo>]</mo></mrow><mn>2</mn></msup></math> - 3<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>[</mo><mfrac><mrow><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><msqrt><mn>6</mn></msqrt><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>6</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn><msqrt><mn>6</mn></msqrt></mrow><mrow><mn>6</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>4</mn></mrow></mfrac><mo>]</mo></mrow><mn>2</mn></msup></math> - 3 <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>[</mo><mfrac><mn>20</mn><mn>2</mn></mfrac><mo>]</mo></mrow><mn>2</mn></msup></math> - 3<br>= 100 - 3 = 97</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. In ∆ABC, DE || BC and 5AE = 3EC. If AB = 6.4 units, then the value of DB (in units) is:</p>",
                    question_hi: "<p>63. ∆ABC में, DE || BC है और 5AE = 3EC है। यदि AB = 6.4 इकाई है, तो DB का मान (इकाई में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>2.4</p>", "<p>5</p>", 
                                "<p>3.2</p>", "<p>4</p>"],
                    options_hi: ["<p>2.4</p>", "<p>5</p>",
                                "<p>3.2</p>", "<p>4</p>"],
                    solution_en: "<p>63.(d)<br>5AE&nbsp;= 3EC<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AE</mi><mi>EC</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br><strong id=\"docs-internal-guid-2b2ae782-7fff-0202-906b-efd00b305891\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeKsB5yDVec80FRta-GQ34wYQZay_pD-gs3Kg-PfOaGUOIT7tyejj0xUpG0WGJu7sYgEqpw8sCHBomACHyT2GgdWbIQaq-rhEpgL8dVU1YraB38T8M6sDgBvaMVcpykV03mRqsjrQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"142\" height=\"140\"></strong><br>DE <math display=\"inline\"><mo>&#8741;</mo></math> BC<br>Hence by BPT<br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>D</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AE</mi><mi>AC</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>D</mi></mrow><mrow><mn>6</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>8</mn></mfrac></math><br>AD = 2.4<br>DB = 6.4 - 2.4 = 4 unit</p>",
                    solution_hi: "<p>63.(d)<br>5AE&nbsp;= 3EC<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AE</mi><mi>EC</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math><br><strong id=\"docs-internal-guid-2b2ae782-7fff-0202-906b-efd00b305891\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXeKsB5yDVec80FRta-GQ34wYQZay_pD-gs3Kg-PfOaGUOIT7tyejj0xUpG0WGJu7sYgEqpw8sCHBomACHyT2GgdWbIQaq-rhEpgL8dVU1YraB38T8M6sDgBvaMVcpykV03mRqsjrQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"142\" height=\"140\"></strong><br>DE <math display=\"inline\"><mo>&#8741;</mo></math> BC<br>इसलिए BPT द्वारा<br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>D</mi></mrow><mrow><mi>A</mi><mi>B</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>AE</mi><mi>AC</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>A</mi><mi>D</mi></mrow><mrow><mn>6</mn><mo>.</mo><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>8</mn></mfrac></math><br>AD = 2.4<br>DB = 6.4 - 2.4 = 4 इकाई</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. If 2a - b = 3 and 8a<sup>3</sup> - b<sup>3</sup> = 999, then find the value of 4a<sup>2</sup> - b<sup>2</sup></p>",
                    question_hi: "<p>64. यदि 2a - b = 3 और 8a<sup>3</sup> - b<sup>3</sup> = 999 है, तो 4a<sup>2</sup> - b<sup>2</sup> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>63</p>", "<p>65</p>", 
                                "<p>61</p>", "<p>67</p>"],
                    options_hi: ["<p>63</p>", "<p>65</p>",
                                "<p>61</p>", "<p>67</p>"],
                    solution_en: "<p>64.(a)<br>2a&nbsp;- b = 3 &hellip;. (i)<br>On cubing both side we get,<br>8a<sup>3</sup>&nbsp;- b<sup>3</sup> - 3 &times; 2ab (2a - b) = 27&nbsp;<br>8a<sup>3</sup>&nbsp;- b<sup>3</sup> - 6ab &times; 3 = 27 <br>999 - 18ab&nbsp;= 27 <br>- 18ab&nbsp;= -972<br>ab = 54<br>ab = 9 &times; 6<br>Take the value of a = 6 and b = 9<br>2a&nbsp;- b = 3 <br>LHS = 2a - b = 2 &times; 6 - 9 = 3 = RHS<br>8a<sup>3</sup> - b<sup>3</sup> = 999<br>LHS = 8a<sup>3</sup>&nbsp;- b<sup>3</sup><br>= 8 &times; 216 - 729 = 999<br>Value of a and b satisfies the given equation,<br>So,<br>4a<sup>2</sup>&nbsp;- b<sup>2</sup> = 4 &times; 36 - 81 = 63</p>",
                    solution_hi: "<p>64.(a)<br>2a&nbsp;- b = 3 &hellip;. (i)<br>दोनों ओर से घन करने पर हमें प्राप्त होता है,<br>8a<sup>3</sup>&nbsp;- b<sup>3</sup> - 3 &times; 2ab (2a - b) = 27&nbsp;<br>8a<sup>3</sup>&nbsp;- b<sup>3</sup> - 6ab &times; 3 = 27 <br>999 - 18ab&nbsp;= 27 <br>- 18ab&nbsp;= -972<br>ab = 54<br>ab = 9 &times; 6<br>a = 6 और b = 9 का मान रखने पर <br>2a&nbsp;- b = 3 <br>LHS = 2a - b = 2 &times; 6 - 9 = 3 = RHS<br>8a<sup>3</sup> - b<sup>3</sup> = 999<br>LHS = 8a<sup>3</sup>&nbsp;- b<sup>3</sup><br>= 8 &times; 216 - 729 = 999<br>a और b का मान दिए गए समीकरण को संतुष्ट करता है,<br>इसलिए,<br>4a<sup>2</sup>&nbsp;- b<sup>2</sup> = 4 &times; 36 - 81 = 63</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Study the given bar graph and answer the question that follows.<br>The following bar graph shows the number of deaths that happened in cities 1, 2, 3 and 4 in the months mentioned.<br><strong id=\"docs-internal-guid-3ebfb74f-7fff-604e-7836-758c2eb96dd7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdEvrH769JE_U5lyIZNzw1ZNh_g_GriVZ45qKntk61fA8ixAtbZaRRdAuWSoNL5dcVOZSWdBlhXe7daeVd8ttygrGY9Sb8IjpYPWS-LO9rO4drJjTzWm7jQKxC6WtHV-O7pRWBxXQ?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"416\" height=\"252\"></strong><br>The average number of deaths that occurred during April across all the cities (rounded off to the next integer) is:</p>",
                    question_hi: "<p>65. दिए गए बार-ग्राफ का अध्ययन कीजिए और इसके बाद आगे दिए गए प्रश्न का उत्तर दीजिए।<br>निम्नलिखित बार ग्राफ उल्लिखित महीनों में शहर 1, 2, 3 और 4 में हुई मौतों की संख्या को दर्शाता है।<br><strong id=\"docs-internal-guid-2cf86372-7fff-ef72-5000-f995adec77e9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXct-jmSXIf3Y4_zXGtBeGZsi5GLuED_lB9C62HBfR9J1jUfCfbpfmK2-bE8GC0yfUt2ub48W7itwhiw-liboEi1bFKrN_BLmIY2LYJ2hUDrN-5nNUCQhwq-mi8NB5X51rFh02TX?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"440\" height=\"264\"></strong><br>सभी शहरों में अप्रैल के दौरान होने वाली मौतों की औसत संख्या (अगले पूर्णांक तक पूर्णांकित) ज्ञात कीजिए।</p>",
                    options_en: ["<p>928</p>", "<p>934</p>", 
                                "<p>953</p>", "<p>965</p>"],
                    options_hi: ["<p>928</p>", "<p>934</p>",
                                "<p>953</p>", "<p>965</p>"],
                    solution_en: "<p>65.(b)<br>Average number of death during April in all cities = <math display=\"inline\"><mfrac><mrow><mn>860</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>765</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>960</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1148</mn><mi>&#160;</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3733</mn><mn>4</mn></mfrac></math> = 933.33 &sim; 934</p>",
                    solution_hi: "<p>65.(b)<br>सभी शहरों में अप्रैल के दौरान मृत्यु की औसत संख्या = <math display=\"inline\"><mfrac><mrow><mn>860</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>765</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>960</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1148</mn><mi>&#160;</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3733</mn><mn>4</mn></mfrac></math> = 933.33 &sim; 934</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If the radius and height of a right circular cylinder are 21 cm and 5 cm,. respectively, then the total surface area of the cylinder is (use &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    question_hi: "<p>66. यदि एक लम्ब वृत्तीय बेलन की त्रिज्या और ऊँचाई क्रमशः 21 cm और 5 cm है, तो बेलन का संपूर्ण पृष्ठीय क्षेत्रफल क्या है (&pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> का उपयोग कीजिए)?</p>",
                    options_en: ["<p>4312 cm<sup>2</sup></p>", "<p>3432 cm<sup>2</sup></p>", 
                                "<p>5212 cm<sup>2</sup></p>", "<p>3816 cm<sup>2</sup></p>"],
                    options_hi: ["<p>4312 cm<sup>2</sup></p>", "<p>3432 cm<sup>2</sup></p>",
                                "<p>5212 cm<sup>2</sup></p>", "<p>3816 cm<sup>2</sup></p>"],
                    solution_en: "<p>66.(b)<br>T.S.A. of cylinder = <math display=\"inline\"><mn>2</mn><mi>&#960;</mi><mi>r</mi><mo>(</mo><mi>h</mi><mo>+</mo><mi>r</mi><mo>)</mo></math> = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 21 (21 + 5) = 44 &times; 3 &times; 26 = 3432 cm<sup>2</sup></p>",
                    solution_hi: "<p>66.(b)<br>बेलन का कुल पृष्ठीय क्षेत्रफल = <math display=\"inline\"><mn>2</mn><mi>&#960;</mi><mi>r</mi><mo>(</mo><mi>h</mi><mo>+</mo><mi>r</mi><mo>)</mo></math> = 2 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 21 (21 + 5) = 44 &times; 3 &times; 26 = 3432 cm<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. In a circle with center O, an arc ABC subtends an angle of 138&deg; at the centre of the circle. The chord AB is produced to a point P. Then, the measure of &ang;CBP is:</p>",
                    question_hi: "<p>67. O केंद्र वाले वृत्त में, एक चाप ABC वृत्त के केंद्र पर 138&deg; का कोण अंतरित करता है। जीवा AB को बिंदु P तक बढ़ाया जाता है। तो, &ang;CBP की माप कितनी होगी?</p>",
                    options_en: ["<p>108&deg;</p>", "<p>42&deg;</p>", 
                                "<p>111&deg;</p>", "<p>69&deg;</p>"],
                    options_hi: ["<p>108&deg;</p>", "<p>42&deg;</p>",
                                "<p>111&deg;</p>", "<p>69&deg;</p>"],
                    solution_en: "<p>67.(d)<br><strong id=\"docs-internal-guid-b4d947a9-7fff-ac71-58dc-79f843b7ac9b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd-gjL-T7hIZ_8etJ2b6pfi8M9aLMdPMrSqBGvwBm4Orhlf1nv5u-LdpxvRY2f197mI2b2ZqKxeo3S8E_sA_owxHTzhrefcPl2yIiRtS3jFS2icJyPLySpgdfMPOTTQ_JXTJZmeWA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"188\" height=\"228\"></strong><br>&ang;ADC = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 138&deg; = 69&deg; <br>Now,<br>&ang;ABC = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - 69&deg; = 111&deg;<br>&ang;ABC + &ang;CBP = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> &hellip; (linear pair)<br>&ang;CBP = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - 111&deg; = 69&deg;</p>",
                    solution_hi: "<p>67.(d)<br><strong id=\"docs-internal-guid-b4d947a9-7fff-ac71-58dc-79f843b7ac9b\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXd-gjL-T7hIZ_8etJ2b6pfi8M9aLMdPMrSqBGvwBm4Orhlf1nv5u-LdpxvRY2f197mI2b2ZqKxeo3S8E_sA_owxHTzhrefcPl2yIiRtS3jFS2icJyPLySpgdfMPOTTQ_JXTJZmeWA?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"188\" height=\"228\"></strong><br>&ang;ADC = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 138&deg; = 69&deg; <br>अब,<br>&ang;ABC = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - 69&deg; = 111&deg;<br>&ang;ABC + &ang;CBP = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> &hellip; (रैखिक युग्म)<br>&ang;CBP = <math display=\"inline\"><msup><mrow><mn>180</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> - 111&deg; = 69&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Raman fixes the sale price of his goods at 16% above the cost price. He sells his goods at 12% less than the fixed price. Find the profit percentage correct to two places of decimal.</p>",
                    question_hi: "<p>68. रमन अपनी वस्तुओं का विक्रय मूल्य क्रय मूल्य से 16% अधिक पर निर्धारित करता है। वह अपनी वस्तुएं निर्धारित मूल्य से 12% कम पर बेचता है। दो दशमलव स्थान तक सही लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>1.07%</p>", "<p>2.08%</p>", 
                                "<p>0.08%</p>", "<p>3.01%</p>"],
                    options_hi: ["<p>1.07%</p>", "<p>2.08%</p>",
                                "<p>0.08%</p>", "<p>3.01%</p>"],
                    solution_en: "<p>68.(b)<br>Ratio&nbsp; &nbsp; &rarr;&nbsp; CP&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;MP&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;SP<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;29&nbsp; &nbsp; &nbsp; : <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;22<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &mdash;-------------------------------------------<br>Final&nbsp; &nbsp; &rarr;&nbsp; &nbsp; &nbsp;625&nbsp; &nbsp; :&nbsp; &nbsp; 725&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;638<br>Profit % = <math display=\"inline\"><mfrac><mrow><mn>638</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>625</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>25</mn></mfrac></math> &times; 4 = 2.08%</p>",
                    solution_hi: "<p>68.(b)<br>अनुपात&nbsp; &nbsp; &rarr; क्रय मूल्य&nbsp; &nbsp; :&nbsp; &nbsp; अंकित मूल्य&nbsp; &nbsp; :&nbsp; &nbsp; विक्रय मूल्य <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 29&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;: <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;22<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&mdash;--------------------------------------------------------<br>अंतिम &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 625&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 725&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;638<br>लाभ % = <math display=\"inline\"><mfrac><mrow><mn>638</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>625</mn></mrow><mrow><mn>625</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>25</mn></mfrac></math> &times; 4 = 2.08%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Study the below data and answer the question that follows.<br><strong id=\"docs-internal-guid-cda5b05e-7fff-7460-b383-3ccf57ba0158\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXev_WhyaFwKqHNKG2BVxbzAh2rhLJSSMH_8Sk_g0Orddxb99MKgQ4WMdfKelG54aFaz-pbNc9_dJLH9CJQrFbWEUfoBzgmPJ9NZXmhRmBaI0oUz-rAeBvU44kAsMo-JosN0ZHPkXg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"255\" height=\"215\"></strong><br>The company has issued 10 lakh shares between its five partners. If Jeevan offers to sell 20,000 of his shares to Meera, how many shares will Meera have?</p>",
                    question_hi: "<p>69. निम्न आँकड़ों का अध्ययन कीजिए और प्रश्न का उत्तर दीजिए।<br><strong id=\"docs-internal-guid-674aded2-7fff-d2ad-6b9a-b567cb268af2\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfKAyqdRipY3qj9b6u12cYUnrQwmsoUwR87tdusWKDHc6l2SuqAW88rQeCLvuQiTPwGaC6WR05xVQB21LSNgPc5CKO0QrNcVesrHgbUEA4EUj2_mV2qDHalNiTkdjhkopmH4h7AIg?key=Nt079UcDJ8wH8Zqhx8u1PKE7\" width=\"224\" height=\"223\"></strong><br>कंपनी ने अपने पाँच साझेदारों के बीच 10 लाख शेयर जारी किए हैं। यदि जीवन मीरा को अपने 20,000 शेयर बेचने की पेशकश करता है, तो मीरा के पास कितने शेयर होंगे?</p>",
                    options_en: ["<p>5,10,000</p>", "<p>4,80,000</p>", 
                                "<p>4,60,000</p>", "<p>4,70,000</p>"],
                    options_hi: ["<p>5,10,000</p>", "<p>4,80,000</p>",
                                "<p>4,60,000</p>", "<p>4,70,000</p>"],
                    solution_en: "<p>69.(d)<br>Shares Meera has = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 10 lakh = 4.5 lakh<br>After receiving 20,000 shares from Jeevan<br>Meera now has = 4.5 lakh + 20,000 = 4.7 lakh</p>",
                    solution_hi: "<p>69.(d)<br>मीरा के शेयर = <math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 10 लाख = 4.5 लाख<br>जीवन से 20,000 शेयर प्राप्त करने के बाद<br>मीरा के पास कुल शेयर = 4.5 लाख + 20,000 = 4.7 लाख</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. The table given below shows the distribution of employees in four different companies in 2022.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731555512764.png\" alt=\"rId61\"> <br>Find the total number of male employees working in company D in 2022.</p>",
                    question_hi: "<p>70. नीचे दी गई तालिका 2022 में चार विभिन्न कंपनियों में कर्मचारियों का बंटन दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1731555512891.png\" alt=\"rId62\"> <br>2022 में कंपनी D में कार्यरत पुरुष कर्मचारियों की कुल संख्या ज्ञात करें।</p>",
                    options_en: ["<p>6000</p>", "<p>6750</p>", 
                                "<p>6250</p>", "<p>6500</p>"],
                    options_hi: ["<p>6000</p>", "<p>6750</p>",
                                "<p>6250</p>", "<p>6500</p>"],
                    solution_en: "<p>70.(c)<br>Employees in company D = 25 &times;&nbsp;450 = 11250<br>Male employee in company D = 11250 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math> = 6250</p>",
                    solution_hi: "<p>70.(c)<br>कंपनी D में कर्मचारी = 25 &times;&nbsp;450 = 11250<br>कंपनी D में पुरुष कर्मचारी = 11250 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>9</mn></mfrac></math>= 6250</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. In △ABC, right angled at B if tanC = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt><mo>,</mo><mo>&#160;&#160;</mo><mi>then</mi><mi mathvariant=\"normal\">&#160;</mi><mi>find</mi><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">C</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">C</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">C</mi></mrow></mfrac></math></p>",
                    question_hi: "<p>71. △ABC में, B पर समकोण है। यदि tanC = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mi>s</mi><mi>i</mi><mi>n</mi></mrow><mn>2</mn></msup><mi>C</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>s</mi></mrow><mn>2</mn></msup><mi>C</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>o</mi><mi>t</mi></mrow><mn>2</mn></msup><mi>C</mi></mrow></mfrac></math>ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>16</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>71.(a)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">C</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">C</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">a</mi><msup><mrow><mi mathvariant=\"bold-italic\">n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">C</mi></mrow></mfrac></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>71.(a)<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">C</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">C</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">C</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mi mathvariant=\"bold-italic\">t</mi><mi mathvariant=\"bold-italic\">a</mi><msup><mrow><mi mathvariant=\"bold-italic\">n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">C</mi></mrow></mfrac></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>3</mn></mfrac></mstyle></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. What principal would amount to ₹21,420 in 2 years at the rate of 9.5% p.a. simple interest?</p>",
                    question_hi: "<p>72. 9.5% वार्षिक साधारण ब्याज की दर से 2 वर्षों में कितना मूलधन ₹21,420 हो जाएगा?</p>",
                    options_en: ["<p>₹11,273</p>", "<p>₹12,000</p>", 
                                "<p>₹18,000</p>", "<p>₹16,000</p>"],
                    options_hi: ["<p>₹11,273</p>", "<p>₹12,000</p>",
                                "<p>₹18,000</p>", "<p>₹16,000</p>"],
                    solution_en: "<p>72.(c)<br>Interest in two years = 2 &times;&nbsp;9.5 % = 19 %<br>Principal = <math display=\"inline\"><mfrac><mrow><mn>21420</mn></mrow><mrow><mn>119</mn></mrow></mfrac></math> &times; 100 = 18,000</p>",
                    solution_hi: "<p>72.(c)<br>दो वर्षों में ब्याज = 2 &times;&nbsp;9.5 % = 19 %<br>मूलधन = <math display=\"inline\"><mfrac><mrow><mn>21420</mn></mrow><mrow><mn>119</mn></mrow></mfrac></math> &times; 100 = 18,000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The sum of two consecutive even numbers is 174. Find the smaller number.</p>",
                    question_hi: "<p>73. दो क्रमागत सम संख्याओं का योग 174 है। छोटी संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>84</p>", "<p>90</p>", 
                                "<p>86</p>", "<p>88</p>"],
                    options_hi: ["<p>84</p>", "<p>90</p>",
                                "<p>86</p>", "<p>88</p>"],
                    solution_en: "<p>73.(c)<br>Let two consecutive even no. = x, x + 2<br>According to question,<br>x + x + 2 = 174<br>2x&nbsp;= 172<br>x = 86</p>",
                    solution_hi: "<p>73.(c)<br>माना x, और x + 2 दो क्रमागत सम संख्याएं है ।&nbsp;<br>प्रश्न के अनुसार,<br>x + x + 2 = 174<br>2x&nbsp;= 172<br>x = 86</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A thief takes off on his bike at a certain speed, after seeing a police car at a distance of 250 m. The police car starts chasing the thief and catches him. If the thief runs 1.5 km before being caught and the speed of the police car is 70 km/h, then what is the speed of thief&rsquo;s bike (in km/h)?</p>",
                    question_hi: "<p>74. 250 m की दूरी पर एक पुलिस कार को देखकर एक चोर एक निश्चित चाल से अपनी बाइक से भागता है। पुलिस की गाड़ी चोर का पीछा करती है और उसे पकड़ लेती है। यदि चोर पकड़े जाने से पहले 1.5 km भागता है और पुलिस की कार की चाल 70 km/h है, तो चोर की बाइक की चाल (km/h में) कितनी है?</p>",
                    options_en: ["<p>65</p>", "<p>60</p>", 
                                "<p>55</p>", "<p>50</p>"],
                    options_hi: ["<p>65</p>", "<p>60</p>",
                                "<p>55</p>", "<p>50</p>"],
                    solution_en: "<p>74.(b)<br>Time taken by police car to cover 1750 m = <math display=\"inline\"><mfrac><mrow><mn>1750</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></mrow><mrow><mn>70</mn></mrow></mfrac></math> hr = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>1000</mn></mfrac></math> hr<br>Speed of thief = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mrow><mfrac><mrow><mn>25</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></mrow></mfrac></math> km/h = 60 km/h</p>",
                    solution_hi: "<p>74.(b)<br>पुलिस की गाड़ी को 1750 मीटर तय करने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>1750</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></mrow><mrow><mn>70</mn></mrow></mfrac></math> घंटे = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>1000</mn></mfrac></math> घंटे<br>चोर की गति = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mrow><mfrac><mrow><mn>25</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></mrow></mfrac></math> किमी/घंटा = 60 किमी/घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Suresh&rsquo;s expenditure and savings are in the ratio of 3 : 1. His income increases by 25%. If his savings increase by 20%, then by how much percentage does his expenditure increase?</p>",
                    question_hi: "<p>75. सुरेश के खर्च और बचत का अनुपात 3 : 1 है। उसका वेतन 25% बढ़ जाता है। यदि उसकी बचत 20% बढ़ जाए तो उसका खर्च कितने प्रतिशत बढ़ेगा?</p>",
                    options_en: ["<p>27<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>25<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", 
                                "<p>28<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>26<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>27<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>25<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>",
                                "<p>28<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>", "<p>26<math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>75.(d)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Expenditure + saving = Income<br>Initial &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 30&nbsp; &nbsp; &nbsp; &nbsp; +&nbsp; &nbsp; &nbsp; &nbsp;10&nbsp; = 40 unit<br>Final &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; x&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;+&nbsp; &nbsp; &nbsp; &nbsp; 12 = 50 unit<br>x = 50 - 12 = 38<br>Increase in expenditure = <math display=\"inline\"><mfrac><mrow><mn>38</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>30</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>30</mn></mfrac></math> &times; 100 = 26<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</p>",
                    solution_hi: "<p>75.(d)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; व्यय + बचत = आय<br>प्रारंभिक &rarr;&nbsp; &nbsp;&nbsp; 30 + 10&nbsp; &nbsp;= 40 इकाई<br>अंतिम &rarr;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; x + 12&nbsp; &nbsp; = 50 इकाई<br>x = 50 - 12 = 38<br>व्यय में वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>38</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>30</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>30</mn></mfrac></math> &times; 100 = 26<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math>%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate option to fill in the blank. <br>Just bite the ______ and tell him the truth.</p>",
                    question_hi: "<p>76. Select the most appropriate option to fill in the blank. <br>Just bite the ______ and tell him the truth.</p>",
                    options_en: ["<p>bullet</p>", "<p>gun</p>", 
                                "<p>dust</p>", "<p>shot</p>"],
                    options_hi: ["<p>bullet</p>", "<p>gun</p>",
                                "<p>dust</p>", "<p>shot</p>"],
                    solution_en: "<p>76.(a) bullet<br>&lsquo;Bite the bullet&rsquo; is an idiom which means to face a difficult situation bravely. The given sentence expresses an urge to just bite the bullet and tell him the truth. Hence, &lsquo;bullet&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(a) bullet<br>&lsquo;Bite the bullet&rsquo; एक idiom है, जिसका <br>अर्थ है किसी मुश्किल परिस्थिति का बहादुरी से सामना करना। दिए गए sentence, साहस दिखाने और उसे सच बताने का अनुरोध(urge) व्यक्त करता है। अतः, &lsquo;bullet&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "77. Select the most appropriate option to fill in the blank. <br />I like bread made up of corn __________.",
                    question_hi: "77. Select the most appropriate option to fill in the blank. <br />I like bread made up of corn __________.",
                    options_en: [" flower  ", " flour ", 
                                " floor  ", " failure "],
                    options_hi: [" flower  ", " flour ",
                                " floor  ", " failure "],
                    solution_en: "77.(b) flour<br />‘Flour’ means powdered grain, especially of wheat. The given sentence states that I like bread made up of corn. Hence, ‘flour’ is the most appropriate answer.",
                    solution_hi: "77.(b) flour<br />‘Flour’ का अर्थ है पिसा हुआ अनाज, विशेष रूप से गेहूँ का। दिए गए sentence में कहा गया है कि मुझे मक्के (corn) के आटे(flour) से बनी रोटी (bread) पसंद है। अतः, ‘flour’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the option that can be used as a one-word substitute for the given group of words. <br>Loud enough to be heard.</p>",
                    question_hi: "<p>78. Select the option that can be used as a one-word substitute for the given group of words. <br>Loud enough to be heard.</p>",
                    options_en: ["<p>Audible</p>", "<p>Audit</p>", 
                                "<p>Audacious</p>", "<p>Audacity</p>"],
                    options_hi: ["<p>Audible</p>", "<p>Audit</p>",
                                "<p>Audacious</p>", "<p>Audacity</p>"],
                    solution_en: "<p>78.(a) <strong>Audible-</strong> loud enough to be heard.<br><strong>Audit-</strong> an official inspection of an organization\'s accounts, typically by an independent body.<br><strong>Audacious-</strong> showing a willingness to take surprisingly bold risks.<br><strong>Audacity-</strong> a willingness to take bold risks.</p>",
                    solution_hi: "<p>78.(a) <strong>Audible</strong> (सुनाई देने योग्य)- loud enough to be heard.<br><strong>Audit</strong> (लेखा परीक्षा)- an official inspection of an organization\'s accounts, typically by an independent body.<br><strong>Audacious</strong> (साहसी)- showing a willingness to take surprisingly bold risks.<br><strong>Audacity</strong> (साहसिकता)- a willingness to take bold risks.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate ANTONYM of the given word. <br>Insert</p>",
                    question_hi: "<p>79.Select the most appropriate ANTONYM of the given word. <br>Insert</p>",
                    options_en: ["<p>Remove</p>", "<p>Inform</p>", 
                                "<p>Select</p>", "<p>Inject</p>"],
                    options_hi: ["<p>Remove</p>", "<p>Inform</p>",
                                "<p>Select</p>", "<p>Inject</p>"],
                    solution_en: "<p>79.(a) <strong>Remove-</strong> to take something away or off.<br><strong>Insert-</strong> to put something into something else.<br><strong>Inform-</strong> to give someone facts or information.<br><strong>Select-</strong> to carefully choose something from a group.<br><strong>Inject-</strong> to introduce a substance into something, typically with a syringe.</p>",
                    solution_hi: "<p>79.(a) <strong>Remove</strong> (हटाना)- to take something away or off.<br><strong>Insert</strong> ( सम्मलित करना)- to put something into something else.<br><strong>Inform</strong> (सूचना देना)- to give someone facts or information.<br><strong>Select</strong> (चुनना)- to carefully choose something from a group.<br><strong>Inject</strong> (प्रविष्ट कराना)- to introduce a substance into something, typically with a syringe.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate option that can substitute the underlined word in the given sentence. <br><span style=\"text-decoration: underline;\">Verity</span> is a good virtue for human life.</p>",
                    question_hi: "<p>80. Select the most appropriate option that can substitute the underlined word in the given sentence. <br><span style=\"text-decoration: underline;\">Verity</span> is a good virtue for human life.</p>",
                    options_en: ["<p>Knowledge</p>", "<p>Truth</p>", 
                                "<p>Information</p>", "<p>Cooperation </p>"],
                    options_hi: ["<p>Knowledge</p>", "<p>Truth</p>",
                                "<p>Information</p>", "<p>Cooperation</p>"],
                    solution_en: "<p>80.(b) Truth<br>&lsquo;Verity&rsquo; means the state of being true. The given sentence states that truth is a good virtue for human life. Hence, \'Truth\' is the most appropriate answer.</p>",
                    solution_hi: "<p>80.(b) Truth<br>&lsquo;Verity&rsquo; का अर्थ है सत्य होने की स्थिति। दिए गए sentence में बताया गया है कि truth, human life के लिए एक अच्छा गुण है। अतः, \'Truth\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "81. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. Founding father / is / of / New Historicism / Greenblatt / considered / the. ",
                    question_hi: "81.Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. Founding father / is / of / New Historicism / Greenblatt / considered / the. ",
                    options_en: [" The founding father of New Historicism is considered Greenbllat.  ", " The founding father is considered Greenblatt of New Historicism.  ", 
                                " Greenblatt is considered the founding father of New Historicism. ", " Greenblatt is the founding father of New Historicism considered."],
                    options_hi: [" The founding father of New Historicism is considered Greenbllat.  ", " The founding father is considered Greenblatt of New Historicism.  ",
                                " Greenblatt is considered the founding father of New Historicism. ", " Greenblatt is the founding father of New Historicism considered."],
                    solution_en: "81.(c) Greenblatt is considered the founding father of New Historicism.",
                    solution_hi: "81.(c) Greenblatt is considered the founding father of New Historicism.",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the option that expresses the following sentence in active voice. <br>The issue has been successfully resolved by the customer care team.</p>",
                    question_hi: "<p>82.Select the option that expresses the following sentence in active voice. <br>The issue has been successfully resolved by the customer care team.</p>",
                    options_en: ["<p>The customer care team successfully resolves the issue.</p>", "<p>The customer care team successfully resolved the issue.</p>", 
                                "<p>The issue has successfully resolved the customer care team.</p>", "<p>The customer care team has successfully resolved the issue.</p>"],
                    options_hi: ["<p>The customer care team successfully resolves the issue.</p>", "<p>The customer care team successfully resolved the issue.</p>",
                                "<p>The issue has successfully resolved the customer care team.</p>", "<p>The customer care team has successfully resolved the issue.</p>"],
                    solution_en: "<p>82.(d) The customer care team has successfully resolved the issue. (Correct)<br>(a) The customer care team successfully <span style=\"text-decoration: underline;\">resolves</span> the issue. (Incorrect Tense)<br>(b) The customer care team successfully <span style=\"text-decoration: underline;\">resolved</span> the issue. (Incorrect Tense)<br>(c) The issue has successfully resolved the customer care team. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>82.(d) The customer care team has successfully resolved the issue. (Correct)<br>(a) The customer care team successfully <span style=\"text-decoration: underline;\">resolves</span> the issue. (गलत Tense)<br>(b) The customer care team successfully <span style=\"text-decoration: underline;\">resolved</span> the issue. (गलत Tense)<br>(c) The issue has successfully resolved the customer care team. (गलत Sentence Structure)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate verb / verb phrase to fill in the blank. <br>The team ________________ the championship before their star player got injured.</p>",
                    question_hi: "<p>83. Select the most appropriate verb / verb phrase to fill in the blank. <br>The team ________________ the championship before their star player got injured.</p>",
                    options_en: ["<p>was won</p>", "<p>had won</p>", 
                                "<p>has won</p>", "<p>win</p>"],
                    options_hi: ["<p>was won</p>", "<p>had won</p>",
                                "<p>has won</p>", "<p>win</p>"],
                    solution_en: "<p>83.(b) had won<br>If two actions took place in the past then the 1st action must be in the Past perfect tense(Had + V<sub>3</sub>) and the 2nd action must be in the Simple Past tense (V<sub>2</sub>). The first action is winning the championship. Hence, &lsquo;had won (V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>83.(b) had won<br>यदि दो actions, past में हुए हैं तो पहला action, past perfect tense (Had + V<sub>3</sub>) में होना चाहिए और दूसरा action , simple past tense (V<sub>2</sub>) में होना चाहिए। Championship जीतना first action है। अतः, \'had won (V<sub>3</sub>)\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the option that can be used as a one-word substitute for the given group of words. <br>Money collected from people for a cause.</p>",
                    question_hi: "<p>84. Select the option that can be used as a one-word substitute for the given group of words. <br>Money collected from people for a cause.</p>",
                    options_en: ["<p>Collection</p>", "<p>Deposit</p>", 
                                "<p>Fund</p>", "<p>Savings</p>"],
                    options_hi: ["<p>Collection</p>", "<p>Deposit</p>",
                                "<p>Fund</p>", "<p>Savings</p>"],
                    solution_en: "<p>84.(c) <strong>Fund-</strong> money collected from people for a cause.</p>",
                    solution_hi: "<p>84.(c) <strong>Fund-</strong> money collected from people for a cause./किसी उद्देश्य के लिए लोगों से एकत्रित की गई धनराशि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "85. Select the most appropriate option that can rectify the incorrectly spelt word in the following sentence. In our quest for power, we often fail to acknowlege the existence of other people. ",
                    question_hi: "85.  Select the most appropriate option that can rectify the incorrectly spelt word in the following sentence. In our quest for power, we often fail to acknowlege the existence of other people. ",
                    options_en: [" acknowledje ", " accknowledge ", 
                                " acknowledge ", " acknowlede"],
                    options_hi: [" acknowledje ", " accknowledge ",
                                " acknowledge ", " acknowlede"],
                    solution_en: "85.(c) acknowledge<br />\'Acknowledge\' is the correct spelling. ",
                    solution_hi: "85.(c) acknowledge<br />\'Acknowledge\' सही spelling है।  ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "86.  Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. <br />A.He remembered her as she once was – an eager, glowing girl. <br />B.He had not seen her for nearly thirteen years. <br />C.Mr. Satterthwaite went to meet her.<br />D.And now he saw – a Frozen Lady.",
                    question_hi: "86. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph. <br />A.He remembered her as she once was – an eager, glowing girl. <br />B.He had not seen her for nearly thirteen years. <br />C.Mr. Satterthwaite went to meet her.<br />D.And now he saw – a Frozen Lady.",
                    options_en: [" DCBA", " CBAD ", 
                                " DBAC ", " ABCD"],
                    options_hi: [" DCBA", " CBAD ",
                                " DBAC ", " ABCD"],
                    solution_en: "<p>86.(b) <strong>CBAD</strong><br>Sentence C will be the starting line as it introduces the main idea of the parajumble i.e. &lsquo;Mr. Satterthwaite went to meet her.&rsquo; And, Sentence B states that he had not seen her for nearly thirteen years. So, B will follow C. Further, Sentence A states that he remembered her as an eager, glowing girl &amp; Sentence D states that now he saw a Frozen Lady. So, D will follow A. Going through the options, option (b) has the correct sequence.</p>",
                    solution_hi: "<p>86.(b) <strong>CBAD</strong><br>Sentence C प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार &lsquo;Mr. Satterthwaite went to meet her&rsquo; का परिचय देता है। और, Sentence B बताता है कि उसने उसे लगभग तेरह वर्षों से नहीं देखा था। इसलिए, C के बाद B आएगा। इसके अलावा, Sentence A बताता है कि उसे वह एक eager, glowing girl के रूप में याद है और Sentence D बताता है कि अब उसने एक Frozen Lady को देखा। इसलिए, A के बाद D आएगा। अतः options के माध्यम से जाने पर option (b) में सही sequence है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "87. Identify from the given options the word which is similar in meaning to the following word. <br />Munificent ",
                    question_hi: "87.Identify from the given options the word which is similar in meaning to the following word. <br />Munificent ",
                    options_en: [" Magnanimous ", " Malicious ", 
                                " Miserly ", " Underappreciated"],
                    options_hi: [" Magnanimous ", " Malicious ",
                                " Miserly ", " Underappreciated"],
                    solution_en: "<p>87.(a) <strong>Magnanimous-</strong> generous or forgiving, especially towards a rival.<br><strong>Munificent-</strong> very generous or giving.<br><strong>Malicious-</strong> intending to do harm or cause suffering.<br><strong>Miserly-</strong> unwilling to spend or give; stingy.<br><strong>Underappreciated-</strong> not sufficiently recognized or valued.</p>",
                    solution_hi: "<p>87.(a) <strong>Magnanimous</strong> (उदार)- generous or forgiving, especially towards a rival.<br><strong>Munificent</strong> (दानशील)- very generous or giving.<br><strong>Malicious</strong> (द्वेषपूर्ण)- intending to do harm or cause suffering.<br><strong>Miserly</strong> (कृपण/कंजूस)- unwilling to spend or give; stingy.<br><strong>Underappreciated</strong> (अल्पमानित)- not sufficiently recognized or valued.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "88. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br />P) once said <br />Q) a wise man <br />R) that action speaks <br />S) louder than words ",
                    question_hi: "88. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence. <br />P) once said <br />Q) a wise man <br />R) that action speaks <br />S) louder than words ",
                    options_en: [" QSRP ", " QPRS ", 
                                " PQRS ", " SQPR"],
                    options_hi: [" QSRP ", " QPRS ",
                                " PQRS ", " SQPR"],
                    solution_en: "88.(b) QPRS<br />The given sentence starts with Part Q as it introduces the main subject of the sentence, i.e. A wise man. Part Q will be followed by Part P as it contains the main verb of the sentence, i.e. said. Further, Part R contains the subject and the verb of the second clause & Part S states completes the second clause by stating that actions speak louder than words. So, S will follow R. Going through the options, option ‘b’ has the correct sequence.",
                    solution_hi: "88.(b) QPRS<br />दिया गया sentence, Part Q से प्रारंभ होगा क्योंकि इसमें sentence का मुख्य विषय, ‘A wise man’ शामिल है। Part Q के बाद Part P आएगा क्योंकि इसमें sentence की main verb, ‘said’ है। इसके अलावा, Part R में  second clause का subject और verb शामिल है और Part S यह बताते हुए second clause को पूरा करता है कि कार्य (actions), शब्दों  (words) से अधिक प्रभावशाली होते हैं। इसलिए, R के बाद S आएगा। अतः options के माध्यम से जाने पर option ‘b’  में सही sequence है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate meaning of the underlined idiom. <br>Bincy&rsquo;s mother <span style=\"text-decoration: underline;\">kept her cards close to her chest</span>.</p>",
                    question_hi: "<p>89. Select the most appropriate meaning of the underlined idiom. <br>Bincy&rsquo;s mother <span style=\"text-decoration: underline;\">kept her cards close to her chest</span>.</p>",
                    options_en: ["<p>Decided to show more affection</p>", "<p>Informed that all the cards must be kept safe</p>", 
                                "<p>Kept her purse and money by herself</p>", "<p>Kept her plans and thoughts hidden</p>"],
                    options_hi: ["<p>Decided to show more affection</p>", "<p>Informed that all the cards must be kept safe</p>",
                                "<p>Kept her purse and money by herself</p>", "<p>Kept her plans and thoughts hidden</p>"],
                    solution_en: "<p>89.(d) <strong>Kept her cards close to her chest-</strong> Kept her plans and thoughts hidden.</p>",
                    solution_hi: "<p>89.(d)<strong> Kept her cards close to her chest -</strong> Kept her plans and thoughts hidden./अपनी योजनाओं एवं विचारों को छुपाये रखना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "90. Identify the error in the given sentence. <br />My colleague always works latest than me.",
                    question_hi: "90.. Identify the error in the given sentence. <br />My colleague always works latest than me.",
                    options_en: [" My colleague ", " latest ", 
                                " always works ", " than me"],
                    options_hi: [" My colleague ", " latest ",
                                " always works ", " than me"],
                    solution_en: "90.(b) latest<br />We use a comparative degree with ‘than’ to make a comparison between two things. ‘Later’ is the correct comparative degree of ‘late’. Hence, ‘later’ is the most appropriate answer.",
                    solution_hi: "90.(b) latest<br />हम दो चीज़ों के बीच comparison करने के लिए ‘than’ के साथ comparative degree का प्रयोग करते हैं। ‘Late’ की सही comparative degree ‘later’ है। अतः, ‘later’ सबसे उपयुक्त उत्तर है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91.Select the option that expresses the following sentence in passive voice. <br>Gill scored 3 centuries in Tata IPL 2023</p>",
                    question_hi: "<p>91.Select the option that expresses the following sentence in passive voice. <br>Gill scored 3 centuries in Tata IPL 2023</p>",
                    options_en: ["<p>3 centuries has been scored by Gill in Tata IPL 2023.</p>", "<p>3 centuries were scored by Gill in Tata IPL 2023.</p>", 
                                "<p>3 centuries had been scored by Gill in Tata IPL 2023.</p>", "<p>3 centuries were being scored by Gill in Tata IPL 2023.</p>"],
                    options_hi: ["<p>3 centuries has been scored by Gill in Tata IPL 2023.</p>", "<p>3 centuries were scored by Gill in Tata IPL 2023.</p>",
                                "<p>3 centuries had been scored by Gill in Tata IPL 2023.</p>", "<p>3 centuries were being scored by Gill in Tata IPL 2023.</p>"],
                    solution_en: "<p>91.(b) 3 centuries were scored by Gill in Tata IPL 2023. (Correct)<br>(a) 3 centuries <span style=\"text-decoration: underline;\">has been scored</span> by Gill in Tata IPL 2023. (Incorrect Tense)<br>(c) 3 centuries <span style=\"text-decoration: underline;\">had been</span> scored by Gill in Tata IPL 2023. (Incorrect Helping Verb)<br>(d) 3 centuries <span style=\"text-decoration: underline;\">were being</span> scored by Gill in Tata IPL 2023. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>91.(b) 3 centuries were scored by Gill in Tata IPL 2023. (Correct)<br>(a) 3 centuries <span style=\"text-decoration: underline;\">has been scored</span> by Gill in Tata IPL 2023. (गलत Tense)<br>(c) 3 centuries <span style=\"text-decoration: underline;\">had been</span> scored by Gill in Tata IPL 2023. (गलत Helping Verb)<br>(d) 3 centuries <span style=\"text-decoration: underline;\">were being</span> scored by Gill in Tata IPL 2023. (गलत Helping Verb)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate synonym of the given word. <br>Carcass</p>",
                    question_hi: "<p>92. Select the most appropriate synonym of the given word. <br>Carcass</p>",
                    options_en: ["<p>Gale</p>", "<p>Scrap</p>", 
                                "<p>Prison</p>", "<p>Corpse</p>"],
                    options_hi: ["<p>Gale</p>", "<p>Scrap</p>",
                                "<p>Prison</p>", "<p>Corpse</p>"],
                    solution_en: "<p>92.(d) <strong>Corpse-</strong> the dead body of a human being.<br><strong>Carcass-</strong> the dead body of an animal.<br><strong>Gale-</strong> a very strong wind.<br><strong>Scrap-</strong> a small piece or fragment of something.<br><strong>Prison-</strong> a place where people are confined as punishment for a crime.</p>",
                    solution_hi: "<p>92.(d) <strong>Corpse</strong> (शव/लाश)- the dead body of a human being.<br><strong>Carcass</strong> (पशु शव)- the dead body of an animal.<br><strong>Gale</strong> (तूफान)- a very strong wind.<br><strong>Scrap</strong> (टुकड़ा)- a small piece or fragment of something.<br><strong>Prison</strong> (कारागार )- a place where people are confined as punishment for a crime.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "93. Select the INCORRECTLY spelt word.",
                    question_hi: "93. Select the INCORRECTLY spelt word.",
                    options_en: [" Accessibility ", " Aptitude ", 
                                " Apparatus ", " Atrocios"],
                    options_hi: [" Accessibility ", " Aptitude ",
                                " Apparatus ", " Atrocios"],
                    solution_en: "93.(d) Atrocios<br />\'Atrocious\' is the correct spelling. ",
                    solution_hi: "93.(d) Atrocios<br />\'Atrocious\' सही spelling है।  ",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the correct ANTONYM of the underlined word to fill in the blank and complete the following sentence. <br>He is always <span style=\"text-decoration: underline;\">active</span> in a party but _______ in the class.</p>",
                    question_hi: "<p>94. Select the correct ANTONYM of the underlined word to fill in the blank and complete the following sentence. <br>He is always <span style=\"text-decoration: underline;\">active</span> in a party but _______ in the class.</p>",
                    options_en: ["<p>inert</p>", "<p>careless</p>", 
                                "<p>angry</p>", "<p>naughty</p>"],
                    options_hi: ["<p>inert</p>", "<p>careless</p>",
                                "<p>angry</p>", "<p>naughty</p>"],
                    solution_en: "<p>94.(a) <strong>Inert-</strong> lacking the ability or strength to move.<br><strong>Active-</strong> engaging in or ready for action.<br><strong>Careless-</strong> not giving sufficient attention or thought to avoiding harm or errors.<br><strong>Angry-</strong> feeling or showing strong annoyance or hostility.<br><strong>Naughty-</strong> disobedient or mischievous, typically in a playful way.</p>",
                    solution_hi: "<p>94.(a) <strong>Inert</strong> (अक्षम)- lacking the ability or strength to move.<br><strong>Active</strong> (सक्रिय)- engaging in or ready for action.<br><strong>Careless</strong> (लापरवाह)- not giving sufficient attention or thought to avoiding harm or errors.<br><strong>Angry</strong> (क्रोधित)- feeling or showing strong annoyance or hostility.<br><strong>Naughty</strong> (नटखट/शरारती)- disobedient or mischievous, typically in a playful way.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the option that expresses the opposite meaning of the underlined word. <br>In front of the big screen, this behaviour is generally considered <span style=\"text-decoration: underline;\">taboo.</span></p>",
                    question_hi: "<p>95.Select the option that expresses the opposite meaning of the underlined word. <br>In front of the big screen, this behaviour is generally considered <span style=\"text-decoration: underline;\">taboo.</span></p>",
                    options_en: ["<p>Obedience</p>", "<p>Acceptable</p>", 
                                "<p>Promotion</p>", "<p>Prescription</p>"],
                    options_hi: ["<p>Obedience</p>", "<p>Acceptable</p>",
                                "<p>Promotion</p>", "<p>Prescription</p>"],
                    solution_en: "<p>95.(b) <strong>Acceptable-</strong> regarded as satisfactory or permissible.<br><strong>Taboo-</strong> prohibited or restricted by social custom.<br><strong>Obedience-</strong> compliance with an order, request, or law.<br><strong>Promotion-</strong> the act of raising someone to a higher position or rank.<br><strong>Prescription-</strong> an instruction written by a medical professional for treatment.</p>",
                    solution_hi: "<p>95.(b) <strong>Acceptable</strong> (स्वीकार्य)- regarded as satisfactory or permissible.<br><strong>Taboo</strong> (निषेध)- prohibited or restricted by social custom.<br><strong>Obedience</strong> (आज्ञापालन)- compliance with an order, request, or law.<br><strong>Promotion</strong> (पदोन्नति)- the act of raising someone to a higher position or rank.<br><strong>Prescription</strong> (औषधपत्र)- an instruction written by a medical professional for treatment.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong>Cloze Test:</strong> <br>Social media companies (96)______ claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. The assertion (97)______ entirely unfounded. Recent history shows how (98)______ citizens have been mobilised against totalitarian regimes on social networking sites. Starting from MeToo to Black Lives Matter, digital networks (99)______ indispensable in bringing together critical voices against social and political (100)______ . <br>Select the most appropriate option to fill in blank no. 96.</p>",
                    question_hi: "<p>96.<strong>Cloze Test:</strong> <br>Social media companies (96)______ claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. The assertion (97)______ entirely unfounded. Recent history shows how (98)______ citizens have been mobilised against totalitarian regimes on social networking sites. Starting from MeToo to Black Lives Matter, digital networks (99)______ indispensable in bringing together critical voices against social and political (100)______ . <br>Select the most appropriate option to fill in blank no. 96.</p>",
                    options_en: [" frequently ", " seldom", 
                                " never ", " don’t"],
                    options_hi: [" frequently ", " seldom",
                                " never ", " don’t"],
                    solution_en: "96.(a) frequently<br />‘Frequently’ means happening often. The given passage states that social media companies frequently claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. Hence, \'frequently\' is the most appropriate answer.",
                    solution_hi: "96.(a) frequently<br />‘Frequently’ का अर्थ है बार-बार होने वाला। दिए गए passage में बताया गया है कि social media कंपनियाँ अक्सर दावा करती हैं कि वे democracy को बढ़ावा देती हैं और आम लोगों के सशक्तिकरण(empowerment) के लिए समर्पित हैं। अतः, \'frequently\' सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong> <br>Social media companies (96)______ claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. The assertion (97)______ entirely unfounded. Recent history shows how (98)______ citizens have been mobilised against totalitarian regimes on social networking sites. Starting from MeToo to Black Lives Matter, digital networks (99)______ indispensable in bringing together critical voices against social and political (100)______ . <br>Select the most appropriate option to fill in blank no. 97.</p>",
                    question_hi: "<p>97.&nbsp; <strong>Cloze Test:</strong> <br>Social media companies (96)______ claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. The assertion (97)______ entirely unfounded. Recent history shows how (98)______ citizens have been mobilised against totalitarian regimes on social networking sites. Starting from MeToo to Black Lives Matter, digital networks (99)______ indispensable in bringing together critical voices against social and political (100)______ . <br>Select the most appropriate option to fill in blank no. 97.</p>",
                    options_en: ["<p>cannot</p>", "<p>should</p>", 
                                "<p>shall</p>", "<p>may not be</p>"],
                    options_hi: ["<p>cannot</p>", "<p>should</p>",
                                "<p>shall</p>", "<p>may not be</p>"],
                    solution_en: "<p>97.(d) may not be<br>The given sentence is in the passive voice and &lsquo;may be + V<sub>3</sub>&rsquo; is the correct passive structure for the modal &lsquo;may&rsquo;. Hence, &lsquo;may not be&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(d) may not be<br>दिया गया sentence, passive voice में है और &lsquo;may be + V<sub>3</sub>&rsquo;, modal &lsquo;may&rsquo; का सही passive structure है। अतः, &lsquo;may not be&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong>&nbsp;<br>Social media companies (96)______ claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. The assertion (97)______ entirely unfounded. Recent history shows how (98)______ citizens have been mobilised against totalitarian regimes on social networking sites. Starting from MeToo to Black Lives Matter, digital networks (99)______ indispensable in bringing together critical voices against social and political (100)______ . <br>Select the most appropriate option to fill in blank no. 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong>&nbsp;<br>Social media companies (96)______ claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. The assertion (97)______ entirely unfounded. Recent history shows how (98)______ citizens have been mobilised against totalitarian regimes on social networking sites. Starting from MeToo to Black Lives Matter, digital networks (99)______ indispensable in bringing together critical voices against social and political (100)______ . <br>Select the most appropriate option to fill in blank no. 98.</p>",
                    options_en: ["<p>alien</p>", "<p>common</p>", 
                                "<p>neutral</p>", "<p>foreign</p>"],
                    options_hi: ["<p>alien</p>", "<p>common</p>",
                                "<p>neutral</p>", "<p>foreign</p>"],
                    solution_en: "<p>98.(b) common<br>The given passage states that recent history shows how common citizens have been mobilised against totalitarian regimes on social networking sites. Hence, &lsquo;common&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(b) common<br>दिए गए passage में बताया गया है कि recent history से पता चलता है कि किस प्रकार common citizens को social networking sites पर अधिनायकवादी शासन(totalitarian regimes) के खिलाफ प्रेरित किया गया है। अतः, &lsquo;common&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:</strong> <br>Social media companies (96)______ claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. The assertion (97)______ entirely unfounded. Recent history shows how (98)______ citizens have been mobilised against totalitarian regimes on social networking sites. Starting from MeToo to Black Lives Matter, digital networks (99)______ indispensable in bringing together critical voices against social and political (100)______ . <br>Select the most appropriate option to fill in blank no. 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong> <br>Social media companies (96)______ claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. The assertion (97)______ entirely unfounded. Recent history shows how (98)______ citizens have been mobilised against totalitarian regimes on social networking sites. Starting from MeToo to Black Lives Matter, digital networks (99)______ indispensable in bringing together critical voices against social and political (100)______ . <br>Select the most appropriate option to fill in blank no. 99.</p>",
                    options_en: ["<p>can</p>", "<p>were</p>", 
                                "<p>that</p>", "<p>have been</p>"],
                    options_hi: ["<p>can</p>", "<p>were</p>",
                                "<p>that</p>", "<p>have been</p>"],
                    solution_en: "<p>99.(d) have been<br>The given sentence is an example of present perfect tense and &lsquo;Plural Sub + have + V<sub>3</sub>&rsquo; is the correct structure for it. Hence, &lsquo;have been(V<sub>3</sub>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) have been<br>दिया गया sentence, present perfect tense ​​का example है और &lsquo;Plural Sub + have + V<sub>3</sub>&rsquo; इसके लिए सही structure है। अतः, &lsquo;have been(V<sub>3</sub>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100.<strong>Cloze Test:</strong> <br>Social media companies (96)______ claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. The assertion (97)______ entirely unfounded. Recent history shows how (98)______ citizens have been mobilised against totalitarian regimes on social networking sites. Starting from MeToo to Black Lives Matter, digital networks (99)______ indispensable in bringing together critical voices against social and political (100)______ . <br>Select the most appropriate option to fill in blank no. 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong> <br>Social media companies (96)______ claim that they promote democracy and are dedicated to the empowerment of ordinary individuals. The assertion (97)______ entirely unfounded. Recent history shows how (98)______ citizens have been mobilised against totalitarian regimes on social networking sites. Starting from MeToo to Black Lives Matter, digital networks (99)______ indispensable in bringing together critical voices against social and political (100)______ . <br>Select the most appropriate option to fill in blank no. 100.</p>",
                    options_en: ["<p>election</p>", "<p>democracy</p>", 
                                "<p>injustice</p>", "<p>justice</p>"],
                    options_hi: ["<p>election</p>", "<p>democracy</p>",
                                "<p>injustice</p>", "<p>justice</p>"],
                    solution_en: "<p>100.(c) injustice<br>&lsquo;Injustice&rsquo; means a situation in which there is no fairness and justice. The given passage states that digital networks have been indispensable in bringing together critical voices against social and political injustice. Hence, \'injustice\' is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) injustice<br>&lsquo;Injustice&rsquo; का अर्थ है ऐसी स्थिति जिसमें निष्पक्षता (fairness)और न्याय (justice) न हो। दिए गए passage में कहा गया है कि social और political injustice के विरुद्ध आलोचनात्मक आवाज़ों (critical voices) को एक साथ लाने में digital network अत्यावश्यक(indispensable ) रहे हैं। अतः, \'injustice\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>