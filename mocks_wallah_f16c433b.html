<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. What sum (in ₹) will earn a simple interest of ₹240 in 2 years at 16% per year rate of interest?</p>",
                    question_hi: "<p>1. किस राशि (₹ में) पर 16% प्रति वर्ष की ब्याज दर से 2 वर्षों में ₹240 का साधारण ब्याज अर्जित होगा?</p>",
                    options_en: ["<p>1500</p>", "<p>1000</p>", 
                                "<p>750</p>", "<p>375</p>"],
                    options_hi: ["<p>1500</p>", "<p>1000</p>",
                                "<p>750</p>", "<p>375</p>"],
                    solution_en: "<p>1.(c) S.I = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> 240 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> P = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>240</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>16</mn><mo>&#215;</mo><mn>2</mn></mrow></mfrac></math> = ₹ 750</p>",
                    solution_hi: "<p>1.(c) साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> 240 = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> मूलधन = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>240</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>16</mn><mo>&#215;</mo><mn>2</mn></mrow></mfrac></math>= ₹ 750</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. Find the simple interest (in ₹) on ₹4000 as a sum borrowed at 3% per year rate of interest for 6 years.</p>",
                    question_hi: "<p>2. 6 वर्षों के लिए 3% प्रति वर्ष की ब्याज दर पर उधार ली गई राशि ₹4000 पर साधारण ब्याज (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>720</p>", "<p>660</p>", 
                                "<p>760</p>", "<p>680</p>"],
                    options_hi: ["<p>720</p>", "<p>660</p>",
                                "<p>760</p>", "<p>680</p>"],
                    solution_en: "<p>2.(a) S.I = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4000</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>6</mn></mrow><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac></math>= ₹ 720</p>",
                    solution_hi: "<p>2.(a) साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4000</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>6</mn></mrow><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac></math>= ₹ 720</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. Etika invested some amount at a rate 9% per annum and earns a S.I. of ₹810 after 3 years. If the rate of interest has been 4% higher, how much more interest (in ₹) would have Etika earned on the same amount in the same time period?</p>",
                    question_hi: "<p>3. इतिका ने 9% वार्षिक ब्&zwj;याज की दर से कुछ राशि निवेश की और 3 वर्षों के बाद ₹ 810 का साधारण ब्याज अर्जित करती है। यदि ब्याज दर 4% अधिक होती, तो समान समय अवधि में उसी राशि पर इतिका ने कितना अधिक ब्याज (₹ में) अर्जित किया होता?</p>",
                    options_en: ["<p>240</p>", "<p>405</p>", 
                                "<p>360</p>", "<p>540</p>"],
                    options_hi: ["<p>240</p>", "<p>405</p>",
                                "<p>360</p>", "<p>540</p>"],
                    solution_en: "<p>3.(c) S.I = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>R</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math>&nbsp;</p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> 810 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> P = ₹ 3000</p>\n<p>When interest rate increase = 9 + 4 = 13%</p>\n<p>Then,</p>\n<p>S.I = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3000</mn><mo>&#215;</mo><mn>13</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math> = ₹ 1170</p>\n<p>So, required interest = 1170 - 810 = ₹ 360</p>",
                    solution_hi: "<p>3.(c) साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> 810 = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> मूलधन = ₹ 3000</p>\n<p>ब्याज दर बढ़ने पर = 9 + 4 = 13%</p>\n<p>तब,</p>\n<p>साधारण ब्याज =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3000</mn><mo>&#215;</mo><mn>13</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math> = ₹ 1170</p>\n<p>इसलिए, आवश्यक ब्याज = 1170 - 810 = ₹ 360</p>",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Find the simple interest (in ₹) on ₹2500 as a sum borrowed at 7% per year rate of interest for 3 years.</p>",
                    question_hi: "<p>4. 3 वर्षों के लिए 7% वार्षिक की ब्याज दर पर उधार ली गई राशि ₹2500 पर साधारण ब्याज (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>465</p>", "<p>485</p>", 
                                "<p>565</p>", "<p>525</p>"],
                    options_hi: ["<p>465</p>", "<p>485</p>",
                                "<p>565</p>", "<p>525</p>"],
                    solution_en: "<p>4.(d) <br>S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>S.I. =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math> = ₹525</p>",
                    solution_hi: "<p>4.(d) <br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>साधारण ब्याज =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>&#215;</mo><mn>7</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math> = ₹525</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. At a certain rate of simple interest, a sum becomes ₹9,430 in 3 years and ₹11,070 in 7 years. Find the sum.",
                    question_hi: "5. साधारण ब्याज की एक निश्चित दर पर, कोई राशि 3 वर्षों में ₹9,430 और 7 वर्षों में ₹11,070 हो जाती है। राशि ज्ञात कीजिए।",
                    options_en: [" ₹8,200 ", " ₹8,000", 
                                " ₹8,350", " ₹7,800"],
                    options_hi: [" ₹8,200 ", " ₹8,000",
                                " ₹8,350", " ₹7,800<br /> "],
                    solution_en: "<p>5.(a)<br>Difference in amounts = 11070 - 9430 = ₹1640</p>\n<p>Difference in time = 7 - 3 = 4 years</p>\n<p>S.I. for 1 year =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1640</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹410</p>\n<p>Amount = principal + S.I.</p>\n<p>Hence, Required sum = 9430 - 3(410) = ₹8200</p>",
                    solution_hi: "<p>5.(a)<br>राशि में अंतर = 11070 - 9430 = ₹1640</p>\n<p>समय का अंतर = 7 - 3 = 4 वर्ष</p>\n<p>1 वर्ष के लिए साधारण ब्याज =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1640</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = ₹410</p>\n<p>राशि = मूलधन + साधारण ब्याज</p>\n<p>अत: आवश्यक योग = 9430 - 3(410) = ₹8200</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Find the simple interest (in ₹) on ₹2500 as a sum borrowed at 9% per year rate of interest for 4 years.</p>",
                    question_hi: "<p>6. 4 वर्षों के लिए 9% वार्षिक की ब्याज दर पर उधार ली गई राशि ₹2500 पर साधारण ब्याज (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>900</p>", "<p>860</p>", 
                                "<p>840</p>", "<p>940</p>"],
                    options_hi: ["<p>900</p>", "<p>860</p>",
                                "<p>840</p>", "<p>940</p>"],
                    solution_en: "<p>6.(a)<br>S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math><br>S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math>&nbsp;= ₹900</p>",
                    solution_hi: "<p>6.(a)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>साधारण ब्याज =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math> = ₹900</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Find the simple interest on ₹36,000 at an annual rate of 22% over an 8 month period.</p>",
                    question_hi: "<p>7. ₹36,000 पर 22% की वार्षिक दर से 8 महीने का साधारण व्याज ज्ञात करें।</p>",
                    options_en: ["<p>₹5,270</p>", "<p>₹5,290</p>", 
                                "<p>₹5,275</p>", "<p>₹5,280</p>"],
                    options_hi: ["<p>₹5,270</p>", "<p>₹5,290</p>",
                                "<p>₹5,275</p>", "<p>₹5,280</p>"],
                    solution_en: "<p>7.(d) S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Principal</mi><mo>&#215;</mo><mi>rate</mi><mo>&#215;</mo><mi>time</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>S.I. =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36000</mn><mo>&#215;</mo><mn>22</mn><mo>&#215;</mo><mn>8</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math>&nbsp;= ₹5280</p>",
                    solution_hi: "<p>7.(d)&nbsp;साधारण व्याज =&nbsp;<math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>साधारण व्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>36000</mn><mo>&#215;</mo><mn>22</mn><mo>&#215;</mo><mn>8</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = ₹5280</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Find the simple interest (in ₹) on ₹3000 as a sum borrowed at 9% per year rate of interest for 2 years.</p>",
                    question_hi: "<p>8. 2 वर्षों के लिए 9% वार्षिक की ब्याज दर पर उधार ली गई राशि ₹3000 पर साधारण ब्याज (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>540</p>", "<p>580</p>", 
                                "<p>480</p>", "<p>500</p>"],
                    options_hi: ["<p>540</p>", "<p>580</p>",
                                "<p>480</p>", "<p>500</p>"],
                    solution_en: "<p>8.(a) S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Principal</mi><mo>&#215;</mo><mi>rate</mi><mo>&#215;</mo><mi>time</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3000</mn><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi></mrow></mfrac></math>&nbsp;= ₹540</p>",
                    solution_hi: "<p>8.(a) साधारण व्याज = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>साधारण व्याज =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3000</mn><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>2</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi></mrow></mfrac></math> = ₹540</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. What sum (in ₹) will earn a simple interest of ₹240 in 3 years at 16% per year rate of interest?</p>",
                    question_hi: "<p>9. किस राशि (₹ में) पर 16% प्रति वर्ष की ब्याज दर से 3 वर्षों में ₹240 का साधारण ब्याज अर्जित होगा?</p>",
                    options_en: ["<p>750</p>", "<p>500</p>", 
                                "<p>1000</p>", "<p>250</p>"],
                    options_hi: ["<p>750</p>", "<p>500</p>",
                                "<p>1000</p>", "<p>250</p>"],
                    solution_en: "<p>9.(b) Let Principal = ₹<math display=\"inline\"><mi>x</mi></math></p>\n<p>S.I. =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Principal</mi><mo>&#215;</mo><mi>rate</mi><mo>&#215;</mo><mi>time</mi></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math>240 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> x = ₹500</p>",
                    solution_hi: "<p>9.(b) माना मूलधन = ₹<math display=\"inline\"><mi>x</mi></math></p>\n<p>साधारण व्याज =&nbsp;<math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math>240 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#215;</mo><mn>16</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> x = ₹500</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. Find the simple interest (in ₹) on ₹2000 as a sum borrowed at 8% per year rate of interest for 2 years.</p>",
                    question_hi: "<p>10. 2 वर्षों के लिए 8% वार्षिक की ब्याज दर पर उधार ली गई राशि ₹2000 पर साधारण ब्याज (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>280</p>", "<p>320</p>", 
                                "<p>360</p>", "<p>260</p>"],
                    options_hi: ["<p>280</p>", "<p>320</p>",
                                "<p>360</p>", "<p>260</p>"],
                    solution_en: "<p>10.(b) S.I = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2000</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math> = 320</p>",
                    solution_hi: "<p>10.(b) साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2000</mn><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math> = 320</p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A specific amount of money is loaned out at a simple interest rate, resulting in ₹15,000 in 2 years and ₹20,000 in 4 years. The rate percentage per annum is:</p>",
                    question_hi: "<p>11. एक विशिष्ट धनराशि साधारण ब्याज दर पर उधार दी जाती है, जो 2 वर्षों में ₹15,000 और 4 वर्षों में ₹20,000 हो जाती है। वार्षिक दर प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>25%</p>", "<p>20%</p>", 
                                "<p>18%</p>", "<p>15%</p>"],
                    options_hi: ["<p>25%</p>", "<p>20%</p>",
                                "<p>18%</p>", "<p>15%</p>"],
                    solution_en: "<p>11.(a) According to question,</p>\n<p>2 years = 20000 - 15000 = 5000</p>\n<p>1 years = 2500</p>\n<p>Now,</p>\n<p>S.I in 2 years = 5000 and Principal = 15000 - 5000 = 10000</p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> S.I = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">R</mi><mo>&#215;</mo><mi mathvariant=\"normal\">T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> 5000 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10000</mn><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> R = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5000</mn><mrow><mn>100</mn><mo>&#215;</mo><mn>2</mn></mrow></mfrac></math> = 25%</p>",
                    solution_hi: "<p>11.(a) प्रश्न के अनुसार,</p>\n<p>2 वर्ष = 20000 - 15000 = 5000</p>\n<p>1 वर्ष = 2500</p>\n<p>अब,</p>\n<p>2 वर्ष का ब्याज = 5000 और मूलधन = 15000 - 5000 = 10000</p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> साधारण ब्याज =<math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mrow><mn>100</mn><mo>&#160;</mo></mrow></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> 5000 = <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10000</mn><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mn>2</mn></mrow><mn>100</mn></mfrac></math></p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math> दर = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5000</mn><mrow><mn>100</mn><mo>&#215;</mo><mn>2</mn></mrow></mfrac></math> = 25%</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Find the simple interest (in ₹) on ₹3000 as a sum borrowed at 9% per year rate of interest for 6 years.</p>",
                    question_hi: "<p>12. 6 वर्षों के लिए 9% वार्षिक की ब्याज दर पर उधार ली गई राशि ₹3000 पर साधारण ब्याज (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>1580</p>", "<p>1660</p>", 
                                "<p>1560</p>", "<p>1620</p>"],
                    options_hi: ["<p>1580</p>", "<p>1660</p>",
                                "<p>1560</p>", "<p>1620</p>"],
                    solution_en: "<p>12.(d) S.I = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>R</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>T</mi></mrow><mn>100</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3000</mn><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> = 1620</p>",
                    solution_hi: "<p>12.(d) साधारण ब्याज = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mo>&#215;</mo><mi>&#2342;&#2352;</mi><mo>&#215;</mo><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3000</mn><mo>&#215;</mo><mn>9</mn><mo>&#215;</mo><mn>6</mn></mrow><mn>100</mn></mfrac></math> = 1620</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Find the simple interest (in ₹) on ₹2500 as a sum borrowed at 6% per year rate of interest for 5 years.</p>",
                    question_hi: "<p>13. 5 वर्षों के लिए 6% वार्षिक की ब्याज दर पर उधार ली गई राशि ₹2500 पर साधारण ब्याज (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>750</p>", "<p>710</p>", 
                                "<p>790</p>", "<p>690</p>"],
                    options_hi: ["<p>750</p>", "<p>710</p>",
                                "<p>790</p>", "<p>690</p>"],
                    solution_en: "<p>13.(a)<br>S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math>&nbsp;</p>\n<p>S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>&#215;</mo><mn>6</mn><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math>&nbsp;= ₹750</p>",
                    solution_hi: "<p>13.(a)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>साधारण ब्याज =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2500</mn><mo>&#215;</mo><mn>6</mn><mo>&#215;</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = ₹750</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Find the simple interest (in ₹) on ₹3500 as a sum borrowed at 6% per year rate of interest for 3 years.</p>",
                    question_hi: "<p>14. 3 वर्षों के लिए 6% प्रति वर्ष की ब्याज दर पर उधार ली गई राशि ₹3500 पर साधारण ब्याज (₹ में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>630</p>", "<p>670</p>", 
                                "<p>590</p>", "<p>570</p>"],
                    options_hi: ["<p>630</p>", "<p>670</p>",
                                "<p>590</p>", "<p>570</p>"],
                    solution_en: "<p>14.(a)<br>S.I. = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>S.I. =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3500</mn><mo>&#215;</mo><mn>6</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math>&nbsp;= ₹630</p>",
                    solution_hi: "<p>14.(a)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mi>R</mi><mo>&#215;</mo><mi>T</mi></mrow><mn>100</mn></mfrac></math></p>\n<p>साधारण ब्याज =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3500</mn><mo>&#215;</mo><mn>6</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>100</mn></mfrac></math> = ₹630</p>",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The simple interest on a certain sum for 4 years at 23% per annum is ₹11,960 less than the simple interest on the same sum for 8 years. Find the sum.</p>",
                    question_hi: "<p>15. एक निश्चित धनराशि पर 4 वर्षों के लिए 23% की वार्षिक दर पर साधारण ब्याज, उसी धनराशि पर 8 वर्षों के साधारण ब्याज से ₹11,960 कम है। धनराशि ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹12,000</p>", "<p>₹12,500</p>", 
                                "<p>₹13,500</p>", "<p>₹13,000</p>"],
                    options_hi: ["<p>₹12,000</p>", "<p>₹12,500</p>",
                                "<p>₹13,500</p>", "<p>₹13,000</p>"],
                    solution_en: "<p>15.(d)<br>Interest obtain in 4 year = 4 &times; 23% = 92%<br>Interest obtain in 8 year = 8 &times; 23% = 184%<br>According to the question,<br>(184 - 92)% = ₹11,960<br>92% = ₹11,960<br>Hence, sum (100%) = <math display=\"inline\"><mfrac><mrow><mn>11960</mn></mrow><mrow><mn>92</mn></mrow></mfrac></math> &times; 100 = ₹13,000</p>",
                    solution_hi: "<p>15.(d)<br>4 वर्ष में प्राप्त ब्याज = 4 &times; 23% = 92%<br>8 वर्ष में प्राप्त ब्याज = 8 &times; 23% = 184%<br>प्रश्न के अनुसार,<br>(184 - 92)% = ₹11,960<br>92% = ₹11,960<br>अतः, मूलधन (100%) = <math display=\"inline\"><mfrac><mrow><mn>11960</mn></mrow><mrow><mn>92</mn></mrow></mfrac></math> &times; 100 = ₹13,000</p>",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>