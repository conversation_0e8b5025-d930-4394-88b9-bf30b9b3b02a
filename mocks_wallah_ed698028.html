<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> For what angle D is &#8710;ABC congruent to &#8710;DEF, given AC = 2.5 cm, BC = 5 cm, &ang;C = 75&deg;, DE = 2.5 cm and DF = 5 cm?</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> &#8710;ABC </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2306;&#2327;&#2360;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> &#8710;DEF </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> AC = 2.5 cm, BC = 5 cm, &ang;C = 75&deg;, DE = 2.5 cm </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> DF = 5 cm</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2339;</span><span style=\"font-family: Cambria Math;\"> D </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>75&deg;</p>\n", "<p>25&deg;</p>\n", 
                                "<p>35&deg;</p>\n", "<p>90&deg;</p>\n"],
                    options_hi: ["<p>75&deg;</p>\n", "<p>25&deg;</p>\n",
                                "<p>35&deg;</p>\n", "<p>90&deg;</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Since AC = DE = 2.5 cm, BC = DF = 5cm, &ang;C = &ang;D = 75&deg;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, &#8710; ACB <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#10861;</mo></math></span><span style=\"font-family: Cambria Math;\">&nbsp;&#8710; </span><span style=\"font-family: Cambria Math;\">EDF(</span><span style=\"font-family: Cambria Math;\">by SAS congruence) </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2370;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> AC</span><span style=\"font-family: Cambria Math;\"> = DE = 2.5 cm, BC = DF = 5cm, &ang;C = &ang;D = 75&deg;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> &#8710;</span><span style=\"font-family: Cambria Math;\"> ACB <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#10861;</mo></math></span><span style=\"font-family: Cambria Math;\"> &#8710; EDF (SAS </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2357;&#2366;&#2306;&#2327;&#2360;&#2350;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> If </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn></math><span style=\"font-family: Cambria Math;\">, then the value of </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">is :</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367; </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn></math> <span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>999</mn><mn>212</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>199</mn><mn>212</mn></mfrac></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>999</mn><mn>512</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>199</mn><mn>512</mn></mfrac></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>999</mn><mn>212</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>199</mn><mn>212</mn></mfrac></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>999</mn><mn>512</mn></mfrac></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>199</mn><mn>512</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">Dividing both sides by 8x. We get</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac><mo>=</mo><msup><mfenced><mrow><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac></mrow></mfenced><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>&times;</mo><mi>x</mi><mo>&times;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&times;</mo><mfenced><mrow><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac></mrow></mfenced><mo>=</mo><mo>-</mo><mfrac><mn>729</mn><mn>512</mn></mfrac><mo>+</mo><mfrac><mn>27</mn><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>999</mn><mn>512</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Formula used: </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>8</mn><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mi>x</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>0</mn></math></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2325;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 8x </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>=</mo><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>3</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac><mo>=</mo><msup><mfenced><mrow><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac></mrow></mfenced><mn>3</mn></msup><mo>-</mo><mn>3</mn><mo>&times;</mo><mi>x</mi><mo>&times;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>&times;</mo><mfenced><mrow><mo>-</mo><mfrac><mn>9</mn><mn>8</mn></mfrac></mrow></mfenced><mo>=</mo><mo>-</mo><mfrac><mn>729</mn><mn>512</mn></mfrac><mo>+</mo><mfrac><mn>27</mn><mn>8</mn></mfrac><mo>=</mo><mfrac><mn>999</mn><mn>512</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2351;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2340;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>b</mi><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></msup><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>3</mn><mi>a</mi><mi>b</mi><mo>(</mo><mi>a</mi><mo>+</mo><mi>b</mi><mo>)</mo></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">The profit triples if the selling price is doubled. The profit percentage is:</span></p>\n",
                    question_hi: "<p>3.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>110</p>\n", "<p>50</p>\n", 
                                "<p>100</p>\n", "<p>28</p>\n"],
                    options_hi: ["<p>110</p>\n", "<p>50</p>\n",
                                "<p>100</p>\n", "<p>28</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let CP and SP be &#8377;x and &#8377;y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CP&nbsp; &nbsp; &nbsp;SP&nbsp; &nbsp; &nbsp;Profit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Original&nbsp; &nbsp; x&nbsp; &nbsp; &nbsp; &nbsp; y&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">y</span><span style=\"font-family: Cambria Math;\"> - x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;x&nbsp; &nbsp; &nbsp; &nbsp;2y&nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">2y</span><span style=\"font-family: Cambria Math;\"> - x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2y - x = 3 (y - x) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2y - x = 3y - 3x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x = y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>y</mi></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the profit% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>1</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 100%</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;x </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;y </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;&nbsp; &nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">x&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;y&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">y</span><span style=\"font-family: Cambria Math;\"> - x</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">x&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 2y&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">2y</span><span style=\"font-family: Cambria Math;\"> - x</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2366;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2y - x = 3 (y - x) </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2y - x = 3y - 3x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">2x = y</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>x</mi><mi>y</mi></mfrac><mo>=</mo><mfrac><mn>1</mn><mn>2</mn></mfrac></math> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> % = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>-</mo><mn>1</mn></mrow><mn>1</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 100 = 100%</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> A man can complete a work in 20 days, and a woman can complete the same work in 10 days. If they work on it together for 5 days, then how many days will the woman take to complete the rest of the work?</span></p>\n",
                    question_hi: "<p>4. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2352;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2375;&#2306;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", "<p>1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\n", 
                                "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", "<p>1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n"],
                    options_hi: ["<p>2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", "<p>1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math></p>\n",
                                "<p>3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", "<p>1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669282395/word/media/image1.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Efficiency of man = 1 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Efficiency of woman = 2 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Work done by both of them in 5 days = (1+2) &times; 5 = 15 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Remaining work = 20 - 15 = 5 unit</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Time taken by woman to complete the remaining </span><span style=\"font-family: Cambria Math;\">work =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>=</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\">days </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669282395/word/media/image1.png\"></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2350;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 1 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2350;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 2 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = (1+2) &times; 5 = 15 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 20 - 15 = 5 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac><mo>=</mo><mn>2</mn><mfrac><mn>1</mn><mn>2</mn></mfrac></math><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. <span style=\"font-family: Cambria Math;\">A family spends 8% of its monthly income on charity. In a particular month, the family spent &#8377;1,200 on charity. What is the income in that month?</span></p>\n",
                    question_hi: "<p>5. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,200 </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2368;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;15,000</p>\n", "<p>&#8377;16,000</p>\n", 
                                "<p>&#8377;12,000</p>\n", "<p>&#8377;18,000</p>\n"],
                    options_hi: ["<p>&#8377;15,000</p>\n", "<p>&#8377;16,000</p>\n",
                                "<p>&#8377;12,000</p>\n", "<p>&#8377;18,000</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the income of the family be &#8377;x.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x &times; 8% = 1200</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>25</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1200</mn><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1200</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>25</mn><mo>&nbsp;</mo></mrow><mn>2</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>&#8377;</mo><mn>15</mn><mo>,</mo><mn>000</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;x </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2366;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> :</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x &times; 8% = 1200</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>2</mn><mn>25</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>1200</mn><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1200</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>25</mn><mo>&nbsp;</mo></mrow><mn>2</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>&#8377;</mo><mn>15</mn><mo>,</mo><mn>000</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> How many numbers between 100 and 1000 are divisible by 17?</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> 100 </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1000 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> 17 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>51</p>\n", "<p>53</p>\n", 
                                "<p>52</p>\n", "<p>54</p>\n"],
                    options_hi: ["<p>51</p>\n", "<p>53</p>\n",
                                "<p>52</p>\n", "<p>54</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">No&rsquo;s which</span><span style=\"font-family: Cambria Math;\"> are divisible by 17 form an AP </span><span style=\"font-family: Cambria Math;\">i.e</span><span style=\"font-family: Cambria Math;\"> 102, 119, 136&hellip;&hellip;.. 986</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, n =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>986</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>102</mn></mrow><mn>17</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>884</mn><mn>17</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>52</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>53</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> n </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2381;&#2352;&#2375;&#2339;&#2368;</span><span style=\"font-family: Cambria Math;\"> 102, 119, 136&hellip;</span><span style=\"font-family: Cambria Math;\">&hellip;..</span><span style=\"font-family: Cambria Math;\"> 986 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 17 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\">, n = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>986</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>102</mn></mrow><mn>17</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>884</mn><mn>17</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>52</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>1</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>53</mn></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> Study the following bar-chart and answer the questions that follow.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The bar-chart shows the sales of laptops (in thousands) over a period of four years.</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669282395/word/media/image2.png\"></p>\r\n<p><span style=\"font-family: Cambria Math;\">Find the percentage increase in sales from 2012 to 2013, giving your answer correct to two places of decimal.</span></p>\n",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2339;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2339;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2375;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2357;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2332;&#2366;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2326;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669282395/word/media/image2.png\"></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2319;</span><span style=\"font-family: Cambria Math;\">, 2012 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2013 </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>42.98%</p>\n", "<p>48.54%</p>\n", 
                                "<p>45.64%</p>\n", "<p>44.44%</p>\n"],
                    options_hi: ["<p>42.98%</p>\n", "<p>48.54%</p>\n",
                                "<p>45.64%</p>\n", "<p>44.44%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total no of sales of laptop in the year 2012 = 36</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total no of sales of laptop in the year 2013 = 52</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Percentage increase in sales =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>52</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>36</mn></mrow><mn>36</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>16</mn><mo>&nbsp;</mo></mrow><mn>36</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>400</mn></mrow><mn>9</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>44</mn><mo>.</mo><mn>44</mn><mo>%</mo><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2012 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 36</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 2013 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = 52</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2325;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> = </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>52</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>36</mn></mrow><mn>36</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>16</mn><mo>&nbsp;</mo></mrow><mn>36</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>400</mn></mrow><mn>9</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>44</mn><mo>.</mo><mn>44</mn><mo>%</mo><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">The area of a triangle is 96 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> and the length of one of its sides is 24 cm. What is the length of the perpendicular drawn to this side of the triangle from the opposite vertex?</span></p>\n",
                    question_hi: "<p>8.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> 96 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 24 cm </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2369;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2381;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2368;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2368;&#2306;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>16 cm</p>\n", "<p>8 cm</p>\n", 
                                "<p>4 cm</p>\n", "<p>12 cm</p>\n"],
                    options_hi: ["<p>16 cm</p>\n", "<p>8 cm</p>\n",
                                "<p>4 cm</p>\n", "<p>12 cm</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the base of the triangle = 24cm</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Area of </span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 24 &times; height</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">96 = 12 &times; height</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">H = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>12</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 8 cm</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> = 24cm</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2349;&#2369;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> &times; 24 &times; </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2306;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">96 = 12 &times; </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2306;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">H =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>12</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 8 cm</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> Find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mo>&nbsp;</mo><mfrac><mrow><msup><mn>9</mn><mn>3</mn></msup><mo>+</mo><msup><mn>9</mn><mn>4</mn></msup><mo>+</mo><msup><mn>9</mn><mn>5</mn></msup><mo>+</mo><msup><mn>9</mn><mn>6</mn></msup><mo>+</mo><msup><mn>9</mn><mn>7</mn></msup></mrow><mrow><msup><mn>9</mn><mn>1</mn></msup><mo>+</mo><msup><mn>9</mn><mn>2</mn></msup><mo>+</mo><msup><mn>9</mn><mn>3</mn></msup><mo>+</mo><msup><mn>9</mn><mn>4</mn></msup><mo>+</mo><msup><mn>9</mn><mn>5</mn></msup></mrow></mfrac></mrow></mfenced><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math></span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mo>&nbsp;</mo><mfrac><mrow><msup><mn>9</mn><mn>3</mn></msup><mo>+</mo><msup><mn>9</mn><mn>4</mn></msup><mo>+</mo><msup><mn>9</mn><mn>5</mn></msup><mo>+</mo><msup><mn>9</mn><mn>6</mn></msup><mo>+</mo><msup><mn>9</mn><mn>7</mn></msup></mrow><mrow><msup><mn>9</mn><mn>1</mn></msup><mo>+</mo><msup><mn>9</mn><mn>2</mn></msup><mo>+</mo><msup><mn>9</mn><mn>3</mn></msup><mo>+</mo><msup><mn>9</mn><mn>4</mn></msup><mo>+</mo><msup><mn>9</mn><mn>5</mn></msup></mrow></mfrac></mrow></mfenced><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>9</p>\n", "<p>3</p>\n", 
                                "<p>729</p>\n", "<p>81</p>\n"],
                    options_hi: ["<p>9</p>\n", "<p>3</p>\n",
                                "<p>729</p>\n", "<p>81</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mo>&nbsp;</mo><mfrac><mrow><msup><mn>9</mn><mn>3</mn></msup><mo>+</mo><msup><mn>9</mn><mn>4</mn></msup><mo>+</mo><msup><mn>9</mn><mn>5</mn></msup><mo>+</mo><msup><mn>9</mn><mn>6</mn></msup><mo>+</mo><msup><mn>9</mn><mn>7</mn></msup></mrow><mrow><msup><mn>9</mn><mn>1</mn></msup><mo>+</mo><msup><mn>9</mn><mn>2</mn></msup><mo>+</mo><msup><mn>9</mn><mn>3</mn></msup><mo>+</mo><msup><mn>9</mn><mn>4</mn></msup><mo>+</mo><msup><mn>9</mn><mn>5</mn></msup></mrow></mfrac></mrow></mfenced><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mfrac><mrow><msup><mn>9</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>9</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>9</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mn>9</mn><mn>4</mn></msup><mo>)</mo></mrow><mrow><mn>9</mn><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>9</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>9</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>9</mn><mrow><mo>&nbsp;</mo><mn>4</mn></mrow></msup><mo>)</mo></mrow></mfrac></mfenced><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><msup><mn>9</mn><mn>2</mn></msup><mo>)</mo></mrow><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>9</mn></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mfenced><mrow><mo>&nbsp;</mo><mfrac><mrow><msup><mn>9</mn><mn>3</mn></msup><mo>+</mo><msup><mn>9</mn><mn>4</mn></msup><mo>+</mo><msup><mn>9</mn><mn>5</mn></msup><mo>+</mo><msup><mn>9</mn><mn>6</mn></msup><mo>+</mo><msup><mn>9</mn><mn>7</mn></msup></mrow><mrow><msup><mn>9</mn><mn>1</mn></msup><mo>+</mo><msup><mn>9</mn><mn>2</mn></msup><mo>+</mo><msup><mn>9</mn><mn>3</mn></msup><mo>+</mo><msup><mn>9</mn><mn>4</mn></msup><mo>+</mo><msup><mn>9</mn><mn>5</mn></msup></mrow></mfrac></mrow></mfenced><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>=</mo><mo>&nbsp;</mo><msup><mfenced><mfrac><mrow><msup><mn>9</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>9</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>9</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><msup><mn>9</mn><mn>4</mn></msup><mo>)</mo></mrow><mrow><mn>9</mn><mo>(</mo><mn>1</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>9</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>9</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>9</mn><mn>3</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mn>9</mn><mrow><mo>&nbsp;</mo><mn>4</mn></mrow></msup><mo>)</mo></mrow></mfrac></mfenced><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><msup><mn>9</mn><mn>2</mn></msup><mo>)</mo></mrow><mfrac><mn>1</mn><mn>2</mn></mfrac></msup><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>9</mn></math></span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10.<span style=\"font-family: Cambria Math;\"> The value of sin2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi></math></span><span style=\"font-family: Cambria Math;\"> (tan<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi></math></span><span style=\"font-family: Cambria Math;\"> + cot<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&theta;</mi></math></span><span style=\"font-family: Cambria Math;\"> ) </span><span style=\"font-family: Cambria Math;\">is :</span></p>\n",
                    question_hi: "<p>10.<span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>sin</mi><mn>2</mn><mi>&theta;</mi><mo>&nbsp;</mo><mo>(</mo><mi>tan</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mi>c</mi><mi>o</mi><mi>t</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>)</mo><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\n", "<p>1</p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", "<p>2</p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math></p>\n", "<p>1</p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math></p>\n", "<p>2</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mi>S</mi><mi>i</mi><mi>n</mi><mo>&nbsp;</mo><mn>2</mn><mi>&theta;</mi><mo>&nbsp;</mo><mo>(</mo><mfrac><mrow><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mrow><mi>cos</mi><mi>&theta;</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mi>sin</mi><mi>&theta;</mi></mrow></mfrac><mo>)</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>2</mn><mo>&nbsp;</mo><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>(</mo><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mi>sin</mi><mi>&theta;</mi></mrow></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>2</mn><mo>&nbsp;</mo><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo></mrow><mrow><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mi>S</mi><mi>i</mi><mi>n</mi><mo>&nbsp;</mo><mn>2</mn><mi>&theta;</mi><mo>&nbsp;</mo><mo>(</mo><mfrac><mrow><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo></mrow><mrow><mi>cos</mi><mi>&theta;</mi></mrow></mfrac><mo>+</mo><mfrac><mrow><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi></mrow><mrow><mi>sin</mi><mi>&theta;</mi></mrow></mfrac><mo>)</mo><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>2</mn><mo>&nbsp;</mo><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>(</mo><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi>&theta;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>&theta;</mi></mrow><mrow><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mi>sin</mi><mi>&theta;</mi></mrow></mfrac><mo>)</mo><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mn>2</mn><mo>&nbsp;</mo><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mrow><mn>1</mn><mo>&nbsp;</mo></mrow><mrow><mi>sin</mi><mi>&theta;</mi><mo>&nbsp;</mo><mi>cos</mi><mi>&theta;</mi></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11.<span style=\"font-family: Cambria Math;\"> Study the given table and answer the question that follow.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The following table gives the month-wise number of different types of scooters produced by a company during the first six months of 1992.</span></p>\r\n<table style=\"border-collapse: collapse; width: 37.1445%; height: 203px;\" border=\"3\">\r\n<tbody>\r\n<tr style=\"height: 0.5px;\">\r\n<td style=\"width: 27.2267%; height: 73.3194px;\" rowspan=\"2\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"16px\"><mfrac><mrow><mi>M</mi><mi>o</mi><mi>n</mi><mi>t</mi><mi>h</mi><mi>s</mi></mrow><mrow><mi>T</mi><mi>y</mi><mi>p</mi><mi>e</mi><mi>s</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>s</mi><mi>c</mi><mi>o</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>s</mi></mrow></mfrac></mstyle></math></td>\r\n<td style=\"height: 73.3194px; width: 15.2401%; text-align: center;\" rowspan=\"2\"><strong>JANUARY</strong></td>\r\n<td style=\"height: 73.3194px; width: 16.4387%; text-align: center;\" rowspan=\"2\"><strong>FEBRUARY</strong></td>\r\n<td style=\"height: 73.3194px; width: 12.8428%; text-align: center;\" rowspan=\"2\"><strong>MARCH</strong></td>\r\n<td style=\"height: 73.3194px; width: 10.2742%; text-align: center;\" rowspan=\"2\"><strong>APRIL</strong></td>\r\n<td style=\"height: 73.3194px; width: 8.56185%; text-align: center;\" rowspan=\"2\"><strong>MAY</strong></td>\r\n<td style=\"height: 73.3194px; width: 9.41803%; text-align: center;\" rowspan=\"2\"><strong>JUNE</strong></td>\r\n</tr>\r\n<tr style=\"height: 72.8194px;\"></tr>\r\n<tr style=\"height: 22.3889px;\">\r\n<td style=\"width: 27.2267%; height: 22.3889px; text-align: center;\"><strong>X</strong></td>\r\n<td style=\"width: 15.2401%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 16.4387%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 12.8428%; height: 22.3889px; text-align: center;\"><strong>18</strong></td>\r\n<td style=\"width: 10.2742%; height: 22.3889px; text-align: center;\"><strong>40</strong></td>\r\n<td style=\"width: 8.56185%; height: 22.3889px; text-align: center;\"><strong>20</strong></td>\r\n<td style=\"width: 9.41803%; height: 22.3889px; text-align: center;\"><strong>15</strong></td>\r\n</tr>\r\n<tr style=\"height: 22.3889px;\">\r\n<td style=\"width: 27.2267%; height: 22.3889px; text-align: center;\"><strong>Y</strong></td>\r\n<td style=\"width: 15.2401%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 16.4387%; height: 22.3889px; text-align: center;\"><strong>27</strong></td>\r\n<td style=\"width: 12.8428%; height: 22.3889px; text-align: center;\"><strong>50</strong></td>\r\n<td style=\"width: 10.2742%; height: 22.3889px; text-align: center;\"><strong>45</strong></td>\r\n<td style=\"width: 8.56185%; height: 22.3889px; text-align: center;\"><strong>30</strong></td>\r\n<td style=\"width: 9.41803%; height: 22.3889px; text-align: center;\"><strong>20</strong></td>\r\n</tr>\r\n<tr style=\"height: 22.3889px;\">\r\n<td style=\"width: 27.2267%; height: 22.3889px; text-align: center;\"><strong>Z</strong></td>\r\n<td style=\"width: 15.2401%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 16.4387%; height: 22.3889px; text-align: center;\"><strong>27</strong></td>\r\n<td style=\"width: 12.8428%; height: 22.3889px; text-align: center;\"><strong>15</strong></td>\r\n<td style=\"width: 10.2742%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 8.56185%; height: 22.3889px; text-align: center;\"><strong>30</strong></td>\r\n<td style=\"width: 9.41803%; height: 22.3889px; text-align: center;\"><strong>20</strong></td>\r\n</tr>\r\n<tr style=\"height: 22.3889px;\">\r\n<td style=\"width: 27.2267%; height: 22.3889px; text-align: center;\"><strong>T</strong></td>\r\n<td style=\"width: 15.2401%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 16.4387%; height: 22.3889px; text-align: center;\"><strong>26</strong></td>\r\n<td style=\"width: 12.8428%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 10.2742%; height: 22.3889px; text-align: center;\"><strong>0</strong></td>\r\n<td style=\"width: 8.56185%; height: 22.3889px; text-align: center;\"><strong>30</strong></td>\r\n<td style=\"width: 9.41803%; height: 22.3889px; text-align: center;\"><strong>15</strong></td>\r\n</tr>\r\n<tr style=\"height: 22.3889px;\">\r\n<td style=\"width: 27.2267%; height: 22.3889px; text-align: center;\"><strong>TOTAL</strong></td>\r\n<td style=\"width: 15.2401%; height: 22.3889px; text-align: center;\"><strong>100</strong></td>\r\n<td style=\"width: 16.4387%; height: 22.3889px; text-align: center;\"><strong>105</strong></td>\r\n<td style=\"width: 12.8428%; height: 22.3889px; text-align: center;\"><strong>108</strong></td>\r\n<td style=\"width: 10.2742%; height: 22.3889px; text-align: center;\"><strong>110</strong></td>\r\n<td style=\"width: 8.56185%; height: 22.3889px; text-align: center;\"><strong>110</strong></td>\r\n<td style=\"width: 9.41803%; height: 22.3889px; text-align: center;\"><strong>90</strong></td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n<p><span style=\"font-family: Cambria Math;\">In Zwhich two of the six given months was the total number of scooters produced by the company of all types taken together equal?</span></p>\n",
                    question_hi: "<p>11.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2381;&#2351;&#2351;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2330;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2354;&#2367;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> 1992 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2368;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2370;&#2335;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<table style=\"border-collapse: collapse; width: 37.1445%; height: 203px;\" border=\"3\">\r\n<tbody>\r\n<tr style=\"height: 0.5px;\">\r\n<td style=\"width: 27.2267%; height: 73.3194px;\" rowspan=\"2\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle mathsize=\"16px\"><mfrac><mrow><mi>M</mi><mi>o</mi><mi>n</mi><mi>t</mi><mi>h</mi><mi>s</mi></mrow><mrow><mi>T</mi><mi>y</mi><mi>p</mi><mi>e</mi><mi>s</mi><mo>&nbsp;</mo><mi>o</mi><mi>f</mi><mo>&nbsp;</mo><mi>s</mi><mi>c</mi><mi>o</mi><mi>o</mi><mi>t</mi><mi>e</mi><mi>r</mi><mi>s</mi></mrow></mfrac></mstyle></math></td>\r\n<td style=\"height: 73.3194px; width: 15.2401%; text-align: center;\" rowspan=\"2\"><strong>JANUARY</strong></td>\r\n<td style=\"height: 73.3194px; width: 16.4387%; text-align: center;\" rowspan=\"2\"><strong>FEBRUARY</strong></td>\r\n<td style=\"height: 73.3194px; width: 12.8428%; text-align: center;\" rowspan=\"2\"><strong>MARCH</strong></td>\r\n<td style=\"height: 73.3194px; width: 10.2742%; text-align: center;\" rowspan=\"2\"><strong>APRIL</strong></td>\r\n<td style=\"height: 73.3194px; width: 8.56185%; text-align: center;\" rowspan=\"2\"><strong>MAY</strong></td>\r\n<td style=\"height: 73.3194px; width: 9.41803%; text-align: center;\" rowspan=\"2\"><strong>JUNE</strong></td>\r\n</tr>\r\n<tr style=\"height: 72.8194px;\"></tr>\r\n<tr style=\"height: 22.3889px;\">\r\n<td style=\"width: 27.2267%; height: 22.3889px; text-align: center;\"><strong>X</strong></td>\r\n<td style=\"width: 15.2401%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 16.4387%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 12.8428%; height: 22.3889px; text-align: center;\"><strong>18</strong></td>\r\n<td style=\"width: 10.2742%; height: 22.3889px; text-align: center;\"><strong>40</strong></td>\r\n<td style=\"width: 8.56185%; height: 22.3889px; text-align: center;\"><strong>20</strong></td>\r\n<td style=\"width: 9.41803%; height: 22.3889px; text-align: center;\"><strong>15</strong></td>\r\n</tr>\r\n<tr style=\"height: 22.3889px;\">\r\n<td style=\"width: 27.2267%; height: 22.3889px; text-align: center;\"><strong>Y</strong></td>\r\n<td style=\"width: 15.2401%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 16.4387%; height: 22.3889px; text-align: center;\"><strong>27</strong></td>\r\n<td style=\"width: 12.8428%; height: 22.3889px; text-align: center;\"><strong>50</strong></td>\r\n<td style=\"width: 10.2742%; height: 22.3889px; text-align: center;\"><strong>45</strong></td>\r\n<td style=\"width: 8.56185%; height: 22.3889px; text-align: center;\"><strong>30</strong></td>\r\n<td style=\"width: 9.41803%; height: 22.3889px; text-align: center;\"><strong>20</strong></td>\r\n</tr>\r\n<tr style=\"height: 22.3889px;\">\r\n<td style=\"width: 27.2267%; height: 22.3889px; text-align: center;\"><strong>Z</strong></td>\r\n<td style=\"width: 15.2401%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 16.4387%; height: 22.3889px; text-align: center;\"><strong>27</strong></td>\r\n<td style=\"width: 12.8428%; height: 22.3889px; text-align: center;\"><strong>15</strong></td>\r\n<td style=\"width: 10.2742%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 8.56185%; height: 22.3889px; text-align: center;\"><strong>30</strong></td>\r\n<td style=\"width: 9.41803%; height: 22.3889px; text-align: center;\"><strong>20</strong></td>\r\n</tr>\r\n<tr style=\"height: 22.3889px;\">\r\n<td style=\"width: 27.2267%; height: 22.3889px; text-align: center;\"><strong>T</strong></td>\r\n<td style=\"width: 15.2401%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 16.4387%; height: 22.3889px; text-align: center;\"><strong>26</strong></td>\r\n<td style=\"width: 12.8428%; height: 22.3889px; text-align: center;\"><strong>25</strong></td>\r\n<td style=\"width: 10.2742%; height: 22.3889px; text-align: center;\"><strong>0</strong></td>\r\n<td style=\"width: 8.56185%; height: 22.3889px; text-align: center;\"><strong>30</strong></td>\r\n<td style=\"width: 9.41803%; height: 22.3889px; text-align: center;\"><strong>15</strong></td>\r\n</tr>\r\n<tr style=\"height: 22.3889px;\">\r\n<td style=\"width: 27.2267%; height: 22.3889px; text-align: center;\"><strong>TOTAL</strong></td>\r\n<td style=\"width: 15.2401%; height: 22.3889px; text-align: center;\"><strong>100</strong></td>\r\n<td style=\"width: 16.4387%; height: 22.3889px; text-align: center;\"><strong>105</strong></td>\r\n<td style=\"width: 12.8428%; height: 22.3889px; text-align: center;\"><strong>108</strong></td>\r\n<td style=\"width: 10.2742%; height: 22.3889px; text-align: center;\"><strong>110</strong></td>\r\n<td style=\"width: 8.56185%; height: 22.3889px; text-align: center;\"><strong>110</strong></td>\r\n<td style=\"width: 9.41803%; height: 22.3889px; text-align: center;\"><strong>90</strong></td>\r\n</tr>\r\n</tbody>\r\n</table>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2370;&#2335;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2368;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>January and May</p>\n", "<p>April and May</p>\n", 
                                "<p>January and February</p>\n", "<p>January and March</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2357;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2312;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2381;&#2352;&#2376;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2312;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2357;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2352;&#2357;&#2352;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2357;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2352;&#2381;&#2330;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">On observing the </span><span style=\"font-family: Cambria Math;\">data ,</span><span style=\"font-family: Cambria Math;\"> we get that the total no of different types of scooter produced by company taken together, equal to the total no of different types of scooter produced in April and May </span><span style=\"font-family: Cambria Math;\">i.e</span><span style=\"font-family: Cambria Math;\"> 110.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2337;&#2375;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2370;&#2335;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2381;&#2352;&#2376;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2325;&#2370;&#2335;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2341;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> 110 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: " <p>12.</span><span style=\"font-family:Cambria Math\"> Find the value of the given expression:</span></p> <p><span style=\"font-family:Cambria Math\">42 ÷ 7 × (5 + 7) ÷ 3</span></p>",
                    question_hi: " <p>12. </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">व्यंजक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कीजिए</span><span style=\"font-family:Cambria Math\">:</span></p> <p><span style=\"font-family:Cambria Math\">42 ÷ 7 × (5 + 7) ÷ 3</span></p>",
                    options_en: [" <p> 1.5</span></p>", " <p> 3</span></p>", 
                                " <p> 24</span></p>", " <p> 32</span></p>"],
                    options_hi: [" <p> 1.5</span></p>", " <p> 3</span></p>",
                                " <p> 24</span></p>", " <p> 32</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">12.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">= 42 ÷ 7 × (5 + 7) ÷ 3</span></p> <p><span style=\"font-family:Cambria Math\">= 6 × 12 ÷ 3</span></p> <p><span style=\"font-family:Cambria Math\">= 6 × 4 = 24</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">12.(</span><span style=\"font-family:Cambria Math\">c)</span></p> <p><span style=\"font-family:Cambria Math\">= 42 ÷ 7 × (5 + 7) ÷ 3</span></p> <p><span style=\"font-family:Cambria Math\">= 6 × 12 ÷ 3</span></p> <p><span style=\"font-family:Cambria Math\">= 6 × 4 = 24</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13.<span style=\"font-family: Cambria Math;\"> Bhanu spends 20% of his monthly salary on rent, 10% on stationery, 14% on transport, 18% on electricity, and the balance of &#8377;1,520 on remaining items. What is the monthly salary of Bhanu?</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2344;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2352;&#2366;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, 10% </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2335;&#2375;&#2358;&#2344;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, 14% </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2367;&#2357;&#2361;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, 18% </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2332;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,520 </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2344;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;10,000</p>\n", "<p>&#8377;6,000</p>\n", 
                                "<p>&#8377;4,000</p>\n", "<p>&#8377;2,000</p>\n"],
                    options_hi: ["<p>&#8377;10,000</p>\n", "<p>&#8377;6,000</p>\n",
                                "<p>&#8377;4,000</p>\n", "<p>&#8377;2,000</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the monthly salary of Bhanu be x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x &times; (100 - (20+10+14+18</span><span style=\"font-family: Cambria Math;\">))%</span><span style=\"font-family: Cambria Math;\"> = 1520</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x &times; (100 - </span><span style=\"font-family: Cambria Math;\">62)%</span><span style=\"font-family: Cambria Math;\"> = 1520</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x &times; 38% = 1520</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&times;</mo><mfrac><mn>38</mn><mn>100</mn></mfrac><mo>=</mo><mn>1520</mn><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>=</mo><mfrac><mrow><mn>1520</mn><mo>&times;</mo><mn>100</mn></mrow><mn>38</mn></mfrac><mo>=</mo><mn>4000</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2344;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2360;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2366;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x &times; (100 - (20+10+14+18</span><span style=\"font-family: Cambria Math;\">))%</span><span style=\"font-family: Cambria Math;\"> = 1520</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x &times; (100 - </span><span style=\"font-family: Cambria Math;\">62)%</span><span style=\"font-family: Cambria Math;\"> = 1520</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x &times; 38% = 1520</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&times;</mo><mfrac><mn>38</mn><mn>100</mn></mfrac><mo>=</mo><mn>1520</mn><mspace linebreak=\"newline\"></mspace><mi>x</mi><mo>=</mo><mfrac><mrow><mn>1520</mn><mo>&times;</mo><mn>100</mn></mrow><mn>38</mn></mfrac><mo>=</mo><mn>4000</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: " <p>14.</span><span style=\"font-family:Cambria Math\"> The pie chart shows the percentage-wise break up of students in terms of </span><span style=\"font-family:Cambria Math\">specialisation</span><span style=\"font-family:Cambria Math\"> in Engineering.</span></p> <p><span style=\"font-family:Cambria Math\">Total number of students = 8000</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669282395/word/media/image5.png\"/></p> <p><span style=\"font-family:Cambria Math\">What is the ratio of the total number of students having either Networking or Graphics as their </span><span style=\"font-family:Cambria Math\">specialisations</span><span style=\"font-family:Cambria Math\"> to the number of students having C programming as their </span><span style=\"font-family:Cambria Math\">specialisation</span><span style=\"font-family:Cambria Math\">?</span></p>",
                    question_hi: " <p>14. </span><span style=\"font-family:Nirmala UI\">दिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छात्रों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इंजीनियरिंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विशेषज्ञता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आधार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतिशत</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">वार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वितरण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिखाया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span></p> <p><span style=\"font-family:Nirmala UI\">छात्रों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> = 8000</span></p> <p><img src=\"https://ssccglpinnacle.com/admin/resources/qdocs/test-1-EN-1/1669282395/word/media/image5.png\"/></p> <p><span style=\"font-family:Nirmala UI\">नेटवर्किंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">या</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ग्राफिक्स</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विशेषता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वाले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छात्रों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> C </span><span style=\"font-family:Nirmala UI\">प्रोग्रामिंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विशेषज्ञता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वाले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छात्रों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुपात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ज्ञात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करें</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span></p>",
                    options_en: [" <p> </span><span style=\"font-family:Cambria Math\">18 :</span><span style=\"font-family:Cambria Math\"> 13</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">29 :</span><span style=\"font-family:Cambria Math\"> 11</span></p>", 
                                " <p> </span><span style=\"font-family:Cambria Math\">11 :</span><span style=\"font-family:Cambria Math\"> 19</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">31 :</span><span style=\"font-family:Cambria Math\"> 17</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:Cambria Math\">18 :</span><span style=\"font-family:Cambria Math\"> 13</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">29 :</span><span style=\"font-family:Cambria Math\"> 11</span></p>",
                                " <p> </span><span style=\"font-family:Cambria Math\">11 :</span><span style=\"font-family:Cambria Math\"> 19</span></p>", " <p> </span><span style=\"font-family:Cambria Math\">31 :</span><span style=\"font-family:Cambria Math\"> 17</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">No of students having graphics and networking as their </span><span style=\"font-family:Cambria Math\">specialisations</span><span style=\"font-family:Cambria Math\">  =</span><span style=\"font-family:Cambria Math\"> 8000 × (12 + 19)% = 8000 × 31%</span></p> <p><span style=\"font-family:Cambria Math\">No of students having C programming as their </span><span style=\"font-family:Cambria Math\">specialisations</span><span style=\"font-family:Cambria Math\"> = 8000 × 17%</span></p> <p><span style=\"font-family:Cambria Math\">So, the required ratio = 8000 × 31</span><span style=\"font-family:Cambria Math\">%  :</span><span style=\"font-family:Cambria Math\">  8000 × 17% = 31 : 17</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">14.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Nirmala UI\">ग्राफिक्स</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नेटवर्किंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अपनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विशेषज्ञता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रखने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वाले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छात्रों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\"> = 8000 × (12 + </span><span style=\"font-family:Cambria Math\">19)%</span><span style=\"font-family:Cambria Math\"> = 8000 × 31%</span></p> <p><span style=\"font-family:Cambria Math\">C </span><span style=\"font-family:Nirmala UI\">प्रोग्रामिंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अपनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विशेषज्ञता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रूप</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रखने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वाले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">छात्रों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संख्या</span><span style=\"font-family:Cambria Math\">  =</span><span style=\"font-family:Cambria Math\"> 8000 × 17%</span></p> <p><span style=\"font-family:Nirmala UI\">अतः</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अभीष्ट</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुपात</span><span style=\"font-family:Cambria Math\"> = 8000 × 31</span><span style=\"font-family:Cambria Math\">% :</span><span style=\"font-family:Cambria Math\"> 8000 × 17% = 31: 17</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15.<span style=\"font-family: Cambria Math;\"> A certain sum is invested at 26% </span><span style=\"font-family: Cambria Math;\">p.a</span><span style=\"font-family: Cambria Math;\">, interest compounded annually. In how many years (</span><span style=\"font-family: Cambria Math;\">approx</span><span style=\"font-family: Cambria Math;\">) will it double?</span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 26% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2351;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2352;&#2381;&#2359;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2325;&#2381;&#2352;&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2327;&#2369;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2319;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>4</p>\n", "<p>3</p>\n", 
                                "<p>2</p>\n", "<p>5</p>\n"],
                    options_hi: ["<p>4</p>\n", "<p>3</p>\n",
                                "<p>2</p>\n", "<p>5</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Short trick:</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">No of years it takes to double the </span><span style=\"font-family: Cambria Math;\">money =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>69</mn><mi>R</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + 0.35</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, time = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>69</mn><mn>26</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + 0.35 </span><span style=\"font-family: Cambria Math;\">= 2.65</span><span style=\"font-family: Cambria Math;\"> + 0.35 = 3 years</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">:</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2376;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2327;&#2369;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>69</mn><mi>R</mi></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + 0.35</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>69</mn><mn>26</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> + 0.35 = 2.65 + 0.35 = 3 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. <span style=\"font-family: Cambria Math;\">A watercraft is rowed down 32 km along a river in 4 hours and 10 km against the river in 5 hours. Find the speed of the watercraft and that of the river.</span></p>\n",
                    question_hi: "<p>16. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 4 </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 32 km </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2346;&#2352;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 5 </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 10 km </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>4.5 km/h and 2.5 km/h</p>\n", "<p>4 km/h and 3 km/h</p>\n", 
                                "<p>5 km/h and 3 km/h</p>\n", "<p>3.5 km/h and 1.5 km/h</p>\n"],
                    options_hi: ["<p>4.5 km/h <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2.5 km/h</span></p>\n", "<p>4 km/h <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3 km/h</span></p>\n",
                                "<p>5 km/h <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 3 km/h</span></p>\n", "<p>3.5 km/h <span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 1.5 km/h</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Downstream speed of watercraft =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 8 km/hr</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Upstream speed of watercraft =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 2 km/hr</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Speed of watercraft =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>10</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> = 5 km/hr</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Speed of stream = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>6</mn><mn>2</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 3 km/hr</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>32</mn><mn>4</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 8 km/hr</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2346;&#2352;&#2368;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2358;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 2 km/hr</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>10</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> = 5 km/hr</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2343;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>-</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mfrac><mn>6</mn><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> = 3 km/hr</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17.<span style=\"font-family: Cambria Math;\"> Roshni travelled at the speed of 66 km/h while going from point A to point B and returned via the same route at the speed of 92.4 km/h. What was Roshni&rsquo;s overall average speed during the two-way journey?</span></p>\n",
                    question_hi: "<p>17.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2358;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> A </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2306;&#2342;&#2369;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> 66 km/h </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2352;&#2381;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 92.4 km/h </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2346;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2380;&#2335;&#2368;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2340;&#2352;&#2347;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2358;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>79 km/h</p>\n", "<p>79.2 km/h</p>\n", 
                                "<p>78 km/h</p>\n", "<p>77 km/h</p>\n"],
                    options_hi: ["<p>79 km/h</p>\n", "<p>79.2 km/h</p>\n",
                                "<p>78 km/h</p>\n", "<p>77 km/h</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Average speed of Roshni during two-way </span><span style=\"font-family: Cambria Math;\">journey =</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>66</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>92</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo></mrow><mrow><mn>66</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>92</mn><mo>.</mo><mn>4</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>66</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>92</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>158</mn><mo>.</mo><mn>4</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>92</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>2</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>77</mn><mo>&nbsp;</mo><mi>k</mi><mi>m</mi><mo>/</mo><mi>h</mi><mi>r</mi></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2340;&#2352;&#2347;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2380;&#2352;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2358;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>66</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>92</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo></mrow><mrow><mn>66</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>92</mn><mo>.</mo><mn>4</mn></mrow></mfrac><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>66</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>92</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>158</mn><mo>.</mo><mn>4</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>92</mn><mo>.</mo><mn>4</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>2</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>77</mn><mo>&nbsp;</mo><mi>k</mi><mi>m</mi><mo>/</mo><mi>h</mi><mi>r</mi></math></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18.<span style=\"font-family: Cambria Math;\"> The ratio of the ages of two friends is </span><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> 9, while after 10 years it will become 19 : 23. What will be their average age 7 years from now?</span></p>\n",
                    question_hi: "<p>18.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> 9 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> , </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> 19 : 23 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2351;&#2375;&#2327;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>33 years</p>\n", "<p>35 years</p>\n", 
                                "<p>36 years</p>\n", "<p>39 years</p>\n"],
                    options_hi: ["<p>33 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>35 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                                "<p>36 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n", "<p>39 <span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Present age of two </span><span style=\"font-family: Cambria Math;\">friends 7</span><span style=\"font-family: Cambria Math;\"> : 9 ) &times; 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Their Age after 10 years&nbsp; &nbsp;19 </span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\"> 23</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mn>23</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>18</mn><mo>)</mo></math>= 5 unit represents 10 years difference</p>\r\n<p><span style=\"font-family: Cambria Math;\">1 unit represent =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 2 yrs</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the present age of two friends = 14 &times; 2 = 28 </span><span style=\"font-family: Cambria Math;\">yrs</span><span style=\"font-family: Cambria Math;\"> and 18 &times; 2 = 36 </span><span style=\"font-family: Cambria Math;\">yrs</span><span style=\"font-family: Cambria Math;\"> respectively</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Their age after 7 </span><span style=\"font-family: Cambria Math;\">yrs</span><span style=\"font-family: Cambria Math;\"> = 28 + 7 = 35 </span><span style=\"font-family: Cambria Math;\">yrs</span><span style=\"font-family: Cambria Math;\"> and 36 + 7 = 43 </span><span style=\"font-family: Cambria Math;\">yrs</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the average age =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>43</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>78</mn><mo>&nbsp;</mo></mrow><mn>2</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mn>39</mn><mo>&nbsp;</mo><mi>y</mi><mi>r</mi><mi>s</mi></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">7 :</span><span style=\"font-family: Cambria Math;\"> 9 ) &times; 2</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2350;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp;19 :23</span></p>\r\n<p><span style=\"font-weight: 400;\">(23 - 18)</span>= 5 <span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">10 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 2 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2340;&#2381;&#2352;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> = 14 &times; 2 = 28 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 18 &times; 2 = 36 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> = 28 + 7 = 35 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 36 + 7 = 43 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2369;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>43</mn></mrow><mn>2</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>78</mn><mo>&nbsp;</mo></mrow><mn>2</mn></mfrac></math><span style=\"font-family: Cambria Math;\">=39 </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19.<span style=\"font-family: Cambria Math;\"> If </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 11, what is the value of </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><msup><mi>x</mi><mn>4</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></mrow></mfenced></math><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    question_hi: "<p>19.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mfrac><mn>1</mn><mi>x</mi></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = 11 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced><mrow><msup><mi>x</mi><mn>4</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></mrow></mfenced></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>14159</p>\n", "<p>14163</p>\n", 
                                "<p>15127</p>\n", "<p>15131</p>\n"],
                    options_hi: ["<p>14159</p>\n", "<p>14163</p>\n",
                                "<p>15127</p>\n", "<p>15131</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><mi>x</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>11</mn><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mn>11</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>121</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>123</mn><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>4</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mn>123</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>15129</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>15127</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>&nbsp;</mo><mo>-</mo><mfrac><mrow><mo>&nbsp;</mo><mn>1</mn></mrow><mi>x</mi></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>11</mn><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mn>11</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>121</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>123</mn><mspace linebreak=\"newline\"></mspace><msup><mi>x</mi><mn>4</mn></msup><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><msup><mn>123</mn><mn>2</mn></msup><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>15129</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>2</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>15127</mn></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20.<span style=\"font-family: Cambria Math;\"> A 37% discount was offered on an item with a marked price of &#8377;2,900. What was the selling price of the item?</span></p>\n",
                    question_hi: "<p>20.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;2,900 </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 37% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>&#8377;1,827</p>\n", "<p>&#8377;1,073</p>\n", 
                                "<p>&#8377; 1,823</p>\n", "<p>&#8377;1,829</p>\n"],
                    options_hi: ["<p>&#8377;1,827</p>\n", "<p>&#8377;1,073</p>\n",
                                "<p>&#8377; 1,823</p>\n", "<p>&#8377;1,829</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">MP of an item = &#8377;2,900</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">SP of an item = 2900 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = &#8377;1827</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = &#8377;2,900</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 2900 &times;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>63</mn><mn>100</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> = &#8377;1827</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21.&nbsp;<span style=\"font-weight: 400;\">What is the value of a sphere whose diameter is 16.8 cm? </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mi>U</mi><mi>s</mi><mi>e</mi><mo>&nbsp;</mo><mi mathvariant=\"normal\">&pi;</mi><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>22</mn></mrow><mn>7</mn></mfrac><mo>]</mo></math></p>\n",
                    question_hi: "<p>21. <span style=\"font-weight: 400;\">&#2313;&#2360; &#2327;&#2379;&#2354;&#2375; &#2325;&#2366; &#2350;&#2366;&#2344; &#2310;&#2351;&#2340;&#2344; &#2332;&#2381;&#2334;&#2366;&#2340; &#2325;&#2368;&#2332;&#2367;&#2319; &#2332;&#2367;&#2360;&#2325;&#2366; &#2357;&#2381;&#2351;&#2366;&#2360; 16.8 cm &#2361;&#2376;?&nbsp; <math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>[</mo><mo>&nbsp;</mo><mi mathvariant=\"normal\">&pi;</mi><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mi>&#2325;&#2366;</mi><mo>&nbsp;</mo><mi>&#2346;&#2381;&#2352;&#2351;&#2379;&#2327;</mi><mo>&nbsp;</mo><mi>&#2325;&#2368;&#2332;&#2367;&#2319;</mi><mo>&nbsp;</mo><mo>]</mo></math></span></p>\n",
                    options_en: ["<p>2487.312&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n", "<p>2473.812&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n", 
                                "<p>2483.712&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n", "<p>2483.172&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n"],
                    options_hi: ["<p>2487.312&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n", "<p>2473.812&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n",
                                "<p>2483.712&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n", "<p>2483.172&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup></math></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Volume of sphere = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mfrac><mrow><mn>4</mn><mo>&nbsp;</mo></mrow><mn>3</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mrow><mn>16</mn><mo>.</mo><mn>8</mn></mrow><mn>2</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>88</mn></mrow><mn>21</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2483</mn><mo>.</mo><mn>712</mn><mo>&nbsp;</mo><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup><mo>&nbsp;</mo><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2379;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>&nbsp;</mo></mrow><mn>3</mn></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>22</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><msup><mrow><mo>(</mo><mfrac><mrow><mn>16</mn><mo>.</mo><mn>8</mn></mrow><mn>2</mn></mfrac><mo>)</mo></mrow><mn>3</mn></msup><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>88</mn></mrow><mn>21</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>8</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>2483</mn><mo>.</mo><mn>712</mn><mo>&nbsp;</mo><mi>c</mi><msup><mi>m</mi><mn>3</mn></msup><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. <span style=\"font-family: Cambria Math;\">If the radii of two cylinders are in the ratio of </span><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 3 and their heights are in the ratio of 5 : 3, then the ratio of their volumes is:</span></p>\n",
                    question_hi: "<p>22. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2354;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">2 :</span><span style=\"font-family: Cambria Math;\"> 3 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> 5 : 3 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>22: 15</p>\n", "<p><span style=\"font-family: Cambria Math;\">20 :</span><span style=\"font-family: Cambria Math;\"> 27</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 4</span></p>\n", "<p>16: 23</p>\n"],
                    options_hi: ["<p>22: 15</p>\n", "<p><span style=\"font-family: Cambria Math;\">20 :</span><span style=\"font-family: Cambria Math;\"> 27</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">3 :</span><span style=\"font-family: Cambria Math;\"> 4</span></p>\n", "<p>16: 23</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Volume of </span><span style=\"font-family: Cambria Math;\">cylinder =</span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>r</mi><mi>a</mi><mi>d</mi><mi>i</mi><mi>u</mi><msup><mi>s</mi><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\"> &times;&nbsp; Height (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></span><span style=\"font-family: Cambria Math;\"> is constant)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Original&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>2</mn></msup></math></span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &times;&nbsp; &nbsp; &nbsp; 5</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">New&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 27&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>2</mn></msup></math>&nbsp; &nbsp; &nbsp; </span><span style=\"font-family: Cambria Math;\">&times;&nbsp; &nbsp; &nbsp; 3&nbsp;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the ratio of volumes of cylinders = </span><span style=\"font-family: Cambria Math;\">20 :</span><span style=\"font-family: Cambria Math;\"> 27</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &#2348;&#2375;&#2354;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2381;&#2352;&#2367;&#2332;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">2</span><span style=\"font-family: Cambria Math;\"> &times; </span><span style=\"font-family: Nirmala UI;\">&#2314;&#2305;&#2330;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">&pi;</mi></math></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;20&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>2</mn></msup></math>&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">&times;&nbsp; &nbsp; &nbsp; 5</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 27&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>2</mn></msup></math>&nbsp; &nbsp; &nbsp;</span><span style=\"font-family: Cambria Math;\">&times;&nbsp; &nbsp; &nbsp; 3</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;</span><span style=\"font-family: Cambria Math;\">: </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2354;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> = 20 : 27</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. <span style=\"font-family: Cambria Math;\">A man rides his bicycle for 10 </span><span style=\"font-family: Cambria Math;\">kilometres</span><span style=\"font-family: Cambria Math;\"> at a speed of 12 </span><span style=\"font-family: Cambria Math;\">kilometres</span><span style=\"font-family: Cambria Math;\"> per hour, then for another 12 </span><span style=\"font-family: Cambria Math;\">kilometres</span><span style=\"font-family: Cambria Math;\"> at a speed of 10 </span><span style=\"font-family: Cambria Math;\">kilometres</span><span style=\"font-family: Cambria Math;\"> per hour. What is his average speed for the entire trip approximately?</span></p>\n",
                    question_hi: "<p>23. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2311;&#2325;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10 km </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> 12 km/h </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2354;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2367;&#2352;</span><span style=\"font-family: Cambria Math;\"> 12 km </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> 10 km/h </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2354;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2349;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>10.8 km/h</p>\n", "<p>11.8 km/h</p>\n", 
                                "<p>11.4 km/h</p>\n", "<p>10 km/h</p>\n"],
                    options_hi: ["<p>10.8 <span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2366;</span></p>\n", "<p>11.8 <span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2366;</span></p>\n",
                                "<p>11.4 <span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2366;</span></p>\n", "<p>10 <span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2366;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Time taken to cover 10 km by bicycle at the speed of 12 km/</span><span style=\"font-family: Cambria Math;\">hr</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>12</mn></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>6</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> hrs</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Time taken to cover 12 km at the speed of 10 km/</span><span style=\"font-family: Cambria Math;\">hr</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>10</mn></mfrac><mo>=</mo><mfrac><mn>6</mn><mn>5</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> hrs</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Total time taken to cover 22 km distance =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>6</mn></mrow><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>36</mn><mo>&nbsp;</mo></mrow><mn>30</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>61</mn><mn>30</mn></mfrac><mo>&nbsp;</mo><mi>h</mi><mi>r</mi><mi>s</mi></math></p>\r\n<p><span style=\"font-family: Cambria Math;\">Average speed =</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>12</mn></mrow><mstyle displaystyle=\"true\"><mfrac><mn>61</mn><mn>30</mn></mfrac></mstyle></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>22</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>30</mn><mn>61</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>660</mn><mn>61</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>10</mn><mo>.</mo><mn>8</mn><mo>&nbsp;</mo><mi>k</mi><mi>m</mi><mo>/</mo><mi>h</mi><mi>r</mi></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">12 km/h </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2311;&#2325;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 10 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\">=</span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>12</mn></mfrac><mo>=</mo><mfrac><mn>5</mn><mn>6</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">10 km/h </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 12 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>10</mn></mfrac><mo>=</mo><mfrac><mn>6</mn><mn>5</mn></mfrac></math> </span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">22 km </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>6</mn></mrow><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>25</mn><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>36</mn><mo>&nbsp;</mo></mrow><mn>30</mn></mfrac><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mfrac><mn>61</mn><mn>30</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2328;&#2306;&#2335;&#2375;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2324;&#2360;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">= </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>+</mo><mo>&nbsp;</mo><mn>12</mn></mrow><mstyle displaystyle=\"true\"><mfrac><mn>61</mn><mn>30</mn></mfrac></mstyle></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>22</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>30</mn><mn>61</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>660</mn><mn>61</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>10</mn><mo>.</mo><mn>8</mn><mo>&nbsp;</mo><mi>k</mi><mi>m</mi><mo>/</mo><mi>h</mi><mi>r</mi></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. <span style=\"font-family: Cambria Math;\">The marked price of a mobile phone was &#8377;35,600. The shop owner offered a 10% trade discount on the marked price but on further request by the customer, he sold the phone for &#8377;30,000 after giving a cash discount. How much is this cash discount as a percentage of the price after the trade discount? (Give your answer correct to 2 decimal places.)</span></p>\n",
                    question_hi: "<p>24. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2379;&#2348;&#2366;&#2311;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;35,600 </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2325;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2375;&#2358;&#2325;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2352;&#2366;&#2361;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2352;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2325;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;30,000 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2325;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2 </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2370;&#2352;&#2381;&#2339;&#2366;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>6.37%</p>\n", "<p>5.75%</p>\n", 
                                "<p>4.95%</p>\n", "<p>5.25%</p>\n"],
                    options_hi: ["<p>6.37%</p>\n", "<p>5.75%</p>\n",
                                "<p>4.95%</p>\n", "<p>5.25%</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the second discount be x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">ATQ,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mn>35</mn><mo>,</mo><mn>600</mn><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>9</mn></mrow><mrow><mn>10</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>,</mo><mn>000</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>30</mn><mo>,</mo><mn>000</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn></mrow><mrow><mn>35</mn><mo>,</mo><mn>600</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>9</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>250</mn></mrow><mn>267</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">So, the required discount = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>267</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>250</mn></mrow><mn>267</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1700</mn><mn>267</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>367</mn><mo>&nbsp;</mo><mo>&asymp;</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>37</mn><mo>%</mo></math></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> x </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2358;&#2381;&#2344;&#2366;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&rArr;</mo><mn>35</mn><mo>,</mo><mn>600</mn><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mrow><mo>&nbsp;</mo><mn>9</mn></mrow><mrow><mn>10</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&times;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>,</mo><mn>000</mn><mspace linebreak=\"newline\"></mspace><mo>&rArr;</mo><mo>&nbsp;</mo><mi>x</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>30</mn><mo>,</mo><mn>000</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>10</mn></mrow><mrow><mn>35</mn><mo>,</mo><mn>600</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>9</mn></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mfrac><mrow><mo>&nbsp;</mo><mn>250</mn></mrow><mn>267</mn></mfrac></math></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2340;&#2307;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>267</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>250</mn></mrow><mn>267</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>1700</mn><mn>267</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>367</mn><mo>&nbsp;</mo><mo>&asymp;</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>37</mn><mo>%</mo></math></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: " <p>25. </span><span style=\"font-family:Cambria Math\">A and B share a few marbles in the ratio </span><span style=\"font-family:Cambria Math\">4 :</span><span style=\"font-family:Cambria Math\"> 5. If B gets 10 marbles more than A, then what is A’s share?</span></p>",
                    question_hi: " <p>25.</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कंचे</span><span style=\"font-family:Cambria Math\"> A </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> B </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बीच</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">4 :</span><span style=\"font-family:Cambria Math\"> 5 </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुपात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बांटे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यदि</span><span style=\"font-family:Cambria Math\"> B </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> A </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> 10 </span><span style=\"font-family:Nirmala UI\">कंचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अधिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मिलते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\"> A </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कितने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कंचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मिलेंगे</span><span style=\"font-family:Cambria Math\"> ?</span></p>",
                    options_en: [" <p> 8 marbles</span></p>", " <p> 42 marbles</span></p>", 
                                " <p> 12 marbles</span></p>", " <p> 40 marbles</span></p>"],
                    options_hi: [" <p> 8 </span><span style=\"font-family:Nirmala UI\">कंचे</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> 42 </span><span style=\"font-family:Nirmala UI\">कंचे</span><span style=\"font-family:Cambria Math\"> </span></p>",
                                " <p> 12 </span><span style=\"font-family:Nirmala UI\">कंचे</span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> 40 </span><span style=\"font-family:Nirmala UI\">कंचे</span><span style=\"font-family:Cambria Math\"> </span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">25.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Cambria Math\">Let the marbles of A and B be 4x and 5x respectively</span></p> <p><span style=\"font-family:Cambria Math\">ATQ,</span></p> <p><span style=\"font-family:Cambria Math\">5x - 4x = 10</span></p> <p><span style=\"font-family:Cambria Math\">x = 10</span></p> <p><span style=\"font-family:Cambria Math\">Share of A = 4x = 4 × 10 = 40</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">25.(</span><span style=\"font-family:Cambria Math\">d)</span></p> <p><span style=\"font-family:Nirmala UI\">माना</span><span style=\"font-family:Cambria Math\"> A </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> B </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कंचे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्रमशः</span><span style=\"font-family:Cambria Math\"> 4x </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> 5x </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Nirmala UI\">।</span></p> <p><span style=\"font-family:Nirmala UI\">प्रश्नानुसार</span><span style=\"font-family:Cambria Math\">,</span></p> <p><span style=\"font-family:Cambria Math\">5x - 4x = 10</span></p> <p><span style=\"font-family:Cambria Math\">x = 10</span></p> <p><span style=\"font-family:Cambria Math\">A </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हिस्सा</span><span style=\"font-family:Cambria Math\"> = 4x = 4 × 10 = 40</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>