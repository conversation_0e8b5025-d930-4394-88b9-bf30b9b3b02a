<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The number nearest to 7658 and exactly divisible by 45 is:</p>",
                    question_hi: "<p>1. 7658 के निकटतम और 45 से पूर्ण विभाज्य संख्या ______है।</p>",
                    options_en: ["<p>7645</p>", "<p>7660</p>", 
                                "<p>7640</p>", "<p>7650</p>"],
                    options_hi: ["<p>7645</p>", "<p>7660</p>",
                                "<p>7640</p>", "<p>7650</p>"],
                    solution_en: "<p>1.(d)<br><math display=\"inline\"><mfrac><mrow><mn>7658</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> = 8 (remainder) <br>So the nearest number of 7658 which is exactly divisible by 8 will be 7658 - 8 = 7650</p>",
                    solution_hi: "<p>1.(d)<br><math display=\"inline\"><mfrac><mrow><mn>7658</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> = 8 (शेषफल) <br>अतः 7658 की निकटतम संख्या जो 8 से पूर्णतः विभाज्य है, 7658 - 8 = 7650 होगी</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. If two numbers are each divided by the same divisor, then the remainders are 6 and 7, respectively. If the sum of the two numbers be divided by the same divisor, then the remainder is 5. The divisor is:</p>",
                    question_hi: "<p>2. यदि दो संख्याओं में से प्रत्येक को एक ही भाजक से विभाजित किया जाता है, तो शेषफल क्रमशः 6 और 7 होते हैं। यदि उन दो संख्याओं के योग को उसी भाजक से विभाजित किया जाए, तो शेषफल 5 प्राप्त होता है। भाजक कितना है ?</p>",
                    options_en: ["<p>6</p>", "<p>4</p>", 
                                "<p>13</p>", "<p>8</p>"],
                    options_hi: ["<p>6</p>", "<p>4</p>",
                                "<p>13</p>", "<p>8</p>"],
                    solution_en: "<p>2.(d)<br>Let the numbers be a and b and the divisor be d. The remainders when a and b are divided by d are 6 and 7 respectively. Thus,<br>a = d<math display=\"inline\"><msub><mrow><mi>q</mi></mrow><mrow><mn>1</mn></mrow></msub></math> + 6<br>b = d<math display=\"inline\"><msub><mrow><mi>q</mi></mrow><mrow><mn>2</mn></mrow></msub></math> + 7<br>For their sum<br>a + b = d(<math display=\"inline\"><msub><mrow><mi>q</mi></mrow><mrow><mn>1</mn></mrow></msub></math> + q<sub>2</sub>) + 13 &hellip; (i)<br>When a + b is divided by d, the remainder is 5,<br>a + b = dq + 5 &hellip; (ii)<br>Equating (i) and (ii) we get,<br>d(<math display=\"inline\"><msub><mrow><mi>q</mi></mrow><mrow><mn>1</mn></mrow></msub></math> + q<sub>2</sub>) + 13 = dq + 5<br>d(<math display=\"inline\"><msub><mrow><mi>q</mi></mrow><mrow><mn>1</mn></mrow></msub></math> + q<sub>2 </sub>- q) = - 8<br>Thus, d must divide 8. Testing values, we find that d = 8 satisfies the condition.<br>Therefore, the divisor is 8</p>",
                    solution_hi: "<p>2.(d)<br>माना संख्याएँ a और b हैं और भाजक d है। जब a और b को d से विभाजित किया जाता है तो शेषफल क्रमशः 6 और 7 होते हैं। इस प्रकार,<br>a = d<math display=\"inline\"><msub><mrow><mi>q</mi></mrow><mrow><mn>1</mn></mrow></msub></math> + 6<br>b = d<math display=\"inline\"><msub><mrow><mi>q</mi></mrow><mrow><mn>2</mn></mrow></msub></math> + 7<br>उनके योग के लिए<br>a + b = d(<math display=\"inline\"><msub><mrow><mi>q</mi></mrow><mrow><mn>1</mn></mrow></msub></math> + q<sub>2</sub>) + 13 &hellip; (i)<br>जब a + b को d से विभाजित किया जाता है, तो शेषफल 5 होता है,<br>a + b = dq + 5 &hellip; (ii)<br>(i) और (ii) की तुलना करने पर,<br>d(<math display=\"inline\"><msub><mrow><mi>q</mi></mrow><mrow><mn>1</mn></mrow></msub></math> + q<sub>2</sub>) + 13 = dq + 5<br>d(<math display=\"inline\"><msub><mrow><mi>q</mi></mrow><mrow><mn>1</mn></mrow></msub></math> + q<sub>2 </sub>- q) = - 8<br>इस प्रकार, 8, d से विभाजित होगा। मानों का परीक्षण करने पर, हम पाते हैं कि d = 8 शर्त को पूरा करता है।<br>अत: भाजक 8 है ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. What is the smallest number that must be added to 19487161 so that it is divisible by 11 ?</p>",
                    question_hi: "<p>3. निम्न में से वह सबसे छोटी संख्या कौन-सी है जिसे 19487161 में जोड़ना होगा ताकि वह 11 से विभाज्य हो जाए ?</p>",
                    options_en: ["<p>5</p>", "<p>16</p>", 
                                "<p>11</p>", "<p>10</p>"],
                    options_hi: ["<p>5</p>", "<p>16</p>",
                                "<p>11</p>", "<p>10</p>"],
                    solution_en: "<p>3.(d)<br>Divisibility rule of 11 = the difference between the sum of digits at its odd places and that of digits at the even places should be either 0 or divisible by 11<br>19487161 <math display=\"inline\"><mo>&#8594;</mo></math><br>Even place= 9 + 8 + 1 + 1 = 19<br>Odd place = 1 + 4 + 7 + 6 = 18<br>With the help of option,<br>Odd place = 18 + <strong>1 </strong>= 19<br>Even place = 19 +<strong> 0</strong> = 19<br>Difference = 19 - 19 = 0<br>So the required number will be 10.</p>",
                    solution_hi: "<p>3.(d)<br>11 का विभाज्यता नियम = विषम स्थानों पर अंकों के योग और सम स्थानों पर अंकों के योग के बीच का अंतर या तो 0 है या 11 से विभाज्य होना चाहिए । <br>19487161 <math display=\"inline\"><mo>&#8594;</mo></math><br>सम स्थान = 9 + 8 + 1 + 1 = 19<br>विषम स्थान = 1 + 4 + 7 + 6 = 18<br>विकल्प की सहायता से,<br>विषम स्थान = 18 +<strong> 1</strong> = 19<br>सम स्थान = 19 + <strong>0</strong> = 19<br>अंतर = 19 - 19 = 0<br>अतः अभीष्ट संख्या 10 होगी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If 3c2933k is divisible by both 5 and 11, where c and k are single digit natural numbers, then c + k = ______.</p>",
                    question_hi: "<p>4. यदि 3c2933k, 5 और 11 दोनों से विभाज्य है जहां c और k एक अंक वाली प्राकृतिक संख्याएं हैं, तो c+k =_______है।</p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>5</p>", "<p>7</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>5</p>", "<p>7</p>"],
                    solution_en: "<p>4.(a)<br>Divisibility of 11: The difference of the sum of digits at odd position and sum of digits at even position in a number is 0 or divisible by 11.<br>Now, (3 + 2 + 3 + k) - (c + 9 + 3) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math>(8 + k) = (c + 12) &hellip;&hellip;(i)<br>Divisibility of 5: If the last digit of the number should be 0 or 5 then that number will be divisible by 5.<br>Value of k = 5 (given we take only natural no.)<br>Put value of k in equation (i), we get<br><math display=\"inline\"><mo>&#8658;</mo></math>(8 + 5) = (c + 12) <br><math display=\"inline\"><mo>&#8658;</mo></math>c = 13 - 12 = 1<br>So,<br>c + k = 1 + 5 = 6</p>",
                    solution_hi: "<p>4.(a) <br>11 की विभाज्यता: किसी संख्या में विषम स्थान पर अंकों के योग और सम स्थान पर अंकों के योग का अंतर 0 हो या 11 से विभाज्य होना चाहिए <br>अब, (3 + 2 + 3 + k) - (c + 9 + 3) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math>(8 + k) = (c + 12) &hellip;&hellip;(i)<br>5 से विभाज्यता: यदि किसी संख्या का अंतिम अंक 0 या 5 हो तो वह संख्या 5 से विभाज्य होगी।<br>K का मान = 5 (दिया है, हम केवल प्राकृतिक संख्या लेंगे)<br>समीकरण (i) में k का मान रखने पर, हमें मिलता है<br><math display=\"inline\"><mo>&#8658;</mo></math>(8 + 5) = (c + 12) <br><math display=\"inline\"><mo>&#8658;</mo></math>c = 13 - 12 = 1<br>इसलिए,<br>c + k = 1 + 5 = 6</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5. Raju has ₹11,000 and starts saving ₹5,000 each week towards buying a new laptop. At the same time, Ramesh has ₹60,000 and begins spending ₹2,000 per week on supplies for his art class. Will there be a week when they have the same amount of money? ",
                    question_hi: "5. राजू के पास ₹11,000 हैं और वह नया लैपटॉप खरीदने के लिए प्रति सप्ताह ₹5,000 की बचत करना शुरू करता है। उसी समय, रमेश के पास ₹60,000 हैं और वह अपनी कला कक्षा के लिए सामग्री पर प्रति सप्ताह ₹2,000 खर्च करना शुरू करता है। क्या कोई ऐसा सप्ताह होगा जब उनके पास समान धनराशि होगी?",
                    options_en: [" Yes, after 5 weeks", " No, they will never have the same amount", 
                                " Yes, after 7 weeks", " Yes, after 6 weeks"],
                    options_hi: [" हाँ, 5 सप्ताह के बाद", " नहीं, उनके पास कभी भी समान धनराशि नहीं होगी",
                                " हाँ, 7 सप्ताह के बाद", " हाँ, 6 सप्ताह के बाद"],
                    solution_en: "5.(d) According to question,<br />Amount of Raju after 6 weeks   = 11000 + 7 × 5000 = ₹ 46000<br />Now, <br />Amount of Ramesh after 6 weeks = ₹60000 - 7 × 2000 = ₹ 46000 ",
                    solution_hi: "5.(d) प्रश्न के अनुसार,<br />6 सप्ताह के बाद राजू की राशि = 11000 + 7 × 5000 = ₹ 46000<br />अब, <br />6 सप्ताह के बाद रमेश की राशि = ₹ 60000 - 7 × 2000 = ₹ 46000",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. The sum of two&minus;digit number and the number obtained by interchanging the digit is 77. If the difference of digits is 1, then the number is:</p>",
                    question_hi: "<p>6. दो-अंकों की संख्या का योग और उसके अंको को आपस में बदलने पर प्राप्त संख्या 77 है। यदि अंकों का अंतर 1 है, तो संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>67</p>", "<p>34</p>", 
                                "<p>12</p>", "<p>45</p>"],
                    options_hi: ["<p>67</p>", "<p>34</p>",
                                "<p>12</p>", "<p>45</p>"],
                    solution_en: "<p>6.(b)<br>To solve these kinds of questions we take help of the options,<br>Option (a) 67 + 76 = 143 (not satisfied)<br><strong>Option (b) 34 + 43 = 77 (satisfied)</strong><br>Option (c)12 + 21 = 33 (not satisfied)<br>Option (d) 45 + 54 = 99 (not satisfied)<br>So number will be 34</p>",
                    solution_hi: "<p>6.(b)<br>इस प्रकार के प्रश्नों को हल करने के लिए हम विकल्पों की सहायता लेते हैं,<br>विकल्प (a) 67 + 76 = 143 (संतुष्ट नहीं)<br><strong>विकल्प (b) 34 + 43 = 77 (संतुष्ट)</strong><br>विकल्प (c)12 + 21 = 33 (संतुष्ट नहीं)<br>विकल्प (d) 45 + 54 = 99 (संतुष्ट नहीं)<br>तो संख्या 34 होगी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. Write the smallest digit in the blank space of a number 7__7624, so that the number formed is divisible by 3.</p>",
                    question_hi: "<p>7. एक संख्या 7__7624 के रिक्त स्थान में सबसे छोटा अंक लिखिए, जिससे बनने वाली संख्या 3 से विभाज्य हो जाए।</p>",
                    options_en: ["<p>2</p>", "<p>1</p>", 
                                "<p>3</p>", "<p>0</p>"],
                    options_hi: ["<p>2</p>", "<p>1</p>",
                                "<p>3</p>", "<p>0</p>"],
                    solution_en: "<p>7.(b)<br>Divisibility rule of 3 = sum of digits is divisible by 3<br>7 + <math display=\"inline\"><mi>x</mi></math> + 7 + 6 + 2 + 4 = 26 + x<br>Put <math display=\"inline\"><mi>x</mi></math> = 1<br>26 + 1 = 27 (which is divisible by 3)<br>So the smallest number will be 1</p>",
                    solution_hi: "<p>7.(b)<br>3 की विभाज्यता नियम = अंकों का योग 3 से विभाज्य हो<br>7 + <math display=\"inline\"><mi>x</mi></math> + 7 + 6 + 2 + 4 = 26 + x<br><math display=\"inline\"><mi>x</mi></math> = 1 रखने पर,<br>26 + 1 = 27 (जो 3 से विभाज्य है)<br>अतः सबसे छोटी संख्या 1 होगी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. After the division of a number successively by 2, 3 and 5, the remainders are 1, 2 and 3, respectively. What will be the remainder, if 13 divides the same number (if the last quotient is 1) ?</p>",
                    question_hi: "<p>8. किसी संख्या को क्रमशः 2, 3 और 5 से विभाजित करने पर शेषफल क्रमशः 1, 2 और 3 प्राप्त होता हैं। यदि उसी संख्या को 13 से विभाजित किया जाए तो शेषफल क्या होगा (यदि अंतिम भागफल 1 है) ?</p>",
                    options_en: ["<p>2</p>", "<p>0</p>", 
                                "<p>3</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>0</p>",
                                "<p>3</p>", "<p>1</p>"],
                    solution_en: "<p>8.(d) <br>In this type of questions, we start the calculation from the last and come towards the initial position.<br>The number which when divided by 5 leaves remainder 3 and quotient is 1 <br>= 5 &times; 1 + 3 = 8<br>When a number is divided by 3 , the remainder is 2<br>= 8&times; 3 + 2 = 26<br>When a number is divided by 2, the remainder is 1 <br>= 26 &times; 2 + 1 = 53<br>So, number is 53<br>Hence, rem. <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1<br><strong>Short trick:-</strong> <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736590520877.png\" alt=\"rId4\" width=\"323\" height=\"157\"><br>Hence, rem. <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1</p>",
                    solution_hi: "<p>8.(d) <br>इस प्रकार के प्रश्नों में हम गणना आखिरी से शुरू करके प्रारंभिक स्थिति की ओर आते हैं।<br>वह संख्या जिसे 5 से विभाजित करने पर शेषफल 3 तथा भागफल 1 प्राप्त होता है <br>= 5 &times; 1 + 3 = 8<br>जब किसी संख्या को 3 से विभाजित किया जाता है, तो शेषफल 2 होता है<br>= 8&times; 3 + 2 = 26<br>जब किसी संख्या को 2 से विभाजित किया जाता है, तो शेषफल 1 होता है <br>= 26 &times; 2 + 1 = 53<br>तो, संख्या 53 है<br>इसलिए, शेष <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1<br><strong>शॉर्ट ट्रिक:-</strong><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1736590521006.png\" alt=\"rId5\" width=\"305\" height=\"141\"><br>इसलिए, शेष&nbsp; <math display=\"inline\"><mfrac><mrow><mn>53</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> = 1</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. How many factors of 14,400 are divisible by 18 but not by 36 ?</p>",
                    question_hi: "<p>9. 14,400 के कितने गुणनखंड 18 से विभाज्य हैं, लेकिन 36 से नहीं ?</p>",
                    options_en: ["<p>3</p>", "<p>5</p>", 
                                "<p>4</p>", "<p>2</p>"],
                    options_hi: ["<p>3</p>", "<p>5</p>",
                                "<p>4</p>", "<p>2</p>"],
                    solution_en: "<p>9.(a)<br>Factor of 14400 = <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 18 &times; (2<sup>5</sup> &times; 5<sup>2</sup>)<br>Divisible by 18 then remaining factor = 6 &times; 3 = 18<br><math display=\"inline\"><mo>&#8658;</mo></math><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 36 &times; (2<sup>4</sup> &times; 5<sup>2</sup>)<br>Divisible by 36 then remaining factor = 5 &times; 3 = 15<br>Required factor divisible by 18 but not by 36 = 18 - 15 = 3</p>",
                    solution_hi: "<p>9.(a)<br>14400 का गुणनखंड = <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 18 &times; (2<sup>5</sup> &times; 5<sup>2</sup>)<br>18 से विभाज्य तो शेष गुणनखंड = 6 &times; 3 = 18<br><math display=\"inline\"><mo>&#8658;</mo></math><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>6</mn></mrow></msup></math> &times; 3<sup>2</sup> &times; 5<sup>2</sup> = 36 &times; (2<sup>4</sup> &times; 5<sup>2</sup>)<br>36 से विभाज्य तो शेष गुणनखंड = 5 &times; 3 = 15<br>आवश्यक गुणनखंड 18 से विभाज्य है लेकिन 36 से नहीं = 18 - 15 = 3</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. K being any odd number greater than 1, k<sup>33</sup> &ndash;k is always divisible by:</p>",
                    question_hi: "<p>10. K 1 से बड़ी कोई भी विषम संख्या होने पर, k<sup>33</sup> &ndash;k हमेशा _______से विभाज्य होगा।</p>",
                    options_en: ["<p>5</p>", "<p>13</p>", 
                                "<p>24</p>", "<p>15</p>"],
                    options_hi: ["<p>5</p>", "<p>13</p>",
                                "<p>24</p>", "<p>15</p>"],
                    solution_en: "<p>10.(c)<strong> Given:</strong> k<sup>33</sup> &ndash;k<br>= k(k<sup>32</sup> - k)<br>= k(k<sup>16</sup> - 1)(k<sup>16</sup> + 1)<br>= k(k<sup>8</sup> - 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>4 </sup>- 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>2</sup> - 1)(k<sup>2</sup> + 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k - 1)(k + 1)(k<sup>2</sup> +1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>Put the value of k = 3, 5 ,7&hellip;..<br>= 3 &times; 2 &times; 4[(3<sup>2</sup> +1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>= 24 &times; [(3<sup>2</sup> +1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>From the above expression it is clear that k<sup>33</sup> - k will always be divisible by 24.</p>",
                    solution_hi: "<p>10.(c)<strong> दिया गया:</strong> k<sup>33</sup> &ndash;k<br>= k(k<sup>32</sup> - k)<br>= k(k<sup>16</sup> - 1)(k<sup>16</sup> + 1)<br>= k(k<sup>8</sup> - 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>4 </sup>- 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k<sup>2</sup> - 1)(k<sup>2</sup> + 1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>= k(k - 1)(k + 1)(k<sup>2</sup> +1)(k<sup>4</sup> + 1)(k<sup>8</sup> + 1)(k<sup>16</sup> + 1)<br>k का मान = 3, 5, 7 &hellip;.. रखने पर,<br>= 3 &times; 2 &times; 4[(3<sup>2</sup> +1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>= 24 &times; [(3<sup>2</sup> +1)(3<sup>4</sup> + 1)(3<sup>8</sup> + 1)(3<sup>16</sup> + 1)]<br>उपरोक्त अभिव्यक्ति से यह स्पष्ट है कि k<sup>33</sup> - k हमेशा 24 से विभाज्य होगा ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. In an election, voters have the option to vote for one of three candidates. They also have the option to select \'None Of The Above\' (NOTA) to reject all the candidates. All three candidates get 6 times the NOTA votes. The winner gets twice the votes of the second runner-up. The first runner-up gets 900 more votes than NOTA and defeats the second runner-up by 150 votes. Find the total number of votes cast.</p>",
                    question_hi: "<p>11. एक चुनाव में, मतदाताओं के पास तीन उम्मीदवारों में से एक उम्मीदवार को वोट देने का विकल्प है। उनके पास सभी उम्मीदवारों को अस्वीकार करने के लिए \'उपरोक्त में से कोई नहीं\' (None Of The Above - NOTA) के चयन का भी विकल्प है। तीनों उम्मीदवारों को नोटा (NOTA) के 6 गुना वोट मिलते हैं। विजेता को दूसरे उपविजेता के वोटों के दोगुने वोट मिलते हैं। पहले उपविजेता को नोटा (NOTA) से 900 वोट अधिक मिलते हैं और वह दूसरे उपविजेता को 150 वोट से हरा देता है। डाले गए मतों की कुल संख्या ज्ञात करें।</p>",
                    options_en: ["<p>15177</p>", "<p>11577</p>", 
                                "<p>11025</p>", "<p>17715</p>"],
                    options_hi: ["<p>15177</p>", "<p>11577</p>",
                                "<p>11025</p>", "<p>17715</p>"],
                    solution_en: "<p>11.(c)<br>Let winner = A , 1<sup>st</sup> runner up = B, 2<sup>nd</sup> runner up = C<br>and nota = N<br>According to question<br>A = 2C, B = N + 900 and C = N + 900 - 150 <br>Now,<br>A + B + C = 6N<br>2C + B + C = 6N<br>3C + B = 6N<br>3C + N + 900 = 6N<br>3(N + 900 - 150) + N + 900 = 6N<br>3N + 2250 + N + 900 = 6N<br>3150 = 2N<br>N = 1575<br>Hence, B = N + 900 = 2475<br>C = N + 900 - 150 <br>= 2325<br>A = 2C = 4650<br>Total votes = A + B + C + N<br>= 4650 + 2475 + 2325 + 1575 <br>= 11025</p>",
                    solution_hi: "<p>11.(c)<br>माना विजेता = A ,प्रथम उपविजेता = B ,द्वितीय उपविजेता = C<br>और नोटा = N<br>प्रश्न के अनुसार<br>A = 2C, B = N + 900 and C = N + 900 - 150 <br>अब,<br>A + B + C = 6N<br>2C + B + C = 6N<br>3C + B = 6N<br>3C + N + 900 = 6N<br>3(N + 900 - 150) + N + 900 = 6N<br>3N + 2250 + N + 900 = 6N<br>3150 = 2N<br>N = 1575<br>अत:, B = N + 900 = 2475<br>C = N + 900 - 150 <br>= 2325<br>A = 2C = 4650<br>कुल वोट = A + B + C + N<br>= 4650 + 2475 + 2325 + 1575 <br>= 11025</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "12. How many numbers between 10 and 65 are divisible by 2, 3 and 4 ?",
                    question_hi: "12. 10 और 65 के बीच कितनी संख्याएं 2, 3 और 4 से विभाज्य हैं ?",
                    options_en: [" 5 ", " 7 ", 
                                " 9 ", " 3"],
                    options_hi: [" 5 ", " 7 ",
                                " 9 ", " 3"],
                    solution_en: "12.(a)<br />LCM of 2, 3, 4 = 12<br />Numbers between 10 to 65 which is divisible by 2, 3, 4 = 12, 24, 36, 48, 60<br />Hence, the required numbers will be 5.",
                    solution_hi: "12.(a)<br />2, 3, 4 का LCM = 12<br />10 से 65 के बीच की संख्याएँ जो 2, 3, 4 से विभाज्य है = 12, 24, 36, 48, 60<br />अतः, अभीष्ट संख्याएँ 5 होगी।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. A&rsquo;s marks in Mathematics are directly proportional to practice time. In 6 hours of&nbsp;practice, A gets 70 marks. What should be the practice time (approximately) to get 90&nbsp;Marks ?</p>",
                    question_hi: "<p>13. गणित में A के अंक उसके अभ्यास समय के अनुक्रमानुपाती (directly proportional) है। 6 घंटे&nbsp;के अभ्यास में A को 70 अंक मिलते हैं। 90 अंक प्राप्त करने के लिए उसका अभ्यास समय (लगभग) क्या&nbsp;होना चाहिए ?</p>",
                    options_en: ["<p>7.7 hours</p>", "<p>7 hours</p>", 
                                "<p>8.3 hours</p>", "<p>8 hours</p>"],
                    options_hi: ["<p>7.7 घंटा</p>", "<p>7 घंटा</p>",
                                "<p>8.3 घंटा</p>", "<p>8 घंटा</p>"],
                    solution_en: "<p>13.(a)<br>Practice time to get 70 marks = 6 hours<br>Practice time to get 90 marks = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> &times; 90 = 7.7 hours (approx)</p>",
                    solution_hi: "<p>13.(a)<br>70 अंक प्राप्त करने के लिए अभ्यास का समय = 6 घंटे<br>90 अंक प्राप्त करने के लिए अभ्यास का समय = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>70</mn></mrow></mfrac></math> &times; 90 = 7.7 घंटे (लगभग)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A two-digit number is 12 more than five times the sum of its digits. The number formed by reversing the digits is 9 less than the original number. The number is:</p>",
                    question_hi: "<p>14. दो अंकों की एक संख्या अपने अंकों के योग के पांच गुने से 12 अधिक है। अंकों को उलटने से बनी संख्या मूल संख्या से 9 कम है। वह संख्या ______है।</p>",
                    options_en: ["<p>96</p>", "<p>78</p>", 
                                "<p>87</p>", "<p>69</p>"],
                    options_hi: ["<p>96</p>", "<p>78</p>",
                                "<p>87</p>", "<p>69</p>"],
                    solution_en: "<p>14.(c)<br>Let number = 10<math display=\"inline\"><mi>x</mi></math> + y<br>According to the question,<br>Number is 12 more than five times the sum of its digits<br>10<math display=\"inline\"><mi>x</mi></math> + y = 5(x + y) + 12<br>5<math display=\"inline\"><mi>x</mi></math> - 4y = 12-------(i)<br>Number formed by reversing the digits is 9 less than the original number<br>10y + <math display=\"inline\"><mi>x</mi></math> = 10x + y - 9<br><math display=\"inline\"><mi>x</mi></math> - y = 1-------(ii)<br>Equation [(i) - 5 &times; (ii)] we get;<br>y = 7<br>Put y = 7 in equation (ii) we get;<br><math display=\"inline\"><mi>x</mi></math> - 7 = 1 &rArr; x = 8<br>Hence, number = 10 &times; 8 + 7 = 87</p>",
                    solution_hi: "<p>14.(c)<br>माना संख्या = 10x&nbsp;+ y<br>प्रश्न के अनुसार,<br>संख्या अपने अंकों के योग के पांच गुना से 12 अधिक है<br>10<math display=\"inline\"><mi>x</mi></math> + y = 5(x + y) + 12<br>5<math display=\"inline\"><mi>x</mi></math> - 4y = 12-------(i)<br>अंकों को उलटने से बनी संख्या मूल संख्या से 9 कम है<br>10y + <math display=\"inline\"><mi>x</mi></math> = 10x + y - 9<br><math display=\"inline\"><mi>x</mi></math> - y = 1-------(ii)<br>समीकरण [(i) - 5 &times; (ii)] हमें मिलता है;<br>y = 7<br>समीकरण (ii) में y = 7 रखने पर हमें प्राप्त होता है;<br><math display=\"inline\"><mi>x</mi></math> - 7 = 1 &rArr; x = 8<br>अत: संख्या = 10 &times; 8 + 7 = 87</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. When the integer n is divided by 5, the remainder is 3. What is the remainder if 6n is divided by 5 ?</p>",
                    question_hi: "<p>15. जब पूर्णांक n को 5 से भाग दिया जाता है, तो शेषफल 3 प्राप्त होता है। यदि 6n को 5 से भाग दिया जाए तो शेषफल क्या होगा ?</p>",
                    options_en: ["<p>0</p>", "<p>2</p>", 
                                "<p>3</p>", "<p>1</p>"],
                    options_hi: ["<p>0</p>", "<p>2</p>",
                                "<p>3</p>", "<p>1</p>"],
                    solution_en: "<p>15.(c)<br>Let n = (5 + 3) = 8<br>Then, 6n = 6 &times; 8 = 48<br>Hence, rem. <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>n</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> = rem. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>5</mn></mfrac></math> = 3</p>",
                    solution_hi: "<p>15.(c)<br>माना n = (5 + 3) = 8<br>फिर , 6n = 6 &times; 8 = 48<br>इसलिए , शेष. <math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>n</mi></mrow><mrow><mn>5</mn></mrow></mfrac></math> = शेष. <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>48</mn><mn>5</mn></mfrac></math> = 3</p>",
                    correct: "c ",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>