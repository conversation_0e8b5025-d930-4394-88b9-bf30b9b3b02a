<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. What is MPLADS?</p>",
                    question_hi: "<p>1. What is MPLADS?</p>",
                    options_en: ["<p>A scheme launched by the government of India which enables Members of Parliament to do development work in their constituencies.</p>", "<p>A scheme launched by the Madhya Pradesh government for the protection of ladies</p>", 
                                "<p>A scheme launched by Maharashtra and Punjab government for legal assistance to deprived sections</p>", "<p>A scheme launched by Madhya Pradesh government for the protection of lions and other endangered species</p>"],
                    options_hi: ["<p>भारत सरकार द्वारा शुरू की गई एक योजना जो संसद सदस्यों को अपने निर्वाचन क्षेत्रों में विकास कार्य करने में सक्षम बनाती है</p>", "<p>मध्य प्रदेश सरकार द्वारा महिलाओं की सुरक्षा के लिए शुरू की गई एक योजना</p>",
                                "<p>वंचित वर्गों को कानूनी सहायता के लिए महाराष्ट्र और पंजाब सरकार द्वारा शुरू की गई एक योजना</p>", "<p>मध्य प्रदेश सरकार द्वारा शेरों और अन्य लुप्तप्राय प्रजातियों के संरक्षण के लिए शुरू की गई एक योजना</p>"],
                    solution_en: "<p>1.(a) MPLADS (Members of Parliament Local Area Development Scheme) was introduced in 1993 under the Ministry of Statistics and Programme Implementation, Government of India. <strong>Objective -</strong> To enable the MP to suggest and get executed developmental works of a capital nature based on locally felt needs with an emphasis on the creation of durable assets.</p>",
                    solution_hi: "<p>1.(a) MPLADS (संसद सदस्य स्थानीय क्षेत्र विकास योजना) 1993 में भारत सरकार के सांख्यिकी और कार्यक्रम कार्यान्वयन मंत्रालय के तहत शुरू की गई थी। <strong>उद्देश्य </strong>- सांसद को स्थायी संपत्तियों के निर्माण पर जोर देने के साथ स्थानीय स्तर पर महसूस की जाने वाली जरूरतों के आधार पर पूंजीगत प्रकृति के विकासात्मक कार्यों का सुझाव देने और निष्पादित करने में सक्षम बनाना।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Which scheme of the Government of India provides for the Delivery of LPG cylinders in the below poverty line (BPL) families?</p>",
                    question_hi: "<p>2. भारत सरकार की कौन सी योजना गरीबी रेखा से नीचे (BPL) परिवारों में LPG सिलेंडर की डिलीवरी प्रदान करती है?</p>",
                    options_en: ["<p>Ayushman</p>", "<p>National Health Mission</p>", 
                                "<p>National Social Assistance Programme</p>", "<p>Ujjwala</p>"],
                    options_hi: ["<p>आयुष्मान</p>", "<p>राष्ट्रीय स्वास्थ्य मिशन</p>",
                                "<p>राष्ट्रीय सामाजिक सहायता कार्यक्रम</p>", "<p>उज्जवला</p>"],
                    solution_en: "<p>2.(d) <strong>Pradhan Mantri Ujjwala Yojna </strong>(1 May 2016, Ballia, Uttar Pradesh). <strong>Ayushman Bharat </strong>(23 September 2018) - A program which aims to provide a service to create a healthy, capable and content new India.<strong> National Health Mission</strong> (1 May 2013) - Subsumed the National Rural Health Mission (12 April 2005) and National Urban Health Mission (1 May 2013) to provide affordable universal access to public health care services. <strong>National Social Assistance Programme</strong> (15 August 1995) - Provides financial assistance to the elderly, widows and persons with disabilities (Social pensions).</p>",
                    solution_hi: "<p>2.(d) <strong>प्रधानमंत्री उज्ज्वला योजना</strong> (1 मई 2016, बलिया, उत्तर प्रदेश)। आयुष्मान भारत (23 सितंबर 2018) - एक कार्यक्रम जिसका उद्देश्य एक स्वस्थ, सक्षम और संतुष्ट नए भारत के निर्माण के लिए सेवा प्रदान करना है। <strong>राष्ट्रीय स्वास्थ्य मिशन</strong> (1 मई 2013) - सार्वजनिक स्वास्थ्य देखभाल सेवाओं तक किफायती सार्वभौमिक पहुंच प्रदान करने के लिए राष्ट्रीय ग्रामीण स्वास्थ्य मिशन (12 अप्रैल 2005) और राष्ट्रीय शहरी स्वास्थ्य मिशन (1 मई 2013) को शामिल किया गया। <strong>राष्ट्रीय सामाजिक सहायता कार्यक्रम</strong> (15 अगस्त 1995) - बुजुर्गों, विधवाओं और विकलांग व्यक्तियों को वित्तीय सहायता प्रदान करता है (सामाजिक पेंशन)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. What is the name of the web portal launched by the Department of Telecommunication that will allow tracking of radiations emitted from mobile towers within their locality and check for their compliance with the Electromagnetic Field (EMF) emission norms defined by the government?</p>",
                    question_hi: "<p>3. दूरसंचार विभाग द्वारा शुरू किए गए वेब पोर्टल का नाम क्या है जो अपने इलाके में मोबाइल टावरों से निकलने वाली विकिरणों की ट्रैकिंग की अनुमति देगा और सरकार द्वारा परिभाषित विद्युत चुम्बकीय क्षेत्र (EMF) उत्सर्जन मानदंडों के अनुपालन की जांच करेगा?</p>",
                    options_en: ["<p>Tarang Sanchar</p>", "<p>Sanchar Seva</p>", 
                                "<p>Tarang Kiran</p>", "<p>Sanchar Gyan</p>"],
                    options_hi: ["<p>तरंग संचार</p>", "<p>संचार सेवा</p>",
                                "<p>तरंग किरण</p>", "<p>संचार ज्ञान</p>"],
                    solution_en: "<p>3.(a) <strong>Tarang Sanchar -</strong> It is the web portal launched by the Department of Telecommunication s, with the goal of generating confidence and conviction about the safety and harmlessness of mobile towers, as well as dispelling myths and misconceptions. The EMF Portal provides a public interface with a simple map-based search tool for seeing mobile towers in the neighborhood of any location.</p>",
                    solution_hi: "<p>3.(a)<strong> तरंग संचार - </strong>यह दूरसंचार विभाग द्वारा शुरू किया गया वेब पोर्टल है, जिसका लक्ष्य मोबाइल टावरों की सुरक्षा और हानिरहितता के बारे में विश्वास और दृढ़ विश्वास पैदा करना है, साथ ही मिथकों और गलतफहमियों को दूर करना है। EMF पोर्टल किसी भी स्थान के पड़ोस में मोबाइल टावरों को देखने के लिए एक सरल मानचित्र-आधारित खोज उपकरण के साथ एक सार्वजनिक इंटरफ़ेस प्रदान करता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The Ministry of Human Resource Development has designed a one stop education portal which caters to the needs of students, starting from elementary students to research scholars, teachers and life-long learners. What is the name of that portal?</p>",
                    question_hi: "<p>4. मानव संसाधन विकास मंत्रालय ने एक वन स्टॉप एजुकेशनलपोर्टल तैयार किया है जो छात्रों की जरूरतों को पूरा करता है, प्रारंभिक छात्रों से लेकर शोध करने वाले विद्वानों, शिक्षकों और जीवन भर सीखने वालों तक। उस पोर्टल का नाम क्या है?</p>",
                    options_en: ["<p>PRASHIKSHAK</p>", "<p>SAKSHAT</p>", 
                                "<p>PADHAI</p>", "<p>DIKSHA</p>"],
                    options_hi: ["<p>प्रशिक्षक</p>", "<p>साक्षात</p>",
                                "<p>पढाई</p>", "<p>दीक्षा</p>"],
                    solution_en: "<p>4.(b) <strong>SAKSHAT </strong>- Launched on October 30, 2006. <strong>PRASHIKSHAK </strong>- The teacher education portal for District Institutes of Education and Training (DIETs) - Launched on 30th June 2016, by the Ministry of Education (MoE). <strong>DIKSHA </strong>- A National Teacher\'s Platform built to host Open Educational Resources (OER) and tools for teachers in schools, teacher educators in Teacher Education Institutes (TEIs), and student teachers in TEIs. Launched on 5 September 2017, by the Ministry of Education (MoE).</p>",
                    solution_hi: "<p>4.(b) <strong>साक्षात(SAKSHAT) -</strong> यह 30 अक्टूबर 2006 को लॉन्च किया गया। <strong>प्रशिक्षक(PRASHIKSHAK) - </strong>जिला शिक्षा एवं प्रशिक्षण संस्थानों (DIET) के लिए शिक्षा पोर्टल जिसे 30 जून 2016 को शिक्षा मंत्रालय (MoE) द्वारा लॉन्च किया गया। <strong>दीक्षा (DIKSHA) -</strong> स्कूलों में शिक्षकों, टीचर एजुकेशन इंस्टीटूट्स (TEI) में शिक्षक प्रशिक्षकों और TEI में छात्र शिक्षकों के लिए मुक्त शैक्षिक संसाधनों (OER) और उपकरणों की मेजबानी के लिए बनाया गया एक राष्ट्रीय शिक्षक मंच। शिक्षा मंत्रालय (MoE) द्वारा 5 सितंबर 2017 को लॉन्च किया गया।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following is NOT a part of the National Social Assistance Programme?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन राष्ट्रीय सामाजिक सहायता कार्यक्रम का हिस्सा नहीं है?</p>",
                    options_en: ["<p>Indira Gandhi National Disability Pension Scheme</p>", "<p>Indira Gandhi National Widow Pension Scheme</p>", 
                                "<p>Annapurna</p>", "<p>AYUSH</p>"],
                    options_hi: ["<p>इंदिरा गांधी राष्ट्रीय विकलांगता पेंशन योजना</p>", "<p>इंदिरा गांधी राष्ट्रीय विधवा पेंशन योजना</p>",
                                "<p>अन्नपूर्णा</p>", "<p>आयुष</p>"],
                    solution_en: "<p>5.(d) <strong>AYUSH. National Social Assistance Programme (NSAP): </strong>Represents a significant step towards the fulfilment of the Directive Principles in <strong>Article 42 </strong>(provision for securing just and humane conditions of work and for maternity relief) and in particular<strong> Article 41 </strong>(Right to work, to education and to public assistance in cases of unemployment, old age, sickness and disablement, and in other cases of undeserved want). <strong>AYUSH </strong>(Ayurveda, Yoga, and Naturopathy, Unani, Siddha, and Homeopathy): Formed on 9th November 2014.</p>",
                    solution_hi: "<p>5.(d) <strong>आयुष। राष्ट्रीय सामाजिक सहायता कार्यक्रम (NSAP): अनुच्छेद 42</strong> (काम की उचित और मानवीय स्थितियों को सुरक्षित करने और मातृत्व राहत के लिए प्रावधान) और विशेष रूप से <strong>अनुच्छेद 41</strong> (काम करने का अधिकार, शिक्षा का अधिकार और बेरोजगारी, बुढ़ापा, बीमारी और विकलांगता के मामलों में और अवांछित अभाव के अन्य मामलों में सार्वजनिक सहायता का अधिकार) में निदेशक सिद्धांतों की पूर्ति की दिशा में एक महत्वपूर्ण कदम का प्रतिनिधित्व करता है। <strong>आयुष </strong>(आयुर्वेद, योग और प्राकृतिक चिकित्सा, यूनानी, सिद्ध और होम्योपैथी): 9 नवंबर 2014 को गठित।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which flagship programme under the Ministry of Rural Development aims to organise the rural poor women into their own institutions like self-help groups and their federations, producers, collectives etc and also ensure their financial inclusion and livelihood support?</p>",
                    question_hi: "<p>6. ग्रामीण विकास मंत्रालय के तहत किस प्रमुख कार्यक्रम का उद्देश्य ग्रामीण गरीब महिलाओं को स्वयं सहायता समूहों और उनके संघों, उत्पादकों, सामूहिक आदि जैसे अपने स्वयं के संस्थानों में संगठित करना और उनके वित्तीय समावेशन और आजीविका सहायता को सुनिश्चित करना है?</p>",
                    options_en: ["<p>The National Social Assistance Programme(NSAP)</p>", "<p>Rashtriya Krishi Vikas Yojana</p>", 
                                "<p>Mahatma Gandhi National Rural Employment Guarantee Programme(MGNREGA)</p>", "<p>Deendayal Antyodaya Yojana-National Rural Livelihood Mission(DAY-NRLM)</p>"],
                    options_hi: ["<p>राष्ट्रीय सामाजिक सहायता कार्यक्रम (NSAP)</p>", "<p>राष्ट्रीय कृषि विकास योजना</p>",
                                "<p>महात्मा गांधी राष्ट्रीय ग्रामीण रोजगार गारंटी कार्यक्रम (MGNREGA)</p>", "<p>दीनदयाल अंत्योदय योजना-राष्ट्रीय ग्रामीण आजीविका मिशन (DAY-NRLM)</p>"],
                    solution_en: "<p>6.(d) <strong>DAY-NRLM :</strong> Launched as &lsquo;Aajeevika&rsquo; in 2011 by the Ministry of Rural Development and renamed as DAY-NRLM in 2015. <strong>MGNREGA</strong>: Launched - 2006 by the Ministry of Rural development under Mahatma Gandhi Employment Guarantee Act 2005, <strong>Objective</strong>:- To guarantee 100 days of employment and to adult members of any rural household.<strong> NSAP:</strong> Launched -15th August 1995. <strong>Rashtriya Krishi Vikas Yojana:</strong> Initiated in 2007 as an umbrella scheme for ensuring the holistic development of agriculture and allied sectors.</p>",
                    solution_hi: "<p>6.(d) <strong>DAY-NRLM :</strong> ग्रामीण विकास मंत्रालय द्वारा 2011 में \'आजीविका\' के रूप में लॉन्च किया गया और 2015 में इसका नाम बदलकर DAY-NRLM कर दिया गया। <strong>MGNREGA</strong>: महात्मा गांधी रोजगार गारंटी अधिनियम 2005 के तहत ग्रामीण विकास मंत्रालय द्वारा 2006 में लॉन्च किया गया, <strong>उद्देश्य:- </strong>किसी भी ग्रामीण परिवार के वयस्क सदस्यों को 100 दिन का गारंटीकृत रोजगार प्रदान करना । NSAP: 15 अगस्त 1995 को लॉन्च किया गया। <strong>राष्ट्रीय कृषि विकास योजना: </strong>कृषि और संबद्ध क्षेत्रों के समग्र विकास को सुनिश्चित करने के लिए एक व्यापक योजना के रूप में 2007 में शुरू की गई।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. The National TB Programme (NTP) was launched by the Government of India in year____in the form of a district TB Centre Model involved with BCG Vaccination and TB Treatment.</p>",
                    question_hi: "<p>7. राष्ट्रीय टीबी कार्यक्रम (NTP) भारत सरकार द्वारा वर्ष _____ में BCG टीकाकरण और TB उपचार से जुड़े एक जिला TB केंद्र मॉडल के रूप में शुरू किया गया था।</p>",
                    options_en: ["<p>1962</p>", "<p>1963</p>", 
                                "<p>1961</p>", "<p>1960</p>"],
                    options_hi: ["<p>1962</p>", "<p>1963</p>",
                                "<p>1961</p>", "<p>1960</p>"],
                    solution_en: "<p>7.(a) <strong>1962. National Tuberculosis Elimination Programme (NTEP) -</strong> Aims to strategically reduce TB (Tuberculosis) burden in India by 2025. Prime Minister Narendra Modi\'s declaration of having a \'TB Mukt Bharat\' by 2025. World TB Day 2023, with the theme \"Yes! We can end TB!\'. World TB Day - March 24.</p>",
                    solution_hi: "<p>7.(a) <strong>1962 । राष्ट्रीय क्षय रोग उन्मूलन कार्यक्रम (NTEP) -</strong> इसका लक्ष्य 2025 तक भारत में TB (तपेदिक) को रणनीतिक रूप से कम करना है। प्रधान मंत्री नरेंद्र मोदी द्वारा 2025 तक &lsquo;TB मुक्त भारत\' की घोषणा की गयी है । विश्व TB दिवस 2023 की थीम &lsquo;यस! वी कैन एंड टीबी! (Yes! We can end TB!)&rsquo; । विश्व टीबी दिवस - 24 मार्च।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. The Mid-Day Meal scheme comes under the purview of which ministry?</p>",
                    question_hi: "<p>8. मध्याह्न भोजन योजना किस मंत्रालय के अधीन है?</p>",
                    options_en: ["<p>Ministry of Women and Child Development</p>", "<p>Ministry of Education</p>", 
                                "<p>Ministry of Social Justice and Empowerment</p>", "<p>Ministry of Health and Family Welfare</p>"],
                    options_hi: ["<p>महिला एवं बाल विकास मंत्रालय</p>", "<p>शिक्षा मंत्रालय</p>",
                                "<p>सामाजिक न्याय एवं अधिकारिता मंत्रालय</p>", "<p>स्वास्थ्य और परिवार कल्याण मंत्रालय</p>"],
                    solution_en: "<p>8.(b).<strong> Ministry of Education.</strong> The Mid-Day Meal Scheme is a school meal program of the Government of India. It was launched in <strong>1995 </strong>throughout India by the Ministry of Education. It is now covered under the National Food Security Act, 2013. The central government provides 60% of the cost of the scheme, while the state governments provide the remaining 40%. In <strong>September 2021,</strong> the Mid-Day Meal Scheme was renamed<strong> &lsquo;PM POSHAN&rsquo;</strong> or Pradhan Mantri Poshan Shakti Nirman.</p>",
                    solution_hi: "<p>8.(b) <strong>शिक्षा मंत्रालय। </strong>मध्याह्न भोजन योजना (मिड-डे मील योजना) भारत सरकार का एक विद्यालयी भोजन कार्यक्रम है। इसकी शुरुआत 1995 में शिक्षा मंत्रालय द्वारा सम्पूर्ण भारत में की गई थी। अब यह राष्ट्रीय खाद्य सुरक्षा अधिनियम, 2013 के अंतर्गत शामिल है। केंद्र सरकार योजना की लागत का 60% हिस्सा प्रदान करती है, जबकि राज्य सरकारें शेष 40% प्रदान करती हैं।<strong> सितंबर 2021</strong> में, मध्याह्न भोजन योजना का नाम बदलकर<strong> \'पी.एम. पोषण\' </strong>या प्रधान मंत्री पोषण शक्ति निर्माण योजना कर दिया गया है ।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Mission Indradhanush is related to which of the following government schemes?</p>",
                    question_hi: "<p>9. मिशन इंद्रधनुष निम्नलिखित में से किस सरकारी योजना से संबंधित है?</p>",
                    options_en: ["<p>E-Banking</p>", "<p>E-Trading</p>", 
                                "<p>Child Vaccination</p>", "<p>Child Education</p>"],
                    options_hi: ["<p>ई- बैंकिंग</p>", "<p>ई- ट्रेडिंग</p>",
                                "<p>बाल टीकाकरण</p>", "<p>बाल शिक्षा</p>"],
                    solution_en: "<p>9.(c) <strong>Child Vaccination. Mission Indradhanush</strong> is a health mission of the Government of India. Launched by Union Health Minister J. P. Nadda on 25 December 2014. Aim: To cover all those children who are either unvaccinated or are partially vaccinated against vaccine-preventable diseases.</p>",
                    solution_hi: "<p>9.(c) <strong>बाल टीकाकरण। मिशन इंद्रधनुष </strong>भारत सरकार का एक स्वास्थ्य मिशन है। 25 दिसंबर 2014 को केंद्रीय स्वास्थ्य मंत्री जे.पी. नड्डा द्वारा लॉन्च किया गया। उद्देश्य: उन सभी बच्चों को कवर करना, जिनका या तो टीकाकरण नहीं हुआ है या जिन्हें टीके से बचाव योग्य बीमारियों के खिलाफ आंशिक रूप से टीका लगाया गया है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Which of the following is NOT a part of the growth area under the Digital India programme?</p>",
                    question_hi: "<p>10. निम्नलिखित में से कौन डिजिटल इंडिया कार्यक्रम के तहत विकास क्षेत्र का हिस्सा नहीं है?</p>",
                    options_en: ["<p>Broadband highways</p>", "<p>E-governance for reforming India</p>", 
                                "<p>Universal access to mobile services</p>", "<p>Public school education for all</p>"],
                    options_hi: ["<p>ब्रॉडबैंड हाईवे</p>", "<p>भारत में सुधार के लिए ई-गवर्नेंस</p>",
                                "<p>मोबाइल सेवाओं तक सार्वभौमिक पहुंच</p>", "<p>सभी के लिए पब्लिक स्कूल शिक्षा</p>"],
                    solution_en: "<p>10.(d) <strong>Public school education for all. </strong>Digital India aims to provide the much-needed thrust to the nine pillars of growth areas, namely Broadband Highways, Universal Access to Mobile Connectivity, Public Internet Access Programme, e - Governance (Reforming Government through Technology), e - Kranti (Electronic Delivery of Services), Information for All, Electronics Manufacturing, IT for Jobs and Early Harvest Programmes.<strong> Launch year:</strong> 2015.</p>",
                    solution_hi: "<p>10.(d) <strong>सभी के लिए पब्लिक स्कूल शिक्षा।</strong> डिजिटल इंडिया का लक्ष्य विकास क्षेत्रों के नौ स्तंभों, अर्थात् ब्रॉडबैंड राजमार्ग, मोबाइल कनेक्टिविटी तक सार्वभौमिक पहुंच, पब्लिक इंटरनेट एक्सेस प्रोग्राम, ई - गवर्नेंस (प्रौद्योगिकी के माध्यम से सरकार में सुधार), ई - क्रांति (सेवाओं की इलेक्ट्रॉनिक डिलीवरी), सभी के लिए सूचना, इलेक्ट्रॉनिक्स विनिर्माण, नौकरियों के लिए IT और अर्ली हार्वेस्ट कार्यक्रम को आवश्यक बल प्रदान करना है। <strong>लॉन्च वर्ष:</strong> 2015</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following statements is NOT the purpose behind the Swadhar Greh Scheme?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन सा कथन स्वाधार गृह योजना के पीछे का उद्देश्य नहीं है?</p>",
                    options_en: ["<p>Provide support to enable women to start afresh</p>", "<p>Provide legal aid to Women</p>", 
                                "<p>Shelter, food, and clothing for women in distress</p>", "<p>Provide Government jobs to socially backward women</p>"],
                    options_hi: ["<p>महिलाओं को नए सिरे से शुरुआत करने में सक्षम बनाने के लिए सहायता प्रदान करना</p>", "<p>महिलाओं को कानूनी सहायता प्रदान करना</p>",
                                "<p>संकट में पड़ी महिलाओं के लिए आश्रय, भोजन और वस्त्र</p>", "<p>सामाजिक रूप से पिछड़ी महिलाओं को सरकारी नौकरियाँ प्रदान करना</p>"],
                    solution_en: "<p>11.(d) <strong>Provide Government jobs to socially backward women.</strong> The <strong>Swadhar Greh scheme</strong> (launched: 2001 - 2002) is targeted to provide institutional support to women victims of difficult circumstances and rehabilitate them so that they could lead their life with dignity. The scheme envisages providing shelter, food, clothing and health as well as economic and social security for these women.</p>",
                    solution_hi: "<p>11.(d) <strong>सामाजिक रूप से पिछड़ी महिलाओं को सरकारी नौकरियाँ प्रदान करना ।</strong> <strong>स्वाधार गृह योजना </strong>(लॉन्च: 2001 - 2002) का लक्ष्य कठिन परिस्थितियों की पीड़ित महिलाओं को संस्थागत सहायता प्रदान करना और उनका पुनर्वास करना है ताकि वे सम्मान के साथ अपना जीवन जी सकें। इस योजना में इन महिलाओं के लिए आश्रय, भोजन, कपड़े और स्वास्थ्य के साथ-साथ आर्थिक और सामाजिक सुरक्षा प्रदान करने की परिकल्पना की गई है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. What was the previous name of the Department of AYUSH?</p>",
                    question_hi: "<p>12. AYUSH विभाग का पुराना नाम क्या था ?</p>",
                    options_en: ["<p>Department of Health Science</p>", "<p>Department of Pharmaceuticals</p>", 
                                "<p>Department of Health and Family Welfare</p>", "<p>Department of Indian System of Medicine and Homeopathy</p>"],
                    options_hi: ["<p>स्वास्थ्य विज्ञान विभाग</p>", "<p>फार्मास्यूटिकल्स विभाग</p>",
                                "<p>स्वास्थ्य और परिवार कल्याण विभाग</p>", "<p>भारतीय चिकित्सा प्रणाली और होम्योपैथी विभाग</p>"],
                    solution_en: "<p>12.(d) <strong>Department of Indian System of Medicine and Homeopathy. AYUSH </strong>(Ayurveda, Yoga &amp; Naturopathy, Unani, Siddha, and Homeopathy) - it is responsible for developing education, research and propagation of traditional medicine systems in India. <strong>Formed </strong>- 9 November 2014, <strong>Nodal Ministry -</strong> Ministry of Health and Family Welfare.</p>",
                    solution_hi: "<p>12.(d) <strong>भारतीय चिकित्सा प्रणाली एवं होम्योपैथी विभाग। AYUSH</strong> (आयुर्वेद, योग और प्राकृतिक चिकित्सा, यूनानी, सिद्ध और होम्योपैथी) - यह भारत में पारंपरिक चिकित्सा प्रणालियों की शिक्षा, अनुसंधान और प्रसार के विकास के लिए उत्तरदायी है। <strong>गठन </strong>- 9 नवंबर 2014, <strong>नोडल मंत्रालय - </strong>स्वास्थ्य और परिवार कल्याण मंत्रालय।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Name the scheme which provides risk coverage of Rs 2 lakhs for accidental death or full disability and Rs 1 lakh for partial disability for people in the age group 18 to 70 years.</p>",
                    question_hi: "<p>13. उस योजना का नाम बताइए जो 18 से 70 वर्ष की आयु के लोगों के लिए आकस्मिक मृत्यु या पूर्ण विकलांगता के लिए 2 लाख रुपये और आंशिक विकलांगता के लिए 1 लाख रुपये का जोखिम कवरेज प्रदान करती है।</p>",
                    options_en: ["<p>Pradhan Mantri Mudra Yojana</p>", "<p>Pradhan Mantri Suraksha Bima Yojana</p>", 
                                "<p>Pradhan Mantri Jeevan Jyoti Beema Yojana</p>", "<p>Atal Pension Yojana</p>"],
                    options_hi: ["<p>प्रधानमंत्री मुद्रा योजना</p>", "<p>प्रधानमंत्री सुरक्षा बीमा योजना</p>",
                                "<p>प्रधानमंत्री जीवन ज्योति बीमा योजना</p>", "<p>अटल पेंशन योजना</p>"],
                    solution_en: "<p>13.(b) <strong>Pradhan Mantri Suraksha Bima Yojana -</strong> it is a government-backed accident insurance scheme in India. <strong>launched by -</strong> the Prime Minister Narendra Modi (9 May 2015) in Kolkata, <strong>Ministry -</strong> Finance. <strong>Pradhan Mantri Mudra Yojana -</strong> a scheme under Mudra Bank which was launched by Prime Minister Narendra Modi on 8 April 2015 (New Delhi). <strong>Pradhan Mantri Jeevan Jyoti Beema Yojana - </strong>a Government-backed Life insurance scheme in India ( 9 May, 2015 from Kolkata). <strong>Atal Pension Yojana (Swavalamban Yojana) -</strong> It was launched by Prime Minister Narendra Modi on 9 May 2015 in Kolkata.</p>",
                    solution_hi: "<p>13.(b) <strong>प्रधानमंत्री सुरक्षा बीमा योजना -</strong> यह भारत सरकार द्वारा समर्थित दुर्घटना बीमा योजना है। प्रधानमंत्री नरेंद्र मोदी (9 मई 2015) द्वारा कोलकाता में <strong>लॉन्च </strong>किया गया, <strong>मंत्रालय </strong>- वित्त। <strong>प्रधान मंत्री मुद्रा योजना -</strong> मुद्रा बैंक के तहत एक योजना है जिसे प्रधान मंत्री नरेंद्र मोदी द्वारा 8 अप्रैल 2015 (नई दिल्ली) को लॉन्च किया गया था। <strong>प्रधान मंत्री जीवन ज्योति बीमा योजना -</strong> भारत सरकार द्वारा समर्थित जीवन बीमा योजना (9 मई, 2015 कोलकाता से)। <strong>अटल पेंशन योजना (स्वावलंबन योजना) -</strong> इसे 9 मई 2015 को कोलकाता में प्रधान मंत्री नरेंद्र मोदी द्वारा लॉन्च किया गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Under which ministry was the National Mission for Manuscript established in February 2003?</p>",
                    question_hi: "<p>14. फरवरी 2003 में राष्ट्रीय पाण्डुलिपि मिशन की स्थापना किस मंत्रालय के अधीन की गई थी?</p>",
                    options_en: ["<p>Ministry of Culture</p>", "<p>Ministry of Finance</p>", 
                                "<p>Ministry of Human Resource Development</p>", "<p>Ministry of Tourism</p>"],
                    options_hi: ["<p>संस्कृति मंत्रालय</p>", "<p>वित्त मंत्रालय</p>",
                                "<p>मानव संसाधन विकास मंत्रालय</p>", "<p>पर्यटन मंत्रालय</p>"],
                    solution_en: "<p>14.(a) <strong>Ministry of Culture. National Mission for Manuscripts (NAMAMI):</strong> It is an autonomous organization established to survey, locate and conserve Indian manuscripts, with an aim to create a national resource base for manuscripts, for enhancing their access, awareness and use for educational purposes. <strong>Headquarter</strong>: New Delhi</p>",
                    solution_hi: "<p>14.(a) <strong>संस्कृति मंत्रालय। राष्ट्रीय पांडुलिपि मिशन (NAMAMI): </strong>यह भारतीय पांडुलिपियों के सर्वेक्षण, पता लगाने और संरक्षण के लिए स्थापित एक स्वायत्त (स्वराज्य के अधीन) संगठन है, जिसका उद्देश्य शैक्षिक उद्देश्यों के लिए उनकी पहुंच, जागरूकता और उपयोग को बढ़ाने के लिए पांडुलिपियों के लिए एक राष्ट्रीय संसाधन आधार बनाना है। <strong>मुख्यालय</strong>: नई दिल्ली</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Name the scheme initiated to protect elderly persons aged 60 years and above against a future fall in their interest income due to uncertain market conditions.</p>",
                    question_hi: "<p>15. अनिश्चित बाजार स्थितियों के कारण उनकी ब्याज आय में भविष्य में गिरावट के खिलाफ 60 वर्ष और उससे अधिक आयु के बुजुर्ग व्यक्तियों की सुरक्षा के लिए शुरू की गई योजना का नाम बताइए।</p>",
                    options_en: ["<p>Varishtha Pension Bima Yojana 2003</p>", "<p>Varishtha Pension Bima Yojana 2014</p>", 
                                "<p>Pradhan Mantri Vaya Vandana Yojana</p>", "<p>Atal Pension Yojana (APY)</p>"],
                    options_hi: ["<p>वरिष्ठ पेंशन बीमा योजना 2003</p>", "<p>वरिष्ठ पेंशन बीमा योजना 2014</p>",
                                "<p>प्रधानमंत्री वय वंदना योजना</p>", "<p>अटल पेंशन योजना</p>"],
                    solution_en: "<p>15.(c) <strong>Pradhan Mantri Vaya Vandana Yojana</strong> (May 2017) is an insurance policy-cum-pension scheme that provides alternative avenues of income to senior citizens of the country. The scheme offers a guaranteed interest rate of 8% per annum for 10 years. <strong>Atal Pension Yojana</strong> is a government-backed pension scheme, primarily targeted at the unorganized sector. Launched by Prime Minister Narendra Modi on 9 May 2015 in Kolkata.</p>",
                    solution_hi: "<p>15.(c) <strong>प्रधानमंत्री वय वंदना योजना </strong>(मई 2017) एक बीमा पॉलिसी-सह-पेंशन योजना है जो देश के वरिष्ठ नागरिकों को आय के वैकल्पिक मार्ग प्रदान करती है। यह योजना 10 वर्षों के लिए 8% प्रति वर्ष की गारंटीकृत ब्याज दर प्रदान करती है। <strong>अटल पेंशन योजना</strong> सरकार द्वारा समर्थित पेंशन योजना है, जिसका लक्ष्य मुख्य रूप से असंगठित क्षेत्र है। यह प्रधान मंत्री नरेंद्र मोदी द्वारा 9 मई 2015 को कोलकाता में प्रारंभ किया गया।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>