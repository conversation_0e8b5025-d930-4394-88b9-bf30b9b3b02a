<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. If a<sup>3</sup> + 3a<sup>2</sup> + 3a = 7, then the value of a<sup>2</sup> + 2a is:</p>",
                    question_hi: "<p>1. यदि a<sup>3</sup> + 3a<sup>2</sup> + 3a = 7, है, तो a<sup>2</sup> + 2a का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>3</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>1</p>",
                                "<p>2</p>", "<p>4</p>"],
                    solution_en: "<p>1.(a) <strong>Given</strong>, aa<sup>3</sup> + 3a<sup>2</sup> + 3a = 7<br>By hit and trial , put a = 1 , which satisfy the condition <br>&there4; a<sup>2</sup> + 2a = <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></math>+ 2 (1) = 3</p>",
                    solution_hi: "<p>1.(a) <strong>दिया गया है,</strong> a<sup>3</sup> + 3a<sup>2</sup> + 3a = 7<br>हिट एंड ट्रायल द्वारा, a = 1 रखने पर ,शर्त पूरी होती है,<br>&there4; a<sup>2</sup> + 2a = <math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msup></math>+ 2 (1) = 3</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. If x is an integer such that x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math>, then find the value of x - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math>.</p>",
                    question_hi: "<p>2. यदि x इस प्रकार एक पूर्णांक है कि x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math> है, तो x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>63</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>36</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>32</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>2.(b) <math display=\"inline\"><mi>x</mi></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mfrac><mrow><mn>65</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>4225</mn></mrow><mrow><mn>64</mn></mrow></mfrac><mo>-</mo><mn>4</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>4225</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>256</mn></mrow><mrow><mn>64</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>3969</mn><mn>64</mn></mfrac></msqrt><mo>=</mo><mfrac><mn>63</mn><mn>8</mn></mfrac></math></p>",
                    solution_hi: "<p>2.(b) <math display=\"inline\"><mi>x</mi></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>65</mn><mn>8</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mrow><mo>(</mo><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><mi>x</mi></mfrac><mo>)</mo></mrow><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mfrac><mrow><mn>65</mn></mrow><mrow><mn>8</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>4</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>4225</mn></mrow><mrow><mn>64</mn></mrow></mfrac><mo>-</mo><mn>4</mn></msqrt></math> <br>= <math display=\"inline\"><msqrt><mfrac><mrow><mn>4225</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>256</mn></mrow><mrow><mn>64</mn></mrow></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>3969</mn><mn>64</mn></mfrac></msqrt><mo>=</mo><mfrac><mn>63</mn><mn>8</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "3. The sum of five consecutive numbers is 80. Find the largest number.",
                    question_hi: "3. पाँच क्रमागत संख्याओं का योग 80 है। सबसे बड़ी संख्या ज्ञात कीजिए।",
                    options_en: [" 18", " 15", 
                                " 19", " 14"],
                    options_hi: [" 18", " 15",
                                " 19", " 14"],
                    solution_en: "3.(a) Let the five consecutive numbers are x - 2 , x -1 , x , x + 1 , x + 2<br />According to question<br />x - 2 +  x -1 + x +  x + 1 + x + 2   = 80<br /> <math display=\"inline\"><mo>⇒</mo></math>   5x = 80     ⇒ x = 16<br />Largest number = x + 2 = 16 + 2 = 18      ",
                    solution_hi: "3.(a) मान लीजिए कि पाँच क्रमागत संख्याएँ  x - 2 , x -1 , x , x + 1 , x + 2 हैं<br />प्रश्न के अनुसार , <br /> x - 2 +  x -1 + x +  x + 1 + x + 2   = 80<br /><math display=\"inline\"><mo>⇒</mo></math>   5x = 80     ⇒ x = 16<br />सबसे बड़ी संख्या  = x + 2 = 16 + 2 = 18  ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If x = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math>, then find the value of (x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math>).</p>",
                    question_hi: "<p>4. यदि x = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> है, तो (x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math>) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>188</p>", "<p>194</p>", 
                                "<p>186</p>", "<p>196</p>"],
                    options_hi: ["<p>188</p>", "<p>194</p>",
                                "<p>186</p>", "<p>196</p>"],
                    solution_en: "<p>4.(b) <strong>Given </strong>x = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 7 + 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 14<br>x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math> = 14<sup>2</sup> - 2 = 196 - 2 = 194</p>",
                    solution_hi: "<p>4.(b) <strong id=\"docs-internal-guid-ecb3153a-7fff-315e-f9ef-d98396e595de\">दिया गया है</strong> x = 7 - 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 7 + 4<math display=\"inline\"><msqrt><mn>3</mn></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 14<br>x<sup>2</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math> = 14<sup>2</sup> - 2 = 196 - 2 = 194</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "5.  If the system of the following equations has the value of the variables as three consecutive integers, then the value of a is ____.<br />x - y + z = 2a<br />x + 4y - 2z = 3(4 - a)<br />2x - 3y + 4z = 6 - 2a",
                    question_hi: "5. यदि निम्नलिखित समीकरणों की प्रणाली में चरों का मान लगातार तीन पूर्णांकों के रूप में है, तो a का मान ____ है।<br />x - y + z = 2a<br />x + 4y - 2z = 3(4 - a)<br />2x - 3y + 4z = 6 - 2a",
                    options_en: [" 2", " 4", 
                                " 1", "  3"],
                    options_hi: [" 2", " 4",
                                " 1", "  3"],
                    solution_en: "5.(c) Let the three consecutive numbers <math display=\"inline\"><mi>x</mi></math>, y, and z be 3 , 2 and 1 respectively,<br />x - y + z = 2a ….  (i)<br />Put the value x, y and z, we get<br /><math display=\"inline\"><mo>⇒</mo></math> 3 - 2 + 1 = 2a<br /><math display=\"inline\"><mo>⇒</mo></math> a = 1<br />x + 4y - 2z = 3(4 - a) ….  (ii)<br /><math display=\"inline\"><mo>⇒</mo></math> 3 + 8 - 2 = 3(4 - a)<br /><math display=\"inline\"><mo>⇒</mo></math> 9 = 3(4 - a)<br /><math display=\"inline\"><mo>⇒</mo></math> a = 1<br />2x - 3y + 4z = 6 - 2a ….  (iii)<br /><math display=\"inline\"><mo>⇒</mo></math> 6 - 6 + 4 = 6 - 2a<br /><math display=\"inline\"><mo>⇒</mo></math> 4 = 6 - 2a<br /><math display=\"inline\"><mo>⇒</mo></math> a = 1<br />It is clear form  in all above equations satisfies the value of x, y and z,  so the value of a = 1 ",
                    solution_hi: "5.(c) माना ,  तीन क्रमागत संख्याएँ x, y, और z क्रमशः 3, 2 और 1 हैं<br />x - y + z = 2a ….  (i)<br />x, y और z का मान रखने पर  <br /><math display=\"inline\"><mo>⇒</mo></math> 3 - 2 + 1 = 2a<br /><math display=\"inline\"><mo>⇒</mo></math> a = 1<br />x + 4y - 2z = 3(4 - a) ….  (ii)<br /><math display=\"inline\"><mo>⇒</mo></math> 3 + 8 - 2 = 3(4 - a)<br /><math display=\"inline\"><mo>⇒</mo></math> 9 = 3(4 - a)<br /><math display=\"inline\"><mo>⇒</mo></math> a = 1<br />2x - 3y + 4z = 6 - 2a ….  (iii)<br /><math display=\"inline\"><mo>⇒</mo></math> 6 - 6 + 4 = 6 - 2a<br /><math display=\"inline\"><mo>⇒</mo></math> 4 = 6 - 2a<br /><math display=\"inline\"><mo>⇒</mo></math> a = 1<br />यह स्पष्ट है कि उपरोक्त सभी समीकरणों में x, y और z का मान संतुष्ट होता है, अतः  a का मान = 1 ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 2, then find the value of (x<sup>2723</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3356</mn></msup></mfrac></math>).</p>",
                    question_hi: "<p>6. यदि x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 2 है, तो (x<sup>2723</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3356</mn></msup></mfrac></math>) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>1</p>", "<p>2</p>", 
                                "<p>0</p>", "<p>-1</p>"],
                    options_hi: ["<p>1</p>", "<p>2</p>",
                                "<p>0</p>", "<p>-1</p>"],
                    solution_en: "<p>6.(b) <strong>Given: </strong><math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 2<br>Put the value of <math display=\"inline\"><mi>x</mi></math> = 1 satisfies the equation,<br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1</mn></mfrac></math> = 2 <br><math display=\"inline\"><mo>&#8658;</mo></math> LHS = RHS<br>Now,<br>(x<sup>2723</sup> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3356</mn></mrow></msup></mrow></mfrac></math>) = (1)<sup>2723</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mn>1</mn><mn>3356</mn></msup></mfrac></math> = 1 + 1 = 2</p>",
                    solution_hi: "<p>6.(b) <strong>दिया गया है : </strong><math display=\"inline\"><mi>x</mi></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 2<br><math display=\"inline\"><mi>x</mi></math> = 1 का मान समीकरण को संतुष्ट करता है,<br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1</mn></mfrac></math> = 2<br><math display=\"inline\"><mo>&#8658;</mo></math> LHS = RHS<br>अब,<br>(x<sup>2723</sup> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3356</mn></mrow></msup></mrow></mfrac></math>) = (1)<sup>2723</sup> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mn>1</mn><mn>3356</mn></msup></mfrac></math> = 1 + 1 = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. If 3a + 2b = 27 and 27<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math> + 8b<sup>3</sup> = 1458, then find 2ab.</p>",
                    question_hi: "<p>7. यदि 3a + 2b = 27 और 27<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math> + 8b<sup>3</sup> = 1458 है, तो 2ab का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>72</p>", "<p>70</p>", 
                                "<p>77</p>", "<p>75</p>"],
                    options_hi: ["<p>72</p>", "<p>70</p>",
                                "<p>77</p>", "<p>75</p>"],
                    solution_en: "<p>7.(d) <br>3a + 2b = 27 <br>On cubing both side we get<br>(3<math display=\"inline\"><mi>a</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> + (2b)<sup>3</sup> + 3ab(a + b) = 27<sup>3</sup><br>27<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math> + 8b<sup>3</sup> + 3 &times; 3a &times; 2b(3a + 2b) = 27<sup>3</sup><br>1458 + 18<math display=\"inline\"><mi>a</mi><mi>b</mi></math> &times; 27 = 19683<br>18<math display=\"inline\"><mi>a</mi><mi>b</mi></math> &times; 27 = 19683 - 1458 <br>2<math display=\"inline\"><mi>a</mi><mi>b</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18225</mn><mrow><mn>9</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math> = 75</p>",
                    solution_hi: "<p>7.(d) <br>3a + 2b = 27 <br>दोनों ओर घन करने पर हमें प्राप्त होता है<br>(3<math display=\"inline\"><mi>a</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> + (2b)<sup>3</sup> + 3ab(a + b) = 27<sup>3</sup><br>27<math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></math> + 8b<sup>3</sup> + 3 &times; 3a &times; 2b(3a + 2b) = 27<sup>3</sup><br>1458 + 18<math display=\"inline\"><mi>a</mi><mi>b</mi></math> &times; 27 = 19683<br>18<math display=\"inline\"><mi>a</mi><mi>b</mi></math> &times; 27 = 19683 - 1458 <br>2<math display=\"inline\"><mi>a</mi><mi>b</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18225</mn><mrow><mn>9</mn><mo>&#215;</mo><mn>27</mn></mrow></mfrac></math> = 75</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. If (a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> then what is the value of (a<sup>6</sup> + a<sup>-6</sup>) ?</p>",
                    question_hi: "<p>8. यदि (a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> है, तो (a<sup>6</sup> + a<sup>-6</sup>) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>3048190</p>", "<p>3048542</p>", 
                                "<p>3048132</p>", "<p>3048625</p>"],
                    options_hi: ["<p>3048190</p>", "<p>3048542</p>",
                                "<p>3048132</p>", "<p>3048625</p>"],
                    solution_en: "<p>8.(a) (a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>a<sup>2 </sup>&nbsp;+ <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> = (7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>)<sup>2 </sup>- 2 = 49 &times; 3 - 2 = 145<br>(a<sup>6</sup> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>6</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math>) = 145<sup>3 </sup>- 3 &times; 145 = 3048625 - 435 = 3048190</p>",
                    solution_hi: "<p>8.(a) (a + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>a<sup>2 </sup>&nbsp;+ <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math> = (7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>)<sup>2 </sup>- 2 = 49 &times; 3 - 2 = 145<br>(a<sup>6</sup> + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>6</mn></mrow></msup><mi>&#160;</mi></mrow></mfrac></math>) = 145<sup>3 </sup>- 3 &times; 145 = 3048625 - 435 = 3048190</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. If (x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = 7, x &gt; 0. The positive value of (x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) is :</p>",
                    question_hi: "<p>9. यदि (x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = 7, x &gt; 0 है। (x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) का धनात्मक मान कितना है ?</p>",
                    options_en: ["<p>5<math display=\"inline\"><msqrt><mn>4</mn></msqrt></math></p>", "<p>5<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", 
                                "<p>3<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", "<p>4<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    options_hi: ["<p>5<math display=\"inline\"><msqrt><mn>4</mn></msqrt></math></p>", "<p>5<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>",
                                "<p>3<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>", "<p>4<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math></p>"],
                    solution_en: "<p>9.(c) (x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = 7<br>(x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msqrt><mn>49</mn><mo>-</mo><mn>4</mn></msqrt><mo>&#160;</mo><mo>=</mo><msqrt><mn>45</mn></msqrt><mo>=</mo><mn>3</mn><msqrt><mn>5</mn></msqrt></math></p>",
                    solution_hi: "<p>9.(c) (x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = 7<br>(x - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>7</mn><mn>2</mn></msup><mo>-</mo><mn>4</mn></msqrt><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><msqrt><mn>49</mn><mo>-</mo><mn>4</mn></msqrt><mo>&#160;</mo><mo>=</mo><msqrt><mn>45</mn></msqrt><mo>=</mo><mn>3</mn><msqrt><mn>5</mn></msqrt></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. For the equations ax + (a<sup>2</sup> + 1)y = 4 and 4x + ay = a<sup>2</sup> , which of the following statements is TRUE ?</p>",
                    question_hi: "<p>10. समीकरण ax + (a<sup>2</sup> + 1)y = 4 and 4x + ay = a<sup>2</sup> ,के लिए, निम्नलिखित में से कौन-सा कथन सत्य है ?</p>",
                    options_en: ["<p>If a = 6, then x = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>28</mn></mrow></mfrac></math> , y = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>28</mn></mfrac></math> are the solutions.</p>", "<p>If a = 6, then x = 48, y = 4 are the solutions.</p>", 
                                "<p>If a = -12, then x = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>28</mn></mrow></mfrac></math>, y = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>28</mn></mfrac></math> are the solutions.</p>", "<p>If a = -12, then x = 48, y = 4 are the solutions</p>"],
                    options_hi: ["<p>यदि a = 6 है , तो x = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>28</mn></mrow></mfrac></math> , y = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>28</mn></mfrac></math> हल हैं।</p>", "<p>यदि a = 6 है , तो x = 48, y = 4 हल हैं।</p>",
                                "<p>यदि a = -12 है , तो x = <math display=\"inline\"><mfrac><mrow><mn>325</mn></mrow><mrow><mn>28</mn></mrow></mfrac></math>, y = - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>28</mn></mfrac></math> हल हैं।</p>", "<p>यदि a = -12 है , तो x = 48, y = 4 हल हैं।</p>"],
                    solution_en: "<p>10.(d) given, ax + (a<sup>2</sup> + 1)y = 4 and 4x + ay = a<sup>2</sup><br>After checking all options one by one, only option (d) satisfies,<br>a = -12, then x = 48, y = 4<br><math display=\"inline\"><mo>&#8658;</mo></math> ax + (a<sup>2</sup> + 1)y = 4<br>LHS = ax + (a<sup>2</sup> + 1)y<br>= -12 &times; 48 + [(-12)<sup>2</sup> + 1]4<br>= - 576 + 145 &times; 4 <br>= - 576 + 580 = 4 = RHS<br><math display=\"inline\"><mo>&#8658;</mo></math> 4x + ay = a<sup>2</sup><br>LHS = 4x + ay<br>= 4 &times; 48 - 12 &times; 4 = 192 - 48 = 144<br>RHS = a<sup>2</sup> = (-12)<sup>2 </sup>= 144<br>LHS = RHS</p>",
                    solution_hi: "<p>10.(d) दिया है, ax + (a<sup>2</sup> + 1)y = 4 and 4x + ay = a<sup>2</sup><br>सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (d) संतुष्ट करता है,<br>a = -12, तो x = 48, y = 4<br><math display=\"inline\"><mo>&#8658;</mo></math> ax + (a<sup>2</sup> + 1)y = 4<br>LHS = ax + (a<sup>2</sup> + 1)y<br>= -12 &times; 48 + [(-12)<sup>2</sup> + 1]4<br>= - 576 + 145 &times; 4 <br>= - 576 + 580 = 4 = RHS<br><math display=\"inline\"><mo>&#8658;</mo></math> 4x + ay = a<sup>2</sup><br>LHS = 4x + ay<br>= 4 &times; 48 - 12 &times; 4 = 192 - 48 = 144<br>RHS = a<sup>2</sup> = (-12)<sup>2</sup> = 144<br>LHS = RHS</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. If a + b + c = 0, then the value of (a<sup>2</sup> + b<sup>2</sup> + 2ab) is equal to:</p>",
                    question_hi: "<p>11. यदि a + b + c = 0 है, तो (a<sup>2 </sup>+ b<sup>2</sup> + 2ab) का मान क्या है ?</p>",
                    options_en: ["<p>c<sup>2</sup></p>", "<p>-c<sup>2</sup></p>", 
                                "<p>c</p>", "<p>-c</p>"],
                    options_hi: ["<p>c<sup>2</sup></p>", "<p>-c<sup>2</sup></p>",
                                "<p>c</p>", "<p>-c</p>"],
                    solution_en: "<p>11.(a)<strong> Given:</strong> a + b + c = 0<br><math display=\"inline\"><mi>a</mi></math> + b = -c<br>On squaring both side we get,<br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = (-c)<sup>2</sup><br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = c<sup>2</sup></p>",
                    solution_hi: "<p>11.(a) <strong>दिया गया है :</strong> a + b + c = 0<br><math display=\"inline\"><mi>a</mi></math> + b = -c<br>दोनों पक्षों का वर्ग करने पर हमें प्राप्त होता है,<br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = (-c)<sup>2</sup><br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + b<sup>2</sup> + 2ab = c<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Let 3t &ndash; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>t</mi></mrow></mfrac></math> = 3, then which of the following expressions has the value equal to 12 ?</p>",
                    question_hi: "<p>12. मान लीजिए 3t &ndash; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>t</mi></mrow></mfrac></math> = 3 है, तो निम्नलिखित में से किस व्यंजक का मान 12 के बराबर है?</p>",
                    options_en: ["<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> + 2</p>", "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> - 2</p>", 
                                "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1</p>", "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math>&nbsp;</p>"],
                    options_hi: ["<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> + 2</p>", "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> - 2</p>",
                                "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1</p>", "<p>9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math>&nbsp;</p>"],
                    solution_en: "<p>12.(c) 3t &ndash; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>t</mi></mrow></mfrac></math> = 3 <br>Taking square roots both side,<br><math display=\"inline\"><mo>&#8658;</mo></math> 9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math>&nbsp; - 2 = 9<br><math display=\"inline\"><mo>&#8658;</mo></math> 9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> = 11<br>adding 1 to both sides<br><math display=\"inline\"><mo>&#8658;</mo></math> 9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1 = 11 + 1<br><math display=\"inline\"><mo>&#8658;</mo></math> 9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1 = 12</p>",
                    solution_hi: "<p>12.(c) 3t &ndash; <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn><mi>t</mi></mrow></mfrac></math> = 3 <br>दोनों तरफ वर्गमूल करने पर,<br><math display=\"inline\"><mo>&#8658;</mo></math> 9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math>&nbsp; - 2 = 9<br><math display=\"inline\"><mo>&#8658;</mo></math> 9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> = 11<br>दोनों तरफ 1 जोड़ने पर<br><math display=\"inline\"><mo>&#8658;</mo></math> 9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1 = 11 + 1<br><math display=\"inline\"><mo>&#8658;</mo></math> 9<math display=\"inline\"><msup><mrow><mi>t</mi></mrow><mrow><mn>2</mn></mrow></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>1</mn><mo>&#160;</mo></mrow><mrow><mn>9</mn><msup><mi>t</mi><mn>2</mn></msup></mrow></mfrac></math> + 1 = 12</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If 3x + 2y = 10 and 2xy = 7, then find the value of 3x - 2y (if 3x &ndash; 2y &gt; 0).</p>",
                    question_hi: "<p>13. यदि 3x + 2y = 10 और 2xy = 7 है, तो 3x - 2y का मान ज्ञात कीजिए (यदि 3x - 2y &gt; 0)।</p>",
                    options_en: ["<p>4</p>", "<p>10</p>", 
                                "<p>8</p>", "<p>6</p>"],
                    options_hi: ["<p>4</p>", "<p>10</p>",
                                "<p>8</p>", "<p>6</p>"],
                    solution_en: "<p>13.(a)<strong> Given:</strong> 3x + 2y = 10 and 2xy = 7<br>We know that,<br>(a - b) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>4</mn><mi>&#160;</mi><mo>&#215;</mo><mi>a</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>b</mi></msqrt></math><br>(3x - 2y) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>3</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mn>2</mn><mi>y</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>4</mn><mi>&#160;</mi><mo>&#215;</mo><mn>3</mn><mi>x</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mi>y</mi></msqrt></math><br>So,<br>(3x - 2y) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>12</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>100</mn><mo>-</mo><mn>84</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> = 4</p>",
                    solution_hi: "<p>13.(a) <strong>दिया गया है : </strong>3x + 2y = 10 and 2xy = 7<br>हम जानते हैं की ,<br>(a - b) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mi>a</mi><mi>&#160;</mi><mo>+</mo><mi>b</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>4</mn><mi>&#160;</mi><mo>&#215;</mo><mi>a</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>b</mi></msqrt></math><br>(3x - 2y) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>3</mn><mi>x</mi><mi>&#160;</mi><mo>+</mo><mn>2</mn><mi>y</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>4</mn><mi>&#160;</mi><mo>&#215;</mo><mn>3</mn><mi>x</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mi>y</mi></msqrt></math><br>इसलिए , <br>(3x - 2y) = <math display=\"inline\"><msqrt><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mn>12</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7</mn></msqrt></math><br>= <math display=\"inline\"><msqrt><mn>100</mn><mo>-</mo><mn>84</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> = 4</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. What conclusion can be drawn about the solution of the following system of linear equations in two variables: <br>3<math display=\"inline\"><mi>x</mi></math>+ 2y = 7 <br>2<math display=\"inline\"><mi>x</mi></math> + 3y = 7</p>",
                    question_hi: "<p>14. दो चरों में रैखिक समीकरणों के निम्नलिखित निकाय के हल के बारे में क्या निष्कर्ष निकाला जा सकता है ?&nbsp;<br>3<math display=\"inline\"><mi>x</mi></math> + 2y = 7 <br>2<math display=\"inline\"><mi>x</mi></math> + 3y = 7</p>",
                    options_en: ["<p>No solution</p>", "<p>Unique solution</p>", 
                                "<p>Infinite solutions</p>", "<p>More than two solution</p>"],
                    options_hi: ["<p>कोई हल नहीं</p>", "<p>अद्वितीय हल</p>",
                                "<p>अनंत हल</p>", "<p>दो से अधिक हल</p>"],
                    solution_en: "<p>14.(b) <strong>Given ,</strong> <br>3<math display=\"inline\"><mi>x</mi></math> + 2y = 7 &hellip;. (i)<br>2<math display=\"inline\"><mi>x</mi></math> + 3y = 7 &hellip;..(ii)<br>If <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math>, then these equations have a unique Solution <br>Here , <math display=\"inline\"><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = 3 , a<sub>2</sub> = 2 , b<sub>1</sub> = 2 , b<sub>2</sub> = 3<br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>&there4; it has unique solution</p>",
                    solution_hi: "<p>14.(b) <strong>दिया गया है ,</strong> <br>3<math display=\"inline\"><mi>x</mi></math> + 2y = 7 &hellip;. (i)<br>2<math display=\"inline\"><mi>x</mi></math> + 3y= 7 &hellip;..(ii)<br>यदि <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>b</mi><mn>1</mn></msub><msub><mi>b</mi><mn>2</mn></msub></mfrac></math> है, तो इन समीकरणों का एक अद्वितीय हल है&nbsp;<br>यहाँ, <math display=\"inline\"><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = 3 , a<sub>2</sub> = 2 , b<sub>1</sub> = 2 , b<sub>2</sub> = 3 <br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>&there4; इसका अद्वितीय हल है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Simplify the following expression.<br>(3<math display=\"inline\"><mi>x</mi></math> + 17 - 2y)(3x - 17 - 2y)</p>",
                    question_hi: "<p>15. निम्नलिखित व्यंजक को हल कीजिए।<br>(3<math display=\"inline\"><mi>x</mi></math> + 17 - 2y)(3x - 17 - 2y)</p>",
                    options_en: ["<p>9<math display=\"inline\"><mi>x</mi></math><sup>2</sup> - 12xy + 4y<sup>2</sup> + 289</p>", "<p>9<math display=\"inline\"><mi>x</mi></math><sup>2</sup> + 12xy + 4y<sup>2</sup> - 289</p>", 
                                "<p>9<math display=\"inline\"><mi>x</mi></math><sup>2 </sup>- 12xy + 4y<sup>2</sup> - 289</p>", "<p>9<math display=\"inline\"><mi>x</mi></math><sup>2</sup> - 6xy + 4y<sup>2</sup> - 289</p>"],
                    options_hi: ["<p>9<math display=\"inline\"><mi>x</mi></math><sup>2</sup> - 12xy + 4y<sup>2</sup> + 289</p>", "<p>9<math display=\"inline\"><mi>x</mi></math><sup>2</sup> + 12xy + 4y<sup>2</sup> - 289</p>",
                                "<p>9<math display=\"inline\"><mi>x</mi></math><sup>2</sup> - 12xy + 4y<sup>2</sup> - 289</p>", "<p>9<math display=\"inline\"><mi>x</mi></math><sup>2 </sup>- 6xy + 4y<sup>2</sup> - 289</p>"],
                    solution_en: "<p>15.(c) (3<math display=\"inline\"><mi>x</mi></math> + 17 - 2y)(3x - 17 - 2y)<br>After rearranging we can write it, <br>= [(3<math display=\"inline\"><mi>x</mi></math> - 2y) +17] [( 3x - 2y) - 17)<br>= (a + b)(a - b) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>= <math display=\"inline\"><msup><mrow><mo>(</mo><mn>3</mn><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>y</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></msup></math> - 17<sup>2</sup><br>= 9<math display=\"inline\"><mi>x</mi></math><sup>2</sup> - 12xy + 4y<sup>2 </sup>- 289</p>",
                    solution_hi: "<p>15.(c) (3<math display=\"inline\"><mi>x</mi></math> + 17 - 2y)(3x - 17 - 2y)<br>पुनर्व्यवस्थित करने के बाद हम इसे लिख सकते हैं, <br>= [(3<math display=\"inline\"><mi>x</mi></math> - 2y) +17] [( 3x - 2y) - 17)<br>= (a + b)(a - b) = <math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mi>b</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br>= <math display=\"inline\"><msup><mrow><mo>(</mo><mn>3</mn><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>2</mn><mi>y</mi><mo>)</mo><mi>&#160;</mi></mrow><mrow><mn>2</mn></mrow></msup></math> - 17<sup>2</sup><br>= 9<math display=\"inline\"><mi>x</mi></math><sup>2</sup> - 12xy + 4y<sup>2 </sup>- 289</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>