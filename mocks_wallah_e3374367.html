<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: " <p>1. </span><span style=\"font-family:Cambria Math\">Given below are four jumbled sentences. Pick the option that gives their correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. Amongst all the cities of Japan, Hiroshima is the one that experiences earliest mornings.</span></p> <p><span style=\"font-family:Cambria Math\">Q. It experiences the earliest sun rise amongst all the countries.</span></p> <p><span style=\"font-family:Cambria Math\">R. According to the words of the Japanese envoy himself, that name was chosen because the country was so close to where the sun rises.</span></p> <p><span style=\"font-family:Cambria Math\">S. Japan is also known as the land of the rising sun. </span></p>",
                    question_hi: " <p>1. </span><span style=\"font-family:Cambria Math\">Given below are four jumbled sentences. Pick the option that gives their correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. Amongst all the cities of Japan, Hiroshima is the one that experiences earliest mornings.</span></p> <p><span style=\"font-family:Cambria Math\">Q. It experiences the earliest sun rise amongst all the countries.</span></p> <p><span style=\"font-family:Cambria Math\">R. According to the words of the Japanese envoy himself, that name was chosen because the country was so close to where the sun rises.</span></p> <p><span style=\"font-family:Cambria Math\">S. Japan is also known as the land of the rising sun. </span></p>",
                    options_en: [" <p> SRQP  </span></p>", " <p> PSQR</span></p>", 
                                " <p> QPRS </span></p>", " <p> SQRP</span></p>"],
                    options_hi: [" <p> SRQP  </span></p>", " <p> PSQR</span></p>",
                                " <p> QPRS </span></p>", " <p> SQRP</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">1</span><span style=\"font-family:Cambria Math\">.(d) </span><span style=\"font-family:Cambria Math\">SQRP</span></p> <p><span style=\"font-family:Cambria Math\">Sentence S will be the starting line as it introduces the main idea of the parajumble i.e. Japan, the land of the rising sun. However, Sentence Q states that it experiences the earliest sun rise amongst all the countries. So, Q will follow S. Further, Sentence R states that the name was chosen because the country was so close to where the sun rises and Sentence P states that Hiroshima in Japan is the one that experiences earliest mornings. So, P will follow R. Going through the options, option d has the correct sequence.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">1</span><span style=\"font-family:Cambria Math\">.(d) </span><span style=\"font-family:Cambria Math\">SQRP</span></p> <p><span style=\"font-family:Cambria Math\">Sentence S  </span><span style=\"font-family:Nirmala UI\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘Japan, the land of the rising sun’ </span><span style=\"font-family:Nirmala UI\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हालाँकि</span><span style=\"font-family:Cambria Math\">, Sentence Q </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">देशों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सबसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सूर्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उदय</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुभव</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, S </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> Q </span><span style=\"font-family:Nirmala UI\">आएगा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आगे</span><span style=\"font-family:Cambria Math\">, Sentence R </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नाम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चुना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">था</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">देश</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सूरज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उगने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करीब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">था</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> Sentence P </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जापान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हिरोशिमा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सबसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सुबह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अनुभव</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, R,</span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> P </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\">, option d </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">2.</span><span style=\"font-family:Cambria Math\"> Some parts of a few sentences have been jumbled up, and labelled A, B, C and D.</span></p> <p><span style=\"font-family:Cambria Math\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p> <p><span style=\"font-family:Cambria Math\">The Ramayana is one of the largest ancient</span></p> <p><span style=\"font-family:Cambria Math\">A. 24,000 verses divided into seven Khanda (parts)</span></p> <p><span style=\"font-family:Cambria Math\">B. the first and the seventh being later additions.</span></p> <p><span style=\"font-family:Cambria Math\">C. epics in world literature. It consists of nearly</span></p> <p><span style=\"font-family:Cambria Math\">D. It belongs to the genre of Itihasa, narratives of past events, interspersed with teachings on the goals of human life.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">2.</span><span style=\"font-family:Cambria Math\"> Some parts of a few sentences have been jumbled up, and labelled A, B, C and D.</span></p> <p><span style=\"font-family:Cambria Math\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p> <p><span style=\"font-family:Cambria Math\">The Ramayana is one of the largest ancient</span></p> <p><span style=\"font-family:Cambria Math\">A. 24,000 verses divided into seven Khanda (parts)</span></p> <p><span style=\"font-family:Cambria Math\">B. the first and the seventh being later additions.</span></p> <p><span style=\"font-family:Cambria Math\">C. epics in world literature. It consists of nearly</span></p> <p><span style=\"font-family:Cambria Math\">D. It belongs to the genre of Itihasa, narratives of past events, interspersed with teachings on the goals of human life.</span></p>",
                    options_en: [" <p>  DCBA</span></p>", " <p>  CABD</span></p>", 
                                " <p>  BDCA</span></p>", " <p> ABDC</span></p>"],
                    options_hi: [" <p>  DCBA</span></p>", " <p>  CABD</span></p>",
                                " <p>  BDCA</span></p>", " <p> ABDC</span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">.(b)</span><span style=\"font-family:Cambria Math\"> CABD</span></p> <p><span style=\"font-family:Cambria Math\">Sentence C will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family:Cambria Math\">Ramayana is one of the largest ancient epics in world literature. </span><span style=\"font-family:Cambria Math\">However, Sentence A states that it has </span><span style=\"font-family:Cambria Math\">24,000 verses divided into seven Khanda</span><span style=\"font-family:Cambria Math\">. So, A will follow C. Further, Sentence B states that </span><span style=\"font-family:Cambria Math\">the first and the seventh Khanda being later additions </span><span style=\"font-family:Cambria Math\">and Sentence D states that </span><span style=\"font-family:Cambria Math\">it belongs to the genre of Itihasa</span><span style=\"font-family:Cambria Math\">. So, D will follow B. Going through the options, option b has the correct sequence.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">2</span><span style=\"font-family:Cambria Math\">.(b) </span><span style=\"font-family:Cambria Math\"> CABD</span></p> <p><span style=\"font-family:Cambria Math\">Sentence C </span><span style=\"font-family:Nirmala UI\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘</span><span style=\"font-family:Cambria Math\">Ramayana is one of the largest ancient epics in world literature’ </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हालाँकि</span><span style=\"font-family:Cambria Math\">, Sentence A </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> 24,000 </span><span style=\"font-family:Nirmala UI\">छंद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> 7 </span><span style=\"font-family:Nirmala UI\">खंड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विभाजित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, C, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> A </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आगे</span><span style=\"font-family:Cambria Math\">, Sentence B </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सातवाँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">खंड</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जोड़ा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\">  Sentence D </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इतिहास</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शैली</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संबंधित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, B, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">D </span><span style=\"font-family:Nirmala UI\">आएगा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> ,  option b </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">3. </span><span style=\"font-family:Cambria Math\">Some parts of a few sentences have been jumbled up, and </span><span style=\"font-family:Cambria Math\">labelled </span><span style=\"font-family:Cambria Math\">A, B, C and D.</span></p> <p><span style=\"font-family:Cambria Math\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p> <p><span style=\"font-family:Cambria Math\">Mr. Summers spoke frequently </span></p> <p><span style=\"font-family:Cambria Math\">A. the one that had been constructed when the first people settled down to make a village here</span></p> <p><span style=\"font-family:Cambria Math\">B.  liked to upset even as much tradition as was represented by the black box. There was a</span></p> <p><span style=\"font-family:Cambria Math\">C. to the villagers about making a new box, but no one</span></p> <p><span style=\"font-family:Cambria Math\">D.  story that the present box had been made with some pieces of the box that had preceded it</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">3. </span><span style=\"font-family:Cambria Math\">Some parts of a few sentences have been jumbled up, and </span><span style=\"font-family:Cambria Math\">labelled </span><span style=\"font-family:Cambria Math\">A, B, C and D.</span></p> <p><span style=\"font-family:Cambria Math\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p> <p><span style=\"font-family:Cambria Math\">Mr. Summers spoke frequently </span></p> <p><span style=\"font-family:Cambria Math\">A. the one that had been constructed when the first people settled down to make a village here</span></p> <p><span style=\"font-family:Cambria Math\">B.  liked to upset even as much tradition as was represented by the black box. There was a</span></p> <p><span style=\"font-family:Cambria Math\">C. to the villagers about making a new box, but no one</span></p> <p><span style=\"font-family:Cambria Math\">D.  story that the present box had been made with some pieces of the box that had preceded it</span></p>",
                    options_en: [" <p> BDAC</span></p>", " <p> DACB</span></p>", 
                                " <p> ACDB</span></p>", " <p> CBDA</span></p>"],
                    options_hi: [" <p> BDAC</span></p>", " <p> DACB</span></p>",
                                " <p> ACDB</span></p>", " <p> CBDA</span></p>"],
                    solution_en: " <p>3.(d) </span><span style=\"font-family:Cambria Math\"> CBDA</span></p> <p><span style=\"font-family:Cambria Math\">Sentence C will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family:Cambria Math\">Mr. Summers spoke frequently to the villagers about making a new box. </span><span style=\"font-family:Cambria Math\">However, Sentence B states that </span><span style=\"font-family:Cambria Math\">no one liked to upset</span><span style=\"font-family:Cambria Math\">. So, B will follow C. Further, Sentence D states that </span><span style=\"font-family:Cambria Math\">there was a story that the present box had been made with some pieces of the box that had preceded it </span><span style=\"font-family:Cambria Math\">and Sentence A states that </span><span style=\"font-family:Cambria Math\"> the one that had been constructed when the first people settled down to make a village here</span><span style=\"font-family:Cambria Math\">. So, A will follow D. Going through the options, option d has the correct sequence.</span></p>",
                    solution_hi: " <p>3.(d) </span><span style=\"font-family:Cambria Math\"> CBDA</span></p> <p><span style=\"font-family:Cambria Math\">Sentence C </span><span style=\"font-family:Nirmala UI\">प्रारम्भिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘</span><span style=\"font-family:Cambria Math\">Mr. Summers spoke frequently to the villagers about making a new box’ </span><span style=\"font-family:Nirmala UI\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हालांकि</span><span style=\"font-family:Cambria Math\">, Sentence B </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">किसी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">परेशान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पसंद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, C </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> B </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आगे</span><span style=\"font-family:Cambria Math\">, Sentence D </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कहानी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">थी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वर्तमान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बॉक्स</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बॉक्स</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कुछ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">टुकड़ों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">साथ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बनाया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">था</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इससे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">था</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> Sentence A </span><span style=\"font-family:Nirmala UI\">बताता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बनाया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">था</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पहले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">थे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यहाँ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गाँव</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बनाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नीचे।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, D, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">A</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\">, option d </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Rearrange the parts of the sentence in correct order.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">P. won the right to host the 2020 Games, explicitly stated that </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Q. Tokyo 2020 represented a key way to open up Japanese </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">R. culture and people to the watching and visiting world </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">S. Shinzo Abe, who was prime minister at the time Japan</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Rearrange the parts of the sentence in correct order.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">P. won the right to host the 2020 Games, explicitly stated that </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Q. Tokyo 2020 represented a key way to open up Japanese </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">R. culture and people to the watching and visiting world </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">S. Shinzo Abe, who was prime minister at the time Japan</span></p>\n",
                    options_en: ["<p>QRSP</p>\n", "<p>RPQS</p>\n", 
                                "<p>SRPQ</p>\n", "<p>SPQR</p>\n"],
                    options_hi: ["<p>QRSP</p>\n", "<p>RPQS</p>\n",
                                "<p>SRPQ</p>\n", "<p>SPQR</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4</span><span style=\"font-family: Cambria Math;\">.(d) </span><span style=\"font-family: Cambria Math;\">SPQR</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence S will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family: Cambria Math;\">Shinzo Abe. </span><span style=\"font-family: Cambria Math;\">However, Sentence P states that he was the PM when Japan</span><span style=\"font-family: Cambria Math;\"> won the right to host the 2020 Games</span><span style=\"font-family: Cambria Math;\">. So, P will follow S. Further, Sentence Q states that </span><span style=\"font-family: Cambria Math;\">Tokyo 2020 represented a key way </span><span style=\"font-family: Cambria Math;\">and Sentence R states that it </span><span style=\"font-family: Cambria Math;\">opens up Japanese culture and people to the watching and visiting world</span><span style=\"font-family: Cambria Math;\">. So, R will follow Q . Going through the options, option d has the correct sequence.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4</span><span style=\"font-family: Cambria Math;\">.(d) </span><span style=\"font-family: Cambria Math;\">SPQR</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence S </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> line </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> parajumble </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;</span><span style=\"font-family: Cambria Math;\">Shinzo Abe&rsquo;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2354;&#2366;&#2305;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">, Sentence P </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2346;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2020 </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2375;&#2354;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2332;&#2348;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, S, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">, Sentence Q </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2379;&#2325;&#2381;&#2351;&#2379;</span><span style=\"font-family: Cambria Math;\"> 2020 </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2340;&#2381;&#2357;&#2346;&#2370;&#2352;&#2381;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2368;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2344;&#2367;&#2343;&#2367;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Sentence R </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2346;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2360;&#2381;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2327;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2326;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2366;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2344;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2379;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, Q,</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">R</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> options </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> , option d </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">5. </span><span style=\"font-family:Cambria Math\">Given below are four jumbled sentences. Pick the option that gives their correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. The earliest years of a child\'s life are critical. </span></p> <p><span style=\"font-family:Cambria Math\">Q. This is why an establishment has been created for the protection of children in these crucial years, known as ECCE.</span></p> <p><span style=\"font-family:Cambria Math\">R. It is during the early years that children develop the cognitive, physical, social and emotional skills that they need to succeed in life.</span></p> <p><span style=\"font-family:Cambria Math\">S. These years determine child\'s survival and thriving in life, and lay the foundations for her/his learning and holistic development.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">5. </span><span style=\"font-family:Cambria Math\">Given below are four jumbled sentences. Pick the option that gives their correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. The earliest years of a child\'s life are critical. </span></p> <p><span style=\"font-family:Cambria Math\">Q. This is why an establishment has been created for the protection of children in these crucial years, known as ECCE.</span></p> <p><span style=\"font-family:Cambria Math\">R. It is during the early years that children develop the cognitive, physical, social and emotional skills that they need to succeed in life.</span></p> <p><span style=\"font-family:Cambria Math\">S. These years determine child\'s survival and thriving in life, and lay the foundations for her/his learning and holistic development.</span></p>",
                    options_en: [" <p> RSPQ</span></p>", " <p> PSRQ</span></p>", 
                                " <p> PSQR</span></p>", " <p> QPSR</span></p> <p><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> RSPQ</span></p>", " <p> PSRQ</span></p>",
                                " <p> PSQR</span></p>", " <p> QPSR</span></p>"],
                    solution_en: " <p>5.(b) </span><span style=\"font-family:Cambria Math\">PSRQ</span></p> <p><span style=\"font-family:Cambria Math\">Sentence P will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family:Cambria Math\">The earliest years of a child\'s life are critical. </span><span style=\"font-family:Cambria Math\">However, Sentence S states that </span><span style=\"font-family:Cambria Math\">these years determine child\'s survival and thriving in life</span><span style=\"font-family:Cambria Math\">. So, S will follow P. Further, Sentence R states that </span><span style=\"font-family:Cambria Math\">it is during the early years that children develop the cognitive, physical, social and emotional skills </span><span style=\"font-family:Cambria Math\">and Sentence Q states that </span><span style=\"font-family:Cambria Math\">this is why an establishment has been created for the protection of children in these crucial years, known as ECCE</span><span style=\"font-family:Cambria Math\">. So, Q will follow R. Going through the options, option b has the correct sequence.</span></p>",
                    solution_hi: " <p>5.(b) </span><span style=\"font-family:Cambria Math\">PSRQ</span></p> <p><span style=\"font-family:Cambria Math\">Sentence P </span><span style=\"font-family:Nirmala UI\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘</span><span style=\"font-family:Cambria Math\">The earliest years of a child\'s life are critical’ </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हालांकि</span><span style=\"font-family:Cambria Math\">, Sentence S </span><span style=\"font-family:Nirmala UI\">बताता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ये</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वर्ष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बच्चे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जीवन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जीवित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रहने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संपन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निर्धारण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">,  P, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> S </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आगे</span><span style=\"font-family:Cambria Math\">, Sentence R </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वर्षों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दौरान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बच्चे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संज्ञानात्मक</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">शारीरिक</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">सामाजिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भावनात्मक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कौशल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विकसित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> Sentence Q </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यही</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कारण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बच्चों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सुरक्षा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रतिष्ठान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बनाया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">महत्वपूर्ण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वर्षों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">ECCE</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नाम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, R, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Q </span><span style=\"font-family:Nirmala UI\">आएगा</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> , option b </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">6.</span><span style=\"font-family:Cambria Math\"> Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Cambria Math\">It could be</span></p> <p><span style=\"font-family:Cambria Math\">P. became a byword for superhuman intellectual ability</span></p> <p><span style=\"font-family:Cambria Math\">Q. argued that 1919 was the year when Einstein\'s name </span></p> <p><span style=\"font-family:Cambria Math\">R. making possible the small industry of</span></p> <p><span style=\"font-family:Cambria Math\">S. Einstein-themed merchandise that still exists today</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">6.</span><span style=\"font-family:Cambria Math\"> Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Cambria Math\">It could be</span></p> <p><span style=\"font-family:Cambria Math\">P. became a byword for superhuman intellectual ability</span></p> <p><span style=\"font-family:Cambria Math\">Q. argued that 1919 was the year when Einstein\'s name </span></p> <p><span style=\"font-family:Cambria Math\">R. making possible the small industry of</span></p> <p><span style=\"font-family:Cambria Math\">S. Einstein-themed merchandise that still exists today</span></p>",
                    options_en: [" <p> QPRS</span></p>", " <p>  PSQR</span></p>", 
                                " <p>  RSPQ</span></p>", " <p>  PQRS</span></p>"],
                    options_hi: [" <p> QPRS</span></p>", " <p>  PSQR</span></p>",
                                " <p>  RSPQ</span></p>", " <p>  PQRS</span></p>"],
                    solution_en: " <p>6.(a)</span><span style=\"font-family:Cambria Math\"> QPRS</span></p> <p><span style=\"font-family:Cambria Math\">Sentence Q will be the starting line as it introduces the main idea of the parajumble i.e. argument on the year 1919. However, Sentence P states that it is the year </span><span style=\"font-family:Cambria Math\">when Einstein\'s name became a byword for superhuman intellectual ability</span><span style=\"font-family:Cambria Math\">. So, P will follow Q . Further, Sentence R talks about the small industry and Sentence S talks about </span><span style=\"font-family:Cambria Math\">Einstein-themed merchandise that still exists today</span><span style=\"font-family:Cambria Math\">. So, S will follow R. Going through the options, option a has the correct sequence.</span></p>",
                    solution_hi: " <p>6.(a) </span><span style=\"font-family:Cambria Math\"> QPRS</span></p> <p><span style=\"font-family:Cambria Math\">Sentence Q </span><span style=\"font-family:Nirmala UI\">प्रारम्भिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘argument on the year 1919’ </span><span style=\"font-family:Nirmala UI\">शामिल</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हालांकि</span><span style=\"font-family:Cambria Math\">, Sentence P </span><span style=\"font-family:Nirmala UI\">बताता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वर्ष</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जब</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आइंस्टीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नाम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अलौकिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बौद्धिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्षमता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संकेत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, Q, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> P </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अलावा</span><span style=\"font-family:Cambria Math\">, Sentence R </span><span style=\"font-family:Nirmala UI\">छोटे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उद्योग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> Sentence S </span><span style=\"font-family:Nirmala UI\">आइंस्टीन</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">थीम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वाले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आज</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मौजूद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, R, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">S </span><span style=\"font-family:Nirmala UI\">आएगा</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> , option a </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: " <p>7.</span><span style=\"font-family:Cambria Math\"> Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. on the US naval base, close to the prison compounds</span></p> <p><span style=\"font-family:Cambria Math\">Q. housing the remaining 39 detainees held in the \"war on terror\"</span></p> <p><span style=\"font-family:Cambria Math\">R. inviting tenders for private contractors to run the Migrant Operations Center</span></p> <p><span style=\"font-family:Cambria Math\">S. the Immigration and Customs Enforcement (ICE) bureau is</span></p>",
                    question_hi: " <p>7.</span><span style=\"font-family:Cambria Math\"> Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. on the US naval base, close to the prison compounds</span></p> <p><span style=\"font-family:Cambria Math\">Q. housing the remaining 39 detainees held in the \"war on terror\"</span></p> <p><span style=\"font-family:Cambria Math\">R. inviting tenders for private contractors to run the Migrant Operations Center</span></p> <p><span style=\"font-family:Cambria Math\">S. the Immigration and Customs Enforcement (ICE) bureau is</span></p>",
                    options_en: [" <p><span style=\"font-family:Cambria Math\"> RPQS</span></p>", " <p><span style=\"font-family:Cambria Math\"> QSRP</span></p>", 
                                " <p><span style=\"font-family:Cambria Math\"> SPQR</span></p>", " <p><span style=\"font-family:Cambria Math\"> SRPQ</span></p>"],
                    options_hi: [" <p><span style=\"font-family:Cambria Math\"> RPQS</span></p>", " <p><span style=\"font-family:Cambria Math\"> QSRP</span></p>",
                                " <p><span style=\"font-family:Cambria Math\"> SPQR</span></p>", " <p><span style=\"font-family:Cambria Math\"> SRPQ</span></p>"],
                    solution_en: " <p>7.(d) </span><span style=\"font-family:Cambria Math\"> SRPQ</span></p> <p><span style=\"font-family:Cambria Math\">Sentence S will be the starting line as it introduces the main idea of the parajumble i.e. Immigration and Customs Enforcement (ICE) bureau. However, Sentence R states that it is inviting tenders for private contractors to run the Migrant Operations Center. So, R will follow S. Further, Sentence P states that it is on the US naval base and Sentence Q states that it has remaining 39 detainees held in the war on terror. So, Q will follow P. Going through the options, option d has the correct sequence.</span></p>",
                    solution_hi: " <p>7.(d) </span><span style=\"font-family:Cambria Math\"> SRPQ</span></p> <p><span style=\"font-family:Cambria Math\">Sentence S </span><span style=\"font-family:Nirmala UI\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘Immigration and Customs Enforcement (ICE) bureau’ </span><span style=\"font-family:Nirmala UI\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हालांकि</span><span style=\"font-family:Cambria Math\">, Sentence R </span><span style=\"font-family:Nirmala UI\">बताता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रवासी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संचालन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">केंद्र</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चलाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निजी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ठेकेदारों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निविदाएं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आमंत्रित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रहा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसलिए</span><span style=\"font-family:Cambria Math\">, S, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> R </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आगे</span><span style=\"font-family:Cambria Math\">, Sentence P </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अमेरिकी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नौसैनिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अड्डे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वाक्य</span><span style=\"font-family:Cambria Math\"> Q </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पास</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शेष</span><span style=\"font-family:Cambria Math\"> 39 </span><span style=\"font-family:Nirmala UI\">बंदियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आतंकवाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">खिलाफ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">युद्ध</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रखा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, P, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> Q </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\">,  option d  </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">Some parts of one or more sentences have been jumbled up, and labelled A, B, C and </span><span style=\"font-family: Cambria Math;\">D. Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Their approach sensitized</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A. of it. Similarly, behind them, in the halls,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B. flicked on when they came within ten feet</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C. a switch somewhere and the nursery light</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">D. lights went on and off as they left them behind, with a soft automaticity.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8. </span><span style=\"font-family: Cambria Math;\">Some parts of one or more sentences have been jumbled up, and labelled A, B, C and </span><span style=\"font-family: Cambria Math;\">D. Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Their approach sensitized</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">A. of it. Similarly, behind them, in the halls,</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">B. flicked on when they came within ten feet</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">C. a switch somewhere and the nursery light</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">D. lights went on and off as they left them behind, with a soft automaticity.</span></p>\n",
                    options_en: ["<p>DCBA</p>\n", "<p>CBAD</p>\n", 
                                "<p>BADC</p>\n", "<p>BCAD</p>\n"],
                    options_hi: ["<p>DCBA</p>\n", "<p>CBAD</p>\n",
                                "<p>BADC</p>\n", "<p>BCAD</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8</span><span style=\"font-family: Cambria Math;\">.(b) </span><span style=\"font-family: Cambria Math;\">CBAD </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence C will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family: Cambria Math;\">Their approach sensitized a switch somewhere. </span><span style=\"font-family: Cambria Math;\">However, Sentence B states that </span><span style=\"font-family: Cambria Math;\"> the nursery light flicked on</span><span style=\"font-family: Cambria Math;\">. So, B will follow C. Further, Sentence A states that they came within ten feet of it and Sentence D states that</span><span style=\"font-family: Cambria Math;\"> behind them, in the halls lights went on and off as they left them behind</span><span style=\"font-family: Cambria Math;\">. So, D will follow A. Going through the options, option b has the correct sequence.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8</span><span style=\"font-family: Cambria Math;\">.(b) </span><span style=\"font-family: Cambria Math;\">CBAD </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence C </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> line </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2376;&#2352;&#2366;&#2332;&#2306;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;</span><span style=\"font-family: Cambria Math;\">Their approach sensitized a switch somewhere&rsquo;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2354;&#2366;&#2305;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">, Sentence B </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2340;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2352;&#2381;&#2360;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2340;&#2381;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2354;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, C </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> B </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2327;&#2375;</span><span style=\"font-family: Cambria Math;\">, Sentence A </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2368;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Sentence D </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2368;&#2331;&#2375;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2377;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2358;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2368;&#2331;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2379;&#2337;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2375;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, A, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">D </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> options </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> , option b </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">9. </span><span style=\"font-family:Cambria Math\">Some parts of a few sentences have been jumbled up, and labelled A, B, C and D.</span></p> <p><span style=\"font-family:Cambria Math\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p> <p><span style=\"font-family:Cambria Math\">My years in my father\'s house</span></p> <p><span style=\"font-family:Cambria Math\">A. would have been unbearable had</span></p> <p><span style=\"font-family:Cambria Math\">B. the feel of his hand clutching mine, his</span></p> <p><span style=\"font-family:Cambria Math\">C. I not had my brother. I never forgot</span></p> <p><span style=\"font-family:Cambria Math\">D. refusal to abandon me. Perhaps he and I would have been close even otherwise.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">9. </span><span style=\"font-family:Cambria Math\">Some parts of a few sentences have been jumbled up, and labelled A, B, C and D.</span></p> <p><span style=\"font-family:Cambria Math\">Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p> <p><span style=\"font-family:Cambria Math\">My years in my father\'s house</span></p> <p><span style=\"font-family:Cambria Math\">A. would have been unbearable had</span></p> <p><span style=\"font-family:Cambria Math\">B. the feel of his hand clutching mine, his</span></p> <p><span style=\"font-family:Cambria Math\">C. I not had my brother. I never forgot</span></p> <p><span style=\"font-family:Cambria Math\">D. refusal to abandon me. Perhaps he and I would have been close even otherwise.</span></p>",
                    options_en: [" <p> ACBD </span></p>", " <p> DBCA</span></p>", 
                                " <p> CBDA </span></p>", " <p> BCDA</span></p>"],
                    options_hi: [" <p> ACBD </span></p>", " <p> DBCA</span></p>",
                                " <p> CBDA </span></p>", " <p> BCDA</span></p>"],
                    solution_en: " <p>9.(a) </span><span style=\"font-family:Cambria Math\">ACBD </span></p> <p><span style=\"font-family:Cambria Math\">Sentence A will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family:Cambria Math\">My years in my father\'s house would have been unbearable. </span><span style=\"font-family:Cambria Math\">However, Sentence C states that if </span><span style=\"font-family:Cambria Math\">I not had my brother.</span><span style=\"font-family:Cambria Math\"> So, C will follow A. Further, Sentence B states that </span><span style=\"font-family:Cambria Math\">I never forgot the feel of his hand clutching mine</span><span style=\"font-family:Cambria Math\"> and Sentence D states that </span><span style=\"font-family:Cambria Math\">his refusal to abandon me</span><span style=\"font-family:Cambria Math\">. So, D will follow B. Going through the options, option a has the correct sequence.</span></p>",
                    solution_hi: " <p>9.(a)</span><span style=\"font-family:Cambria Math\">ACBD </span></p> <p><span style=\"font-family:Cambria Math\">Sentence A </span><span style=\"font-family:Nirmala UI\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमे</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘</span><span style=\"font-family:Cambria Math\">My years in my father\'s house would have been unbearable’ </span><span style=\"font-family:Nirmala UI\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">हालाँकि</span><span style=\"font-family:Cambria Math\">, Sentence C </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कहा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अगर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मेरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पास</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मेरा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भाई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होता।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, A, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> C </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आगे</span><span style=\"font-family:Cambria Math\">,  Sentence B </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मैं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हाथ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मेरे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हाथ</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पकड़ने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भावना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कभी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भूला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> Sentence D </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मुझे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">त्यागने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इनकार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिया।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, B, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">D </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> , option a </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">10. </span><span style=\"font-family:Cambria Math\">Rearrange the parts of the sentence in correct order. </span></p> <p><span style=\"font-family:Cambria Math\">Approaching her</span></p> <p><span style=\"font-family:Cambria Math\">P. upon the impacts of county lines drug-running networks </span></p> <p><span style=\"font-family:Cambria Math\">Q. 80th birthday in February next year, Hill\'s</span></p> <p><span style=\"font-family:Cambria Math\">R. relevance and urgency this time focusing</span></p> <p><span style=\"font-family:Cambria Math\">S. writing has lost none of its immediate</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">10. </span><span style=\"font-family:Cambria Math\">Rearrange the parts of the sentence in correct order. </span></p> <p><span style=\"font-family:Cambria Math\">Approaching her</span></p> <p><span style=\"font-family:Cambria Math\">P. upon the impacts of county lines drug-running networks </span></p> <p><span style=\"font-family:Cambria Math\">Q. 80th birthday in February next year, Hill\'s</span></p> <p><span style=\"font-family:Cambria Math\">R. relevance and urgency this time focusing</span></p> <p><span style=\"font-family:Cambria Math\">S. writing has lost none of its immediate</span></p>",
                    options_en: [" <p> QSRP </span></p>", " <p> SQRP </span></p>", 
                                " <p> RPQS </span></p>", " <p> PQSR</span></p>"],
                    options_hi: [" <p> QSRP </span></p>", " <p> SQRP </span></p>",
                                " <p> RPQS </span></p>", " <p> PQSR</span></p>"],
                    solution_en: " <p>10.(a) </span><span style=\"font-family:Cambria Math\"> QSRP </span></p> <p><span style=\"font-family:Cambria Math\">Sentence Q will be the starting line as it introduces the main idea of the parajumble i.e. Her </span><span style=\"font-family:Cambria Math\">80th birthday. </span><span style=\"font-family:Cambria Math\">However, Sentence S talks about </span><span style=\"font-family:Cambria Math\">Hill\'s writing</span><span style=\"font-family:Cambria Math\">. So, S will follow Q . Further, Sentence R states that it has not lost its relevance and urgency and Sentence P states that it focuses </span><span style=\"font-family:Cambria Math\">upon the impacts of county lines drug-running networks</span><span style=\"font-family:Cambria Math\">. So, P will follow R. Going through the options, option a has the correct sequence.</span></p>",
                    solution_hi: " <p>10.(a) </span><span style=\"font-family:Cambria Math\"> QSRP </span></p> <p><span style=\"font-family:Cambria Math\">Sentence Q  </span><span style=\"font-family:Nirmala UI\">प्रारम्भिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘Her </span><span style=\"font-family:Cambria Math\">80th birthday’</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हालाँकि</span><span style=\"font-family:Cambria Math\">, Sentence S </span><span style=\"font-family:Nirmala UI\">हिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लेखन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">,</span><span style=\"font-family:Cambria Math\">Q</span><span style=\"font-family:Cambria Math\">, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> S </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अलावा</span><span style=\"font-family:Cambria Math\">, Sentence R </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अपनी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रासंगिकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तात्कालिकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नहीं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">खोई</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> Sentence P  </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">काउंटी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लाइन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ड्रग</span><span style=\"font-family:Cambria Math\">-</span><span style=\"font-family:Nirmala UI\">रनिंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नेटवर्क</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रभावों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ध्यान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">केंद्रित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, R, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">P </span><span style=\"font-family:Nirmala UI\">आएगा</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\"> , option a </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">11.</span><span style=\"font-family:Cambria Math\"> Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. we found that thyroid hormones regulate white bar formation </span></p> <p><span style=\"font-family:Cambria Math\">Q. sea anemone species in which the young fish is recruited</span></p> <p><span style=\"font-family:Cambria Math\">R. and that a shift in hormone levels, associated with ecological</span></p> <p><span style=\"font-family:Cambria Math\">S. differences, results in divergent colour patterns in different</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">11.</span><span style=\"font-family:Cambria Math\"> Rearrange the parts of the sentence in correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. we found that thyroid hormones regulate white bar formation </span></p> <p><span style=\"font-family:Cambria Math\">Q. sea anemone species in which the young fish is recruited</span></p> <p><span style=\"font-family:Cambria Math\">R. and that a shift in hormone levels, associated with ecological</span></p> <p><span style=\"font-family:Cambria Math\">S. differences, results in divergent colour patterns in different</span></p>",
                    options_en: [" <p> SQPR </span></p>", " <p> RSQP </span></p>", 
                                " <p> PRSQ </span></p>", " <p> PQRS</span></p>"],
                    options_hi: [" <p> SQPR </span></p>", " <p> RSQP </span></p>",
                                " <p> PRSQ </span></p>", " <p> PQRS</span></p>"],
                    solution_en: " <p>11.(c) </span><span style=\"font-family:Cambria Math\">PRSQ </span></p> <p><span style=\"font-family:Cambria Math\">Sentence P will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family:Cambria Math\">thyroid hormones regulate white bar formation.</span><span style=\"font-family:Cambria Math\"> However, Sentence R states that it also regulates </span><span style=\"font-family:Cambria Math\">a shift in hormone levels & ecological differences</span><span style=\"font-family:Cambria Math\">. So, R will follow P. Further, Sentence S states that it </span><span style=\"font-family:Cambria Math\">results </span><span style=\"font-family:Cambria Math\">in divergent colour patterns </span><span style=\"font-family:Cambria Math\">and Sentence Q talks about the colour patterns in </span><span style=\"font-family:Cambria Math\">sea anemone species</span><span style=\"font-family:Cambria Math\">. So, Q will follow S. Going through the options, option c has the correct sequence.</span></p>",
                    solution_hi: " <p>11.(c) </span><span style=\"font-family:Cambria Math\">PRSQ </span></p> <p><span style=\"font-family:Cambria Math\">Sentence P </span><span style=\"font-family:Nirmala UI\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\">  ‘</span><span style=\"font-family:Cambria Math\">thyroid hormones regulate white bar formation’ </span><span style=\"font-family:Nirmala UI\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हालाँकि</span><span style=\"font-family:Cambria Math\">, Sentence R </span><span style=\"font-family:Nirmala UI\">बताता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हार्मोन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">स्तर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पारिस्थितिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अंतर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बदलाव</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">नियंत्रित</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, P, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">R</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आगे</span><span style=\"font-family:Cambria Math\">, Sentence S </span><span style=\"font-family:Nirmala UI\">बताता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">परिणाम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">भिन्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पैटर्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> Sentence Q </span><span style=\"font-family:Nirmala UI\">समुद्री</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">एनीमोन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">प्रजातियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रंग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पैटर्न</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बारे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बात</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, S, </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Q </span><span style=\"font-family:Nirmala UI\">आएगा</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\">, option c </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: " <p>12. </span><span style=\"font-family:Cambria Math\">Given below are four jumbled sentences. Pick the option that gives their correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. To stop Caesar from gaining too much power, Brutus and the conspirators kill him on the Ides of March.</span></p> <p><span style=\"font-family:Cambria Math\">Q. Brutus and his friend Cassius lose and kill themselves, leaving Antony to rule in Rome.</span></p> <p><span style=\"font-family:Cambria Math\">R. Mark Antony drives the conspirators out of Rome and fights them in a battle.</span></p> <p><span style=\"font-family:Cambria Math\">S. The tale of Julius Caesar involves jealous conspirators who convince Caesar\'s most trusted ally Brutus to join their assassination plot against Caesar.</span></p>",
                    question_hi: " <p>12. </span><span style=\"font-family:Cambria Math\">Given below are four jumbled sentences. Pick the option that gives their correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. To stop Caesar from gaining too much power, Brutus and the conspirators kill him on the Ides of March.</span></p> <p><span style=\"font-family:Cambria Math\">Q. Brutus and his friend Cassius lose and kill themselves, leaving Antony to rule in Rome.</span></p> <p><span style=\"font-family:Cambria Math\">R. Mark Antony drives the conspirators out of Rome and fights them in a battle.</span></p> <p><span style=\"font-family:Cambria Math\">S. The tale of Julius Caesar involves jealous conspirators who convince Caesar\'s most trusted ally Brutus to join their assassination plot against Caesar.</span></p>",
                    options_en: [" <p> SQPR</span></p>", " <p> SPRQ </span></p>", 
                                " <p> QPSR </span></p>", " <p> SQRP</span></p>"],
                    options_hi: [" <p>  SQPR</span></p>", " <p> SPRQ </span></p>",
                                " <p> QPSR </span></p>", " <p> SQRP</span></p>"],
                    solution_en: " <p>12.(b) </span><span style=\"font-family:Cambria Math\"> SPRQ </span></p> <p><span style=\"font-family:Cambria Math\">Sentence S will be the starting line as it introduces the main idea of the parajumble i.e. The tale of Julius Caesar involves jealous conspirators. However, Sentence P states that Brutus and the conspirators kill Caesar. So, P will follow S. Further, Sentence R states that Mark Antony drives the </span><span style=\"font-family:Cambria Math\">conspirators out of Rome and Sentence Q states that Brutus and his friend Cassius lose and kill themselves. So, Q will follow R. Going through the options, option b has the correct sequence.</span></p>",
                    solution_hi: " <p>12.(b) </span><span style=\"font-family:Cambria Math\"> SPRQ </span></p> <p><span style=\"font-family:Cambria Math\">Sentence S </span><span style=\"font-family:Nirmala UI\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पैराजंबल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘The tale of Julius Caesar involves jealous conspirators’ </span><span style=\"font-family:Nirmala UI\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> However,  Sentence P </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> Brutus </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">साजिशकर्ता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सीज़र</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">डालते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, S,</span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\">  P </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आगे</span><span style=\"font-family:Cambria Math\">,  Sentence R </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> Mark Antony </span><span style=\"font-family:Nirmala UI\">ने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">साजिशकर्ताओं</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रोम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाहर</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">निकाल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दिया</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> Sentence Q </span><span style=\"font-family:Nirmala UI\">ने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कहा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ब्रूटस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उसका</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दोस्त</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कैसियस</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">गए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">खुद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">डाला।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, R, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> Q  </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\">  , option b </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">13. </span><span style=\"font-family:Cambria Math\">Some parts of a sentence have been jumbled up, and labelled P, Q, R and S. Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p> <p><span style=\"font-family:Cambria Math\">Many retailers have invested</span></p> <p><span style=\"font-family:Cambria Math\">P. time and money into understanding its</span></p> <p><span style=\"font-family:Cambria Math\">Q. make them healthier so they can continue to be marketed</span></p> <p><span style=\"font-family:Cambria Math\">R. promotions work, and changing product ingredients to </span></p> <p><span style=\"font-family:Cambria Math\">S. complexity, refitting shops, trialling which healthier</span></p>",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">Some parts of a sentence have been jumbled up, and labelled P, Q, R and S. Select the option that gives the correct sequence in which these parts can be rearranged to form a meaningful and grammatically correct sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Many retailers have invested</span></p>\r\n<p>P. time and money into understanding its</p>\r\n<p>Q. make them healthier so they can continue to be marketed</p>\r\n<p>R. promotions work, and changing product ingredients to</p>\r\n<p>S. complexity, refitting shops, trialling which healthier</p>\n",
                    options_en: [" <p>   PSRQ</span></p>", " <p>   SRPQ</span></p>", 
                                " <p>   PQRS</span></p>", " <p>  QRPS</span></p>"],
                    options_hi: ["<p>PSRQ</p>\n", "<p>SRPQ</p>\n",
                                "<p>PQRS</p>\n", "<p>QRPS</p>\n"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">13</span><span style=\"font-family:Cambria Math\">.(a) </span><span style=\"font-family:Cambria Math\">PSRQ</span></p> <p><span style=\"font-family:Cambria Math\">Sentence P will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family:Cambria Math\">Many retailers have invested time and money. </span><span style=\"font-family:Cambria Math\">However, Sentence S talks about </span><span style=\"font-family:Cambria Math\">complexity, refitting shops, trialling</span><span style=\"font-family:Cambria Math\">. So, S will follow P. Further, Sentence R states that healthier </span><span style=\"font-family:Cambria Math\">promotions work </span><span style=\"font-family:Cambria Math\">and Sentence Q states that </span><span style=\"font-family:Cambria Math\">changing product ingredients to make them healthier</span><span style=\"font-family:Cambria Math\">. So, Q will follow R. Going through the options, option a has the correct sequence.</span></p>",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13</span><span style=\"font-family: Cambria Math;\">.(a) </span><span style=\"font-family: Cambria Math;\"> PSRQ</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Sentence P </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2366;&#2352;&#2306;&#2349;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> line </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> parajumble </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &lsquo;</span><span style=\"font-family: Cambria Math;\">Many retailers have invested time and money&rsquo; </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2366;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2354;&#2366;&#2305;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\">, Sentence S </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2335;&#2367;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2367;&#2347;&#2367;&#2335;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2377;&#2346;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2381;&#2352;&#2366;&#2351;&#2354;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, P, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> S </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\">, Sentence R </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2360;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> Sentence Q </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2361;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2350;&#2327;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2360;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\">, R, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2319;&#2327;&#2366;&#2404;</span><span style=\"font-family: Cambria Math;\"> options </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2343;&#2381;&#2351;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\">, option a </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> sequence </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: " <p><span style=\"font-family:Cambria Math\">14.</span><span style=\"font-family:Cambria Math\"> Given below are four jumbled sentences. Pick the option that gives their correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. The average adult gets two to four colds a year, while the average child may get six to eight.</span></p> <p><span style=\"font-family:Cambria Math\">Q. It is the most frequent infectious disease in humans.</span></p> <p><span style=\"font-family:Cambria Math\">R. They occur more commonly during the winter.</span></p> <p><span style=\"font-family:Cambria Math\">S. The common cold, also known simply as the cold, is a viral infectious disease of the upper respiratory tract that primarily affects the nose.</span></p>",
                    question_hi: " <p><span style=\"font-family:Cambria Math\">14.</span><span style=\"font-family:Cambria Math\"> Given below are four jumbled sentences. Pick the option that gives their correct order.</span></p> <p><span style=\"font-family:Cambria Math\">P. The average adult gets two to four colds a year, while the average child may get six to eight.</span></p> <p><span style=\"font-family:Cambria Math\">Q. It is the most frequent infectious disease in humans.</span></p> <p><span style=\"font-family:Cambria Math\">R. They occur more commonly during the winter.</span></p> <p><span style=\"font-family:Cambria Math\">S. The common cold, also known simply as the cold, is a viral infectious disease of the upper respiratory tract that primarily affects the nose.</span></p>",
                    options_en: [" <p> QSRP</span></p>", " <p> SQPR</span></p>", 
                                " <p> QPSR</span></p>", " <p> PQRS</span></p>"],
                    options_hi: [" <p> QSRP</span></p>", " <p> SQPR</span></p>",
                                " <p> QPSR</span></p>", " <p> PQRS</span></p>"],
                    solution_en: " <p>14.(b) </span><span style=\"font-family:Cambria Math\">SQPR</span></p> <p><span style=\"font-family:Cambria Math\">Sentence S will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family:Cambria Math\">The common cold affects the nose. </span><span style=\"font-family:Cambria Math\">However, Sentence Q states that </span><span style=\"font-family:Cambria Math\">it is the most frequent infectious disease in humans.</span><span style=\"font-family:Cambria Math\"> So, Q will follow S. Further, Sentence P states that </span><span style=\"font-family:Cambria Math\">the average adult gets two to four colds a year </span><span style=\"font-family:Cambria Math\">and Sentence R states that </span><span style=\"font-family:Cambria Math\">they occur more commonly during the winter</span><span style=\"font-family:Cambria Math\">. So, R will follow P. Going through the options, option b has the correct sequence.</span></p>",
                    solution_hi: " <p>14.(b) </span><span style=\"font-family:Cambria Math\">SQPR</span></p> <p><span style=\"font-family:Cambria Math\">Sentence S </span><span style=\"font-family:Nirmala UI\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\">  </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘</span><span style=\"font-family:Cambria Math\">The common cold affects the nose’ </span><span style=\"font-family:Nirmala UI\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हालाँकि</span><span style=\"font-family:Cambria Math\">, Sentence Q </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">यह</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मनुष्यों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सबसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अधिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वाला</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">संक्रामक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रोग</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, S  </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> Q </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आगे</span><span style=\"font-family:Cambria Math\">, Sentence P </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">औसत</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वयस्क</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">साल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दो</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सर्दी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होती</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Sentence R  </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सर्दियों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दौरान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अधिक</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होते</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हैं।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, P, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">R </span><span style=\"font-family:Nirmala UI\">आएगा</span><span style=\"font-family:Nirmala UI\">।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\">, option b </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: " <p>15. </span><span style=\"font-family:Cambria Math\">Rearrange the parts of the sentence in correct order. </span></p> <p><span style=\"font-family:Cambria Math\">        As global </span></p> <p><span style=\"font-family:Cambria Math\">P. electricity generation is expected to </span></p> <p><span style=\"font-family:Cambria Math\">Q. significantly to maintain its current share of the mix </span></p> <p><span style=\"font-family:Cambria Math\">R. double over the next three decades, nuclear power </span></p> <p><span style=\"font-family:Cambria Math\">S. generating capacity would need to expand   </span></p>",
                    question_hi: " <p>15. </span><span style=\"font-family:Cambria Math\">Rearrange the parts of the sentence in correct order. </span></p> <p><span style=\"font-family:Cambria Math\">        As global </span></p> <p><span style=\"font-family:Cambria Math\">P. electricity generation is expected to </span></p> <p><span style=\"font-family:Cambria Math\">Q. significantly to maintain its current share of the mix </span></p> <p><span style=\"font-family:Cambria Math\">R. double over the next three decades, nuclear power </span></p> <p><span style=\"font-family:Cambria Math\">S. generating capacity would need to expand   </span></p>",
                    options_en: [" <p> PRSQ </span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> QSPR </span><span style=\"font-family:Cambria Math\"> </span></p>", 
                                " <p> PQSR </span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> SQPR</span><span style=\"font-family:Cambria Math\"> </span></p>"],
                    options_hi: [" <p> PRSQ </span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> QSPR </span><span style=\"font-family:Cambria Math\"> </span></p>",
                                " <p> PQSR </span><span style=\"font-family:Cambria Math\"> </span></p>", " <p> SQPR</span><span style=\"font-family:Cambria Math\"> </span></p>"],
                    solution_en: " <p><span style=\"font-family:Cambria Math\">15</span><span style=\"font-family:Cambria Math\">.(a) </span><span style=\"font-family:Cambria Math\"> PRSQ </span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">Sentence P will be the starting line as it introduces the main idea of the parajumble i.e. </span><span style=\"font-family:Cambria Math\">global  electricity generation. </span><span style=\"font-family:Cambria Math\">However, Sentence R states that it </span><span style=\"font-family:Cambria Math\">is expected to double over the next three decades</span><span style=\"font-family:Cambria Math\">. So, R will follow P. Further, Sentence S states that </span><span style=\"font-family:Cambria Math\">nuclear power generating capacity would need to expand </span><span style=\"font-family:Cambria Math\">and Sentence Q states that it should expand </span><span style=\"font-family:Cambria Math\">to maintain its current share of the mix</span><span style=\"font-family:Cambria Math\">. So, Q will follow S. Going through the options, option a has the correct sequence.</span></p>",
                    solution_hi: " <p><span style=\"font-family:Cambria Math\">15</span><span style=\"font-family:Cambria Math\">.(a) </span><span style=\"font-family:Cambria Math\"> PRSQ </span><span style=\"font-family:Cambria Math\"> </span></p> <p><span style=\"font-family:Cambria Math\">Sentence P </span><span style=\"font-family:Nirmala UI\">प्रारंभिक</span><span style=\"font-family:Cambria Math\"> line </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्योंकि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसमें</span><span style=\"font-family:Cambria Math\"> parajumble </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मुख्य</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विचार</span><span style=\"font-family:Cambria Math\"> ‘</span><span style=\"font-family:Cambria Math\">global  electricity generation’ </span><span style=\"font-family:Nirmala UI\">शामिल</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हालाँकि</span><span style=\"font-family:Cambria Math\">, Sentence R </span><span style=\"font-family:Nirmala UI\">बताता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अगले</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तीन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दशकों</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसके</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">दुगुने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उम्मीद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, P, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\">   R </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आगे</span><span style=\"font-family:Cambria Math\">, Sentence S </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">परमाणु</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">ऊर्जा</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">उत्पादन</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">क्षमता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">का</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विस्तार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">की</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">आवश्यकता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">होगी</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">और</span><span style=\"font-family:Cambria Math\"> Sentence Q </span><span style=\"font-family:Nirmala UI\">कहता</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">है</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">कि</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">इसे</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">मिश्रण</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">अपने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">वर्तमान</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">हिस्से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">को</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बनाए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">रखने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">लिए</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">विस्तार</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">करना</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">चाहिए।</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">तो</span><span style=\"font-family:Cambria Math\">, S, </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">बाद</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Cambria Math\">Q </span><span style=\"font-family:Nirmala UI\">आएगा।</span><span style=\"font-family:Cambria Math\"> options </span><span style=\"font-family:Nirmala UI\">के</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">माध्यम</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">से</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">जाने</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">पर</span><span style=\"font-family:Cambria Math\">, option a </span><span style=\"font-family:Nirmala UI\">में</span><span style=\"font-family:Cambria Math\"> </span><span style=\"font-family:Nirmala UI\">सही</span><span style=\"font-family:Cambria Math\"> sequence </span><span style=\"font-family:Nirmala UI\">है।</span></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>