<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. To raise awareness for tiger conservation&rsquo;, when is global tiger day celebrated?</p>",
                    question_hi: "<p>1. बाघ संरक्षण के प्रति जागरूकता बढ़ाने के लिए वैश्विक बाघ दिवस कब मनाया जाता है?</p>",
                    options_en: ["<p>26 July</p>", "<p>25 July</p>", 
                                "<p>20 July</p>", "<p>29 July</p>"],
                    options_hi: ["<p>26 जुलाई</p>", "<p>25 जुलाई</p>",
                                "<p>20 जुलाई</p>", "<p>29 जुलाई</p>"],
                    solution_en: "<p>1.(d) <strong>29 July. </strong>Global tiger day was started in 2010 at the Saint Petersburg Tiger Summit in Russia. Tadoba Tiger Reserve, Maharashtra hosts National Global Tiger Day Celebrations 2022. <strong>Important days -</strong> International Polar Bear Day (27 February); World Wildlife Day (3 March); National Panda Day (16 March); World Sparrow Day (20 March); World Elephant Day (12 August); National Honey Bee Day (19 August).</p>",
                    solution_hi: "<p>1.(d) <strong>29 जुलाई।</strong> वैश्विक बाघ दिवस की शुरुआत 2010 में रूस के सेंट पीटर्सबर्ग टाइगर शिखर सम्मेलन में की गई थी। ताडोबा टाइगर रिजर्व, महाराष्ट्र राष्ट्रीय वैश्विक बाघ दिवस समारोह 2022 की मेजबानी करता है।<strong> महत्वपूर्ण दिवस-</strong> अंतर्राष्ट्रीय ध्रुवीय भालू दिवस (27 फरवरी); विश्व वन्यजीव दिवस (3 मार्च); राष्ट्रीय पांडा दिवस (16 मार्च); विश्व गौरैया दिवस (20 मार्च); विश्व हाथी दिवस (12 अगस्त); राष्ट्रीय मधुमक्खी दिवस (19 अगस्त)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. When is the Constitution Day celebrated in India?</p>",
                    question_hi: "<p>2. भारत में संविधान दिवस कब मनाया जाता है?</p>",
                    options_en: ["<p>15th August</p>", "<p>24th January</p>", 
                                "<p>26th November</p>", "<p>26th January</p>"],
                    options_hi: ["<p>15 अगस्त</p>", "<p>24 जनवरी</p>",
                                "<p>26 नवंबर</p>", "<p>26 जनवरी</p>"],
                    solution_en: "<p>2.(c) <strong>26th November.</strong> The Constituent Assembly of India adopted the Constitution of India on 26th November 1949, which came into effect from 26th January 1950. <strong>Important Days and Dates </strong>- National Birds Day (5th January), National Voters Day (25th January), National Science Day (28 February), National Vaccination Day (16 March), National Mathematics Day (22 December), National Good Governance Day (25 December), International Yoga Day (21 June).</p>",
                    solution_hi: "<p>2.(c) <strong>26 नवंबर |</strong> भारत की संविधान सभा ने 26 नवंबर 1949 को भारत का संविधान अपनाया, जो 26 जनवरी 1950 से लागू हुआ। <strong>महत्वपूर्ण दिन और तारीखें - </strong>राष्ट्रीय पक्षी दिवस (5 जनवरी), राष्ट्रीय मतदाता दिवस (25 जनवरी), राष्ट्रीय विज्ञान दिवस (28फरवरी), राष्ट्रीय टीकाकरण दिवस (16 मार्च), राष्ट्रीय गणित दिवस (22 दिसंबर), राष्ट्रीय सुशासन दिवस (25 दिसंबर), अंतर्राष्ट्रीय योग दिवस (21 जून)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. When is the &lsquo;world health day&rsquo; celebrated?</p>",
                    question_hi: "<p>3. विश्व स्वास्थ्य दिवस कब मनाया जाता है?</p>",
                    options_en: ["<p>14th April</p>", "<p>7th April</p>", 
                                "<p>8th April</p>", "<p>10th April</p>"],
                    options_hi: ["<p>14 अप्रैल</p>", "<p>7 अप्रैल</p>",
                                "<p>8 अप्रैल</p>", "<p>10 अप्रैल</p>"],
                    solution_en: "<p>3.(b) <strong>7<sup>th</sup> April.</strong> It is celebrated to mark the foundation of the World Health Organization (WHO) on 7<sup>th</sup> April 1948. Headquarters of WHO - Geneva (Switzerland). <strong>14<sup>th </sup>April -</strong> Dr Babasaheb Ambedkar Jayanti. <strong>10<sup>th</sup> April -</strong> World Homoeopathy Day (WHD).</p>",
                    solution_hi: "<p>3.(b) <strong>7 अप्रैल। </strong>यह 7 अप्रैल 1948 को विश्व स्वास्थ्य संगठन (WHO) की स्थापना के उपलक्ष्य में मनाया जाता है । WHO का मुख्यालय - जिनेवा (स्विट्जरलैंड)। <strong>14 अप्रैल -</strong> डॉ. बाबासाहेब अम्बेडकर जयंती। <strong>10 अप्रैल </strong>- विश्व होम्योपैथी दिवस (WHD)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. When is the &lsquo;world population day&rsquo; celebrated?</p>",
                    question_hi: "<p>4. विश्व जनसंख्या दिवस कब मनाया जाता है ?</p>",
                    options_en: ["<p>11th July</p>", "<p>5th July</p>", 
                                "<p>1st July</p>", "<p>21st July</p>"],
                    options_hi: ["<p>11 जुलाई</p>", "<p>5 जुलाई</p>",
                                "<p>1 जुलाई</p>", "<p>21 जुलाई</p>"],
                    solution_en: "<p>4.(a) <strong>11th July.</strong> It was established by the United Nations Development Programme (UNDP) in 1989. <strong>Important days - </strong>3 May (World Press Freedom Day), 31 May (World No-Tobacco Day), 20 July (International Moon Day), 31 October (World Cities Day).</p>",
                    solution_hi: "<p>4.(a) <strong>11 जुलाई।</strong> इसकी स्थापना संयुक्त राष्ट्र विकास कार्यक्रम (UNDP) द्वारा 1989 में की गई थी।<strong> महत्वपूर्ण दिवस - </strong>3 मई (विश्व प्रेस स्वतंत्रता दिवस), 31 मई (विश्व तंबाकू निषेध दिवस), 20 जुलाई (अंतर्राष्ट्रीय चंद्रमा दिवस), 31 अक्टूबर (विश्व शहर दिवस)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. When is &ldquo;International Day of Sign Languages&rsquo; celebrated annually across the world?</p>",
                    question_hi: "<p>5. विश्व में प्रतिवर्ष \"अंतर्राष्ट्रीय सांकेतिक भाषा दिवस\" ​​कब मनाया जाता है?</p>",
                    options_en: ["<p>13th September</p>", "<p>13th October</p>", 
                                "<p>23rd October</p>", "<p>23rd September</p>"],
                    options_hi: ["<p>13 सितंबर</p>", "<p>13 अक्टूबर</p>",
                                "<p>23 अक्टूबर</p>", "<p>23 सितंबर</p>"],
                    solution_en: "<p>5.(d) <strong>23rd September. </strong>The International Day of Sign Languages was first celebrated in 2018 as part of the International Week of the Deaf. <strong>International Days and Dates:</strong> World Braille Day (04 January), World Wetlands Day (2 February), World Radio Day (13 February), World Wildlife Day (3 March), World Water Day (22 March).</p>",
                    solution_hi: "<p>5.(d) <strong>23 सितंबर। </strong>अंतर्राष्ट्रीय सांकेतिक भाषा दिवस प्रथम बार 2018 में बधिरों के अंतर्राष्ट्रीय सप्ताह के भाग के रूप में मनाया गया था। <strong>अंतर्राष्ट्रीय दिवस और तिथियाँ :</strong> विश्व ब्रेल दिवस (04 जनवरी), विश्व आर्द्रभूमि दिवस (2 फरवरी), विश्व रेडियो दिवस (13 फरवरी), विश्व वन्यजीव दिवस (3 मार्च), विश्व जल दिवस (22 मार्च)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. When is National Consumer Day observed every year?</p>",
                    question_hi: "<p>6. प्रत्येक वर्ष राष्ट्रीय उपभोक्ता दिवस कब मनाया जाता है?</p>",
                    options_en: ["<p>24th December</p>", "<p>5th June</p>", 
                                "<p>24th October</p>", "<p>5th December</p>"],
                    options_hi: ["<p>24 दिसंबर</p>", "<p>5 जून</p>",
                                "<p>24 अक्टूबर</p>", "<p>5 दिसंबर</p>"],
                    solution_en: "<p>6.(a) <strong>24th December.</strong> On this day in 1986, India\'s Consumer Protection Act came into effect. So, the day is observed to make people aware of their rights as consumers to avoid getting exploited. <strong>15th March -</strong> World Consumers Rights Day. 5th June - World Environment Day. 24th October - United Nation Day, World development information Day. 5th December - International volunteer day for economic and social development.</p>",
                    solution_hi: "<p>6.(a) <strong>24 दिसंबर।</strong> 1986 में आज ही के दिन भारत का उपभोक्ता संरक्षण अधिनियम लागू हुआ था। इसलिए, उपभोक्ताओं के रूप में लोगों को शोषण से बचने के लिए उनके अधिकारों के बारे में जागरूक करने के लिए यह दिन मनाया जाता है। <strong>15 मार्च - </strong>विश्व उपभोक्ता अधिकार दिवस। 5 जून - विश्व पर्यावरण दिवस। 24 अक्टूबर - संयुक्त राष्ट्र दिवस, विश्व विकास सूचना दिवस। 5 दिसंबर - आर्थिक और सामाजिक विकास के लिए अंतर्राष्ट्रीय स्वयंसेवक दिवस।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "7. The United Nations International Day of Women and Girls in Science is annually observed on ____.",
                    question_hi: "7. संयुक्त राष्ट्र विज्ञान में महिलाओं और लड़कियों का अंतर्राष्ट्रीय दिवस प्रतिवर्ष ____ को मनाया जाता है।",
                    options_en: [" 29th January", " 15th January", 
                                " 15th February", " 11th February"],
                    options_hi: [" 29 जनवरी", " 15 जनवरी",
                                " 15 फरवरी", " 11 फरवरी"],
                    solution_en: "<p>7.(d) <strong>11th February.</strong> <strong>The International Day of Women and Girls in Science</strong> is an annual observance adopted by the United Nations General Assembly to promote the full and equal access and participation of women in Science, Technology, Engineering and Mathematics (STEM) fields. <strong>9 January - </strong>NRI (Non-Resident Indian) Day or Pravasi Bharatiya Divas. 25 January - National Voters Day.</p>",
                    solution_hi: "<p>7.(d) <strong>11 फरवरी। विज्ञान के क्षेत्र में महिलाओं और लड़कियों का अंतर्राष्ट्रीय दिवस,</strong>संयुक्त राष्ट्र महासभा द्वारा महिलाओं की पूर्ण और समान पहुंच एवं भागीदारी को बढ़ावा देने के लिए प्रौद्योगिकी, इंजीनियरिंग और गणित के (STEM) क्षेत्रों में अपनाया गया एक वार्षिक उत्सव है। <strong>9 जनवरी -</strong> NRI (Non-Resident Indian) दिवस या प्रवासी भारतीय दिवस। 25 जनवरी - राष्ट्रीय मतदाता दिवस।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. When is the International Ozone day celebrated?</p>",
                    question_hi: "<p>8. अंतर्राष्ट्रीय ओजोन दिवस कब मनाया जाता है?</p>",
                    options_en: ["<p>16 September</p>", "<p>10 September</p>", 
                                "<p>28 March</p>", "<p>29 March</p>"],
                    options_hi: ["<p>16 सितंबर</p>", "<p>10 सितंबर</p>",
                                "<p>28 मार्च</p>", "<p>29 मार्च</p>"],
                    solution_en: "<p>8.(a) <strong>16 September. </strong>This day marks the signing of the Montreal Protocol on Substances that deplete the Ozone Layer on 16 September 1987. The day was first celebrated on September 16, 1995. 10 September - World Suicide Prevention Day (WSPD). 10 December - Human Rights Day.</p>",
                    solution_hi: "<p>8.(a) <strong>16 सितम्बर।</strong> यह दिन 16 सितंबर 1987 को ओजोन परत को क्षति पहुंचाने वाले पदार्थों के लिए मॉन्ट्रियल प्रोटोकॉल पर हस्ताक्षर करने का प्रतीक है। यह दिवस प्रथम बार 16 सितंबर 1995 को मनाया गया था। 10 सितंबर - विश्व आत्महत्या रोकथाम दिवस (WSPD)। 10 दिसंबर - मानवाधिकार दिवस।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. The International Yoga Day is celebrated on:</p>",
                    question_hi: "<p>9. अंतर्राष्ट्रीय योग दिवस किस दिन मनाया जाता है?</p>",
                    options_en: ["<p>13 June</p>", "<p>21 May</p>", 
                                "<p>24 July</p>", "<p>21 June</p>"],
                    options_hi: ["<p>13 जून</p>", "<p>21 मई</p>",
                                "<p>24 जुलाई</p>", "<p>21 जून</p>"],
                    solution_en: "<p>9.(d)<strong> 21 June.</strong> International Yoga day was proposed by India during the opening of the 69<sup>th</sup> session of the United Nations General Assembly (UNGA), held in 2014. The First Yoga Day was celebrated in 2015. 21<sup>st</sup> May - International Tea Day. 24<sup>th</sup> July - The Central Board of Direct Taxes (CBDT) observed the Income Tax Day. 13<sup>th</sup> June - International Albinism Awareness Day.</p>",
                    solution_hi: "<p>9.(d) <strong>21 जून। </strong>2014 में आयोजित संयुक्त राष्ट्र महासभा (UNGA) के 69वें अधिवेशन के उद्घाटन के दौरान भारत द्वारा अंतर्राष्ट्रीय योग दिवस का प्रस्ताव रखा गया था। प्रथम योगा दिवस 2015 में मनाया गया था। 21 मई - अंतर्राष्ट्रीय चाय दिवस। 24 जुलाई - केंद्रीय प्रत्यक्ष कर बोर्ड (CBDT) ने आयकर दिवस मनाया। 13 जून - अंतर्राष्ट्रीय ऐल्बिनिज़म जागरूकता दिवस।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. On which day is World Day to Combat Desertification and Drought observed?</p>",
                    question_hi: "<p>10. विश्व मरुस्थलीकरण एवं सूखा रोकथाम दिवस किस तिथि को मनाया जाता है?</p>",
                    options_en: ["<p>5th June</p>", "<p>22nd April</p>", 
                                "<p>17th June</p>", "<p>22nd May</p>"],
                    options_hi: ["<p>5 जून</p>", "<p>22 अप्रैल</p>",
                                "<p>17 जून</p>", "<p>22 मई</p>"],
                    solution_en: "<p>10.(c) <strong>17th June. </strong>The World Day to Combat Desertification and Drought is observed every year to promote public awareness of international efforts to combat desertification. International Days and dates - World Wetlands Day (2 February), World Wildlife Day (3 March), World Water Day (22 March), World Environment Day (05 June), World Nature Conservation Day (28 July).</p>",
                    solution_hi: "<p>10.(c) <strong>17 जून। </strong>मरुस्थलीकरण से निपटने के लिए अंतर्राष्ट्रीय प्रयासों के बारे में जन जागरूकता को बढ़ावा देने के लिए प्रत्येक वर्ष विश्व मरुस्थलीकरण और सूखा रोकथाम दिवस मनाया जाता है। अंतर्राष्ट्रीय दिवस और तिथियाँ - विश्व आर्द्रभूमि दिवस (2 फरवरी), विश्व वन्यजीव दिवस (3 मार्च), विश्व जल दिवस (22 मार्च), विश्व पर्यावरण दिवस (05 जून), विश्व प्रकृति संरक्षण दिवस (28 जुलाई)।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11.The Government of India decided to Celebrate the birthday of ________ as &ldquo;Parakram Diwas&rdquo; every year.</p>",
                    question_hi: "<p>11. भारत सरकार ने प्रतिवर्ष ________ के जन्मदिन को \"पराक्रम दिवस\" ​​​​के रूप में मनाने का फैसला किया।</p>",
                    options_en: ["<p>Deen Dayal Upadhyaya</p>", "<p>Subhash Chandra Bose</p>", 
                                "<p>Swami Vivekananda</p>", "<p>Vir Savarkar</p>"],
                    options_hi: ["<p>दीन दयाल उपाध्याय</p>", "<p>सुभाष चंद्र बोस</p>",
                                "<p>स्वामी विवेकानंद</p>", "<p>वीर सावरकर</p>"],
                    solution_en: "<p>11.(b) <strong>Subhash Chandra Bose</strong>. He was born on 23rd January 1897 in Cuttack. He founded the Indian National Army (Azad Hind Fauj) in August 1942.The title \'Netaji\' was given to Subhas Chandra Bose by German and Indian officials at the Special Bureau of India in Berlin. Bose was elected as the President of Congress in 1938 at the Haripura session. He founded the All India Forward Bloc in 1939.</p>",
                    solution_hi: "<p>11.(b) <strong>सुभाष चंद्र बोस।</strong> इनका जन्म 23 जनवरी 1897 को कटक में हुआ था। इन्होंने अगस्त 1942 में भारतीय राष्ट्रीय सेना (आजाद हिंद फौज) की स्थापना की। बर्लिन में भारत के स्पेशल ब्यूरो में जर्मन और भारतीय अधिकारियों द्वारा सुभाष चंद्र बोस को \'नेताजी\' की उपाधि दी गई थी। बोस को 1938 के हरिपुरा अधिवेशन में कांग्रेस का अध्यक्ष चुना गया था। इन्होंने 1939 में ऑल इंडिया फॉरवर्ड ब्लॉक की स्थापना की थी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. When is &lsquo;Human Rights Day&rsquo; annually observed around the world?</p>",
                    question_hi: "<p>12. विश्व में \'मानवाधिकार दिवस\' प्रति वर्ष किस तिथि को मनाया जाता है?</p>",
                    options_en: ["<p>10th December</p>", "<p>20th December</p>", 
                                "<p>23rd March</p>", "<p>2nd October</p>"],
                    options_hi: ["<p>10 दिसंबर</p>", "<p>20 दिसंबर</p>",
                                "<p>23 मार्च</p>", "<p>2 अक्टूबर</p>"],
                    solution_en: "<p>12.(a) <strong>10th December.</strong> This day the United Nations General Assembly adopted, in 1948, the Universal Declaration of Human Rights (UDHR). <strong>Other days: 20th December </strong>- International Human solidarity,<strong> 2nd October -</strong> Mahatma Gandhi&rsquo;s birthday, International day of non-violence,<strong> 22 March -</strong> World Water Day, <strong>23rd March -</strong> World Meteorological Day, <strong>24 March - </strong>World Tuberculosis (TB) Day, <strong>27 March -</strong> World Theatre Day.</p>",
                    solution_hi: "<p>12.(a) <strong>10 दिसंबर।</strong> इस दिन संयुक्त राष्ट्र महासभा ने 1948 में मानव अधिकारों की सार्वभौम घोषणा (UDHR) को अपनाया था। <strong>अन्य दिवस: 20 दिसंबर -</strong> अंतर्राष्ट्रीय मानवीय एकता दिवस, <strong>2 अक्टूबर - </strong>महात्मा गांधी का जन्मदिन दिवस, अंतर्राष्ट्रीय अहिंसा दिवस, <strong>22 मार्च - </strong>विश्व जल दिवस, <strong>23 मार्च - </strong>विश्व मौसम विज्ञान दिवस, <strong>24 मार्च - </strong>विश्व क्षय रोग (TB) दिवस, <strong>27 मार्च -</strong> विश्व रंगमंच दिवस।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. World Tuberculosis (TB) Day is observed on _________every year.</p>",
                    question_hi: "<p>13. विश्व क्षय रोग (TB) दिवस हर साल _________ को मनाया जाता है।</p>",
                    options_en: ["<p>24 April</p>", "<p>24 March</p>", 
                                "<p>24 September</p>", "<p>24 January</p>"],
                    options_hi: ["<p>24 अप्रैल</p>", "<p>24 मार्च</p>",
                                "<p>24 सितंबर</p>", "<p>24 जनवरी</p>"],
                    solution_en: "<p>13.(b) <strong>24 March. Dr. Robert Koch</strong> in 1882 identified the bacterium (Mycobacterium tuberculosis) that causes tuberculosis.<strong> The first World TB Day </strong>was observed in 1983, with the <strong>theme </strong>- \"Defeat TB : Now and Forever\". <strong>24 April - </strong>National Panchayati Day. <strong>24 January - </strong>National Girl Child Day.</p>",
                    solution_hi: "<p>13.(b) <strong>24 मार्च। डॉ. रॉबर्ट कोच </strong>ने 1882 में उस जीवाणु (माइकोबैक्टीरियम ट्यूबरकुलोसिस) की पहचान की जो तपेदिक का कारण बनता है। <strong>पहला विश्व TB दिवस </strong>1983 में मनाया गया था।, <strong>थीम - \"TB </strong>को हराएं : अभी और हमेशा के लिए\"। <strong>24 अप्रैल - </strong>राष्ट्रीय पंचायती दिवस।<strong> 24 जनवरी - </strong>राष्ट्रीय बालिका दिवस।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. Which of the following days is observed as World Blood Donor Day?</p>",
                    question_hi: "<p>14. निम्नलिखित में से किस दिन को विश्व रक्तदाता दिवस के रूप में मनाया जाता है?</p>",
                    options_en: ["<p>10 June</p>", "<p>14 June</p>", 
                                "<p>12 June</p>", "<p>16 June</p>"],
                    options_hi: ["<p>10 जून</p>", "<p>14 जून</p>",
                                "<p>12 जून</p>", "<p>16 जून</p>"],
                    solution_en: "<p>14.(a) <strong>14 June. </strong>This day is also celebrated as the birthday anniversary of <strong>Karl Landsteiner</strong> (June 14, 1868). This day was Officially designated as an annual event by the World Health Assembly in 2005. <strong>13th August -</strong> World Organ Donation Day. <strong>February 14 - </strong>National Donor Day. International human rights day -<strong> 10 December.</strong></p>",
                    solution_hi: "<p>14.(b) <strong>14 जून। </strong>इस दिन को कार्ल लैंडस्टीनर (14 जून, 1868) की जयंती के रूप में भी मनाया जाता है। इस दिन को आधिकारिक तौर पर 2005 में विश्व स्वास्थ्य सभा द्वारा एक वार्षिक कार्यक्रम के रूप में नामित किया गया था। <strong>13 अगस्त -</strong> विश्व अंग दान दिवस। <strong>14 फरवरी - </strong>राष्ट्रीय दाता (डोनर) दिवस। अंतर्राष्ट्रीय मानवाधिकार दिवस - <strong>10 दिसंबर।</strong></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Bangladesh celebrates its Independence Day on:</p>",
                    question_hi: "<p>15. बांग्लादेश अपना स्वतंत्रता दिवस मनाता है:</p>",
                    options_en: ["<p>20 April</p>", "<p>26 February</p>", 
                                "<p>26 March</p>", "<p>12 March</p>"],
                    options_hi: ["<p>20 April</p>", "<p>26 February</p>",
                                "<p>26 March</p>", "<p>12 March</p>"],
                    solution_en: "<p>15.(c) <strong>26 March.</strong> Bangladesh was proclaimed as an independent nation by Sheikh Mujibur Rahman On 26 March 1971. On the night of <strong>25 March 1971,</strong> the Pakistani army launched \'<strong>Operation Searchlight\'</strong> in which they killed students, intellectuals and civilians in Bangladesh. <strong>1st President -</strong> Sheikh Mujibur Rahman. <strong>1st Prime Minister - </strong>Tajuddin Ahmad.</p>",
                    solution_hi: "<p>15.(c) <strong>26 मार्च।</strong> 26 मार्च 1971 को शेख मुजीबुर रहमान द्वारा बांग्लादेश को एक स्वतंत्र राष्ट्र घोषित किया गया था। <strong>25 मार्च 1971</strong> की रात को, पाकिस्तानी सेना ने <strong>\'ऑपरेशन सर्चलाइट\' </strong>शुरू किया जिसमें उन्होंने बांग्लादेश में छात्रों, बुद्धिजीवियों और नागरिकों की हत्या कर दी।<strong> प्रथम राष्ट्रपति - </strong>शेख मुजीबुर रहमान। <strong>प्रथम प्रधान मंत्री </strong>- ताजुद्दीन अहमद।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>