<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Cambria Math;\">Which of the following statements is correct regarding large dams?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. They have social problems because they displace large number of peasants and tribals without </span><span style=\"font-family: Cambria Math;\">adequate compensation or rehabilitation. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Il. They have Environmental problems because they contribute enormously to deforestation and the loss </span><span style=\"font-family: Cambria Math;\">of biological diversity. </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">1. </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2306;&#2343;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2341;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">I. </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2350;&#2366;&#2332;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2360;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2367;&#2357;&#2366;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2381;&#2351;&#2366;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2310;&#2357;&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2340;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2405;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2381;&#2351;&#2366;&#2357;&#2352;&#2339;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2360;&#2381;&#2351;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2379;&#2306;&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2344;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2344;&#2366;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2376;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2357;&#2367;&#2343;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>Only l</p>\n", "<p>Both l and Il</p>\n", 
                                "<p>Only <span style=\"font-family: Cambria Math;\">ll</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Neither I nor Il</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2405;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2357;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2405;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2405;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\"> <strong>Both I and II. </strong><strong>Advantages of Dams - </strong><span style=\"font-weight: 400;\">&nbsp;Irrigation, tourist attraction, flood control, hydro power generation, etc. </span><strong>Disadvantages of dams -</strong><span style=\"font-weight: 400;\"> High construction cost, unbalanced sedimentation, deforestation, ecological imbalance, endangered aquatic life etc. </span><strong>Oldest Dam - </strong><span style=\"font-weight: 400;\">&nbsp;Kallanai dam (Kaveri river, Tamil Nadu). </span><strong>Highest Dam -</strong><span style=\"font-weight: 400;\"> Tehri Dam (Bhagirathi river, Uttarakhand). </span><strong>Longest Dam -</strong><span style=\"font-weight: 400;\"> Hirakud Dam (Mahanadi, Odisha). </span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">b)</span><strong>I &#2324;&#2352; II &#2342;&#2379;&#2344;&#2379;&#2306; </strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2348;&#2366;&#2306;&#2343;&#2379;&#2306; &#2325;&#2375; &#2354;&#2366;&#2349;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2367;&#2306;&#2330;&#2366;&#2312;, &#2346;&#2352;&#2381;&#2351;&#2335;&#2325; &#2310;&#2325;&#2352;&#2381;&#2359;&#2339;, &#2348;&#2366;&#2338;&#2364; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2339;, &#2332;&#2354; &#2357;&#2367;&#2342;&#2381;&#2351;&#2369;&#2340; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344;, &#2310;&#2342;&#2367;&#2404; </span><strong>&#2348;&#2366;&#2306;&#2343;&#2379;&#2306; &#2325;&#2375; &#2344;&#2369;&#2325;&#2360;&#2366;&#2344; - </strong><span style=\"font-weight: 400;\">&#2313;&#2330;&#2381;&#2330; &#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2354;&#2366;&#2327;&#2340;, &#2309;&#2360;&#2306;&#2340;&#2369;&#2354;&#2367;&#2340; &#2309;&#2357;&#2360;&#2366;&#2342;&#2344;, &#2357;&#2344;&#2379;&#2306; &#2325;&#2368; &#2325;&#2335;&#2366;&#2312;, &#2346;&#2366;&#2352;&#2367;&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;&#2325; &#2309;&#2360;&#2306;&#2340;&#2369;&#2354;&#2344;, &#2360;&#2306;&#2325;&#2335;&#2327;&#2381;&#2352;&#2360;&#2381;&#2340; &#2332;&#2354;&#2368;&#2351; &#2332;&#2368;&#2357; &#2310;&#2342;&#2367;&#2404; </span><strong>&#2360;&#2348;&#2360;&#2375; &#2346;&#2369;&#2352;&#2366;&#2344;&#2366; &#2348;&#2366;&#2306;&#2343;</strong><span style=\"font-weight: 400;\"> - &#2325;&#2354;&#2381;&#2354;&#2344;&#2312; &#2348;&#2366;&#2306;&#2343; (&#2325;&#2366;&#2357;&#2375;&#2352;&#2368; &#2344;&#2342;&#2368;, &#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369;)&#2404; </span><strong>&#2360;&#2348;&#2360;&#2375; &#2314;&#2306;&#2330;&#2366; &#2348;&#2366;&#2306;&#2343; - </strong><span style=\"font-weight: 400;\">&#2335;&#2367;&#2361;&#2352;&#2368; &#2348;&#2366;&#2306;&#2343; (&#2349;&#2366;&#2327;&#2368;&#2352;&#2341;&#2368; &#2344;&#2342;&#2368;, &#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2326;&#2306;&#2337;)&#2404;</span><strong> &#2360;&#2348;&#2360;&#2375; &#2354;&#2306;&#2348;&#2366; &#2348;&#2366;&#2306;&#2343; -</strong><span style=\"font-weight: 400;\"> &#2361;&#2368;&#2352;&#2366;&#2325;&#2369;&#2306;&#2337; &#2348;&#2366;&#2306;&#2343; (&#2350;&#2361;&#2366;&#2344;&#2342;&#2368;, &#2323;&#2337;&#2367;&#2358;&#2366;)&#2404;&nbsp;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2.<span style=\"font-family: Cambria Math;\"> Which among the following companies fired about 300 employees under allegations of moonlighting </span><span style=\"font-family: Cambria Math;\">in September</span><span style=\"font-family: Cambria Math;\"> 2022?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2340;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2344;&#2354;&#2366;&#2311;&#2335;&#2367;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2379;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2361;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2327;&#2349;&#2327;</span><span style=\"font-family: Cambria Math;\"> 300 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2381;&#2350;&#2330;&#2366;&#2352;&#2367;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2381;&#2326;&#2366;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">MindTree</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Amazon India</p>\n", 
                                "<p>Wipro</p>\n", "<p>Microsoft India</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2311;&#2306;&#2337;&#2335;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2350;&#2375;&#2332;&#2364;&#2379;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2346;&#2381;&#2352;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2360;&#2377;&#2347;&#2364;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>&nbsp;Wipro.</strong><span style=\"font-weight: 400;\"> </span><strong>Moonlighting</strong><span style=\"font-weight: 400;\"> means taking up a second job or multiple other work assignments apart from one&rsquo;s full-time job. Wipro Limited is a leading technology services and consulting company. </span><strong>Establishment</strong><span style=\"font-weight: 400;\"> - 29 December 1945.&nbsp; </span><strong>Executive Chairman</strong><span style=\"font-weight: 400;\"> - Rishad Premji</span><strong> </strong><span style=\"font-weight: 400;\">(As of April 2023).&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">c)</span><strong>&#2357;&#2367;&#2346;&#2381;&#2352;&#2379;&#2404; </strong><span style=\"font-weight: 400;\">&nbsp;</span><strong>&#2350;&#2370;&#2344;&#2354;&#2366;&#2311;&#2335;&#2367;&#2306;&#2327;</strong><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2361;&#2376; &#2319;&#2325; &#2346;&#2370;&#2352;&#2381;&#2339;&#2325;&#2366;&#2354;&#2367;&#2325; &#2344;&#2380;&#2325;&#2352;&#2368; &#2325;&#2375; &#2309;&#2354;&#2366;&#2357;&#2366; &#2342;&#2370;&#2360;&#2352;&#2368; &#2344;&#2380;&#2325;&#2352;&#2368; &#2351;&#2366; &#2325;&#2312; &#2309;&#2344;&#2381;&#2351; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2352;&#2344;&#2366;&#2404; &#2357;&#2367;&#2346;&#2381;&#2352;&#2379; &#2354;&#2367;&#2350;&#2367;&#2335;&#2375;&#2337; &#2319;&#2325; &#2346;&#2381;&#2352;&#2350;&#2369;&#2326; &#2346;&#2381;&#2352;&#2380;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;&#2368; &#2360;&#2375;&#2357;&#2366; &#2324;&#2352; &#2360;&#2354;&#2366;&#2361;&#2325;&#2366;&#2352;&#2368; &#2325;&#2306;&#2346;&#2344;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; -</strong><span style=\"font-weight: 400;\"> 29 &#2342;&#2367;&#2360;&#2306;&#2348;&#2352; 1945 | </span><strong>&#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2366;&#2352;&#2368; &#2330;&#2375;&#2351;&#2352;&#2350;&#2376;&#2344;</strong><span style=\"font-weight: 400;\"> - &#2352;&#2367;&#2358;&#2342; &#2346;&#2381;&#2352;&#2375;&#2350;&#2332;&#2368; (&#2309;&#2346;&#2381;&#2352;&#2376;&#2354; 2023 &#2340;&#2325;)&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">Which of the following two teams played in the final match of season 8 of PKL? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3.</span><span style=\"font-family: Cambria Math;\"> PKL </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> 8</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2364;&#2366;&#2311;&#2344;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2376;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2368;&#2350;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2368;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2325;&#2364;&#2366;&#2348;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2310;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Dabang</span><span style=\"font-family: Cambria Math;\"> Delhi vs. Patna Pirates </span></p>\n", "<p>Bengal Warriors vs. Patna Pirates</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Dabang</span><span style=\"font-family: Cambria Math;\"> Delhi vs. Bengal Warriors </span></p>\n", "<p><span style=\"font-family: Cambria Math;\">Dabang</span><span style=\"font-family: Cambria Math;\"> Delhi vs. Bengaluru Bulls</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2348;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2335;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2311;&#2352;&#2375;&#2335;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2327;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2377;&#2352;&#2367;&#2351;&#2352;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2335;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2311;&#2352;&#2375;&#2335;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2348;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2327;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2377;&#2352;&#2367;&#2351;&#2352;&#2381;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2348;&#2306;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2306;&#2327;&#2354;&#2369;&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2369;&#2354;&#2381;&#2360;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Dabang Delhi vs. Patna Pirates.</strong><span style=\"font-weight: 400;\"> </span><strong>PKL</strong><span style=\"font-weight: 400;\"> - Pro Kabbadi league. Dabang Delhi beat three-time champion (maximum) Patna Pirates (37-36) in the final of the Pro Kabaddi League Season 8 in Bengaluru to win the title for the first time. In </span><strong>2014 </strong><span style=\"font-weight: 400;\">the Pro Kabaddi League was founded in India and the winner of the 1st season was Jaipur Pink Panthers. </span><strong>Season 9 Winner</strong><span style=\"font-weight: 400;\"> - Jaipur Pink Panthers.</span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">a)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2342;&#2348;&#2306;&#2327; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; &#2348;&#2344;&#2366;&#2350; &#2346;&#2335;&#2344;&#2366; &#2346;&#2366;&#2311;&#2352;&#2375;&#2335;&#2381;&#2360; &#2404; </strong><strong>&nbsp;PKL</strong><span style=\"font-weight: 400;\"> - &#2346;&#2381;&#2352;&#2379; &#2325;&#2348;&#2337;&#2381;&#2337;&#2368; &#2354;&#2368;&#2327;&#2404; &#2348;&#2375;&#2306;&#2327;&#2354;&#2369;&#2352;&#2369; &#2350;&#2375;&#2306; &#2346;&#2381;&#2352;&#2379; &#2325;&#2348;&#2337;&#2381;&#2337;&#2368; &#2354;&#2368;&#2327; &#2360;&#2368;&#2332;&#2344; &#2325;&#2375; 8&#2357;&#2375;&#2306; &#2360;&#2306;&#2360;&#2381;&#2325;&#2352;&#2339; &#2325;&#2375; &#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2376;&#2330; &#2350;&#2375;&#2306; &#2342;&#2348;&#2306;&#2327; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; &#2344;&#2375; &#2340;&#2368;&#2344; &#2348;&#2366;&#2352; &#2325;&#2368; &#2330;&#2376;&#2306;&#2346;&#2367;&#2351;&#2344; (&#2309;&#2343;&#2367;&#2325;&#2340;&#2350;) &#2346;&#2335;&#2344;&#2366; &#2346;&#2366;&#2311;&#2352;&#2375;&#2335;&#2381;&#2360; (37-36) &#2325;&#2379; &#2361;&#2352;&#2366;&#2325;&#2352; &#2346;&#2361;&#2354;&#2368; &#2348;&#2366;&#2352; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2366;&#2404;&nbsp; &#2346;&#2381;&#2352;&#2379; &#2325;&#2348;&#2337;&#2381;&#2337;&#2368; &#2354;&#2368;&#2327; &#2325;&#2368; &#2360;&#2381;&#2341;&#2366;&#2346;&#2344;&#2366; </span><strong>2014 </strong><span style=\"font-weight: 400;\">&#2350;&#2375;&#2306; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2361;&#2369;&#2312; &#2341;&#2368; &#2324;&#2352; &#2346;&#2361;&#2354;&#2375; &#2360;&#2368;&#2332;&#2364;&#2344; &#2325;&#2375; &#2357;&#2367;&#2332;&#2375;&#2340;&#2366; &#2332;&#2351;&#2346;&#2369;&#2352; &#2346;&#2367;&#2306;&#2325; &#2346;&#2376;&#2306;&#2341;&#2352;&#2381;&#2360; &#2341;&#2375;&#2404; </span><strong>&#2360;&#2368;&#2332;&#2344; 9 &#2357;&#2367;&#2332;&#2375;&#2340;&#2366;</strong><span style=\"font-weight: 400;\"> - &#2332;&#2351;&#2346;&#2369;&#2352; &#2346;&#2367;&#2306;&#2325; &#2346;&#2376;&#2306;&#2341;&#2352;&#2381;&#2360;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4.</span><span style=\"font-family: Cambria Math;\"> Which famous male lawn tennis player retired in September 2022? </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2358;&#2361;&#2370;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2352;&#2369;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2377;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2375;&#2344;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2340;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2377;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2375;&#2344;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2344;&#2381;&#2351;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Roger Federer</p>\n", "<p>Rafael Nadal</p>\n", 
                                "<p>Novak Djokovic</p>\n", "<p>Carlos Alcaraz</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2379;&#2332;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2347;&#2364;&#2375;&#2337;&#2352;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2347;&#2375;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2337;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2379;&#2357;&#2366;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2379;&#2325;&#2379;&#2357;&#2367;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2354;&#2379;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2354;&#2381;&#2325;&#2366;&#2352;&#2375;&#2332;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Roger Federer</strong><span style=\"font-weight: 400;\"> from Switzerland, has won 20 Grand Slam men\'s singles titles including 8 Wimbledon titles. The headquarters of the International Tennis Federation is located in London, England.&nbsp; All India Tennis Association (AITA) headquarters is in New Delhi. </span><strong>Ramanathan Krishnan</strong><span style=\"font-weight: 400;\"> was the first Indian to reach the semi-final in Wimbledon Tennis Championship. </span><strong>Rafael Nadal - </strong><span style=\"font-weight: 400;\">Spanish professional tennis player. </span><strong>Novak Djokovic</strong><span style=\"font-weight: 400;\"> - Serbian professional tennis player.&nbsp;&nbsp;&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><span style=\"font-weight: 400;\">&#2360;&#2381;&#2357;&#2367;&#2335;&#2381;&#2332;&#2352;&#2354;&#2376;&#2306;&#2337; &#2325;&#2375; </span><strong>&#2352;&#2379;&#2332;&#2352; &#2347;&#2364;&#2375;&#2337;&#2352;&#2352;</strong><span style=\"font-weight: 400;\"> &#2344;&#2375; 8 &#2357;&#2367;&#2306;&#2348;&#2354;&#2337;&#2344; &#2326;&#2367;&#2340;&#2366;&#2348; &#2360;&#2361;&#2367;&#2340; 20 &#2327;&#2381;&#2352;&#2376;&#2306;&#2337; &#2360;&#2381;&#2354;&#2376;&#2350; &#2346;&#2369;&#2352;&#2369;&#2359; &#2319;&#2325;&#2354; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2309;&#2306;&#2340;&#2352;&#2381;&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2335;&#2375;&#2344;&#2367;&#2360; &#2350;&#2361;&#2366;&#2360;&#2306;&#2328; </strong><span style=\"font-weight: 400;\">&#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; &#2354;&#2306;&#2342;&#2344;, &#2311;&#2306;&#2327;&#2381;&#2354;&#2376;&#2306;&#2337; &#2350;&#2375;&#2306; &#2360;&#2381;&#2341;&#2367;&#2340; &#2361;&#2376;&#2404; &#2309;&#2326;&#2367;&#2354; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2335;&#2375;&#2344;&#2367;&#2360; &#2360;&#2306;&#2328; (</span><strong>AITA</strong><span style=\"font-weight: 400;\">) &#2325;&#2366; &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; </span><strong>&#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</strong><span style=\"font-weight: 400;\"> &#2350;&#2375;&#2306; &#2361;&#2376;&#2404;&nbsp; &#2357;&#2367;&#2306;&#2348;&#2354;&#2337;&#2344; &#2335;&#2375;&#2344;&#2367;&#2360; &#2330;&#2376;&#2306;&#2346;&#2367;&#2351;&#2344;&#2358;&#2367;&#2346; &#2325;&#2375; &#2360;&#2375;&#2350;&#2368;&#2347;&#2366;&#2311;&#2344;&#2354; &#2350;&#2375;&#2306; &#2346;&#2361;&#2369;&#2306;&#2330;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2346;&#2361;&#2354;&#2375; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; </span><strong>&#2352;&#2366;&#2350;&#2344;&#2366;&#2341;&#2344; &#2325;&#2371;&#2359;&#2381;&#2339;&#2344; </strong><span style=\"font-weight: 400;\">&#2341;&#2375;&#2404; </span><strong>&#2352;&#2366;&#2347;&#2375;&#2354; &#2344;&#2337;&#2366;&#2354;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2381;&#2346;&#2375;&#2344;&#2367;&#2358; &#2346;&#2375;&#2358;&#2375;&#2357;&#2352; &#2335;&#2375;&#2344;&#2367;&#2360; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368;&#2404; </span><strong>&#2344;&#2379;&#2357;&#2366;&#2325; &#2332;&#2379;&#2325;&#2379;&#2357;&#2367;&#2330;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2352;&#2381;&#2348;&#2367;&#2351;&#2366;&#2312; &#2346;&#2375;&#2358;&#2375;&#2357;&#2352; &#2335;&#2375;&#2344;&#2367;&#2360; &#2326;&#2367;&#2354;&#2366;&#2337;&#2364;&#2368;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5. </span><span style=\"font-family: Cambria Math;\">In India, what is the total length of the coastline of the mainland, including Andaman and Nicobar and&nbsp; </span><span style=\"font-family: Cambria Math;\">Lakshadweep?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2337;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2325;&#2379;&#2348;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2325;&#2381;&#2359;&#2342;&#2381;&#2357;&#2368;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2369;&#2326;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2370;&#2350;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2350;&#2369;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2306;&#2348;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>8,514.4 km</p>\n", "<p>7,818.7 km</p>\n", 
                                "<p>7,516.6 km</p>\n", "<p>8,123.1 km</p>\n"],
                    options_hi: ["<p>8,514.4 km</p>\n", "<p>7,818.7 km</p>\n",
                                "<p>7,516.6 km</p>\n", "<p>8,123.1 km</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">c) </span><strong>7,516.6 km.</strong><span style=\"font-weight: 400;\"> India land boundary - 15,200 km. Length of Coastline of the Indian mainland - 6100 km. State with the longest coastline - Gujarat (1214 km), State with the second longest coastline - Andhra Pradesh (974 km). Andaman and Nicobar Islands coastline - 1962 km.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><span style=\"font-weight: 400;\">&nbsp;</span><strong>7,516.6 &#2325;&#2367;&#2350;&#2368;</strong><span style=\"font-weight: 400;\">&#2404; </span><span style=\"font-weight: 400;\">&#2349;&#2366;&#2352;&#2340; &#2325;&#2368; &#2360;&#2381;&#2341;&#2354;&#2368;&#2351; &#2360;&#2368;&#2350;&#2366; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312;&nbsp; - 15,200 &#2325;&#2367;&#2350;&#2368;&#2404; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2350;&#2369;&#2326;&#2381;&#2351; &#2349;&#2370;&#2350;&#2367; &#2325;&#2368; &#2340;&#2335;&#2352;&#2375;&#2326;&#2366; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312; - 6100 &#2325;&#2367;.&#2350;&#2368;&#2404; &#2360;&#2348;&#2360;&#2375; &#2354;&#2306;&#2348;&#2368; &#2340;&#2335;&#2352;&#2375;&#2326;&#2366; &#2357;&#2366;&#2354;&#2366; &#2352;&#2366;&#2332;&#2381;&#2351; - &#2327;&#2369;&#2332;&#2352;&#2366;&#2340; (1214 &#2325;&#2367;&#2350;&#2368;), &#2342;&#2370;&#2360;&#2352;&#2368; &#2360;&#2348;&#2360;&#2375; &#2354;&#2306;&#2348;&#2368; &#2340;&#2335;&#2352;&#2375;&#2326;&#2366; &#2357;&#2366;&#2354;&#2366; &#2352;&#2366;&#2332;&#2381;&#2351; - &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358; (974 &#2325;&#2367;&#2350;&#2368;)&#2404; &#2309;&#2306;&#2337;&#2350;&#2366;&#2344; &#2324;&#2352; &#2344;&#2367;&#2325;&#2379;&#2348;&#2366;&#2352; &#2342;&#2381;&#2357;&#2368;&#2346; &#2360;&#2350;&#2370;&#2361; &#2325;&#2368; &#2340;&#2335; &#2352;&#2375;&#2326;&#2366; - 1962 &#2325;&#2367;&#2350;&#2368;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">Who is famously known as sitar maestro in India? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2340;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2381;&#2340;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2340;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2319;&#2360;&#2381;&#2335;&#2381;&#2352;&#2379;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Maharshi Patanjali</p>\n", "<p>Pt. Ravi Shankar</p>\n", 
                                "<p>Ustad Bismillah khan</p>\n", "<p>Sitara Devi</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2352;&#2381;&#2359;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2340;&#2306;&#2332;&#2354;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2357;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2306;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2381;&#2340;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2360;&#2381;&#2350;&#2367;&#2354;&#2381;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2364;&#2366;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2340;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2357;&#2368;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Pandit Ravi Shankar</strong><span style=\"font-weight: 400;\">.</span><strong> Awards</strong><span style=\"font-weight: 400;\"> - Bharat Ratna (1999), Padma Bhushan (1967), Padma Vibhushan (1981), Ramon Magsaysay Award (1992).&nbsp; </span><strong>Maharshi Patanjali - </strong><span style=\"font-weight: 400;\">The Father of Yoga. </span><strong>Ustad Bismillah khan (</strong><span style=\"font-weight: 400;\">Shehnai Vadak) </span><strong>Awards</strong><span style=\"font-weight: 400;\"> - Bharat Ratna (2001), Padma Shri (1961), Padma Bhushan (1968), Padma Vibhushan (1980). </span><strong>Sitara Devi - </strong><span style=\"font-weight: 400;\">Classical Kathak Dancer.</span></p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>&#2346;&#2306;. &#2352;&#2357;&#2367; &#2358;&#2306;&#2325;&#2352;</strong><span style=\"font-weight: 400;\"> &#2404; </span><strong>&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; -</strong><span style=\"font-weight: 400;\"> &#2349;&#2366;&#2352;&#2340; &#2352;&#2340;&#2381;&#2344; (1999), &#2346;&#2342;&#2381;&#2350; &#2349;&#2370;&#2359;&#2339; (1967), &#2346;&#2342;&#2381;&#2350; &#2357;&#2367;&#2349;&#2370;&#2359;&#2339; (1981), &#2352;&#2375;&#2350;&#2344; &#2350;&#2376;&#2327;&#2381;&#2360;&#2375;&#2360;&#2375; &#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352; (1992)&#2404; </span><strong>&#2350;&#2361;&#2352;&#2381;&#2359;&#2367; &#2346;&#2340;&#2306;&#2332;&#2354;&#2367;</strong><span style=\"font-weight: 400;\"> - &#2351;&#2379;&#2327; &#2325;&#2375; &#2332;&#2344;&#2325; &#2404;&nbsp; </span><strong>&#2313;&#2360;&#2381;&#2340;&#2366;&#2342; &#2348;&#2367;&#2360;&#2381;&#2350;&#2367;&#2354;&#2381;&#2354;&#2366;&#2361; &#2326;&#2366;&#2344;</strong><span style=\"font-weight: 400;\"> (&#2358;&#2361;&#2344;&#2366;&#2312; &#2357;&#2366;&#2342;&#2325;) </span><strong>&#2346;&#2369;&#2352;&#2360;&#2381;&#2325;&#2366;&#2352;</strong><span style=\"font-weight: 400;\"> - &#2349;&#2366;&#2352;&#2340; &#2352;&#2340;&#2381;&#2344; (2001), &#2346;&#2342;&#2381;&#2350; &#2358;&#2381;&#2352;&#2368; (1961), &#2346;&#2342;&#2381;&#2350; &#2349;&#2370;&#2359;&#2339; (1968), &#2346;&#2342;&#2381;&#2350; &#2357;&#2367;&#2349;&#2370;&#2359;&#2339; (1980)&#2404;</span><strong> &#2360;&#2367;&#2340;&#2366;&#2352;&#2366; &#2342;&#2375;&#2357;&#2368;</strong><span style=\"font-weight: 400;\"> - &#2358;&#2366;&#2360;&#2381;&#2340;&#2381;&#2352;&#2368;&#2351; &#2325;&#2341;&#2325; &#2344;&#2352;&#2381;&#2340;&#2325;&#2368;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">7. </span><span style=\"font-family: Cambria Math;\">Which Indian financial institution protects investors in a stock market?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">7.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2340;&#2381;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2375;&#2351;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2332;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2357;&#2375;&#2358;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2369;&#2352;&#2325;&#2381;&#2359;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Bombay Stock Exchange</p>\n", "<p>Reserve Bank of India</p>\n", 
                                "<p>Securities and Exchange Board of India</p>\n", "<p>National Stock Exchange</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2377;&#2350;&#2381;&#2348;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2335;&#2377;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2381;&#2360;&#2330;&#2375;&#2306;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2367;&#2332;&#2352;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2376;&#2306;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2344;&#2367;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2379;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;&#2358;&#2344;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2335;&#2377;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;&#2381;&#2360;&#2330;&#2375;&#2306;&#2332;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>Securities and Exchange Board of India (SEBI)</strong><span style=\"font-weight: 400;\"> was constituted as a non-statutory body on April 12, 1988.</span><strong> Chairperson - </strong><span style=\"font-weight: 400;\">Ms. Madhabi Puri Buch (as of April 2023). SEBI headquarters - Mumbai. Headquarters of Bombay Stock Exchange (</span><strong>1875</strong><span style=\"font-weight: 400;\">), Reserve Bank of India (</span><strong>1935</strong><span style=\"font-weight: 400;\">) and National Stock Exchange (</span><strong>1992</strong><span style=\"font-weight: 400;\">)</span><strong> &ndash; Mumbai</strong></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-weight: 400;\">&nbsp;</span><strong>&#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2346;&#2381;&#2352;&#2340;&#2367;&#2349;&#2370;&#2340;&#2367; &#2324;&#2352; &#2357;&#2367;&#2344;&#2367;&#2350;&#2351; &#2348;&#2379;&#2352;&#2381;&#2337; (SEBI)</strong><span style=\"font-weight: 400;\"> &#2325;&#2366; &#2327;&#2336;&#2344; 12 &#2309;&#2346;&#2381;&#2352;&#2376;&#2354;, 1988 &#2325;&#2379; &#2319;&#2325; &#2327;&#2376;&#2352;-&#2360;&#2366;&#2306;&#2357;&#2367;&#2343;&#2367;&#2325; &#2344;&#2367;&#2325;&#2366;&#2351; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; SEBI &#2325;&#2368; &#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359; - </span><strong>&#2350;&#2366;&#2343;&#2357;&#2368; &#2346;&#2369;&#2352;&#2368; &#2348;&#2369;&#2330; </strong><span style=\"font-weight: 400;\">(&#2309;&#2346;&#2381;&#2352;&#2376;&#2354; 2023 &#2340;&#2325;)&#2404; </span><strong>SEBI &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351;</strong><span style=\"font-weight: 400;\"> - &#2350;&#2369;&#2306;&#2348;&#2312;&#2404; &#2348;&#2377;&#2350;&#2381;&#2348;&#2375; &#2360;&#2381;&#2335;&#2377;&#2325; &#2319;&#2325;&#2381;&#2360;&#2330;&#2375;&#2306;&#2332; (</span><strong>1875</strong><span style=\"font-weight: 400;\">) , &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2352;&#2367;&#2332;&#2352;&#2381;&#2357; &#2348;&#2376;&#2306;&#2325; (</span><strong>1935</strong><span style=\"font-weight: 400;\">) &#2324;&#2352; &#2344;&#2375;&#2358;&#2344;&#2354; &#2360;&#2381;&#2335;&#2377;&#2325; &#2319;&#2325;&#2381;&#2360;&#2330;&#2375;&#2306;&#2332; (</span><strong>1992</strong><span style=\"font-weight: 400;\">) &#2325;&#2375; &#2350;&#2369;&#2326;&#2381;&#2351;&#2366;&#2354;&#2351; - </span><strong>&#2350;&#2369;&#2306;&#2348;&#2312; </strong><span style=\"font-weight: 400;\">&#2404;&nbsp;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> Which of the following was the capital of </span><span style=\"font-family: Cambria Math;\">Pallavas</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">8.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2354;&#2381;&#2354;&#2357;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2343;&#2366;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Aihole</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Puhar</p>\n", 
                                "<p>Raichur Doab</p>\n", "<p>Kanchipuram</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2320;&#2361;&#2379;&#2354;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2361;&#2366;&#2352;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2351;&#2330;&#2370;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;&#2310;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2306;&#2330;&#2368;&#2346;&#2369;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Kanchipuram</strong><span style=\"font-weight: 400;\">. Founder of Pallava dynasty (275 CE to 897 CE) - Simhavishnu. Pallava empire covered the southern part of India mainly Tamilnadu state. </span><strong>Mahendravarman I </strong><span style=\"font-weight: 400;\">and</span><strong> Narasimhavarman I</strong><span style=\"font-weight: 400;\"> were the greatest rulers of the Pallava dynasty.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">d) </span><strong>&#2325;&#2366;&#2306;&#2330;&#2368;&#2346;&#2369;&#2352;&#2350;</strong><span style=\"font-weight: 400;\">&#2404; &#2346;&#2354;&#2381;&#2354;&#2357; &#2357;&#2306;&#2358; &#2325;&#2375; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2346;&#2325; (275 &#2312;. &#2360;&#2375; 897 &#2312;.) - &#2360;&#2367;&#2306;&#2361;&#2357;&#2367;&#2359;&#2381;&#2339;&#2369; &#2404; &#2346;&#2354;&#2381;&#2354;&#2357; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351; &#2344;&#2375; &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2342;&#2325;&#2381;&#2359;&#2367;&#2339;&#2368; &#2349;&#2366;&#2327; &#2350;&#2375;&#2306; &#2350;&#2369;&#2326;&#2381;&#2351; &#2352;&#2370;&#2346; &#2360;&#2375; &#2340;&#2350;&#2367;&#2354;&#2344;&#2366;&#2337;&#2369; &#2352;&#2366;&#2332;&#2381;&#2351; &#2340;&#2325; &#2347;&#2376;&#2354;&#2366; &#2361;&#2369;&#2310; &#2341;&#2366;&#2404; </span><strong>&#2350;&#2361;&#2375;&#2344;&#2381;&#2342;&#2381;&#2352;&#2357;&#2352;&#2381;&#2350;&#2344; &#2346;&#2381;&#2352;&#2341;&#2350;</strong><span style=\"font-weight: 400;\"> &#2324;&#2352; </span><strong>&#2344;&#2352;&#2360;&#2367;&#2306;&#2361;&#2357;&#2352;&#2381;&#2350;&#2344; &#2346;&#2381;&#2352;&#2341;&#2350;</strong><span style=\"font-weight: 400;\"> &#2346;&#2354;&#2381;&#2354;&#2357; &#2357;&#2306;&#2358; &#2325;&#2375; &#2360;&#2348;&#2360;&#2375; &#2350;&#2361;&#2366;&#2344; &#2358;&#2366;&#2360;&#2325; &#2341;&#2375;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">9. </span><span style=\"font-family: Cambria Math;\">The Industrial Policy Resolution of 1956 formed the basis of the__________.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">9.</span><span style=\"font-family: Cambria Math;\"> 1956 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2360;&#2381;&#2340;&#2366;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> __________ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2351;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>Fifth Five Year plan</p>\n", "<p>Second Five Year plan</p>\n", 
                                "<p>Fourth Five Year plan</p>\n", "<p>Third Five Year plan</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2305;&#2330;&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2330;&#2340;&#2369;&#2352;&#2381;&#2341;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2371;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2379;&#2332;&#2344;&#2366;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>Second Five Year plan</strong><span style=\"font-weight: 400;\">. </span><strong>Industrial Resolution Policy 1956 -</strong><span style=\"font-weight: 400;\">&nbsp; Based upon the Mahalanobis Model of growth. It increased the participation of the public sector in the Indian economy. The </span><strong>Planning Commission</strong><span style=\"font-weight: 400;\"> was replaced by NITI ( National Institution for Transforming India) Aayog in 2015. NITI Aayog </span><strong>Chairman</strong><span style=\"font-weight: 400;\"> - Prime Minister. CEO - B.V.R. Subrahmanyam. Vice Chairman - Suman Bery (as of April 2023).&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-weight: 400;\">&nbsp;</span><strong>&#2342;&#2381;&#2357;&#2367;&#2340;&#2368;&#2351; &#2346;&#2306;&#2330;&#2357;&#2352;&#2381;&#2359;&#2368;&#2351; &#2351;&#2379;&#2332;&#2344;&#2366;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2324;&#2342;&#2381;&#2351;&#2379;&#2327;&#2367;&#2325; &#2360;&#2306;&#2325;&#2354;&#2381;&#2346; &#2344;&#2368;&#2340;&#2367; </strong><strong>1956</strong><span style=\"font-weight: 400;\">: &#2357;&#2367;&#2325;&#2366;&#2360; &#2325;&#2375; &#2350;&#2361;&#2366;&#2354;&#2344;&#2379;&#2348;&#2367;&#2360; &#2350;&#2377;&#2337;&#2354; &#2346;&#2352; &#2310;&#2343;&#2366;&#2352;&#2367;&#2340; &#2361;&#2376; I &#2311;&#2360;&#2344;&#2375; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2309;&#2352;&#2381;&#2341;&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366; &#2350;&#2375;&#2306; &#2360;&#2366;&#2352;&#2381;&#2357;&#2332;&#2344;&#2367;&#2325; &#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352; &#2325;&#2368; &#2349;&#2366;&#2327;&#2368;&#2342;&#2366;&#2352;&#2368; &#2325;&#2379; &#2348;&#2338;&#2364;&#2366;&#2351;&#2366;&#2404; </span><strong>&#2351;&#2379;&#2332;&#2344;&#2366; &#2310;&#2351;&#2379;&#2327;</strong><span style=\"font-weight: 400;\"> &#2325;&#2379; 2015 &#2350;&#2375;&#2306; NITI ( &#2344;&#2375;&#2358;&#2344;&#2354; &#2311;&#2306;&#2360;&#2381;&#2335;&#2368;&#2335;&#2370;&#2358;&#2344; &#2347;&#2377;&#2352; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2377;&#2352;&#2381;&#2350;&#2367;&#2306;&#2327; &#2311;&#2306;&#2337;&#2367;&#2351;&#2366;) &#2310;&#2351;&#2379;&#2327; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340; &#2325;&#2367;&#2351;&#2366; &#2327;&#2351;&#2366; &#2341;&#2366;&#2404; &#2344;&#2368;&#2340;&#2367; &#2310;&#2351;&#2379;&#2327; &#2325;&#2375; </span><strong>&#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359;</strong><span style=\"font-weight: 400;\"> - &#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368;&#2404; </span><strong>CEO</strong><span style=\"font-weight: 400;\"> - &#2348;&#2368;&#2357;&#2368;&#2310;&#2352; &#2360;&#2369;&#2348;&#2381;&#2352;&#2350;&#2339;&#2381;&#2351;&#2350;</span><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2313;&#2346;&#2366;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359;</strong><span style=\"font-weight: 400;\"> - &#2360;&#2369;&#2350;&#2344; &#2348;&#2375;&#2352;&#2368; (&#2309;&#2346;&#2381;&#2352;&#2376;&#2354; 2023 &#2340;&#2325;)&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Cambria Math;\">Which of the following colloid is an example of foam? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">10. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2354;&#2366;&#2311;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2398;&#2379;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2366;&#2361;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Sponge</p>\n", "<p>Butter</p>\n", 
                                "<p>Cheese</p>\n", "<p>Mist</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2346;&#2306;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2325;&#2381;&#2326;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2330;&#2368;&#2332;&#2364;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2361;&#2366;&#2360;&#2366;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">a) </span><strong>Sponge. </strong><span style=\"font-weight: 400;\">A </span><strong>colloid </strong><span style=\"font-weight: 400;\">is a mixture in which one substance microscopically dispersed insoluble particles are suspended throughout other substances. A colloid is a heterogeneous mixture. </span><strong>True Solution</strong><span style=\"font-weight: 400;\"> is a homogeneous mixture of two or more substances in which a substance dissolved (solute) in solvent has the particle size of less than 10</span><span style=\"font-weight: 400;\">-9</span><span style=\"font-weight: 400;\"> m or 1 nm. Example - Simple solution of sugar in water. A </span><strong>suspension</strong><span style=\"font-weight: 400;\"> is a heterogeneous mixture of two or more substances. In suspension, the particles are suspended throughout the solution in bulk and can be easily seen by naked eyes.</span></p>\n",
                    solution_hi: "<p>10.(a)<strong>&#2360;&#2381;&#2346;&#2306;&#2332;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2325;&#2379;&#2354;&#2366;&#2311;&#2337;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2320;&#2360;&#2366; &#2350;&#2367;&#2358;&#2381;&#2352;&#2339; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2319;&#2325; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; &#2350;&#2375;&#2306; &#2360;&#2370;&#2325;&#2381;&#2359;&#2381;&#2350; &#2352;&#2370;&#2346; &#2360;&#2375; &#2348;&#2367;&#2326;&#2352;&#2375; &#2309;&#2328;&#2369;&#2354;&#2344;&#2358;&#2368;&#2354; &#2325;&#2339;&#2379;&#2306; &#2325;&#2379; &#2309;&#2344;&#2381;&#2351; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341;&#2379;&#2306; &#2350;&#2375;&#2306; &#2344;&#2367;&#2354;&#2306;&#2348;&#2367;&#2340; &#2325;&#2352; &#2342;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2325;&#2379;&#2354;&#2366;&#2311;&#2337; &#2319;&#2325; &#2357;&#2367;&#2359;&#2350;&#2366;&#2306;&#2327; &#2350;&#2367;&#2358;&#2381;&#2352;&#2339; &#2361;&#2376;&#2404;</span><strong> &#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325; &#2357;&#2367;&#2354;&#2351;&#2344;- </strong><span style=\"font-weight: 400;\">&#2351;&#2361;&nbsp; &#2342;&#2379; &#2351;&#2366; &#2342;&#2379; &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341;&#2379;&#2306; &#2325;&#2366; &#2319;&#2325; &#2360;&#2332;&#2366;&#2340;&#2368;&#2351; &#2350;&#2367;&#2358;&#2381;&#2352;&#2339; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2357;&#2367;&#2354;&#2366;&#2351;&#2325; &#2350;&#2375;&#2306; &#2328;&#2369;&#2354;&#2375; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341; (&#2357;&#2367;&#2354;&#2375;&#2351;) &#2325;&#2375; &#2325;&#2339; &#2325;&#2366; &#2310;&#2325;&#2366;&#2352; 10</span><span style=\"font-weight: 400;\">-9</span><span style=\"font-weight: 400;\"> &#2350;&#2368;&#2335;&#2352; &#2351;&#2366; 1 &#2344;&#2376;&#2344;&#2379; &#2350;&#2368;&#2335;&#2352;&nbsp; &#2360;&#2375; &#2325;&#2350; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; - &#2358;&#2352;&#2381;&#2325;&#2352;&#2366; &#2324;&#2352; &#2332;&#2354; &#2325;&#2366; &#2360;&#2366;&#2343;&#2366;&#2352;&#2339; &#2357;&#2367;&#2354;&#2351;&#2344;&#2404; </span><strong>&#2344;&#2367;&#2354;&#2306;&#2348;&#2344; (Suspension) - </strong><span style=\"font-weight: 400;\">&#2342;&#2379; &#2351;&#2366; &#2342;&#2379; &#2360;&#2375; &#2309;&#2343;&#2367;&#2325; &#2346;&#2342;&#2366;&#2352;&#2381;&#2341;&#2379;&#2306; &#2325;&#2366; &#2357;&#2367;&#2359;&#2350; &#2350;&#2367;&#2358;&#2381;&#2352;&#2339; &#2361;&#2376;&#2404; &#2311;&#2360;&#2350;&#2375;&#2306; &#2325;&#2339; ,&#2348;&#2396;&#2368; &#2350;&#2366;&#2340;&#2381;&#2352;&#2366; &#2350;&#2375;&#2306; &#2346;&#2370;&#2352;&#2375; &#2357;&#2367;&#2354;&#2351;&#2344; &#2350;&#2375;&#2306; &#2344;&#2367;&#2354;&#2306;&#2348;&#2367;&#2340; &#2352;&#2361;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2344;&#2327;&#2381;&#2344; &#2310;&#2306;&#2326;&#2379;&#2306; &#2360;&#2375; &#2310;&#2360;&#2366;&#2344;&#2368; &#2360;&#2375; &#2342;&#2375;&#2326;&#2375; &#2332;&#2366; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Cambria Math;\">Which of the following shortcut key is used to switch to print preview in MS-Word 365?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">11. </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2369;&#2306;&#2332;&#2368;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2350;&#2319;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2337;</span><span style=\"font-family: Cambria Math;\"> 365 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2367;&#2306;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2368;&#2357;&#2381;&#2351;&#2370;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Ctrl + Alt + P</p>\n", "<p>Ctrl + Alt + I</p>\n", 
                                "<p>Alt + P</p>\n", "<p>Alt + I</p>\n"],
                    options_hi: ["<p>Ctrl + Alt + P</p>\n", "<p>Ctrl + Alt + I</p>\n",
                                "<p>Alt + P</p>\n", "<p>Alt + I</p>\n"],
                    solution_en: "<p>11.(b) <strong>Ctrl + Alt + I</strong><span style=\"font-weight: 400;\">. </span><strong>Microsoft short keys: Alt+P</strong><span style=\"font-weight: 400;\"> - Open the Layout tab.</span><strong> Alt+F</strong><span style=\"font-weight: 400;\"> - Open the File page, </span><strong>Alt+M</strong><span style=\"font-weight: 400;\"> - Open the Mailings tab, </span><strong>Alt+R</strong><span style=\"font-weight: 400;\"> - Open the Review tab, </span><strong>Alt+W</strong><span style=\"font-weight: 400;\"> - Open the View tab,</span><strong> Ctrl+A</strong><span style=\"font-weight: 400;\"> - Select all document content, </span><strong>Ctrl+E</strong><span style=\"font-weight: 400;\"> - Center align the text, </span><strong>Ctrl+L</strong><span style=\"font-weight: 400;\"> - Align the text to the left.,</span><strong> Ctrl+R</strong><span style=\"font-weight: 400;\"> - Align the text to the right,&nbsp; </span><strong>Ctrl+Alt+S -</strong><span style=\"font-weight: 400;\"> Split the document window.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">b) </span><strong>Ctrl + Alt + I</strong><span style=\"font-weight: 400;\">. </span><strong>&nbsp;&#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379;&#2360;&#2377;&#2347;&#2381;&#2335; &#2358;&#2366;&#2352;&#2381;&#2335; &#2325;&#2368;&#2395;:</strong><span style=\"font-weight: 400;\"> </span><strong>Alt+P</strong><span style=\"font-weight: 400;\"> - &#2354;&#2375;&#2310;&#2313;&#2335; &#2335;&#2376;&#2348; &#2326;&#2379;&#2354;&#2375;&#2306;&#2404; </span><strong>Alt+F</strong><span style=\"font-weight: 400;\"> - &#2347;&#2364;&#2366;&#2311;&#2354; &#2346;&#2375;&#2332; &#2326;&#2379;&#2354;&#2375;&#2306;, </span><strong>Alt+M</strong><span style=\"font-weight: 400;\"> - &#2350;&#2375;&#2354;&#2367;&#2306;&#2327; &#2335;&#2376;&#2348; &#2326;&#2379;&#2354;&#2375;&#2306;,</span><strong> Alt+R</strong><span style=\"font-weight: 400;\"> - &#2352;&#2367;&#2357;&#2381;&#2351;&#2370; &#2335;&#2376;&#2348; &#2326;&#2379;&#2354;&#2375;&#2306;, </span><strong>Alt+W </strong><span style=\"font-weight: 400;\">- &#2357;&#2381;&#2351;&#2370; &#2335;&#2376;&#2348; &#2326;&#2379;&#2354;&#2375;&#2306;, </span><strong>Ctrl+A</strong><span style=\"font-weight: 400;\"> - &#2346;&#2370;&#2352;&#2366; &#2337;&#2366;&#2325;&#2381;&#2351;&#2350;&#2375;&#2344;&#2381;&#2335; &#2325;&#2306;&#2335;&#2375;&#2344;&#2381;&#2335; &#2325;&#2366; &#2330;&#2351;&#2344; &#2325;&#2352;&#2375;&#2306;, </span><strong>Ctrl+E</strong><span style=\"font-weight: 400;\"> - &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2325;&#2379; &#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2350;&#2375;&#2306; &#2360;&#2306;&#2352;&#2375;&#2326;&#2367;&#2340; &#2325;&#2352;&#2375;&#2306;, </span><strong>Ctrl+L</strong><span style=\"font-weight: 400;\"> - &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2325;&#2379; &#2348;&#2366;&#2312;&#2306; &#2323;&#2352; &#2360;&#2306;&#2352;&#2375;&#2326;&#2367;&#2340; &#2325;&#2352;&#2375;&#2306;&#2404;, </span><strong>Ctrl+R</strong><span style=\"font-weight: 400;\"> - &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2325;&#2379; &#2342;&#2366;&#2312;&#2306; &#2323;&#2352; &#2360;&#2306;&#2352;&#2375;&#2326;&#2367;&#2340; &#2325;&#2352;&#2375;&#2306;, </span><strong>Ctrl+Alt+S</strong><span style=\"font-weight: 400;\"> - &#2337;&#2366;&#2325;&#2381;&#2351;&#2350;&#2375;&#2344;&#2381;&#2335; &#2357;&#2367;&#2306;&#2337;&#2379; &#2325;&#2379; &#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340; &#2325;&#2352;&#2375;&#2306;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">12. </span><span style=\"font-family: Cambria Math;\">Which state among the following has the largest reserves of gold ore in India as of 2021?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">12.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 2021 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2379;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2351;&#2360;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2348;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2306;&#2337;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Jharkhand</p>\n", "<p>Rajasthan</p>\n", 
                                "<p>Bihar</p>\n", "<p>Karnataka</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2333;&#2366;&#2352;&#2326;&#2306;&#2337;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Bihar.</strong><span style=\"font-weight: 400;\"> Karnataka (Land of Gold) produces 80% of the gold in India.</span><strong> Three goldfields in India</strong><span style=\"font-weight: 400;\"> - Kolar Gold Field (Karnataka), Kolar Hutti Goldfield (Karnataka) and Ramgiri Gold Fields (Andhra Pradesh). Largest producer of Gold in the world - </span><strong>China</strong><span style=\"font-weight: 400;\">. The largest producers of gold ore - Bihar (44%), Rajasthan (25%), Karnataka (21%), West Bengal (3%) and Jharkhand (2%).</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>&#2348;&#2367;&#2361;&#2366;&#2352;</strong><span style=\"font-weight: 400;\">&#2404;&nbsp; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; (&#2360;&#2379;&#2344;&#2375; &#2325;&#2368; &#2349;&#2370;&#2350;&#2367;\') 80% &#2360;&#2379;&#2344;&#2375; &#2325;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; &#2340;&#2368;&#2344; &#2360;&#2379;&#2344;&#2375; &#2325;&#2368; &#2326;&#2366;&#2344;(&#2327;&#2379;&#2354;&#2381;&#2337;&#2347;&#2368;&#2354;&#2381;&#2337;) </strong><span style=\"font-weight: 400;\">: &#2325;&#2379;&#2354;&#2366;&#2352; &#2327;&#2379;&#2354;&#2381;&#2337; &#2347;&#2368;&#2354;&#2381;&#2337; (&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;), &#2325;&#2379;&#2354;&#2366;&#2352; &#2361;&#2335;&#2381;&#2335;&#2368; &#2327;&#2379;&#2354;&#2381;&#2337;&#2347;&#2368;&#2354;&#2381;&#2337; (&#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325;) &#2324;&#2352; &#2352;&#2366;&#2350;&#2327;&#2367;&#2352;&#2368; &#2327;&#2379;&#2354;&#2381;&#2337;&#2347;&#2368;&#2354;&#2381;&#2337; (&#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;)&#2404; &#2357;&#2367;&#2358;&#2381;&#2357; &#2350;&#2375;&#2306; &#2360;&#2379;&#2344;&#2375; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325; - </span><strong>&#2330;&#2368;&#2344;</strong><span style=\"font-weight: 400;\">&#2404; &#2360;&#2379;&#2344;&#2375; &#2325;&#2375; &#2309;&#2351;&#2360;&#2381;&#2325; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2366; &#2313;&#2340;&#2381;&#2346;&#2366;&#2342;&#2325; - &#2348;&#2367;&#2361;&#2366;&#2352; (44%), &#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344; (25%), &#2325;&#2352;&#2381;&#2344;&#2366;&#2335;&#2325; (21%), &#2346;&#2358;&#2381;&#2330;&#2367;&#2350; &#2348;&#2306;&#2327;&#2366;&#2354; (3%) &#2324;&#2352; &#2333;&#2366;&#2352;&#2326;&#2306;&#2337; (2%)&#2404;</span></p>\r\n<p>&nbsp;</p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Cambria Math;\">Which river of India drains into the Bay of Bengal?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">13. </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2327;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2366;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Mahi</p>\n", "<p>Narmada</p>\n", 
                                "<p>Godavari</p>\n", "<p>Tapi</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2352;&#2381;&#2350;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2379;&#2342;&#2366;&#2357;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>Godavari.</strong><span style=\"font-weight: 400;\"> </span><strong>Rivers that drain into the Bay of Bengal -</strong><span style=\"font-weight: 400;\"> Mahanadi, Ganga, Brahmaputra, Krishna and Kaveri etc. Godavari is the largest peninsular river. </span><strong>Rivers that drains into the Arabian Sea -</strong><span style=\"font-weight: 400;\"> Tapi, Narmada, Sindhu, Purna, and Sabarmati etc.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">c)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2327;&#2379;&#2342;&#2366;&#2357;&#2352;&#2368;&#2404;</strong><span style=\"font-weight: 400;\"> </span><strong>&#2344;&#2342;&#2367;&#2351;&#2366;&#2305; &#2332;&#2379; &#2348;&#2306;&#2327;&#2366;&#2354; &#2325;&#2368; &#2326;&#2366;&#2337;&#2364;&#2368; &#2350;&#2375;&#2306; &#2327;&#2367;&#2352;&#2340;&#2368; &#2361;&#2376;&#2306;</strong><span style=\"font-weight: 400;\"> - &#2350;&#2361;&#2366;&#2344;&#2342;&#2368;, &#2327;&#2306;&#2327;&#2366;, &#2348;&#2381;&#2352;&#2361;&#2381;&#2350;&#2346;&#2369;&#2340;&#2381;&#2352;, &#2325;&#2371;&#2359;&#2381;&#2339;&#2366; &#2324;&#2352; &#2325;&#2366;&#2357;&#2375;&#2352;&#2368; &#2310;&#2342;&#2367;&#2404; &#2327;&#2379;&#2342;&#2366;&#2357;&#2352;&#2368; &#2360;&#2348;&#2360;&#2375; &#2348;&#2337;&#2364;&#2368; &#2346;&#2381;&#2352;&#2366;&#2351;&#2342;&#2381;&#2357;&#2368;&#2346;&#2368;&#2351; &#2344;&#2342;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2309;&#2352;&#2348; &#2360;&#2366;&#2327;&#2352; &#2350;&#2375;&#2306; &#2327;&#2367;&#2352;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2344;&#2342;&#2367;&#2351;&#2366;&#2305;</strong><span style=\"font-weight: 400;\"> - &#2340;&#2366;&#2346;&#2368;, &#2344;&#2352;&#2381;&#2350;&#2342;&#2366;, &#2360;&#2367;&#2306;&#2343;&#2369;, &#2346;&#2370;&#2352;&#2381;&#2339;&#2366; &#2324;&#2352; &#2360;&#2366;&#2348;&#2352;&#2350;&#2340;&#2368; &#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">14. </span><span style=\"font-family: Cambria Math;\">What is used for holding program instructions that can\'t be changed throughout the life of the computer? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">14.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2330;&#2366;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2342;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>ROM</p>\n", "<p>Register</p>\n", 
                                "<p>RAM</p>\n", "<p>Cache</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2352;&#2379;&#2350; (ROM)</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2332;&#2367;&#2360;&#2381;&#2335;&#2352;</span><span style=\"font-family: Cambria Math;\"> (Register) </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2375;&#2350;</span><span style=\"font-family: Cambria Math;\"> (RAM) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2325;&#2376;&#2358; (Cache</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">a)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>ROM</strong><span style=\"font-weight: 400;\">. It is a type of storage medium that permanently stores data. It is a non-volatile computer memory. The various types of ROM are MROM (Masked ROM), PROM (Programmable Read Only Memory), EPROM (Erasable and Programmable Read Only Memory), EEPROM (Electrically Erasable and Programmable Read Only Memory).&nbsp; RAM - Random Access Memory. Cache - A high-speed data storage software.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">a) </span><strong>ROM</strong><span style=\"font-weight: 400;\">&#2404;</span><strong> </strong><span style=\"font-weight: 400;\">&#2351;&#2361; &#2319;&#2325; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2325;&#2366; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2350;&#2366;&#2343;&#2381;&#2351;&#2350; &#2361;&#2376; &#2332;&#2379; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2360;&#2381;&#2341;&#2366;&#2351;&#2368; &#2352;&#2370;&#2346; &#2360;&#2375; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361;&#2368;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2319;&#2325; &#2344;&#2377;&#2344;-&#2357;&#2377;&#2354;&#2375;&#2335;&#2366;&#2311;&#2354; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; &#2361;&#2376;&#2404; </span><strong>ROM &#2325;&#2375; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2361;&#2376;&#2306;</strong><span style=\"font-weight: 400;\">- MROM (&#2350;&#2366;&#2360;&#2381;&#2325;&#2337; &#2352;&#2368;&#2337; &#2323;&#2344;&#2354;&#2368; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368;), PROM (&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2375;&#2348;&#2354; &#2352;&#2368;&#2337; &#2323;&#2344;&#2354;&#2368; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368;), EPROM (&#2311;&#2352;&#2375;&#2332;&#2364;&#2375;&#2348;&#2354; &#2319;&#2306;&#2337; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2375;&#2348;&#2354; &#2352;&#2368;&#2337; &#2323;&#2344;&#2354;&#2368; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368;), EEPROM (&#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2367;&#2325;&#2354;&#2368; &#2311;&#2352;&#2375;&#2332;&#2375;&#2348;&#2354; &#2319;&#2306;&#2337; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;&#2375;&#2348;&#2354; &#2352;&#2368;&#2337; &#2323;&#2344;&#2354;&#2368; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368;)&#2404; </span><strong>RAM </strong><span style=\"font-weight: 400;\">- &#2352;&#2376;&#2306;&#2337;&#2350; &#2319;&#2325;&#2381;&#2360;&#2375;&#2360; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368;&#2404; </span><strong>&#2325;&#2376;&#2358; (Cache)</strong><span style=\"font-weight: 400;\">- &#2319;&#2325; &#2361;&#2366;&#2312;-&#2360;&#2381;&#2346;&#2368;&#2337; &#2337;&#2375;&#2335;&#2366; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Cambria Math;\">The famous monument, Aram Bagh, was built by Babur. It is located in__________.</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">15. </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2350;&#2366;&#2352;&#2325;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2327;</span><span style=\"font-family: Cambria Math;\"> (Aram Bagh), </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2357;&#2366;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2361;</span><span style=\"font-family: Cambria Math;\"> ________</span><span style=\"font-family: Cambria Math;\">_ </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span></p>\n",
                    options_en: ["<p>Delhi<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Uttar<span style=\"font-family: Cambria Math;\"> Pradesh </span></p>\n", 
                                "<p>Madhya<span style=\"font-family: Cambria Math;\"> Pradesh </span></p>\n", "<p>Bihar<span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2354;&#2381;&#2354;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2343;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)</span><span style=\"font-weight: 400;\">&nbsp;</span><strong>Uttar Pradesh</strong><span style=\"font-weight: 400;\">. </span><strong>Aram Bagh</strong><span style=\"font-weight: 400;\"> - Built by Babur in 1528 in Agra. Babur was the founder of the Mughal Empire. </span><strong>War fought by Babur</strong><span style=\"font-weight: 400;\"> - First Battle of Panipat (1526) -&nbsp; Babur defeated Ibrahim Lodi, Battle of Khanwa (1527) - Babur defeated Rana Sanga, Battle of Chanderi (1528) - Babar defeated Madini rai, Battle of Ghaghra (1529) - Babar defeated Afghan. </span><strong>Monuments Built by Babur:</strong><span style=\"font-weight: 400;\"> The avenue garden in Kabul, the Panipat Mosque and the Kabuli Bagh Mosque.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">b)<strong>&#2313;&#2340;&#2381;&#2340;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;&#2404; &#2310;&#2352;&#2366;&#2350; &#2348;&#2366;&#2327;</strong><span style=\"font-weight: 400;\"> -&#2310;&#2327;&#2352;&#2366; &#2350;&#2375;&#2306; 1528 &#2350;&#2375;&#2306; &#2348;&#2366;&#2348;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2367;&#2340;&#2404;&nbsp; &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2313;&#2346;&#2350;&#2361;&#2366;&#2342;&#2381;&#2357;&#2368;&#2346; &#2348;&#2366;&#2348;&#2352;, &#2350;&#2369;&#2327;&#2354; &#2360;&#2366;&#2350;&#2381;&#2352;&#2366;&#2332;&#2381;&#2351; &#2325;&#2366; &#2360;&#2306;&#2360;&#2381;&#2341;&#2366;&#2346;&#2325; &#2341;&#2366;&#2404;</span><strong> &#2348;&#2366;&#2348;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2354;&#2337;&#2364;&#2375; &#2327;&#2319; &#2351;&#2369;&#2342;&#2381;&#2343;</strong><span style=\"font-weight: 400;\"> - &#2346;&#2366;&#2344;&#2368;&#2346;&#2340; &#2325;&#2368; &#2346;&#2361;&#2354;&#2368; &#2354;&#2337;&#2364;&#2366;&#2312; (1526) - &#2348;&#2366;&#2348;&#2352; &#2344;&#2375; &#2311;&#2348;&#2381;&#2352;&#2366;&#2361;&#2367;&#2350; &#2354;&#2379;&#2342;&#2368; &#2325;&#2379; &#2361;&#2352;&#2366;&#2351;&#2366;, &#2326;&#2366;&#2344;&#2357;&#2366; &#2325;&#2368; &#2354;&#2337;&#2364;&#2366;&#2312; (</span><strong>1527</strong><span style=\"font-weight: 400;\">) - &#2348;&#2366;&#2348;&#2352; &#2344;&#2375; &#2352;&#2366;&#2339;&#2366; &#2360;&#2366;&#2306;&#2327;&#2366; &#2325;&#2379; &#2361;&#2352;&#2366;&#2351;&#2366;, &#2330;&#2306;&#2342;&#2375;&#2352;&#2368; &#2325;&#2368; &#2354;&#2337;&#2364;&#2366;&#2312; (</span><strong>1528</strong><span style=\"font-weight: 400;\">) - &#2348;&#2366;&#2348;&#2352; &#2344;&#2375; &#2350;&#2375;&#2342;&#2367;&#2344;&#2368;&#2352;&#2366;&#2351; &#2325;&#2379; &#2361;&#2352;&#2366;&#2351;&#2366;, &#2328;&#2366;&#2328;&#2352;&#2366; &#2325;&#2368; &#2354;&#2337;&#2364;&#2366;&#2312; (</span><strong>1529</strong><span style=\"font-weight: 400;\">) - &#2348;&#2366;&#2348;&#2352; &#2344;&#2375; &#2309;&#2347;&#2327;&#2366;&#2344; &#2325;&#2379; &#2361;&#2352;&#2366;&#2351;&#2366;&#2404;</span><strong> &#2348;&#2366;&#2348;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2344;&#2367;&#2352;&#2381;&#2350;&#2367;&#2340; &#2360;&#2381;&#2350;&#2366;&#2352;&#2325;:</strong><span style=\"font-weight: 400;\"> &#2325;&#2366;&#2348;&#2369;&#2354; &#2350;&#2375;&#2306; \"&#2319;&#2357;&#2375;&#2344;&#2381;&#2351;&#2370; &#2327;&#2366;&#2352;&#2381;&#2337;&#2344;\", &#2346;&#2366;&#2344;&#2368;&#2346;&#2340; &#2350;&#2360;&#2381;&#2332;&#2367;&#2342; &#2324;&#2352; &#2325;&#2366;&#2348;&#2369;&#2354;&#2368; &#2348;&#2366;&#2327; &#2350;&#2360;&#2381;&#2332;&#2367;&#2342;&#2404;</span></span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\"> In which state did PM Narendra Modi laid the foundation stone for Bulk Drug Park in October </span><span style=\"font-family: Cambria Math;\">2022 ?</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">16.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2325;&#2381;&#2335;&#2370;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2343;&#2366;&#2344;&#2350;&#2306;&#2340;&#2381;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2352;&#2375;&#2306;&#2342;&#2381;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2379;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2354;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2381;&#2352;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2366;&#2352;&#2381;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2343;&#2366;&#2352;&#2358;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2326;&#2368;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Bihar</p>\n", "<p>Himachal Pradesh</p>\n", 
                                "<p>Punjab</p>\n", "<p>Uttarakhand</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2361;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2361;&#2367;&#2350;&#2366;&#2330;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2306;&#2332;&#2366;&#2348;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2340;&#2352;&#2366;&#2326;&#2306;&#2337;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">b)<strong>Himachal Pradesh.</strong><span style=\"font-weight: 400;\"> The three states get a bulk drug park:&nbsp; Himachal Pradesh, Gujarat and Andhra Pradesh</span><strong>.&nbsp; Bulk drug park</strong><span style=\"font-weight: 400;\"> - Its objective is to increase the competitiveness of the domestic manufacturing of bulk drugs and decrease import dependence.</span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">16.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>&#2361;&#2367;&#2350;&#2366;&#2330;&#2354; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;</strong><span style=\"font-weight: 400;\">&#2404; &#2340;&#2368;&#2344; &#2352;&#2366;&#2332;&#2381;&#2351;&#2379;&#2306; &#2325;&#2379; &#2348;&#2354;&#2381;&#2325; &#2337;&#2381;&#2352;&#2327; &#2346;&#2366;&#2352;&#2381;&#2325; &#2350;&#2367;&#2354;&#2366; : &#2361;&#2367;&#2350;&#2366;&#2330;&#2354; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;, &#2327;&#2369;&#2332;&#2352;&#2366;&#2340; &#2324;&#2352; &#2310;&#2306;&#2343;&#2381;&#2352; &#2346;&#2381;&#2352;&#2342;&#2375;&#2358;&#2404; </span><strong>&#2348;&#2354;&#2381;&#2325; &#2337;&#2381;&#2352;&#2327; &#2346;&#2366;&#2352;&#2381;&#2325;</strong><span style=\"font-weight: 400;\"> - &#2311;&#2360;&#2325;&#2366; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2348;&#2354;&#2381;&#2325; </span><strong>&#2337;&#2381;&#2352;&#2327; </strong><span style=\"font-weight: 400;\">&#2325;&#2375; &#2328;&#2352;&#2375;&#2354;&#2370; &#2357;&#2367;&#2344;&#2367;&#2352;&#2381;&#2350;&#2366;&#2339; &#2325;&#2368; &#2346;&#2381;&#2352;&#2340;&#2367;&#2360;&#2381;&#2346;&#2352;&#2381;&#2343;&#2366;&#2340;&#2381;&#2350;&#2325;&#2340;&#2366; &#2325;&#2379; &#2348;&#2338;&#2364;&#2366;&#2344;&#2366; &#2324;&#2352; &#2310;&#2351;&#2366;&#2340; &#2346;&#2352; &#2344;&#2367;&#2352;&#2381;&#2349;&#2352;&#2340;&#2366; &#2325;&#2350; &#2325;&#2352;&#2344;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. <span style=\"font-family: Cambria Math;\">Which of the following comes under the Union List of 7th schedule of the Indian constitution? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">17.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> 7</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2360;&#2370;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2328;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2370;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2340;&#2352;&#2381;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Public Health and sanitation</p>\n", "<p>Public order</p>\n", 
                                "<p>Hospitals and dispensaries</p>\n", "<p>Establishment of standards of weight and measure</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2366;&#2360;&#2381;&#2341;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2357;&#2330;&#2381;&#2331;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2379;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2357;&#2360;&#2381;&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2309;&#2360;&#2381;&#2346;&#2340;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2359;&#2343;&#2366;&#2354;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2306;&#2335;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2346;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2351;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span><strong>7th schedule </strong><span style=\"font-weight: 400;\">- division of powers between the Union government and State governments. </span><strong>Article 246</strong><span style=\"font-weight: 400;\"> - three lists in the Seventh Schedule - Union (100 subjects), State (61 subjects) and Concurrent lists (52 subjects).&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">17.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span><strong>&nbsp;&#2360;&#2366;&#2340;&#2357;&#2368;&#2306; &#2309;&#2344;&#2369;&#2360;&#2370;&#2330;&#2368;</strong><span style=\"font-weight: 400;\"> - &#2325;&#2375;&#2306;&#2342;&#2381;&#2352; &#2360;&#2352;&#2325;&#2366;&#2352; &#2324;&#2352; &#2352;&#2366;&#2332;&#2381;&#2351; &#2360;&#2352;&#2325;&#2366;&#2352;&#2379;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2358;&#2325;&#2381;&#2340;&#2367;&#2351;&#2379;&#2306; &#2325;&#2366; &#2357;&#2367;&#2349;&#2366;&#2332;&#2344;&#2404; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 246</strong><span style=\"font-weight: 400;\"> - &#2360;&#2366;&#2340;&#2357;&#2368;&#2306; &#2309;&#2344;&#2369;&#2360;&#2370;&#2330;&#2368; &#2350;&#2375;&#2306; &#2340;&#2368;&#2344; &#2360;&#2370;&#2330;&#2367;&#2351;&#2366;&#2305; - &#2360;&#2306;&#2328; (100 &#2357;&#2367;&#2359;&#2351;), &#2352;&#2366;&#2332;&#2381;&#2351; (61 &#2357;&#2367;&#2359;&#2351;) &#2324;&#2352; &#2360;&#2350;&#2357;&#2352;&#2381;&#2340;&#2368; &#2360;&#2370;&#2330;&#2367;&#2351;&#2366;&#2305; (52 &#2357;&#2367;&#2359;&#2351;)&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Who led the Battle of Plassey on behalf of the British East India Company? </span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">18.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2381;&#2352;&#2367;&#2335;&#2367;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2312;&#2360;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2306;&#2346;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2323;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2354;&#2366;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2369;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;&#2340;&#2371;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2341;&#2366;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Charles Stuart</p>\n", "<p>Warren Hastings</p>\n", 
                                "<p>Robert Clive</p>\n", "<p>James Lancaster</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2330;&#2366;&#2352;&#2381;&#2354;&#2381;&#2360; &#2360;&#2381;&#2335;&#2369;&#2309;&#2352;&#2381;&#2335; (Charles Stuart )</span></p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2357;&#2377;&#2352;&#2375;&#2344; &#2361;&#2375;&#2360;&#2381;&#2335;&#2367;&#2306;&#2327;&#2381;&#2360; (Warren Hastings)</span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2377;&#2348;&#2352;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2354;&#2366;&#2311;&#2357;</span><span style=\"font-family: Cambria Math;\"> (Robert Clive) </span></p>\n", "<p>&nbsp;&#2332;&#2375;&#2350;&#2381;&#2360; &#2354;&#2375;&#2306;&#2325;&#2375;&#2360;&#2381;&#2335;&#2352; (James Lancaster)</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>Robert Clive. Battle of Plassey (1757) -</strong><span style=\"font-weight: 400;\"> Robert Clive defeated the Nawab of Bengal Siraj-ud-daula (West Bengal). </span><strong>&ldquo;Black Hole Tragedy&rdquo; -</strong><span style=\"font-weight: 400;\">&nbsp; 123 Britishers died in a small room. </span><strong>1st Governor-General of Bengal: </strong><span style=\"font-weight: 400;\">Warren Hastings.&nbsp; </span><strong>1st Governor-General of India:</strong><span style=\"font-weight: 400;\"> Lord William Bentinck.</span><strong> 1st Viceroy of India:</strong><span style=\"font-weight: 400;\"> Lord Canning</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">18.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>&#2352;&#2377;&#2348;&#2352;&#2381;&#2335; &#2325;&#2381;&#2354;&#2366;&#2311;&#2357;</strong><span style=\"font-weight: 400;\">&#2404; &#2346;&#2381;&#2354;&#2366;&#2360;&#2368; &#2325;&#2366; &#2351;&#2369;&#2342;&#2381;&#2343; (</span><strong>1757</strong><span style=\"font-weight: 400;\">) - &#2352;&#2377;&#2348;&#2352;&#2381;&#2335; &#2325;&#2381;&#2354;&#2366;&#2311;&#2357; &#2344;&#2375; &#2348;&#2306;&#2327;&#2366;&#2354; &#2325;&#2375; &#2344;&#2357;&#2366;&#2348; &#2360;&#2367;&#2352;&#2366;&#2332;-&#2313;&#2342;-&#2342;&#2380;&#2354;&#2366; (&#2360;&#2367;&#2352;&#2366;&#2332;&#2369;&#2342;&#2381;&#2342;&#2380;&#2354;&#2366;) (&#2346;&#2358;&#2381;&#2330;&#2367;&#2350; &#2348;&#2306;&#2327;&#2366;&#2354;) &#2325;&#2379; &#2361;&#2352;&#2366;&#2351;&#2366;&#2404; \"</span><strong>&#2348;&#2381;&#2354;&#2376;&#2325; &#2361;&#2379;&#2354; &#2340;&#2381;&#2352;&#2366;&#2360;&#2342;&#2368;\"(&#2325;&#2366;&#2354; &#2325;&#2379;&#2336;&#2352;&#2368;)</strong><span style=\"font-weight: 400;\"> - &#2319;&#2325; &#2331;&#2379;&#2335;&#2375; &#2360;&#2375; &#2325;&#2350;&#2352;&#2375; &#2350;&#2375;&#2306; 123 &#2309;&#2306;&#2327;&#2381;&#2352;&#2375;&#2332;&#2379;&#2306; &#2325;&#2368; &#2350;&#2380;&#2340; &#2361;&#2379; &#2327;&#2312; &#2341;&#2368;&#2404; &#2348;&#2306;&#2327;&#2366;&#2354; &#2325;&#2375; &#2346;&#2381;&#2352;&#2341;&#2350; &#2327;&#2357;&#2352;&#2381;&#2344;&#2352;-&#2332;&#2344;&#2352;&#2354;:</span><strong> &#2357;&#2366;&#2352;&#2375;&#2344; &#2361;&#2375;&#2360;&#2381;&#2335;&#2367;&#2306;&#2327;&#2381;&#2360;</strong><span style=\"font-weight: 400;\">&#2404; &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2346;&#2381;&#2352;&#2341;&#2350; &#2327;&#2357;&#2352;&#2381;&#2344;&#2352;-&#2332;&#2344;&#2352;&#2354;: </span><strong>&#2354;&#2377;&#2352;&#2381;&#2337; &#2357;&#2367;&#2354;&#2367;&#2351;&#2350; &#2348;&#2375;&#2306;&#2335;&#2367;&#2306;&#2325;</strong><span style=\"font-weight: 400;\">&#2404; &#2349;&#2366;&#2352;&#2340; &#2325;&#2366; &#2346;&#2381;&#2352;&#2341;&#2350; &#2357;&#2366;&#2351;&#2360;&#2352;&#2366;&#2351;: </span><strong>&#2354;&#2377;&#2352;&#2381;&#2337; &#2325;&#2376;&#2344;&#2367;&#2306;&#2327;&#2404;</strong><span style=\"font-weight: 400;\">&nbsp;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">19. </span><span style=\"font-family: Cambria Math;\">Which Indian writer wrote the famous book \"</span><span style=\"font-family: Cambria Math;\">Mansarovar</span><span style=\"font-family: Cambria Math;\">\"? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">19. </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2326;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2360;&#2367;&#2342;&#2381;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> &ldquo;</span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2360;&#2352;&#2379;&#2357;&#2352;</span><span style=\"font-family: Cambria Math;\">\" </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2326;&#2368;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>Gita<span style=\"font-family: Cambria Math;\"> Sahgal </span></p>\n", "<p>Ram<span style=\"font-family: Cambria Math;\"> Prasad </span><span style=\"font-family: Cambria Math;\">Bismil</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Premchand</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Yashpal<span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2368;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2327;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2360;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2367;&#2360;&#2381;&#2350;&#2367;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2375;&#2350;&#2330;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2358;&#2346;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>Premchand. Mansarovar -</strong><span style=\"font-weight: 400;\"> Short stories of eight-volume series. </span><strong>Books of Mushi Premchand: </strong><span style=\"font-weight: 400;\">Godaan, Idgah, Do Bailon Ki Katha, Kaphan, Nirmala, Gaban, Poos Ki Raat, Sevasadan, Namak Ka Daroga, Panch Parmeshwar etc. </span><strong>Books of Yashpal:</strong><span style=\"font-weight: 400;\"> Jhutha Sach, Meri Teri Uski Baat, Phulo Ka Kurta, Bhookh Ke Teen Din etc.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">19.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>&#2346;&#2381;&#2352;&#2375;&#2350;&#2330;&#2306;&#2342;</strong><span style=\"font-weight: 400;\">&#2404; </span><strong>&#2350;&#2366;&#2344;&#2360;&#2352;&#2379;&#2357;&#2352; </strong><span style=\"font-weight: 400;\">- &#2310;&#2336; &#2326;&#2306;&#2337;&#2379;&#2306; &#2325;&#2368; &#2354;&#2328;&#2369; &#2325;&#2341;&#2366; &#2358;&#2381;&#2352;&#2371;&#2306;&#2326;&#2354;&#2366;&#2404; </span><strong>&#2350;&#2369;&#2358;&#2368; &#2346;&#2381;&#2352;&#2375;&#2350;&#2330;&#2306;&#2342; &#2325;&#2368; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325;&#2375;&#2306;:</strong><span style=\"font-weight: 400;\"> &#2327;&#2379;&#2342;&#2366;&#2344;, &#2312;&#2342;&#2327;&#2366;&#2361;, &#2342;&#2379; &#2348;&#2376;&#2354;&#2379;&#2306; &#2325;&#2368; &#2325;&#2341;&#2366;, &#2325;&#2347;&#2344;, &#2344;&#2367;&#2352;&#2381;&#2350;&#2354;&#2366;, &#2327;&#2348;&#2344;, &#2346;&#2370;&#2360; &#2325;&#2368; &#2352;&#2366;&#2340;, &#2360;&#2375;&#2357;&#2366;&#2360;&#2342;&#2344;, &#2344;&#2350;&#2325; &#2325;&#2366; &#2342;&#2352;&#2379;&#2327;&#2366;, &#2346;&#2306;&#2330; &#2346;&#2352;&#2350;&#2375;&#2358;&#2381;&#2357;&#2352; &#2310;&#2342;&#2367;&#2404; </span><strong>&#2351;&#2358;&#2346;&#2366;&#2354; &#2325;&#2368; &#2346;&#2369;&#2360;&#2381;&#2340;&#2325;&#2375;&#2306;:</strong><span style=\"font-weight: 400;\"> &#2333;&#2370;&#2336;&#2366; &#2360;&#2330;, &#2350;&#2375;&#2352;&#2368; &#2340;&#2375;&#2352;&#2368; &#2313;&#2360;&#2325;&#2368; &#2348;&#2366;&#2340;, &#2347;&#2370;&#2354;&#2379; &#2325;&#2366; &#2325;&#2369;&#2352;&#2381;&#2340;&#2366;, &#2349;&#2369;&#2326; &#2325;&#2375; &#2340;&#2368;&#2344; &#2342;&#2367;&#2344; &#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">20. </span><span style=\"font-family: Cambria Math;\"> At high </span><span style=\"font-family: Cambria Math;\">doses,_</span><span style=\"font-family: Cambria Math;\">_______ kills cancer cells or slows their growth by damaging their DNA. Cancer cells&nbsp; </span><span style=\"font-family: Cambria Math;\">whose DNA is damaged beyond repair, stop dividing or die. When the damaged cells die, they are broken </span><span style=\"font-family: Cambria Math;\">down and removed by the body. </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">20.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2337;&#2364;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2340;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">,_</span><span style=\"font-family: Cambria Math;\">_______ </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2306;&#2360;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2352;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2368;&#2319;&#2344;&#2319;</span><span style=\"font-family: Cambria Math;\"> (DNA) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2371;&#2342;&#2381;&#2343;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2368;&#2350;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2376;&#2306;&#2360;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2367;&#2344;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2337;&#2368;&#2319;&#2344;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2340;&#2367;&#2327;&#2381;&#2352;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;&#2381;&#2361;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2336;&#2368;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2361;&#2368;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2366;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2306;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2359;&#2340;&#2367;&#2327;&#2381;&#2352;&#2360;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;&#2358;&#2367;&#2325;&#2366;&#2319;&#2305;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2352;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2381;&#2357;&#2366;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2325;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;&#2306;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>Chemotherapy</p>\n", "<p>Radiation therapy</p>\n", 
                                "<p>Surgery</p>\n", "<p>Physiotherapy <span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2350;&#2379;&#2341;&#2375;&#2352;&#2375;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> (Chemotherapy) </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2367;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2325;&#2367;&#2340;&#2381;&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> (Radiation therapy) </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2352;&#2381;&#2332;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> (Surgery) </span></p>\n", "<p><span style=\"font-weight: 400;\">&#2349;&#2380;&#2340;&#2367;&#2325; &#2330;&#2367;&#2325;&#2367;&#2340;&#2381;&#2360;&#2366; (Physiotherapy)</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">20.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>Radiation therapy. </strong><span style=\"font-weight: 400;\">The X-ray generating methodology is applied to cancer therapy.</span><strong> </strong><span style=\"font-weight: 400;\">X-rays discovered in 1895 by W. C. Roentgen.</span><strong> Types of radiation therapy: </strong><span style=\"font-weight: 400;\">1. External beam radiation therapy comes from a machine that aims radiation at your cancer. 2. Internal radiation therapy is a treatment in which a source of radiation is put inside your body.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">20.(</span><span style=\"font-family: Cambria Math;\">b)</span><strong>&#2357;&#2367;&#2325;&#2367;&#2352;&#2339; &#2330;&#2367;&#2325;&#2367;&#2340;&#2381;&#2360;&#2366;&#2404;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325;&#2381;&#2360;-&#2352;&#2375; &#2332;&#2344;&#2352;&#2375;&#2335;&#2367;&#2306;&#2327; &#2346;&#2342;&#2381;&#2343;&#2340;&#2367;, &#2325;&#2376;&#2306;&#2360;&#2352; &#2341;&#2375;&#2352;&#2375;&#2346;&#2368; &#2346;&#2352; &#2354;&#2366;&#2327;&#2370; &#2361;&#2379;&#2340;&#2368; &#2361;&#2376;&#2404; </span><strong>&#2357;&#2367;&#2354;&#2361;&#2350; &#2325;&#2377;&#2344;&#2352;&#2376;&#2337; &#2352;&#2379;&#2344;&#2381;&#2335;&#2332;&#2375;&#2344;</strong><span style=\"font-weight: 400;\"> &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; 1895 &#2350;&#2375;&#2306; &#2319;&#2325;&#2381;&#2360;-&#2352;&#2375; &#2325;&#2368; &#2326;&#2379;&#2332; &#2325;&#2368; &#2327;&#2312;&#2404; </span><strong>&#2357;&#2367;&#2325;&#2367;&#2352;&#2339; &#2330;&#2367;&#2325;&#2367;&#2340;&#2381;&#2360;&#2366; &#2325;&#2375; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352;:</strong><span style=\"font-weight: 400;\"> 1. &#2348;&#2366;&#2361;&#2381;&#2351; &#2348;&#2368;&#2350; &#2357;&#2367;&#2325;&#2367;&#2352;&#2339; &#2330;&#2367;&#2325;&#2367;&#2340;&#2381;&#2360;&#2366;, &#2319;&#2325; &#2350;&#2358;&#2368;&#2344; &#2360;&#2375; &#2344;&#2367;&#2325;&#2354;&#2340;&#2368; &#2361;&#2376;, &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2342;&#2381;&#2342;&#2375;&#2358;&#2381;&#2351; &#2325;&#2376;&#2306;&#2360;&#2352; &#2346;&#2352; &#2357;&#2367;&#2325;&#2367;&#2352;&#2339; &#2337;&#2366;&#2354;&#2344;&#2366; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;&#2404; 2. &#2310;&#2306;&#2340;&#2352;&#2367;&#2325; &#2357;&#2367;&#2325;&#2367;&#2352;&#2339; &#2330;&#2367;&#2325;&#2367;&#2340;&#2381;&#2360;&#2366; &#2319;&#2325; &#2320;&#2360;&#2366; &#2313;&#2346;&#2330;&#2366;&#2352; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2358;&#2352;&#2368;&#2352; &#2325;&#2375; &#2309;&#2306;&#2342;&#2352; &#2357;&#2367;&#2325;&#2367;&#2352;&#2339; &#2325;&#2366; &#2319;&#2325; &#2360;&#2381;&#2352;&#2379;&#2340; &#2337;&#2366;&#2354;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">21. </span><span style=\"font-family: Cambria Math;\">Which of the following articles of the Indian Constitution is related to the writ of the High Courts?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">21. </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2357;&#2367;&#2343;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2330;&#2381;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2367;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2348;&#2306;&#2343;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>32<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>126</p>\n", 
                                "<p>76</p>\n", "<p>226</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>32<span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>126</p>\n",
                                "<p>76</p>\n", "<p>226</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span><strong>226</strong><span style=\"font-weight: 400;\">. </span><strong>Article 32</strong><span style=\"font-weight: 400;\"> - Empowers the Supreme Court of India to issue writs.</span><strong> Types of writs -</strong><span style=\"font-weight: 400;\"> Habeas corpus, Mandamus, Prohibition, Quo warranto, Certiorari. There are 25 High Courts in India. </span><strong>Oldest High Court -</strong><span style=\"font-weight: 400;\"> Calcutta High Court (1862 West Bengal). </span><strong>1st Chief Justice of India (CJI)-</strong><span style=\"font-weight: 400;\"> Justice Hiralal Jekisundas Kania. </span><strong>50th CJI of India - </strong><span style=\"font-weight: 400;\">Justice D.Y. Chandrachud (as of April 2023).</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">21.(</span><span style=\"font-family: Cambria Math;\">d)&nbsp;</span><strong>226 </strong><span style=\"font-weight: 400;\">&#2404;&nbsp; </span><strong>&#2309;&#2344;&#2369;&#2330;&#2381;&#2331;&#2375;&#2342; 32</strong><span style=\"font-weight: 400;\"> - &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2360;&#2352;&#2381;&#2357;&#2379;&#2330;&#2381;&#2330; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351; &#2325;&#2379; &#2352;&#2367;&#2335; &#2332;&#2366;&#2352;&#2368; &#2325;&#2352;&#2344;&#2375; &#2325;&#2366; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;&#2404;</span><strong> &#2352;&#2367;&#2335;&#2379;&#2306; &#2325;&#2375; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; -</strong><span style=\"font-weight: 400;\"> &#2348;&#2306;&#2342;&#2368; &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2325;&#2381;&#2359;&#2368;&#2325;&#2352;&#2339;, &#2346;&#2352;&#2350;&#2366;&#2342;&#2375;&#2358;, &#2344;&#2367;&#2359;&#2375;&#2343;, &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;-&#2346;&#2371;&#2330;&#2381;&#2331;&#2366;, &#2313;&#2340;&#2381;&#2346;&#2381;&#2352;&#2375;&#2359;&#2339; &#2404; &#2349;&#2366;&#2352;&#2340; &#2350;&#2375;&#2306; </span><strong>25 </strong><span style=\"font-weight: 400;\">&#2313;&#2330;&#2381;&#2330; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351; &#2361;&#2376;&#2306;&#2404; &#2360;&#2348;&#2360;&#2375; &#2346;&#2369;&#2352;&#2366;&#2344;&#2366; &#2313;&#2330;&#2381;&#2330; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351; - &#2325;&#2354;&#2325;&#2340;&#2381;&#2340;&#2366; &#2313;&#2330;&#2381;&#2330; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2354;&#2351; (1862 &#2346;&#2358;&#2381;&#2330;&#2367;&#2350; &#2348;&#2306;&#2327;&#2366;&#2354;)&#2404; &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2346;&#2381;&#2352;&#2341;&#2350; &#2350;&#2369;&#2326;&#2381;&#2351; &#2344;&#2381;&#2351;&#2366;&#2351;&#2366;&#2343;&#2368;&#2358; (CJI)- &#2344;&#2381;&#2351;&#2366;&#2351;&#2350;&#2370;&#2352;&#2381;&#2340;&#2367; &#2361;&#2368;&#2352;&#2366;&#2354;&#2366;&#2354; &#2332;&#2375;&#2325;&#2367;&#2360;&#2369;&#2306;&#2342;&#2366;&#2360; &#2325;&#2366;&#2344;&#2367;&#2351;&#2366;&#2404; </span><strong>&#2349;&#2366;&#2352;&#2340; &#2325;&#2375; 50&#2357;&#2375;&#2306; CJI</strong><span style=\"font-weight: 400;\"> - &#2344;&#2381;&#2351;&#2366;&#2351;&#2350;&#2370;&#2352;&#2381;&#2340;&#2367; &#2343;&#2344;&#2306;&#2332;&#2351; &#2351;&#2358;&#2357;&#2306;&#2340; &#2330;&#2306;&#2342;&#2381;&#2352;&#2330;&#2370;&#2337;&#2364; (&#2309;&#2346;&#2381;&#2352;&#2376;&#2354; 2023 &#2340;&#2325;)&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">22. </span><span style=\"font-family: Cambria Math;\">Which of the following rivers originates in Rajasthan? </span></p>\n",
                    question_hi: "<p>22.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2380;&#2344;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2342;&#2381;&#2327;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\"><span style=\"font-family: Cambria Math;\"> ?</span></span></p>\n",
                    options_en: ["<p>Mahi</p>\n", "<p>Narmada</p>\n", 
                                "<p>Tapi</p>\n", "<p>Banas</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2361;&#2368;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2352;&#2381;&#2350;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2366;&#2346;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2348;&#2344;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><span style=\"font-weight: 400;\">&nbsp;</span><strong>Banas</strong><span style=\"font-weight: 400;\">. The river originates from the Khamnor hills of the Aravali range. It is a tributary of the Chambal River. It is also known as &ldquo;Van ki Asha&rdquo;. Length of Banas: 512 km. </span><strong>Tributaries</strong><span style=\"font-weight: 400;\">: Kothari, Dai, Dheel, Menali etc.&nbsp;&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">22.(</span><span style=\"font-family: Cambria Math;\">d)</span><span style=\"font-family: Cambria Math;\">&nbsp;</span><strong>&#2348;&#2344;&#2366;&#2360;&#2404;</strong><span style=\"font-weight: 400;\"> &#2351;&#2361; &#2344;&#2342;&#2368; &#2309;&#2352;&#2366;&#2357;&#2354;&#2368; &#2346;&#2352;&#2381;&#2357;&#2340;&#2350;&#2366;&#2354;&#2366; &#2325;&#2368; &#2326;&#2350;&#2344;&#2379;&#2352; &#2346;&#2361;&#2366;&#2337;&#2364;&#2367;&#2351;&#2379;&#2306; &#2360;&#2375; &#2344;&#2367;&#2325;&#2354;&#2340;&#2368; &#2361;&#2376;&#2404; &#2351;&#2361; &#2330;&#2350;&#2381;&#2348;&#2354; &#2344;&#2342;&#2368; &#2325;&#2368; &#2360;&#2361;&#2366;&#2351;&#2325; &#2344;&#2342;&#2368; &#2361;&#2376;&#2404; &#2311;&#2360;&#2375; \"&#2357;&#2344; &#2325;&#2368; &#2310;&#2358;&#2366;\" &#2325;&#2375; &#2344;&#2366;&#2350; &#2360;&#2375; &#2349;&#2368; &#2332;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; </span><strong>&#2348;&#2344;&#2366;&#2360; &#2325;&#2368; &#2354;&#2306;&#2348;&#2366;&#2312;</strong><span style=\"font-weight: 400;\">: 512 &#2325;&#2367;&#2350;&#2368;&#2404;</span><strong> &#2360;&#2361;&#2366;&#2351;&#2325; &#2344;&#2342;&#2367;&#2351;&#2366;&#2305;</strong><span style=\"font-weight: 400;\">: &#2325;&#2379;&#2336;&#2366;&#2352;&#2368;, &#2342;&#2366;&#2312;, &#2338;&#2368;&#2354;, &#2350;&#2375;&#2344;&#2366;&#2354;&#2368; &#2310;&#2342;&#2367;&#2404;</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">23. </span><span style=\"font-family: Cambria Math;\">Which team won the 2021-22 I-League? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">23. </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2335;&#2368;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> 2021-22 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2368;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2368;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p><span style=\"font-family: Cambria Math;\">Neroca</span><span style=\"font-family: Cambria Math;\"> FC </span></p>\n", "<p>Mohammedan SC</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">Gokulam</span><span style=\"font-family: Cambria Math;\"> Kerala FC </span></p>\n", "<p>Rajasthan United</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;&#2352;&#2379;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2347;&#2364;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2379;&#2361;&#2350;&#2381;&#2350;&#2337;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2360;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2327;&#2379;&#2325;&#2369;&#2354;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;&#2352;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2347;&#2364;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2332;&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2366;&#2311;&#2335;&#2375;&#2337;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>Gokulam Kerala FC. Runner - up:&nbsp; </strong><span style=\"font-weight: 400;\">Kolkata\'s Mohammedan Sporting Club.</span><strong> </strong><span style=\"font-weight: 400;\">Gokulam Kerala won their second I-League title in a row. </span><strong>Best Defender:</strong><span style=\"font-weight: 400;\"> Bouba Aminou. </span><strong>Best Midfielder: </strong><span style=\"font-weight: 400;\">Jithin MS. </span><strong>Golden Boot:</strong><span style=\"font-weight: 400;\"> Marcus Joseph.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">23.(</span><span style=\"font-family: Cambria Math;\">c)&nbsp;</span><strong>&#2327;&#2379;&#2325;&#2369;&#2354;&#2350; &#2325;&#2375;&#2352;&#2354; &#2319;&#2347;&#2364;&#2360;&#2368;&#2404;</strong><span style=\"font-weight: 400;\"> &#2313;&#2346;&#2357;&#2367;&#2332;&#2375;&#2340;&#2366;: </span><strong>&#2325;&#2379;&#2354;&#2325;&#2366;&#2340;&#2366; &#2325;&#2366; &#2350;&#2379;&#2361;&#2350;&#2381;&#2350;&#2342;&#2344; &#2360;&#2381;&#2346;&#2379;&#2352;&#2381;&#2335;&#2367;&#2306;&#2327; &#2325;&#2381;&#2354;&#2348;&#2404;</strong><span style=\"font-weight: 400;\"> &#2327;&#2379;&#2325;&#2369;&#2354;&#2350; &#2325;&#2375;&#2352;&#2354; &#2344;&#2375; &#2354;&#2327;&#2366;&#2340;&#2366;&#2352; &#2342;&#2370;&#2360;&#2352;&#2366; I -&#2354;&#2368;&#2327; &#2326;&#2367;&#2340;&#2366;&#2348; &#2332;&#2368;&#2340;&#2366;&#2404; &#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336; &#2337;&#2367;&#2347;&#2375;&#2306;&#2337;&#2352;: &#2348;&#2380;&#2348;&#2366; &#2309;&#2350;&#2367;&#2344;&#2380;&#2404; &#2360;&#2352;&#2381;&#2357;&#2358;&#2381;&#2352;&#2375;&#2359;&#2381;&#2336; &#2350;&#2367;&#2337;&#2347;&#2368;&#2354;&#2381;&#2337;&#2352;: &#2332;&#2367;&#2340;&#2367;&#2344; &#2319;&#2350;.&#2319;&#2360;&#2404; </span><strong>&#2327;&#2379;&#2354;&#2381;&#2337;&#2344; &#2348;&#2370;&#2335;</strong><span style=\"font-weight: 400;\">: &#2350;&#2366;&#2352;&#2381;&#2325;&#2360; &#2332;&#2379;&#2360;&#2375;&#2347;&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">24.</span><span style=\"font-family: Cambria Math;\"> Which of the following has revamped the E-Baal </span><span style=\"font-family: Cambria Math;\">Nidan</span><span style=\"font-family: Cambria Math;\"> online portal in September </span><span style=\"font-family: Cambria Math;\">2022 ?</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">24.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2367;&#2340;&#2306;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2312;</span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2342;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2321;&#2344;&#2354;&#2366;&#2311;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2379;&#2352;&#2381;&#2335;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>UNICEF India</p>\n", "<p>National Commission for Protection of Child Rights</p>\n", 
                                "<p>NITI Aayog</p>\n", "<p>Ministry of women and child development</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2351;&#2370;&#2344;&#2367;&#2360;&#2375;&#2347;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2306;&#2337;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2306;&#2352;&#2325;&#2381;&#2359;&#2339;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2344;&#2368;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2361;&#2367;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2357;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2306;&#2340;&#2381;&#2352;&#2366;&#2354;&#2351;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">b)<span style=\"font-weight: 400;\">&nbsp;</span><strong>National Commission for Protection of Child Rights. &ldquo;E-Baal Nidan&rdquo;: </strong><span style=\"font-weight: 400;\">Online complaint mechanism to report violations committed against a child. </span><strong>Launched</strong><span style=\"font-weight: 400;\">: 2015. </span><strong>NCPCR: </strong><span style=\"font-weight: 400;\">Investigate child rights violations. </span><strong>Established:</strong><span style=\"font-weight: 400;\"> March 2007 under the Commission for Protection of Child Rights Act, 2005. </span><strong>Chairperson:</strong><span style=\"font-weight: 400;\"> Priyank Kanoongo (as of April 2023).&nbsp;</span><span style=\"font-weight: 400;\"> </span></span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">24.(</span><span style=\"font-family: Cambria Math;\">b)&nbsp;</span><strong>&#2352;&#2366;&#2359;&#2381;&#2335;&#2381;&#2352;&#2368;&#2351; &#2348;&#2366;&#2354; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; &#2360;&#2306;&#2352;&#2325;&#2381;&#2359;&#2339; &#2310;&#2351;&#2379;&#2327;</strong><span style=\"font-weight: 400;\">&#2404;</span><strong> \"&#2312;-&#2348;&#2366;&#2354; &#2344;&#2367;&#2342;&#2366;&#2344;\":</strong><span style=\"font-weight: 400;\"> &#2348;&#2330;&#2381;&#2330;&#2375; &#2325;&#2375; &#2326;&#2367;&#2354;&#2366;&#2347; &#2325;&#2367;&#2319; &#2327;&#2319; &#2313;&#2354;&#2381;&#2354;&#2306;&#2328;&#2344;&#2379;&#2306; &#2325;&#2368; &#2352;&#2367;&#2346;&#2379;&#2352;&#2381;&#2335; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2321;&#2344;&#2354;&#2366;&#2311;&#2344; &#2358;&#2367;&#2325;&#2366;&#2351;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2366; &#2319;&#2325; &#2346;&#2379;&#2352;&#2381;&#2335;&#2354; &#2361;&#2376;&#2404; </span><strong>&#2354;&#2377;&#2344;&#2381;&#2330;:</strong><span style=\"font-weight: 400;\"> 2015 &#2404; </span><strong>NCPCR:</strong><span style=\"font-weight: 400;\"> &#2348;&#2366;&#2354; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352;&#2379;&#2306; &#2325;&#2375; &#2313;&#2354;&#2381;&#2354;&#2306;&#2328;&#2344; &#2325;&#2368; &#2332;&#2366;&#2306;&#2330; &#2325;&#2352;&#2344;&#2366;&#2404; </span><strong>&#2360;&#2381;&#2341;&#2366;&#2346;&#2367;&#2340;:</strong><span style=\"font-weight: 400;\">&nbsp; &#2348;&#2366;&#2354; &#2309;&#2343;&#2367;&#2325;&#2366;&#2352; &#2360;&#2306;&#2352;&#2325;&#2381;&#2359;&#2339; &#2309;&#2343;&#2367;&#2344;&#2367;&#2351;&#2350; 2005 &#2325;&#2375; &#2310;&#2351;&#2379;&#2327; &#2325;&#2375; &#2340;&#2361;&#2340; , &#2350;&#2366;&#2352;&#2381;&#2330; 2007 &#2350;&#2375;&#2306; &#2404; </span><strong>&#2309;&#2343;&#2381;&#2351;&#2325;&#2381;&#2359;: </strong><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2367;&#2351;&#2306;&#2325; &#2325;&#2366;&#2344;&#2370;&#2344;&#2327;&#2379; (&#2309;&#2346;&#2381;&#2352;&#2376;&#2354; 2023 &#2340;&#2325;)&#2404;</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">25.</span><span style=\"font-family: Cambria Math;\"> Who is famously known as the father of the Indian nuclear </span><span style=\"font-family: Cambria Math;\">programme</span><span style=\"font-family: Cambria Math;\">? </span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">25.</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2352;&#2340;&#2368;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;&#2350;&#2366;&#2339;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2344;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>Homi J. Bhabha</p>\n", "<p>Vikram Sarabhai</p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">C.</span><span style=\"font-family: Cambria Math;\">V.Raman</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>Venkatraman Radhakrishnan</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2349;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2352;&#2366;&#2349;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p><span style=\"font-family: Nirmala UI;\">&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2350;&#2344;</span></p>\n", "<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2375;&#2306;&#2325;&#2335;&#2352;&#2350;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2366;&#2343;&#2366;&#2325;&#2371;&#2359;&#2381;&#2339;&#2344;</span></p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">25.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>Homi J. Bhabha.</strong><span style=\"font-weight: 400;\"> Bhabha Atomic Research Centre (Mumbai - 1954), Tata Institute of Fundamental Research (Mumbai - 1945), ISRO (Bangalore - 1969), DRDO (New Delhi - 1958). </span><strong>Vikram Sarabhai - </strong><span style=\"font-weight: 400;\">Father of Indian Space Programme. </span><strong>APJ Abdul Kalam -</strong><span style=\"font-weight: 400;\"> Missile Man of India.&nbsp;</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">25.(</span><span style=\"font-family: Cambria Math;\">a)&nbsp;</span><strong>&#2361;&#2379;&#2350;&#2368; &#2332;. &#2349;&#2366;&#2349;&#2366; &#2404;</strong><span style=\"font-weight: 400;\"> &#2349;&#2366;&#2349;&#2366; &#2319;&#2335;&#2377;&#2350;&#2367;&#2325; &#2352;&#2367;&#2360;&#2352;&#2381;&#2330; &#2360;&#2375;&#2306;&#2335;&#2352; (&#2350;&#2369;&#2306;&#2348;&#2312; - 1954), &#2335;&#2366;&#2335;&#2366; &#2311;&#2306;&#2360;&#2381;&#2335;&#2368;&#2335;&#2381;&#2351;&#2370;&#2335; &#2321;&#2347; &#2347;&#2306;&#2337;&#2366;&#2350;&#2375;&#2306;&#2335;&#2354; &#2352;&#2367;&#2360;&#2352;&#2381;&#2330; (&#2350;&#2369;&#2306;&#2348;&#2312; - 1945)&#2404; </span><strong>ISRO </strong><span style=\"font-weight: 400;\">(&#2348;&#2376;&#2306;&#2327;&#2354;&#2379;&#2352; - 1969), </span><strong>DRDO </strong><span style=\"font-weight: 400;\">(&#2344;&#2312; &#2342;&#2367;&#2354;&#2381;&#2354;&#2368; - 1958)&#2404; </span><strong>&#2357;&#2367;&#2325;&#2381;&#2352;&#2350; &#2360;&#2366;&#2352;&#2366;&#2349;&#2366;&#2312;</strong><span style=\"font-weight: 400;\"> - &#2349;&#2366;&#2352;&#2340;&#2368;&#2351; &#2309;&#2306;&#2340;&#2352;&#2367;&#2325;&#2381;&#2359; &#2325;&#2366;&#2352;&#2381;&#2351;&#2325;&#2381;&#2352;&#2350; &#2325;&#2375; &#2332;&#2344;&#2325; &#2404; </span><strong>&nbsp;APJ &#2309;&#2348;&#2381;&#2342;&#2369;&#2354; &#2325;&#2354;&#2366;&#2350; </strong><span style=\"font-weight: 400;\">- &#2349;&#2366;&#2352;&#2340; &#2325;&#2375; &#2350;&#2367;&#2360;&#2366;&#2311;&#2354; &#2350;&#2376;&#2344;&#2404;</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>