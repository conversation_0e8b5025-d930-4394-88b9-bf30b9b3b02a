<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">12:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 12 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the most appropriate meaning of the following idiom.<br>To bring to light</p>",
                    question_hi: "<p>1. Select the most appropriate meaning of the following idiom.<br>To bring to light</p>",
                    options_en: ["<p>To engage in conversation</p>", "<p>To lighten</p>", 
                                "<p>To disclose</p>", "<p>To disengage</p>"],
                    options_hi: ["<p>To engage in conversation</p>", "<p>To lighten</p>",
                                "<p>To disclose</p>", "<p>To disengage</p>"],
                    solution_en: "<p>1.(c) <strong>To bring to light-</strong> to disclose.<br>E.g.- The investigation brought to light several previously unknown facts about the case.</p>",
                    solution_hi: "<p>1.(c) <strong>To bring to light-</strong> to disclose./ उजागर करना।<br>E.g.- The investigation brought to light several previously unknown facts about the case.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "2. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />(a) false news websites<br />(b) spreading propaganda about politics<br />(c) thrive because the advertisements and politicians<br />(d) and social networking sites<br />(e) make a lot of money",
                    question_hi: "2. Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />(a) false news websites<br />(b) spreading propaganda about politics<br />(c) thrive because the advertisements and politicians<br />(d) and social networking sites<br />(e) make a lot of money",
                    options_en: [" aecbd ", " adcbe ", 
                                " adecb ", " aedcb"],
                    options_hi: [" aecbd ", " adcbe ",
                                " adecb ", " aedcb"],
                    solution_en: "2.(b) adcbe<br />The given sentence starts with Part (a) as it introduces the main subject of the sentence, i.e. false news websites. Part (d) contains the other part of the subject joined by conjunction ‘and’ & Part (c) has the main verb of the sentence. So, Part (c) will follow Part (d). Further, Part (b) states that advertisements and politicians spread propaganda about politics & Part (e) states that such advertisements and politicians make a lot of money. So, Part (e) will follow Part (b). Going through the options, option ‘b’ has the correct sequence.",
                    solution_hi: "2.(b) adcbe<br />दिया गया sentence Part (a) से प्रारंभ होता है क्योंकि इसमें sentence का main subject, ‘false news websites’ शामिल है। Part (d) में conjunction ‘and’ से जुड़ा हुआ subject का दूसरा भाग शामिल है और Part (c) में sentence की main verb है। इसलिए, Part (d) के बाद Part (c) आएगा। इसके अलावा, Part (b) में कहा गया है कि advertisements और politicians राजनीति के बारे में प्रचार करते हैं और Part (e) में कहा गया है कि ऐसे advertisements और politicians बहुत पैसा कमाते हैं। इसलिए, Part (b) के बाद  Part (e) आएगा। अतः options के माध्यम से जाने पर,  option ‘b’ में सही sequence है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "3.  Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />P) willing to go<br />Q) is not<br />R) my son<br />S) camping",
                    question_hi: "3.  Parts of a sentence are given below in jumbled order. Arrange the parts in the correct order to form a meaningful sentence.<br />P) willing to go<br />Q) is not<br />R) my son<br />S) camping",
                    options_en: [" RQPS ", " SPRQ ", 
                                "  PQRS", " QRSP"],
                    options_hi: [" RQPS ", " SPRQ ",
                                "  PQRS", " QRSP"],
                    solution_en: "3.(a) RQPS<br />The correct sentence is “My son is not willing to go camping.”",
                    solution_hi: "3.(a) RQPS<br />“My son is not willing to go camping.” सही sentence है।  ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "4. Select the most appropriate homophone to fill in the blank.<br />The ointment will help to ___________ the wound.",
                    question_hi: "4. Select the most appropriate homophone to fill in the blank.<br />The ointment will help to ___________ the wound.",
                    options_en: ["  he\'ll", "  heel", 
                                "  heal", " kneel"],
                    options_hi: ["  he\'ll", "  heel",
                                "  heal", " kneel"],
                    solution_en: "4.(c) heal<br />‘Heal’ means to cause (a wound, injury, or person) to become sound or healthy again. The given sentence states that the ointment will help to heal the wound. Hence, ‘heal’ is the most appropriate answer.",
                    solution_hi: "4.(c) heal<br />‘Heal’ का अर्थ है फिर से स्वस्थ या ठीक करना (घाव, चोट या व्यक्ति को)। दिए गए sentence में कहा गया है कि ointment, घाव को ठीक करने में मदद करेगा। अतः, ‘heal’ सबसे उपयुक्त उत्तर है।",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the most appropriate ANTONYM of the given word.<br>Attack</p>",
                    question_hi: "<p>5. Select the most appropriate ANTONYM of the given word.<br>Attack</p>",
                    options_en: ["<p>Ambush</p>", "<p>Defence</p>", 
                                "<p>Production</p>", "<p>Engagement</p>"],
                    options_hi: ["<p>Ambush</p>", "<p>Defence</p>",
                                "<p>Production</p>", "<p>Engagement</p>"],
                    solution_en: "<p>5.(b) <strong>Defence</strong>- protection against harm or attack.<br><strong>Attack</strong>- aggressive action against something or someone.<br><strong>Ambush</strong>- a surprise attack.<br><strong>Production</strong>- the process of creating or manufacturing something.<br><strong>Engagement</strong>- involvement or participation in an activity or event.</p>",
                    solution_hi: "<p>5.(b) <strong>Defence </strong>(सुरक्षा/रक्षा)- protection against harm or attack.<br><strong>Attack </strong>(आक्रमण)- aggressive action against something or someone.<br><strong>Ambush</strong> (घात लगाना)- a surprise attack.<br><strong>Production </strong>(उत्पादन/निर्माण)- the process of creating or manufacturing something.<br><strong>Engagement </strong>(भागीदारी/शामिल होना)- involvement or participation in an activity or event.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "6. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br />A. It was as simple as announcing what you have in your store or the services you offer in your premises.<br />B. Over the years, advertising has evolved into a major industry that goes beyond informing to persuading and influencing.<br />C. Advertising was initially meant to make people aware of the goods available in the market.<br />D. It is a form of brainwashing consumers.",
                    question_hi: "6. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br />A. It was as simple as announcing what you have in your store or the services you offer in your premises.<br />B. Over the years, advertising has evolved into a major industry that goes beyond informing to persuading and influencing.<br />C. Advertising was initially meant to make people aware of the goods available in the market.<br />D. It is a form of brainwashing consumers.",
                    options_en: [" BADC ", "  ADBC", 
                                " DABC ", " CABD"],
                    options_hi: [" BADC ", "  ADBC",
                                " DABC ", " CABD"],
                    solution_en: "6.(d) CABD<br />Sentence C will be the starting line as it introduces the main idea of the parajumble, i.e. advertising was initially meant to make people aware of the goods available in the market. And, Sentence A talks about the simplicity of advertising. So, A will follow C. Further, Sentence B states that advertising has evolved over the years & Sentence D states that now it is a form of brainwashing consumers. So, D will follow B. Going through the options, option ‘d’ has the correct sequence.",
                    solution_hi: "6.(d) CABD<br />Sentence C प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार ‘advertising was initially meant to make people aware of the goods available in the market’ का परिचय देता है। और, Sentence A, advertising की simplicity के बारे में बात करता है। इसलिए, C के बाद A आएगा। इसके अलावा, Sentence B बताता है कि advertising पिछले कुछ वर्षों में विकसित हुआ है और Sentence D बताता है कि अब यह उपभोक्ताओं को गुमराह (brainwashing) का एक तरीका है। इसलिए, B के बाद  D आएगा। अतः options के माध्यम से जाने पर,  option ‘d’ में सही sequence है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the most appropriate ANTONYM of the underlined word.<br>We should plan our actions <span style=\"text-decoration: underline;\">judiciously</span> before going to war.</p>",
                    question_hi: "<p>7. Select the most appropriate ANTONYM of the underlined word.<br>We should plan our actions <span style=\"text-decoration: underline;\">judiciously </span>before going to war.</p>",
                    options_en: ["<p>Irreverent</p>", "<p>Irrational</p>", 
                                "<p>Irrelevant</p>", "<p>Irritatingly</p>"],
                    options_hi: ["<p>Irreverent</p>", "<p>Irrational</p>",
                                "<p>Irrelevant</p>", "<p>Irritatingly</p>"],
                    solution_en: "<p>7.(b) <strong>Irrational</strong>- lacking reason or logic.<br><strong>Judiciously</strong>- with good judgment or sense.<br><strong>Irreverent</strong>- showing a lack of respect for things usually taken seriously.<br><strong>Irrelevant</strong>- not related to the matter at hand.<br><strong>Irritatingly</strong>- in a way that causes annoyance or irritation.</p>",
                    solution_hi: "<p>7.(b) <strong>Irrational </strong>(अतार्किक)- lacking reason or logic.<br><strong>Judiciously </strong>(विवेकपूर्वक)- with good judgment or sense.<br><strong>Irreverent </strong>(अप्रासंगिक)- showing a lack of respect for things usually taken seriously.<br><strong>Irrelevant </strong>(असंगत)- not related to the matter at hand.<br><strong>Irritatingly </strong>(चिड़चिड़ाहट से)- in a way that causes annoyance or irritation.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "8. Select the sentence that has a grammatical error.",
                    question_hi: "8. Select the sentence that has a grammatical error.",
                    options_en: [" It is said that Joseph was not ready to go to war. ", " Karl Marx was a renowned social scientist. ", 
                                " Indian force are known for their loyalty and integrity. ", " It is impossible to wake Christine up in the morning."],
                    options_hi: [" It is said that Joseph was not ready to go to war. ", " Karl Marx was a renowned social scientist. ",
                                " Indian force are known for their loyalty and integrity. ", " It is impossible to wake Christine up in the morning."],
                    solution_en: "8.(c) Indian force are known for their loyalty and integrity.<br />‘Forces’ will be used instead of ‘force’ according to the plural verb ‘are’ and the plural adjective ‘their’. Hence, the correct sentence is: “Indian forces are known for their loyalty and integrity.”",
                    solution_hi: "8.(c) Indian force are known for their loyalty and integrity.<br />Plural verb ‘are’ और  plural adjective ‘their’ के अनुसार ‘force’ के स्थान पर ‘forces’ का use किया जाएगा। अतः, “Indian forces are known for their loyalty and integrity” सही sentence है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the correct meaning of the underlined idiom in the following sentence.<br>Rina <span style=\"text-decoration: underline;\">pulled a long face</span> since her husband had not bought her a diamond necklace on their anniversary.</p>",
                    question_hi: "<p>9. Select the correct meaning of the underlined idiom in the following sentence.<br>Rina<span style=\"text-decoration: underline;\"> pulled a long face</span> since her husband had not bought her a diamond necklace on their anniversary.</p>",
                    options_en: ["<p>To be euphoric</p>", "<p>To be ecstatic</p>", 
                                "<p>To look saddened</p>", "<p>To be electrified</p>"],
                    options_hi: ["<p>To be euphoric</p>", "<p>To be ecstatic</p>",
                                "<p>To look saddened</p>", "<p>To be electrified</p>"],
                    solution_en: "<p>9.(c) <strong>Pulled a long face</strong>- to look saddened.</p>",
                    solution_hi: "<p>9.(c) <strong>Pulled a long face-</strong> to look saddened./उदास या दुःखी दिखना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "10. Select the correct spelling to fill in the blank.<br />The thought of eating half-cooked food under compulsion fills me with _________ .",
                    question_hi: "10. Select the correct spelling to fill in the blank.<br />The thought of eating half-cooked food under compulsion fills me with _________ .",
                    options_en: ["  ripugnance", " repegnance ", 
                                " repugnance ", " repugnence"],
                    options_hi: ["  ripugnance", " repegnance ",
                                " repugnance ", " repugnence"],
                    solution_en: "10.(c) repugnance<br />\'Repugnance\' is the correct spelling.",
                    solution_hi: "10.(c) repugnance<br />\'Repugnance\' सही spelling है। ",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "11. Select the INCORRECTLY spelt word from the given sentence.<br />The resturant offers a wide variety of dishes.",
                    question_hi: "11. Select the INCORRECTLY spelt word from the given sentence.<br />The resturant offers a wide variety of dishes.",
                    options_en: [" variety", "  resturant", 
                                "  dishes", " offers"],
                    options_hi: [" variety", "  resturant",
                                "  dishes", " offers"],
                    solution_en: "11.(b) resturant<br />\'Restaurant\' is the correct spelling.",
                    solution_hi: "11.(b) resturant<br />\'Restaurant\' सही spelling है। ",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the option that expresses the given sentence in passive voice.<br>They are building a new hospital in the city.</p>",
                    question_hi: "<p>12. Select the option that expresses the given sentence in passive voice.<br>They are building a new hospital in the city.</p>",
                    options_en: ["<p>A new hospital was built in the city by them.</p>", "<p>A new hospital has been built in the city by them.</p>", 
                                "<p>A new hospital will be built in the city by them.</p>", "<p>A new hospital is being built in the city by them.</p>"],
                    options_hi: ["<p>A new hospital was built in the city by them.</p>", "<p>A new hospital has been built in the city by them.</p>",
                                "<p>A new hospital will be built in the city by them.</p>", "<p>A new hospital is being built in the city by them.</p>"],
                    solution_en: "<p>12.(d) A new hospital is being built in the city by them. (Correct)<br>(a) A new hospital<span style=\"text-decoration: underline;\"> was built</span> in the city by them. (Incorrect Tense)<br>(b) A new hospital <span style=\"text-decoration: underline;\">has been built</span> in the city by them. (Incorrect Tense)<br>(c) A new hospital<span style=\"text-decoration: underline;\"> will be built </span>in the city by them. (Incorrect Tense)</p>",
                    solution_hi: "<p>12.(d) A new hospital is being built in the city by them. (Correct)<br>(a) A new hospital <span style=\"text-decoration: underline;\">was built </span>in the city by them. (गलत Tense)<br>(b) A new hospital <span style=\"text-decoration: underline;\">has been built </span>in the city by them. (गलत Tense)<br>(c) A new hospital<span style=\"text-decoration: underline;\"> will be built </span>in the city by them. (गलत Tense)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the most appropriate option to substitute the underlined part of the given sentence. If there is no need to substitute it, select \'No substitution\'.<br>As the rulers of the planet, humans like to think that <span style=\"text-decoration: underline;\">it is the largest creatures</span> that will emerge victorious from the struggle for survival.</p>",
                    question_hi: "<p>13. Select the most appropriate option to substitute the underlined part of the given sentence. If there is no need to substitute it, select \'No substitution\'.<br>As the rulers of the planet, humans like to think that <span style=\"text-decoration: underline;\">it is the largest creatures</span> that will emerge victorious from the struggle for survival.</p>",
                    options_en: ["<p>No substitution</p>", "<p>it is the most large creatures</p>", 
                                "<p>they are a largest creature</p>", "<p>they are the largest creatures</p>"],
                    options_hi: ["<p>No substitution</p>", "<p>it is the most large creatures</p>",
                                "<p>they are a largest creature</p>", "<p>they are the largest creatures</p>"],
                    solution_en: "<p>13.(d) they are the largest creatures<br>&lsquo;Humans&rsquo; is a plural noun that will take the plural pronoun &lsquo;they&rsquo; and the plural verb &lsquo;are&rsquo;. Hence, &lsquo;they are the largest creatures&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>13.(d) they are the largest creatures<br>&lsquo;Humans&rsquo;&lsquo; एक plural noun है जिसके साथ plural pronoun &lsquo;they&rsquo; और plural verb &lsquo;are&rsquo; का प्रयोग होगा। अतः, &lsquo;they are the largest creatures&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Experts acknowledged that some<span style=\"text-decoration: underline;\"> counterfeiting of document </span>techniques are virtually impossible to detect.</p>",
                    question_hi: "<p>14. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>Experts acknowledged that some <span style=\"text-decoration: underline;\">counterfeiting of document</span> techniques are virtually impossible to detect.</p>",
                    options_en: ["<p>fiasco</p>", "<p>forgery</p>", 
                                "<p>impromptu</p>", "<p>frenzy</p>"],
                    options_hi: ["<p>fiasco</p>", "<p>forgery</p>",
                                "<p>impromptu</p>", "<p>frenzy</p>"],
                    solution_en: "<p>14.(b) forgery<br>&lsquo;Forgery&rsquo; means the crime of illegally copying something in order to deceive someone. The given sentence states that experts acknowledged that some forgery techniques are virtually impossible to detect. Hence, &lsquo;forgery&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>14.(b) forgery<br>&lsquo;Forgery&rsquo; का अर्थ है किसी को धोखा देने के लिए अवैध रूप से किसी चीज की नकल करना। दिए गए sentence में कहा गया है कि विशेषज्ञों(experts) ने स्वीकार किया है कि कुछ जालसाजी(forgery) तकनीकों का पता लगाना लगभग असंभव है। अतः, &lsquo;forgery&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "7",
                    question_en: "<p>15. Select the option that expresses the given sentence in passive voice.<br>They have built a new bridge across the river.</p>",
                    question_hi: "<p>15. Select the option that expresses the given sentence in passive voice.<br>They have built a new bridge across the river.</p>",
                    options_en: ["<p>A new bridge has been built across the river by them.</p>", "<p>A new bridge by them has been built across the river.</p>", 
                                "<p>A new bridge has built across the river by them.</p>", "<p>The river has been built a new bridge by them.</p>"],
                    options_hi: ["<p>A new bridge has been built across the river by them.</p>", "<p>A new bridge by them has been built across the river.</p>",
                                "<p>A new bridge has built across the river by them.</p>", "<p>The river has been built a new bridge by them.</p>"],
                    solution_en: "<p>15.(a) A new bridge has been built across the river by them. (correct)<br>(b) A new bridge by them has been built across the river. (Incorrect Sentence Structure)<br>(c) A new bridge<span style=\"text-decoration: underline;\"> has built </span>across the river by them. (&lsquo;Been&rsquo; is missing)<br>(d) The river has been built a new bridge by them. (Incorrect Sentence Structure)</p>",
                    solution_hi: "<p>15.(a) A new bridge has been built across the river by them.(correct)<br>(b) A new bridge by them has been built across the river. (गलत Sentence Structure)<br>(c) A new bridge <span style=\"text-decoration: underline;\">has built </span>across the river by them. (&lsquo;Been&rsquo; missing है)<br>(d) The river has been built a new bridge by them. (गलत Sentence Structure)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "7",
                    question_en: "<p>16. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>The chef requires ___________ vegetables for the salad. [stale]</p>",
                    question_hi: "<p>16. Select the most appropriate ANTONYM of the word in the brackets to fill in the blank.<br>The chef requires ___________ vegetables for the salad. [stale]</p>",
                    options_en: ["<p>green</p>", "<p>fresh</p>", 
                                "<p>cut</p>", "<p>leafy</p>"],
                    options_hi: ["<p>green</p>", "<p>fresh</p>",
                                "<p>cut</p>", "<p>leafy</p>"],
                    solution_en: "<p>16.(b) <strong>Fresh</strong>- recently made or obtained.<br><strong>Stale</strong>- no longer fresh and pleasant.<br><strong>Leafy</strong>- full of leaves.</p>",
                    solution_hi: "<p>16.(b) <strong>Fresh </strong>(ताजा)- recently made or obtained.<br><strong>Stale </strong>(बासी)- no longer fresh and pleasant.<br><strong>Leafy </strong>(पत्तेदार)- full of leaves.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "7",
                    question_en: "<p>17. Select the most appropriate ANTONYM of the underlined word in the sentence given below.<br>I <span style=\"text-decoration: underline;\">refuse </span>to let anything come in the way of my dreams.</p>",
                    question_hi: "<p>17. Select the most appropriate ANTONYM of the underlined word in the sentence given below.<br>I <span style=\"text-decoration: underline;\">refuse </span>to let anything come in the way of my dreams.</p>",
                    options_en: ["<p>repress</p>", "<p>balk</p>", 
                                "<p>accord</p>", "<p>permit</p>"],
                    options_hi: ["<p>repress</p>", "<p>balk</p>",
                                "<p>accord</p>", "<p>permit</p>"],
                    solution_en: "<p>17.(d) <strong>Permit</strong>- to allow or give permission.<br><strong>Refuse</strong>- to indicate or show unwillingness to accept.<br><strong>Repress</strong>- to restrain or hold back.<br><strong>Balk</strong>- to hesitate or be unwilling to accept an idea or undertaking.<br><strong>Accord</strong>- to agree or be in harmony.</p>",
                    solution_hi: "<p>17.(d) <strong>Permit </strong>(अनुमति देना)- to allow or give permission.<br><strong>Refuse </strong>(अस्वीकार करना)- to indicate or show unwillingness to accept.<br><strong>Repress </strong>(रोकना)- to restrain or hold back.<br><strong>Balk </strong>(हिचकिचाना)- to hesitate or be unwilling to accept an idea or undertaking.<br><strong>Accord </strong>(सहमत होना)- to agree or be in harmony.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "7",
                    question_en: "18. The following sentence has been split into four segments. Identify the segment that contains an error.<br />The principal gave up / the prizes to / the winners on / Republic Day.",
                    question_hi: "18. The following sentence has been split into four segments. Identify the segment that contains an error.<br />The principal gave up / the prizes to / the winners on / Republic Day.",
                    options_en: ["  The principal gave up", " the winners on ", 
                                "  the prizes to", " Republic Day"],
                    options_hi: ["  The principal gave up", " the winners on ",
                                "  the prizes to", " Republic Day"],
                    solution_en: "18.(a) The principal gave up<br />‘Gave out’ is the correct phrasal verb to use here. ‘Give out’ means distributing something. The given sentence talks about the distribution of the prizes. Hence, ‘The principal gave out’ is the most appropriate answer.",
                    solution_hi: "18.(a) The principal gave up<br />यहाँ प्रयोग करने के लिए सही phrasal verb \'gave out\' है। Give out’ का अर्थ है कुछ वितरित करना। दिया गया sentence पुरस्कारों के वितरण के बारे में बात करता है। अतः, ‘The principal gave out’ सबसे उपयुक्त उत्तर है।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "7",
                    question_en: "<p>19. Select the option that can be used as a one-word substitute for the underlined group of words in the following sentence.<br>Almost immediately, they heard the <span style=\"text-decoration: underline;\">high shrill piercing cry</span> of tires on the side road.</p>",
                    question_hi: "<p>19. Select the option that can be used as a one-word substitute for the underlined group of words in the following sentence.<br>Almost immediately, they heard the <span style=\"text-decoration: underline;\">high shrill piercing cry </span>of tires on the side road.</p>",
                    options_en: ["<p>beseech</p>", "<p>creche</p>", 
                                "<p>screech</p>", "<p>bleach</p>"],
                    options_hi: ["<p>beseech</p>", "<p>creche</p>",
                                "<p>screech</p>", "<p>bleach</p>"],
                    solution_en: "<p>19.(c) <strong>Screech</strong>- high shrill piercing cry.<br><strong>Beseech</strong>- to ask for something in a way that shows you need it very much.<br><strong>Creche- </strong>a nursery where babies and young children are cared for during the working day.<br><strong>Bleach</strong>- to remove color from by means of chemicals or by exposure to the sun\'s rays.</p>",
                    solution_hi: "<p>19.(c) <strong>Screech </strong>(चीख़)- high shrill piercing cry.<br><strong>Beseech </strong>(विनती करना)- to ask for something in a way that shows you need it very much.<br><strong>Creche </strong>(शिशुगृह)- a nursery where babies and young children are cared for during the working day.<br><strong>Bleach</strong> (विरंजित करना)- to remove color from by means of chemicals or by exposure to the sun\'s rays.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "7",
                    question_en: "<p>20. Select the most appropriate ANTONYM of the given word.<br>Synthetic</p>",
                    question_hi: "<p>20. Select the most appropriate ANTONYM of the given word.<br>Synthetic</p>",
                    options_en: ["<p>Artificial</p>", "<p>Imitation</p>", 
                                "<p>Processed</p>", "<p>Natural</p>"],
                    options_hi: ["<p>Artificial</p>", "<p>Imitation</p>",
                                "<p>Processed</p>", "<p>Natural</p>"],
                    solution_en: "<p>20.(d) <strong>Natural</strong>- existing in or produced by nature.<br><strong>Synthetic</strong>- made by chemical synthesis, especially to imitate a natural product.<br><strong>Artificial</strong>- made or produced by human beings rather than occurring naturally.<br><strong>Imitation</strong>- a copy or reproduction of something, often to mimic its characteristics.<br><strong>Processed</strong>- treated or prepared by a series of mechanical or chemical operations for use or sale.</p>",
                    solution_hi: "<p>20.(d) <strong>Natural </strong>(प्राकृतिक)- existing in or produced by nature.<br><strong>Synthetic </strong>(संश्लेषित)- made by chemical synthesis, especially to imitate a natural product.<br><strong>Artificial </strong>(कृत्रिम)- made or produced by human beings rather than occurring naturally.<br><strong>Imitation</strong> (प्रतिरूपता)- a copy or reproduction of something, often to mimic its characteristics.<br><strong>Processed </strong>(प्रसंस्कृत)- treated or prepared by a series of mechanical or chemical operations for use or sale.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "7",
                    question_en: "<p>21. <strong>Cloze Test:</strong><br>The adverse (21)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (22) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (23) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (24) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (25) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    question_hi: "<p>21. <strong>Cloze Test:</strong><br>The adverse (21)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (22) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (23) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (24) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (25) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 21.</p>",
                    options_en: ["<p>renounce</p>", "<p>affects</p>", 
                                "<p>effects</p>", "<p>practice</p>"],
                    options_hi: ["<p>renounce</p>", "<p>affects</p>",
                                "<p>effects</p>", "<p>practice</p>"],
                    solution_en: "<p>21.(c) effects<br>&lsquo;Effect&rsquo; means the result of a particular action. The given passage states that the adverse effects of climate change and environmental degradation are increasingly driving human mobility the world over. Hence, &lsquo;effects&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>21.(c) effects<br>&lsquo;Effect&rsquo; का अर्थ है किसी विशेष action का परिणाम। दिए गए passage में कहा गया है कि जलवायु परिवर्तन(climate change) और पर्यावरणीय क्षरण(degradation) के प्रतिकूल प्रभाव दुनिया भर में मानव गतिशीलता(mobility) को तेजी से बढ़ा रहे हैं। अतः, &lsquo;effects&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "7",
                    question_en: "<p>22. <strong>Cloze Test:</strong><br>The adverse (21)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (22) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (23) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (24) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (25) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    question_hi: "<p>22. <strong>Cloze Test:</strong><br>The adverse (21)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (22) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (23) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (24) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (25) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 22.</p>",
                    options_en: ["<p>adaptive</p>", "<p>adoptive</p>", 
                                "<p>minimum</p>", "<p>affective</p>"],
                    options_hi: ["<p>adaptive</p>", "<p>adoptive</p>",
                                "<p>minimum</p>", "<p>affective</p>"],
                    solution_en: "<p>22.(a) adaptive<br>&lsquo;Adaptive&rsquo; means having the ability or tendency to adapt to different situations. The given passage states that the adverse effects of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low adaptive capacity. Hence, &lsquo;adaptive&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>22.(a) adaptive<br>&lsquo;Adaptive&rsquo; का अर्थ है विभिन्न परिस्थितियों के अनुकूल होने की क्षमता या प्रवृत्ति होना। दिए गए passage में कहा गया है कि जलवायु परिवर्तन और पर्यावरणीय क्षरण के प्रतिकूल प्रभाव दुनिया भर में मानव गतिशीलता को तेजी से बढ़ा रहे हैं, विशेषकर उन देशों में जहां उच्च अवशोषण (high exposure) और कम अनुकूलन (low adaptive) क्षमता है। अतः, &lsquo;adaptive&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "7",
                    question_en: "<p>23. <strong>Cloze Test:</strong><br>The adverse (21)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (22) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (23) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (24) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (25) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    question_hi: "<p>23. <strong>Cloze Test:</strong><br>The adverse (21)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (22) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (23) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (24) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (25) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 23.</p>",
                    options_en: ["<p>regard</p>", "<p>fast</p>", 
                                "<p>fixate</p>", "<p>irregular</p>"],
                    options_hi: ["<p>regard</p>", "<p>fast</p>",
                                "<p>fixate</p>", "<p>irregular</p>"],
                    solution_en: "<p>23.(d) irregular<br>&lsquo;Irregular&rsquo; means not even or balanced in arrangement. The given passage states that desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through irregular migration.<br>Hence, &lsquo;irregular&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>23.(d) irregular<br>&lsquo;Irregular&rsquo; का अर्थ है व्यवस्था में संतुलन न होना। दिए गए passage में कहा गया है कि हताशा (desperation) और दूषित पर्यावरण (deteriorating environments) भी अनियमित migration के माध्यम से अन्यत्र आजीविका की तलाश करने के लिए मजबूर कर सकते हैं। अतः, &lsquo;irregular&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "7",
                    question_en: "<p>24. <strong>Cloze Test:</strong><br>The adverse (21)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (22) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (23) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (24) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (25) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    question_hi: "<p>24. <strong>Cloze Test:</strong><br>The adverse (21)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (22) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (23) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (24) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (25) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 24.</p>",
                    options_en: ["<p>depend from</p>", "<p>depend at</p>", 
                                "<p>depend on</p>", "<p>depend in</p>"],
                    options_hi: ["<p>depend from</p>", "<p>depend at</p>",
                                "<p>depend on</p>", "<p>depend in</p>"],
                    solution_en: "<p>24.(c) depend on<br>&lsquo;On&rsquo; is a fixed preposition used after &lsquo;depend&rsquo;. Hence, &lsquo;depend on&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>24.(c) depend on<br>&lsquo;On&rsquo; एक fixed preposition है जिसका प्रयोग &lsquo;depend&rsquo; के बाद किया जाता है। अतः, &lsquo;depend on&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. <strong>Cloze Test:</strong><br>The adverse (21)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (22) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (23) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (24) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (25) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    question_hi: "<p>25. <strong>Cloze Test:</strong><br>The adverse (21)______ of climate change and environmental degradation are increasingly driving human mobility the world over, particularly in countries with high exposure and low (22) ________ capacity. While most climate-related mobility currently occurs within countries, desperation and deteriorating environments can also compel people to seek a livelihood elsewhere through (23) _______ migration. While climate change negatively impacts everyone, everywhere, those already in vulnerable situations due to geography, poverty, gender, age, disability, origin, or other status, including migrant women who (24) _______ climate-sensitive livelihoods, and children who are less able to survive extreme weather events, are at the greatest risk of suffering harm. It is (25) _______ to recognise this reality and take meaningful action to protect the human rights of those most affected by climate change, including migrants.<br>Select the most appropriate option to fill in blank number 25.</p>",
                    options_en: ["<p>optional</p>", "<p>secondary</p>", 
                                "<p>resistant</p>", "<p>imperative</p>"],
                    options_hi: ["<p>optional</p>", "<p>secondary</p>",
                                "<p>resistant</p>", "<p>imperative</p>"],
                    solution_en: "<p>25.(d) imperative<br>&lsquo;Imperative&rsquo; means extremely important or urgent. The given passage states that it is imperative to recognise this reality. Hence, &lsquo;imperative&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>25.(d) imperative<br>&lsquo;Imperative&rsquo; का अर्थ है अत्यंत महत्वपूर्ण। दिए गए passage में कहा गया है कि इस वास्तविकता(reality) को पहचानना अनिवार्य(imperative) है। अतः, &lsquo;imperative&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>