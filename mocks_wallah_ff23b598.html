<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">90:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 90 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["33"] = {
                name: "CBT",
                start: 0,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="33">CBT</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "33",
                    question_en: "<p>1. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><msqrt><mn>5</mn><mi>&#160;</mi></msqrt><mo>+</mo><msqrt><mn>125</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><msqrt><mn>80</mn></msqrt><mo>+</mo><mn>6</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow></mfrac></math> is..</p>",
                    question_hi: "<p>1. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><msqrt><mn>5</mn><mi>&#160;</mi></msqrt><mo>+</mo><msqrt><mn>125</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><msqrt><mn>80</mn></msqrt><mo>+</mo><mn>6</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow></mfrac></math>का मान क्या है ?</p>",
                    options_en: ["<p>an irrational number</p>", "<p>a rational number</p>", 
                                "<p>an integer</p>", "<p>a natural number</p>"],
                    options_hi: ["<p>एक अपरिमेय संख्या</p>", "<p>एक परिमेय संख्या</p>",
                                "<p>पूर्णांक</p>", "<p>एक प्राकृतिक संख्या</p>"],
                    solution_en: "<p>1.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><msqrt><mn>5</mn><mi>&#160;</mi></msqrt><mo>+</mo><msqrt><mn>125</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><msqrt><mn>80</mn></msqrt><mo>+</mo><mn>6</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>5</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><mn>4</mn><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>6</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><msqrt><mn>5</mn></msqrt></mrow><mrow><mn>10</mn><msqrt><mn>5</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> (Rational number)</p>",
                    solution_hi: "<p>1.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><msqrt><mn>5</mn><mi>&#160;</mi></msqrt><mo>+</mo><msqrt><mn>125</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><msqrt><mn>80</mn></msqrt><mo>+</mo><mn>6</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>3</mn><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>5</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow><mrow><mo>(</mo><mn>4</mn><msqrt><mn>5</mn></msqrt><mo>+</mo><mn>6</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><msqrt><mn>5</mn></msqrt></mrow><mrow><mn>10</mn><msqrt><mn>5</mn></msqrt></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> (परिमेय संख्या)</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "33",
                    question_en: "<p>2. The dance form &lsquo;Chharhi&rsquo; has originated from the state of :</p>",
                    question_hi: "<p>2. नृत्य रूप \'छरही\' की उत्पत्ति किस राज्य से हुई है?</p>",
                    options_en: ["<p>Bihar</p>", "<p>West Bengal</p>", 
                                "<p>Himachal Pradesh</p>", "<p>Mizoram</p>"],
                    options_hi: ["<p>बिहार</p>", "<p>पश्चिम बंगाल</p>",
                                "<p>हिमाचल प्रदेश</p>", "<p>मिजोरम</p>"],
                    solution_en: "<p>2.(c) <strong>Himachal Pradesh. Folk Dance in India:</strong> Himachal Pradesh - Jhora, Jhali, Chharhi, Dhaman, Chhapeli, Mahasu, Nati, Dangi. Bihar - Jata-Jatin, Bakho - Bakhain, Panwariya. Mizoram - Cheraw Dance, Khuallam, Chailam, Sawlakin, Chawnglaizawn, Zangtalam.</p>",
                    solution_hi: "<p>2.(c) <strong>हिमाचल प्रदेश। भारत में लोक नृत्य: </strong>हिमाचल प्रदेश - झोड़ा, झाली, छरही, धामन, छपेली, महासू, नाटी, डांगी। बिहार - जाट-जटिन, बखो-बखैन, पंवरिया। मिजोरम - चेराव नृत्य, खुल्लम, चैलम, सावलकिन, चावंगलाइज़ोन, ज़ंगतालम।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "33",
                    question_en: "<p>3. The sum of three consecutive numbers is 177. The mean of these three numbers is:</p>",
                    question_hi: "<p>3. तीन क्रमिक संख्याओं का योग 177 है। इन तीन संख्याओं का माध्य क्या होगा?</p>",
                    options_en: ["<p>59</p>", "<p>60</p>", 
                                "<p>57</p>", "<p>58</p>"],
                    options_hi: ["<p>59</p>", "<p>60</p>",
                                "<p>57</p>", "<p>58</p>"],
                    solution_en: "<p>3.(a)<br>Let the first consecutive number be x .<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mi>x</mi><mo>+</mo><mn>1</mn><mo>+</mo><mi>x</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>177</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi>x</mi><mo>=</mo><mn>177</mn><mo>-</mo><mn>3</mn></math><br><math display=\"inline\"><mo>&#8658;</mo><mn>3</mn><mi>x</mi><mo>=</mo><mn>174</mn></math> &rArr; x = 58<br>Numbers are 58 , 59 , 60<br>Hence, the mean = 59<math display=\"inline\"><mi>&#4966;</mi></math><br>Alternate<math display=\"inline\"><mi>&#4966;</mi></math><br>Mean = <math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>u</mi><mi>m</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>t</mi><mi>h</mi><mi>e</mi><mi>&#160;</mi><mi>g</mi><mi>i</mi><mi>v</mi><mi>e</mi><mi>n</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi></mrow><mrow><mi>t</mi><mi>o</mi><mi>t</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>177</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 59</p>",
                    solution_hi: "<p>3.(a) माना, पहली क्रमागत संख्या x है।<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mo>+</mo><mi>x</mi><mo>+</mo><mn>1</mn><mo>+</mo><mi>x</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>177</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi>x</mi><mo>=</mo><mn>177</mn><mo>-</mo><mn>3</mn></math><br><math display=\"inline\"><mo>&#8658;</mo><mn>3</mn><mi>x</mi><mo>=</mo><mn>174</mn></math> &rArr; x = 58<br>संख्याएं 58, 59, 60 हैं<br>अतः माध्य = 59<br>एकांतर<math display=\"inline\"><mi>&#4966;</mi></math><br>माध्य = <math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2368;</mi><mi>&#160;</mi><mi>&#2327;&#2312;</mi><mi>&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mi>&#160;</mi><mi>&#2351;&#2379;&#2327;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mi>&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow></mfrac></math>&nbsp;</p>\n<p>= <math display=\"inline\"><mfrac><mrow><mn>177</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 59</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "33",
                    question_en: "<p>4. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>Cloth : Shirt : : Wood : ?</p>",
                    question_hi: "<p>4. उस विकल्प का चयन करें जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br>कपड़ा : कमीज : : लकड़ी : ?</p>",
                    options_en: ["<p>Ash</p>", "<p>Trouser</p>", 
                                "<p>Furniture</p>", "<p>Tree</p>"],
                    options_hi: ["<p>राख</p>", "<p>पतलून</p>",
                                "<p>फर्नीचर</p>", "<p>पेड़</p>"],
                    solution_en: "<p>4.(c) Clothes : Shirt. [Shirts are one type of variety of clothes]<br>Using the same analogy<br>Wood : Furniture</p>",
                    solution_hi: "<p>4.(c)<br>कपड़े : कमीज [शर्ट एक प्रकार के कपड़े हैं]<br>उसी सादृश्य का उपयोग करते हुए<br>लकड़ी : फर्नीचर</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "33",
                    question_en: "<p>5. Which of the following is a scalar quantity?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन सी एक अदिश राशि है?</p>",
                    options_en: ["<p>Momentum</p>", "<p>Force</p>", 
                                "<p>Mass</p>", "<p>Velocity</p>"],
                    options_hi: ["<p>संवेग</p>", "<p>बल</p>",
                                "<p>द्रव्यमान</p>", "<p>वेग</p>"],
                    solution_en: "<p>5.(c) <strong>Mass.</strong> Scalar Quantity - A scalar quantity is defined as the physical quantity with only magnitude and no direction. Example - speed, distance, mass, etc. Vector quantity - The physical quantity in which both quantity (magnitude) and direction are contained. Examples - velocity, force, momentum, etc.</p>",
                    solution_hi: "<p>5.(c) <strong>द्रव्यमान</strong> । अदिश राशि - एक अदिश राशि को एक ऐसी भौतिक राशि के रूप में परिभाषित किया जाता है जिसमें केवल परिमाण होता है और कोई दिशा नहीं होती है। उदाहरण - गति, दूरी, द्रव्यमान आदि। सदिश राशि - वह भौतिक राशि जिसमें मात्रा (परिमाण) और दिशा दोनों निहित होती हैं। उदाहरण - वेग, बल, संवेग आदि।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "33",
                    question_en: "<p>6. (1 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>n</mi></mrow></mfrac></math>) + (1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi>n</mi></mfrac></math>) + (1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mi>n</mi></mfrac></math>) + up to n terms will result as:</p>",
                    question_hi: "<p>6. (1 - <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>n</mi></mrow></mfrac></math>) + (1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi>n</mi></mfrac></math>) + (1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mi>n</mi></mfrac></math>)+ _________ n पदों तक, परिणाम क्या होगा ?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>2</mn><mi>n</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><mi>n</mi></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>2</mn><mi>n</mi><mo>-</mo><mn>1</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn><mi>n</mi></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>"],
                    solution_en: "<p>6.(d)</p>\n<p>(1 -&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>n</mi></mrow></mfrac></math>) + (1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi>n</mi></mfrac></math>) + (1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mi>n</mi></mfrac></math>)&hellip;&hellip;&hellip;+ up to n terms<br>= (n) <math display=\"inline\"><mo>-</mo></math> <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>n</mi></mrow></mfrac></math>(1 + 2 + 3 + 4 +... + n)<br>= (n) <math display=\"inline\"><mo>-</mo></math> <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>n</mi></mrow></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>= (n) <math display=\"inline\"><mo>-</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>",
                    solution_hi: "<p>6.(d)</p>\n<p>(1 -&nbsp;<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>n</mi></mrow></mfrac></math>) + (1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mi>n</mi></mfrac></math>) + (1 - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mi>n</mi></mfrac></math>) &hellip;&hellip;&hellip;.+ n पद तक <br>= (n) <math display=\"inline\"><mo>-</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>n</mi></mfrac></mstyle></math>(1 + 2 + 3 + 4 +... + n)<br>= (n) <math display=\"inline\"><mo>-</mo></math> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>n</mi></mfrac></math> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math><br>= (n) <math display=\"inline\"><mo>-</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>n</mi><mo>+</mo><mn>1</mn><mo>)</mo></mrow><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>n</mi><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "33",
                    question_en: "<p>7. In which of the following states is the Mathuri folk dance practised?</p>",
                    question_hi: "<p>7. निम्नलिखित में से किस राज्य में माथुरी लोक नृत्य प्रचलित है?</p>",
                    options_en: ["<p>Goa</p>", "<p>Telangana</p>", 
                                "<p>Mizoram</p>", "<p>Jharkhand</p>"],
                    options_hi: ["<p>गोवा</p>", "<p>तेलंगाना</p>",
                                "<p>मिजोरम</p>", "<p>झारखंड</p>"],
                    solution_en: "<p>7.(b) <strong>Telangana</strong> (Adilabad district). The Mathuri tribe (Koppu Lambadi) dances on the occasion of Krishna Ashtami in Sravana Masam. Other Folk Dances - Gussadi dance, Dhimsa dance, Lambadi dance, Perini Sivatandavam, and Dappu dance. Tribes of Telangana - Gond, Koya, Lambada, Banjara, Chenchu, Kolam, Thoti.</p>",
                    solution_hi: "<p>7.(b) <strong>तेलंगाना</strong> (आदिलाबाद जिला)। मथुरी जनजाति (कोप्पु लंबाडी) श्रावण मास में कृष्ण अष्टमी के अवसर पर नृत्य करती है। अन्य लोक नृत्य - गुसाडी नृत्य, ढिम्सा नृत्य, लंबाडी नृत्य, पेरिनी शिव तांडवम, और दप्पू नृत्य। तेलंगाना की जनजातियाँ - गोंड, कोया, लंबाडा, बंजारा, चेंचू, कोलम, थोटी।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "33",
                    question_en: "<p>8. The intercepts made by the plane 3x - 4y - 2z = 6 with the coordinate axis are:</p>",
                    question_hi: "<p>8. निर्देशांक अक्ष के साथ समतल 3x - 4y - 2z = 6 द्वारा किए गए अंतःखंड हैं:</p>",
                    options_en: ["<p>- 2 , <math display=\"inline\"><mo>-</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> , 3</p>", "<p>2 , <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> , - 3</p>", 
                                "<p>- 2 , <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> , 3</p>", "<p>2 , <math display=\"inline\"><mo>-</mo></math><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>, - 3</p>"],
                    options_hi: ["<p><math display=\"inline\"><mo>-</mo></math> 2 , - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>, 3</p>", "<p>2 , <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> , - 3</p>",
                                "<p>- 2 , <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> , 3</p>", "<p>2 , <math display=\"inline\"><mo>-</mo></math><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>, - 3</p>"],
                    solution_en: "<p>8.(d)<br>To find the intercepts made by the plane 3x - 4y - 2z = 6 with the coordinate axis <br>we have to keep x = y = 0 to find z intercept <br>we have to keep y = z = 0 to find x intercept <br>and we have to keep x = z = 0 to find y intercept ;<br>So , x = 2 , y = - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> , z = - 3 ;</p>",
                    solution_hi: "<p>8.(d) निर्देशांक अक्ष के साथ तल 3x - 4y - 2z = 6 द्वारा किए गए अंतःखंडों को खोजने के लिए z अंत:खंड ज्ञात करने के लिए हमें x = y = 0 रखना होगा<br>x अंत:खंड ज्ञात करने के लिए हमें y = z = 0 रखना होगा<br>और हमें y अंत:खंड ज्ञात करने के लिए x = z = 0 रखना होगा;<br>तो , x = 2, y = - <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> , z = - 3 ;</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "33",
                    question_en: "<p>9. Select the option that is related to the third term in the same way as the second term is related to the first term.<br>Hand : Thumb :: Pen : ?</p>",
                    question_hi: "<p>9. उस विकल्प का चयन कीजिये जो तीसरे पद से उसी प्रकार संबंधित है जैसे दूसरा पद पहले पद से संबंधित है।<br>हस्त : अंगूठा :: पेन : ?</p>",
                    options_en: ["<p>Nib</p>", "<p>Holder</p>", 
                                "<p>Paper</p>", "<p>Finger</p>"],
                    options_hi: ["<p>निब</p>", "<p>होल्डर</p>",
                                "<p>कागज़</p>", "<p>अंगुली</p>"],
                    solution_en: "<p>9.(a) As the thumb is related to the hand, similarly, nib is related to the pen.</p>",
                    solution_hi: "<p>9.(a) जैसे अंगूठा हाथ से संबंधित है, वैसे ही निब का संबंध कलम से है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "33",
                    question_en: "<p>10.Name the world famous scientist known for his theory of relativity.</p>",
                    question_hi: "<p>10. उस विश्व प्रसिद्ध वैज्ञानिक का नाम बताइए जिसे उनके सापेक्षता के सिद्धांत के लिए जाना जाता है।</p>",
                    options_en: ["<p>Thomas Alva Edison</p>", "<p>Christian Bernard</p>", 
                                "<p>Albert Einstein</p>", "<p>John Dalton</p>"],
                    options_hi: ["<p>थॉमस अल्वा एडीसन</p>", "<p>क्रिश्चियन बर्नार्ड</p>",
                                "<p>अल्बर्ट आइंस्टीन</p>", "<p>जॉन डाल्टन</p>"],
                    solution_en: "<p>10.(c) <strong>Albert Einstein. </strong>According to this theory, mass can be converted into energy and vice-versa. i.e., E = <math class=\"wrs_chemistry\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>mc</mi><mn>2</mn></msup></math>, Where, c = velocity of light. E is the energy equivalent of mass m. Christian Bernard was a South African Cardiac Surgeon who performed the world\'s first human-to-human Heart transplant operation. John Dalton&rsquo;s important contribution was the atomic theory. Thomas Alva Edison\'s invention is well known for his invention of the light bulb.</p>",
                    solution_hi: "<p>10.(c) <strong>अल्बर्ट आइंस्टीन</strong>। इस सिद्धांत के अनुसार, द्रव्यमान को ऊर्जा में परिवर्तित किया जा सकता है और इसके विपरीत यानी, E = <math class=\"wrs_chemistry\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>mc</mi><mn>2</mn></msup></math>, जहां, c = प्रकाश का वेग। E ऊर्जा है जो द्रव्यमान m के बराबर है। क्रिश्चियन बर्नार्ड एक दक्षिण अफ़्रीकी कार्डियक सर्जन थे जिन्होंने दुनिया का पहला मानव-से-मानव हृदय प्रत्यारोपण ऑपरेशन किया था। जॉन डाल्टन परमाणु सिद्धांत मे महत्वपूर्ण योगदान दिया था। थॉमस अल्वा एडिसन प्रकाश बल्ब के आविष्कार के लिए जाने जाते है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "33",
                    question_en: "<p>11. The LCM and the HCF of two numbers are given as 459 and 3, respectively. If one number is 51, then find the other number.</p>",
                    question_hi: "<p>11. दो संख्याओं के लघुत्तम समापवर्त्य (LCM) और महत्तम समापवर्तक (HCF) क्रमशः 459 और 3 हैं। यदि एक संख्या 51 है, तो दूसरी संख्या ज्ञात कीजिए ।</p>",
                    options_en: ["<p>37</p>", "<p>21</p>", 
                                "<p>27</p>", "<p>33</p>"],
                    options_hi: ["<p>37</p>", "<p>21</p>",
                                "<p>27</p>", "<p>33</p>"],
                    solution_en: "<p>11.(c) Other number <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mo>.</mo><mi>C</mi><mo>.</mo><mi>M</mi><mo>&#215;</mo><mi>H</mi><mo>.</mo><mi>C</mi><mo>.</mo><mi>F</mi></mrow><mrow><mi>f</mi><mi>i</mi><mi>r</mi><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>459</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>51</mn></mfrac></math> = 27</p>",
                    solution_hi: "<p>11.(c) दूसरी संख्या = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>L</mi><mo>.</mo><mi>C</mi><mo>.</mo><mi>M</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>H</mi><mo>.</mo><mi>C</mi><mo>.</mo><mi>F</mi></mrow><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mi>&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>=</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>459</mn><mo>&#215;</mo><mn>3</mn></mrow><mn>51</mn></mfrac></math> = 27</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "33",
                    question_en: "<p>12. Dr Lakshminarayan Subramaniam is an Indian ______was honoured with the coveted Padma Bhushan in 2001.</p>",
                    question_hi: "<p>12. डॉ लक्ष्मीनारायण सुब्रमण्यम एक भारतीय ______ हैं जिन्हें 2001 में प्रतिष्ठित पद्म भूषण से सम्मानित किया गया था।</p>",
                    options_en: ["<p>Sitarist</p>", "<p>Pianist</p>", 
                                "<p>Guitarist</p>", "<p>Violinist</p>"],
                    options_hi: ["<p>सितार वादक</p>", "<p>पियानो वादक</p>",
                                "<p>गिटार वादक</p>", "<p>वायलिन वादक</p>"],
                    solution_en: "<p>12.(d) <strong>Violinist.</strong> Dr Lakshminarayan Subramaniam Awards - Padma Shri (1988), Sangeet Natak Akademi Award (1990). Some famous Violinist players - Balabhaskar, Gingger Shankar, Johar Ali Khan, Kala Ramnath, L. Athira Krishna, L. Shankar, Lalgudi Jayaraman, M. S. Gopalakrishnan, Mehli Mehta.</p>",
                    solution_hi: "<p>12.(d) <strong>वायलिन वादक</strong>। डॉ. लक्ष्मीनारायण सुब्रमण्यम पुरस्कार - पद्म श्री (1988), संगीत नाटक अकादमी पुरस्कार (1990)। कुछ प्रसिद्ध वायलिन वादक - बालाभास्कर, जिंजर शंकर, जौहर अली खान, कला रामनाथ, एल. अथिरा कृष्णा, एल. शंकर, लालगुडी जयारमन, M. S. गोपालकृष्णन, मेहली मेहता।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "33",
                    question_en: "<p>13. Find the ODD one out from the given options.</p>",
                    question_hi: "<p>13. दिए गए विकल्पों में से असंगत को चुनें।</p>",
                    options_en: ["<p>317</p>", "<p>137</p>", 
                                "<p>153</p>", "<p>731</p>"],
                    options_hi: ["<p>317</p>", "<p>137</p>",
                                "<p>153</p>", "<p>731</p>"],
                    solution_en: "<p>13.(c) <strong>Logic:-</strong> except 153, all numbers are formed by using digits 1, 3 and 7.</p>",
                    solution_hi: "<p>13.(c) <strong>तर्क:-</strong> 153 को छोड़कर सभी संख्याएँ अंक 1, 3 और 7 का उपयोग करके बनाई जाती हैं।.</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "33",
                    question_en: "<p>14. In which of the following cases, no work is done ?</p>",
                    question_hi: "<p>14. निम्न में से किस मामले में, कोई कार्य नहीं होता ?</p>",
                    options_en: ["<p>A windmill is extracting water from a well.</p>", "<p>A donkey is walking with a weight on its back.</p>", 
                                "<p>Suman is swimming in a pool.</p>", "<p>An engine is pulling a train.</p>"],
                    options_hi: ["<p>एक पवन चक्की कुएँ से पानी निकाल रही है।</p>", "<p>एक गधा अपनी पीठ पर वजन लेकर चल रहा है।</p>",
                                "<p>सुमन एक पूल में तैर रही है।</p>", "<p>एक इंजन ट्रेन को खींच रहा है।</p>"],
                    solution_en: "<p>14. (b) <strong>A donkey is carrying a weight on its back.</strong> If the donkey is walking but the weight on its back remains stationary (not moving), then no work is done on the weight. This is because there is no displacement of the weight in the direction of the force applied by the donkey. W = F.d cos(90) = 0.</p>",
                    solution_hi: "<p>14. (b) <strong>एक गधा अपनी पीठ पर वजन लेकर चल रहा है।</strong> यदि गधा चल रहा है लेकिन उसकी पीठ पर वजन स्थिर (चल नहीं रहा) रहता है, तो वजन पर कोई कार्य नहीं होता है। ऐसा इसलिए है क्योंकि गधे द्वारा लगाए गए बल की दिशा में वजन का कोई विस्थापन नहीं होता है। W = F.d cos(90) = 0.</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "33",
                    question_en: "<p>15. Two numbers are in the ratio 3 : 2. Their LCM and HCF are 24 and 4, respectively. The greater of the two numbers is:</p>",
                    question_hi: "<p>15. दो संख्याएँ 3 : 2 के अनुपात में हैं। उनका लघुत्तम समापवर्त्य और म.स.प क्रमशः 24 और 4 है। दो संख्याओं में से बड़ी है:</p>",
                    options_en: ["<p>8</p>", "<p>12</p>", 
                                "<p>14</p>", "<p>10</p>"],
                    options_hi: ["<p>8</p>", "<p>12</p>",
                                "<p>14</p>", "<p>10</p>"],
                    solution_en: "<p>15.(b) <br>Let the numbers are 3x and 2x.<br>Product of the numbers = L.C.M &times; H.C.F <br>= 24 &times; 4 = 96<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>x</mi><mo>&#215;</mo><mn>2</mn><mi>x</mi><mo>=</mo><mn>96</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mo>&#8658;</mo><mn>6</mn><msup><mi>x</mi><mn>2</mn></msup><mo>=</mo><mn>96</mn></mstyle></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>x</mi></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>96</mn><mn>6</mn></mfrac></msqrt><mo>=</mo><mn>4</mn></math><br>Greater number = 3x = 12</p>",
                    solution_hi: "<p>15.(b) माना संख्याएँ 3x और 2x हैं।<br>संख्याओं का गुणनफल = ल . स. &times; म. स. <br>= 24 &times; 4 = 96<br><math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>x</mi><mo>&#215;</mo><mn>2</mn><mi>x</mi><mo>=</mo><mn>96</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mo>&#8658;</mo><mn>6</mn><msup><mi>x</mi><mn>2</mn></msup><mo>=</mo><mn>96</mn></mstyle></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>x</mi></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>96</mn><mn>6</mn></mfrac></msqrt><mo>=</mo><mn>4</mn></math><br>बड़ी संख्या = 3x = 12</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "16",
                    section: "33",
                    question_en: "<p>16. \'Nishagandhi Dance Festival\' is celebrated in which of these states?</p>",
                    question_hi: "<p>16. इनमें से किस राज्य में \'निशागांधी नृत्य महोत्सव\' मनाया जाता है?</p>",
                    options_en: ["<p>Kerala</p>", "<p>Karnataka</p>", 
                                "<p>Tamil Nadu</p>", "<p>Uttar Pradesh</p>"],
                    options_hi: ["<p>केरल</p>", "<p>कर्नाटक</p>",
                                "<p>तमिलनाडु</p>", "<p>उत्तर प्रदेश</p>"],
                    solution_en: "<p>16.(a) <strong>Nishagandhi Nritye Utsav</strong> {Kerala (Trivandrum), Time - October- November and March- April, Duration- 1 week}. Festivals of Kerala - Adoor Gajamela, Attuvela Mahotsavam, Chettikulangara Bharani, Machattu Mamangam, Thirunakkara Arattu, Attukal Pongala, Kalpathi Ratholsavam etc. Karnataka - Kambala Festival, Hampi Festival, Pattadakal Dance Festival, Makar Sankranti, Ugadi, Vairamudi Festival, Karaga Festival etc. Tamil Nadu - Pongal Festival, Natyanjali Dance Festival, Karthigai Deepam, Jallikattu Bull Festival, Vinayaka Chathurthi etc.</p>",
                    solution_hi: "<p>16.(a) <strong>निशागंधी नृत्य उत्सव</strong> {केरल (त्रिवेंद्रम), समय - अक्टूबर-नवंबर और मार्च-अप्रैल, अवधि- 1 सप्ताह}। केरल के त्यौहार - अडूर गजमेला, अट्टुवेला महोत्सवम, चेट्टीकुलंगरा भरानी, मचाट्टू ममंगम, थिरुनक्करा अरट्टू, अट्टुकल पोंगाला, कल्पथी राठोलस्वम आदि। कर्नाटक - कंबाला महोत्सव, हम्पी महोत्सव, पट्टदकल नृत्य महोत्सव, मकर संक्रांति, उगादी, वैरामुडी महोत्सव, करगा महोत्सव आदि। तमिलनाडु - पोंगल महोत्सव, नट्यांजलि नृत्य महोत्सव, कार्तिगई दीपम, जल्लीकट्टू बुल फेस्टिवल, विनायक चतुर्थी आदि।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "17",
                    section: "33",
                    question_en: "<p>17. In this question, a group of numbers/symbols is coded using letters as per the table given below and the conditions which follow. The correct combination of codes following the conditions is your answer.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173710290.png\" alt=\"rId5\" width=\"419\" height=\"49\"> <br><strong>Conditions:</strong><br>(i) If the first element is a symbol and the last is a number, the codes for these two (the first and the last elements) are to be interchanged.<br>(i) If the first element is an odd number and the last is an even number, the first and the last elements are to be coded as<br>(iii) If both the second and the third elements are perfect squares, the third element is to be coded as the code for the second element.<br>Question : +#7&amp;6</p>",
                    question_hi: "<p>17. इस प्रश्न में, संख्याओं/ प्रतीकों के एक समूह को नीचे दी गई तालिका और उसके बाद दी गई शर्तों के अनुसार अक्षरों का उपयोग करके कूटबद्ध किया जाता है। शर्तों का पालन करने वाले कूटों का सही संयोजन आपका उत्तर है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173710427.png\" alt=\"rId6\" width=\"376\" height=\"53\"> <br><strong>शर्तें:</strong><br>(i) यदि पहला घटक एक प्रतीक है और अंतिम घटक एक संख्या है, तो इन दोनों (पहले और अंतिम घटक) के कूटों को परस्पर बदल दिया जाएगा।<br>(ii) यदि पहला घटक एक विषम संख्या है और अंतिम घटक एक सम संख्या है, तो पहले और अंतिम दोनों घटकों को के रूप में कूटबद्ध किया जाएगा।<br>(iii) यदि दूसरा और तीसरा दोनों घटक पूर्ण वर्ग हैं, तो तीसरे घटक को दूसरे घटक के कूट के रूप में कूटबद्ध किया जाएगा।<br>प्रश्न: + # 7 &amp; 6 का कूट क्या होगा?</p>",
                    options_en: ["<p>DEPSR</p>", "<p>RDPRS</p>", 
                                "<p>SDPER</p>", "<p>PERDS</p>"],
                    options_hi: ["<p>DEPSR</p>", "<p>RDPRS</p>",
                                "<p>SDPER</p>", "<p>PERDS</p>"],
                    solution_en: "<p>17.(c) given: + # 7 &amp; 6<br>Code for +#7&amp;6 (Without apply condition) = RDPES<br>According to the given conditions:- <br>Applying the condition no. (I)<br>Code for +#7&amp;6 = SDPER</p>",
                    solution_hi: "<p>17.(c) दिया गया : + # 7 &amp; 6<br>के लिए कोड +#7&amp;6 के लिए कोड (लागू शर्त के बिना) = RDPES<br>दी गई शर्त (I) के अनुसार:-<br>के लिए कोड +#7&amp;6 = SDPER</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "18",
                    section: "33",
                    question_en: "<p>18. Through which of the following mediums can sound NOT travel?</p>",
                    question_hi: "<p>18. निम्नलिखित में से किस माध्यम से ध्वनि गति नहीं कर सकती है?</p>",
                    options_en: ["<p>Steel</p>", "<p>Vacuum</p>", 
                                "<p>Air</p>", "<p>Milk</p>"],
                    options_hi: ["<p>इस्पात</p>", "<p>निर्वात</p>",
                                "<p>वायु</p>", "<p>दूध</p>"],
                    solution_en: "<p>18.(b) <strong>vacuum.</strong> Sound waves are longitudinal waves that travel through a medium like air or water and the speed of sound in air is 343 m/sec. In any medium, the speed of sound is independent of frequency. The speed of sound is maximum in solids and minimum in gases. The unit of loudness of sound is decibel (dB). Among all metals, speed of sound is maximum in Aluminium.</p>",
                    solution_hi: "<p>18.(b) <strong>निर्वात।</strong> ध्वनि तरंगें अनुदैर्ध्य तरंगें हैं जो हवा या पानी जैसे माध्यम से चलती हैं और हवा में ध्वनि की गति 343 मीटर/सेकंड होती है। किसी भी माध्यम में ध्वनि की गति आवृत्ति से स्वतंत्र होती है। ध्वनि की गति ठोस में अधिकतम तथा गैस में न्यूनतम होती है। ध्वनि की प्रबलता की इकाई डेसिबल (dB) है। सभी धातुओं में ध्वनि की गति सबसे अधिक एल्युमीनियम में होती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "19",
                    section: "33",
                    question_en: "<p>19. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msqrt><mn>211600</mn></msqrt><mo>+</mo><msqrt><mn>400</mn></msqrt></mrow><mn>48000</mn></mfrac></msqrt></math><strong>&nbsp;</strong> equals :</p>",
                    question_hi: "<p>19. <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msqrt><mn>211600</mn></msqrt><mo>+</mo><msqrt><mn>400</mn></msqrt></mrow><mn>48000</mn></mfrac></msqrt></math>&nbsp;किसके बराबर है :</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>48</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>48</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>19.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msqrt><mn>211600</mn></msqrt><mo>+</mo><msqrt><mn>400</mn></msqrt></mrow><mn>48000</mn></mfrac></msqrt></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>460</mn><mo>+</mo><mn>20</mn></mrow><mn>48000</mn></mfrac></msqrt></math><br><math display=\"inline\"><mo>=</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>1</mn><mn>100</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></p>",
                    solution_hi: "<p>19.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><msqrt><mn>211600</mn></msqrt><mo>+</mo><msqrt><mn>400</mn></msqrt></mrow><mn>48000</mn></mfrac></msqrt></math></p>\n<p>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mn>460</mn><mo>+</mo><mn>20</mn></mrow><mn>48000</mn></mfrac></msqrt></math><br><math display=\"inline\"><mo>=</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>1</mn><mn>100</mn></mfrac></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>10</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "20",
                    section: "33",
                    question_en: "<p>20. Which of the following festivals is also known as \'peerla panduga&rsquo;?</p>",
                    question_hi: "<p>20. निम्नलिखित में से किस त्योहार को &lsquo;पीरला पांडुगा (peerla panduga)\' के नाम से भी जाना जाता है?</p>",
                    options_en: ["<p>Bakrid</p>", "<p>Lailat al-Qadr</p>", 
                                "<p>Muharam</p>", "<p>Ramadan</p>"],
                    options_hi: ["<p>बकरीद</p>", "<p>लैलतुल-क़द्र (Lailat al-Qadr)</p>",
                                "<p>मुहर्रम</p>", "<p>रमज़ान (Ramadan)</p>"],
                    solution_en: "<p>20.(c) <strong>Muharam.</strong> Peerla Panduga is&nbsp;a festival celebrated by Hindus and Muslims in the Telangana State. Famous Festivals of Telangana: Bathukamma - A state festival; Concurrence with Durga Navratri. Bonalu - Celebrated in the month of Ashada (July or August); During this festival, women prepare a Bonam and offer it to the Goddess by carrying it on their heads, Pothuraju, Rangam, Ghatam. Samakka Saarakka Jaathara - A biennial festival celebrated in the month of February; To commemorate the Mother-Daughter-duo(Samakka-Saarakka), who stood against the oppression of Kakatiyas in 13th century.</p>",
                    solution_hi: "<p>20.(c) <strong>मुहर्रम।</strong> पीरला पांडुगा तेलंगाना राज्य में हिंदुओं और मुसलमानों द्वारा मनाया जाने वाला एक त्योहार है। तेलंगाना के प्रसिद्ध त्यौहार: बथुकम्मा - एक राज्य उत्सव; दुर्गा नवरात्रि में मनाया जाता है । बोनालू - आषाढ़ (जुलाई या अगस्त) के महीने में मनाया जाता है; इस त्योहार के दौरान, महिलाएं एक बोनम तैयार करती हैं और इसे अपने सिर, पोथुराजू, रंगम, घटम पर ले जाकर देवी को अर्पित करती हैं। समक्का सारक्का जाथारा - फरवरी के महीने में मनाया जाने वाला एक द्विवार्षिक त्योहार; 13वीं शताब्दी में काकतीय लोगों के उत्पीड़न के खिलाफ खड़े होने वाली मां-बेटी की जोड़ी (समक्का-सारक्का) को याद करने के लिए।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "21",
                    section: "33",
                    question_en: "<p>21. In this question, a set of numbers/symbols are coded using letters as per the table given below followed by the conditions. The correct combination of codes according to the conditions will be your answer.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173710569.png\" alt=\"rId7\" width=\"473\" height=\"43\"><br><strong>terms:</strong><br>(i) If the first element is a symbol, and the last element is a number, then the codes of these two should be interchanged.<br>(ii) If the first component is an odd number, and the last component an even number, then the first and the last components are to be coded as <math display=\"inline\"><mi>&#981;</mi></math>.<br>(iii) If both the first and third components are numbers, then the third component should be coded as the code for the first component.<br>How will \'2 @ 7 # = 5\' be coded?</p>",
                    question_hi: "<p>21. इस प्रश्न में, संख्याओं/प्रतीकों के एक समूह को नीचे दी गई तालिका और उसके बाद दी गई शर्तों के अनुसार अक्षरों का उपयोग करके कूटबद्ध किया गया है। शर्तों के अनुसार कूटों का सही संयोजन आपका उत्तर होगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173710690.png\" alt=\"rId8\" width=\"487\" height=\"53\"> <br><strong>शर्तें:</strong><br>(i) यदि पहला घटक एक प्रतीक है, और अंतिम घटक एक संख्या है, तो इन दोनों के कूटों को आपस में बदला जाना चाहिए।<br>(ii) यदि पहला घटक एक विषम संख्या है, और अंतिम घटक एक सम संख्या है, तो पहले और अंतिम घटकों को <math display=\"inline\"><mi>&#981;</mi></math> के रूप में कूटबद्ध किया जाना चाहिए।<br>(iii) यदि पहले और तीसरे दोनों घटक संख्याएं हैं, तो तीसरे घटक को पहले घटक के कूट के रूप में कूटबद्ध किया जाना चाहिए।<br>\'2 @ 7 # = 5\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>Q T A Y D C</p>", "<p>Q T Q Y D C</p>", 
                                "<p>A T Q Y D X</p>", "<p>A T A Y D X</p>"],
                    options_hi: ["<p>Q T A Y D C</p>", "<p>Q T Q Y D C</p>",
                                "<p>A T Q Y D X</p>", "<p>A T A Y D X</p>"],
                    solution_en: "<p>21.(d) The components given in the question are &ldquo;2 @ 7 # = 5&rdquo;<br>&ldquo;2 @ 7 # = 5&rdquo; will be coded as &rArr; ATQYDX<br>According to condition 3, on substituting <br>the code of the third component by <br>giving the code of the first component, We get<br>&ldquo;2 @ 7 # = 5&rdquo; will be coded as <br>&rArr;<strong> A T A Y D X</strong></p>",
                    solution_hi: "<p>21.(d)<br>प्रश्न में दिए गए घटक &ldquo;2 @ 7 # = 5&rdquo; हैं<br>&ldquo;2 @ 7 # = 5&rdquo; का कोडित रूप &rArr; ATQYDX<br>शर्त 3 ​​के अनुसार, पहले घटक के कोड को तीसरे घटक के कोड से प्रतिस्थापित करने पर, हम प्राप्त करते हैं<br>&ldquo;2 @ 7 # = 5&rdquo; का कोडित रूप &rArr; <strong>A T A Y D X</strong></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "22",
                    section: "33",
                    question_en: "<p>22. The resistors of values 3&Omega;, 6&Omega; and 15&Omega; are connected in parallel. What will be equivalent resistance in the circuit ?</p>",
                    question_hi: "<p>22. 3&Omega;, 6&Omega; और 15 &Omega; मान के प्रतिरोधक सामानांतर में जुड़े हैं। परिपथ में समतुल्य प्रतिरोध कितना होगा ?</p>",
                    options_en: ["<p>0.8</p>", "<p>1.4</p>", 
                                "<p>2.1</p>", "<p>1.7</p>"],
                    options_hi: ["<p>0.8</p>", "<p>1.4</p>",
                                "<p>2.1</p>", "<p>1.7</p>"],
                    solution_en: "<p>22.(d) <strong>1.7&Omega;.</strong><br>When resistance connected in Parallel : <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>q</mi></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>1</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>2</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>3</mn></msub></mfrac></math>+&hellip;.. +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mi>n</mi></msub></mfrac></math><br>According to question,<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>q</mi></mrow></msub></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>1</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>2</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>3</mn></msub></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>q</mi></mrow></msub></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>15</mn></mfrac><mo>=</mo><mfrac><mn>17</mn><mn>30</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mrow><mi>e</mi><mi>q</mi></mrow></msub><mo>=</mo><mfrac><mn>30</mn><mn>17</mn></mfrac></math> = 1.7 &Omega;.</p>",
                    solution_hi: "<p>22.(d)<strong> 1.7 &Omega;.</strong><br>जब प्रतिरोध समानांतर क्रम में जुड़े हो :<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>q</mi></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>1</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>2</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>3</mn></msub></mfrac></math>+&hellip;.. +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mi>n</mi></msub></mfrac></math><br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>q</mi></mrow></msub></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>1</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>2</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>3</mn></msub></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>q</mi></mrow></msub></mrow></mfrac></math>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>+</mo><mfrac><mn>1</mn><mn>15</mn></mfrac><mo>=</mo><mfrac><mn>17</mn><mn>30</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>R</mi><mrow><mi>e</mi><mi>q</mi></mrow></msub><mo>=</mo><mfrac><mn>30</mn><mn>17</mn></mfrac></math> = 1.7 &Omega;.</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "23",
                    section: "33",
                    question_en: "<p>23. Simplify the given expression.<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>140</mn><mo>&#215;</mo><mn>140</mn><mo>+</mo><mn>280</mn><mo>&#215;</mo><mn>450</mn><mo>&#247;</mo><mn>3</mn><mo>+</mo><mn>150</mn><mo>&#215;</mo><mn>150</mn></mrow><mn>290</mn></mfrac></math></p>",
                    question_hi: "<p>23. दिए गए व्यंजक को सरल कीजिए।<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>140</mn><mo>&#215;</mo><mn>140</mn><mo>+</mo><mn>280</mn><mo>&#215;</mo><mn>450</mn><mo>&#247;</mo><mn>3</mn><mo>+</mo><mn>150</mn><mo>&#215;</mo><mn>150</mn></mrow><mn>290</mn></mfrac></math></p>",
                    options_en: ["<p>270</p>", "<p>290</p>", 
                                "<p>280</p>", "<p>275</p>"],
                    options_hi: ["<p>270</p>", "<p>290</p>",
                                "<p>280</p>", "<p>275</p>"],
                    solution_en: "<p>23.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>140</mn><mo>&#215;</mo><mn>140</mn><mo>+</mo><mn>280</mn><mo>&#215;</mo><mn>450</mn><mo>&#247;</mo><mn>3</mn><mo>+</mo><mn>150</mn><mo>&#215;</mo><mn>150</mn></mrow><mn>290</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>19600</mn><mo>+</mo><mn>280</mn><mo>&#215;</mo><mn>150</mn><mo>+</mo><mn>22500</mn></mrow><mn>290</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>19600</mn><mo>+</mo><mn>42000</mn><mo>+</mo><mn>22500</mn></mrow><mn>290</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>84100</mn></mrow><mrow><mn>290</mn></mrow></mfrac></math> <br>= 290</p>",
                    solution_hi: "<p>23.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>140</mn><mo>&#215;</mo><mn>140</mn><mo>+</mo><mn>280</mn><mo>&#215;</mo><mn>450</mn><mo>&#247;</mo><mn>3</mn><mo>+</mo><mn>150</mn><mo>&#215;</mo><mn>150</mn></mrow><mn>290</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>19600</mn><mo>+</mo><mn>280</mn><mo>&#215;</mo><mn>150</mn><mo>+</mo><mn>22500</mn></mrow><mn>290</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>19600</mn><mo>+</mo><mn>42000</mn><mo>+</mo><mn>22500</mn></mrow><mn>290</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>84100</mn></mrow><mrow><mn>290</mn></mrow></mfrac></math> <br>= 290</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "24",
                    section: "33",
                    question_en: "<p>24. With which of the following sports is the English professional club Chelsea associated?</p>",
                    question_hi: "<p>24. अंग्रेजी पेशेवर क्लब चेल्सी निम्नलिखित में से किस खेल से संबंधित है?</p>",
                    options_en: ["<p>Cricket</p>", "<p>Rugby</p>", 
                                "<p>Football</p>", "<p>Hockey</p>"],
                    options_hi: ["<p>क्रिकेट</p>", "<p>रग्बी</p>",
                                "<p>फ़ुटबॉल</p>", "<p>हॉकी</p>"],
                    solution_en: "<p>24.(c) <strong>Football.</strong> Other Famous Football <strong>Clubs</strong> - Real Madrid, FC Barcelona, Liverpool, Bayern Munich, Juventus, Arsenal. Rugby Clubs - Toulouse, Crusaders, Stormers, Sharks, Leinster, Munster. Hockey clubs - HC Bloemendaal, Oranje Zwart, HC Den Bosch, Club de Campo. Cricket clubs - Sonnet Cricket Club, Calcutta Cricket club.</p>",
                    solution_hi: "<p>24.(c) <strong>फ़ुटबॉल</strong> । अन्य प्रसिद्ध फुटबॉल <strong>क्लब</strong> - रियल मैड्रिड, एफसी बार्सिलोना, लिवरपूल, बायर्न म्यूनिख, जुवेंटस, आर्सेनल। रग्बी क्लब - टूलूज़, क्रूसेडर्स, स्टॉर्मर्स, शार्क, लेइनस्टर, मुंस्टर। हॉकी क्लब - एचसी ब्लोमेंडाल, ओरांजे ज़्वार्ट, एचसी डेन बॉश, क्लब डी कैम्पो। क्रिकेट क्लब - सॉनेट क्रिकेट क्लब, कलकत्ता क्रिकेट क्लब।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "25",
                    section: "33",
                    question_en: "<p>25. What should come in place of \'?\' in the given series? <br>4 : 9, 25 : 49, ? , 289 : 361</p>",
                    question_hi: "<p>25. दी गई श्रृंखला में \'?\' के स्थान पर क्या आना चाहिए?<br>4 : 9, 25 : 49, ? , 289 : 361</p>",
                    options_en: ["<p>729 : 64</p>", "<p>225 : 245</p>", 
                                "<p>121 : 169</p>", "<p>49 : 216</p>"],
                    options_hi: ["<p>729 : 64</p>", "<p>225 : 245</p>",
                                "<p>121 : 169</p>", "<p>49 : 216</p>"],
                    solution_en: "<p>25.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173710950.png\" alt=\"rId9\" width=\"374\" height=\"108\"><br>So the required ratio = 121 : 169</p>",
                    solution_hi: "<p>25.(c)<br><strong>तर्क :</strong> क्रमागत अभाज्य संख्या का वर्ग<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173711174.png\" alt=\"rId10\" width=\"359\" height=\"103\"><br>अतः अभीष्ट अनुपात = 121 : 169 है |</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "26",
                    section: "33",
                    question_en: "<p>26. The magnetic field strength inside a current carrying solenoid is:</p>",
                    question_hi: "<p>26. किसी धारावाही परिनालिका के अंदर चुंबकीय क्षेत्र________ होता है।</p>",
                    options_en: ["<p>less at ends and more at center</p>", "<p>uniform and non-zero</p>", 
                                "<p>more at ends and less at center</p>", "<p>zero</p>"],
                    options_hi: ["<p>सिरों पर कम और केंद्र में अधिक</p>", "<p>एकसमान और अशून्य</p>",
                                "<p>सिरों पर अधिक और केंद्र पर कम</p>", "<p>शून्य</p>"],
                    solution_en: "<p>26.(b) <strong>Uniform and non-zero.</strong> Solenoid - A wire coil that works as an electromagnet when electricity flows through it. The magnetic fields of a solenoid are determined by the density of coils, the number of turns, and the current flowing through it. Since the magnetic field lines inside it are parallel and equidistant which means the strength of magnetic field is uniform inside the solenoid.</p>",
                    solution_hi: "<p>26.(b) <strong>एकसमान और अशून्य।</strong> परिनालिका - एक तार कुण्डली जो विद्युत प्रवाहित होने पर विद्युत चुंबक के रूप में काम करता है। परिनालिका के चुंबकीय क्षेत्र को कुण्डली के घनत्व, फेरो की संख्या और इसके माध्यम से बहने वाली धारा द्वारा निर्धारित किया जाता है। चूँकि इसके अंदर चुंबकीय क्षेत्र रेखाएँ समानांतर और समान दूरी पर हैं, जिसका अर्थ है कि परिनालिका के अंदर चुंबकीय क्षेत्र की शक्ति एक समान है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "27",
                    section: "33",
                    question_en: "<p>27. Find the value of [ (12 &divide; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> )X { 6 &divide; 4 x (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>-</mo><mn>7</mn></mrow><mn>6</mn></mfrac></math>)} &divide; { 4 &times; 6 &divide; 3 }]</p>",
                    question_hi: "<p>27. [ (12 &divide; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> ) X { 6 &divide; 4 x (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>-</mo><mn>7</mn></mrow><mn>6</mn></mfrac></math>)} &divide; { 4 &times; 6 &divide; 3 }] का मान ज्ञात कीजिए</p>",
                    options_en: ["<p>1<math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>", 
                                "<p>1 <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>1<math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>11</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>",
                                "<p>1 <math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>", "<p>1<math display=\"inline\"><mfrac><mrow><mn>17</mn></mrow><mrow><mn>32</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>27.(c) [ (12 &divide; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> ) &times; { 6 &divide; 4 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>-</mo><mn>7</mn></mrow><mn>6</mn></mfrac></math>)} &divide; { 4 &times; 6 &divide; 3 }]<br>= [ 9 <math display=\"inline\"><mo>&#215;</mo></math> { 6 &divide; 4 &times;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>)} &divide; 8 ]<br>= [ 9 <math display=\"inline\"><mo>&#215;</mo></math> { <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>} &divide; 8 ]<br>= [ 9 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>&divide; 8 ] = [ 9 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>32</mn></mfrac></math>] = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>32</mn></mfrac></math> <br><math display=\"inline\"><mo>=</mo></math> 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>32</mn></mfrac></math></p>",
                    solution_hi: "<p>27.(c) [ (12 &divide; <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> ) &times; { 6 &divide; 4 &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>-</mo><mn>7</mn></mrow><mn>6</mn></mfrac></math>)} &divide; { 4 &times; 6 &divide; 3 }]<br>= [ 9 <math display=\"inline\"><mo>&#215;</mo></math> { 6 &divide; 4 &times;(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>)} &divide; 8 ]<br>= [ 9 <math display=\"inline\"><mo>&#215;</mo></math> { <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math>} &divide; 8 ]<br>= [ 9 <math display=\"inline\"><mo>&#215;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math>&divide; 8 ] = [ 9 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>32</mn></mfrac></math>] = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>45</mn><mn>32</mn></mfrac></math> <br><math display=\"inline\"><mo>=</mo></math> 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>32</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "28",
                    section: "33",
                    question_en: "<p>28. The Book - The Test of My Life was written by-</p>",
                    question_hi: "<p>28. पुस्तक - द टेस्ट ऑफ माई लाइफ किसके द्वारा लिखी गई थी?</p>",
                    options_en: ["<p>Sania Nehwal</p>", "<p>Kapil Dev</p>", 
                                "<p>Yuvraj Singh</p>", "<p>Sachin Tendulkar</p>"],
                    options_hi: ["<p>साइना नेहवाल</p>", "<p>कपिल देव</p>",
                                "<p>युवराज सिंह</p>", "<p>सचिन तेंदुलकर</p>"],
                    solution_en: "<p>28.(c) <strong>Yuvraj Singh</strong> is a former Indian cricketer. Awards - Arjuna (2012), Padma Shri (2014). <strong>Books written by famous cricket players</strong>: Sunil Gawaskar - &ldquo;Runs and Ruins&rdquo;, &ldquo;Straight Drive&rdquo;, &ldquo;Sunny days&rdquo;; Kapil Dev - &ldquo;Straight from the Heart&rdquo;, &ldquo;By god\'s decree&rdquo;, &ldquo;Cricket is my style&rdquo;; Sachin Tendulkar - &ldquo;Playing it my way&rdquo;; Shane Warne - &ldquo;No Spin: My Autobiography&rdquo;; Sanjay Manjrekar - &ldquo;Imperfect&rdquo;; VVS Laxman - &ldquo; 281 and Beyond&rdquo;.</p>",
                    solution_hi: "<p>28.(c) <strong>युवराज सिंह</strong> एक पूर्व भारतीय क्रिकेटर हैं। पुरस्कार - अर्जुन (2012), पद्म श्री (2014)। <strong>प्रसिद्ध क्रिकेट खिलाड़ियों द्वारा लिखित पुस्तकें:</strong> सुनील गावस्कर - \"रन्स एंड रुइन्स\", \"स्ट्रेट ड्राइव\", \"सनी डेज़\", कपिल देव - \"स्ट्रेट फ्रॉम द हर्ट \", \"बाय गॉड्स डिक्री\", \"क्रिकेट इज माय स्टाइल\"; सचिन तेंदुलकर - \"प्लेइंग इट माय वे\"; शेन वार्न - \"नो स्पिन: माई ऑटोबायोग्राफी\"; संजय मांजरेकर - \"इम्पर्फेक्ट \"; वी.वी.एस. लक्ष्मण - \"281 एंड बियॉन्ड\"।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "29",
                    section: "33",
                    question_en: "<p>29. Which of the following terms will replace the question mark (?) in the given series to make it logically complete?<br>A1, B27, C125, ?</p>",
                    question_hi: "<p>29. निम्नलिखित में से कौन सा पद दी गई श्रृंखला को तार्किक रूप से पूर्ण बनाने के लिए प्रश्न चिह्न (?) को प्रतिस्थापित करेगा? <br>A1, B27, C125, ?</p>",
                    options_en: ["<p>D216</p>", "<p>D343</p>", 
                                "<p>C343</p>", "<p>D516</p>"],
                    options_hi: ["<p>D216</p>", "<p>D343</p>",
                                "<p>C343</p>", "<p>D516</p>"],
                    solution_en: "<p>29.(b) Logic - <br>A + 1 = B , B + 1 = C , C + 1 = D<br><math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></msup></math> = 1 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>3</mn></msup></math>= 27 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>3</mn></msup></math>= 125 ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>7</mn><mn>3</mn></msup></math>= 343 <br>(cube of odd numbers )</p>",
                    solution_hi: "<p>29.(b) तर्क -<br>A + 1 = B , B + 1 = C , C + 1 = D<br><math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></msup></math> = 1 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>3</mn><mn>3</mn></msup></math>= 27 , <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>5</mn><mn>3</mn></msup></math>= 125 ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>7</mn><mn>3</mn></msup></math>= 343<br>(विषम संख्याओं का घन)</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "30",
                    section: "33",
                    question_en: "<p>30. If the value of the refractive index increases then:</p>",
                    question_hi: "<p>30. यदि अपवर्तनांक का मान बढ़ता है, तो ________।</p>",
                    options_en: ["<p>deviation decreases</p>", "<p>deviation in direction of light increases</p>", 
                                "<p>there is no deviation in direction of light</p>", "<p>light will follow curved path</p>"],
                    options_hi: ["<p>विचलन घटता है</p>", "<p>प्रकाश की दिशा में विचलन बढ़ता है</p>",
                                "<p>प्रकाश की दिशा में कोई विचलन नहीं होता है</p>", "<p>प्रकाश वक्र पथ पर गमन करता है</p>"],
                    solution_en: "<p>30.(b) <strong>Deviation in direction of light increases.</strong> The deviation of light occurs when a beam of light changes route as it travels from one medium to another. The phenomenon of deviation of light rays from their original path,when they pass from one medium to another, is called refraction. As the refractive index of a medium increases, the speed of light in that medium decreases because the speed of light in a medium is inversely proportional to the refractive index of the medium, i.e., n &prop; 1/v.</p>",
                    solution_hi: "<p>30.(b) <strong>प्रकाश की दिशा में विचलन बढ़ता है।</strong> प्रकाश का विचलन तब होता है जब प्रकाश की किरण एक माध्यम से दूसरे माध्यम में जाने पर अपना मार्ग बदल लेती है। प्रकाश किरणों के एक माध्यम से दूसरे माध्यम में जाने पर उनके मूल पथ से विचलित होने की घटना को अपवर्तन कहते हैं। जैसे ही किसी माध्यम का अपवर्तनांक बढ़ता है, उस माध्यम में प्रकाश की गति कम हो जाती है क्योंकि किसी माध्यम में प्रकाश की गति माध्यम के अपवर्तनांक के व्युत्क्रमानुपाती होती है, अर्थात n &prop; 1/v।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "31",
                    section: "33",
                    question_en: "<p>31. The value of 4<sup>2</sup> &ndash; 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mo>)</mo></mrow><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math></p>",
                    question_hi: "<p>31. 4<sup>2</sup> &ndash; 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mo>)</mo></mrow><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math> का मान है</p>",
                    options_en: ["<p>1</p>", "<p>0</p>", 
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>1</p>", "<p>0</p>",
                                "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", "<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>31.(d) 4<sup>2</sup> &ndash; 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mo>)</mo></mrow><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math><br>= 16 <math display=\"inline\"><mo>-</mo><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>+</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math>= 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>31.(d) 4<sup>2</sup> &ndash; 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mn>8</mn><mo>)</mo></mrow><mfrac><mn>2</mn><mn>3</mn></mfrac></msup></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mfrac><mn>16</mn><mn>9</mn></mfrac><mo>)</mo></mrow><mfrac><mn>1</mn><mn>2</mn></mfrac></msup></math><br>= 16 <math display=\"inline\"><mo>-</mo><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>+</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math>= 5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "32",
                    section: "33",
                    question_en: "<p>32. Which day of the calendar year is acknowledged as the \'Rare Disease Day\'?</p>",
                    question_hi: "<p>32. कैलेंडर वर्ष के किस दिन को \'दुर्लभ रोग दिवस\' (\'Rare Disease Day\') के रूप में मनाया जाता है?</p>",
                    options_en: ["<p>08 February</p>", "<p>18 February</p>", 
                                "<p>28 March</p>", "<p>28 February</p>"],
                    options_hi: ["<p>08 फरवरी</p>", "<p>18 फरवरी</p>",
                                "<p>28 मार्च</p>", "<p>28 फरवरी</p>"],
                    solution_en: "<p>32.(d) <strong>28 February</strong>. Important Health Days: January 31- Anti Leprosy Day, February 4 - World Cancer Day, March 24 - World Tuberculosis Day, April 17 - World Hemophilia Day, May 19 - World Hepatitis Day, October 20 - World Malaria Day, December 1 - World Aids Day.</p>",
                    solution_hi: "<p>32.(d) <strong>28 फरवरी । </strong>महत्वपूर्ण स्वास्थ्य दिवस: 31 जनवरी- कुष्ठ निवारण दिवस, 4 फरवरी- विश्व कैंसर दिवस, 24 मार्च- विश्व क्षय रोग दिवस, 17 अप्रैल- विश्व हीमोफीलिया दिवस, 19 मई- विश्व हेपेटाइटिस दिवस, 20 अक्टूबर- विश्व मलेरिया दिवस, 1 दिसंबर- विश्व एड्स दिवस।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "33",
                    section: "33",
                    question_en: "<p>33. Select the option that represents the letters that, when placed from left to right in the same sequence in the blanks below. will complete the letter series.<br>JJL _ KKJJ _ KKKJ _ LKKKJJ</p>",
                    question_hi: "<p>33. उस विकल्प का चयन करें जो अक्षरों का प्रतिनिधित्व करता है, जब उन्हें नीचे रिक्त स्थान में समान क्रम में बाएं से दाएं रखा जाता है। पत्र श्रृंखला को पूरा करेंगे।<br>JJL _ KKJJ _ KKKJ _ LKKKJJ</p>",
                    options_en: ["<p>KLJ</p>", "<p>KJL</p>", 
                                "<p>JKL</p>", "<p>KLK</p>"],
                    options_hi: ["<p>KLJ</p>", "<p>KJL</p>",
                                "<p>JKL</p>", "<p>KLK</p>"],
                    solution_en: "<p>33.(a)<br>JJ / L<strong>K</strong>KK / JJ / <strong>L</strong>KKK / J<strong>J</strong> / LKKK / JJ</p>",
                    solution_hi: "<p>33.(a)<br>JJ / L<strong>K</strong>KK / JJ / <strong>L</strong>KKK / J<strong>J</strong> / LKKK / JJ</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "34",
                    section: "33",
                    question_en: "<p>34. The most common isotopes used in a nuclear weapon are of:</p>",
                    question_hi: "<p>34. परमाणु हथियार में उपयोग किए जाने वाले सबसे सामान्य समस्थानिक__________ हैं।</p>",
                    options_en: ["<p>Uranium and plutonium</p>", "<p>Deuterium and lithium</p>", 
                                "<p>Uranium and lithium</p>", "<p>Deuterium and plutonium</p>"],
                    options_hi: ["<p>यूरेनियम और प्लूटोनियम</p>", "<p>ड्यूटेरियम और लिथियम</p>",
                                "<p>यूरेनियम और लिथियम</p>", "<p>ड्यूटेरियम और प्लूटोनियम</p>"],
                    solution_en: "<p>34.(a) <strong>Uranium and plutonium</strong>. They are Uranium 235 and Plutonium 239. Nuclear energy is created when Uranium atoms are split in a process called Fission (releases a tremendous amount of energy in the form of heat).</p>",
                    solution_hi: "<p>34.(a) <strong>यूरेनियम और प्लूटोनियम</strong> । वे यूरेनियम 235 और प्लूटोनियम 239 हैं। परमाणु ऊर्जा तब बनती है जब यूरेनियम परमाणु विखंडन नामक प्रक्रिया में विभाजित होते हैं (ऊष्मा के रूप में भारी मात्रा में ऊर्जा मुक्त होती है)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "35",
                    section: "33",
                    question_en: "<p>35. If sinA = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><msqrt><mn>17</mn></msqrt></mrow></mfrac></math> , then tan2A = ? <br>(0&deg; &lt; A &lt; 90&deg;)</p>",
                    question_hi: "<p>35. यदि sinA = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><msqrt><mn>17</mn></msqrt></mrow></mfrac></math> तो tan2A = ? <br>(0&deg; &lt; A &lt; 90&deg;)</p>",
                    options_en: ["<p>- <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", 
                                "<p>- <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>- <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>",
                                "<p>- <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>15</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>35.(b) sinA = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><msqrt><mn>17</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>h</mi></mfrac></math><br>b = 1 , tanA = 4<br>tan2A = tan(A + A ) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>A</mi><mo>+</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>t</mi><mi>a</mi><mi>n</mi><mi>A</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>4</mn></mrow><mrow><mn>1</mn><mo>-</mo><mn>16</mn></mrow></mfrac></math> = &ndash; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math></p>",
                    solution_hi: "<p>35.(b) sinA = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><msqrt><mn>17</mn></msqrt></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>p</mi><mi>h</mi></mfrac></math><br>b = 1 , tanA = 4<br>tan2A = tan(A + A ) = <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>A</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>A</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>A</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>A</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>+</mo><mn>4</mn></mrow><mrow><mn>1</mn><mo>-</mo><mn>16</mn></mrow></mfrac></math> = &ndash; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>15</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "36",
                    section: "33",
                    question_en: "<p>36. Where is the headquarters of the International Committee of the Red Cross situated?</p>",
                    question_hi: "<p>36. रेड क्रॉस की अंतर्राष्ट्रीय समिति का मुख्यालय कहाँ स्थित है?</p>",
                    options_en: ["<p>Prague</p>", "<p>Geneva</p>", 
                                "<p>New Delhi</p>", "<p>New York</p>"],
                    options_hi: ["<p>प्राग</p>", "<p>जिनेवा</p>",
                                "<p>नई दिल्ली</p>", "<p>न्यूयॉर्क</p>"],
                    solution_en: "<p>36.(b) <strong>Geneva.</strong> The International Committee of the Red Cross is a humanitarian organization which is a three-time Nobel Prize Laureate. <strong>International Organizations</strong> and their <strong>Headquarters:</strong> United Nations Development Programme (UNDP) - New York City (USA), Food and Agriculture Organization (FAO) - Rome (Italy), International Labour Organization (ILO) - Geneva (Switzerland), International Monetary Fund (IMF) - Washington DC (USA), World Health Organization (WHO) - Geneva (Switzerland), International Fund for Agricultural Development (IFAD) - Rome (Italy).</p>",
                    solution_hi: "<p>36.(b) <strong>जिनेवा।</strong> रेड क्रॉस की अंतर्राष्ट्रीय&nbsp;समिति एक मानवतावादी संगठन है जो तीन बार नोबेल पुरस्कार विजेता रहा है। <strong>अंतर्राष्ट्रीय संगठन</strong> और उनके <strong>मुख्यालय:</strong> संयुक्त राष्ट्र विकास कार्यक्रम (UNDP) - न्यूयॉर्क शहर (USA), खाद्य और कृषि संगठन (FAO) - रोम (इटली), अंतर्राष्ट्रीय श्रम संगठन (ILO) - जिनेवा (स्विट्जरलैंड), अंतर्राष्ट्रीय मुद्रा कोष (IMF) - वाशिंगटन डीसी (USA), विश्व स्वास्थ्य संगठन (WHO) - जिनेवा (स्विट्जरलैंड), अंतर्राष्ट्रीय कृषि&nbsp;विकास कोष (IFAD) - रोम (इटली)।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "37",
                    section: "33",
                    question_en: "<p>37. Find out missing number in the following:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173711367.png\" alt=\"rId11\" width=\"293\" height=\"70\"></p>",
                    question_hi: "<p>37. निम्नलिखित में लुप्त संख्या ज्ञात कीजिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173711367.png\" alt=\"rId11\" width=\"293\" height=\"70\"></p>",
                    options_en: ["<p>136</p>", "<p>126</p>", 
                                "<p>84</p>", "<p>108</p>"],
                    options_hi: ["<p>136</p>", "<p>126</p>",
                                "<p>84</p>", "<p>108</p>"],
                    solution_en: "<p>37.(b) <strong>Logic :-</strong> [ a + b = c ]<br>15 + 75 = 90<br>18 + 90 = 108<br>Similarly, 21 + 105 = <strong>126</strong></p>",
                    solution_hi: "<p>37.(b) <strong>तर्क :-</strong> [ a + b = c ]<br>15 + 75 = 90<br>18 + 90 = 108<br>इसी प्रकार, 21 + 105 = <strong>126</strong></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "38",
                    section: "33",
                    question_en: "<p>38. Which of the following chemical reactions is balanced?</p>",
                    question_hi: "<p>38. निम्नलिखित में से कौन सी रासायनिक अभिक्रिया संतुलित है?</p>",
                    options_en: ["<p>Zn + <math display=\"inline\"><msub><mrow><mi>H</mi></mrow><mrow><mn>2</mn></mrow></msub><msub><mrow><mi>S</mi><mi>O</mi></mrow><mrow><mn>4</mn></mrow></msub></math>&rarr; Zn<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>SO</mi><mn>4</mn></msub></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math></p>", "<p>Fe +&nbsp;<math display=\"inline\"><msub><mrow><mi>H</mi></mrow><mrow><mn>2</mn></mrow></msub><mi>O</mi></math>&rarr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>Fe</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>4</mn></msub><mo>+</mo><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math></p>", 
                                "<p>Fe + 4<math display=\"inline\"><msub><mrow><mi>H</mi></mrow><mrow><mn>2</mn></mrow></msub><mi>O</mi></math>&rarr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>Fe</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>4</mn></msub><mo>+</mo><mn>4</mn><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math></p>", "<p>Mg + <math display=\"inline\"><msub><mrow><mi>O</mi></mrow><mrow><mn>2</mn></mrow></msub></math>&rarr; MgO</p>"],
                    options_hi: ["<p>Zn + <math display=\"inline\"><msub><mrow><mi>H</mi></mrow><mrow><mn>2</mn></mrow></msub><msub><mrow><mi>S</mi><mi>O</mi></mrow><mrow><mn>4</mn></mrow></msub></math>&rarr; Zn<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>SO</mi><mn>4</mn></msub></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math></p>", "<p>Fe +&nbsp;<math display=\"inline\"><msub><mrow><mi>H</mi></mrow><mrow><mn>2</mn></mrow></msub><mi>O</mi></math>&rarr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>Fe</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>4</mn></msub><mo>+</mo><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math></p>",
                                "<p>Fe + 4<math display=\"inline\"><msub><mrow><mi>H</mi></mrow><mrow><mn>2</mn></mrow></msub><mi>O</mi></math>&rarr;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>Fe</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>4</mn></msub><mo>+</mo><mn>4</mn><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math></p>", "<p>Mg + <math display=\"inline\"><msub><mrow><mi>O</mi></mrow><mrow><mn>2</mn></mrow></msub></math>&rarr; MgO</p>"],
                    solution_en: "<p>38.(a) <strong>Zn + H<sub>2</sub>SO<sub>4</sub>&nbsp;&rarr; ZnSO<sub>4</sub> + H<sub>2</sub></strong>. When one molecule of zinc and one molecule of sulphuric acid reacts it results in the production of one molecule of zinc sulphate and one molecule of hydrogen. This is a displacement reaction . Because zinc is more reactive than hydrogen so it displaces hydrogen from its place and forms&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Z</mi><mi>n</mi><mi>S</mi><msub><mi>O</mi><mn>4</mn></msub></math>. <strong>Law of conservation of mass</strong> - When a chemical reaction occurs, the mass of the products should be equal to the mass of the reactants. So, the amount of the atoms in each element does not change in the chemical reaction.</p>",
                    solution_hi: "<p>38.(a) <strong>Zn + H<sub>2</sub>SO<sub>4</sub>&nbsp;&rarr; ZnSO<sub>4</sub> + H<sub>2</sub></strong>। जब ज़िंक का एक अणु और सल्फ्यूरिक अम्ल का एक अणु अभिक्रिया करते हैं तो इसके परिणामस्वरूप ज़िंक सल्फेट का एक अणु और हाइड्रोजन का एक अणु बनता है। यह एक विस्थापन प्रतिक्रिया है। जिंक हाइड्रोजन से अधिक क्रियाशील होने के कारण हाइड्रोजन को उसके स्थान से विस्थापित कर&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>Z</mi><mi>n</mi><mi>S</mi><msub><mi>O</mi><mn>4</mn></msub></math>बनाता है। <strong>द्रव्यमान के संरक्षण का नियम</strong> - जब कोई रासायनिक अभिक्रिया होती है, तो उत्पादों का द्रव्यमान अभिकारकों के द्रव्यमान के बराबर होना चाहिए। इसलिए, रासायनिक अभिक्रिया में प्रत्येक तत्व में परमाणुओं की मात्रा नहीं बदलती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "39",
                    section: "33",
                    question_en: "<p>39. Radius of a solid spherical ball with volume 38808 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math>is:</p>",
                    question_hi: "<p>39. 38808 cm&sup3; आयतन वाली एक ठोस गोलाकार गेंद की त्रिज्या क्या है?</p>",
                    options_en: ["<p>9261 cm</p>", "<p>21 cm</p>", 
                                "<p>42 cm</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> cm</p>"],
                    options_hi: ["<p>9261 cm</p>", "<p>21 cm</p>",
                                "<p>42 cm</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> cm</p>"],
                    solution_en: "<p>39.(b) Volume of the spherical solid = 38808 cm&sup3;<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>R</mi><mn>3</mn></msup></math> = 38808<br><math display=\"inline\"><mo>&#8658;</mo><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>R</mi><mn>3</mn></msup></math> = 38808<br><math display=\"inline\"><mo>&#8658;</mo><msup><mrow><mi>R</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>38808</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>7</mn></mrow><mn>88</mn></mfrac></math> = 9261&nbsp;<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>R</mi><mo>=</mo><mn>21</mn><mi>&#160;</mi><mi>c</mi><mi>m</mi></math></p>",
                    solution_hi: "<p>39.(b)<br>गोलाकार ठोस का आयतन = 38808 cm&sup3;<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>R</mi><mn>3</mn></msup></math>=38808<br><math display=\"inline\"><mo>&#8658;</mo><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>R</mi><mn>3</mn></msup></math> = 38808<br><math display=\"inline\"><mo>&#8658;</mo><msup><mrow><mi>R</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>=</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>38808</mn><mo>&#215;</mo><mn>3</mn><mo>&#215;</mo><mn>7</mn></mrow><mn>88</mn></mfrac></math> = 9261&nbsp;<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>R</mi><mo>=</mo><mn>21</mn><mi>&#160;</mi><mi>c</mi><mi>m</mi></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "40",
                    section: "33",
                    question_en: "<p>40. Deen Dayal Upadhyaya Grameen Kaushalya Yojana was launched in ____.</p>",
                    question_hi: "<p>40. दीन दयाल उपाध्याय ग्रामीण कौशल्या योजना _______ शुरू की गई थी:</p>",
                    options_en: ["<p>2014</p>", "<p>2015</p>", 
                                "<p>2018</p>", "<p>2017</p>"],
                    options_hi: ["<p>2014</p>", "<p>2015</p>",
                                "<p>2018</p>", "<p>2017</p>"],
                    solution_en: "<p>40.(a) <strong>2014.</strong> This was launched on 25 September 2014 by Union Ministers Nitin Gadkari and Venkaiah Naidu on the occasion of 98th birth anniversary of Pandit Deendayal Upadhyaya. <strong>Vision</strong> - to transform rural poor youth into an economically independent and globally relevant workforce. Ministry - Ministry of Rural Development.</p>",
                    solution_hi: "<p>40.(a) <strong>2014</strong> । इसे 25 सितंबर 2014 को पंडित दीनदयाल उपाध्याय की 98वीं जयंती के अवसर पर केंद्रीय मंत्री नितिन गडकरी और वेंकैया नायडू द्वारा लॉन्च किया गया था। <strong>उद्देश्य</strong> - ग्रामीण गरीब युवाओं को आर्थिक रूप से स्वतंत्र और वैश्विक स्तर पर प्रासंगिक कार्यबल में बदलना। मंत्रालय - ग्रामीण विकास मंत्रालय।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "41",
                    section: "33",
                    question_en: "<p>41. Read the given statement and the arguments carefully and select the appropriate answer from the given options.<br><strong>Statement:</strong><br>Global warming has become an unprecedented issue over the globe.<br><strong>Argument:</strong><br>I. Urban life style produces a vast amount of greenhouse gases.<br>II. Change in temperature in many countries, is an indicator of global warming.</p>",
                    question_hi: "<p>41. दिए गए कथनों और तर्कों को ध्यानपूर्वक पढ़िए और दिए गए विकल्पों में से उपयुक्त उत्तर का चयन कीजिए।<br><strong>कथन:</strong><br>ग्लोबल वार्मिंग दुनिया भर में एक अभूतपूर्व मुद्दा बन गया है।<br><strong>तर्कः</strong><br>I. शहरी जीवन शैली बड़ी मात्रा में ग्रीनहाउस गैसों का उत्पादन करती है। <br>II. कई देशों में तापमान में बदलाव ग्लोबल वार्मिंग का सूचक है।</p>",
                    options_en: ["<p>II weakens while I strengthens the statement.</p>", "<p>I weakens while II strengthens the statement.</p>", 
                                "<p>Both I and II weaken the statement.</p>", "<p>Both I and II strengthen the Statement.</p>"],
                    options_hi: ["<p>II कथन का समर्थन नहीं करता है, जबकि I कथन का समर्थन करता है।</p>", "<p>I कथन का समर्थन नहीं करता है, जबकि II कथन का समर्थन करता है।</p>",
                                "<p>I और II दोनों कथन का समर्थन नहीं करते हैं।</p>", "<p>I और II दोनों कथन का समर्थन करते हैं।</p>"],
                    solution_en: "<p>41.(d) Urban life style i.e. large no. of motor vehicle, deforestation which produces a huge amount of greenhouse gases. So the statement I holds.<br>Increase in global warming directly impacts the climate and causes change in temperature in many countries. Hence statement II also holds.<br>So, both I and II strengthen the statement.</p>",
                    solution_hi: "<p>41.(d) शहरी जीवन शैली यानी मोटर वाहन की बड़ी संख्या, वनों की कटाई जो भारी मात्रा में ग्रीनहाउस गैसों का उत्पादन करने के लिए जिम्मेदार है । इसलिए कथन, तर्क I को समर्थन करता है ।<br>ग्लोबल वार्मिंग में वृद्धि सीधे जलवायु को प्रभावित करती है और कई देशों में तापमान में परिवर्तन का कारण बनती है। इसलिए तर्क II भी मान्य है।<br>इसलिए, I और II दोनों कथन को मजबूत करते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "42",
                    section: "33",
                    question_en: "<p>42. The change of a substance from liquid state to gaseous state is called:</p>",
                    question_hi: "<p>42. किसी वस्तु का तरल अवस्था से गैसीय अवस्था में परिवर्तन कहलाता है:</p>",
                    options_en: ["<p>solidification</p>", "<p>evaporation</p>", 
                                "<p>Acidification</p>", "<p>liquefaction</p>"],
                    options_hi: ["<p>घनीकरण</p>", "<p>वाष्पीकरण</p>",
                                "<p>अम्लीकरण</p>", "<p>द्रवीकरण</p>"],
                    solution_en: "<p>42.(b) <strong>Evaporation</strong> is a surface phenomenon. If the surface area is increased, the rate of evaporation increases. The rate of evaporation depends on temperature, the presence of wind velocity and the humidity. Condensation is the process through which the physical state of matter changes from the gaseous phase into the liquid phase.</p>",
                    solution_hi: "<p>42.(b) <strong>वाष्पीकरण</strong> एक सतही घटना है। यदि सतह क्षेत्र बढ़ाया जाता है, तो वाष्पीकरण की दर बढ़ जाती है। वाष्पीकरण की दर तापमान, वायु के वेग की उपस्थिति और आर्द्रता पर निर्भर करती है। संघनन वह प्रक्रिया है जिसके माध्यम से पदार्थ की भौतिक अवस्था गैसीय अवस्था से तरल अवस्था में परिवर्तित हो जाती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "43",
                    section: "33",
                    question_en: "<p>43. A semicircle has been drawn on the length of a rectangle. The area of the shaded region in the figure is:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173711579.png\" alt=\"rId12\" width=\"151\" height=\"107\"></p>",
                    question_hi: "<p>43. एक आयत की लंबाई पर एक अर्धवृत्त खींचा गया है। आकृति में छायांकित क्षेत्र का क्षेत्रफल है:<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173711579.png\" alt=\"rId12\" width=\"151\" height=\"107\"></p>",
                    options_en: ["<p>129 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>63 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", 
                                "<p>14 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>77 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    options_hi: ["<p>129 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>63 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>",
                                "<p>14 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>77 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    solution_en: "<p>43.(b)<br>Radius of the semicircle = 7 cm<br>Area of the shaded region = Area of the rectangle - Area of the semicircle<br>= 14 <math display=\"inline\"><mo>&#215;</mo><mn>10</mn><mo>-</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>27</mn></mfrac></math>&times; 7 &times; 7<br>= 140 - 77 = 63 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>",
                    solution_hi: "<p>43.(b) अर्धवृत्त की त्रिज्या = 7 cm<br>छायांकित क्षेत्र का क्षेत्रफल = आयत का क्षेत्रफल - अर्धवृत्त का क्षेत्रफल<br>= 14 <math display=\"inline\"><mo>&#215;</mo><mn>10</mn><mo>-</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>27</mn></mfrac></math>&times; 7 &times; 7<br>= 140 - 77 = 63 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "44",
                    section: "33",
                    question_en: "<p>44. Which Gana did Gautam Buddha belong to?</p>",
                    question_hi: "<p>44. गौतम बुद्ध का संबंध निम्नलिखित मे से किस गण से था ?</p>",
                    options_en: ["<p>Lichchavi</p>", "<p>Sakya</p>", 
                                "<p>Moriya</p>", "<p>Malla</p>"],
                    options_hi: ["<p>लिच्छवी</p>", "<p>शाक्य</p>",
                                "<p>मोरिया</p>", "<p>मल्ला</p>"],
                    solution_en: "<p>44.(b) <strong>Sakya.</strong> Gautam Buddha <strong>Born:</strong> 563 BCE, Lumbini (Nepal). <strong>Died:</strong>&thinsp;483 BCE Kushinagar. Known for: Founding Buddhism. Three books: The Sutta (conventional teaching), The Vinaya (disciplinary code), The Abhidhamma (moral psychology). The five forms that represent Buddha are: Lotus and Bull - Birth, Horse - Renunciation, Bodhi Tree - Enlightenment, Wheel - First Sermon , Stupa - Death. The three pillars of his teachings are: Buddha - Teacher, Dhamma - Teachings, Sangha - The Community.</p>",
                    solution_hi: "<p>44.(b) <strong>शाक्य।</strong> गौतम बुद्ध <strong>जन्म:</strong> 563 ईसा पूर्व, लुंबिनी (नेपाल)। <strong>मृत्यु:-</strong> 483 ई.पू. कुशीनगर। गौतम बुद्ध बौद्ध धर्म के संस्थापक के रूप में जाने जाते है तीन पुस्तकें: सुत्त (पारंपरिक शिक्षण), विनय (अनुशासनात्मक संहिता), अभिधम्म (नैतिक मनोविज्ञान)। बुद्ध का प्रतिनिधित्व करने वाले पांच प्रतीक हैं: कमल और बैल - जन्म, घोड़ा - त्याग, बोधि वृक्ष - आत्मज्ञान, पहिया - पहला उपदेश, स्तूप - मृत्यु। उनकी शिक्षाओं के तीन स्तंभ हैं: बुद्ध - शिक्षक, धम्म - शिक्षाएं, संघ - समुदाय।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "45",
                    section: "33",
                    question_en: "<p>45. A statement is given followed by two arguments I and II. Read the statement and the arguments carefully and select the appropriate answer from the given options.<br><strong>Statement:</strong><br>People are becoming more conscious about moving towards a healthy lifestyle.<br><strong>Arguments:</strong><br>I. Many people are switching to organic farming as the food is healthier.<br>II. People are planning meals and cooking at home more than before.</p>",
                    question_hi: "<p>45 एक कथन और उसके बाद दो तर्क I और ॥ दिए गए हैं। कथन और तर्कों का ध्यानपूर्वक अध्ययन कीजिए, और दिए गए विकल्पों में से उपयुक्त उत्तर का चयन कीजिए।<br><strong>कथन:</strong><br>लोग स्वस्थ जीवन शैली अपनाने के बारे में अधिक जागरूक हो रहे हैं।<br><strong>तर्क:</strong><br>I. बहुत से लोग जैविक खेती को अपना रहे हैं, क्योंकि इसके द्वारा उत्पन्न खाद्य पदार्थ स्वास्थ्यवर्धक होते हैं।<br>II. लोग घर पर खाना पकाने और उसे खाने को पहले से अधिक तरजीह दे रहे हैं।</p>",
                    options_en: ["<p>Both I and II weaken the statement</p>", "<p>I weakens, while II strengthens the statement</p>", 
                                "<p>II weakens, while I strengthens the statement</p>", "<p>Both I and II strengthen the statement</p>"],
                    options_hi: ["<p>I और II दोनों कथन का समर्थन नहीं करते हैं।</p>", "<p>I कथन का समर्थन नहीं करता, जबकि II कथन का समर्थन करता है।</p>",
                                "<p>II कथन का समर्थन नहीं करता, जबकि। कथन का समर्थन करता है।</p>", "<p>I और II दोनों कथन का समर्थन करते हैं।</p>"],
                    solution_en: "<p>45.(d) As from the statement Both I and II arguments strengthen the statement.</p>",
                    solution_hi: "<p>45.(d) कथन के अनुसार I और II दोनों तर्क कथन का समर्थन करते हैं।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "46",
                    section: "33",
                    question_en: "<p>46. Which of the following carbon compounds is a major component of biogas and compressed natural gas?</p>",
                    question_hi: "<p>46. इनमे से कौन सा कार्बन यौगिक, बायोगैस और कंप्रेस्ड नेचुरल गैस का एक प्रमुख घटक है?</p>",
                    options_en: ["<p>Methane</p>", "<p>Butane</p>", 
                                "<p>Ethane</p>", "<p>Propane</p>"],
                    options_hi: ["<p>मेथेन</p>", "<p>ब्यूटेन</p>",
                                "<p>एथेन</p>", "<p>प्रोपेन</p>"],
                    solution_en: "<p>46.(a) <strong>Methane</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CH</mi><mn>4</mn></msub></math> , a greenhouse gas) - A hydrocarbon that is a primary component of natural gas. <strong>Butane</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>10</mn></msub></math>) - A highly flammable, colorless, easily liquefied gas that quickly vaporizes at room temperature and pressure. <strong>Ethane</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>6</mn></msub></math>) - An organic chemical compound has a role as a refrigerant and a plant metabolite. <strong>Propane</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>8</mn></msub></math>) - A flammable hydrocarbon gas that is liquefied through pressurisation and commonly used for fuel in heating, cooking, hot water.</p>",
                    solution_hi: "<p>46.(a) <strong>मेथेन</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>CH</mi><mn>4</mn></msub></math>, एक ग्रीनहाउस गैस) - एक हाइड्रोकार्बन जो प्राकृतिक गैस का प्राथमिक घटक है। <strong>ब्यूटेन</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>4</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>10</mn></msub></math>) - एक अत्यधिक ज्वलनशील, रंगहीन, आसानी से द्रवीभूत गैस जो कमरे के तापमान और दबाव पर जल्दी से वाष्पीकृत हो जाती है। <strong>इथेन</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>2</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>6</mn></msub></math>) - एक कार्बनिक रासायनिक यौगिक की रेफ्रिजरेंट और प्लांट मेटाबोलाइट के रूप में भूमिका होती है। <strong>प्रोपेन</strong> (<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>3</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>8</mn></msub></math>) - एक ज्वलनशील हाइड्रोकार्बन गैस जिसे दबाव के माध्यम से द्रवित किया जाता है और आमतौर पर हीटिंग, खाना पकाने, पानी गर्म करने में तथा ईंधन के लिए उपयोग किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "47",
                    section: "33",
                    question_en: "<p>47. In the given circle with center O. the acute angle at the center measures 88&deg;. In the quadrilateral drawn inside the circle, what is the measure of the angle opposite to <math display=\"inline\"><mi>&#8736;</mi><mi>O</mi></math>? [Figure is not drawn to scale]<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173711770.png\" alt=\"rId13\" width=\"107\" height=\"101\"></p>",
                    question_hi: "<p>47. केंद्र O वाले दिए गए वृत्त में, केंद्र पर न्यून कोण 88&deg; है। वृत्त के भीतर खींचे गए चतुर्भुज में O के सम्मुख कोण का माप क्या है? [आकृति पैमाने पर नहीं खींची गई है]<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173711770.png\" alt=\"rId13\" width=\"107\" height=\"101\"></p>",
                    options_en: ["<p>88&deg;</p>", "<p>68&deg;</p>", 
                                "<p>272&deg;</p>", "<p>136&deg;</p>"],
                    options_hi: ["<p>88&deg;</p>", "<p>68&deg;</p>",
                                "<p>272&deg;</p>", "<p>136&deg;</p>"],
                    solution_en: "<p>47.(d) Opposite of <math display=\"inline\"><mi>&#8736;</mi><mi>O</mi></math> <br>= (360&deg; - 88&deg;)<math display=\"inline\"><mi>&#160;</mi><mo>&#247;</mo></math> 2 = 136&deg;</p>",
                    solution_hi: "<p>47.(d) <math display=\"inline\"><mi>&#8736;</mi><mi>O</mi></math> का सम्मुख कोण <br>= (360&deg; - 88&deg;)<math display=\"inline\"><mi>&#160;</mi><mo>&#247;</mo></math> 2 = 136&deg;</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "48",
                    section: "33",
                    question_en: "<p>48. Name the daughter of one of the most important rulers in early Indian history, Chandragupta II.</p>",
                    question_hi: "<p>48. प्रारंभिक भारतीय इतिहास के सबसे महत्वपूर्ण शासकों में से एक चंद्रगुप्त द्वितीय की पुत्री का नाम बताइए ?</p>",
                    options_en: ["<p>Rudrama Devi</p>", "<p>Lopamudra</p>", 
                                "<p>Parvati Gupta</p>", "<p>Prabhavati Gupta</p>"],
                    options_hi: ["<p>रुद्रमादेवी</p>", "<p>लोपमुद्रा</p>",
                                "<p>पार्वती गुप्त</p>", "<p>प्रभावतीगुप्त</p>"],
                    solution_en: "<p>48.(d) <strong>Prabhavati Gupta</strong> (Gupta princess and Vakataka Queen). She was the consort of Maharaja Rudrasena II. Chandragupta II (Vikramaditya), son of Samudragupta (Napoleon of India) was one of the most powerful emperors of the Gupta Empire in northern India. Reign of Gupta Dynasty: Sri Gupta (240 - 280 AD, Founder), Ghatotkacha (280 - 319 AD), Chandragupta I (319 - 335 AD), Samudragupta (335 - 375 AD), Chandragupta Vikramaditya (376 - 415 AD), Kumaragupta I (415 - 455 AD), Skandagupta (455 - 467 AD), Vishnugupta (540 - 550 AD).</p>",
                    solution_hi: "<p>48.(d) <strong>प्रभावती गुप्ता</strong> (गुप्ता राजकुमारी और वाकाटक रानी)। वह महाराजा रुद्रसेन द्वितीय की पत्नी थीं। समुद्रगुप्त (भारत का नेपोलियन) का पुत्र चंद्रगुप्त द्वितीय (विक्रमादित्य) उत्तरी भारत में गुप्त साम्राज्य के सबसे शक्तिशाली सम्राटों में से एक था। गुप्त राजवंश का शासनकाल: श्री गुप्त (240 - 280 ई., संस्थापक), घटोत्कच (280 - 319 ई.), चंद्रगुप्त प्रथम (319 - 335 ई.), समुद्रगुप्त (335 - 375 ई.), चंद्रगुप्त विक्रमादित्य (376 - 415 ई.), कुमारगुप्त प्रथम (415-455 ई.), स्कंदगुप्त (455-467 ई.), विष्णुगुप्त (540-550 ई.)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "49",
                    section: "33",
                    question_en: "<p>49. Read the given statement and the arguments carefully and select the appropriate answer from the given options.<br><strong>Statement :</strong><br>Education is the backbone of human society.<br><strong>Arguments :</strong><br>I. Schools are learning centres of societal norms and regulations. <br>II. Any society with less concern about education, cannot grow and develop in present times.</p>",
                    question_hi: "<p>49. दिए गए कथन और तर्कों को ध्यानपूर्वक पढ़िए और दिए गए विकल्पों में से उपयुक्त उत्तर का चयन कीजिए।<br><strong>कथन :</strong><br>शिक्षा मानव समाज की रीढ़ है।<br><strong>तर्क :</strong><br>I. स्कूल सामाजिक मानदंडों और विनियमों के सीखने के केंद्र हैं।<br>II. शिक्षा के बारे में कम चिंता वाला कोई भी समाज वर्तमान समय में विकसित और विकसित नहीं हो सकता है।</p>",
                    options_en: ["<p>II Weakens while I strengthen the statement.</p>", "<p>Both I and II strengthen the statement.</p>", 
                                "<p>I Weakens while II strengthens the statement.</p>", "<p>Both I and II weaken the statement.</p>"],
                    options_hi: ["<p>II कथन का समर्थन नहीं करता है जबकि I कथन का समर्थन करता है।</p>", "<p>I और II दोनों कथन का समर्थन करते हैं।</p>",
                                "<p>I कथन का समर्थन नहीं करता है। जबकि II कथन का समर्थन करता है।</p>", "<p>I और II दोनों कथन का समर्थन नहीं करते हैं।</p>"],
                    solution_en: "<p>49.(b) I.Schools are learning centers of societal norms and regulations. <br>II. Any society with less concern about education, cannot grow and develop in present times.<br>Both I and II Arguments strengthen the statement.</p>",
                    solution_hi: "<p>49.(b) I. स्कूल सामाजिक मानदंडों और विनियमों के सीखने के केंद्र हैं।<br>II. शिक्षा के बारे में कम चिंता वाला कोई भी समाज वर्तमान समय में विकसित और विकसित नहीं हो सकता है।<br>I और II दोनों तर्क कथन को मजबूत करता हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "50",
                    section: "33",
                    question_en: "<p>50. Which of the following is NOT generally the properties of non-metals?</p>",
                    question_hi: "<p>50. निम्नलिखित में से कौन सा सामान्यतः अधातुओं का गुण नहीं है?</p>",
                    options_en: ["<p>Dull and lackluster</p>", "<p>Conductors</p>", 
                                "<p>Brittle</p>", "<p>Light substances</p>"],
                    options_hi: ["<p>धूमिल एवं मंद</p>", "<p>चालक</p>",
                                "<p>भंगुर</p>", "<p>हल्के पदार्थ</p>"],
                    solution_en: "<p>50.(b) <strong>Conductors</strong> - A substance or material that allows electricity to flow through it. Non-metal - An element that is generally a poor conductor of heat and electricity, and most properties of non-metals are opposite of metals. Examples - Nitrogen, Fluorine, Sulphur etc. Metal - A solid material that conducts heat and electricity. They are hard, shiny, malleable, and ductile. Examples - Gold, Silver, Aluminium, Copper, Iron, etc.</p>",
                    solution_hi: "<p>50.(b) <strong>चालक</strong> - ऐसे पदार्थ या सामग्री जो विद्युत को अपने माध्यम से प्रवाहित होने देते है। अधातु - एक तत्व जो आम तौर पर ऊष्मा और विद्युत का कुचालक होता है, और अधातुओं के अधिकांश गुण धातुओं के विपरीत होते हैं। उदाहरण - नाइट्रोजन, फ्लोरीन, सल्फर आदि। धातु - एक ठोस पदार्थ जो ऊष्मा और विद्युत का संचालक करता है। ये कठोर, चमकदार, आघातवर्धनीय और तन्य होते हैं। उदाहरण - सोना, चाँदी, एल्युमीनियम, तांबा, लोहा, आदि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "51",
                    section: "33",
                    question_en: "<p>51. What is the area of the largest square that is inscribed in a semicircle of radius 10 cm?</p>",
                    question_hi: "<p>51. 10 cm त्रिज्या वाले अर्धवृत्त में अंकित सबसे बड़े वर्ग का क्षेत्रफल कितना है?</p>",
                    options_en: ["<p>70<math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>90 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", 
                                "<p>80 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>120 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    options_hi: ["<p>70 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>90 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>",
                                "<p>80 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>", "<p>120 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>"],
                    solution_en: "<p>51.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173711976.png\" alt=\"rId14\" width=\"162\" height=\"107\"><br>Let side of square = 2a, OD = a<br>In <math display=\"inline\"><mi>&#916;</mi><mi>B</mi><mi>O</mi><mi>D</mi><mo>,</mo></math><br><math display=\"inline\"><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mn>4</mn><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>100</mn></math> &rArr;5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup></math> = 100<br><math display=\"inline\"><mo>&#8658;</mo><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>20</mn></math> &rArr; a=2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math>, Side = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>Area of square = <math display=\"inline\"><msup><mrow><mo>(</mo><mn>4</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>80</mn><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br><strong>Shortcut:-</strong><br>Side of square = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>r</mi></mrow><mrow><msqrt><mn>5</mn></msqrt></mrow></mfrac></math> &rArr; a =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><msqrt><mn>5</mn></msqrt></mfrac></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>Area = 80 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>",
                    solution_hi: "<p>51.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173711976.png\" alt=\"rId14\" width=\"162\" height=\"107\"><br>माना वर्ग की भुजा = 2a <math display=\"inline\"><mo>&#8658;</mo></math> OD = a<br><math display=\"inline\"><mi>&#916;</mi><mi>B</mi><mi>O</mi><mi>D</mi></math> में,<br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><mn>4</mn><msup><mi>a</mi><mn>2</mn></msup><mo>=</mo><mn>100</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>5</mn><msup><mi>a</mi><mn>2</mn></msup><mo>=</mo><mn>100</mn></math><br><math display=\"inline\"><mo>&#8658;</mo><mi>&#160;</mi><msup><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mi>&#160;</mi><mn>20</mn></math> &rArr; a = 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>भुजा = 4<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math><br>वर्ग का क्षेत्रफल = <math display=\"inline\"><msup><mrow><mo>(</mo><mn>4</mn><msqrt><mn>5</mn></msqrt><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>=</mo><mn>80</mn><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math><br><strong>शॉर्टकट :-</strong><br>वर्ग की भुजा = <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>r</mi></mrow><mrow><msqrt><mn>5</mn></msqrt></mrow></mfrac></math> &rArr; a = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><msqrt><mn>5</mn></msqrt></mfrac></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>5</mn></msqrt></math><br>क्षेत्रफल = 80 <math display=\"inline\"><mi>&#160;</mi><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>2</mn></mrow></msup></math></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "52",
                    section: "33",
                    question_en: "<p>52. The ideals of liberty, equality and fraternity enshrined in the Indian Constitution are taken from the constitution of ______.</p>",
                    question_hi: "<p>52. भारतीय संविधान में समावेशित स्वतंत्रता, समानता और बंधुता के आदर्श ______ के संविधान से लिए गए हैं।</p>",
                    options_en: ["<p>United States of America</p>", "<p>Soviet Union</p>", 
                                "<p>Japan</p>", "<p>France</p>"],
                    options_hi: ["<p>संयुक्त राष्ट्र अमेरिका</p>", "<p>सोवियत संघ</p>",
                                "<p>जापान</p>", "<p>फ्रांस</p>"],
                    solution_en: "<p>52.(d) <strong>France.</strong> Borrowed features of Indian Constitution from other countries: Japan (Procedure established by law), Germany (Suspension of Fundamental Rights during emergency), Ireland (Directive Principles of State Policy, Nomination of members to Rajya Sabha, Method of election of the president).</p>",
                    solution_hi: "<p>52.(d) <strong>फ़्रांस।</strong> भारतीय संविधान की कुछ विशेषताएं अन्य देशों से उधार ली गईं है: जापान (विधि द्वारा स्थापित प्रक्रिया), जर्मनी (आपातकाल के दौरान मौलिक अधिकारों का निलंबन), आयरलैंड (राज्य के नीति निर्देशक सिद्धांत, राज्यसभा के लिए सदस्यों का नामांकन, राष्ट्रपति के चुनाव की विधि)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "53",
                    section: "33",
                    question_en: "<p>53. Read the given statements and conclusions carefully and decide which of the given conclusions logically follows (s) from the statements.<br><strong>Statements:</strong><br>Some fruits are vegetables.<br>Some vegetables are not edible.<br><strong>Conclusions:</strong><br>1. Some fruits are not edible.<br>2. Some vegetables are fruits.</p>",
                    question_hi: "<p>53. दिए गए कथनों और निष्कर्षों का ध्यानपूर्वक अध्ययन करें, और तय करें कि कौन से निष्कर्ष तार्किक रूप से कथनों का पालन करते हैं?<br><strong>कथन:</strong><br>कुछ फल सब्जियां हैं।<br>कुछ सब्जियां खाद्य नहीं हैं।<br><strong>निष्कर्ष:</strong><br>1. कुछ फल खाद्य नहीं हैं।<br>2. कुछ सब्जियां फल हैं।</p>",
                    options_en: ["<p>Conclusion 2 is correct.</p>", "<p>Conclusion 1 is correct.</p>", 
                                "<p>Neither conclusion 1 nor 2 is correct.</p>", "<p>Both conclusions 1 and 2 are correct.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष 2 सही है।</p>", "<p>केवल निष्कर्ष 1 सही है ।</p>",
                                "<p>1 और 2 दोनों सही नहीं हैं।</p>", "<p>1 और 2 दोनों सही हैं।</p>"],
                    solution_en: "<p>53.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173712164.png\" alt=\"rId15\" width=\"249\" height=\"64\"></p>",
                    solution_hi: "<p>53.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173712376.png\" alt=\"rId16\" width=\"269\" height=\"69\"></p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "54",
                    section: "33",
                    question_en: "<p>54. Which of the following describes how electrons are put into orbitals in a particular order for filling ?</p>",
                    question_hi: "<p>54. निम्नलिखित में से कौन सा यह बताता है कि इलेक्ट्रॉनों को कक्षाओं में एक विशेष क्रम में भरा जाता है?</p>",
                    options_en: ["<p>Hund\'s rule</p>", "<p>Wiswesser rule</p>", 
                                "<p>Aufbau principle</p>", "<p>Pauli\'s exclusion principle</p>"],
                    options_hi: ["<p>हुंड का नियम</p>", "<p>विश्वेसर का नियम</p>",
                                "<p>ऑफबाऊ नियम</p>", "<p>पाउली का अपवर्जन नियम</p>"],
                    solution_en: "<p>54.(c) <strong>Aufbau principle</strong> - It states that electrons fill lower-energy atomic orbitals before filling higher-energy ones, By following this rule, we can predict the electron configurations for atoms or ions. Pauli exclusion principle- In a single atom, no two electrons will have an identical set or the same quantum numbers. Hund\'s rule - Every orbital in a sublevel is singly occupied before any orbital is doubly occupied. The Wiswesser rule gives a simple method to determine the energetic sequence of the atomic subshells (n, l). n is the principal quantum number and l is the azimuthal quantum number.</p>",
                    solution_hi: "<p>54.(c) <strong>ऑफबाऊ नियम </strong>- यह बताता है कि इलेक्ट्रॉन उच्च-ऊर्जा वाले परमाणु कक्षाओं को भरने से पहले निम्न-ऊर्जा परमाणु कक्षाओं को भरते हैं, इस नियम का पालन कर के, हम परमाणुओं या आयनों के लिए इलेक्ट्रॉन विन्यास की भविष्यवाणी कर सकते हैं। पाउली का अपवर्जन का नियम - एक परमाणु कक्षक मे दो इलेक्ट्रॉन समान (परमाणु कक्षक मे प्रत्येक इलेक्ट्रॉन, दूसरे इलेक्ट्रॉन से भिन्नता रखता है) नहीं रह सकता या एक ही क्वांटम संख्या नहीं होगा। हुंड का नियम - किसी भी कक्षको में इलेक्ट्रॉन पहले एक-एक करके भरे जाते हैं, उसके बाद ही उन इलेक्ट्रॉन के जोड़ा बनाना शुरू होता है। विस्वेसर नियम परमाणु उप-कोश (n, l) के ऊर्जावान अनुक्रम को निर्धारित करने के लिए एक सरल विधि देता है। n प्रिंसिपल क्वांटम संख्या है और l दिगंशीय (अज़ीमुथल) क्वांटम संख्या है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "55",
                    section: "33",
                    question_en: "<p>55. If x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> and y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math>, then the value of x&sup2; + y&sup2; is..</p>",
                    question_hi: "<p>55. यदि x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> और y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math> , तो x&sup2; + y&sup2; का मान है।</p>",
                    options_en: ["<p>14</p>", "<p>10</p>", 
                                "<p>13</p>", "<p>15</p>"],
                    options_hi: ["<p>14</p>", "<p>10</p>",
                                "<p>13</p>", "<p>15</p>"],
                    solution_en: "<p>55. (a) x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math>and y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>y = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> &rArr; x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>3</mn><mo>+</mo><mn>1</mn><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>2</mn></mfrac></math>= 4<br>x&sup2; + y&sup2; = <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math>= 16 - 2 = 14</p>",
                    solution_hi: "<p>55.(a) x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> और y =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math>&nbsp;<br>y = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> &rArr; x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>3</mn><mo>+</mo><mn>1</mn><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>3</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>2</mn></mfrac></math>= 4<br>x&sup2; + y&sup2; = <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math>= 16 - 2 = 14</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "56",
                    section: "33",
                    question_en: "<p>56. The Tropic of Cancer does not pass through _________.</p>",
                    question_hi: "<p>56. कर्क रेखा ________ से होकर नहीं गुजरती है।</p>",
                    options_en: ["<p>Jharkhand</p>", "<p>Nagaland</p>", 
                                "<p>Gujarat</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>झारखंड</p>", "<p>नागालैंड</p>",
                                "<p>गुजरात</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p>56.(b) <strong>Nagaland.</strong> Tropic of Cancer (23&deg; 30\'N) - Divides the country into almost two equal parts. It passes through eight states (Rajasthan, Gujarat, Madhya Pradesh, Chhattisgarh, Jharkhand, West Bengal, Tripura and Mizoram) in India.</p>",
                    solution_hi: "<p>56.(b) <strong>नागालैंड।</strong> कर्क रेखा (23&deg;30\' N) - देश को लगभग दो बराबर भागों में विभाजित करती है। यह भारत में आठ राज्यों (राजस्थान, गुजरात, मध्य प्रदेश, छत्तीसगढ़, झारखंड, पश्चिम बंगाल, त्रिपुरा और मिजोरम) से होकर गुजरती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "57",
                    section: "33",
                    question_en: "<p>57. Select the Venn diagram that best represents the relationship between<br>Musical Instruments, Drums and Flute</p>",
                    question_hi: "<p>57. उस वेन आरेख का चयन कीजिये जो निम्नलिखित के बीच के संबंध का सबसे अच्छा प्रतिनिधित्व करता है&nbsp;</p>\n<p>संगीत वाद्ययंत्र, ढोल और बांसुरी</p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-16a7e073-7fff-97b1-428a-093eefe5e523\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcahigfcXPuhSfTpqmhYcH3Mroxle-w6fThtPvkkq53-0KZ8mcBXhEuwLyPeRUb5iW8zc8UP_kBoDQHtFE5Y6lq8epmOIpLkCnhVW3KVc_b8IUH9RYddL5odX1Ud2qRueUB0GIXbw?key=AjRAXbeQiEOp6KHfvU2BInHG\" width=\"81\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-83097f22-7fff-ad93-5ce4-20ed0c812430\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcPOz8uCbDxaapm4L8RoSXPhuyofi0f_8PQHvFZG3txVDJahAvS4KQsiUEkstN6i323u2Ol2l9jVabBj6y5olZ1dF8sfHv7VlOQ1p4o-CAqvF4fBRKApIx3T_pu3P6IEkzUd_2Z?key=AjRAXbeQiEOp6KHfvU2BInHG\" width=\"110\" height=\"51\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-45afadc1-7fff-d5da-4c8c-131f56549456\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdiIh-6uilDYUxDAOBdVjHvtzhmD-Ephx3XfuboPhCxlOOgQGHk3ocr1-XGJRAGBT2WC4-q66xZIonmR-KR1Zb3AYxIvQnL52eKergmvCDUye4C9wwOBnGcM53a-Q_zBXxuc_QAJQ?key=AjRAXbeQiEOp6KHfvU2BInHG\" width=\"75\" height=\"77\"></strong></p>", "<p><strong id=\"docs-internal-guid-19895b8b-7fff-0504-10fb-ade9537d75ce\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXerkcBniaP1yghbLPzO49M8QjjNt0WY0fRiHye3VWzF1kQ0Ytq2TWysa58uqJPk-dFCN1DePWSbuhfJGUpcOgNptN3mCCuifvRRBJHx0xLbLPXqmGfJ9-6VUFKrfsasqsvRl1To?key=AjRAXbeQiEOp6KHfvU2BInHG\" width=\"135\" height=\"53\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-c45ef4ee-7fff-2ca8-ebed-070f4a348324\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcahigfcXPuhSfTpqmhYcH3Mroxle-w6fThtPvkkq53-0KZ8mcBXhEuwLyPeRUb5iW8zc8UP_kBoDQHtFE5Y6lq8epmOIpLkCnhVW3KVc_b8IUH9RYddL5odX1Ud2qRueUB0GIXbw?key=AjRAXbeQiEOp6KHfvU2BInHG\" width=\"81\" height=\"72\"></strong></p>", "<p><strong id=\"docs-internal-guid-90109735-7fff-3c8e-4a6d-15e45ceb005d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcPOz8uCbDxaapm4L8RoSXPhuyofi0f_8PQHvFZG3txVDJahAvS4KQsiUEkstN6i323u2Ol2l9jVabBj6y5olZ1dF8sfHv7VlOQ1p4o-CAqvF4fBRKApIx3T_pu3P6IEkzUd_2Z?key=AjRAXbeQiEOp6KHfvU2BInHG\" width=\"110\" height=\"51\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-36c93a13-7fff-4ded-a561-e638932fb127\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdiIh-6uilDYUxDAOBdVjHvtzhmD-Ephx3XfuboPhCxlOOgQGHk3ocr1-XGJRAGBT2WC4-q66xZIonmR-KR1Zb3AYxIvQnL52eKergmvCDUye4C9wwOBnGcM53a-Q_zBXxuc_QAJQ?key=AjRAXbeQiEOp6KHfvU2BInHG\" width=\"75\" height=\"77\"></strong></p>", "<p><strong id=\"docs-internal-guid-4c57d983-7fff-8e09-6157-728d7d419385\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXerkcBniaP1yghbLPzO49M8QjjNt0WY0fRiHye3VWzF1kQ0Ytq2TWysa58uqJPk-dFCN1DePWSbuhfJGUpcOgNptN3mCCuifvRRBJHx0xLbLPXqmGfJ9-6VUFKrfsasqsvRl1To?key=AjRAXbeQiEOp6KHfvU2BInHG\" width=\"135\" height=\"53\"></strong></p>"],
                    solution_en: "<p>57.(c) <br>Musical Instruments, Drums and Flute<br><strong id=\"docs-internal-guid-4bcfa13c-7fff-c389-8c1d-b5916d97cab7\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcPUzU6_4fQoHjjv1m7h4g3Gp3kgtrdEJHQ-BTjWBmwLB4A5aoSdoKJa1o4BPDe2ZIczKm9sN2ufzcHYv-iyPvESzNEBZhuFk-i4kk7pAQ3DIwQc8waVE0Ubqm7vpXHe2jktrwq?key=AjRAXbeQiEOp6KHfvU2BInHG\" width=\"242\" height=\"124\"></strong></p>",
                    solution_hi: "<p>57.(c) संगीत वाद्ययंत्र, ढोल और बांसुरी<br><strong id=\"docs-internal-guid-3e6ffb50-7fff-a566-9290-81d4028d71a9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdtvnHFdYpgVyWoWfKKREYGa_PQ93QeZVdnQtsmuYVwKhGMAIP3PL70yO89AmoRO8tAeU9vL_xYoUO7oHSp4aIz_kT2r9sfiZRY9-SgQdEhlt3h0_8k51Xlnec6sYupCMxYBx8sTA?key=AjRAXbeQiEOp6KHfvU2BInHG\" width=\"233\" height=\"118\"></strong></p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "58",
                    section: "33",
                    question_en: "<p>58. The atomic size ________ moving from left to right and _______ moving from up to down in the periodic table.</p>",
                    question_hi: "<p>58. आवर्त सारणी में परमाणु का आकार बाएं से दाएं जाने पर _____और ऊपर से नीचे जाने पर _______।</p>",
                    options_en: ["<p>increases, increases</p>", "<p>decreases, increases</p>", 
                                "<p>decreases, decreases</p>", "<p>increases, decreases</p>"],
                    options_hi: ["<p>बढ़ता है, बढ़ता है</p>", "<p>घटता है, बढ़ता है</p>",
                                "<p>घटता है, घटता है</p>", "<p>बढ़ता है, घटता है</p>"],
                    solution_en: "<p>58.(b) <strong>Decreases, Increases. </strong>The <strong>atomic radius</strong> decreases across a period (Horizontal Row) and increases down a group (Vertical Column). Across a period, effective nuclear charge increases as electron shielding remains constant. It causes greater attractions to the electrons, pulling the electron cloud closer to the nucleus which results in a smaller atomic radius. Down a group, the number of energy levels increases, so there is a greater distance between the nucleus and the outermost orbital. This results in a larger atomic radius.</p>",
                    solution_hi: "<p>58.(b) <strong>घटता है, बढ़ता है | परमाणु त्रिज्या</strong> एक आवर्त में आगे जाने पर घट जाती है और एक समूह में नीचे जाने पर बढ़ जाती है। <strong>एक आवर्त में जाने पर</strong>, प्रभावी नाभिकीय आवेश बढ़ता है क्योंकि इलेक्ट्रॉन परिरक्षण (electron shielding) स्थिर रहता है। यह इलेक्ट्रॉनों के लिए अधिक आकर्षण का कारण बनता है, इलेक्ट्रॉन क्लाउड (electron cloud) को नाभिक के करीब खींचता है जिसके परिणामस्वरूप एक छोटी परमाणु त्रिज्या होती है। एक <strong>समूह के नीचे</strong>, ऊर्जा स्तरों की संख्या बढ़ जाती है, इसलिए नाभिक और सबसे बाहरी आर्बिटल के बीच अधिक दूरी होती है। इसके परिणामस्वरूप एक बड़े परमाणु त्रिज्या का होना है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "59",
                    section: "33",
                    question_en: "<p>59. Find the value of 3(x + y) if x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> and y =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math> .</p>",
                    question_hi: "<p>59. 3(x + y) का मान ज्ञात कीजिए यदि x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> और y = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math> .</p>",
                    options_en: ["<p>12</p>", "<p>10</p>", 
                                "<p>8</p>", "<p>13</p>"],
                    options_hi: ["<p>12</p>", "<p>10</p>",
                                "<p>8</p>", "<p>13</p>"],
                    solution_en: "<p>59.(a) <strong>Given,</strong><br>x =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> , y =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>Now putting the value of x and y in 3(x+y)<br>= 3 {( <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> ) + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math>)}<br>=3&times;(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mo>+</mo><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math>)<br>= 3 <math display=\"inline\"><mo>&#215;</mo><mi>&#160;</mi></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>3</mn><mo>+</mo><mn>1</mn><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math>) = 12</p>",
                    solution_hi: "<p>59.(a) <strong>दिया गया है,</strong><br>x =&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> , y =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math><br>अब x और y का मान 3(x + y) में रखने पर<br>= 3 {( <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow></mfrac></math> ) + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn></mrow><mrow><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn></mrow></mfrac></math>)}<br>=3&times;(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mo>+</mo><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo><mo>(</mo><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>1</mn><mo>)</mo><mi>&#160;</mi></mrow></mfrac></math>)<br>= 3 <math display=\"inline\"><mo>&#215;</mo><mi>&#160;</mi></math>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mo>+</mo><mn>1</mn><mo>+</mo><mn>2</mn><msqrt><mn>3</mn></msqrt><mo>+</mo><mn>3</mn><mo>+</mo><mn>1</mn><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac></math>) = 12</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "60",
                    section: "33",
                    question_en: "<p>60. Which of the following is NOT a tributary of the Yamuna river system?</p>",
                    question_hi: "<p>60. निम्नलिखित में से कौन सी यमुना नदी प्रणाली की सहायक नदी नहीं है?</p>",
                    options_en: ["<p>Hindon</p>", "<p>Chambal</p>", 
                                "<p>Sindh</p>", "<p>Chenab</p>"],
                    options_hi: ["<p>हिंडन</p>", "<p>चंबल</p>",
                                "<p>सिंध</p>", "<p>चिनाब</p>"],
                    solution_en: "<p>60.(d) <strong>Chenab,</strong> Sutlej, Jhelum, Beas and Ravi (tributary of Indus). Betwa, Hindon, and Chambal (tributaries of the Yamuna River). Giri, Sind, Uttangan, Sengar, and Rind (small tributaries of the Yamuna River). Yamuna origin - The Great Himalayan range. The longest tributary of the Yamuna River - Tons (flow from the Garhwal region).</p>",
                    solution_hi: "<p>60.(d) <strong>चिनाब,</strong> सतलज, झेलम, ब्यास और रावी (सिंधु की सहायक नदी)। बेतवा, हिंडन और चंबल (यमुना नदी की सहायक नदियाँ)। गिरि, सिंध, उत्तांगन, सेंगर और रिंद (यमुना नदी की छोटी सहायक नदियाँ)। यमुना का उद्गम - महान हिमालय श्रृंखला। यमुना नदी की सबसे लंबी सहायक नदी - टोंस (गढ़वाल क्षेत्र से प्रवाहित)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "61",
                    section: "33",
                    question_en: "<p>61. Five friends V, W, X, Y, and Z are eating dinner around a circular dining table, facing the centre. X is an immediate neighbour of both Z and W. V is sitting to the immediate left of W. Y is sitting second to the left of W. Who is sitting to the immediate left of X ?</p>",
                    question_hi: "<p>61. पांच मित्र - V, W, X, Y और Z एक वृत्ताकार डाइनिंग टेबल के परितः बैठकर उसके केंद्र की ओर मुख करके रात्रि का भोजन कर रहे हैं। X, Z और W दोनों के ठीक बगल में बैठा है। V, W के बाईं ओर ठीक बगल में बैठा है। Y, W के बाईं ओर दूसरे स्थान पर बैठा है। X के बाईं ओर ठीक बगल में कौन बैठा है?</p>",
                    options_en: ["<p>W</p>", "<p>Y</p>", 
                                "<p>V</p>", "<p>Z</p>"],
                    options_hi: ["<p>W</p>", "<p>Y</p>",
                                "<p>V</p>", "<p>Z</p>"],
                    solution_en: "<p>61.(a) As per question Five Friends can sit in following way,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173713786.png\" alt=\"rId23\" width=\"118\" height=\"118\"><br>From the above diagram we can say that W is the person who sits immediate left of X.</p>",
                    solution_hi: "<p>61.(a) प्रश्नानुसार पाँच मित्र निम्नलिखित प्रकार से बैठ सकते हैं,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173713786.png\" alt=\"rId23\" width=\"118\" height=\"118\"><br>उपरोक्त आरेख से हम कह सकते हैं कि W वह व्यक्ति है जो X के ठीक बाएं बैठा है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "62",
                    section: "33",
                    question_en: "<p>62. What is saprotrophic nutrition?</p>",
                    question_hi: "<p>62. मृतपोषी (saprotrophic) पोषण क्या है?</p>",
                    options_en: ["<p>Organism takes food from dead and decaying matter</p>", "<p>Organism depends on other animals and shares nutrition</p>", 
                                "<p>Organism depends on insects for nutrition</p>", "<p>Organism depends on plants for nutrition</p>"],
                    options_hi: ["<p>जीव अपना भोजन मृत और क्षयकारी पदार्थ से प्राप्त करता है</p>", "<p>जीव पोषण के लिए अन्य जानवरों पर निर्भर रहता है और पोषण साझा करता है</p>",
                                "<p>जीव, पोषण के लिए कीड़ों पर निर्भर रहता है</p>", "<p>जीव, पोषण के लिए पौधों पर निर्भर रहता है</p>"],
                    solution_en: "<p>62.(a) <strong>Saprotrophic nutrition</strong> is a process of chemoheterotrophic extracellular digestion involved in the processing of decayed organic matter. Example - fungi and soil bacteria.</p>",
                    solution_hi: "<p>62.(a) <strong>मृतपोषी पोषण</strong> (Saprotrophic nutrition), क्षयग्रस्त कार्बनिक पदार्थों के प्रसंस्करण में शामिल कीमोहेटरोट्रॉफ़िक बाह्यकोशिकीय पाचन की एक प्रक्रिया है। उदाहरण - कवक एवं मृदा जीवाणु।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "63",
                    section: "33",
                    question_en: "<p>63.If <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>4</mn></mrow></msup></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></math> = 322 then, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup></math>-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = ?</p>",
                    question_hi: "<p>63. यदि <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>4</mn></mrow></msup></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac></math>&nbsp;= 322 है तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup></math>-&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> का मान ज्ञात कीजिये ?</p>",
                    options_en: ["<p>70</p>", "<p>76</p>", 
                                "<p>67</p>", "<p>84</p>"],
                    options_hi: ["<p>70</p>", "<p>76</p>",
                                "<p>67</p>", "<p>84</p>"],
                    solution_en: "<p>63.(b) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>4</mn></msup><mo>+</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>4</mn></msup></mfrac><mi>&#160;</mi></math> =322<br><math display=\"inline\"><mo>&#8658;</mo><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>322</mn><mo>+</mo><mn>2</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>324</mn></msqrt></math>= 18<br><math display=\"inline\"><mo>&#8658;</mo><mi>x</mi><mo>-</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>18</mn><mo>-</mo><mn>2</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> = 4<br>Now, <br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mn>3</mn></msup></math> + 3 &times; 4&nbsp;<br>= 64 + 12 = 76</p>",
                    solution_hi: "<p>63.(b) <math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>4</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>4</mn></mrow></msup></mrow></mfrac><mi>&#160;</mi></math> =322<br><math display=\"inline\"><mo>&#8658;</mo><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>2</mn></msup></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>322</mn><mo>+</mo><mn>2</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>324</mn></msqrt></math>= 18<br><math display=\"inline\"><mo>&#8658;</mo><mi>x</mi><mo>-</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>18</mn><mo>-</mo><mn>2</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> = 4<br>अभी, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>x</mi><mn>3</mn></msup><mo>-</mo><mfrac><mn>1</mn><msup><mi>x</mi><mn>3</mn></msup></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>4</mn><mn>3</mn></msup></math> + 3 &times; 4&nbsp;<br>= 64 + 12 = 76</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "64",
                    section: "33",
                    question_en: "<p>64. Select the industry in which goods are produced in the home of the producer.</p>",
                    question_hi: "<p>64. उस उद्योग का चयन कीजिये जिसमें उत्पादक के घर में सामान का उत्पादन होता है।</p>",
                    options_en: ["<p>Large-scale industry</p>", "<p>Cottage industry</p>", 
                                "<p>Secondary industry</p>", "<p>Quaternary industry </p>"],
                    options_hi: ["<p>बड़े पैमाने का उद्योग</p>", "<p>कुटीर-उद्योग</p>",
                                "<p>दितीयक उद्योग</p>", "<p>चतुर्धातुक उद्योग</p>"],
                    solution_en: "<p>64.(b) <strong>Cottage industry.</strong> Other Examples - Carpentry, smithy, carpet, weaving, pottery, blanket making, stone carving. The heavy industries like steel, textile, and automobile manufacturing industry falls under the category of large-scale industry. The secondary industry sector (manufacturing industry) processes the raw materials supplied by primary industries. Quaternary industries are those that involve advanced technologies (generally in computers and communications).</p>",
                    solution_hi: "<p>64.(b) <strong>कुटीर-उद्योग।</strong> अन्य उदाहरण - बढ़ईगीरी, लोहार, कालीन, बुनाई, मिट्टी के बर्तन, कंबल बनाना, पत्थर पर नक्काशी। इस्पात, कपड़ा और ऑटोमोबाइल विनिर्माण उद्योग जैसे भारी उद्योग बड़े पैमाने के उद्योग की श्रेणी में आते हैं। द्वितीयक उद्योग क्षेत्र (विनिर्माण उद्योग) प्राथमिक उद्योगों द्वारा आपूर्ति किए गए कच्चे माल को संसाधित करता है। चतुर्धातुक उद्योग वे हैं जिनमें उन्नत प्रौद्योगिकियां (आमतौर पर कंप्यूटर और संचार में) शामिल होती हैं।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "65",
                    section: "33",
                    question_en: "<p>65. Six friends are sitting around a round table facing the centre with equal distances between two neighbours. Wasim is second to the right of Muskan. Ekta is sitting to the immediate left of Rohit. Anushri is sitting third to the left of Wasim. Suresh is the second to the left of Ekta. Who is to the immediate right of Wasim?</p>",
                    question_hi: "<p>65. छह दोस्त, एक गोलाकार मेज के परितः मेज के केंद्र की ओर मुख करके इस प्रकार बैठे कि एक-दूसरे के बगल में बैठे दो व्यक्तियों के बीच की दूरी समान है। वसीम, मुस्कान के दाईं ओर दूसरे स्थान पर बैठा है । एकता, रोहित के बाईं ओर ठीक बगल में बैठी है। अनुश्री, वसीम के बाईं ओर तीसरे स्थान पर बैठी है। सुरेश, एकता के बाईं ओर दूसरे स्थान पर बैठा है। वसीम के दाईं ओर ठीक बगल में कौन बैठा/बैठी है?</p>",
                    options_en: ["<p>Rohit</p>", "<p>Ekta</p>", 
                                "<p>Muskan</p>", "<p>Suresh</p>"],
                    options_hi: ["<p>रोहित</p>", "<p>एकता</p>",
                                "<p>मुस्कान</p>", "<p>सुरेश</p>"],
                    solution_en: "<p>65.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173714026.png\" alt=\"rId24\" width=\"171\" height=\"142\"><br>We can clearly see that Ekta is sitting immediate right of Wasim.</p>",
                    solution_hi: "<p>65.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173714281.png\" alt=\"rId25\" width=\"154\" height=\"150\"><br>हम स्पष्ट रूप से देख सकते हैं कि एकता वसीम के ठीक दायें बैठी है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "66",
                    section: "33",
                    question_en: "<p>66. ________, combines with <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub></math> present in the cells of our body and provides energy.</p>",
                    question_hi: "<p>66. _______, हमारे शरीर की कोशिकाओं में उपस्थित <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub></math>के साथ संयोजित होकर ऊर्जा प्रदान करता है।</p>",
                    options_en: ["<p>Glycogen</p>", "<p>Glucose</p>", 
                                "<p>Pyruvate</p>", "<p>Amino acids</p>"],
                    options_hi: ["<p>ग्लाइकोजन</p>", "<p>शर्करा</p>",
                                "<p>पाइरूवेट</p>", "<p>अमीनो अम्ल</p>"],
                    solution_en: "<p>66.(b) <strong>Glucose.</strong> It is a simple sugar that serves as a primary source of energy for living organisms, including humans. During digestion, food is broken down into simpler substances. For example, rice, potatoes and bread contain carbohydrates. These carbohydrates are broken down to form glucose. This glucose combines with oxygen in the cells of our body and provides energy. Respiration (reaction):&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>6</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>12</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>6</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><mn>6</mn><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>&#8594;</mo><mn>6</mn><msub><mi>CO</mi><mn>2</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><mn>6</mn><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi><mo>(</mo><mi mathvariant=\"normal\">l</mi><mo>)</mo></math> + energy.</p>",
                    solution_hi: "<p>66.(b) <strong>शर्करा। </strong>यह एक साधारण शर्करा है जो मनुष्यों सहित जीवित जीवों के लिए ऊर्जा के प्राथमिक स्रोत के रूप में कार्य करती है। पाचन के दौरान भोजन सरल पदार्थों में टूट जाता है। उदाहरण के लिए, चावल, आलू और ब्रेड में कार्बोहाइड्रेट होते हैं। ये कार्बोहाइड्रेट टूटकर शर्करा बनाते हैं। यह शर्करा हमारे शरीर की कोशिकाओं में ऑक्सीजन के साथ मिलकर ऊर्जा प्रदान करता है। श्वसन (अभिक्रिया) : <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">C</mi><mn>6</mn></msub><msub><mi mathvariant=\"normal\">H</mi><mn>12</mn></msub><msub><mi mathvariant=\"normal\">O</mi><mn>6</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><mn>6</mn><msub><mi mathvariant=\"normal\">O</mi><mn>2</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>&#8594;</mo><mn>6</mn><msub><mi>CO</mi><mn>2</mn></msub><mo>(</mo><mi>aq</mi><mo>)</mo><mo>+</mo><mn>6</mn><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><mi mathvariant=\"normal\">O</mi><mo>(</mo><mi mathvariant=\"normal\">l</mi><mo>)</mo></math> + ऊर्जा।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "67",
                    section: "33",
                    question_en: "<p>67. Find the Fourth proportional of 12, 24 and 45.</p>",
                    question_hi: "<p>67. 12, 24 और 45 का चतुर्थानुपाती ज्ञात कीजिए।</p>",
                    options_en: ["<p>90</p>", "<p>25</p>", 
                                "<p>30</p>", "<p>60</p>"],
                    options_hi: ["<p>90</p>", "<p>25</p>",
                                "<p>30</p>", "<p>60</p>"],
                    solution_en: "<p>67.(a) 12 : 24 :: 45 : x <br>x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>45</mn></mrow><mn>12</mn></mfrac></math> = 90</p>",
                    solution_hi: "<p>67.(a) 12 : 24 :: 45 : x<br>x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>24</mn><mo>&#215;</mo><mn>45</mn></mrow><mn>12</mn></mfrac></math> = 90</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "68",
                    section: "33",
                    question_en: "<p>68. Which Indian ministry launched the SHe-Box portal to enhance workplace safety for women?</p>",
                    question_hi: "<p>68. महिलाओं के लिए कार्यस्थल सुरक्षा बढ़ाने के लिए SHe-Box पोर्टल किस भारतीय मंत्रालय द्वारा लॉन्च किया गया?</p>",
                    options_en: ["<p>Ministry of Home Affairs</p>", "<p>Ministry of Labour and Employment</p>", 
                                "<p>Ministry of Women and Child Development</p>", "<p>Ministry of Social Justice and Empowerment</p>"],
                    options_hi: ["<p>गृह मंत्रालय</p>", "<p>श्रम और रोजगार मंत्रालय</p>",
                                "<p>महिला और बाल विकास मंत्रालय</p>", "<p>सामाजिक न्याय और अधिकारिता मंत्रालय</p>"],
                    solution_en: "<p>68.(c) <strong>Ministry of Women and Child Development.</strong><br>The Ministry of Women and Child Development launched the SHe-Box portal on August 29, 2024, providing a centralized platform for women to file and monitor complaints of workplace sexual harassment.</p>",
                    solution_hi: "<p>68.(c) <strong>महिला और बाल विकास मंत्रालय।</strong><br>महिला और बाल विकास मंत्रालय ने 29 अगस्त 2024 को SHe-Box पोर्टल लॉन्च किया, जो महिलाओं को कार्यस्थल पर यौन उत्पीड़न की शिकायत दर्ज करने और उसकी निगरानी करने के लिए एक केंद्रीकृत मंच प्रदान करता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "69",
                    section: "33",
                    question_en: "<p>69. A certain number of people are sitting in a straight line facing north. Only three people are sitting between D and I. There are only two people sitting between K and O. I is sitting to the immediate left of O. No one is sitting to the left of D. Only one person is sitting to the right of O. How many people are sitting in the row?</p>",
                    question_hi: "<p>69. कुछ व्यक्ति, एक सीधी रेखा में उत्तर की ओर मुख करके बैठे हैं। D और I के बीच केवल तीन व्यक्ति बैठे हैं। K और O के बीच केवल दो व्यक्ति बैठे हैं। I, O के बाईं ओर ठीक बगल में बैठा है। D के बाईं ओर कोई नहीं बैठा है। O के दाईं ओर केवल एक व्यक्ति बैठा है। पंक्ति में कितने व्यक्ति बैठे हैं?</p>",
                    options_en: ["<p>6</p>", "<p>5</p>", 
                                "<p>8</p>", "<p>7</p>"],
                    options_hi: ["<p>6</p>", "<p>5</p>",
                                "<p>8</p>", "<p>7</p>"],
                    solution_en: "<p>69.(d)&nbsp;ATQ,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173714414.png\" alt=\"rId26\" width=\"349\" height=\"42\"><br>We can see that there are 7 people in the&nbsp;row</p>",
                    solution_hi: "<p>69.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173714414.png\" alt=\"rId26\" width=\"349\" height=\"42\"><br>हम देख सकते हैं कि पंक्ति में 7 लोग हैं</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "70",
                    section: "33",
                    question_en: "<p>70. The uterine wall in females prepares itself to receive the developing fertilized egg. In case there is no fertilization, the thickened lining of the uterine wall breaks down and goes out of the body along with blood. This is called:</p>",
                    question_hi: "<p>70. महिलाओं में गर्भाशय की भित्ति (uterine wall), स्वयं को विकासशील निषेचित अंडाणु को ग्रहण करने के लिए तैयार करती है। यदि कोई निषेचन नहीं होता है, तो गर्भाशय की भित्ति की मोटी परत (lining) निस्तारित होकर रक्त के साथ शरीर से बाहर निकल जाती है। इसे ___कहा जाता है।</p>",
                    options_en: ["<p>Menopause</p>", "<p>Menstruation</p>", 
                                "<p>Secondary sexual characters</p>", "<p>Adam&rsquo;s apple</p>"],
                    options_hi: ["<p>रजोनिवृत्ति (Menopause)</p>", "<p>मासिक धर्म/रजोधर्म (Menstruation)</p>",
                                "<p>गौण लैंगिक लक्षण (Secondary sexual characters)</p>", "<p>कंठमणि (Adam&rsquo;s apple)</p>"],
                    solution_en: "<p>70.(b) <strong>Menstruation. Menopause</strong> - Marks the end of their reproductive years. <strong>Secondary sexual characters</strong> - Growth of facial hair, Shoulder become wider, Voice becomes hoarse, Breasts enlarge and mammary glands develop, Voice becomes shrill. <strong>Adam\'s apple</strong> - The protruding part of the throat formed due to the enlargement of larynx. This enlargement of the larynx or voice box occurs during puberty.</p>",
                    solution_hi: "<p>70.(b) <strong>मासिक धर्म / रजोधर्म । रजोनिवृत्ति</strong> - उनके प्रजनन वर्षों के अंत का प्रतीक है। <strong>गौण लैंगिक लक्षण</strong> (Secondary sexual characters)- चेहरे पर बालों का बढ़ना, कंधा चौड़ा हो जाना, आवाज भारी हो जाती है, स्तन में वृद्धि और स्तन ग्रंथियां विकसित हो जाती हैं, आवाज तीखी हो जाती है। <strong>एडम्&zwj;स एपल्&zwj;स/टेंटुआ </strong>(Adam\'s apple) - गले का बाहर निकला हुआ भाग जो स्वरयंत्र के बढ़ने के कारण बनता है। स्वरयंत्र या आवाज बॉक्स की यह वृद्धि यौवनारम्भ के दौरान होती है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "71",
                    section: "33",
                    question_en: "<p>71. If <math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mi>b</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> and 3a + 2b = 24, then find the value of a.</p>",
                    question_hi: "<p>71. यदि <math display=\"inline\"><mfrac><mrow><mi>a</mi></mrow><mrow><mi>b</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>और 3a + 2b = 24 हैं, तो a का मान ज्ञात कीजिए ।</p>",
                    options_en: ["<p>4</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mn>5</mn></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>4</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>13</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mn>5</mn></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>71.(d) 3a = 4b&hellip;(1)<br>3a + 2b = 24<br><math display=\"inline\"><mo>&#8658;</mo><mn>4</mn><mi>b</mi><mo>+</mo><mn>2</mn><mi>b</mi><mo>=</mo><mn>24</mn></math> &rArr;6b=24<br><math display=\"inline\"><mo>&#8658;</mo><mi>b</mi><mo>=</mo><mn>4</mn><mi>&#160;</mi><mi>t</mi><mi>h</mi><mi>e</mi><mi>n</mi><mi>&#160;</mi><mn>3</mn><mi>a</mi><mo>=</mo><mn>16</mn><mo>&#8658;</mo><mi>a</mi><mo>=</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math></p>",
                    solution_hi: "<p>71.(d) 3a = 4b&hellip;(1)<br>3a + 2b = 24<br><math display=\"inline\"><mo>&#8658;</mo><mn>4</mn><mi>b</mi><mo>+</mo><mn>2</mn><mi>b</mi><mo>=</mo><mn>24</mn></math> &rArr; 6b = 24<br><math display=\"inline\"><mo>&#8658;</mo><mi>b</mi><mo>=</mo><mn>4</mn><mi>&#160;</mi><mi>&#2340;</mi><mi>&#2348;</mi><mi>&#160;</mi><mi>&#160;</mi><mn>3</mn><mi>a</mi><mo>=</mo><mn>16</mn><mo>&#8658;</mo><mi>a</mi><mo>=</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>3</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "72",
                    section: "33",
                    question_en: "<p>72. Where was the Women\'s Twenty20 (T20) World Cup 2024 held?</p>",
                    question_hi: "<p>72. महिला ट्वेंटी20 (T20) विश्व कप 2024 कहां आयोजित हुआ?</p>",
                    options_en: ["<p>India</p>", "<p>Bangladesh</p>", 
                                "<p>Sri Lanka</p>", "<p>United Arab Emirates (UAE)</p>"],
                    options_hi: ["<p>भारत</p>", "<p>बांग्लादेश</p>",
                                "<p>श्रीलंका</p>", "<p>संयुक्त अरब अमीरात (UAE)</p>"],
                    solution_en: "<p>72.(d) <strong>United Arab Emirates (UAE)</strong>. <br>The ICC Women&rsquo;s Twenty20 (T20) World Cup 2024 was held from 3rd to 20th October 2024 in Dubai and Sharjah, United Arab Emirates (UAE). New Zealand&rsquo;s Women&rsquo;s Cricket team won their first-ever ICC Women&rsquo;s T20 World Cup title by defeating South Africa&rsquo;s Women&rsquo;s Cricket team by 32 runs in the final.</p>",
                    solution_hi: "<p>72. (d) <strong>संयुक्त अरब अमीरात (UAE)।</strong><br>ICC महिला ट्वेंटी20 (T20) विश्व कप 2024, 3 से 20 अक्टूबर 2024 तक दुबई और शारजाह, संयुक्त अरब अमीरात (UAE) में आयोजित हुआ। न्यूजीलैंड की महिला क्रिकेट टीम ने दक्षिण अफ्रीका की महिला क्रिकेट टीम को फाइनल में 32 रनों से हराकर अपना पहला ICC महिला T20 विश्व कप खिताब जीता।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "73",
                    section: "33",
                    question_en: "<p>73. Among four stalls, P, Q, R and S. S is 40 m to the north of R. P is 60 m to the east of R, and Q is 50 m to the south of R. How far and in which direction is Q with reference to S ?</p>",
                    question_hi: "<p>73. P, Q, R और S इन चार स्टॉलों में S, R के उत्तर में 40m पर स्थित है। P, R के पूर्व में 60m पर स्थित है और Q, R के दक्षिण में 50m पर स्थित है। S के सापेक्ष Q कितनी दूर और किस दिशा में स्थित है?</p>",
                    options_en: ["<p>90 m, North</p>", "<p>100 m, North</p>", 
                                "<p>110 m, South</p>", "<p>90 m, South</p>"],
                    options_hi: ["<p>90 मीटर, उत्तर</p>", "<p>100 मीटर, उत्तर</p>",
                                "<p>110 मीटर, दक्षिण</p>", "<p>90 मीटर, दक्षिण</p>"],
                    solution_en: "<p>73.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173714557.png\" alt=\"rId27\" width=\"130\" height=\"173\"><br>We can clearly see that Q is 90 m south with reference to S.</p>",
                    solution_hi: "<p>73.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173714557.png\" alt=\"rId27\" width=\"130\" height=\"173\"><br>हम स्पष्ट रूप से देख सकते हैं कि Q, S के सन्दर्भ में 90 मीटर दक्षिण में है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "74",
                    section: "33",
                    question_en: "<p>74. Which of the following plants produce unisexual flowers?</p>",
                    question_hi: "<p>74. निम्नलिखित में से कौन से पौधे एकलिंगी पुष्प उत्पन्न करते हैं?</p>",
                    options_en: ["<p>Mustard and Hibiscus</p>", "<p>Watermelon and Papaya</p>", 
                                "<p>Hibiscus and Papaya</p>", "<p>Watermelon and Mustard</p>"],
                    options_hi: ["<p>सरसों और गुड़हल</p>", "<p>तरबूज और पपीता</p>",
                                "<p>गुड़हल और पपीता</p>", "<p>तरबूज और सरसों</p>"],
                    solution_en: "<p>74.(b) <strong>Watermelon and Papaya</strong>. Unisexual flowers - A flower that has either the male or female reproductive organs in separate flowers. Examples : Papaya, watermelon, Pumpkin, Pine etc. Bisexual Flowers - A flower that has both male and female reproductive organs in separate flowers. Examples : Lily, Rose, Sunflower, Tulip, Daffodil, Mustard, Brinjal, Hibiscus, Tomato, etc.</p>",
                    solution_hi: "<p>74.(b) <strong>तरबूज और पपीता।</strong> एकलिंगी फूल - एक फूल जिसमें अलग-अलग फूलों में नर या मादा प्रजनन अंग होते हैं। उदाहरण : पपीता, तरबूज, कद्दू, चीड़ आदि। उभयलिंगी फूल - ऐसे फूल जिनमें नर और मादा दोनों प्रजनन अंग अलग-अलग फूलों में होते हैं। उदाहरण : लिली, गुलाब, सूरजमुखी, ट्यूलिप, डैफोडिल, सरसों, बैंगन, हिबिस्कस, टमाटर, आदि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "75",
                    section: "33",
                    question_en: "<p>75. Two mixtures A and B have the following compositions. Mixture A has copper and tin in the ratio 1 : 2. Mixture B has copper and tin in the ratio 1 : 3. If equal quantities of mixtures A and B are used for producing mixture C, then find the ratio of copper and tin in mixture C.</p>",
                    question_hi: "<p>75. दो मिश्रणों A और B के संघटन निम्नलिखित हैं: मिश्रण A में तांबा और टिन का अनुपात 1 : 2 है। मिश्रण B में तांबा और टिन का अनुपात 1 : 3 है। यदि मिश्रणों A और B को समान मात्राओं में मिलाकर मिश्रण C बनाया गया हो, तो मिश्रण C में तांबा और टिन का अनुपात ज्ञात कीजिए।</p>",
                    options_en: ["<p>7 : 17</p>", "<p>7 : 12</p>", 
                                "<p>2 : 5</p>", "<p>1 : 5</p>"],
                    options_hi: ["<p>7 : 17</p>", "<p>7 : 12</p>",
                                "<p>2 : 5</p>", "<p>1 : 5</p>"],
                    solution_en: "<p>75.(a) <br>Ratio&nbsp; &nbsp; &nbsp; &nbsp;<math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;Copper&nbsp; &nbsp;:&nbsp; &nbsp; Tin<br>Mixture A <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 2 )&times; 4 &rarr; 4 : 8<br>Mixture B <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; 3 )&times; 3 &rarr; 3 : 9<br>For equal quantities of mixture,<br>So, the ratio of copper and tin in the mixture C <math display=\"inline\"><mo>&#8658;</mo></math> (4 + 3) : (8 + 9) = 7 : 17</p>",
                    solution_hi: "<p>75.(a)<br>अनुपात&nbsp; <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; ताँबा&nbsp; :&nbsp; &nbsp;टिन<br>मिश्रण A <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 1&nbsp; &nbsp; :&nbsp; &nbsp;2 )&times; 4 &rarr; 4 : 8<br>मिश्रण B <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 1&nbsp; &nbsp; :&nbsp; &nbsp;3 )&times; 3 &rarr; 3 : 9<br>इसलिए, मिश्रण C में तांबे और टिन का अनुपात <math display=\"inline\"><mo>&#8658;</mo></math> (4 + 3) : (8 + 9) = 7 : 17</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "76",
                    section: "33",
                    question_en: "<p>76. Who authored the book \"MODIALOGUE: Conversations for a Viksit Bharat\", released in November 2024?</p>",
                    question_hi: "<p>76. नवंबर 2024 में प्रकाशित पुस्तक \"मोडियालॉग: कन्वर्सेशन फॉर ए विकसित भारत\" के लेखक कौन हैं?</p>",
                    options_en: ["<p>Dr. Ashwin Fernandes</p>", "<p>Dr. S. Jaishankar</p>", 
                                "<p>Dr. Davendra Kumar Dhodawat</p>", "<p>Dr. Vikram Singh</p>"],
                    options_hi: ["<p>डॉ. अश्विन फर्नांडिस</p>", "<p>डॉ. एस. जयशंकर</p>",
                                "<p>डॉ. दवेंद्र कुमार धोडावत</p>", "<p>डॉ. विक्रम सिंह</p>"],
                    solution_en: "<p>76. (a) <strong>Dr. Ashwin Fernandes.</strong> The book examines Prime Minister Modi\'s communication strategies through his Mann Ki Baat radio program.</p>",
                    solution_hi: "<p>76.(a) <strong>डॉ. अश्विन फर्नांडिस।</strong> यह पुस्तक प्रधानमंत्री मोदी की मन की बात रेडियो कार्यक्रम के माध्यम से संचार रणनीतियों की जांच करती है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "77",
                    section: "33",
                    question_en: "<p>77. Aakash starts from Point O and drives 3 km towards the north. He then takes a right turn, drives 5 km, turns right and drives 10 km. He then takes a right turn and drives 5 km. He takes a final right turn, drives 3 km and stops at Point Z. How far and towards which direction should he drive in order to reach Point O from Point Z ? <br>(All turns are 90 degree turns only)</p>",
                    question_hi: "<p>77. आकाश बिंदु O से चलना शुरू करता है और उत्तर की ओर 3 km ड्राइव करता है। फिर वह दाएं मुड़ता है, 5km ड्राइव करता है। वह फिर से दाएं मुड़ता है और 10 km ड्राइव करता है। वह फिर से दाएं मुड़ता है और 5 km ड्राइव करता है। अंत में वह दाएं मुड़ता है, 3 km ड्राइव करता है और बिंदु Z पर रुक जाता है। बिंदु Z से बिंदु O तक पहुंचने के लिए उसे किस दिशा में और कितनी दूरी तय करनी होगी ?&nbsp;(सभी मोड़ केवल 90 डिग्री के मोड़ हैं)</p>",
                    options_en: ["<p>4km towards the north</p>", "<p>2 km towards the east</p>", 
                                "<p>2 km towards the west</p>", "<p>3 km towards the east</p>"],
                    options_hi: ["<p>उत्तर की ओर 4 km</p>", "<p>पूर्व की ओर 2 km</p>",
                                "<p>पश्चिम की ओर 2 km</p>", "<p>पूर्व की ओर 3 km</p>"],
                    solution_en: "<p>77.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173714771.png\" alt=\"rId28\" width=\"160\" height=\"142\"><br>We can clearly see that O is 4 km north from point Z.</p>",
                    solution_hi: "<p>77.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173714771.png\" alt=\"rId28\" width=\"192\" height=\"170\"><br>हम स्पष्ट रूप से देख सकते हैं कि O, बिंदु Z से 4 किमी उत्तर में है।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "78",
                    section: "33",
                    question_en: "<p>78. Which of the following is a unicellular organism?</p>",
                    question_hi: "<p>78. इनमें से कौन सा एककोशिकीय जीव है?</p>",
                    options_en: ["<p>Cuscuta</p>", "<p>Paramecium</p>", 
                                "<p>Lice</p>", "<p>Bread mould</p>"],
                    options_hi: ["<p>अमरबेल (Cuscuta)</p>", "<p>पैरामीशियम (Paramecium)</p>",
                                "<p>जूं (Lice)</p>", "<p>ब्रेड फफूंद (Bread mould )</p>"],
                    solution_en: "<p>78.(b) <strong>Paramecium</strong> is a slipper -shaped, unicellular organism found in pond water. <strong>Unicellular organisms</strong> are made up of only one cell. In a unicellular organism, all the important life processes such as respiration, digestion, excretion, and reproduction take place within a single cell. Examples - Bacteria, Protists, and yeast.</p>",
                    solution_hi: "<p>78.(b) <strong>पैरामीशियम</strong> एक चप्पल के आकार का, एककोशिकीय जीव है जो तालाब के पानी में पाया जाता है।<strong> एककोशिकीय जीव</strong> केवल एक कोशिका से बने होते हैं। एककोशिकीय जीव में, सभी महत्वपूर्ण जीवन प्रक्रियाएं जैसे श्वसन, पाचन, उत्सर्जन और प्रजनन एक ही कोशिका के भीतर होते हैं। उदाहरण - बैक्टीरिया, प्रोटिस्ट और यीस्ट।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "79",
                    section: "33",
                    question_en: "<p>79. A, B and C can do a piece of work in 20, 30 and 60 days, respectively. In how many days can A complete the work if he is assisted by B and C on every third day?</p>",
                    question_hi: "<p>79. A, B और C किसी कार्य को क्रमशः 20, 30 और 60 दिन में कर सकते हैं। यदि प्रत्येक तीसरे दिन B और C, A की सहायता करते हैं, तो&nbsp;A उस कार्य को कितने दिन में पूरा कर सकता है?</p>",
                    options_en: ["<p>15 days</p>", "<p>13 days</p>", 
                                "<p>24 days</p>", "<p>12 days</p>"],
                    options_hi: ["<p>15 दिन</p>", "<p>13 दिन</p>",
                                "<p>24 दिन</p>", "<p>12 दिन</p>"],
                    solution_en: "<p>79.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173715120.png\" alt=\"rId29\" width=\"204\" height=\"129\"><br>A work for 2 days alone and assisted by B and C on third day,<br>work completed in 3 days&nbsp;&nbsp;=12 units<br>1 unit work of time taken to completed in =<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>days<br>60 unit work will be completed in = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>&times; 60<br><math display=\"inline\"><mo>=</mo></math> 15 days</p>",
                    solution_hi: "<p>79.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173715432.png\" alt=\"rId30\" width=\"186\" height=\"139\"><br>A अकेले 2 दिनों के लिए काम करता है और तीसरे दिन B और C द्वारा सहायता की जाती है,<br>3 दिन में किया गया कार्य = 12 इकाई<br>1 इकाई कार्य पूरा होने में लगा समय =<math display=\"inline\"><mi>&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> दिनों <br>60 इकाई कार्य पूरा होने में लगा समय =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math>&times; 60 = 15 दिन</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "80",
                    section: "33",
                    question_en: "<p>80. Who was the oldest member of Japan\'s royal family who passed away at the age of 101 in 2024?</p>",
                    question_hi: "<p>80. जापान के शाही परिवार के सबसे बुजुर्ग सदस्य, जिनका 2024 में 101 वर्ष की आयु में निधन हो गया, कौन थे?</p>",
                    options_en: ["<p>Princess Takamatsu</p>", "<p>Princess Mikasa</p>", 
                                "<p>Princess Chichibu</p>", "<p>Empress Michiko</p>"],
                    options_hi: ["<p>प्रिंसेस ताकामात्सु</p>", "<p>प्रिंसेस मिकासा</p>",
                                "<p>प्रिंसेस चिचिबु</p>", "<p>महारानी मिचिको</p>"],
                    solution_en: "<p>80.(b) <strong>Princess Mikasa</strong>. Yuriko Princess Mikasa was the wife of Prince Mikasa, the fourth son of Emperor Taisho. As the oldest member of Japan\'s imperial family, she was the last surviving paternal great-aunt by marriage of Emperor Naruhito. Born in the Taisho era, she held a prominent position within the royal family.</p>",
                    solution_hi: "<p>80.(b) <strong>प्रिंसेस मिकासा।</strong> युरिको प्रिंसेस मिकासा, प्रिंस मिकासा की पत्नी थीं, जो सम्राट तैशो के चौथे पुत्र थे। जापान के शाही परिवार के सबसे बुजुर्ग सदस्य के रूप में, वह सम्राट नारुहितो की विवाह के माध्यम से अंतिम जीवित पितृसत्तात्मक महान चाची थीं। तैशो युग में जन्मी, उनका शाही परिवार में एक महत्वपूर्ण स्थान था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "81",
                    section: "33",
                    question_en: "<p>81. If 1 is added to each odd digit and 2 is subtracted from each even digit in the number 6513742678, how many digits will appear more than once in the new number thus formed?</p>",
                    question_hi: "<p>81. यदि संख्या 6513742678 में प्रत्येक विषम अंक में 1 जोड़ा जाए और प्रत्येक सम अंक में से 2 घटाया जाए, तो इस प्रकार बनी नई संख्या में कितने अंक एक से अधिक बार आएंगे?</p>",
                    options_en: ["<p>Two</p>", "<p>Six</p>", 
                                "<p>Four</p>", "<p>Five</p>"],
                    options_hi: ["<p>दो</p>", "<p>छह</p>",
                                "<p>चार</p>", "<p>पांच</p>"],
                    solution_en: "<p>81.(c) Given number = 6513742678<br>According to the given instruction after adding 1 to the odd digit and subtracting 2 from the even digit <br>New number = 4624820486<br>The digits 2 , 4 ,6, 8 have appeared more than once.</p>",
                    solution_hi: "<p>81.(c) दी गई संख्या = 6513742678<br>दिए गए निर्देश के अनुसार विषम अंक में 1 जोड़ने तथा सम अंक में से 2 घटाने पर<br>नई संख्या = 4624820486<br>अंक 2, 4, 6, 8 एक से अधिक बार आए हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "82",
                    section: "33",
                    question_en: "<p>82. The &lsquo;Urea Cycle&rsquo; takes place in the human ________.</p>",
                    question_hi: "<p>82. मानव में \'यूरिया चक्र\' निम्न में से किस अंग में होता है।</p>",
                    options_en: ["<p>Kidney</p>", "<p>Liver</p>", 
                                "<p>Lungs</p>", "<p>Pancreas</p>"],
                    options_hi: ["<p>वृक्क</p>", "<p>यकृत</p>",
                                "<p>फेफडो</p>", "<p>अग्न्याशय</p>"],
                    solution_en: "<p>82.(b) <strong>Liver.</strong> The urea cycle (also known as the ornithine cycle) is a cycle of biochemical reactions that produces urea&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mi>N</mi><msub><mi>H</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msub></math>CO from ammonia (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>N</mi><msub><mi>H</mi><mn>3</mn></msub></math>). It converts highly toxic ammonia to urea for excretion. The liver is the largest solid organ in the body. <strong>Kidney-</strong> It filters out a variety of water-soluble waste products and environmental toxins into the urine for excretion. Other excretory organs-<br>Ureter, Urinary bladder and Urethra.</p>",
                    solution_hi: "<p>82.(b) <strong>यकृत</strong> । यूरिया चक्र (ornithine चक्र के रूप में भी जाना जाता है) जैव रासायनिक अभिक्रियाओं का एक चक्र है जो अमोनिया (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>N</mi><msub><mi>H</mi><mn>3</mn></msub></math>) से यूरिया <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mrow><mo>(</mo><mi>N</mi><msub><mi>H</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msub></math>CO का उत्पादन करता है। यह उत्सर्जन के लिए अत्यधिक विषैले अमोनिया को यूरिया में परिवर्तित करता है। यकृत शरीर का सबसे बड़ा ठोस अंग है।<strong> वृक्क (Kidney) -</strong> यह उत्सर्जन के लिए मूत्र में विभिन्न प्रकार के जल में घुलनशील अपशिष्ट उत्पादों और पर्यावरण के विषाक्त पदार्थों को फ़िल्टर करता है। अन्य उत्सर्जी अंग- यूरेटर, यूरिनरी ब्लैडर और यूरेथ्रा।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "83",
                    section: "33",
                    question_en: "<p>83. Two taps can separately fill a cistern in 20 minutes and 25 minutes. Both taps are open for 10 minutes after which the slower one is closed. How long will it take to fill the remaining portion by the other tap alone ?</p>",
                    question_hi: "<p>83. दो नल अलग-अलग एक टंकी को क्रमशः 20 मिनट और 25 मिनट में भर सकते हैं। दोनों नल 10 मिनट के लिए खोले जाते हैं, जिसके बाद धीमे नल को बंद कर दिया जाता है। टंकी के शेष हिस्से को अकेले भरने में दूसरे नल को कितना समय लगेगा ?</p>",
                    options_en: ["<p>10 minutes</p>", "<p>4 minutes</p>", 
                                "<p>5 minutes</p>", "<p>2 minutes</p>"],
                    options_hi: ["<p>10 मिनट</p>", "<p>4 मिनट</p>",
                                "<p>5 मिनट</p>", "<p>2 मिनट</p>"],
                    solution_en: "<p>83.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173715661.png\" alt=\"rId31\" width=\"208\" height=\"143\"><br>Total tank filled by both tap A and B in 10min. = 10 &times; (5 + 4) = 90 unit<br>Remaining portion <br>= (100 - 90)unit = 10 unit<br>Tap A (faster tap) filled remaining 10 unit in <math display=\"inline\"><mo>&#8594;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>5</mn></mfrac></math>&rArr; 2min.</p>",
                    solution_hi: "<p>83.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173715860.png\" alt=\"rId32\" width=\"201\" height=\"144\"><br>10 मिनट में नल A और B दोनों द्वारा कुल टैंक भरा गया. = 10 &times; (5 + 4) = 90 यूनिट<br>शेष भाग = (100 - 90)यूनिट = 10 यूनिट<br>शेष 10 यूनिट भरने में नल A (तेज नल ) द्वारा लिया गया समय <math display=\"inline\"><mo>&#8594;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>5</mn></mfrac></math>&rArr; 2 मिनट</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "84",
                    section: "33",
                    question_en: "<p>84. ______ aid (s) in the emulsification of fat.</p>",
                    question_hi: "<p>84. ______ वसा के पायसीकरण में सहायता करता है।</p>",
                    options_en: ["<p>Vit-K Trypsin</p>", "<p>Bile salts</p>", 
                                "<p>HCl</p>", "<p>Pepsin</p>"],
                    options_hi: ["<p>विटामिन k ट्रिप्सिन</p>", "<p>पित्त लवण</p>",
                                "<p>HCL</p>", "<p>पेप्सिन</p>"],
                    solution_en: "<p>84.(b) <strong>Bile salts</strong> (found in the liver). Trypsin (found in the small intestine) is an enzyme that aids with digestion. Hydrochloric acid (found in the stomach) makes the environment in the stomach acidic, killing the microbes in the stomach. Pepsin (discovered in 1836 by Theodore Schwann) is an endopeptidase enzyme that degrades proteins into peptides.</p>",
                    solution_hi: "<p>84.(b)<strong> पित्त लवण </strong>(यकृत में पाया जाता है)। ट्रिप्सिन (छोटी आंत में पाया जाता है) एक एंजाइम है जो पाचन में सहायता करता है। हाइड्रोक्लोरिक अम्ल (आमाशय में पाया जाता है) आमाशय में उपस्थित पदार्थ को अम्लीय बना देता है, आमाशय में रोगाणुओं को नष्ट करता है। पेप्सिन (थिओडोर श्वान द्वारा 1836 में खोजा गया) एक एंडोपेप्टिडेज़ एंजाइम है जो प्रोटीन को पेप्टाइड्स में विघटित करता है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "85",
                    section: "33",
                    question_en: "<p>85. If &lsquo;@&rsquo; indicates either &lsquo;+&rsquo; or &lsquo;&times;&rsquo;, what will be the minimum value of &lsquo;1 @ 2 @ 3 @ 4 @ 5&rsquo;?<br>(Note: we have choice to replace each of the four @ signs in the expression by either &lsquo;+&rsquo; or &lsquo;&times;&rsquo; operation)</p>",
                    question_hi: "<p>85. यदि \'@\' या तो \'+\' या \'&times;\' को इंगित करता है, तो \'1 @ 2 @ 3 @ 4 @ 5\' का न्यूनतम मान क्या होगा?<br>(नोट: हमारे पास व्यंजक में चार @ चिह्नों में से प्रत्येक को \'+\' या \'&times;\' संक्रिया द्वारा प्रतिस्थापित करने का विकल्प है)</p>",
                    options_en: ["<p>14</p>", "<p>12</p>", 
                                "<p>15</p>", "<p>13</p>"],
                    options_hi: ["<p>14</p>", "<p>12</p>",
                                "<p>15</p>", "<p>13</p>"],
                    solution_en: "<p>85.(a) As we know (1 &times; 2) &lt; ( 1 + 2) and this is the only case where sum is greater than multiplication.<br>So, in the first case, we will take @ = &times; and in all other cases we will take @ = +, <br>The minimum value of the given expression.<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 @ 2 @ 3 @ 4 @ 5 <br>= 1 &times; 2 + 3 + 4 + 5 = 14</p>",
                    solution_hi: "<p>85.(a) जैसा कि हम जानते हैं (1 &times; 2) &lt; (1 + 2) और यह एकमात्र स्तिथि है जहां योग गुणन से अधिक है।<br>तो पहली स्तिथि में हम लेंगे @ = &times; और अन्य सभी स्तिथिओं में हम लेंगे @ = +<br>दिए गए व्यंजक का न्यूनतम मान<br><math display=\"inline\"><mo>&#8658;</mo></math> 1 @ 2 @ 3 @ 4 @ 5 <br>= 1&times; 2 + 3 + 4 + 5 = 14</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "86",
                    section: "33",
                    question_en: "<p>86. Two friends start at the same time from cities P and Q towards cities Q and P respectively. After meeting at some point between P and Q, they reach their destinations in 54 and 24 minutes respectively. In what time did the one who moved from Q to P complete his journey?</p>",
                    question_hi: "<p>86. दो दोस्त, एक ही समय क्रमशः शहर P से Q और शहर Q से P की ओर चलना शुरू करते हैं। P और Q के बीच किसी बिंदु पर मिलने के बाद, वे क्रमशः 54 और 24 मिनट में अपने-अपने गंतव्य स्थान तक पहुंच जाते हैं। Q से P तक जाने वाले दोस्त ने अपनी यात्रा कितने समय में पूरी की?</p>",
                    options_en: ["<p>48 minutes</p>", "<p>36 minutes</p>", 
                                "<p>72 minutes</p>", "<p>60 minutes</p>"],
                    options_hi: ["<p>48 मिनट</p>", "<p>36 मिनट</p>",
                                "<p>72 मिनट</p>", "<p>60 मिनट</p>"],
                    solution_en: "<p>86.(d) Time prior to crossing each other = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msub><mi>T</mi><mn>1</mn></msub><mo>&#215;</mo><msub><mi>T</mi><mn>2</mn></msub></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>54</mn><mo>&#215;</mo><mn>24</mn></msqrt></math><br>= 36 minutes<br>Time taken by person to moved from Q to P to complete his journey <br>= 36 + 24 = 60 minutes</p>",
                    solution_hi: "<p>86.(d) एक दूसरे को पार करने से पहले का समय <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msub><mi>T</mi><mn>1</mn></msub><mo>&#215;</mo><msub><mi>T</mi><mn>2</mn></msub></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>54</mn><mo>&#215;</mo><mn>24</mn></msqrt></math>&nbsp;= 36 minutes<br>व्यक्ति द्वारा Q से P तक अपनी यात्रा पूरी करने में लगा समय = 36 + 24 = 60 minutes</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "87",
                    section: "33",
                    question_en: "<p>87. Which among the following is NOT an extension for a video file ?</p>",
                    question_hi: "<p>87. निम्नलिखित में से कौन एक वीडियो फ़ाइल (video file) का एक्सटेंशन नहीं है ?</p>",
                    options_en: ["<p>.avi</p>", "<p>.mov</p>", 
                                "<p>.jpeg</p>", "<p>.mp4</p>"],
                    options_hi: ["<p>.avi</p>", "<p>.mov</p>",
                                "<p>.jpeg</p>", "<p>.mp4</p>"],
                    solution_en: "<p>87.(c) <strong>jpeg</strong> stands for &ldquo;Joint Photographic Experts Group&rdquo;. It is an extension of the image file<strong>. avi</strong> is Audio Video Interleave.</p>",
                    solution_hi: "<p>87.(c) <strong>jpeg</strong> का विस्तार &ldquo;Joint Photographic Experts Group&rdquo; है। यह इमेज फाइल का एक्सटेंशन है। <strong>.avi </strong>ऑडियो वीडियो इंटरलीव (Audio Video Interleave) है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "88",
                    section: "33",
                    question_en: "<p>88. In a class of 50 students, Aryan ranks 17th from the top. What is his rank from the bottom?</p>",
                    question_hi: "<p>88. 50 विद्यार्थियों की एक कक्षा में आर्यन का स्थान ऊपर से 17वां है। नीचे से उसका स्थान क्या है?</p>",
                    options_en: ["<p>32nd</p>", "<p>18th</p>", 
                                "<p>34th</p>", "<p>33rd</p>"],
                    options_hi: ["<p>32वां</p>", "<p>18वां</p>",
                                "<p>34वां</p>", "<p>33वां</p>"],
                    solution_en: "<p>88.(c) As we know, rank from the top = [total strength - rank from bottom + 1 ]<br>So, aryan&rsquo;s rank from the bottom = (50 - 17 + 1) = 34th.</p>",
                    solution_hi: "<p>88.(c) जैसा कि हम जानते हैं, ऊपर से रैंक = [ कुल संख्या - नीचे से रैंक +1]<br>तो, नीचे से आर्यन की रैंक = (50 - 17 + 1) = 34 वां</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "89",
                    section: "33",
                    question_en: "<p>89. The speed of a stream is 4 km/h. A boat can go 40 km downstream and 12 km upstream in 4 hours. What is the speed (in km/h) of the boat in still water?</p>",
                    question_hi: "<p>89. धारा की चाल 4 km/h है। एक नाव धारा की दिशा में 40 km और धारा की विपरीत दिशा में 12 km की दूरी 4 घंटे में तय कर सकती है। शांत जल में नाव की चाल (km/h में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>16</p>", "<p>12</p>", 
                                "<p>10</p>", "<p>14</p>"],
                    options_hi: ["<p>16</p>", "<p>12</p>",
                                "<p>10</p>", "<p>14</p>"],
                    solution_en: "<p>89.(b) Let the speed of the boat in still water be x km/h.<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mrow><mi>x</mi><mo>-</mo><mn>4</mn></mrow></mfrac></math> = 4<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mi>x</mi><mo>-</mo><mn>160</mn><mo>+</mo><mn>12</mn><mi>x</mi><mo>+</mo><mn>48</mn></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>16</mn></mrow></mfrac></math> =4<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mrow><mn>4</mn><mi>x</mi></mrow><mn>2</mn></msup></math> - 64 = 52x - 112<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mrow><mn>4</mn><mi>x</mi></mrow><mn>2</mn></msup></math><strong> </strong>- 52x + 48 = 0<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mi>x</mi><mn>2</mn></msup></math><strong> </strong>- 13x + 12 = 0<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mi>x</mi><mn>2</mn></msup></math> - 12x - x + 12 = 0<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>(x - 12)(x - 1) = 0 &rArr; x = 12 or 1<br>Hence, the speed of the boat = 12 km/h</p>",
                    solution_hi: "<p>89.(b)<br>माना स्थिर जल में नाव की गति x किमी/घंटा है।<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mrow><mi>x</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mrow><mi>x</mi><mo>-</mo><mn>4</mn></mrow></mfrac></math> = 4<br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mi>x</mi><mo>-</mo><mn>160</mn><mo>+</mo><mn>12</mn><mi>x</mi><mo>+</mo><mn>48</mn></mrow><mrow><msup><mi>x</mi><mn>2</mn></msup><mo>-</mo><mn>16</mn></mrow></mfrac></math> = 4<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mrow><mn>4</mn><mi>x</mi></mrow><mn>2</mn></msup></math> - 64 = 52x - 112<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mrow><mn>4</mn><mi>x</mi></mrow><mn>2</mn></msup></math><strong> </strong>- 52x + 48 = 0<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mi>x</mi><mn>2</mn></msup></math><strong> </strong>- 13x + 12 = 0<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><msup><mi>x</mi><mn>2</mn></msup></math> - 12x - x + 12 = 0<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>(x - 12)(x - 1) = 0 &rArr; x = 12 या 1<br>अत: नाव की गति = 12 किमी/घंटा</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "90",
                    section: "33",
                    question_en: "<p>90. Which of the following is not a source of geothermal energy?</p>",
                    question_hi: "<p>90. निम्न में से कौन - सा भूतापीय ऊर्जा का स्रोत नहीं है?</p>",
                    options_en: ["<p>Lava flows</p>", "<p>Geysers</p>", 
                                "<p>Biomass</p>", "<p>Fumaroles</p>"],
                    options_hi: ["<p>लावास्तर (Lava flows)</p>", "<p>उष्णोत्स (Geysers)</p>",
                                "<p>बायोमास (Biomass)</p>", "<p>वाष्पमुख (Fumaroles)</p>"],
                    solution_en: "<p>90.(c) <strong>Biomass. Geothermal energy</strong> (Renewable) is heat energy from the earth&mdash;Geo (earth) + thermal (heat). Geothermal resources are reservoirs of hot water (Geysers, Hot springs, Steam vents, Hydrothermal vents and mud pots) that exist or are human made at varying temperatures and depths below the Earth\'s surface. Uses - heating and cooling buildings through geothermal heat pumps, generating electricity through geothermal power plants. Fumaroles are openings in the earth\'s surface that emit steam and volcanic gases, such as sulfur dioxide and carbon dioxide. A geyser is a spring characterized by an intermittent discharge of water ejected turbulently and accompanied by steam.</p>",
                    solution_hi: "<p>90.(c) <strong>बायोमास</strong> । भूतापीय ऊर्जा <strong>(नवीकरणीय),</strong> पृथ्वी से प्राप्त ऊष्मा ऊर्जा है - भू (पृथ्वी) + तापीय (ऊष्मा)। भूतापीय संसाधन, गर्म जल (गीज़र, गर्म झरने, भाप छिद्र , जलतापीय छिद्र और मिट्टी के बर्तन) के जलाशय हैं जो पृथ्वी की सतह के नीचे अलग-अलग ताप और गहराई पर मौजूद या मानव निर्मित हैं । उपयोग - भूतापीय ताप पंपों के माध्यम से इमारतों को गर्म करना और ठंडा करना, भूतापीय विद्युत संयंत्रों के माध्यम से बिजली उत्पन्न करना। फुमारोल्स (Fumaroles) पृथ्वी की सतह में खुले हैं जो भाप और ज्वालामुखीय गैसों जैसे सल्फर डाइऑक्साइड और कार्बन डाइऑक्साइड का उत्सर्जन करते हैं। एक गीज़र (Geyser) एक वसंत है जो पानी के आंतरायिक निर्वहन की विशेषता है जो अशांत रूप से निकलता है और भाप के साथ होता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "91",
                    section: "33",
                    question_en: "<p>91. Each of the six friends A, B, C, D, E, and F is reading one book among the books G, H, I, J, K and L in a reading program. A and B are not reading books G, J and L. F is reading book H. B, C and D are not reading the books I and L. Which book is being read by A?</p>",
                    question_hi: "<p>91. छह दोस्तों A, B, C, D, E और F में से प्रत्येक एक पढ़ने के कार्यक्रम में G, H, I, J, K और L किताबों में से एक किताब पढ़ रहा है। A और B किताबें G, J और L नहीं पढ़ रहे हैं। F किताब H पढ़ रहा है। B, C और D किताबें I और L नहीं पढ़ रहे हैं। A कौन सी किताब पढ़ रहा है?</p>",
                    options_en: ["<p>J</p>", "<p>I</p>", 
                                "<p>G</p>", "<p>K</p>"],
                    options_hi: ["<p>J</p>", "<p>I</p>",
                                "<p>G</p>", "<p>K</p>"],
                    solution_en: "<p>91.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173715994.png\" alt=\"rId33\" width=\"109\" height=\"150\"></p>",
                    solution_hi: "<p>91.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173715994.png\" alt=\"rId33\" width=\"109\" height=\"150\"></p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "92",
                    section: "33",
                    question_en: "<p>92. Nilesh bought some quantity of barley for <math display=\"inline\"><mi>&#8377;</mi></math>5,000. Half of it is sold at a profit of 20%. If Nilesh wishes to make a profit of 15% on the whole, find the selling price (in ₹) of the remaining barley.</p>",
                    question_hi: "<p>92. नीलेश ने जौ की कुछ मात्रा ₹5,000 में खरीदी। उसने इसके आधे भाग को 20% के लाभ पर बेचा। यदि नीलेश, पूरे सौदे में 15% लाभ प्राप्त करना चाहता हो, तो जौ की शेष मात्रा का विक्रय मूल्य (₹ में) ज्ञात कीजिए ।</p>",
                    options_en: ["<p>2,500</p>", "<p>3,250</p>", 
                                "<p>2,750</p>", "<p>3,000</p>"],
                    options_hi: ["<p>2,500</p>", "<p>3,250</p>",
                                "<p>2,750</p>", "<p>3,000</p>"],
                    solution_en: "<p>92.(c) Price of barley at 15% profit <br>= 5000 &times; <math display=\"inline\"><mfrac><mrow><mn>115</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 5750<br>Therefore total profit <br>= 5750 - 5000 = <math display=\"inline\"><mi>&#160;</mi><mi>&#8377;</mi></math> 750<br>ATQ,<br>He sold half of barley at 20% profit <br>Then profit = 2500 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 500<br>Remaining profit i.e 750 - 500 = <math display=\"inline\"><mi>&#8377;</mi></math> 250 will be at price of remaining half of barley <br>Therefore selling price <br>= 2500 +250 = <math display=\"inline\"><mi>&#160;</mi><mi>&#8377;</mi></math> 2750</p>",
                    solution_hi: "<p>92.(c) 15% लाभ पर जौ का मूल्य <br>= 5000 &times; <math display=\"inline\"><mfrac><mrow><mn>115</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 5750<br>इसलिए कुल लाभ = 5750 - 5000 = ₹ 750<br>प्रश्न के अनुसार,<br>उसने आधे जौ को 20% लाभ पर बेच दिया<br>तो लाभ = 2500 &times; <math display=\"inline\"><mfrac><mrow><mn>20</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 500<br>बचा हुआ मुनाफा यानी 750 - 500 = ₹ 250 बचे हुए आधे जौ के भाव पर होगा<br>इसलिए विक्रय मूल्य = 2500 + 250 = ₹ 2750</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "93",
                    section: "33",
                    question_en: "<p>93. In today\'s scenario, which change seen in human attitude has helped in decreasing the production of non-biodegradable waste to some extent?</p>",
                    question_hi: "<p>93. आज के परिदृश्य में, मनुष्यों की अभिवृत्ति में हुए किस परिवर्तन ने अजैवनिम्नीकरणीय अपशिष्ट के उत्पादन को कुछ हद तक कम करने में सहायता की है?</p>",
                    options_en: ["<p>Use of Plastic in Packaging</p>", "<p>Use and throw attitude</p>", 
                                "<p>Improvement in lifestyle</p>", "<p>Use of jute bags</p>"],
                    options_hi: ["<p>पैकेजिंग में प्लास्टिक का उपयोग</p>", "<p>प्रयोग करना और फेक देने का स्वभाव</p>",
                                "<p>जीवनशैली में सुधार</p>", "<p>जुट के थैलों का उपयोग</p>"],
                    solution_en: "<p>93.(d) <strong>Use of jute bags. Non Bio Biodegradable Waste</strong> - It is defined as a substance that cannot be decomposed or dissolved naturally and acts as a source of pollution. Ways to reduce non-biodegradable wastes: Recycling, Use of Jute bags, Reuse, etc.</p>",
                    solution_hi: "<p>93.(d) <strong>जूट की थैलियों का प्रयोग। गैर जैवनिम्नीकरणीय अपशिष्ट</strong> - इसे एक ऐसे पदार्थ के रूप में परिभाषित किया जाता है जिसे प्राकृतिक रूप से विघटित या भंग नहीं किया जा सकता है और यह प्रदूषण के स्रोत के रूप में कार्य करता है। गैर-जैवनिम्नीकरणीय अपशिष्ट कचरे को कम करने के तरीके : पुनर्चक्रण, जूट बैग का उपयोग, पुन: उपयोग, आदि।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "94",
                    section: "33",
                    question_en: "<p>94. Q, W, E, R, T and Y live on seven different floors of the same building with one floor being vacant. The lowermost floor in the building is numbered 1, the floor above it, number 2 and so on till the topmost floor is numbered 7. T lives on the 7th floor, and W lives on the 1st floor. Q lives between E and R. One floor immediately above E is vacant. Only Y lives between R and W. The 5th floor is occupied by E. On which floor does Q live?</p>",
                    question_hi: "<p>94. Q, W, E, R, T और Y एक ही इमारत की सात अलग-अलग मंजिलों पर रहते हैं, जिसमें एक मंजिल खाली है। इमारत में सबसे निचली मंजिल का क्रमांक 1 है, उसके ऊपर की मंजिल का क्रमांक 2 है, और इसी तरह सबसे ऊपरी मंजिल का क्रमांक 7 है। T, सातवीं मंजिल पर रहता है, और W पहली मंजिल पर रहता है। Q, E और R के बीच की मंजिल पर रहता है। E के ठीक ऊपर की एक मंजिल खाली है। R और W के बीच केवल Y रहता है। 5वीं मंजिल पर E रहता है। Q किस मंजिल पर रहता है?</p>",
                    options_en: ["<p>2nd</p>", "<p>3rd</p>", 
                                "<p>4th</p>", "<p>5th</p>"],
                    options_hi: ["<p>दूसरा</p>", "<p>तीसरा</p>",
                                "<p>चौथा</p>", "<p>पांचवां</p>"],
                    solution_en: "<p>94.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173716103.png\" alt=\"rId34\" width=\"124\" height=\"193\"><br>Therefore, Q lives on 4th floor</p>",
                    solution_hi: "<p>94.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1739173716215.png\" alt=\"rId35\" width=\"102\" height=\"174\"><br>इसलिए, Q चौथी मंजिल पर रहता है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "95",
                    section: "33",
                    question_en: "<p>95. A table and a swing were sold for ₹9,936 each. The table was sold for a profit of 8% and the swing was sold for a loss of 8%. What is the gain or loss percentage in the whole transaction?</p>",
                    question_hi: "<p>95. एक मेज और एक झूला, प्रत्येक को ₹9,936 में बेचा गया। मेज को 8% के लाभ पर और झूले को 8% की हानि पर बेचा गया। इस पूरे लेनदेन में लाभ या हानि प्रतिशत ज्ञात करें।</p>",
                    options_en: ["<p>2% profit</p>", "<p>0.64% loss</p>", 
                                "<p>0.64% profit</p>", "<p>No profit no loss</p>"],
                    options_hi: ["<p>2% लाभ</p>", "<p>0.64% हानि</p>",
                                "<p>0.64% लाभ</p>", "<p>न लाभ न हानि</p>"],
                    solution_en: "<p>95.(b)Since, selling price is same for both <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CP&nbsp; &nbsp; &nbsp; SP<br>Table <math display=\"inline\"><mo>&#8594;</mo></math> 25&nbsp; &nbsp;:&nbsp; &nbsp;27 .. &times; 23<br>Swing <math display=\"inline\"><mo>&#8594;</mo></math>25&nbsp; &nbsp;:&nbsp; &nbsp;23 .. &times; 27<br>After balancing the ratio for SP <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; CP&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;SP<br>Table <math display=\"inline\"><mo>&#8594;</mo></math> 575&nbsp; &nbsp;:&nbsp; &nbsp;621 <br>Swing <math display=\"inline\"><mo>&#8594;</mo></math><span style=\"text-decoration: underline;\">675&nbsp; &nbsp;:&nbsp; &nbsp;621</span><br>Total <math display=\"inline\"><mo>&#8594;</mo></math> 1250&nbsp; &nbsp;:&nbsp; 1242<br>Therefore, required % = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>1250</mn></mrow></mfrac></math> &times; 100<br>= 0.64% loss<br><strong>Alternate method :</strong><br>If two article are sold for same price and one of them sold at X% profit and another sold for x% loss<br>Than there are always (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><mn>100</mn></mfrac></math>)% loss will occur.<br>So, loss percentage = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><mn>100</mn></mfrac></math>)% = 0.64%</p>",
                    solution_hi: "<p>95.(b) <br>चूंकि, दोनों का विक्रय मूल्य समान है<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; क्रय मूल्य&nbsp; &nbsp; &nbsp; विक्रय मूल्य<br>मेज <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;27 .. &times; 23<br>झूला <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;25&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;23 .. &times; 27<br>विक्रय मूल्य के अनुपात को संतुलित करने के बाद <br>&nbsp; &nbsp; &nbsp; &nbsp;क्रय मूल्य&nbsp; &nbsp; विक्रय मूल्य<br>मेज <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; 575&nbsp; &nbsp;:&nbsp; &nbsp; 621 <br>झूला <math display=\"inline\"><mo>&#8594;</mo></math> <span style=\"text-decoration: underline;\">675&nbsp; &nbsp;:&nbsp; &nbsp; 621</span><br>कुल <math display=\"inline\"><mo>&#8594;</mo></math>1250 : 1242<br>इसलिए , आवश्यक % <br>= <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>1250</mn></mrow></mfrac></math> &times; 100 = 0.64%&nbsp; हानि <br><strong>वैकल्पिक विधि :</strong><br>यदि दो वस्तुओं को समान मूल्य पर बेचा जाता है और उनमें से एक को <math display=\"inline\"><mi>x</mi></math> % लाभ पर बेचा जाता है और दूसरी को x % हानि पर बेचा जाता है<br>वहां से हमेशा (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi>x</mi><mn>2</mn></msup><mn>100</mn></mfrac></math>)% नुकसान होगा।<br>इसलिए, हानि प्रतिशत = (<math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>10</mn><mn>0</mn></mrow></mfrac></math>)% = 0.64%</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "96",
                    section: "33",
                    question_en: "<p>96. Which among the following UN agencies is responsible for the safety and peaceful use of nuclear technology?</p>",
                    question_hi: "<p>96. निम्नलिखित संयुक्त राष्ट्र एजेंसियों में से कौन परमाणु प्रौद्योगिकी की सुरक्षा और शांतिपूर्ण उपयोग के लिए जिम्मेदार है?</p>",
                    options_en: ["<p>The UN Committee of Disarmament</p>", "<p>UN International Safeguard Committee</p>", 
                                "<p>International Atomic Energy Agency</p>", "<p>United Nations Security Council</p>"],
                    options_hi: ["<p>संयुक्त राष्ट्र निरस्त्रीकरण समिति</p>", "<p>संयुक्त राष्ट्र अंतर्राष्ट्रीय सुरक्षा समिति</p>",
                                "<p>अंतर्राष्ट्रीय परमाणु ऊर्जा एजेंसी</p>", "<p>संयुक्त राष्ट्र सुरक्षा परिषद</p>"],
                    solution_en: "<p>96.(c)<strong> International Atomic Energy Agency.</strong> It was established in 1957 as an autonomous international organisation within the United Nations system. Headquarter - Vienna, Austria. <strong>Other international organisations: </strong>United Nations Disarmament Commission (1952); United Nations Security Council (UNSC, 1945). Its permanent member - China, Russia, the United Kingdom, the United States of America, and France.</p>",
                    solution_hi: "<p>96.(c) <strong>अंतरराष्ट्रीय परमाणु ऊर्जा एजेंसी। </strong>इसकी स्थापना 1957 में संयुक्त राष्ट्र प्रणाली के अंतर्गत एक स्वायत्त अंतर्राष्ट्रीय संगठन के रूप में की गई थी। मुख्यालय - वियना, ऑस्ट्रिया। <strong>अन्य अंतर्राष्ट्रीय संगठन </strong>: संयुक्त राष्ट्र निरस्त्रीकरण आयोग (1952), संयुक्त राष्ट्र सुरक्षा परिषद (UNSC, 1945)। इसके स्थायी सदस्य - चीन, रूस, यूनाइटेड किंगडम, संयुक्त राज्य अमेरिका और फ्रांस हैं।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "97",
                    section: "33",
                    question_en: "<p>97. Read the given statements and conclusions carefully and decide which of the conclusions logically follow(s) from the statements.<br><strong>Statement :</strong> <br>C &lt; D , E <math display=\"inline\"><mo>&#8805;</mo></math> B , B &gt; D<br><strong>Conclusions :</strong> <br>1. B &gt; C <br>2. E &lt; D</p>",
                    question_hi: "<p>97. दिए गए कथनों और निष्कर्षों को ध्यान से पढ़िए और तय कीजिये कि कौन-सा निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है।<br><strong>कथन :</strong> <br>C &lt; D , E <math display=\"inline\"><mo>&#8805;</mo></math> B , B &gt; D<br><strong>निष्कर्ष :</strong> <br>1. B &gt; C <br>2. E &lt; D</p>",
                    options_en: ["<p>Neither conclusion 1 nor 2 is true</p>", "<p>Only conclusion 2 is true</p>", 
                                "<p>Only conclusion 1 is true</p>", "<p>Both conclusions 1 and 2 are true</p>"],
                    options_hi: ["<p>न तो निष्कर्ष 1 और न ही 2 सत्य है</p>", "<p>केवल निष्कर्ष 2 सत्य है</p>",
                                "<p>केवल निष्कर्ष 1 सत्य है</p>", "<p>दोनों निष्कर्ष 1 और 2 सत्य हैं</p>"],
                    solution_en: "<p>97.(c) We can arrange all as follows. <br>C &lt; D &lt; B <math display=\"inline\"><mo>&#8804;</mo></math> E<br>I. B &gt; C = true (as C &lt; D &lt; B),<br>II. E &lt; D = false (as D &lt; B <math display=\"inline\"><mo>&#8804;</mo></math> E), here D is less than E.<br>So, Only conclusion I is true.</p>",
                    solution_hi: "<p>97.(c) हम सभी को निम्नानुसार व्यवस्थित कर सकते हैं।<br>C &lt; D &lt; B<math display=\"inline\"><mi>&#160;</mi><mo>&#8804;</mo></math> E<br>I. B &gt; C = सत्य (क्योंकि C &lt; D &lt; B),<br>II. E &lt; D = असत्य (क्योंकि D &lt; B <math display=\"inline\"><mo>&#8804;</mo></math> E), यहाँ D, E से छोटा है। अतः, केवल निष्कर्ष I सत्य है।</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "98",
                    section: "33",
                    question_en: "<p>98. If the Simple Interest on a certain sum for 15 months at 7.5% per annum exceeds the Simple Interest on the same sum for 8 months at 12.5% per annum by Rs.32.50, then find the sum.</p>",
                    question_hi: "<p>98. यदि किसी धनराशि पर 7.5% प्रति वार्षिक दर पर 15 महीने का साधारण ब्याज, उतनी ही राशि पर 12.5% वार्षिक दर पर 8 महीने के साधारण ब्याज से रु.32.50 अधिक है, वह धनराशि ज्ञात कीजिए ।</p>",
                    options_en: ["<p>Rs.3000</p>", "<p>Rs.3060</p>", 
                                "<p>Rs.3120</p>", "<p>Rs.2900</p>"],
                    options_hi: ["<p>रु.3000</p>", "<p>रु.3060</p>",
                                "<p>रु.3120</p>", "<p>रु.2900</p>"],
                    solution_en: "<p>98.(c) Let the principal = P<br>According to question , <br>S.I. for 15 months <math display=\"inline\"><mo>-</mo></math> S.I.for 8 months<br>= Rs.32.50<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>7</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>12</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = 32.50<br>112.5P <math display=\"inline\"><mo>-</mo></math> 100P = 39000 Rs.<br>P <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39000</mn><mrow><mn>12</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math> = 3120 Rs.</p>",
                    solution_hi: "<p>98.(c) माना मूलधन = P<br>प्रश्न के अनुसार,<br>15 महीने के लिए साधारण ब्याज<math display=\"inline\"><mo>-</mo></math> 8 महीने के लिए साधारण ब्याज = रु. 32.50<br><math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>&#160;</mi><mo>&#215;</mo><mn>7</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mn>15</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>P</mi><mo>&#215;</mo><mn>8</mn><mo>&#215;</mo><mn>12</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>12</mn></mrow></mfrac></math> = 32.50<br>112.5P <math display=\"inline\"><mo>-</mo></math> 100P = 39000 Rs.<br>P <math display=\"inline\"><mo>&#8594;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>39000</mn><mrow><mn>12</mn><mo>.</mo><mn>5</mn></mrow></mfrac></math>= रु.3120</p>",
                    correct: "c",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "99",
                    section: "33",
                    question_en: "<p>99. Read the following information carefully and answer the given questions given below<br>5 8 2 7 4 5 9 1 6 3 7 5 6 2 3 9 8 6 1 4 8 1 3 6<br>How many such even digits are there that are immediately preceded by a perfect square and immediately followed by an even digit? (Note: 1 is also a perfect square)</p>",
                    question_hi: "<p>99. निम्नलिखित जानकारी को ध्यानपूर्वक पढिए और नीचे दिए गए प्रश्नों के उत्तर दीजिए: <br>5 8 2 7 4 5 9 1 6 3 7 5 6 2 3 9 8 6 1 4 8 1 3 6. <br>ऐसी कितने सम अंक हैं, जिनमें से प्रत्येक के ठीक पहले एक पूर्ण वर्ग है और ठीक बाद एक सम अंक है? (नोट: 1 भी एक पूर्ण वर्ग है)</p>",
                    options_en: ["<p>Four</p>", "<p>one</p>", 
                                "<p>Two</p>", "<p>Three</p>"],
                    options_hi: ["<p>चार</p>", "<p>एक</p>",
                                "<p>दो</p>", "<p>तीन</p>"],
                    solution_en: "<p>99.(b) Given series: 5 8 2 7 4 5 9 1 6&nbsp;3 7 5 6 2 3 <strong>9 8 6</strong> 1 4 8 1 3 6<br>As per the question, <br>The required digits are: <strong>9 8 6</strong> <br>The number of such even digits = 1</p>",
                    solution_hi: "<p>99.(b) दी गई श्रृंखला: 5 8 2 7 4 5 9 1 6 3 7 5 6 2 3<strong> 9 8 6</strong> 1 4 8 1 3 6<br>प्रश्न के अनुसार,<br>अभीष्ट अंक हैं:<strong> 9 8 6</strong><br>ऐसे सम अंकों की संख्या = 1</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. If the speeds of a train in 10 successive hours are <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>2</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>3</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>4</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>5</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>6</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>7</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>8</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>9</mn></msub></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>10</mn></msub></math> then the average speed of the train is:</p>",
                    question_hi: "<p>100. यदि 10 क्रमागत घंटों में एक रेलगाड़ी की गति <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>2</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>3</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>4</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>5</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>6</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>7</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>8</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>9</mn></msub></math>&nbsp;और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>10</mn></msub></math> हैं, तो रेलगाड़ी की औसत गति है</p>",
                    options_en: ["<p>geometric mean of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>2</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>3</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>4</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>5</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>6</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>7</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>8</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>9</mn></msub></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>10</mn></msub></math></p>", "<p>harmonic mean of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>2</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>3</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>4</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>5</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>6</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>7</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>8</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>9</mn></msub></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>10</mn></msub></math></p>", 
                                "<p>median of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>2</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>3</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>4</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>5</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>6</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>7</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>8</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>9</mn></msub></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>10</mn></msub></math></p>", "<p>arithmetic mean of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>2</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>3</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>4</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>5</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>6</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>7</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>8</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>9</mn></msub></math> and <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>10</mn></msub></math></p>"],
                    options_hi: ["<p>गुणोत्तर माध्य <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>2</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>3</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>4</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>5</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>6</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>7</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>8</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>9</mn></msub></math>&nbsp;और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>10</mn></msub></math></p>", "<p>हरात्मक माध्य<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>2</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>3</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>4</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>5</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>6</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>7</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>8</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>9</mn></msub></math>&nbsp;और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>10</mn></msub></math></p>",
                                "<p>माध्यिका<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>2</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>3</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>4</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>5</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>6</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>7</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>8</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>9</mn></msub></math>&nbsp;और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>10</mn></msub></math></p>", "<p>समान्तर माध्य<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>1</mn></msub></math>,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>2</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>3</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>4</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>5</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>6</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>7</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>a</mi><mn>8</mn></msub></math>, <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>9</mn></msub></math>&nbsp;और <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#160;</mo><msub><mi>a</mi><mn>10</mn></msub></math></p>"],
                    solution_en: "<p>100.(d) The average speed of the train is the arithmetic mean of all speeds.</p>",
                    solution_hi: "<p>100.(d) ट्रेन की औसत गति सभी गतियों का अंकगणितीय माध्य है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>