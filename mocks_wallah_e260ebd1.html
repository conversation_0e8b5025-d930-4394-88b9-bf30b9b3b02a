<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. Which of the following is a part of apical meristem found in roots.</p>",
                    question_hi: "<p>1. निम्नलिखित में से कौन जड़ों में पाए जाने वाले शिखर विभज्यो तक (apical meristem) का एक भाग है।</p>",
                    options_en: ["<p>Differentiating vascular tissue</p>", "<p>Axillary bud</p>", 
                                "<p>Protoderm</p>", "<p>Leaf primordium</p>"],
                    options_hi: ["<p>विभेदक संवहनी ऊतक (Differentiating vascular tissue)</p>", "<p>अक्षीय कली (Axillary bud)</p>",
                                "<p>अधित्वक (Protoderm)</p>", "<p>लीफ प्रिमोर्डियम (Leaf primordium)</p>"],
                    solution_en: "<p>1.(c) <strong>Protoderm</strong>. Apical meristem is the region of plant tissue where growth occurs, and it\'s found in both roots and shoots. In roots, the apical meristem consists of three primary meristematic tissues: Protoderm (outermost layer) - Gives rise to the epidermis. Ground meristem (middle layer) - Gives rise to the cortex and endodermis. Procambium (innermost layer) - Gives rise to the vascular tissue.</p>",
                    solution_hi: "<p>1.(c) <strong>अधित्वक ​​(प्रोटोडर्म)</strong>। शीर्षस्थ विभज्योतक (एपिकल मेरिस्टेम) पौधे के ऊतकों का वह क्षेत्र है जहाँ वृद्धि होती है, और यह जड़ों और टहनियों दोनों में पाया जाता है। जड़ों में, शीर्षस्थ विभज्योतक तीन प्राथमिक विभज्योतक ऊतकों से बना होता है: अधित्वक (सबसे बाहरी परत) - उपत्वक को जन्म देता है। भू-विभज्योतक (मध्य परत) - कॉर्टेक्स और एंडोडर्मिस को जन्म देता है। प्रोकैम्बियम (सबसे भीतरी परत) - संवहनी ऊतक को जन्म देता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. In a symport:</p>",
                    question_hi: "<p>2. एक सिमपोर्ट (symport) में:</p>",
                    options_en: ["<p>both molecules cross the membrane in the same direction</p>", "<p>a molecule moves across a membrane independent of other molecules</p>", 
                                "<p>both molecules cross the membrane in the opposite direction</p>", "<p>a molecule moves across a membrane dependent of other molecules</p>"],
                    options_hi: ["<p>दोनों अणु एक ही दिशा में झिल्ली को पार करते हैं</p>", "<p>एक अणु अन्य अणुओं से स्वतंत्र एक झिल्ली को पार करता है</p>",
                                "<p>दोनों अणु झिल्ली को विपरीत दिशा में पार करते हैं</p>", "<p>एक अणु अन्य अणुओं के अनुसार झिल्ली को पार करता है</p>"],
                    solution_en: "<p>2.(a) Symport: Two or more molecules move together in the same direction across the membrane using the same transport protein. Antiport: Two or more molecules move in opposite directions across the membrane. Uniport: A single molecule moves across the membrane independently of other molecules.</p>",
                    solution_hi: "<p>2.(a) सिमपोर्ट: दो या दो से अधिक अणु एक ही परिवहन प्रोटीन का उपयोग करके झिल्ली के पार एक ही दिशा में एक साथ चलते हैं। एंटीपोर्ट: दो या दो से अधिक अणु झिल्ली के पार विपरीत दिशाओं में चलते हैं। यूनिपोर्ट: एकल अणु ,अन्य अणुओं से स्वतंत्र रूप से झिल्ली के पार चलता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. The phenomenon of the interaction between nonallelic genes at two or more loci resulting in one gene masking the phenotypic expression of another gene, is known as:</p>",
                    question_hi: "<p>3. दो या दो से अधिक लोकी (loci) पर गैर-युग्मक (nonallelic) जीनों के बीच अंतःक्रिया की घटना जिसके परिणामस्वरूप एक जीन दूसरे जीन की फेनोटाइपिक अभिव्यक्ति (phenotypic expression) को छुपाता (masking) है, ______ कहलाता है?</p>",
                    options_en: ["<p>incomplete inheritance</p>", "<p>linkage</p>", 
                                "<p>epistasis</p>", "<p>complete inheritance</p>"],
                    options_hi: ["<p>अपूर्ण वंशागति (incomplete inheritance)</p>", "<p>लिंकेज (linkage)</p>",
                                "<p>एपिस्टासिस (epistasis)</p>", "<p>पूर्ण वंशागति (complete inheritance)</p>"],
                    solution_en: "<p>3.(c) <strong>Epistasis</strong>. Inheritance is the process by which genetic information is passed from parents to their offspring. Incomplete inheritance, also known as incomplete dominance, is a pattern of inheritance where neither allele of a gene is completely dominant over the other. Linkage is a physical association of two genes.</p>",
                    solution_hi: "<p>3.(c) <strong>एपिस्टासिस </strong>(epistasis)। वंशानुक्रम वह प्रक्रिया है जिसके द्वारा आनुवंशिक जानकारी माता-पिता से उनकी संतानों तक पहुँचती है। अपूर्ण वंशानुक्रम, जिसे अपूर्ण प्रभुत्व के रूप में भी जाना जाता है, वंशानुक्रम का एक पैटर्न है जहाँ किसी जीन का कोई भी एलील दूसरे पर पूरी तरह से प्रभावी नहीं होता है। लिंकेज दो जीनों का एक भौतिक जुड़ाव है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the Mendel\'s laws states that a diploid organism passes a randomly selected allele for a trait to its offspring, such that the offspring inherit one allele from each parent?</p>",
                    question_hi: "<p>4. मेंडल के किस नियम के अनुसार एक द्विगुणित जीव, अपनी संतति के लिए एक गुण के लिए एक यादृच्छिक रूप से चयनित युग्म विकल्पी (allele) स्थानांतरित करता है, जिससे कि संतति प्रत्येक माता- पिता से एक युग्म विकल्पी को प्राप्त कर सके ?</p>",
                    options_en: ["<p>Law of independent assortment</p>", "<p>Law of segregation</p>", 
                                "<p>Law of dominance</p>", "<p>Law of paired factor</p>"],
                    options_hi: ["<p>स्वतंत्र चयन का नियम (Law of independent assortment)</p>", "<p>पृथक्करण का नियम (Law of segregation)</p>",
                                "<p>प्रभुत्व का नियम (Law of dominance)</p>", "<p>युग्मित कारक का नियम (Law of paired factor)</p>"],
                    solution_en: "<p>4.(b) <strong>Law of segregation.</strong> Mendel&rsquo;s Law of Dominance, also known as the first law of inheritance, states that when parents with pure, contrasting traits are crossed, the trait appearing in the next generation is the dominant one, and the hybrid offspring will display only the dominant trait in their phenotype. Law of Independent Assortment : It states that when two pairs of traits are combined in a hybrid, the segregation of one pair of characters occurs independently of the other pair. This law is based on observations from dihybrid crosses.</p>",
                    solution_hi: "<p>4.(b)<strong> पृथक्करण का नियम</strong>। मेंडल के प्रभुत्व का नियम, जिसे वंशानुक्रम का पहला नियम भी कहा जाता है, कहता है कि जब शुद्ध, विपरीत लक्षणों वाले माता-पिता का संकरण होता है, तो अगली पीढ़ी में दिखाई देने वाला लक्षण प्रमुख होता है, और हाइब्रिड संतान अपने फेनोटाइप में केवल प्रमुख लक्षण प्रदर्शित करेगी। स्वतंत्र अपव्यूहन का नियम: यह बताता है कि जब दो जोड़े लक्षणों को एक हाइब्रिड में मिलाया जाता है, तो लक्षणों के एक जोड़े का पृथक्करण दूसरे जोड़े से स्वतंत्र रूप से होता है। यह नियम डिहाइब्रिड संकरण से प्राप्त अवलोकनों पर आधारित है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Which of the following causes enlargement and extension growth of cells?</p>",
                    question_hi: "<p>5. निम्नलिखित में से कौन-सा कोशिकाओं के विस्तार और फैलाव का कारण बनता है?</p>",
                    options_en: ["<p>Pressure potential</p>", "<p>Osmotic pressure</p>", 
                                "<p>Imbibition</p>", "<p>Turgor pressure</p>"],
                    options_hi: ["<p>दाब विभव (Pressure potential)</p>", "<p>परासरण दाब (Osmotic pressure)</p>",
                                "<p>अंतःशोषण (Imbibition)</p>", "<p>स्फीति दाब (Turgor pressure)</p>"],
                    solution_en: "<p>5.(d) <strong>Turgor pressure</strong>. Pressure potential is controlled by solute potential (when solute potential decreases, pressure potential increases) and the opening and closing of stomata. Osmotic pressure is the minimum pressure which needs to be applied to a solution to prevent the inward flow of its pure solvent across a semipermeable membrane. Imbibition is a special type of diffusion that takes place when liquid is absorbed by solids-colloids causing an increase in volume.</p>",
                    solution_hi: "<p>5.(d) <strong>स्फीति दाब</strong>। दाब विभव को विलेय विभव (जब विलेय विभव घटता है, दाब विभव बढ़ता है) और रंध्रों के खुलने और बंद होने से नियंत्रित किया जाता है। परासरण दाब वह न्यूनतम दाब है जिसे एक अर्धपारगम्य झिल्ली में इसके शुद्ध विलायक के अंतर्मुखी प्रवाह को रोकने के लिए, एक विलयन पर लागू करने की आवश्यकता होती है। अंतःशोषण एक विशेष प्रकार का विसरण है जो तब होता है जब द्रव को ठोस-कोलाइड द्वारा अवशोषित किया जाता है जिससे आयतन में वृद्धि होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. Which of the following is a medium-sized globular protein that acts as a pancreatic serine protease found in the digestive system of many vertebrates?</p>",
                    question_hi: "<p>6. निम्नलिखित में से कौन-सा एक मध्यम आकार का ग्लोबुलर प्रोटीन (globular protein) है जो कई कशेरुकी प्राणियों के पाचन तंत्र में पाए जाने वाले पैंक्रियाटिक सेरीन प्रोटीएज़ (pancreatic serine protease) के रूप में काम करता है?</p>",
                    options_en: ["<p>Trypsin</p>", "<p>Amylase</p>", 
                                "<p>Lipase</p>", "<p>Pepsin</p>"],
                    options_hi: ["<p>ट्रिप्सिन</p>", "<p>एमाइलेज़</p>",
                                "<p>लाइपेज़</p>", "<p>पेप्सिन</p>"],
                    solution_en: "<p>6.(a) <strong>Trypsin</strong>. Globular proteins are a common type of protein that are spherical or globe-shaped and are somewhat soluble in water. They are also known as spheroproteins. Amylase: An enzyme found in saliva that breaks down starches into maltose. Lipase: An enzyme that breaks down fats, secreted by the pancreas and, in smaller amounts, by gastric glands. Pepsin: An enzyme in the stomach that converts proteins into proteoses and peptones.</p>",
                    solution_hi: "<p>6.(a) <strong>ट्रिप्सिन</strong>। ग्लोबुलर प्रोटीन एक सामान्य प्रकार का प्रोटीन है जो गोलाकार या ग्लोब के आकार का होता है और जल में कुछ हद तक घुलनशील होता है। इन्हें स्फेरोप्रोटीन के नाम से भी जाना जाता है। एमाइलेज: लार में पाया जाने वाला एक एंजाइम जो स्टार्च को माल्टोज में तोड़ता है। लाइपेज: एक एंजाइम जो वसा को तोड़ता है, जिसे अग्न्याशय और कुछ मात्रा में गैस्ट्रिक ग्रंथियों द्वारा स्रावित किया जाता है। पेप्सिन: आमाशय में एक एंजाइम जो प्रोटीन को प्रोटिओसिस और पेप्टोन में परिवर्तित करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. In honey bees, _____</p>",
                    question_hi: "<p>7. मधुमक्खियों में, _____।</p>",
                    options_en: ["<p>males are haploid and females are diploid.</p>", "<p>males are tetraploid and females are triploid.</p>", 
                                "<p>males are diploid and females are haploid.</p>", "<p>males are triploid and females are tetraploid.</p>"],
                    options_hi: ["<p>नर अगुणित होते हैं और मादा द्विगुणित होती हैं</p>", "<p>नर चतुर्गुणित होते हैं और मादा त्रिगुणित होती हैं</p>",
                                "<p>नर द्विगुणित होते हैं और मादा अगुणित होती हैं</p>", "<p>नर त्रिगुणित होते हैं और मादा चतुर्गुणित होती हैं</p>"],
                    solution_en: "<p>7.(a)<strong> males are haploid and females are diploid. </strong>The females are diploid having 32 chromosomes and males are haploid, i.e., having 16 chromosomes. This is called a haplodiploid sex determination system and has special characteristic features such as the males produce sperms by mitosis, they do not have father and thus cannot have sons, but have a grandfather and can have grandsons.</p>",
                    solution_hi: "<p>7.(a) <strong>नर अगुणित होते हैं और मादा द्विगुणित होती हैं। </strong>मादाएं द्विगुणित होती हैं जिनमें 32 गुणसूत्र होते हैं और नर अगुणित होते हैं, यानी 16 गुणसूत्र होते हैं। इसे हेप्लोडिप्लोइड लिंग निर्धारण प्रणाली कहा जाता है और इसमें विशेष विशेषताएं होती हैं जैसे कि नर माइटोसिस द्वारा शुक्राणु उत्पन्न करते हैं, उनके पिता नहीं होते और इसलिए उनके बेटे नहीं हो सकते, लेकिन उनके दादा होते हैं और उनके पोते हो सकते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which of the following is NOT a mutation-based disease?</p>",
                    question_hi: "<p>8. निम्नलिखित में से कौन-सा उत्परिवर्तन-आधारित (mutation-based) रोग नहीं है?</p>",
                    options_en: ["<p>Malaria</p>", "<p>Down syndrome</p>", 
                                "<p>Sickle cell anaemia</p>", "<p>Phenylketonuria</p>"],
                    options_hi: ["<p>मलेरिया (Malaria)</p>", "<p>डाउन सिंड्रोम (Down syndrome)</p>",
                                "<p>सिकल सेल एनीमिया (Sickle cell anaemia)</p>", "<p>फेनाइलेक्टोनुरिया (Phenylketonuria)</p>"],
                    solution_en: "<p>8.(a) <strong>Malaria </strong>is an infectious disease caused by a parasite. Down syndrome: It is caused by a chromosomal mutation, specifically trisomy 21, where an individual has an extra copy of chromosome 21. Sickle cell anemia: It is caused by the hemoglobin-&beta; gene. Phenylketonuria (PKU): PKU is caused by a mutation in the PAH gene, which affects the enzyme phenylalanine hydroxylase.</p>",
                    solution_hi: "<p>8.(a) <strong>मलेरिया </strong>एक संक्रामक रोग है जो परजीवी के कारण होता है। डाउन सिंड्रोम: यह क्रोमोसोमल उत्परिवर्तन के कारण होता है, विशेष रूप से ट्राइसॉमी 21, जहाँ किसी व्यक्ति के पास क्रोमोसोम 21 की एक अतिरिक्त प्रतिलिपि होती है। सिकल सेल एनीमिया: यह हीमोग्लोबिन-&beta; जीन के कारण होता है। फेनाइलेक्टोनुरिया (PKU): यह PAH जीन में उत्परिवर्तन के कारण होता है, जो फेनिलएलनिन हाइड्रॉक्सिलेज एंजाइम को प्रभावित करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which of the following is a food-borne disease caused by the consumption of contaminated food or beverages?</p>",
                    question_hi: "9. निम्नलिखित में से कौन-सी दूषित भोजन या पेय पदार्थों के सेवन से होने वाली खाद्य जनित बीमारी है?",
                    options_en: [" Tuberculosis ", " Malaria ", 
                                " Cholera ", " Chicken pox"],
                    options_hi: [" तपेदिक ", " मलेरिया ",
                                " हैज़ा", " चिकन पॉक्स"],
                    solution_en: "<p>9.(c) <strong>Cholera</strong>. It is an acute diarrhoeal infection caused by the bacterium Vibrio cholerae. Malaria is spread to humans through the bite of an infected female Anopheles mosquito. Tuberculosis (TB) is caused by the bacteria Mycobacterium tuberculosis, which spreads through the air from person to person. Chickenpox is caused by the varicella-zoster virus (VZV), which is a member of the herpesvirus family.</p>",
                    solution_hi: "<p>9.(c) <strong>हैज़ा</strong>। यह जीवाणु विब्रियो कोलेरा के कारण होने वाला एक तीव्र दस्त संक्रमण है। मलेरिया संक्रमित मादा एनोफिलीज मच्छर के काटने से मनुष्यों में फैलता है। तपेदिक (TB) माइकोबैक्टीरियम ट्यूबरकुलोसिस नामक जीवाणु के कारण होता है, जो हवा के माध्यम से एक व्यक्ति से दूसरे व्यक्ति में फैलता है। चिकनपॉक्स वैरिसेला-ज़ोस्टर वायरस (VZV) के कारण होता है, जो हर्पीसवायरस समूह का सदस्य है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. The cell wall of bacteria is made up of:</p>",
                    question_hi: "<p>10. जीवाणुओं की कोशिका भित्ति किससे बनी होती है?</p>",
                    options_en: ["<p>peptidoglycan</p>", "<p>glycogen</p>", 
                                "<p>cellulose</p>", "<p>peptone</p>"],
                    options_hi: ["<p>पेप्टिडोग्लाइकन (peptidoglycan)</p>", "<p>ग्लाइकोजन (glycogen)</p>",
                                "<p>सेल्यूलोज (cellulose)</p>", "<p>पेप्टॉन (peptone)</p>"],
                    solution_en: "<p>10.(a) <strong>Peptidoglycan </strong>is a complex molecule made of sugars and amino acids. It provides structural support and shape to bacterial cells and protects them from environmental stress. Glycogen is a storage polysaccharide found in animals, not related to cell walls. Cellulose is a polysaccharide that makes up the cell walls of plants, not bacteria. Peptones are products of protein digestion used as nutrient sources in microbial culture media, not structural components of cell walls.</p>",
                    solution_hi: "<p>10.(a) <strong>पेप्टिडोग्लाइकन </strong>शर्करा और अमीनो अम्ल से बना एक जटिल अणु है। यह जीवाणु कोशिकाओं को संरचनात्मक सहायता और आकार प्रदान करता है और उन्हें पर्यावरणीय तनाव से बचाता है। ग्लाइकोजन जानवरों में पाया जाने वाला एक संचयन पॉलीसैकेराइड है, जो कोशिका भित्ति से संबंधित नहीं है। सेल्यूलोज़ एक पॉलीसैकेराइड है जो बैक्टीरिया की नहीं बल्कि पौधों की कोशिका भित्ति में पाया जाता है। पेप्टोन्स, प्रोटीन पाचन के उत्पाद हैं, जिनका उपयोग कोशिका भित्ति के संरचनात्मक घटक के रूप में नहीं बल्कि सूक्ष्मजीव संवर्धन माध्यम में पोषक स्रोत के रूप में किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Identify a carbohydrate but does not fit into the definition.</p>",
                    question_hi: "<p>11. निम्नलिखित में से उस कार्बोहाइड्रेट की पहचान कीजिए जो कार्बोहाइड्रेट की परिभाषा के अनुरूप नहीं&nbsp;है।</p>",
                    options_en: ["<p>Maltose</p>", "<p>Sucrose</p>", 
                                "<p>Fructose</p>", "<p>Rhamnose</p>"],
                    options_hi: ["<p>माल्टोज़ (Maltose)</p>", "<p>सुक्रोज़ (Sucrose)</p>",
                                "<p>फ्रक्टोज़ (Fructose)</p>", "<p>रम्नोज़ (Rhamnose)</p>"],
                    solution_en: "<p>11.(d) <strong>Rhamnose (C<sub>6</sub>H<sub>12</sub>O5).</strong> Carbohydrates are primarily produced by plants and form a very large group of naturally occurring organic compounds. Some common examples of carbohydrates are cane sugar, glucose, starch, etc. Most of them have a general formula, C<sub>x</sub>(H<sub>2</sub>O)<sub>y</sub>.</p>",
                    solution_hi: "<p>11.(d) <strong>रम्नोज़ (Rhamnose) (C<sub>6</sub>H<sub>12</sub>O<sub>5</sub>)</strong>। कार्बोहाइड्रेट मुख्य रूप से पौधों द्वारा उत्पादित होते हैं और प्राकृतिक रूप से पाए जाने वाले कार्बनिक यौगिकों का एक बहुत बड़ा समूह बनाते हैं। कार्बोहाइड्रेट के कुछ सामान्य उदाहरण गन्ना चीनी, ग्लूकोज, स्टार्च आदि हैं। उनमें से अधिकांश का सामान्य सूत्र C<sub>x</sub>(H<sub>2</sub>O)<sub>y</sub> है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which of the following muscles regulates the exit of food from the stomach into the small intestine?</p>",
                    question_hi: "<p>12. निम्नलिखित में से कौन-सी पेशी भोजन के आमाशय से छोटी आंत में निर्गम (exit) को नियंत्रित करती है?</p>",
                    options_en: ["<p>Gastrocnemius</p>", "<p>Rectus</p>", 
                                "<p>Pectoralis</p>", "<p>Sphincter</p>"],
                    options_hi: ["<p>गैस्ट्रॉनिमियस (Gastrocnemius)</p>", "<p>रेक्टस (Rectus)</p>",
                                "<p>वक्षपेशी (Pectoralis)</p>", "<p>संवरणी (Sphincter)</p>"],
                    solution_en: "<p>12.(d) <strong>Sphincter</strong>. From the stomach, the food enters the small intestine through the pyloric sphincter, which regulates the exit of food from the stomach. The small intestine is the site of complete digestion of carbohydrates, proteins, and fats. Digested food is absorbed by the walls of the small intestine. The unabsorbed food is then passed into the large intestine, where its walls absorb more water from this material. The remaining waste material is removed from the body via the anus, and its exit is regulated by the anal sphincter.</p>",
                    solution_hi: "<p>12.(d) <strong>संवरणी</strong>। पेट से भोजन छोटी आंत में प्रवेश करता है, जहाँ पाइलोरिक संवरणी भोजन के निकास को नियंत्रित करता है। छोटी आंत कार्बोहाइड्रेट, प्रोटीन और वसा के पूर्ण पाचन का स्थान है। पचा हुआ भोजन छोटी आंत की दीवारों द्वारा अवशोषित किया जाता है। जो भोजन अवशोषित नहीं होता, वह बड़ी आंत में भेजा जाता है, जहाँ उसकी दीवारें उस सामग्री से अधिक पानी अवशोषित करती हैं। शेष अपशिष्ट पदार्थ शरीर से मलद्वार के माध्यम से बाहर निकाला जाता है, और इसके निकास को मलाशय संवरणी द्वारा नियंत्रित किया जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which non-essential amino acid is synthesised by the hydroxylation of phenylalanine in a reaction catalysed by phenylalanine hydroxylase?</p>",
                    question_hi: "<p>13. फेनिलएलनिन हाइड्रॉक्सिलेज़ (phenylalanine hydroxylase) द्वारा उत्प्रेरित अभिक्रिया में, फेनिलएलनिन के हाइड्रॉक्सिकरण द्वारा कौन-सा गैर-आवश्यक एमीनो अम्ल (amino acid) संश्लेषित होता है?</p>",
                    options_en: ["<p>Cysteine</p>", "<p>Glycine</p>", 
                                "<p>Tyrosine</p>", "<p>Glutamine</p>"],
                    options_hi: ["<p>सिस्टीन (Cysteine)</p>", "<p>ग्लाइसिन (Glycine)</p>",
                                "<p>टायरोसिन (Tyrosine)</p>", "<p>ग्लूटामिन (Glutamine)</p>"],
                    solution_en: "<p>13.(c) <strong>Tyrosine</strong>. Amino acids are the building blocks of proteins. When proteins are broken down, amino acids are released. It facilitates various bodily functions such as: breaking down food, repairing tissues, and supporting overall growth and maintenance. Essential amino acids are: histidine, isoleucine, leucine, lysine, methionine, phenylalanine, threonine, tryptophan, and valine. Non-essential amino acids are: alanine, arginine, asparagine, aspartic acid, cysteine, glutamic acid, glutamine, glycine, proline, serine, and tyrosine.</p>",
                    solution_hi: "<p>13.(c) <strong>टायरोसिन (Tyrosine)</strong>। अमीनो एसिड प्रोटीन के निर्माण खंड के रूप में कार्य करते हैं। जब प्रोटीन टूटते हैं, तो अमीनो एसिड निकलते हैं। यह विभिन्न शारीरिक कार्यों को सुगम बनाता है जैसे: भोजन को तोड़ना, ऊतकों को स्वस्थ रखना और समग्र विकास तथा रखरखाव का समर्थन करना। आवश्यक अमीनो एसिड हैं: हिस्टिडीन, आइसोल्यूसीन, ल्यूसीन, लाइसिन, मेथियोनीन, फेनिलएलनिन, थ्रेओनीन, ट्रिप्टोफैन और वेलिन। अनावश्यक अमीनो एसिड हैं: एलानिन, आर्जिनिन, एस्परैगिन, एस्पार्टिक एसिड, सिस्टीन, ग्लूटामिक एसिड, ग्लूटामाइन, ग्लाइसिन, प्रोलाइन, सेरीन और टायरोसिन।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. In which of the following plants do roots arise from parts of a plant other than the radicle ?</p>",
                    question_hi: "<p>14. निम्नलिखित में से किस पादप में जड़ें, मूलक (radicle) के अलावा पौधे के अन्य भागों से निकलती हैं?</p>",
                    options_en: ["<p>Maize</p>", "<p>Mustard plant</p>", 
                                "<p>Monstera</p>", "<p>Turnip</p>"],
                    options_hi: ["<p>मक्का</p>", "<p>सरसों का पौधा</p>",
                                "<p>मॉन्स्टेरा</p>", "<p>शलजम</p>"],
                    solution_en: "<p>14.(c) <strong>Monstera</strong>. Three types of roots are found in Monstera deliciosa: aerial roots, aerial roots that have entered the soil (aerial-subterranean roots), and lateral roots that form on aerial-subterranean roots (lateral-subterranean roots). The three root types differed in anatomical development and in growth. It is an example of adventitious roots, which grow from parts of the plant other than the radicle. These roots can be aerial or underground and may develop from nodes (money plant, bamboo), stem cuttings (rose), branches (banyan), or the stem base (fibrous roots in monocots).</p>",
                    solution_hi: "<p>14.(c) <strong>मॉन्स्टेरा</strong>। मॉन्स्टेरा डेलिसियोसा में तीन प्रकार की जड़ें पायी जाती हैं: वायवीय जड़ें, वायवीय जड़ें जो मृदा में प्रवेश कर चुकी हैं (वायवीय -भूमिगत जड़ें), और पार्श्व जड़ें जो वायवीय -भूमिगत जड़ों पर बनती हैं (पार्श्व-भूमिगत जड़ें)। तीनों जड़ प्रकारो की संरचनात्मक विकास और वृद्धि में भिन्न होती है। यह अपस्थानिक जड़ों का एक उदाहरण है, जो मूलांकुर के अलावा पौधे के अन्य भागों से विकसित होती हैं। ये जड़ें वायवीय या भूमिगत हो सकती हैं और नोड्स (मनी प्लांट, बांस), स्टेम कटिंग (गुलाब), शाखाओं (बरगद), या स्टेम बेस (मोनोकॉट्स में रेशेदार जड़ें) से विकसित हो सकती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Which of the following statements most accurately describes the Cyclostomata&nbsp;group?</p>",
                    question_hi: "<p>15. निम्नलिखित में से कौन-सा कथन साइक्लोस्टोमेटा समूह (Cyclostomata group) का सबसे सटीक वर्णन करता है?</p>",
                    options_en: ["<p>Their skin is covered with scales/plates and their hearts have only two chambers.</p>", "<p>They are ectothermic animals having mucus glands in the skin, and a three-chambered heart.</p>", 
                                "<p>They are warm-blooded, oviparous, bipedal, feathered, winged and toothless vertebrates.</p>", "<p>They are characterised by having an elongated eel-like body, circular mouth, slimy skin and are scaleless.</p>"],
                    options_hi: ["<p>उनकी त्वचा शल्क /प्लेटों से ढकी होती है और उनके हृदयों में केवल दो कक्ष होते हैं।</p>", "<p>वे बाह्ययोष्मी (ऍक्टोथर्म) जन्तु होते हैं जिनकी त्वचा में श्लेष्म ग्रंथियां होती हैं, और एक तीन-कक्षीय हृदय होता है।</p>",
                                "<p>वे गर्म रक्त वाले, अंडाकार, द्विपाद, पंख वाले, परों वाले और दांत रहित कशेरुक होते हैं।</p>", "<p>वे एक लम्बे ईल जैसे शरीर, गोलाकार मुंह, पतली त्वचा वाले होते हैं और उनमें शल्क नहीं पाए जाते हैं।</p>"],
                    solution_en: "<p>15.(d) The Cyclostomata group is a subclass of jawless fishes. They are marine but migrate for spawning to fresh water. After spawning, within a few days, they die. Their larvae, after metamorphosis, return to the ocean. They have an elongated body bearing 6-15 pairs of gill slits for respiration. Examples: Petromyzon (Lamprey) and Myxine (Hagfish).</p>",
                    solution_hi: "<p>15.(d) साइक्लोस्टोमेटा समूह जबड़े रहित मछलियों का एक उपवर्ग है। यह समुद्री हैं, लेकिन अंडे देने के लिए ताजे पानी में पलायन करती हैं। जनन के कुछ दिनों के बाद, वे मर जाते हैं। उनके लार्वा, कायांतरण के बाद, समुद्र में वापस लौट जाते हैं। इनका शरीर लम्बा होता है, जिसमें श्वसन के लिए 6-15 जोड़े गिल स्लिट होते हैं। उदाहरण: पेट्रोमाइज़न (लैम्प्रे) और मिक्सीन (हैगफ़िश)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>