<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "1. Find the values of x, y and z, so as to satisfy the equations given below:<br />x + y + z = 12; x + y - z = 6; x - y + z = 4",
                    question_hi: "1. x,y और z के वे मान ज्ञात करें, जिससे नीचे दिए गए समीकरणों को संतुष्ट किया जा सके:<br />x + y + z = 12; x + y - z = 6; x - y + z = 4",
                    options_en: [" x = 5, y = 4, z = - 3", " x = 5, y = - 4, z = 3", 
                                " x = 5, y = 4, z = 3", " x = 5, y = - 4, z = - 3"],
                    options_hi: [" x = 5, y = 4, z = - 3", " x = 5, y = - 4, z = 3",
                                " x  =5, y = 4, z = 3", " x = 5, y = - 4, z = - 3"],
                    solution_en: "1.(c) After checking all the options one by one, only option (c) satisfied.<br />x = 5, y = 4, z = 3<br /><math display=\"inline\"><mo>⇒</mo></math> x + y + z = 12; <br />LHS = 5 + 4 + 3 = 12 = RHS<br /><math display=\"inline\"><mo>⇒</mo></math>  x + y - z = 6;<br />LHS = 5 + 4 - 3 = 6 = RHS <br /><math display=\"inline\"><mo>⇒</mo></math> x - y + z = 4<br />LHS = 5 - 4 + 3 = 4 = RHS",
                    solution_hi: "1.(c) सभी विकल्पों को एक-एक करके जांचने के बाद, केवल विकल्प (c) संतुष्ट है।<br />x = 5 , y = 4 , z = 3 <br /><math display=\"inline\"><mo>⇒</mo></math> x + y + z = 12; <br />बायाँ पक्ष = 5 + 4 + 3 = 12 = दायाँ पक्ष<br /><math display=\"inline\"><mo>⇒</mo></math>  x + y - z = 6;<br />बायाँ पक्ष = 5 + 4 - 3 = 6 = दायाँ पक्ष<br /><math display=\"inline\"><mo>⇒</mo></math> x - y + z = 4<br />बायाँ पक्ष = 5 - 4 + 3 = 4 = दायाँ पक्ष",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. A policeman noticed a thief at a distance of 500 metres. The policeman started running and the thief also started running at the same time. The thief is running at a speed of 15 km/h. It took 15 minutes for the policeman to catch the thief. Find the speed of the policeman (in km/h).</p>",
                    question_hi: "<p>2. एक पुलिसकर्मी ने 500 मीटर की दूरी पर एक चोर को देखा। पुलिसकर्मी दौड़ने लगा और चोर भी उसी समय दौड़ने लगा। चोर 15 km/h की चाल से दौड़ रहा है। चोर को पकड़ने में पुलिसकर्मी को 15 मिनट लग गए। पुलिसकर्मी की चाल (km/h में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>11</p>", "<p>17</p>", 
                                "<p>12</p>", "<p>10</p>"],
                    options_hi: ["<p>11</p>", "<p>17</p>",
                                "<p>12</p>", "<p>10</p>"],
                    solution_en: "<p>2.(b) Let the speed of the policeman be x&nbsp;km/h. The thief is running at a speed of 15 km/h, and the initial distance between them is 500 meters, which is 0.5 km.<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>5</mn></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>15</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>15</mn><mn>60</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>5</mn></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>15</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2 = x - 15<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 17 km/h</p>",
                    solution_hi: "<p>2.(b) माना पुलिसकर्मी की चाल x&nbsp;km/h. चोर की चाल 15 km/h, और उनके बीच की प्रारंभिक दूरी 500 मीटर है , जो कि 0.5 किमी है ।<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>5</mn></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>15</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>15</mn><mn>60</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>0</mn><mo>.</mo><mn>5</mn></mrow><mrow><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>15</mn></mrow></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 2 = x - 15<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 17 km/h</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The given table shows the number of books on different subjects.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880619039.png\" alt=\"rId6\" width=\"250\" height=\"140\"> <br>What is the ratio of the number of books for subject B1 to the average number of books per subject?</p>",
                    question_hi: "<p>3. दी गई तालिका विभिन्न विषयों की पुस्तकों की संख्या को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880619137.png\" alt=\"rId7\" width=\"200\"> <br>विषय B1 की पुस्तकों की संख्या और प्रति विषय पुस्तकों की औसत संख्या का अनुपात क्या है?</p>",
                    options_en: ["<p>3 : 5</p>", "<p>10 : 11</p>", 
                                "<p>4 : 5</p>", "<p>2 : 5</p>"],
                    options_hi: ["<p>3 : 5</p>", "<p>10 : 11</p>",
                                "<p>4 : 5</p>", "<p>2 : 5</p>"],
                    solution_en: "<p>3.(b) Average number of books<br>= <math display=\"inline\"><mfrac><mrow><mn>120</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>120</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>120</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>660</mn><mn>5</mn></mfrac></math> = 132<br>Number of books B1 = 120<br>Required ratio = 120 : 132 or 10 : 11</p>",
                    solution_hi: "<p>3.(b) पुस्तकों की औसत संख्या<br>= <math display=\"inline\"><mfrac><mrow><mn>120</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>120</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>120</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>660</mn><mn>5</mn></mfrac></math> = 132<br>B1 पुस्तकों की संख्या = 120<br>आवश्यक अनुपात = 120 : 132 या 10 : 11</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. If A : B = 6 : 8 and B : C = 5 : 12, then A : B : C is:</p>",
                    question_hi: "<p>4. यदि A : B = 6 : 8 और B : C = 5 : 12 है, तो A : B : C का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>11 : 32 : 44</p>", "<p>10 : 23 : 14</p>", 
                                "<p>14 : 31 : 28</p>", "<p>15 : 20 : 48</p>"],
                    options_hi: ["<p>11 : 32 : 44</p>", "<p>10 : 23 : 14</p>",
                                "<p>14 : 31 : 28</p>", "<p>15 : 20 : 48</p>"],
                    solution_en: "<p>4.(d) Ratio &rarr;&nbsp; &nbsp;A : B : C <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 6 : 8 : 8<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 5 : 5 : 12<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;____________<br>A : B : C = 30 : 40 : 96 or 15 : 20 : 48</p>",
                    solution_hi: "<p>4.(d) आनुपात &rarr;&nbsp; &nbsp;A : B : C <br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;6 : 8 : 8<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5 : 5 : 12<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;______________<br>A : B : C = 30 : 40 : 96 या 15 : 20 : 48</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. If 2 tan&theta; = 3, then <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow></mfrac></math> is equal to:</p>",
                    question_hi: "<p>5. यदि 2 tan&theta; = 3 है, तो <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>6</mn><mi>&#160;</mi></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>5.(d) <strong>Given:</strong> 2 tan&theta; = 3 <br>then <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sin&#952;</mi><mi>cos&#952;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &rArr; sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>cos&theta;<br>Now,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>3</mn><mn>2</mn></mfrac><mi>cos&#952;</mi><mo>&#160;</mo><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>3</mn><mn>2</mn></mfrac><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><mn>9</mn><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn><mi>cos&#952;</mi></mrow><mn>2</mn></mfrac></mrow><mrow><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><mn>9</mn><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn><mi>cos&#952;</mi></mrow><mn>2</mn></mfrac></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>cos&#952;</mi></mrow><mrow><mn>13</mn><mi>cos&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math></p>",
                    solution_hi: "<p>5.(d) <strong>दिया गया है</strong> : 2 tan&theta; = 3 <br>तब <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sin&#952;</mi><mi>cos&#952;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> &rArr; sin&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math>cos&theta;<br>अब,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>3</mn><mn>2</mn></mfrac><mi>cos&#952;</mi><mo>&#160;</mo><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>3</mn><mn>2</mn></mfrac><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><mn>9</mn><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn><mi>cos&#952;</mi></mrow><mn>2</mn></mfrac></mrow><mrow><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><mn>9</mn><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>4</mn><mi>cos&#952;</mi></mrow><mn>2</mn></mfrac></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mi>cos&#952;</mi></mrow><mrow><mn>13</mn><mi>cos&#952;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>13</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. If p cosA = 2q sinA and 2p cosecA - q secA = 3, then the value of p&sup2; + 4q&sup2; is:</p>",
                    question_hi: "<p>6. यदि p cosA = 2q sinA और 2p cosecA - q secA = 3 है, तो p&sup2; + 4q&sup2; का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>2</p>", "<p>4</p>", 
                                "<p>1</p>", "<p>3</p>"],
                    options_hi: ["<p>2</p>", "<p>4</p>",
                                "<p>1</p>", "<p>3</p>"],
                    solution_en: "<p>6.(b) <strong>Given:</strong> p cosA = 2q sinA <br><math display=\"inline\"><mo>&#8658;</mo></math> p = 2q &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math> &hellip;&hellip;(i)<br>2p cosecA - q secA = 3<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">p</mi></mrow><mi>sinA</mi></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mi mathvariant=\"normal\">q</mi><mi>cosA</mi></mfrac></math> = 3<br><math display=\"inline\"><mo>&#8658;</mo></math> 2p &times; cosA - q &times; sinA = 3 &times; sinA.cosA<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 &times; 2q &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math> &times; cosA - q &times; sinA = 3 &times; sinA.cosA [from eq. (i)]<br><math display=\"inline\"><mo>&#8658;</mo></math> 4q &times; sinA - q &times; sinA = 3 &times; sinA.cosA<br><math display=\"inline\"><mo>&#8658;</mo></math> 3q &times; sinA = 3 &times; sinA.cosA<br><math display=\"inline\"><mo>&#8658;</mo></math> q = cosA <br>Put the value of q in eq. (i)<br><math display=\"inline\"><mo>&#8658;</mo></math> p = 2 &times; cosA &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = 2sinA<br>Now,<br>p&sup2; + 4q&sup2;<br>= (2SinA)&sup2; + 4(cosA)&sup2;<br>= 4(sin&sup2;A + cos&sup2;A) = 4</p>",
                    solution_hi: "<p>6.(b) <strong>दिया गया है :</strong> p cosA = 2q sinA <br><math display=\"inline\"><mo>&#8658;</mo></math> p = 2q &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math> &hellip;&hellip;(i)<br>2p cosecA - q secA = 3<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">p</mi></mrow><mi>sinA</mi></mfrac><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mfrac><mi mathvariant=\"normal\">q</mi><mi>cosA</mi></mfrac></math> = 3<br><math display=\"inline\"><mo>&#8658;</mo></math> 2p &times; cosA - q &times; sinA = 3 &times; sinA.cosA<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 &times; 2q &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math> &times; cosA - q &times; sinA = 3 &times; sinA.cosA .....[समीकरण&nbsp; (i) से ]<br><strong id=\"docs-internal-guid-a4835f3a-7fff-55e8-5738-e9693cb6450e\"></strong><math display=\"inline\"><mo>&#8658;</mo></math> 4q &times; sinA - q &times; sinA = 3 &times; sinA.cosA<br><math display=\"inline\"><mo>&#8658;</mo></math> 3q &times; sinA = 3 &times; sinA.cosA<br><math display=\"inline\"><mo>&#8658;</mo></math> q = cosA <br>समीकरण&nbsp; (i) मे q का मान रखने पर<br><math display=\"inline\"><mo>&#8658;</mo></math> p = 2 &times; cosA &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sinA</mi><mi>cosA</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> p = 2sinA<br>अब,<br>p&sup2; + 4q&sup2;<br>= (2SinA)&sup2; + 4(cosA)&sup2;<br>= 4(sin&sup2;A + cos&sup2;A) = 4</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. &Delta;EFG and &Delta;HIJ are similar. Also, &ang;E = &ang;H and &ang;F = &ang;1. If 3EF = HI and FG = 9 cm, then IJ is equal to ______ .</p>",
                    question_hi: "<p>7. &Delta;EFG और &Delta;HIJ समान हैं। साथ ही, &ang;E = &ang;H और &ang;F = &ang;I है। यदि 3EF = HI और FG = 9 cm है, तो IJ _______ के बराबर है।</p>",
                    options_en: ["<p>9 cm</p>", "<p>27 cm</p>", 
                                "<p>3 cm</p>", "<p>18 cm</p>"],
                    options_hi: ["<p>9 cm</p>", "<p>27 cm</p>",
                                "<p>3 cm</p>", "<p>18 cm</p>"],
                    solution_en: "<p>7.(b) <strong>Given:</strong> &Delta;EFG <math display=\"inline\"><mo>&#8764;</mo></math> &Delta;HIJ<br>Then, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mi>EF</mi><mi>HI</mi></mfrac></mstyle><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mi>FG</mi><mi>IJ</mi></mfrac></mstyle><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mi>GE</mi><mi>HJ</mi></mfrac></mstyle></math><br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 3EF = HI<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>EF</mi><mi>HI</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math><br>Now,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>EF</mi><mi>HI</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mi>FG</mi><mi>IJ</mi></mfrac></math> <br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>9</mn><mi>IJ</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> IJ = 27 cm</p>",
                    solution_hi: "<p>7.(b) <strong>दिया गया है :</strong> &Delta;EFG <math display=\"inline\"><mo>&#8764;</mo></math> &Delta;HIJ<br>तब,&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"true\"><mfrac><mi>EF</mi><mi>HI</mi></mfrac></mstyle><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mi>FG</mi><mi>IJ</mi></mfrac></mstyle><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mi>GE</mi><mi>HJ</mi></mfrac></mstyle></math><br>प्रश्न के अनुसार ,<br><math display=\"inline\"><mo>&#8658;</mo></math> 3EF = HI<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>EF</mi><mi>HI</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>1</mn><mn>3</mn></mfrac></math><br>तब,<br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>EF</mi><mi>HI</mi></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mi>FG</mi><mi>IJ</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>&#160;</mo><mo>=</mo><mo>&#160;</mo><mfrac><mn>9</mn><mi>IJ</mi></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> IJ = 27 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p style=\"text-align: justify;\">8. Study the given bar-graph and answer the question that follows. <br>The bar-graph shows the sales of books (in thousands) from six branches (B1, B2, B3, B4, B5 and B6) of a publishing company during two consecutive years 2000 and 2001.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880619236.png\" alt=\"rId8\" width=\"350\"> <br>The total sales of branch B6 for both the years is what percentage of the total sales of branch B3 for both the years (correct up to two decimal places)?</p>",
                    question_hi: "<p>8. दिए गए दंड-आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br>दंड-आलेख दो क्रमिक वर्षों 2000 और 2001 के दौरान एक प्रकाशन कंपनी की छह शाखाओं (B1, B2, B3, B4, B5 और B6) से पुस्तकों की बिक्री (हजार में) दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880619336.png\" alt=\"rId9\" width=\"350\"> <br>दोनों वर्षों के लिए शाखा B6 की कुल बिक्री, दोनों वर्षों के लिए शाखा B3 की कुल बिक्री का कितने प्रतिशत है (दो दशमलव स्थान तक पूर्णांकित)?</p>",
                    options_en: ["<p>70.69%</p>", "<p>73.17%</p>", 
                                "<p>69.25%</p>", "<p>82.26%</p>"],
                    options_hi: ["<p>70.69%</p>", "<p>73.17%</p>",
                                "<p>69.25%</p>", "<p>82.26%</p>"],
                    solution_en: "<p>8.(b)<br>Total sales of branch B6 for both years = 70 + 80 = 150<br>Total sales of branch B3 for both years = 95 +110 = 205<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>205</mn></mrow></mfrac></math> &times; 100 = 73.17%</p>",
                    solution_hi: "<p>8.(b)<br>दोनो वर्षों के लिए शाखा B6 की कुल बिक्री = 70 + 80 = 150<br>दोनो वर्षों के लिए शाखा B3 की कुल बिक्री = 95 +110 = 205<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>150</mn></mrow><mrow><mn>205</mn></mrow></mfrac></math> &times; 100 = 73.17%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Places A and B are 20 km apart on the highway. A car starts from A and another from B at the same time. If the cars travel in the same direction at different speeds, they meet in one hour. If they travelled towards each other, they met in 12 minutes. What are the speeds (in km/h) of two cars?</p>",
                    question_hi: "<p>9. राजमार्ग पर स्थान A और B एक-दूसरे से 20 km दूर हैं। एक कार A से और दूसरी कार B से एक ही समय पर चलना प्रारंभ करती है। यदि कारें अलग-अलग चाल से एक ही दिशा में चलती हैं, तो वे एक घंटे में मिलती हैं। यदि वे एक-दूसरे की ओर चलती हैं, तो वे 12 मिनट में मिलती हैं। दोनों कारों की चाल (km/h में) क्या हैं?</p>",
                    options_en: ["<p>60,40</p>", "<p>52,48</p>", 
                                "<p>51,49</p>", "<p>55,45</p>"],
                    options_hi: ["<p>60,40</p>", "<p>52,48</p>",
                                "<p>51,49</p>", "<p>55,45</p>"],
                    solution_en: "<p>9.(a) Let the speed of two cars be <math display=\"inline\"><mi>x</mi></math> km/h and y km/h respectively,<br>According to the question,<br>When car travel in the same direction,<br>(x&nbsp;- y) &times; 1 = 20 <br>(x&nbsp;- y) = 20 &hellip;&hellip;..(i)<br>When car travel in the opposite direction,<br>(x&nbsp;+ y) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>60</mn></mfrac></math> = 20 <br>(x&nbsp;+ y) = 100 &hellip;&hellip;..(ii)<br>Add the equation (i) and (ii)<br>2x&nbsp;= 120<br><math display=\"inline\"><mi>x</mi></math> = 60 km/h<br>Put the value of <math display=\"inline\"><mi>x</mi></math> in equation (i),<br>60 - <math display=\"inline\"><mi>y</mi></math> = 20<br><math display=\"inline\"><mi>y</mi></math> = 40 km/h<br>Hence, the speed of two cars will be 60 km/h and 40 km/h respectively.</p>",
                    solution_hi: "<p>9.(a) माना दोनो कारों की चाल क्रमशः <math display=\"inline\"><mi>x</mi></math> km/h और y km/h है ,<br>प्रश्च के अनुसार,<br>जब दोनो कार एक ही दिशा में चलती है ,<br>(x&nbsp;- y) &times; 1 = 20 <br>(x&nbsp;- y) = 20 &hellip;&hellip;..(i)<br>जब दोनो कार विपरित दिशा में चलती है,<br>(x + y) &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>60</mn></mfrac></math> = 20 <br>(x&nbsp;+ y) = 100 &hellip;&hellip;..(ii)<br>समी. (i) और समी.(ii) को जोड़ने पर <br>2x&nbsp;= 120<br>x = 60 km/h<br>x का मान समी.(i) मे रखने पर,<br>60 - <math display=\"inline\"><mi>y</mi></math> = 20<br><math display=\"inline\"><mi>y</mi></math> = 40 km/h<br>अतः दोनो कारो की चाल क्रमशः 60 km/h और 40 km/h है ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. If 45&deg; and 65&deg; are the angles of a triangle, then find the exterior angle of the third angle (remaining angle).</p>",
                    question_hi: "<p>10. यदि किसी त्रिभुज के कोण 45&deg; और 65&deg; हैं, तो तीसरे कोण (शेष कोण) का बाह्य कोण ज्ञात कीजिए।</p>",
                    options_en: ["<p>110&deg;</p>", "<p>120&deg;</p>", 
                                "<p>70&deg;</p>", "<p>100&deg;</p>"],
                    options_hi: ["<p>110&deg;</p>", "<p>120&deg;</p>",
                                "<p>70&deg;</p>", "<p>100&deg;</p>"],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880619546.png\" alt=\"rId10\" width=\"200\"><br>In <math display=\"inline\"><mi>&#916;</mi></math>PQR,<br>45&deg; + 65&deg; + &ang;RQP = 180&deg;<br>&ang;RQP = 180&deg; - 110 = 70&deg;<br>Now,<br>&ang;RQS = 180&deg; - 70&deg; = 110&deg;</p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880619546.png\" alt=\"rId10\" width=\"200\"><br><math display=\"inline\"><mi>&#916;</mi></math>PQR में,<br>45&deg; + 65&deg; + &ang;RQP = 180&deg;<br>&ang;RQP = 180&deg; - 110 = 70&deg;<br>अब,<br>&ang;RQS = 180&deg; - 70&deg; = 110&deg;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. A right triangle with sides 3 cm, 4 cm and 5 cm is rotated about the sides of 3 cm to form a cone. The volume of the cone so formed is:</p>",
                    question_hi: "<p>11. 3 cm, 4 cm और 5 cm भुजाओं वाले एक समकोण त्रिभुज को एक शंकु बनाने के लिए 3 cm वाली भुजा के परितः घुमाया जाता है। इस प्रकार बने शंकु का आयतन क्या होगा?</p>",
                    options_en: ["<p>20&pi; cm&sup3;</p>", "<p>16&pi; cm&sup3;</p>", 
                                "<p>25&pi; cm&sup3;</p>", "<p>28&pi; cm&sup3;</p>"],
                    options_hi: ["<p>20&pi; cm&sup3;</p>", "<p>16&pi; cm&sup3;</p>",
                                "<p>25&pi; cm&sup3;</p>", "<p>28&pi; cm&sup3;</p>"],
                    solution_en: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880619827.png\" alt=\"rId11\" width=\"180\"><br>Volume of the cone = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math>h<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; &pi; &times; 4 &times; 4 &times; 3 = 16&pi; cm&sup3;</p>",
                    solution_hi: "<p>11.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880619827.png\" alt=\"rId11\" width=\"180\"><br>शंकु का आयतन = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math>h<br>= <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; &pi; &times; 4 &times; 4 &times; 3 = 16&pi; cm&sup3;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. Pipe L can fill a pool in 30 hours and pipe M in 45 hours. If both the pipes are opened in an empty pool, how much time will they take to fill it?</p>",
                    question_hi: "<p>12. पाइप L एक पूल को 30 घंटे में और पाइप M इसे 45 घंटे में भर सकता है। यदि दोनों पाइपों को एक खाली पूल में खोल दिया जाए, तो उन्हें इसे भरने में कितना समय लगेगा?</p>",
                    options_en: ["<p>24 hours</p>", "<p>17 hours</p>", 
                                "<p>18 hours</p>", "<p>20 hours</p>"],
                    options_hi: ["<p>24 घंटे</p>", "<p>17 घंटे</p>",
                                "<p>18 घंटे</p>", "<p>20 घंटे</p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880620087.png\" alt=\"rId13\" width=\"200\" height=\"138\"><br>Both pipe take time to fill the pool = <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 18 hours</p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880620181.png\" alt=\"rId14\" width=\"200\"><br>दोनो पाइपों को खाली पूल भरने मे लगा समय = <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 18 घंटे</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. If <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">x</mi></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi mathvariant=\"normal\">x</mi></msqrt></mfrac></math> = 7, then the value of x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> is equal to:</p>",
                    question_hi: "<p>13. यदि <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">x</mi></msqrt></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mi mathvariant=\"normal\">x</mi></msqrt></mfrac></math> = 7 है, तो x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> का मान निम्न में से किसके बराबर होगा?</p>",
                    options_en: ["<p>47</p>", "<p>49</p>", 
                                "<p>51</p>", "<p>45</p>"],
                    options_hi: ["<p>47</p>", "<p>49</p>",
                                "<p>51</p>", "<p>45</p>"],
                    solution_en: "<p>13.(a) <br>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">x</mi></msqrt><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msqrt><mi mathvariant=\"normal\">x</mi></msqrt></mfrac></math>= 7<br>On squaring both side, <br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> + 2 = 49<br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = 47</p>",
                    solution_hi: "<p>13.(a) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi mathvariant=\"normal\">x</mi></msqrt><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mfrac><mn>1</mn><msqrt><mi mathvariant=\"normal\">x</mi></msqrt></mfrac></math> = 7<br>दोनो पक्षो का वर्ग करने पर, <br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> + 2 = 49<br><math display=\"inline\"><mo>&#8658;</mo></math> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = 47</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. A merchant has 1000 kg of sugar, part of which he sells at a 10% profit and the rest at a 40% profit. He gains 20% on the whole. The quantity sold at 40% profit is:</p>",
                    question_hi: "<p>14. एक व्यापारी के पास 1000 kg चीनी है, जिसका एक भाग वह 10% लाभ पर और शेष 40% लाभ पर बेचता है। उसे पूरे सौदे में 20% का लाभ होता है। 40% लाभ पर बेची गई मात्रा की गणना करें।</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>543</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math> kg</p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>383</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math> kg</p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>333</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math> kg</p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>443</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math> kg</p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>543</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math> kg</p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>383</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math> kg</p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>333</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math> kg</p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>443</mn><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;kg</p>"],
                    solution_en: "<p>14.(c)&nbsp;Total quantity of sugar = 1000 kg<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880620461.png\" alt=\"rId15\" width=\"150\"><br>Sugar sold at 40 % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 1000 = 333<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> kg</p>",
                    solution_hi: "<p>14.(c)&nbsp;चीनी की कुल मात्रा = 1000 किग्रा<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880620461.png\" alt=\"rId15\" width=\"150\"><br>40% लाभ में बेंची गयी चीनी = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 1000 = 333<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> किग्रा</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. Akshay purchased pens and notebooks from a shop for a total sum of ₹180. From&nbsp;the following data, what is the value of \'X\'?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880620598.png\" alt=\"rId16\" width=\"350\" height=\"71\"></p>",
                    question_hi: "<p>15. अक्षय ने किसी दुकान से कुल ₹180 के पेन और नोटबुक खरीदे। निम्नांकित आँकड़ों से \'X\' का मान क्या होगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880620770.png\" alt=\"rId17\" width=\"250\"></p>",
                    options_en: ["<p>4</p>", "<p>6</p>", 
                                "<p>5</p>", "<p>7</p>"],
                    options_hi: ["<p>4</p>", "<p>6</p>",
                                "<p>5</p>", "<p>7</p>"],
                    solution_en: "<p>15.(b) According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 10x + 20 &times; 6 = 180<br><math display=\"inline\"><mo>&#8658;</mo></math> 10x = 180 - 120<br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>10</mn></mfrac></math> = 6</p>",
                    solution_hi: "<p>15.(b) प्रश्च के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 10x + 20 &times; 6 = 180<br><math display=\"inline\"><mo>&#8658;</mo></math> 10x = 180 - 120<br><math display=\"inline\"><mo>&#8658;</mo></math> x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>10</mn></mfrac></math> = 6</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. The concentrations of three acids, A, B, and C, are specified as 20%, 30%, and 40%, respectively. They are blended in a ratio of 3 : 5 : a, yielding a solution with a concentration of 30%. What is the value of \'a\'?</p>",
                    question_hi: "<p>16. तीन अम्लों, A,B और C की सांद्रता क्रमशः 20%, 30% और 40% के रूप में निर्दिष्ट की गई है। इन्हें 3 : 5 : a के अनुपात में मिश्रित किया जाता है, जिससे 30% सांद्रता वाला विलयन प्राप्त होता है। \'a\' का मान क्या है?</p>",
                    options_en: ["<p>3</p>", "<p>1</p>", 
                                "<p>2</p>", "<p>4</p>"],
                    options_hi: ["<p>3</p>", "<p>1</p>",
                                "<p>2</p>", "<p>4</p>"],
                    solution_en: "<p>16.(a) <strong>Given:</strong> concentration in new solution = 30%<br>Acid &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr;&nbsp; A&nbsp; :&nbsp; B&nbsp; &nbsp;: C<br>Ratio &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr;&nbsp; 3&nbsp; :&nbsp; 5&nbsp; &nbsp;: a<br>Concentration(%) &rarr; 20 :&nbsp; 30 : 40<br>Deviation &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &rarr; -10 :&nbsp; &nbsp;0&nbsp; : +10<br>According to question<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>30</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>40</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">a</mi></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">a</mi></mrow></mfrac></math> = 30<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>150</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>40</mn><mi mathvariant=\"normal\">a</mi></mrow><mrow><mn>8</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">a</mi></mrow></mfrac></math> = 30<br>210 + 40a&nbsp;= 240 + 30a<br>10a&nbsp;= 30 &rArr; a = 3</p>",
                    solution_hi: "<p>16.(a) <strong>दिया है :</strong>&nbsp;<span class=\"Y2IQFc\" lang=\"hi\">नए समाधान में</span>&nbsp;= 30%<br>अम्ल &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&rarr; A&nbsp; &nbsp;: B&nbsp; :&nbsp; C<br>अनुपात &nbsp; &nbsp; &nbsp;&rarr; 3&nbsp; &nbsp;:&nbsp; 5&nbsp; :&nbsp; a<br>सांद्रता (%) &rarr; 20 : 30 : 40<br>विचलन &nbsp; &nbsp; &nbsp;&rarr;-10 :&nbsp; 0&nbsp; : +10<br>प्रश्न के अनुसार<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>20</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>30</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>40</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mo>&#160;</mo><mi mathvariant=\"normal\">a</mi></mrow><mrow><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">a</mi></mrow></mfrac></math> = 30<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>60</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>150</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mn>40</mn><mi mathvariant=\"normal\">a</mi></mrow><mrow><mn>8</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">a</mi></mrow></mfrac></math> = 30<br>210 + 40a&nbsp;= 240 + 30a<br>10a&nbsp;= 30 &rArr; a = 3</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. Simplify <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>001</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>058</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>01</mn></mrow></mfrac></math>.</p>",
                    question_hi: "<p>17. निम्नलिखित को सरल करें। <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>001</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>058</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>01</mn></mrow></mfrac></math>.</p>",
                    options_en: ["<p>0.48</p>", "<p>0.28</p>", 
                                "<p>0.37</p>", "<p>0.42</p>"],
                    options_hi: ["<p>0.48</p>", "<p>0.28</p>",
                                "<p>0.37</p>", "<p>0.42</p>"],
                    solution_en: "<p>17.(a) <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>001</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>058</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>01</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>58</mn><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>0</mn><mo>.</mo><mn>58</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>1</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>]</mo></mrow></mfrac></math><br>Using identity : (a)&sup3; - (b)&sup3; = (a - b)(a&sup2; + b&sup2; + ab)<br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo><mo>[</mo><mo>(</mo><msup><mrow><mn>0</mn><mo>.</mo><mn>58</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>1</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mo>]</mo></mrow><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>0</mn><mo>.</mo><mn>58</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>1</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>]</mo></mrow></mfrac></math><br>= 0.58 - 0.1 = 0.48</p>",
                    solution_hi: "<p>17.(a) <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>001</mn></mrow><mrow><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>058</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>01</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>58</mn><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>0</mn><mo>.</mo><mn>58</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>1</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>]</mo></mrow></mfrac></math><br>सर्वसमिका से : (a)&sup3; - (b)&sup3; = (a - b)(a&sup2; + b&sup2; + ab)<br>= <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo><mo>[</mo><mo>(</mo><msup><mrow><mn>0</mn><mo>.</mo><mn>58</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>1</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mo>]</mo></mrow><mrow><msup><mrow><mo>[</mo><mo>(</mo><mn>0</mn><mo>.</mo><mn>58</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>1</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>0</mn><mo>.</mo><mn>58</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mn>0</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>]</mo></mrow></mfrac></math><br>= 0.58 - 0.1 = 0.48</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. Ravi\'s salary was increased by 50%. After some time, his new salary was also increased by 50%. Find his gain percentage of salary.</p>",
                    question_hi: "<p>18. रवि के वेतन में 50% की वृद्धि की गई। कुछ समय बाद उसके नए वेतन में भी 50% की वृद्धि कर दी गई। उसके वेतन का लाभ-प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: ["<p>125%</p>", "<p>100%</p>", 
                                "<p>200%</p>", "<p>150%</p>"],
                    options_hi: ["<p>125%</p>", "<p>100%</p>",
                                "<p>200%</p>", "<p>150%</p>"],
                    solution_en: "<p>18.(a) Let the Ravi&rsquo;s salary be ₹100<br>According to the question,<br>Ravi&rsquo;s new salary = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹225<br>Gain percentage = <math display=\"inline\"><mfrac><mrow><mn>225</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 125 %</p>",
                    solution_hi: "<p>18.(a) मना रवि का वेतन ₹100 <br>प्रश्च के अनुसार,<br>रवि का नया वेतन = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = ₹225<br>तो लाभ-प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>225</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 125 %</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. The given bar graph shows the production of bikes by a company (in thousands) over the years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880620859.png\" alt=\"rId18\" width=\"350\"> <br>In how many of the given years was the production of bikes more than the average production of bikes over the years ?.</p>",
                    question_hi: "<p>19. निम्नांकित बार ग्राफ दिए गए वर्षों में एक कंपनी द्वारा बाइक के उत्पादन (हजारों में) को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880620949.png\" alt=\"rId19\" width=\"300\"> <br>दिए गए वर्षों में से कितने वर्षों में बाइक का उत्पादन, दिए गए वर्षों में बाइक के औसत उत्पादन से अधिक था ?.</p>",
                    options_en: ["<p>1 year</p>", "<p>2 year</p>", 
                                "<p>3 year</p>", "<p>4 year</p>"],
                    options_hi: ["<p>1 वर्ष</p>", "<p>2 वर्ष</p>",
                                "<p>3 वर्ष</p>", "<p>4 वर्ष</p>"],
                    solution_en: "<p>19.(c) Average production of bikes all over the years<br>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>50</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>70</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>90</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>40</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn></mrow><mn>7</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>375</mn><mn>7</mn></mfrac></math> = 53.57 <br>Bike production of 3 years (2010, 2011 and 2013) more than the average marks.</p>",
                    solution_hi: "<p>19.(c) कंपनी द्वारा सभी वर्षो में बाइक औसत उत्पादन&nbsp;<br>&nbsp;= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>35</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>50</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>70</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>90</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>40</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>30</mn></mrow><mn>7</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>375</mn><mn>7</mn></mfrac></math> = 53.57<br>3 वर्षों (2010, 2011 और 2013) में बाइक उत्पादन औसत अंक से अधिक है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. If cot&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>, then evaluate <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sec&#952;</mi><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>(</mo><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>)</mo></mrow><mrow><msup><mi>cosec</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math>.</p>",
                    question_hi: "<p>20. यदि cot&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mi>sec&#952;</mi><mo>(</mo><mn>1</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>(</mo><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>)</mo></mrow><mrow><msup><mi>cosec</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>", 
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math></p>",
                                "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>5</mn></mfrac></math></p>"],
                    solution_en: "<p>20.(a) <strong>Given :</strong> cot&theta; = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>[</mo><mi>sec&#952;</mi><mo>(</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>(</mo><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>]</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>cosec</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>[</mo><mfrac><mn>1</mn><mi>cos&#952;</mi></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mo>)</mo><mo>(</mo><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mo>)</mo><mo>]</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mi>sin&#952;</mi></mfrac><mo>)</mo></mrow><mn>3</mn></msup></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>[</mo><mfrac><mn>1</mn><mi>cos&#952;</mi></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mfrac><mrow><msup><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mo>)</mo><mo>(</mo><mfrac><mrow><msup><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mo>)</mo><mo>]</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></mfrac></math><br>∵ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></math> = 1, 1 - cos&sup2;&theta; = sin&sup2;&theta;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sin&#952;</mi><mi>cos&#952;</mi></mfrac></math> <br>= tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>cot&#952;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>20.(a) <strong>दिया गया :</strong> cot&theta; = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>[</mo><mi>sec&#952;</mi><mo>(</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>(</mo><msup><mi>cosec</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>)</mo><mo>]</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><msup><mi>cosec</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>[</mo><mfrac><mn>1</mn><mi>cos&#952;</mi></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mo>)</mo><mo>(</mo><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mrow><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mo>)</mo><mo>]</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><msup><mrow><mo>(</mo><mfrac><mn>1</mn><mi>sin&#952;</mi></mfrac><mo>)</mo></mrow><mn>3</mn></msup></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>[</mo><mfrac><mn>1</mn><mi>cos&#952;</mi></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mo>(</mo><mfrac><mrow><msup><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mo>)</mo><mo>(</mo><mfrac><mrow><msup><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac><mo>)</mo><mo>]</mo><mi mathvariant=\"normal\">&#160;</mi></mrow><mfrac><mn>1</mn><mrow><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></mfrac></math><br>∵ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi></math> = 1, 1 - cos&sup2;&theta; = sin&sup2;&theta;<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>3</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow><mrow><mi>cos&#952;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>sin</mi><mn>4</mn></msup><mi mathvariant=\"normal\">&#952;</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>sin&#952;</mi><mi>cos&#952;</mi></mfrac></math> <br>= tan&theta; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>cot&#952;</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. If (<math display=\"inline\"><msup><mrow><mn>77</mn></mrow><mrow><mn>77</mn></mrow></msup></math>+ 77) is divided by 78, then the remainder will be:</p>",
                    question_hi: "<p>21. यदि (<math display=\"inline\"><msup><mrow><mn>77</mn></mrow><mrow><mn>77</mn></mrow></msup></math>+ 77) को 78 से विभाजित किया जाए, तो शेषफल क्या होगा?</p>",
                    options_en: ["<p>76</p>", "<p>77</p>", 
                                "<p>78</p>", "<p>1</p>"],
                    options_hi: ["<p>76</p>", "<p>77</p>",
                                "<p>78</p>", "<p>1</p>"],
                    solution_en: "<p>21.(a) <br><math display=\"block\"><mfrac><mrow><mo>(</mo><msup><mrow><mn>77</mn></mrow><mrow><mn>77</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>77</mn><mo>)</mo><mi>&#160;</mi></mrow><mrow><mn>78</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mo>[</mo><msup><mrow><mo>(</mo><mn>78</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>77</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>77</mn><mo>]</mo><mi>&#160;</mi></mrow><mrow><mn>78</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mn>78</mn></mrow><mrow><mn>77</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>77</mn></mrow></msup><mi>&#160;</mi></mrow><mrow><mn>78</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>77</mn><mn>78</mn></mfrac></math><br>Remainder = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>77</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math>= 76</p>",
                    solution_hi: "<p>21.(a),<br><math display=\"block\"><mfrac><mrow><mo>(</mo><msup><mrow><mn>77</mn></mrow><mrow><mn>77</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>77</mn><mo>)</mo><mi>&#160;</mi></mrow><mrow><mn>78</mn></mrow></mfrac></math><br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>[</mo><msup><mrow><mo>(</mo><mn>78</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow><mn>77</mn></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>77</mn><mo>]</mo><mi>&#160;</mi></mrow><mn>78</mn></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mn>78</mn></mrow><mrow><mn>77</mn></mrow></msup><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mrow><mn>77</mn></mrow></msup><mi>&#160;</mi></mrow><mrow><mn>78</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>77</mn><mn>78</mn></mfrac></math><br>शेषफल = <math display=\"inline\"><mfrac><mrow><mi>&#160;</mi><mn>77</mn><mi>&#160;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>1</mn></mrow><mrow><mn>78</mn></mrow></mfrac></math>= 76</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. Two successive discounts of 40% and 30% are equal to a single discount of:</p>",
                    question_hi: "<p>22. 40% और 30% की दो क्रमिक छूट_______ की एकल छूट के बराबर हैं।</p>",
                    options_en: ["<p>70%</p>", "<p>35%</p>", 
                                "<p>58%</p>", "<p>60%</p>"],
                    options_hi: ["<p>70%</p>", "<p>35%</p>",
                                "<p>58%</p>", "<p>60%</p>"],
                    solution_en: "<p>22.(c)<br>Required discount = 40 + 30 - <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>40</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>30</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>= 70 - 12 = 58% <br>Hence, a single discount will be 58%</p>",
                    solution_hi: "<p>22.(c)<br>आवश्यक छूट = 40 + 30 - <math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>40</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>30</mn><mo>)</mo></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>= 70 - 12 = 58% <br>अतः , एकल छूट 58% के बराबर है ।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. In a circle of radius 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm, a chord is at a distance of 10 cm from the centre of the circle. Find the length (in cm) of the chord.</p>",
                    question_hi: "<p>23. 5<math display=\"inline\"><msqrt><mn>13</mn></msqrt></math> cm त्रिज्या वाले एक वृत्त में, एक जीवा वृत्त के केंद्र से 10 cm की दूरी पर है। जीवा की लंबाई (cm में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>30</p>", "<p>28</p>", 
                                "<p>36</p>", "<p>15</p>"],
                    options_hi: ["<p>30</p>", "<p>28</p>",
                                "<p>36</p>", "<p>15</p>"],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880621107.png\" alt=\"rId20\" width=\"150\" height=\"144\"><br>In <math display=\"inline\"><mi>&#916;</mi></math>AON,<br>By using pythagoras theorem,<br>AN&sup2; + ON&sup2; = AO&sup2;<br><math display=\"inline\"><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> + AN&sup2; = (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math>)&sup2;<br>100 + AN&sup2; = 325<br>AN = <math display=\"inline\"><msqrt><mn>325</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></msqrt><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math><br>AN = 15 cm<br>AN = NB = 15 cm<br>So, AB = 30 cm</p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1730880621107.png\" alt=\"rId20\" width=\"150\"><br><math display=\"inline\"><mi>&#916;</mi></math>AON में,<br>पाइथागोरस प्रमेय से,<br>AN&sup2; + ON&sup2; = AO&sup2;<br><math display=\"inline\"><msup><mrow><mo>(</mo><mn>10</mn><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> + AN&sup2; = (5<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math>)&sup2;<br>100 + AN&sup2; = 325<br>AN = <math display=\"inline\"><msqrt><mn>325</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>100</mn></msqrt><mi>&#160;</mi></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>225</mn></msqrt></math><br>AN = 15 cm<br>AN = NB = 15 cm<br>तो, AB = 30 cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. What annual instalment will discharge a debt of ₹26,160 due in 4 years at 6% simple interest p.a .?</p>",
                    question_hi: "<p>24. 6% वार्षिक साधारण ब्याज़ पर 4 वर्षों में देय ₹26,160 के ऋण को चुकता करने के लिए आवश्यक वार्षिक किस्त क्या होगी?</p>",
                    options_en: ["<p>₹4,500</p>", "<p>₹5,500</p>", 
                                "<p>₹6,000</p>", "<p>₹5,800</p>"],
                    options_hi: ["<p>₹4,500</p>", "<p>₹5,500</p>",
                                "<p>₹6,000</p>", "<p>₹5,800</p>"],
                    solution_en: "<p>24.(c)&nbsp;Rate = 6%<br>Let the installment = 100 units<br>Ratio &rarr; installment &nbsp; : &nbsp; amount<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 100<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 106<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 112<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;100&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 118<br>Total amount = 100 + 106 + 112 + 118 = 436 units<br>According to the question,<br>436 units = 26160<br>100 units = <math display=\"inline\"><mfrac><mrow><mn>26160</mn></mrow><mrow><mn>436</mn></mrow></mfrac></math> &times; 100 = ₹6000</p>",
                    solution_hi: "<p>24.(c)&nbsp;दर = 6%<br>माना किश्त = 100 इकाई<br>अनुपात &rarr; &nbsp; &nbsp;किश्त&nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; राशि<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 100 &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 100<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 106<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 112<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;100&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 118<br>कुल राशि = 100 + 106 + 112 + 118 = 436 इकाई&nbsp;<br>प्रश्न के अनुसार,<br>436 इकाई = 26160<br>100 इकाई = <math display=\"inline\"><mfrac><mrow><mn>26160</mn></mrow><mrow><mn>436</mn></mrow></mfrac></math> &times; 100 = ₹6000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Simplify: 21 - [6 + 7 - {3.22 - (1.1 &times; 0.2)}].</p>",
                    question_hi: "<p>25. सरल कीजिए:<br>21 - [6 + 7 - {3.22 - (1.1 &times; 0.2)}].</p>",
                    options_en: ["<p>13</p>", "<p>12</p>", 
                                "<p>11</p>", "<p>10</p>"],
                    options_hi: ["<p>13</p>", "<p>12</p>",
                                "<p>11</p>", "<p>10</p>"],
                    solution_en: "<p>25.(c) 21 - [6 + 7 - {3.22 - (1.1 &times;&nbsp;0.2)}]<br>= 21 - [6 + 7 - {3.22 - 0.22}]<br>= 21 - [6 + 7 - 3]&nbsp;= 21 - 10 = 11</p>",
                    solution_hi: "<p>25.(c) 21 - [6 + 7 - {3.22 - (1.1 &times;&nbsp;0.2)}]<br>= 21 - [6 + 7 - {3.22 - 0.22}]<br>= 21 - [6 + 7 - 3]&nbsp;= 21 - 10 = 11</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>