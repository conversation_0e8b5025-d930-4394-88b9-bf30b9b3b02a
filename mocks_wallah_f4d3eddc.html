<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1. Select the option that can be used as a one-word substitute for the given phrase. <br>A short interesting story about a real person or event</p>",
                    question_hi: "<p>1. Select the option that can be used as a one-word substitute for the given phrase. <br>A short interesting story about a real person or event</p>",
                    options_en: ["<p>Poem</p>", "<p>Sketch</p>", 
                                "<p>Anecdote</p>", "<p>Narrative</p>"],
                    options_hi: ["<p>Poem</p>", "<p>Sketch</p>",
                                "<p>Anecdote</p>", "<p>Narrative</p>"],
                    solution_en: "<p>1.(c) <strong>Anecdote</strong>- a short interesting story about a real person or event.<br><strong>Poem</strong>- a piece of writing in which the words are arranged in separate lines, often ending in rhyme, and are chosen for their sound and for the images and ideas they suggest.<br><strong>Sketch</strong>- a simple, quickly-made drawing that does not have many details.<br><strong>Narrative</strong>- a spoken or written account of connected events.</p>",
                    solution_hi: "<p>1.(c) <strong>Anecdote </strong>(किस्सा) - a short interesting story about a real person or event.<br><strong>Poem </strong>(कविता) - a piece of writing in which the words are arranged in separate lines, often ending in rhyme, and are chosen for their sound and for the images and ideas they suggest.<br><strong>Sketch </strong>(रेखाचित्र) - a simple, quickly-made drawing that does not have many details.<br><strong>Narrative </strong>(कथा) - a spoken or written account of connected events.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p>2. Select the option that can be used as a one-word substitute for the underlined group of words. <br>She is <span style=\"text-decoration: underline;\">proficient in speaking many languages.</span></p>",
                    question_hi: "<p>2. Select the option that can be used as a one-word substitute for the underlined group of words. <br>She is <span style=\"text-decoration: underline;\">proficient in speaking many languages.</span></p>",
                    options_en: ["<p>Monolithic</p>", "<p>Multilingual</p>", 
                                "<p>Heterolinguistic</p>", "<p>Bilingual</p>"],
                    options_hi: ["<p>Monolithic</p>", "<p>Multilingual</p>",
                                "<p>Heterolinguistic</p>", "<p>Bilingual</p>"],
                    solution_en: "<p>2.(b) <strong>Multilingual</strong>- proficient in speaking many languages.<br><strong>Monolithic</strong>- formed of a single large block of stone.<br><strong>Heterolinguistic</strong>- pertaining to or characterized by the use of different languages.<br><strong>Bilingual</strong>- a person who speaks two languages equally well.</p>",
                    solution_hi: "<p>2.(b) <strong>Multilingual </strong>(बहुभाषी) - proficient in speaking many languages.<br><strong>Monolithic</strong> (विशालकाय) - formed of a single large block of stone.<br><strong>Heterolinguistic </strong>(विषमभाषिक) - pertaining to or characterized by the use of different languages.<br><strong>Bilingual </strong>(द्विभाषी) - a person who speaks two languages equally well.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p>3. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who likes to argue about anything</p>",
                    question_hi: "<p>3. Select the option that can be used as a one-word substitute for the given group of words. <br>A person who likes to argue about anything</p>",
                    options_en: ["<p>Reticent</p>", "<p>Coward</p>", 
                                "<p>Veracious</p>", "<p>Contentious</p>"],
                    options_hi: ["<p>Reticent</p>", "<p>Coward</p>",
                                "<p>Veracious</p>", "<p>Contentious</p>"],
                    solution_en: "<p>3.(d) <strong>Contentious</strong>- a person who likes to argue about anything.<br><strong>Reticent</strong>- unwilling to speak about your thoughts or feelings.<br><strong>Coward</strong>- a person who is not brave, is easily frightened, or tries to avoid danger or difficulties.<br><strong>Veracious</strong>- speaking or representing the truth.</p>",
                    solution_hi: "<p>3.(d) <strong>Contentious </strong>(विवादास्पद) - a person who likes to argue about anything.<br><strong>Reticent </strong>(मितभाषी) - unwilling to speak about your thoughts or feelings.<br><strong>Coward </strong>(कायर) - a person who is not brave, is easily frightened, or tries to avoid danger or difficulties.<br><strong>Veracious </strong>(सत्यप्रिय) - speaking or representing the truth.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p>4. Select the word which means the same as the group of words given. <br>Unit of weight for precious stones</p>",
                    question_hi: "<p>4. Select the word which means the same as the group of words given. <br>Unit of weight for precious stones</p>",
                    options_en: ["<p>Pure</p>", "<p>Reliable</p>", 
                                "<p>Carat</p>", "<p>Accurate</p>"],
                    options_hi: ["<p>Pure</p>", "<p>Reliable</p>",
                                "<p>Carat</p>", "<p>Accurate</p>"],
                    solution_en: "<p>4.(c) <strong>Carat</strong>- unit of weight for precious stones.<br><strong>Pure</strong>- not mixed or adulterated with any other substance or material.<br><strong>Reliable</strong>- able to be trusted.<br><strong>Accurate</strong>- correct, exact, and without any mistakes.</p>",
                    solution_hi: "<p>4.(c) <strong>Carat </strong>(कैरेट) - unit of weight for precious stones.<br><strong>Pure </strong>(शुद्ध) - not mixed or adulterated with any other substance or material.<br><strong>Reliable </strong>(विश्वसनीय) - able to be trusted.<br><strong>Accurate </strong>(सटीक) - correct, exact, and without any mistakes.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p>5. Select the option that can be used as a one-word substitute for the given group of words.<br>One who does not show response to good or bad</p>",
                    question_hi: "<p>5. Select the option that can be used as a one-word substitute for the given group of words.<br>One who does not show response to good or bad</p>",
                    options_en: ["<p>Stoic</p>", "<p>Sceptic</p>", 
                                "<p>Cynic</p>", "<p>Phobic</p>"],
                    options_hi: ["<p>Stoic</p>", "<p>Sceptic</p>",
                                "<p>Cynic</p>", "<p>Phobic</p>"],
                    solution_en: "<p>5.(a) <strong>Stoic</strong>- one who does not show response to good or bad.<br><strong>Sceptic</strong>- a person inclined to question or doubt accepted opinions.<br><strong>Cynic</strong>- a person who believes that people are motivated purely by self-interest rather than acting for honourable or unselfish reasons.<br><strong>Phobic</strong>- having or involving an extreme or irrational fear of or aversion to something.</p>",
                    solution_hi: "<p>5.(a) <strong>Stoic </strong>(उदासीन) - one who does not show response to good or bad.<br><strong>Sceptic </strong>(अविश्वासी) - a person inclined to question or doubt accepted opinions.<br><strong>Cynic </strong>(मानवद्वेषी) - a person who believes that people are motivated purely by self-interest rather than acting for honourable or unselfish reasons.<br><strong>Phobic </strong>(भयग्रस्त) - having or involving an extreme or irrational fear of or aversion to something.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p>6. Select the option that can be used as a one-word substitute for the given group of words. <br>Morbid compulsion to consume alcohol continuously</p>",
                    question_hi: "<p>6. Select the option that can be used as a one-word substitute for the given group of words. <br>Morbid compulsion to consume alcohol continuously</p>",
                    options_en: ["<p>Satyromania</p>", "<p>Maniac</p>", 
                                "<p>Kleptomania</p>", "<p>Dipsomania</p>"],
                    options_hi: ["<p>Satyromania</p>", "<p>Maniac</p>",
                                "<p>Kleptomania</p>", "<p>Dipsomania</p>"],
                    solution_en: "<p>6.(d) <strong>Dipsomania</strong>- morbid compulsion to consume alcohol continuously.<br><strong>Satyromania</strong>- a man\'s excessive and often uncontrollable sexual desire and behavior.<br><strong>Maniac</strong>- a person exhibiting extremely wild or violent behaviour.<br><strong>Kleptomania</strong>- a recurrent urge to steal, typically without regard for need or profit.</p>",
                    solution_hi: "<p>6.(d) <strong>Dipsomania </strong>(शराब की लत)- morbid compulsion to consume alcohol continuously.<br><strong>Satyromania </strong>(अनितंत्रित यौन इच्छा)- a man\'s excessive and often uncontrollable sexual desire and behavior.<br><strong>Maniac </strong>(पागल)- a person exhibiting extremely wild or violent behaviour.<br><strong>Kleptomania </strong>(चोरी करने की लत)- a recurrent urge to steal, typically without regard for need or profit.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7. Select the option that can be used as a one-word substitute for the underlined group of words. <br>The question papers are placed in the lockers of the school in a way that they can&rsquo;t be reached.</p>",
                    question_hi: "<p>7. Select the option that can be used as a one-word substitute for the underlined group of words. <br>The question papers are placed in the lockers of the school in a way that they can&rsquo;t be reached.</p>",
                    options_en: ["<p>are irrepressible</p>", "<p>are inaccessible</p>", 
                                "<p>are affordable</p>", "<p>are affable</p>"],
                    options_hi: ["<p>are irrepressible</p>", "<p>are inaccessible</p>",
                                "<p>are affordable</p>", "<p>are affable</p>"],
                    solution_en: "<p>7.(b) <strong>are inaccessible-</strong> can&rsquo;t be reached.<br><strong>are irrepressible- </strong>not able to be controlled or restrained.<br><strong>are affordable-</strong> reasonably priced.<br><strong>are affable- </strong>friendly, good-natured, or easy to talk to.</p>",
                    solution_hi: "<p>7.(b)<strong> are inaccessible</strong> (दुर्गम)- can&rsquo;t be reached.<br><strong>are irrepressible</strong> (अदम्य )- not able to be controlled or restrained.<br><strong>are affordable</strong> (किफायती)- reasonably priced.<br><strong>are affable</strong> (मैत्रीपूर्ण)- friendly, good-natured, or easy to talk to.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. Select the option that can be used as a one-word substitute for the given group of words. <br>An order of law requiring people to remain indoors</p>",
                    question_hi: "<p>8. Select the option that can be used as a one-word substitute for the given group of words. <br>An order of law requiring people to remain indoors</p>",
                    options_en: ["<p>Restriction</p>", "<p>Curfew</p>", 
                                "<p>Limit</p>", "<p>Check in</p>"],
                    options_hi: ["<p>Restriction</p>", "<p>Curfew</p>",
                                "<p>Limit</p>", "<p>Check in</p>"],
                    solution_en: "<p>8.(b) <strong>Curfew</strong>- an order of law requiring people to remain indoors.<br><strong>Restriction</strong>- an official limit or control on what people or companies are allowed to do, or on what can happen.<br><strong>Limit</strong>- a point or level beyond which something does not or may not extend or pass.<br><strong>Check in- </strong>arrive and register at a hotel or airport.</p>",
                    solution_hi: "<p>8.(b) <strong>Curfew </strong>(कर्फ़्यू/निषेधाज्ञा)- an order of law requiring people to remain indoors.<br><strong>Restriction </strong>(प्रतिबंध)- an official limit or control on what people or companies are allowed to do, or on what can happen.<br><strong>Limit </strong>(सीमा)- a point or level beyond which something does not or may not extend or pass.<br><strong>Check</strong> <strong>in </strong>(आगमन)- arrive and register at a hotel or airport.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who sells and arranges cut flowers.</p>",
                    question_hi: "<p>9. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who sells and arranges cut flowers.</p>",
                    options_en: ["<p>Horticulturist</p>", "<p>Botanist</p>", 
                                "<p>Gardener</p>", "<p>Florist</p>"],
                    options_hi: ["<p>Horticulturist</p>", "<p>Botanist</p>",
                                "<p>Gardener</p>", "<p>Florist</p>"],
                    solution_en: "<p>9.(d) <strong>Florist</strong>- a person who sells and arranges cut flowers.<br><strong>Horticulturist</strong>- a person who studies or grows garden plants.<br><strong>Botanist</strong>- an expert in or student of the scientific study of plants.<br><strong>Gardener</strong>- someone who works in a garden, growing and taking care of plants.</p>",
                    solution_hi: "<p>9.(d) <strong>Florist </strong>(पुष्प-विक्रेता)- a person who sells and arranges cut flowers.<br><strong>Horticulturist </strong>(उद्यान विशेषज्ञ)- a person who studies or grows garden plants.<br><strong>Botanist </strong>(वनस्पति शास्त्री)- an expert in or student of the scientific study of plants.<br><strong>Gardener </strong>(माली)- someone who works in a garden, growing and taking care of plants.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10. Select the option that can be used as a one-word substitute for the given group of words.<br>A small group of people who spend their time together and do not welcome other people into that group</p>",
                    question_hi: "<p>10. Select the option that can be used as a one-word substitute for the given group of words.<br>A small group of people who spend their time together and do not welcome other people into that group</p>",
                    options_en: ["<p>Squad</p>", "<p>Clique</p>", 
                                "<p>Employees</p>", "<p>Lobby</p>"],
                    options_hi: ["<p>Squad</p>", "<p>Clique</p>",
                                "<p>Employees</p>", "<p>Lobby</p>"],
                    solution_en: "<p>10.(b) <strong>Clique- </strong>a small group of people who spend their time together and do not welcome other people into that group.<br><strong>Squad</strong>- a small organized group of military personnel.<br><strong>Employees</strong>- someone who is paid to work for someone else.<br><strong>Lobby</strong>- a group of people who try to persuade the government or an official group to do something.</p>",
                    solution_hi: "<p>10.(b) <strong>Clique </strong>(गुट/गिरोह)- a small group of people who spend their time together and do not welcome other people into that group.<br><strong>Squad </strong>(सैन्य दल)- a small organized group of military personnel.<br><strong>Employees </strong>(कर्मचारी)- someone who is paid to work for someone else.<br><strong>Lobby </strong>(सभाकक्ष)- a group of people who try to persuade the government or an official group to do something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11. Select the option that can be used as a one-word substitute for the given group of words.<br>A place where fruit trees are grown</p>",
                    question_hi: "<p>11. Select the option that can be used as a one-word substitute for the given group of words.<br>A place where fruit trees are grown</p>",
                    options_en: ["<p>Orchard</p>", "<p>Garden</p>", 
                                "<p>Quay</p>", "<p>Museum</p>"],
                    options_hi: ["<p>Orchard</p>", "<p>Garden</p>",
                                "<p>Quay</p>", "<p>Museum</p>"],
                    solution_en: "<p>11.(a) <strong>Orchard- </strong>a place where fruit trees are grown.<br><strong>Garden-</strong> a piece of land, usually near a home, where flowers and other plants are grown.<br><strong>Quay</strong>- a long structure, usually built of stone, where boats can be tied up to take on and off their goods.<br><strong>Museum</strong>- a building in which objects of historical, scientific, artistic, or cultural interest are stored and exhibited.</p>",
                    solution_hi: "<p>11.(a) <strong>Orchard </strong>(फलोद्यान)- a place where fruit trees are grown.<br><strong>Garden </strong>(बगीचा)- a piece of land, usually near a home, where flowers and other plants are grown.<br><strong>Quay </strong>(घाट)- a long structure, usually built of stone, where boats can be tied up to take on and off their goods.<br><strong>Museum </strong>(संग्रहालय)- a building in which objects of historical, scientific, artistic, or cultural interest are stored and exhibited.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12. Select the option that can be used as a one-word substitute for the given group of words.<br>A government by the officials</p>",
                    question_hi: "<p>12. Select the option that can be used as a one-word substitute for the given group of words.<br>A government by the officials</p>",
                    options_en: ["<p>Autocracy</p>", "<p>Bureaucracy</p>", 
                                "<p>Monarchy</p>", "<p>Plutocracy</p>"],
                    options_hi: ["<p>Autocracy</p>", "<p>Bureaucracy</p>",
                                "<p>Monarchy</p>", "<p>Plutocracy</p>"],
                    solution_en: "<p>12.(b) <strong>Bureaucracy</strong>- a government by the officials.<br><strong>Autocracy</strong>- a system of government by one person with absolute power.<br><strong>Monarchy</strong>- a form of government with a monarch at the head.<br><strong>Plutocracy</strong>- a system of government in which the richest people in a country rule or have power.</p>",
                    solution_hi: "<p>12.(b) <strong>Bureaucracy</strong> (नौकरशाही)- a government by the officials.<br><strong>Autocracy </strong>(एकतंत्र)- a system of government by one person with absolute power.<br><strong>Monarchy </strong>(राजतंत्र)- a form of government with a monarch at the head.<br><strong>Plutocracy </strong>(धनिकतंत्र)- a system of government in which the richest people in a country rule or have power.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13. Select the option that can be used as a one word substitute for the given group of words.<br>An often-imaginary place or state of utter perfection and happiness.</p>",
                    question_hi: "<p>13. Select the option that can be used as a one word substitute for the given group of words.<br>An often-imaginary place or state of utter perfection and happiness.</p>",
                    options_en: ["<p>Inferno</p>", "<p>Dystopia</p>", 
                                "<p>Euphoria</p>", "<p>Utopia</p>"],
                    options_hi: ["<p>Inferno</p>", "<p>Dystopia</p>",
                                "<p>Euphoria</p>", "<p>Utopia</p>"],
                    solution_en: "<p>13.(d) <strong>Utopia</strong>- an often-imaginary place or state of utter perfection and happiness.<br><strong>Inferno</strong>- a large fire that is dangerously out of control.<br><strong>Dystopia</strong>- an imagined world or society in which people lead wretched, dehumanized, fearful lives.<br><strong>Euphoria</strong>- a feeling or state of intense excitement and happiness.</p>",
                    solution_hi: "<p>13.(d) <strong>Utopia </strong>(आदर्श राज्य)- an often-imaginary place or state of utter perfection and happiness.<br><strong>Inferno </strong>(भयानक आग)- a large fire that is dangerously out of control.<br><strong>Dystopia</strong> (दुःस्थानता)- an imagined world or society in which people lead wretched, dehumanized, fearful lives.<br><strong>Euphoria</strong> (उत्साह की भावना)- a feeling or state of intense excitement and happiness.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14. select the option that can be used as a one-word substitute for the given group of words.<br>Fear of closed spaces</p>",
                    question_hi: "<p>14. select the option that can be used as a one-word substitute for the given group of words.<br>Fear of closed spaces</p>",
                    options_en: ["<p>Photophobia</p>", "<p>Optophobia</p>", 
                                "<p>Agoraphobia</p>", "<p>Claustrophobia</p>"],
                    options_hi: ["<p>Photophobia</p>", "<p>Optophobia</p>",
                                "<p>Agoraphobia</p>", "<p>Claustrophobia</p>"],
                    solution_en: "<p>14.(d) <strong>Claustrophobia</strong>- fear of closed spaces.<br><strong>Photophobia</strong>- fear of light.<br><strong>Optophobia</strong>- the fear of opening one\'s eyes<br><strong>Agoraphobia</strong>- fear of going outside and being in public places.</p>",
                    solution_hi: "<p>14.(d) <strong>Claustrophobia </strong>(बंद स्थानों का डर)- fear of closed spaces.<br><strong>Photophobia </strong>(प्रकाश का डर)- fear of light.<br><strong>Optophobia </strong>(आंखें खोलने का डर)- the fear of opening one\'s eyes<br><strong>Agoraphobia </strong>(सार्वजनिक स्थानों पर जाने का डर)- fear of going outside and being in public places.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Select the option that can be used as a one-word substitute for the given group of words.<br>a short section (as of a book) that leads to or explains the main part.</p>",
                    question_hi: "<p>15. Select the option that can be used as a one-word substitute for the given group of words.<br>a short section (as of a book) that leads to or explains the main part.</p>",
                    options_en: ["<p>Foreword</p>", "<p>Conclusion</p>", 
                                "<p>Postscript</p>", "<p>Paraphrase</p>"],
                    options_hi: ["<p>Foreword</p>", "<p>Conclusion</p>",
                                "<p>Postscript</p>", "<p>Paraphrase</p>"],
                    solution_en: "<p>15.(a) <strong>Foreword</strong>- a short section (as of a book) that leads to or explains the main part.<br><strong>Conclusion</strong>- the end or finish of an event, process, or text.<br><strong>Postscript</strong>- a short remark or message added to the bottom of a letter after you have signed your name, usually introduced by the abbreviation PS.<br><strong>Paraphrase</strong>- express the meaning of (something written or spoken) using different words, especially to achieve greater clarity.</p>",
                    solution_hi: "<p>15.(a) <strong>Foreword </strong>(प्रस्तावना)- a short section (as of a book) that leads to or explains the main part.<br><strong>Conclusion </strong>(निष्कर्ष)- the end or finish of an event, process, or text.<br><strong>Postscript </strong>(अनुलेख) - a short remark or message added to the bottom of a letter after you have signed your name, usually introduced by the abbreviation PS.<br><strong>Paraphrase </strong>(अर्थ स्पष्ट करना)- express the meaning of (something written or spoken) using different words, especially to achieve greater clarity.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>