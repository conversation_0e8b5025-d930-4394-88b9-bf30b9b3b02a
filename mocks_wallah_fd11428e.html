<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">6:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 6 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["7"] = {
                name: "English",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "7",
                    question_en: "<p>1.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The factory has been<span style=\"text-decoration: underline;\"> _</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">_&nbsp; &nbsp; &nbsp; &nbsp;</span>(</span><span style=\"font-family: Cambria Math;\">exhaling) black smoke from its chimney.</span></p>\n",
                    question_hi: "<p>1.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the bracketed word in the following sentence to fill in the blank.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The factory has been <span style=\"text-decoration: underline;\">_</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">_&nbsp; &nbsp; &nbsp;</span>(</span><span style=\"font-family: Cambria Math;\">exhaling) black smoke from its chimney.</span></p>\n",
                    options_en: ["<p>emitting</p>\n", "<p>preserving</p>\n", 
                                "<p>remitting</p>\n", "<p>indicting</p>\n"],
                    options_hi: ["<p>emitting</p>\n", "<p>preserving</p>\n",
                                "<p>remitting</p>\n", "<p>indicting</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Emit</strong></span><span style=\"font-family: Cambria Math;\"><strong>ting </strong>- </span><span style=\"font-family: Cambria Math;\">producing or discharging something, especially gas or radiation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Exhaling</strong>-</span><span style=\"font-family: Cambria Math;\"> breathing air or smoke out through mouth or nose.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Preserving</strong>-</span><span style=\"font-family: Cambria Math;\"> maintaining something in its original or existing state.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Remitting</strong>-</span><span style=\"font-family: Cambria Math;\"> sending money to a person or place, usually </span><span style=\"font-family: Cambria Math;\">in payment.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Indicting</strong>-</span><span style=\"font-family: Cambria Math;\"> formally accusing someone of a crime.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Emitting </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2313;&#2340;&#2381;&#2360;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - producing or discharging something, especially gas or radiation.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Exhaling</strong> </span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2366;&#2305;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2331;&#2379;&#2337;&#2364;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> breathing air or smoke out through mouth or nose.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Preserving</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2352;</span><span style=\"font-family: Cambria Math;\">&#2325;&#2381;&#2359;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> -</span><span style=\"font-family: Cambria Math;\"> maintaining something in its original or existing state.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Remitting </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2351;&#2350;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2349;&#2369;&#2327;&#2340;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - sending money to a person or place, usually in payment.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Indicting</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2352;&#2379;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2354;&#2327;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - formally accusing someone of a crime.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Diminutive</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">2. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Diminutive</span></p>\n",
                    options_en: ["<p>Miniature</p>\n", "<p>Enormous</p>\n", 
                                "<p>Propaganda</p>\n", "<p>Distinguish</p>\n"],
                    options_hi: ["<p>Miniature</p>\n", "<p>Enormous</p>\n",
                                "<p>Propaganda</p>\n", "<p>Distinguish</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Miniature</strong></span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">something very small, like a tiny version of something bigger.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Diminutive</span></strong><span style=\"font-family: Cambria Math;\">- a word or </span><span style=\"font-family: Cambria Math;\">name that makes something sound</span><span style=\"font-family: Cambria Math;\"> small or cute.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Enormous</span></strong><span style=\"font-family: Cambria Math;\">- extremely, incredibly big or huge.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Propaganda</span></strong><span style=\"font-family: Cambria Math;\">- information that </span><span style=\"font-family: Cambria Math;\">tries to persuade or influence people, often with a hidden agenda.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Distinguish</span></strong><span style=\"font-family: Cambria Math;\">- to notice what makes something or someone special or different from others.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Miniature </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2354;&#2328;&#2369;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">something very small, like a tiny version of something bigger.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Diminu</span><span style=\"font-family: Cambria Math;\">tive</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2331;&#2379;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\">) - a word or name that makes something sound small or cute.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Enormous </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- extremely, incredibly big or huge.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Propaganda </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2330;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- information that tries to persuade or influence people, often with a hidden agenda.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Distinguish </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2306;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> to notice what makes something or someone special or different from others.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The teacher was accused of </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">brazen</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">favouritism</span><span style=\"font-family: Cambria Math;\"> in assigning the grades.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">3. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The teacher was accused of </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">brazen</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">favouritism</span><span style=\"font-family: Cambria Math;\"> in assigning the grades.</span></p>\n",
                    options_en: ["<p>Modest</p>\n", "<p>Shy</p>\n", 
                                "<p>Unashamed</p>\n", "<p>Ashamed</p>\n"],
                    options_hi: ["<p>Modest</p>\n", "<p>Shy</p>\n",
                                "<p>Unashamed</p>\n", "<p>Ashamed</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Unashamed</strong></span><span style=\"font-family: Cambria Math;\">- feeling no guilt, embarr</span><span style=\"font-family: Cambria Math;\">assment, or regret for one\'s actions.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Brazen</span></strong><span style=\"font-family: Cambria Math;\">- bold, shameless, or audacious, often to the point of being disrespectful.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Modest</span></strong><span style=\"font-family: Cambria Math;\">- having a humble and unassuming attitude or appearance.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Shy</strong>-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">one who</span><span style=\"font-family: Cambria Math;\"> feels nervous or uncomfortable socially.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Ashamed</span></strong><span style=\"font-family: Cambria Math;\">- feeling e</span><span style=\"font-family: Cambria Math;\">mbarrassed, guilty, or regretful about something one has done.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Unashamed </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2381;&#2354;&#2332;&#2381;&#2332;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- feeling no guilt, embarrassment, or regret for one\'s actions.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Brazen </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2352;&#2381;&#2354;&#2332;&#2381;&#2332;</span><span style=\"font-family: Cambria Math;\">) - bold, shameless, or audacious, often to the point of being disrespectful.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">M</span><span style=\"font-family: Cambria Math;\">odest </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2350;&#2366;&#2350;&#2370;&#2354;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- having a humble and unassuming attitude or appearance.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Shy</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2306;&#2325;&#2379;&#2330;&#2368;</span><span style=\"font-family: Cambria Math;\">) - </span><span style=\"font-family: Cambria Math;\">one who</span><span style=\"font-family: Cambria Math;\"> feels nervous or uncomfortable socially.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Ashamed</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2358;&#2352;&#2381;&#2350;&#2367;&#2306;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\">) - feeling embarrassed, guilty, or regretful about something one has done.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The ruins of this building do tell us the story of a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">defunct</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">organisation</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">4. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate </span><span style=\"font-family: Cambria Math;\">synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The ruins of this building do tell us the story of a </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">defunct</span></span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">organisation</span><span style=\"font-family: Cambria Math;\">.</span></p>\n",
                    options_en: ["<p>obsolete</p>\n", "<p>ancient</p>\n", 
                                "<p>contemporary</p>\n", "<p>prominent</p>\n"],
                    options_hi: ["<p>obsolete</p>\n", "<p>ancient</p>\n",
                                "<p>contemporary</p>\n", "<p>prominent</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Obsolete</strong>- </span><span style=\"font-family: Cambria Math;\">no longer in use or outdated.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Def</span><span style=\"font-family: Cambria Math;\">unct</span></strong><span style=\"font-family: Cambria Math;\">- no longer functioning, operational, or in existence.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Ancient</span></strong><span style=\"font-family: Cambria Math;\">- extremely old or belonging to a distant past.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Contemporary</span></strong><span style=\"font-family: Cambria Math;\">- relating to the current or modern time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Prominent</span></strong><span style=\"font-family: Cambria Math;\">- well-known, notable, or widely recognized.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Obsolete</strong></span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2381;&#2352;&#2330;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> no longer in use or outdated.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Defunct </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2381;&#2352;&#2330;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - no longer functioning, operational, or in existence.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Ancient </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2330;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\">) - extremely old or belonging to a distant past.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Contemporary </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2350;&#2325;&#2366;&#2354;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\">) - relating to the current or modern time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Prominent </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2350;&#2369;&#2326;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- well-known, notable, or widely recognized.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Camouflage</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">5.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Camouflage</span></p>\n",
                    options_en: ["<p>Sabotage</p>\n", "<p>Execute</p>\n", 
                                "<p>Gigantic</p>\n", "<p>Disguise</p>\n"],
                    options_hi: ["<p>Sabot<span style=\"font-family: Cambria Math;\">age</span></p>\n", "<p>Execute</p>\n",
                                "<p>Gigantic</p>\n", "<p>Disguise</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d) </span><strong><span style=\"font-family: Cambria Math;\">Disguise</span></strong><span style=\"font-family: Cambria Math;\">- to conceal or hide one\'s true identity or appearance.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Camouflage</span></strong><span style=\"font-family: Cambria Math;\">- use of colors or patterns to blend in or mix with the surroundings to hide.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Sabotage</span></strong><span style=\"font-family: Cambria Math;\">- intentionally causing harm,</span><span style=\"font-family: Cambria Math;\"> damage, or disruption to something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Execute</span></strong><span style=\"font-family: Cambria Math;\">- to carry out or perform a task, plan, or action.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Gigantic</span></strong><span style=\"font-family: Cambria Math;\">- extremely large in size.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">d) </span><strong><span style=\"font-family: Cambria Math;\">Disguise</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2349;&#2375;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2342;&#2354;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to conceal or hide one\'s true identity or appearance.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Camouflage </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2331;&#2354;&#2366;&#2357;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">) - use of colors or patterns to blend in or mix with the surroundings to hide.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Sabotage </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2369;&#2325;&#2364;&#2360;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2361;&#2369;&#2306;&#2330;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - intentionally causing harm, damage, or disruption to something.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Execute </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2359;&#2381;&#2346;&#2366;&#2342;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- to carry out or perform a task, plan, or action.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Gigantic </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2358;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- extremely large in size.</span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "7",
                    question_en: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Conspicuous</span></p>\n",
                    question_hi: "<p><span style=\"font-family: Cambria Math;\">6. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Conspicuous</span></p>\n",
                    options_en: ["<p>Opaque</p>\n", "<p>Unnoticeable</p>\n", 
                                "<p>Obvious</p>\n", "<p>Effective</p>\n"],
                    options_hi: ["<p><span style=\"font-family: Cambria Math;\">Opaque</span></p>\n", "<p>Unnoticeable</p>\n",
                                "<p>Obvious</p>\n", "<p>Effective</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Obvious</strong></span><span style=\"font-family: Cambria Math;\">- easily seen, understood, or recognized; clear.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Conspicuous</span></strong><span style=\"font-family: Cambria Math;\">- clearly visible or noticeable.</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Opaque</span></strong><span style=\"font-family: Cambria Math;\">- not transparent or clear.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Unnoticeable</span></strong><span style=\"font-family: Cambria Math;\">- not easily noticed or visible.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Effective</span></strong><span style=\"font-family: Cambria Math;\">- successful in producing a desired or intended result.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Obvious </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- easily seen, understood, or recognized; clear.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Conspicuous </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2369;&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">) - clearly visible or noticeable.</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Opaque </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">- not transparent or clear.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Unnoticeable</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2343;&#2381;&#2351;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2351;&#2379;&#2327;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\">) - not easily noticed or visible.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Effective</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2349;&#2366;&#2357;&#2368;</span><span style=\"font-family: Cambria Math;\">) - successful in producing a desired or intended result.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "7",
                    question_en: "<p>7.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym for the underlined word in the given sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">We cannot achieve suc</span><span style=\"font-family: Cambria Math;\">cess until these </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">antiquated</span></span><span style=\"font-family: Cambria Math;\"> policies are not amended. </span></p>\n",
                    question_hi: "<p>7.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym for the underlined word in the given sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">We cannot achieve success until these </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">antiquated</span></span><span style=\"font-family: Cambria Math;\"> policies </span><span style=\"font-family: Cambria Math;\">are not amended. </span></p>\n",
                    options_en: ["<p>Outdated</p>\n", "<p>Current</p>\n", 
                                "<p>New</p>\n", "<p>Updated</p>\n"],
                    options_hi: ["<p>Outdated</p>\n", "<p>Current</p>\n",
                                "<p>New</p>\n", "<p>Updated</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Outdated</strong>- </span><span style=\"font-family: Cambria Math;\">old-fashioned and no longer useful.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Antiquated</strong>- </span><span style=\"font-family: Cambria Math;\">old-fashioned or outdated.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Current</strong>- </span><span style=\"font-family: Cambria Math;\">belonging to the present time.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>New</strong>- </span><span style=\"font-family: Cambria Math;\">produced, introduced or discovered recently.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Updated</strong>- </span><span style=\"font-family: Cambria Math;\">having new parts or new information.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">a) <strong>Outdated </strong></span><span style=\"font-family: Cambria Math;\">( </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2352;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2381;&#2352;&#2330;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) - old-fashioned and no longer useful.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Antiquated </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2346;&#2381;&#2352;&#2330;&#2354;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> old-fashioned or outdated.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Current </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2352;&#2381;&#2340;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">) - belonging to the</span><span style=\"font-family: Cambria Math;\"> present time.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">New </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2357;&#2368;&#2344;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2344;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> produced, introduced or discovered recently.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Updated </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2357;&#2368;&#2344;&#2368;&#2325;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">having new parts or new information.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "7",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The doctors say that ma</span><span style=\"font-family: Cambria Math;\">jority of the people who attended the fest are ill due to eating of </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">adulterated</span> </span><span style=\"font-family: Cambria Math;\">food.</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The doctors say that ma</span><span style=\"font-family: Cambria Math;\">jority of the people who attended the fest are ill due to eating of </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">adulterated</span> </span><span style=\"font-family: Cambria Math;\">food.</span></p>\n",
                    options_en: ["<p>spicy</p>\n", "<p>lascivious</p>\n", 
                                "<p>contaminated</p>\n", "<p>damp</p>\n"],
                    options_hi: ["<p>spicy</p>\n", "<p>lascivious</p>\n",
                                "<p>contaminated</p>\n", "<p>damp</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Contaminated</strong>- </span><span style=\"font-family: Cambria Math;\">having been made impure by addition of a poisonous or polluting substance.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Adulterated</strong>- </span><span style=\"font-family: Cambria Math;\">lessened in purity by the addition of an impure substance.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Spicy</strong>- </span><span style=\"font-family: Cambria Math;\">having the quality, </span><span style=\"font-family: Cambria Math;\">flavour</span><span style=\"font-family: Cambria Math;\"> or fragrance of spice.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Lascivious</strong>- </span><span style=\"font-family: Cambria Math;\">expressing a strong desire for sexual activity.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Damp</strong>- </span><span style=\"font-family: Cambria Math;\">slightly wet.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Contaminated </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2370;&#2359;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">having been made impure by addition of a poisonous or polluting substance.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Adulterated</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2350;&#2367;&#2354;&#2366;&#2357;&#2335;&#2368;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">lessened in purity by the addition of an impure substance.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Spicy </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2360;&#2381;&#2357;&#2366;&#2342;&#2367;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">having the quality, </span><span style=\"font-family: Cambria Math;\">flavour</span><span style=\"font-family: Cambria Math;\"> or fragrance of spice.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Lascivious </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2366;&#2350;&#2369;&#2325;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">expres</span><span style=\"font-family: Cambria Math;\">sing a strong desire for sexual activity.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Damp </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2350;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Cambria Math;\">&#2327;&#2368;&#2354;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">slightly wet.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "7",
                    question_en: "<p>9.<span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Worn</span></p>\n",
                    question_hi: "<p>9.<span style=\"font-family: Cambria Math;\"> Select the most approp</span><span style=\"font-family: Cambria Math;\">riate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Worn</span></p>\n",
                    options_en: ["<p>Dilemma</p>\n", "<p>Energetic</p>\n", 
                                "<p>Dilapidated</p>\n", "<p>Diminutive</p>\n"],
                    options_hi: ["<p>Dilemma</p>\n", "<p>Energetic</p>\n",
                                "<p>Dilapidated</p>\n", "<p>Diminutive</p>\n"],
                    solution_en: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c) </span><strong><span style=\"font-family: Cambria Math;\">Dilapidated</span></strong><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">in a state of disrepair or ruin due to age or neglect.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Worn</strong>- </span><span style=\"font-family: Cambria Math;\">damaged and dirty as a result of much use.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Dilemma</strong>- </span><span style=\"font-family: Cambria Math;\">a situation in which a difficult choice has to be made between two or more alternatives.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Energetic</strong>- </span><span style=\"font-family: Cambria Math;\">showing or involving great activity.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Diminutive</strong>- </span><span style=\"font-family: Cambria Math;\">extremely or unusually small.</span></p>\n",
                    solution_hi: "<p><span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c) <strong>Dilapidated </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2332;&#2352;&#2381;&#2332;&#2352;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">in a state of disrepair or ruin due to age or neglect.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Worn </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2347;&#2335;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2346;&#2369;&#2352;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">damaged and dirty as a result of much use.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Dilemma </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2357;&#2367;&#2343;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a situation in which a difficult choice has to be made between two or more alternatives.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Energetic </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2314;&#2352;&#2381;&#2332;&#2366;&#2357;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">showing</span><span style=\"font-family: Cambria Math;\"> or involving great activity.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Diminutive </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Cambria Math;\">&#2354;&#2328;&#2369;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">extremely or unusually small.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "7",
                    question_en: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word from the following sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Magical </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The stories of The Arabian Nights are enchanting, interesting, vivaciou</span><span style=\"font-family: Cambria Math;\">s, and natural.</span></p>\n",
                    question_hi: "<p>10<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word from the following sentence. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Magical&nbsp; </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The stories of The Arabian Nights are enchanting, interesting, vivacious, an</span><span style=\"font-family: Cambria Math;\">d natural.</span></p>\n",
                    options_en: ["<p>enchanting</p>\n", "<p>Interesting</p>\n", 
                                "<p>natural</p>\n", "<p>vivacious</p>\n"],
                    options_hi: ["<p>enchanting</p>\n", "<p>Interesting</p>\n",
                                "<p>natural</p>\n", "<p>vivacious</p>\n"],
                    solution_en: "<p>10<span style=\"font-family: Cambria Math;\">.(a) <strong>Enchanting</strong>-</span><span style=\"font-family: Cambria Math;\"> delightfully charming or attractive.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Magical</strong>- </span><span style=\"font-family: Cambria Math;\">beautiful or delightful in a way that seems removed from everyday life.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Interesting</strong>- </span><span style=\"font-family: Cambria Math;\">holding or catching the attention.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Natural</strong>- </span><span style=\"font-family: Cambria Math;\">existing in or produced by nature.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Vivacious</strong>- </span><span style=\"font-family: Cambria Math;\">extremely lively and animated.</span></p>\n",
                    solution_hi: "<p>10<span style=\"font-family: Cambria Math;\">.(a) <strong>Enchanting</strong></span><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2342;&#2381;&#2349;&#2369;&#2340;</span><span style=\"font-family: Cambria Math;\">) - delightfully charming or attractive.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Magical </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2332;&#2366;&#2342;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\">) - beautiful or delightful in a way that seems </span><span style=\"font-family: Cambria Math;\">removed from everyday life.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Interesting</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2367;&#2354;&#2330;&#2360;&#2381;&#2346;</span><span style=\"font-family: Cambria Math;\">) - holding or catching the attention.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Natural </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2366;&#2325;&#2371;&#2340;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\">) - existing in or produced by nature.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Vivacious </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2332;&#2368;&#2357;&#2344;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> extremely lively and animated.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "7",
                    question_en: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined</span><span style=\"font-family: Cambria Math;\"> word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">deteriorated</span> </span><span style=\"font-family: Cambria Math;\">building was declared unsafe for the inmates.</span></p>\n",
                    question_hi: "<p>11<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the underlined word in the given sentence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">The </span><span style=\"text-decoration: underline;\"><span style=\"font-family: Cambria Math;\">de</span></span><span style=\"font-family: Cambria Math;\"><span style=\"text-decoration: underline;\">teriorated</span> </span><span style=\"font-family: Cambria Math;\">building was declared unsafe for the inmates.</span></p>\n",
                    options_en: ["<p>dilapidated</p>\n", "<p>dissipated</p>\n", 
                                "<p>meliorated</p>\n", "<p>reprobated</p>\n"],
                    options_hi: ["<p>dilapidated</p>\n", "<p>dissipated</p>\n",
                                "<p>meliorated</p>\n", "<p>reprobated</p>\n"],
                    solution_en: "<p>11<span style=\"font-family: Cambria Math;\">.(a) <strong>Dilapidated</strong>- </span><span style=\"font-family: Cambria Math;\">old and in poor condition.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Deteriorated</strong>- </span><span style=\"font-family: Cambria Math;\">inferior in quality or value.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Dissipated</strong>- </span><span style=\"font-family: Cambria Math;\">gradually dis</span><span style=\"font-family: Cambria Math;\">appeared.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Meliorated</strong>- </span><span style=\"font-family: Cambria Math;\">improved or made better.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Reprobated</strong>- </span><span style=\"font-family: Cambria Math;\">strongly condemned or rejected by others.</span></p>\n",
                    solution_hi: "<p>11<span style=\"font-family: Cambria Math;\">.(a) <strong>Dilapidated </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2332;&#2352;&#2381;&#2332;&#2352;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">old and in poor condition.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Deteriorated </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2352;&#2381;&#2348;&#2354;</span><span style=\"font-family: Cambria Math;\">)-</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">inferior in quality or value.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Dissipated </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2354;&#2369;&#2346;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">gradually disappeared.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Meliorated </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2348;&#2375;&#2361;&#2340;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2348;&#2344;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">improved or made better.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Reprobated</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2344;&#2367;&#2306;&#2342;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">strongly condemned or rejected by others.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "7",
                    question_en: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Requirement</span></p>\n",
                    question_hi: "<p>12<span style=\"font-family: Cambria Math;\">.</span><span style=\"font-family: Cambria Math;\"> Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Requirement</span></p>\n",
                    options_en: ["<p>Essential</p>\n", "<p>Serious</p>\n", 
                                "<p>Vague</p>\n", "<p>Importance</p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n"],
                    options_hi: ["<p>Essential</p>\n", "<p>Serious</p>\n",
                                "<p>Vague</p>\n", "<p>Importance</p>\n"],
                    solution_en: "<p>12<span style=\"font-family: Cambria Math;\">.(a) <strong>Essential</strong>- </span><span style=\"font-family: Cambria Math;\">absolutely necessary.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Requirement</strong>- </span><span style=\"font-family: Cambria Math;\">a thing that is needed or wanted.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Serious</strong>- </span><span style=\"font-family: Cambria Math;\">requiring much thought or work.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Vague</strong>- </span><span style=\"font-family: Cambria Math;\">not clearly expressed.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Importance</strong>-</span><span style=\"font-family: Cambria Math;\"> the state of being valuable.</span></p>\n",
                    solution_hi: "<p>12<span style=\"font-family: Cambria Math;\">.(a) <strong>Essential </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Cambria Math;\">- </span><span style=\"font-family: Cambria Math;\">absolutely necessary.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Requirement </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) - a thing that is needed or wanted.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Serious </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2327;&#2306;&#2349;&#2368;&#2352;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">requiring much thou</span><span style=\"font-family: Cambria Math;\">ght or work.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Vague </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2346;&#2359;&#2381;&#2335;</span><span style=\"font-family: Cambria Math;\">) - not clearly expressed.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Importance</span></strong><span style=\"font-family: Cambria Math;\"><strong> </strong>(</span><span style=\"font-family: Cambria Math;\">&#2350;&#2361;&#2340;&#2381;&#2340;&#2381;&#2357;</span><span style=\"font-family: Cambria Math;\">) - the state of being valuable.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "7",
                    question_en: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Callous</span></p>\n",
                    question_hi: "<p>13<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">S</span><span style=\"font-family: Cambria Math;\">elect the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Callous</span></p>\n",
                    options_en: ["<p>Inordinate</p>\n", "<p>Compassionate</p>\n", 
                                "<p>Insensitive</p>\n", "<p>Unshackle</p>\n"],
                    options_hi: ["<p>Inordinate</p>\n", "<p>Compassionate</p>\n",
                                "<p>Insensitive</p>\n", "<p>Unshackle</p>\n"],
                    solution_en: "<p>13<span style=\"font-family: Cambria Math;\">.(c) <strong>Insensitive</strong>- </span><span style=\"font-family: Cambria Math;\">showing no concern for others&rsquo; feelings.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Callous</strong>- </span><span style=\"font-family: Cambria Math;\">having an insensitive and cruel disregard for others.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Inordinate</strong>- </span><span style=\"font-family: Cambria Math;\">much more than usual or expected.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Compassionate</strong>- </span><span style=\"font-family: Cambria Math;\">showing pity, sympathy and understanding for feelings of other people.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Unshackle</strong>- </span><span style=\"font-family: Cambria Math;\">to set free.</span></p>\n",
                    solution_hi: "<p>13<span style=\"font-family: Cambria Math;\">.(c) <strong>Insensitive </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2306;&#2357;&#2375;&#2342;&#2344;&#2358;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\">) - showi</span><span style=\"font-family: Cambria Math;\">ng no concern for others&rsquo; feelings.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Callous </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2306;&#2357;&#2375;&#2342;&#2344;&#2358;&#2368;&#2354;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">having an insensitive and cruel disregard for others.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Inordinate </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2366;&#2343;&#2366;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">) - much more than usual or expected.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Compassionate </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2369;&#2339;&#2366;&#2350;&#2351;</span><span style=\"font-family: Cambria Math;\">) - showing pity, sympathy and understanding for feelings of other people.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Unshackle </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2348;&#2306;&#2343;&#2344;&#2350;&#2369;&#2325;&#2381;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to set free.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "7",
                    question_en: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Deterioration</span></p>\n",
                    question_hi: "<p>14<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Deterioration</span></p>\n",
                    options_en: ["<p>Retrogression</p>\n", "<p>Demonstration</p>\n", 
                                "<p>Malfunction</p>\n", "<p>Ramification</p>\n"],
                    options_hi: ["<p>Retrogression</p>\n", "<p>Demonstration</p>\n",
                                "<p>Malfunction</p>\n", "<p>Ramification</p>\n"],
                    solution_en: "<p>14<span style=\"font-family: Cambria Math;\">.(a) <strong>Retrogression</strong>- </span><span style=\"font-family: Cambria Math;\">the process of returning to an earlier state, typically a worse one.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Deterioration</strong>- </span><span style=\"font-family: Cambria Math;\">the process of becoming progressively worse.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Demonstration</strong>- </span><span style=\"font-family: Cambria Math;\">an act of showing that something exists or is true by giving proof or evidence.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Malfunction</strong>- </span><span style=\"font-family: Cambria Math;\">a failure to functi</span><span style=\"font-family: Cambria Math;\">on normally.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Ramification</strong>- </span><span style=\"font-family: Cambria Math;\">a complex or unwelcome consequence of an action or event.</span></p>\n",
                    solution_hi: "<p>14<span style=\"font-family: Cambria Math;\">.(a) <strong>Retrogression </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2342;&#2369;&#2352;&#2381;&#2327;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">the process of returning to an earlier state, typically a worse one.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Deterioration </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2340;&#2344;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">the process of becoming progressively wo</span><span style=\"font-family: Cambria Math;\">rse.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Demonstration </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2346;&#2381;&#2352;&#2350;&#2366;&#2339;&#2368;&#2325;&#2352;&#2339;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">an act of showing that something exists or is true by giving proof or evidence.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Malfunction </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2347;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) - a failure to function normally.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Ramification </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2332;&#2335;&#2367;&#2354;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\">) - a complex or unwelcome consequence of an action or event.</span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Objection</span></p>\n",
                    question_hi: "<p>15<span style=\"font-family: Cambria Math;\">. </span><span style=\"font-family: Cambria Math;\">Select the most appropriate synonym of the given word.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Objection</span></p>\n",
                    options_en: ["<p>Moan</p>\n", "<p>Disapproval</p>\n", 
                                "<p>Counter</p>\n", "<p>Sanction</p>\n"],
                    options_hi: ["<p>Moan</p>\n", "<p>Disapproval</p>\n",
                                "<p>Counter</p>\n", "<p>Sanction</p>\n"],
                    solution_en: "<p>15<span style=\"font-family: Cambria Math;\">.(b) <strong>Disapproval </strong>- </span><span style=\"font-family: Cambria Math;\">expression of an </span><span style=\"font-family: Cambria Math;\">unfavourable</span><span style=\"font-family: Cambria Math;\"> opinion.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Objection </strong>- </span><span style=\"font-family: Cambria Math;\">an expression or feeling of disapproval.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Moan </strong>- </span><span style=\"font-family: Cambria Math;\">a long, low sound made by a person expressing physical or mental suffering or sexual pleasure.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Counter </strong>- </span><span style=\"font-family: Cambria Math;\">to react to somet</span><span style=\"font-family: Cambria Math;\">hing with an opposing opinion.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\"><strong>Sanction </strong>- </span><span style=\"font-family: Cambria Math;\">official permission or approval for an action.</span></p>\n",
                    solution_hi: "<p>15<span style=\"font-family: Cambria Math;\">.(b) <strong>Disapproval </strong></span><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2360;&#2381;&#2357;&#2368;&#2325;&#2371;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">) - expression of an </span><span style=\"font-family: Cambria Math;\">unfavourable</span><span style=\"font-family: Cambria Math;\"> opinion.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Objection </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2310;&#2346;&#2340;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">an expression or feeling of disapproval.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Moan </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2366;&#2361;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) -</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">a long, lo</span><span style=\"font-family: Cambria Math;\">w sound made by a person expressing physical or mental suffering or sexual pleasure.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Counter </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2357;&#2367;&#2352;&#2379;&#2343;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - to react to something with an opposing opinion.</span></p>\r\n<p><strong><span style=\"font-family: Cambria Math;\">Sanction </span></strong><span style=\"font-family: Cambria Math;\">(</span><span style=\"font-family: Cambria Math;\">&#2309;&#2343;&#2367;&#2325;&#2371;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">&#2325;&#2352;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\">) - official permission or approval for an action.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>