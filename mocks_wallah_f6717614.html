<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets?&nbsp;(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)&nbsp;<br>9 &ndash; 27 &ndash; 31 &ndash; 36; 10 &ndash; 30 &ndash; 34 &ndash; 39</p>",
                    question_hi: "<p>1. नीचे संख्याओं के दो समुच्चय दिए गए हैं। संख्याओं के प्रत्येक समुच्चय में, पहली संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर दूसरी संख्या प्राप्त होती है। इसी प्रकार, दूसरी संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर तीसरी संख्या प्राप्त होती है और इसी तरह आगे भी होती है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए समुच्चयों की तरह संक्रियाओं के समान समुच्चय का अनुसरण करता है?&nbsp;(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 लीजिए - 13 पर की जाने वाली संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि केवल 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना, तथा 1 और 3 पर गणितीय संक्रिया करने की अनुमति नहीं है।)<br>9 &ndash; 27 &ndash; 31 &ndash; 36; 10 &ndash; 30 &ndash; 34 &ndash; 39</p>",
                    options_en: ["<p>8 &ndash; 24 &ndash; 28 &ndash; 35</p>", "<p>15 &ndash; 45 &ndash; 50 &ndash; 55</p>", 
                                "<p>11 &ndash; 33 &ndash; 38 &ndash; 44</p>", "<p>5 &ndash; 15 &ndash; 19 &ndash; 24</p>"],
                    options_hi: ["<p>8 &ndash; 24 &ndash; 28 &ndash; 35</p>", "<p>15 &ndash; 45 &ndash; 50 &ndash; 55</p>",
                                "<p>11 &ndash; 33 &ndash; 38 &ndash; 44</p>", "<p>5 &ndash; 15 &ndash; 19 &ndash; 24</p>"],
                    solution_en: "<p>1.(d)<strong> Logic:</strong> 1st number &times; 3 = 2nd number, 2nd number + 4 = 3rd number, <br>3rd number + 5 = 4th number.<br>9 &ndash; 27 &ndash; 31 &ndash; 36 :- 9 &times; 3 = 27 , 27 + 4 = 31, 31 +5 = 36<br>10 &ndash; 30 &ndash; 34 &ndash; 39 :- 10 &times; 3 = 30, 30 + 4 = 34, 34 + 5= 39<br>Similarly<br>5 &ndash; 15 &ndash; 19 &ndash; 24 :- 5 &times; 3 = 15, 15 + 4= 19, 19 + 5 = 24</p>",
                    solution_hi: "<p>1.(d) <strong>तर्क: </strong>पहली संख्या &times; 3 = दूसरी संख्या, दूसरी संख्या + 4 = तीसरी संख्या, <br>तीसरी संख्या + 5 = चौथी संख्या.<br>9 &ndash; 27 &ndash; 31 &ndash; 36 :- 9 &times; 3 = 27 , 27 + 4 = 31, 31 +5 = 36<br>10 &ndash; 30 &ndash; 34 &ndash; 39 :- 10 &times; 3 = 30, 30 + 4 = 34, 34 + 5= 39<br>इसी प्रकार <br>5 &ndash; 15 &ndash; 19 &ndash; 24 :- 5 &times; 3 = 15, 15 + 4= 19, 19 + 5 = 24</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In this question, three statements are given, followed by three conclusions numbered I, Il and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All computers are printers.<br>Some printers are laptops.<br>All laptops are mobiles.<br><strong>Conclusions :</strong><br>I. Some printers are computers.<br>II. Some laptops are printers.<br>III. Some mobiles are laptops.</p>",
                    question_hi: "<p>2. इस प्रश्न में, तीन कथन और उसके बाद तीन निष्कर्ष क्रमांक।, ॥ और II। दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/ हैं।<br><strong>कथन :</strong><br>सभी कंप्यूटर, प्रिंटर हैं।<br>कुछ प्रिंटर, लैपटॉप हैं।<br>सभी लैपटॉप, मोबाइल हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ प्रिंटर, कंप्यूटर हैं।<br>II. कुछ लैपटॉप, प्रिंटर हैं।<br>III. कुछ मोबाइल, लैपटॉप हैं।</p>",
                    options_en: ["<p>Only conclusion III follows.</p>", "<p>Only conclusions II and III follow.</p>", 
                                "<p>All the conclusions follow.</p>", "<p>Only conclusions I and III follow.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष III अनुसरण करता है।</p>", "<p>केवल निष्कर्ष II और III अनुसरण करते हैं।</p>",
                                "<p>सभी निष्कर्ष अनुसरण करते हैं।</p>", "<p>केवल निष्कर्ष। और III अनुसरण करते हैं।</p>"],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798775659.png\" alt=\"rId4\" width=\"299\" height=\"140\"><br>All the conclusions follow.</p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798775813.png\" alt=\"rId5\" width=\"309\" height=\"140\"><br>सभी निष्कर्ष अनुसरण करते हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Which of the following numbers will replace the question mark (?) in the given series? <br>101, 103, 107, 109, ?, 127, 131, 137</p>",
                    question_hi: "<p>3. निम्नलिखित में से कौन-सी संख्या, दी गई श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आएगी?<br>101, 103, 107, 109, ?, 127, 131, 137</p>",
                    options_en: ["<p>113</p>", "<p>111</p>", 
                                "<p>123</p>", "<p>125</p>"],
                    options_hi: ["<p>113</p>", "<p>111</p>",
                                "<p>123</p>", "<p>125</p>"],
                    solution_en: "<p>3.(a) <strong>Logic :-</strong> Series of Prime number<br>101, 103, 107, 109, 113, 127, 131 , 137</p>",
                    solution_hi: "<p>3.(a) <strong>तर्क :- </strong>अभाज्य संख्या की श्रृंखला<br>101, 103, 107, 109, 113, 127, 131 , 137</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the mother of B&rsquo;;<br>&lsquo;A &minus; B&rsquo; means &lsquo;A is the brother of B&rsquo;;<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo; and<br>&lsquo;A &divide; B&rsquo; means &lsquo;A is the father of B&rsquo;.<br>Based on the above, how is 5 related to 3 if &lsquo;5 &minus; 2 &times; 4 &divide; 3&rsquo; ?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में,<br>\'A + B\' का अर्थ है \'A, B की माता है\';<br>\'A &minus; B\' का अर्थ है \'A, B का भाई है\';<br>\'A &times; B\' का अर्थ है \'A, B की पत्नी है\' और<br>\'A &divide; B\' का अर्थ है \'A, B का पिता है\'।<br>उपर्युक्त के आधार पर, यदि \'5 &minus; 2 &times; 4 &divide; 3\' है, तो 5, 3 से किस प्रकार संबंधित है?</p>",
                    options_en: ["<p>Father&rsquo;s sister</p>", "<p>Mother&rsquo;s brother</p>", 
                                "<p>Father&rsquo;s brother</p>", "<p>Mother&rsquo;s sister</p>"],
                    options_hi: ["<p>पिता की बहन</p>", "<p>माता का भाई</p>",
                                "<p>पिता का भाई</p>", "<p>माता की बहन</p>"],
                    solution_en: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798775913.png\" alt=\"rId6\" width=\"184\" height=\"120\"><br>5 is the brother of 3&rsquo;s mother.</p>",
                    solution_hi: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798775913.png\" alt=\"rId6\" width=\"184\" height=\"120\"><br>5, 3 की माता का भाई है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a certain code language, \'MAP\' is written as \'102\' and \'ROT\' is written as \'56\'. How will \'SEC\' be written in that language?</p>",
                    question_hi: "<p>5. एक निश्चित कूट भाषा में \'MAP\' को \'102\' और \'ROT\' को \'56\' के रूप में लिखा जाता है। उस भाषा में \'SEC\' कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>108</p>", "<p>98</p>", 
                                "<p>84</p>", "<p>112</p>"],
                    options_hi: ["<p>108</p>", "<p>98</p>",
                                "<p>84</p>", "<p>112</p>"],
                    solution_en: "<p>5.(a)<strong> Logic :-</strong> (Sum of the place value of opposite letter)&times; 2 <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776031.png\" alt=\"rId7\" width=\"127\" height=\"160\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776140.png\" alt=\"rId8\" width=\"111\" height=\"160\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776242.png\" alt=\"rId9\" width=\"118\" height=\"160\"></p>",
                    solution_hi: "<p>5.(a) <strong>तर्क :- </strong>(विपरीत अक्षर के स्थानीय मान का योग)&times; 2<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776357.png\" alt=\"rId10\" width=\"115\" height=\"160\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776650.png\" alt=\"rId11\" width=\"104\" height=\"161\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776242.png\" alt=\"rId9\" width=\"118\" height=\"160\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Select the correct mirror image of the given figure when the mirror is placed at MN.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776754.png\" alt=\"rId12\" width=\"108\" height=\"90\"></p>",
                    question_hi: "<p>6. जब दर्पण को MN पर रखा जाता हो तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776754.png\" alt=\"rId12\" width=\"108\" height=\"90\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776867.png\" alt=\"rId13\" width=\"115\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776968.png\" alt=\"rId14\" width=\"116\" height=\"20\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777078.png\" alt=\"rId15\" width=\"115\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777215.png\" alt=\"rId16\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776867.png\" alt=\"rId13\" width=\"115\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798776968.png\" alt=\"rId14\" width=\"116\" height=\"20\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777078.png\" alt=\"rId15\" width=\"115\" height=\"20\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777215.png\" alt=\"rId16\" width=\"136\" height=\"20\"></p>"],
                    solution_en: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777078.png\" alt=\"rId15\" width=\"126\" height=\"22\"></p>",
                    solution_hi: "<p>6.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777078.png\" alt=\"rId15\" width=\"126\" height=\"22\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Which letter cluster will replace the question mark (?) to complete the given series?<br>RDLS, VKBF, ?, DYHF, HFXS</p>",
                    question_hi: "<p>7. निम्नलिखित में से कौन-सा अक्षर समूह प्रश्न-चिह्न (?) का स्थान लेगा और दी गई शृंखला को पूरा करेगा?<br>RDLS, VKBF, ?, DYHF, HFXS</p>",
                    options_en: ["<p>FHWR</p>", "<p>ZTTS</p>", 
                                "<p>ZRRS</p>", "<p>KBET</p>"],
                    options_hi: ["<p>FHWR</p>", "<p>ZTTS</p>",
                                "<p>ZRRS</p>", "<p>KBET</p>"],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777327.png\" alt=\"rId17\" width=\"347\" height=\"115\"></p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777327.png\" alt=\"rId17\" width=\"347\" height=\"115\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different. The odd one out is not based on the number of consonants/vowels or their position in the letter cluster</p>",
                    question_hi: "<p>8. चार अक्षर समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक समान हैं। उस असमान विकल्प को चुनिए। अक्षर समूह में, असमान विकल्प व्यंजनों/नोंस्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: ["<p>RTTUWW</p>", "<p>NOPPSS</p>", 
                                "<p>ACCDFF</p>", "<p>IKKLNN</p>"],
                    options_hi: ["<p>RTTUWW</p>", "<p>NOPPSS</p>",
                                "<p>ACCDFF</p>", "<p>IKKLNN</p>"],
                    solution_en: "<p>8.(b)&nbsp;By observing four options we can see that in each option except option (b) the 2nd letter is repeated two times.<br>Hence, option (b) is an odd one out.</p>",
                    solution_hi: "<p>8.(b)&nbsp;चार विकल्पों को देखकर हम देख सकते हैं कि विकल्प (b) को छोड़कर प्रत्येक विकल्प में दूसरा अक्षर दो बार दोहराया गया है।<br>इसलिए, विकल्प (b) असमान है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. 51 is related to 256 by certain logic. Following the same logic, 53 is related to 266. To which of the following is 55 related, following the same logic? (NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be&nbsp;performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>9. 51 एक निश्चित तर्क का अनुसरण करते हुए 256 से संबंधित है। इसी तर्क का अनुसरण करते हुए 53, 266 से संबंधित है। समान तर्क का अनुसरण करते हुए, 55 निम्नलिखित में से किससे संबंधित है?&nbsp;नोट : संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>276</p>", "<p>290</p>", 
                                "<p>249</p>", "<p>213</p>"],
                    options_hi: ["<p>276</p>", "<p>290</p>",
                                "<p>249</p>", "<p>213</p>"],
                    solution_en: "<p>9.(a) <strong>Logic: </strong>(1st number &times; 5) + 1 = 2nd number <br>(51 : 256) :- (51 &times; 5) + 1 = 256<br>(53 : 266) :- (53 &times; 5) + 1 = 266<br>Similarly<br>(55 : <math display=\"inline\"><mi>x</mi></math>) :- (55 &times; 5) + 1 = 276</p>",
                    solution_hi: "<p>9.(a)<strong> तर्क: </strong>(पहली संख्या &times; 5) +1 = दूसरी संख्या <br>(51 : 256) :- (51 &times; 5) + 1 = 256<br>(53 : 266) :- (53 &times; 5) + 1 = 266<br>उसी प्रकार<br>(55 : <math display=\"inline\"><mi>x</mi></math>) :- (55 &times; 5) + 1 = 276</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. How many rectangles are there in the following figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777472.png\" alt=\"rId18\" width=\"147\" height=\"109\"></p>",
                    question_hi: "<p>10. निम्नलिखित आकृति में कितने आयत हैं?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777472.png\" alt=\"rId18\" width=\"147\" height=\"109\"></p>",
                    options_en: ["<p>10</p>", "<p>5</p>", 
                                "<p>7</p>", "<p>9</p>"],
                    options_hi: ["<p>10</p>", "<p>5</p>",
                                "<p>7</p>", "<p>9</p>"],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777651.png\" alt=\"rId19\" width=\"172\" height=\"131\"><br>There are total 9 rectangle = ABCD, FGCE, IJKL, HNML, ZXVA&rsquo;, YB&rsquo;WX, RQPO, RTUS, FB&rsquo;UN.</p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777651.png\" alt=\"rId19\" width=\"172\" height=\"131\"><br>कुल 9 आयत हैं = ABCD, FGCE, IJKL, HNML, ZXVA&rsquo;, YB&rsquo;WX, RQPO, RTUS, FB&rsquo;UN.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. In a certain code language,<br>&lsquo;A $ B&rsquo; means &lsquo;A is the mother of B&rsquo;,<br>&lsquo;A # B&rsquo; means &lsquo;A is the father of B&rsquo;,<br>&lsquo;A ! B&rsquo; means &lsquo;A is the sister of B&rsquo;,<br>&lsquo;A @ B&rsquo; means &lsquo;A is the brother of B&rsquo;,<br>&lsquo;A * B&rsquo; means &lsquo;A is the wife of B&rsquo;,<br>&lsquo;A &gt; B&rsquo; means &lsquo;A is the husband of B&rsquo;,<br>&lsquo;A % B&rsquo; means &lsquo;A is the father-in-law of B&rsquo;,<br>&lsquo;A ^ B&rsquo; means &lsquo;A is the mother-in-law of B&rsquo;.<br>Based on the above, how is U related to A if<br>a) &lsquo;P &gt; Q $ R &gt; S ! T&rsquo;,<br>b) &lsquo;U # T&rsquo;,<br>c) &lsquo;R # A&rsquo;?</p>",
                    question_hi: "<p>11. एक निश्चित कूट भाषा में,<br>&lsquo;A $ B&rsquo; का अर्थ &lsquo;A, B की माता है&rsquo;,<br>&lsquo;A # B&rsquo; का अर्थ &lsquo;A, B का पिता है&rsquo;,<br>&lsquo;A ! B&rsquo; का अर्थ &lsquo;A, B की बहन है&rsquo;,<br>&lsquo;A @ B&rsquo; का अर्थ &lsquo;A, B का भाई है&rsquo;,<br>&lsquo;A * B&rsquo; का अर्थ &lsquo;A, B की पत्नी है&rsquo;,<br>&lsquo;A &gt; B&rsquo; का अर्थ &lsquo;A, B का पति है&rsquo;,<br>&lsquo;A % B&rsquo; का अर्थ &lsquo;A, B का ससुर है&rsquo;,<br>&lsquo;A ^ B&rsquo; का अर्थ &lsquo;A, B की सास है&rsquo;।<br>उपर्युक्त के आधार पर, U का A से क्या संबंध है, यदि<br>a) &lsquo;P &gt; Q $ R &gt; S ! T&rsquo;,<br>b) &lsquo;U # T&rsquo;,<br>c) &lsquo;R # A&rsquo;?</p>",
                    options_en: ["<p>Mother&rsquo;s brother</p>", "<p>Father</p>", 
                                "<p>Mother&rsquo;s father</p>", "<p>Son</p>"],
                    options_hi: ["<p>मामा</p>", "<p>पिता</p>",
                                "<p>नाना</p>", "<p>पुत्र</p>"],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777764.png\" alt=\"rId20\" width=\"248\" height=\"169\"><br>U is the Mother&rsquo;s father of A.</p>",
                    solution_hi: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777764.png\" alt=\"rId20\" width=\"248\" height=\"169\"><br>U, A का नाना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Select the option in which the given figure is embedded (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777863.png\" alt=\"rId21\" width=\"98\" height=\"98\"></p>",
                    question_hi: "<p>12. उस विकल्प का चयन कीजिए, जिसमें दी गई आकृति निहित है (घूर्णन की अनुमति नहींहै)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798777863.png\" alt=\"rId21\" width=\"98\" height=\"98\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778033.png\" alt=\"rId22\" width=\"84\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778145.png\" alt=\"rId23\" width=\"82\" height=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778344.png\" alt=\"rId24\" width=\"77\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778539.png\" alt=\"rId25\" width=\"75\" height=\"77\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778033.png\" alt=\"rId22\" width=\"84\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778145.png\" alt=\"rId23\" width=\"80\" height=\"78\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778344.png\" alt=\"rId24\" width=\"77\" height=\"80\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778539.png\" alt=\"rId25\" width=\"78\" height=\"80\"></p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778738.png\" alt=\"rId26\" width=\"93\" height=\"91\"></p>",
                    solution_hi: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778738.png\" alt=\"rId26\" width=\"93\" height=\"91\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778852.png\" alt=\"rId27\" width=\"116\" height=\"132\"></p>",
                    question_hi: "<p>13. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778852.png\" alt=\"rId27\" width=\"116\" height=\"132\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778964.png\" alt=\"rId28\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779064.png\" alt=\"rId29\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779156.png\" alt=\"rId30\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779247.png\" alt=\"rId31\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778964.png\" alt=\"rId28\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779064.png\" alt=\"rId29\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779156.png\" alt=\"rId30\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779247.png\" alt=\"rId31\"></p>"],
                    solution_en: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778964.png\" alt=\"rId28\"></p>",
                    solution_hi: "<p>13.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798778964.png\" alt=\"rId28\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out.&nbsp;(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>14. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।&nbsp;(नोट : गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>16 &ndash; 9 &ndash; 139</p>", "<p>25 &ndash; 7 &ndash; 175</p>", 
                                "<p>12 &ndash; 7 &ndash; 79</p>", "<p>17 &ndash; 5 &ndash; 80</p>"],
                    options_hi: ["<p>16 &ndash; 9 &ndash; 139</p>", "<p>25 &ndash; 7 &ndash; 175</p>",
                                "<p>12 &ndash; 7 &ndash; 79</p>", "<p>17 &ndash; 5 &ndash; 80</p>"],
                    solution_en: "<p>14.(b) <strong>Logic:</strong> (1st no. &times; 2nd no.) - 5 = 3rd no.<br>16 &ndash; 9 &ndash; 139 :- (16 &times; 9) - 5 = 139<br>12 &ndash; 7 &ndash; 79 :- (12 &times; 7) - 5 = 79<br>17 &ndash; 5 &ndash; 80 :- (17 &times; 5) - 5 = 80<br>But<br>25 &ndash; 7 &ndash; 175 :- (25 &times; 7) - 5 = 170 (<math display=\"inline\"><mo>&#8800;</mo></math>175)</p>",
                    solution_hi: "<p>14.(b) <strong>तर्क: </strong>(पहली संख्या &times; दूसरी संख्या) - 5 = तीसरी संख्या <br>16 &ndash; 9 &ndash; 139 :- (16 &times; 9) - 5 = 139<br>12 &ndash; 7 &ndash; 79 :- (12 &times; 7) - 5 = 79<br>17 &ndash; 5 &ndash; 80 :- (17 &times; 5) - 5 = 80<br>लेकिन<br>25 &ndash; 7 &ndash; 175 :- (25 &times; 7) - 5 = 170 (<math display=\"inline\"><mo>&#8800;</mo></math>175)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. What will come in place of the question mark (?) in the following equation, if &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; are interchanged and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; are interchanged?<br>14014 &times; 14 &ndash; 10 &divide; 8 + 14 = ?</p>",
                    question_hi: "<p>15. यदि &lsquo;+&rsquo; और &lsquo;&ndash;&rsquo; को आपस में बदल दिया जाए और &lsquo;&times;&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए, तो निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>14014 &times; 14 &ndash; 10 &divide; 8 + 14 = ?</p>",
                    options_en: ["<p>1062</p>", "<p>1063</p>", 
                                "<p>1067</p>", "<p>1064</p>"],
                    options_hi: ["<p>1062</p>", "<p>1063</p>",
                                "<p>1067</p>", "<p>1064</p>"],
                    solution_en: "<p>15.(c) <strong>Given:</strong> 14014 &times; 14 &ndash; 10 &divide; 8 + 14 = ?<br>As per the given instructions after interchanging the symbol &lsquo;+&rsquo; and &lsquo;&ndash;&rsquo; and &lsquo;&times;&rsquo; and &lsquo;&divide;&rsquo; , we get.<br>14014 &divide; 14 + 10 &times; 8 - 14<br>1001 + 80 - 14<br>1067</p>",
                    solution_hi: "<p>15.(c) <strong>दिया गया: </strong>14014 &times; 14 &ndash; 10 &divide; 8 + 14 = ?<br>दिए गए निर्देशों के अनुसार प्रतीक \'+\' और \'-\' तथा \'&times;\' और \'&divide;\' को आपस में बदलने के बाद, हमें प्राप्त होता है।<br>14014 &divide; 14 + 10 &times; 8 - 14<br>1001 + 80 - 14<br>1067</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>JAP : BSH :: NDZ : FVR :: LCX : ?</p>",
                    question_hi: "<p>16. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है।<br>JAP : BSH :: NDZ : FVR :: LCX : ?</p>",
                    options_en: ["<p>EVP</p>", "<p>DUP</p>", 
                                "<p>DVP</p>", "<p>EUP</p>"],
                    options_hi: ["<p>EVP</p>", "<p>DUP</p>",
                                "<p>DVP</p>", "<p>EUP</p>"],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779342.png\" alt=\"rId32\" width=\"116\" height=\"90\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779449.png\" alt=\"rId33\" width=\"120\" height=\"90\"><br>Similarly<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779542.png\" alt=\"rId34\" width=\"121\" height=\"90\"></p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779342.png\" alt=\"rId32\" width=\"116\" height=\"90\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779449.png\" alt=\"rId33\" width=\"120\" height=\"90\"><br>इसी तरह,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779542.png\" alt=\"rId34\" width=\"121\" height=\"90\"><br><br></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Which figure should replace the question mark (?) if the following series were to be continued?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779754.png\" alt=\"rId35\" width=\"381\" height=\"78\"></p>",
                    question_hi: "<p>17. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779754.png\" alt=\"rId35\" width=\"381\" height=\"78\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779869.png\" alt=\"rId36\" width=\"84\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780040.png\" alt=\"rId37\" width=\"84\" height=\"85\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780191.png\" alt=\"rId38\" width=\"84\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780343.png\" alt=\"rId39\" width=\"84\" height=\"85\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798779869.png\" alt=\"rId36\" width=\"84\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780040.png\" alt=\"rId37\" width=\"85\" height=\"85\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780191.png\" alt=\"rId38\" width=\"85\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780343.png\" alt=\"rId39\" width=\"85\" height=\"85\"></p>"],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780040.png\" alt=\"rId37\" width=\"102\" height=\"103\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780040.png\" alt=\"rId37\" width=\"102\" height=\"103\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Three statements are given, followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>Some rats are mouse.<br>No mouse is a rodent.<br>Some rats are kittens.<br><strong>Conclusions :</strong><br>I. No rodent is a kitten.<br>II. Some rats are rodent.<br>III. No kitten is a mouse.</p>",
                    question_hi: "<p>18. तीन कथन दिए गए हैं, जिसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्धारित करें कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं। <br><strong>कथन :</strong> <br>कुछ चूहे, मूषक हैं। <br>कोई मूषक, कृंतक नहीं है। <br>कुछ चूहे, बिल्ली-शावक (Kittens) है। <br><strong>निष्&zwj;कर्ष :</strong> <br>I. कोई कृंतक, बिल्ली-शावक (Kittens) नहीं है। <br>II. कुछ चूहे, कृंतक हैं। <br>III. कोई बिल्ली-शावक (Kittens), चूहा नहीं है।</p>",
                    options_en: ["<p>Only conclusion III follows</p>", "<p>Only conclusion II follows</p>", 
                                "<p>Neither conclusion follows</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>केवल निष्कर्ष III अनुसरण करता है</p>", "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                                "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>", "<p>केवल निष्कर्ष I अनुसरण करता है</p>"],
                    solution_en: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780540.png\" alt=\"rId40\" width=\"383\" height=\"60\"><br>Neither conclusion follows</p>",
                    solution_hi: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780726.png\" alt=\"rId41\" width=\"397\" height=\"60\"><br>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In a certain code language, &lsquo;FITCH&rsquo; is coded as &lsquo;47601&rsquo; and &lsquo;DITCH&rsquo; is coded as &lsquo;70619&rsquo;. What is the code for &lsquo;D&rsquo; in the given code language?</p>",
                    question_hi: "<p>19. एक निश्चित कूट भाषा में, \'FITCH\' को \'47601\' के रूप में कूटबद्ध किया जाता है और \'DITCH\' को \'70619\' के रूप में कूटबद्ध किया जाता है। इसी कूट भाषा में \'D\' के लिए कूट क्या होगा?</p>",
                    options_en: ["<p>7</p>", "<p>0</p>", 
                                "<p>9</p>", "<p>1</p>"],
                    options_hi: ["<p>7</p>", "<p>0</p>",
                                "<p>9</p>", "<p>1</p>"],
                    solution_en: "<p>19.(c) FITCH :- 47601&hellip;&hellip;(i)<br>DITCH :- 70619&hellip;&hellip;(ii)<br>From (i) and (ii) the code of &lsquo;D&rsquo; = &lsquo;9&rsquo;.</p>",
                    solution_hi: "<p>19.(c) FITCH :- 47601&hellip;&hellip;(i)<br>DITCH :- 70619&hellip;&hellip;(ii)<br>(i) और (ii) से \'D\' का कोड = \'9\'।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Which of the following operations should be interchanged to make the following equation correct?<br>12 + 12 &minus; 4 &times; 4 &divide; 2 = 22</p>",
                    question_hi: "<p>20. दिए गए समीकरण को सही बनाने (संतुलित करने) के लिए निम्नलिखित में से किन संक्रियाओं को आपस में बदलना होगा?<br>12 + 12 &minus; 4 &times; 4 &divide; 2 = 22</p>",
                    options_en: ["<p>+ and &divide;</p>", "<p>&divide; and &minus;</p>", 
                                "<p>&minus; and +</p>", "<p>&times; and &minus;</p>"],
                    options_hi: ["<p>+ और &divide;</p>", "<p>&divide; और &minus;</p>",
                                "<p>&minus; और +</p>", "<p>&times; और &minus;</p>"],
                    solution_en: "<p>20.(b) <strong>Given :</strong> 12 + 12 &minus; 4 &times; 4 &divide; 2 = 22<br>After going through all the options, option (b) satisfies.<br>12 + 12 &divide; 4 &times; 4 - 2 = 22<br>12 + 3 &times; 4 - 2 = 22<br>12 + 12 - 2 = 22<br>12 + 10 = 22</p>",
                    solution_hi: "<p>20.(b)<strong> दिया गया है :</strong> 12 + 12 &minus; 4 &times; 4 &divide; 2 = 22<br>सभी विकल्पों को देखने के बाद विकल्प (b) संतुष्ट करता है।<br>12 + 12 &divide; 4 &times; 4 - 2 = 22<br>12 + 3 &times; 4 - 2 = 22<br>12 + 12 - 2 = 22<br>12 + 10 = 22</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. In a certain code language, &lsquo;FAVORITE&rsquo; is written as &lsquo;ETIROVAF&rsquo; and &lsquo;MINIMIZE&rsquo; is written as &lsquo;EZIMINIM&rsquo;. How will &lsquo;RESEMBLE&rsquo; be written in that language?</p>",
                    question_hi: "<p>21. एक निश्चित कूट भाषा में, \'FAVORITE\' को \'ETIROVAF\' के रूप में लिखा जाता है और \'MINIMIZE\' को \'EZIMINIM\' के रूप में लिखा जाता है। उसी भाषा में \'RESEMBLE\' कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>ELBMESER</p>", "<p>ELBRESEM</p>", 
                                "<p>ERESLMBE</p>", "<p>ERLBMESR</p>"],
                    options_hi: ["<p>ELBMESER</p>", "<p>ELBRESEM</p>",
                                "<p>ERESLMBE</p>", "<p>ERLBMESR</p>"],
                    solution_en: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780919.png\" alt=\"rId42\" width=\"152\" height=\"80\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781047.png\" alt=\"rId43\" width=\"151\" height=\"80\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781146.png\" alt=\"rId44\" width=\"153\" height=\"80\"></p>",
                    solution_hi: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798780919.png\" alt=\"rId42\" width=\"152\" height=\"80\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781047.png\" alt=\"rId43\" width=\"151\" height=\"80\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781146.png\" alt=\"rId44\" width=\"153\" height=\"80\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option that represents the letters which, when sequentially placed from left to right in the blanks below, will complete the letter series.<br>fd_e_ef_dd_d_ff_d_d_f</p>",
                    question_hi: "<p>22. उस विकल्प का चयन कीजिए, जो उन अक्षरों को दर्शाता है, जिन्हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ क्रमिक रूप से रखने पर अक्षर श्रृंखला पूरी होगी।<br>fd_e_ef_dd_d_ff_d_d_f</p>",
                    options_en: ["<p>ddfeeded</p>", "<p>ddfefdee</p>", 
                                "<p>ddfdedef</p>", "<p>ddfeedee</p>"],
                    options_hi: ["<p>ddfeeded</p>", "<p>ddfefdee</p>",
                                "<p>ddfdedef</p>", "<p>ddfeedee</p>"],
                    solution_en: "<p>22.(d) fd<span style=\"text-decoration: underline;\"><strong>d</strong></span>e<span style=\"text-decoration: underline;\"><strong>d</strong></span>ef / <span style=\"text-decoration: underline;\"><strong>f</strong></span>dd<span style=\"text-decoration: underline;\"><strong>e</strong></span>d<span style=\"text-decoration: underline;\"><strong>e</strong></span>f / f<span style=\"text-decoration: underline;\"><strong>d</strong></span>d<strong><span style=\"text-decoration: underline;\">e</span></strong>d<span style=\"text-decoration: underline;\"><strong>e</strong></span>f</p>",
                    solution_hi: "<p>22.(d) fd<span style=\"text-decoration: underline;\"><strong>d</strong></span>e<span style=\"text-decoration: underline;\"><strong>d</strong></span>ef / <span style=\"text-decoration: underline;\"><strong>f</strong></span>dd<span style=\"text-decoration: underline;\"><strong>e</strong></span>d<span style=\"text-decoration: underline;\"><strong>e</strong></span>f / f<span style=\"text-decoration: underline;\"><strong>d</strong></span>d<strong><span style=\"text-decoration: underline;\">e</span></strong>d<span style=\"text-decoration: underline;\"><strong>e</strong></span>f</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group?&nbsp;(Note: The odd letter-cluster is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>23. अंग्रेजी वर्णमाला क्रम पर आधारित, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। किस अक्षर-समूह का संबंध उस समूह से नहीं है?&nbsp;(नोट: असंगत अक्षर-समूह, व्यंजनों/नोंस्वरों की संख्या या इस अक्षर-समूह में उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: ["<p>NKP</p>", "<p>KHM</p>", 
                                "<p>QNR</p>", "<p>TQV </p>"],
                    options_hi: ["<p>NKP</p>", "<p>KHM</p>",
                                "<p>QNR</p>", "<p>TQV</p>"],
                    solution_en: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781247.png\" alt=\"rId45\" width=\"70\" height=\"40\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781363.png\" alt=\"rId46\" width=\"71\" height=\"40\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781461.png\" alt=\"rId47\" width=\"70\" height=\"40\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781555.png\" alt=\"rId48\" width=\"68\" height=\"40\"></p>",
                    solution_hi: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781247.png\" alt=\"rId45\" width=\"70\" height=\"40\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781363.png\" alt=\"rId46\" width=\"71\" height=\"40\">&nbsp; &nbsp;,&nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781461.png\" alt=\"rId47\" width=\"70\" height=\"40\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781555.png\" alt=\"rId48\" width=\"68\" height=\"40\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. The position of how many letters will remain unchanged if each of the letters in the word &lsquo;DELIGHT&rsquo; is arranged in the English alphabetical order?</p>",
                    question_hi: "<p>24. यदि शब्द \'DELIGHT\' के प्रत्येक अक्षर को अँग्रेजी वर्णमाला क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा?</p>",
                    options_en: ["<p>One</p>", "<p>None</p>", 
                                "<p>Two</p>", "<p>Three</p>"],
                    options_hi: ["<p>एक</p>", "<p>कोई नहीं</p>",
                                "<p>दो</p>", "<p>तीन</p>"],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781741.png\" alt=\"rId49\" width=\"134\" height=\"65\"><br>Hence, the position of 3 letters will remain unchanged.</p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781741.png\" alt=\"rId49\" width=\"134\" height=\"65\"><br>अतः, 3 अक्षरों की स्थिति अपरिवर्तित रहेगी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Identify the figure given in the options, which when put in place of the question mark (?) will logically complete the series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781864.png\" alt=\"rId50\" width=\"291\" height=\"60\"></p>",
                    question_hi: "<p>25. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे प्रश्न-चिह्न (?) के स्थान पर रखने पर श्रृंखला तार्किक रूप से पूर्ण हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781864.png\" alt=\"rId50\" width=\"291\" height=\"60\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781961.png\" alt=\"rId51\" width=\"50\" height=\"50\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782057.png\" alt=\"rId52\" width=\"50\" height=\"50\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782164.png\" alt=\"rId53\" width=\"50\" height=\"50\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782264.png\" alt=\"rId54\" width=\"50\" height=\"50\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798781961.png\" alt=\"rId51\" width=\"50\" height=\"50\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782057.png\" alt=\"rId52\" width=\"50\" height=\"50\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782164.png\" alt=\"rId53\" width=\"50\" height=\"50\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782264.png\" alt=\"rId54\" width=\"50\" height=\"50\"></p>"],
                    solution_en: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782264.png\" alt=\"rId54\" width=\"54\" height=\"54\"></p>",
                    solution_hi: "<p>25.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782264.png\" alt=\"rId54\" width=\"54\" height=\"54\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following states had benefitted by the Indira Gandhi Canal ?</p>",
                    question_hi: "<p>26. इंदिरा गांधी नहर (Indira Gandhi Canal) से निम्नलिखित में से किस राज्य को लाभ हुआ?</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>Uttar Pradesh</p>", 
                                "<p>Gujarat</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>उत्तर प्रदेश</p>",
                                "<p>गुजरात</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p>26.(a) <strong>Rajasthan.</strong> The Indira Gandhi Canal (previously known as the Rajasthan Canal) is India\'s longest (650 km) canal which terminates in irrigation facilities in the Thar desert. It originates at Harike barrage in Punjab and runs parallel to the Pakistan border at an average distance of 40 km in Thar Desert (Marusthali) of Rajasthan. The canal uses water from the Sutlej - Beas rivers.</p>",
                    solution_hi: "<p>26.(a) <strong>राजस्थान। </strong>इंदिरा गांधी नहर (जिसे पहले राजस्थान नहर के नाम से जाना जाता था) भारत की सबसे लंबी (650 किमी) नहर है जो थार रेगिस्तान में सिंचाई सुविधाओं के साथ समाप्त होती है। यह पंजाब में हरिके बैराज से निकलती है और राजस्थान के थार रेगिस्तान (मरुस्थली) में औसतन 40 किमी की दूरी पर पाकिस्तान सीमा के समानांतर चलती है। यह नहर सतलज-ब्यास नदियों के जल का उपयोग करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. \'My Country My Life\' is the autobiography of:</p>",
                    question_hi: "<p>27. \'माई कंट्री माई लाइफ (My Country My Life)\' किसकी आत्मकथा है?</p>",
                    options_en: ["<p>Khushwant Singh</p>", "<p>Manmohan Singh</p>", 
                                "<p>Natwar Singh</p>", "<p>Lal Krishna Advani</p>"],
                    options_hi: ["<p>खुशवंत सिंह</p>", "<p>मनमोहन सिंह</p>",
                                "<p>नटवर सिंह</p>", "<p>लाल कृष्ण आडवाणी</p>"],
                    solution_en: "<p>27.(d) <strong>Lal Krishna Advani.</strong> He is an Indian politician who served as the 7th Deputy Prime Minister of India from 2002 to 2004. Awards: Bharat Ratna (2024), Padma Vibhushan (2015). Other Notable Autobiographies: Khushwant Singh - &ldquo;Truth, Love And A Little Malice&rdquo;; Manmohan Singh - &ldquo;Changing India&rdquo;; Natwar Singh - &ldquo;One Life Is Not Enough&rdquo;.</p>",
                    solution_hi: "<p>27.(d) <strong>लाल कृष्ण आडवाणी।</strong> वे एक भारतीय राजनेता हैं, जिन्होंने 2002 से 2004 तक भारत के 7वें उप-प्रधानमंत्री के रूप में कार्य किया। पुरस्कार: भारत रत्न (2024), पद्म विभूषण (2015)। अन्य उल्लेखनीय आत्मकथाएँ: खुशवंत सिंह - \"ट्रुथ लव एण्ड ए लिटल मैलिस\"; मनमोहन सिंह - \"चेंजिंग इंडिया\"; नटवर सिंह - \"वन लाइफ इज नॉट इनफ\"। </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which political party launched the Dr. Ambedkar Samman Yojana in December 2024 to support Dalit students pursuing higher education abroad?</p>",
                    question_hi: "<p>28. किस राजनीतिक पार्टी ने दिसंबर 2024 में डॉ. आंबेडकर सम्मान योजना शुरू की, जो विदेश में उच्च शिक्षा प्राप्त कर रहे दलित छात्रों को समर्थन प्रदान करती है?</p>",
                    options_en: ["<p>Bharatiya Janata Party (BJP)</p>", "<p>Indian National Congress (INC)</p>", 
                                "<p>Aam Aadmi Party (AAP)</p>", "<p>Bahujan Samaj Party (BSP)</p>"],
                    options_hi: ["<p>भारतीय जनता पार्टी (BJP)</p>", "<p>भारतीय राष्ट्रीय कांग्रेस (INC)</p>",
                                "<p>आम आदमी पार्टी (AAP)</p>", "<p>बहुजन समाज पार्टी (BSP)</p>"],
                    solution_en: "<p>28.(c) <strong>Aam Aadmi Party (AAP).</strong><br>AAP leader Arvind Kejriwal announced the &lsquo;Dr. Ambedkar Samman Scholarship Yojana&rsquo; to fully fund travel and stay for Delhi\'s Dalit students studying at top global universities. The scheme aims to empower education and honor Dr. B.R. Ambedkar\'s vision.</p>",
                    solution_hi: "<p>28.(c) <strong>आम आदमी पार्टी (AAP)।</strong><br>AAP नेता अरविंद केजरीवाल ने \'डॉ. आंबेडकर सम्मान छात्रवृत्ति योजना\' की घोषणा की, जिसके तहत दिल्ली के दलित छात्रों को शीर्ष वैश्विक विश्वविद्यालयों में पढ़ाई के लिए यात्रा और रहने का पूरा खर्च दिया जाएगा। इस योजना का उद्देश्य शिक्षा को सशक्त बनाना और डॉ. बी. आर. आंबेडकर के दृष्टिकोण को सम्मानित करना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who among the following signed a pact with Lord Irwin on March 5, 1931?</p>",
                    question_hi: "<p>29. निम्नलिखित में से किसने 5 मार्च, 1931 को लॉर्ड इरविन के साथ एक संधि पर हस्&zwj;ताक्षर किए ?</p>",
                    options_en: ["<p>B R Ambedkar</p>", "<p>Subhas Chandra Bose</p>", 
                                "<p>Mahatma Gandhi</p>", "<p>Jawaharlal Nehru</p>"],
                    options_hi: ["<p>बी. आर. अम्बेडकर</p>", "<p>सुभाष चंद्र बोस</p>",
                                "<p>महात्मा गाँधी</p>", "<p>जवाहर लाल नेहरू</p>"],
                    solution_en: "<p>29.(c) <strong>Mahatma Gandhi. </strong>The Gandhi-Irwin Pact, also known as the Delhi Pact, was an important agreement signed on March 5, 1931, between Mahatma Gandhi and Lord Irwin. It was a political agreement that aimed to resolve the civil disobedience movement and ease political tensions in India.</p>",
                    solution_hi: "<p>29.(c) <strong>महात्मा गांधी।</strong> गांधी-इरविन समझौता, जिसे दिल्ली समझौता भी कहा जाता है, 5 मार्च, 1931 को महात्मा गांधी और लॉर्ड इरविन के बीच हस्ताक्षरित एक महत्वपूर्ण समझौता था। यह एक राजनीतिक समझौता था जिसका उद्देश्य सविनय अवज्ञा आंदोलन को सुलझाना और भारत में राजनीतिक तनाव को कम करना था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Which of the following is NOT related to goals of Fundamental Duties of Indian Citizens?</p>",
                    question_hi: "<p>30. निम्नलिखित में से कौन-सा विकल्प भारतीय नागरिकों के मौलिक कर्तव्यों के लक्ष्यों से संबंधित नहीं है?</p>",
                    options_en: ["<p>Preserve</p>", "<p>Cherish</p>", 
                                "<p>Courage</p>", "<p>Respect</p>"],
                    options_hi: ["<p>संरक्षित करना (Preserve)</p>", "<p>संजोना (Cherish)</p>",
                                "<p>साहस (Courage)</p>", "<p>आदर (Respect)</p>"],
                    solution_en: "<p>30.(c) <strong>Courage.</strong> Fundamental Duties (Part IV-A, Article 51 A): Incorporated in the 42nd Constitutional Amendment Act. Article 51 A (f) - To value and preserve the rich heritage of our composite culture. Article 51 A(b) - To cherish and follow the noble ideals which inspired our national struggle for freedom. Article 51 A (a) - To abide by the Constitution and respect its ideals and institutions, the National Flag and the National Anthem.</p>",
                    solution_hi: "<p>30.(c) <strong>साहस (Courage) ।</strong> मौलिक कर्तव्य (भाग IV-A, अनुच्छेद 51 A): 42वें संविधान संशोधन अधिनियम में शामिल किया गया है। अनुच्छेद 51 A (f) - हमारी समग्र संस्कृति की समृद्ध विरासत को महत्व देना और संरक्षित करना। अनुच्छेद 51 A (b) - उन महान आदर्शों को संजोना और उनका पालन करना, जिन्होंने हमारे राष्ट्रीय स्वतंत्रता संग्राम को प्रेरित किया। अनुच्छेद 51 A (a) - संविधान का पालन करना और उसके आदर्शों और संस्थाओं, राष्ट्रीय ध्वज और राष्ट्रगान का सम्मान करना। </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. The digestive enzymes inside lysosomes are made by:</p>",
                    question_hi: "<p>31. लाइसोसोम (lysosomes) के भीतर पाचन एंजाइम _____ द्वारा बनाए जाते हैं।</p>",
                    options_en: ["<p>rough endoplasmic reticulum</p>", "<p>plastids themselves</p>", 
                                "<p>Golgi apparatus</p>", "<p>mitochondria</p>"],
                    options_hi: ["<p>रूक्ष अंतःप्रद्रव्य जालिका (rough endoplasmic reticulum)</p>", "<p>स्वयं प्लास्टिड (plastids themselves)</p>",
                                "<p>गॉल्जी उपकरण (Golgi apparatus)</p>", "<p>सूत्रकणिका (mitochondria)</p>"],
                    solution_en: "<p>31.(a)<strong> rough endoplasmic reticulum. </strong>Digestive enzymes are proteins that break down food so our body can absorb nutrients. Plastids are double membrane bound organelles found in the cytoplasm of plant cells and some protists such as Euglena. Golgi apparatus is a central membrane organelle for trafficking and post-translational modification of protein and lipid in the cell. Mitochondria are the \"powerhouses\" of the cell, breaking down fuel molecules and capturing energy in cellular respiration.</p>",
                    solution_hi: "<p>31.(a) <strong>रूक्ष अंतःप्रद्रव्य जालिका</strong> (rough endoplasmic reticulum)। पाचन एंजाइम प्रोटीन होते हैं जो भोजन को तोड़ते हैं जिससे हमारा शरीर पोषक तत्वों को अवशोषित कर सके। प्लास्टिड पादप की कोशिकाओं और यूग्लीना जैसे कुछ प्रोटिस्ट के कोशिका द्रव्य में पाए जाने वाले दोहरे झिल्ली से बंधे अंग हैं। गॉल्जी उपकरण कोशिका में प्रोटीन और लिपिड के यातायात और अनुवादोत्तर संशोधन के लिए एक केंद्रीय झिल्ली अंग है। माइटोकॉन्ड्रिया कोशिका का \"पावरहाउस\" हैं, जो ईंधन के अणुओं को तोड़ते हैं और कोशिकीय श्वसन में ऊर्जा को संरक्षित करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Who was the famous Venetian traveller of the 13th century who visited Kerala?</p>",
                    question_hi: "<p>32. 13वीं शताब्दी का वह प्रसिद्ध विनीशियन (Venetian) यात्री कौन था, जिसने केरल की यात्रा की थी?</p>",
                    options_en: ["<p>Abdur Razzaq</p>", "<p>Marco Polo</p>", 
                                "<p>Barthema</p>", "<p>Nicolo Conti</p>"],
                    options_hi: ["<p>अब्दुर रज्जाक</p>", "<p>मार्को पोलो</p>",
                                "<p>बारथेमा</p>", "<p>निकोलो कोंटी</p>"],
                    solution_en: "<p>32.(b) <strong>Marco Polo.</strong> He visited Southern India during the reigns of Rudramma Devi of the Kakatiyas and Pandyan ruler Madverman. Abdur Razzaq, a Persian ambassador sent by Shah Rukh of the Timurid Empire, visited Calicut in 1442 CE during the Zamorin\'s reign. Nicolo Conti, a Venetian merchant and explorer, spent around 25 years travelling in the Indian Ocean region during the 15th century.</p>",
                    solution_hi: "<p>32.(b) <strong>मार्को पोलो।</strong> उन्होंने काकतीय शासक रुद्रम्मा देवी और पांड्य शासक मदवर्मन के शासनकाल के दौरान दक्षिण भारत का दौरा किया। तिमुरी साम्राज्य के शाहरुख द्वारा भेजे गए फारसी राजदूत अब्दुर रज्जाक ने ज़मोरिन के शासनकाल के दौरान 1442 ई. में कालीकट का दौरा किया। वेनिस के व्यापारी और खोजकर्ता निकोलो कोंटी ने 15वीं शताब्दी के दौरान हिंद महासागर क्षेत्र में लगभग 25 वर्ष यात्रा की।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which Indian ministry launched the \'FloodWatch India\' 2.0 app to enhance flood monitoring capabilities?</p>",
                    question_hi: "<p>33. बाढ़ निगरानी क्षमताओं को बढ़ाने के लिए \'फ्लडवॉच इंडिया\' 2.0 ऐप किस भारतीय मंत्रालय द्वारा लॉन्च किया गया?</p>",
                    options_en: ["<p>Ministry of Environment, Forest and Climate Change</p>", "<p>Ministry of Home Affairs</p>", 
                                "<p>Ministry of Jal Shakti</p>", "<p>Ministry of Science and Technology</p>"],
                    options_hi: ["<p>पर्यावरण, वन और जलवायु परिवर्तन मंत्रालय</p>", "<p>गृह मंत्रालय</p>",
                                "<p>जल शक्ति मंत्रालय</p>", "<p>विज्ञान और प्रौद्योगिकी मंत्रालय</p>"],
                    solution_en: "<p>33.(c) <strong>Ministry of Jal Shakti.</strong><br>The Ministry of Jal Shakti launched \'FloodWatch India\' 2.0, developed by the Central Water Commission, to provide real-time flood forecasts from 592 monitoring stations and data on 150 major reservoirs, enhancing flood preparedness and response.</p>",
                    solution_hi: "<p>33.(c) <strong>जल शक्ति मंत्रालय।</strong><br>जल शक्ति मंत्रालय ने \'फ्लडवॉच इंडिया\' 2.0 ऐप लॉन्च किया, जिसे केंद्रीय जल आयोग द्वारा विकसित किया गया है। यह 592 निगरानी स्टेशनों से वास्तविक समय में बाढ़ पूर्वानुमान और 150 प्रमुख जलाशयों की जानकारी प्रदान करता है, जिससे बाढ़ की तैयारी और प्रतिक्रिया को बेहतर बनाया जा सके।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following states had benefitted by the Indira Gandhi Canal ?</p>",
                    question_hi: "<p>34. इंदिरा गांधी नहर (Indira Gandhi Canal) से निम्नलिखित में से किस राज्य को लाभ हुआ?</p>",
                    options_en: ["<p>Rajasthan</p>", "<p>Uttar Pradesh</p>", 
                                "<p>Gujarat</p>", "<p>Madhya Pradesh</p>"],
                    options_hi: ["<p>राजस्थान</p>", "<p>उत्तर प्रदेश</p>",
                                "<p>गुजरात</p>", "<p>मध्य प्रदेश</p>"],
                    solution_en: "<p>34.(a) <strong>Rajasthan</strong>. The Indira Gandhi Canal (previously known as the Rajasthan Canal) is India\'s longest (650 km) canal which terminates in irrigation facilities in the Thar desert. It originates at Harike barrage in Punjab and runs parallel to the Pakistan border at an average distance of 40 km in Thar Desert (Marusthali) of Rajasthan. The canal uses water from the Sutlej - Beas rivers.</p>",
                    solution_hi: "<p>34.(a)<strong> राजस्थान।</strong> इंदिरा गांधी नहर (जिसे पहले राजस्थान नहर के नाम से जाना जाता था) भारत की सबसे लंबी (650 किमी) नहर है जो थार रेगिस्तान में सिंचाई सुविधाओं के साथ समाप्त होती है। यह पंजाब में हरिके बैराज से निकलती है और राजस्थान के थार रेगिस्तान (मरुस्थली) में औसतन 40 किमी की दूरी पर पाकिस्तान सीमा के समानांतर चलती है। यह नहर सतलज-ब्यास नदियों के जल का उपयोग करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. \'My Country My Life\' is the autobiography of:</p>",
                    question_hi: "<p>35. \'माई कंट्री माई लाइफ (My Country My Life)\' किसकी आत्मकथा है?</p>",
                    options_en: ["<p>Khushwant Singh</p>", "<p>Manmohan Singh</p>", 
                                "<p>Natwar Singh</p>", "<p>Lal Krishna Advani</p>"],
                    options_hi: ["<p>खुशवंत सिंह</p>", "<p>मनमोहन सिंह</p>",
                                "<p>नटवर सिंह</p>", "<p>लाल कृष्ण आडवाणी</p>"],
                    solution_en: "<p>35.(d)<strong> Lal Krishna Advani.</strong> He is an Indian politician who served as the 7th Deputy Prime Minister of India from 2002 to 2004. Awards: Bharat Ratna (2024), Padma Vibhushan (2015). Other Notable Autobiographies: Khushwant Singh - &ldquo;Truth, Love And A Little Malice&rdquo;; Manmohan Singh - &ldquo;Changing India&rdquo;; Natwar Singh - &ldquo;One Life Is Not Enough&rdquo;.</p>",
                    solution_hi: "<p>35.(d)<strong> लाल कृष्ण आडवाणी।</strong> वे एक भारतीय राजनेता हैं, जिन्होंने 2002 से 2004 तक भारत के 7वें उप-प्रधानमंत्री के रूप में कार्य किया। पुरस्कार: भारत रत्न (2024), पद्म विभूषण (2015)। अन्य उल्लेखनीय आत्मकथाएँ: खुशवंत सिंह - \"ट्रुथ लव एण्ड ए लिटल मैलिस\"; मनमोहन सिंह - \"चेंजिंग इंडिया\"; नटवर सिंह - \"वन लाइफ इज नॉट इनफ\"।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. A river is capable of forming which of the following landscapes when the river is in its middle course?</p>",
                    question_hi: "<p>36. जब कोई नदी अपने मध्य मार्ग में होती है तो वह नदी निम्नलिखित में से कौन-सा भू-दृश्य (landscape) बनाने में सक्षम होती है?</p>",
                    options_en: ["<p>Waterfalls</p>", "<p>A delta</p>", 
                                "<p>V shaped valley</p>", "<p>A meander</p>"],
                    options_hi: ["<p>झरने (Waterfalls)</p>", "<p>एक डेल्टा (A delta)</p>",
                                "<p>V आकार की घाटी (V shaped valley)</p>", "<p>एक विसर्प (A meander)</p>"],
                    solution_en: "<p>36.(d) <strong>A meander</strong> is a U-shaped, loop-like channel pattern in a river that is formed by erosion and deposition. V-shaped valley is a valley that is shaped like the letter \"V\" and is formed by erosion from a river or stream over time. A waterfall is a place where water in a river or stream drops vertically or nearly vertically over a rocky ledge or series of steep drops into a pool below. A delta is a depositional feature formed at the mouth of a river, where it meets standing water like the sea or ocean.</p>",
                    solution_hi: "<p>36.(d) <strong>एक विसर्प (A meander) नदी</strong> में एक U-आकार का, लूप जैसा चैनल संरचना होता है जो कटाव और निक्षेपण के कारण बनता है। V-आकार की घाटी एक ऐसी घाटी है जो अक्षर \"V\" के आकार की होती है और समय के साथ नदी या नाले के कटाव से बनती है। झरना वह स्थान होता है जहां नदी या नाले का जल एक चट्टानी किनारे या खड़ी ढलानों से एक तालाब में गिरता है। डेल्टा एक निक्षेपणीय भूआकृति है, जो नदी के मुहाने पर बनती है, जहाँ वह स्थिर जल, जैसे समुद्र या महासागर से मिलती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. The digestive enzymes inside lysosomes are made by:</p>",
                    question_hi: "<p>37. लाइसोसोम (lysosomes) के भीतर पाचन एंजाइम _____ द्वारा बनाए जाते हैं।</p>",
                    options_en: ["<p>rough endoplasmic reticulum</p>", "<p>plastids themselves</p>", 
                                "<p>Golgi apparatus</p>", "<p>mitochondria</p>"],
                    options_hi: ["<p>रूक्ष अंतःप्रद्रव्य जालिका (rough endoplasmic reticulum)</p>", "<p>स्वयं प्लास्टिड (plastids themselves)</p>",
                                "<p>गॉल्जी उपकरण (Golgi apparatus)</p>", "<p>सूत्रकणिका (mitochondria)</p>"],
                    solution_en: "<p>37.(a) <strong>rough endoplasmic reticulum.</strong> Digestive enzymes are proteins that break down food so our body can absorb nutrients. Plastids are double membrane bound organelles found in the cytoplasm of plant cells and some protists such as Euglena. Golgi apparatus is a central membrane organelle for trafficking and post-translational modification of protein and lipid in the cell. Mitochondria are the \"powerhouses\" of the cell, breaking down fuel molecules and capturing energy in cellular respiration.</p>",
                    solution_hi: "<p>37.(a) <strong>रूक्ष अंतःप्रद्रव्य जालिका</strong> (rough endoplasmic reticulum)। पाचन एंजाइम प्रोटीन होते हैं जो भोजन को तोड़ते हैं जिससे हमारा शरीर पोषक तत्वों को अवशोषित कर सके। प्लास्टिड पादप की कोशिकाओं और यूग्लीना जैसे कुछ प्रोटिस्ट के कोशिका द्रव्य में पाए जाने वाले दोहरे झिल्ली से बंधे अंग हैं। गॉल्जी उपकरण कोशिका में प्रोटीन और लिपिड के यातायात और अनुवादोत्तर संशोधन के लिए एक केंद्रीय झिल्ली अंग है। माइटोकॉन्ड्रिया कोशिका का \"पावरहाउस\" हैं, जो ईंधन के अणुओं को तोड़ते हैं और कोशिकीय श्वसन में ऊर्जा को संरक्षित करते हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. What is the primary gas responsible for the leavening effect in bread when yeast ferments sugars?</p>",
                    question_hi: "<p>38. जब खमीर शर्करा को किण्वित करता है तो ब्रेड में खमीरीकरण प्रभाव (leavening effect) मुख्य रूप से किस गैस के कारण होता है?</p>",
                    options_en: ["<p>Carbon dioxide</p>", "<p>Nitrogen</p>", 
                                "<p>Oxygen</p>", "<p>Helium</p>"],
                    options_hi: ["<p>कार्बन डाइऑक्साइड</p>", "<p>नाइट्रोजन</p>",
                                "<p>ऑक्सीजन</p>", "<p>हीलियम</p>"],
                    solution_en: "<p>38.(a) <strong>Carbon dioxide. </strong>When yeast consumes sugars in dough, it produces carbon dioxide as a byproduct of fermentation, which gets trapped within the dough, causing it to rise and become fluffy. The microorganism responsible for this process is called yeast, specifically Saccharomyces cerevisiae. Fermentation is the chemical process where yeast converts sugars into carbon dioxide and alcohol.</p>",
                    solution_hi: "<p>38.(a) <strong>कार्बन डाइऑक्साइड।</strong> जब खमीर आटे में मौजूद शर्करा को खाते है, तो यह किण्वन के उपोत्पाद के रूप में कार्बन डाइऑक्साइड उत्पन्न करते है, जो आटे के अंडर फँसी हुई गैस, आटे को फूलाने देता है और बड़ा कर देता है। इस प्रक्रिया के लिए जिम्मेदार सूक्ष्मजीव को खमीर कहा जाता है, विशेष रूप से सैकरोमाइस सेरेविसिया। किण्वन वह रासायनिक प्रक्रिया है जिसमें खमीर शर्करा को कार्बन डाइऑक्साइड और एल्कोहल में परावर्तित कर देता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Sarhul is an agricultural festival of tribals, which is primarily celebrated in the Indian state of ________.</p>",
                    question_hi: "<p>39. सरहुल (Sarhul) आदिवासियों का कृषि से संबंधित एक त्यौहार है, जो मुख्य रूप से भारत के ________ राज्य में मनाया जाता है।</p>",
                    options_en: ["<p>Uttarakhand</p>", "<p>Sikkim</p>", 
                                "<p>Jharkhand</p>", "<p>Gujarat</p>"],
                    options_hi: ["<p>उत्तराखंड</p>", "<p>सिक्किम</p>",
                                "<p>झारखंड</p>", "<p>गुजरात</p>"],
                    solution_en: "<p>39.(c) <strong>Jharkhand.</strong> Sarhul is celebrated every year, on the third day of the moon or the fortnight of \'Chaitra\' by the Oraon, the Munda, and the Ho tribes. Other festivals of Jharkhand: Karma, Manda, Sarhul, Jani shikar etc. Sikkim - Losar, Saga Dawa, Pang Lhabsol . Uttarakhand - Kawad Yatra, Bissu Mela, Nanda Devi Raj Jat. Gujarat - Garba, Rann Utsav, International Kite Festival, Tarnetar fair.</p>",
                    solution_hi: "<p>39.(c) <strong>झारखंड।</strong> सरहुल प्रत्येक वर्ष चंद्रमा के तीसरे दिन या \'चैत्र\' के पखवाड़े में ओरांव, मुंडा और हो जनजातियों द्वारा मनाया जाता है। झारखंड के अन्य त्योहार: करमा, मंडा, सरहुल, जनी शिकार आदि। सिक्किम - लोसर, सागा दावा, पांग ल्हाबसोल। उत्तराखंड - कावड़ यात्रा, बिस्सू मेला, नंदा देवी राज जात। गुजरात - गरबा, रण उत्सव, अंतर्राष्ट्रीय पतंग महोत्सव, तरनेतर मेला।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Who has been appointed as the new Director General of the Bureau of Civil Aviation Security (BCAS) ?</p>",
                    question_hi: "<p>40. नागरिक उड्डयन सुरक्षा ब्यूरो (BCAS) के नए महानिदेशक के रूप में किसे नियुक्त किया गया है ?</p>",
                    options_en: ["<p>Rajesh Nirwan</p>", "<p>Zulfiquar Hasan</p>", 
                                "<p>Usha Padhee</p>", "<p>Amrit Mohan Prasad</p>"],
                    options_hi: ["<p>राजेश निरवान</p>", "<p>जुल्फिकार हसन</p>",
                                "<p>उषा पड्&zwnj;ही</p>", "<p>अमृत मोहन प्रसाद</p>"],
                    solution_en: "<p>40.(a) <strong>Rajesh Nirwan.</strong><br>BCAS is a wing under the Ministry of Civil Aviation responsible for overseeing civil aviation security. BCAS was initially set up as a cell within the Directorate General of Civil Aviation (DGCA) in January 1978, following recommendations from the Pande Committee after the hijacking of an Indian Airlines flight in 1976. It became an independent department under the Ministry of Civil Aviation on April 1, 1987, in response to the Kanishka Tragedy of 1985.</p>",
                    solution_hi: "<p>40.(a) <strong>राजेश निरवान।</strong><br>BCAS नागरिक उड्डयन मंत्रालय के अंतर्गत एक शाखा है, जो नागरिक उड्डयन सुरक्षा की देखरेख करता है। BCAS की स्थापना जनवरी 1978 में नागरिक उड्डयन महानिदेशालय (DGCA) के एक प्रकोष्ठ के रूप में की गई थी। इसकी स्थापना पांडे समिति की सिफारिशों के आधार पर 1976 में इंडियन एयरलाइंस के विमान अपहरण की घटना के बाद की गई थी। 1 अप्रैल 1987 को, इसे स्वतंत्र विभाग के रूप में नागरिक उड्डयन मंत्रालय के अंतर्गत लाया गया, जो 1985 की कनिष्क त्रासदी के मद्देनजर उठाया गया कदम था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Who was the Registrar General and Census Commissioner of India for the 2011 Indian Census?</p>",
                    question_hi: "<p>41. 2011 की भारतीय जनगणना के लिए भारत के महापंजीयक और जनगणना आयुक्त कौन थे?</p>",
                    options_en: ["<p>Vivek Joshi</p>", "<p>Devender Kumar Sikri</p>", 
                                "<p>Jayant Kumar Banthia</p>", "<p>Dr. C Chandramoul</p>"],
                    options_hi: ["<p>विवेक जोशी</p>", "<p>देवेंद्र कुमार सीकरी</p>",
                                "<p>जयंत कुमार बंठिया</p>", "<p>डॉ. सी. चंद्रमौली</p>"],
                    solution_en: "<p>41.(d) <strong>Dr. C Chandramouli. </strong>The Census 2011 is the 15th National census survey conducted by the Census Organization of India. The 2011 Indian National Census has been conducted in 2 phases - house listing and population. The national census survey covered all the 28 states of the country and 7 Union territories including 640 districts, 497 cities, 5767 tehsils &amp; over 6 lakh villages.</p>",
                    solution_hi: "<p>41.(d) <strong>डॉ. सी चंद्रमौली। </strong>जनगणना 2011 भारत के जनगणना संगठन द्वारा आयोजित 15वीं राष्ट्रीय जनगणना सर्वेक्षण है। 2011 की भारतीय राष्ट्रीय जनगणना 2 चरणों में आयोजित की गई है - मकान सूचीकरण और जनसंख्या। राष्ट्रीय जनगणना सर्वेक्षण में देश के सभी 28 राज्यों और 7 केंद्र शासित प्रदेशों को शामिल किया गया है जिसमें 640 जिले, 497 शहर, 5767 तहसील और 6 लाख से अधिक गांव शामिल हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. The Indian Bureau of Mines (IBM) was established in the year ________.</p>",
                    question_hi: "<p>42. भारतीय खान ब्यूरो (Indian Bureau of Mines - IBM) की स्थापना वर्ष ________ में की गई थी।</p>",
                    options_en: ["<p>1948</p>", "<p>1973</p>", 
                                "<p>1985</p>", "<p>1960</p>"],
                    options_hi: ["<p>1948</p>", "<p>1973</p>",
                                "<p>1985</p>", "<p>1960</p>"],
                    solution_en: "<p>42.(a) <strong>1948. </strong>The Indian Bureau of Mines (IBM) is a government organization that promotes the scientific development and conservation of mineral resources, and protects the environment in mines. IBM headquarters is in Nagpur. Some other notable institutes and organizations: {Geological Survey of India (GSI) - 1851, Kolkata, West Bengal}, {National Mineral Development Corporation (NMDC) - 1958, Hyderabad, Telangana}, Indian School of Mines (ISM), Dhanbad (Now IIT Dhanbad) - 1926, {Mineral Exploration Corporation Limited (MECL)- 1972, Nagpur, Maharashtra}.</p>",
                    solution_hi: "<p>42.(a) <strong>1948.</strong> भारतीय खान ब्यूरो (IBM) एक सरकारी संगठन है जो खनिज संसाधनों के वैज्ञानिक विकास और संरक्षण को बढ़ावा देता है, और खानों में पर्यावरण की रक्षा करता है। IBM का मुख्यालय नागपुर में है। कुछ अन्य उल्लेखनीय संस्थान एवं संगठन: {भारतीय भूवैज्ञानिक सर्वेक्षण (GSI)-1851, कोलकाता, पश्चिम बंगाल}, {राष्ट्रीय खनिज विकास निगम (NMDC) - 1958, हैदराबाद, तेलंगाना}, इंडियन स्कूल ऑफ माइन्स (ISM), धनबाद (अब IIT धनबाद) - 1926, {मिनरल एक्सप्लोरेशन कॉर्पोरेशन लिमिटेड (MECL) - 1972, नागपुर, महाराष्ट्र}।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which of the following statements is/are correct according to the Census of India-2011?<br>(A) A person aged seven years or above, who can both read and write with understanding in any language, is treated as literate. <br>(B) A person who can only read but cannot write is not literate.</p>",
                    question_hi: "<p>43. भारत की जनगणना-2011 के अनुसार, निम्नलिखित में से कौन-सा/कौन-से कथन सही है/हैं?<br>(A) सात वर्ष या उससे अधिक उम्र का व्यक्ति, जो किसी भी भाषा को समझने के साथ पढ़ और लिख सकता है, साक्षर माना जाता है।<br>(B) एक व्यक्ति जो केवल पढ़ सकता है, लेकिन लिख नहीं सकता, वह साक्षर नहीं है।</p>",
                    options_en: ["<p>Both (A) and (B) are correct</p>", "<p>Only (B) is correct</p>", 
                                "<p>Only (A) is correct</p>", "<p>Neither (A) nor (B) is correct</p>"],
                    options_hi: ["<p>(A) और (B) दोनों सही हैं</p>", "<p>केवल (B) सही है</p>",
                                "<p>केवल (A) सही है</p>", "<p>न तो (A) और न ही (B) सही है</p>"],
                    solution_en: "<p>43.(a) According to the 2011 Census of India, the overall literacy rate is 74.04%, with the youth literacy rate being about 9% higher than the adult rate. The female literacy rate is 65.46%. Kerala has the highest literacy rate at 94%, while Bihar has the lowest at 61.8%.</p>",
                    solution_hi: "<p>43.(a) भारत की 2011 की जनगणना के अनुसार, कुल साक्षरता दर 74.04% है, जिसमें युवा साक्षरता दर वयस्क दर से लगभग 9% अधिक है। महिला साक्षरता दर 65.46% है। केरल में साक्षरता दर सबसे अधिक 94% है, जबकि बिहार में सबसे कम 61.8% है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following statements are correct about Ribosomes?<br>Statement A: Ribosomes have no membrane.<br>Statement B: Digestive hydrolytic enzymes are present in Ribosomes.<br>Statement C: These are called suicidal bags.<br>Statement D: Ribosomes play an important role in protein synthesis.</p>",
                    question_hi: "<p>44. राइबोसोम (Ribosomes) के बारे में निम्नलिखित में से कौन-सा कथन सही है?<br>कथन A: राइबोसोम में कोई झिल्ली नहीं होती।<br>कथन B: राइबोसोम में पाचन हाइड्रोलाइटिक एंजाइम (Digestive hydrolytic enzymes) मौजूद होते हैं।<br>कथन C: इन्हें आत्मघाती थैलियाँ कहा जाता है।<br>कथन D: राइबोसोम प्रोटीन संश्लेषण में महत्वपूर्ण भूमिका निभाते हैं।</p>",
                    options_en: ["<p>Only statements A and D</p>", "<p>Only statements A and B</p>", 
                                "<p>Only statements A, B and C</p>", "<p>Only statements A and C</p>"],
                    options_hi: ["<p>केवल कथन A और D</p>", "<p>केवल कथन A और B</p>",
                                "<p>केवल कथन A, B और C</p>", "<p>केवल कथन A और C</p>"],
                    solution_en: "<p>44.(a) <strong>Only statements A and D. </strong>Ribosomes are made up of both proteins and RNA. Digestive hydrolytic enzymes are primarily present in lysosomes within a cell. These are known as suicide bags\' because the enzymes of lysosomes eat up their own cells if the cells get damaged. Multiple ribosomes that are joined to the mRNA and contain developing polypeptide chains are known as polysomes.</p>",
                    solution_hi: "<p>44.(a) <strong>केवल कथन A और D. </strong>राइबोसोम प्रोटीन और RNA दोनों से बने होते हैं। पाचन हाइड्रोलाइटिक एंजाइम मुख्य रूप से कोशिका के भीतर लाइसोसोम में मौजूद होते हैं। इन्हें \'आत्महत्या की थैली\' के नाम से जाना जाता है क्योंकि यदि कोशिकाएँ क्षतिग्रस्त हो जाती हैं तो लाइसोसोम के एंजाइम अपनी ही कोशिकाओं को नष्ट कर देते हैं। कई राइबोसोम जो mRNA से जुड़े होते हैं और जिनमें विकासशील पॉलीपेप्टाइड श्रृंखलाएँ होती हैं, उन्हें पॉलीसोम के रूप में जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following is a public sector industry in India?</p>",
                    question_hi: "<p>45. निम्नलिखित में से कौन-सा भारत में सार्वजनिक क्षेत्र का एक उद्योग है?</p>",
                    options_en: ["<p>Tata Iron and Steel Company (TISCO)</p>", "<p>Bajaj</p>", 
                                "<p>Hindustan Unilever Limited</p>", "<p>Steel Authority of India Limited (SAIL)</p>"],
                    options_hi: ["<p>टाटा आयरन एंड स्टील कंपनी (TISCO)</p>", "<p>बजाज</p>",
                                "<p>हिंदुस्तान यूनिलीवर लिमिटेड</p>", "<p>स्टील अथॉरिटी ऑफ इंडिया लिमिटेड (SAIL)</p>"],
                    solution_en: "<p>45.(d) <strong>Steel Authority of India Limited (SAIL). </strong>SAIL can trace its origins back to the Hindustan Steel Limited (HSL) which was set up on 19 January 1954. SAIL was incorporated on January 24, 1973.</p>",
                    solution_hi: "<p>45.(d)<strong> स्टील अथॉरिटी ऑफ इंडिया लिमिटेड (SAIL)। </strong>सेल (SAIL) की उत्पत्ति हिंदुस्तान स्टील लिमिटेड (HSL) से मानी जाती है, जिसकी स्थापना 19 जनवरी 1954 को हुई थी। सेल को 24 जनवरी 1973 को निगमित किया गया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following was the first subcontinental empire?</p>",
                    question_hi: "<p>46. निम्नलिखित में से प्रथम उपमहाद्वीपीय साम्राज्य कौन सा था?</p>",
                    options_en: ["<p>Satavahana Empire</p>", "<p>Mauryan Empire</p>", 
                                "<p>Kushan Empire</p>", "<p>Gupta Empire</p>"],
                    options_hi: ["<p>सातवाहन साम्राज्य</p>", "<p>मौर्य साम्राज्य</p>",
                                "<p>कुषाण साम्राज्य</p>", "<p>गुप्त साम्राज्य</p>"],
                    solution_en: "<p>46.(b) <strong>Mauryan Empire. </strong>Chandragupta Maurya established the Mauryan Empire by defeating the Nandas in 322 BCE. Pataliputra was the capital of the Mauryan empire. Simuka was the founder of the Satavahana Dynasty. The founder of the Kushan dynasty was Kujala Kadphises.</p>",
                    solution_hi: "<p>46.(b) <strong>मौर्य साम्राज्य। </strong>चंद्रगुप्त मौर्य ने 322 ईसा पूर्व में नंदों को पराजित कर मौर्य साम्राज्य की स्थापना की। पाटलिपुत्र मौर्य साम्राज्य की राजधानी थी। सिमुक सातवाहन राजवंश के संस्थापक थे। कुषाण वंश के संस्थापक कुजुल कडफिसेस थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. In which of the following months is the Tuluni festival celebrated in Nagaland by the Sumi tribe?</p>",
                    question_hi: "<p>47. निम्नलिखित में से किस महीने में नागालैंड में सुमी जनजाति (Sumi tribe) द्वारा तुलुनी महोत्सव (Tuluni festival) मनाया जाता है?</p>",
                    options_en: ["<p>June</p>", "<p>May</p>", 
                                "<p>August</p>", "<p>July</p>"],
                    options_hi: ["<p>जून</p>", "<p>मई</p>",
                                "<p>अगस्त</p>", "<p>जुलाई</p>"],
                    solution_en: "<p>47.(d) <strong>July. </strong>The Tuluni festival is an important agricultural festival, marking the harvest season, and is dedicated to ensuring prosperity and good fortune. Some other harvest festivals in India and celebration months: Makar Sankranti (January), Baisakhi (April), Lohri (January), Bhogali Bihu (January-February), Basant Panchami (January or February), Pongal (January), Gudi Padwa (first day of Chaitra, March and April).</p>",
                    solution_hi: "<p>47.(d) <strong>जुलाई।</strong> तुलुनी महोत्सव एक महत्वपूर्ण कृषि त्योहार है, जो फसल कटाई के मौसम का प्रतीक है, और समृद्धि और सौभाग्य सुनिश्चित करने के लिए समर्पित है। भारत में कुछ अन्य फसल त्योहार और उत्सव के महीने: मकर संक्रांति (जनवरी), बैसाखी (अप्रैल), लोहड़ी (जनवरी), भोगली बिहू (जनवरी-फरवरी), बसंत पंचमी (जनवरी या फरवरी), पोंगल (जनवरी), गुड़ी पड़वा (चैत्र, मार्च और अप्रैल का पहला दिन)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which of the following groups of plants are categorised as cryptogams?</p>",
                    question_hi: "<p>48. निम्नलिखित में से किस पादप समूह को क्रिप्टोगैम (cryptogams) के रूप में वर्गीकृत किया गया है?</p>",
                    options_en: ["<p>Gymnosperms and Angiosperms</p>", "<p>Pteridophyta and Angiosperms</p>", 
                                "<p>Thallophyta and Gymnosperms</p>", "<p>Bryophytes and Thallophyta</p>"],
                    options_hi: ["<p>जिम्नोस्पर्म और एंजियोस्पर्म</p>", "<p>टेरिडोफाइटा और एंजियोस्पर्म</p>",
                                "<p>थैलोफाइटा और जिम्नोस्पर्म</p>", "<p>ब्रायोफाइटा और थैलोफाइटा</p>"],
                    solution_en: "<p>48.(d) <strong>Bryophytes, Thallophyta</strong> and pteridophytes are called cryptogamae. They have naked embryos that are called spores. The reproductive organs of plants in all these three groups are not clearly visible. It reproduces by spores, without flowers or seeds. Thallophyta - Plants that do not have well-differentiated body design fall in this group. The plants in this group are commonly called algae. Bryophytes - These are called the amphibians of the plant kingdom. The plant body is commonly differentiated to form stem and leaf-like structures. Gymnosperms and Angiosperms are called &lsquo;phanerogams&rsquo;.</p>",
                    solution_hi: "<p>48.(d) <strong>ब्रायोफाइट्स, थैलोफाइटा</strong> और टेरिडोफाइट्स को क्रिप्टोगैमी कहा जाता है। इनमें नग्न भ्रूण होते हैं जिन्हें बीजाणु कहा जाता है। इन तीनों समूहों के पौधों के प्रजनन अंग स्पष्ट रूप से दिखाई नहीं देते हैं। यह फूल या बीज के बिना बीजाणुओं द्वारा प्रजनन करता है। थैलोफाइटा - वे पौधे जिनके शरीर की बनावट अच्छी तरह से विभेदित नहीं होती है, इस समूह में आते हैं। इस समूह के पौधों को आम तौर पर शैवाल कहा जाता है। ब्रायोफाइट्स - इन्हें पादप जगत के उभयचर कहा जाता है। पौधे का शरीर आम तौर पर तने और पत्ती जैसी संरचना बनाने के लिए विभेदित होता है। जिम्नोस्पर्म और एंजियोस्पर्म को \'फैनरोगैम\' कहा जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Match the following and choose the correct option:<br>1. Multi-Party System&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; a) USA<br>2. Two Party system&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; b) China<br>3. Single Party system&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; c) India</p>",
                    question_hi: "<p>49. निम्नलिखित को सुमेलित कीजिए और सही विकल्प का चयन कीजिए:<br>1. बहुदलीय प्रणाली&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;a) यूएसए<br>2. द्विदलीय प्रणाली&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; b) चीन<br>3. एकल दल प्रणाली&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;c) भारत</p>",
                    options_en: ["<p>1-a, 2-b, 3-c</p>", "<p>1-c, 2-b, 3-a</p>", 
                                "<p>1-b, 2-c, 3-a</p>", "<p>1-c, 2-a, 3-b</p>"],
                    options_hi: ["<p>1-a, 2-b, 3-c</p>", "<p>1-c, 2-b, 3-a</p>",
                                "<p>1-b, 2-c, 3-a</p>", "<p>1-c, 2-a, 3-b</p>"],
                    solution_en: "<p>49.(d) <strong>1-c. 2-a 3-b.</strong> India follows a multi-party system, with several political parties competing at the national and state levels. The USA has a two-party system, primarily dominated by the Democratic and Republican parties. China operates under a single-party system, where the Communist Party is the only legal political party.</p>",
                    solution_hi: "<p>49.(d) <strong>1-c. 2-a 3-b.</strong> भारत में बहुदलीय प्रणाली है, जिसमें राष्ट्रीय और राज्य स्तर पर कई राजनीतिक दल प्रतिस्पर्धा करते हैं। अमेरिका में द्वि-दलीय प्रणाली है, जिसमें मुख्य रूप से डेमोक्रेटिक और रिपब्लिकन पार्टियों का वर्चस्व है। चीन एकल-दल प्रणाली के तहत कार्य करता है, जहाँ कम्युनिस्ट पार्टी एकमात्र कानूनी राजनीतिक पार्टी है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Where was the India House established to work for India&rsquo;s liberation from British possession?</p>",
                    question_hi: "<p>50. ब्रिटिश कब्जे से भारत को आजाद कराने के लिए काम करने हेतु इंडिया हाउस (India House) की स्थापना कहाँ की गई थी?</p>",
                    options_en: ["<p>London</p>", "<p>San Francisco</p>", 
                                "<p>Kabul</p>", "<p>Berlin</p>"],
                    options_hi: ["<p>लंदन</p>", "<p>सैन फ्रांसिस्को</p>",
                                "<p>काबुल</p>", "<p>बर्लिन</p>"],
                    solution_en: "<p>50.(a) <strong>London.</strong> India House was established in 1905 by Shyamji Krishna Varma. It served as a hub for Indian nationalists and revolutionaries working for India\'s liberation from British rule. Shyamji Krishna Varma also formed &lsquo;India Home Rule Society&rdquo;. He also started &ldquo;The Indian Sociologist&rdquo; in London.</p>",
                    solution_hi: "<p>50.(a) <strong>लंदन।</strong> इंडिया हाउस की स्थापना 1905 में श्यामजी कृष्ण वर्मा ने लंदन में की थी। यह भारतीय राष्ट्रीयतावादियों और क्रांतिकारियों के लिए एक केंद्र के रूप में कार्य करता था, जो भारत को ब्रिटिश शासन से स्वतंत्रता दिलाने के लिए संघर्ष कर रहे थे। श्यामजी कृष्ण वर्मा ने \'इंडिया होम रूल सोसाइटी\' का भी गठन किया। उन्होंने लंदन में \"द इंडियन सोशियोलॉजिस्ट\" की भी शुरुआत की।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51.&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>25</mn></msup><mo>+</mo><msup><mn>6</mn><mn>26</mn></msup><mo>+</mo><msup><mn>6</mn><mn>27</mn></msup><mo>+</mo><msup><mn>6</mn><mn>28</mn></msup></math>&nbsp;is divisible by :</p>",
                    question_hi: "<p>51.&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>25</mn></msup><mo>+</mo><msup><mn>6</mn><mn>26</mn></msup><mo>+</mo><msup><mn>6</mn><mn>27</mn></msup><mo>+</mo><msup><mn>6</mn><mn>28</mn></msup></math>&nbsp;निम्न से किस संख्या से विभाज्य है ?</p>",
                    options_en: ["<p>254</p>", "<p>259</p>", 
                                "<p>256</p>", "<p>255</p>"],
                    options_hi: ["<p>254</p>", "<p>259</p>",
                                "<p>256</p>", "<p>255</p>"],
                    solution_en: "<p>51.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>25</mn></msup><mo>+</mo><msup><mn>6</mn><mn>26</mn></msup><mo>+</mo><msup><mn>6</mn><mn>27</mn></msup><mo>+</mo><msup><mn>6</mn><mn>28</mn></msup></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>25</mn></msup></math> ( 1 + 6 + 6&sup2; + 6&sup3; )<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>25</mn></msup></math> ( 1 +6 + 36 + 216)<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>25</mn></msup></math>&nbsp;(259)<br>Hence the given expression is divisible by 259.</p>",
                    solution_hi: "<p>51.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>25</mn></msup><mo>+</mo><msup><mn>6</mn><mn>26</mn></msup><mo>+</mo><msup><mn>6</mn><mn>27</mn></msup><mo>+</mo><msup><mn>6</mn><mn>28</mn></msup></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>25</mn></msup></math> ( 1 + 6 + 6&sup2; + 6&sup3; )<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>25</mn></msup></math> ( 1 +6 + 36 + 216)<br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>6</mn><mn>25</mn></msup></math> (259)<br>अतः दिया गया व्यंजक 259 से विभाज्य है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The average weight of a family of five members (in kg) whose weights are 40 kg, 49 kg, 56 kg, 65 kg and 38 kg is:</p>",
                    question_hi: "<p>52. 40 kg, 49 kg, 56 kg, 65 kg और 38 kg वज़न वाले पांच सदस्यों के एक परिवार का औसत वजन (kg में) कितना होगा?</p>",
                    options_en: ["<p>48.6</p>", "<p>49.6</p>", 
                                "<p>50.6</p>", "<p>51.6</p>"],
                    options_hi: ["<p>48.6</p>", "<p>49.6</p>",
                                "<p>50.6</p>", "<p>51.6</p>"],
                    solution_en: "<p>52.(b) Average weight of a family = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>49</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>56</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>65</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>38</mn></mrow><mn>5</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>248</mn><mn>5</mn></mfrac></mstyle></math> = 49.6 kg</p>",
                    solution_hi: "<p>52.(b) एक परिवार का औसत वजन = <math display=\"inline\"><mfrac><mrow><mn>40</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>49</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>56</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>65</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>38</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>248</mn><mn>5</mn></mfrac></mstyle></math> = 49.6 kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. A function of two variable varies directly with x and inversely with y. Determine function when x = 5 and y = 3. Given that, for x = 0 and y = 1, f = 15 and for x = 1 and y = 15, f = 2.</p>",
                    question_hi: "<p>53. दो चर का एक फलन x के अनुक्रमानुपाती है और y के व्&zwj;युत्&zwj;क्रमानुपाती है। x = 5 और y = 3 होने पर फलन ज्ञात कीजिए। दिया है कि, x = 0 और y = 1 के लिए f = 15 है तथा x = 1 और y = 15 के लिए f = 2 है।</p>",
                    options_en: ["<p>10</p>", "<p>2</p>", 
                                "<p>5</p>", "<p>15</p>"],
                    options_hi: ["<p>10</p>", "<p>2</p>",
                                "<p>5</p>", "<p>15</p>"],
                    solution_en: "<p>53.(a) <br>Here two values of function is given hence it is of two variable .<br>According to the question,<br><math display=\"inline\"><mi>f</mi></math>&prop; x and f &prop;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>y</mi></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math>f =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub><mi mathvariant=\"normal\">x</mi><mo>+</mo><msub><mi mathvariant=\"normal\">k</mi><mn>2</mn></msub><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">y</mi></mfrac></mstyle></math>&hellip; (i)<br>Where <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub></math> and&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>2</mn></msub></math> are constant<br>On putting <math display=\"inline\"><mi>x</mi></math> = 0 , y = 1, f = 15 in (i) we get <br><math display=\"inline\"><mi>f</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub><mi mathvariant=\"normal\">x</mi><mo>+</mo><msub><mi mathvariant=\"normal\">k</mi><mn>2</mn></msub><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">y</mi></mfrac></mstyle></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>2</mn></msub></math> = 15 &hellip; (ii)<br>On putting <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi mathvariant=\"normal\">x</mi></math> = 1 , y = 15, f = 2 in (i) we get<br><math display=\"inline\"><mi>f</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub><mi mathvariant=\"normal\">x</mi><mo>+</mo><msub><mi mathvariant=\"normal\">k</mi><mn>2</mn></msub><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">y</mi></mfrac></mstyle></math><br>2 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub><mo>+</mo><mn>15</mn><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>15</mn></mfrac></mstyle></math>&hellip; [from (ii)]<br><math display=\"inline\"><msub><mrow><mi>k</mi></mrow><mrow><mn>1</mn></mrow></msub></math> = 1<br>Now,<br>when <math display=\"inline\"><mi>x</mi></math> = 5 and y = 3<br><math display=\"inline\"><mo>&#8658;</mo></math> f = 1 &times; 5 + 15 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> f = 5 + 5 = 10</p>",
                    solution_hi: "<p>53.(a) <br>यहां फलन के दो मान दिए गए हैं इसलिए यह दो चर का है।<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mi>f</mi></math> &prop; x और f &prop;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>y</mi></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math> f =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub><mi mathvariant=\"normal\">x</mi><mo>+</mo><msub><mi mathvariant=\"normal\">k</mi><mn>2</mn></msub><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">y</mi></mfrac></mstyle></math>&hellip; (i)<br>जहाँ <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub></math> और&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>2</mn></msub></math> स्थिरांक (constant) हैं<br>समीकरण (i) में x&nbsp;= 0 , y = 1, f = 15 रखने पर हमें प्राप्त होता है, <br><math display=\"inline\"><mi>f</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub><mi mathvariant=\"normal\">x</mi><mo>+</mo><msub><mi mathvariant=\"normal\">k</mi><mn>2</mn></msub><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">y</mi></mfrac></mstyle></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>2</mn></msub></math> = 15 &hellip; (ii)<br>समीकरण (i) में <math display=\"inline\"><mi>x</mi></math> = 1 , y = 15, f = 2 रखने पर हमें प्राप्त होता है, <br><math display=\"inline\"><mi>f</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub><mi mathvariant=\"normal\">x</mi><mo>+</mo><msub><mi mathvariant=\"normal\">k</mi><mn>2</mn></msub><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">y</mi></mfrac></mstyle></math><br>2 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub><mo>+</mo><mn>15</mn><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>15</mn></mfrac></mstyle></math>&hellip; [(ii) से]<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">k</mi><mn>1</mn></msub></math> = 1<br>अब,<br>जब&nbsp; x = 5 और y = 3<br><math display=\"inline\"><mo>&#8658;</mo></math>f = 1 &times; 5 + 15 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>3</mn></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math>f = 5 + 5 = 10</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. If a = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>4</mn></msup><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><msup><mn>7</mn><mn>3</mn></msup></math>, b =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>4</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>7</mn><mn>4</mn></msup></math> and c =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>4</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>4</mn></msup><mo>&#215;</mo><mn>7</mn></math>, then the HCF of a, b and c is:</p>",
                    question_hi: "<p>54. यदि a = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>4</mn></msup><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><msup><mn>7</mn><mn>3</mn></msup></math>, b =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>4</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>7</mn><mn>4</mn></msup><mo>&#160;</mo></math>और c = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>4</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>4</mn></msup><mo>&#215;</mo><mn>7</mn></math>, है, X तो a, b और c का एचसीएफ (HCF) क्या होगा ?</p>",
                    options_en: ["<p>1260</p>", "<p>1575</p>", 
                                "<p>630</p>", "<p>180</p>"],
                    options_hi: ["<p>1260</p>", "<p>1575</p>",
                                "<p>630</p>", "<p>180</p>"],
                    solution_en: "<p>54.(a)<br>a = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>4</mn></msup><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><msup><mn>7</mn><mn>3</mn></msup></math><br>b = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>4</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>7</mn><mn>4</mn></msup></math><br>c = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>4</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>4</mn></msup><mo>&#215;</mo><mn>7</mn></math><br>HCF of a, b, c = 2&sup2; &times; 3&sup2; &times; 5 &times; 7 = 36 &times; 35 = 1260</p>",
                    solution_hi: "<p>54.(a)<br>a =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>4</mn></msup><mo>&#215;</mo><mn>5</mn><mo>&#215;</mo><msup><mn>7</mn><mn>3</mn></msup></math><br>b =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>3</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>4</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>7</mn><mn>4</mn></msup></math><br>c =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mn>2</mn><mn>4</mn></msup><mo>&#215;</mo><msup><mn>3</mn><mn>2</mn></msup><mo>&#215;</mo><msup><mn>5</mn><mn>4</mn></msup><mo>&#215;</mo><mn>7</mn></math><br>a, b, c का HCF = 2&sup2; &times; 3&sup2; &times; 5 &times; 7 = 36 &times; 35 = 1260</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If sec&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>29</mn><mi>&#160;</mi></mrow><mrow><mn>20</mn><mi>&#160;</mi></mrow></mfrac></mstyle></math> where 0 &lt; &theta; &lt; 90&deg;, then what is the value of 3cosec&theta; + 3cot&theta;?</p>",
                    question_hi: "<p>55. यदि sec&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>29</mn><mn>20</mn></mfrac></mstyle></math> है, जहाँ 0 &lt; &theta; &lt; 90&deg;है, तो 3cosec&theta; + 3cot&theta; का मान क्या होगा?</p>",
                    options_en: ["<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn><mi>&#160;</mi></mrow></mfrac></math></p>", "<p>7</p>", 
                                "<p>14</p>", "<p>49</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>7</mn><mi>&#160;</mi></mrow><mrow><mn>3</mn><mi>&#160;</mi></mrow></mfrac></math></p>", "<p>7</p>",
                                "<p>14</p>", "<p>49</p>"],
                    solution_en: "<p>55.(b) <strong>Given</strong> : sec&theta; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>29</mn><mn>20</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>hypotenuse</mi><mi>base</mi></mfrac></mstyle></math><br>By pythagorean triplet (20 , 21 , 29)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782389.png\" alt=\"rId55\" width=\"149\" height=\"119\"><br>Then,<br>3cosec&theta; + 3cot&theta;<br>= 3 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>29</mn><mn>21</mn></mfrac></math> + 3 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>20</mn><mn>21</mn></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mn>87</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>147</mn><mn>21</mn></mfrac></mstyle></math> = 7</p>",
                    solution_hi: "<p>55.(b) <strong>दिया गया है</strong> : sec&theta;&nbsp;=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>29</mn><mn>20</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi>&#2310;&#2343;&#2366;&#2352;</mi></mfrac></mstyle></math><br>(20 , 21 , 29) पायथागॉरियन त्रिक द्वारा<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782389.png\" alt=\"rId55\" width=\"149\" height=\"119\"><br>इसलिए ,<br>3cosec&theta; + 3cot&theta;<br>= 3 &times; <math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> + 3 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>20</mn><mn>21</mn></mfrac></mstyle></math><br>= <math display=\"inline\"><mfrac><mrow><mn>87</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>21</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>147</mn><mn>21</mn></mfrac></mstyle></math> = 7</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. Study the given graph carefully and answer the questions that follows. The graph shows the demand and production of different companies.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782602.png\" alt=\"rId56\" width=\"330\" height=\"170\"> <br>Lowest production is what percentage of the highest demand ?</p>",
                    question_hi: "<p>56. दिए गए ग्राफ़ का ध्यानपूर्वक अध्ययन कीजिए और निम्नलिखित प्रश्न का उत्तर दीजिए। ग्राफ़ विभिन्न कंपनियों की मांग और उत्पादन को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798782849.png\" alt=\"rId57\" width=\"309\" height=\"170\"> <br>न्यूनतम उत्पादन सर्वाधिक मांग का कितना प्रतिशत है?</p>",
                    options_en: ["<p>29%</p>", "<p>30%</p>", 
                                "<p>71%</p>", "<p>70%</p>"],
                    options_hi: ["<p>29%</p>", "<p>30%</p>",
                                "<p>71%</p>", "<p>70%</p>"],
                    solution_en: "<p>56.(a) <br>required% = <math display=\"inline\"><mfrac><mrow><mn>1450</mn></mrow><mrow><mn>5000</mn></mrow></mfrac></math> &times; 100 = 29%</p>",
                    solution_hi: "<p>56.(a) <br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mn>1450</mn></mrow><mrow><mn>5000</mn></mrow></mfrac></math> &times; 100 = 29%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. From a point A, a tangent line is drawn to the circle of radius 7 units. From the same point, a secant is drawn to the circle which cuts the circle at B and C and the point B is near A than C. What is the length of BC in units if the length of the tangent to the circle from point A is 21 units and the length of AB is 14 units?</p>",
                    question_hi: "<p>57. किसी बिंदु A से 7 इकाई त्रिज्या वाले वृत्त पर एक स्पर्श रेखा खींची जाती है। उसी बिंदु से वृत्त पर एक छेदक रेखा खींची जाती है जो वृत्त को B और C पर काटती है और बिंदु B, C की तुलना में A के निकट है। यदि बिंदु A से वृत्त की स्पर्श रेखा की लंबाई 21 इकाई है और AB की लंबाई 14 इकाई है, तो इकाई में BC की लंबाई कितनी है?</p>",
                    options_en: ["<p>18.5</p>", "<p>16.5</p>", 
                                "<p>15.5</p>", "<p>17.5</p>"],
                    options_hi: ["<p>18.5</p>", "<p>16.5</p>",
                                "<p>15.5</p>", "<p>17.5</p>"],
                    solution_en: "<p>57.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798783085.png\" alt=\"rId58\" width=\"270\" height=\"123\"><br>We know that,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>AT</mi><mn>2</mn></msup></math> = AC &times; AB<br><math display=\"inline\"><msup><mrow><mn>21</mn></mrow><mrow><mn>2</mn></mrow></msup></math> = (14 + BC) &times; 14<br>441 = 196 + 14 BC<br>14 BC = 245<br>BC = <math display=\"inline\"><mfrac><mrow><mn>245</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math> = 17.5 units</p>",
                    solution_hi: "<p>57.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798783085.png\" alt=\"rId58\" width=\"270\" height=\"123\"><br>हम जानते हैं,<br>AT&sup2; = AC &times; AB<br>21&sup2; = (14 + BC) &times; 14<br>441 = 196 + 14 BC<br>14 BC = 245<br>BC = <math display=\"inline\"><mfrac><mrow><mn>245</mn></mrow><mrow><mn>14</mn></mrow></mfrac></math> = 17.5 इकाई</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. In a race of 400 m, Bhim gave Saral a head start by 20 m at the start of the race. Saral took 48 seconds to complete the race. What was the speed (in m/s) of Bhim if Saral defeated Bhim by a margin of 2 seconds?</p>",
                    question_hi: "<p>58. 400 m की एक दौड़ में भीम ने दौड़ की शुरुआत में सरल को 20 m से बढ़त दिलाई। सरल ने दौड़ पूरी करने में 48 सेकेंड का समय लिया। यदि सरल ने भीम को 2 सेकंड के अंतर से हरा दिया, तो भीम की गति (m/s में) क्या थी?</p>",
                    options_en: ["<p>7.6</p>", "<p>8</p>", 
                                "<p>8.7</p>", "<p>7.4</p>"],
                    options_hi: ["<p>7.6</p>", "<p>8</p>",
                                "<p>8.7</p>", "<p>7.4</p>"],
                    solution_en: "<p>58.(b)<br>Time taken by saral to complete the race = 48sec <br>So , time taken by Bhim to complete the race =( 48 + 2 ) sec<br>Now , Distance covered by Bhim = 400m<br>Hence required speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>400</mn><mrow><mn>48</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>400</mn><mn>50</mn></mfrac></mstyle></math> = 8m/s</p>",
                    solution_hi: "<p>58.(b)<br>सरल को दौड़ पूरी करने में लगा समय = 48 सेकंड<br>तो, भीम द्वारा दौड़ पूरी करने में लिया गया समय =( 48 + 2 ) सेकंड<br>अब, भीम द्वारा तय की गई दूरी = 400 मीटर<br>अतः आवश्यक गति = <math display=\"inline\"><mfrac><mrow><mn>400</mn></mrow><mrow><mn>48</mn><mo>+</mo><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>400</mn><mn>50</mn></mfrac></mstyle></math> = 8m/s</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Simplify<br><math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn></mrow><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math></p>",
                    question_hi: "<p>59. निम्नलिखित को सरल कीजिए <br><math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn></mrow><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math></p>",
                    options_en: ["<p>30.2</p>", "<p>23.6</p>", 
                                "<p>28.7</p>", "<p>25.5</p>"],
                    options_hi: ["<p>30.2</p>", "<p>23.6</p>",
                                "<p>28.7</p>", "<p>25.5</p>"],
                    solution_en: "<p>59.(b)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mo>)</mo><mn>3</mn></msup><mo>+</mo><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>3</mn></msup><mo>+</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mo>)</mo><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn></mrow><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mo>)</mo><mn>2</mn></msup><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mo>)</mo><mn>2</mn></msup><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mo>)</mo><mn>2</mn></msup><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math><br><strong>Identity used:</strong>- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">a</mi><msup><mo>)</mo><mn>3</mn></msup><mo>+</mo><mo>(</mo><mi mathvariant=\"normal\">b</mi><msup><mo>)</mo><mn>3</mn></msup><mo>+</mo><mo>(</mo><mi mathvariant=\"normal\">c</mi><msup><mo>)</mo><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>abc</mi></math> = (a +b+c)[(a)&sup2;+(b)&sup2;+ (c)&sup2;-ab-bc -ac]<br><math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn></mrow><mrow><mfrac><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn></mrow><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow></mfrac></mrow></mfrac></math> = 8.3 + 9.2 + 6.1 = 23.6</p>",
                    solution_hi: "<p>59.(b)<br><math display=\"block\"><mfrac><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn></mrow><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn></mrow></mfrac></math><br><strong>प्रयुक्त सूत्र :-</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><mi mathvariant=\"normal\">a</mi><msup><mo>)</mo><mn>3</mn></msup><mo>+</mo><mo>(</mo><mi mathvariant=\"normal\">b</mi><msup><mo>)</mo><mn>3</mn></msup><mo>+</mo><mo>(</mo><mi mathvariant=\"normal\">c</mi><msup><mo>)</mo><mn>3</mn></msup><mo>-</mo><mn>3</mn><mi>abc</mi></math> = (a +b+c)[(a)&sup2;+(b)&sup2;+ (c)&sup2;-ab-bc -ac]<br><math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn></mrow><mrow><mfrac><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>9</mn><mo>.</mo><mn>2</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>+</mo><mo>(</mo><mn>6</mn><mo>.</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup><mo>-</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn></mrow><mrow><mo>(</mo><mn>8</mn><mo>.</mo><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>9</mn><mo>.</mo><mn>2</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>6</mn><mo>.</mo><mn>1</mn><mo>)</mo></mrow></mfrac></mrow></mfrac></math> = 8.3 + 9.2 + 6.1 = 23.6</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. A vendor started selling vegetables at ₹10 per kg, but couldn&rsquo;t find buyers at this rate. So he reduced the price to ₹7.2 per kg, but uses a faulty weight of 900 g instead of 1 kg. Find the percentage change in the actual price.</p>",
                    question_hi: "<p>60. एक विक्रेता ने ₹10 प्रति kg के हिसाब से सब्जियाँ बेचना शुरू किया, लेकिन इस दर पर खरीदार नहीं मिला। इसलिए उसने कीमत घटाकर ₹7.2 प्रति kg कर दी, लेकिन 1 kg के बजाय 900 g के दोषपूर्ण वजन का उपयोग किया। वास्तविक मूल्य में प्रतिशत परिवर्तन ज्ञात कीजिए।</p>",
                    options_en: ["<p>10%</p>", "<p>15%</p>", 
                                "<p>20%</p>", "<p>25%</p>"],
                    options_hi: ["<p>10%</p>", "<p>15%</p>",
                                "<p>20%</p>", "<p>25%</p>"],
                    solution_en: "<p>60.(c) <math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>A</mi><mi>c</mi><mi>t</mi><mi>u</mi><mi>a</mi><mi>l</mi><mi>&#160;</mi><mi>P</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>72</mn><mn>10</mn></mfrac></mstyle><mo>&#215;</mo><mstyle displaystyle=\"false\"><mfrac><mn>1000</mn><mn>900</mn></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>72</mn><mn>90</mn></mfrac></mstyle></math><br>Percentage change in the actual price = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>90</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>72</mn></mrow><mn>90</mn></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>60.(c) <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2357;&#2366;&#2360;&#2381;&#2340;&#2357;&#2367;&#2325;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2368;&#2350;&#2340;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>72</mn><mn>10</mn></mfrac><mo>&#215;</mo><mfrac><mn>1000</mn><mn>900</mn></mfrac><mo>=</mo><mfrac><mn>72</mn><mn>90</mn></mfrac></mstyle></math><br>वास्तविक कीमत में प्रतिशत परिवर्तन = <math display=\"inline\"><mfrac><mrow><mn>90</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>72</mn></mrow><mrow><mn>90</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. X,Y and Z can do a work in 24 days, 5 days and 12 days, respectively. In how many days can they do the same work if they work together?</p>",
                    question_hi: "<p>61. X, Y और Z एक काम को क्रमशः 24 दिन, 5 दिन और 12 दिन में कर सकते हैं। यदि वे साथ मिलकर काम करते हैं तो वे उसी काम को कितने दिनों में कर सकते हैं?</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> days</p>", "<p><math display=\"inline\"><mn>3</mn><mfrac><mrow><mn>1</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> days</p>", 
                                "<p>4 days</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> days</p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> दिन</p>", "<p><math display=\"inline\"><mn>3</mn><mfrac><mrow><mn>1</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math> दिन</p>",
                                "<p>4 दिन</p>", "<p><math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> दिन</p>"],
                    solution_en: "<p>61.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798783467.png\" alt=\"rId60\" width=\"209\" height=\"140\"><br><br>Total efficiency = 5 + 24 + 10 = 39 unit<br>Total work = 120 unit<br>Time taken together = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>120</mn><mn>39</mn></mfrac><mo>=</mo><mfrac><mn>40</mn><mn>13</mn></mfrac><mo>=</mo><mn>3</mn><mfrac><mn>1</mn><mn>13</mn></mfrac></math>&nbsp;days</p>",
                    solution_hi: "<p>61.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798783615.png\" alt=\"rId61\" width=\"210\" height=\"149\"><br>कुल क्षमता = 5 + 24 + 10 = 39 इकाई<br>कुल कार्य = 120 इकाई<br>कुल मिलाकर लिया गया समय =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>120</mn><mn>39</mn></mfrac><mo>=</mo><mfrac><mn>40</mn><mn>13</mn></mfrac><mo>=</mo><mn>3</mn><mfrac><mn>1</mn><mn>13</mn></mfrac></mstyle></math>&nbsp;दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. If &theta;&nbsp;is an acute angle and cot&theta; + tan&theta; = 2, then find the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>12</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cot</mi><mn>12</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mn>2</mn><msup><mi>tan</mi><mn>5</mn></msup><msup><mi>&#952;cot</mi><mn>7</mn></msup><mi mathvariant=\"normal\">&#952;</mi></math>.</p>",
                    question_hi: "<p>62. यदि &theta; न्यून कोण है और cot&theta; + tan&theta; = 2 है, तो&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>12</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cot</mi><mn>12</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mn>2</mn><msup><mi>tan</mi><mn>5</mn></msup><msup><mi>&#952;cot</mi><mn>7</mn></msup><mi mathvariant=\"normal\">&#952;</mi></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>2</p>", "<p>3</p>", 
                                "<p>4</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>3</p>",
                                "<p>4</p>", "<p>1</p>"],
                    solution_en: "<p>62.(c)<br>cot&theta; + tan&theta; = 2<br>Let &theta; = 45&deg; <br>cot 45&deg; + tan 45&deg; = 2<br>1 + 1 = 2 <math display=\"inline\"><mo>&#8658;</mo></math> 2 = 2 (LHS = RHS)<br>now,&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>12</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cot</mi><mn>12</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mn>2</mn><mo>&#160;</mo><msup><mi>tan</mi><mn>5</mn></msup><msup><mi>&#952;cot</mi><mn>7</mn></msup><mi mathvariant=\"normal\">&#952;</mi></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>tan</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>12</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>cot</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>12</mn></msup><mo>+</mo><mn>2</mn><mo>&#160;</mo><msup><mrow><mo>(</mo><mi>tan</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>5</mn></msup><msup><mrow><mo>(</mo><mi>cot</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>7</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math>1 + 1 + 2(1)(1) = 4</p>",
                    solution_hi: "<p>62.(c)<br>cot&theta; + tan&theta; = 2<br>माना &theta; = 45&deg; <br>cot 45&deg; + tan 45&deg; = 2<br>1 + 1 = 2 <math display=\"inline\"><mo>&#8658;</mo></math> 2 = 2 (LHS = RHS)<br>अब ,<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>tan</mi><mn>12</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><msup><mi>cot</mi><mn>12</mn></msup><mi mathvariant=\"normal\">&#952;</mi><mo>+</mo><mn>2</mn><mo>&#160;</mo><msup><mi>tan</mi><mn>5</mn></msup><msup><mi>&#952;cot</mi><mn>7</mn></msup><mi mathvariant=\"normal\">&#952;</mi></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>tan</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mn>12</mn><mo>&#160;</mo></mrow></msup><mo>+</mo><msup><mrow><mo>(</mo><mi>cot</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>12</mn></msup><mo>+</mo><mn>2</mn><mo>&#160;</mo><msup><mrow><mo>(</mo><mi>tan</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>5</mn></msup><msup><mrow><mo>(</mo><mi>cot</mi><mo>&#160;</mo><mn>45</mn><mo>&#176;</mo><mo>)</mo></mrow><mn>7</mn></msup></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 1 + 1 + 2(1)(1) = 4</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. By selling an article for ₹2,160, Prashant allows a 20% discount and earns 28% profit. If the article is sold without any discount, the profit will be:</p>",
                    question_hi: "<p>63. एक वस्तु पर 20% की छूट देने के बाद प्रशांत उसे ₹2,160 में बेचता है और 28% का लाभ कमाता है। यदि वस्तु को बिना किसी छूट के बेचा जाए, तो लाभ कितना होगा?</p>",
                    options_en: ["<p>50%</p>", "<p>55%</p>", 
                                "<p>65%</p>", "<p>60%</p>"],
                    options_hi: ["<p>50%</p>", "<p>55%</p>",
                                "<p>65%</p>", "<p>60%</p>"],
                    solution_en: "<p>63.(d)<br>ATQ,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>CP</mi><mi>MP</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mi mathvariant=\"normal\">D</mi><mo>)</mo><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mi mathvariant=\"normal\">P</mi><mo>%</mo></mrow></mfrac></mstyle></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>CP</mi><mi>MP</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>80</mn><mn>128</mn></mfrac></mstyle></math>=<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>5</mn><mn>8</mn></mfrac></mstyle></math><br>Since, there is no discount given , then MP = SP <br>Hence, profit% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>8</mn><mo>-</mo><mn>5</mn></mrow><mn>5</mn></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>5</mn></mfrac></mstyle></math>&times;100 = 60%</p>",
                    solution_hi: "<p>63.(d)<br>प्रश्न के अनुसार,,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>-</mo><mi>&#2331;&#2370;&#2335;</mi><mo>)</mo><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mi>&#2354;&#2366;&#2349;</mi><mo>%</mo></mrow></mfrac></mstyle></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>80</mn><mn>128</mn></mfrac></mstyle></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>5</mn><mn>8</mn></mfrac></mstyle></math><br>चूँकि कोई छूट नहीं दी गई है, तो अंकित मूल्य = विक्रय मूल्य <br>अतः लाभ% = <math display=\"inline\"><mfrac><mrow><mn>8</mn><mo>-</mo><mn>5</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>&times;100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>5</mn></mfrac></mstyle></math>&times;100 = 60%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A police station, a bank and a safe house for the thief are in a straight line with a bank in between. The distance between the police station and the safe house is four times the distance between the bank and the safe house. After looting the bank at 9:15 A.M, the thief runs away. A policeman gets the information instantly and chases him at the speed of 16 km/h. The policeman catches the thief at the gate of the safe house in fifteen minutes. What is the distance between the bank and the police station?</p>",
                    question_hi: "<p>64. एक पुलिस थाना एक बैंक और चोर के लिए एक सेफ हाउस एक सीधी रेखा में हैं, जिसमें बैंक बीच में स्थित है। पुलिस थाना और सेफ हाउस के बीच की दूरी बैंक और सेफ हाउस के बीच की दूरी की चार गुना है। 9:15 A.M पर बैंक लूटने के बाद चोर भाग जाता है। एक पुलिसकर्मी तुरंत सूचना प्राप्त करता है और 16 km/h की चाल से चोर का पीछा करता है। पुलिसकर्मी पंद्रह मिनट में चोर को सेफ हाउस के गेट पर पकड़ लेता है। बैंक और पुलिस थाने के बीच की दूरी कितनी है?</p>",
                    options_en: ["<p>2 km</p>", "<p>1 km</p>", 
                                "<p>4 km</p>", "<p>3 km</p>"],
                    options_hi: ["<p>2 km</p>", "<p>1 km</p>",
                                "<p>4 km</p>", "<p>3 km</p>"],
                    solution_en: "<p>64.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798783780.png\" alt=\"rId62\" width=\"331\" height=\"124\"><br>Time taken by police to catch the thief = 15 minute = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hour<br>Distance covered by police = 4x , speed of police = 16 km/h<br>So, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>4</mn><mi>x</mi></mrow><mn>16</mn></mfrac></mstyle></math> &rArr; x = 1km<br>Required distance (3x) = 3 km</p>",
                    solution_hi: "<p>64.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798783989.png\" alt=\"rId63\" width=\"315\" height=\"124\"><br>पुलिस द्वारा चोर को पकड़ने में लगा समय = 15 मिनट = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटा<br>पुलिस द्वारा तय की गई दूरी = 4x , पुलिस की गति = 16 km/h<br>तो , <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>4</mn><mi>x</mi></mrow><mn>16</mn></mfrac></mstyle></math> &rArr; x = 1km<br>आवश्यक दूरी (3x) = 3 km</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A battery manufacturer manufactures five different types of batteries. The total revenue for the year 2020 is ₹25,00,000 and 20,000 units were exported in 2020. The distribution of revenue and units for the five different types of batteries is shown in the charts<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798784241.png\" alt=\"rId64\" width=\"261\" height=\"200\"> <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798784347.png\" alt=\"rId65\" width=\"232\" height=\"196\"> <br>Which type of battery provides the lowest revenue per unit?</p>",
                    question_hi: "<p>65. एक बैटरी निर्माता पांच अलग-अलग प्रकार की बैटरियों का निर्माण करता है। वर्ष 2020 के लिए कुल राजस्व ₹25,00,000 है और 2020 में 20,000 इकाइयों का निर्यात किया गया था। पांच अलग-अलग प्रकार की बैटरियों के लिए राजस्व और इकाइयों का वितरण चार्ट में दिखाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798784502.png\" alt=\"rId66\" width=\"278\" height=\"215\"><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798784616.png\" alt=\"rId67\" width=\"240\" height=\"215\"> <br>किस प्रकार की बैटरी प्रति इकाई सबसे कम राजस्व प्रदान करती है?</p>",
                    options_en: ["<p>Sodium</p>", "<p>Li-ion</p>", 
                                "<p>M-Air</p>", "<p>Lead acid</p>"],
                    options_hi: ["<p>सोडियम</p>", "<p>ली-आयन</p>",
                                "<p>एम-एयर</p>", "<p>लेड ऐसिड</p>"],
                    solution_en: "<p>65.(d)<br>Export units of sodium = 20000 &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 400<br>Export units of Li-ion = 200 &times; 30 = 6000<br>Export units of M-Air = 200 &times; 20 = 4000<br>Export units of Lead acid = 200 &times; <math display=\"inline\"><mn>35</mn></math> = 7000<br>revenue of sodium per units = <math display=\"inline\"><mfrac><mrow><mn>2500000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>400</mn></mrow></mfrac></math> = 312.5<br>revenue of Li-ion per units = <math display=\"inline\"><mfrac><mrow><mn>2500000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>35</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6000</mn></mrow></mfrac></math> = 145.83<br>revenue of M-Air per units = <math display=\"inline\"><mfrac><mrow><mn>2500000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4000</mn></mrow></mfrac></math> = 125<br>revenue of Lead acid per units = <math display=\"inline\"><mfrac><mrow><mn>2500000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>25</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7000</mn></mrow></mfrac></math> = 89.28<br>We can clearly see that the revenue per unit of lead acid battery is the lowest.</p>",
                    solution_hi: "<p>65.(d)<br>सोडियम की निर्यात इकाइयाँ = 20000 &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 400<br>ली-आयन की निर्यात इकाइयाँ = 200 &times; 30 = 6000<br>एम-एयर की निर्यात इकाइयाँ = 200 &times; 20 = 4000<br>लेड एसिड की निर्यात इकाइयाँ = 200 &times; <math display=\"inline\"><mn>35</mn></math> = 7000<br>प्रति यूनिट सोडियम का राजस्व = <math display=\"inline\"><mfrac><mrow><mn>2500000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>400</mn></mrow></mfrac></math> = 312.5<br>प्रति इकाई ली-आयन का राजस्व = <math display=\"inline\"><mfrac><mrow><mn>2500000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>35</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6000</mn></mrow></mfrac></math> = 145.83<br>प्रति यूनिट एम-एयर का राजस्व = <math display=\"inline\"><mfrac><mrow><mn>2500000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>20</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4000</mn></mrow></mfrac></math> = 125<br>प्रति यूनिट लेड एसिड का राजस्व = <math display=\"inline\"><mfrac><mrow><mn>2500000</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>25</mn></mrow><mrow><mn>100</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7000</mn></mrow></mfrac></math> = 89.28<br>हम स्पष्ट रूप से देख सकते हैं कि लेड एसिड बैटरी की प्रति यूनिट आय सबसे कम है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. In <math display=\"inline\"><mi>&#916;</mi></math>DEF, DE = 12 cm, EF = 15 cm, and &ang;DEF = 90&deg;. &Delta;DEF is congruent to &Delta;XYZ. If YZ = 15 cm, then what is the length of XZ?</p>",
                    question_hi: "<p>66. <math display=\"inline\"><mi>&#916;</mi></math>DEF में, DE = 12cm, EF = 15 cm , और &ang;DEF = 90&deg; है। &Delta;DEF, &Delta;XYZ के सर्वांगसम है। यदि YZ = 15cm है, तो XZ की लंबाई कितनी होगी?</p>",
                    options_en: ["<p>3<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>", "<p>2<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>", 
                                "<p>5<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>", "<p>4<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>"],
                    options_hi: ["<p>3<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>", "<p>2<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>",
                                "<p>5<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>", "<p>4<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>"],
                    solution_en: "<p>66.(a) If <math display=\"inline\"><mi>&#916;</mi></math>DEF is congruent to &Delta;XYZ their corresponding sides and angle will be equal to each other i.e DE = XY , EF = YZ , DF = XZ<br>In <math display=\"inline\"><mi>&#916;</mi></math>DEF , DF =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>12</mn><mn>2</mn></msup><mo>+</mo><msup><mn>15</mn><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>369</mn></msqrt></math> = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>41</mn></msqrt></math> cm<br>Hence DF = XZ = 3<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>66.(a) यदि <math display=\"inline\"><mi>&#916;</mi></math>DEF &Delta;XYZ के सर्वांगसम है तो उनकी संगत भुजाएं और कोण एक दूसरे के बराबर होंगे अर्थात DE = XY , EF = YZ , DF = XZ<br><math display=\"inline\"><mi>&#916;</mi></math>DEF में, DF =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>12</mn><mn>2</mn></msup><mo>+</mo><msup><mn>15</mn><mn>2</mn></msup></msqrt></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>369</mn></msqrt></math> = 3<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>41</mn></msqrt></math> cm<br>अतः DF = XZ = 3<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. A trader sells 25 kg of rice at ₹1,400. A customer asks for 15% discount. The shopkeeper agrees to it but instead of 1 kg he gives 10% less rice. What is the effective discount that the customer gets (correct to one decimal place)?</p>",
                    question_hi: "<p>67. एक व्यापारी 25 kg चावल ₹1,400 में बेचता है। एक ग्राहक 15% छूट की मांग करता है। दुकानदार इसके लिए सहमत हो जाता है लेकिन वह 1 kg के बजाय 10% कम चावल देता है। ग्राहक को मिलने वाली प्रभावी छूट क्या है (दशमलव के एक स्थान तक सही)?</p>",
                    options_en: ["<p>5.8%</p>", "<p>5.7%</p>", 
                                "<p>5.6%</p>", "<p>5.1%</p>"],
                    options_hi: ["<p>5.8%</p>", "<p>5.7%</p>",
                                "<p>5.6%</p>", "<p>5.1%</p>"],
                    solution_en: "<p>67.(c)<br>MP = 3000<br>SP = 3000 <math display=\"inline\"><mo>&#215;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>85</mn><mn>100</mn></mfrac></math> = ₹2040</p>",
                    solution_hi: "<p>67.(c)<br>अंकित मूल्य = 3000<br>विक्रय मूल्य = 3000 <math display=\"inline\"><mo>&#215;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mn>100</mn></mfrac><mo>&#215;</mo><mfrac><mn>85</mn><mn>100</mn></mfrac></math>&nbsp;= ₹2040</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. A rectangular courtyard is 4m 95cm long and 16m 65cm broad. It is to be paved with the square tiles of the same size. Find the least number of such square tiles required to pave the rectangular courtyard.</p>",
                    question_hi: "<p>68. एक आयताकार आंगन 4m 95cm लंबा और 16m 65cm चौड़ा है। इसे समान आकार की वर्गाकार टाइलों से पक्का किया जाना है। आयताकार आंगन को पक्का करने के लिए आवश्यक ऐसी वर्गाकार टाइलों की न्&zwj;यूनतम संख्या ज्ञात कीजिए।</p>",
                    options_en: ["<p>877</p>", "<p>944</p>", 
                                "<p>388</p>", "<p>407</p>"],
                    options_hi: ["<p>877</p>", "<p>944</p>",
                                "<p>388</p>", "<p>407</p>"],
                    solution_en: "<p>68.(d)<br>Length = 495 cm<br>Width = 1665 cm<br>Now, finding HCF of (495 and 1665)<br><math display=\"inline\"><mfrac><mrow><mn>1665</mn></mrow><mrow><mn>495</mn></mrow></mfrac></math> = 3 (remainder = 180)<br><math display=\"inline\"><mfrac><mrow><mn>495</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math> = 2 (remainder = 135)<br><math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>135</mn></mrow></mfrac></math> = 1 (remainder = 45)<br><math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> = 3 (remainder = 0)<br>HCF (495 , 1665) = 45<br>So, dimension of per required tile = 45 &times; 45 cm&sup2;<br>Area of the courtyard = 495 &times; 1665 cm&sup2;<br>Hence, required tiles = <math display=\"inline\"><mfrac><mrow><mn>495</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1665</mn></mrow><mrow><mn>45</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>45</mn></mrow></mfrac></math> = 11 &times; 37 = 407</p>",
                    solution_hi: "<p>68.(d)<br>लंबाई = 495 सेमी<br>चौड़ाई = 1665 सेमी<br>अब , (495 और 1665) का HCF<br><math display=\"inline\"><mfrac><mrow><mn>1665</mn></mrow><mrow><mn>495</mn></mrow></mfrac></math> = 3 (शेष = 180)<br><math display=\"inline\"><mfrac><mrow><mn>495</mn></mrow><mrow><mn>180</mn></mrow></mfrac></math> = 2 (शेष = 135)<br><math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>135</mn></mrow></mfrac></math> = 1 (शेष = 45)<br><math display=\"inline\"><mfrac><mrow><mn>135</mn></mrow><mrow><mn>45</mn></mrow></mfrac></math> = 3 (शेष = 0)<br>HCF (495 , 1665) = 45<br>तो, प्रति आवश्यक टाइल का आयाम = 45 &times; 45 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>2</mn></msup></math> <br>आंगन का क्षेत्रफल = 495 &times; 1665 <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mi>&#2360;&#2375;&#2350;&#2368;</mi><mn>2</mn></msup></math><br>अतः, आवश्यक टाइलें = <math display=\"inline\"><mfrac><mrow><mn>495</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1665</mn></mrow><mrow><mn>45</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>45</mn></mrow></mfrac></math> = 11 &times; 37 = 407</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A certain sum of money becomes seven times itself when invested at a certain rate of simple interest, in 14 years. How much time (in years, rounded off to 2 decimal places) will it take to become 18 times itself at the same rate?</p>",
                    question_hi: "<p>69. साधारण ब्याज की एक निश्चित दर पर निवेश करने पर एक निश्चित धनराशि 14 वर्षों में स्वयं की सात गुना हो जाती है। इसे उसी ब्&zwj;याज दर पर स्वयं की 18 गुना होने में कितना समय (वर्षों में, 2 दशमलव स्थानों तक पूर्णांकित) लगेगा?</p>",
                    options_en: ["<p>27.33</p>", "<p>39.67</p>", 
                                "<p>42.78</p>", "<p>35.5</p>"],
                    options_hi: ["<p>27.33</p>", "<p>39.67</p>",
                                "<p>42.78</p>", "<p>35.5</p>"],
                    solution_en: "<p>69.(b) <br>Amount = Principal (1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>rate</mi><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>time</mi></mrow><mn>100</mn></mfrac></math>)<br><math display=\"inline\"><mo>&#8658;</mo></math>7 P = P (1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>14</mn><mi>r</mi></mrow><mn>100</mn></mfrac></mstyle></math>)<br><math display=\"inline\"><mo>&#8658;</mo></math>r =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>300</mn><mn>7</mn></mfrac></mstyle></math> %<br>Now,<br>18 P = P (1 + <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow></mfrac></math> t)<br><math display=\"inline\"><mo>&#8658;</mo></math>17 =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>300</mn><mrow><mn>7</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac></mstyle></math> t &rArr; t = 39.67 years</p>",
                    solution_hi: "<p>69.(b) <br>मिश्रधन = मूलधन (1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2352;</mi><mo>&#160;&#160;</mo><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow><mn>100</mn></mfrac></math>)<br><math display=\"inline\"><mo>&#8658;</mo></math>7 P = P (1 +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>14</mn><mi>r</mi></mrow><mn>100</mn></mfrac></mstyle></math>)<br><math display=\"inline\"><mo>&#8658;</mo></math>r =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>300</mn><mn>7</mn></mfrac></mstyle></math> %<br>अब,<br>18 P = P (1 + <math display=\"inline\"><mfrac><mrow><mn>300</mn></mrow><mrow><mn>7</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow></mfrac></math> t)<br><math display=\"inline\"><mo>&#8658;</mo></math>17 =&nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>300</mn><mrow><mn>7</mn><mo>&#215;</mo><mn>100</mn></mrow></mfrac></mstyle></math>t &rArr; t = 39.67 वर्ष</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A grocer mixes two varieties of tea worth ₹120/kg and ₹125/kg in a ratio to produce a new variety worth ₹122/kg. What is the ratio of the varieties to be mixed to obtain the new variety?</p>",
                    question_hi: "<p>70. एक पंसारी ₹120/kg और ₹125/kg मूल्य की दो किस्मों की चाय को एक अनुपात में मिलाकर ₹122/kg मूल्य वाली एक नई किस्म तैयार करता है। नई किस्म प्राप्त करने के लिए मिश्रित की जाने वाली किस्मों का अनुपात कितना है?</p>",
                    options_en: ["<p>2 : 5</p>", "<p>4 : 4</p>", 
                                "<p>1 : 3</p>", "<p>3 : 2</p>"],
                    options_hi: ["<p>2 : 5</p>", "<p>4 : 4</p>",
                                "<p>1 : 3</p>", "<p>3 : 2</p>"],
                    solution_en: "<p>70.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798784789.png\" alt=\"rId68\" width=\"247\" height=\"150\"><br>Required ratio = 3 : 2</p>",
                    solution_hi: "<p>70.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744798785009.png\" alt=\"rId69\" width=\"241\" height=\"150\"><br>आवश्यक अनुपात = 3 : 2</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. The average of 12 numbers is 47. The average of the first 5 numbers is 45 and the average of the next 4 numbers is 52. If the 10th number is 10 less than the 11th number and is 5 more than the 12th number, then what is the average value of the 11th and 12th numbers?</p>",
                    question_hi: "<p>71. 12 संख्याओं का औसत 47 है। पहली 5 संख्याओं का औसत 45 हैऔर अगली 4 संख्याओं का औसत 52 है। यदि 10वीं संख्या, 11वीं संख्या से 10 कम है और 12वीं संख्या से 5 अधिक है, तो 11वींऔर 12वीं संख्याओं का औसत क्या होगा?</p>",
                    options_en: ["<p>44.5</p>", "<p>42.5</p>", 
                                "<p>46.5</p>", "<p>47.5</p>"],
                    options_hi: ["<p>44.5</p>", "<p>42.5</p>",
                                "<p>46.5</p>", "<p>47.5</p>"],
                    solution_en: "<p>71.(a)<br>Sum of the deviation of first 5 no from the overall average = (45 - 47) &times; 5 = -2 &times; 5 = -10<br>Sum of the deviation of next 4 no from the overall average = (52 - 47) &times; 4 = + 5 &times; 4 = +20<br>Net deviation = 20 - 10 = +10<br>Let 10th no be x.<br>Then, 11th no = (x+10) and 12th no = (x- 5)<br>According to the question,<br><math display=\"inline\"><mi>x</mi></math>+(x+10)+(x-5) = 47 &times; 3 -10 (since net deviation should be zero)<br>3<math display=\"inline\"><mi>x</mi></math>+5 = 131<br>3<math display=\"inline\"><mi>x</mi></math> = 131-5 = 126<br><math display=\"inline\"><mi>x</mi></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>126</mn><mn>3</mn></mfrac></mstyle></math> = 42<br>So, the average of 11th and 12th no = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>10</mn><mo>+</mo><mi>x</mi><mo>-</mo><mn>5</mn></mrow><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>5</mn></mrow><mn>2</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>42</mn><mo>+</mo><mn>5</mn></mrow><mn>2</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>89</mn><mn>2</mn></mfrac></mstyle></math> = 44.5</p>",
                    solution_hi: "<p>71.(a)<br>समग्र औसत से प्रथम 5 संख्याओं के विचलन का योग = (45 - 47) &times; 5 = -2 &times; 5 = -10<br>समग्र औसत से अगली 4 संख्याओं के विचलन का योग = (52 - 47) &times; 4 = + 5 &times; 4 = +20<br>शुद्ध विचलन = 20 - 10 = +10<br>माना कि 10वीं संख्या x&nbsp;है।<br>फिर, 11वीं संख्या = (x&nbsp;+ 10) और 12वीं संख्या = (x - 5)<br>प्रश्न के अनुसार,<br>x+(x+10)+(x-5) = 47 &times; 3 -10 (चूंकि शुद्ध विचलन शून्य होना चाहिए)<br>3x+5 = 131<br>3x&nbsp;= 131-5 = 126<br>x =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>126</mn><mn>3</mn></mfrac></math> = 42<br>तो, 11वीं और 12वीं संख्या का औसत = <math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>10</mn><mo>+</mo><mi>x</mi><mo>-</mo><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>5</mn></mrow><mn>2</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mo>&#215;</mo><mn>42</mn><mo>+</mo><mn>5</mn></mrow><mn>2</mn></mfrac></mstyle></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>89</mn><mn>2</mn></mfrac></mstyle></math> = 44.5</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If the mean and the mode of a particular data set are 36 and 63, respectively, then, using an empirical relation, find the value of the median of the same data set.</p>",
                    question_hi: "<p>72. यदि किसी विशेष डेटा सेट का माध्य और बहुलक क्रमशः 36 और 63 है, तो एक अनुभवजन्य संबंध का उपयोग करके, उसी डेटा सेट के माध्यिका का मान ज्ञात करें।</p>",
                    options_en: ["<p>39</p>", "<p>40</p>", 
                                "<p>45</p>", "<p>55</p>"],
                    options_hi: ["<p>39</p>", "<p>40</p>",
                                "<p>45</p>", "<p>55</p>"],
                    solution_en: "<p>72.(c)<br>Mode = 3 Median - 2 Mean<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>63</mn><mo>=</mo><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>Median</mi><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>36</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi>Median</mi><mo>=</mo><mn>63</mn><mo>+</mo><mn>72</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>Median</mi><mi mathvariant=\"normal\">&#160;</mi><mo>=</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mn>135</mn><mn>3</mn></mfrac><mo>=</mo><mn>45</mn></math></p>",
                    solution_hi: "<p>72.(c)<br>बहुलक = 3 माध्यिका - 2 माध्य<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>63</mn><mo>=</mo><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mn>36</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>3</mn><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</mi><mo>=</mo><mn>63</mn><mo>+</mo><mn>72</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>&#2350;&#2366;&#2343;&#2381;&#2351;&#2367;&#2325;&#2366;</mi><mo>=</mo><mfrac><mn>135</mn><mn>3</mn></mfrac><mo>=</mo><mn>45</mn></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. If a = <math display=\"inline\"><mo>(</mo><msqrt><mn>2</mn></msqrt><mi>&#160;</mi><mo>-</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></msup></math>, then the value of (a - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>)&sup3; + 3 (a - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>) is:</p>",
                    question_hi: "<p>73. यदि a = <math display=\"inline\"><mo>(</mo><msqrt><mn>2</mn></msqrt><mi>&#160;</mi><mo>-</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></mrow></msup></math> है, तो (a - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>)&sup3; + 3 (a - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>2</p>", "<p>-2</p>", 
                                "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>- <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    options_hi: ["<p>2</p>", "<p>-2</p>",
                                "<p><math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>", "<p>- <math display=\"inline\"><msqrt><mn>2</mn></msqrt></math></p>"],
                    solution_en: "<p>73.(b)<br>a&sup3; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></msup></math><br>On taking cube both side,<br>a&sup3; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1&nbsp;<br>and <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> + 1<br>Now,<br><math display=\"inline\"><msup><mrow><mo>(</mo><msup><mrow><mi>a</mi><mo>-</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow></mrow></msup></math>= a&sup3; -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></mstyle></math> - 3 &times; a &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math> (a - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mrow><mi>a</mi><mo>-</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math>+ 3(a - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>) = a&sup3; -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></mstyle></math><br>On putting value of a&sup3; and&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></mstyle></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mrow><mi>a</mi><mo>-</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math>+ 3(a - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> -1 - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> +1)<br>= <math display=\"inline\"><msqrt><mn>2</mn></msqrt><mi>&#160;</mi><mo>-</mo><mn>1</mn></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> -1&nbsp;= <math display=\"inline\"><mo>-</mo></math>2</p>",
                    solution_hi: "<p>73.(b)<br>a&sup3; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><msqrt><mn>2</mn></msqrt><mo>-</mo><mn>1</mn><mo>)</mo></mrow><mfrac><mn>1</mn><mn>3</mn></mfrac></msup></math><br>दोनों ओर का घन करने पर,<br>a&sup3; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> - 1&nbsp;<br>और <br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msup><mrow><mi>a</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> +1<br>अब,<br><math display=\"inline\"><msup><mrow><mo>(</mo><msup><mrow><mi>a</mi><mo>-</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow></mrow></msup></math>= a&sup3; -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></mstyle></math> - 3 &times; a &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>(a - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>(</mo><msup><mrow><mi>a</mi><mo>-</mo><mfrac><mn>1</mn><mi>a</mi></mfrac><mo>)</mo></mrow><mn>3</mn></msup></math>+ 3(a - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>) = a&sup3; -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></mstyle></math><br>a&sup3; और&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><msup><mi>a</mi><mn>3</mn></msup></mfrac></mstyle></math> का मान रखने पर<br><math display=\"inline\"><msup><mrow><mo>(</mo><msup><mrow><mi>a</mi><mo>-</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mi>a</mi></mrow></mfrac><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow></mrow></msup></math>+ 3(a - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mi>a</mi></mfrac></mstyle></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> -1 - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>+1)<br>= <math display=\"inline\"><msqrt><mn>2</mn></msqrt><mi>&#160;</mi><mo>-</mo><mn>1</mn></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> -1&nbsp;= <math display=\"inline\"><mo>-</mo></math>2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A boatman rows to a place 30 km distant and back in 14 hours. He finds that he can row 10 km with the stream at the same time as 4 km against the stream. Find the speed (in km/h) of the stream.</p>",
                    question_hi: "<p>74. एक नाविक को नाव से 30 km की दूरी तक जाने और वापस आने में 14 घंटे का समय लगता है। वह पाता है कि वह जितने समय में धारा की दिशा में 10 km नाव चला सकता है, उतने ही समय में धारा के विपरीत दिशा में 4 km नाव चला सकता है। धारा की चाल (km/h में) ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>74.(b) Let the speed of boat in still water = xkm/h<br>Speed of stream = y km/h<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>10</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></mstyle><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mi>k</mi></mrow><mrow><mn>5</mn><mi>k</mi></mrow></mfrac></mstyle></math><br>x = 3.5k and y = 1.5k<br>According to question , <br><math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mi>&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></mstyle></math> = 14<br><math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mi>&#160;</mi><mn>5</mn><mi>k</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mrow><mn>2</mn><mi>k</mi></mrow></mfrac></mstyle></math> = 14<br><math display=\"inline\"><mo>&#8658;</mo></math>k =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>21</mn><mn>14</mn></mfrac></mstyle></math><br>&there4; Speed of stream = 1.5k = 1.5 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>14</mn></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>4</mn></mfrac></math>km/h</p>",
                    solution_hi: "<p>74.(b) माना , शांत पानी में नाव की गति = <math display=\"inline\"><mi>x</mi></math> किमी/घंटा<br>धारा की गति =y किमी/घंटा<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mn>4</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac><mo>=</mo><mstyle displaystyle=\"false\"><mfrac><mrow><mn>2</mn><mi>k</mi></mrow><mrow><mn>5</mn><mi>k</mi></mrow></mfrac></mstyle></math><br><math display=\"inline\"><mi>x</mi><mo>=</mo><mn>3</mn><mo>.</mo><mn>5</mn><mi>k</mi><mi>&#160;</mi></math>और y=1.5k<br>प्रश्न के अनुसार , <br><math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mi>x</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>y</mi><mi>&#160;</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></mstyle></math> = 14<br><math display=\"inline\"><mfrac><mrow><mn>30</mn></mrow><mrow><mi>&#160;</mi><mn>5</mn><mi>k</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>30</mn><mrow><mn>2</mn><mi>k</mi></mrow></mfrac></mstyle></math> = 14<br><math display=\"inline\"><mo>&#8658;</mo></math>k =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>21</mn><mn>14</mn></mfrac></mstyle></math><br>&there4; धारा की गति = 1.5k = 1.5 &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>21</mn><mn>14</mn></mfrac><mo>=</mo><mfrac><mn>9</mn><mn>4</mn></mfrac></mstyle></math>&nbsp;किमी/घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. A person spends 60% of his salary on family expenses, 10% on medical expenses, 5% on charity, and saves the remaining amount. If the amount on savings is ₹4,000, find his monthly salary.</p>",
                    question_hi: "<p>75. एक व्यक्ति अपने वेतन का 60% पारिवारिक व्यय पर, 10% चिकित्सा व्यय पर, 5% दान पर खर्च करता है, और शेष धनराशि बचाता है। यदि बचत राशि ₹4,000 है, तो उसका मासिक वेतन ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹12,000</p>", "<p>₹15,000</p>", 
                                "<p>₹14,000</p>", "<p>₹16,000</p>"],
                    options_hi: ["<p>₹12,000</p>", "<p>₹15,000</p>",
                                "<p>₹14,000</p>", "<p>₹16,000</p>"],
                    solution_en: "<p>75.(d) Let the monthly salary be 100%<br>According to question,<br>Saving = 100 - (60 + 10 + 5) = 25%<br>Now,<br><math display=\"inline\"><mo>&#8658;</mo></math>25% = 4000<br><math display=\"inline\"><mo>&#8658;</mo></math>100 % = ₹ 16000</p>",
                    solution_hi: "<p>75.(d) मासिक वेतन 100% है। <br>प्रश्न के अनुसार,<br>बचत = 100 - (60 + 10 + 5) = 25%<br>अब, <br><math display=\"inline\"><mo>&#8658;</mo></math>25% = 4000<br><math display=\"inline\"><mo>&#8658;</mo></math>100 % = ₹ 16000 </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence, which contains the error.<br>The article suggests that when a person is under unusual stress you should be especially careful to eat a well-balanced diet.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence, which contains the error.<br>The article suggests that when a person is under unusual stress you should be especially careful to eat a well-balanced diet.</p>",
                    options_en: ["<p>the article suggests that when a person is under unusual stress.</p>", "<p>to eat a well-balanced diet.</p>", 
                                "<p>no error</p>", "<p>you should be especially careful.</p>"],
                    options_hi: ["<p>the article suggests that when a person is under unusual stress.</p>", "<p>to eat a well-balanced diet.</p>",
                                "<p>no error</p>", "<p>you should be especially careful.</p>"],
                    solution_en: "<p>76.(d) you should be especially careful.<br>The noun used in the sentence is &ldquo;a person&rdquo; so &ldquo;he&rdquo; should be used and not &ldquo;you&rdquo;. </p>",
                    solution_hi: "<p>76.(d) you should be especially careful. वाक्य में प्रयुक्त संज्ञा \"एक व्यक्ति\" है इसलिए &ldquo;you&rdquo; के स्थान पर &ldquo;he&rdquo; का उपयोग किया जाना चाहिए। </p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate ANTONYM of the underlined word.<br>If you want to be a good detective, it helps to have an <span style=\"text-decoration: underline;\">inquisitive</span> nature.</p>",
                    question_hi: "<p>77. Select the most appropriate ANTONYM of the underlined word.<br>If you want to be a good detective, it helps to have an <span style=\"text-decoration: underline;\">inquisitive</span> nature.</p>",
                    options_en: ["<p>Comprehensive</p>", "<p>Itemised</p>", 
                                "<p>Intrusive</p>", "<p>Disinterested</p>"],
                    options_hi: ["<p>Comprehensive</p>", "<p>Itemised</p>",
                                "<p>Intrusive</p>", "<p>Disinterested</p>"],
                    solution_en: "<p>77.(d) <strong>Disinterested- </strong>not influenced by considerations of personal advantage.<br><strong>Inquisitive</strong>- having or showing an interest in learning things.<br><strong>Comprehensive-</strong> including or dealing with all or nearly all elements or aspects of something.<br><strong>Itemised- </strong>broken down into detailed parts.<br><strong>Intrusive- </strong>coming without invitation or welcome.</p>",
                    solution_hi: "<p>77.(d) <strong>Disinterested</strong> (निःस्वार्थ) - not influenced by considerations of personal advantage.<br><strong>Inquisitive</strong> (जिज्ञासु) - having or showing an interest in learning things.<br><strong>Comprehensive</strong> (व्यापक) - including or dealing with all or nearly all elements or aspects of something.<br>Itemised (सूचीबद्ध) - broken down into detailed parts.<br><strong>Intrusive</strong> (अनधिकार प्रवेशी) - coming without invitation or welcome.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the option that will most suitably substitute the underlined part of the given sentence.<br>He was not happy <span style=\"text-decoration: underline;\">until he gave up his job and live</span> as a fakir in the forests.</p>",
                    question_hi: "<p>78. Select the option that will most suitably substitute the underlined part of the given sentence.<br>He was not happy <span style=\"text-decoration: underline;\">until he gave up his job and live</span> as a fakir in the forests.</p>",
                    options_en: ["<p>until he gave up his job and lived</p>", "<p>until he gives up his job and lived</p>", 
                                "<p>unless he gave up his job and lives</p>", "<p>until he gave up his job and lives</p>"],
                    options_hi: ["<p>until he gave up his job and lived</p>", "<p>until he gives up his job and lived</p>",
                                "<p>unless he gave up his job and lives</p>", "<p>until he gave up his job and lives</p>"],
                    solution_en: "<p>78.(a)<strong> until he gave up his job and lived</strong> <br>The given sentence is in the simple past tense so the verb must be used in the simple past form(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>). However, the past form of &lsquo;live&rsquo; is &lsquo;lived&rsquo;. Hence, &lsquo;until he gave up his job and lived(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a)<strong> until he gave up his job and lived</strong> <br>दिया गया वाक्य simple past tense में है, इसलिए इसमें verb की simple past form(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>) का प्रयोग किया जाएगा। &lsquo;Live&rsquo; का past form &lsquo;lived&rsquo; है। अतः, &lsquo;until he gave up his job and lived (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>2</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. In the following question, out of the four alternatives choose the one which can be substituted for the given words/ sentence.<br>Confirm or support something</p>",
                    question_hi: "<p>79. In the following question, out of the four alternatives choose the one which can be substituted for the given words/ sentence.<br>Confirm or support something</p>",
                    options_en: ["<p>Collaborate</p>", "<p>Corroborate</p>", 
                                "<p>Cooperate</p>", "<p>Consider</p>"],
                    options_hi: ["<p>Collaborate</p>", "<p>Corroborate</p>",
                                "<p>Cooperate</p>", "<p>Consider</p>"],
                    solution_en: "<p>79.(b) <strong>Corroborate</strong><br><strong>Corroborate</strong>- Confirm or support something <br><strong>Collaborate-</strong> to work together with somebody, especially to create or produce something <br><strong>Cooperate-</strong> to work with somebody else to achieve something <br><strong>Consider-</strong> to think about something carefully,</p>",
                    solution_hi: "<p>79.(b) Corroborate<br><strong>Corroborate</strong>( संपुष्टि करना)- Confirm or support something <br><strong>Collaborate(</strong>सहयोग)- to work together with somebody, especially to create or produce something <br><strong>Cooperate</strong>(सहयोग)- to work with somebody else to achieve something <br><strong>Consider</strong>(विचार करना)- to think about something carefully,</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>The schools take care of the overall needs of the students.</p>",
                    question_hi: "<p>80. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>The schools take care of the overall needs of the students.</p>",
                    options_en: ["<p>The overall needs of the students are taken care of by the schools.</p>", "<p>The overall need of the students will be taken care of by the schools</p>", 
                                "<p>The overall needs of the students are taken care by the schools</p>", "<p>The overall needs of the students are to be taken care of by the schools</p>"],
                    options_hi: ["<p>The overall needs of the students are taken care of by the schools.</p>", "<p>The overall need of the students will be taken care of by the schools</p>",
                                "<p>The overall needs of the students are taken care by the schools</p>", "<p>The overall needs of the students are to be taken care of by the schools</p>"],
                    solution_en: "<p>80.(a)&nbsp;The overall needs of the students are taken care of by the schools.<br>(b)&nbsp;The overall need of the students <strong>will be taken</strong> care of by the schools.&nbsp;(Tense has changed)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>(c) The overall needs of the students are taken care by the schools. (Preposition &lsquo;of&rsquo; is missing)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>(d) The overall needs of the students <strong>are to be taken care </strong>of by the schools. (Incorrect structure)&nbsp;&nbsp;</p>",
                    solution_hi: "<p>80.(a)&nbsp;The overall needs of the students are taken care of by the schools.<br>(b) The overall need of the students <strong>will be taken</strong> care of by the schools.&nbsp;(Tense बदल गया है)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>(c) The overall needs of the students are taken care by the schools. (Preposition &lsquo;of&rsquo; का प्रयोग नहीं हुआ है )&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>(d) The overall needs of the students<strong> are to be taken care</strong> of by the schools. (Structure गलत है )&nbsp;&nbsp;</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. Mobile phones or smartphones are becoming popular all over the world.<br>B. But at the same time, it also harms us in many ways.<br>C. It is the most widely used means of communication today.<br>D. Today, it is very affordable and available to everyone.</p>",
                    question_hi: "<p>81. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. Mobile phones or smartphones are becoming popular all over the world.<br>B. But at the same time, it also harms us in many ways.<br>C. It is the most widely used means of communication today.<br>D. Today, it is very affordable and available to everyone.</p>",
                    options_en: ["<p>ABCD</p>", "<p>DACB</p>", 
                                "<p>CABD</p>", "<p>ACDB</p>"],
                    options_hi: ["<p>ABCD</p>", "<p>DACB</p>",
                                "<p>CABD</p>", "<p>ACDB</p>"],
                    solution_en: "<p>81.(d) ACDB<br>Sentence A will be the starting line as it introduces the main idea of the parajumble, i.e. &lsquo;the world-wide popularity of mobile phones or smartphones&rsquo;. And, Sentence C states that it is the most widely used means of communication today. So, C will follow A. Further, Sentence D states that it is very affordable and available to everyone &amp; Sentence B states that it also harms us in many ways. So, B will follow D. Going through the options, option &lsquo;d&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>81.(d) ACDB<br>Sentence A प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार &lsquo;the world-wide popularity of mobile phones or smartphones&rsquo; का परिचय देती है। और, Sentence C बताता है कि यह आज communication का सबसे व्यापक रूप से उपयोग किया जाने वाला साधन है। इसलिए, A के बाद C आएगा। इसके अलावा, Sentence D बताता है कि यह बहुत किफायती (affordable) है और सभी के लिए उपलब्ध है और Sentence B बताता है कि यह हमें कई तरह से नुकसान भी पहुँचाता है। इसलिए, D के बाद B आएगा। अतः options के माध्यम से जाने पर, option &lsquo;d&rsquo; में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>She said, &ldquo;I am going to the pictures. Where are you going ?&rdquo;</p>",
                    question_hi: "<p>82. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>She said, &ldquo;I am going to the pictures. Where are you going ?&rdquo;</p>",
                    options_en: ["<p>She said that I was going to the pictures and wanted to know where he was going.</p>", "<p>She said that she was going to the pictures and wanted to know where I was going.</p>", 
                                "<p>She asked that she was going to the pictures and wanted to know where she was going.</p>", "<p>She said that she was going to the pictures and wanted to know where was I gone.</p>"],
                    options_hi: ["<p>She said that I was going to the pictures and wanted to know where he was going.</p>", "<p>She said that she was going to the pictures and wanted to know where I was going.</p>",
                                "<p>She asked that she was going to the pictures and wanted to know where she was going.</p>", "<p>She said that she was going to the pictures and wanted to know where was I gone.</p>"],
                    solution_en: "<p>82.(b) She said that she was going to the pictures and wanted to know where I was going.<br>(a) She said that <strong>I </strong>was going to the pictures and wanted to know where he was going. (Incorrect change of pronoun)<br>(c) She <strong>asked</strong> that she was going to the pictures and wanted to know where she was going.<br>(Incorrect Reporting Verb)<br>(d) She said that she was going to the pictures and wanted to know where <span style=\"text-decoration: underline;\">was I gone</span>. (Incorrect structure.)</p>",
                    solution_hi: "<p>82.(b) She said that she was going to the pictures and wanted to know where I was going.<br>(a) She said that I was going to the pictures and wanted to know where he was going. (Pronoun का गलत परिवर्तन)<br>(c) She <strong>asked</strong> that she was going to the pictures and wanted to know where she was going.<br>(गलत Reporting Verb)<br>(d) She said that she was going to the pictures and wanted to know where <span style=\"text-decoration: underline;\"><strong>was I gone</strong></span>. (गलत structure.)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Identify the segment in the sentence, which contains error.<br>Lack of required / vitamins and minerals / lead against / several complications / in the human body.</p>",
                    question_hi: "<p>83. Identify the segment in the sentence, which contains error.<br>Lack of required / vitamins and minerals / lead against / several complications / in the human body.</p>",
                    options_en: ["<p>in the human body</p>", "<p>Lack of required</p>", 
                                "<p>lead against</p>", "<p>several complications</p>"],
                    options_hi: ["<p>in the human body</p>", "<p>Lack of required</p>",
                                "<p>lead against</p>", "<p>several complications</p>"],
                    solution_en: "<p>83.(c) &lsquo;Lead&rsquo; will take the preposition &lsquo;to&rsquo; with it &amp; the subject &lsquo;lack of&rsquo; is singular so it will take a singular form of lead which is &lsquo;leads&rsquo;. Hence, &lsquo;leads to&rsquo; is the most appropriate structure.</p>",
                    solution_hi: "<p>83.(c) \'Lead\' के साथ preposition &lsquo;to&rsquo; का प्रयोग किया जाता है। वाक्य में subject \'lack of&rsquo;\' singular है इसलिए lead की singular form लगेगी जो \'leads\' है। इसलिए, \'leads to\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>84. Select the INCORRECTLY spelt word.</p>",
                    options_en: ["<p>Aquire</p>", "<p>Congratulate</p>", 
                                "<p>Hygiene</p>", "<p>Category</p>"],
                    options_hi: ["<p>Aquire</p>", "<p>Congratulate</p>",
                                "<p>Hygiene</p>", "<p>Category</p>"],
                    solution_en: "<p>84.(a) Aquire <br>&lsquo;Acquire&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>84.(a) Aquire <br>&lsquo;Acquire&rsquo; सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate option to substitute the underlined segment in the following sentence.<br>The company <span style=\"text-decoration: underline;\">do not broke</span> the law at any time.</p>",
                    question_hi: "<p>85. Select the most appropriate option to substitute the underlined segment in the following sentence.<br>The company <span style=\"text-decoration: underline;\">do not broke</span> the law at any time.</p>",
                    options_en: ["<p>did not broken</p>", "<p>do not brakes</p>", 
                                "<p>does not break</p>", "<p>did not breaking</p>"],
                    options_hi: ["<p>did not broken</p>", "<p>do not brakes</p>",
                                "<p>does not break</p>", "<p>did not breaking</p>"],
                    solution_en: "<p>85.(c) <strong>does not break</strong><br>&lsquo;Do/does/did + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi mathvariant=\"normal\">V</mi><mn>1</mn></msub></math>&rsquo; is the correct grammatical structure. Hence, &lsquo;does not break&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>85.(c) <strong>does not break</strong><br>&lsquo;Do/does/did + V1&rsquo; सही grammatical structure है। अतः, &lsquo;does not break&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Did you visit your grandmother in the village last summer?</p>",
                    question_hi: "<p>86. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Did you visit your grandmother in the village last summer?</p>",
                    options_en: ["<p>Have your grandmother been visited by you last summer?</p>", "<p>Was your grandmother been visited in the village by you last summer?</p>", 
                                "<p>Was your grandmother visited in the village by you last summer?</p>", "<p>Were your grandmother been visited by you last summer?</p>"],
                    options_hi: ["<p>Have your grandmother been visited by you last summer?</p>", "<p>Was your grandmother been visited in the village by you last summer?</p>",
                                "<p>Was your grandmother visited in the village by you last summer?</p>", "<p>Were your grandmother been visited by you last summer?</p>"],
                    solution_en: "<p>86.(c)&nbsp;Was your grandmother visited in the village by you last summer?<br>(a)&nbsp;<strong>Have your grandmother been</strong> visited by you last summer? (Tense has changed)<br>(b)&nbsp;Was your grandmother <strong>been</strong> visited in the village by you last summer? (Incorrect word used)<br>(d)&nbsp;Were your grandmother been visited by you last summer? (&lsquo;in the village&rsquo; is missing)</p>",
                    solution_hi: "<p>86.(c)&nbsp;Was your grandmother visited in the village by you last summer?<br>(a)&nbsp;<strong>Have your grandmother been</strong> visited by you last summer? (Tense बदल गया है )<br>(b)&nbsp;Was your grandmother <strong>been</strong> visited in the village by you last summer? (गलत word प्रयोग किया है )<br>(d)&nbsp;Were your grandmother been visited by you last summer? (&lsquo;in the village&rsquo; का प्रयोग नहीं हुआ है)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the idiom.<br>A fly on the wall</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the idiom.<br>A fly on the wall</p>",
                    options_en: ["<p>A vigilant security guard</p>", "<p>An unperceived observer</p>", 
                                "<p>Very intelligent person</p>", "<p>An unwelcome guest</p>"],
                    options_hi: ["<p>A vigilant security guard</p>", "<p>An unperceived observer</p>",
                                "<p>Very intelligent person</p>", "<p>An unwelcome guest</p>"],
                    solution_en: "<p>87.(b) A fly on the wall - an unperceived observer.<br>E.g.- I will love to be a fly on the wall when the admissions officer reads my application.</p>",
                    solution_hi: "<p>87.(b) A fly on the wall - an unperceived observer/न दिखने वाला प्रेक्षक (देखने वाला)<br>E.g.- I will love to be a fly on the wall when the admissions officer reads my application.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate meaning of the idiom ( in the context).<br>The personality development class started with an <strong><span style=\"text-decoration: underline;\">ice-breaking</span></strong> session.</p>",
                    question_hi: "<p>88. Select the most appropriate meaning of the idiom ( in the context).<br>The personality development class started with an <span style=\"text-decoration: underline;\"><strong>ice-breaking</strong></span> session.</p>",
                    options_en: ["<p>having breakfast</p>", "<p>starting conversation</p>", 
                                "<p>introducing chief guest</p>", "<p>making speeches</p>"],
                    options_hi: ["<p>having breakfast</p>", "<p>starting conversation</p>",
                                "<p>introducing chief guest</p>", "<p>making speeches</p>"],
                    solution_en: "<p>88.(b) starting conversation<br>Example-I tried to break the ice by asking him about his health.</p>",
                    solution_hi: "<p>88.(b) starting conversation<br>उदाहरण - I tried to break the ice by asking him about his health.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate option to fill in the blank. <br>The teacher asked the students to ______ electricity by switching off lights when they left the room.</p>",
                    question_hi: "<p>89. Select the most appropriate option to fill in the blank. <br>The teacher asked the students to ______ electricity by switching off lights when they left the room.</p>",
                    options_en: ["<p>deposit</p>", "<p>save</p>", 
                                "<p>rescue</p>", "<p>retain</p>"],
                    options_hi: ["<p>deposit</p>", "<p>save</p>",
                                "<p>rescue</p>", "<p>retain</p>"],
                    solution_en: "<p>89.(b) save<br>The given sentence states that the teacher asked the students to save electricity by switching off lights when they left the room. Hence &lsquo;save&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>89.(b) save<br>दिए गए sentence में बताया गया है कि teacher ने students से कहा कि वे कमरे (room) से बाहर निकलते समय लाइट (lights) बंद (off) करके बिजली (electricity) बचाएं। अतः &lsquo;save&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. In the following question, out of the four alternatives choose the one which can be substituted for the given words/ sentence.<br>A change in the form or nature of something</p>",
                    question_hi: "<p>90. In the following question, out of the four alternatives choose the one which can be substituted for the given words/ sentence.<br>A change in the form or nature of something</p>",
                    options_en: ["<p>Metamorphosis</p>", "<p>Hibernation</p>", 
                                "<p>Camouflaging</p>", "<p>Refraction</p>"],
                    options_hi: ["<p>Metamorphosis</p>", "<p>Hibernation</p>",
                                "<p>Camouflaging</p>", "<p>Refraction</p>"],
                    solution_en: "<p>90.(a) Metamorphosis<br><strong>Metamorphosis- </strong>A change in the form or nature of something <br><strong>Hibernation</strong>- the state of being asleep for the winter <br><strong>Camouflaging-</strong> to conceal or disguise by camouflage <br><strong>Refraction- </strong>a deflection from a straight path undergone by a light ray or energy</p>",
                    solution_hi: "<p>90.(a) <strong>Metamorphosis</strong><br><strong>Metamorphosis(कायापलट)</strong>- A change in the form or nature of something <br><strong>Hibernation(शीत निद्रा)-</strong> the state of being asleep for the winter <br><strong>Camouflaging(छलावरण)-</strong> to conceal or disguise by camouflage <br><strong>Refraction(अपवर्तन)- </strong>a deflection from a straight path undergone by a light ray or energy</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate synonym of the given word.<br>Pliable</p>",
                    question_hi: "<p>91. Select the most appropriate synonym of the given word.<br>Pliable</p>",
                    options_en: ["<p>Rigid</p>", "<p>Probable</p>", 
                                "<p>Malleable</p>", "<p>Severe</p>"],
                    options_hi: ["<p>Rigid</p>", "<p>Probable</p>",
                                "<p>Malleable</p>", "<p>Severe</p>"],
                    solution_en: "<p>91.(c) <strong>Malleable </strong>- that can be hit or pressed into shape easily.<br><strong>Pliable </strong>- easily bent.<br><strong>Rigid </strong>- not able to be changed or adapted.<br><strong>Probable</strong> - likely to happen.<br><strong>Severe </strong>- extremely bad or serious.</p>",
                    solution_hi: "<p>91.(c) <strong>Malleable</strong> (लचीला) - that can be hit or pressed into shape easily.<br><strong>Pliable</strong> (लचीला) - easily bent.<br><strong>Rigid</strong> (कठोर) - not able to be changed or adapted.<br><strong>Probable</strong> (संभावित) - likely to happen.<br><strong>Severe</strong> (गंभीर) - extremely bad or serious.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the INCORRECTLY spelt word in the given sentence.<br>&lsquo;Vande Mataram&rsquo;, a poignant poem, is obviously and indisputebly the premier national song of India.</p>",
                    question_hi: "<p>92. Select the INCORRECTLY spelt word in the given sentence.<br>&lsquo;Vande Mataram&rsquo;, a poignant poem, is obviously and indisputebly the premier national song of India.</p>",
                    options_en: ["<p>Premier</p>", "<p>Poignant</p>", 
                                "<p>Indisputebly</p>", "<p>Obviously</p>"],
                    options_hi: ["<p>Premier</p>", "<p>Poignant</p>",
                                "<p>Indisputebly</p>", "<p>Obviously</p>"],
                    solution_en: "<p>92.(c) <strong>indisputebly</strong><br>&lsquo;Indisputably&rsquo; is the correct spelling</p>",
                    solution_hi: "<p>92.(c) <strong>indisputebly</strong><br>&lsquo;Indisputably&rsquo; सही spelling है। </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate synonym of the underlined word in the given sentence. <br>We should not <span style=\"text-decoration: underline;\">discriminate</span> against people who are different from us.</p>",
                    question_hi: "<p>93. Select the most appropriate synonym of the underlined word in the given sentence. <br>We should not <span style=\"text-decoration: underline;\">discriminate</span> against people who are different from us.</p>",
                    options_en: ["<p>part</p>", "<p>differentiate</p>", 
                                "<p>divide</p>", "<p>sever</p>"],
                    options_hi: ["<p>part</p>", "<p>differentiate</p>",
                                "<p>divide</p>", "<p>sever</p>"],
                    solution_en: "<p>93.(b) <strong>Differentiate-</strong> to identify the difference between the things.<br><strong>Discriminate-</strong> to treat someone unfairly because of their differences, such as race, gender, or age.<br><strong>Part-</strong> a piece or portion of a whole.<br><strong>Divide-</strong> to separate something into parts or groups.<br><strong>Sever-</strong> divide by cutting or slicing, especially suddenly or forcibly.</p>",
                    solution_hi: "<p>93.(b) <strong>Differentiate</strong> (अंतर करना) - to identify the difference between the things.<br><strong>Discriminate</strong> (भेदभाव करना) - to treat someone unfairly because of their differences, such as race, gender, or age.<br><strong>Part</strong> (भाग) - a piece or portion of a whole.<br><strong>Divide</strong> (विभाजित करना) - to separate something into parts or groups.<br><strong>Sever</strong> (अलग करना) - divide by cutting or slicing, especially suddenly or forcibly.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate ANTONYM of the word &lsquo;bold&rsquo; in the given sentence.<br>A person can be friendly, timid, clever, or <strong>fearless</strong> based on the context of the situation.</p>",
                    question_hi: "<p>94. Select the most appropriate ANTONYM of the word &lsquo;bold&rsquo; in the given sentence.<br>A person can be friendly, timid, clever, or f<strong>earless</strong> based on the context of the situation.</p>",
                    options_en: ["<p>clever</p>", "<p>timid</p>", 
                                "<p>fearless</p>", "<p>friendly</p>"],
                    options_hi: ["<p>clever</p>", "<p>timid</p>",
                                "<p>fearless</p>", "<p>friendly</p>"],
                    solution_en: "<p>94.(b) <strong>Timid</strong>- showing a lack of courage and confidence<br><strong>Fearless</strong>- showing a lack of fear<br><strong>Clever</strong>- quick in learning<br><strong>Friendly</strong>- one who is kind and caring</p>",
                    solution_hi: "<p>94.(b) <strong>Timid</strong> (डरपोक) - showing a lack of courage and confidence<br><strong>Fearless</strong> (निडर) - showing a lack of fear<br><strong>Clever</strong> (चालाक/चतुर) - quick in learning<br><strong>Friendly</strong> (मैत्रीपूर्ण) - one who is kind and caring</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate idiom to fill in the blank. <br>After a day long trek, we were so tired that we were ready to _________.</p>",
                    question_hi: "<p>95. Select the most appropriate idiom to fill in the blank. <br>After a day long trek, we were so tired that we were ready to _________.</p>",
                    options_en: ["<p>face the music</p>", "<p>hit the sack</p>", 
                                "<p>get into deep water</p>", "<p>go from rags to riches</p>"],
                    options_hi: ["<p>face the music</p>", "<p>hit the sack</p>",
                                "<p>get into deep water</p>", "<p>go from rags to riches</p>"],
                    solution_en: "<p>95.(b) <strong>Hit the sack-</strong> to go to bed or sleep.</p>",
                    solution_hi: "<p>95.(b) <strong>Hit the sack-</strong> to go to bed or sleep./बिस्तर पर जाना या सोना।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 96.</p>",
                    options_en: ["<p>Moreover</p>", "<p>Therefore</p>", 
                                "<p>Furthermore</p>", "<p>However</p>"],
                    options_hi: ["<p>Moreover</p>", "<p>Therefore</p>",
                                "<p>Furthermore</p>", "<p>However</p>"],
                    solution_en: "<p>96.(d) However<br>&lsquo;However&rsquo; is used to introduce a statement that contrasts with the previous information. The given passage states that coming events cast their shadows before and then states a contrasting idea that it is not universally true. Hence, \'however\' is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(d) However<br>&lsquo;However&rsquo; का प्रयोग किसी statement को introduce करने के लिए किया जाता है जो पिछली जानकारी के विपरीत होता है। दिए गए passage में बताया गया है कि आने वाली घटनाएँ पहले अपनी छाया डालती हैं और फिर एक विपरीत विचार दिया गया है कि यह हमेशा सही नहीं होता है। अतः, \'however\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 97.</p>",
                    options_en: ["<p>legalise</p>", "<p>foresee</p>", 
                                "<p>rescind</p>", "<p>affect</p>"],
                    options_hi: ["<p>legalise</p>", "<p>foresee</p>",
                                "<p>rescind</p>", "<p>affect</p>"],
                    solution_en: "<p>97.(b) foresee<br>&lsquo;Foresee&rsquo; means to realize or understand something in advance or before it happens. The given passage states that something can happen within a second, and one may not foresee it. Hence, \'foresee\' is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) foresee<br>&lsquo;Foresee&rsquo; का अर्थ है किसी चीज़ को पहले से या उसके घटित होने से पहले महसूस करना या समझना। दिए गए passage में बताया गया है कि एक सेकंड के अंदर-अंदर कुछ हो सकता है, और कोई इसका पूर्वानुमान नहीं लगा सकता है। अतः, \'foresee\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 98.</p>",
                    options_en: ["<p>Therefore</p>", "<p>Moreover</p>", 
                                "<p>Nevertheless</p>", "<p>However</p>"],
                    options_hi: ["<p>Therefore</p>", "<p>Moreover</p>",
                                "<p>Nevertheless</p>", "<p>However</p>"],
                    solution_en: "<p>98.(b) Moreover<br>&lsquo;Moreover&rsquo; is used to provide additional information about something. Similarly, the given passage provides additional information that predictions based on certain signs have gone wrong. Hence, \'Moreover\' is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(b) Moreover<br>&lsquo;Moreover&rsquo; का प्रयोग किसी चीज़ के बारे में अतिरिक्त जानकारी (additional information) प्रदान करने के लिए किया जाता है। इसी तरह, दिया गया passage अतिरिक्त जानकारी प्रदान करता है कि कुछ संकेतों पर आधारित भविष्यवाणियाँ गलत हो गई हैं। अतः, \'Moreover\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    question_hi: "<p>99.<strong> Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 99.</p>",
                    options_en: ["<p>Besides</p>", "<p>Secondly</p>", 
                                "<p>Therefore</p>", "<p>Despite</p>"],
                    options_hi: ["<p>Besides</p>", "<p>Secondly</p>",
                                "<p>Therefore</p>", "<p>Despite</p>"],
                    solution_en: "<p>99.(a) Besides<br>&lsquo;Besides&rsquo; means furthermore or in addition to. The given passage further states that some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. Hence, \'Besides\' is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(a) Besides<br>&lsquo;Besides&rsquo; का अर्थ है इसके अलावा या इसके अतिरिक्त। दिए गए passage में आगे बताया गया है कि कुछ अप्राकृतिक आपदाएँ जो आने वाली हैं, वे कुछ बुरे संकेतों द्वारा अपनी छाया डालती हैं। अतः, \'Besides\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong><br>There is a saying that coming events cast their shadows before. (96)________, it is not universally true. Something can happen within a second, and one may not (97)________ it. (98)________, some instances show that predictions based on certain signs have gone wrong. People generally say that natural calamities can be predicted by observing the animals. But what if animals are suffering from some disease and don&rsquo;t show any signs before the event appears? They may fail to make peculiar sounds or actions about the events which are going to take place. (99)________, some unnatural calamities that are likely to appear may forecast their shadows by some bad omens. (100)________, we should not completely cancel out the possibilities that animals can sense certain unnatural happenings.<br>Select the most appropriate option to fill in blank number 100.</p>",
                    options_en: ["<p>However</p>", "<p>Nevertheless</p>", 
                                "<p>Moreover</p>", "<p>Therefore</p>"],
                    options_hi: ["<p>However</p>", "<p>Nevertheless</p>",
                                "<p>Moreover</p>", "<p>Therefore</p>"],
                    solution_en: "<p>100.(d) Therefore<br>&lsquo;Therefore&rsquo; is used to introduce a logical conclusion or result. The given passage states a conclusion that we should not completely cancel out the possibilities that animals can sense certain unnatural happenings. Hence, \'Therefore\' is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(d) Therefore<br>&lsquo;Therefore&rsquo; का प्रयोग logical conclusion या result को बताने के लिए किया जाता है। दिए गए passage में निष्कर्ष दिया गया है कि हमें इस संभावना को पूरी तरह से नकारना नहीं चाहिए कि जानवर कुछ अप्राकृतिक घटनाओं को महसूस कर सकते हैं। अतः, \'Therefore\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>