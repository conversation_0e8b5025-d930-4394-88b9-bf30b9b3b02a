<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. In a certain code language, \'DESTROY\' is written as \'DYORTSE\' and \'EFFECTS\' is written as \'ESTCEFF\'. How will \'FOUNDED\' be written in that language?</p>",
                    question_hi: "<p>1. एक निश्चित कूट भाषा में, \'DESTROY\' को \'DYORTSE\' के रूप में लिखा जाता है और \'EFFECTS\' को \'ESTCEFF\' के रूप में लिखा जाता है। उसी भाषा में \'FOUNDED\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>FOEDDNU</p>", "<p>DFOUND</p>", 
                                "<p>FDEDNUO</p>", "<p>DEDNUOF</p>"],
                    options_hi: ["<p>FOEDDNU</p>", "<p>DFOUNDE</p>",
                                "<p>FDEDNUO</p>", "<p>DEDNUOF</p>"],
                    solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382014716.png\" alt=\"rId5\" width=\"168\" height=\"94\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382014912.png\" alt=\"rId6\" width=\"165\" height=\"93\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015069.png\" alt=\"rId7\" width=\"163\" height=\"88\"></p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382014716.png\" alt=\"rId5\" width=\"168\" height=\"94\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382014912.png\" alt=\"rId6\" width=\"165\" height=\"93\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015069.png\" alt=\"rId7\" width=\"163\" height=\"88\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the option in which the given figure is embedded. (Rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015166.png\" alt=\"rId8\" width=\"97\" height=\"89\"></p>",
                    question_hi: "<p>2. उस विकल्प का चयन कीजिए, जिसमें दी गई आकृति निहित है। (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015166.png\" alt=\"rId8\" width=\"85\" height=\"78\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015278.png\" alt=\"rId9\" width=\"90\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015388.png\" alt=\"rId10\" width=\"90\" height=\"80\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015494.png\" alt=\"rId11\" width=\"91\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015610.png\" alt=\"rId12\" width=\"90\" height=\"87\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015278.png\" alt=\"rId9\" width=\"90\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015388.png\" alt=\"rId10\" width=\"92\" height=\"82\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015494.png\" alt=\"rId11\" width=\"91\" height=\"84\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015610.png\" alt=\"rId12\" width=\"91\" height=\"88\"></p>"],
                    solution_en: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015723.png\" alt=\"rId13\" width=\"90\" height=\"87\"></p>",
                    solution_hi: "<p>2.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015723.png\" alt=\"rId13\" width=\"90\" height=\"87\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which is the one that DOES NOT belong to that group?<br>(Note: The odd man out is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>3. अंग्रेजी वर्णमाला क्रम के आधार पर, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से एकसमान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा अक्षर-समूह उस समूह से संबंधित नहीं है?<br>(नोट: असंगत अक्षर-समूह, व्यंजनों/स्वरों की संख्या या अक्षर-समूह में इनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: ["<p>HKI</p>", "<p>RUS</p>", 
                                "<p>MPN</p>", "<p>QUR</p>"],
                    options_hi: ["<p>HKI</p>", "<p>RUS</p>",
                                "<p>MPN</p>", "<p>QUR</p>"],
                    solution_en: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015852.png\" alt=\"rId14\" width=\"140\" height=\"54\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015996.png\" alt=\"rId15\" width=\"144\" height=\"54\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016096.png\" alt=\"rId16\" width=\"145\" height=\"54\">but <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016213.png\" alt=\"rId17\" width=\"142\" height=\"52\"></p>",
                    solution_hi: "<p>3.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015852.png\" alt=\"rId14\" width=\"140\" height=\"54\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382015996.png\" alt=\"rId15\" width=\"144\" height=\"54\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016096.png\" alt=\"rId16\" width=\"145\" height=\"54\">लेकिन<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016213.png\" alt=\"rId17\" width=\"142\" height=\"52\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the figure from among the given options that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016426.png\" alt=\"rId18\" width=\"424\" height=\"68\"></p>",
                    question_hi: "<p>4. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकती है। <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016426.png\" alt=\"rId18\" width=\"424\" height=\"68\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016567.png\" alt=\"rId19\" width=\"81\" height=\"65\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016713.png\" alt=\"rId20\" width=\"80\" height=\"65\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016825.png\" alt=\"rId21\" width=\"80\" height=\"65\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016964.png\" alt=\"rId22\" width=\"80\" height=\"65\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016567.png\" alt=\"rId19\" width=\"80\" height=\"65\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016713.png\" alt=\"rId20\" width=\"80\" height=\"65\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016825.png\" alt=\"rId21\" width=\"80\" height=\"65\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016964.png\" alt=\"rId22\" width=\"80\" height=\"65\"></p>"],
                    solution_en: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016713.png\" alt=\"rId20\" width=\"80\" height=\"65\"></p>",
                    solution_hi: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382016713.png\" alt=\"rId20\" width=\"80\" height=\"65\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the option that represents the letters that when placed from left to right in the blanks below will complete the given letter series. <br>L M N _ O L L M _ Q O L L _ N Q O _ L M N Q _ L L M</p>",
                    question_hi: "<p>5. उस विकल्प का चयन करें जो उन अक्षरों को निरूपित करता है जिन्&zwj;हें नीचे दिए गए रिक्त स्थानों में बाएँ से दाएँ रखे जाने पर दी गई अक्षर श्रृंखला पूरी हो जाएगी।<br>L M N _ O L L M _ Q O L L _ N Q O _ L M N Q _ L L M</p>",
                    options_en: ["<p>QNMLO</p>", "<p>QMNLO</p>", 
                                "<p>QLMNO</p>", "<p>NQLMO</p>"],
                    options_hi: ["<p>QNMLO</p>", "<p>QMNLO</p>",
                                "<p>QLMNO</p>", "<p>NQLMO</p>"],
                    solution_en: "<p>5.(a)<br>L M N <span style=\"text-decoration: underline;\"><strong>Q</strong></span> O L/ L M <span style=\"text-decoration: underline;\"><strong>N</strong></span> Q O L/ L <span style=\"text-decoration: underline;\"><strong>M</strong></span> N Q O <span style=\"text-decoration: underline;\"><strong>L</strong></span>/ L M N Q <span style=\"text-decoration: underline;\"><strong>O</strong></span> L/ L M</p>",
                    solution_hi: "<p>5.(a)<br>L M N <span style=\"text-decoration: underline;\"><strong>Q</strong></span> O L/ L M <span style=\"text-decoration: underline;\"><strong>N</strong></span> Q O L/ L <span style=\"text-decoration: underline;\"><strong>M</strong></span> N Q O <span style=\"text-decoration: underline;\"><strong>L</strong></span>/ L M N Q <span style=\"text-decoration: underline;\"><strong>O</strong></span> L/ L M</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given conclusions logically follow/s from the given statement.<br><strong>Statements :</strong><br>Some roses are flowers.<br>All buds are roses.<br>All lilies are flowers.<br><strong>Conclusion (I) :</strong> Some buds are lilies.<br><strong>Conclusion (II) :</strong> All flowers are buds.</p>",
                    question_hi: "<p>6. तीन कथनों के बाद I, II क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/हैं।<br><strong>कथन :</strong><br>कुछ गुलाब, फूल हैं।<br>सभी कलियां, गुलाब हैं।<br>सभी लिली, फूल हैं।<br><strong>निष्कर्ष (I) :</strong> कुछ कलियां, लिली हैं।<br><strong>निष्कर्ष (II) : </strong>सभी फूल, कलियां हैं।</p>",
                    options_en: ["<p>Only conclusion (II) follows.</p>", "<p>Only conclusion (I) follows.</p>", 
                                "<p>Both conclusions (I) and (II) follow.</p>", "<p>Neither conclusion (I) nor (II) follows.</p>"],
                    options_hi: ["<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>", "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                                "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>", "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>"],
                    solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382017228.png\" alt=\"rId23\" width=\"207\" height=\"75\"><br>Neither conclusion I nor II follow.</p>",
                    solution_hi: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382017500.png\" alt=\"rId24\" width=\"213\" height=\"74\"><br>न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the term from among the given options that can replace the question mark (?) in the following series based on the English alphabetical order.<br>XYZ, YAC, ?, AEI, BGL, CIO</p>",
                    question_hi: "<p>7. दिए गए विकल्पों में से उस पद का चयन कीजिए जो अंग्रेजी वर्णमाला क्रम के आधार पर निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्थान पर आ सकता है।<br>XYZ, YAC, ?, AEI, BGL, CIO</p>",
                    options_en: ["<p>ZCF</p>", "<p>MOP</p>", 
                                "<p>ZAB</p>", "<p>YZL</p>"],
                    options_hi: ["<p>ZCF</p>", "<p>MOP</p>",
                                "<p>ZAB</p>", "<p>YZL</p>"],
                    solution_en: "<p>7.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382017786.png\" alt=\"rId25\" width=\"349\" height=\"93\"></p>",
                    solution_hi: "<p>7.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382017786.png\" alt=\"rId25\" width=\"349\" height=\"93\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. In a certain code language,<br>&lsquo;A + B&rsquo; means &lsquo;A is the son of B&rsquo;;<br>&lsquo;A # B&rsquo; means &lsquo;A is the sister of B&rsquo;;<br>&lsquo;A &times; B&rsquo; means &lsquo;A is the wife of B&rsquo;; and,<br>&lsquo;A @ B&rsquo; means &lsquo;A is the father of B&rsquo;.<br>Based on the above, how is M related to T if &lsquo;M # N @ L &times; P + T&rsquo;?</p>",
                    question_hi: "<p>8. एक निश्चित कूट भाषा में,<br>&lsquo;A + B&rsquo; का अर्थ है \'&lsquo;A, B&rsquo; का पुत्र है\';<br>\'A # B\' का अर्थ है \'A, B की बहन है\';<br>&lsquo;A &times; B&rsquo; का अर्थ है \'&lsquo;A, B की पत्नी है\'; और,<br>\'A @ B\' का अर्थ है \'A, B का पिता है\'।<br>उपरोक्त के आधार पर, यदि \'M # N @ L &times; P + T\' है, तो M का T से क्या संबंध है?</p>",
                    options_en: ["<p>Son&rsquo;s wife&rsquo;s sister</p>", "<p>Son&rsquo;s wife&rsquo;s father&rsquo;s sister</p>", 
                                "<p>Son&rsquo;s wife&rsquo;s father</p>", "<p>Son&rsquo;s wife&rsquo;s father&rsquo;s mother</p>"],
                    options_hi: ["<p>पुत्र की पत्नी की बहन</p>", "<p>पुत्र की पत्नी के पिता की बहन</p>",
                                "<p>पुत्र की पत्नी के पिता</p>", "<p>पुत्र की पत्नी के पिता की माता</p>"],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382017980.png\" alt=\"rId26\" width=\"188\" height=\"94\"><br>Hence, M is the son\'s wife&rsquo;s father&rsquo;s sister of T.</p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382017980.png\" alt=\"rId26\" width=\"188\" height=\"94\"><br>अतः, M, T के बेटे की पत्नी के पिता की बहन है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Which of the following numbers will replace the question mark (?) in the given series?<br>324, 361, 400, 441, 484, ?</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी?<br>324, 361, 400, 441, 484, ?</p>",
                    options_en: ["<p>567</p>", "<p>586</p>", 
                                "<p>592</p>", "<p>529</p>"],
                    options_hi: ["<p>567</p>", "<p>586</p>",
                                "<p>592</p>", "<p>529</p>"],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382018189.png\" alt=\"rId27\" width=\"266\" height=\"80\"></p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382018189.png\" alt=\"rId27\" width=\"266\" height=\"80\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Study the given diagram carefully and answer the question that follows. The numbers in different sections indicate the number of students who like particular subjects.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382018509.png\" alt=\"rId28\" width=\"162\" height=\"181\"> <br>How many students like Hindi and Science both, but NOT Maths?</p>",
                    question_hi: "<p>10. दिए गए आरेख का ध्यानपूर्वक अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। विभिन्न वर्गों वाली संख्याएँ उन छात्रों की संख्या दर्शाती हैं जो विशेष विषयों को पसंद करते हैं।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382018711.png\" alt=\"rId29\" width=\"149\" height=\"175\"> <br>कितने छात्र हिंदी और विज्ञान दोनों पसंद करते हैं, लेकिन गणित को पसंद नहीं करते?</p>",
                    options_en: ["<p>12</p>", "<p>11</p>", 
                                "<p>4</p>", "<p>10</p>"],
                    options_hi: ["<p>12</p>", "<p>11</p>",
                                "<p>4</p>", "<p>10</p>"],
                    solution_en: "<p>10.(c)<br>No. of students like Hindi and Science both but not maths = 4</p>",
                    solution_hi: "<p>10.(c)<br>हिंदी और विज्ञान दोनों पसंद करने वाले लेकिन गणित पसंद नहीं करने वाले छात्रों की संख्या = 4</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. The same operation(s) are followed in all the given number pairs except one. Find that odd number pair. (NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>11. दिए गए संख्या युग्मों में से एक को छोड़कर अन्य सभी में समान संक्रिया/संक्रियाओं का अनुसरण किया गया है। वह असंगत संख्या युग्म ज्ञात कीजिए। <br>(नोट : संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: ["<p>7 : 15</p>", "<p>11 : 121</p>", 
                                "<p>22 : 45</p>", "<p>5 : 11</p>"],
                    options_hi: ["<p>7 : 15</p>", "<p>11 : 121</p>",
                                "<p>22 : 45</p>", "<p>5 : 11</p>"],
                    solution_en: "<p>11.(b)<br><strong>Logic:- </strong>(<math display=\"inline\"><msup><mrow><mn>1</mn></mrow><mrow><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">t</mi></mrow></msup><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">o</mi><mo>.</mo></math>) &times; 2 + 1 = 2nd no.<br>(7 : 15):- (7) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 1 = 15<br>(22 : 45):- (22) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 1 = 45<br>(5 : 11):- (5) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 1 = 11<br>but<br>(11 : 121):- (11) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 1 = 23 &ne; 121</p>",
                    solution_hi: "<p>11.(b)<br><strong>तर्क:-</strong> (प्रथम संख्या) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 1 = दूसरी संख्या <br>(7 : 15):- (7) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 1 = 15<br>(22 : 45):- (22) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 1 = 45<br>(5 : 11):- (5) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 1 = 11<br>लेकिन <br>(11 : 121):- (11) <math display=\"inline\"><mo>&#215;</mo></math> 2 + 1 = 23 &ne; 121</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. What would be the word on the opposite side of \'Feb\' if the given sheet is folded to form a cube ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382018801.png\" alt=\"rId30\" width=\"157\" height=\"190\"></p>",
                    question_hi: "<p>12. यदि नीचे दी गई शीट को मोड़कर एक घन बनाया जाए तो Feb के विपरीत फलक पर कौन-सा शब्द होगा? <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382018801.png\" alt=\"rId30\" width=\"150\" height=\"182\"></p>",
                    options_en: ["<p>Jan</p>", "<p>June</p>", 
                                "<p>May</p>", "<p>Mar</p>"],
                    options_hi: ["<p>Jan</p>", "<p>June</p>",
                                "<p>May</p>", "<p>Mar</p>"],
                    solution_en: "<p>12.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382018911.png\" alt=\"rId31\" width=\"186\" height=\"234\"><br><strong>The opposite faces are :-</strong> March &harr; April, May &harr; February , Jan &harr; June.</p>",
                    solution_hi: "<p>12.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382018911.png\" alt=\"rId31\" width=\"186\" height=\"234\"><br><strong>विपरीत फलक हैं:- </strong>March &harr; April, May &harr; February , Jan &harr; June.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. In a certain code language, \'mix jaggery well\' is written as \'rb co ot\' and \'well done boss\' is written as \'cx rb op\'. How is \'well\' written in the given language?</p>",
                    question_hi: "<p>13. एक निश्चित कूट भाषा में, \'mix jaggery well\' को \'rb co ot\' लिखा जाता है और \'well done boss\' को \'cx rb op\' लिखा जाता है। उसी कूट भाषा में \'well\' को कैसे लिखा जाएगा?</p>",
                    options_en: ["<p>co</p>", "<p>cx</p>", 
                                "<p>rb</p>", "<p>ot</p>"],
                    options_hi: ["<p>co</p>", "<p>cx</p>",
                                "<p>rb</p>", "<p>ot</p>"],
                    solution_en: "<p>13.(c) mix jaggery well &rarr; rb co ot .... (i)<br>well done boss &rarr; cx rb op&hellip;&hellip; (ii)<br>From (i) and (ii) &lsquo;well&rsquo; and &lsquo;rb&rsquo; are common. Hence Code of &lsquo;well&rsquo; = &lsquo;rb&rsquo;.</p>",
                    solution_hi: "<p>13.(c) mix jaggery well &rarr; rb co ot .... (i)<br>well done boss &rarr; cx rb op&hellip;&hellip; (ii)<br>(i) और (ii) से &lsquo;well&rsquo; और \'rb\' उभयनिष्ठ हैं। अतः &lsquo;well&rsquo; का कोड = \'rb\'।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. A, B, C, D, E, F and G are sitting around a circular table facing the centre (but not necessarily in the same order). B sits second to the right of E, who is second to the right of C. G sits third to the left of D, who is not an immediate neighbour of F. A sits second to the right of D. <br>What is the position of E with respect to C?</p>",
                    question_hi: "<p>14. A, B, C, D, E, F और G एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। B, उस E के दाएँ से दूसरे स्थान पर बैठा है, जो C के दाएँ से दूसरे स्थान पर बैठा है। G, उस D के बाएँ से तीसरे स्थान पर बैठा है, जो F का निकटतम पड़ोसी नहीं है। A, D के दाएँ से दूसरे स्थान पर बैठा है। C के सापेक्ष में E का स्थान क्या है?</p>",
                    options_en: ["<p>Immediate right</p>", "<p>Second to the right</p>", 
                                "<p>Immediate left</p>", "<p>Second to the left</p>"],
                    options_hi: ["<p>ठीक दाएँ</p>", "<p>दाएँ से दूसरा</p>",
                                "<p>ठीक बाएँ</p>", "<p>बाएँ से दूसरा</p>"],
                    solution_en: "<p>14.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019082.png\" alt=\"rId32\" width=\"127\" height=\"137\"><br>The position of E with respect to C is second to the right.</p>",
                    solution_hi: "<p>14.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019082.png\" alt=\"rId32\" width=\"127\" height=\"137\"><br>C के संबंध में E का स्थान दायें से दूसरा है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Priya starts from point A and drives 9 km towards North. She then takes a right turn, drives 4 km to reach point B. She then turns right, drives 10 km and stops at point C. In which direction is point C with respect to point A?</p>",
                    question_hi: "<p>15. प्रिया, बिंदु A से शुरू करती है और उत्तर की ओर 9 km ड्राइव करती है। फिर वह दाएँ मुड़ती है, बिंदु B तक पहुँचने के लिए 4 km ड्राइव करती है। फिर वह दाएँ मुड़ती है, 10 km ड्राइव करती है और बिंदु C पर रुक जाती है। बिंदु A के संदर्भ में बिंदु C किस दिशा में है?</p>",
                    options_en: ["<p>South-east</p>", "<p>North-west</p>", 
                                "<p>South</p>", "<p>North-east</p>"],
                    options_hi: ["<p>दक्षिण-पूर्व</p>", "<p>उत्तर-पश्चिम</p>",
                                "<p>दक्षिण</p>", "<p>उत्तर-पूर्व</p>"],
                    solution_en: "<p>15.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019191.png\" alt=\"rId33\" width=\"183\" height=\"175\"><br>C is in the South - East direction with respect to A.</p>",
                    solution_hi: "<p>15.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019395.png\" alt=\"rId34\" width=\"141\" height=\"165\"><br>A के संदर्भ में C दक्षिण-पूर्व दिशा में है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. What will come in the place of the question mark (?) in the following equation if \'+\' and \'&times;\' are interchanged and \'-\' and\' &lsquo;&divide;&rsquo; are interchanged?<br>39 &divide; 6 - 3 &times; 9 + 4 = ?</p>",
                    question_hi: "<p>16. निम्नलिखित समीकरण में प्रश्न चिह्न (?) के स्थान पर क्या आयेगा यदि &lsquo;+&rsquo; और &lsquo;&times;&rsquo; को आपस में बदल दिया जाए और &lsquo;-&rsquo; और &lsquo;&divide;&rsquo; को आपस में बदल दिया जाए?<br>39 &divide; 6 - 3 &times; 9 + 4 = ?</p>",
                    options_en: ["<p>83</p>", "<p>81</p>", 
                                "<p>56</p>", "<p>73</p>"],
                    options_hi: ["<p>83</p>", "<p>81</p>",
                                "<p>56</p>", "<p>73</p>"],
                    solution_en: "<p>16.(d) <strong>Given :- </strong>39 <math display=\"inline\"><mo>&#247;</mo></math> 6 - 3 &times; 9 + 4<br>As per given instruction after interchanging &lsquo;+&rsquo; and &lsquo;&times;&rsquo; and &lsquo;-&rsquo; and &lsquo;<math display=\"inline\"><mo>&#247;</mo></math>&rsquo;<br>39 - 6 <math display=\"inline\"><mo>&#247;</mo></math> 3 + 9 &times; 4<br>39 - 2 + 36<br>37 + 36 = 73</p>",
                    solution_hi: "<p>16.(d) <strong>दिया गया :- </strong>39 <math display=\"inline\"><mo>&#247;</mo></math> 6 - 3 &times; 9 + 4<br>दिए गए निर्देश के अनुसार \'+\' और \'&times;\' और \'-\' और \'<math display=\"inline\"><mo>&#247;</mo></math>\' को आपस में बदलने के बाद<br>39 - 6 <math display=\"inline\"><mo>&#247;</mo></math> 3 + 9 &times; 4<br>39 - 2 + 36<br>37 + 36 = 73</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>17. निम्नलिखित संख्याओं में से तीन संख्याएं एक निश्चित प्रकार से एकसमान हैं और एक उनसे असंगत है। उस असंगत संख्या का चयन कीजिए।<br>(नोट : संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: ["<p>829</p>", "<p>643</p>", 
                                "<p>821</p>", "<p>237</p>"],
                    options_hi: ["<p>829</p>", "<p>643</p>",
                                "<p>821</p>", "<p>237</p>"],
                    solution_en: "<p>17.(d)<br>All the options have prime numbers except option (d).</p>",
                    solution_hi: "<p>17.(d)<br>विकल्प (d) को छोड़कर सभी विकल्पों में अभाज्य संख्याएँ हैं ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Each of the letters in the word &lsquo;REDUCTION&rsquo; are arranged in the English alphabetical order. How many letters are there in the English alphabetical order between the letter which is the third from the left end and the fourth from the left end in the group of letters thus formed ?</p>",
                    question_hi: "<p>18. शब्द \'REDUCTION\' के प्रत्येक अक्षर को अंग्रेजी वर्णानुक्रम में व्यवस्थित किया गया है। इस प्रकार बने अक्षरों के समूह में बाएं से तीसरे और बाएं से चौथे अक्षर के बीच अंग्रेजी वर्णानुक्रम में कितने अक्षर हैं ?</p>",
                    options_en: ["<p>Two</p>", "<p>One</p>", 
                                "<p>Three</p>", "<p>Four</p>"],
                    options_hi: ["<p>दो</p>", "<p>एक</p>",
                                "<p>तीन</p>", "<p>चार</p>"],
                    solution_en: "<p>18.(c) <strong>Given: </strong>REDUCTION<br>Arrange in english alphabetical order - CDEINORTU<br>The letter third from the left is &lsquo;E&rsquo; and fourth from the left end is &lsquo;I&rsquo;<br>Letter between &lsquo;E&rsquo; and &lsquo;I&rsquo; in the English alphabet is three.</p>",
                    solution_hi: "<p>18.(c) <strong>दिया गया: </strong>REDUCTION<br>अंग्रेजी वर्णमाला क्रम में व्यवस्थित करने पर - CDEINORTU<br>बाएं छोर से तीसरा अक्षर \'E\' है और बाएं छोर से चौथा अक्षर \'I\' है<br>अंग्रेजी वर्णमाला में \'E\' और \'I\' के बीच अक्षर तीन है।.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Select the set in which the numbers are related in the same way as are the numbers of the following set.</p>\n<p>(24, 15, 120)&nbsp;<br>(21, 19, 133) <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>19. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ उसी प्रकार संबंधित हैं, जिस प्रकार निम्नलिखित समुच्चयों की संख्याएँ संबंधित हैं। <br>(24, 15, 120) <br>(21, 19, 133) <br>(नोट: पूर्ण संख्याओं पर संक्रिया का प्रयोग, संख्याओं को उनके घटक अंकों में विभक्त किए बिना किया जाना चाहिए। उदाहरण के लिए, 13 :- 13 पर संक्रिया जैसे कि जोड़ने/घटाने/गुणा करने आदि को 13 में किया जा सकता है। 13 को 1 और 3 में विभक्त करके और फिर 1 और 3 पर गणितीय संक्रियाओं का प्रयोग करने की अनुमति नहीं है।)</p>",
                    options_en: ["<p>(18, 16, 99)</p>", "<p>(31, 9, 96)</p>", 
                                "<p>(33, 10, 170)</p>", "<p>(27, 12, 108)</p>"],
                    options_hi: ["<p>(18, 16, 99)</p>", "<p>(31, 9, 96)</p>",
                                "<p>(33, 10, 170)</p>", "<p>(27, 12, 108)</p>"],
                    solution_en: "<p>19.(d)<br><strong>Logic:- </strong><math display=\"inline\"><mfrac><mrow><msup><mrow><mn>1</mn></mrow><mrow><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">t</mi></mrow></msup><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">o</mi><mo>.</mo></mrow><mrow><mn>3</mn></mrow></mfrac><mo>&#215;</mo></math> 2<sup>nd </sup>no. = 3<sup>rd</sup> no.<br>(24, 15, 120) :- <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>&#215;</mo></math> 15 &rArr; 8 &times; 15 = 120<br>(21, 19, 133) :- <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>&#215;</mo></math> 19 &rArr; 7 &times; 19 = 133<br>Similarly, <br>(27, 12, 108) :- <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>&#215;</mo></math> 12 &rArr; 9 &times; 12 = 108</p>",
                    solution_hi: "<p>19.(d)<br>तर्क :- <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2368;</mi><mi>&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi>&#160;</mi></mrow><mn>3</mn></mfrac></math>&times; दूसरी संख्या = तीसरी संख्या <br>(24, 15, 120) :- <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>&#215;</mo></math> 15 &rArr; 8 &times; 15 = 120<br>(21, 19, 133) :- <math display=\"inline\"><mfrac><mrow><mn>21</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>&#215;</mo></math> 19 &rArr; 7 &times; 19 = 133<br>इसी प्रकार , <br>(27, 12, 108) :- <math display=\"inline\"><mfrac><mrow><mn>27</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>&#215;</mo></math> 12 &rArr; 9 &times; 12 = 108</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. If a man\'s age is four times the sum of the ages of his three children, and after 6 years his age will be twice the sum of the ages of his three children, then what is the man\'s present age?</p>",
                    question_hi: "<p>20. यदि एक व्यक्ति की आयु उसके तीन बच्चों की आयु के योग की चार गुना है, और 6 वर्ष बाद उसकी आयु उसके तीनों बच्चों की आयु के योग की दोगुनी हो जाएगी, तो उस व्यक्ति की वर्तमान आयु कितनी है?</p>",
                    options_en: ["<p>60 years</p>", "<p>56 years</p>", 
                                "<p>45 years</p>", "<p>30 years</p>"],
                    options_hi: ["<p>60 वर्ष</p>", "<p>56 वर्ष</p>",
                                "<p>45 वर्ष</p>", "<p>30 वर्ष</p>"],
                    solution_en: "<p>20.(a) Let age of three children are p year , q year , r year<br>According to question , <br>Man&rsquo;s age (M ) = 4 (p + q + r) &hellip;(i)<br>After 6 Years<br><math display=\"inline\"><mo>&#8658;</mo></math> M + 6 = 2 (p + 6 + q + 6 + r + 6 ) <br><math display=\"inline\"><mo>&#8658;</mo></math>M + 6 = 2 (p + q + r) + 36<br>From eq (i)<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 (p + q + r) + 6 = 2 (p + q + r) + 36<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 (p + q + r) = 30 &rArr; (p + q + r) = 15<br>&there4; Man&rsquo;s Present age = 4 &times; 15 = 60 years</p>",
                    solution_hi: "<p>20.(a) माना तीन बच्चों की उम्र क्रमशः p वर्ष, q वर्ष, r वर्ष है । <br>प्रश्न के अनुसार,<br>व्यक्ति की आयु (M ) = 4 (p + q + r) &hellip;(i)<br>6 वर्ष बाद<br><math display=\"inline\"><mo>&#8658;</mo></math> M + 6 = 2 (p + 6 + q + 6 + r + 6 ) <br><math display=\"inline\"><mo>&#8658;</mo></math>M + 6 = 2 (p + q + r) + 36<br>समीकरण (i) से<br><math display=\"inline\"><mo>&#8658;</mo></math> 4 (p + q + r) + 6 = 2 (p + q + r) + 36<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 (p + q + r) = 30 &rArr; (p + q + r) = 15<br>&there4; व्यक्ति की वर्तमान आयु = 4 &times; 15 = 60 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. If 4 December 2014 was Thursday, then what was the day of the week on 10 December 2018?</p>",
                    question_hi: "<p>21. यदि 4 दिसंबर 2014 को गुरुवार था, तो 10 दिसंबर 2018 को सप्ताह का कौन-सा दिन रहा होगा?</p>",
                    options_en: ["<p>Monday</p>", "<p>Tuesday</p>", 
                                "<p>Wednesday</p>", "<p>Friday</p>"],
                    options_hi: ["<p>सोमवार</p>", "<p>मंगलवार</p>",
                                "<p>बुधवार</p>", "<p>शुक्रवार</p>"],
                    solution_en: "<p>21.(a) 4 December 2014 was Thursday. On moving to 2018 the number of odd days = <br>+1 + 2 + 1 + 1 = 5. We have reached till 4 December 2018, but we have to reach till 10 December, the number of days between = 6. Total number of day = 6 + 5 = 11. On dividing 11 by 7, the remainder = 4. Thursday + 4 = Monday.</p>",
                    solution_hi: "<p>21.(a) 4 दिसंबर 2014 को गुरुवार था. 2018 में जाने पर विषम दिनों की संख्या = <br>+1 + 2 + 1 + 1 = 5. हम 4 दिसंबर 2018 तक पहुंच गए हैं, लेकिन हमें 10 दिसंबर तक पहुंचना है, बीच में दिनों की संख्या = 6. दिनों की कुल संख्या = 6 + 5 = 11. 11 को 7 से विभाजित करने पर शेषफल = 4. गुरुवार + 4 = सोमवार।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Which of the following letter-clusters should replace # and % so that the pattern and relationship followed between the letter-cluster pair on the left side of :: is the same as that on the right side of :: ? <br># : VXB :: ACG : %</p>",
                    question_hi: "<p>22. निम्नलिखित में से कौन-सा अक्षर-समूह # और % के स्थान पर आना चाहिए ताकि :: के बाएँ ओर के अक्षर- समूह युग्म के बीच जिस पैटर्न और संबंध का अनुसरण किया गया है, उसी पैटर्न और संबंध का अनुसरण :: के दाएँ ओर के अक्षर-समूह युग्म में किया जाता हो?<br># : VXB:: ACG : %</p>",
                    options_en: ["<p># - GIM, % - KMQ</p>", "<p># - UVN, % - KMQ</p>", 
                                "<p># - HDN, % - BDH</p>", "<p># - UWA, % - BDH</p>"],
                    options_hi: ["<p># - GIM, % - KMQ</p>", "<p># - UVN, % - KMQ</p>",
                                "<p># - HDN, % - BDH</p>", "<p># - UWA, % - BDH</p>"],
                    solution_en: "<p>22.(d)</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019513.png\" alt=\"rId35\" width=\"142\" height=\"98\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019724.png\" alt=\"rId36\" width=\"150\" height=\"102\"></p>",
                    solution_hi: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019513.png\" alt=\"rId35\" width=\"174\" height=\"120\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019724.png\" alt=\"rId36\" width=\"173\" height=\"118\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the correct mirror image of the given figure when the mirror is placed at MN as shown. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019828.png\" alt=\"rId37\" width=\"108\" height=\"113\"></p>",
                    question_hi: "<p>23. दर्पण को MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिम्ब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019828.png\" alt=\"rId37\" width=\"108\" height=\"113\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019929.png\" alt=\"rId38\" width=\"80\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020063.png\" alt=\"rId39\" width=\"81\" height=\"84\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020189.png\" alt=\"rId40\" width=\"80\" height=\"75\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020279.png\" alt=\"rId41\" width=\"80\" height=\"77\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382019929.png\" alt=\"rId38\" width=\"80\" height=\"83\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020063.png\" alt=\"rId39\" width=\"80\" height=\"83\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020189.png\" alt=\"rId40\" width=\"80\" height=\"75\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020279.png\" alt=\"rId41\" width=\"81\" height=\"78\"></p>"],
                    solution_en: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020189.png\" alt=\"rId40\" width=\"80\" height=\"75\"></p>",
                    solution_hi: "<p>23.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020189.png\" alt=\"rId40\" width=\"81\" height=\"76\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below. (The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants vowels in the word.) <br>Continue - Interrupt</p>",
                    question_hi: "<p>24. उस शब्द-युग्म का चयन कीजिए जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए समान संबंध का सबसे अच्छा निरूपण करता है। (शब्दों को अर्थपूर्ण हिंदी शब्दों के रूप में माना जाना चाहिए और उन्हें शब्द में अक्षरों की संख्या व्यंजन स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं किया जाना चाहिए।)<br>निरंतर - व्यवधान</p>",
                    options_en: ["<p>Cow - Buffalo</p>", "<p>Attic - Common</p>", 
                                "<p>Safe - Secure</p>", "<p>Connect - Join</p>"],
                    options_hi: ["<p>गाय - भैंस</p>", "<p>अटारी - तहखाना</p>",
                                "<p>सकुशल - सुरक्षित</p>", "<p>जोड़ना - मिलाना</p>"],
                    solution_en: "<p>24.(b) The correct answer is Attic - Common.</p>",
                    solution_hi: "<p>24.(b) सही उत्तर अटारी - तहखाना है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. In a certain code language, \'STUD\' is coded as \'8469\', and \'MUST\' is coded as \'9764\'. What is the code for \'M\' in that language?</p>",
                    question_hi: "<p>25. एक निश्चित कूट भाषा में \'STUD\' को \'8469\' के रूप में कूटबद्ध किया जाता है और \'MUST\' को \'9764\' के रूप में कूटबद्ध किया जाता है। उस भाषा में \'M\' के लिए कूट क्या है?</p>",
                    options_en: ["<p>6</p>", "<p>7</p>", 
                                "<p>8</p>", "<p>9</p>"],
                    options_hi: ["<p>6</p>", "<p>7</p>",
                                "<p>8</p>", "<p>9</p>"],
                    solution_en: "<p>25.(b) STUD &rarr; 8 4 6 9&hellip;&hellip;. (i)<br>MUST &rarr; 9 7 6 4&hellip;&hellip;.. (ii)<br>From (i) and (ii) &lsquo;U&rsquo; &lsquo;S&rsquo; and &lsquo;T&rsquo; and &lsquo;6&rsquo;,&lsquo;9&rsquo; and &lsquo;4&rsquo; are common. The only different &lsquo;M&rsquo; and &lsquo;7&rsquo;. The code of &lsquo;M&rsquo; = &lsquo;7&rsquo;</p>",
                    solution_hi: "<p>25.(b) STUD &rarr; 8 4 6 9&hellip;&hellip;. (i)<br>MUST &rarr; 9 7 6 4&hellip;&hellip;.. (ii)<br>(i) और (ii) से \'U\' \'S\' और \'T\' और \'6\', \'9\' और \'4\' उभयनिष्ठ हैं। केवल \'M\' और \'7\' भिन्न हैं। \'M\' का कोड = \'7\'</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following options has all vector quantities?</p>",
                    question_hi: "<p>26. निम्नलिखित में से किस विकल्प में सभी सदिश राशियाँ (vector quantities) हैं?</p>",
                    options_en: ["<p>Force, Velocity, Momentum, Energy and Power</p>", "<p>Force, Velocity, Momentum, Energy and Acceleration</p>", 
                                "<p>Power, Momentum, Energy, Speed and Work</p>", "<p>Force, Velocity, Momentum, Weight and Acceleration</p>"],
                    options_hi: ["<p>बल, वेग, संवेग, ऊर्जा और शक्ति</p>", "<p>बल, वेग, संवेग, ऊर्जा और त्वरण</p>",
                                "<p>शक्ति, संवेग, ऊर्जा, चाल और कार्य</p>", "<p>बल, वेग, संवेग, भार और त्वरण</p>"],
                    solution_en: "<p>26.(d) Vector quantities have both magnitude and direction. Force (F), Velocity (v), Momentum (p), Weight (w), and Acceleration (a) are all vector quantities because they have both magnitude and direction. Energy (E), Power (P), Speed (v) and Work (W) are scalar quantities because they only have magnitude and no direction.</p>",
                    solution_hi: "<p>26.(d) सदिश राशियों में परिमाण और दिशा दोनों होते हैं। बल (F), वेग (v), संवेग (p), भार (w) और त्वरण (a) सभी सदिश राशियाँ हैं क्योंकि उनमें परिमाण और दिशा दोनों होते हैं। ऊर्जा (E), शक्ति (P), गति (v) और कार्य (W) अदिश राशियाँ हैं क्योंकि उनमें केवल परिमाण होता है और कोई दिशा नहीं होती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Who became the first Indian woman to win an Olympic medal?.</p>",
                    question_hi: "<p>27. ओलंपिक पदक जीतने वाली पहली भारतीय महिला कौन बनीं?</p>",
                    options_en: ["<p>Sakshi Malik</p>", "<p>Mary Kom</p>", 
                                "<p>Saina Nehwal</p>", "<p>Karnam Malleswari</p>"],
                    options_hi: ["<p>साक्षी मलिक</p>", "<p>मैरी कॉम</p>",
                                "<p>साइना नेहवाल</p>", "<p>कर्णम मल्लेश्वरी</p>"],
                    solution_en: "<p>27.(d) <strong>Karnam Malleswari</strong> (Weightlifter). Other Indian female Olympic winners: Mary Kom (Boxer) - First Olympic medal in boxing by an Indian woman. Sakshi Malik (Wrestler) - The first Indian female wrestler to bag a medal at the Olympics (2016). Saina Nehwal (Badminton) - The first Indian badminton player to win an Olympic medal and has represented the country in three Summer Games (Beijing 2008, London 2012, Rio 2016). International Olympic Committee: Founded on 23 June 1894, Paris (France). Headquarters - Lausanne, Switzerland.</p>",
                    solution_hi: "<p>27.(d) <strong>कर्णम मल्लेश्वरी</strong> (भारोत्तोलक)। अन्य भारतीय महिला ओलंपिक विजेता: मैरी कॉम (मुक्केबाज) - मुक्केबाजी में किसी भारतीय महिला द्वारा पहला ओलंपिक पदक। साक्षी मलिक (पहलवान) - ओलंपिक (2016) में पदक जीतने वाली पहली भारतीय महिला पहलवान। साइना नेहवाल (बैडमिंटन) - ओलंपिक पदक जीतने वाली पहली भारतीय बैडमिंटन खिलाड़ी और तीन ग्रीष्मकालीन खेलों (बीजिंग 2008, लंदन 2012, रियो 2016) में देश का प्रतिनिधित्व किया। अंतर्राष्ट्रीय ओलंपिक समिति: 23 जून 1894 को पेरिस (फ्रांस) में स्थापित। मुख्यालय - लुसाने, स्विट्जरलैंड।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Bilhan was a court poet in which of the following kingdoms?</p>",
                    question_hi: "<p>28. बिल्हान (Bilhan) निम्नलिखित में से किस साम्राज्य के दरबारी कवि थे?</p>",
                    options_en: ["<p>Vijayaditya VII</p>", "<p>Vikramaditya VI</p>", 
                                "<p>Kulottunga I</p>", "<p>Rajaraja II</p>"],
                    options_hi: ["<p>विजयादित्य VII</p>", "<p>विक्रमादित्य VI</p>",
                                "<p>कुलोत्तुंगा I</p>", "<p>राजराज II</p>"],
                    solution_en: "<p>28.(b) <strong>Vikramaditya VI.</strong> The Chalukya dynasty was established by Pulakeshin I in 543. He took Vatapi (modern Badami in Bagalkot district, Karnataka) under his control and made it his capital. He and his descendants are referred to as \"Chalukyas of Badami\".</p>",
                    solution_hi: "<p>28.(b) <strong>विक्रमादित्य VI</strong>. चालुक्य वंश की स्थापना पुलकेशिन प्रथम ने 543 ई. में की थी। उन्होंने वातापी (कर्नाटक के बागलकोट जिले में आधुनिक बादामी) को अपने नियंत्रण में लिया और इसे अपनी राजधानी बनाया। उन्हें और उनके वंशजों को \"बादामी के चालुक्य\" के रूप में जाना जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. If four resistances of 4 ohm each are connected in parallel, what will be their effective resistance?</p>",
                    question_hi: "<p>29. यदि प्रत्येक 4 ओम के चार प्रतिरोधक (resistances) समानांतर क्रम (parallel) में जुड़े हुए हैं, तो उनका प्रभावी प्रतिरोध क्या होगा?</p>",
                    options_en: ["<p>4 ohm</p>", "<p>1 ohm</p>", 
                                "<p>10 ohm</p>", "<p>16 ohm</p>"],
                    options_hi: ["<p>4 ओम</p>", "<p>1 ओम</p>",
                                "<p>10 ओम</p>", "<p>16 ओम</p>"],
                    solution_en: "<p>29.(b) 1 ohm. The formula for the effective resistance 𝑅eff of resistors connected in parallel is given by:<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>f</mi><mi>f</mi></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>1</mn></msub></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>2</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>3</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>4</mn></msub></mfrac></math><br>For four resistors of 4 ohms each:<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>f</mi><mi>f</mi></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>= 1<br>Thus, <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>f</mi><mi>f</mi></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1</mn></mfrac></math>= 1 ohm. So, the effective resistance is 1 ohm.</p>",
                    solution_hi: "<p>29.(b) 1 ओम। समान्तर क्रम में जुड़े प्रतिरोधकों के प्रभावी प्रतिरोध 𝑅eff का सूत्र इस प्रकार दिया गया है:<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>f</mi><mi>f</mi></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>1</mn></msub></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>2</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>3</mn></msub></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msub><mi>R</mi><mn>4</mn></msub></mfrac></math><br>प्रत्येक 4 ओम के चार प्रतिरोधकों के लिए:<br><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>f</mi><mi>f</mi></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>= 1<br>इस प्रकार, <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><msub><mrow><mi>R</mi></mrow><mrow><mi>e</mi><mi>f</mi><mi>f</mi></mrow></msub></mrow></mfrac></math> = 11 = 1 ओम। इसलिए, प्रभावी प्रतिरोध 1 ओम होगा।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. To which of the following places would a school teacher take her students for a field trip to see Hoshang Shah\'s tomb, Hindola Mahal, Champa Baoli and Jahaaz Mahal?</p>",
                    question_hi: "<p>30. निम्नलिखित में से किस स्थान पर कोई स्कूल शिक्षिका अपने विद्यार्थियों को होशंग शाह का मकबरा, हिंडोला महल, चंपा बावली और जहाज महल देखने के लिए भ्रमण पर ले जाएगी?</p>",
                    options_en: ["<p>Mandu, Madhya Pradesh</p>", "<p>Varanasi, Uttar Pradesh</p>", 
                                "<p>Agra, Uttar Pradesh</p>", "<p>Ratlam, Madhya Pradesh</p>"],
                    options_hi: ["<p>मांडू, मध्य प्रदेश</p>", "<p>वाराणसी, उत्तर प्रदेश</p>",
                                "<p>आगरा, उत्तर प्रदेश</p>", "<p>रतलाम, मध्य प्रदेश</p>"],
                    solution_en: "<p>30.(a) <strong>Mandu, Madhya Pradesh. </strong>Hoshang Shah\'s tomb, India&rsquo;s first marble tomb, dates to the 15th century. Hindola Mahal was constructed under Hushang Shah\'s reign circa 1425 AD. Champa Baoli is within the Jahaz Mahal complex, situated between Kapoor and Munj Sagar Talab.</p>",
                    solution_hi: "<p>30.(a) <strong>मांडू, मध्य प्रदेश । </strong>होशंग शाह का मकबरा, भारत का पहला संगमरमर का मकबरा, 15वीं शताब्दी का है। हिंडोला महल का निर्माण हुशंग शाह के शासनकाल में लगभग 1425 ई. में हुआ था। चंपा बावली जहाज महल परिसर के भीतर है, जो कपूर और मुंज सागर तालाब के बीच स्थित है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. ______ is the provision of small credit to those who would have no other access to financial services.</p>",
                    question_hi: "<p>31. निम्नलिखित में से क्&zwj;या उन लोगों के लिए छोटे ऋण का प्रावधान करता है जिनकी वित्तीय सेवाओं तक कोई अन्य पहुंच नहीं होती है?</p>",
                    options_en: ["<p>Microloan</p>", "<p>Microfinance</p>", 
                                "<p>Home Loan</p>", "<p>Pradhan Mantri Awas Yojana</p>"],
                    options_hi: ["<p>सूक्ष्म ऋण</p>", "<p>सूक्ष्म वित्त</p>",
                                "<p>गृह ऋण</p>", "<p>प्रधानमंत्री आवास योजना</p>"],
                    solution_en: "<p>31.(b) <strong>Microfinance</strong> institutions (MFIs) provide financial services, such as microloans, microsavings, and microinsurance, to low-income populations. The definition of \"small loans\" varies by country. In India, loans below Rs. 1 lakh are considered microloans. Examples of MFIs: Equitas Small Finance, Fusion Microfinance Pvt Ltd, Annapurna Microfinance Pvt Ltd.</p>",
                    solution_hi: "<p>31.(b) <strong>सूक्ष्म वित्त</strong> संस्थाएं (MFI) कम आय वाले लोगों को सूक्ष्म ऋण, सूक्ष्म बचत और सूक्ष्म बीमा जैसी वित्तीय सेवाएं प्रदान करती हैं। \"छोटे ऋणों\" की परिभाषा देश के अनुसार अलग-अलग होती है। भारत में, 1 लाख रुपये से कम के ऋणों को सूक्ष्म ऋण माना जाता है। MFI के उदाहरण: इक्विटास स्मॉल फाइनेंस, फ्यूजन माइक्रोफाइनेंस प्राइवेट लिमिटेड, अन्नपूर्णा माइक्रोफाइनेंस प्राइवेट लिमिटेड।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. What is the product formed when zinc and sulphuric acid react?</p>",
                    question_hi: "<p>32. जिंक और सल्फ्यूरिक अम्ल की अभिक्रिया से कौन-सा उत्पाद निर्मित होता है?</p>",
                    options_en: ["<p>Zinc sulphate</p>", "<p>Zinc hydroxide</p>", 
                                "<p>Zinc sulphide</p>", "<p>Zinc oxide</p>"],
                    options_hi: ["<p>जिंक सल्फेट</p>", "<p>जिंक हाइड्रॉक्साइड</p>",
                                "<p>जिंक सल्फाइड</p>", "<p>जिंक ऑक्साइड</p>"],
                    solution_en: "<p>32.(a) <strong>Zinc sulphate.</strong> Zinc reacts with dilute Sulphuric acid to form Zinc sulphate and Hydrogen gas is evolved from the reaction. This is a single displacement reaction of a non-metal by a metal. Both metals and nonmetals take part in displacement reactions.</p>\n<p>&nbsp;Zn (s) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><msub><mi>SO</mi><mn>4</mn></msub></math>(aq) &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>ZnSO</mi><mn>4</mn></msub></math>(aq) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math>(g).</p>",
                    solution_hi: "<p>32.(a) जिंक सल्फेट। जिंक तनु सल्फ्यूरिक अम्ल के साथ अभिक्रिया करके जिंक सल्फेट बनाता है और अभिक्रिया से हाइड्रोजन गैस निकलती है। यह एक धातु द्वारा अधातु की एकल विस्थापन अभिक्रिया है। धातु और अधातु दोनों ही विस्थापन अभिक्रियाओं में भाग लेते हैं।<br>&nbsp;Zn (s) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub><msub><mi>SO</mi><mn>4</mn></msub></math>(aq) &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi>ZnSO</mi><mn>4</mn></msub></math>(aq) + <math xmlns=\"http://www.w3.org/1998/Math/MathML\" class=\"wrs_chemistry\"><msub><mi mathvariant=\"normal\">H</mi><mn>2</mn></msub></math>(g).</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which amongst the following statements is correct about Mughal emperor Akbar?</p>",
                    question_hi: "<p>33. मुगल बादशाह अकबर के बारे में निम्नलिखित में से कौन सा कथन सही है?</p>",
                    options_en: ["<p>He founded a new religion called &lsquo;Din-e Ilahi&rsquo;.</p>", "<p>He fought and won the third battle of Panipat against the Hindu King Hemu.</p>", 
                                "<p>He was declared emperor in 1602.</p>", "<p>He was highly educated and knew many languages.</p>"],
                    options_hi: ["<p>उसने \'दीन-ए इलाही\' नामक एक नए धर्म की स्थापना की।</p>", "<p>उसने हिंदू राजा हेमू के विरूद्ध पानीपत का तृतीय युद्ध लड़ा और जीता।</p>",
                                "<p>उसे 1602 में सम्राट घोषित किया गया था।</p>", "<p>वह बहुत अधिक शिक्षित था और कई भाषाओं को जानता था।</p>"],
                    solution_en: "<p>33.(a)<strong> He founded a new religion called &lsquo;Din-e Ilahi&rsquo;.</strong> The theory of Din-i-Ilahi means belief in one God. The first initiated disciples of Din-i-Ilahi during emperor Akbar included Birbal, Prince Salim, and Abul-Fazl ibn Mubarak. He became the third emperor of the Mughal dynasty in 1556 AD till 1605. The Second Battle of Panipat (November 5, 1556) was fought between Hemu, and the army of Akbar.</p>",
                    solution_hi: "<p>33.(a) <strong>उन्होंने \'दीन-ए-इलाही\' नामक एक नए धर्म की स्थापना की।</strong> दीन-ए-इलाही का अर्थ है एक ईश्वर में विश्वास। सम्राट अकबर के दौरान दीन-ए-इलाही के पहले दीक्षित शिष्यों में बीरबल, राजकुमार सलीम और अबुल-फ़ज़ल इब्न मुबारक शामिल थे। वह 1556 ई. से 1605 तक मुगल वंश का तीसरा सम्राट रहा। पानीपत की दूसरी लड़ाई (5 नवंबर, 1556) , हेमू और अकबर की सेना के बीच लड़ी गई थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. In which form of exchange are goods used as a medium of exchange?</p>",
                    question_hi: "<p>34. विनिमय के माध्यम के रूप में माल का उपयोग किस प्रकार के विनिमय में किया जाता है?</p>",
                    options_en: ["<p>Online exchange</p>", "<p>Credit exchange</p>", 
                                "<p>Currency exchange</p>", "<p>Barter exchange</p>"],
                    options_hi: ["<p>ऑनलाइन विनिमय</p>", "<p>क्रेडिट विनिमय</p>",
                                "<p>मुद्रा विनिमय</p>", "<p>वस्तु विनिमय</p>"],
                    solution_en: "<p>34.(d)<strong> Barter exchange</strong>. The barter system is a method of exchange where goods are traded directly for other goods without using money. However, it faced several challenges, such as the lack of double coincidence of wants, the absence of a common measure of value, no standard for deferred or future payments, and issues with storing wealth, which could lead to waste.</p>",
                    solution_hi: "<p>34.(d) <strong>वस्तु विनिमय।</strong> वस्तु विनिमय प्रणाली विनिमय की एक विधि है जिसमें मुद्रा का उपयोग किए बिना वस्तुओं के बदले सीधे अन्य वस्तुओं का व्यापार किया जाता है। हालाँकि, इसे कई चुनौतियों का सामना करना पड़ा, जैसे कि इच्छाओं के दोहरे संयोग की कमी, मूल्य के एक सामान्य माप की अनुपस्थिति, स्थगित या भविष्य के भुगतानों के लिए कोई मानक नहीं, और धन के भंडारण से जुड़ी समस्याएँ, जो बर्बादी का कारण बन सकती हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. The decomposition of gaseous Ammonia on a hot platinum surface is a _______________ order reaction at high pressure.</p>",
                    question_hi: "<p>35. एक गर्म प्लेटिनम सतह पर गैसीय अमोनिया का अपघटन उच्च दाब पर __________ क्रम की अभिक्रिया है।</p>",
                    options_en: ["<p>two</p>", "<p>zero</p>", 
                                "<p>three</p>", "<p>one</p>"],
                    options_hi: ["<p>दो</p>", "<p>शून्य</p>",
                                "<p>तीन</p>", "<p>एक</p>"],
                    solution_en: "<p>35.(b) <strong>zero.</strong> In this reaction, platinum serves as a catalyst. Under high pressure, the metal surface becomes saturated with gas molecules. Chemical decomposition, also known as chemical breakdown, is the process of breaking down a single chemical entity (such as a molecule or reaction intermediate) into two or more fragments. It is generally considered the opposite of chemical synthesis.</p>",
                    solution_hi: "<p>35.(b) <strong>शून्य।</strong> इस अभिक्रिया में प्लेटिनम उत्प्रेरक का कार्य करता है। उच्च दाब में, धातु की सतह गैस के अणुओं से संतृप्त हो जाती है। रासायनिक अपघटन, जिसे रासायनिक विखंडन के रूप में भी जाना जाता है, एक एकल रासायनिक इकाई (जैसे अणु या प्रतिक्रिया मध्यवर्ती) को दो या अधिक भागों में तोड़ने की प्रक्रिया है। इसे सामान्यतः रासायनिक संश्लेषण के विपरीत माना जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Bahu Mela in_______is the major festival held at the Kali Temple in the Bahu Fort twice a year.</p>",
                    question_hi: "<p>36. बाहु किले में काली मंदिर में साल में दो बार आयोजित होने वाला बाहु मेला______का एक मुख्य त्यौहार है।</p>",
                    options_en: ["<p>Punjab</p>", "<p>Leh</p>", 
                                "<p>Jammu</p>", "<p>Haryana</p>"],
                    options_hi: ["<p>पंजाब</p>", "<p>लेह</p>",
                                "<p>जम्मू</p>", "<p>हरियाणा</p>"],
                    solution_en: "<p>36.(c) <strong>Jammu.</strong> Bahu Mela, one of the largest Hindu festivals in Jammu, is a vibrant celebration held at Bahu Fort. Major festivals in Jammu and Kashmir include the Tulip Festival, Shikara Festival, Saffron Festival and Gurez Festival.</p>",
                    solution_hi: "<p>36.(c) <strong>जम्मू।</strong> जम्मू में सबसे बड़े हिंदू त्योहारों में से एक बाहू मेला, बाहू किले में आयोजित एक जीवंत उत्सव है। जम्मू और कश्मीर के प्रमुख त्योहारों में ट्यूलिप फेस्टिवल, शिकारा फेस्टिवल, केसर फेस्टिवल और गुरेज फेस्टिवल शामिल हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. On 31 October 1940, who among the following was arrested for offering individual Satyagraha to protest against India&rsquo;s forced participation in the Second World War ?</p>",
                    question_hi: "<p>37. 31 अक्टूबर 1940 को, द्वितीय विश्व युद्ध में भारत की जबरन भागीदारी के विरोध में व्यक्तिगत सत्याग्रह की पेशकश करने के लिए निम्नलिखित में से किसे गिरफ्तार किया गया था ?</p>",
                    options_en: ["<p>Sardar Vallabhbhai Patel</p>", "<p>Saifuddin Kitchlew</p>", 
                                "<p>Jawaharlal Nehru</p>", "<p>Subhas Chandra Bose</p>"],
                    options_hi: ["<p>सरदार वल्लभभाई पटेल</p>", "<p>सैफुद्दीन किचलू</p>",
                                "<p>जवाहर लाल नेहरू</p>", "<p>सुभाष चंद्र बोस</p>"],
                    solution_en: "<p>37.(c) <strong>Jawaharlal Nehru.</strong> On 31 October 1940, Jawaharlal Nehru was arrested for participating in individual Satyagraha, protesting against India&rsquo;s forced involvement in the Second World War. During this period, Nehru also engaged in limited Civil Disobedience and spent approximately nine years in jail between 1921 and 1945.</p>",
                    solution_hi: "<p>37.(c) <strong>जवाहरलाल नेहरू।</strong> 31 अक्टूबर 1940 को जवाहरलाल नेहरू को व्यक्तिगत सत्याग्रह में भाग लेने के लिए गिरफ्तार किया गया, जो भारत की मजबूरन द्वितीय विश्व युद्ध में भागीदारी के खिलाफ विरोध था। इस दौरान नेहरू ने सीमित सविनय अवज्ञा आंदोलन में भी भाग लिया और 1921 से 1945 के बीच लगभग नौ साल जेल में बिताए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. What is it called in basketball when a shot goes through the basket without touching the rim or backboard?</p>",
                    question_hi: "<p>38. बास्केटबॉल में, जब एक शॉट रिम या बैकबोर्ड को छुए बिना बास्केट से जाता है, तो इसे क्या कहा जाता है?</p>",
                    options_en: ["<p>Screen</p>", "<p>Dime</p>", 
                                "<p>Swish</p>", "<p>Technical foul</p>"],
                    options_hi: ["<p>स्क्रीन (Screen)</p>", "<p>डाइम (Dime)</p>",
                                "<p>स्विश (Swish)</p>", "<p>टेक्निकल फाउल (Technical foul)</p>"],
                    solution_en: "<p>38.(c) <strong>Swish.</strong> Other terminologies : A screen is a blocking maneuver by an offensive player that creates space for a teammate to receive a pass or take a shot; it is also known as a pick. A \"dime\" refers to a precisely delivered pass that allows a teammate to easily score. A technical foul is called on a player, coach, or team for unsportsmanlike conduct, typically without any physical contact with an opponent.</p>",
                    solution_hi: "<p>38.(c) <strong>स्विश (Swish)।</strong> अन्य शब्दावली : स्क्रीन एक आक्रामक खिलाड़ी द्वारा किया जाने वाला अवरोधक प्रयास है जो एक साथी खिलाड़ी के लिए पास प्राप्त करने या शॉट लेने के लिए जगह बनाता है; इसे पिक के नाम से भी जाना जाता है। एक \"डाइम\" एक सटीक रूप से दिया गया पास है जो एक साथी खिलाड़ी को आसानी से स्कोर करने की अनुमति प्रदान करता है। तकनीकी फाउल एक खिलाड़ी, कोच, या टीम पर खेल भावना के विपरीत व्यवहार के लिए दिया जाता है, आमतौर पर प्रतिद्वंद्वी के साथ किसी भी शारीरिक संपर्क के बिना।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Microbes like Rhizobium, Nitrosomonas and Nitrobacter are used for:</p>",
                    question_hi: "<p>39. राइजोबियम, नाइट्रोसोमोनास और नाइट्रोबैक्टर जैसे सूक्ष्मजीवों का उपयोग कहाँ किया जाता है?</p>",
                    options_en: ["<p>nitrogen cycling</p>", "<p>carbon cycling</p>", 
                                "<p>water cycling</p>", "<p>sulphur cycling</p>"],
                    options_hi: ["<p>नाइट्रोजन चक्र</p>", "<p>कार्बन चक्र</p>",
                                "<p>जल चक्र</p>", "<p>सल्फर चक्र</p>"],
                    solution_en: "<p>39.(a) <strong>nitrogen cycling.</strong> Nitrogen cycle is the biogeochemical cycle that describes the transformation of nitrogen and nitrogen-containing compounds in nature. Atmospheric nitrogen is the biggest source of nitrogen. Green plants absorb nitrogen in the form of nitrates and nitrites from the soil and water. Animals get nitrogen when they feed upon plants. Carbon cycle - In which carbon is exchanged between soil, water and atmosphere (air) of the earth. Sulfur cycle - The movement of sulfur through the atmosphere, mineral forms, and through living things. Water cycle - The continuous movement of water from the earth to the atmosphere.</p>",
                    solution_hi: "<p>39.(a) <strong>नाइट्रोजन चक्र।</strong> नाइट्रोजन चक्र जैव-रासायनिक चक्र है जो प्रकृति में नाइट्रोजन और नाइट्रोजन युक्त यौगिकों के परिवर्तन का वर्णन करता है। वायुमंडलीय नाइट्रोजन, नाइट्रोजन का सबसे बड़ा स्रोत है। हरे पौधे मिट्टी और जल से नाइट्रेट और नाइट्राइट के रूप में नाइट्रोजन को अवशोषित करते हैं। जानवर पौधों को खा करके नाइट्रोजन प्राप्त करते है। कार्बन चक्र - जिसमें मृदा, जल और पृथ्वी के वायुमंडल (वायु) के बीच कार्बन का आदान-प्रदान होता है। सल्फर चक्र - वायुमंडल, खनिज रूपों और जीवित चीजों के माध्यम से सल्फर की संचलन। जल चक्र - पृथ्वी से वायुमंडल में जल का निरंतर संचलन।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which team won the inaugural Women\'s Hockey India League title?</p>",
                    question_hi: "<p>40. पहली महिला हॉकी इंडिया लीग का खिताब किस टीम ने जीता?</p>",
                    options_en: ["<p>Odisha Warriors</p>", "<p>Soorma Hockey Club</p>", 
                                "<p>Bengal Tigers</p>", "<p>Delhi SG Pipers</p>"],
                    options_hi: ["<p>ओडिशा वॉरियर्स</p>", "<p>सोर्मा हॉकी क्लब</p>",
                                "<p>बंगाल टाइगर्स</p>", "<p>दिल्ली एसजी पाइपर्स</p>"],
                    solution_en: "<p>40.(a) <strong>Odisha Warriors.</strong><br>Odisha Warriors won the inaugural Women\'s Hockey India League title. In the final match, they defeated Soorma Hockey Club with a score of 2-1. The league was held from January 12, 2025, to January 26, 2025, where the Odisha Warriors showcased exceptional performance to secure the championship.</p>",
                    solution_hi: "<p>40. (a)<strong> ओडिशा वॉरियर्स।</strong><br>ओडिशा वॉरियर्स ने पहली महिला हॉकी इंडिया लीग का खिताब जीता। फाइनल में उन्होंने सोर्मा हॉकी क्लब को 2-1 से हराया। यह लीग 12 जनवरी 2025 से 26 जनवरी 2025 तक आयोजित की गई थी, जिसमें ओडिशा वॉरियर्स ने अपने बेहतरीन प्रदर्शन से चैंपियनशिप जीती।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Which theory describes the collective effects of changes in Earth\'s movements on its climate over thousands of years?</p>",
                    question_hi: "<p>41. कौन सा सिद्धांत हजारों वर्षों में पृथ्वी की गति में परिवर्तन के कारण उसकी जलवायु पर पड़ने वाले सामूहिक प्रभावों का वर्णन करता है?</p>",
                    options_en: ["<p>Milankovitch theory</p>", "<p>Copernicus\' heliocentric theory</p>", 
                                "<p>Plate tectonics theory</p>", "<p>Continental drift theory</p>"],
                    options_hi: ["<p>मिलनकोविच सिद्धांत (Milankovitch theory)</p>", "<p>कॉपरनिकस का हेलियोसेंट्रिक सिद्धांत (Copernicus\' heliocentric theory)</p>",
                                "<p>प्लेट विवर्तनिकी सिद्धांत (Plate tectonics theory)</p>", "<p>महाद्वीपीय बहाव सिद्धांत (Continental drift theory)</p>"],
                    solution_en: "<p>41.(a) <strong>Milankovitch theory.</strong> Copernican heliocentrism, proposed by Copernicus in 1543, positions the Sun at the center, with Earth and other planets orbiting it. Plate tectonics is a scientific theory that explains how major landforms are created as a result of Earth\'s subterranean movements.</p>",
                    solution_hi: "<p>41.(a) <strong>मिलनकोविच सिद्धांत।</strong> कॉपरनिकस द्वारा 1543 में प्रस्तावित कॉपरनिकस हेलियोसेंट्रिज्म के अनुसार सूर्य केंद्र में होता है, जबकि पृथ्वी और अन्य ग्रह इसकी परिक्रमा करते हैं। प्लेट विवर्तनिकी एक वैज्ञानिक सिद्धांत है जो बताता है कि पृथ्वी की भूमिगत गतिविधियों के परिणामस्वरूप प्रमुख भू-आकृतियों का निर्माण किस प्रकार होता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Who among the following is conferred upon Padma Shri 2021 for his/her contribution to Panthi folk dance ?</p>",
                    question_hi: "<p>42. निम्नलिखित में से किसे पंथी लोक नृत्य में उनके योगदान के लिए पद्म श्री 2021 से सम्मानित किया गया है ?</p>",
                    options_en: ["<p>Satyaram Reang</p>", "<p>Radhe Devi</p>", 
                                "<p>Dulari Devi</p>", "<p>Radhe Shyam Barle</p>"],
                    options_hi: ["<p>सत्यराम रियांग</p>", "<p>राधे देवी</p>",
                                "<p>दुलारी देवी</p>", "<p>राधे श्याम बारले</p>"],
                    solution_en: "<p>42.(d) <strong>Radhe Shyam Barle </strong>is a Panthi folk dancer and artist. Panthi is a folk dance of the Satnami community of Chhattisgarh. The songs convey the spirit of renunciation of their gurus and teachings of Saints such as Kabir, Ravidas and Dadu. Satyaram Reang (Tripura) was an Indian folk Hojagiri dancer who received Padma Shri in 2021. Dulari Devi (Bihar) is an artist working in the Mithila art tradition and received Padma Shri in 2021.</p>",
                    solution_hi: "<p>42.(d) <strong>राधे श्याम बारले</strong> एक पंथी लोक नर्तक और कलाकार हैं। पंथी छत्तीसगढ़ के सतनामी समुदाय का लोक नृत्य हैं। गीत उनके गुरुओं के त्याग की भावना और कबीर, रविदास और दादू जैसे संतों की शिक्षाओं को व्यक्त करते हैं। सत्यराम रियांग (त्रिपुरा) एक भारतीय लोक होजागिरी नर्तक थे, जिन्हें 2021 में पद्म श्री से सम्मानित किया गया था। दुलारी देवी (बिहार) मिथिला कला परंपरा में कार्य करने वाली एक कलाकार हैं और उन्हें 2021 में पद्म श्री से सम्मानित किया गया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which of the following states is one of those states that have no Panchayati Raj Institution at all?</p>",
                    question_hi: "<p>43. निम्नलिखित में से कौन-सा राज्य उन राज्यों में से एक है जहां कोई पंचायती राज संस्था नहीं है?</p>",
                    options_en: ["<p>Assam</p>", "<p>Gujarat</p>", 
                                "<p>Nagaland</p>", "<p>Punjab</p>"],
                    options_hi: ["<p>असम</p>", "<p>गुजरात</p>",
                                "<p>नागालैंड</p>", "<p>पंजाब</p>"],
                    solution_en: "<p>43.(c) <strong>Nagaland.</strong> Panchayats (Part IX, Articles 243 to 243-O): It is the system of local self-government of villages in rural India. Rajasthan was the first state to implement Panchayati Raj in India (on October 2nd, 1959, in Nagaur district), followed by Andhra Pradesh later in 1959. 24th April - National Panchayati Raj Day. Currently, the Panchayati Raj system exists in all states except Nagaland, Meghalaya, and Mizoram, and in all Union Territories except Delhi.</p>",
                    solution_hi: "<p>43.(c) <strong>नागालैंड।</strong> पंचायत (भाग IX, अनुच्छेद 243 से 243-O): यह ग्रामीण भारत में गांवों की स्थानीय स्वशासन की प्रणाली है। राजस्थान भारत में पंचायती राज को लागू करने वाला पहला राज्य था (2 अक्टूबर, 1959 को नागौर जिले में), उसके बाद 1959 में आंध्र प्रदेश ने लागू हुआ। 24 अप्रैल - राष्ट्रीय पंचायती राज दिवस मनाया जाता है। वर्तमान में, पंचायती राज प्रणाली नागालैंड, मेघालय और मिजोरम को छोड़कर सभी राज्यों में, और दिल्ली को छोड़कर सभी केंद्र शासित प्रदेशों में मौजूद है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following rivers marks the eastern-most boundary of the Himalayas ?</p>",
                    question_hi: "<p>44. निम्नलिखित में से कौन-सी नदी हिमालय की सबसे पूर्वी सीमा बनाती है?</p>",
                    options_en: ["<p>Kali</p>", "<p>Teesta</p>", 
                                "<p>Satluj</p>", "<p>Brahmaputra</p>"],
                    options_hi: ["<p>काली</p>", "<p>तीस्ता</p>",
                                "<p>सतलुज</p>", "<p>ब्रह्मपुत्र</p>"],
                    solution_en: "<p>44.(d) <strong>Brahmaputra River</strong> flows through the northeastern part of India, originating from the Angsi Glacier in Tibet and passing through Arunachal Pradesh before entering Assam. The Teesta River rises in the Pauhunri Mountain of the eastern Himalayas and flows through the Indian states of Sikkim and West Bengal. The Satluj River originates in Raksas Tal near Mansarovar in Tibet. The Kali River flows through the Uttara Kannada district of Karnataka.</p>",
                    solution_hi: "<p>44.(d) <strong>ब्रह्मपुत्र नदी</strong> का उद्गम भारत के पूर्वोत्तर भाग से होता है, जो तिब्बत के अंगसी ग्लेशियर से निकलती है और असम में प्रवेश करने से पहले अरुणाचल प्रदेश से होकर गुजरती है। तीस्ता नदी का उद्गम पूर्वी हिमालय के पौहुनरी पर्वत से होता है और सिक्किम एवं पश्चिम बंगाल के भारतीय राज्यों से होकर प्रवाहित होती है। सतलुज नदी का उद्गम तिब्बत में मानसरोवर के निकट राक्षस ताल से होता है। काली नदी का उद्गम कर्नाटक के उत्तर कन्नड़ जिले से होता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Which of the following is NOT a poverty alleviation programme in India?</p>",
                    question_hi: "<p>45. निम्नलिखित में से कौन-सा भारत में गरीबी उन्मूलन कार्यक्रम (poverty alleviation programme) नहीं है?</p>",
                    options_en: ["<p>Rural Housing - Indira Awaas Yojana (IAY)</p>", "<p>Namami Gange</p>", 
                                "<p>Rural Employment Generation Programme (REGP)</p>", "<p>Sampoorna Grameen Rozgar Yojana (SGRY)</p>"],
                    options_hi: ["<p>ग्रामीण आवास- इंदिरा आवास योजना (IAY)</p>", "<p>नमामि गंगे</p>",
                                "<p>ग्रामीण रोजगार सृजन कार्यक्रम (REGP)</p>", "<p>संपूर्ण ग्रामीण रोजगार योजना (SGRY)</p>"],
                    solution_en: "<p>45.(b) <strong>Namami Gange.</strong> India has implemented several poverty alleviation programs aimed at reducing poverty and improving living standards. Poverty alleviation programme in India: Mahatma Gandhi National Rural Employment Guarantee Act (2005), Pradhan Mantri Awas Yojana (2015), National Rural Livelihood Mission (2011), Public Distribution System (PDS), Pradhan Mantri Garib Kalyan Yojana (2016).</p>",
                    solution_hi: "<p>45.(b) <strong>नमामि गंगे।</strong> भारत ने गरीबी कम करने और जीवन स्तर में सुधार लाने के उद्देश्य से कई गरीबी उन्मूलन कार्यक्रम लागू किए हैं। भारत में गरीबी उन्मूलन कार्यक्रम: महात्मा गांधी राष्ट्रीय ग्रामीण रोजगार गारंटी अधिनियम (2005), प्रधानमंत्री आवास योजना (2015), राष्ट्रीय ग्रामीण आजीविका मिशन (2011), सार्वजनिक वितरण प्रणाली (PDS), प्रधानमंत्री गरीब कल्याण योजना (2016)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who has been honored with the Maharaja Hari Singh Award?</p>",
                    question_hi: "<p>46. किसे महाराजा हरि सिंह पुरस्कार से सम्मानित किया गया है ?</p>",
                    options_en: ["<p>Lieutenant Governor Manoj Sinha</p>", "<p>Pandit Prem Nath Dogra</p>", 
                                "<p>Pandit Girdhari Lal Dogra</p>", "<p>Shri Devender Singh Rana</p>"],
                    options_hi: ["<p>उपराज्यपाल मनोज सिन्हा</p>", "<p>पंडित प्रेम नाथ डोगरा</p>",
                                "<p>पंडित गिरधारी लाल डोगरा</p>", "<p>श्री देवेंद्र सिंह राणा</p>"],
                    solution_en: "<p>46.(a) <strong>Lieutenant Governor Manoj Sinha.</strong> Lieutenant Governor Manoj Sinha received the Maharaja Hari Singh Award at a ceremony in Jammu, organized by the Gulistan News Network as part of the Maharaja Hari Singh Peace and Harmony Award 2024-25. The award also recognized Pandit Prem Nath Dogra, Pandit Girdhari Lal Dogra, and Shri Devender Singh Rana for their contributions to public welfare.</p>",
                    solution_hi: "<p>46.(a)<strong> उपराज्यपाल मनोज सिन्हा. </strong>उपराज्यपाल मनोज सिन्हा ने महाराजा हरि सिंह पुरस्कार प्राप्त किया। यह सम्मान गुलिस्तां न्यूज़ नेटवर्क द्वारा महाराजा हरि सिंह शांति और सद्भावना पुरस्कार 2024-25 के तहत जम्मू में आयोजित एक समारोह में प्रदान किया गया। इस पुरस्कार के तहत जनकल्याण में योगदान के लिए पंडित प्रेम नाथ डोगरा, पंडित गिरधारी लाल डोगरा और श्री देवेंद्र सिंह राणा को भी सम्मानित किया गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. What is the duration of Pradhan Mantri Matsya Sampada Yojana?</p>",
                    question_hi: "<p>47. प्रधानमंत्री मत्स्य संपदा योजना की अवधि क्या है?</p>",
                    options_en: ["<p>7 years</p>", "<p>8 years</p>", 
                                "<p>6 years</p>", "<p>5 years</p>"],
                    options_hi: ["<p>7 वर्ष</p>", "<p>8 वर्ष</p>",
                                "<p>6 वर्ष</p>", "<p>5 वर्ष</p>"],
                    solution_en: "<p>47.(d) <strong>5 years.</strong> The Pradhan Mantri Matsya Sampada Yojana (PMMSY) was launched in 2020, by the Department of Fisheries, Ministry of Fisheries, Animal Husbandry, and Dairying. It aims to enhance fish production to 220 lakh metric tons by 2024-25 from 137.58 lakh metric tons in 2018-19 at an average annual growth rate of about 9%.</p>",
                    solution_hi: "<p>47.(d) <strong>5 वर्ष। </strong>मत्स्य पालन विभाग, मत्स्य पालन, पशुपालन और डेयरी मंत्रालय द्वारा 2020 में प्रधानमंत्री मत्स्य संपदा योजना (PMMSY) शुरू की गई थी। इसका लक्ष्य 2018-19 में 137.58 लाख मीट्रिक टन से 2024-25 तक मछली उत्पादन को लगभग 9% की औसत वार्षिक वृद्धि दर से बढ़ाकर 220 लाख मीट्रिक टन करना है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which fundamental duty applies on you when you see a few school students, on their educational trip to Humayun&rsquo;s tomb, writing their names on the walls?</p>",
                    question_hi: "<p>48. जब आप कुछ स्कूली विद्यार्थियों को \'हुमायूं के मकबरे\' की शैक्षणिक यात्रा के दौरान दीवारों पर अपना नाम लिखते हुए देखते हैं, तो आप पर कौन-सा मौलिक कर्तव्य लागू होता है?</p>",
                    options_en: ["<p>To safeguard public property</p>", "<p>To defend the country and render national service</p>", 
                                "<p>To uphold and protect the sovereignty, unity and integrity of India</p>", "<p>To protect and improve the natural environment</p>"],
                    options_hi: ["<p>सार्वजनिक संपत्ति की सुरक्षा करना</p>", "<p>देश की रक्षा करना और राष्ट्रीय सेवा प्रदान करना</p>",
                                "<p>भारत की संप्रभुता, एकता और अखंडता को बनाए रखना और उसकी रक्षा करना</p>", "<p>प्राकृतिक पर्यावरण की रक्षा करना और उसका संवर्द्धन करना</p>"],
                    solution_en: "<p>48.(a) <strong>To safeguard public property.</strong> Fundamental Duties are mentioned in Part IV-A (Article 51A) of the Indian Constitution. Article 51A &mdash;It shall be the duty of every citizen of India&mdash;(a) to abide by the Constitution and respect its ideals and institutions, the National Flag and the National Anthem; (b) to cherish and follow the noble ideals which inspired our national struggle for freedom; (c) to uphold and protect the sovereignty, unity and integrity of India;(d) to defend the country and render national service when called upon to do so.</p>",
                    solution_hi: "<p>48.(a) <strong>सार्वजनिक संपत्ति की सुरक्षा करना।</strong> भारतीय संविधान के भाग IV-A (अनुच्छेद 51A) में मौलिक कर्तव्यों का उल्लेख किया गया है। अनुच्छेद 51A - भारत के प्रत्येक नागरिक का कर्तव्य होगा - (a) संविधान का पालन करना तथा उसके आदर्शों एवं संस्थाओं, राष्ट्रीय ध्वज और राष्ट्रगान का सम्मान करना; (b) स्वतंत्रता के लिए हमारे राष्ट्रीय आंदोलन को प्रेरित करने वाले उच्च आदर्शों को संजोए रखना और उनका पालन करना; (c) भारत की संप्रभुता, एकता और अखंडता को बनाए रखना और उसकी रक्षा करना; (d) देश की रक्षा करना और आह्वान किए जाने पर राष्ट्रीय सेवा करना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. How many countries participated in the 2016 South Asian Games?</p>",
                    question_hi: "<p>49. 2016 के दक्षिण एशियाई खेलों में कितने देशों ने भाग लिया?</p>",
                    options_en: ["<p>Five</p>", "<p>Six</p>", 
                                "<p>Eight</p>", "<p>Seven</p>"],
                    options_hi: ["<p>पांच</p>", "<p>छह</p>",
                                "<p>आठ</p>", "<p>सात</p>"],
                    solution_en: "<p>49.(c) <strong>Eight.</strong> The 12th South Asian Games was hosted by India in Guwahati and Shillong. A total of 524 participants (387 athletes and 137 officials) competed across 23 sports. India topped the medal tally with 308 medals, followed by Sri Lanka with 186 medals, and Pakistan with 106 medals. The South Asian Games is a quadrennial multi-sport event held among athletes from South Asia. The South Asia Olympic Council, which was formed in 1983, governs it. The first edition of the South Asian Games was held in Kathmandu, Nepal.</p>",
                    solution_hi: "<p>49.(c) <strong>आठ।</strong> 12वें दक्षिण एशियाई खेलों की मेज़बानी भारत ने गुवाहाटी और शिलांग में की गई थी। कुल 524 प्रतिभागियों (387 एथलीट और 137 अधिकारी) ने 23 खेलों में भाग लिया था। भारत 308 पदकों के साथ पदक तालिका में शीर्ष स्थान पर रहा, उसके बाद श्रीलंका 186 पदकों के साथ दूसरे स्थान पर और पाकिस्तान 106 पदकों के साथ तीसरे स्थान पर रहा। दक्षिण एशियाई खेल दक्षिण एशिया के एथलीटों के बीच आयोजित होने वाला एक बहु-खेल आयोजन है। 1983 में गठित दक्षिण एशिया ओलंपिक परिषद इसका संचालन करती है। दक्षिण एशियाई खेलों का प्रथम संस्करण नेपाल के काठमांडू में आयोजित किया गया था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which of the following statements about the aims of National Mission for Sustainable Agriculture (NMSA) of India is/are correct?<br>1.To make agriculture more productive.<br>2.To make agriculture more sustainable.<br>3.To promote organic farming.</p>",
                    question_hi: "<p>50. भारत के राष्ट्रीय सतत कृषि मिशन (NMSA) के बारे में निम्नलिखित में से कौन सा/से कथन सही है/हैं?<br>1.कृषि को अधिक उत्पादक बनाना।<br>2.कृषि को अधिक सतत बनाना।<br>3.जैविक खेती को बढ़ावा देना।</p>",
                    options_en: ["<p>Only 1 is correct.</p>", "<p>Only 2 and 3 are correct.</p>", 
                                "<p>1, 2 and 3 are correct.</p>", "<p>Only 1 and 2 are correct.</p>"],
                    options_hi: ["<p>केवल 1 सही है।</p>", "<p>केवल 2 और 3 सही हैं।</p>",
                                "<p>1, 2 और 3 सही हैं।</p>", "<p>केवल 1 और 2 सही हैं।</p>"],
                    solution_en: "<p>50.(c) <strong>1, 2 and 3 are correct. </strong>National Mission for Sustainable Agriculture (NMSA) has been made operational from the year 2014-15 which aims at making agriculture more productive, sustainable, remunerative and climate resilient by promoting location specific integrated /composite farming systems; soil and moisture conservation measures; comprehensive soil health management; eﬃcient water management practices and mainstreaming rainfed technologies.</p>",
                    solution_hi: "<p>50.(c) <strong>1, 2 और 3 सही हैं।</strong> राष्ट्रीय सतत कृषि मिशन (NMSA) को वर्ष 2014-15 से कार्यान्वित किया गया है, जिसका उद्देश्य स्थान-विशिष्ट एकीकृत/संयुक्त कृषि प्रणालियों को बढ़ावा देकर, मृदा एवं नमी संरक्षण उपायों, व्यापक मृदा स्वास्थ्य प्रबंधन, कुशल जल प्रबंधन प्रथाओं एवं वर्षा आधारित प्रौद्योगिकियों को मुख्यधारा में लाकर कृषि को अधिक उत्पादक, टिकाऊ, लाभकारी और जलवायु अनुकूल बनाना है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Find the value of [9 + 3 -2 <math display=\"inline\"><mo>&#215;</mo></math> {(4 &divide;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>) -4 } + 81 &divide; 9 + 81 &divide; 9 + 3 - 9] + 9 .</p>",
                    question_hi: "<p>51. [9 + 3 -2 <math display=\"inline\"><mo>&#215;</mo></math> {(4 &divide;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>) -4 } + 81 &divide; 9 + 81 &divide; 9 + 3 - 9] + 9 का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>0</p>", "<p>6</p>", 
                                "<p>9</p>", "<p>3</p>"],
                    options_hi: ["<p>0</p>", "<p>6</p>",
                                "<p>9</p>", "<p>3</p>"],
                    solution_en: "<p>51.(c)<br>[9 + 3 -2 <math display=\"inline\"><mo>&#215;</mo></math> {(4 &divide;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>) - 4 } + 81 &divide; 9 + 81 &divide; 9 + 3 - 9] + 9 <br>[9 + 3 -2 <math display=\"inline\"><mo>&#215;</mo></math> {(16) - 4 } + 9 + 9 + 3 - 9] + 9 <br>[12 - 2 <math display=\"inline\"><mo>&#215;</mo></math> {12} + 12] + 9 <br>[12 - 24 + 12] + 9 = 9</p>",
                    solution_hi: "<p>51.(c)<br>[9 + 3 -2 <math display=\"inline\"><mo>&#215;</mo></math> {(4 &divide;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math>) - 4 } + 81 &divide; 9 + 81 &divide; 9 + 3 - 9] + 9&nbsp;<br>[9 + 3 -2 <math display=\"inline\"><mo>&#215;</mo></math> {(16) - 4 } + 9 + 9 + 3 - 9] + 9 <br>[12 - 2 <math display=\"inline\"><mo>&#215;</mo></math> {12} + 12] + 9 <br>[12 - 24 + 12] + 9 = 9</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. A person reaches his destination 30 minutes late, if his speed is 3 km/h, and reaches 30 minutes before time if his speed is 4 km/h. Find the distance of his destination from his starting point.</p>",
                    question_hi: "<p>52. एक व्यक्ति 3 km/h की चाल से चलने पर अपने गंतव्य पर 30 मिनट देरी से पहुंचता है, और यदि उसकी चाल 4 km/h हो जाती है, तो वह समय से 30 मिनट पहले पहुंच जाता है। उसके आरंभिक बिंदु से उसके गंतव्य की दूरी ज्ञात कीजिए।</p>",
                    options_en: ["<p>11 km</p>", "<p>14 km</p>", 
                                "<p>12 km</p>", "<p>13 km</p>"],
                    options_hi: ["<p>11 km</p>", "<p>14 km</p>",
                                "<p>12 km</p>", "<p>13 km</p>"],
                    solution_en: "<p>52.(c)<br>Difference in time = 60 minute = 1 hour<br><math display=\"inline\"><mfrac><mrow><mi>D</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>D</mi><mn>4</mn></mfrac></math>= 1<br>4D&nbsp;- 3D = 12<br>D = 12 km</p>",
                    solution_hi: "<p>52.(c)<br>समय में अंतर = 60 मिनट = 1 घंटा<br><math display=\"inline\"><mfrac><mrow><mi>D</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>D</mi><mn>4</mn></mfrac></math>= 1<br>4D&nbsp;- 3D = 12<br>D = 12 km</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The inner and outer radii of a circular track are 25 m and 28 m, respectively. The cost (in ₹) of levelling the track at ₹28 per m&sup2; is:<br>(Use 𝜋 = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    question_hi: "<p>53. एक वृत्ताकार पथ की आंतरिक और बाहरी त्रिज्याएँ क्रमशः 25 m और 28 m हैं। ₹28 प्रति m&sup2; की दर से पथ को समतल करने की लागत (₹ में) कितनी है? [𝜋 = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> का उपयोग कीजिए ]</p>",
                    options_en: ["<p>15,421</p>", "<p>13,992</p>", 
                                "<p>11,229</p>", "<p>17,355</p>"],
                    options_hi: ["<p>15,421</p>", "<p>13,992</p>",
                                "<p>11,229</p>", "<p>17,355</p>"],
                    solution_en: "<p>53.(b) <br>Area of circular track = <math display=\"inline\"><mi>&#960;</mi><mo>(</mo><msubsup><mrow><mi>R</mi></mrow><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msubsup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msubsup><mrow><mi>R</mi></mrow><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msubsup><mo>)</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math> &pi;[(28)<sup>2</sup> - (25)<sup>2</sup>] = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>[784 - 625]<br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>[</mo><mn>159</mn><mo>]</mo></math><br>Now, cost of levelling the track = 28 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>[</mo><mn>159</mn><mo>]</mo></math> = 4 &times; 22 &times; 159 = 13992</p>",
                    solution_hi: "<p>53.(b) <br>वृत्ताकार ट्रैक का क्षेत्रफल = <math display=\"inline\"><mi>&#960;</mi><mo>(</mo><msubsup><mrow><mi>R</mi></mrow><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msubsup><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><msubsup><mrow><mi>R</mi></mrow><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></msubsup><mo>)</mo></math><br><math display=\"inline\"><mo>&#8658;</mo></math> &pi;[(28)<sup>2</sup> - (25)<sup>2</sup>] = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>[784 - 625]<br>= <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>[</mo><mn>159</mn><mo>]</mo></math><br>अब, ट्रैक को समतल करने की लागत = 28 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac><mo>[</mo><mn>159</mn><mo>]</mo></math> = 4 &times; 22 &times; 159 = 13992</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. A man can row a distance of 6 km in 1 hour in still water. He can row the same distance in 45 minutes with the current. Find the total time taken by him to row a distance of 16 km with the current and to return to the starting point.</p>",
                    question_hi: "<p>54. एक व्यक्ति स्थिर जल में 6 km की दूरी 1 घंटे में तय कर सकता है। उतनी ही दूरी को वह धारा के अनुकूल 45 मिनट में तय कर सकता है। धारा के अनुकूल 16 km की दूरी तय करने और प्रारंभिक बिंदु पर लौटने के लिए उसके द्वारा लिया गया कुल समय ज्ञात कीजिए।</p>",
                    options_en: ["<p>4 hours and 40 minutes</p>", "<p>6 hours</p>", 
                                "<p>4 hours</p>", "<p>6 hours and 40 minutes</p>"],
                    options_hi: ["<p>4 घंटे and 40 मिनट</p>", "<p>6 घंटे</p>",
                                "<p>4 घंटे</p>", "<p>6 घंटे and 40 मिनट</p>"],
                    solution_en: "<p>54.(b)<br>Speed of man in still water (<math display=\"inline\"><mi>x</mi></math>) = 6 km /h <br>Downstream speed = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>45</mn><mi>&#160;</mi><mo>&#215;</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>60</mn></mrow></mfrac></mrow></mfrac></math> = 8 km / h<br>Speed of current (<math display=\"inline\"><mi>y</mi></math>) = 8 - 6 = 2 km/h<br>Time taken to row and return to starting point = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>8</mn></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>4</mn></mfrac></math>= 2 + 4 = 6 hours</p>",
                    solution_hi: "<p>54.(b)<br>शांत जल में मनुष्य की गति (<math display=\"inline\"><mi>x</mi></math>) = 6 km /h <br>धारा के अनुकूल गति = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>45</mn><mi>&#160;</mi><mo>&#215;</mo><mfrac><mrow><mn>1</mn></mrow><mrow><mn>60</mn></mrow></mfrac></mrow></mfrac></math> = 8 km / h<br>धारा की गति (<math display=\"inline\"><mi>y</mi></math>) = 8 - 6 = 2 km/h<br>शुरुआती बिंदु से जाने और वापस लौटने में लगने वाला समय =<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mi>x</mi><mo>+</mo><mi>y</mi></mrow></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mrow><mi>x</mi><mo>-</mo><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>8</mn></mfrac></math>+ <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>16</mn><mn>4</mn></mfrac></math>&nbsp;= 2 + 4 = 6 घंटे</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Successive division of a number by 2, 3, 5 and 7 gives remainders 1,4,0 and 5,&nbsp;respectively. What will be the sum of the remainders if the same number is divided by&nbsp;7, 5, 3 and 2 successively ?</p>",
                    question_hi: "<p>55. किसी संख्या को 2, 3, 5 और 7 से क्रमिक रूप से भाग देने पर क्रमशः 1, 4, 0 और 5 शेषफल प्राप्त&nbsp;होता है। यदि उसी संख्या को क्रमिक रूप से 7, 5, 3 और 2 से भाग दिया जाए तो शेषफलों का योग क्या&nbsp;होगा ?</p>",
                    options_en: ["<p>17</p>", "<p>8</p>", 
                                "<p>10</p>", "<p>9</p>"],
                    options_hi: ["<p>17</p>", "<p>8</p>",
                                "<p>10</p>", "<p>9</p>"],
                    solution_en: "<p>55.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020429.png\" alt=\"rId42\" width=\"276\" height=\"110\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020534.png\" alt=\"rId43\" width=\"302\" height=\"79\"><br>Hence, sum of remainder = 5 + 2 + 1 + 1 = 9</p>",
                    solution_hi: "<p>55.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020429.png\" alt=\"rId42\" width=\"276\" height=\"110\"><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020534.png\" alt=\"rId43\" width=\"302\" height=\"79\"><br>अतः, शेषफल का योग = 5 + 2 + 1 + 1 = 9</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. In a state election between two parties, 83% of the voters cast their votes, out of which 4% of the votes were declared invalid. Party A got 156611 votes which were 83% of the total valid votes. Find the total number of votes enrolled in that election. (Consider integer part only)</p>",
                    question_hi: "<p>56. दो दलों के बीच एक राज्य के चुनाव में, 83% मतदाताओं ने अपने मत डाले, जिनमें से 4% मतों को अवैध घोषित कर दिया गया। दल A को 156611 मत मिले जो कुल वैध मतों का 83% था। उस चुनाव में नामांकित मतों की कुल संख्या ज्ञात कीजिए। (केवल पूर्णांक भाग पर विचार कीजिए)</p>",
                    options_en: ["<p>23,46,807</p>", "<p>2,36,807</p>", 
                                "<p>2,34,888</p>", "<p>2,58,233</p>"],
                    options_hi: ["<p>23,46,807</p>", "<p>2,36,807</p>",
                                "<p>2,34,888</p>", "<p>2,58,233</p>"],
                    solution_en: "<p>56.(b)<br>Total number of enrolled votes = 100<math display=\"inline\"><mi>x</mi></math><br>Number of cast votes = 100<math display=\"inline\"><mi>x</mi></math> &times; 83% = 83x<br>Total number of valid votes = 83<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math><br>Number of votes got by party A = 83<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>83</mn><mn>100</mn></mfrac></math><br>83<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>83</mn><mn>100</mn></mfrac></math>= 156611<br>Number of enrolled votes (100<math display=\"inline\"><mi>x</mi></math>) =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>156611</mn><mo>&#215;</mo><mn>100</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>83</mn><mo>&#215;</mo><mn>96</mn><mo>&#215;</mo><mn>83</mn></mrow></mfrac></math> = 2,36,807 (approx)</p>",
                    solution_hi: "<p>56.(b)<br>नामांकित वोटों की कुल संख्या = 100<math display=\"inline\"><mi>x</mi></math><br>डाले गए वोटों की संख्या = 100<math display=\"inline\"><mi>x</mi></math> &times; 83% = 83x<br>वैध मतों की कुल संख्या = 83<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math><br>पार्टी A को मिले वोटों की संख्या = 83<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>83</mn><mn>100</mn></mfrac></math><br>83<math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>96</mn><mn>100</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>83</mn><mn>100</mn></mfrac></math>= 156611<br>नामांकित मतों की संख्या (100<math display=\"inline\"><mi>x</mi></math>) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>156611</mn><mo>&#215;</mo><mn>100</mn><mo>&#215;</mo><mn>100</mn></mrow><mrow><mn>83</mn><mo>&#215;</mo><mn>96</mn><mo>&#215;</mo><mn>83</mn></mrow></mfrac></math> = 2,36,807 (लगभग)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. If cotA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mi>&#160;</mi><mn>12</mn></mrow></mfrac></math>, then find the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></msqrt><msqrt><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></msqrt></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>17</mn><mn>7</mn></mfrac></msqrt></math>.</p>",
                    question_hi: "<p>57. यदि cotA = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mi>&#160;</mi><mn>12</mn></mrow></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></msqrt><msqrt><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></msqrt></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>17</mn><mn>7</mn></mfrac></msqrt></math>&nbsp;का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mi>&#160;</mi><msqrt><mn>119</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mi>&#160;</mi><msqrt><mn>119</mn></msqrt></mrow></mfrac></math></p>", 
                                "<p>0</p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mi>&#160;</mi><msqrt><mn>119</mn></msqrt></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mi>&#160;</mi><msqrt><mn>119</mn></msqrt></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mi>&#160;</mi><msqrt><mn>119</mn></msqrt></mrow></mfrac></math></p>",
                                "<p>0</p>", "<p>- <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mi>&#160;</mi><msqrt><mn>119</mn></msqrt></mrow></mfrac></math></p>"],
                    solution_en: "<p>57.(d) <br>cotA = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>12</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>b</mi><mi>p</mi></mfrac></math><br>h = <math display=\"inline\"><msqrt><msup><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math><br>h = 13<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></msqrt><msqrt><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></msqrt></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>17</mn><mn>7</mn></mfrac></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mfrac><mn>13</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>13</mn><mn>12</mn></mfrac></msqrt><msqrt><mfrac><mn>13</mn><mn>5</mn></mfrac><mo>+</mo><mfrac><mn>13</mn><mn>12</mn></mfrac></msqrt></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>17</mn><mn>7</mn></mfrac></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mn>156</mn><mo>-</mo><mn>65</mn></mrow><mn>60</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mrow><mn>156</mn><mo>+</mo><mn>65</mn></mrow><mn>60</mn></mfrac><mo>&#160;</mo></mstyle></mfrac></msqrt></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>17</mn><mn>7</mn></mfrac></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>91</mn><mn>221</mn></mfrac></msqrt></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>17</mn><mn>7</mn></mfrac></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>13</mn><mo>&#215;</mo><mn>7</mn></msqrt><msqrt><mn>13</mn><mo>&#215;</mo><mn>17</mn></msqrt></mfrac><mo>-</mo><mfrac><msqrt><mn>17</mn></msqrt><msqrt><mn>7</mn></msqrt></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>7</mn></msqrt><msqrt><mn>17</mn></msqrt></mfrac><mo>-</mo><mfrac><msqrt><mn>17</mn></msqrt><msqrt><mn>7</mn></msqrt></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><msqrt><mn>119</mn></msqrt></mfrac></math></p>",
                    solution_hi: "<p>57.(d) <br>cot A = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mi>&#160;</mi><mn>12</mn></mrow></mfrac></math> =&nbsp;<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#2310;&#2343;&#2366;&#2352;</mi><mi>&#2354;&#2306;&#2348;</mi></mfrac></math><br>कर्ण = <math display=\"inline\"><msqrt><msup><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></msup><mo>+</mo><msup><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></msup></msqrt></math> = 13<br>प्रश्न के अनुसार,</p>\n<p><math display=\"inline\"><mo>&#8658;</mo></math><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>-</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></msqrt><msqrt><mi>s</mi><mi>e</mi><mi>c</mi><mi>A</mi><mo>+</mo><mi>cos</mi><mi>e</mi><mi>c</mi><mi>A</mi></msqrt></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>17</mn><mn>7</mn></mfrac></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mfrac><mn>13</mn><mn>5</mn></mfrac><mo>-</mo><mfrac><mn>13</mn><mn>12</mn></mfrac></msqrt><msqrt><mfrac><mn>13</mn><mn>5</mn></mfrac><mo>+</mo><mfrac><mn>13</mn><mn>12</mn></mfrac></msqrt></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>17</mn><mn>7</mn></mfrac></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mstyle displaystyle=\"true\"><mfrac><mrow><mn>156</mn><mo>-</mo><mn>65</mn></mrow><mn>60</mn></mfrac></mstyle><mstyle displaystyle=\"true\"><mfrac><mrow><mn>156</mn><mo>+</mo><mn>65</mn></mrow><mn>60</mn></mfrac><mo>&#160;</mo></mstyle></mfrac></msqrt></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>17</mn><mn>7</mn></mfrac></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>91</mn><mn>221</mn></mfrac></msqrt></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mn>17</mn><mn>7</mn></mfrac></msqrt></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>13</mn><mo>&#215;</mo><mn>7</mn></msqrt><msqrt><mn>13</mn><mo>&#215;</mo><mn>17</mn></msqrt></mfrac><mo>-</mo><mfrac><msqrt><mn>17</mn></msqrt><msqrt><mn>7</mn></msqrt></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>7</mn></msqrt><msqrt><mn>17</mn></msqrt></mfrac><mo>-</mo><mfrac><msqrt><mn>17</mn></msqrt><msqrt><mn>7</mn></msqrt></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><msqrt><mn>119</mn></msqrt></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A sells an object to B at 10% profit, B sells it to C at 60% profit and C sells it to D at 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% profit. If D paid ₹59.40, then at what price did A buy it?</p>",
                    question_hi: "<p>58. A, B को एक वस्तु 10% लाभ पर बेचता है, B उसी वस्तु को C को 60% लाभ पर बेचता है और C उसी वस्तु को D को 12<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math>% लाभ पर बेचता है। यदि D ने उस वस्तु के लिए ₹59.40 का भुगतान किया, तो A ने उसे किस मूल्य पर खरीदा?</p>",
                    options_en: ["<p>₹30</p>", "<p>₹32</p>", 
                                "<p>₹34</p>", "<p>₹28</p>"],
                    options_hi: ["<p>₹30</p>", "<p>₹32</p>",
                                "<p>₹34</p>", "<p>₹28</p>"],
                    solution_en: "<p>58.(a)<br>Let &lsquo;A&rsquo; buy the object at Rs. <math display=\"inline\"><mi>x</mi></math><br>According to the question,<br><math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>5</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math>= 59.40<br><math display=\"inline\"><mi>x</mi></math> = 59.40 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>11</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>9</mn></mfrac></math>= Rs. 30</p>",
                    solution_hi: "<p>58.(a)<br>मान लीजिए \'A\' वस्तु को <math display=\"inline\"><mi>x</mi></math> रुपये में खरीदता है। <br>प्रश्न के अनुसार,<br><math display=\"inline\"><mi>x</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>5</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>8</mn></mfrac></math>= 59.40<br><math display=\"inline\"><mi>x</mi></math> = 59.40 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>11</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>8</mn></mfrac></math>&times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8</mn><mn>9</mn></mfrac></math>= रु. 30</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. If a 9-digit number 389x6378y is divisible by 72, then the value of 6x + 7y is:</p>",
                    question_hi: "<p>59. यदि एक 9 अंकों की संख्या 389x6378y, 72 से विभाज्य है, तो 6x + 7y का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>64</p>", "<p>32</p>", 
                                "<p>16</p>", "<p>28</p>"],
                    options_hi: ["<p>64</p>", "<p>32</p>",
                                "<p>16</p>", "<p>28</p>"],
                    solution_en: "<p>59.(a)<br><strong>72 = 9 &times; 8</strong><br>For 389x6378y to be divisible by 8, its last 3 digit no i.e.78y should be divisible by 8.<br>For this , y = 4<br>Now, For 389x6378<strong>4</strong> to be divisible by 9, the sum of its digits i.e. 3 + 8 + 9 + x + 6 + 3 + 7 + 8 + 4 = 48+x, should be divisible by 9.<br>For this, we have x = 6<br>Hence, 6x + 7y = 6 &times; 6 + 7 &times; 4 = 36 + 28 = 64</p>",
                    solution_hi: "<p>59.(a)<br><strong>72 = 9 &times; 8</strong><br>389x6378y को 8 से विभाज्य होने के लिए, इसकी अंतिम 3 अंक संख्या यानी 78y को 8 से विभाज्य होना चाहिए।<br>इसके लिए, y = 4<br>अब, 389x6378<strong>4</strong> को 9 से विभाज्य होने के लिए, इसके अंकों का योग, यानी 3 + 8 + 9 + x + 6 + 3 + 7 + 8 + 4 = 48+x, 9 से विभाज्य होना चाहिए।<br>इसके लिए, हमारे पास x = 6 है<br>अतः, 6x + 7y = 6 &times; 6 + 7 &times; 4 = 36 + 28 = 64</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. To clear a stock of items, a seller gives an 8% discount on the marked price. He spends ₹<math display=\"inline\"><mi>x</mi></math> on the promotion of the discount offer. His total cost of the items is ₹2,50,000 and the marked price is 10% more than the cost price. Finally, he earns no profit or no loss. What is the value of x?</p>",
                    question_hi: "<p>60. वस्तुओं के स्टॉक को खाली करने के लिए एक विक्रेता अंकित मूल्य पर 8% की छूट देता है। वह ₹<math display=\"inline\"><mi>x</mi></math> इस छूट ऑफर के प्रचार पर खर्च करता है। उसकी वस्तुओं की कुल लागत ₹2,50,000 है और अंकित मूल्य लागत मूल्य से 10% अधिक है। अंत में उसे न तो लाभ और न ही हानि होती है। x का मान क्या है?</p>",
                    options_en: ["<p>6000</p>", "<p>4500</p>", 
                                "<p>3000</p>", "<p>7500</p>"],
                    options_hi: ["<p>6000</p>", "<p>4500</p>",
                                "<p>3000</p>", "<p>7500</p>"],
                    solution_en: "<p>60.(c) Let CP = 100% <br><math display=\"inline\"><mo>&#8658;</mo></math> MP = 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math>= 110%<br><math display=\"inline\"><mo>&#8658;</mo></math> SP = 110% &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>25</mn></mfrac></math>= 101.2%<br>According to the question,<br><math display=\"inline\"><mo>&#8658;</mo></math> 100% = 250000<br><math display=\"inline\"><mo>&#8658;</mo></math> 1% = 2500<br><math display=\"inline\"><mo>&#8658;</mo></math> 1.2% = 2500 &times; 1.2 = 3000<br>So, the value of <math display=\"inline\"><mi>x</mi></math> = ₹ 3000</p>",
                    solution_hi: "<p>60.(c) माना क्रय मूल्य = 100% <br><math display=\"inline\"><mo>&#8658;</mo></math> अंकित मूल्य = 100 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math>= 110%<br><math display=\"inline\"><mo>&#8658;</mo></math> विक्रय मूल्य = 110% &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>23</mn><mn>25</mn></mfrac></math>= 101.2%<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mo>&#8658;</mo></math> 100% = 250000<br><math display=\"inline\"><mo>&#8658;</mo></math> 1% = 2500<br><math display=\"inline\"><mo>&#8658;</mo></math> 1.2% = 2500 &times; 1.2 = 3000<br>तो, <math display=\"inline\"><mi>x</mi></math> का मान = ₹ 3000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The ratio between male and female members in a club is 2 : 3. If the number of male members is increased by 200, the ratio becomes 5 : 6. How many female members are there in the club?</p>",
                    question_hi: "<p>61. एक क्लब में पुरुष और महिला सदस्यों के बीच अनुपात 2:3 है। यदि पुरुष सदस्यों की संख्या 200 बढ़ा दी जाए, तो अनुपात 5:6 हो जाता है। क्लब में कितनी महिला सदस्य हैं?</p>",
                    options_en: ["<p>1200</p>", "<p>900</p>", 
                                "<p>1400</p>", "<p>1000</p>"],
                    options_hi: ["<p>1200</p>", "<p>900</p>",
                                "<p>1400</p>", "<p>1000</p>"],
                    solution_en: "<p>61.(a)<br>Let male = 2x<br>Female = 3x<br>New ratio<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>200</mn></mrow><mrow><mn>3</mn><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 4x + 400 = 5x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 400<br><math display=\"inline\"><mo>&#8658;</mo></math> Female = 3 &times; 400 = 1200</p>",
                    solution_hi: "<p>61.(a)<br>माना , पुरुष = 2x<br>स्त्री = 3x<br>नया अनुपात<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>200</mn></mrow><mrow><mn>3</mn><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>6</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> 4x + 400 = 5x<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 400<br><math display=\"inline\"><mo>&#8658;</mo></math> स्त्री = 3 &times; 400 = 1200</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. An electric pole casts a shadow of length 28 m at a time when a nearby tree 8 m high casts a shadow of length 14 m. Find the height of the pole.</p>",
                    question_hi: "<p>62. एक बिजली का खंभा 28 m लंबाई की छाया ऐसे समय में डालता है जब 8 m ऊंचे एक पेड़ की लंबाई 14 m की छाया होती है। खम्भे की ऊँचाई ज्ञात कीजिए ?</p>",
                    options_en: ["<p>16 m</p>", "<p>14 m</p>", 
                                "<p>20 m</p>", "<p>15 m</p>"],
                    options_hi: ["<p>16 m</p>", "<p>14 m</p>",
                                "<p>20 m</p>", "<p>15 m</p>"],
                    solution_en: "<p>62.(a)<br>The ratio of height and shadow for both the electric pole and tree should be the same.<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><mi>H</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>P</mi><mi>o</mi><mi>l</mi><mi>e</mi><mi>&#160;</mi></mrow><mrow><mi>L</mi><mi>e</mi><mi>n</mi><mi>g</mi><mi>t</mi><mi>h</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>s</mi><mi>h</mi><mi>a</mi><mi>d</mi><mi>o</mi><mi>w</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>p</mi><mi>o</mi><mi>l</mi><mi>e</mi><mi>&#160;</mi></mrow></mfrac><mo>=</mo><mfrac><mrow><mi>H</mi><mi>e</mi><mi>i</mi><mi>g</mi><mi>h</mi><mi>t</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>t</mi><mi>r</mi><mi>e</mi><mi>e</mi></mrow><mrow><mi>L</mi><mi>e</mi><mi>n</mi><mi>g</mi><mi>t</mi><mi>h</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>s</mi><mi>h</mi><mi>a</mi><mi>d</mi><mi>o</mi><mi>w</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>t</mi><mi>r</mi><mi>e</mi><mi>e</mi></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>h</mi><mn>28</mn></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>14</mn></mfrac></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mi>h</mi><mo>=</mo><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>28</mn></mrow><mn>14</mn></mfrac></math>= 16 m</p>",
                    solution_hi: "<p>62.(a)<br>बिजली के खंभे और पेड़ दोनों के लिए ऊंचाई और छाया का अनुपात समान होना चाहिए।<br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><mi>&#2326;&#2350;&#2381;&#2349;&#2375;</mi><mi>&#160;</mi><mi>&#2325;&#2368;</mi><mi>&#160;</mi><mi>&#2314;&#2305;&#2330;&#2366;&#2312;</mi></mrow><mrow><mi>&#2326;&#2350;&#2381;&#2349;&#2375;</mi><mi>&#160;</mi><mi>&#2325;&#2368;</mi><mi>&#160;</mi><mi>&#2331;&#2366;&#2351;&#2366;</mi><mi>&#160;</mi><mi>&#2325;&#2368;</mi><mi>&#160;</mi><mi>&#2354;&#2350;&#2381;&#2348;&#2366;&#2312;</mi></mrow></mfrac></math> =<math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mi>&#2357;&#2371;&#2325;&#2381;&#2359;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2314;&#2305;&#2330;&#2366;&#2312;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2357;&#2371;&#2325;&#2381;&#2359;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2331;&#2366;&#2351;&#2366;</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mi>&#2354;&#2350;&#2381;&#2348;&#2366;&#2312;</mi><mo>&#160;</mo></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>h</mi><mn>28</mn></mfrac><mo>=</mo><mfrac><mn>8</mn><mn>14</mn></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo><mi>h</mi><mo>=</mo><mfrac><mrow><mn>8</mn><mo>&#215;</mo><mn>28</mn></mrow><mrow><mn>14</mn></mrow></mfrac><mo>=</mo><mn>16</mn></math>मीटर</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If a sum of ₹5,000 is taken at a simple rate of interest of 15% per annum for 3 years and another sum of ₹8,000 is taken at a simple interest of 12% per annum for 4 years, the positive difference of the interests paid is:</p>",
                    question_hi: "<p>63. यदि ₹5,000 की राशि 3 वर्षों के लिए 15% वार्षिक साधारण ब्याज दर पर ली जाती है और ₹8,000 की अन्य राशि 4 वर्षों के लिए 12% वार्षिक साधारण ब्याज पर ली जाती है, तो भुगतान किए गए ब्याज का धनात्मक अंतर ज्ञात करें।</p>",
                    options_en: ["<p>₹1,643</p>", "<p>₹1,590</p>", 
                                "<p>₹1,286</p>", "<p>₹1,378</p>"],
                    options_hi: ["<p>₹1,643</p>", "<p>₹1,590</p>",
                                "<p>₹1,286</p>", "<p>₹1,378</p>"],
                    solution_en: "<p>63.(b) <br>Interest on 5000 for 3 years at 15% per annum<br><math display=\"inline\"><mo>&#8658;</mo></math> 5000 &times; 45% = 2250<br>Interest on 8000 for 4 years at 12% per annum<br><math display=\"inline\"><mo>&#8658;</mo></math> 8000 &times; 48% = 3840<br>Difference of interest paid = 3840 - 2250 = 1590</p>",
                    solution_hi: "<p>63.(b) <br>5000 पर 3 साल के लिए 15% प्रति वर्ष की दर से ब्याज<br>= 5000 &times; 45% = 2250<br>8000 पर 4 साल के लिए 12% प्रति वर्ष की दर से ब्याज = 8000 &times; 48% = 3840<br>भुगतान किये गये ब्याज का अंतर = 3840 - 2250 = 1590</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "64. The least number which is is exactly divisible by 125, 75, 150 and 70?",
                    question_hi: "64. वह न्यूनतम संख्या कौन-सी है, जो 125, 75, 150 तथा 70 से पूरी तरह विभाज्य हो?",
                    options_en: [" 5250", " 5480", 
                                " 5383 ", " 2494"],
                    options_hi: [" 5250", " 5480",
                                " 5383 ", " 2494"],
                    solution_en: "64.(a) L.C.M. of (125,75,150,70) = 5250",
                    solution_hi: "64.(a) (125,75,150,70) का L.C.M. = 5250",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. A vessel contains 18 litres mixture of milk and water in the ratio 5 : 1. If 3 litres of milk is added to the vessel, then how much water (in litres) should be added to the vessel to have milk and water in the ratio 9 : 2 ?</p>",
                    question_hi: "<p>65. एक बर्तन में 5 : 1 के अनुपात में दूध और पानी का 18 लीटर मिश्रण है। यदि बर्तन में 3 लीटर दूध मिलाया जाता है, तो बर्तन में कितना पानी (लीटर में) मिलाया जाना चाहिए ताकि दूध और पानी का अनुपात 9 : 2 हो जाए?</p>",
                    options_en: ["<p>2</p>", "<p>1.5</p>", 
                                "<p>0.5</p>", "<p>1</p>"],
                    options_hi: ["<p>2</p>", "<p>1.5</p>",
                                "<p>0.5</p>", "<p>1</p>"],
                    solution_en: "<p>65.(d)<br>The initial quantity of milk = 18 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>5</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 15 liters<br>New quantity of milk = 15 + 3 = 18 liter<br>Initial quantity of water = (18 - 15) = 3 liters. <br>Let the quantity of water added be <math display=\"inline\"><mi>x</mi></math> liters<br>New quantity of water = <math display=\"inline\"><mn>3</mn><mi>&#160;</mi></math>+ x <br>According to the question , <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mrow><mn>3</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math><br>36 = 27 + 9<math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mi>x</mi></math> = 1 liters</p>",
                    solution_hi: "<p>65.(d)<br>दूध की प्रारंभिक मात्रा = 18 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mrow><mn>5</mn><mo>+</mo><mn>1</mn></mrow></mfrac></math> = 15 लीटर<br>दूध की नई मात्रा = 15 + 3 = 18 लीटर<br>पानी की प्रारम्भिक मात्रा = (18 - 15) = 3 लीटर. <br>माना कि मिलाये गये पानी की मात्रा <math display=\"inline\"><mi>x</mi></math> लीटर है<br>पानी की नई मात्रा = <math display=\"inline\"><mn>3</mn><mi>&#160;</mi></math>+ x <br>प्रश्न के अनुसार, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mrow><mn>3</mn><mo>+</mo><mi>x</mi></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>2</mn></mfrac></math><br>36 = 27 + 9<math display=\"inline\"><mi>x</mi></math><br><math display=\"inline\"><mi>x</mi></math> = 1 लीटर</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. A clock is sold for ₹550 cash or in the instalment scheme, for ₹250 cash down payment and ₹310 after one month. Find the rate of interest charged in the installment scheme.</p>",
                    question_hi: "<p>66. एक घड़ी ₹550 नकद या किश्त योजना में ₹250 नकद डाउन पेमेंट और एक महीने बाद ₹310 में बेची जाती है। किश्त योजना में लिए जाने वाले ब्याज की दर ज्ञात कीजिए।</p>",
                    options_en: ["<p>35%</p>", "<p>45%</p>", 
                                "<p>40%</p>", "<p>20%</p>"],
                    options_hi: ["<p>35%</p>", "<p>45%</p>",
                                "<p>40%</p>", "<p>20%</p>"],
                    solution_en: "<p>66.(c) Interest = 550 - (250 + 310) = 10<br>Financed amount = 550 - 250 = 300<br>Annual rate of interest = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#215;</mo><mn>10</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>300</mn></mfrac></math> <br>= 40%</p>",
                    solution_hi: "<p>66.(c) ब्याज = 550 - (250 + 310) = 10<br>वित्तपोषित राशि = 550 - 250 = 300<br>वार्षिक ब्याज दर= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>&#215;</mo><mn>10</mn><mo>&#215;</mo><mn>12</mn></mrow><mn>300</mn></mfrac></math>&nbsp;= 40%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The maximum value of (2 sin &theta; + 3 cos &theta;) is:</p>",
                    question_hi: "<p>67. (2 sin &theta; + 3 cos &theta;) का अधिकतम मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><msqrt><mn>17</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>11</mn></msqrt></math></p>", 
                                "<p>9</p>", "<p><math display=\"inline\"><msqrt><mn>13</mn></msqrt></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><msqrt><mn>17</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>11</mn></msqrt></math></p>",
                                "<p>9</p>", "<p><math display=\"inline\"><msqrt><mn>13</mn></msqrt></math></p>"],
                    solution_en: "<p>67.(d) Maximum value of a sin x + b cos x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>)</mo></msqrt></math><br>maximum value of (2 sin <math display=\"inline\"><mi>&#952;</mi></math> + 3 cos &theta;) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><mo>+</mo><mn>9</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>",
                    solution_hi: "<p>67.(d) a sin x + b cos x का अधिकतम मान = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mo>(</mo><msup><mi>a</mi><mn>2</mn></msup><mo>+</mo><msup><mi>b</mi><mn>2</mn></msup><mo>)</mo></msqrt></math> <br>(2 sin &theta; + 3 cos &theta;) का अधिकतम मान =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>4</mn><mo>+</mo><mn>9</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>13</mn></msqrt></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. The average temperature of a city for the first sixteen days of January is 22&deg;C, and the average temperature for the last sixteen days of the same month is 26&deg;C. If the average temperature for the entire month is 24&deg;C, what is the temperature on the sixteenth day?</p>",
                    question_hi: "<p>68. जनवरी के पहले सोलह दिनों के लिए एक शहर का औसत तापमान 22&deg;C रहा, और उसी महीने के अंतिम सोलह दिनों का औसत तापमान 26&deg;C रहा। यदि पूरे महीने का औसत तापमान 24&deg;C रहा, तो सोलहवें दिन का तापमान कितना रहा?</p>",
                    options_en: ["<p>24&deg;C</p>", "<p>25&deg;C</p>", 
                                "<p>23&deg;C</p>", "<p>22&deg;C</p>"],
                    options_hi: ["<p>24&deg;C</p>", "<p>25&deg;C</p>",
                                "<p>23&deg;C</p>", "<p>22&deg;C</p>"],
                    solution_en: "<p>68.(a) <br>Temp. on 16th day = 16 &times;( 22 + 26) - 31 &times; 24 = 768 - 744 = 24&deg;C</p>",
                    solution_hi: "<p>68.(a) <br>16वें दिन का तापमान = 16 &times;( 22 + 26) - 31 &times; 24 = 768 - 744 = 24&deg;C</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. Mira can complete a piece of work in 20 days, Nina in 30 days and Sima in 40 days. Mira and Sima worked together for 4 days and then Mira was replaced by Nina. In how many days, altogether, was the work completed?</p>",
                    question_hi: "<p>69. मीरा एक काम को 20 दिनों में, नीना 30 दिनों में और सीमा 40 दिनों में पूरा कर सकती है। मीरा और सीमा ने 4 दिनों तक एक साथ मिलकर काम किया और फिर मीरा की जगह नीना ने ले ली। कुल मिलाकर कार्य कितने दिनों में पूरा हुआ?</p>",
                    options_en: ["<p>18</p>", "<p>20</p>", 
                                "<p>16</p>", "<p>14</p>"],
                    options_hi: ["<p>18</p>", "<p>20</p>",
                                "<p>16</p>", "<p>14</p>"],
                    solution_en: "<p>69.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020658.png\" alt=\"rId44\" width=\"191\" height=\"111\"><br>Work done by Mira and Sima = (6+3)&times;4 = 36 unit <br>Remaining work = 120 - 36 = 84 unit<br>Now, work done by Nina and Sima = <math display=\"inline\"><mfrac><mrow><mn>84</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 12 days<br>So, the time taken to complete the whole work = 12+4 = 16 days</p>",
                    solution_hi: "<p>69.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020800.png\" alt=\"rId45\" width=\"171\" height=\"109\"><br>मीरा और सिमा द्वारा किया गया कार्य = (6+3)&times;4 = 36 इकाई<br>शेष कार्य = 120 - 36 = 84 इकाई<br>अब, नीना और सिमा द्वारा किया गया कार्य = <math display=\"inline\"><mfrac><mrow><mn>84</mn></mrow><mrow><mn>4</mn><mo>+</mo><mn>3</mn></mrow></mfrac></math> = 12 दिन<br>अतः, पूरा कार्य पूरा करने में लगा समय = 12+4 = 16 दिन</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. O is the centre of the circle. Its two chord AB and CD intersect each other at the point P (internally) within the circle. If AB = 22 cm, PB = 14 cm, CP = 8 cm, then find the value of the PD.</p>",
                    question_hi: "<p>70. O वृत्त का केन्द्र है। इसकी दो जीवाएँ AB और CD एक-दूसरे को वृत्त के अंदर बिंदु P पर (आंतरिक रूप से) प्रतिच्छेद करती हैं। यदि AB = 22 cm, PB = 14 cm, CP = 8 cm है, तो PD का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>12 cm</p>", "<p>22 cm</p>", 
                                "<p>11 cm</p>", "<p>14 cm</p>"],
                    options_hi: ["<p>12 cm</p>", "<p>22 cm</p>",
                                "<p>11 cm</p>", "<p>14 cm</p>"],
                    solution_en: "<p>70.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020935.png\" alt=\"rId46\" width=\"152\" height=\"123\"><br>Using intersecting chord theorem , we have ;<br>AP &times; PB = CP &times; PD<br>8 &times; 14 = 8 &times; PD&nbsp;<br>8 &times; 14 = 8PD<br>PD = 14 cm</p>",
                    solution_hi: "<p>70.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382020935.png\" alt=\"rId46\" width=\"152\" height=\"123\"><br>प्रतिच्छेदी जीवा प्रमेय का उपयोग करते हुए, हमारे पास है;<br>AP &times; PB = CP &times; PD<br>8 &times; 14 = 8 &times; PD&nbsp;<br>8 &times; 14 = 8PD<br>PD = 14 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Read the given information and answer the question that follows.<br>The following table gives the percentage of marks obtained by seven students in six different subjects in an examination.<br>The number in the brackets give the maximum marks in each subject.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382021076.png\" alt=\"rId47\" width=\"468\" height=\"268\"> <br>If someone secured all the highest scores that have been obtained by some student or the other in the six subjects as given in the table above, what would be the exact overall percentage score obtained by that student?</p>",
                    question_hi: "<p>71. दी गई जानकारी को ध्यानपूर्वक पढ़ें और आगे दिए गए प्रश्न का उत्तर दें।<br>नीचे दी गई तालिका में एक परीक्षा के छह अलग-अलग विषयों में सात विद्यार्थियों द्वारा प्राप्त अंकों का प्रतिशत दर्शाया गया है।<br>कोष्ठक में दी गई संख्या प्रत्येक विषय के अधिकतम अंक को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382021184.png\" alt=\"rId48\" width=\"362\" height=\"243\"> <br>यदि किसी ने उपरोक्त तालिका में दिए गए छह विषयों में किसी भी विद्यार्थी या अन्य को प्राप्त अंकों की तुलना में उच्चतम अंक प्राप्त किए हैं, तो उस विद्यार्थी को प्राप्त कुल प्रतिशत अंक ज्ञात करें।</p>",
                    options_en: ["<p>91 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>", "<p>95.16 %</p>", 
                                "<p>91<math display=\"inline\"><mi>%</mi></math></p>", "<p>90 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>91 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>", "<p>95.16 %</p>",
                                "<p>91<math display=\"inline\"><mi>%</mi></math></p>", "<p>90 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>71.(a)<br>Highest marks in maths = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 150<br>Highest marks in chemistry = 130 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 104<br>Highest marks in Physics = 120 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 108<br>Highest marks in Geography = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 95<br>Highest marks in History = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 54<br>Highest marks in Computer science = 40 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 36<br>Required percentage = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>+</mo><mn>104</mn><mo>+</mo><mn>108</mn><mo>+</mo><mn>95</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>36</mn></mrow><mrow><mn>150</mn><mo>+</mo><mn>130</mn><mo>+</mo><mn>120</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>60</mn><mo>+</mo><mn>40</mn></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>547</mn></mrow><mrow><mn>600</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>547</mn><mn>6</mn></mfrac></math>= 91<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>%</p>",
                    solution_hi: "<p>71.(a)<br>गणित में उच्चतम अंक = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 150<br>रसायन विज्ञान में उच्चतम अंक = 130 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 104<br>भौतिकी में उच्चतम अंक = 120 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 108<br>भूगोल में उच्चतम अंक = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 95<br>इतिहास में उच्चतम अंक = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 54<br>कंप्यूटर विज्ञान में उच्चतम अंक = 40 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 36<br>आवश्यक प्रतिशत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>150</mn><mo>+</mo><mn>104</mn><mo>+</mo><mn>108</mn><mo>+</mo><mn>95</mn><mo>+</mo><mn>54</mn><mo>+</mo><mn>36</mn></mrow><mrow><mn>150</mn><mo>+</mo><mn>130</mn><mo>+</mo><mn>120</mn><mo>+</mo><mn>100</mn><mo>+</mo><mn>60</mn><mo>+</mo><mn>40</mn></mrow></mfrac></math>&nbsp;&times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>547</mn></mrow><mrow><mn>600</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>547</mn><mn>6</mn></mfrac></math>= 91<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. The following observations are arranged in ascending order.<br>29, 32, 48, 50, x, x + 2, 72, 78, 84, 95<br>If the median is 63, then the value of x is:</p>",
                    question_hi: "<p>72. निम्नलिखित प्रेक्षणों को आरोही क्रम में व्यवस्थित किया गया है।<br>29, 32, 48, 50, x, x + 2, 72, 78, 84, 95.<br>यदि माध्यिका 63 है, तो x का मान क्या है?</p>",
                    options_en: ["<p>50</p>", "<p>31</p>", 
                                "<p>62</p>", "<p>63</p>"],
                    options_hi: ["<p>50</p>", "<p>31</p>",
                                "<p>62</p>", "<p>63</p>"],
                    solution_en: "<p>72.(c)<br>29, 32, 48, 50, x, x+2 , 72, 78, 84, 95<br>Median = <math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>+</mo><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> <br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><mi>x</mi><mo>+</mo><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>63</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>2</mn><mi>x</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>126</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>2</mn><mi>x</mi><mo>=</mo><mn>124</mn><mo>&#8658;</mo><mi>x</mi><mo>=</mo><mn>62</mn></math><br>Hence, the value of x is 62.</p>",
                    solution_hi: "<p>72.(c)<br>29, 32, 48, 50, x, x+2 , 72, 78, 84, 95<br>माध्य = <math display=\"inline\"><mfrac><mrow><mi>x</mi><mo>+</mo><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> <br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mfrac><mrow><mi>x</mi><mo>+</mo><mi>x</mi><mo>+</mo><mn>2</mn></mrow><mn>2</mn></mfrac><mo>=</mo><mn>63</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>2</mn><mi>x</mi><mo>+</mo><mn>2</mn><mo>=</mo><mn>126</mn></math><br><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo><mn>2</mn><mi>x</mi><mo>=</mo><mn>124</mn><mo>&#8658;</mo><mi>x</mi><mo>=</mo><mn>62</mn></math><br>इसलिए, x का मान 62 है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Pipe Q can fill the tank in 60 hours while pipe R may fill in 45 hours. Q and R pipes are opened together for 6 hours after which pipe W is also opened to empty the tank. All three pipes are opened simultaneously for 24 hours to reach the half level mark. How much time (in hours) will pipe W alone take to empty the entire tank?</p>",
                    question_hi: "<p>73. पाइप Q, किसी टंकी को 60 घंटे में भर सकता है, जबकि पाइप R इसे 45 घंटे में भर सकता है। Q और R पाइप, 6 घंटे के लिए एक साथ खोले जाते हैं, जिसके बाद टंकी को खाली करने के लिए पाइप W को भी खोला जाता है। आधे स्तर के निशान तक पहुंचने के लिए तीनों पाइपों को 24 घंटे के लिए एक साथ खोला जाता है। अकेले पाइप W द्वारा पूरी टंकी को खाली करने में लगने वाला समय (घंटे में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>48</p>", "<p>42</p>", 
                                "<p>36</p>", "<p>30</p>"],
                    options_hi: ["<p>48</p>", "<p>42</p>",
                                "<p>36</p>", "<p>30</p>"],
                    solution_en: "<p>73.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382021318.png\" alt=\"rId49\" width=\"242\" height=\"150\"><br>Efficiency of Q and R in 1 hour = 3 + 4 = 7 unit<br>Water filled by Q and R in 6 hours = 7 <math display=\"inline\"><mo>&#215;</mo></math> 6 = 42 units<br>Now <br>Q and R along with pipe W opened then tank will be half filled in 24 hours i.e. 90 unit<br>Water filled by Q and R in 24 hour + previous 6 hours = 7 <math display=\"inline\"><mo>&#215;</mo></math> 24 + 42 unit = 168 + 42 = 210 unit<br>But it was only 90 unit filled <br>Hence water emptied by W in 24 hours = 210 - 90 = 120 unit<br>Efficiency of W in 1 hour = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 5 unit<br>Hence,<br>Time taken by W to empty entire tank = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 36 hours</p>",
                    solution_hi: "<p>73.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1742382021423.png\" alt=\"rId50\" width=\"277\" height=\"166\"><br>1 घंटे में Q और R की दक्षता = 3 + 4 = 7 इकाई<br>Q और R द्वारा 6 घंटे में भरा गया पानी = 7 <math display=\"inline\"><mo>&#215;</mo></math> 6 = 42 इकाई<br>अब<br>Q और R को पाइप W के साथ खोला गया तो टंकी 24 घंटे में आधी यानी 90 इकाई भर जाएगी<br>पिछले 6 घंटे और 24 घंटे में Q और R द्वारा भरा गया पानी = 7 <math display=\"inline\"><mo>&#215;</mo></math> 24 + 42 इकाई= 168 + 42 = 210 इकाई<br>लेकिन यह केवल 90 इकाई ही भरा था <br>अतः 24 घंटे में W द्वारा खाली किया गया पानी = 210 - 90 = 120 इकाई<br>1 घंटे में W की दक्षता = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 5 इकाई<br>इस तरह,<br>W द्वारा संपूर्ण टैंक खाली करने में लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 36 hours</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A box contains 2 black, 6 green and 4 yellow balls. If 2 balls are picked up at random, the probability that both are green-coloured is:</p>",
                    question_hi: "<p>74. एक बॉक्स में 2 काली, 6 हरी और 4 पीली गेंदें हैं। यदि 2 गेंदों को यादृच्छिक रूप से उठाया जाता है, तो दोनों के हरे रंग के होने की प्रायिकता है:</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>11</mn></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>22</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>11</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>74.(a) Number of ways of selecting two balls of green coloured <br><math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mi>n</mi><mo>(</mo><mi>E</mi><mo>)</mo></mrow></mfenced></math> = <sup>6</sup>C<sub>2</sub> = 15<br>Number of ways of two balls out of 12 balls <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mi>n</mi><mo>(</mo><mi>S</mi><mo>)</mo></mrow></mfenced></math> = <sup>12</sup>C<sub>2</sub> = 66<br>Required probability = <math display=\"inline\"><mfrac><mrow><mi>n</mi><mo>(</mo><mi>E</mi><mo>)</mo></mrow><mrow><mi>n</mi><mo>(</mo><mi>s</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>66</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>22</mn></mfrac></math></p>",
                    solution_hi: "<p>74.(a) <br>हरे रंग की दो गेंदों को चुनने के तरीकों की संख्या <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mi>n</mi><mo>(</mo><mi>E</mi><mo>)</mo></mrow></mfenced></math> = <sup>6</sup>C<sub>2</sub>= 15<br>12 गेंदों में से दो गेंदों के तरीकों की संख्या <math display=\"inline\"><mfenced open=\"{\" close=\"}\" separators=\"|\"><mrow><mi>n</mi><mo>(</mo><mi>S</mi><mo>)</mo></mrow></mfenced></math> = <sup>12</sup>C<sub>2</sub>= 66<br>आवश्यक प्रायिकता = <math display=\"inline\"><mfrac><mrow><mi>n</mi><mo>(</mo><mi>E</mi><mo>)</mo></mrow><mrow><mi>n</mi><mo>(</mo><mi>s</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>15</mn><mn>66</mn></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>22</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. If (<math display=\"inline\"><mi>x</mi></math> + y + z) = 23 and x<sup>2</sup>+ y<sup>2</sup>+ z<sup>2</sup> = 179, then find the value of (xy + yz + zx).</p>",
                    question_hi: "<p>75. यदि (<math display=\"inline\"><mi>x</mi></math> + y + z) = 23 और x<sup>2</sup>+ y<sup>2</sup>+ z<sup>2</sup> = 179 है, तो (xy + yz + zx) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>280</p>", "<p>225</p>", 
                                "<p>175</p>", "<p>350</p>"],
                    options_hi: ["<p>280</p>", "<p>225</p>",
                                "<p>175</p>", "<p>350</p>"],
                    solution_en: "<p>75.(c)<br><strong>Given</strong><br>(<math display=\"inline\"><mi>x</mi></math> + y + z) = 23<br>Taking square both sides<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>+</mo><mi>z</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = 23<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2 (xy + yz + zx) = 529<br><math display=\"inline\"><mo>&#8658;</mo></math> 179 + 2 (xy + yz + zx) = 529<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 (xy + yz + zx) = 529 - 179 = 350<br><math display=\"inline\"><mo>&#8658;</mo></math> xy + yz + zx = 175</p>",
                    solution_hi: "<p>75.(c)<br>दिया गया<br>(<math display=\"inline\"><mi>x</mi></math> + y + z) = 23<br>दोनों तरफ वर्ग करने पर, <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>x</mi><mo>+</mo><mi>y</mi><mo>+</mo><mi>z</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = 23<sup>2</sup><br><math display=\"inline\"><mo>&#8658;</mo></math> x<sup>2</sup> + y<sup>2</sup> + z<sup>2</sup> + 2 (xy + yz + zx) = 529<br><math display=\"inline\"><mo>&#8658;</mo></math> 179 + 2 (xy + yz + zx) = 529<br><math display=\"inline\"><mo>&#8658;</mo></math> 2 (xy + yz + zx) = 529 - 179 = 350<br><math display=\"inline\"><mo>&#8658;</mo></math> xy + yz + zx = 175</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If you don&rsquo;t find any error, mark &lsquo;No error&rsquo; as your answer.<br>By the time I reaching / the metro station, / the train had departed.</p>",
                    question_hi: "<p>76. The following sentence has been divided into parts. One of them may contain an error. Select the part that contains the error from the given options. If you don&rsquo;t find any error, mark &lsquo;No error&rsquo; as your answer.<br>By the time I reaching / the metro station, / the train had departed.</p>",
                    options_en: ["<p>By the time I reaching</p>", "<p>the metro station</p>", 
                                "<p>the train had departed</p>", "<p>no error</p>"],
                    options_hi: ["<p>By the time I reaching</p>", "<p>the metro station</p>",
                                "<p>the train had departed</p>", "<p>no error</p>"],
                    solution_en: "<p>76.(a) By the time I reaching <br>If two actions happened in the past, 1st action is written in past perfect tense(had + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math>) and 2nd action is written in simple past tense(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>2</mn></msub></math>). In the given sentence, reaching the metro station is the 2nd action. Hence, &lsquo;By the time I reached(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>2</mn></msub></math>)&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(a) By the time I reaching <br>यदि दो actions past में हुई हैं, तो 1st action को past perfect tense (had + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>3</mn></msub></math>) में लिखा जाता है और 2nd action को simple past tense(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>2</mn></msub></math>) में लिखा जाता है। दिए गए sentence में, metro station पर पहुँचना 2nd action है। इसलिए, &lsquo;By the time I reached(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msub><mi>V</mi><mn>2</mn></msub></math>)&rsquo; सबसे उपयुक्त उत्तर है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate option to substitute the underlined segment in the given sentence.</p>\n<p>Though Kalpana listened to Sheetal carefully, she knew that she should&nbsp;<span style=\"text-decoration: underline;\">take it with a grain of salt.</span></p>",
                    question_hi: "<p>77. Select the most appropriate option to substitute the underlined segment in the given sentence.</p>\n<p>Though Kalpana listened to Sheetal carefully, she knew that she should&nbsp;<span style=\"text-decoration: underline;\">take it with a grain of salt.</span></p>",
                    options_en: ["<p>regard it as exaggerated and not completely true</p>", "<p>move according to the trend</p>", 
                                "<p>be do it meticulously as taking salt</p>", "<p>accept it as really useful and worthy</p>"],
                    options_hi: ["<p>regard it as exaggerated and not completely true</p>", "<p>move according to the trend</p>",
                                "<p>be do it meticulously as taking salt</p>", "<p>accept it as really useful and worthy</p>"],
                    solution_en: "<p>77.(a) <strong>Take it with a grain of salt</strong>- regard it as exaggerated and not completely true.</p>",
                    solution_hi: "<p>77.(a) <strong>Take it with a grain of salt</strong>- regard it as exaggerated and not completely true./ इसे अतिशयोक्तिपूर्ण और पूर्णतः सत्य न मानें।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Ranjeet <strong><span style=\"text-decoration: underline;\">would have been looked</span></strong> smart in traditional clothes.</p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Ranjeet <span style=\"text-decoration: underline;\"><strong>would have been looked</strong></span> smart in traditional clothes.</p>",
                    options_en: ["<p>was looked</p>", "<p>would be looked</p>", 
                                "<p>would have looked</p>", "<p>No improvement</p>"],
                    options_hi: ["<p>was looked</p>", "<p>would be looked</p>",
                                "<p>would have looked</p>", "<p>No improvement</p>"],
                    solution_en: "<p>78.(c) would have looked.<br>The given sentence is in the active voice. Hence, we will remove &lsquo;been&rsquo; from the given sentence &amp; &lsquo;would have looked&rsquo; becomes the most appropriate answer.</p>",
                    solution_hi: "<p>78.(c) would have looked.<br>दिया गया वाक्य active voice में है। इसलिए, हम दिए गए वाक्य से &lsquo;been&rsquo; को हटा देंगे और &lsquo;would have looked&rsquo; सबसे उपयुक्त उत्तर होगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. &nbsp;Find the correctly spelt word.</p>",
                    question_hi: "<p>79. &nbsp;Find the correctly spelt word.</p>",
                    options_en: ["<p>coquete</p>", "<p>hystericle</p>", 
                                "<p>idiosyncracy</p>", "<p>onomatopoeia</p>"],
                    options_hi: ["<p>coquete</p>", "<p>hystericle</p>",
                                "<p>idiosyncracy</p>", "<p>onomatopoeia</p>"],
                    solution_en: "<p>79.(d) Onomatopoeia <br>Other words - coquette, hysterical and idiosyncrasy.</p>",
                    solution_hi: "<p>79.(d) Onomatopoeia<br>दूसरे शब्द - coquette, hysterical and idiosyncrasy. <br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80.&nbsp;Was your bag left in the bus?</p>",
                    question_hi: "<p>80.&nbsp;Was your bag left in the bus?</p>",
                    options_en: ["<p>Did you leave your bag in the bus?</p>", "<p>Was you leaving your bag in the bus?</p>", 
                                "<p>Did you left your bag in the bus?</p>", "<p>Have you left your bag in the bus?</p>"],
                    options_hi: ["<p>Did you leave your bag in the bus?</p>", "<p>Was you leaving your bag in the bus?</p>",
                                "<p>Did you left your bag in the bus?</p>", "<p>Have you left your bag in the bus?</p>"],
                    solution_en: "<p>80.(a) Did you leave your bag in the bus? (Correct)<br>(b) <span style=\"text-decoration: underline;\">Was you leaving</span> your bag in the bus? (Tense has changed)<br>(c) <span style=\"text-decoration: underline;\">Did you left</span> your bag in the bus? (Incorrect verb)<br>(d) <span style=\"text-decoration: underline;\">Have you left</span> your bag in the bus? (Tense has changed)</p>",
                    solution_hi: "<p>80.(a) Did you leave your bag in the bus? (Correct)<br>(b) <span style=\"text-decoration: underline;\">Was you leaving</span> your bag in the bus ? (गलत tense (past continuous) का प्रयोग किया गया है । Did you leave (simple past) का प्रयोग होगा । )<br>(c) <span style=\"text-decoration: underline;\">Did you left</span> your bag in the bus ? (गलत verb (left) का प्रयोग किया गया है। (leave) का प्रयोग होगा । )<br>(d) <span style=\"text-decoration: underline;\">Have you left</span> your bag in the bus? (गलत tenses (present perfect) का प्रयोग किया गया है। Did you leave (simple past) का प्रयोग होगा । )</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate ANTONYM of the given word. <br>Quaint</p>",
                    question_hi: "<p>81. Select the most appropriate ANTONYM of the given word. <br>Quaint</p>",
                    options_en: ["<p>Distinct</p>", "<p>Ridiculous</p>", 
                                "<p>Brisk</p>", "<p>Ordinary</p>"],
                    options_hi: ["<p>Distinct</p>", "<p>Ridiculous</p>",
                                "<p>Brisk</p>", "<p>Ordinary</p>"],
                    solution_en: "<p>81.(d) <strong>Ordinary-</strong> normal or usual.<br><strong>Quaint-</strong> attractively unusual or old-fashioned.<br><strong>Distinct-</strong> clearly different or unique.<br><strong>Ridiculous-</strong> deserving or inviting mockery.<br><strong>Brisk-</strong> quick or energetic.</p>",
                    solution_hi: "<p>81.(d) <strong>Ordinary</strong> (सामान्य) - normal or usual.<br><strong>Quaint</strong> (विलक्षण) - attractively unusual or old-fashioned.<br><strong>Distinct</strong> (विशिष्ट) - clearly different or unique.<br><strong>Ridiculous</strong> (हास्यास्पद) - deserving or inviting mockery.<br><strong>Brisk</strong> (स्फूर्तिकारक) - quick or energetic.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Your lunch box has been packed by mother.</p>",
                    question_hi: "<p>82. Your lunch box has been packed by mother.</p>",
                    options_en: ["<p>Mother had packed your lunch box.</p>", "<p>Mother is packing your lunch box.</p>", 
                                "<p>Mother has packed your lunch box.</p>", "<p>Mother will pack your lunch box</p>"],
                    options_hi: ["<p>Mother had packed your lunch box.</p>", "<p>Mother is packing your lunch box.</p>",
                                "<p>Mother has packed your lunch box.</p>", "<p>Mother will pack your lunch box</p>"],
                    solution_en: "<p>82.(c) Mother has packed your lunch box. (Correct)<br>(a) Mother <span style=\"text-decoration: underline;\">had packed</span> your lunch box. (Tense has changed)<br>(b) Mother <span style=\"text-decoration: underline;\">is packing</span> your lunch box. (Tense has changed)<br>(d) Mother <span style=\"text-decoration: underline;\">will pack</span> your lunch box. (Tense has changed)</p>",
                    solution_hi: "<p>82.(c) Mother has packed your lunch box. (Correct)<br>(a) Mother <span style=\"text-decoration: underline;\">had packed</span> your lunch box. (गलत tense (past perfect) का प्रयोग किया गया है। (has packed (present perfect) का प्रयोग होगा। )<br>(b) Mother <span style=\"text-decoration: underline;\">is packing</span> your lunch box. (गलत (present continuous) का प्रयोग किया गया है। (has packed (past perfect) का प्रयोग होगा । )<br>(d) Mother <span style=\"text-decoration: underline;\">will pack</span> your lunch box. (गलत tense (simple future) का प्रयोग किया गया है। (has packed (present perfect) का प्रयोग होगा। )</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) And this in turn has effects on the media.<br>(B) In most urban societies, it is women who take most of the everyday decisions about what to buy for their households. <br>(C )Significant proportions of advertising expenditure are now directed at women<br>(D)This has made advertisers very sensitive to the views and perspectives of women as consumers.</p>",
                    question_hi: "<p>83. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) And this in turn has effects on the media.<br>(B) In most urban societies, it is women who take most of the everyday decisions about what to buy for their households. <br>(C )Significant proportions of advertising expenditure are now directed at women<br>(D)This has made advertisers very sensitive to the views and perspectives of women as consumers.</p>",
                    options_en: ["<p>BACD</p>", "<p>ADCB</p>", 
                                "<p>ABCD</p>", "<p>BDCA</p>"],
                    options_hi: ["<p>BACD</p>", "<p>ADCB</p>",
                                "<p>ABCD</p>", "<p>BDCA</p>"],
                    solution_en: "<p>83 (d) BDCA<br>Sentence B will be the starting line as it contains the main idea of the parajumble i.e.women who take everyday decisions about what to buy for their households .Sentence D states that this has made advertisers very sensitive to the views and perspectives of women as consumers . So, D will follow B. Further, Sentence C states that significant proportions of advertising expenditure are now directed at women and Sentence A states that this in turn has effects on the media . So, A will follow C. Going through the options, option (d) BDCA has the correct sequence.</p>",
                    solution_hi: "<p>83. (d) BDCA<br>Sentence B starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo;महिलाएं जो अपने घरों के लिए क्या खरीदना है, इस बारे में हर रोज निर्णय लेती हैं&rsquo; शामिल है। Sentence D बताता है कि इसने विज्ञापनदाताओं को उपभोक्ताओं के रूप में महिलाओं के विचारों और दृष्टिकोणों के प्रति बहुत संवेदनशील बना दिया है। तो, B के बाद D आएगा। आगे, Sentence C कहता है कि विज्ञापन व्यय का महत्वपूर्ण अनुपात अब महिलाओं पर निर्देशित है और Sentence A कहता है कि इसका मीडिया पर प्रभाव पड़ता है। इसलिए, C के बाद A आएगा। options के माध्यम से जाने पर, option (d) BDCA में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the option that is similar in meaning to the underlined word in the following sentence.<br>Looking after small children is a real <span style=\"text-decoration: underline;\">conundrum</span> for working parents.</p>",
                    question_hi: "<p>84. Select the option that is similar in meaning to the underlined word in the following sentence.<br>Looking after small children is a real <span style=\"text-decoration: underline;\">conundrum</span> for working parents.</p>",
                    options_en: ["<p>pacification</p>", "<p>plan</p>", 
                                "<p>problem</p>", "<p>peace</p>"],
                    options_hi: ["<p>pacification</p>", "<p>plan</p>",
                                "<p>problem</p>", "<p>peace</p>"],
                    solution_en: "<p>84.(c) <strong>Problem-</strong> a matter or situation that requires a solution.<br><strong>Conundrum-</strong> a confusing and difficult problem or question.<br><strong>Pacification-</strong> the act of calming or bringing peace.<br><strong>Plan-</strong> a detailed proposal or strategy.<br><strong>Peace-</strong> a state of tranquility or calmness.</p>",
                    solution_hi: "<p>84.(c) <strong>Problem</strong> (समस्या) - a matter or situation that requires a solution.<br><strong>Conundrum</strong> (जटिल समस्या) - a confusing and difficult problem or question.<br><strong>Pacification</strong> (शांतिस्थापन) - the act of calming or bringing peace.<br><strong>Plan</strong> (योजना) - a detailed proposal or strategy.<br><strong>Peace</strong> (शांति) - a state of tranquility or calmness.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>85. Select the INCORRECTLY spelt word.</p>",
                    options_en: ["<p>Warranty</p>", "<p>Committee</p>", 
                                "<p>Dilemma</p>", "<p>Neice</p>"],
                    options_hi: ["<p>Warranty</p>", "<p>Committee</p>",
                                "<p>Dilemma</p>", "<p>Neice</p>"],
                    solution_en: "<p>85.(d) Neice<br>&lsquo;Niece&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>85.(d) Neice<br>&lsquo;Niece&rsquo; सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct.</p>\n<p>Mobile phones are so importance these days that they are no longer luxury items but have become a necessity.</p>",
                    question_hi: "<p>86. Identify the segment in the sentence which contains grammatical error. Mark &lsquo;no error&rsquo; in case the sentence given is correct.</p>\n<p>Mobile phones are so importance these days that they are no longer luxury items but have become a necessity.</p>",
                    options_en: ["<p>a necessity</p>", "<p>so importance these days</p>", 
                                "<p>no longer</p>", "<p>No Error</p>"],
                    options_hi: ["<p>a necessity</p>", "<p>so importance these days</p>",
                                "<p>no longer</p>", "<p>No Error</p>"],
                    solution_en: "<p>86.(b) so importance these days<br>Here, so important (Adjective) these days ........ should be used. It is not proper to use importance (Noun) here. .</p>",
                    solution_hi: "<p>86.(b) So importance these days<br>यहाँ, So important (Adjective) these days........ का प्रयोग करना चाहिए। यहाँ importance (Noun) का प्रयोग उचित नहीं है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate idiom to fill in the blank. <br>The team always _______ to come up with unique advertisements for all its clients</p>",
                    question_hi: "<p>87. Select the most appropriate idiom to fill in the blank. <br>The team always _______ to come up with unique advertisements for all its clients</p>",
                    options_en: ["<p>thinks outside the box</p>", "<p>throw caution to the winds</p>", 
                                "<p>take a back seat</p>", "<p>turn the tables</p>"],
                    options_hi: ["<p>thinks outside the box</p>", "<p>throw caution to the winds</p>",
                                "<p>take a back seat</p>", "<p>turn the tables</p>"],
                    solution_en: "<p>87.(a) <strong>thinks outside the box</strong>- to think creatively and innovatively, often by looking at a problem from a different perspective.</p>",
                    solution_hi: "<p>87.(a) <strong>thinks outside the box</strong> - to think creatively and innovatively, often by looking at a problem from a different perspective./रचनात्मक और नवीन ढंग से सोचना, अक्सर किसी समस्या को एक अलग परिप्रेक्ष्य से देखना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>The judge declared the agreement <span style=\"text-decoration: underline;\"><strong>null and void.</strong></span></p>",
                    question_hi: "<p>88. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below as underlined in the sentence.<br>The judge declared the agreement <span style=\"text-decoration: underline;\"><strong>null and void.</strong></span></p>",
                    options_en: ["<p>empty</p>", "<p>annulled</p>", 
                                "<p>unavoidable</p>", "<p>not binding</p>"],
                    options_hi: ["<p>empty</p>", "<p>annulled</p>",
                                "<p>unavoidable</p>", "<p>not binding</p>"],
                    solution_en: "<p>88.(d) not binding<br>Eg- The law related to wearing a mask in public places stands null and void.</p>",
                    solution_hi: "<p>88.(d) not binding/ बाध्यकारी नहीं<br>जैसे- The law related to wearing a mask in public places stands null and void./ सार्वजनिक स्थानों पर मास्क पहनने से संबंधित कानून null and void है।।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Environmental or ecological factors need not only be destructive to cause change, they can be constructive as well. <br>(B) This transformation could be total destruction of society, like in a Tsunami or earthquake.<br>(C) A good example is the discovery of oil in the desert regions of West Asia (also called the Middle East).<br>(D )Sometimes natural disasters lead to a total transformation of society.</p>",
                    question_hi: "<p>89. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Environmental or ecological factors need not only be destructive to cause change, they can be constructive as well. <br>(B)This transformation could be total destruction of society, like in a Tsunami or earthquake.<br>(C)A good example is the discovery of oil in the desert regions of West Asia (also called the Middle East).<br>(D)Sometimes natural disasters lead to a total transformation of society.</p>",
                    options_en: ["<p>DCBA</p>", "<p>CBAD</p>", 
                                "<p>ABCD</p>", "<p>DBAC</p>"],
                    options_hi: ["<p>DCBA</p>", "<p>CBAD</p>",
                                "<p>ABCD</p>", "<p>DBAC</p>"],
                    solution_en: "<p>89.(d) DBAC<br>Sentence D will be the first sentence as the main idea of the parajumble &ldquo;transformation due to natural disasters&rdquo; is explained. B will follow D as the effect of this transformation is given in B. B will be followed by A as it explains that sometimes it can be constructive as well. At the end it will be C as it mentions about the discovery of oil in West Asia. So, option (d) DBAC has the correct sequence.</p>",
                    solution_hi: "<p>89.(d) DBAC<br>Parajumble के मुख्य विचार &ldquo;transformation due to natural disasters&rdquo; के रूप में sentence D first sentence होगा। D के बाद B आएगा क्योंकि इस परिवर्तन का प्रभाव B में दिया गया है। B के बाद A आएगा क्योंकि यह बताता है कि कभी-कभी यह बढ़िया भी हो सकता है। अंत में यह C होगा क्योंकि इसमें पश्चिम एशिया में तेल की खोज का जिक्र है। इसलिए, option (d) DBAC का सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that can be used as a one-word substitute for the given group of words.<br>Dwarfed varieties of trees and shrubs in pots</p>",
                    question_hi: "<p>90. Select the option that can be used as a one-word substitute for the given group of words.<br>Dwarfed varieties of trees and shrubs in pots</p>",
                    options_en: ["<p>Bonafide</p>", "<p>Bonsai</p>", 
                                "<p>Orchard</p>", "<p>Botanical</p>"],
                    options_hi: ["<p>Bonafide</p>", "<p>Bonsai</p>",
                                "<p>Orchard</p>", "<p>Botanical</p>"],
                    solution_en: "<p>90.(b) <strong>Bonsai-</strong> dwarf varieties of trees and shrubs in pots.<br><strong>Bonafide-</strong> real or genuine.<br><strong>Orchard-</strong> a piece of land on which fruit trees are grown.<br><strong>Botanical-</strong> of or relating to plants or botany.</p>",
                    solution_hi: "<p>90.(b) <strong>Bonsai</strong> (बोनसाई) - dwarf varieties of trees and shrubs in pots.<br><strong>Bonafide</strong> (वास्तविक) - real or genuine.<br><strong>Orchard</strong> (बगीचे) - a piece of land on which fruit trees are grown.<br><strong>Botanical</strong> (वनस्पति विज्ञान) - of or relating to plants or botany.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>It\'s all Greek to me</p>",
                    question_hi: "<p>91. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>It\'s all Greek to me</p>",
                    options_en: ["<p>I cannot understand anything</p>", "<p>I am aware of the historical facts</p>", 
                                "<p>I know this subject well</p>", "<p>I can understand the cause of something</p>"],
                    options_hi: ["<p>I cannot understand anything</p>", "<p>I am aware of the historical facts</p>",
                                "<p>I know this subject well</p>", "<p>I can understand the cause of Something</p>"],
                    solution_en: "<p>91.(a) It\'s all Greek to me- I cannot understand anything.<br>E.g.- My uncle and my father were discussing politics, but it was all greek to me.</p>",
                    solution_hi: "<p>91.(a) It\'s all Greek to me - I cannot understand anything (कुछ समझ न आना ).<br>E.g.- My uncle and my father were discussing politics, but it was all Greek to me.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate option to fill in the blank.<br>If you do well in this exam next year then my efforts ____________ successful.</p>",
                    question_hi: "<p>92. Select the most appropriate option to fill in the blank.<br>If you do well in this exam next year then my efforts ____________ successful.</p>",
                    options_en: ["<p>has been</p>", "<p>would have been</p>", 
                                "<p>have been</p>", "<p>will be</p>"],
                    options_hi: ["<p>has been</p>", "<p>would have been</p>",
                                "<p>have been</p>", "<p>will be</p>"],
                    solution_en: "<p>92.(d) will be<br>Clearly &ldquo;next year&rdquo; has been mentioned so it relates to the future. So future tense will be used.<br><strong>(It is a conditional sentence. So the structure will be &ldquo; if + simple present + simple future&rdquo;)</strong></p>",
                    solution_hi: "<p>92.(d) will be<br>स्पष्ट रूप से &ldquo;next year&rdquo; का उल्लेख किया गया है, इसलिए यह future से संबंधित है। अर्थात future tense का उपयोग किया जाएगा।<br><strong>(यह एक conditional sentence है , तो structure होगा - &ldquo; if + simple present + simple future&rdquo;)</strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Find a word that is the synonym of <br>DERISION</p>",
                    question_hi: "<p>93. Find a word that is the synonym of <br>DERISION</p>",
                    options_en: ["<p>Humiliation</p>", "<p>Embarrassment</p>", 
                                "<p>Ridicule</p>", "<p>Condemnation</p>"],
                    options_hi: ["<p>Humiliation</p>", "<p>Embarrassment</p>",
                                "<p>Ridicule</p>", "<p>Condemnation</p>"],
                    solution_en: "<p>93.(c) <strong>Ridicule</strong> - the subjection of someone or something to contemptuous and dismissive language or behaviour.<br><strong>Derision</strong> - contemptuous ridicule or mockery.<br><strong>Humiliation</strong> - the action of humiliating someone or the state of being humiliated.<br><strong>Embarrassment</strong> - a feeling of self-consciousness, shame, or awkwardness.<br><strong>Condemnation</strong> - the expression of very strong disapproval; censure.</p>",
                    solution_hi: "<p>93.(c) <strong>Ridicule</strong> - तिरस्कारपूर्ण भाषा या व्यवहार के लिए किसी को या किसी चीज़ को अधीन करना।<br><strong>Derision</strong> - तिरस्कारपूर्ण उपहास।<br><strong>Humiliation</strong> - किसी को अपमानित करने की क्रिया या अपमानित होने की अवस्था या भाव।<br><strong>Embarrassment</strong> - आत्म-चेतना, शर्म या अजीबता की भावना।<br><strong>Condemnation</strong> - बहुत मजबूत अस्वीकृति की अभिव्यक्ति; निंदा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Choose the word opposite in meaning to the given word.<br>Nourish</p>",
                    question_hi: "<p>94. Choose the word opposite in meaning to the given word.<br>Nourish</p>",
                    options_en: ["<p>starve</p>", "<p>foster</p>", 
                                "<p>sustain</p>", "<p>strengthen</p>"],
                    options_hi: ["<p>starve</p>", "<p>foster</p>",
                                "<p>sustain</p>", "<p>strengthen</p>"],
                    solution_en: "<p>94.(a) starve<br>Nourish means to give somebody/something the right kind of food so that one can grow and be healthy.</p>",
                    solution_hi: "<p>94.(a) starve<br>Nourish का अर्थ है किसी को/किसी चीज़ को सही प्रकार का भोजन देना ताकि वह बढ़ सके और स्वस्थ रह सके।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>He set up institutions of intentional _________.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>He set up institutions of intentional _________.</p>",
                    options_en: ["<p>reputation</p>", "<p>famous</p>", 
                                "<p>repute</p>", "<p>renown</p>"],
                    options_hi: ["<p>reputation</p>", "<p>famous</p>",
                                "<p>repute</p>", "<p>renown</p>"],
                    solution_en: "<p>95.(a) reputation. <br>When we talk about something having (possessing) a good name, we say it has a &lsquo;reputation&rsquo;.</p>",
                    solution_hi: "<p>95.(a) reputation. <br>जब हम किसी चीज के बारे में बात करते हैं जिसका नाम अच्छा है, तो हम कहते हैं कि उसकी एक &lsquo;reputation\' है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96.<strong> Cloze Test:</strong> <br>The library, if used properly, is invaluable in helping you develop discernment. It is rich ____96____ information that goes far beyond the ____97____ of any one text book or course. ____98____ your text book author does not make a particular____99____ clear and you feel the need ____100____ another description in greater detail or in other words, go to the library and check other books on the subject. <br>Select the most appropriate option to fill in the blank no.96</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong> <br>The library, if used properly, is invaluable in helping you develop discernment. It is rich ____96____ information that goes far beyond the ____97____ of any one text book or course. ____98____ your text book author does not make a particular____99____ clear and you feel the need ____100____ another description in greater detail or in other words, go to the library and check other books on the subject. <br>Select the most appropriate option to fill in the blank no.96</p>",
                    options_en: ["<p>of</p>", "<p>for</p>", 
                                "<p>in</p>", "<p>with</p>"],
                    options_hi: ["<p>of</p>", "<p>for</p>",
                                "<p>in</p>", "<p>with</p>"],
                    solution_en: "<p>96.(c) in <br>The phrase &lsquo;rich in something&rsquo; means containing a lot of something. The given passage states that the Library contains a lot of information. Hence, &lsquo;in&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(c) in <br>वाक्य &lsquo;rich in something&rsquo; का अर्थ है, जिसमें बहुत कुछ हो। दिए गए passage में कहा गया है कि पुस्तकालय में बहुत सारी जानकारियाँ होती है। इसलिए, &lsquo;in&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97.<strong> Cloze Test:</strong> <br>The library, if used properly, is invaluable in helping you develop discernment. It is rich ____96____ information that goes far beyond the ____97____ of any one text book or course. ____98____ your text book author does not make a particular____99____ clear and you feel the need ____100____ another description in greater detail or in other words, go to the library and check other books on the subject. <br>Select the most appropriate option to fill in the blank no.97</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong> <br>The library, if used properly, is invaluable in helping you develop discernment. It is rich ____96____ information that goes far beyond the ____97____ of any one text book or course. ____98____ your text book author does not make a particular____99____ clear and you feel the need ____100____ another description in greater detail or in other words, go to the library and check other books on the subject.<br>Select the most appropriate option to fill in the blank no.97</p>",
                    options_en: ["<p>pages</p>", "<p>limits</p>", 
                                "<p>confines</p>", "<p>limitations</p>"],
                    options_hi: ["<p>pages</p>", "<p>limits</p>",
                                "<p>confines</p>", "<p>limitations</p>"],
                    solution_en: "<p>97.(b) limits <br>&lsquo;Limit&rsquo; means something that bounds, restrains, or confines. The given passage talks about the information that goes beyond the limits. Hence, &lsquo;limits&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) limits <br>&lsquo;Limit&rsquo; का अर्थ है जो सीमाबद्ध या सीमित करता हो। दिया गया passage उस जानकारी के बारे में बात करता है जो सीमा से बाहर जाती है। इसलिए, &lsquo;Limit&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong> <br>The library, if used properly, is invaluable in helping you develop discernment. It is rich ____96____ information that goes far beyond the ____97____ of any one text book or course. ____98____ your text book author does not make a particular____99____ clear and you feel the need ____100____ another description in greater detail or in other words, go to the library and check other books on the subject.<br>Select the most appropriate option to fill in the blank no.98</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong> <br>The library, if used properly, is invaluable in helping you develop discernment. It is rich ____96____ information that goes far beyond the ____97____ of any one text book or course. ____98____ your text book author does not make a particular____99____ clear and you feel the need ____100____ another description in greater detail or in other words, go to the library and check other books on the subject. <br>Select the most appropriate option to fill in the blank no.98</p>",
                    options_en: ["<p>If</p>", "<p>While</p>", 
                                "<p>When</p>", "<p>Suppose</p>"],
                    options_hi: ["<p>If</p>", "<p>While</p>",
                                "<p>When</p>", "<p>Suppose</p>"],
                    solution_en: "<p>98.(a) If<br>We generally use &lsquo;if&rsquo; in conditional sentences. Similarly, the given passage talks about a condition of your book author not making a particular point clear. Hence, &lsquo;if&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(a) If<br>हम आमतौर पर conditional sentences में \' if \' का प्रयोग करते हैं। इसी तरह, दिया गया passage , लेखक के बारे में बात करता है जो किसी विशेष बिंदु को स्पष्ट नहीं करता है। अतः \'if \' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99.<strong>Cloze Test:</strong> <br>The library, if used properly, is invaluable in helping you develop discernment. It is rich ____96____ information that goes far beyond the ____97____ of any one text book or course. ____98____ your text book author does not make a particular____99____ clear and you feel the need ____100____ another description in greater detail or in other words, go to the library and check other books on the subject.<br>Select the most appropriate option to fill in the blank no.99</p>",
                    question_hi: "<p>99.<strong>Cloze Test:</strong> <br>The library, if used properly, is invaluable in helping you develop discernment. It is rich ____96____ information that goes far beyond the ____97____ of any one text book or course. ____98____ your text book author does not make a particular____99____ clear and you feel the need ____100____ another description in greater detail or in other words, go to the library and check other books on the subject. <br>Select the most appropriate option to fill in the blank no.99</p>",
                    options_en: ["<p>information</p>", "<p>entry</p>", 
                                "<p>explanation</p>", "<p>point</p>"],
                    options_hi: ["<p>information</p>", "<p>entry</p>",
                                "<p>explanation</p>", "<p>point</p>"],
                    solution_en: "<p>99.(d) point<br>&lsquo;Point&rsquo; means a particular fact, idea, or opinion that somebody expresses. The given passage talks about making a particular point clear. Hence, &lsquo;point&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) point<br>&lsquo;Point&rsquo; का अर्थ है एक विशेष तथ्य या विचार जो कोई व्यक्ति व्यक्त करता है। दिया गया passage एक विशेष बिंदु को स्पष्ट करने की बात करता है। अतः &lsquo;Point&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100.<strong>Cloze Test:</strong> <br>The library, if used properly, is invaluable in helping you develop discernment. It is rich ____96____ information that goes far beyond the ____97____ of any one text book or course. ____98____ your text book author does not make a particular____99____ clear and you feel the need ____100____ another description in greater detail or in other words, go to the library and check other books on the subject. <br>Select the most appropriate option to fill in the blank no.100</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong> <br>The library, if used properly, is invaluable in helping you develop discernment. It is rich ____96____ information that goes far beyond the ____97____ of any one text book or course. ____98____ your text book author does not make a particular____99____ clear and you feel the need ____100____ another description in greater detail or in other words, go to the library and check other books on the subject. <br>Select the most appropriate option to fill in the blank no.100</p>",
                    options_en: ["<p>for</p>", "<p>of</p>", 
                                "<p>to</p>", "<p>about</p>"],
                    options_hi: ["<p>for</p>", "<p>of</p>",
                                "<p>to</p>", "<p>about</p>"],
                    solution_en: "<p>100.(a) for<br>We generally use the preposition &lsquo;for&rsquo; after &lsquo;need&rsquo;. The given passage talks about the need for another description in greater detail. Hence, &lsquo;for&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(a) for<br>हम आम तौर पर &lsquo;need&rsquo; के बाद preposition &lsquo;for&rsquo; का उपयोग करते हैं। दिया गया passage अधिक विस्तार से एक और विवरण की आवश्यकता के बारे में बात करता है। इसलिए, \'for\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>