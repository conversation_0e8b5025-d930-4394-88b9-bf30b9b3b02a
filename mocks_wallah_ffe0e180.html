<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. What is Sukanya Samriddhi Yojana?</p>",
                    question_hi: "<p>1. सुकन्या समृद्धि योजना क्या है?</p>",
                    options_en: ["<p>A scheme to provide bicycles for girl studying in the 10th class.</p>", "<p>A scheme to develop self- defence skill in girl</p>", 
                                "<p>A scheme to provide skill that give employability to women.</p>", "<p>A small deposit scheme for girl child.</p>"],
                    options_hi: ["<p>10वीं कक्षा में पढ़ने वाली लड़कियों के लिए साइकिल उपलब्ध करवाने की योजना।</p>", "<p>लड़कियों में आत्मरक्षा कौशल विकसित करने की योजना।</p>",
                                "<p>महिलाओं को रोजगार देने वाले कौशल प्रदान करने की योजना।</p>", "<p>बालिकाओं के लिए एक लघु जमा योजना।</p>"],
                    solution_en: "<p>1.(d) <strong>Sukanya Samriddhi Yojana:</strong> Minimum deposit ₹ 250 and Maximum deposit ₹ 1.5 Lakh in a financial year. <strong>Launched on </strong>- 22 January 2015. It is a government of India backed a saving scheme targeted at the parents of girl children. The account can be opened anytime between the birth of a girl child and the time she attains 10 years age by the parent/guardian.</p>",
                    solution_hi: "<p>1.(d) <strong>सुकन्या समृद्धि योजना: </strong>एक वित्तीय वर्ष में न्यूनतम जमा राशि ₹ 250 और अधिकतम जमा राशि ₹ 1.5 लाख तक। <strong>योजना का शुभारम्भ हुआ -</strong> 22 जनवरी 2015 । यह भारत सरकार समर्थित एक बचत योजना है जो बालिकाओं के माता-पिता पर लक्षित है। यह खाता किसी भी समय बालिका के जन्म से 10 वर्ष की आयु होने तक माता-पिता /अभिभावक द्वारा खुलवाया जा सकता है।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Who is the inspiration behind Saansad Adarsh Gram Yojana?</p>",
                    question_hi: "<p>2. सांसद आदर्श ग्राम योजना के प्रेरणास्त्रोत कौन है?</p>",
                    options_en: ["<p>Lal Bahadur Shastri</p>", "<p>Jawaharlal Nehru</p>", 
                                "<p>Sardar Vallabh Bhai Patel</p>", "<p>Mahatma Gandhi</p>"],
                    options_hi: ["<p>लाल बहादुर शास्त्री</p>", "<p>जवाहरलाल नेहरू</p>",
                                "<p>सरदार बल्लभ भाई पटेल</p>", "<p>महात्मा गांधी</p>"],
                    solution_en: "<p>2.(d) <strong>Mahatma Gandhi. Saansad Adarsh Gram Yojana: Launched on</strong> October 11, 2014 (birth anniversary of former activist Loknayak Jayprakash Narayan) by the Prime Minister of India (Narendra Modi). <strong>Aim </strong>- To translate the comprehensive vision of Mahatma Gandhi about an ideal Indian village into reality.</p>",
                    solution_hi: "<p>2.(d) <strong>महात्मा गांधी। सांसद आदर्श ग्राम योजना :</strong> भारत के प्रधान मंत्री (नरेंद्र मोदी) द्वारा 11 अक्टूबर, 2014 (पूर्व कार्यकर्ता लोकनायक जयप्रकाश नारायण की जयंती) को <strong>शुरू की </strong>गई थी । <strong>उद्देश्य </strong>- एक आदर्श भारतीय गाँव के बारे में महात्मा गांधी की व्यापक दृष्टिकोण को वास्तविकता में बदलना।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. &lsquo;Samagra Shiksha&rsquo; is a scheme for school education, which extends from:</p>",
                    question_hi: "<p>3. \'समग्र शिक्षा\' स्कूली शिक्षा के लिए एक योजना है, यह कहाँ तक विस्तारित है?</p>",
                    options_en: ["<p>pre- nursery to class XII</p>", "<p>class I to X only</p>", 
                                "<p>class I to VIII only</p>", "<p>class I to XII only</p>"],
                    options_hi: ["<p>पूर्व -नर्सरी से XII कक्षा तक</p>", "<p>केवल कक्षा I से X तक</p>",
                                "<p>केवल कक्षा I से VIII</p>", "<p>केवल कक्षा I से XII तक</p>"],
                    solution_en: "<p>3.(a) <strong>Pre- nursery to class XII. Samagra Shiksha scheme - </strong>It was launched in 2018 and it subsumed the three schemes of Sarva Shiksha Abhiyan (SSA), Rashtriya Madhyamik Shiksha Abhiyan (RMSA) and Teacher Education (TE). <strong>Aim </strong>- To ensure that all children have access to quality education with an equitable and inclusive classroom environment which should take care of their diverse background, multilingual needs, different academic abilities and make them active participants in the learning process.</p>",
                    solution_hi: "<p>3.(a) <strong>पूर्व -नर्सरी से XII कक्षा तक। समग्र शिक्षा योजना -</strong> इसे 2018 में लॉन्च किया गया था और इसमें सर्व शिक्षा अभियान (SSA), राष्ट्रीय माध्यमिक शिक्षा अभियान (RMSA) और शिक्षक शिक्षा (TE) की तीन योजनाओं को शामिल किया गया था। <strong>उद्देश्य </strong>- यह सुनिश्चित करना कि सभी बच्चों को एक समान और समावेशी कक्षा वातावरण के साथ गुणवत्तापूर्ण शिक्षा प्राप्त हो, जिसमें उनकी विविध पृष्ठभूमि, बहुभाषी आवश्यकताओं, विभिन्न शैक्षणिक क्षमताओं का ध्यान रखा जाए और उन्हें सीखने की प्रक्रिया में सक्रिय भागीदार बनाया जाए।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Which of the following welfare schemes&rsquo; achievements have been recognised by the Guinness World Records ?</p>",
                    question_hi: "<p>4. निम्नलिखित में से किस कल्याणकारी योजना की उपलब्धियों को गिनीज वर्ल्ड रिकॉर्ड द्वारा मान्यता दी गई है?</p>",
                    options_en: ["<p>Pradhan Mantri Jan Dhan Yojana</p>", "<p>Pradhan Mantri Suraksha Bima Yojana</p>", 
                                "<p>Pradhan Mantri Kaushal Vikas Yojana</p>", "<p>Pradhan Mantri Krishi Sinchai Yojana</p>"],
                    options_hi: ["<p>प्रधानमंत्री जन धन योजना</p>", "<p>प्रधानमंत्री सुरक्षा बीमा योजना</p>",
                                "<p>प्रधानमंत्री कौशल विकास योजना</p>", "<p>प्रधानमंत्री कृषि सिंचाई योजना</p>"],
                    solution_en: "<p>4.(a) <strong>Pradhan Mantri Jan Dhan Yojana (PMJDY).</strong> Launched on - 28 August 2014 (by the Prime Minister of India, Narendra Modi). <strong>Objective </strong>- Access to banking facilities with at least one basic banking account for every household, financial literacy, access to credit, insurance and pension facility. <strong>Motto of PMJDY -</strong> &ldquo;Mera Khata Bhagya Vidhata&rdquo;. <strong>Pradhan Mantri Suraksha Bima Yojana</strong> is an accident insurance scheme in India launched by Prime Minister Narendra Modi in 2015. <strong>Pradhan Mantri Krishi Sinchayee Yojana </strong>launched to improve agricultural productivity in the country and ensure better utilization of resources.</p>",
                    solution_hi: "<p>4.(a) <strong>प्रधानमंत्री जन धन योजना (PMJDY)।</strong> 28 अगस्त 2014 (भारत के प्रधान मंत्री नरेंद्र मोदी द्वारा) को लॉन्च किया गया। <strong>उद्देश्य </strong>- प्रत्येक परिवार के लिए कम से कम एक बुनियादी बैंकिंग खाते के साथ बैंकिंग सुविधाओं तक पहुंच, वित्तीय साक्षरता, ऋण तक पहुंच, बीमा और पेंशन सुविधा उपलब्ध कराना। <strong>PMJDY का आदर्श वाक्य - </strong>\"मेरा खाता भाग्य विधाता\"। <strong>प्रधान मंत्री सुरक्षा बीमा योजना</strong> 2015 में प्रधान मंत्री नरेंद्र मोदी द्वारा शुरू की गई भारत में एक दुर्घटना बीमा योजना है। देश में कृषि उत्पादकता में सुधार और संसाधनों का बेहतर उपयोग सुनिश्चित करने के लिए <strong>प्रधानमंत्री कृषि सिंचाई योजना</strong> शुरू की गई।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. &lsquo;Operation Greens&rsquo; is a government scheme for</p>",
                    question_hi: "<p>5. \'ऑपरेशन ग्रीन\'_________एक सरकारी योजना है।</p>",
                    options_en: ["<p>General price levels of crops</p>", "<p>Supply stablisation of TOP crops (Tomato Onion Potato)</p>", 
                                "<p>development of bamboo crops</p>", "<p>research and investment in crop education</p>"],
                    options_hi: ["<p>फसलों के सामान्य मूल्य स्तर</p>", "<p>TOP (टमाटर प्याज आलू) फसलों की आपूर्ति</p>",
                                "<p>बांस की फसलों का विकास</p>", "<p>फसल शिक्षा में अनुसंधान और निवेश</p>"],
                    solution_en: "<p>5.(b) <strong>Supply stablisation of TOP crops (Tomato Onion Potato). </strong>It was a scheme designed for integrated development of Tomato, Onion and Potato (TOP) value chain. Operation Greens is a pricing scheme aimed at ensuring that farmers get the right price for their produce. It is approved by the Ministry of Food Processing Industries in the year 2018-2019.</p>",
                    solution_hi: "<p>5.(b) <strong>TOP फसलों की आपूर्ति (टमाटर प्याज आलू)। </strong>यह टमाटर, प्याज और आलू (TOP) मूल्य श्रृंखला के एकीकृत विकास के लिए तैयार की गई योजना थी। ऑपरेशन ग्रीन एक मूल्य निर्धारण योजना है जिसका उद्देश्य यह सुनिश्चित करना है कि किसानों को उनकी उपज का सही मूल्य मिले। इसे वर्ष 2018-2019 में खाद्य प्रसंस्करण उद्योग मंत्रालय द्वारा अनुमोदित किया गया है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. The &lsquo;SATH-E&rsquo; project is associated with which of the following fields ?</p>",
                    question_hi: "<p>6. &lsquo;SATH-E&rsquo;\' परियोजना निम्नलिखित में से किस क्षेत्र से संबंधित है?</p>",
                    options_en: ["<p>Communication</p>", "<p>Education</p>", 
                                "<p>Agriculture</p>", "<p>Transportation</p>"],
                    options_hi: ["<p>संचार</p>", "<p>शिक्षा</p>",
                                "<p>कृषि</p>", "<p>परिवहन</p>"],
                    solution_en: "<p>6.(b) <strong>Education. Project SATH-E,</strong> &lsquo;Sustainable Action for Transforming Human Capital-Education&rsquo;, was launched in 2017 to identify and build three &lsquo;role model&rsquo; States for the school education sector. Jharkhand, Odisha and Madhya Pradesh were chosen as role model states.</p>",
                    solution_hi: "<p>6.(b) <strong>शिक्षा। प्रोजेक्ट SATH-E,</strong> \'मानव पूंजी-शिक्षा में परिवर्तन के लिए सतत कार्रवाई\', स्कूल शिक्षा क्षेत्र के लिए तीन \'रोल मॉडल\' राज्यों की पहचान करने और निर्माण करने के लिए 2017 में शुरू की गई थी। झारखंड, ओडिशा और मध्य प्रदेश को रोल मॉडल राज्य के रूप में चुना गया था।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. When was the Swachh Bharat Mission launched?</p>",
                    question_hi: "<p>7. स्वच्छ भारत मिशन कब शुरू किया गया था ?</p>",
                    options_en: ["<p>15 August 2015</p>", "<p>2 October 2014</p>", 
                                "<p>15 August 2014</p>", "<p>2 October 2015</p>"],
                    options_hi: ["<p>15 अगस्त 2015</p>", "<p>2 अक्टूबर 2014</p>",
                                "<p>15 अगस्त 2014</p>", "<p>2 अक्टूबर 2015</p>"],
                    solution_en: "<p>7.(b)<strong> 2 october 2014. Launched by Narendra Modi</strong> (Prime minister). It is a country-wide campaign initiated by the Government of India in 2014 to eliminate open defecation and improve solid waste management. The<strong> List of Schemes and Programmes </strong>Launched by Hon&rsquo;ble Prime Minister, Narendra Modi are Make in India (25th September 2014), digital india (1st July 2015), skill india (15th July 2015), smart cities (29th April 2015), Ujawala yojana (1 May 2016), Pradhan Mantri Fasal Bima Yojana (18 February 2016) and Pradhan Mantri Jeevan Jyoti Bima Yojana (9th May 2015) etc.</p>",
                    solution_hi: "<p>7.(b) <strong>2 अक्टूबर 2014 । इसे नरेंद्र मोदी (प्रधानमंत्री</strong>) द्वारा शुरू किया गया था। यह खुले में शौच को खत्म करने और ठोस अपशिष्ट प्रबंधन में सुधार के लिए 2014 में भारत सरकार द्वारा शुरू किया गया एक देशव्यापी अभियान है। माननीय प्रधानमंत्री नरेंद्र मोदी द्वारा शुरू की गई <strong>योजनाओं और कार्यक्रमों की सूची</strong> इस प्रकार है: मेक इन इंडिया (25 सितंबर 2014), डिजिटल इंडिया (1 जुलाई 2015), स्किल इंडिया (15 जुलाई 2015), स्मार्ट सिटी (29 अप्रैल 2015), उज्ज्वला योजना (1 मई 2016), प्रधानमंत्री फसल बीमा योजना (18 फरवरी 2016) और प्रधानमंत्री जीवन ज्योति बीमा योजना (9 मई 2015) आदि।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Which year was the Beti Bachao, Beti Padhao yojana launched?</p>",
                    question_hi: "<p>8. बेटी बचाओ, बेटी पढाओ योजना किस वर्ष शुरू की गई थी?</p>",
                    options_en: ["<p>2017</p>", "<p>2015</p>", 
                                "<p>2014</p>", "<p>2016</p>"],
                    options_hi: ["<p>2017</p>", "<p>2015</p>",
                                "<p>2014</p>", "<p>2016</p>"],
                    solution_en: "<p>8.(b) <strong>2015</strong>. It was launched by the honorable Prime Minister Narendra Modi on 22 January 2015 in Panipat,<strong> Haryana.</strong> It <strong>aims </strong>to address the issues of declining girl child ratio and protection and empowerment of girl child.</p>",
                    solution_hi: "<p>8.(b) <strong>2015 </strong>। इसका शुभारंभ माननीय प्रधानमंत्री नरेंद्र मोदी द्वारा 22 जनवरी 2015 को हरियाणा के पानीपत में किया गया था। इसका <strong>उद्देश्य </strong>बालिका शिशु अनुपात में गिरावट और बालिकाओं की सुरक्षा और सशक्तिकरण के मुद्दों का समाधान करना है।</p>",
                    correct: "b",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Who launched the Sukanya Samridhi Yojana?</p>",
                    question_hi: "<p>9. सुकन्या समृद्धि योजना किसने शुरू की?</p>",
                    options_en: ["<p>ID Deve Gowda</p>", "<p>Atal Bihari Vajpayee</p>", 
                                "<p>Manmohan Singh</p>", "<p>Narendra Modi</p>"],
                    options_hi: ["<p>आईडी देवेगौड़ा</p>", "<p>बिहारी वाजपेयी</p>",
                                "<p>मनमोहन सिंह</p>", "<p>नरेंद्र मोदी</p>"],
                    solution_en: "<p>9.(d) <strong>Narendra Modi. Sukanya Samridhi Yojana</strong> (SSY): It was launched by Prime Minister Narendra Modi on January 22, 2015 as part of the Beti Bachao, Beti Padhao campaign. The main objective of this scheme is to secure the future of the girl child and provide them with relevant opportunities to achieve financial prosperity. This scheme is available from the time of birth of the girl child till 10 years of her age. <strong>Prime Minister of India -</strong> H.D. Deve Gowda (1996-97), Atal Bihari Vajpyee (1999-2004), Manmohan Singh (2004-14).</p>",
                    solution_hi: "<p>9.(d) <strong>नरेंद्र मोदी। सुकन्या समृद्धि योजना (SSY):</strong> इसे प्रधानमंत्री नरेंद्र मोदी ने 22 जनवरी 2015 को बेटी बचाओ, बेटी पढ़ाओ अभियान के हिस्से के रूप में शुभारम्भ किया था। इस योजना का मुख्य उद्देश्य बेटियों के भविष्य को सुरक्षित बनाना है और उन्हें वित्तीय समृद्धि प्राप्त करने के लिए संबंधित अवसर प्रदान करना है। यह योजना बेटी के जन्म के समय से लेकर उसकी 10 वर्ष उम्र तक के लिए योजना है। <strong>भारत के प्रधान मंत्री - </strong>एच.डी. देवेगौड़ा (1996-97), अटल बिहारी वाजपेई (1999-2004), मनमोहन सिंह (2004-14)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Pradhan Mantri Swasthya Suraksha Yojana (PMSSY) was launched in the year</p>",
                    question_hi: "<p>10. प्रधान मंत्री स्वास्थ्य सुरक्षा योजना (PMSSY) किस वर्ष में शुरू की गई थी ?</p>",
                    options_en: ["<p>2004</p>", "<p>2005</p>", 
                                "<p>2006</p>", "<p>2003</p>"],
                    options_hi: ["<p>2004</p>", "<p>2005</p>",
                                "<p>2006</p>", "<p>2003</p>"],
                    solution_en: "<p>10.(d) <strong>2003. PMSSY (Pradhan Mantri Swasthya Suraksha Yojana) - </strong>It was introduced in 2003 to correct imbalances in the availability of tertiary healthcare services and improve the quality of medical education. The scheme has two components (i) setting up of new AIIMS, and (ii) upgradation of selected Government Medical College Institutions (GMCIs). <strong>Nodal Ministry -</strong> Ministry of Health and Family Welfare.</p>",
                    solution_hi: "<p>10.(d) <strong>2003 । PMSSY (प्रधानमंत्री स्वास्थ्य सुरक्षा योजना) - </strong>इसे तृतीयक स्वास्थ्य सेवाओं की उपलब्धता में असंतुलन को सही करने और चिकित्सा शिक्षा की गुणवत्ता में सुधार के लिए 2003 में शुरू किया गया था। इस योजना के दो घटक हैं (i) नए AIIMS की स्थापना, और (ii) चयनित सरकारी मेडिकल कॉलेज संस्थानों (GMCIs) का अपग्रेडेशन। <strong>नोडल मंत्रालय</strong> - स्वास्थ्य और परिवार कल्याण मंत्रालय।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which watershed development and management program is being implemented by the <br>central and state governments?</p>",
                    question_hi: "<p>11. केंद्र और राज्य सरकारों द्वारा कौन सा जल विभाजक विकास और प्रबंधन कार्यक्रम लागू किया जा रहा है?</p>",
                    options_en: ["<p>Hariyali</p>", "<p>Arvary Pani Sansad</p>", 
                                "<p>Neeru-Meeru</p>", "<p>Desh Sudhar</p>"],
                    options_hi: ["<p>हरियाली</p>", "<p>अरवरी जल संसद</p>",
                                "<p>नीरू-मीरू</p>", "<p>देश सुधार</p>"],
                    solution_en: "<p>11.(a) <strong>Haryali</strong>: A watershed development project sponsored by the Central Government. <strong>Aims </strong>- Enabling the rural population to conserve water for drinking, irrigation, fisheries and afforestation. The Project is being executed by Gram Panchayats with people&rsquo;s participation. <strong>Other watershed projects - Neeru-Meeru</strong> (Water and you) programme in Andhra Pradesh. <strong>Arvary Pani Sansad </strong>in (Alwar-Rajasthan).</p>",
                    solution_hi: "<p>11.(a) <strong>हरियाली</strong>: केंद्र सरकार द्वारा प्रायोजित एक जलग्रहण विकास परियोजना है । <strong>उद्देश्य </strong>- ग्रामीण आबादी को पीने, सिंचाई, मछली पालन और वनीकरण के लिए जल के संरक्षण में सक्षम बनाना। परियोजना को ग्राम पंचायतों द्वारा लोगों की भागीदारी से क्रियान्वित किया जा रहा है। <strong>अन्य जलविभाजक परियोजनाएँ - </strong>आंध्र प्रदेश में <strong>नीरू-मीरू</strong> (Water and you ) कार्यक्रम। <strong>अरवारी पानी संसद </strong>(अलवर-राजस्थान)।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which of the following rural housing schemes by the Government of India is re-structured into Pradhan Mantri Gramin Awas Yojana?</p>",
                    question_hi: "<p>12. भारत सरकार द्वारा निम्नलिखित में से किस ग्रामीण आवास योजना को प्रधानमंत्री ग्रामीण आवास योजना में पुनर्गठित किया गया है?</p>",
                    options_en: ["<p>Rajiv Awas Yojana</p>", "<p>Deendayal Antyodaya Yojana</p>", 
                                "<p>Jawahar Gram Samridhi Yojana</p>", "<p>Indira Awas Yojana</p>"],
                    options_hi: ["<p>राजीव आवास योजना</p>", "<p>दीनदयाल अंत्योदय योजना</p>",
                                "<p>जवाहर ग्राम समृद्धि योजना</p>", "<p>इंदिरा आवास योजना</p>"],
                    solution_en: "<p>12.(d)<strong> Indira Awas Yojana: </strong>Launched - 1985 by Rajiv Gandhi. It was restructured in 2015. The cost of unit assistance is shared between Central and State Governments in the ratio 60:40 in plain areas and 90:10 for North Eastern and hilly states. <strong>Pradhan Mantri Awas Yojana (Urban):</strong> Launched - 25 June 2015. Jawahar Gram Samridhi Yojana (JGSY): <strong>Launched </strong>- 1 April 1999.</p>",
                    solution_hi: "<p>12.(d) <strong>इंदिरा आवास योजना:</strong> लॉन्च - 1985, राजीव गांधी द्वारा। इसे 2015 में पुनर्गठित किया गया था। यूनिट सहायता की लागत केंद्र और राज्य सरकारों के बीच मैदानी क्षेत्रों में 60:40 और उत्तर पूर्वी और पहाड़ी राज्यों के लिए 90:10 के अनुपात में साझा की जाती है। <strong>प्रधानमंत्री आवास योजना (शहरी):</strong> लॉन्च - 25 जून 2015। जवाहर ग्राम समृद्धि योजना (JGSY): लॉन्च - 1 अप्रैल 1999 ।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. In which year was the Setu Bharatam Programme launched?</p>",
                    question_hi: "<p>13. सेतु भारतम कार्यक्रम किस वर्ष शुरू किया गया था?</p>",
                    options_en: ["<p>2016</p>", "<p>2017</p>", 
                                "<p>2013</p>", "<p>2018</p>"],
                    options_hi: ["<p>2016</p>", "<p>2017</p>",
                                "<p>2013</p>", "<p>2018</p>"],
                    solution_en: "<p>13.(a) <strong>2016</strong>. The Setu Bharatam program launched by Prime Minister Narendra Modi in New Delhi. <strong>Aims </strong>- Make all national highways free of railway level crossing to prevent the frequent accidents and loss of lives. <strong>Ministry </strong>- Ministry of Road Transport and Highways. Target Year of completion of Setu Bharatam - 2019.</p>",
                    solution_hi: "<p>13.(a) <strong>2016</strong>। प्रधानमंत्री नरेंद्र मोदी द्वारा नई दिल्ली में सेतु भारतम कार्यक्रम लॉन्च किया गया। <strong>उद्देश्य </strong>- लगातार होने वाली दुर्घटनाओं और जानमाल की हानि को रोकने के लिए सभी राष्ट्रीय राजमार्गों को रेलवे क्रॉसिंग से मुक्त बनाना। <strong>मंत्रालय </strong>- सड़क परिवहन और राजमार्ग मंत्रालय। सेतु भारतम के पूरा होने का लक्ष्य वर्ष - 2019।</p>",
                    correct: "a",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. The Bharatmala Pariyojana is associated with:</p>",
                    question_hi: "<p>14. भारतमाला परियोजना किस से संबंधित है?</p>",
                    options_en: ["<p>Ports</p>", "<p>Telecom</p>", 
                                "<p>Railways</p>", "<p>Highways</p>"],
                    options_hi: ["<p>बन्दरगाह</p>", "<p>दूरसंचार</p>",
                                "<p>रेलवे</p>", "<p>राजमार्ग</p>"],
                    solution_en: "<p>14.(d) <strong>Highways</strong>. It was launched on 31 July 2015. Target - 10 lakh crore rupees, under this, apart from new highways, those projects which are still incomplete will also be completed. And connectivity between Char Dham Kedarnath, Badrinath, Yamunotri and Gangotri will be improved.</p>",
                    solution_hi: "<p>14.(d) <strong>राजमार्ग</strong>। इसे 31 जुलाई 2015 को शुरू किया गया था। लक्ष्य - 10 लाख करोड़ रुपये, इसके तहत नए राजमार्ग के अलावा उन परियोजनाओं को भी पूरा किया जाएगा जो अब तक अधूरे हैं। तथा चार धाम केदारनाथ, बद्रीनाथ, यमुनोत्री और गंगोत्री के बीच संयोजकता बेहतर की जाएगी।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. The National Rural Health Mission was launched in the year:</p>",
                    question_hi: "<p>15. राष्ट्रीय ग्रामीण स्वास्थ्य मिशन किस वर्ष में शुरू किया गया था:</p>",
                    options_en: ["<p>2012</p>", "<p>2007</p>", 
                                "<p>2002</p>", "<p>2005</p>"],
                    options_hi: ["<p>2012</p>", "<p>2007</p>",
                                "<p>2002</p>", "<p>2005</p>"],
                    solution_en: "<p>15.(d) <strong>2005</strong>. National Rural Health Mission (NRHM), <strong>Objective </strong>- Provide accessible, affordable and quality health care to the rural population, especially the vulnerable groups. <strong>NRHM </strong>focuses on Reproductive, Maternal, Newborn, Child Health and Adolescent (RMNCH+A) Services. NHM has a <strong>Mission Steering Group</strong> headed by the Union Minister for Health &amp; Family Welfare to provide guidance to the mission. National Urban Health Mission (Launched in 2013).</p>",
                    solution_hi: "<p>15.(d) <strong>2005</strong>। राष्ट्रीय ग्रामीण स्वास्थ्य मिशन (NRHM), <strong>उद्देश्य </strong>- ग्रामीण आबादी, विशेषकर कमजोर वर्गों को सुलभ, सस्ती और गुणवत्तापूर्ण स्वास्थ्य देखभाल प्रदान करना। <strong>NRHM </strong>प्रजनन, मातृ, नवजात शिशु, बाल स्वास्थ्य और किशोर (RMNCH+A) सेवाओं पर ध्यान केंद्रित किया गया है। मिशन को मार्गदर्शन प्रदान करने के लिए NHM के पास केंद्रीय स्वास्थ्य और परिवार कल्याण मंत्री की अध्यक्षता में एक <strong>मिशन संचालन समूह ( Mission Steering Group) </strong>है। राष्ट्रीय शहरी स्वास्थ्य मिशन (2013 में शुरू किया गया)।</p>",
                    correct: "d",
                    pos_marks: 1.0,
                    neg_marks: 0.33
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>