<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the set in which the numbers are related in the same way as are the numbers of the following set. <br>3 &mdash; 20 &mdash; 5 <br>4 &mdash; 17 &mdash; 3<br>NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>1. उस समुच्चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार से संबंधित हैं जिस प्रकार निम्नलिखित समुच्चय की संख्याएँ संबंधित हैं। <br>3 &mdash; 20 &mdash; 5 <br>4 &mdash; 17 &mdash; 3 <br>नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 को लें - 13 पर संक्रियाएं जैसे 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: [
                        "<p>8 &mdash; 36 &mdash; 11</p>",
                        "<p>9 &mdash; 77 &mdash; 8</p>",
                        "<p>11 &mdash; 28 &mdash; 5</p>",
                        "<p>5 &mdash; 28 &mdash; 9</p>"
                    ],
                    options_hi: [
                        "<p>8 &mdash; 36 &mdash; 11</p>",
                        "<p>9 &mdash; 77 &mdash; 8</p>",
                        "<p>11 &mdash; 28 &mdash; 5</p>",
                        "<p>5 &mdash; 28 &mdash; 9</p>"
                    ],
                    solution_en: "<p>1.(b)<br><strong>Logic:- </strong>(1<sup>st</sup>no.&times; 3<sup>rd</sup>no.) + 5 = 2<sup>nd</sup>no.<br>(3 - 20 - 5) :- (3 &times;&nbsp;5) + 5 &rArr; 15 + 5 = 20<br>(4 - 17 - 3) :- (4 &times; 3) + 5 &rArr; 12 + 5 = 17<br>Similarly <br>(9 - 77 - 8) :- (9 &times; 8) + 5 &rArr; 72 + 5 = 77</p>",
                    solution_hi: "<p>1.(b)<br><strong>तर्क :-</strong> (पहली संख्या &times; तीसरी संख्या) + 5 = दूसरी संख्या <br>(3 - 20 - 5) :- (3 &times; 5) + 5 &rArr; 15 + 5 = 20<br>(4 - 17 - 3) :- (4 &times; 3) + 5 &rArr; 12 + 5 = 17<br>इसी प्रकार, <br>(9 - 77 - 8) :- (9 &times; 8) + 5 &rArr; 72 + 5 = 77</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. A paper is folded and cut as shown below. How will it appear when unfolded ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536215.png\" alt=\"rId4\" width=\"304\" height=\"78\"></p>",
                    question_hi: "<p>2. नीचे दर्शाए गए अनुसार एक कागज को मोड़कर काटा जाता है। खोलने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536215.png\" alt=\"rId4\" width=\"304\" height=\"78\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536352.png\" alt=\"rId5\" width=\"91\" height=\"87\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536455.png\" alt=\"rId6\" width=\"91\" height=\"94\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536565.png\" alt=\"rId7\" width=\"89\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536672.png\" alt=\"rId8\" width=\"91\" height=\"86\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536352.png\" alt=\"rId5\" width=\"93\" height=\"89\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536455.png\" alt=\"rId6\" width=\"92\" height=\"95\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536565.png\" alt=\"rId7\" width=\"91\" height=\"92\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536672.png\" alt=\"rId8\" width=\"93\" height=\"88\"></p>"
                    ],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536565.png\" alt=\"rId7\" width=\"89\" height=\"90\"></p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536565.png\" alt=\"rId7\" width=\"89\" height=\"90\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. The same operation(s) are followed in all the given number pairs except one. Find that odd number pair.<br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>3. दिए गए संख्या युग्मों में से एक को छोड़कर अन्य सभी में समान संक्रिया/संक्रियाओं का अनुसरण किया गया है। वह असंगत संख्या युग्म ज्ञात कीजिए।<br>(नोट : संख्याओं को उसके संघटक अंकों में खंडित किए बिना संक्रियाएँ पूर्णांकों पर की जानी चाहिए। उदाहरण के लिए 13- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/घटाना/गुणा इत्यादि 13 पर ही की जानी चाहिए। 13 को 1 और 3 में खंडित करना और फिर 1 और 3 पर गणितीय संक्रियाएँ करना वर्जित है।)</p>",
                    options_en: [
                        "<p>19 : 3</p>",
                        "<p>55 : 9</p>",
                        "<p>49 : 8</p>",
                        "<p>24 : 4</p>"
                    ],
                    options_hi: [
                        "<p>19 : 3</p>",
                        "<p>55 : 9</p>",
                        "<p>49 : 8</p>",
                        "<p>24 : 4</p>"
                    ],
                    solution_en: "<p>3.(d) <strong>Logic :</strong> (1st number - 1) &divide; 6 = 2nd number <br>19 : 3 :- (19 - 1) &divide; 6 = 18 &divide; 6 = 3<br>55 : 9 :- (55 - 1) &divide; 6 = 54 &divide; 6 = 9<br>49 : 8 :- (49 -1) &divide; 6 = 48 &divide; 6 = 8<br>But<br>24 : 4 :- (24 - 1) &divide; 6 = 23 &divide; 6 = 3.833 (&ne; 4)</p>",
                    solution_hi: "<p>3.(d) <strong>तर्क : </strong>(पहली संख्या - 1) &divide; 6 = दूसरी संख्या <br>19 : 3 :- (19 - 1) &divide; 6 = 18 &divide; 6 = 3<br>55 : 9 :- (55 - 1) &divide; 6 = 54 &divide; 6 = 9<br>49 : 8 :- (49 -1) &divide; 6 = 48 &divide; 6 = 8<br>लेकिन <br>24 : 4 :- (24 - 1) &divide; 6 = 23 &divide; 6 = 3.833 (&ne; 4)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group? <br>(Note : The odd letter-cluster is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>4. अंग्रेजी वर्णमाला क्रम पर आधारित, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। किस अक्षर-समूह का संबंध उस समूह से नहीं है?<br>(नोट : असंगत अक्षर-समूह, व्यंजनों/नोंस्वरों की संख्या या इस अक्षर-समूह में उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>IFM</p>",
                        "<p>GDK</p>",
                        "<p>TQX</p>",
                        "<p>OLR </p>"
                    ],
                    options_hi: [
                        "<p>IFM</p>",
                        "<p>GDK</p>",
                        "<p>TQX</p>",
                        "<p>OLR</p>"
                    ],
                    solution_en: "<p>4.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536914.png\" alt=\"rId10\" width=\"115\" height=\"60\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537029.png\" alt=\"rId11\" width=\"111\" height=\"63\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537128.png\" alt=\"rId12\" width=\"117\" height=\"61\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537252.png\" alt=\"rId13\" width=\"116\" height=\"60\"></p>",
                    solution_hi: "<p>4.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582536914.png\" alt=\"rId10\" width=\"115\" height=\"60\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537029.png\" alt=\"rId11\" width=\"111\" height=\"63\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537128.png\" alt=\"rId12\" width=\"117\" height=\"61\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537252.png\" alt=\"rId13\" width=\"116\" height=\"60\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. In a certain code language, \'PLAYER\' is written as \'CNRSFZ\' and \'LIQUID\' is written as \'SKNEJV\'. How will \'RECORD\' be written in that language ?</p>",
                    question_hi: "<p>5. किसी निश्चित कोड भाषा में \'PLAYER\' को \'CNRSFZ\' के रूप में लिखा गया है और \'LIQUID\' को \'SKNEJV\' के रूप में लिखा गया है। उसी भाषा में \'RECORD\' को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>ETGPSP</p>",
                        "<p>ETGESP</p>",
                        "<p>EGTPES</p>",
                        "<p>EGTESP</p>"
                    ],
                    options_hi: [
                        "<p>ETGPSP</p>",
                        "<p>ETGESP</p>",
                        "<p>EGTPES</p>",
                        "<p>EGTESP</p>"
                    ],
                    solution_en: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537359.png\" alt=\"rId14\" width=\"145\" height=\"75\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537465.png\" alt=\"rId15\" width=\"146\" height=\"78\"><br>Similarly, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537587.png\" alt=\"rId16\" width=\"152\" height=\"80\"></p>",
                    solution_hi: "<p>5.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537359.png\" alt=\"rId14\" width=\"145\" height=\"75\">, <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537465.png\" alt=\"rId15\" width=\"146\" height=\"78\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537587.png\" alt=\"rId16\" width=\"152\" height=\"80\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language, \'APPLE\' is coded as 250 and \'BERRY\' is coded as 340. How will \'LITCHI\' be coded in the same language ?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में, \'APPLE\' को 250 के रूप में कूटबद्ध किया जाता है और \'BERRY\' को 340 के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'LITCHI\' को किस रूप में कूटबद्ध किया जाएगा?</p>",
                    options_en: [
                        "<p>305</p>",
                        "<p>306</p>",
                        "<p>350</p>",
                        "<p>330</p>"
                    ],
                    options_hi: [
                        "<p>305</p>",
                        "<p>306</p>",
                        "<p>350</p>",
                        "<p>330</p>"
                    ],
                    solution_en: "<p>6.(a) <strong>Logic :-</strong> (Sum of the place value of letter) &times; 5<br>APPLE :- (1 + 16 + 16 + 12 + 5) &times; 5 &rArr; (50) &times; 5 = 250<br>BERRY :- (2 + 5 + 18 + 18 + 25) &times; 5 &rArr; (68) &times; 5 = 340<br>Similarly,<br>LITCHI :- (12 + 9 + 20 + 3 + 8 + 9) &times; 5 &rArr; (61) &times; 5 = 305</p>",
                    solution_hi: "<p>6.(a) <strong>तर्क :-</strong> (अक्षर के स्थानीय मान का योग) &times; 5<br>APPLE :- (1 + 16 + 16 + 12 + 5) &times; 5 &rArr; (50) &times; 5 = 250<br>BERRY :- (2 + 5 + 18 + 18 + 25) &times; 5 &rArr; (68) &times; 5 = 340<br>इसी प्रकार,<br>LITCHI :- (12 + 9 + 20 + 3 + 8 + 9) &times; 5 &rArr; (61) &times; 5 = 305</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. What should come in place of the question mark (?) in the given series?<br>2, 3, 6, 12, 10, 21, 14, ?</p>",
                    question_hi: "<p>7. दी गई श्रृंखला में प्रश्नवाचक चिह्न (?) के स्थान पर क्या आना चाहिए ? <br>2, 3, 6, 12, 10, 21, 14, ?</p>",
                    options_en: [
                        "<p>25</p>",
                        "<p>27</p>",
                        "<p>29 </p>",
                        "<p>30</p>"
                    ],
                    options_hi: [
                        "<p>25</p>",
                        "<p>27</p>",
                        "<p>29</p>",
                        "<p>30</p>"
                    ],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537728.png\" alt=\"rId17\" width=\"273\" height=\"79\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537728.png\" alt=\"rId17\" width=\"273\" height=\"79\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. Three statements are given, followed by Three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All calculators are dustbins.<br>All complexes are dustbins.<br>Some adamants are cameras.<br><strong>Conclusions :</strong><br>I. Some calculators are complexes.<br>II. Some dustbins are calculators.<br>III. Some dustbins are adamants.</p>",
                    question_hi: "<p>8. तीन कथन दिए गए हैं जिनके बाद I, II और III से संख्&zwj;यांकित तीन निष्कर्ष दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन सा/से निष्कर्ष, कथनों का तार्किक रूप से अनुसरण करता/करते है/हैं?<br><strong>कथन :</strong><br>सभी कैलकुलेटर, कूड़ेदान हैं।<br>सभी कॉम्प्लेक्स, कूड़ेदान हैं।<br>कुछ मणि, कैमरे हैं।<br><strong>निष्कर्ष :</strong><br>I. कुछ कैलकुलेटर, कॉम्प्लेक्स हैं।<br>II. कुछ कूड़ेदान, कैलकुलेटर हैं।<br>III. कुछ कूड़ेदान, मणि हैं।</p>",
                    options_en: [
                        "<p>Both conclusions I and II follow</p>",
                        "<p>Both conclusions II and III follow</p>",
                        "<p>Only conclusion II follows</p>",
                        "<p>Only conclusion III follows</p>"
                    ],
                    options_hi: [
                        "<p>निष्कर्ष I और II दोनों अनुसरण करते हैं</p>",
                        "<p>निष्कर्ष II और III दोनों अनुसरण करते हैं</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है</p>",
                        "<p>केवल निष्कर्ष III अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582537865.png\" alt=\"rId18\" width=\"279\" height=\"93\"><br>Only conclusion II follows.</p>",
                    solution_hi: "<p>8.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538028.png\" alt=\"rId19\" width=\"314\" height=\"112\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Select the option that represents the letters that, when sequentially placed from left to right in the blanks below, will complete the letter series.<br>I _ _ I G O _ N D _ G O I N _ _ G O _ N _ I _ _</p>",
                    question_hi: "<p>9. उस विकल्प का चयन कीजिए जो उन अक्षरों को निरूपित करता है, जिन्हें नीचे दिए गए रिक्त स्थानों में क्रमिक रूप से बाएं से दाएं रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी।<br>I _ _ IGO_ND_GOIN _ _ GO_N_I _ _</p>",
                    options_en: [
                        "<p>DNIGOINDNO</p>",
                        "<p>NDIIDIIDGO</p>",
                        "<p>INGODIIGON</p>",
                        "<p>NDIDIIIGON</p>"
                    ],
                    options_hi: [
                        "<p>DNIGOINDNO</p>",
                        "<p>NDIIDIIDGO</p>",
                        "<p>INGODIIGON</p>",
                        "<p>NDIDIIIGON</p>"
                    ],
                    solution_en: "<p>9.(b) <br>I <span style=\"text-decoration: underline;\"><strong>ND</strong></span> I G O / <span style=\"text-decoration: underline;\"><strong>I</strong></span> N D <span style=\"text-decoration: underline;\"><strong>I</strong></span> G O / I N <span style=\"text-decoration: underline;\"><strong>D I</strong></span>G O / <span style=\"text-decoration: underline;\"><strong>I</strong></span>N<span style=\"text-decoration: underline;\"><strong>D</strong></span> I <span style=\"text-decoration: underline;\"><strong>GO</strong></span></p>",
                    solution_hi: "<p>9.(b)<br>I <span style=\"text-decoration: underline;\"><strong>ND</strong></span> I G O / <span style=\"text-decoration: underline;\"><strong>I</strong></span> N D <span style=\"text-decoration: underline;\"><strong>I</strong></span> G O / I N <span style=\"text-decoration: underline;\"><strong>D I</strong></span>G O / <span style=\"text-decoration: underline;\"><strong>I</strong></span>N<span style=\"text-decoration: underline;\"><strong>D</strong></span> I <span style=\"text-decoration: underline;\"><strong>GO</strong></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a certain language, <br>A# B means A is the sister of B, <br>A @ B means A is the son of B, <br>A &amp; B means A is the wife of B and <br>A% B means A is the father of B. <br>E &amp; P @ D % K # L<br>Based on the above, how is E related to K?</p>",
                    question_hi: "<p>10. एक निश्चित भाषा में,<br>A # B का अर्थ A, B की बहन है,<br>A @ B का अर्थ A, B का बेटा है, <br>A &amp; B का अर्थ A, B की पत्नी है और <br>A % B का अर्थ A, B का पिता है। <br>E &amp; P @ D % K # L<br>उपरोक्त के आधार पर, E का K से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Brother\'s wife</p>",
                        "<p>Mother\'s mother</p>",
                        "<p>Mother</p>",
                        "<p>Sister</p>"
                    ],
                    options_hi: [
                        "<p>भाई की पत्नी</p>",
                        "<p>माँ की माँ</p>",
                        "<p>माँ</p>",
                        "<p>बहन</p>"
                    ],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538192.png\" alt=\"rId20\" width=\"218\" height=\"90\"><br>E is the wife of K&rsquo;s brother.</p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538192.png\" alt=\"rId20\" width=\"218\" height=\"90\"><br>E, K के भाई की पत्नी है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. What should come in place of the question mark (?) in the given series based on the English alphabetical order?<br>SRQ, ONM, KJI, GFE, ?</p>",
                    question_hi: "<p>11. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई शृंखला में प्रश्न-चिह्न (?) के स्&zwj;थान पर क्या आएगा?<br>SRQ, ONM, KJI, GFE, ?</p>",
                    options_en: [
                        "<p>BAZ</p>",
                        "<p>DCB</p>",
                        "<p>CBA</p>",
                        "<p>ABC</p>"
                    ],
                    options_hi: [
                        "<p>BAZ</p>",
                        "<p>DCB</p>",
                        "<p>CBA</p>",
                        "<p>ABC</p>"
                    ],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538344.png\" alt=\"rId21\" width=\"247\" height=\"87\"></p>",
                    solution_hi: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538344.png\" alt=\"rId21\" width=\"247\" height=\"87\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Six symbols %, &amp;, ?, +, $ and @ are written on different faces of a dice. Two positions of this dice are sown in the figure below. Find the symbol on the face opposite to the one having @.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538516.png\" alt=\"rId22\" width=\"182\" height=\"89\"></p>",
                    question_hi: "<p>12. एक पासे के विभिन्न फलकों पर छह प्रतीक %, &amp;, ?, +, $ और @ अंकित हैं। नीचे दी गई आकृति में इस पासे की दो स्थितियाँ दर्शाई गई हैं। @ वाले फलक के विपरीत फलक पर प्रतीक ज्ञात कीजिए । <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538516.png\" alt=\"rId22\" width=\"182\" height=\"89\"></p>",
                    options_en: [
                        "<p>$</p>",
                        "<p>&amp;</p>",
                        "<p>%</p>",
                        "<p>?</p>"
                    ],
                    options_hi: [
                        "<p>$</p>",
                        "<p>&amp;</p>",
                        "<p>%</p>",
                        "<p>?</p>"
                    ],
                    solution_en: "<p>12.(d) From both the dice the opposite face are <br>&amp; &harr; $ , % &harr; + , ? &harr; @</p>",
                    solution_hi: "<p>12.(d) दोनों पासों के विपरीत फलक हैं <br>&amp; &harr; $ , % &harr; + , ? &harr; @</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out.<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>13. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।<br>(<strong>नोट :</strong> गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>31 &ndash; 5 &ndash; 160</p>",
                        "<p>27 &ndash; 9 &ndash; 243</p>",
                        "<p>21 &ndash; 4 &ndash; 89</p>",
                        "<p>13 &ndash; 8 &ndash; 109</p>"
                    ],
                    options_hi: [
                        "<p>31 &ndash; 5 &ndash; 160</p>",
                        "<p>27 &ndash; 9 &ndash; 243</p>",
                        "<p>21 &ndash; 4 &ndash; 89</p>",
                        "<p>13 &ndash; 8 &ndash; 109</p>"
                    ],
                    solution_en: "<p>13.(b)<strong> Logic :-</strong> (1st number &times; 2nd number) + 5 = 3rd number<br>(31 - 5 - 160) :- (31 &times; 5) + 5 &rArr; (155) + 5 = 160<br>(21 - 4 - 89) :- (21 &times; 4) + 5 &rArr; (84) + 5 = 89<br>(13 - 8 - 109) :- (13 &times; 8) + 5 &rArr; (104) + 5 = 109<br>But,<br>(27 - 9 - 243) :- (27 &times; 9) + 5 &rArr; (243) + 5 = 248(Not 243)</p>",
                    solution_hi: "<p>13.(b) <strong>तर्क :-</strong> (पहली संख्या &times; दूसरी संख्या) + 5 = तीसरी संख्या<br>(31 - 5 - 160) :- (31 &times; 5) + 5 &rArr; (155) + 5 = 160<br>(21 - 4 - 89) :- (21 &times; 4) + 5 &rArr; (84) + 5 = 89<br>(13 - 8 - 109) :- (13 &times; 8) + 5 &rArr; (104) + 5 = 109<br>लेकिन,<br>(27 - 9 - 243) :- (27 &times; 9) + 5 &rArr; (243) + 5 = 248 (243 नहीं)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. A, B, C, D, E, F and G are sitting around a circular table facing the centre (but not necessarily in the same order). Only 2 people sit between B and D when counted from the left of D. E and C are immediate neighbours of A. G sits immediately to the right of F. Only 1 person sits between F and E when counted from the right of E. Who sits immediately to the left of F?</p>",
                    question_hi: "<p>14. A, B, C, D, E, F और G एक वृत्ताकार मेज के चारों ओर केंद्र की ओर मुख करके बैठे हैं (परंतु जरूरी नहीं कि वे इसी क्रम में बैठे हों)। D के बाईं ओर से गिनती करने पर B और D के बीच में केवल 2 व्यक्ति बैठे हैं। E और C, A के निकटतम पड़ोसी हैं। G, F के ठीक दाईं ओर बैठा है। E के दाईं ओर से गिनती करने पर F और E के बीच में केवल 1 व्यक्ति बैठा है। F के ठीक बाईं ओर कौन बैठा है?</p>",
                    options_en: [
                        "<p>G</p>",
                        "<p>A</p>",
                        "<p>B</p>",
                        "<p>D</p>"
                    ],
                    options_hi: [
                        "<p>G</p>",
                        "<p>A</p>",
                        "<p>B</p>",
                        "<p>D</p>"
                    ],
                    solution_en: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538675.png\" alt=\"rId23\" width=\"104\" height=\"117\"></p>",
                    solution_hi: "<p>14.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538675.png\" alt=\"rId23\" width=\"104\" height=\"117\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Preeti starts from point A and drives 7 km towards South. From here, she takes a left turn and drives 9 km. She then takes a final left turn and drives 7 km to reach point B. How far should Preeti drive to reach point A from point B?</p>",
                    question_hi: "<p>15. प्रीति, बिंदु A से शुरू करती है और 7 km दक्षिण की ओर ड्राइव करती है। यहाँ से, वह बाएँ मुड़ती है और 9 km ड्राइव करती है। फिर वह अंतिम बार बाएँ मुड़ती है और बिंदु B तक पहुँचने के लिए 7 km ड्राइव करती है। प्रीति को बिंदु B से बिंदु A तक पहुँचने के लिए कितनी दूर ड्राइव करनी चाहिए ?</p>",
                    options_en: [
                        "<p>6 km</p>",
                        "<p>8 km</p>",
                        "<p>9 km</p>",
                        "<p>7 km</p>"
                    ],
                    options_hi: [
                        "<p>6 km</p>",
                        "<p>8 km</p>",
                        "<p>9 km</p>",
                        "<p>7 km</p>"
                    ],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538786.png\" alt=\"rId24\" width=\"224\" height=\"106\"><br>She should drive 9km to reach point A.</p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538786.png\" alt=\"rId24\" width=\"224\" height=\"106\"><br>बिंदु A तक पहुँचने के लिए उसे 9 किमी ड्राइव करनी चाहिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. &lsquo;Simple&rsquo; is related to &lsquo;Basic&rsquo; in the same way as &lsquo;Difficult&rsquo; is related to &lsquo;________&rsquo;.</p>",
                    question_hi: "<p>16. \'सरल\' का संबंध \'बुनियादी\' से उसी प्रकार है, जैसे \'मुश्किल\' का संबंध \'________\' से है।</p>",
                    options_en: [
                        "<p>Trivial</p>",
                        "<p>Wrong</p>",
                        "<p>Imaginary</p>",
                        "<p>Complex</p>"
                    ],
                    options_hi: [
                        "<p>मामूली</p>",
                        "<p>गलत</p>",
                        "<p>काल्पनिक</p>",
                        "<p>जटिल</p>"
                    ],
                    solution_en: "<p>16.(d)<br>As &lsquo;Simple&rsquo; is the synonym of &lsquo;Basic&rsquo; similarly &lsquo;Difficult&rsquo; is the synonym of &lsquo;Complex&rsquo;</p>",
                    solution_hi: "<p>16.(d)<br>जैसे \'सरल\', \'बुनियादी\' का पर्याय है उसी प्रकार \'कठिन\', &lsquo;जटिल&rsquo; का पर्याय है ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. If \'A\' stands for \'&divide;\', \'B\' stands for \'&times;\', \'C\' stands for \'+\' and \'D\' stands for &lsquo;-&rsquo;, what will come in place of the question mark (?) in the following equation?<br>35 B 7 D 120 A 12 C 38 = ?</p>",
                    question_hi: "<p>17. यदि \'A\' का अर्थ \'&divide;\', \'B\' का अर्थ \'&times;\', C\' का अर्थ &lsquo;+&rsquo; और \'D\' का अर्थ &lsquo;-&rsquo; है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>35 B 7 D 120 A 12 C 38 = ?</p>",
                    options_en: [
                        "<p>473</p>",
                        "<p>173</p>",
                        "<p>373</p>",
                        "<p>273</p>"
                    ],
                    options_hi: [
                        "<p>473</p>",
                        "<p>173</p>",
                        "<p>373</p>",
                        "<p>273</p>"
                    ],
                    solution_en: "<p>17.(d) <strong>Given :-</strong> 35 B 7 D 120 A 12 C 38 <br>As per given instruction after interchanging the letter with sign we get<br>35 &times; 7 - 120 &divide; 12 + 38<br>245 - 10 + 38 = 273</p>",
                    solution_hi: "<p>17.(d) <strong>दिया गया :- </strong>35 B 7 D 120 A 12 C 38 <br>दिए गए निर्देश के अनुसार अक्षर को चिन्ह से बदलने पर हमें प्राप्त होता है<br>35 &times; 7 - 120 &divide; 12 + 38<br>245 - 10 + 38 = 273</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. The position of how many letters will remain unchanged if each of the letters in the word NUCLEAR is arranged in the alphabetical order?</p>",
                    question_hi: "<p>18. यदि शब्द NUCLEAR के प्रत्येक अक्षर को वर्णमाला के क्रम में व्यवस्थित किया जाए, तो कितने अक्षरों का स्थान अपरिवर्तित रहेगा?</p>",
                    options_en: [
                        "<p>None</p>",
                        "<p>Three</p>",
                        "<p>One</p>",
                        "<p>Two</p>"
                    ],
                    options_hi: [
                        "<p>एक का भी नहीं</p>",
                        "<p>तीन</p>",
                        "<p>एक</p>",
                        "<p>दो</p>"
                    ],
                    solution_en: "<p>18.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538979.png\" alt=\"rId25\" width=\"189\" height=\"106\"><br>The position of only one letter remain unchanged.</p>",
                    solution_hi: "<p>18.(c) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582538979.png\" alt=\"rId25\" width=\"189\" height=\"106\"><br>केवल एक अक्षर का स्थान अपरिवर्तित रहता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. Which figure should replace the question mark (?) if the following series were to be continued?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539186.png\" alt=\"rId26\" width=\"356\" height=\"72\"></p>",
                    question_hi: "<p>19. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539186.png\" alt=\"rId26\" width=\"356\" height=\"72\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539301.png\" alt=\"rId27\" width=\"98\" height=\"97\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539404.png\" alt=\"rId28\" width=\"100\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539528.png\" alt=\"rId29\" width=\"100\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539660.png\" alt=\"rId30\" width=\"100\" height=\"99\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539301.png\" alt=\"rId27\" width=\"100\" height=\"99\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539404.png\" alt=\"rId28\" width=\"101\" height=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539528.png\" alt=\"rId29\" width=\"100\" height=\"98\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539660.png\" alt=\"rId30\" width=\"102\" height=\"101\"></p>"
                    ],
                    solution_en: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539301.png\" alt=\"rId27\" width=\"100\" height=\"99\"></p>",
                    solution_hi: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539301.png\" alt=\"rId27\" width=\"100\" height=\"99\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. If 17 August 2003 was a Sunday, then what was the day of the week on 12 September&nbsp;2014?</p>",
                    question_hi: "<p>20. यदि 17 अगस्त 2003 को रविवार था, तो 12 सितंबर 2014 को सप्ताह का कौन-सा दिन था?</p>",
                    options_en: [
                        "<p>Monday</p>",
                        "<p>Wednesday</p>",
                        "<p>Sunday</p>",
                        "<p>Friday</p>"
                    ],
                    options_hi: [
                        "<p>सोमवार</p>",
                        "<p>बुधवार</p>",
                        "<p>रविवार</p>",
                        "<p>शुक्रवार</p>"
                    ],
                    solution_en: "<p>20.(d) 17 August 2003 was Sunday. On moving to 2014 the number of odd days = <br>+2 + 1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 = 14. We have reached till 17 August, but we have to go to 12 september, the number of days between = 14 + 12 = 26. Total number of odd days = 26 + 14 = 40. On dividing 40 by 7, remainder = 5. Sunday + 5 = Friday.</p>",
                    solution_hi: "<p>20.(d) 17 अगस्त 2003 को रविवार था. 2014 में जाने पर विषम दिनों की संख्या = <br>+2 + 1 + 1 + 1 + 2 + 1 + 1 + 1 + 2 + 1 + 1 = 14. हम 17 अगस्त तक पहुंच गए हैं, लेकिन हमें 12 सितंबर तक जाना है, बीच में दिनों की संख्या = 14 + 12 = 26. विषम दिनों की कुल संख्या = 26 + 14 = 40. 40 को 7 से विभाजित करने पर शेषफल = 5. रविवार + 5 = शुक्रवार.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which is the one that DOES NOT belong to that group?<br>(Note : The odd man out is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>21. अंग्रेजी वर्णमाला क्रम के आधार पर, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से एकसमान हैं और इस प्रकार एक समूह बनाते हैं। कौन-सा अक्षर-समूह उस समूह से संबंधित नहीं है?<br>(<strong>नोट : </strong>असंगत अक्षर-समूह, व्यंजनों/स्वरों की संख्या या अक्षर-समूह में इनके स्थान पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>KJE</p>",
                        "<p>JHD</p>",
                        "<p>FDZ</p>",
                        "<p>AYU</p>"
                    ],
                    options_hi: [
                        "<p>KJE</p>",
                        "<p>JHD</p>",
                        "<p>FDZ</p>",
                        "<p>AYU</p>"
                    ],
                    solution_en: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539758.png\" alt=\"rId31\" width=\"130\" height=\"63\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539868.png\" alt=\"rId32\" width=\"133\" height=\"59\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539969.png\" alt=\"rId33\" width=\"140\" height=\"60\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540147.png\" alt=\"rId34\" width=\"137\" height=\"66\"></p>",
                    solution_hi: "<p>21.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539758.png\" alt=\"rId31\" width=\"130\" height=\"63\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539868.png\" alt=\"rId32\" width=\"133\" height=\"59\">,<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582539969.png\" alt=\"rId33\" width=\"140\" height=\"60\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540147.png\" alt=\"rId34\" width=\"137\" height=\"66\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option in which the given figure is embedded. (Rotation is NOT allowed.)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540256.png\" alt=\"rId35\"></p>",
                    question_hi: "<p>22. उस विकल्प का चयन कीजिए जिसमें दी गई आकृति सन्निहित है। (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540256.png\" alt=\"rId35\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540375.png\" alt=\"rId36\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540471.png\" alt=\"rId37\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540576.png\" alt=\"rId38\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540683.png\" alt=\"rId39\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540375.png\" alt=\"rId36\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540471.png\" alt=\"rId37\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540576.png\" alt=\"rId38\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540683.png\" alt=\"rId39\"></p>"
                    ],
                    solution_en: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540787.png\" alt=\"rId40\" width=\"131\" height=\"114\"></p>",
                    solution_hi: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582540787.png\" alt=\"rId40\" width=\"131\" height=\"114\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. In a certain code,&nbsp;&lsquo;pen pencil eraser&rsquo; is coded as &lsquo;dy ph mt&rsquo;,&nbsp;&lsquo;pencil box crayon&rsquo; is coded as &lsquo;lp ph st&rsquo; and&nbsp;&lsquo;box pen board&rsquo; is coded as &lsquo;st bq dy&rsquo;.&nbsp;All the codes are two-letter codes only.&nbsp;What is \'board\' coded as ?</p>",
                    question_hi: "<p>23. एक निश्चित कूट में,&nbsp;&lsquo;pen pencil eraser&rsquo; को &lsquo;dy ph mt&rsquo; के रूप में कूटबद्ध किया गया है,&nbsp;&lsquo;pencil box crayon&rsquo; को &lsquo;lp ph st&rsquo; के रूप में कूटबद्ध किया गया है और&nbsp;&lsquo;box pen board&rsquo; को &lsquo;st bq dy&rsquo; के रूप में कूटबद्ध किया गया है।&nbsp;सभी कूट केवल दो अक्षर वाले कूट हैं।&nbsp;\'board\' को किस रूप में कूटबद्ध किया गया है?</p>",
                    options_en: [
                        "<p>lp</p>",
                        "<p>st</p>",
                        "<p>dy</p>",
                        "<p>bq</p>"
                    ],
                    options_hi: [
                        "<p>lp</p>",
                        "<p>st</p>",
                        "<p>dy</p>",
                        "<p>bq</p>"
                    ],
                    solution_en: "<p>23.(d)<br><strong id=\"docs-internal-guid-0b4ec452-7fff-a3f5-78c9-853e5c24ff52\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcLG4LSje9Zd8N72xw-U31tt5ZcxL4vMhg5t2EkQCvmkPW0Ddk_HBVOGt9pvcxGizOMlaU7OyiPvhrlO3b2CSSnRA9_vr-MLyCR00xhuOoNg-TqhymtDg2aIf1by_n_sQOhm0d2tw?key=UZ-KNBXmevFxRozknDMJ0qEm\" width=\"288\" height=\"136\"></strong><br>The code of &lsquo;board&rsquo; = &lsquo;bq&rsquo;.</p>",
                    solution_hi: "<p>23.(d)<br><strong id=\"docs-internal-guid-0b4ec452-7fff-a3f5-78c9-853e5c24ff52\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcLG4LSje9Zd8N72xw-U31tt5ZcxL4vMhg5t2EkQCvmkPW0Ddk_HBVOGt9pvcxGizOMlaU7OyiPvhrlO3b2CSSnRA9_vr-MLyCR00xhuOoNg-TqhymtDg2aIf1by_n_sQOhm0d2tw?key=UZ-KNBXmevFxRozknDMJ0qEm\" width=\"288\" height=\"136\"></strong><br>\'board\' का कोड = \'bq\'.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the correct mirror image of the given combination when the mirror is placed at&nbsp;MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541068.png\" alt=\"rId42\" width=\"106\" height=\"120\"></p>",
                    question_hi: "<p>24. दर्पण को नीचे दर्शाए अनुसार MN पर रखे जाने पर दिए गए संयोजन के सही दर्पण प्रतिबिंब का चयन&nbsp;कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541068.png\" alt=\"rId42\" width=\"106\" height=\"120\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541190.png\" alt=\"rId43\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541346.png\" alt=\"rId44\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541447.png\" alt=\"rId45\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541543.png\" alt=\"rId46\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541190.png\" alt=\"rId43\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541346.png\" alt=\"rId44\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541447.png\" alt=\"rId45\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541543.png\" alt=\"rId46\"></p>"
                    ],
                    solution_en: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541346.png\" alt=\"rId44\"></p>",
                    solution_hi: "<p>24.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541346.png\" alt=\"rId44\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Select the option that is related to the fifth letter cluster in the same way as the second letter cluster is related to the first letter cluster and the fourth letter cluster is related to the third letter cluster. <br>IMPROVE : RPMIEVO :: HISTORY : TSIHYRO :: JOURNEY : ?</p>",
                    question_hi: "<p>25. उस विकल्प का चयन कीजिए जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह, पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह, तीसरे अक्षर-समूह से संबंधित है |<br>IMPROVE : RPMIEVO :: HISTORY : TSIHYRO :: JOURNEY : ?</p>",
                    options_en: [
                        "<p>RUOJYEN</p>",
                        "<p>JOURYEN</p>",
                        "<p>NEYJOUR</p>",
                        "<p>YENRUOJ</p>"
                    ],
                    options_hi: [
                        "<p>RUOJYEN</p>",
                        "<p>JOURYEN</p>",
                        "<p>NEYJOUR</p>",
                        "<p>YENRUOJ</p>"
                    ],
                    solution_en: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541661.png\" alt=\"rId47\" width=\"156\" height=\"84\">and <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541816.png\" alt=\"rId48\" width=\"165\" height=\"83\"><br>&nbsp;Similarly<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541954.png\" alt=\"rId49\" width=\"191\" height=\"93\"></p>",
                    solution_hi: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541661.png\" alt=\"rId47\" width=\"156\" height=\"84\">और <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541816.png\" alt=\"rId48\" width=\"165\" height=\"83\"><br>&nbsp;इसी प्रकार<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582541954.png\" alt=\"rId49\" width=\"191\" height=\"93\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. The term \'anchor runner\' is related to _____________.</p>",
                    question_hi: "<p>26. \'एंकर रनर\' शब्द किससे संबंधित है?</p>",
                    options_en: [
                        "<p>steeple chase</p>",
                        "<p>1500 m race</p>",
                        "<p>relay race</p>",
                        "<p>marathon race</p>"
                    ],
                    options_hi: [
                        "<p>स्टीपल चेज़</p>",
                        "<p>1500 m दौड़</p>",
                        "<p>रिले दौड़</p>",
                        "<p>मैराथन दौड़</p>"
                    ],
                    solution_en: "<p>26.(c) <strong>relay race -</strong> It is a team events, In which each relay team needs four runners. Each runner runs the quarter of the running track. Related Terminology - Baton exchange. Steeple chase&mdash;A hurdle race with water jumps came into existence in England around 1850. The marathon is a long-distance foot race with a distance of 42.195 km and 20 and 50 km walk, usually run as a road race.</p>",
                    solution_hi: "<p>26.(c)<strong> रिले दौड़ -</strong> यह एक टीम इवेंट है, जिसमें प्रत्येक रिले टीम को चार धावकों की आवश्यकता होती है। प्रत्येक धावक रनिंग ट्रैक का चौथाई भाग दौड़ता है। संबंधित शब्दावली - बैटन एक्सचेंज। स्टीपल चेज़ - इंग्लैंड में 1850 के आसपास जल मे छलांग के साथ एक हर्डल दौड़ को शामिल किया गया था। मैराथन एक लंबी दूरी की पैदल दौड़ है जिसमें 42.195 किमी की दूरी और 20 किमी और 50 किमी की पैदल दूरी होती है, जिसकी दौड़ आमतौर पर सड़क पर करायी जाती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Which type of soil is found on about 40% of the total area of India?</p>",
                    question_hi: "<p>27. भारत के कुल क्षेत्रफल के लगभग 40% भाग पर किस प्रकार की मिट्टी पाई जाती है?</p>",
                    options_en: [
                        "<p>Black soil</p>",
                        "<p>Alluvial soil</p>",
                        "<p>Laterite soil</p>",
                        "<p>Red and Yellow soil</p>"
                    ],
                    options_hi: [
                        "<p>काली मिट्टी</p>",
                        "<p>जलोढ़ मिट्टी</p>",
                        "<p>लैटेराइट मिट्टी</p>",
                        "<p>लाल और पीली मिट्टी</p>"
                    ],
                    solution_en: "<p>27.(b) <strong>Alluvial soil </strong>- the soil which is deposited by surface water, commonly found in river valleys and northern plains. It is highly fertile and rich in humus and lime. Crops cultivated on it include sugarcane, rice, maize, wheat, oilseeds, and pulses.</p>",
                    solution_hi: "<p>27.(b) <strong>जलोढ़ मिट्टी -</strong> सतही जल द्वारा एकत्र की गई मिट्टी, आमतौर पर नदी घाटियों और उत्तरी मैदानों में पायी जाती है। यह अत्यधिक उपजाऊ होती है और इसमें ह्यूमस और चूना प्रचुर मात्रा में मौजूद होता है। इस पर उगाई जाने वाली फसलों में गन्ना, चावल, मक्का, गेहूं, तिलहन और दालें आदि शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Sunayana Hazarilal won the Padma Shri for which form of classical dance?</p>",
                    question_hi: "<p>28. सुनयना हजारीलाल ने किस शास्त्रीय नृत्य के लिए पद्म श्री प्राप्त किया?</p>",
                    options_en: [
                        "<p>Kathakali</p>",
                        "<p>Odissi</p>",
                        "<p>Kathak</p>",
                        "<p>Bharatanatyam</p>"
                    ],
                    options_hi: [
                        "<p>कथकली</p>",
                        "<p>ओडिसी</p>",
                        "<p>कथक</p>",
                        "<p>भरतनाट्यम</p>"
                    ],
                    solution_en: "<p>28.(c) <strong>Kathak.</strong> Sunayana Hazarilal belongs to Janakiprasad Gharana ( or Benares Gharana). She was awarded Padma Shri (2011). Other Famous classical dancer: Pandit Birju Maharaj (Kathak), Rukmini Devi Arundale (Bharatanatyam), Kelucharan Mohapatra (Odissi), Guru Vempati Chinna Satyam (Kuchipudi dance), Guru Bipin Singh (Manipuri dance).</p>",
                    solution_hi: "<p>28.(c) <strong>कथक। </strong>सुनयना हजारीलाल का संबंध जानकीप्रसाद घराने (या बनारस घराने) से हैं। उन्हें पद्मश्री (2011) से सम्मानित किया गया है। अन्य प्रसिद्ध शास्त्रीय नर्तक: पंडित बिरजू महाराज (कथक), रुक्मिणी देवी अरुंडेल (भरतनाट्यम), केलुचरण महापात्र (ओडिसी), गुरु वेम्पति चिन्ना सत्यम (कुचिपुड़ी नृत्य), गुरु बिपिन सिंह (मणिपुरी नृत्य)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Who was sworn in as the 19th Governor of Manipur in January 2025?</p>",
                    question_hi: "<p>29. जनवरी 2025 में मणिपुर के 19वें राज्यपाल के रूप में किसने शपथ ली?</p>",
                    options_en: [
                        "<p>Rajendra Vishwanath Arlekar</p>",
                        "<p>Ajay Kumar Bhalla</p>",
                        "<p>Arif Mohammed Khan</p>",
                        "<p>Keshari Nath Tripathi</p>"
                    ],
                    options_hi: [
                        "<p>राजेंद्र विश्वनाथ आर्लेकर</p>",
                        "<p>अजय कुमार भल्ला</p>",
                        "<p>आरिफ मोहम्मद खान</p>",
                        "<p>केशरी नाथ त्रिपाठी</p>"
                    ],
                    solution_en: "<p>29(b) <strong>Ajay Kumar Bhalla.</strong> He is a 1984-batch retired Indian Administrative Service (IAS) officer of the Assam-Meghalaya cadre. About Manipur: Capital - Imphal. Chief minister - N. Biren Singh. Official Language - Manipuri.</p>",
                    solution_hi: "<p>29.(b) <strong>अजय कुमार भल्ला।</strong> वे असम-मेघालय कैडर के 1984 बैच के सेवानिवृत्त भारतीय प्रशासनिक सेवा (IAS) अधिकारी हैं। मणिपुर के बारे में: राजधानी - इंफाल। मुख्यमंत्री - एन. बीरेन सिंह। आधिकारिक भाषा - मणिपुरी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. What is the root-like structure at the base of an algae (seaweed) that binds the algae to a hard substrate like a stone?</p>",
                    question_hi: "<p>30. शैवाल (समुद्री शैवाल) के आधार पर मौजूद जड़ जैसी संरचना क्या कहलाती है, जो शैवाल को पत्थर जैसे किसी कठोर अवस्तर से चिपकाकर रखती है ?</p>",
                    options_en: [
                        "<p>Holdfast</p>",
                        "<p>Midrib</p>",
                        "<p>Stipe</p>",
                        "<p>Frond</p>"
                    ],
                    options_hi: [
                        "<p>होल्डफ़ास्ट</p>",
                        "<p>मिडरिब</p>",
                        "<p>स्टिप</p>",
                        "<p>फ्रॉन्ड</p>"
                    ],
                    solution_en: "<p>30.(a) <strong>Holdfast. </strong>Algae are a diverse group of photosynthetic, eukaryotic organisms, representing a polyphyletic grouping that includes species from various clades. The midrib, or midvein, is the central, thick vein running from the base to the tip of a leaf, providing support and structure. A stipe is the stalk or stem that supports a structure in plants, algae, or fungi. A frond refers to a large, divided leaf, typically associated with ferns and some palm trees, but can also denote the leaves of flowering plants like mimosa or sumac.</p>",
                    solution_hi: "<p>30.(a) <strong>होल्डफ़ास्ट।</strong> शैवाल प्रकाश संश्लेषक, यूकेरियोटिक जीवों का एक विविध समूह है, जो एक पॉलीफाइलेटिक समूह का प्रतिनिधित्व करता है जिसमें विभिन्न वर्गों की प्रजातियां शामिल हैं। मध्यशिरा पत्ती के आधार से सिरे तक केन्द्रीय मोटी शिरा होती है, जो आधार और संरचना प्रदान करती है। स्टाइप वह डंठल या तना है जो पौधों, शैवाल या कवक में किसी संरचना का समर्थन करता है। फ्रोंड एक बड़े, विभाजित पत्ते को संदर्भित करता है, जो आमतौर पर फर्न और कुछ ताड़ के पेड़ों से जुड़ा होता है, लेकिन यह मिमोसा या सुमाक जैसे फूल वाले पौधों की पत्तियों को भी निरूपित कर सकता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Natpuja dance is the folk dance of________.</p>",
                    question_hi: "<p>31. नटपूजा नृत्य निम्नलिखित में से किस राज्य का लोक - नृत्य है ?</p>",
                    options_en: [
                        "<p>Bihar</p>",
                        "<p>Punjab</p>",
                        "<p>Uttar Pradesh</p>",
                        "<p>Assam</p>"
                    ],
                    options_hi: [
                        "<p>बिहार</p>",
                        "<p>पंजाब</p>",
                        "<p>उत्तर प्रदेश</p>",
                        "<p>असम</p>"
                    ],
                    solution_en: "<p>31.(d) <strong>Assam. </strong>Indian states and Folk dances: Assam - Bihu, Bichhua, Maharas, Bagurumba, etc. Bihar: Jata-Jatin, Bakho-Bakhain, Panwariya, Bidesia, Jatra. Uttar Pradesh: Nautanki, Raslila, Kajri, Jhora, Chappeli, Jaita. Punjab: Bhangra, Giddha, Daff, Dhaman etc. Kerala: Rakhal, Nat Rash, Maha Rash, Raukhat etc.</p>",
                    solution_hi: "<p>31.(d) <strong>असम ।</strong> भारतीय राज्य और लोक नृत्य: असम - बिहू, बिछुआ, महारास, बगुरुम्बा, आदि। बिहार: जटा-जतिन, बखो - बखैन, पंवारिया, बिदेसिया, जात्रा। उत्तर प्रदेश: नौटंकी, रासलीला, कजरी, झोड़ा, छपेली, जैता। पंजाब: भांगड़ा, गिद्धा, डफ, धामन आदि। केरल: राखल, नट रश, महा रश, रौखत आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. The much-loved English novel, \'Alice\'s Adventures in Wonderland\' was written by which of the following authors?</p>",
                    question_hi: "<p>32. बहुचर्चित अंग्रेजी उपन्यास \'एलिस\'ज एडवेंचर्स इन वंडरलैंड (Alice\'s Adventures in Wonderland)\' निम्नलिखित में से किस लेखक ने लिखा था?</p>",
                    options_en: [
                        "<p>Herman Melville</p>",
                        "<p>Edgar Allan Poe</p>",
                        "<p>Lewis Carroll</p>",
                        "<p>Mary Shelly</p>"
                    ],
                    options_hi: [
                        "<p>हरमन मेलविल</p>",
                        "<p>एडगर ऍलन पो</p>",
                        "<p>लुईस कैरोल</p>",
                        "<p>मैरी शेली</p>"
                    ],
                    solution_en: "<p>32.(c) <strong>Lewis Carroll</strong> wrote &lsquo;Through the Looking Glass&rsquo;, &lsquo;The Hunting of the Snark&rsquo; and &lsquo;The Game of Logic&rsquo;. Books and authors - &lsquo;Tropic of Cancer&rsquo; - Henry Miller, &lsquo;A passage to India&rsquo; - E. M. Foster, &lsquo;Absolute Power&rsquo; - David Baldacci, &lsquo;Descent of Man&rsquo; - Charles Darwin, &lsquo;A MidSummer Night Dream&rsquo; - William Shakespeare, &lsquo;War and Peace&rsquo; - Leo Tolstoy.</p>",
                    solution_hi: "<p>32.(c) <strong>लुईस कैरोल</strong> ने \'थ्रू द लुकिंग ग्लास\', \'द हंटिंग ऑफ द स्नार्क\' और \'द गेम ऑफ लॉजिक\' लिखा था । पुस्तकें एवं उनके लेखक - \'ट्रॉपिक ऑफ कैंसर\' - हेनरी मिलर, \'ए पैसेज टू इंडिया\' - ई. एम. फोस्टर, \'एब्सोल्यूट पावर\' - डेविड बाल्डैकी, \'डिसेंट ऑफ मैन\' - चार्ल्स डार्विन, \'ए मिडसमर नाइट ड्रीम\' - विलियम शेक्सपियर, \'वॉर एंड पीस\' - लियो टॉल्स्टॉय।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Who among the following was the founder and director of \'Darpana Academy of Performing Arts\' in Ahmedabad that trains students in dance, drama, music and puppetry?</p>",
                    question_hi: "<p>33. निम्नलिखित में से कौन अहमदाबाद में \'दर्पण एकेडमी ऑफ परफॉर्मिंग आर्ट्स\' के संस्थापक और निर्देशक थे, जो छात्रों को नृत्य, नाटक, संगीत और कठपुतली में प्रशिक्षित करता है?</p>",
                    options_en: [
                        "<p>Mrinalini Sarabhai</p>",
                        "<p>Padma Subrahmanyam</p>",
                        "<p>Shovana Narayan</p>",
                        "<p>Sonal Mansingh</p>"
                    ],
                    options_hi: [
                        "<p>मृणालिनी साराभाई</p>",
                        "<p>पद्मा सुब्रह्मण्यम</p>",
                        "<p>शोभना नारायण</p>",
                        "<p>सोनल मानसिंह</p>"
                    ],
                    solution_en: "<p>33.(a) <strong>Mrinalini Sarabhai. </strong>Darpana Academy of Performing Arts is a school for performing arts in Ahmedabad, Gujarat, established by Mrinalini Sarabhai and Vikram Sarabhai in 1949, it has been directed by their daughter Mallika Sarabhai for the last three decades. Famous Indian classical dancers : Padma Subramanyam (Bharatanatayam), Shovana Narayan (Kathak), Sonal Mansingh (Bharatanatyam and Odissi).</p>",
                    solution_hi: "<p>33.(a) <strong>मृणालिनी साराभाई।</strong> दर्पण एकेडमी ऑफ परफॉर्मिंग आर्ट्स, अहमदाबाद, गुजरात में प्रदर्शन कला के लिए एक स्कूल है, जिसकी स्थापना 1949 में मृणालिनी साराभाई और विक्रम साराभाई ने की थी, पिछले तीन दशकों से इसका निर्देशन उनकी बेटी मल्लिका साराभाई कर रही हैं। प्रसिद्ध भारतीय शास्त्रीय नर्तक: पद्मा सुब्रमण्यम (भरतनाट्यम), शोवना नारायण (कथक), सोनल मानसिंह (भरतनाट्यम और ओडिसी)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which of the following was discovered by GN Ramachandran?</p>",
                    question_hi: "<p>34. निम्नलिखित में से किसकी खोज जी. एन. रामचंद्रन ने की थी?</p>",
                    options_en: [
                        "<p>Fluid mosaic model of a cell</p>",
                        "<p>Plasma membrane</p>",
                        "<p>Golgi bodies</p>",
                        "<p>Triple helical structure of collagen</p>"
                    ],
                    options_hi: [
                        "<p>कोशिका का तरल मोज़ैक मॉडल</p>",
                        "<p>जीवद्रव्यझिल्ली</p>",
                        "<p>गॉल्जीकाय</p>",
                        "<p>कोलेजन की तिहरी कुंडलित संरचना</p>"
                    ],
                    solution_en: "<p>34.(d)<strong> Triple helical structure of collagen.</strong> G.N. Ramachandran, an Indian physicist, is renowned for his work that resulted in the creation of the Ramachandran plot, which aids in understanding peptide structure. The collagen triple helix, a right-handed super-coiled structure, consists of three parallel &alpha;-chains, each adopting a polyproline II helical conformation.</p>",
                    solution_hi: "<p>34.(d)<strong> कोलेजन की तिहरी कुंडलित संरचना । </strong>जी.एन. रामचंद्रन, एक भारतीय भौतिक वैज्ञानिक है, जो अपने कार्य के लिए प्रसिद्ध हैं जिसके परिणामस्वरूप रामचंद्रन प्लॉट का निर्माण हुआ, जो पेप्टाइड संरचना को समझने में सहायता करता है। कोलेजन की तिहरी कुंडलित, एक दाएं हाथ की सुपर-कॉइल संरचना, तीन समानांतर &alpha;-चेन से मिलकर बनी है, जिनमें से प्रत्येक पॉलीप्रोलाइन II कुंडलित संरचना को अपनाती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of the following Chola Kings raided the Ganga Valley region ?</p>",
                    question_hi: "<p>35. निम्नलिखित में से किस चोल राजा ने गंगा घाटी क्षेत्र पर आक्रमण किया?</p>",
                    options_en: [
                        "<p>Rajendra I</p>",
                        "<p>Uttama Chola</p>",
                        "<p>Arinjaya Chola</p>",
                        "<p>Gandaraditya</p>"
                    ],
                    options_hi: [
                        "<p>राजेंद्र I</p>",
                        "<p>उत्तम चोल</p>",
                        "<p>अरिंजय चोल</p>",
                        "<p>गंदारादित्य</p>"
                    ],
                    solution_en: "<p>35.(a) <strong>Rajendra I </strong>(Son of Rajaraja). He was the first to venture to the banks of Ganges. He was popularly called the Victor of the Ganges. This period is referred to as the golden age of the Cholas. He also raided Sri Lanka and countries of Southeast Asia, developing a navy for these expeditions. Rajendra I was the founder of &lsquo;Gangaikonda Cholapuram&rsquo;. The title of &ldquo;Gangai Kondan&rdquo; was adopted by him.</p>",
                    solution_hi: "<p>35.(a)<strong> राजेंद्र प्रथम</strong> (राजराजा का पुत्र)। वह गंगा तट पर जाने वाले पहले व्यक्ति थे। उन्हें लोकप्रिय रूप से गंगा का विजेता कहा जाता था। इस काल को चोलों का स्वर्ण युग कहा जाता है। उन्होंने इन अभियानों के लिए एक नौसेना विकसित करते हुए, श्रीलंका और दक्षिण पूर्व एशिया के देशों पर भी छापा मारा। राजेंद्र प्रथम \'गंगईकोंडा चोलपुरम\' के संस्थापक थे। उनके द्वारा \"गंगई कोंडन\" की उपाधि धारण की गई थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which is the most common tidal pattern?</p>",
                    question_hi: "<p>36. सबसे आम ज्वारीय पैटर्न निम्नलिखित में से कौन-सा है?</p>",
                    options_en: [
                        "<p>Mixed tides</p>",
                        "<p>Diurnal tides</p>",
                        "<p>Spring tides</p>",
                        "<p>Semi-diurnal tides</p>"
                    ],
                    options_hi: [
                        "<p>मिश्रित ज्वारभाटा</p>",
                        "<p>दैनिक ज्वारभाटा</p>",
                        "<p>बृहत ज्वारभाटा</p>",
                        "<p>अर्धदैनिक ज्वारभाटा</p>"
                    ],
                    solution_en: "<p>36.(d) <strong>Semi-diurnal tides</strong> exhibit two high and two low tides each day, with both highs and both lows of roughly equal height. The gravitational pull of the celestial bodies on the Earth leads to the formation of tides. In 1687, Sir Isaac Newton explained that ocean tides result from the gravitational attraction of the sun and moon on the oceans of the earth. Tides Based on Frequency - Semi-Diurnal Tides, Diurnal Tides , Mixed Tides. Tides Based on the Position of Earth, Sun, and the Moon: Spring Tides, Neap Tides.</p>",
                    solution_hi: "<p>36.(d) <strong>अर्ध-दैनिक ज्वार- भाटा</strong> प्रत्येक दिन दो उच्च और दो निम्न ज्वार प्रदर्शित करते हैं, जिनमें दोनों उच्च और दोनों निम्न ज्वार लगभग समान ऊंचाई के होते हैं। पृथ्वी पर आकाशीय पिंडों के गुरुत्वाकर्षण की क्रियाशीलता के कारण ज्वार - भाटा की उत्पत्ति होती है। 1687 में, सर आइजैक न्यूटन ने बताया कि समुद्री ज्वार- भाटा पृथ्वी के महासागरों पर सूर्य और चंद्रमा के गुरुत्वाकर्षण आकर्षण के कारण उत्पन्न होते हैं। आवृत्ति के आधार पर ज्वार -भाटा: अर्ध-दैनिक ज्वार-भाटा, दैनिक ज्वार-भाटा, मिश्रित ज्वार-भाटा। पृथ्वी, सूर्य और चंद्रमा की स्थिति के आधार पर ज्वार-भाटा: वृहत ज्वार-भाटा, लघु ज्वार-भाटा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. According to the 2011 census of India, what is the total percentage of Christian population in the country?</p>",
                    question_hi: "<p>37. भारत की 2011 की जनगणना के अनुसार, देश में ईसाई जनसंख्या का कुल प्रतिशत कितना है?</p>",
                    options_en: [
                        "<p>3.3 percent</p>",
                        "<p>2.3 percent</p>",
                        "<p>0.3 percent</p>",
                        "<p>1.3 percent</p>"
                    ],
                    options_hi: [
                        "<p>3.3 प्रतिशत</p>",
                        "<p>2.3 प्रतिशत</p>",
                        "<p>0.3 प्रतिशत</p>",
                        "<p>1.3 प्रतिशत</p>"
                    ],
                    solution_en: "<p>37.(b) <strong>2.3 percent.</strong> As per 2011 Census: Among 121.09 crores population (religions) are: Hindu 96.63 crores (79.8%), Muslim (14.2%), Sikh (1.7%), Buddhist (0.7%), Jain (0.4%), others (0.9%). Muslims (14.2%) form the largest religious minority in India.</p>",
                    solution_hi: "<p>37.(b)<strong> 2.3 प्रतिशत।</strong> 2011 की जनगणना के अनुसार: 121.09 करोड़ की आबादी में (धर्म) हैं: हिंदू 96.63 करोड़ (79.8%), मुस्लिम (14.2%), सिख (1.7%), बौद्ध (0.7%), जैन (0.4%), अन्य (0.9%)। मुस्लिम (14.2%) भारत में सबसे बड़ा धार्मिक अल्पसंख्यक हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Which of the following increased the legal age of marriage for girls from 10 years to 12 years in British India?</p>",
                    question_hi: "<p>38. निम्नलिखित में से किसने ब्रिटिश भारत में लड़कियों की शादी की कानूनी उम्र 10 वर्ष से बढ़ाकर 12 वर्ष कर दी?</p>",
                    options_en: [
                        "<p>Hindu Widows Remarriage Act of 1856</p>",
                        "<p>Pitt&rsquo;s India Act of 1784</p>",
                        "<p>Government of India Act of 1935</p>",
                        "<p>Age of Consent Bill, 1891</p>"
                    ],
                    options_hi: [
                        "<p>1856 का हिंदूविधवा पुनर्विवाह अधिनियम</p>",
                        "<p>1784 का पिट्स इंडिया एक्ट</p>",
                        "<p>1935 का भारत सरकार अधिनियम</p>",
                        "<p>सहमति आयु विधेयक, 1891</p>"
                    ],
                    solution_en: "<p>38.(d) <strong>Age of Consent Bill, 1891</strong>. Sarda Act 1929 also fixed the marriageable age for girls at 14 years and 18 years for boys. Hindu Widows Remarriage Act of 1856 passed by Lord Canning and It made it permissible for Hindu widows to remarry.</p>",
                    solution_hi: "<p>38.(d) <strong>सहमति की आयु विधेयक, 1891. </strong>सारदा अधिनियम 1929 में लड़कियों के लिए विवाह योग्य आयु 14 वर्ष और लड़कों के लिए 18 वर्ष निर्धारित की गई। 1856 का हिंदू विधवा पुनर्विवाह अधिनियम लॉर्ड कैनिंग द्वारा पारित किया गया और इसने हिंदू विधवाओं के पुनर्विवाह को अनुमति दे दी।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Which Clause of Article 20 incorporates the doctrine of double jeopardy?</p>",
                    question_hi: "<p>39. अनुच्छेद 20 का कौन सा खंड दोहरे दंड के सिद्धांत (doctrine of double jeopardy) को शामिल करता है?</p>",
                    options_en: [
                        "<p>3</p>",
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>4</p>"
                    ],
                    options_hi: [
                        "<p>3</p>",
                        "<p>2</p>",
                        "<p>1</p>",
                        "<p>4</p>"
                    ],
                    solution_en: "<p>39.(b) <strong>2.</strong> Double Jeopardy is a legal term and it means that a person can not be punished for the same offense more than once. Article 20(2) of the Constitution of India and Section 300 of the Criminal Procedure Code say that no person shall be prosecuted and punished for the same offense more than once. Article 20 grants protection against arbitrary and excessive punishment to an accused person, whether citizen or foreigner or legal person like a company or a corporation. Three provisions: No ex-post-facto law, No double jeopardy, No self-incrimination.</p>",
                    solution_hi: "<p>39.(b)<strong> 2. </strong>दोहरा दंड एक कानूनी शर्त है और इसका अर्थ है कि किसी व्यक्ति को एक ही अपराध के लिए एक से अधिक बार दंडित नहीं किया जा सकता है। भारतीय संविधान के अनुच्छेद 20(2) और दंड प्रक्रिया संहिता की धारा 300 में कहा गया है कि किसी भी व्यक्ति पर एक ही अपराध के लिए एक से अधिक बार मुकदमा नहीं चलाया जाएगा और उसे दंडित नहीं किया जाएगा। अनुच्छेद 20 - यह अनुच्छेद किसी भी अभियुक्त या दोषी करार व्यक्ति, चाहे वह नागरिक हो या विदेशी या कंपनी व परिषद का कानूनी व्यक्ति हो, उसके विरुद्ध मनमाने और अतिरिक्त दण्ड से संरक्षण प्रदान करता है। तीन प्रावधान: कोई पूर्वव्यापी कानून नहीं, कोई दोहरा दंड नहीं, कोई आत्म-दोष नहीं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. What do you understand by \'Barter System\'?</p>",
                    question_hi: "<p>40. वस्तु विनिमय प्रणाली (बार्टर सिस्टम)\' से आप क्या समझते हैं?</p>",
                    options_en: [
                        "<p>Purchase of goods in exchange for currency</p>",
                        "<p>Buying and selling of goods in exchange for goods</p>",
                        "<p>no buying or selling</p>",
                        "<p>Sale of goods in exchange for money.</p>"
                    ],
                    options_hi: [
                        "<p>मुद्रा के बदले वस्तु की खरीद</p>",
                        "<p>वस्तु के बदले वस्तु की खरीद और बिक्री</p>",
                        "<p>कोई खरीद या बिक्री नहीं</p>",
                        "<p>मुद्रा के बदले वस्तु की बिक्री</p>"
                    ],
                    solution_en: "<p>40.(b) <strong>Barter system -</strong> The act of trading one good or service for another, without the exchange of money. Benefits of Barter System: Budgeting, Economic equilibrium, Better professional relationships. Limitations of Barter System: Finding goods with equal value, Double coincidence of wants is still a big problem in bartering.</p>",
                    solution_hi: "<p>40.(b) <strong>वस्तु विनिमय प्रणाली - </strong>मुद्रा के आदान-प्रदान के बिना एक वस्तु या सेवा का दूसरे के बदले व्यापार करने की क्रिया। वस्तु विनिमय प्रणाली के लाभ: बजट, आर्थिक संतुलन, बेहतर व्यावसायिक संबंध। वस्तु-विनिमय प्रणाली की सीमाएँ: समान मूल्य वाली वस्तुएँ ढूँढना, आवश्यकताओं का दोहरा संयोग अभी भी वस्तु-विनिमय में एक बड़ी समस्या है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. The Service Tax, a tax on services like telephone services, stock brokers, health clubs, beauty parlors, dry cleaning services, etc., was introduced in _______.</p>",
                    question_hi: "<p>41. टेलीफोन सेवाओं (telephone services), स्टॉक ब्रोकरों (stock brokers), स्वास्थ्य क्लबों (health clubs), ब्यूटी पार्लरों (beauty parlors), ड्राई क्लीनिंग सेवाओं (dry cleaning services) आदि जैसी सेवाओं पर लगने वाला एक कर, सेवा कर _____ में पेश किया गया था।</p>",
                    options_en: [
                        "<p>1994-95</p>",
                        "<p>1993-94</p>",
                        "<p>1991-92</p>",
                        "<p>1992-93</p>"
                    ],
                    options_hi: [
                        "<p>1994-95</p>",
                        "<p>1993-94</p>",
                        "<p>1991-92</p>",
                        "<p>1992-93</p>"
                    ],
                    solution_en: "<p>41.(a) <strong>1994-95. </strong>Service tax is a tax imposed by the Government of India on services provided in India. The Dr. Raja Chelliah Committee on tax reforms recommended the introduction of service tax.</p>",
                    solution_hi: "<p>41.(a) <strong>1994-95</strong> । सेवा कर, भारत सरकार द्वारा भारत में प्रदान की जाने वाली सेवाओं पर लगाया जाने वाला कर है। कर सुधारों पर डॉ. राजा चेलैया समिति ने सेवा कर लागू करने की सिफारिश की थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. UPSC is a Constitutional Body to conduct examinations for appointments to the services of the Union and the services of the State, respectively, under Article ________.</p>",
                    question_hi: "<p>42. यूपीएससी (UPSC) एक संवैधानिक निकाय है जो अनुच्छेद _____के तहत क्रमशः संघ की सेवाओं और राज्य की सेवाओं हेतु नियुक्&zwj;तियों के लिए परीक्षा आयोजित करता है।</p>",
                    options_en: [
                        "<p>320</p>",
                        "<p>324</p>",
                        "<p>328</p>",
                        "<p>332</p>"
                    ],
                    options_hi: [
                        "<p>320</p>",
                        "<p>324</p>",
                        "<p>328</p>",
                        "<p>332</p>"
                    ],
                    solution_en: "<p>42.(a)<strong> Article 320.</strong> Article 324 establishes a single Commission to oversee elections to the Central Legislature, including both the Upper and Lower Houses. Article 328 grants state legislatures the authority to create provisions for elections to their respective legislatures, including the preparation of electoral rolls, delimitation of constituencies, and other arrangements for the constitution of the legislature. Article 332 reserves seats in state legislative assemblies for Scheduled Castes and Scheduled Tribes.</p>",
                    solution_hi: "<p>42.(a) <strong>अनुच्छेद 320. </strong>अनुच्छेद 324 केंद्रीय विधानमंडल के चुनावों की देखरेख के लिए एक एकल आयोग की स्थापना करता है, जिसमें ऊपरी और निचले दोनों सदन शामिल हैं। अनुच्छेद 328 राज्य विधानमंडलों को अपने संबंधित विधानमंडलों के चुनावों के लिए प्रावधान बनाने का अधिकार देता है, जिसमें मतदाता सूची तैयार करना, निर्वाचन क्षेत्रों का परिसीमन और विधानमंडल के गठन के लिए अन्य व्यवस्थाएँ शामिल हैं। अनुच्छेद 332 अनुसूचित जातियों और अनुसूचित जनजातियों के लिए राज्य विधान सभाओं में सीटें आरक्षित करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. ____________ is a form of matter in which many of the electrons wander around freely among the nuclei of the atoms.</p>",
                    question_hi: "<p>43. _________ पदार्थ की वह अवस्था है जिसमें अनेक इलेक्ट्रॉन परमाणुओं के नाभिक के बीच स्वतंत्र रूप से गति करते हैं।</p>",
                    options_en: [
                        "<p>Liquid</p>",
                        "<p>Plasma</p>",
                        "<p>Solid</p>",
                        "<p>Gas</p>"
                    ],
                    options_hi: [
                        "<p>द्रव</p>",
                        "<p>प्लाज्मा</p>",
                        "<p>ठोस</p>",
                        "<p>गैस</p>"
                    ],
                    solution_en: "<p>43.(b)<strong> Plasma</strong> has been called the fourth state of matter. Other three are - solid, liquid and gas. Examples of forms of Plasma: Lightning (shining in the sky), Aurorae, The excited low-pressure gas inside neon signs and fluorescent lights, Solarwind, Welding arcs, The Earth\'s ionosphere, Stars (including the Sun), The tail of a comet, Interstellar gas clouds.</p>",
                    solution_hi: "<p>43.(b) <strong>प्लाज्मा </strong>को पदार्थ की चौथी अवस्था कहा गया है। अन्य तीन अवस्थाएँ हैं - ठोस, द्रव और गैस। प्लाज्मा के रूपों के उदाहरण: बिजली (आकाश में चमकने वाली), ऑरोरा, नियॉन साइन और फ्लोरोसेंट लाइट के अंदर उत्तेजित कम दाब वाली गैस, सोलरविंड, वेल्डिंग आर्क, पृथ्वी का आयनमंडल, तारे (सूर्य सहित), धूमकेतु की पूंछ, अंतरतारकीय गैस बादल।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following earthquake waves is more destructive?</p>",
                    question_hi: "<p>44. निम्नलिखित में से कौन-सी भूकंपीय तरंगें अधिक विनाशकारी हैं?</p>",
                    options_en: [
                        "<p>Body Waves</p>",
                        "<p>S-Waves</p>",
                        "<p>Surface waves</p>",
                        "<p>P-Waves</p>"
                    ],
                    options_hi: [
                        "<p>काय तरंग</p>",
                        "<p>S- तरंग</p>",
                        "<p>पृष्ठीय तरंग</p>",
                        "<p>P- तरंग</p>"
                    ],
                    solution_en: "<p>44.(c) <strong>Surface waves</strong>. Earthquake waves are basically of two types - body waves and surface waves. Body waves, generated by energy release at the focus, travel in all directions through the Earth\'s interior. The two types of body waves are P and S-waves. P waves, faster, reach the surface first and traverse through gaseous, liquid, and solid materials. S - waves follow, arriving at the surface later.</p>",
                    solution_hi: "<p>44.(c) <strong>पृष्ठीय तरंग।</strong> भूकंप तरंगें मूलतः दो प्रकार की होती हैं - काय तरंग और सतही तरंगें। फोकस पर ऊर्जा रिलीज से उत्पन्न शारीरिक तरंगें, पृथ्वी के आंतरिक भाग से सभी दिशाओं में यात्रा करती हैं। काय तरंग के दो प्रकार हैं P और S-तरंगें। P तरंगें, तेजी से, पहले सतह पर पहुंचती हैं और गैसीय, तरल और ठोस पदार्थों से होकर गुजरती हैं। S-तरंगें बाद में सतह पर पहुंचती हैं।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. The festival of Pushkaralu is celebrated to promote the cultural heritage of which&nbsp;Indian state ?</p>",
                    question_hi: "<p>45. पुष्करालु त्योहार किस भारतीय राज्य की साँस्कृतिक धरोहर को बढ़ावा देने के लिए मनाया जाता है?</p>",
                    options_en: [
                        "<p>Tamil Nadu</p>",
                        "<p>Karnataka</p>",
                        "<p>Kerala</p>",
                        "<p>Andhra Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>तमिलनाडु</p>",
                        "<p>कर्नाटक</p>",
                        "<p>केरल</p>",
                        "<p>आंध्र प्रदेश</p>"
                    ],
                    solution_en: "<p>45.(d) <strong>Andhra Pradesh. </strong>Famous festivals of Andhra Pradesh: Pongal (Observed at The Inception of The Harvesting Season), Ugadi (Celebrated As The New Year&rsquo;s Day), Rottela Panduga, Prabhala Theertham, Visakha Utsav (Amazing Cultural Festival In South India), Tirupati Tirumala Brahmotsavam, Lumbini Festival (Highlights Buddhist Culture), Rayalaseema Food &amp; Dance Festival , etc.</p>",
                    solution_hi: "<p>45.(d) <strong>आंध्र प्रदेश। </strong>आंध्र प्रदेश के प्रसिद्ध त्योहार: पोंगल (फसल के मौसम की शुरुआत में मनाया जाता है), उगादि (नए साल के दिन के रूप में मनाया जाता है), रोट्टेला पांडुगा, प्रभाला थीर्थम, विशाखा उत्सव (दक्षिण भारत में अद्भुत सांस्कृतिक त्योहार), तिरुपति तिरुमाला ब्रह्मोत्सवम, लुंबिनी महोत्सव (बौद्ध संस्कृति पर प्रकाश डाला गया), रायलसीमा भोजन और नृत्य महोत्सव आदि।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Technetium, the first artificially produced element used in many medical diagnostic imaging scans, is found in which group of the periodic table?</p>",
                    question_hi: "<p>46. कृत्रिम रूप से निर्मित पहला तत्व, टेक्नेटियम (Technetium), जो कई चिकित्सीय नैदानिक इमेजिंग स्कैन में उपयोग किया जाता है, आवर्त सारणी के किस समूह में पाया जाता है?</p>",
                    options_en: [
                        "<p>Group 7</p>",
                        "<p>Group 12</p>",
                        "<p>Group 14</p>",
                        "<p>Group 19</p>"
                    ],
                    options_hi: [
                        "<p>समूह 7</p>",
                        "<p>समूह 12</p>",
                        "<p>समूह 14</p>",
                        "<p>समूह 19</p>"
                    ],
                    solution_en: "<p>46.(a) <strong>Group 7.</strong> Technetium is a chemical element with atomic number 43, represented by the symbol (Tc). This silvery-gray radioactive metal resembles platinum and is typically found as a gray powder. Technetium dissolves in aqua regia, nitric acid, and concentrated sulfuric acid, but is insoluble in hydrochloric acid. Discovered by Carlo Perrier and Emilio Segre in 1937.</p>",
                    solution_hi: "<p>46.(a) <strong>समूह 7. </strong>टेक्नेटियम एक रासायनिक तत्व है जिसका परमाणु क्रमांक 43 है, जिसे प्रतीक (Tc) द्वारा दर्शाया जाता है। यह सिल्वर-ग्रे रेडियोधर्मी धातु प्लैटिनम जैसा दिखता है और सामान्यतः ग्रे पाउडर के रूप में पाया जाता है। टेक्नेटियम एक्वा रेजिया, नाइट्रिक अम्ल और सांद्र सल्फ्यूरिक अम्ल में घुल जाता है, लेकिन हाइड्रोक्लोरिक अम्ल में अघुलनशील होता है। 1937 में कार्लो पेरियर और एमिलियो सेग्रे द्वारा खोजा गया।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Which state launched the \'KSRTC Arogya\' health insurance scheme?</p>",
                    question_hi: "<p>47. किस राज्य ने \'KSRTC आरोग्य\' स्वास्थ्य बीमा योजना शुरू की?</p>",
                    options_en: [
                        "<p>Tamil Nadu</p>",
                        "<p>Karnataka</p>",
                        "<p>Kerala</p>",
                        "<p>Andhra Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>तमिलनाडु</p>",
                        "<p>कर्नाटक</p>",
                        "<p>केरल</p>",
                        "<p>आंध्र प्रदेश</p>"
                    ],
                    solution_en: "<p>47.(b) <strong>Karnataka </strong>Chief Minister Siddaramaiah launched the &lsquo;KSRTC Arogya&rsquo; health insurance scheme to support public transport employees. This scheme offers cashless medical treatment to over 34,000 KSRTC (Karnataka State Road Transport Corporation employees and their families, benefiting around 1.5 lakh people.</p>",
                    solution_hi: "<p>47.(b) <strong>कर्नाटक</strong> के मुख्यमंत्री सिद्धारमैया ने सार्वजनिक परिवहन कर्मचारियों की सहायता के लिए \'KSRTC आरोग्य\' स्वास्थ्य बीमा योजना शुरू की। यह योजना 34,000 से अधिक KSRTC (कर्नाटक राज्य सड़क परिवहन निगम के कर्मचारियों और उनके परिवारों को कैशलेस चिकित्सा उपचार प्रदान करती है, जिससे लगभग 1.5 लाख लोग लाभान्वित होते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. In which of the following states is the Thousand Pillar temple located, in which thousand pillars are built?</p>",
                    question_hi: "<p>48. निम्नलिखित में से किस राज्य में हजार स्तंभ मंदिर स्थित है, जिसमें हजार स्तंभ निर्मित हैं?</p>",
                    options_en: [
                        "<p>Tamil Nadu</p>",
                        "<p>Maharashtra</p>",
                        "<p>Kerala</p>",
                        "<p>Telangana</p>"
                    ],
                    options_hi: [
                        "<p>तमिलनाडु</p>",
                        "<p>महाराष्ट्र</p>",
                        "<p>केरल</p>",
                        "<p>तेलंगाना</p>"
                    ],
                    solution_en: "<p>48.(d) <strong>Telangana.</strong> The Thousand Pillars Temple (Warangal) was built in the 12th century by the Kakatiya King Rudra Deva. It is also known by the name of Sri Rudreshwara Swamy Temple. At this temple, three deities - Lord Shiva, Lord Vishnu and Lord Surya are worshiped. They are known as Trikutalayam.</p>",
                    solution_hi: "<p>48.(d) <strong>तेलंगाना।</strong> हजार स्तंभ मंदिर (वारंगल) का निर्माण 12वीं शताब्दी में काकतीय राजा रुद्र देव द्वारा कराया गया था। इसे श्री रुद्रेश्वर स्वामी मंदिर के नाम से भी जाना जाता है। इस मंदिर में तीन देवताओं &lsquo;भगवान शिव, भगवान विष्णु और भगवान सूर्य&rsquo; की पूजा की जाती है। इन्हें त्रिकुटलायम के नाम से जाना जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. The length of a tennis court is _______.</p>",
                    question_hi: "<p>49. टेनिस कोर्ट की लंबाई______होती है।</p>",
                    options_en: [
                        "<p>78 ft</p>",
                        "<p>70 ft</p>",
                        "<p>80 ft</p>",
                        "<p>73 ft</p>"
                    ],
                    options_hi: [
                        "<p>78 ft</p>",
                        "<p>70 ft</p>",
                        "<p>80 ft</p>",
                        "<p>73 ft</p>"
                    ],
                    solution_en: "<p>49.(a) <strong>78ft.</strong> According to the regulations of the International Tennis Federation (ITF), &lsquo;tennis&rsquo; global governing body: Its court must be rectangular in shape, measuring 23.77 metres long and the width, for doubles (10.97 metres) and singles (8.23 metres). Grand Slam Tournaments and Court Types: Grass Courts - Associated with Wimbledon Open Grand Slam. Clay Courts - Associated with the French Open Grand Slam. Hard Courts - Associated with Australian Open Grand Slam and US Open Grand Slam.</p>",
                    solution_hi: "<p>49.(a) <strong>78 फीट। </strong>टेनिस की वैश्विक शासी निकाय, अंतर्राष्ट्रीय टेनिस महासंघ (ITF) के नियमों के अनुसार: इसका कोर्ट आकार में आयताकार होना चाहिए, जिसकी लंबाई 23.77 मीटर और चौड़ाई युगल के लिए 10.97 मीटर तथा एकल के लिए 8.23 मीटर होनी चाहिए। ग्रैंड स्लैम टूर्नामेंट और कोर्ट के प्रकार: ग्रास कोर्ट - विंबलडन ओपन ग्रैंड स्लैम से संबद्ध। क्ले कोर्ट - फ्रेंच ओपन ग्रैंड स्लैम से संबद्ध। हार्ड कोर्ट - ऑस्ट्रेलियन ओपन ग्रैंड स्लैम और यूएस ओपन ग्रैंड स्लैम से संबद्ध।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which country was declared malaria-free by the WHO in January 2025?</p>",
                    question_hi: "<p>50. जनवरी 2025 में WHO ने किस देश को मलेरिया मुक्त घोषित किया है?</p>",
                    options_en: [
                        "<p>India</p>",
                        "<p>Georgia</p>",
                        "<p>Sri Lanka</p>",
                        "<p>Brazil</p>"
                    ],
                    options_hi: [
                        "<p>भारत</p>",
                        "<p>जॉर्जिया</p>",
                        "<p>श्रीलंका</p>",
                        "<p>ब्राज़ील</p>"
                    ],
                    solution_en: "<p>50.(b) Georgia has become the 45th country and the first in the WHO European Region to attain malaria-free status. To achieve certification, a country must prove that the chain of indigenous transmission has been interrupted for at least three consecutive years. Georgia: Capital - Tbilisi.</p>",
                    solution_hi: "<p>50.(b) जॉर्जिया मलेरिया मुक्त दर्जा प्राप्त करने वाला 45वाँ देश और WHO यूरोपीय क्षेत्र का पहला देश बन गया है। प्रमाणन प्राप्त करने के लिए, किसी देश को यह साबित करना होगा कि स्वदेशी संचरण की श्रृंखला कम से कम लगातार तीन वर्षों तक बाधित रही है। जॉर्जिया: राजधानी - त्बिलिसी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. By selling an article at <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of its actual selling price, Ramesh incurs a loss of 16%. If he sells it at 73% of its actual selling price, then the profit percentage is:</p>",
                    question_hi: "<p>51. किसी वस्तु को इसके मूल विक्रय मूल्य के <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> पर बेचने पर रमेश को 16% की हानि होती है। यदि वह इसे मूल विक्रय मूल्य के 73% पर बेचता है, तो लाभ प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>52.1%</p>",
                        "<p>53.3%</p>",
                        "<p>52.7%</p>",
                        "<p>54.7%</p>"
                    ],
                    options_hi: [
                        "<p>52.1%</p>",
                        "<p>53.3%</p>",
                        "<p>52.7%</p>",
                        "<p>54.7%</p>"
                    ],
                    solution_en: "<p>51.(b) SP &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = CP &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>100</mn></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>S</mi><mi>P</mi></mrow><mrow><mi>C</mi><mi>P</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>100</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>10</mn></mfrac></math><br>After he sells it at 73% of its actual selling price <br>Then new selling price = 21 &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>73</mn><mn>100</mn></mfrac></math> = 15.33<br>Profit percentage = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>.</mo><mn>33</mn><mo>-</mo><mn>10</mn></mrow><mn>10</mn></mfrac></math> &times; 100 = 53.3%</p>",
                    solution_hi: "<p>51.(b) विक्रय मूल्य &times; <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = क्रय मूल्य &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>100</mn></mfrac></math><br><math style=\"font-family: Verdana;\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>84</mn><mn>100</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>21</mn><mn>10</mn></mfrac></math><br>इसके बाद वह इसे इसके वास्तविक विक्रय मूल्य के 73% पर बेचता है <br>तो नया विक्रय मूल्य = 21 &times;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>73</mn><mn>100</mn></mfrac></math> = 15.33&nbsp;<br>लाभ प्रतिशत = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>15</mn><mo>.</mo><mn>33</mn><mo>-</mo><mn>10</mn></mrow><mn>10</mn></mfrac></math> &times; 100 = 53.3%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. The smallest 1-digit number to be added to the 6-digit number 910300 so that it is completely divisible by 11 is:</p>",
                    question_hi: "<p>52. वह एक अंक की सबसे छोटी संख्या बताइए, जिसे 6 अंकों की संख्या 910300 में जोड़ने पर संख्या 11 से पूर्णतः विभाज्य हो जाए।</p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>2</p>",
                        "<p>5</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>2</p>",
                        "<p>5</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>52.(c)<br>Number = 910300<br>For a number to be divisible by 11 the difference between the sum of odd and even place digits is equal to 0 or multiple of 11 . <br>Sum of odd place digits = 0 + 3 + 1 = 4<br>Sum of even place digits = 0 + 0 + 9 = 9<br>Smallest 1 digit number to be added = 9 - 4 = 5</p>",
                    solution_hi: "<p>52.(c)<br>संख्या = 910300<br>किसी संख्या के 11 से विभाज्य होने के लिए विषम और सम स्थान के अंकों के योग के बीच का अंतर 0 या 11 के गुणज के बराबर होता है। <br>विषम स्थान के अंकों का योग = 0 + 3 + 1 = 4<br>सम स्थान के अंकों का योग = 0 + 0 + 9 = 9<br>जोड़ी जाने वाली सबसे छोटी 1 अंकीय संख्या = 9 - 4 = 5</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. A retailer announces a discount of 25% for selling a mobile phone marked at ₹48,000. The cost price of the mobile phone is 45% below the marked price. He offers a further discount of 7.5% if the buyer returns his old mobile phone. What is the ratio of the profit percentages of the retailer with the return of the old mobile phone scheme to that of without the return of the old mobile phone, respectively?</p>",
                    question_hi: "<p>53. एक खुदरा विक्रेता ₹48,000 अंकित मूल्य वाले मोबाइल फोन की बिक्री पर 25% की छूट की घोषणा करता है। मोबाइल फोन का क्रय मूल्य उसके अंकित मूल्य से 45% कम है। यदि खरीदार अपना पुराना मोबाइल फोन लौटाता है तो वह 7.5% की और छूट देता है। पुराने मोबाइल फोन लौटाने की योजना के साथ और पुराना मोबाइल फोन लौटाने की योजना के बिना खुदरा विक्रेता को होने वाले क्रमशः प्रतिशत लाभ का अनुपात ज्ञात करें।</p>",
                    options_en: [
                        "<p>32 : 23</p>",
                        "<p>12 : 11</p>",
                        "<p>11 : 12</p>",
                        "<p>23 : 32</p>"
                    ],
                    options_hi: [
                        "<p>32 : 23</p>",
                        "<p>12 : 11</p>",
                        "<p>11 : 12</p>",
                        "<p>23 : 32</p>"
                    ],
                    solution_en: "<p>53.(d) According to question,<br>C.P. of Mobile phone = 55% of M.P. = <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 48000 = 26,400<br>S.P. of Mobile phone with returning old phone = 48000 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>92</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = 33300<br>Then, Profit% = <math display=\"inline\"><mfrac><mrow><mn>33300</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>26400</mn></mrow><mrow><mn>26400</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6900</mn><mn>264</mn></mfrac></math> %<br>S.P. of Mobile phone without returning old phone = 75% of M.P. = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 48000 = 36,000<br>Then, Profit % = <math display=\"inline\"><mfrac><mrow><mn>36000</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>26400</mn></mrow><mrow><mn>26400</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9600</mn><mn>264</mn></mfrac></math> %<br>Now,<br>Required ratio &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6900</mn><mn>264</mn></mfrac></math>&nbsp;% : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9600</mn><mn>264</mn></mfrac></math>&nbsp;% = 23 : 32</p>",
                    solution_hi: "<p>53.(d) प्रश्न के अनुसार,<br>मोबाइल फोन का क्रय मूल्य = 55% of M.P. = <math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 48000 = 26,400<br>पुराना फ़ोन लौटाने पर मोबाइल फ़ोन का विक्रय मूल्य = 48000 &times; <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>92</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = 33300<br>फिर, लाभ% = <math display=\"inline\"><mfrac><mrow><mn>33300</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>26400</mn></mrow><mrow><mn>26400</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6900</mn><mn>264</mn></mfrac></math>&nbsp;%<br>पुराना फ़ोन लौटाए बिना मोबाइल फ़ोन का विक्रय मूल्य = 75% of M.P. = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> &times; 48000 = 36,000<br>फिर, लाभ % = <math display=\"inline\"><mfrac><mrow><mn>36000</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>26400</mn></mrow><mrow><mn>26400</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9600</mn><mn>264</mn></mfrac></math>&nbsp;%<br>अब,<br>आवश्यक अनुपात &rarr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>6900</mn><mn>264</mn></mfrac></math>&nbsp;% : <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9600</mn><mn>246</mn></mfrac></math> % = 23 : 32</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The smallest natural number which is divisible by 9, 6, 72 and 76 is:</p>",
                    question_hi: "<p>54. वह सबसे छोटी प्राकृतिक संख्या ज्ञात करें जो 9, 6, 72 और 76 से विभाज्य है।</p>",
                    options_en: [
                        "<p>1368</p>",
                        "<p>1327</p>",
                        "<p>1338</p>",
                        "<p>1359</p>"
                    ],
                    options_hi: [
                        "<p>1368</p>",
                        "<p>1327</p>",
                        "<p>1338</p>",
                        "<p>1359</p>"
                    ],
                    solution_en: "<p>54.(a) Smallest number which divisible by 9 , 6 , 72 and 76<br>9 = 3 &times; 3<br>6 =&nbsp;3 &times; 2<br>72 =&nbsp;2 &times; 2 &times; 2 &times; 3 &times; 3 <br>76 =&nbsp;2 &times; 2 &times; 19<br>LCM (9 , 6 ,72 and 76 ) <br>= 2 &times; 2 &times; 2 &times; 3 &times; 3 &times; 19 <br>= 1368</p>",
                    solution_hi: "<p>54.(a) सबसे छोटी संख्या जो 9, 6, 72 और 76 से विभाज्य है<br>9 = 3 &times; 3<br>6 =&nbsp;3 &times; 2<br>72 =&nbsp;2 &times; 2 &times; 2 &times; 3 &times; 3 <br>76 = 2 &times; 2 &times; 19<br>LCM (9 , 6 ,72 और 76 ) <br>= 2 &times; 2 &times; 2 &times; 3 &times; 3 &times; 19 <br>= 1368</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. If 13 &times; 7 &times; 686 &divide; <math display=\"inline\"><msqrt><mn>2401</mn></msqrt></math> = y + 785, then find the value of y.</p>",
                    question_hi: "<p>55. यदि 13 &times; 7 &times; 686 &divide; <math display=\"inline\"><msqrt><mn>2401</mn></msqrt></math> = y + 785 है, तो y का मान ज्ञात कीजिए ?</p>",
                    options_en: [
                        "<p>489</p>",
                        "<p>488</p>",
                        "<p>498</p>",
                        "<p>499</p>"
                    ],
                    options_hi: [
                        "<p>489</p>",
                        "<p>488</p>",
                        "<p>498</p>",
                        "<p>499</p>"
                    ],
                    solution_en: "<p>55.(a) <br>13 &times; 7 &times; 686 &divide; <math display=\"inline\"><msqrt><mn>2401</mn></msqrt></math> = y + 785<br>13 &times; 7 &times; 686 &divide; 49 = y + 785<br>13 &times; 7 &times; 14 = y + 785<br>y = 1274 - 785 = 489</p>",
                    solution_hi: "<p>55.(a) <br>13 &times; 7 &times; 686 &divide; <math display=\"inline\"><msqrt><mn>2401</mn></msqrt></math> = y + 785<br>13 &times; 7 &times; 686 &divide; 49 = y + 785<br>13 &times; 7 &times; 14 = y + 785<br>y = 1274 - 785 = 489</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. The given expression is equivalent to:<br><math display=\"inline\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></msqrt></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow></mfrac></msqrt></math></p>",
                    question_hi: "<p>56. दिया गया व्यंजक किस विकल्प के तुल्य है?<br><math display=\"inline\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></msqrt></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow></mfrac></msqrt></math></p>",
                    options_en: [
                        "<p>2sin &theta;</p>",
                        "<p>2 cosec &theta;</p>",
                        "<p>2 tan &theta;</p>",
                        "<p>2 tan &theta; sec &theta;</p>"
                    ],
                    options_hi: [
                        "<p>2sin &theta;</p>",
                        "<p>2 cosec &theta;</p>",
                        "<p>2 tan &theta;</p>",
                        "<p>2 tan &theta; sec &theta;</p>"
                    ],
                    solution_en: "<p>56.(b) <br><math display=\"inline\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></msqrt></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow></mfrac></msqrt></math><br>= <math display=\"inline\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></msqrt></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></msqrt></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></math> = 2 cosec &theta;</p>",
                    solution_hi: "<p>56.(b) <br><math display=\"inline\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></msqrt></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow></mfrac></msqrt></math><br>= <math display=\"inline\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></msqrt></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mi>&#160;</mi><mo>-</mo><mn>1</mn></mrow></mfrac></msqrt></math><br>= <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>+</mo><mn>1</mn></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></math> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi><mo>-</mo><mn>1</mn></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mi>&#952;</mi></mrow><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>&#952;</mi></mrow></mfrac></math> = 2 cosec &theta;</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. A cuboidal water purifier can carry 10 litres of water. Its breadth is <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of its height and length is <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> of the sum of the breadth and height. The height of the tank is:</p>",
                    question_hi: "<p>57. एक घनाभ के आकार के वॉटर प्यूरिफायर में 10 लीटर पानी आ सकता है। इसकी चौड़ाई, इसकी ऊंचाई की <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> है तथा लंबाई, चौड़ाई और ऊंचाई के योग की <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> है। टैंक की ऊंचाई कितनी है?</p>",
                    options_en: [
                        "<p>20 cm</p>",
                        "<p>10 cm</p>",
                        "<p>80 cm</p>",
                        "<p>50 cm</p>"
                    ],
                    options_hi: [
                        "<p>20 cm</p>",
                        "<p>10 cm</p>",
                        "<p>80 cm</p>",
                        "<p>50 cm</p>"
                    ],
                    solution_en: "<p>57.(d) <br>As we know, 1 litre = 1000 cm<sup>3</sup><br>Then, 10 litres = 10000 cm<sup>3</sup><br>Let the height be15x<br>Breadth of cuboidal water purifier = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 15x = 3x<br>Length of cuboidal water purifier = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>(3x + 15x) = 6x<br>Now, volume of cuboidal water purifier = 3x&nbsp;&times; 6x &times; 15x = 270x<sup>3</sup><br>10000 = 270x<sup>3</sup><br><math display=\"inline\"><mfrac><mrow><mn>1000</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> = x<sup>3</sup><br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math><br>So, the height of cuboidal water purifier = 15 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 50 cm</p>",
                    solution_hi: "<p>57.(d) <br>जैसा कि हम जानते हैं, 1 लीटर = 1000 cm<sup>3</sup><br>फिर, 10 लीटर = 10000 cm<sup>3</sup><br>माना ऊंचाई 15x&nbsp;है<br>घनाकार जल शोधक की चौड़ाई = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; 15x = 3x<br>घनाकार जल शोधक की लंबाई = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>(3x + 15x) = 6x<br>अब, घनाकार जल शोधक का आयतन = 3x&nbsp;&times; 6x &times; 15x = 270x<sup>3</sup><br>10000 = 270x<sup>3</sup><br><math display=\"inline\"><mfrac><mrow><mn>1000</mn></mrow><mrow><mn>27</mn></mrow></mfrac></math> = x<sup>3</sup><br>&rArr; x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>3</mn></mfrac></math><br>तो, घनाकार जल शोधक की ऊंचाई = 15 &times; <math display=\"inline\"><mfrac><mrow><mn>10</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> = 50 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. The expenditure of Sudha is 200% more than her savings. If her expenditure decreases by 6% and savings increase by 26%, then by what percent does her income increase?</p>",
                    question_hi: "<p>58. सुधा का व्यय उसकी बचत से 200% अधिक है। यदि उसके व्यय में 6% की कमी होती है और बचत में 26% की वृद्धि होती है, तो उसकी आय में कितने प्रतिशत की वृद्धि होती है?</p>",
                    options_en: [
                        "<p>0.02</p>",
                        "<p>0.05</p>",
                        "<p>0.06</p>",
                        "<p>0.07</p>"
                    ],
                    options_hi: [
                        "<p>0.02</p>",
                        "<p>0.05</p>",
                        "<p>0.06</p>",
                        "<p>0.07</p>"
                    ],
                    solution_en: "<p>58.(a)<br>Ratio&nbsp; &nbsp; &nbsp;- initial : final<br>expend. -&nbsp; 300 : 282 <br>Savings -&nbsp; 100 : 126<br>-----------------------------------<br><strong>Income&nbsp; - 400 : 408</strong> <br>Required increment% = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math> &times; 100 = 2% = 0.02</p>",
                    solution_hi: "<p>58.(a)<br>अनुपात - प्रारंभिक : अंतिम<br>व्यय&nbsp; &nbsp; &nbsp;-&nbsp; &nbsp;300&nbsp; &nbsp; &nbsp;:&nbsp; 282 <br>बचत&nbsp; &nbsp; -&nbsp; &nbsp;100&nbsp; &nbsp; &nbsp;:&nbsp; 126<br>-----------------------------------<br><strong>आय&nbsp; &nbsp; &nbsp;-&nbsp; &nbsp;400&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;408</strong> <br>आवश्यक वृद्धि% = <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>400</mn></mrow></mfrac></math> &times; 100 = 2% = 0.02</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. By decreasing 17&deg; from each angle of a triangle, the ratio of their angles is 3 : 4 : 7. The&nbsp;radian measure of the greatest angle is:</p>",
                    question_hi: "<p>59. एक त्रिभुज के प्रत्येक कोण से 17&deg; घटाने पर, उनके कोणों का अनुपात 3: 4: 7 है। सबसे बड़े कोण का&nbsp;रेडियन माप क्या होगा ?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>153</mn><mi>&#960;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>167</mn><mi>&#960;</mi></mrow><mrow><mn>180</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>163</mn><mi>&#960;</mi></mrow><mrow><mn>180</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>163</mn><mi>&#960;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>153</mn><mi>&#960;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>167</mn><mi>&#960;</mi></mrow><mrow><mn>180</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>163</mn><mi>&#960;</mi></mrow><mrow><mn>180</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>163</mn><mi>&#960;</mi></mrow><mrow><mn>360</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>59.(d)<br>Let initial angles are 3x + 17&deg;,4x + 17&deg; and 7x+ 17&deg;<br>Then, 14x + 51&deg; = 180&deg;<br>14x = 129&deg;<br>x = <math display=\"inline\"><mfrac><mrow><mn>129</mn><mo>&#176;</mo></mrow><mrow><mn>14</mn></mrow></mfrac></math> <br>Largest angle (7x + 17) = 7 &times; <math display=\"inline\"><mfrac><mrow><mn>129</mn><mo>&#176;</mo></mrow><mrow><mn>14</mn></mrow></mfrac></math> + 17 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#960;</mi><mn>180</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mi>&#960;</mi></mrow><mn>360</mn></mfrac></math> radian.</p>",
                    solution_hi: "<p>59.(d)<br>माना प्रारंभिक कोण 3x + 17&deg;,4x + 17&deg; और 7x+ 17&deg; है <br>तो , 14x + 51&deg; = 180&deg;<br>14x = 129&deg;<br>x = <math display=\"inline\"><mfrac><mrow><mn>129</mn><mo>&#176;</mo></mrow><mrow><mn>14</mn></mrow></mfrac></math><br>सबसे बड़ा कोण (7x + 17) = 7 &times; <math display=\"inline\"><mfrac><mrow><mn>129</mn><mo>&#176;</mo></mrow><mrow><mn>14</mn></mrow></mfrac></math> + 17 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math><br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mo>&#176;</mo></mrow><mn>2</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>&#960;</mi><mn>180</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>163</mn><mi>&#960;</mi></mrow><mn>360</mn></mfrac></math> रेडियन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. If x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 2, then find the value of (x<sup>2723</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3356</mn></msup></mfrac></math>).</p>",
                    question_hi: "<p>60. यदि x + <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>x</mi></mrow></mfrac></math> = 2 है, तो (x<sup>2723</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3356</mn></msup></mfrac></math>) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>0</p>",
                        "<p>-1</p>"
                    ],
                    options_hi: [
                        "<p>1</p>",
                        "<p>2</p>",
                        "<p>0</p>",
                        "<p>-1</p>"
                    ],
                    solution_en: "<p>60.(b) <strong>Given:</strong> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 2<br>Put the value of x&nbsp;= 1 satisfies the equation,<br>&rArr; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1</mn></mfrac></math> = 2 <br>&rArr; LHS = RHS<br>Now,<br>(x<sup>2723</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3356</mn></msup></mfrac></math>) <br>= (1)<sup>2723</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>3356</mn></msup></mfrac></math> <br>= 1 + 1 = 2</p>",
                    solution_hi: "<p>60.(b) <strong>दिया गया है :</strong> x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 2<br>x = 1 का मान समीकरण को संतुष्ट करता है,<br>&rArr; x + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>x</mi></mfrac></math> = 1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>1</mn></mfrac></math> = 2 <br>&rArr; LHS = RHS<br>अब,<br>(x<sup>2723</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mi>x</mi><mn>3356</mn></msup></mfrac></math>) <br>= (1)<sup>2723</sup> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msup><mrow><mo>(</mo><mn>1</mn><mo>)</mo></mrow><mn>3356</mn></msup></mfrac></math> <br>= 1 + 1 = 2</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. In a collection of rare coins, there is one gold coin for every four non-gold coins. If 20 more gold coins are added to the collection, the ratio of the number of gold coins to that of non-gold coins will be 2 : 3. The total number of coins in the collection will now become ________.</p>",
                    question_hi: "<p>61. दुर्लभ सिक्कों के संग्रह में, प्रत्येक चार गैर-सोने के सिक्कों के लिए एक सोने का सिक्का है। यदि संग्रह में 20 और सोने के सिक्के जोड़े जाते हैं, तो सोने के सिक्कों की संख्या और गैर-सोने के सिक्कों की संख्या का अनुपात 2 : 3 होगा। संग्रह में सिक्कों की कुल संख्या अब ________ हो जाएगी।</p>",
                    options_en: [
                        "<p>80</p>",
                        "<p>60</p>",
                        "<p>100</p>",
                        "<p>48</p>"
                    ],
                    options_hi: [
                        "<p>80</p>",
                        "<p>60</p>",
                        "<p>100</p>",
                        "<p>48</p>"
                    ],
                    solution_en: "<p>61.(a) Let the number of gold coins and non gold coins be G&nbsp;and N respectively,<br>&rArr; G = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> &times; N<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>G</mi><mi>N</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>G = x&nbsp;and N = 4x<br>According to the question,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>20</mn></mrow><mrow><mn>4</mn><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>&rArr; 3x + 60 = 8x<br>&rArr; x = 12<br>Gold coins = 12<br>Non gold coins = 4 &times; 12 = 48<br>Total number of coins in the collection <br>= 12 + 48 + 20 <br>= 80</p>",
                    solution_hi: "<p>61.(a) माना , सोने के सिक्कों और गैर सोने के सिक्कों की संख्या क्रमशः G और N है<br>&rArr; G = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math> &times; N<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>G</mi><mi>N</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>4</mn></mfrac></math><br>G = x&nbsp;और N = 4x<br>प्रश्न के अनुसार,<br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mn>20</mn></mrow><mrow><mn>4</mn><mi>x</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>&rArr; 3x + 60 = 8x<br>&rArr; x = 12<br>सोने के सिक्के = 12<br>गैर सोने के सिक्के = 4 &times; 12 = 48<br>संग्रह में सिक्कों की कुल संख्या<br>= 12 + 48 + 20 <br>= 80</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A man\'s speed in still water is 4 km/h more than the speed of the current. If the man takes a total of 10 h to cover 45 km downstream and 35 km upstream, then the speed of the man in still water is:</p>",
                    question_hi: "<p>62. शांत जल में एक व्यक्ति की चाल, धारा की चाल से 4 km/h अधिक है। यदि वह व्यक्ति धारा की अनुकूल दिशा में 45 km और धारा की प्रतिकूल दिशा में 35 km की दूरी तय करने में कुल 10 घंटे लेता है, तो शांत जल में व्यक्ति की चाल कितनी है?</p>",
                    options_en: [
                        "<p>20 km/h</p>",
                        "<p>18 km/h</p>",
                        "<p>15 km/h</p>",
                        "<p>22 km/h</p>"
                    ],
                    options_hi: [
                        "<p>20 km/h</p>",
                        "<p>18 km/h</p>",
                        "<p>15 km/h</p>",
                        "<p>22 km/h</p>"
                    ],
                    solution_en: "<p>62.(a)<br>Let speed of current = x&nbsp;km/h<br>then, speed of man = (x + 4)&nbsp;km/h<br>According to question,<br><math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>4</mn></mfrac></math> = 10<br><math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math> = 10 - 8.75<br><math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math> = 1.25<br>45 = 2.5x&nbsp;+ 5<br>2.5x&nbsp;= 40 <br>&rArr; x = 16<br>Speed of man = x + 4 = 20 km/h</p>",
                    solution_hi: "<p>62.(a)<br>माना धारा की गति = x&nbsp;km/h<br>तो, व्यक्ति की गति = (x + 4)&nbsp;km/hH<br>प्रश्न के अनुसार,<br><math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math> + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>4</mn></mfrac></math> = 10<br><math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math> = 10 - 8.75<br><math display=\"inline\"><mfrac><mrow><mn>45</mn></mrow><mrow><mn>2</mn><mi>x</mi><mo>+</mo><mn>4</mn></mrow></mfrac></math> = 1.25<br>45 = 2.5x&nbsp;+ 5<br>2.5x&nbsp;= 40 <br>&rArr; x = 16<br>व्यक्ति की गति = x + 4 = 20 km/h</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. A chemist prepares 100 grams of medicine by mixing two ingredients X and Y. The rate of X is ₹1,500 per 100 grams and that of Y is ₹50 per 10 grams. X and Y are mixed in such a way that the cost of the resulting medicine is ₹13 per gram. What is the quantity of X (in grams) in the medicine?</p>",
                    question_hi: "<p>63. कोई केमिस्ट, दो अवयवों X और Y को मिश्रित करके 100 ग्राम औषधि तैयार करता है। X का मूल्य ₹1,500 प्रति 100 ग्राम है और Y का मूल्य ₹50 प्रति 10 ग्राम है। X और Y को इस प्रकार मिश्रित किया जाता है कि परिणामी औषधि का मूल्य ₹13 प्रति ग्राम होता है। औषधि में X की मात्रा (ग्राम में) कितनी है?</p>",
                    options_en: [
                        "<p>10</p>",
                        "<p>30</p>",
                        "<p>80</p>",
                        "<p>20</p>"
                    ],
                    options_hi: [
                        "<p>10</p>",
                        "<p>30</p>",
                        "<p>80</p>",
                        "<p>20</p>"
                    ],
                    solution_en: "<p>63.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582542162.png\" alt=\"rId50\" width=\"153\" height=\"133\"><br>According to the question,<br>(8 + 2) unit = 100 gm<br>10 unit = 100 gm &rArr;&nbsp;1 unit = 10 gm<br>Then, quantity of X (8 unit) = 8 &times; 10 = 80 gm</p>",
                    solution_hi: "<p>63.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582542251.png\" alt=\"rId51\" width=\"174\" height=\"162\"><br>प्रश्न के अनुसार,<br>(8 + 2) इकाई = 100 ग्राम<br>10 इकाई = 100 ग्राम &rArr; 1 इकाई = 10 ग्राम<br>तब, X (8 इकाई) की मात्रा = 8 &times; 10 = 80 ग्राम</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. Ratnesh and Vineet worked on alternate days, starting with Ratnesh. The entire work got completed in 54<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> days. If Ratnesh alone can finish the work in 48 days, then in how many days can Vineet alone do the work?</p>",
                    question_hi: "<p>64. रत्नेश और विनीत बारी-बारी से एक दिन छोड़कर काम करते हैं और रत्नेश पहले दिन काम शुरू करता है। संपूर्ण काम 54<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>&nbsp;दिनों में पूरा हो जाता है। यदि रत्नेश अकेले उस काम को 48 दिनों में पूरा कर सकता है, तो विनीत अकेले उस काम को कितने दिनों में पूरा कर सकता है?</p>",
                    options_en: [
                        "<p>64</p>",
                        "<p>62</p>",
                        "<p>60</p>",
                        "<p>66</p>"
                    ],
                    options_hi: [
                        "<p>64</p>",
                        "<p>62</p>",
                        "<p>60</p>",
                        "<p>66</p>"
                    ],
                    solution_en: "<p>64.(a)<br>Ratnesh and Vineet worked on alternate days<br>Total cycle = <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 27<br>Remaining <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> days Ratnesh do the work<br>So, if they together do the work then,<br>27V + (27 + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>)R = 48R<br>27V = 48R&nbsp; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>111</mn><mi>R</mi></mrow><mn>4</mn></mfrac></math> <br>27V = <math display=\"inline\"><mfrac><mrow><mn>192</mn><mi>R</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>111</mn><mi>R</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>27V = <math display=\"inline\"><mfrac><mrow><mn>81</mn><mi>R</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>V = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>R</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math> <br>V : R = 3 : 4 <br>&rArr;&nbsp;efficiency of (vineet : Ratnesh) = 3 : 4<br>Total work = 48R = 48 &times; 4 = 192 unit<br>Hence, time taken by Vineet to do the whole work alone = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>192</mn><mn>3</mn></mfrac></math> = 64 days.</p>",
                    solution_hi: "<p>64.(a)<br>रत्नेश और विनीत बारी-बारी से एक दिन छोड़कर काम करते है तो,<br>कुल चक्र = <math display=\"inline\"><mfrac><mrow><mn>54</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 27<br>शेष <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> दिन रत्नेश कार्य करते हैं<br>अतः, यदि वे मिलकर कार्य करते हैं,<br>27V + (27 + <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>)R = 48R<br>27V = 48R&nbsp; - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>111</mn><mi>R</mi></mrow><mn>4</mn></mfrac></math> <br>27V = <math display=\"inline\"><mfrac><mrow><mn>192</mn><mi>R</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>111</mn><mi>R</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>27V = <math display=\"inline\"><mfrac><mrow><mn>81</mn><mi>R</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math><br>V = <math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>R</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math> <br>V : R = 3 : 4<br>&rArr; (विनीत : रत्नेश) की दक्षता = 3 : 4<br>कुल कार्य = 48R = 48 &times; 4 = 192 इकाई<br>अतः, विनीत द्वारा पूरा कार्य अकेले करने में लिया गया समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>192</mn><mn>3</mn></mfrac></math> = 64 दिन</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Two trains of equal length are running on parallel lines in the same direction at speeds of 40&nbsp;km/h and 22 km/h. The faster train passes the slower train in 40 seconds. The length of each&nbsp;train is:</p>",
                    question_hi: "<p>65. समान लंबाई की दो ट्रेन समांतर पटरियों पर एक ही दिशा में 40 km/h और 22 km/h की चाल से चल रही हैं। तेज़ चलने वाली ट्रेन, धीमी चलने वाली ट्रेन को 40 सेकंड में पार करती है। प्रत्येक ट्रेन की लंबाई ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>100 meters</p>",
                        "<p>114 meters</p>",
                        "<p>90 meters</p>",
                        "<p>82 meters</p>"
                    ],
                    options_hi: [
                        "<p>100 मीटर</p>",
                        "<p>114 मीटर</p>",
                        "<p>90 मीटर</p>",
                        "<p>82 मीटर</p>"
                    ],
                    solution_en: "<p>65.(a) In the same direction, Relative Speed = 40 km/h &minus; 22 km/h = 18 km/h. <br>= 18 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math> = 5 m/s <br>Distance = Speed &times; Time<br>Sum of both train lengths = 2L<br>2L = 5 &times; 40<br>L = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 100 m</p>",
                    solution_hi: "<p>65.(a) एक ही दिशा में, सापेक्ष चाल = 40 किमी/घंटा - 22 किमी/घंटा = 18 किमी/घंटा <br>= 18 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math> = 5 मीटर/सेकंड <br>दूरी = चाल &times; समय<br>दोनों ट्रेन की लंबाई = 2L<br>2L = 5 &times; 40<br>L = <math display=\"inline\"><mfrac><mrow><mn>200</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 100 मीटर</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. There are two pipes used to fill a tank and when operated together, they can fill the tank in 20 minutes. If one pipe can fill the tank two and a half times as quickly as the other, then the faster pipe alone can fill the tank in:</p>",
                    question_hi: "<p>66. एक टंकी को भरने के लिए दो पाइपों का उपयोग किया जाता है और जब उन्हें एक साथ चालू किया जाता है, तो वे टंकी को 20 मिनट में भर सकते हैं। यदि एक पाइप, दूसरे पाइप की तुलना में ढाई गुना तेजी से टंकी भर सकता है, तो तेज पाइप अकेले टंकी को कितने समय में भर सकता है?</p>",
                    options_en: [
                        "<p>30 min</p>",
                        "<p>32 min</p>",
                        "<p>34 min</p>",
                        "<p>28 min</p>"
                    ],
                    options_hi: [
                        "<p>30 मिनट</p>",
                        "<p>32 मिनट</p>",
                        "<p>34 मिनट</p>",
                        "<p>28 मिनट</p>"
                    ],
                    solution_en: "<p>66.(c) time taken by (P1 + P2) pipe to fill the tank = 20 minutes</p>\n<p dir=\"ltr\">&nbsp;According to question&nbsp;</p>\n<p dir=\"ltr\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;P1&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; P2</p>\n<p dir=\"ltr\">Efficiency =&nbsp; &nbsp; &nbsp; 2.5 &nbsp; :&nbsp; &nbsp; 1 &nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math> 5&nbsp; : 2</p>\n<p dir=\"ltr\">So, total work = 20 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> 7 = 140</p>\n<p dir=\"ltr\">&nbsp;Faster pipe alone can fill</p>\n<p dir=\"ltr\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>5</mn></mfrac></math> = 28 minutes</p>\n<p>&nbsp;</p>",
                    solution_hi: "<p>66.(c) टैंक को भरने में (P1 + P2) पाइप द्वारा लिया गया समय = 20 मिनट</p>\n<p dir=\"ltr\">प्रश्नानुसार&nbsp;</p>\n<p dir=\"ltr\">&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;P1 &nbsp; &nbsp; &nbsp; &nbsp; P2</p>\n<p dir=\"ltr\">दक्षता&nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; 2.5&nbsp; &nbsp; :&nbsp; &nbsp; 1 &nbsp; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#8658;</mo></math>5&nbsp; : 2</p>\n<p dir=\"ltr\">तो, कुल काम = 20 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&#215;</mo></math> 7 = 140</p>\n<p dir=\"ltr\">&nbsp;अकेले तेज पाइप भर सकता हैं</p>\n<p dir=\"ltr\">= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>140</mn><mn>5</mn></mfrac></math>= 28 मिनट</p>\n<p>&nbsp;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. In an election between two candidates, the defeated candidate secured 42% of the valid votes polled and lost the election by 2545800 votes. If 365500 votes were declared invalid and 30% people did not cast their vote, what was the approximate number of people (in millions) in the electorate who did NOT cast their votes?</p>",
                    question_hi: "<p>67. दो उम्मीदवारों के बीच एक चुनाव में, पराजित उम्मीदवार ने डाले गए वैध मतों का 42% हासिल किया और 2545800 मतों से चुनाव हार गया। यदि 365500 मतों को अवैध घोषित कर दिया गया और 30% लोगों ने अपना मत नहीं डाला, तो मतदाताओं में मत न डालने वाले लोगों की संख्या (मिलियन में) लगभग कितनी थी?</p>",
                    options_en: [
                        "<p>7</p>",
                        "<p>8</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    options_hi: [
                        "<p>7</p>",
                        "<p>8</p>",
                        "<p>6</p>",
                        "<p>5</p>"
                    ],
                    solution_en: "<p>67.(a) <br>Let the total valid votes be V.<br>The defeated candidate received 42% of the valid votes and lost by 2545800 votes.<br>Therefore, the winning candidate received = 100% - 42% = 58% of the valid votes<br>The difference in votes between the two candidates = 58% - 42% = 16% of the valid votes.<br>V &times; 16% = 2545800<br>V = 15,911,250<br>Total votes polled = 15,911,250 + 365500 = 16,276,750<br>30% of people did not cast their votes so 70% did cast their vote.<br>70% = 16,276,750<br>(people who did not cast their votes) 30% = 6,975,750<br>So, approximate number of people in the electorate who did not cast their votes is 7 million</p>",
                    solution_hi: "<p>67.(a) <br>माना कि कुल वैध वोट V हैं।<br>पराजित उम्मीदवार को वैध मतों का 42% प्राप्त हुआ और वह 2545800 मतों से हार गया।<br>इसलिए, विजयी उम्मीदवार को = 100% - 42% = 58% वैध वोट प्राप्त हुए<br>दोनों उम्मीदवारों के बीच वोटों का अंतर 58% - 42% = 16% वैध वोटों का।<br>V &times; 16% = 2545800<br>V = 15,911,250<br>कुल वोट पड़े = 15,911,250 + 365500 = 16,276,750<br>30% लोगों ने वोट नहीं डाला तो 70% ने वोट डाला।<br>70% = 16,276,750<br>(वे लोग जिन्होंने वोट नहीं डाला) 30% = 6,975,750<br>इस प्रकार, मतदाताओं में वोट न डालने वाले लोगों की अनुमानित संख्या 7 मिलियन है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. From a circular sheet of circumference 264 cm, two equal maximum-sized circular plates are cut off. What will be the circumference of each plate? (Use &pi; = <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>)</p>",
                    question_hi: "<p>68. 264 cm परिधि वाली एक वृत्तीय शीट से, दो समान अधिकतम आकार की वृत्ताकार प्लेटें काटी जाती हैं। प्रत्येक प्लेट की परिधि कितनी होगी? (&pi; =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> का प्रयोग कीजिए )</p>",
                    options_en: [
                        "<p>264 cm</p>",
                        "<p>135 cm</p>",
                        "<p>176 cm</p>",
                        "<p>132 cm</p>"
                    ],
                    options_hi: [
                        "<p>264 cm</p>",
                        "<p>135 cm</p>",
                        "<p>176 cm</p>",
                        "<p>132 cm</p>"
                    ],
                    solution_en: "<p>68.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582542713.png\" alt=\"rId54\" width=\"137\" height=\"130\"><br>Circumference of circle = 2&pi;r<br>264 = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; r<br>(bigger circle) r = 42 cm<br>Radius of smaller circles = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 21 cm<br>Circumference of small circles = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 21 = 132 cm</p>",
                    solution_hi: "<p>68.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582542713.png\" alt=\"rId54\" width=\"137\" height=\"130\"><br>वृत्त की परिधि = 2&pi;r<br>264 = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; r<br>(बड़ा वृत्त) r = 42 cm<br>छोटे वृत्तों की त्रिज्या = <math display=\"inline\"><mfrac><mrow><mn>42</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 21 cm<br>छोटे वृत्तों की परिधि = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 21 = 132 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The marked price of an article is ₹600. A shopkeeper purchased it on two successive discounts of 10% and 20%. Не spent ₹68 on transportation and sold the article for ₹600. Find his gain or loss percentage.</p>",
                    question_hi: "<p>69. एक वस्तु का अंकित मूल्य ₹600 है। एक दुकानदार ने इसे 10% और 20% की दो क्रमागत छूटों पर खरीदा। उसने परिवहन पर ₹68 खर्च किए और वस्तु को ₹600 में बेच दिया। उसका लाभ या हानि प्रतिशत ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>15% Profit</p>",
                        "<p>20% Profit</p>",
                        "<p>18% Loss</p>",
                        "<p>25% Loss</p>"
                    ],
                    options_hi: [
                        "<p>15% लाभ</p>",
                        "<p>20% लाभ</p>",
                        "<p>18% हानि</p>",
                        "<p>25% हानि</p>"
                    ],
                    solution_en: "<p>69.(b) <br>CP for shopkeeper = 600 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> + 68 = 500<br>profit% = <math display=\"inline\"><mfrac><mrow><mn>600</mn><mo>-</mo><mn>500</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    solution_hi: "<p>69.(b) <br>दुकानदार के लिए क्रय मूल्य = 600 &times; <math display=\"inline\"><mfrac><mrow><mn>9</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> + 68 = 500<br>लाभ% = <math display=\"inline\"><mfrac><mrow><mn>600</mn><mo>-</mo><mn>500</mn></mrow><mrow><mn>500</mn></mrow></mfrac></math> &times; 100 = 20%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. A hollow spherical shell is made of a metal of density 6 g/cm<sup>3</sup>. Its internal and external radius are 2 cm and 4 cm, respectively. What is the weight (in kg) of the shell ? (take &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>)</p>",
                    question_hi: "<p>70. एक खोखले गोलाकार खोल को 6 g/cm<sup>3</sup> घनत्व वाली धातु से बनाया गया है। इसकी आंतरिक त्रिज्या और बाह्य त्रिज्या क्रमशः 2 cm और 4 cm है | खोल का भार (kg में) ज्ञात कीजिए | (&pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>लीजिए)</p>",
                    options_en: [
                        "<p>2.73</p>",
                        "<p>1.408</p>",
                        "<p>3.28</p>",
                        "<p>1.592</p>"
                    ],
                    options_hi: [
                        "<p>2.73</p>",
                        "<p>1.408</p>",
                        "<p>3.28</p>",
                        "<p>1.592</p>"
                    ],
                    solution_en: "<p>70.(b)<br>Volume of hollow spherical shell = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;(R<sup>3</sup> - r<sup>3</sup>) <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>(4<sup>3</sup> - 2<sup>3</sup>)<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>(64 - 8)<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;&times; 56<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; 22 &times; 8 cm<sup>3</sup><br>Mass = density &times; volume<br>= <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; 22 &times; 8 kg<br>= 1.408 kg</p>",
                    solution_hi: "<p>70.(b)<br>खोखले गोलाकार खोल का आयतन = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math>&pi;(R<sup>3</sup> - r<sup>3</sup>) <br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>(4<sup>3</sup> - 2<sup>3</sup>)<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>(64 - 8)<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;&times; 56<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; 22 &times; 8 cm<sup>3</sup><br>द्रव्यमान = घनत्व &times; आयतन<br>= <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></math> &times; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>3</mn></mfrac></math> &times; 22 &times; 8 kg<br>= 1.408 kg</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Find the simple interest (in ₹) on ₹2000 at 6.5% per annum rate of interest for the period from 3 February 2023 to 17 April 2023.</p>",
                    question_hi: "<p>71. ₹2000 पर 6.5% वार्षिक ब्याज दर पर 3 फरवरी 2023 से 17 अप्रैल 2023 की अवधि के लिए साधारण ब्याज (₹ में) की गणना करें |</p>",
                    options_en: [
                        "<p>24</p>",
                        "<p>25</p>",
                        "<p>26</p>",
                        "<p>27</p>"
                    ],
                    options_hi: [
                        "<p>24</p>",
                        "<p>25</p>",
                        "<p>26</p>",
                        "<p>27</p>"
                    ],
                    solution_en: "<p>71.(c) P = 2000 ₹ , Rate = 6.5% <br>The time period is from 3 February 2023 to 17 April 2023.<br>Total days = 25 + 31 + 17 = 73 days<br>Convert days to years:<br>T = <math display=\"inline\"><mfrac><mrow><mn>73</mn></mrow><mrow><mn>365</mn></mrow></mfrac></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>&thinsp;years<br>Simple Interest (SI) = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>R</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2000</mn><mo>&#215;</mo><mn>6</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>1</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></math> = 26<br>SI = 26 ₹</p>",
                    solution_hi: "<p>71.(c) P = 2000 ₹ , दर = 6.5% <br>समय अवधि 3 फरवरी 2023 से 17 अप्रैल 2023 तक है।<br>कुल दिन = 25 + 31 + 17 = 73<br>दिनों को वर्षों में बदलें:<br>समय = <math display=\"inline\"><mfrac><mrow><mn>73</mn></mrow><mrow><mn>365</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>&thinsp;वर्ष<br>साधारण ब्याज (SI) = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>R</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2000</mn><mo>&#215;</mo><mn>6</mn><mo>.</mo><mn>5</mn><mo>&#215;</mo><mn>1</mn></mrow><mrow><mn>100</mn><mo>&#215;</mo><mn>5</mn></mrow></mfrac></math> = 26<br>SI = 26 ₹</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If p = <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi></mrow></mfrac></math> , then <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi></mrow></mfrac></math> = ?</p>",
                    question_hi: "<p>72. यदि p = <math display=\"inline\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>A</mi><mi>&#160;</mi></mrow></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi></mrow></mfrac></math> का मान किसके बराबर है?</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>p</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>-</mo><mi>&#160;</mi><mi>p</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>p</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math></p>",
                        "<p>p - 1</p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>p</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>1</mn><mo>-</mo><mi>&#160;</mi><mi>p</mi></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mi>p</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math></p>",
                        "<p>p - 1</p>"
                    ],
                    solution_en: "<p>72.(a)<br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>A</mi></mrow></mfrac></math><br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>A</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi></mrow></mfrac></math><br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>P</mi></mfrac></math></p>",
                    solution_hi: "<p>72.(a)<br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>A</mi></mrow></mfrac></math><br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>+</mo><mi>cos</mi><mi>A</mi></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi></mrow><mrow><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi></mrow></mfrac></math><br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi><mo>)</mo></mrow><mrow><mn>1</mn><mo>-</mo><msup><mi>cos</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>i</mi><msup><mi>n</mi><mn>2</mn></msup><mi>A</mi></mrow></mfrac></math><br>&rArr; p = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi><mo>)</mo></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>A</mi></mrow></mfrac></math><br>&rArr; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>sin</mi><mi>A</mi></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>cos</mi><mi>A</mi><mo>)</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi>P</mi></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Manav\'s average earning per month in the first three months of a year was ₹76096. In April, his earning was 75% more than the average earning in the first three months. If his average earning per month for the whole year is ₹94660, then what will be Manav\'s average earning (in ₹) per month from May to December?</p>",
                    question_hi: "<p>73. वर्ष के प्रथम तीन महीनों में मानव की प्रति माह औसत आय ₹76096 थी। अप्रैल में उसकी आय, प्रथम तीन महीनों की औसत आय से 75% अधिक थी। यदि पूरे वर्ष के लिए उसकी प्रति माह औसत आय ₹94660 है, तो मई से दिसंबर तक मानव की प्रति माह औसत आय (₹ में) कितनी होगी?</p>",
                    options_en: [
                        "<p>96804</p>",
                        "<p>96808</p>",
                        "<p>96813</p>",
                        "<p>96803</p>"
                    ],
                    options_hi: [
                        "<p>96804</p>",
                        "<p>96808</p>",
                        "<p>96813</p>",
                        "<p>96803</p>"
                    ],
                    solution_en: "<p>73.(b)<br>Sum of first three months = 76096 &times; 3 = 2,28,288<br>His April month earning = 76096 &times; <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 1,33,168<br>Sum of the money of May to December <br>= 94,660 &times; 12 - (2,28,288 + 1,33,168) <br>= 11,35,920 - 3,61,456 = 7,74,464<br>Required Average = <math display=\"inline\"><mfrac><mrow><mn>7</mn><mo>,</mo><mn>74</mn><mo>,</mo><mn>464</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 96,808</p>",
                    solution_hi: "<p>73.(b)<br>पहले तीन महीनों का योग = 76096 &times; 3 = 2,28,288<br>अप्रैल महीने की कमाई = 76096 &times; <math display=\"inline\"><mfrac><mrow><mn>7</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 1,33,168<br>मई से दिसंबर का योग = 94,660 &times; 12 - (2,28,288 + 1,33,168) <br>= 11,35,920 - 3,61,456 = 7,74,464<br>आवश्यक औसत = <math display=\"inline\"><mfrac><mrow><mn>7</mn><mo>,</mo><mn>74</mn><mo>,</mo><mn>464</mn></mrow><mrow><mn>8</mn></mrow></mfrac></math> = 96,808</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. Divide 25 into two parts such that 6 times of the larger part added to 4 times of the smaller part is equal to 130. Find the larger part.</p>",
                    question_hi: "<p>74. 25 को दो भागों में इस प्रकार विभाजित कीजिए कि छोटे भाग के 4 गुने में बड़े भाग का 6 गुना जोड़ा जाए तो यह 130 के बराबर होता है। बड़ा भाग ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>13</p>",
                        "<p>20</p>",
                        "<p>15</p>",
                        "<p>14</p>"
                    ],
                    options_hi: [
                        "<p>13</p>",
                        "<p>20</p>",
                        "<p>15</p>",
                        "<p>14</p>"
                    ],
                    solution_en: "<p>74.(c) <br>Let the larger and smaller parts be x and y respectively<br>According to question,<br>x + y = 25 ------------- (i)<br>6x&nbsp;+ 4y = 130 ------------- (ii)<br>Solving eqn(i) &amp; (ii) we have ;<br>y = 10, x&nbsp;= 15<br>So, the larger part = 15</p>",
                    solution_hi: "<p>74.(c) <br>माना कि बड़े और छोटे भाग क्रमशः x&nbsp;और y है<br>प्रश्न के अनुसार,<br>x + y = 25 ------------- (i)<br>6x&nbsp;+ 4y = 130 ------------- (ii)<br>समीकरण (i) और (ii) को हल करने पर हमे प्राप्त होता है<br>y = 10, x&nbsp;= 15<br>तो बडा भाग होगा = 15</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Study the given bar-graph and answer the question that follows.<br>The bar-graph shows the sales of books (in thousands) from six branches (B1, B2, B3, B4, B5 and B6) of a publishing company during two consecutive years 2000 and 2001.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582542869.png\" alt=\"rId55\" width=\"223\" height=\"182\"> <br>What is the ratio of the total sales of branch B2 for both years to the total sales of branch B4 for both years?</p>",
                    question_hi: "<p>75. दिए गए दंड-आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्&zwj;न का उत्तर दीजिए।<br>दंड-आलेख दो क्रमिक वर्षों 2000 और 2001 के दौरान एक प्रकाशन कंपनी की छह शाखाओं (B1, B2, B3, B4, B5 और B6) से पुस्तकों की बिक्री (हजार में) दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1741582543016.png\" alt=\"rId56\" width=\"263\" height=\"215\"> <br>Sales (in thousands numbers) = बिक्री (हजार में)<br>Branches = शाखाएं<br>दोनों वर्षों के लिए शाखा B2 की कुल बिक्री और दोनों वर्षों के लिए शाखा B4 की कुल बिक्री का अनुपात कितना है?</p>",
                    options_en: [
                        "<p>6 : 7</p>",
                        "<p>7 : 9</p>",
                        "<p>2 : 3</p>",
                        "<p>3 : 5</p>"
                    ],
                    options_hi: [
                        "<p>6 : 7</p>",
                        "<p>7 : 9</p>",
                        "<p>2 : 3</p>",
                        "<p>3 : 5</p>"
                    ],
                    solution_en: "<p>75.(b) Total sale of branch B2 = 75 + 65 = 140<br>Total sale of branch B4 = 85 + 95 = 180<br>Required ratio = 140 : 180 = 7 : 9</p>",
                    solution_hi: "<p>75.(b) शाखा B2 की कुल बिक्री = 75 + 65 = 140<br>शाखा B4 की कुल बिक्री = 85 + 95 = 180<br>आवश्यक अनुपात = 140 : 180 = 7 : 9</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. The following sentence has been split into four segments. Identify the segment that&nbsp;contains a grammatical error.<br>Hard had she / thrown the basketball / when it fell / on the ground.</p>",
                    question_hi: "<p>76. The following sentence has been split into four segments. Identify the segment that&nbsp;contains a grammatical error.<br>Hard had she / thrown the basketball / when it fell / on the ground.</p>",
                    options_en: [
                        "<p>thrown the basketball</p>",
                        "<p>when it fell</p>",
                        "<p>on the ground</p>",
                        "<p>Hard had she</p>"
                    ],
                    options_hi: [
                        "<p>thrown the basketball</p>",
                        "<p>when it fell</p>",
                        "<p>on the ground</p>",
                        "<p>Hard had she</p>"
                    ],
                    solution_en: "<p>76.(d) Hard had she<br>&lsquo;Hardly&hellip;when&rsquo; is a fixed pair of conjunctions. Hence, &lsquo;Hardly had she&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(d) Hard had she<br>&lsquo;Hardly&hellip;...when&rsquo; एक fixed pair of conjunctions है। अतः, &lsquo;Hardly had she&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the word which means the same as the group of words given.<br>Briefness; shortness of time</p>",
                    question_hi: "<p>77. Select the word which means the same as the group of words given.<br>Briefness; shortness of time</p>",
                    options_en: [
                        "<p>Short</p>",
                        "<p>Abridged</p>",
                        "<p>Brevity </p>",
                        "<p>Summation</p>"
                    ],
                    options_hi: [
                        "<p>Short</p>",
                        "<p>Abridged</p>",
                        "<p>Brevity</p>",
                        "<p>Summation</p>"
                    ],
                    solution_en: "<p>77.(c) Brevity.<br><strong>Brevity -</strong> concise and exact use of words in writing or speech.<br><strong>Short -</strong> measuring a small distance from end to end.<br><strong>Abridged -</strong> shorten (a book, film, speech, etc.) without losing the sense.<br><strong>Summation - </strong>the process of adding things together.</p>",
                    solution_hi: "<p>77.(c) <strong>Brevity -</strong> लेखन या भाषण में शब्दों का संक्षिप्त और सटीक उपयोग।<br><strong>Short -</strong> एक छोटी सी दूरी । <br><strong>Abridged -</strong> बिना तर्क खोये छोटा करना (एक किताब, फिल्म, भाषण, आदि।)<br><strong>Summation -</strong> चीजों को एक साथ जोड़ने की प्रक्रिया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>Peter was the last to know that she had been <span style=\"text-decoration: underline;\">cheating of him</span></p>",
                    question_hi: "<p>78. Select the most appropriate option that can substitute the underlined segment in the given sentence. <br>Peter was the last to know that she had been <span style=\"text-decoration: underline;\">cheating of him</span></p>",
                    options_en: [
                        "<p>cheating in him</p>",
                        "<p>cheating up him</p>",
                        "<p>cheating on him</p>",
                        "<p>cheat of him</p>"
                    ],
                    options_hi: [
                        "<p>cheating in him</p>",
                        "<p>cheating up him</p>",
                        "<p>cheating on him</p>",
                        "<p>cheat of him</p>"
                    ],
                    solution_en: "<p>78.(c) cheating on him<br>There is a prepositional error in the given sentence. &lsquo;Cheat on someone&rsquo; is the correct phrase. Hence, \'cheating on him\' is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(c) cheating on him<br>दिए गए sentence में एक prepositional error है। &lsquo;Cheat on someone&rsquo; सही phrase है। अतः, \'cheating on him\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the one which can be substituted for the given words/ sentence : <br>Rub or wipe out</p>",
                    question_hi: "<p>79. Choose the one which can be substituted for the given words/ sentence : <br>Rub or wipe out</p>",
                    options_en: [
                        "<p>remove </p>",
                        "<p>terminate</p>",
                        "<p>efface </p>",
                        "<p>plunder</p>"
                    ],
                    options_hi: [
                        "<p>remove </p>",
                        "<p>terminate</p>",
                        "<p>efface </p>",
                        "<p>plunder</p>"
                    ],
                    solution_en: "<p>79.(c) efface<br>Efface means to rub out.<br>Remove means to take out. <br>Terminate means to bring to an end. <br>Plunder means to steal goods from (a place or person), typically using force and in a time of war or civil disorder.</p>",
                    solution_hi: "<p>79. (c) efface<br>Efface का अर्थ है &lsquo;मिटा देना &rsquo;।<br>Remove का अर्थ है निकालना।<br>Terminate का अर्थ है समाप्त करना।<br>Plunder का अर्थ है (किसी स्थान या व्यक्ति से ) सामान चोरी करना, आमतौर पर युद्ध या नागरिक अव्यवस्था के समय में बल का उपयोग करना । </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>His father won&rsquo;t be able to leave for Patna <strong><span style=\"text-decoration: underline;\">until they had arrived</span></strong>.</p>",
                    question_hi: "<p>80. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>His father won&rsquo;t be able to leave for Patna <strong><span style=\"text-decoration: underline;\">until they had arrived</span></strong>.</p>",
                    options_en: [
                        "<p>until they arrive</p>",
                        "<p>until they will arrive</p>",
                        "<p>until they will have arrived</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>until they arrive</p>",
                        "<p>until they will arrive</p>",
                        "<p>until they will have arrived</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>80.(a) until they arrive.<br>If two actions take place in the future then the 1st action must be in the present tense(V<sub>1</sub>) and the 2nd action must be in the future tense(will/shall). Hence, &lsquo;had arrived&rsquo; will be removed from the last part of the sentence &lsquo;until they arrive(V<sub>1</sub>)&rsquo; becomes the most appropriate answer.</p>",
                    solution_hi: "<p>80.(a) until they arrive.<br>यदि भविष्य में दो क्रियाएं होती हैं तो पहली क्रिया present tense(V<sub>1</sub>) में होनी चाहिए और दूसरी क्रिया future tense(will/shall) में होनी चाहिए। इसलिए, वाक्य के अंतिम भाग से &lsquo;had arrived&rsquo; को हटा दिया जाएगा &lsquo;until they arrive(V<sub>1</sub>)&rsquo; सबसे उपयुक्त उत्तर होगा |</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the correct passive form of the given sentences.<br>The department conferred a scholarship to Kailash.</p>",
                    question_hi: "<p>81. Select the correct passive form of the given sentences.<br>The department conferred a scholarship to Kailash.</p>",
                    options_en: [
                        "<p>A scholarship had been conferred to Kailash by the Department.</p>",
                        "<p>A scholarship is being conferred to Kailash by the Department.</p>",
                        "<p>A scholarship was conferred to Kailash by the department.</p>",
                        "<p>A scholarship has been conferred in Kailash by the department.</p>"
                    ],
                    options_hi: [
                        "<p>A scholarship had been conferred to Kailash by the Department.</p>",
                        "<p>A scholarship is being conferred to Kailash by the Department.</p>",
                        "<p>A scholarship was conferred to Kailash by the department.</p>",
                        "<p>A scholarship has been conferred in Kailash by the department.</p>"
                    ],
                    solution_en: "<p>81.(c) A scholarship was conferred to Kailash by the department. <br>(a) A scholarship <span style=\"text-decoration: underline;\">had been conferred</span> to Kailash by the department. (Incorrect Tense)<br>(b) A scholarship <span style=\"text-decoration: underline;\">is being conferred</span> to Kailash by the department. (Incorrect Tense)<br>(d) A scholarship <span style=\"text-decoration: underline;\">has been conferred</span> in Kailash by the department. (Incorrect Tense)</p>",
                    solution_hi: "<p>81.(c) A scholarship was conferred to Kailash by the department. <br>(a) A scholarship <span style=\"text-decoration: underline;\">had been conferred</span> to Kailash by the department. (गलत Tense)<br>(b) A scholarship <span style=\"text-decoration: underline;\">is being conferred</span> to Kailash by the department. (गलत Tense)<br>(d) A scholarship <span style=\"text-decoration: underline;\">has been conferred</span> in Kailash by the department. (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Given below are four jumbled parts of a sentence. Pick the option that gives their correct order.<br>1. A tiger can be killed <br>P. a bear has the <br>Q. physical force but <br>R. by using a trap or<br>S. capacity to hug us <br>6. and kill us to death</p>",
                    question_hi: "<p>82. Given below are four jumbled parts of a sentence. Pick the option that gives their correct order.<br>1. A tiger can be killed <br>P. a bear has the <br>Q. physical force but <br>R. by using a trap or<br>S. capacity to hug us <br>6. and kill us to death</p>",
                    options_en: [
                        "<p>RQPS</p>",
                        "<p>RQSP</p>",
                        "<p>QPSR</p>",
                        "<p>PSRQ</p>"
                    ],
                    options_hi: [
                        "<p>RQPS</p>",
                        "<p>RQSP</p>",
                        "<p>QPSR</p>",
                        "<p>PSRQ</p>"
                    ],
                    solution_en: "<p>82. (a) RQPS<br>The sentence starts with the given phrase &lsquo;A tiger can be killed&rsquo;. Part R will follow this phrase as it mentions the object i.e. can be killed by a trap. However, Part R ends with the conjunction &lsquo;or&rsquo; which will be followed by Part Q. Further, Part Q ends with the conjunction &lsquo;but&rsquo; and part P mentions a clause, in contrast to Part Q. So, P will follow Q. Part S completes the object in Part P i.e. Bear has the capacity to hug us. So, S will follow P. Going through the options, option (a) has the correct sequence.</p>",
                    solution_hi: "<p>82. (a) RQPS<br>Sentence दिए गए phrase &lsquo;A tiger can be killed&rsquo; से शुरू होता है। Part R इस phrase के बाद आएगा क्योंकि इसमें उद्देश्य &lsquo; can be killed by a trap&rsquo; का उल्लेख है। हालाँकि, Part R conjunction &lsquo;or&rsquo; के साथ समाप्त होता है, जिसके बाद Part Q होगा। आगे, Part Q conjunction &lsquo;but&rsquo; के साथ समाप्त होता है और part P, Part Q के विपरीत एक clause का उल्लेख करता है। इसलिए, Q के बाद Pआएगा । Part S, Part P में object को पूरा करता है अर्थात Bear has the capacity to hug us. इसलिए, P के बाद S आएगा। options के माध्यम से जाने पर ,option (a) में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Choose the word opposite in meaning to the given word.<br>Terse</p>",
                    question_hi: "<p>83. Choose the word opposite in meaning to the given word.<br>Terse</p>",
                    options_en: [
                        "<p>Succinct</p>",
                        "<p>Verbose</p>",
                        "<p>Loathe</p>",
                        "<p>Concise</p>"
                    ],
                    options_hi: [
                        "<p>Succinct</p>",
                        "<p>Verbose</p>",
                        "<p>Loathe</p>",
                        "<p>Concise</p>"
                    ],
                    solution_en: "<p>83.(b) <strong>Terse -</strong> said in few words and in a not very friendly way<br><strong>Verbose - </strong>using or containing more words than are needed<br><strong>Succinct -</strong> said clearly, in a few words<br><strong>Loathe -</strong> to hate somebody/something<br><strong>Concise -</strong> giving a lot of information in a few words</p>",
                    solution_hi: "<p>83.(b) <strong>Terse (संक्षिप्त) </strong>- said in few words and in a not very friendly way<br><strong>Verbose (वाचाल)</strong> - using or containing more words than are needed<br><strong>Succinct (संक्षिप्त) </strong>- said clearly, in a few words<br><strong>Loathe (घृणा करना, कोसना) </strong>- to hate somebody/something<br><strong>Concise (संक्षिप्त) </strong>- giving a lot of information in a few words</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Rectify the sentence by selecting the correct spelling from the options. <br>Psycology developed as a significant discipline of study in the twentieth century.</p>",
                    question_hi: "<p>84. Rectify the sentence by selecting the correct spelling from the options. <br>Psycology developed as a significant discipline of study in the twentieth century.</p>",
                    options_en: [
                        "<p>Psychology</p>",
                        "<p>Pscycology</p>",
                        "<p>Pscichology</p>",
                        "<p>Pcychology</p>"
                    ],
                    options_hi: [
                        "<p>Psychology</p>",
                        "<p>Pscycology</p>",
                        "<p>Pscichology</p>",
                        "<p>Pcychology</p>"
                    ],
                    solution_en: "<p>84.(a) Psycology<br>Psychology is the correct spelling.</p>",
                    solution_hi: "<p>84.(a) Psycology<br>Psychology सही spelling है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Select the most appropriate active form of the given sentence.<br>Rani&rsquo;s car is twelve years old but it has not been used much.</p>",
                    question_hi: "<p>85. Select the most appropriate active form of the given sentence.<br>Rani&rsquo;s car is twelve years old but it has not been used much.</p>",
                    options_en: [
                        "<p>Rani&rsquo;s car is twelve years old but she isn&rsquo;t using it much.</p>",
                        "<p>Rani&rsquo;s car is twelve years old but she hasn&rsquo;t used it much.</p>",
                        "<p>Rani&rsquo;s car is twelve years old but she didn&rsquo;t use it much.</p>",
                        "<p>Rani&rsquo;s car is twelve years old but it hasn&rsquo;t used her much.</p>"
                    ],
                    options_hi: [
                        "<p>Rani&rsquo;s car is twelve years old but she isn&rsquo;t using it much.</p>",
                        "<p>Rani&rsquo;s car is twelve years old but she hasn&rsquo;t used it much.</p>",
                        "<p>Rani&rsquo;s car is twelve years old but she didn&rsquo;t use it much.</p>",
                        "<p>Rani&rsquo;s car is twelve years old but it hasn&rsquo;t used her much.</p>"
                    ],
                    solution_en: "<p>85.(b) Rani&rsquo;s car is twelve years old but she hasn&rsquo;t used it much. (Correct)<br>(a) Rani&rsquo;s car is twelve years old but she <span style=\"text-decoration: underline;\">isn&rsquo;t using</span> it much. (Incorrect Helping Verb)<br>(c) Rani&rsquo;s car is twelve years old but she <span style=\"text-decoration: underline;\">didn&rsquo;t use</span> it much. (Incorrect tense)<br>(d) Rani&rsquo;s car is twelve years old but it hasn&rsquo;t used <span style=\"text-decoration: underline;\">her</span> much. (Incorrect pronoun)</p>",
                    solution_hi: "<p>85.(b) Rani&rsquo;s car is twelve years old but she hasn&rsquo;t used it much. (Correct)<br>(a) Rani&rsquo;s car is twelve years old but she <span style=\"text-decoration: underline;\">isn&rsquo;t using</span> it much. (गलत Helping Verb)<br>(c) Rani&rsquo;s car is twelve years old but she <span style=\"text-decoration: underline;\">didn&rsquo;t use</span> it much. (गलत tense)<br>(d) Rani&rsquo;s car is twelve years old but it hasn&rsquo;t used <span style=\"text-decoration: underline;\">her</span> much. (गलत pronoun)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Identify the segment in the sentence which contains grammatical error. <br>Ever since the government announced its new policy the private institutions had run into great problems.</p>",
                    question_hi: "<p>86. Identify the segment in the sentence which contains grammatical error. <br>Ever since the government announced its new policy the private institutions had run into great problems.</p>",
                    options_en: [
                        "<p>Ever since the government</p>",
                        "<p>announced its new policy</p>",
                        "<p>had run into great problems</p>",
                        "<p>the private institutions</p>"
                    ],
                    options_hi: [
                        "<p>Ever since the government</p>",
                        "<p>announced its new policy</p>",
                        "<p>had run into great problems</p>",
                        "<p>the private institutions</p>"
                    ],
                    solution_en: "<p>86.(c) had run into great problems.<br>Use present perfect tense. Replace had by have.</p>",
                    solution_hi: "<p>86.(c) had run into great problems. <br>Present perfect tense का प्रयोग करें। had को have से बदले।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of idiom in the sentence.<br>Tripti gave her report quoting <span style=\"text-decoration: underline;\"><strong>chapter and verse</strong></span> .</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of idiom in the sentence.<br>Tripti gave her report quoting <span style=\"text-decoration: underline;\"><strong>chapter and verse</strong></span> .</p>",
                    options_en: [
                        "<p>provided minute details</p>",
                        "<p>spoke like a preacher</p>",
                        "<p>referred to religious books</p>",
                        "<p>taught like a teacher</p>"
                    ],
                    options_hi: [
                        "<p>provided minute details</p>",
                        "<p>spoke like a preacher</p>",
                        "<p>referred to religious books</p>",
                        "<p>taught like a teacher</p>"
                    ],
                    solution_en: "<p>87.(a) provided minute details<br><strong>Example -</strong> She came home and gave her mother chapter and verse about the interview.</p>",
                    solution_hi: "<p>87.(a) provided minute details/सूक्ष्म विवरण प्रदान करना ।<br><strong>Example -</strong> She came home and gave her mother chapter and verse about the interview.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. He was startled to hear a deep voice right behind him. <br>B. The heavy but soothing voice seemed to come out of the knee-high chilli plant.<br>C. His mother was making fish curry for dinner and wanted some fresh green chillies. <br>D. Ten year old Aalinger had gone to the garden to pick some fully grown shining green chillies.</p>",
                    question_hi: "<p>88. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. He was startled to hear a deep voice right behind him. <br>B. The heavy but soothing voice seemed to come out of the knee-high chilli plant.<br>C. His mother was making fish curry for dinner and wanted some fresh green chillies. <br>D. Ten year old Aalinger had gone to the garden to pick some fully grown shining green chillies.</p>",
                    options_en: [
                        "<p>DACB</p>",
                        "<p>CDAB</p>",
                        "<p>CDBA</p>",
                        "<p>DABC</p>"
                    ],
                    options_hi: [
                        "<p>DACB</p>",
                        "<p>CDAB</p>",
                        "<p>CDBA</p>",
                        "<p>DABC</p>"
                    ],
                    solution_en: "<p>88. (b) CDAB<br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e. the mother needed green chillies to make the dinner. And, Sentence D states where her son went to take the chillies. So, D will follow C. Further, Sentence A states that he heard a deep voice and Sentence B states from where the voice seemed to come. So, B will follow A. Going through the options, option (b) has the correct sequence.</p>",
                    solution_hi: "<p>88. (b) CDAB<br>Sentence C starting line होगी क्योंकि इसमें parajumble का मुख्य विचार &lsquo; the mother needed green chillies to make the dinner&rsquo; शामिल है। Sentence D बताता है कि उसका बेटा मिर्च लेने कहाँ गया था। तो, C के बाद D आएगा। आगे, Sentence A कहता है कि उसने एक आवाज सुनी और Sentence B बताता है कि आवाज कहां से आ रही थी। तो, A के बाद B आएगा। Options के माध्यम से जाने पर , option (b) में सही sequence है।<br><br></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the word which means the same as the group of words given<br>Compulsory enlistment for military or other services.</p>",
                    question_hi: "<p>89. Select the word which means the same as the group of words given<br>Compulsory enlistment for military or other services.</p>",
                    options_en: [
                        "<p>Prescription</p>",
                        "<p>Proscription</p>",
                        "<p>Description</p>",
                        "<p>Conscription</p>"
                    ],
                    options_hi: [
                        "<p>Prescription</p>",
                        "<p>Proscription</p>",
                        "<p>Description</p>",
                        "<p>Conscription</p>"
                    ],
                    solution_en: "<p>89.(d) <strong>Conscription - </strong>compulsory <br>enlistment for state service, typically into the armed forces.<br><strong>Prescription - </strong>an instruction written by a medical practitioner that authorizes a patient to be issued with a medicine or treatment.<br><strong>Proscription - </strong>the action of forbidding something; banning.<br><strong>Description -</strong> a spoken or written account of a person, object, or event.</p>",
                    solution_hi: "<p>89.(d) <strong>Conscription -</strong> राज्य सेवा के लिए अनिवार्य भर्ती, आम तौर पर सशस्त्र बलों में। Prescription - एक चिकित्सक द्वारा लिखित<br>एक निर्देश जो एक मरीज को दवा या उपचार जारी करने के लिए अधिकृत करता है।<br><strong>Proscription -</strong> कुछ मना करने की क्रिया; प्रतिबंध लगाना।<br><strong>Description -</strong> किसी व्यक्ति, वस्तु या घटना का मौखिक या लिखित विवरण।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Find a word that is the synonym of: <br>Perilous</p>",
                    question_hi: "<p>90. Find a word that is the synonym of: <br>Perilous</p>",
                    options_en: [
                        "<p>Dangerous</p>",
                        "<p>Stimulus</p>",
                        "<p>Secure</p>",
                        "<p>Impetus</p>"
                    ],
                    options_hi: [
                        "<p>Dangerous</p>",
                        "<p>Stimulus</p>",
                        "<p>Secure</p>",
                        "<p>Impetus</p>"
                    ],
                    solution_en: "<p>90.(a) <strong>Perilous-</strong> extremely dangerous<br><strong>Dangerous - </strong>likely to cause injury or damage<br><strong>Stimulus - </strong>something that causes activity, development or interest<br><strong>Secure -</strong> free from worry or doubt;<br><strong>Impetus - </strong>something that encourages something else to happen</p>",
                    solution_hi: "<p>90.(a) <strong>Perilous (जोखिम) -</strong> extremely dangerous<br><strong>Dangerous(खतरनाक)-</strong> likely to cause injury or damage<br><strong>Stimulus(प्रोत्साहन)- </strong>something that causes activity, development or interest<br><strong>Secure(सुरक्षित)-</strong> free from worry or doubt;<br><strong>Impetus(प्रेरणा)-</strong> something that encourages something else to happen</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Find the correctly spelt word.</p>",
                    question_hi: "<p>91. Find the correctly spelt word.</p>",
                    options_en: [
                        "<p>occulist</p>",
                        "<p>plebiscete</p>",
                        "<p>immovable</p>",
                        "<p>magnifcient</p>"
                    ],
                    options_hi: [
                        "<p>occulist</p>",
                        "<p>plebiscete</p>",
                        "<p>immovable</p>",
                        "<p>magnifcient</p>"
                    ],
                    solution_en: "<p>91.(c) Immovable.</p>",
                    solution_hi: "<p>91.(c) Immovable.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92.&nbsp;Select the most appropriate option to fill in the blank.&nbsp;.<br>He was called_________ to prove the correctness of the press reports.</p>",
                    question_hi: "<p>92.&nbsp;Select the most appropriate option to fill in the blank.&nbsp;.<br>He was called_________ to prove the correctness of the press reports.</p>",
                    options_en: [
                        "<p>up</p>",
                        "<p>on</p>",
                        "<p>upon</p>",
                        "<p>None</p>"
                    ],
                    options_hi: [
                        "<p>up</p>",
                        "<p>on</p>",
                        "<p>upon</p>",
                        "<p>None</p>"
                    ],
                    solution_en: "<p>92.(c) upon<br>&lsquo;Called upon&rsquo; is a phrasal verb which means to ask formally for someone to do something. The given sentence states that &ldquo;he was called upon(formally asked) to prove the correctness of the press reports&rdquo;. Hence, &lsquo;upon&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>92.(c) upon<br>&lsquo;Called upon&rsquo; एक phrasal verb है जिसका अर्थ औपचारिक रूप से किसी से कुछ करने के लिए कहना है। दिए गए वाक्य में कहा गया है कि &ldquo;he was called upon (formally asked) to prove the correctness of the press reports&rdquo;/ &ldquo;उन्हें प्रेस रिपोर्टों की यथार्थता/ सच्चाई साबित करने के लिए बुलाया गया था (औपचारिक रूप से पूछा गया)&rdquo;। इसलिए, \'upon\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>His grandfather was quite <span style=\"text-decoration: underline;\">grumpy</span> .</p>",
                    question_hi: "<p>93. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>His grandfather was quite <span style=\"text-decoration: underline;\">grumpy</span> .</p>",
                    options_en: [
                        "<p>Tenable</p>",
                        "<p>Amiable</p>",
                        "<p>Possible</p>",
                        "<p>Edible</p>"
                    ],
                    options_hi: [
                        "<p>Tenable</p>",
                        "<p>Amiable</p>",
                        "<p>Possible</p>",
                        "<p>Edible</p>"
                    ],
                    solution_en: "<p>93.(b) <strong>Amiable-</strong> friendly and pleasant.<br><strong>Grumpy-</strong> easily irritated or in a bad mood.<br><strong>Tenable-</strong> able to be maintained or defended against attack.<br><strong>Possible-</strong> able to be done or achieved.<br><strong>Edible-</strong> safe to eat and not harmful.</p>",
                    solution_hi: "<p>93.(b) <strong>Amiable </strong>(मिलनसार) - friendly and pleasant.<br><strong>Grumpy </strong>(क्रोधी) - easily irritated or in a bad mood.<br><strong>Tenable </strong>(दृढ़/समर्थ) - able to be maintained or defended against attack.<br><strong>Possible </strong>(संभव) - able to be done or achieved.<br><strong>Edible </strong>(खाने योग्य) - safe to eat and not harmful.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Find a word that is the synonym of<br>Lacuna</p>",
                    question_hi: "<p>94. Find a word that is the synonym of<br>Lacuna</p>",
                    options_en: [
                        "<p>Misfortune</p>",
                        "<p>Languor</p>",
                        "<p>Apathy</p>",
                        "<p>Hiatus</p>"
                    ],
                    options_hi: [
                        "<p>Misfortune</p>",
                        "<p>Languor</p>",
                        "<p>Apathy</p>",
                        "<p>Hiatus</p>"
                    ],
                    solution_en: "<p>94.(d) <strong>Lacuna-</strong> a gap or missing part, especially in a book <br><strong>Hiatus- </strong>a pause in an activity when nothing happens<br><strong>Misfortune- </strong>bad luck or disaster<br><strong>Languor-</strong> tiredness or inactivity <br><strong>Apathy- </strong>the feeling of not being interested in or enthusiastic about anything</p>",
                    solution_hi: "<p>94.(d) <strong>Lacuna (कमी)-</strong> a gap or missing part, especially in a book <br><strong>Hiatus(ख़ाली जगह)-</strong> a pause in an activity when nothing happens<br><strong>Misfortune(दुर्भाग्य)-</strong> bad luck or disaster<br><strong>Languor( थकान)-</strong> tiredness or inactivity <br><strong>Apathy(उदासीनता)-</strong> the feeling of not being interested in or enthusiastic about anything</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option to fill in the blank.<br>In the Second World War, the British __________ thousands of so-called interceptors &ndash; mostly women &ndash; whose job it was to tune in every day and night to the radio _______ of the various divisions of the German military.</p>",
                    question_hi: "<p>95. Select the most appropriate option to fill in the blank.<br>In the Second World War, the British __________ thousands of so-called interceptors &ndash; mostly women &ndash; whose job it was to tune in every day and night to the radio _______ of the various divisions of the German military.</p>",
                    options_en: [
                        "<p>averted, broadbills</p>",
                        "<p>assented, broadsides</p>",
                        "<p>assembled, broadcasts</p>",
                        "<p>adhered, broadlooms</p>"
                    ],
                    options_hi: [
                        "<p>averted, broadbills</p>",
                        "<p>assented, broadsides</p>",
                        "<p>assembled, broadcasts</p>",
                        "<p>adhered, broadlooms</p>"
                    ],
                    solution_en: "<p>95.(c) Broadcast is a verb it means to transmit (a programme or some information) by radio or television. And broadcast is available only in option c. Also when people get together at a certain place the verb used is assemble. Hence option c is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(c) Broadcast एक verb है जिसका अर्थ, radio या television द्वारा (एक कार्यक्रम या कुछ जानकारी) प्रसारित करना है और Broadcast केवल option(c) में उपलब्ध है। इसके अलावा जब लोग एक निश्चित स्थान पर एक साथ मिलते हैं तो assemble(verb) का प्रयोग किया जाता है। अतः option(c) सबसे उपयुक्त उत्तर है। </p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze test :-</strong><br>Vijayanagara or \"city of victory\" was the (96)______________ of both a city and an empire. The empire was (97)____________ in the fourteenth century. In its heyday, it (98)___________ from the river Krishna in the north to the extreme south of the peninsula. In 1565, the city was sacked and (99)_____________ deserted. Although it fell into ruin in the seventeenth-eighteenth centuries, it lived on in the memories of people living in the Krishna-Tungabhadra doab. They remembered it as Hampi, a name (100)_____________ from that of the local mother goddess, Pampadevi.<br>Select the most appropriate option to fill in blank number 96</p>",
                    question_hi: "<p>96. <strong>Cloze test :-</strong><br>Vijayanagara or \"city of victory\" was the (96)______________ of both a city and an empire. The empire was (97)____________ in the fourteenth century. In its heyday, it (98)___________ from the river Krishna in the north to the extreme south of the peninsula. In 1565, the city was sacked and (99)_____________ deserted. Although it fell into ruin in the seventeenth-eighteenth centuries, it lived on in the memories of people living in the Krishna-Tungabhadra doab. They remembered it as Hampi, a name (100)_____________ from that of the local mother goddess, Pampadevi.<br>Select the most appropriate option to fill in blank number 96</p>",
                    options_en: [
                        "<p>mark</p>",
                        "<p>name</p>",
                        "<p>sign</p>",
                        "<p>label</p>"
                    ],
                    options_hi: [
                        "<p>mark</p>",
                        "<p>name</p>",
                        "<p>sign</p>",
                        "<p>label</p>"
                    ],
                    solution_en: "<p>96.(b) <strong>name</strong><br>The given passage states that Vijayanagara or \"city of victory\" was the name of both a city and an empire. Hence, \'name\' is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(b) <strong>name</strong><br>दिए गए passage में कहा गया है कि विजयनगर या \"city of victory\" एक शहर और एक साम्राज्य दोनों का नाम था। अतः , \'name\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97.<strong>Cloze test :-</strong><br>Vijayanagara or \"city of victory\" was the (96)______________ of both a city and an empire. The empire was (97)____________ in the fourteenth century. In its heyday, it (98)___________ from the river Krishna in the north to the extreme south of the peninsula. In 1565, the city was sacked and (99)_____________ deserted. Although it fell into ruin in the seventeenth-eighteenth centuries, it lived on in the memories of people living in the Krishna-Tungabhadra doab. They remembered it as Hampi, a name (100)_____________ from that of the local mother goddess, Pampadevi.<br>Select the most appropriate option to fill in blank number 97</p>",
                    question_hi: "<p>97.<strong> Cloze test :-</strong><br>Vijayanagara or \"city of victory\" was the (96)______________ of both a city and an empire. The empire was (97)____________ in the fourteenth century. In its heyday, it (98)___________ from the river Krishna in the north to the extreme south of the peninsula. In 1565, the city was sacked and (99)_____________ deserted. Although it fell into ruin in the seventeenth-eighteenth centuries, it lived on in the memories of people living in the Krishna-Tungabhadra doab. They remembered it as Hampi, a name (100)_____________ from that of the local mother goddess, Pampadevi.<br>Select the most appropriate option to fill in blank number 97</p>",
                    options_en: [
                        "<p>founded</p>",
                        "<p>begun</p>",
                        "<p>Implanted</p>",
                        "<p>discovered</p>"
                    ],
                    options_hi: [
                        "<p>founded</p>",
                        "<p>begun</p>",
                        "<p>Implanted</p>",
                        "<p>discovered</p>"
                    ],
                    solution_en: "<p>97.(a) founded<br>&lsquo;Founded&rsquo; means brought into existence. The given passage states that the empire was founded in the fourteenth century. Hence, \'founded\' is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(a) founded<br>&lsquo;Founded&rsquo; का अर्थ है अस्तित्व में लाना। दिए गए passage में कहा गया है कि साम्राज्य (empire) की स्थापना चौदहवीं शताब्दी में हुई थी। अतः, \'founded\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze test :-</strong><br>Vijayanagara or \"city of victory\" was the (96)______________ of both a city and an empire. The empire was (97)____________ in the fourteenth century. In its heyday, it (98)___________ from the river Krishna in the north to the extreme south of the peninsula. In 1565, the city was sacked and (99)_____________ deserted. Although it fell into ruin in the seventeenth-eighteenth centuries, it lived on in the memories of people living in the Krishna-Tungabhadra doab. They remembered it as Hampi, a name (100)_____________ from that of the local mother goddess, Pampadevi.<br>Select the most appropriate option to fill in blank number 98</p>",
                    question_hi: "<p>98. <strong>Cloze test :-</strong><br>Vijayanagara or \"city of victory\" was the (96)______________ of both a city and an empire. The empire was (97)____________ in the fourteenth century. In its heyday, it (98)___________ from the river Krishna in the north to the extreme south of the peninsula. In 1565, the city was sacked and (99)_____________ deserted. Although it fell into ruin in the seventeenth-eighteenth centuries, it lived on in the memories of people living in the Krishna-Tungabhadra doab. They remembered it as Hampi, a name (100)_____________ from that of the local mother goddess, Pampadevi.<br>Select the most appropriate option to fill in blank number 98</p>",
                    options_en: [
                        "<p>started</p>",
                        "<p>magnified</p>",
                        "<p>strained</p>",
                        "<p>stretched</p>"
                    ],
                    options_hi: [
                        "<p>started</p>",
                        "<p>magnified</p>",
                        "<p>strained</p>",
                        "<p>stretched</p>"
                    ],
                    solution_en: "<p>98.(d) <strong>stretched</strong><br>&lsquo;Stretched&rsquo; means to expand in area. The given passage states that in its heyday, it stretched from the river Krishna in the North to the extreme south of the peninsula. Hence, \'stretched\' is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(d) <strong>stretched</strong><br>&lsquo;Stretched&rsquo; का अर्थ है क्षेत्र विस्तार करना। दिए गए passage में कहा गया है कि अपने उत्कर्ष काल (heyday) में, यह उत्तर में कृष्णा नदी से लेकर प्रायद्वीप(peninsula) के सुदूर दक्षिण तक फैला हुआ था। अतः, \'stretched\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99.<strong> Cloze test :-</strong><br>Vijayanagara or \"city of victory\" was the (96)______________ of both a city and an empire. The empire was (97)____________ in the fourteenth century. In its heyday, it (98)___________ from the river Krishna in the north to the extreme south of the peninsula. In 1565, the city was sacked and (99)_____________ deserted. Although it fell into ruin in the seventeenth-eighteenth centuries, it lived on in the memories of people living in the Krishna-Tungabhadra doab. They remembered it as Hampi, a name (100)_____________ from that of the local mother goddess, Pampadevi.<br>Select the most appropriate option to fill in blank number 99</p>",
                    question_hi: "<p>99. <strong>Cloze test :-</strong><br>Vijayanagara or \"city of victory\" was the (96)______________ of both a city and an empire. The empire was (97)____________ in the fourteenth century. In its heyday, it (98)___________ from the river Krishna in the north to the extreme south of the peninsula. In 1565, the city was sacked and (99)_____________ deserted. Although it fell into ruin in the seventeenth-eighteenth centuries, it lived on in the memories of people living in the Krishna-Tungabhadra doab. They remembered it as Hampi, a name (100)_____________ from that of the local mother goddess, Pampadevi.<br>Select the most appropriate option to fill in blank number 99</p>",
                    options_en: [
                        "<p>previously</p>",
                        "<p>subsequently</p>",
                        "<p>accidentally</p>",
                        "<p>presently</p>"
                    ],
                    options_hi: [
                        "<p>previously</p>",
                        "<p>subsequently</p>",
                        "<p>accidentally</p>",
                        "<p>presently</p>"
                    ],
                    solution_en: "<p>99.(b) subsequently<br>&lsquo;Subsequently&rsquo; means afterward or later. The given passage states that the city was sacked and subsequently deserted in 1565. Hence, \'subsequently\' is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) subsequently<br>&lsquo;Subsequently&rsquo; का अर्थ है उसके पश्चात या बाद में। दिए गए passage में कहा गया है कि 1565 में, शहर को लूट लिया गया और बाद में उजाड़ दिया गया। अतः, \'subsequently\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze test :-</strong><br>Vijayanagara or \"city of victory\" was the (96)______________ of both a city and an empire. The empire was (97)____________ in the fourteenth century. In its heyday, it (98)___________ from the river Krishna in the north to the extreme south of the peninsula. In 1565, the city was sacked and (99)_____________ deserted. Although it fell into ruin in the seventeenth-eighteenth centuries, it lived on in the memories of people living in the Krishna-Tungabhadra doab. They remembered it as Hampi, a name (100)_____________ from that of the local mother goddess, Pampadevi.<br>Select the most appropriate option to fill in blank number 100</p>",
                    question_hi: "<p>100.<strong> Cloze test :-</strong><br>Vijayanagara or \"city of victory\" was the (96)______________ of both a city and an empire. The empire was (97)____________ in the fourteenth century. In its heyday, it (98)___________ from the river Krishna in the north to the extreme south of the peninsula. In 1565, the city was sacked and (99)_____________ deserted. Although it fell into ruin in the seventeenth-eighteenth centuries, it lived on in the memories of people living in the Krishna-Tungabhadra doab. They remembered it as Hampi, a name (100)_____________ from that of the local mother goddess, Pampadevi.<br>Select the most appropriate option to fill in blank number 100</p>",
                    options_en: [
                        "<p>extracted</p>",
                        "<p>designed</p>",
                        "<p>derived</p>",
                        "<p>collected</p>"
                    ],
                    options_hi: [
                        "<p>extracted</p>",
                        "<p>designed</p>",
                        "<p>derived</p>",
                        "<p>collected</p>"
                    ],
                    solution_en: "<p>100.(c) derived<br>&lsquo;Derived&rsquo; means taken from something. The given passage states that they remembered it as Hampi, a name derived from that of the local mother goddess, Pampadevi. Hence, \'derived\' is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(c) derived<br>&lsquo;Derived&rsquo; का अर्थ है किसी चीज़ से लिया गया। दिए गए passage में कहा गया है कि वे इसे हम्पी के नाम से याद करते हैं, यह नाम स्थानीय मातृ देवी पंपादेवी के नाम पर पड़ा था। अतः, \'derived\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>