<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 20</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">20</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["31"] = {
                name: "Computer Knowledge",
                start: 0,
                end: 18
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="31">Computer Knowledge</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 19,
                end: 19
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "31",
                    question_en: "<p>1. A command, in DOS, used to set a name to a disk, is</p>\n",
                    question_hi: "<p>1. DOS &#2350;&#2375;&#2306; &#2337;&#2367;&#2360;&#2381;&#2325; &#2325;&#2379; &#2319;&#2325; &#2344;&#2366;&#2350; &#2360;&#2375;&#2335; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2319;&#2325; &#2325;&#2350;&#2366;&#2306;&#2337; &#2361;&#2376;</p>\n",
                    options_en: ["<p>LABEL</p>\n", "<p>VOL</p>\n", 
                                "<p>REN</p>\n", "<p>CLS</p>\n"],
                    options_hi: ["<p>LABEL</p>\n", "<p>VOL</p>\n",
                                "<p>REN</p>\n", "<p>CLS</p>\n"],
                    solution_en: "<p>1.(a) <strong>LABEL </strong>is used to create, delete or change a volume label on a logical drive, such as a hard disk partition or a floppy disk.</p>\n",
                    solution_hi: "<p>1.(a) <strong>LABEL</strong> &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325; &#2337;&#2381;&#2352;&#2366;&#2311;&#2357;, &#2332;&#2376;&#2360;&#2375; &#2361;&#2366;&#2352;&#2381;&#2337; &#2337;&#2367;&#2360;&#2381;&#2325; &#2357;&#2367;&#2349;&#2366;&#2332;&#2344;(partition) &#2351;&#2366; &#2347;&#2364;&#2381;&#2354;&#2377;&#2346;&#2368; &#2337;&#2367;&#2360;&#2381;&#2325; &#2346;&#2352; &#2357;&#2377;&#2354;&#2381;&#2351;&#2370;&#2350; &#2354;&#2375;&#2348;&#2354;(volume label) &#2348;&#2344;&#2366;&#2344;&#2375;, &#2361;&#2335;&#2366;&#2344;&#2375; &#2351;&#2366; &#2348;&#2342;&#2354;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "2",
                    section: "31",
                    question_en: "<p>2. Which of the following is an example of optical disc?</p>\n",
                    question_hi: "<p>2. &#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2366; &#2321;&#2346;&#2381;&#2335;&#2367;&#2325;&#2354; &#2337;&#2367;&#2360;&#2381;&#2325; &#2325;&#2366; &#2313;&#2342;&#2366;&#2361;&#2352;&#2339; &#2361;&#2376;?</p>\n",
                    options_en: ["<p>Digital versatile discs</p>\n", "<p>Magnetic disks</p>\n", 
                                "<p>Memory disks</p>\n", "<p>Data bus disks</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2357;&#2352;&#2381;&#2360;&#2335;&#2366;&#2311;&#2354; &#2337;&#2367;&#2360;&#2381;&#2325; (Digital versatile discs)</span></p>\r\n<p>&nbsp;</p>\n", "<p>&nbsp;&#2350;&#2376;&#2327;&#2381;&#2344;&#2375;&#2335;&#2367;&#2325; &#2337;&#2367;&#2360;&#2381;&#2325; (Magnetic disks)</p>\r\n<p>&nbsp;</p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2350;&#2375;&#2350;&#2379;&#2352;&#2368; &#2337;&#2367;&#2360;&#2381;&#2325; (Memory disks)</span></p>\r\n<p>&nbsp;</p>\n", "<p>&#2337;&#2375;&#2335;&#2366; &#2348;&#2360; &#2337;&#2367;&#2360;&#2381;&#2325; (Data bus disks)</p>\n"],
                    solution_en: "<p>2.(a) <strong>Optical disk</strong> is any storage type in which data is written and read with a laser. It includes <strong>CD, DVD and Blu-ray disc.</strong></p>\n",
                    solution_hi: "<p>2.(a) <strong>&#2321;&#2346;&#2381;&#2335;&#2367;&#2325;&#2354; &#2337;&#2367;&#2360;&#2381;&#2325;</strong> &#2325;&#2379;&#2312; &#2349;&#2368; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2346;&#2381;&#2352;&#2325;&#2366;&#2352; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2337;&#2375;&#2335;&#2366; &#2325;&#2379; &#2354;&#2375;&#2332;&#2352; &#2360;&#2375; &#2354;&#2367;&#2326;&#2366; &#2324;&#2352; &#2346;&#2338;&#2364;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2311;&#2360;&#2350;&#2375;&#2306; CD, DVD &#2324;&#2352; &#2348;&#2381;&#2354;&#2370;-&#2352;&#2375; &#2337;&#2367;&#2360;&#2381;&#2325; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306;&#2404;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "3",
                    section: "31",
                    question_en: "<p><span style=\"font-weight: 400;\">3.</span><span style=\"font-weight: 400;\">USB in data cables stands for</span></p>\n",
                    question_hi: "<p><span style=\"font-weight: 400;\">&nbsp;&#2337;&#2375;&#2335;&#2366; &#2325;&#2375;&#2348;&#2354; &#2350;&#2375;&#2306; USB &#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Unicode Smart Bus</p>\n", "<p>Universal Structural Bus</p>\n", 
                                "<p>Unicode Serial Bus</p>\n", "<p>Universal Serial Bus</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2351;&#2370;&#2344;&#2367;&#2325;&#2379;&#2337; &#2360;&#2381;&#2350;&#2366;&#2352;&#2381;&#2335; &#2348;&#2360; (Unicode Smart Bus)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360;&#2354; &#2360;&#2381;&#2335;&#2381;&#2352;&#2325;&#2381;&#2330;&#2352;&#2354; &#2348;&#2360; (Universal Structural Bus)</span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2351;&#2370;&#2344;&#2367;&#2325;&#2379;&#2337; &#2360;&#2368;&#2352;&#2367;&#2351;&#2354; &#2348;&#2360; (Unicode Serial Bus)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360;&#2354; &#2360;&#2368;&#2352;&#2367;&#2351;&#2354; &#2348;&#2360; (Universal Serial Bus)</span></p>\n"],
                    solution_en: "<p>3.(d) <strong>Universal Serial Bus (USB) </strong>&rarr; It is a common and popular external port available with</p>\r\n<p>computers. Normally, two to four USB ports are provided on a PC. USB also has the plug and play feature, which allows devices ready to be run.</p>\n",
                    solution_hi: "<p>3.(d) <strong>&#2351;&#2370;&#2344;&#2367;&#2357;&#2352;&#2381;&#2360;&#2354; &#2360;&#2368;&#2352;&#2367;&#2351;&#2354; &#2348;&#2360;</strong> (USB) &rarr; &#2351;&#2361; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2360;&#2366;&#2341; &#2313;&#2346;&#2354;&#2348;&#2381;&#2343; &#2319;&#2325; &#2360;&#2366;&#2350;&#2366;&#2344;&#2381;&#2351; &#2324;&#2352; &#2354;&#2379;&#2325;&#2346;&#2381;&#2352;&#2367;&#2351; &#2319;&#2325;&#2381;&#2360;&#2335;&#2352;&#2381;&#2344;&#2354; &#2346;&#2379;&#2352;&#2381;&#2335; &#2361;&#2376;&#2404; &#2310;&#2350; &#2340;&#2380;&#2352; &#2346;&#2352;, &#2319;&#2325; PC &#2346;&#2352; &#2342;&#2379; &#2360;&#2375; &#2330;&#2366;&#2352; USB &#2346;&#2379;&#2352;&#2381;&#2335; &#2342;&#2367;&#2319; &#2332;&#2366;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; USB &#2350;&#2375;&#2306; &#2346;&#2381;&#2354;&#2327; &#2319;&#2306;&#2337; &#2346;&#2381;&#2354;&#2375; &#2347;&#2368;&#2330;&#2352; (plug and play feature) &#2349;&#2368; &#2361;&#2376;, &#2332;&#2379; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2325;&#2379; &#2330;&#2354;&#2366;&#2344;&#2375; (run) &#2325;&#2375; &#2354;&#2367;&#2319; &#2340;&#2376;&#2351;&#2366;&#2352; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "4",
                    section: "31",
                    question_en: "<p>4. A software program that adds functionality to your computer or help your computer perform better is called as</p>\n",
                    question_hi: "<p>4. &#2319;&#2325; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2332;&#2379; &#2310;&#2346;&#2325;&#2375; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2350;&#2375;&#2306; &#2347;&#2306;&#2325;&#2381;&#2358;&#2344;&#2354;&#2367;&#2335;&#2368; &#2332;&#2379;&#2337;&#2364;&#2340;&#2366; &#2361;&#2376; &#2351;&#2366; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2379; &#2348;&#2375;&#2361;&#2340;&#2352; &#2346;&#2352;&#2347;&#2377;&#2352;&#2381;&#2350; (better perform) &#2325;&#2352;&#2344;&#2375; &#2350;&#2375;&#2306; &#2350;&#2342;&#2342; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;, &#2313;&#2360;&#2375; _____ &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                    options_en: ["<p>utility program</p>\n", "<p>function program</p>\n", 
                                "<p>specialized program</p>\n", "<p>manufacturer program</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2351;&#2370;&#2335;&#2367;&#2354;&#2367;&#2335;&#2368; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; (utility program)</span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2347;&#2306;&#2325;&#2381;&#2358;&#2344; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; (function program)</span></p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2360;&#2381;&#2346;&#2375;&#2358;&#2354;&#2366;&#2311;&#2332;&#2381;&#2337; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; (specialized program)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2350;&#2376;&#2344;&#2381;&#2351;&#2369;&#2347;&#2376;&#2325;&#2381;&#2330;&#2352;&#2352; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; (manufacturer program)</span></p>\n"],
                    solution_en: "<p>4.(a) <strong>Utility software</strong> includes all systems and programs on a computer system that maintain its functionality.</p>\n",
                    solution_hi: "<p>4.(a)<strong> &#2351;&#2370;&#2335;&#2367;&#2354;&#2367;&#2335;&#2368; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352;</strong> &#2350;&#2375;&#2306; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2346;&#2352; &#2360;&#2349;&#2368; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350; &#2324;&#2352; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306; &#2332;&#2379; &#2311;&#2360;&#2325;&#2368; &#2347;&#2306;&#2325;&#2381;&#2358;&#2344;&#2354;&#2367;&#2335;&#2368; &#2325;&#2379; &#2348;&#2344;&#2366;&#2319; &#2352;&#2326;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "5",
                    section: "31",
                    question_en: "<p>5. Which of the following a computer&rsquo;s memory, but unlike a virus, it does not replicate itself ?</p>\n",
                    question_hi: "<p>5. &#2344;&#2367;&#2350;&#2381;&#2344;&#2354;&#2367;&#2326;&#2367;&#2340; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2368; &#2319;&#2325; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2368; &#2350;&#2375;&#2350;&#2379;&#2352;&#2368; &#2361;&#2376;, &#2354;&#2375;&#2325;&#2367;&#2344; &#2319;&#2325; &#2357;&#2366;&#2351;&#2352;&#2360; &#2325;&#2375; &#2357;&#2367;&#2346;&#2352;&#2368;&#2340;, &#2351;&#2361; &#2326;&#2369;&#2342; &#2325;&#2379; &#2344;&#2361;&#2368;&#2306; &#2342;&#2379;&#2361;&#2352;&#2366;&#2340;&#2366; &#2361;&#2376;I</p>\n",
                    options_en: ["<p>Trojan horse</p>\n", "<p>Logic bomb</p>\n", 
                                "<p>Cracker</p>\n", "<p>Firewall</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2335;&#2381;&#2352;&#2379;&#2332;&#2344; &#2361;&#2377;&#2352;&#2381;&#2360; (Trojan horse)</span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2354;&#2377;&#2332;&#2367;&#2325; &#2348;&#2350; (Logic bomb)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p>&nbsp;&#2325;&#2381;&#2352;&#2376;&#2325;&#2352; (Cracker)</p>\r\n<p><strong>&nbsp; &nbsp;&nbsp;</strong></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2347;&#2364;&#2366;&#2351;&#2352;&#2357;&#2377;&#2354; (Firewall)</span></p>\n"],
                    solution_en: "<p>5.(b)<strong> A logic bomb</strong> is a malicious program that is triggered when a logical condition is met, such as after a number of transactions have been processed or on a specific date. Malware such as worms often contain logic bombs, which behave in one manner and then change tactics on a specific date and time.<strong>Trojan horse</strong>&rarr;A Trojan horse/virus can be a program that purports to do one action when, in fact, it is performing a malicious action on your computer. Trojan horses can be included in software that you download for free or as attachments in email messages.</p>\n",
                    solution_hi: "<p><strong>5.(b)</strong><span style=\"font-weight: 400;\"> </span><strong>&#2354;&#2377;&#2332;&#2367;&#2325; &#2348;&#2350;</strong><span style=\"font-weight: 400;\"> &#2319;&#2325; &#2342;&#2369;&#2352;&#2381;&#2349;&#2366;&#2357;&#2344;&#2366;&#2346;&#2370;&#2352;&#2381;&#2339; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;(malicious program) &#2361;&#2376; &#2332;&#2379; &#2319;&#2325; &#2340;&#2366;&#2352;&#2381;&#2325;&#2367;&#2325; &#2360;&#2381;&#2341;&#2367;&#2340;&#2367; &#2325;&#2375; &#2346;&#2370;&#2352;&#2366; &#2361;&#2379;&#2344;&#2375; &#2346;&#2352; &#2335;&#2381;&#2352;&#2367;&#2327;&#2352; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2376;&#2360;&#2375; &#2325;&#2367; &#2325;&#2312; &#2354;&#2375;&#2344;-&#2342;&#2375;&#2344; &#2360;&#2306;&#2360;&#2366;&#2343;&#2367;&#2340; &#2361;&#2379;&#2344;&#2375; &#2325;&#2375; &#2348;&#2366;&#2342; &#2351;&#2366; &#2325;&#2367;&#2360;&#2368; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2340;&#2367;&#2341;&#2367; &#2346;&#2352;&#2404; &#2357;&#2352;&#2381;&#2350;&#2381;&#2360;(worms) &#2332;&#2376;&#2360;&#2375; &#2350;&#2376;&#2354;&#2357;&#2375;&#2351;&#2352; &#2350;&#2375;&#2306; &#2309;&#2325;&#2381;&#2360;&#2352; &#2354;&#2377;&#2332;&#2367;&#2325; &#2348;&#2350; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;, &#2332;&#2379; &#2319;&#2325; &#2340;&#2352;&#2361; &#2360;&#2375; &#2357;&#2381;&#2351;&#2357;&#2361;&#2366;&#2352; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2347;&#2367;&#2352; &#2319;&#2325; &#2357;&#2367;&#2358;&#2367;&#2359;&#2381;&#2335; &#2340;&#2367;&#2341;&#2367; &#2324;&#2352; &#2360;&#2350;&#2351; &#2346;&#2352; &#2352;&#2339;&#2344;&#2368;&#2340;&#2367; &#2348;&#2342;&#2354;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; </span><strong>&#2335;&#2381;&#2352;&#2379;&#2332;&#2344; &#2361;&#2377;&#2352;&#2381;&#2360;(Trojan horse)</strong><span style=\"font-weight: 400;\"> &rarr; &#2319;&#2325; &#2335;&#2381;&#2352;&#2379;&#2332;&#2344; &#2361;&#2377;&#2352;&#2381;&#2360;/&#2357;&#2366;&#2351;&#2352;&#2360; &#2319;&#2325; &#2320;&#2360;&#2366; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2379; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376; &#2332;&#2379; &#2319;&#2325; &#2325;&#2366;&#2352;&#2381;&#2351; &#2325;&#2352;&#2344;&#2375; &#2325;&#2366; &#2342;&#2366;&#2357;&#2366; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;, &#2332;&#2348;&#2325;&#2367; &#2357;&#2366;&#2360;&#2381;&#2340;&#2357; &#2350;&#2375;&#2306;, &#2351;&#2361; &#2310;&#2346;&#2325;&#2375; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2346;&#2352; &#2342;&#2369;&#2352;&#2381;&#2349;&#2366;&#2357;&#2344;&#2366;&#2346;&#2370;&#2352;&#2381;&#2339; &#2325;&#2366;&#2352;&#2381;&#2352;&#2357;&#2366;&#2312; &#2325;&#2352;&#2344;&#2366;&#2404; &#2335;&#2381;&#2352;&#2379;&#2332;&#2344; &#2361;&#2377;&#2352;&#2381;&#2360; &#2325;&#2379; &#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376; &#2332;&#2367;&#2360;&#2375; &#2310;&#2346; &#2350;&#2369;&#2347;&#2381;&#2340; &#2350;&#2375;&#2306; &#2337;&#2366;&#2313;&#2344;&#2354;&#2379;&#2337; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306; &#2351;&#2366; &#2312;&#2350;&#2375;&#2354; &#2360;&#2306;&#2342;&#2375;&#2358;&#2379;&#2306; &#2350;&#2375;&#2306; &#2309;&#2335;&#2376;&#2330;&#2350;&#2375;&#2306;&#2335; &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306;&#2404;</span></p>\r\n<p><br><br></p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "6",
                    section: "31",
                    question_en: "<p>6. Compact disc that can store approximately 650-800 MB of data or 74-80 min of music are</p>\n",
                    question_hi: "<ol start=\"6\">\r\n<li><span style=\"font-weight: 400;\"> &#2325;&#2377;&#2350;&#2381;&#2346;&#2376;&#2325;&#2381;&#2335; &#2337;&#2367;&#2360;&#2381;&#2325; &#2332;&#2379; &#2354;&#2327;&#2349;&#2327; 650-800 mb &#2337;&#2375;&#2335;&#2366; &#2351;&#2366; 74-80 &#2350;&#2367;&#2344;&#2335; &#2350;&#2381;&#2351;&#2370;&#2332;&#2367;&#2325; (music) &#2360;&#2381;&#2335;&#2379;&#2352; &#2325;&#2352; &#2360;&#2325;&#2340;&#2368; &#2361;&#2376;, _____ &#2361;&#2376;&#2306;&#2404;</span></li>\r\n</ol>\n",
                    options_en: ["<p>zip discs</p>\n", "<p>CD-ROM</p>\n", 
                                "<p>video cards</p>\n", "<p>pressing machines</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2332;&#2367;&#2346; &#2337;&#2367;&#2360;&#2381;&#2325; (zip discs)</span></p>\n", "<p>CD-ROM</p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2357;&#2368;&#2337;&#2367;&#2351;&#2379; &#2325;&#2366;&#2352;&#2381;&#2337; (video cards)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2352;&#2375;&#2360;&#2367;&#2306;&#2327; &#2350;&#2358;&#2368;&#2344; (pressing machines)</span></p>\n"],
                    solution_en: "<p>6.(b) CD-ROM used <strong>to store programs and data files,</strong> it holds 650 MB or 700MB of data and can store 74-80 min music.</p>\n",
                    solution_hi: "<p>6.(b) CD-ROM &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2324;&#2352; &#2337;&#2375;&#2335;&#2366; &#2347;&#2364;&#2366;&#2311;&#2354; &#2325;&#2379; &#2360;&#2381;&#2335;&#2379;&#2352; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;, &#2311;&#2360;&#2350;&#2375;&#2306; 650MB &#2351;&#2366; 700MB &#2337;&#2375;&#2335;&#2366; &#2361;&#2379;&#2340;&#2366; &#2361;&#2376; &#2324;&#2352; &#2351;&#2361; 74-80 &#2350;&#2367;&#2344;&#2335; &#2325;&#2366; &#2350;&#2381;&#2351;&#2370;&#2332;&#2367;&#2325; &#2360;&#2381;&#2335;&#2379;&#2352; &#2325;&#2352; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "7",
                    section: "31",
                    question_en: "<p>7. Pratyush is ______ fastest supercomputer in the world.</p>\n",
                    question_hi: "<p>7. &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2369;&#2359; (Pratyus) &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; _________ &#2360;&#2348;&#2360;&#2375; &#2340;&#2375;&#2332; &#2360;&#2369;&#2346;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2361;&#2376;&#2404;</p>\n",
                    options_en: ["<p>fourth</p>\n", "<p>first</p>\n", 
                                "<p>second</p>\n", "<p>third</p>\n"],
                    options_hi: ["<p>&#2330;&#2380;&#2341;&#2366;</p>\n", "<p>&#2346;&#2361;&#2354;&#2366;</p>\n",
                                "<p>&#2342;&#2370;&#2360;&#2352;&#2366;</p>\n", "<p>&#2340;&#2368;&#2360;&#2352;&#2366;</p>\n"],
                    solution_en: "<p>7.(a) Pratyush is the <strong>fourth fastest </strong>supercomputer in the world which is used for weather and climate research.</p>\r\n<p>Frontier is the<strong> fastest supercomputer</strong> in the world.</p>\r\n<p>Fugaku is the <strong>second fastest supercomputer </strong>in the world.</p>\r\n<p>HPE Cray EX235a is the third fastest supercomputer in the world.</p>\n",
                    solution_hi: "<p>7.(a) &#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2370;&#2359; &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; <strong>&#2330;&#2380;&#2341;&#2366; &#2360;&#2348;&#2360;&#2375;</strong> &#2340;&#2375;&#2332; &#2360;&#2369;&#2346;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354; &#2350;&#2380;&#2360;&#2350; &#2324;&#2352; &#2332;&#2354;&#2357;&#2366;&#2351;&#2369; &#2309;&#2344;&#2369;&#2360;&#2306;&#2343;&#2366;&#2344; (weather and climate research) &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\r\n<p><strong>&#2347;&#2381;&#2352;&#2306;&#2335;&#2367;&#2351;&#2352;</strong> (Frontier) &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; &#2360;&#2348;&#2360;&#2375; &#2340;&#2375;&#2332; &#2360;&#2369;&#2346;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2361;&#2376;&#2404;</p>\r\n<p><strong>&#2347;&#2369;&#2327;&#2366;&#2325;&#2370;</strong> (Fugaku) &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; &#2342;&#2370;&#2360;&#2352;&#2366; &#2360;&#2348;&#2360;&#2375; &#2340;&#2375;&#2332; &#2360;&#2369;&#2346;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2361;&#2376;&#2404;</p>\r\n<p><strong>HPE Cray EX235a</strong> &#2342;&#2369;&#2344;&#2367;&#2351;&#2366; &#2325;&#2366; &#2340;&#2368;&#2360;&#2352;&#2366; &#2360;&#2348;&#2360;&#2375; &#2340;&#2375;&#2332; &#2360;&#2369;&#2346;&#2352; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2361;&#2376;&#2404;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "8",
                    section: "31",
                    question_en: "<p>8. Which of the following are the components that reside on the motherboard?</p>\n",
                    question_hi: "<p>8. &#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344; &#2360;&#2375; &#2325;&#2306;&#2346;&#2379;&#2344;&#2375;&#2306;&#2335;&#2381;&#2360; (components) &#2361;&#2376;&#2306; &#2332;&#2379; &#2350;&#2342;&#2352;&#2348;&#2379;&#2352;&#2381;&#2337; (motherboard) &#2346;&#2352; &#2352;&#2361;&#2340;&#2375; &#2361;&#2376;&#2306;?</p>\n",
                    options_en: ["<p>Fan</p>\n", "<p>CMOS battery</p>\n", 
                                "<p>PCI slot</p>\n", "<p>All of these</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2347;&#2376;&#2344; (Fan)</span></p>\n", "<p>CMOS &#2348;&#2376;&#2335;&#2352;&#2368;</p>\n",
                                "<p>PCI &#2360;&#2381;&#2354;&#2377;&#2335;</p>\n", "<p>&#2313;&#2346;&#2352;&#2379;&#2325;&#2381;&#2340; &#2360;&#2349;&#2368;</p>\n"],
                    solution_en: "<p>8.(d) Various components of motherboard are &rarr;</p>\r\n<p><span style=\"font-weight: 400;\">(i) CMOS Battery&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(ii) BIOS Chip</span></p>\r\n<p><span style=\"font-weight: 400;\">(iii) Fan&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(iv) Expansion Slot</span></p>\r\n<p><span style=\"font-weight: 400;\">(v) SMPS&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(vi) PCI Slot</span></p>\r\n<p><span style=\"font-weight: 400;\">(vii) Processor Chip&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(viii) Buses</span></p>\r\n<p><strong>CMOS battery&rarr;</strong><span style=\"font-weight: 400;\">A battery that maintains the time, date, hard disk and other configuration settings in the CMOS memory.&nbsp;</span></p>\r\n<p><strong>PCI Slot&rarr;</strong><span style=\"font-weight: 400;\">A PCI slot is a built-in slot on a device that allows for the attachment of various hardware components such as network cards, modems, sound cards, disk controllers and other peripherals.</span></p>\r\n<p><span style=\"font-weight: 400;\">A </span><strong>bus</strong><span style=\"font-weight: 400;\"> is a high-speed internal connection. Buses are used to send control signals and data between the processor and other components.</span></p>\n",
                    solution_hi: "<p>8.(d) Various components of motherboard are &rarr;</p>\r\n<p><span style=\"font-weight: 400;\">(i) CMOS Battery&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(ii) BIOS Chip</span></p>\r\n<p><span style=\"font-weight: 400;\">(iii) Fan&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(iv) Expansion Slot</span></p>\r\n<p><span style=\"font-weight: 400;\">(v) SMPS&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(vi) PCI Slot</span></p>\r\n<p><span style=\"font-weight: 400;\">(vii) Processor Chip&nbsp;</span></p>\r\n<p><span style=\"font-weight: 400;\">(viii) Buses</span></p>\r\n<p><strong>CMOS battery&rarr;</strong><span style=\"font-weight: 400;\">A battery that maintains the time, date, hard disk and other configuration settings in the CMOS memory.&nbsp;</span></p>\r\n<p><strong>PCI Slot&rarr;</strong><span style=\"font-weight: 400;\">A PCI slot is a built-in slot on a device that allows for the attachment of various hardware components such as network cards, modems, sound cards, disk controllers and other peripherals.</span></p>\r\n<p><span style=\"font-weight: 400;\">A </span><strong>bus</strong><span style=\"font-weight: 400;\"> is a high-speed internal connection. Buses are used to send control signals and data between the processor and other components.</span></p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "9",
                    section: "31",
                    question_en: "<p>9. Second generation languages are languages that consists of_________</p>\n",
                    question_hi: "<p>9. &#2342;&#2370;&#2360;&#2352;&#2368; &#2346;&#2368;&#2338;&#2364;&#2368; &#2325;&#2368; &#2349;&#2366;&#2359;&#2366;&#2319;&#2305; &#2320;&#2360;&#2368; &#2349;&#2366;&#2359;&#2366;&#2319;&#2305; &#2361;&#2376;&#2306; &#2332;&#2367;&#2344;&#2350;&#2375;&#2306; __________ &#2358;&#2366;&#2350;&#2367;&#2354; &#2361;&#2376;&#2306;&#2404;</p>\n",
                    options_en: ["<p>assembly language</p>\n", "<p>machine language</p>\n", 
                                "<p>Java</p>\n", "<p>visual basic</p>\n"],
                    options_hi: ["<p>&#2309;&#2360;&#2375;&#2350;&#2381;&#2348;&#2354;&#2368; &#2354;&#2376;&#2306;&#2327;&#2381;&#2357;&#2375;&#2332; (assembly language)</p>\n", "<p>&#2350;&#2358;&#2368;&#2344; &#2354;&#2376;&#2306;&#2327;&#2381;&#2357;&#2375;&#2332; (machine language)</p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2332;&#2366;&#2357;&#2366; (Java)</span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2357;&#2367;&#2332;&#2369;&#2309;&#2354; &#2348;&#2375;&#2360;&#2367;&#2325; (visual basic)</span></p>\n"],
                    solution_en: "<p>9.(a) Assembly language is used in the Second generation. The first generation languages are also called<strong> machine languages/ 1G language</strong>. Languages used in the third generation are <strong>BASIC, COBOL, Pascal.</strong></p>\n",
                    solution_hi: "<p>&nbsp;</p>\r\n<p>9.(a) &#2360;&#2375;&#2325;&#2306;&#2337; &#2332;&#2344;&#2352;&#2375;&#2358;&#2344;(second generation) &#2350;&#2375;&#2306; &#2309;&#2360;&#2375;&#2350;&#2381;&#2348;&#2354;&#2368; &#2354;&#2376;&#2306;&#2327;&#2381;&#2357;&#2375;&#2332; (assembly language) &#2325;&#2366; &#2346;&#2381;&#2352;&#2351;&#2379;&#2327; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2347;&#2352;&#2381;&#2360;&#2381;&#2335; &#2332;&#2344;&#2352;&#2375;&#2358;&#2344;(first generation) &#2325;&#2368; &#2349;&#2366;&#2359;&#2366;&#2323;&#2306; &#2325;&#2379; &#2350;&#2358;&#2368;&#2344; &#2354;&#2376;&#2306;&#2327;&#2381;&#2357;&#2375;&#2332; (machine language)/1G &#2354;&#2376;&#2306;&#2327;&#2381;&#2357;&#2375;&#2332; &#2349;&#2368; &#2325;&#2361;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;I &#2341;&#2352;&#2381;&#2337; &#2332;&#2344;&#2352;&#2375;&#2358;&#2344;(third generation) &#2350;&#2375;&#2306; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2368; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2349;&#2366;&#2359;&#2366;&#2319;&#2305; BASIC, COBOL, &#2346;&#2366;&#2360;&#2381;&#2325;&#2354; (Pascal) &#2361;&#2376;&#2306;&#2404;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "10",
                    section: "31",
                    question_en: "<p>10.What is the full form of EDI?</p>\n",
                    question_hi: "<p><strong>10.</strong><span style=\"font-weight: 400;\"> EDI &#2325;&#2366; &#2346;&#2370;&#2352;&#2381;&#2339; &#2352;&#2370;&#2346; &#2325;&#2381;&#2351;&#2366; &#2361;&#2376;?</span></p>\n",
                    options_en: ["<p>Electronic Data Interchange</p>\n", "<p>Easy Data Interchange</p>\n", 
                                "<p>Electronic Data Interconnect</p>\n", "<p>Electrical Data Interconnect</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325; &#2337;&#2375;&#2335;&#2366; &#2311;&#2306;&#2335;&#2352;&#2330;&#2375;&#2306;&#2332; (Electronic Data Interchange)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2311;&#2332;&#2368;&nbsp; &#2337;&#2375;&#2335;&#2366; &#2311;&#2306;&#2335;&#2352;&#2330;&#2375;&#2306;&#2332; (Easy Data Interchange)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325; &#2337;&#2375;&#2335;&#2366; &#2311;&#2306;&#2335;&#2352;&#2325;&#2344;&#2375;&#2325;&#2381;&#2335; (Electronic Data Interconnect)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2367;&#2325;&#2354; &#2337;&#2375;&#2335;&#2366; &#2311;&#2306;&#2335;&#2352;&#2325;&#2344;&#2375;&#2325;&#2381;&#2335; (Electrical Data Interconnect)</span></p>\n"],
                    solution_en: "<p>10.(a) EDI, which stands for <strong>electronic data interchange</strong>, is the intercompany</p>\r\n<p>communication of business documents in a standard format.</p>\n",
                    solution_hi: "<p>10.(a) EDI,&#2325;&#2366; &#2309;&#2352;&#2381;&#2341; &#2311;&#2354;&#2375;&#2325;&#2381;&#2335;&#2381;&#2352;&#2377;&#2344;&#2367;&#2325; &#2337;&#2375;&#2335;&#2366; &#2311;&#2306;&#2335;&#2352;&#2330;&#2375;&#2306;&#2332; &#2361;&#2376;, &#2319;&#2325; &#2350;&#2366;&#2344;&#2325; &#2346;&#2381;&#2352;&#2366;&#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2357;&#2381;&#2351;&#2366;&#2357;&#2360;&#2366;&#2351;&#2367;&#2325; &#2342;&#2360;&#2381;&#2340;&#2366;&#2357;&#2375;&#2332;&#2379;&#2306; &#2325;&#2366; &#2311;&#2306;&#2335;&#2352;&#2325;&#2306;&#2346;&#2344;&#2368; &#2360;&#2306;&#2330;&#2366;&#2352; &#2361;&#2376;&#2404;</p>\n",
                    correct: "a",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "11",
                    section: "31",
                    question_en: " <p>11</span><span style=\"font-family:\">.</span><span style=\"font-family:\"> A temporary storage area, attached to the CPU, for I/O</span><span style=\"font-family:\"> operations is a</span></p>",
                    question_hi: "<p>11. I/O &#2321;&#2346;&#2352;&#2375;&#2358;&#2344;&#2381;&#2360; &#2325;&#2375; &#2354;&#2367;&#2319; CPU &#2360;&#2375; &#2332;&#2369;&#2337;&#2364;&#2366; &#2319;&#2325; &#2335;&#2375;&#2350;&#2381;&#2346;&#2352;&#2352;&#2368; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2319;&#2352;&#2367;&#2351;&#2366; (temporary storage area) &#2319;&#2325; _____ &#2361;&#2376;&#2404;</p>\n",
                    options_en: [" <p> register </span></p>", " <p> chip </span></p>", 
                                " <p> core</span></p>", " <p> buffer</span></p>"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2352;&#2332;&#2367;&#2360;&#2381;&#2335;&#2352; (register)</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2330;&#2367;&#2346; (chip)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2325;&#2379;&#2352; (core)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2348;&#2347;&#2352; (buffer)</span></p>\n"],
                    solution_en: " <p>11</span><span style=\"font-family:\">.(d) </span><span style=\"font-family:\">Buffer is a</span><span style=\"font-family:\"> </span><span style=\"font-family:\">temporary storage area, attached to the CPU, for I/O operations,where the register holds the data for further execution.</span></p>",
                    solution_hi: "<p>11.(d) &#2348;&#2347;&#2364;&#2352; &#2319;&#2325; &#2335;&#2375;&#2350;&#2381;&#2346;&#2352;&#2352;&#2368; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2319;&#2352;&#2367;&#2351;&#2366; &#2361;&#2376;, &#2332;&#2379; I/O &#2321;&#2346;&#2352;&#2375;&#2358;&#2344;&#2381;&#2360;&#2325;&#2375;&#2354;&#2367;&#2319; CPU &#2360;&#2375;&#2332;&#2369;&#2337;&#2364;&#2366;&#2361;&#2379;&#2340;&#2366;&#2361;&#2376;, &#2332;&#2361;&#2366;&#2305; &#2352;&#2332;&#2367;&#2360;&#2381;&#2335;&#2352; &#2310;&#2327;&#2375; &#2325;&#2375; &#2319;&#2325;&#2381;&#2360;&#2375;&#2325;&#2381;&#2351;&#2370;&#2358;&#2344; &#2325;&#2375; &#2354;&#2367;&#2319; &#2337;&#2375;&#2335;&#2366; &#2352;&#2326;&#2340;&#2366;&#2361;&#2376;&#2404;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "12",
                    section: "31",
                    question_en: "<p>12. ......... may be included in another folder while making a hierarchical structure folder.</p>\n",
                    question_hi: "<p>12.&#2319;&#2325; &#2361;&#2376;&#2352;&#2352;&#2381;&#2330;&#2367;&#2309;&#2354; &#2360;&#2381;&#2335;&#2381;&#2352;&#2325;&#2381;&#2330;&#2352;(hierarchical structure) &#2347;&#2364;&#2379;&#2354;&#2381;&#2337;&#2352; &#2348;&#2344;&#2366;&#2340;&#2375; &#2360;&#2350;&#2351; ......... &#2325;&#2379; &#2309;&#2344;&#2381;&#2351; &#2347;&#2364;&#2379;&#2354;&#2381;&#2337;&#2352; &#2350;&#2375;&#2306; &#2358;&#2366;&#2350;&#2367;&#2354; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                    options_en: ["<p>Minifolder</p>\n", "<p>Object folder</p>\n", 
                                "<p>Small folder</p>\n", "<p>Sub-folder</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2350;&#2367;&#2344;&#2368; &#2347;&#2379;&#2354;&#2381;&#2337;&#2352;(Minifolder)</span></p>\r\n<p><span style=\"font-weight: 400;\">&nbsp;</span></p>\n", "<p><span style=\"font-weight: 400;\">&#2321;&#2348;&#2381;&#2332;&#2375;&#2325;&#2381;&#2335; &#2347;&#2364;&#2379;&#2354;&#2381;&#2337;&#2352;(Object folder)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p><span style=\"font-weight: 400;\">&nbsp;&#2360;&#2381;&#2350;&#2366;&#2354; &#2347;&#2364;&#2379;&#2354;&#2381;&#2337;&#2352;(Small folder)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2348; -&#2347;&#2364;&#2379;&#2354;&#2381;&#2337;&#2352;(Sub-folder)</span></p>\n"],
                    solution_en: "<p>12.(d) A subfolder is similar to a subdomain in that it <strong>allows you to create categories of content</strong>, but they are set up differently on servers.</p>\n",
                    solution_hi: "<p>12.(d) &#2319;&#2325; &#2360;&#2348;&#2347;&#2364;&#2379;&#2354;&#2381;&#2337;&#2352;(subfolder) &#2319;&#2325; &#2360;&#2348;&#2337;&#2379;&#2350;&#2375;&#2344;(subdomain) &#2325;&#2375; &#2360;&#2350;&#2366;&#2344; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; &#2351;&#2361; &#2310;&#2346;&#2325;&#2379; &#2325;&#2306;&#2335;&#2375;&#2306;&#2335; &#2325;&#2379; &#2358;&#2381;&#2352;&#2375;&#2339;&#2367;&#2351;&#2366;&#2306;(categories) &#2348;&#2344;&#2366;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2340;&#2366; &#2361;&#2376;, &#2354;&#2375;&#2325;&#2367;&#2344; &#2357;&#2375; &#2360;&#2352;&#2381;&#2357;&#2352;(server) &#2346;&#2352; &#2309;&#2354;&#2327; &#2340;&#2352;&#2361; &#2360;&#2375; &#2360;&#2375;&#2335; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "13",
                    section: "31",
                    question_en: "<p>13. Which of the following are properties of USB?</p>\n",
                    question_hi: "<p>13. &#2344;&#2367;&#2350;&#2381;&#2344; &#2350;&#2375;&#2306; &#2360;&#2375; &#2325;&#2380;&#2344;-&#2360;&#2368; USB &#2325;&#2368; &#2357;&#2367;&#2358;&#2375;&#2359;&#2340;&#2366;&#2319;&#2305; &#2361;&#2376;&#2306;?</p>\n",
                    options_en: ["<p>Platform independent</p>\n", "<p>Platform dependent</p>\n", 
                                "<p>Source dependent</p>\n", "<p>Software dependent</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2346;&#2381;&#2354;&#2375;&#2335;&#2347;&#2364;&#2377;&#2352;&#2381;&#2350; &#2311;&#2306;&#2337;&#2367;&#2346;&#2375;&#2306;&#2337;&#2375;&#2306;&#2335; (Platform independent)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2346;&#2381;&#2354;&#2375;&#2335;&#2347;&#2364;&#2377;&#2352;&#2381;&#2350; &#2337;&#2367;&#2346;&#2375;&#2306;&#2337;&#2375;&#2306;&#2335; (Platform dependent)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2379;&#2352;&#2381;&#2360; &#2337;&#2367;&#2346;&#2375;&#2306;&#2337;&#2375;&#2306;&#2335; (Source dependent)</span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2377;&#2347;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2337;&#2367;&#2346;&#2375;&#2306;&#2337;&#2375;&#2306;&#2335; (Software dependent)</span></p>\n"],
                    solution_en: "<p>13.(b) It is a device used for data transfer.It is<strong> platform-independent</strong> and refers to softwares that can work on different hardwares or softwares.</p>\n",
                    solution_hi: "<p>13.(b) &#2351;&#2361; &#2337;&#2375;&#2335;&#2366; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2352; &#2325;&#2375; &#2354;&#2367;&#2319; &#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2344;&#2375; &#2357;&#2366;&#2354;&#2366; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2361;&#2376;&#2404; &#2351;&#2361; &#2346;&#2381;&#2354;&#2375;&#2335;&#2347;&#2364;&#2377;&#2352;&#2381;&#2350;-&#2311;&#2306;&#2337;&#2367;&#2346;&#2375;&#2306;&#2337;&#2375;&#2306;&#2335; &#2361;&#2376; &#2324;&#2352; &#2320;&#2360;&#2375; &#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2325;&#2379; &#2360;&#2306;&#2342;&#2352;&#2381;&#2349;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2332;&#2379; &#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344; &#2361;&#2366;&#2352;&#2381;&#2337;&#2357;&#2375;&#2351;&#2352; &#2351;&#2366; &#2360;&#2377;&#2347;&#2364;&#2381;&#2335;&#2357;&#2375;&#2351;&#2352; &#2346;&#2352; &#2325;&#2366;&#2350; &#2325;&#2352; &#2360;&#2325;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</p>\n",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "14",
                    section: "31",
                    question_en: "<p>14. Which was the first PC boot sector virus?</p>\n",
                    question_hi: "<p>14. &#2346;&#2361;&#2354;&#2366; &#2346;&#2368;&#2360;&#2368; &#2348;&#2370;&#2335; &#2360;&#2375;&#2325;&#2381;&#2335;&#2352; &#2357;&#2366;&#2351;&#2352;&#2360; &#2325;&#2380;&#2344; &#2360;&#2366; &#2341;&#2366;?</p>\n",
                    options_en: ["<p>Brain</p>\n", "<p>Bomb</p>\n", 
                                "<p>Payload</p>\n", "<p>Creeper</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2348;&#2381;&#2352;&#2375;&#2344; (Brain)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2348;&#2350; (Bomb)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2346;&#2375;&#2354;&#2379;&#2337; (Payload)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2325;&#2381;&#2352;&#2368;&#2346;&#2352; (Creeper)</span></p>\n"],
                    solution_en: "<p>14.(d) <strong>Brain</strong>, the first PC virus, began infecting in 1986.</p>\n",
                    solution_hi: "<p>14.(d)<strong> &#2348;&#2381;&#2352;&#2375;&#2344;</strong>, &#2346;&#2361;&#2354;&#2366; &#2346;&#2368;&#2360;&#2368; &#2357;&#2366;&#2351;&#2352;&#2360;, 1986 &#2350;&#2375;&#2306; &#2360;&#2306;&#2325;&#2381;&#2352;&#2350;&#2367;&#2340; &#2325;&#2352;&#2344;&#2366; &#2358;&#2369;&#2352;&#2370; &#2361;&#2369;&#2310;&#2404;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "15",
                    section: "31",
                    question_en: "<p><strong>15.</strong><span style=\"font-weight: 400;\"> A hybrid computer is the one having the combined properties of </span></p>\n",
                    question_hi: "<p>15. &#2319;&#2325; &#2361;&#2366;&#2311;&#2348;&#2381;&#2352;&#2367;&#2337; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2357;&#2361; &#2361;&#2376; &#2332;&#2367;&#2360;&#2350;&#2375;&#2306; ______ &#2325;&#2375; &#2325;&#2306;&#2348;&#2366;&#2311;&#2306;&#2337; &#2346;&#2381;&#2352;&#2377;&#2346;&#2352;&#2381;&#2335;&#2368;&#2332; (combined properties) &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404;</p>\n",
                    options_en: ["<p>super and microcomputers</p>\n", "<p>mini and microcomputers</p>\n", 
                                "<p>analog and digital computers</p>\n", "<p>super and mini computers</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2360;&#2369;&#2346;&#2352; &#2324;&#2352; &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; (super &amp; microcomputers)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2350;&#2367;&#2344;&#2368; &#2324;&#2352; &#2350;&#2366;&#2311;&#2325;&#2381;&#2352;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; (mini &amp; microcomputers)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2319;&#2344;&#2366;&#2354;&#2377;&#2327; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; (analog &amp; digital computers)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2369;&#2346;&#2352; &#2324;&#2352; &#2350;&#2367;&#2344;&#2368; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; (super &amp; mini computers)</span></p>\n"],
                    solution_en: "<p>15.(c) These are the combination of analog and digital computers. Machines used in hospitals like ECG and DIALYSIS are the commonly used hybrid computers.</p>\n",
                    solution_hi: "<p>15.(c) &#2351;&#2375; &#2319;&#2344;&#2366;&#2354;&#2377;&#2327; &#2324;&#2352; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2325;&#2375; &#2360;&#2306;&#2351;&#2379;&#2332;&#2344; (combination) &#2361;&#2376;&#2306;&#2404; &#2309;&#2360;&#2381;&#2346;&#2340;&#2366;&#2354;&#2379;&#2306; &#2350;&#2375;&#2306; &#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2368; &#2350;&#2358;&#2368;&#2344;&#2375;&#2306; &#2332;&#2376;&#2360;&#2375; ECG &#2324;&#2352; DIALYSIS &#2310;&#2350;&#2340;&#2380;&#2352; &#2346;&#2352; &#2311;&#2360;&#2381;&#2340;&#2375;&#2350;&#2366;&#2354; &#2361;&#2379;&#2344;&#2375; &#2357;&#2366;&#2354;&#2375; &#2361;&#2366;&#2311;&#2348;&#2381;&#2352;&#2367;&#2337; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2361;&#2376;&#2306;&#2404;</p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "16",
                    section: "31",
                    question_en: " <p>16</span><span style=\"font-family:\">.</span><span style=\"font-family:\"> Data duplication wastes the space, but also promotes a more serious problem called</span></p>",
                    question_hi: " <p>16</span><span style=\"font-family:\">.</span><span style=\"font-family:\"> </span><span style=\"font-family:\">डेटा</span><span style=\"font-family:\"> </span><span style=\"font-family:\">डुप्लीकेशन</span><span style=\"font-family:\"> </span><span style=\"font-family:\">स्पेस</span><span style=\"font-family:\"> </span><span style=\"font-family:\">को</span><span style=\"font-family:\"> </span><span style=\"font-family:\">बर्बाद</span><span style=\"font-family:\"> </span><span style=\"font-family:\">कर</span><span style=\"font-family:\"> </span><span style=\"font-family:\">देता</span><span style=\"font-family:\"> </span><span style=\"font-family:\">है</span><span style=\"font-family:\">, </span><span style=\"font-family:\">लेकिन</span><span style=\"font-family:\"> </span><span style=\"font-family:\">यह</span><span style=\"font-family:\"> </span><span style=\"font-family:\">नामक</span><span style=\"font-family:\"> </span><span style=\"font-family:\">एक</span><span style=\"font-family:\"> </span><span style=\"font-family:\">और</span><span style=\"font-family:\"> </span><span style=\"font-family:\">गंभीर</span><span style=\"font-family:\"> </span><span style=\"font-family:\">समस्या</span><span style=\"font-family:\"> </span><span style=\"font-family:\">को</span><span style=\"font-family:\"> </span><span style=\"font-family:\">भी</span><span style=\"font-family:\"> </span><span style=\"font-family:\">बढ़ावा</span><span style=\"font-family:\"> </span><span style=\"font-family:\">देता</span></p> <p><span style=\"font-family:\">है</span><span style=\"font-family:\">I </span></p>",
                    options_en: [" <p> isolated </span></p>", " <p> data inconsistency</span></p>", 
                                " <p> other than those given as options</span></p>", " <p> program dependency</span></p>"],
                    options_hi: [" <p> </span><span style=\"font-family:\">आइसोलेटेड</span><span style=\"font-family:\">(isolated) </span></p>", " <p> </span><span style=\"font-family:\">डेटा</span><span style=\"font-family:\"> </span><span style=\"font-family:\">इनकंसीस्टे</span><span style=\"font-family:\">ंसी</span><span style=\"font-family:\">(data inconsistency) </span></p>",
                                " <p> </span><span style=\"font-family:\">दिए</span><span style=\"font-family:\"> </span><span style=\"font-family:\">गए</span><span style=\"font-family:\"> </span><span style=\"font-family:\">विकल्पों</span><span style=\"font-family:\"> </span><span style=\"font-family:\">के</span><span style=\"font-family:\"> </span><span style=\"font-family:\">अलावा</span><span style=\"font-family:\"> </span><span style=\"font-family:\">अन्य</span></p>", " <p>  </span><span style=\"font-family:\">प्रोग्राम</span><span style=\"font-family:\"> </span><span style=\"font-family:\">डिपेंडेंसी</span><span style=\"font-family:\">(program dependency) </span></p>"],
                    solution_en: " <p>16</span><span style=\"font-family:\">.(b)</span><span style=\"font-family:\"> </span><span style=\"font-family:\">Data inconsistency occurs when the same data exists in different formats in multiple tables.</span></p>",
                    solution_hi: " <p>16</span><span style=\"font-family:\">.(b)</span><span style=\"font-family:\"> </span><span style=\"font-family:\">डेटा</span><span style=\"font-family:\"> </span><span style=\"font-family:\">इनकंसीस्टेंसी</span><span style=\"font-family:\"> </span><span style=\"font-family:\">तब</span><span style=\"font-family:\"> </span><span style=\"font-family:\">होती</span><span style=\"font-family:\"> </span><span style=\"font-family:\">है</span><span style=\"font-family:\"> </span><span style=\"font-family:\">जब</span><span style=\"font-family:\"> </span><span style=\"font-family:\">एक</span><span style=\"font-family:\"> </span><span style=\"font-family:\">ही</span><span style=\"font-family:\"> </span><span style=\"font-family:\">डेटा</span><span style=\"font-family:\"> </span><span style=\"font-family:\">कई</span><span style=\"font-family:\"> </span><span style=\"font-family:\">टेबल्स</span><span style=\"font-family:\"> </span><span style=\"font-family:\">में</span><span style=\"font-family:\"> </span><span style=\"font-family:\">विभिन्न</span><span style=\"font-family:\"> </span><span style=\"font-family:\">स्वरूपों</span><span style=\"font-family:\"> </span><span style=\"font-family:\">में</span><span style=\"font-family:\"> </span><span style=\"font-family:\">मौजूद</span><span style=\"font-family:\"> </span><span style=\"font-family:\">होता</span><span style=\"font-family:\"> </span><span style=\"font-family:\">है।</span></p>",
                    correct: "b",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "17",
                    section: "31",
                    question_en: " <p>17</span><span style=\"font-family:\">.</span><span style=\"font-family:\"> Compiling creates a (n</span><span style=\"font-family:\">)_</span><span style=\"font-family:\">__________</span></p>",
                    question_hi: "<p>17.&#2325;&#2350;&#2381;&#2346;&#2366;&#2311;&#2354;&#2367;&#2306;&#2327;(Compiling) &#2319;&#2325;___________ &#2348;&#2344;&#2366;&#2340;&#2366; &#2361;&#2376;</p>\n",
                    options_en: [" <p> Error-free program</span></p>", " <p> Program specification</span></p>", 
                                " <p> Subroutine</span></p>", " <p> Executable program</span></p>"],
                    options_hi: ["<p>&#2319;&#2352;&#2352;- &#2347;&#2381;&#2352;&#2368; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;(Error-free program)</p>\n", "<p>&#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2360;&#2381;&#2346;&#2375;&#2360;&#2367;&#2347;&#2367;&#2325;&#2375;&#2358;&#2344;(Program specification)</p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2360;&#2348;&#2352;&#2370;&#2335;&#2368;&#2344;(Subroutine)</span></p>\n", "<p>&#2319;&#2325;&#2381;&#2360;&#2375;&#2325;&#2381;&#2351;&#2370;&#2335;&#2348;&#2354; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;(Executable program)</p>\n"],
                    solution_en: " <p>17</span><span style=\"font-family:\">.(d)</span><span style=\"font-family:\"> Compiler is</span><span style=\"font-family:\"> a program that converts in</span><span style=\"font-family:\">structions into a machine-code or lower-level form so that they can be read and executed by a computer.</span></p>",
                    solution_hi: "<p>17.(d) &#2325;&#2306;&#2346;&#2366;&#2311;&#2354;&#2352;(compiler) &#2319;&#2325; &#2346;&#2381;&#2352;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350; &#2361;&#2376; &#2332;&#2379; &#2344;&#2367;&#2352;&#2381;&#2342;&#2375;&#2358;&#2379;&#2306; &#2325;&#2379; &#2350;&#2358;&#2368;&#2344;-&#2325;&#2379;&#2337;(machine-code) &#2351;&#2366; &#2354;&#2379;&#2309;&#2352;-&#2354;&#2375;&#2357;&#2354;(lower-level) &#2325;&#2375; &#2352;&#2370;&#2346; &#2350;&#2375;&#2306; &#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2367;&#2340; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376; &#2340;&#2366;&#2325;&#2367; &#2313;&#2344;&#2381;&#2361;&#2375;&#2306; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352; &#2342;&#2381;&#2357;&#2366;&#2352;&#2366; &#2346;&#2338;&#2364;&#2366; &#2324;&#2352; &#2319;&#2325;&#2381;&#2360;&#2375;&#2325;&#2381;&#2351;&#2370;&#2335;(execute) &#2325;&#2367;&#2351;&#2366; &#2332;&#2366; &#2360;&#2325;&#2375;&#2404;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "18",
                    section: "31",
                    question_en: "<p>18. Name the fourth layer of OSI model</p>\n",
                    question_hi: "<p>18. OSI &#2350;&#2377;&#2337;&#2354; &#2325;&#2368; &#2330;&#2380;&#2341;&#2368;&#2346;&#2352;&#2340; &#2325;&#2366; &#2344;&#2366;&#2350; &#2348;&#2340;&#2366;&#2311;&#2319;</p>\n",
                    options_en: ["<p>Application layer</p>\n", "<p>Data link layer</p>\n", 
                                "<p>Transport layer</p>\n", "<p>Session layer</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&nbsp;&#2319;&#2346;&#2381;&#2354;&#2368;&#2325;&#2375;&#2358;&#2344; &#2354;&#2375;&#2351;&#2352;(application layer)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2337;&#2375;&#2335;&#2366; &#2354;&#2367;&#2306;&#2325; &#2354;&#2375;&#2351;&#2352;(data link layer)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p><span style=\"font-weight: 400;\">&#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2346;&#2379;&#2352;&#2381;&#2335; &#2354;&#2375;&#2351;&#2352; &#2346;&#2352;&#2340;(transport layer)</span></p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&#2360;&#2375;&#2358;&#2344; &#2354;&#2375;&#2351;&#2352;(session layer)</span></p>\n"],
                    solution_en: "<p>18.(c) Layer 4 of the OSI Model is <strong>Transport Layer </strong>provides transparent transfer of data between end users, providing reliable data transfer services to the upper layers.</p>\r\n<p><strong>Data Link Layer</strong> protocols are generally responsible to simply ensure and confirm that the bits and bytes that are received are identical to the bits and bytes being transferred. Session Layer is the layer of the ISO Open Systems Interconnection (OSI) model that controls the dialogues (connections) between computers. The application layer is <strong>the layer that users interact with and use</strong>. This layer allows users to send data, access data and use networks.</p>\r\n<p>&nbsp;</p>\n",
                    solution_hi: "<p>18.(c) OSI &#2350;&#2377;&#2337;&#2354; &#2325;&#2366; &#2354;&#2375;&#2351;&#2352; 4<strong> &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2346;&#2379;&#2352;&#2381;&#2335; &#2354;&#2375;&#2351;&#2352;(Transport layer)</strong> &#2361;&#2376; &#2332;&#2379; &#2309;&#2306;&#2340;&#2367;&#2350; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366;&#2323;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2337;&#2375;&#2335;&#2366; &#2325;&#2366; &#2346;&#2366;&#2352;&#2342;&#2352;&#2381;&#2358;&#2368; &#2361;&#2360;&#2381;&#2340;&#2366;&#2306;&#2340;&#2352;&#2339; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;, &#2314;&#2346;&#2352;&#2368; &#2346;&#2352;&#2340;&#2379;&#2306; &#2325;&#2379; &#2357;&#2367;&#2358;&#2381;&#2357;&#2360;&#2344;&#2368;&#2351; &#2337;&#2375;&#2335;&#2366; &#2335;&#2381;&#2352;&#2366;&#2306;&#2360;&#2347;&#2352; &#2360;&#2375;&#2357;&#2366;&#2319;&#2306; &#2346;&#2381;&#2352;&#2342;&#2366;&#2344; &#2325;&#2352;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\r\n<p><strong>&#2337;&#2375;&#2335;&#2366; &#2354;&#2367;&#2306;&#2325; &#2354;&#2375;&#2351;&#2352;(data link layer)</strong> &#2346;&#2381;&#2352;&#2379;&#2335;&#2379;&#2325;&#2377;&#2354; &#2310;&#2350; &#2340;&#2380;&#2352; &#2346;&#2352; &#2325;&#2375;&#2357;&#2354; &#2351;&#2361; &#2360;&#2369;&#2344;&#2367;&#2358;&#2381;&#2330;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2346;&#2369;&#2359;&#2381;&#2335;&#2367; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2332;&#2364;&#2367;&#2350;&#2381;&#2350;&#2375;&#2342;&#2366;&#2352; &#2361;&#2379;&#2340;&#2375; &#2361;&#2376;&#2306; &#2325;&#2367; &#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; &#2348;&#2367;&#2335;&#2381;&#2360; &#2324;&#2352; &#2348;&#2366;&#2311;&#2335;&#2381;&#2360; &#2348;&#2367;&#2335;&#2381;&#2360; &#2324;&#2352; &#2348;&#2366;&#2311;&#2335;&#2381;&#2360; &#2325;&#2379; &#2360;&#2381;&#2341;&#2366;&#2344;&#2366;&#2306;&#2340;&#2352;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2360;&#2350;&#2366;&#2344; &#2361;&#2376;&#2306;&#2404; <strong>&#2360;&#2375;&#2358;&#2344; &#2354;&#2375;&#2351;&#2352;(session layer)</strong> &#2310;&#2312;&#2319;&#2360;&#2323; &#2323;&#2346;&#2344; &#2360;&#2367;&#2360;&#2381;&#2335;&#2350;&#2381;&#2360; &#2311;&#2306;&#2335;&#2352;&#2325;&#2344;&#2375;&#2325;&#2381;&#2358;&#2344;(Open Systems Interconnection) (&#2323;&#2319;&#2360;&#2310;&#2312;) &#2350;&#2377;&#2337;&#2354; &#2325;&#2368; &#2346;&#2352;&#2340; &#2361;&#2376; &#2332;&#2379; &#2325;&#2306;&#2346;&#2381;&#2351;&#2370;&#2335;&#2352;&#2379;&#2306; &#2325;&#2375; &#2348;&#2368;&#2330; &#2360;&#2306;&#2357;&#2366;&#2342; (&#2325;&#2344;&#2375;&#2325;&#2381;&#2358;&#2344;) &#2325;&#2379; &#2344;&#2367;&#2351;&#2306;&#2340;&#2381;&#2352;&#2367;&#2340; &#2325;&#2352;&#2340;&#2368; &#2361;&#2376;&#2404; <strong>&#2319;&#2346;&#2381;&#2354;&#2368;&#2325;&#2375;&#2358;&#2344; &#2354;&#2375;&#2351;&#2352;(application layer)</strong> &#2357;&#2361; &#2354;&#2375;&#2351;&#2352; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2375; &#2360;&#2366;&#2341; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366; &#2311;&#2306;&#2335;&#2352;&#2376;&#2325;&#2381;&#2335; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306; &#2324;&#2352; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2340;&#2375; &#2361;&#2376;&#2306;&#2404; &#2351;&#2361; &#2346;&#2352;&#2340; &#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2352;&#2381;&#2340;&#2366;&#2323;&#2306; &#2325;&#2379; &#2337;&#2375;&#2335;&#2366; &#2349;&#2375;&#2332;&#2344;&#2375;, &#2337;&#2375;&#2335;&#2366; &#2340;&#2325; &#2346;&#2361;&#2369;&#2305;&#2330;&#2344;&#2375; &#2324;&#2352; &#2344;&#2375;&#2335;&#2357;&#2352;&#2381;&#2325; &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2344;&#2375; &#2325;&#2368; &#2309;&#2344;&#2369;&#2350;&#2340;&#2367; &#2342;&#2375;&#2340;&#2368; &#2361;&#2376;&#2404;</p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "19",
                    section: "31",
                    question_en: "<p>19. Hard disk devices are considered ...... storage.</p>\n",
                    question_hi: "<p>19. &#2361;&#2366;&#2352;&#2381;&#2337; &#2337;&#2367;&#2360;&#2381;&#2325; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2325;&#2379; ...... &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2350;&#2366;&#2344;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404;</p>\n",
                    options_en: ["<p>flash</p>\n", "<p>temporary</p>\n", 
                                "<p>worthless</p>\n", "<p>non-volatile</p>\n"],
                    options_hi: ["<p><span style=\"font-weight: 400;\">&#2347;&#2381;&#2354;&#2376;&#2358; (flash)</span></p>\r\n<p>&nbsp;</p>\r\n<p>&nbsp;</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2335;&#2375;&#2350;&#2381;&#2346;&#2352;&#2352;&#2368; (temporary)</span></p>\r\n<p>&nbsp;</p>\n",
                                "<p>&#2357;&#2352;&#2381;&#2341;&#2354;&#2376;&#2360; (worthless)</p>\n", "<p><span style=\"font-weight: 400;\">&nbsp;&#2344;&#2377;&#2344;-&#2357;&#2379;&#2354;&#2375;&#2335;&#2366;&#2311;&#2354; (non-volatile)</span></p>\n"],
                    solution_en: "<p>19.(d) <strong>HDD(Hard disk drive)</strong> is a data storage device used for storing and retrieving digital information using rotating disks (platters) coated with magnetic material.<strong>It is a non-volatile</strong> and random access digital data storage device.</p>\n",
                    solution_hi: "<p>19.(d) HDD (&#2361;&#2366;&#2352;&#2381;&#2337; &#2337;&#2367;&#2360;&#2381;&#2325; &#2337;&#2381;&#2352;&#2366;&#2311;&#2357;) &#2319;&#2325; &#2337;&#2375;&#2335;&#2366; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2350;&#2376;&#2327;&#2381;&#2344;&#2375;&#2335;&#2367;&#2325; &#2350;&#2335;&#2375;&#2352;&#2367;&#2351;&#2354; &#2325;&#2375; &#2360;&#2366;&#2341; &#2325;&#2379;&#2335;&#2375;&#2337; &#2352;&#2379;&#2335;&#2375;&#2335;&#2367;&#2306;&#2327; &#2337;&#2367;&#2360;&#2381;&#2325; (&#2346;&#2381;&#2354;&#2376;&#2335;&#2352;&#2381;&#2360;) &#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2325;&#2352;&#2325;&#2375; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2332;&#2366;&#2344;&#2325;&#2366;&#2352;&#2368; &#2325;&#2379; &#2360;&#2306;&#2327;&#2381;&#2352;&#2361;&#2368;&#2340; &#2325;&#2352;&#2344;&#2375; &#2324;&#2352; &#2346;&#2369;&#2344;&#2352;&#2381;&#2346;&#2381;&#2352;&#2366;&#2346;&#2381;&#2340; (storing and retrieving) &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; &#2351;&#2361; &#2319;&#2325; &#2344;&#2377;&#2344;-&#2357;&#2379;&#2354;&#2366;&#2335;&#2366;&#2311;&#2354; &#2324;&#2352; &#2352;&#2376;&#2306;&#2337;&#2350; &#2319;&#2325;&#2381;&#2360;&#2375;&#2360; &#2337;&#2367;&#2332;&#2367;&#2335;&#2354; &#2337;&#2375;&#2335;&#2366; &#2360;&#2381;&#2335;&#2379;&#2352;&#2375;&#2332; &#2337;&#2367;&#2357;&#2366;&#2311;&#2360; &#2361;&#2376;&#2404;</p>\n",
                    correct: "d",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
                questions.push({
                    id: "20",
                    section: "misc",
                    question_en: "<p>20. ______ shortcut key is used in MS-Excel 2010 to open the review tab.</p>\n",
                    question_hi: "<p>20.&#2319;&#2350;&#2319;&#2360;-&#2319;&#2325;&#2381;&#2360;&#2375;&#2354; 2010 (MS -Excel 2010 ) &#2350;&#2375;&#2306;&#2352;&#2367;&#2357;&#2381;&#2351;&#2369; (review) &#2335;&#2376;&#2348;&#2326;&#2379;&#2354;&#2344;&#2375;&#2325;&#2375;&#2354;&#2367;&#2319; _____&#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335;&#2325;&#2368; (key) &#2325;&#2366;&#2313;&#2346;&#2351;&#2379;&#2327;&#2325;&#2367;&#2351;&#2366;&#2332;&#2366;&#2340;&#2366;&#2361;&#2376;</p>\n",
                    options_en: ["<p>ALT + A</p>\n", "<p>ALT + N</p>\n", 
                                "<p>ALT + R</p>\n", "<p>ALT + I</p>\n"],
                    options_hi: ["<p>ALT + A</p>\n", "<p>ALT + N</p>\n",
                                "<p>ALT + R</p>\n", "<p>ALT + I</p>\n"],
                    solution_en: "<p>20.(c) In Microsoft Excel, pressing <strong>Alt + R</strong> opens the Review tab in the Ribbon. ALT+A &rarr;Alt + A is a keyboard shortcut often used to open the Data tab in Excel. ALT + N&rarr; Open the Insert tab to insert tables, pictures and shapes, headers, or text boxes.</p>\n",
                    solution_hi: "<p>20.(c) Microsoft Excel &#2350;&#2375;&#2306;, <strong>Alt + R </strong>&#2342;&#2348;&#2366;&#2344;&#2375; &#2360;&#2375; &#2352;&#2367;&#2348;&#2344; &#2350;&#2375;&#2306; &#2352;&#2367;&#2357;&#2381;&#2351;&#2369; &#2335;&#2376;&#2348; (review tab) &#2326;&#2369;&#2354; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; ALT + A &rarr;Alt A &#2319;&#2325; &#2325;&#2368;&#2348;&#2379;&#2352;&#2381;&#2337; &#2358;&#2377;&#2352;&#2381;&#2335;&#2325;&#2335; &#2361;&#2376; &#2332;&#2367;&#2360;&#2325;&#2366; &#2313;&#2346;&#2351;&#2379;&#2327; &#2309;&#2325;&#2381;&#2360;&#2352; &#2319;&#2325;&#2381;&#2360;&#2375;&#2354; (Excel) &#2350;&#2375;&#2306; &#2337;&#2375;&#2335;&#2366; &#2335;&#2376;&#2348; &#2326;&#2379;&#2354;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2325;&#2367;&#2351;&#2366; &#2332;&#2366;&#2340;&#2366; &#2361;&#2376;&#2404; ALT + N&rarr; &#2335;&#2375;&#2348;&#2354; , &#2330;&#2367;&#2340;&#2381;&#2352; &#2324;&#2352; &#2310;&#2325;&#2371;&#2340;&#2367;&#2351;&#2366;&#2305;, &#2361;&#2376;&#2306;&#2337;&#2352;&#2381;&#2360; , &#2351;&#2366; &#2335;&#2375;&#2325;&#2381;&#2360;&#2381;&#2335; &#2348;&#2377;&#2325;&#2381;&#2360; &#2360;&#2350;&#2381;&#2350;&#2367;&#2354;&#2367;&#2340; &#2325;&#2352;&#2344;&#2375; &#2325;&#2375; &#2354;&#2367;&#2319; &#2311;&#2344;&#2381;&#2360;&#2352;&#2381;&#2335; (Insert) &#2335;&#2376;&#2348; &#2326;&#2379;&#2354;&#2375;&#2306;&#2404;</p>\n",
                    correct: "c",
                    pos_marks: 3.0,
                    neg_marks: 1.0
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>