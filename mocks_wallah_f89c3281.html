<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">25:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 25 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. The sum of the present ages of a father and son is 50 years. If after 5 years, the father&rsquo;s age will be 5 times the age of the son, then what was the father&rsquo;s age 5 years ago?</p>",
                    question_hi: "<p>1. एक पिता और बेटे की वर्तमान आयु का योग 50 वर्ष है। यदि 5 वर्ष बाद पिता की आयु बेटे की आयु की 5 गुना होगी, तो 5 वर्ष पहले पिता की आयु क्या थी?</p>",
                    options_en: ["<p>40</p>", "<p>43</p>", 
                                "<p>47</p>", "<p>36</p>"],
                    options_hi: ["<p>40</p>", "<p>43</p>",
                                "<p>47</p>", "<p>36</p>"],
                    solution_en: "<p>1.(a) <br>Let father&rsquo;s present age = <math display=\"inline\"><mi>x</mi></math> year<br>Then son&rsquo;s age = (50 - <math display=\"inline\"><mi>x</mi></math>) years<br>According to question, after 5 years<br>5 (50 - <math display=\"inline\"><mi>x</mi></math> + 5) = (x - 5)<br>275 - 5<math display=\"inline\"><mi>x</mi></math> = x - 5<br>6<math display=\"inline\"><mi>x</mi></math> = 270 &rArr; x = 45 years<br>Father&rsquo;s age 5 years ago = 45 - 5 = 40 years</p>",
                    solution_hi: "<p>1.(a) <br>माना पिता की वर्तमान आयु = <math display=\"inline\"><mi>x</mi></math> वर्ष<br>तो पुत्र की वर्तमान आयु = (50 - <math display=\"inline\"><mi>x</mi></math>) वर्ष<br>प्रश्न के अनुसार, 5 वर्ष बाद <br>5 (50 - <math display=\"inline\"><mi>x</mi></math> + 5) = (x - 5)<br>275 - 5<math display=\"inline\"><mi>x</mi></math> = x - 5<br>6<math display=\"inline\"><mi>x</mi></math> = 270 &rArr; x = 45 वर्ष<br>5 वर्ष पहले पिता की आयु = 45 - 5 = 40 वर्ष</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. The cost price of a fan is ₹4,400. A merchant wants to make a 24% profit by selling it. At the time of sale, merchant declares a discount of 12% on the marked price. Find the marked price.</p>",
                    question_hi: "<p>2. एक पंखे का क्रय मूल्य ₹4,400 है। एक व्यापारी इसे बेचकर 24% लाभ कमाना चाहता है। बिक्री के समय, व्यापारी अंकित मूल्य पर 12% की छूट की घोषणा करता है। अंकित मूल्य ज्ञात कीजिए।</p>",
                    options_en: ["<p>₹6,020</p>", "<p>₹2,060</p>", 
                                "<p>₹2,600</p>", "<p>₹6,200</p>"],
                    options_hi: ["<p>₹6,020</p>", "<p>₹2,060</p>",
                                "<p>₹2,600</p>", "<p>₹6,200</p>"],
                    solution_en: "<p>2.(d) <br>C.P. of fan = 4,400<br><math display=\"inline\"><mfrac><mrow><mi>C</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow><mrow><mi>M</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mi>P</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>%</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>C</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow><mrow><mi>M</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>12</mn></mrow><mrow><mn>100</mn><mo>+</mo><mn>24</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi>C</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow><mrow><mi>M</mi><mo>.</mo><mi>P</mi><mo>.</mo></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>88</mn><mn>124</mn></mfrac><mo>=</mo><mfrac><mn>22</mn><mn>31</mn></mfrac></math><br>C.P. (22 unit) = 4400<br>M.P. (31 unit) = <math display=\"inline\"><mfrac><mrow><mn>4400</mn></mrow><mrow><mn>22</mn></mrow></mfrac><mo>&#215;</mo><mn>31</mn></math> = 6200<br><strong>Short trick:</strong><br>24% = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, 12% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>25</mn></mfrac></math><br>C.P.&nbsp; &nbsp;:&nbsp; &nbsp; M.P.&nbsp; &nbsp; :&nbsp; &nbsp; S.P.<br>&nbsp; 25&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;31<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 25&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; 22<br>------------------------------------- <br>&nbsp;550&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;775&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;682 <br>C.P. (550 unit) = 4400<br>M.P. (775 unit) = <math display=\"inline\"><mfrac><mrow><mn>4400</mn></mrow><mrow><mn>550</mn></mrow></mfrac><mo>&#215;</mo></math>775 = 8 &times; 775 = 6200</p>",
                    solution_hi: "<p>2.(d) <br>पंखे का क्रय मूल्य = 4,400<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac><mo>=</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mn>100</mn><mo>-</mo><mo>&#160;</mo><mi>&#2331;&#2370;&#2335;</mi><mo>%</mo></mrow><mrow><mn>100</mn><mo>+</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi><mo>%</mo></mrow></mfrac></mstyle></math>&nbsp;<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;</mi></mrow></mfrac></math> ==&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>100</mn><mo>-</mo><mn>12</mn></mrow><mrow><mn>100</mn><mo>+</mo><mn>24</mn></mrow></mfrac></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>88</mn><mn>124</mn></mfrac><mo>=</mo><mfrac><mn>22</mn><mn>31</mn></mfrac></math><br>क्रय मूल्य (22 इकाई) = 4400<br>अंकित मूल्य (31 इकाई) = <math display=\"inline\"><mfrac><mrow><mn>4400</mn></mrow><mrow><mn>22</mn></mrow></mfrac><mo>&#215;</mo><mn>31</mn></math> = 6200<br><strong>शॉर्ट ट्रिक:</strong><br>24% = <math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>25</mn></mrow></mfrac></math>, 12% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>25</mn></mfrac></math><br>क्रय मूल्य : अंकित मूल्य : विक्रय मूल्य <br>&nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;31<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;25&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;22<br>----------------------------------------------- <br>&nbsp; &nbsp; 550&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;775&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;682 <br>क्रय मूल्य (550 इकाई) = 4400<br>अंकित मूल्य (775 इकाई) = <math display=\"inline\"><mfrac><mrow><mn>4400</mn></mrow><mrow><mn>550</mn></mrow></mfrac></math> &times; 775 = 8 &times; 775 = 6200</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. The price of a product increases by 10% in the first year and 20% in the second year. What is the overall percentage increase in the price of the product over the two years?</p>",
                    question_hi: "<p>3. एक उत्पाद के मूल्य में पहले वर्ष में 10% और दूसरे वर्ष में 20% की वृद्धि होती है। दो वर्षों में उत्पाद के मूल्य में कुल प्रतिशत वृद्धि कितनी है?</p>",
                    options_en: ["<p>28%</p>", "<p>32%</p>", 
                                "<p>36%</p>", "<p>30%</p>"],
                    options_hi: ["<p>28%</p>", "<p>32%</p>",
                                "<p>36%</p>", "<p>30%</p>"],
                    solution_en: "<p>3.(b)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Initial&nbsp; &nbsp;:&nbsp; &nbsp;final <br>1<sup>st</sup> year <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; :&nbsp; &nbsp; 11<br>2<sup>nd </sup>year <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp;5&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;6<br>--------------------------------------<br>Net increase <math display=\"inline\"><mo>&#8594;</mo></math> 50&nbsp; &nbsp; :&nbsp; &nbsp;66<br>% increase = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>50</mn></mrow></mfrac><mo>&#215;</mo></math>100 = 32 %</p>",
                    solution_hi: "<p>3.(b)<br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;प्रारंभिक&nbsp; &nbsp;:&nbsp; &nbsp; &nbsp;अंतिम <br>पहला साल <math display=\"inline\"><mo>&#8594;</mo></math> 10&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp;11<br>दूसरा साल <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;5&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; 6<br>------------------------------------------<br>शुद्ध वृद्धि <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 50&nbsp; &nbsp; &nbsp;:&nbsp; &nbsp; &nbsp; &nbsp;66<br>% वृद्धि = <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>50</mn></mrow></mfrac><mo>&#215;</mo></math>100 = 32 %</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4. Find the whole surface area of a cuboid having a length of 19 cm, breadth of 9 cm and height of 4.5 cm.</p>",
                    question_hi: "<p>4. एक घनाभ का संपूर्ण पृष्ठीय क्षेत्रफल ज्ञात करें, जिसकी लंबाई 19 cm, चौड़ाई 9 cm और ऊंचाई 4.5 cm है।</p>",
                    options_en: ["<p>497 cm&sup2;</p>", "<p>619 cm&sup2;</p>", 
                                "<p>594 cm&sup2;</p>", "<p>917 cm&sup2;</p>"],
                    options_hi: ["<p>497 cm&sup2;</p>", "<p>619 cm&sup2;</p>",
                                "<p>594 cm&sup2;</p>", "<p>917 cm&sup2;</p>"],
                    solution_en: "<p>4.(c)<br>Total surface area of cuboid = 2(<math display=\"inline\"><mi>l</mi><mi>b</mi></math> + bh + hl)<br>= 2(19 <math display=\"inline\"><mo>&#215;</mo></math> 9 + 9 &times; 4.5 + 4.5 &times;19)<br>= 2(171 + 40.5 + 85.5)<br>= 2 <math display=\"inline\"><mo>&#215;</mo></math> 297 = 594 cm<sup>2</sup></p>",
                    solution_hi: "<p>4.(c)<br>घनाभ का कुल प्रष्ठीय क्षेत्रफल = 2(<math display=\"inline\"><mi>l</mi><mi>b</mi><mo>+</mo><mi>b</mi><mi>h</mi><mo>+</mo><mi>h</mi><mi>l</mi></math>)<br>= 2(19 <math display=\"inline\"><mo>&#215;</mo></math> 9 + 9 &times; 4.5 + 4.5 &times; 19)<br>= 2(171 + 40.5 + 85.5)<br>= 2 <math display=\"inline\"><mo>&#215;</mo></math> 297 = 594 cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5. ∆ABC~∆EDF and area(∆ABC): area (∆EDF) = 1 : 4. If AB = 7 cm, BC = 8 cm and CA = 9 cm, then DF is equal to:</p>",
                    question_hi: "<p>5. ∆ABC~∆EDF और क्षेत्रफल (ABC): क्षेत्रफल (∆EDF) = 1 : 4 है। यदि AB = 7 cm, BC = 8 cm और CA = 9 cm है, तो DF बराबर होगाः</p>",
                    options_en: ["<p>14 cm</p>", "<p>12 cm</p>", 
                                "<p>18 cm</p>", "<p>16 cm</p>"],
                    options_hi: ["<p>14 cm</p>", "<p>12 cm</p>",
                                "<p>18 cm</p>", "<p>16 cm</p>"],
                    solution_en: "<p>5.(d)<br>According to the question,<br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>&#916;</mi><mi>E</mi><mi>D</mi><mi>F</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>B</mi><mi>C</mi><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mi>D</mi><mi>F</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>8</mn><mn>2</mn></msup><msup><mrow><mo>(</mo><mi>D</mi><mi>F</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>(</mo><mi>D</mi><msup><mrow><mi>F</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 64 &times; 4 = 256<br>DF = 16 cm</p>",
                    solution_hi: "<p>5.(d)<br>प्रश्न के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#160;</mi><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#160;</mi><mi>&#916;</mi><mi>E</mi><mi>D</mi><mi>F</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mrow><mo>(</mo><mi>B</mi><mi>C</mi><mo>)</mo></mrow><mn>2</mn></msup><msup><mrow><mo>(</mo><mi>D</mi><mi>F</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>1</mn><mi>&#160;</mi></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mn>8</mn><mn>2</mn></msup><msup><mrow><mo>(</mo><mi>D</mi><mi>F</mi><mo>)</mo></mrow><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mo>(</mo><mi>D</mi><msup><mrow><mi>F</mi><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></math> = 64 &times; 4 = 256<br>DF = 16 cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6. Study the given bar-graph and answer the question that follows.<br>The bar-graph shows the production of refrigerators (in thousand) by five different companies A, B, C, D and E during 2004 to 2007.<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330220801.png\" alt=\"rId6\" width=\"437\" height=\"265\"> <br>What is the difference between 60% of the average production of refrigerators by all the companies for the year 2005-2006 and 70% of the average of all the companies in 2006-2007?</p>",
                    question_hi: "<p>6. दिए गए बार ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br>बार ग्राफ में 2004 से 2007 के दौरान पाँच अलग-अलग कंपनियों A, B, C, D और E द्वारा रेफ्रिजरेटर के उत्पादन (हजार में) को दर्शाया गया है।<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330221026.png\" alt=\"rId7\" width=\"455\" height=\"275\"> <br>वर्ष 2005-2006 के दौरान सभी कंपनियों के रेफ्रिजरेटर के औसत उत्पादन के 60% और 2006-2007 के दौरान सभी कंपनियों के औसत उत्पादन के 70% के बीच का अंतर कितना है?</p>",
                    options_en: ["<p>400</p>", "<p>4</p>", 
                                "<p>4000</p>", "<p>40</p>"],
                    options_hi: ["<p>400</p>", "<p>4</p>",
                                "<p>4000</p>", "<p>40</p>"],
                    solution_en: "<p>6.(c)<br>Total production of all the companies for 2005 - 2006 = 250 + 340 + 370 + 490 + 430 = 1880 k<br>60 % of the average of all the companies for 2005 - 2006 = <math display=\"inline\"><mfrac><mrow><mn>1880</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 225.6 k<br>Total production of all the companies for 2005 - 2006 = 390 + 200 + 450 + 240 + 360 = 1640 k<br>70 % of the average of all the companies for 2006 - 2007 = <math display=\"inline\"><mfrac><mrow><mn>1640</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 229.6 k<br>Required difference = 229.6 - 225.6 = 4 k or 4000</p>",
                    solution_hi: "<p>6.(c)<br>2005 - 2006 के लिए सभी कंपनियों का कुल उत्पादन = 250 + 340 + 370 + 490 + 430 = 1880 k<br>2005 - 2006 के लिए सभी कंपनियों के औसत का 60% = <math display=\"inline\"><mfrac><mrow><mn>1880</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = 225.6 k<br>2005 - 2006 के लिए सभी कंपनियों का कुल उत्पादन = 390 + 200 + 450 + 240 + 360 = 1640 k<br>2006-2007 के लिए सभी कंपनियों के औसत का 70% = <math display=\"inline\"><mfrac><mrow><mn>1640</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>&#215;</mo><mfrac><mrow><mn>7</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math> = 229.6 k<br>आवश्यक अंतर = 229.6 - 225.6 = 4 k या 4000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. The value of 1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#247;</mo></math>{14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>- (2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>+ 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)}]</p>",
                    question_hi: "<p>7. 1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#247;</mo></math>{14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>- (2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>+ 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)}] का मान क्या है?</p>",
                    options_en: ["<p>5<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>", "<p>3 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>", 
                                "<p>4<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>", "<p>3 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p>5<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>", "<p>3 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>13</mn></mrow></mfrac></math></p>",
                                "<p>4<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>", "<p>3 <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>17</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>7.(a) <br>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#247;</mo></math>{14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>- (2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>+ 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac><mo>&#247;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>3</mn></mfrac></math> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>7</mn><mn>2</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac><mo>&#247;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>3</mn></mfrac></math>&nbsp;- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14</mn><mo>+</mo><mn>21</mn></mrow><mn>6</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac><mo>&#247;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>3</mn></mfrac></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>6</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac><mo>&#247;</mo></math> {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>86</mn><mo>-</mo><mn>35</mn></mrow><mn>6</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac><mo>&#247;</mo></math> {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>6</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>51</mn></mfrac></math>]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>13</mn></mfrac><mo>=</mo><mfrac><mn>68</mn><mn>13</mn></mfrac><mo>=</mo><mn>5</mn><mo>&#160;</mo><mfrac><mn>3</mn><mn>13</mn></mfrac></math></p>",
                    solution_hi: "<p>7.(a) <br>1<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac><mo>&#247;</mo></math>{14<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>- (2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math>+ 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac><mo>&#247;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>3</mn></mfrac></math> - (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac><mo>+</mo><mfrac><mn>7</mn><mn>2</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac><mo>&#247;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>3</mn></mfrac></math>&nbsp;- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>14</mn><mo>+</mo><mn>21</mn></mrow><mn>6</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac><mo>&#247;</mo></math>{<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>43</mn><mn>3</mn></mfrac></math>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>6</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac><mo>&#247;</mo></math> {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>86</mn><mo>-</mo><mn>35</mn></mrow><mn>6</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>6</mn></mfrac><mo>&#247;</mo></math> {<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>6</mn></mfrac></math>)}]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &divide; [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>51</mn></mfrac></math>]<br><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>51</mn><mn>13</mn></mfrac><mo>=</mo><mfrac><mn>68</mn><mn>13</mn></mfrac><mo>=</mo><mn>5</mn><mo>&#160;</mo><mfrac><mn>3</mn><mn>13</mn></mfrac></math></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. Study the following graph and answer the question that follows. The following bar graph shows the percentage profit earned by two companies A and B over the given years.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330221191.png\" alt=\"rId8\" width=\"426\" height=\"309\"> <br>The incomes of two companies A and B in 2006 were in the ratio 3 : 4, respectively. What was the respective ratio of their expenditures in 2006?</p>",
                    question_hi: "<p>8. निम्नलिखित आरेख का अध्ययन कीजिए और उसके नीचे दिए गए प्रश्न का उत्तर दीजिए। <br>निम्नलिखित दंड आरेख दिए गए वर्षों में दो कंपनियों A और B द्वारा अर्जित प्रतिशत लाभ को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330221321.png\" alt=\"rId9\" width=\"449\" height=\"328\"> <br>संदर्भ: Company = कंपनी, PROFIT % = लाभ % <br>2006 में दो कंपनियों A और B की आय क्रमशः 3 : 4 के अनुपात में थी। 2006 में उनके व्यय का क्रमशः अनुपात क्या था?</p>",
                    options_en: ["<p>15 : 22</p>", "<p>14 : 19</p>", 
                                "<p>7 : 22</p>", "<p>27 : 35</p>"],
                    options_hi: ["<p>15 : 22</p>", "<p>14 : 19</p>",
                                "<p>7 : 22</p>", "<p>27 : 35</p>"],
                    solution_en: "<p>8.(a)<br>Concept :- Income = profit + expenditure<br>and <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>&#160;</mo><mo>%</mo></mrow><mn>100</mn></mfrac></math> &times; Expenditure<br>Let e<math display=\"inline\"><mi>x</mi></math>penditure of company A = x<br>and expenditure of compan<math display=\"inline\"><mi>y</mi></math> B = y<br><math display=\"inline\"><mfrac><mrow><mi>I</mi><mi>n</mi><mi>c</mi><mi>o</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>m</mi><mi>p</mi><mi>a</mi><mi>n</mi><mi>y</mi><mi>&#160;</mi><mi>A</mi></mrow><mrow><mi>I</mi><mi>n</mi><mi>c</mi><mi>o</mi><mi>m</mi><mi>e</mi><mi>&#160;</mi><mi>o</mi><mi>f</mi><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>m</mi><mi>p</mi><mi>a</mi><mi>n</mi><mi>y</mi><mi>&#160;</mi><mi>B</mi></mrow></mfrac></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>e</mi><mi>x</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>t</mi><mi>u</mi><mi>r</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>A</mi><mo>+</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>A</mi></mrow><mrow><mi>E</mi><mi>x</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>t</mi><mi>u</mi><mi>r</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>B</mi><mo>+</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>B</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>A</mi></mrow><mrow><mi>E</mi><mi>x</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>d</mi><mi>i</mi><mi>t</mi><mi>u</mi><mi>r</mi><mi>e</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>B</mi><mo>+</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>&#160;</mo><mi>o</mi><mi>f</mi><mo>&#160;</mo><mi>B</mi></mrow></mfrac></math><br>.<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#160;</mo><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>65</mn><mn>100</mn></mfrac></mstyle><mo>&#215;</mo><mi>x</mi></mrow><mrow><mi>y</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>50</mn><mn>100</mn></mfrac></mstyle><mo>&#215;</mo><mi>y</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>65</mn><mo>&#215;</mo><mo>&#160;</mo><mi>x</mi></mrow><mrow><mi>y</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>50</mn><mo>&#215;</mo><mi>y</mi></mrow></mfrac></math><br>3<math display=\"inline\"><mi>y</mi></math> + 3 (0.50y) = 4x + 4(0.65x)<br>3<math display=\"inline\"><mi>y</mi></math> + 1.5 y = 4x + 2.6x<br>4.5<math display=\"inline\"><mi>y</mi></math> = 6.6x<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>6</mn><mo>.</mo><mn>6</mn></mrow></mfrac><mo>=</mo><mfrac><mn>15</mn><mn>22</mn></mfrac></math><br>Hence, <math display=\"inline\"><mi>x</mi><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mi>y</mi></math> = 15 : 22</p>",
                    solution_hi: "<p>8.(a)<br>अवधारणा :- आय = लाभ + व्यय<br>और लाभ = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2354;&#2366;&#2349;</mi><mo>&#160;</mo><mo>%</mo></mrow><mn>100</mn></mfrac><mo>&#215;</mo></math>व्यय<br>माना कि कंपनी A का व्यय = <math display=\"inline\"><mi>x</mi></math><br>और कंपनी का B व्यय = <math display=\"inline\"><mi>y</mi></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2306;&#2346;&#2344;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>A</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2310;&#2351;</mi><mo>&#160;</mo></mrow><mrow><mi>&#2325;&#2306;&#2346;&#2344;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>B</mi><mo>&#160;</mo><mi>&#2325;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2310;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2306;&#2346;&#2344;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>A</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2357;&#2381;&#2351;&#2351;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>&#2325;&#2306;&#2346;&#2344;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>A</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi></mrow><mrow><mi>&#2325;&#2306;&#2346;&#2344;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>B</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2357;&#2381;&#2351;&#2351;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2306;&#2346;&#2344;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>B</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>+</mo><mi>A</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi></mrow><mrow><mi>B</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2357;&#2381;&#2351;&#2351;</mi><mo>&#160;</mo><mo>+</mo><mi>B</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2354;&#2366;&#2349;</mi></mrow></mfrac></math><br>.<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#160;</mo><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>65</mn><mn>100</mn></mfrac></mstyle><mo>&#215;</mo><mi>x</mi></mrow><mrow><mi>y</mi><mo>+</mo><mstyle displaystyle=\"true\"><mfrac><mn>50</mn><mn>100</mn></mfrac></mstyle><mo>&#215;</mo><mi>y</mi></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>65</mn><mo>&#215;</mo><mo>&#160;</mo><mi>x</mi></mrow><mrow><mi>y</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>0</mn><mo>.</mo><mn>50</mn><mo>&#215;</mo><mi>y</mi></mrow></mfrac></math><br>3<math display=\"inline\"><mi>y</mi></math> + 3 (0.50y) = 4x + 4(0.65x)<br>3<math display=\"inline\"><mi>y</mi></math> + 1.5 y = 4x + 2.6x<br>4.5<math display=\"inline\"><mi>y</mi></math> = 6.6x<br><math display=\"inline\"><mfrac><mrow><mi>x</mi></mrow><mrow><mi>y</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>4</mn><mo>.</mo><mn>5</mn></mrow><mrow><mn>6</mn><mo>.</mo><mn>6</mn></mrow></mfrac><mo>=</mo><mfrac><mn>15</mn><mn>22</mn></mfrac></math><br>अतः, <math display=\"inline\"><mi>x</mi><mi>&#160;</mi><mo>:</mo><mi>&#160;</mi><mi>y</mi></math> = 15 : 22</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. Simplify <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>60</mn><mo>&#176;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>60</mn><mo>&#176;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math></p>",
                    question_hi: "<p>9. <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>60</mn><mo>&#176;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">n</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>60</mn><mo>&#176;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">s</mi><mi mathvariant=\"bold-italic\">&#160;</mi><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math> को सरल कीजिए।</p>",
                    options_en: ["<p>2 + <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - 2</p>", 
                                "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - 1</p>", "<p>2 - <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    options_hi: ["<p>2 + <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>", "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - 2</p>",
                                "<p><math display=\"inline\"><msqrt><mn>3</mn></msqrt></math> - 1</p>", "<p>2 - <math display=\"inline\"><msqrt><mn>3</mn></msqrt></math></p>"],
                    solution_en: "<p>9.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn><mo>-</mo><mn>1</mn></msqrt><msqrt><mn>3</mn><mo>-</mo><mn>1</mn></msqrt></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><msqrt><mn>3</mn></msqrt><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>4</mn><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac><mo>&#160;</mo><mo>=</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><msqrt><mn>3</mn></msqrt></math></p>",
                    solution_hi: "<p>9.(d)<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo></mrow><mrow><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>60</mn><mo>&#176;</mo></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><msqrt><mn>3</mn></msqrt></mrow><mrow><mn>2</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn><mo>-</mo><mn>1</mn></msqrt><msqrt><mn>3</mn><mo>-</mo><mn>1</mn></msqrt></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><msqrt><mn>3</mn></msqrt><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><msqrt><mn>3</mn></msqrt><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mo>(</mo><mn>1</mn><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn><mo>-</mo><mn>1</mn></mrow></mfrac></math> <br>= <math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mn>3</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>1</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>&#160;</mo><mn>4</mn><mo>-</mo><mn>2</mn><msqrt><mn>3</mn></msqrt></mrow><mn>2</mn></mfrac><mo>&#160;</mo><mo>=</mo><mn>2</mn><mo>&#160;</mo><mo>-</mo><msqrt><mn>3</mn></msqrt></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. The table shows the percentage distribution of the population (only male and female) according to Gender and Literacy.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330221517.png\" alt=\"rId10\" width=\"359\" height=\"140\"> <br>If state B has 7 crore people, then the number of illiterate females in the state is:</p>",
                    question_hi: "<p>10. दी गई तालिका लिंग और साक्षरता के अनुसार जनसंख्या (केवल पुरुष और महिला) का प्रतिशत बंटन दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330221632.png\" alt=\"rId11\" width=\"360\" height=\"132\"> <br>यदि राज्य B में 7 करोड़ लोग हैं, तो राज्य में निरक्षर महिलाओं की संख्या ज्ञात करें</p>",
                    options_en: ["<p>130.67 lakhs</p>", "<p>201.6 lakhs</p>", 
                                "<p>117.6 lakhs</p>", "<p>78.4 lakhs</p>"],
                    options_hi: ["<p>130.67 लाख</p>", "<p>201.6 लाख</p>",
                                "<p>117.6 लाख</p>", "<p>78.4 लाख</p>"],
                    solution_en: "<p>10.(d)<br>Total population of state B = 7 crore<br>illiterate population of state B = <math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo><mn>7</mn></math> crore<br>illiterate females of state B = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>100</mn></mfrac></math>&times; 7 crore<br>= 0.7840 crore<br>= 78.40 lakh</p>",
                    solution_hi: "<p>10.(d)<br>राज्य B की कुल जनसंख्या = 7 करोड़<br>राज्य B की निरक्षर जनसंख्या = <math display=\"inline\"><mfrac><mrow><mn>28</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo><mn>7</mn></math> करोड़<br>राज्य B की निरक्षर महिलाएँ = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>28</mn><mn>100</mn></mfrac></math>&times; 7 करोड़<br>= 0.7840 करोड़<br>= 78.40 लाख</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. Out of a class of 200 students, 50 students average marks is 40, and the remaining 150 students average is 60. Find the students weighted average.</p>",
                    question_hi: "<p>11. 200 छात्रों की एक कक्षा में, 50 छात्रों के औसत अंक 40 हैं, और शेष 150 छात्रों के औसत अंक 60 हैं। छात्रों का भारित औसत ज्ञात कीजिए।</p>",
                    options_en: ["<p>50</p>", "<p>200</p>", 
                                "<p>55</p>", "<p>150</p>"],
                    options_hi: ["<p>50</p>", "<p>200</p>",
                                "<p>55</p>", "<p>150</p>"],
                    solution_en: "<p>11.(c)<br>Average of 50 people = 40 marks<br>Average of remaining 150 people = 60 marks<br>Weighted average = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>40</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2000</mn><mo>+</mo><mn>9000</mn></mrow><mn>200</mn></mfrac><mo>=</mo><mfrac><mn>11000</mn><mn>200</mn></mfrac></math> = 55</p>",
                    solution_hi: "<p>11.(c)<br>50 व्यक्तियों का औसत = 40 अंक<br>शेष 150 व्यक्तियों का औसत = 60 अंक<br>भारित औसत = <math display=\"inline\"><mfrac><mrow><mn>50</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>40</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>60</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2000</mn><mo>+</mo><mn>9000</mn></mrow><mn>200</mn></mfrac><mo>=</mo><mfrac><mn>11000</mn><mn>200</mn></mfrac></math> = 55</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. In two successive years, 75 and 50 employees of a company appeared at the departmental examination. Respectively, 84% and 52% of them passed. The average rate of pass percentage is:</p>",
                    question_hi: "<p>12. दो क्रमिक वर्षों में एक कंपनी के 75 और 50 कर्मचारी विभागीय परीक्षा में उपस्थित हुए। उनमें से क्रमशः 84% और 52% उत्तीर्ण हुए। उत्तीर्ण प्रतिशत की औसत दर कितनी है?</p>",
                    options_en: ["<p>71%</p>", "<p>41%</p>", 
                                "<p>71<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>", "<p>41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>"],
                    options_hi: ["<p>71%</p>", "<p>41%</p>",
                                "<p>71<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>", "<p>41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>"],
                    solution_en: "<p>12.(c)<br>Average rate of pass % = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>84</mn></mrow><mrow><mn>1</mn><mn>00</mn></mrow></mfrac><mo>&#215;</mo><mi>&#160;</mi><mn>75</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>52</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo><mn>50</mn></mrow><mrow><mn>75</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>50</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>21</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>26</mn></mrow><mrow><mn>125</mn></mrow></mfrac><mo>&#215;</mo></math> 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>63</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>26</mn></mrow><mrow><mn>125</mn></mrow></mfrac><mo>&#215;</mo></math> 100<br>= <math display=\"inline\"><mfrac><mrow><mn>89</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>&#215;</mo></math> 4 = 71<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    solution_hi: "<p>12.(c)<br>उत्तीर्ण होने की औसत दर % = <math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>84</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo><mi>&#160;</mi><mn>75</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>52</mn></mrow><mrow><mn>100</mn></mrow></mfrac><mo>&#215;</mo><mn>50</mn></mrow><mrow><mn>75</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>50</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\"><mfrac><mrow><mn>21</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>26</mn></mrow><mrow><mn>125</mn></mrow></mfrac><mo>&#215;</mo></math> 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>63</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>26</mn></mrow><mrow><mn>125</mn></mrow></mfrac><mo>&#215;</mo></math> 100<br>= <math display=\"inline\"><mfrac><mrow><mn>89</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>&#215;</mo></math> 4 = 71<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. Find the mean proportion to 0.72 and 2.85 (round up to one decimal place).</p>",
                    question_hi: "<p>13. 0.72 और 2.85 का माध्यानुपाती ज्ञात कीजिए (दशमलव के एक स्थान तक सन्निकटित)।</p>",
                    options_en: ["<p>1.4</p>", "<p>0.7</p>", 
                                "<p>2.8</p>", "<p>0.4</p>"],
                    options_hi: ["<p>1.4</p>", "<p>0.7</p>",
                                "<p>2.8</p>", "<p>0.4</p>"],
                    solution_en: "<p>13.(a)<br>0.72 : <math display=\"inline\"><mi>x</mi></math> :: x : 2.85<br>Product of extreme term = product of mean term<br>0.72 <math display=\"inline\"><mo>&#215;</mo></math> 2.85 = x &times; x<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 2.052<br><math display=\"inline\"><mi>x</mi></math> = 1.43</p>",
                    solution_hi: "<p>13.(a)<br>0.72 : <math display=\"inline\"><mi>x</mi></math> :: x : 2.85<br>पहले और आखिरी पदों का गुणनफल = मध्य पदों का गुणनफल<br>0.72 <math display=\"inline\"><mo>&#215;</mo></math> 2.85 = x &times; x<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 2.052<br><math display=\"inline\"><mi>x</mi></math> = 1.43</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. Two circles of radii 3 units and r units, respectively, have a 6 units distance between their centers. If the length of the direct common tangent is <math display=\"inline\"><msqrt><mn>35</mn></msqrt></math> units, then the value of r is _____units.</p>",
                    question_hi: "<p>14. क्रमशः 3 इकाई और r इकाई त्रिज्या वाले दो वृत्तों के केंद्रों के बीच की दूरी 6 इकाई है। यदि सीधी उभयनिष्ठ स्पर्शरेखा की लंबाई<math display=\"inline\"><msqrt><mn>35</mn></msqrt></math> इकाई है, तो r का मान_____इकाई है।</p>",
                    options_en: ["<p>3</p>", "<p>4</p>", 
                                "<p>6</p>", "<p>5</p>"],
                    options_hi: ["<p>3</p>", "<p>4</p>",
                                "<p>6</p>", "<p>5</p>"],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330221775.png\" alt=\"rId12\" width=\"285\" height=\"144\"><br>Length of common tangent <math display=\"inline\"><mo>(</mo><mi>l</mi><mo>)</mo></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><mo>(</mo><msub><mi>r</mi><mn>1</mn></msub><mo>-</mo><msub><mi>r</mi><mn>2</mn></msub><mo>)</mo></msqrt></math><br><math display=\"inline\"><msqrt><mn>35</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>6</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>3</mn><mo>-</mo><msub><mi>r</mi><mn>2</mn></msub><mo>)</mo></msqrt></math><br>On squaring both side<br>35 = 36 - [(3)<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>r</mi><mn>2</mn></msub><mn>2</mn></msup></math> - 2 &times; 3 &times; r<sub>2</sub>]<br>1 = 9 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>r</mi><mn>2</mn></msub><mn>2</mn></msup></math> - 6r<sub>2</sub><br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math>2 - 6r<sub>2</sub> + 8 = 0<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>r</mi><mn>2</mn></msub><mn>2</mn></msup></math> - 4r<sub>2</sub> - 2r<sub>2</sub> + 8 = 0<br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> (r<sub>2 </sub>- 4) - 2(r<sub>2</sub> - 4) = 0<br>(<math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> - 4) (r<sub>2 </sub>- 2) = 0<br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> = 2 or 4<br>Hence option (b) satisfies the value.</p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330221775.png\" alt=\"rId12\" width=\"287\" height=\"145\"><br>उभयनिष्ठ स्पर्शरेखा की लंबाई <math display=\"inline\"><mo>(</mo><mi>l</mi><mo>)</mo></math>= <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi>d</mi><mn>2</mn></msup><mo>-</mo><mo>(</mo><msub><mi>r</mi><mn>1</mn></msub><mo>-</mo><msub><mi>r</mi><mn>2</mn></msub><mo>)</mo></msqrt></math><br><math display=\"inline\"><msqrt><mn>35</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>6</mn><mn>2</mn></msup><mo>-</mo><mo>(</mo><mn>3</mn><mo>-</mo><msub><mi>r</mi><mn>2</mn></msub><mo>)</mo></msqrt></math><br>दोनों तरफ वर्ग करने पर<br>35 = 36 - [(3)<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>r</mi><mn>2</mn></msub><mn>2</mn></msup></math> - 2 &times; 3 &times; r<sub>2</sub>]<br>1 = 9 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>r</mi><mn>2</mn></msub><mn>2</mn></msup></math> - 6r<sub>2</sub><br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math>2 - 6r<sub>2</sub> + 8 = 0<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><msub><mi>r</mi><mn>2</mn></msub><mn>2</mn></msup></math> - 4r<sub>2</sub> - 2r<sub>2</sub> + 8 = 0<br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> (r<sub>2 </sub>- 4) - 2(r<sub>2</sub> - 4) = 0<br>(<math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> - 4) (r<sub>2 </sub>- 2) = 0<br><math display=\"inline\"><msub><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msub></math> = 2 or 4<br>इसलिए विकल्प (b) मान को संतुष्ट करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "17",
                    question_en: "<p>15. In how many years will a sum of ₹4,250 yield a simple interest of ₹510 at a rate of 6% per annum?</p>",
                    question_hi: "<p>15. कितने वर्षों में ₹4,250 की धनराशि पर 6% वार्षिक दर से ₹510 का साधारण ब्याज मिलेगा?</p>",
                    options_en: ["<p>3 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> years</p>", "<p>3 years</p>", 
                                "<p>2 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> years</p>", "<p>2 years</p>"],
                    options_hi: ["<p>3 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष</p>", "<p>3 वर्ष</p>",
                                "<p>2 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> वर्ष</p>", "<p>2 वर्ष</p>"],
                    solution_en: "<p>15.(d)<br>S.I. = <math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>R</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> <br>510 = <math display=\"inline\"><mfrac><mrow><mn>4250</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>510</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>4250</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> = T<br>T = 2 years</p>",
                    solution_hi: "<p>15.(d)<br>साधारण ब्याज = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2350;&#2370;&#2354;&#2343;&#2344;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>&#2342;</mi><mi>&#2352;</mi><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi><mi>&#160;</mi></mrow><mn>100</mn></mfrac></math> <br>510 = <math display=\"inline\"><mfrac><mrow><mn>4250</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mi>T</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>510</mn><mi>&#160;</mi><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>100</mn></mrow><mrow><mn>4250</mn><mi>&#160;</mi><mo>&#215;</mo><mn>6</mn></mrow></mfrac></math> = T<br>T = 2 वर्ष</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "17",
                    question_en: "<p>16. Shantanu&rsquo;s income decreased from ₹42,800 to ₹37,236. Find the percentage decrease in his income.</p>",
                    question_hi: "<p>16. शांतनु की आय ₹42,800 से घटकर ₹37,236 हो गई। उसकी आय में प्रतिशत कमी ज्ञात कीजिए।</p>",
                    options_en: ["<p>13%</p>", "<p>11%</p>", 
                                "<p>14%</p>", "<p>12%</p>"],
                    options_hi: ["<p>13%</p>", "<p>11%</p>",
                                "<p>14%</p>", "<p>12%</p>"],
                    solution_en: "<p>16.(a) <br>Percentage decrease = <math display=\"inline\"><mfrac><mrow><mn>42800</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>37236</mn></mrow><mrow><mn>42800</mn><mi mathvariant=\"bold-italic\">&#160;</mi></mrow></mfrac><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>100</mn></math><br>= <math display=\"inline\"><mfrac><mrow><mn>5564</mn></mrow><mrow><mn>428</mn></mrow></mfrac></math> = 13 %</p>",
                    solution_hi: "<p>16.(a) <br>प्रतिशत कमी = <math display=\"inline\"><mfrac><mrow><mn>42800</mn><mi mathvariant=\"bold-italic\">&#160;</mi><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>37236</mn></mrow><mrow><mn>42800</mn><mi mathvariant=\"bold-italic\">&#160;</mi></mrow></mfrac><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mn>100</mn></math><br>= <math display=\"inline\"><mfrac><mrow><mn>5564</mn></mrow><mrow><mn>428</mn></mrow></mfrac></math> = 13 %</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "17",
                    question_en: "<p>17. The surface area of a spherical ball is 1386 cm<sup>2</sup> . What will be its volume?</p>",
                    question_hi: "<p>17. एक गोलाकार गेंद का पृष्ठीय क्षेत्रफल 1386 cm<sup>2</sup> है। इसका आयतन क्या होगा?</p>",
                    options_en: ["<p>4825 cm<sup>3</sup></p>", "<p>4851 cm<sup>3</sup></p>", 
                                "<p>5000 cm<sup>3</sup></p>", "<p>4800 cm<sup>3</sup></p>"],
                    options_hi: ["<p>4825 cm<sup>3</sup></p>", "<p>4851 cm<sup>3</sup></p>",
                                "<p>5000 cm<sup>3</sup></p>", "<p>4800 cm<sup>3</sup></p>"],
                    solution_en: "<p>17.(b) <br>Surface area of spherical ball = 1386 cm<sup>2</sup><br><math display=\"inline\"><mn>4</mn><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 1386<br><math display=\"inline\"><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1386</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>22</mn></mrow></mfrac></math>= 110.25<br><math display=\"inline\"><mi>r</mi></math> = 10.5 cm<br>Volume = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 10.5 &times; 10.5 &times; 10.5<br>= 4851 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>",
                    solution_hi: "<p>17.(b) <br>गोलाकार गेंद का पृष्ठीय क्षेत्रफल = 1386 cm<sup>2</sup><br><math display=\"inline\"><mn>4</mn><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = 1386<br><math display=\"inline\"><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup></math> =<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1386</mn><mo>&#215;</mo><mn>7</mn></mrow><mrow><mn>4</mn><mo>&#215;</mo><mn>22</mn></mrow></mfrac></math>= 110.25<br><math display=\"inline\"><mi>r</mi></math> = 10.5 cm<br>आयतन = <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>3</mn></mrow></msup></math><br>= <math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&times; 10.5 &times; 10.5 &times; 10.5<br>= 4851 <math display=\"inline\"><msup><mrow><mi>c</mi><mi>m</mi></mrow><mrow><mn>3</mn></mrow></msup></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "17",
                    question_en: "<p>18. If the speed of a boat in still water is 3 km/h and if its speed upstream is 1 km/h, find the speed of the stream.</p>",
                    question_hi: "<p>18. यदि स्थिर जल में एक नाव की चाल 3 km/h है और यदि धारा के प्रतिकूल इसकी चाल 1 km/h है, तो धारा की चाल ज्ञात कीजिए।</p>",
                    options_en: ["<p>0.25 km/h</p>", "<p>1 km/h</p>", 
                                "<p>2 km/h</p>", "<p>0.5 km/h</p>"],
                    options_hi: ["<p>0.25 km/h</p>", "<p>1 km/h</p>",
                                "<p>2 km/h</p>", "<p>0.5 km/h</p>"],
                    solution_en: "<p>18.(c) <br>Speed of boat in still water (<math display=\"inline\"><mi>x</mi></math>) = 3 km/h<br>Let speed of stream = <math display=\"inline\"><mi>y</mi></math> km/h<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>y</mi><mo>=</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>y</mi><mo>=</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mi>&#160;</mi><mi>y</mi><mo>=</mo><mi>&#160;</mi><mo>-</mo><mn>2</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mi>k</mi><mi>m</mi><mo>/</mo><mi>h</mi></math></p>",
                    solution_hi: "<p>18.(c) <br>स्थिर जल में एक नाव की चाल (<math display=\"inline\"><mi>x</mi></math>) = 3 किमी/घंटा<br>माना धारा की की चाल = <math display=\"inline\"><mi>y</mi></math> किमी/घंटा<br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>x</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>y</mi><mo>=</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>y</mi><mo>=</mo><mn>1</mn></math><br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>-</mo><mi>&#160;</mi><mi>y</mi><mo>=</mo><mi>&#160;</mi><mo>-</mo><mn>2</mn></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>y</mi><mi>&#160;</mi><mo>=</mo><mi>&#160;</mi><mn>2</mn></math> किमी/घंटा</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "17",
                    question_en: "<p>19. The following graph shows the wheat imports (in thousand tonnes) over the five years:<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330221901.png\" alt=\"rId13\" width=\"399\" height=\"285\"> <br>What is the ratio of the years which have below-average imports to those which have above-average imports?</p>",
                    question_hi: "<p>19. निम्न ग्राफ़ पाँच वर्षों में होने वाले गेहूँ के आयात (हज़ार टन में) को दर्शाता है:<br><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330222014.png\" alt=\"rId14\" width=\"404\" height=\"243\"> <br>Wheat imports (in thousand tonnes) : गेहूँ आयात (हजार टन में)<br>औसत से कम आयात वाले वर्षों और औसत से अधिक आयात वाले वर्षों के बीच अनुपात कितना है?</p>",
                    options_en: ["<p>3 : 2</p>", "<p>3 : 1</p>", 
                                "<p>2 : 3</p>", "<p>1 : 3</p>"],
                    options_hi: ["<p>3 : 2</p>", "<p>3 : 1</p>",
                                "<p>2 : 3</p>", "<p>1 : 3</p>"],
                    solution_en: "<p>19.(c) <br>Average import = <math display=\"inline\"><mfrac><mrow><mn>36</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>32</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>34</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>37</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>35</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>174</mn><mn>5</mn></mfrac></math> = 34.8<br>Years have below average import = 2019, 2020<br>Years have above average imports = 2018, 2021, 2022<br>Required ratio = 2 : 3</p>",
                    solution_hi: "<p>19.(c) <br>औसत आयात= <math display=\"inline\"><mfrac><mrow><mn>36</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>32</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>34</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>37</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>35</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>174</mn><mn>5</mn></mfrac></math> = 34.8<br>औसत से कम आयात वाले वर्ष = 2019, 2020<br>औसत से अधिक आयात वाले वर्ष = 2018, 2021, 2022<br>अभीष्ट अनुपात = 2 : 3</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "17",
                    question_en: "<p>20. If 640 &divide; 8 + 930 &divide; 15 &ndash; k + 32 &times; 5 = 1104 &divide; 16 &times; 148 &divide; 37, then the value of k is:</p>",
                    question_hi: "<p>20. यदि 640 &divide; 8 + 930 &divide; 15 &ndash; k + 32 &times; 5 = 1104 &divide; 16 &times; 148 &divide; 37 है, तो k का मान क्या है?</p>",
                    options_en: ["<p>276</p>", "<p>26</p>", 
                                "<p>35</p>", "<p>302</p>"],
                    options_hi: ["<p>276</p>", "<p>26</p>",
                                "<p>35</p>", "<p>302</p>"],
                    solution_en: "<p>20.(b) <br>640 &divide; 8 + 930 &divide; 15 &ndash; k + 32 &times; 5 = 1104 &divide; 16 &times; 148 &divide; 37<br><math display=\"inline\"><mo>&#8658;</mo></math> 80 + 62 - k + 160 = 69 &times; 4<br><math display=\"inline\"><mo>&#8658;</mo></math> 80 + 62 - k + 160 = 276<br><math display=\"inline\"><mo>&#8658;</mo></math> 302 - k = 276<br><math display=\"inline\"><mo>&#8658;</mo></math>-k = - 26<br><math display=\"inline\"><mo>&#8658;</mo></math> k = 26</p>",
                    solution_hi: "<p>20.(b) <br>640 &divide; 8 + 930 &divide; 15 &ndash; k + 32 &times; 5 = 1104 &divide; 16 &times; 148 &divide; 37<br><math display=\"inline\"><mo>&#8658;</mo></math> 80 + 62 - k + 160 = 69 &times; 4<br><math display=\"inline\"><mo>&#8658;</mo></math> 80 + 62 - k + 160 = 276<br><math display=\"inline\"><mo>&#8658;</mo></math> 302 - k = 276<br><math display=\"inline\"><mo>&#8658;</mo></math>-k = - 26<br><math display=\"inline\"><mo>&#8658;</mo></math> k = 26</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "17",
                    question_en: "<p>21. A shopkeeper sold an article at a 26% profit. On selling it for ₹2,250 more, he would get a profit of 41%. If this article is sold at 12% profit, then the selling price would be:</p>",
                    question_hi: "<p>21. एक दुकानदार एक वस्तु 26% के लाभ पर बेचता है। इसे ₹2,250 अधिक में बेचने पर उसे 41% का लाभ मिलेगा। यदि यह वस्तु 12% लाभ पर बेची जाती है, तो विक्रय मूल्य कितना है?</p>",
                    options_en: ["<p>₹16,120</p>", "<p>₹15,000</p>", 
                                "<p>₹16,800</p>", "<p>₹15,800</p>"],
                    options_hi: ["<p>₹16,120</p>", "<p>₹15,000</p>",
                                "<p>₹16,800</p>", "<p>₹15,800</p>"],
                    solution_en: "<p>21.(c) <br>Difference between profit = 2250<br>(41 - 26) % = 2250<br>15 % = 2250<br>C.P. (100 %) = <math display=\"inline\"><mfrac><mrow><mn>2250</mn></mrow><mrow><mn>15</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 15000<br>Required Selling Price = <math display=\"inline\"><mn>15000</mn><mo>&#215;</mo><mfrac><mrow><mn>112</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>= 16,800</p>",
                    solution_hi: "<p>21.(c) <br>लाभ का अंतर = 2250<br>(41 - 26) % = 2250<br>15 % = 2250<br>क्रय मूल्य (100 %) = <math display=\"inline\"><mfrac><mrow><mn>2250</mn></mrow><mrow><mn>15</mn></mrow></mfrac><mo>&#215;</mo><mn>100</mn></math> = 15000<br>आवश्यक विक्रय मूल्य = <math display=\"inline\"><mn>15000</mn><mo>&#215;</mo><mfrac><mrow><mn>112</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math><br>= 16,800</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "17",
                    question_en: "<p>22. There are two circles touching each other externally. The radius of the first circle with centre O is 12 cm. The radius of the second circle with centre A is 5 cm. Find the length of their common tangent touching the two circles at points P and Q .</p>",
                    question_hi: "<p>22. दो वृत्त हैं जो एक दूसरे को बाह्य रूप से स्पर्श करते हैं। केंद्र O वाले पहले वृत्त की त्रिज्या 12 cm है। केंद्र A वाले दूसरे वृत्त की त्रिज्या 5 cm है। दोनों वृत्तों को बिंदु P और Q पर स्पर्श करने वाली उनकी उभयनिष्ठ स्पर्श रेखा की लंबाई ज्ञात कीजिए।</p>",
                    options_en: ["<p>5<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>", "<p>14<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> cm</p>", 
                                "<p>4 <math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> cm</p>", "<p>5<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math> cm</p>"],
                    options_hi: ["<p>5<math display=\"inline\"><msqrt><mn>41</mn></msqrt></math> cm</p>", "<p>14<math display=\"inline\"><msqrt><mn>5</mn></msqrt></math> cm</p>",
                                "<p>4 <math display=\"inline\"><msqrt><mn>15</mn></msqrt></math> cm</p>", "<p>5<math display=\"inline\"><msqrt><mn>14</mn></msqrt></math> cm</p>"],
                    solution_en: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330222125.png\" alt=\"rId15\" width=\"215\" height=\"139\"><br>Length of common tangent (<math display=\"inline\"><mi>l</mi></math>) = 2 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msub><mi>r</mi><mn>1</mn></msub><msub><mi>r</mi><mn>2</mn></msub></msqrt></math><br>= <math display=\"inline\"><mn>2</mn><msqrt><mn>12</mn><mo>&#215;</mo><mn>5</mn></msqrt></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>22.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330222125.png\" alt=\"rId15\" width=\"215\" height=\"139\"><br>उभयनिष्ठ स्पर्शरेखा की लंबाई (<math display=\"inline\"><mi>l</mi></math>) = 2 <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msub><mi>r</mi><mn>1</mn></msub><msub><mi>r</mi><mn>2</mn></msub></msqrt></math><br>= <math display=\"inline\"><mn>2</mn><msqrt><mn>12</mn><mo>&#215;</mo><mn>5</mn></msqrt></math> = 4<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>15</mn></msqrt></math> cm</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "17",
                    question_en: "<p>23. Let C be a circle with center O and PQ be the diameter of C. Let AB be a chord on C. If &ang;QOB = 40&deg;, &ang;AOP = 80&deg;, then find &ang;AOB.</p>",
                    question_hi: "<p>23. माना C एक वृत्त है, जिसका केंद्र O है और PQ, C का व्यास है। माना AB, C पर एक जीवा है। यदि &ang;QOB = 40&deg;, &ang;AOP = 80&deg;, तो &ang;AOB ज्ञात कीजिए।</p>",
                    options_en: ["<p>70&deg;</p>", "<p>90&deg;</p>", 
                                "<p>100&deg;</p>", "<p>60&deg;</p>"],
                    options_hi: ["<p>70&deg;</p>", "<p>90&deg;</p>",
                                "<p>100&deg;</p>", "<p>60&deg;</p>"],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330222275.png\" alt=\"rId16\" width=\"191\" height=\"167\"><br><math display=\"inline\"><mi>&#8736;</mi><mi>P</mi><mi>O</mi><mi>A</mi></math> + &ang;AOB + &ang;QOB = 180&deg; <br><math display=\"inline\"><mn>8</mn><msup><mrow><mn>0</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> + &ang;AOB + 40&deg; = 180&deg;<br><math display=\"inline\"><mi>&#8736;</mi><mi>A</mi><mi>O</mi><mi>B</mi></math> = 60&deg;</p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330222275.png\" alt=\"rId16\" width=\"191\" height=\"167\"><br><math display=\"inline\"><mi>&#8736;</mi><mi>P</mi><mi>O</mi><mi>A</mi></math> + &ang;AOB + &ang;QOB = 180&deg; <br><math display=\"inline\"><mn>8</mn><msup><mrow><mn>0</mn></mrow><mrow><mo>&#176;</mo></mrow></msup></math> + &ang;AOB + 40&deg; = 180&deg;<br><math display=\"inline\"><mi>&#8736;</mi><mi>A</mi><mi>O</mi><mi>B</mi></math> = 60&deg;</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "17",
                    question_en: "<p>24. Teena does half of a task in 6 days. Later, with Shyam&rsquo;s help, she completes the remaining work in 4 days. Shyam alone can finish the entire work in:</p>",
                    question_hi: "<p>24. टीना एक काम का आधा भाग 6 दिनों में पूरा करती है। बाद में, श्याम की मदद से, वह शेष काम को 4 दिनों में पूरा करती है। श्याम अकेले संपूर्ण काम कितने दिनों में पूरा कर सकता है?</p>",
                    options_en: ["<p>12 days</p>", "<p>18 days</p>", 
                                "<p>21 days</p>", "<p>24 days</p>"],
                    options_hi: ["<p>12 दिन</p>", "<p>18 दिन</p>",
                                "<p>21 दिन</p>", "<p>24 दिन</p>"],
                    solution_en: "<p>24.(d)<br>Time taken by teena to complete the work = 12 days<br>Time taken by teena and shyama to complete the work = 8 days<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330222408.png\" alt=\"rId17\" width=\"259\" height=\"167\"><br>Efficiency of shyama = 3 - 2 = 1 unit<br>Time taken by shyama alone = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 days</p>",
                    solution_hi: "<p>24.(d)<br>टीना द्वारा कार्य पूरा करने में लिया गया समय = 12 दिन<br>टीना और श्यामा द्वारा कार्य पूरा करने में लिया गया समय = 8 दिन<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1734330222517.png\" alt=\"rId18\" width=\"241\" height=\"164\"><br>श्यामा की दक्षता = 3 - 2 = 1 इकाई<br>अकेले श्यामा द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 24 दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. △ABC and △DEF are similar triangles, and their areas are 81 cm&sup2; and 144 cm&sup2;, respectively. If EF = 20 cm, then what is the value of BC?</p>",
                    question_hi: "<p>25. △ABC और △DEF समरूप त्रिभुज हैं और उनका क्षेत्रफल क्रमशः 81 cm&sup2; और 144 cm&sup2; है। यदि EF = 20 cm है, तो BC का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>9 cm</p>", "<p>15 cm</p>", 
                                "<p>13 cm</p>", "<p>12 cm</p>"],
                    options_hi: ["<p>9 cm</p>", "<p>15 cm</p>",
                                "<p>13 cm</p>", "<p>12 cm</p>"],
                    solution_en: "<p>25.(b) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi><mo>~</mo><mi>&#160;</mi><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi></math><br><math display=\"inline\"><mfrac><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi></mrow><mrow><mi>a</mi><mi>r</mi><mi>e</mi><mi>a</mi><mi>&#160;</mi><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></mrow><mrow><mi>E</mi><msup><mi>F</mi><mn>2</mn></msup></mrow></mfrac></math> <br><math display=\"inline\"><mfrac><mrow><mn>81</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></mrow><msup><mn>20</mn><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mi>B</mi><msup><mrow><mi>C</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>81</mn><mo>&#215;</mo><mn>400</mn></mrow><mn>144</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>81</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>400</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math><br>= 225<br>hence, BC = 15 cm</p>",
                    solution_hi: "<p>25.(b) <br><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#916;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mi>&#160;</mi><mo>~</mo><mi>&#160;</mi><mi>&#916;</mi><mi>D</mi><mi>E</mi><mi>F</mi></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#916;</mi><mi>&#160;</mi><mi>A</mi><mi>B</mi><mi>C</mi><mo>&#160;</mo><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow><mrow><mi>&#916;</mi><mi>&#160;</mi><mi>D</mi><mi>E</mi><mi>F</mi><mi>&#160;</mi><mi>&#2325;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2325;&#2381;&#2359;&#2375;&#2340;&#2381;&#2352;&#2347;&#2354;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></mrow><mrow><mi>E</mi><msup><mi>F</mi><mn>2</mn></msup></mrow></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>81</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>B</mi><msup><mi>C</mi><mn>2</mn></msup></mrow><msup><mn>20</mn><mn>2</mn></msup></mfrac></math><br><math display=\"inline\"><mi>B</mi><msup><mrow><mi>C</mi></mrow><mrow><mn>2</mn></mrow></msup></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>81</mn><mo>&#215;</mo><mn>400</mn></mrow><mn>144</mn></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>81</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>400</mn></mrow><mrow><mn>144</mn></mrow></mfrac></math><br>= 225<br>अत:, BC = 15 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>