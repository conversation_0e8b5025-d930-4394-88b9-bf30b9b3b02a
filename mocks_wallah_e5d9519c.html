<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">15:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 15 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "17",
                    question_en: "<p>1. <span style=\"font-family: Cambria Math;\">A shopkeeper marks his goods at 80% more than their cost price and allows a discount of 48% on the </span><span style=\"font-family: Cambria Math;\">marked</span><span style=\"font-family: Cambria Math;\"> price. His loss percentage is:</span></p>\n",
                    question_hi: "<p>1. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 80% </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 48% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mn>2</mn><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>%</mo></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7</mn><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>%</mo></math></p>\n", 
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>2</mn><mn>5</mn></mfrac><mo>%</mo></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>%</mo></math></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&nbsp;</mo><mn>2</mn><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>%</mo></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>7</mn><mfrac><mn>3</mn><mn>5</mn></mfrac><mo>%</mo></math></p>\n",
                                "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>6</mn><mfrac><mn>2</mn><mn>5</mn></mfrac><mo>%</mo></math></p>\n", "<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>3</mn><mfrac><mn>1</mn><mn>5</mn></mfrac><mo>%</mo></math></p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the cost price be 100 Rs. </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then marked price = 180 Rs.</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>A</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>S</mi><mi>e</mi><mi>l</mi><mi>l</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>p</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>180</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>52</mn><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>93</mn><mo>.</mo><mn>6</mn><mo>&nbsp;</mo><mi>R</mi><mi>s</mi><mo>.</mo><mspace linebreak=\"newline\"></mspace><mi>R</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>i</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>L</mi><mi>o</mi><mi>s</mi><mi>s</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>-</mo><mn>93</mn><mo>.</mo><mn>6</mn></mrow><mn>100</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>4</mn><mo>%</mo><mo>&nbsp;</mo><mi>o</mi><mi>r</mi><mo>&nbsp;</mo><mn>6</mn><mfrac><mn>2</mn><mn>5</mn></mfrac><mo>%</mo><mspace linebreak=\"newline\"></mspace><mi mathvariant=\"bold-italic\">S</mi><mi mathvariant=\"bold-italic\">h</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">t</mi><mo mathvariant=\"bold\">&nbsp;</mo><mi mathvariant=\"bold-italic\">T</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">k</mi><mi mathvariant=\"bold-italic\">s</mi><mo mathvariant=\"bold\">&nbsp;</mo><mo mathvariant=\"bold\">:</mo><mo mathvariant=\"bold\">-</mo><mo mathvariant=\"bold\">&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>R</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>i</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>80</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>48</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>48</mn></mrow><mn>100</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>32</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>38</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>6</mn><mo>.</mo><mn>4</mn><mo>%</mo><mo>&nbsp;</mo><mi>o</mi><mi>r</mi><mo>&nbsp;</mo><mo>-</mo><mn>6</mn><mfrac><mn>2</mn><mn>5</mn></mfrac><mo>%</mo><mspace linebreak=\"newline\"></mspace><mo>-</mo><mi>v</mi><mi>e</mi><mo>&nbsp;</mo><mi>s</mi><mi>h</mi><mi>o</mi><mi>w</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>L</mi><mi>o</mi><mi>s</mi><mi>s</mi><mo>&nbsp;</mo><mi>h</mi><mi>a</mi><mi>p</mi><mi>p</mi><mi>e</mi><mi>n</mi><mi>s</mi><mo>.</mo></math></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">1.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> 100 </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 180 </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;</span><span style=\"font-family: Cambria Math;\">.</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>180</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>52</mn><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>93</mn><mo>.</mo><mn>6</mn><mo>&nbsp;</mo><mi>R</mi><mi>s</mi><mo>.</mo><mspace linebreak=\"newline\"></mspace><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mi>&#2361;&#2366;&#2344;&#2367;</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>-</mo><mn>93</mn><mo>.</mo><mn>6</mn></mrow><mn>100</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>4</mn><mo>%</mo><mo>&nbsp;</mo><mi>o</mi><mi>r</mi><mo>&nbsp;</mo><mn>6</mn><mfrac><mn>2</mn><mn>5</mn></mfrac><mo>%</mo><mspace linebreak=\"newline\"></mspace><mi mathvariant=\"bold-italic\">S</mi><mi mathvariant=\"bold-italic\">h</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">t</mi><mo mathvariant=\"bold\">&nbsp;</mo><mi mathvariant=\"bold-italic\">T</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">k</mi><mi mathvariant=\"bold-italic\">s</mi><mo mathvariant=\"bold\">&nbsp;</mo><mo mathvariant=\"bold\">:</mo><mo mathvariant=\"bold\">-</mo><mo mathvariant=\"bold\">&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mn>80</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>48</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mn>48</mn></mrow><mn>100</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>32</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>38</mn><mo>.</mo><mn>4</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mo>-</mo><mn>6</mn><mo>.</mo><mn>4</mn><mo>%</mo><mo>&nbsp;</mo><mi>o</mi><mi>r</mi><mo>&nbsp;</mo><mo>-</mo><mn>6</mn><mfrac><mn>2</mn><mn>5</mn></mfrac><mo>%</mo><mspace linebreak=\"newline\"></mspace><mo>-</mo><mi>v</mi><mi>e</mi><mo>&nbsp;</mo><mi>&#2330;&#2367;&#2344;&#2381;&#2361;</mi><mo>&nbsp;</mo><mi>&#2344;&#2369;&#2325;&#2360;&#2366;&#2344;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mi>&#2325;&#2379;</mi><mo>&nbsp;</mo><mi>&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</mi><mo>&nbsp;</mo><mi>&#2361;&#2376;&#2404;</mi><mo>&nbsp;</mo></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "17",
                    question_en: "<p>2. <span style=\"font-family: Cambria Math;\">A dishonest shopkeeper professes to sell grains at the cost price, but he uses a weight of 920 gm for 1 kg weight. Find his gain percentage (round up to two decimal places).</span></p>\n",
                    question_hi: "<p>2. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2312;&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2366;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2366;&#2357;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> 1 kg </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2335;</span><span style=\"font-family: Cambria Math;\"> (weight) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 920 gm </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2346;&#2351;&#2379;&#2327;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2344;&#2381;&#2344;&#2367;&#2325;&#2335;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>7.90%</p>\n", "<p>8.70%</p>\n", 
                                "<p>8.50%</p>\n", "<p>7.80%</p>\n"],
                    options_hi: ["<p>7.90%</p>\n", "<p>8.70%</p>\n",
                                "<p>8.50%</p>\n", "<p>7.80%</p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>R</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>i</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>p</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>1000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>920</mn></mrow><mn>920</mn></mfrac><mo>&times;</mo><mn>100</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>10</mn></mrow><mn>92</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mo>.</mo><mn>70</mn><mo>%</mo></math></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">2.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math style=\"font-family: Verdana;\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mi>&#2354;&#2366;&#2349;</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>1000</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>920</mn></mrow><mn>920</mn></mfrac><mo>&times;</mo><mn>100</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>80</mn><mo>&nbsp;</mo><mo>&times;</mo><mn>10</mn></mrow><mn>92</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mo>.</mo><mn>70</mn><mo>%</mo></math></p>\r\n<p>&nbsp;</p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "17",
                    question_en: "<p>3. <span style=\"font-family: Cambria Math;\">In a showroom, the marked prices of various items are displayed in such a manner that after giving a 10% discount to the customers, the owner of the show room earns a profit of 20%. What is the ratio of the marked price and cost price of each item?</span></p>\n",
                    question_hi: "<p>3. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2349;&#2367;&#2344;&#2381;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2352;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2342;&#2352;&#2381;&#2358;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2352;&#2366;&#2361;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2381;&#2351;&#2375;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>3:4</p>\n", "<p>4:3</p>\n", 
                                "<p>5:2</p>\n", "<p>2:5</p>\n"],
                    options_hi: ["<p>3:4</p>\n", "<p>4:3</p>\n",
                                "<p>5:2</p>\n", "<p>2:5</p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>M</mi><mi>a</mi><mi>r</mi><mi>k</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>p</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>t</mi><mo>&nbsp;</mo><mi>p</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>+</mo><mn>20</mn><mo>%</mo></mrow><mrow><mn>100</mn><mo>-</mo><mn>10</mn><mo>%</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mi>M</mi><mi>a</mi><mi>r</mi><mi>k</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>p</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi></mrow><mrow><mi>C</mi><mi>o</mi><mi>s</mi><mi>t</mi><mo>&nbsp;</mo><mi>p</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></mfrac></math></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">3.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>+</mo><mn>20</mn><mo>%</mo></mrow><mrow><mn>100</mn><mo>-</mo><mn>10</mn><mo>%</mo></mrow></mfrac><mspace linebreak=\"newline\"></mspace><mfrac><mrow><mi>&#2309;&#2306;&#2325;&#2367;&#2340;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow><mrow><mi>&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>4</mn><mrow><mn>3</mn><mo>&nbsp;</mo></mrow></mfrac></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "17",
                    question_en: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">A man buys a toy car for &#8377;225 and sells it for &#8377;210. Find his loss percentage (round up to two decimal places).</span></p>\n",
                    question_hi: "<p>4.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2350;&#2368;</span><span style=\"font-family: Cambria Math;\"> &#8377;225 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2367;&#2354;&#2380;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;210 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Nirmala UI;\">&#2342;&#2358;&#2350;&#2354;&#2357;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2381;&#2341;&#2366;&#2344;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2344;&#2381;&#2344;&#2361;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">)</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>4.44%</p>\n", "<p><span style=\"font-family: Cambria Math;\">93.33%</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">3.33%</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">6.67% </span></p>\n"],
                    options_hi: ["<p>4.44%</p>\n", "<p><span style=\"font-family: Cambria Math;\">93.33%</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">3.33%</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">6.67% </span></p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>L</mi><mi>o</mi><mi>s</mi><mi>s</mi><mo>%</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>225</mn><mo>-</mo><mn>210</mn></mrow><mn>225</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>15</mn><mn>225</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>100</mn><mn>15</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>67</mn><mo>%</mo><mo>&nbsp;</mo></math></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">4.(</span><span style=\"font-family: Cambria Math;\">d)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2361;&#2366;&#2344;&#2367;</mi><mo>%</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>225</mn><mo>-</mo><mn>210</mn></mrow><mn>225</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>15</mn><mn>225</mn></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>100</mn><mn>15</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>6</mn><mo>.</mo><mn>67</mn><mo>%</mo><mo>&nbsp;</mo></math></p>\r\n<p><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "17",
                    question_en: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">A publisher supplies books to a book-seller at 20% discount on the list price of &#8377;1,574.80. If the book-seller sells books at the list price and if his expenses for freight etc. are &#8377;45.40. Determine his profit.</span></p>\n",
                    question_hi: "<p>5.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2325;&#2366;&#2358;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,574.80 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2349;&#2366;&#2396;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2310;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2351;</span><span style=\"font-family: Cambria Math;\"> &#8377;45.40 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>&#8377;285.34</p>\n", "<p><span style=\"font-family: Cambria Math;\">&#8377;269.56</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">&#8377;273.50</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#8377;282.22</span></p>\n"],
                    options_hi: ["<p>&#8377;285.34</p>\n", "<p><span style=\"font-family: Cambria Math;\">&#8377;269.56</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">&#8377;273.50</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">&#8377;282.22</span></p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cost price for book-seller = 1574.80 &times;80</span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> 1259.84 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Selling price of book = 1574.80 - </span><span style=\"font-family: Cambria Math;\">45.40 =</span><span style=\"font-family: Cambria Math;\"> 1529.40 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required profit = 1529.40 - </span><span style=\"font-family: Cambria Math;\">1259.84 = 269.56 Rs.</span></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">5.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\">-</span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 1574.80 &times; 80% = 1259.84 Rs.</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2346;&#2369;&#2360;&#2381;&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 1574.80 - </span><span style=\"font-family: Cambria Math;\">45.40 =</span><span style=\"font-family: Cambria Math;\"> 1529.40 Rs.</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> = 1529.40 - </span><span style=\"font-family: Cambria Math;\">1259.84 = 269.56 Rs.</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "17",
                    question_en: "<p>6.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">A laptop is marked at a price 20% above its cost price. At what discount percentage should it be Sold to make a 5% profit?</span></p>\n",
                    question_hi: "<p>6.<span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> 5% </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>11.5%</p>\n", "<p><span style=\"font-family: Cambria Math;\">12.5%</span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\">13.5%</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">10.5%</span></p>\n"],
                    options_hi: ["<p>11.5%</p>\n", "<p><span style=\"font-family: Cambria Math;\">12.5%</span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\">13.5%</span></p>\n", "<p><span style=\"font-family: Cambria Math;\">10.5%</span></p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the cost price of the laptop = 100 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then marked price = 120 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">And selling price = 105 Rs.</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>R</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>i</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>d</mi><mi>i</mi><mi>s</mi><mi>c</mi><mi>o</mi><mi>u</mi><mi>n</mi><mi>t</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>120</mn><mo>-</mo><mn>105</mn></mrow><mn>120</mn></mfrac><mo>&times;</mo><mn>100</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>15</mn><mo>&times;</mo><mn>5</mn></mrow><mn>6</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>%</mo></math></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">6.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2376;&#2346;&#2335;&#2377;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 100 Rs.</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 120 Rs.</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 105 Rs.</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mi>&#2331;&#2370;&#2335;</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>120</mn><mo>-</mo><mn>105</mn></mrow><mn>120</mn></mfrac><mo>&times;</mo><mn>100</mn><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>15</mn><mo>&times;</mo><mn>5</mn></mrow><mn>6</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>12</mn><mo>.</mo><mn>5</mn><mo>%</mo></math></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "17",
                    question_en: "<p>7. <span style=\"font-family: Cambria Math;\">Manoj marked his goods 30% above the Cost Price and gives a 12% to the discounts to the customers. The profit or loss percentage </span><span style=\"font-family: Cambria Math;\">is :</span></p>\n",
                    question_hi: "<p>7. <span style=\"font-family: Nirmala UI;\">&#2350;&#2344;&#2379;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;&#2323;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2352;&#2366;&#2361;&#2325;&#2379;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 12% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>14.8% loss</p>\n", "<p>15.4% profit</p>\n", 
                                "<p>14.4% profit</p>\n", "<p>15% loss</p>\n"],
                    options_hi: ["<p>14.8% <span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n", "<p>15.4% <span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span></p>\n",
                                "<p>14.4% <span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span></p>\n", "<p>15% <span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span></p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit /Loss </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>30</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>30</mn><mo>&times;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 18 - </span><span style=\"font-family: Cambria Math;\">3.6 =</span><span style=\"font-family: Cambria Math;\"> 14.4% </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">+</span><span style=\"font-family: Cambria Math;\">ve</span><span style=\"font-family: Cambria Math;\"> sign indicates profit.</span></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">7.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">/</span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>30</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>12</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>30</mn><mo>&times;</mo><mn>12</mn></mrow><mn>100</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 18 - </span><span style=\"font-family: Cambria Math;\">3.6 =</span><span style=\"font-family: Cambria Math;\"> 14.4% </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">+</span><span style=\"font-family: Cambria Math;\">ve</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2367;&#2344;&#2381;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;&#2381;&#2358;&#2366;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "17",
                    question_en: "<p>8. <span style=\"font-family: Cambria Math;\">A shopkeeper marked a product 40% above the cost price and </span><span style=\"font-family: Cambria Math;\">Sold</span><span style=\"font-family: Cambria Math;\"> the product for &#8377;3,780 by giving a discount of 10%. The profit percentage of the shopkeeper </span><span style=\"font-family: Cambria Math;\">is :</span></p>\n",
                    question_hi: "<p>8. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 40% </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2325;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2340;&#2381;&#2346;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;3,780 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    options_en: ["<p>30%</p>\n", "<p>26%</p>\n", 
                                "<p>42%</p>\n", "<p>14%</p>\n"],
                    options_hi: ["<p>30%</p>\n", "<p>26%</p>\n",
                                "<p>42%</p>\n", "<p>14%</p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>P</mi><mi>r</mi><mi>o</mi><mi>f</mi><mi>i</mi><mi>t</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>40</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>10</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>40</mn><mo>&times;</mo><mn>10</mn></mrow><mn>100</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>30</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>26</mn><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo></math></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">8.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">% =</span><span style=\"font-family: Cambria Math;\"> 40 - </span><span style=\"font-family: Cambria Math;\">10 -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>40</mn><mo>&times;</mo><mn>10</mn></mrow><mn>100</mn></mfrac></math></span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 30 -&nbsp;&nbsp;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">4 =</span><span style=\"font-family: Cambria Math;\"> 26% </span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "17",
                    question_en: "<p>9. <span style=\"font-family: Cambria Math;\">S sells an item costing &#8377;37,800 to P with 8% profit. P sells it to Q with a 4% loss. At what price (in &#8377;) does P sell the item? (Correct to a nearest rupee) </span></p>\n",
                    question_hi: "<p>9. <span style=\"font-family: Cambria Math;\">S, &#8377;37,800 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> 8% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 4% </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2366;&#2341;</span><span style=\"font-family: Cambria Math;\"> Q </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> P </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> (&#8377; </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">) </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">? (</span><span style=\"font-family: Nirmala UI;\">&#2344;&#2367;&#2325;&#2335;&#2340;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2369;&#2346;&#2351;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2361;&#2368;</span><span style=\"font-family: Cambria Math;\">)</span></p>\n",
                    options_en: ["<p>39,919</p>\n", "<p>39,119</p>\n", 
                                "<p>39,191</p>\n", "<p>39,911</p>\n"],
                    options_hi: ["<p>39,919</p>\n", "<p>39,119</p>\n",
                                "<p>39,191</p>\n", "<p>39,911</p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>R</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>i</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>l</mi><mi>l</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>p</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>37</mn><mo>,</mo><mn>800</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>27</mn><mn>25</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>24</mn><mn>25</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>39191</mn><mo>&nbsp;</mo><mi>R</mi><mi>s</mi><mo>.</mo><mspace linebreak=\"newline\"></mspace><mi mathvariant=\"bold-italic\">S</mi><mi mathvariant=\"bold-italic\">h</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">t</mi><mo mathvariant=\"bold\">&nbsp;</mo><mi mathvariant=\"bold-italic\">T</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">k</mi><mi mathvariant=\"bold-italic\">s</mi><mo mathvariant=\"bold\">&nbsp;</mo><mo mathvariant=\"bold\">:</mo><mo mathvariant=\"bold\">-</mo><mo mathvariant=\"bold\">&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>N</mi><mi>e</mi><mi>t</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mi>c</mi><mi>h</mi><mi>a</mi><mi>n</mi><mi>g</mi><mi>e</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>8</mn><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo></mrow><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>.</mo><mn>68</mn><mo>%</mo><mspace linebreak=\"newline\"></mspace><mi>R</mi><mi>e</mi><mi>q</mi><mi>u</mi><mi>i</mi><mi>r</mi><mi>e</mi><mi>d</mi><mo>&nbsp;</mo><mi>s</mi><mi>e</mi><mi>l</mi><mi>l</mi><mi>i</mi><mi>n</mi><mi>g</mi><mo>&nbsp;</mo><mi>p</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>37</mn><mo>,</mo><mn>800</mn><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mrow><mn>103</mn><mo>.</mo><mn>68</mn></mrow><mn>100</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>39191</mn><mo>&nbsp;</mo><mi>R</mi><mi>s</mi><mo>.</mo></math></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">9.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>37</mn><mo>,</mo><mn>800</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>27</mn><mn>25</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>24</mn><mn>25</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>39191</mn><mo>&nbsp;</mo><mi>R</mi><mi>s</mi><mo>.</mo><mspace linebreak=\"newline\"></mspace><mi mathvariant=\"bold-italic\">S</mi><mi mathvariant=\"bold-italic\">h</mi><mi mathvariant=\"bold-italic\">o</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">t</mi><mo mathvariant=\"bold\">&nbsp;</mo><mi mathvariant=\"bold-italic\">T</mi><mi mathvariant=\"bold-italic\">r</mi><mi mathvariant=\"bold-italic\">i</mi><mi mathvariant=\"bold-italic\">c</mi><mi mathvariant=\"bold-italic\">k</mi><mi mathvariant=\"bold-italic\">s</mi><mo mathvariant=\"bold\">&nbsp;</mo><mo mathvariant=\"bold\">:</mo><mo mathvariant=\"bold\">-</mo><mo mathvariant=\"bold\">&nbsp;</mo><mspace linebreak=\"newline\"></mspace><mi>&#2358;&#2369;&#2342;&#2381;&#2343;</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mi>&#2346;&#2352;&#2367;&#2357;&#2352;&#2381;&#2340;&#2344;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>8</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>4</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mrow><mn>8</mn><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo></mrow><mn>100</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>3</mn><mo>.</mo><mn>68</mn><mo>%</mo><mspace linebreak=\"newline\"></mspace><mi>&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</mi><mo>&nbsp;</mo><mi>&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>37</mn><mo>,</mo><mn>800</mn><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mrow><mn>103</mn><mo>.</mo><mn>68</mn></mrow><mn>100</mn></mfrac><mspace linebreak=\"newline\"></mspace><mo>=</mo><mo>&nbsp;</mo><mn>39191</mn><mo>&nbsp;</mo><mi>R</mi><mi>s</mi><mo>.</mo></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "17",
                    question_en: "<p>10. <span style=\"font-family: Cambria Math;\">If a man purchases </span><span style=\"font-family: Cambria Math;\">kismis</span><span style=\"font-family: Cambria Math;\"> at &#8377;320 per kg and sells it at &#8377;6 per 25 grams, then his profit / loss percentage is:</span></p>\n",
                    question_hi: "<p>10. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2325;&#2381;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> &#8377;320 </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2354;&#2379;&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2350;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377;6 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 25 </span><span style=\"font-family: Nirmala UI;\">&#2327;&#2381;&#2352;&#2366;&#2350;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> / </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>20% loss</p>\n", "<p>25% loss</p>\n", 
                                "<p>25% profit</p>\n", "<p>20% profit</p>\n"],
                    options_hi: ["<p>20% <span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n", "<p>25% <span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                                "<p>25% <span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span></p>\n", "<p>20% <span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span></p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Selling price of </span><span style=\"font-family: Cambria Math;\">kismis</span><span style=\"font-family: Cambria Math;\"> per kg. = 6&times;40 = 240 </span><span style=\"font-family: Cambria Math;\">Rs./</span><span style=\"font-family: Cambria Math;\">kg</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required loss% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>320</mn><mo>-</mo><mn>240</mn></mrow><mn>320</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times;100 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 25%</span></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">10.(</span><span style=\"font-family: Cambria Math;\">b)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2360;&#2350;&#2367;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2327;&#2381;&#2352;&#2366;</span><span style=\"font-family: Cambria Math;\">. = 6 &times; 40 = 240 </span><span style=\"font-family: Cambria Math;\">Rs./</span><span style=\"font-family: Cambria Math;\">kg</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\">% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>320</mn><mo>-</mo><mn>240</mn></mrow><mn>320</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\">&times;100 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 25%</span></p>\n",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "17",
                    question_en: "<p>11. <span style=\"font-family: Cambria Math;\">The ratio of cost price (CP) and marked price (MP) of an article is </span><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 7. A discount of 20% is given on MP and the shopkeeper made a profit of &#8377;80. Find the CP of the article.</span></p>\n",
                    question_hi: "<p>11. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> (CP) </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> (MP) </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2344;&#2369;&#2346;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\">4 :</span><span style=\"font-family: Cambria Math;\"> 7 </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> MP </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2369;&#2325;&#2366;&#2344;&#2342;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> &#8377; 80 </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> CP </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p>&#8377;190</p>\n", "<p>&#8377;170</p>\n", 
                                "<p>&#8377;200</p>\n", "<p>&#8377;180</p>\n"],
                    options_hi: ["<p>&#8377;190</p>\n", "<p>&#8377;170</p>\n",
                                "<p>&#8377;200</p>\n", "<p>&#8377;180</p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Selling </span><span style=\"font-family: Cambria Math;\">price =</span><span style=\"font-family: Cambria Math;\"> 7 &times;80% = 5.6 units</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Profit = 5.6 - </span><span style=\"font-family: Cambria Math;\">4 =</span><span style=\"font-family: Cambria Math;\"> 1.6 units = 80 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cost </span><span style=\"font-family: Cambria Math;\">price (</span><span style=\"font-family: Cambria Math;\">4 units) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mrow><mn>1</mn><mo>.</mo><mn>6</mn></mrow></mfrac><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> = 200 Rs.</span></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">11.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 7 &times;80% = 5.6 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> =</span><span style=\"font-family: Cambria Math;\"> 5.6 - </span><span style=\"font-family: Cambria Math;\">4 = 1.6 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> = 80 Rs.</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> (</span><span style=\"font-family: Cambria Math;\">4 </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2325;&#2366;&#2312;</span><span style=\"font-family: Cambria Math;\"> ) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>80</mn><mrow><mn>1</mn><mo>.</mo><mn>6</mn></mrow></mfrac><mo>&times;</mo><mn>4</mn><mo>&nbsp;</mo></math></span><span style=\"font-family: Cambria Math;\"> = 200 Rs.</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "17",
                    question_en: "<p>12. <span style=\"font-family: Cambria Math;\">In April 2019, Dinesh purchased a new car for &#8377;7,00,000 and </span><span style=\"font-family: Cambria Math;\">Sold</span><span style=\"font-family: Cambria Math;\"> it in April 2022 at its depreciated cost. If the rate of depreciation in the first year is 20%, 15% in the second year and 10% every year thereafter, what amount of money will Dinesh get as the selling </span><span style=\"font-family: Cambria Math;\">price ?</span></p>\n",
                    question_hi: "<p>12. <span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2381;&#2352;&#2376;&#2354;</span><span style=\"font-family: Cambria Math;\"> 2019 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> &#8377;7,00,000 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2344;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2326;&#2352;&#2368;&#2342;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2381;&#2352;&#2376;&#2354;</span><span style=\"font-family: Cambria Math;\"> 2022 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;&#2361;&#2381;&#2352;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2351;&#2366;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2361;&#2354;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;&#2361;&#2381;&#2352;&#2366;&#2360;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2352;</span><span style=\"font-family: Cambria Math;\"> 20%, </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2370;&#2360;&#2352;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> 15% </span><span style=\"font-family: Nirmala UI;\">&#2324;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2366;&#2342;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2352;&#2381;&#2359;</span><span style=\"font-family: Cambria Math;\"> 10% </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2352;&#2370;&#2346;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2367;&#2344;&#2375;&#2358;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2343;&#2344;&#2352;&#2366;&#2358;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2367;&#2354;&#2375;&#2327;&#2368;</span><span style=\"font-family: Cambria Math;\">?</span></p>\n",
                    options_en: ["<p>&#8377;4,28,400</p>\n", "<p>&#8377;4,28,600</p>\n", 
                                "<p>&#8377;4,68,400</p>\n", "<p>&#8377;5,28,400</p>\n"],
                    options_hi: ["<p>&#8377;4,28,400</p>\n", "<p>&#8377;4,28,600</p>\n",
                                "<p>&#8377;4,68,400</p>\n", "<p>&#8377;5,28,400</p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required selling price = </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7,00,000 </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mn>17</mn><mn>20</mn></mfrac><mo>&times;</mo><mfrac><mn>9</mn><mn>10</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> = 700 &times; 36 &times;17 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 700 &times; 612 = </span><span style=\"font-family: Cambria Math;\">&#8377; </span><span style=\"font-family: Cambria Math;\"> 4</span><span style=\"font-family: Cambria Math;\">,28,400 </span></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">12.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">7,00,000 </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mo>&times;</mo><mfrac><mn>4</mn><mn>5</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mfrac><mn>17</mn><mn>20</mn></mfrac><mo>&times;</mo><mfrac><mn>9</mn><mn>10</mn></mfrac></math><span style=\"font-family: Cambria Math;\"> = 700 &times; 36 &times;17 </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">= 700 &times; 612 = </span><span style=\"font-family: Cambria Math;\">&#8377; </span><span style=\"font-family: Cambria Math;\">4,28,400 </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "17",
                    question_en: "<p>13. <span style=\"font-family: Cambria Math;\">If a craftsman were to sell a table at &#8377;1,300, he would lose 35%. To gain 30%, he would sell it for:</span></p>\n",
                    question_hi: "<p>13. <span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2358;&#2367;&#2354;&#2381;&#2346;&#2325;&#2366;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2332;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,300 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> 35% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2361;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2311;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2367;&#2340;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2375;&#2327;&#2366;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>&#8377;2,600</p>\n", "<p>&#8377;2,500</p>\n", 
                                "<p>&#8377;2,300</p>\n", "<p>&#8377;2,400</p>\n"],
                    options_hi: ["<p>&#8377;2,600</p>\n", "<p>&#8377;2,500</p>\n",
                                "<p>&#8377;2,300</p>\n", "<p>&#8377;2,400</p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required selling </span><span style=\"font-family: Cambria Math;\">price =</span><span style=\"font-family: Cambria Math;\"> </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1300 </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>65</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>130</mn><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>169000</mn><mn>65</mn></mfrac><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#8377; </span><span style=\"font-family: Cambria Math;\"> 2600</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">13.(</span><span style=\"font-family: Cambria Math;\">a)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">1300 &times; </span><span style=\"font-family: Cambria Math;\"> </span><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>100</mn><mn>65</mn></mfrac><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>130</mn><mrow><mn>100</mn><mo>&nbsp;</mo></mrow></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>169000</mn><mn>65</mn></mfrac><mo>&nbsp;</mo></math><span style=\"font-family: Cambria Math;\"> = </span><span style=\"font-family: Cambria Math;\">&#8377; </span><span style=\"font-family: Cambria Math;\"> 2600</span><span style=\"font-family: Cambria Math;\"> </span></p>\n",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "17",
                    question_en: "<p>14. <span style=\"font-family: Cambria Math;\">A profit earned by selling an article for &#8377;1,032 is equal to the loss incurred when the same article is Sold for &#8377;648. What should be the selling price of the article if the profit is 30</span><span style=\"font-family: Cambria Math;\">% ?</span></p>\n",
                    question_hi: "<p>14. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;1,032 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2352;&#2381;&#2332;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2379;</span><span style=\"font-family: Cambria Math;\"> &#8377;648 </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2375;&#2306;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2375;&#2330;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2369;&#2312;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2351;&#2342;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2349;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2340;&#2379;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2360;&#2381;&#2340;&#2369;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2351;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2330;&#2366;&#2361;&#2367;&#2319;</span><span style=\"font-family: Cambria Math;\"> ?</span></p>\n",
                    options_en: ["<p>&#8377;2,520</p>\n", "<p>&#8377;1,050</p>\n", 
                                "<p>&#8377;1,092</p>\n", "<p>&#8377;998</p>\n"],
                    options_hi: ["<p>&#8377;2,520</p>\n", "<p>&#8377;1,050</p>\n",
                                "<p>&#8377;1,092</p>\n", "<p>&#8377;998</p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Equating the value of cost </span><span style=\"font-family: Cambria Math;\">price :</span><span style=\"font-family: Cambria Math;\">- </span></p>\r\n<p><span style=\"font-family: Cambria Math;\">648 + x </span><span style=\"font-family: Cambria Math;\">= 1032 - x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1032</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>648</mn></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 192</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Cost price = 648 + 192 </span><span style=\"font-family: Cambria Math;\">= 840</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Required Selling </span><span style=\"font-family: Cambria Math;\">price =</span><span style=\"font-family: Cambria Math;\"> 840 &times;130% = &#8377; 1092</span></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">14.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2348;&#2352;&#2366;&#2348;&#2352;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2352;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> :</span><span style=\"font-family: Cambria Math;\">-</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">648 + x </span><span style=\"font-family: Cambria Math;\">= 1032 - x</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">x = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1032</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mn>648</mn></mrow><mn>2</mn></mfrac></math></span><span style=\"font-family: Cambria Math;\"> = 192</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2354;&#2366;&#2327;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 648 + 192 </span><span style=\"font-family: Cambria Math;\">= 840</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2310;&#2357;&#2358;&#2381;&#2351;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 840 &times;130% = &#8377; 1092</span></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. <span style=\"font-family: Cambria Math;\">A trader allows a 40% discount on his goods, but he loses 30% on them. Find the percentage of the marked price above the cost price. </span></p>\n",
                    question_hi: "<p>15. <span style=\"font-family: Nirmala UI;\">&#2319;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2381;&#2351;&#2366;&#2346;&#2366;&#2352;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2346;&#2344;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2354;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 40% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2331;&#2370;&#2335;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2342;&#2375;&#2340;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Cambria Math;\">, </span><span style=\"font-family: Nirmala UI;\">&#2354;&#2375;&#2325;&#2367;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2313;&#2344;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2352;</span><span style=\"font-family: Cambria Math;\"> 30% </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2366;&#2344;&#2367;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2379;&#2340;&#2368;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2361;&#2376;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2360;&#2375;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2343;&#2367;&#2325;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2366;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2346;&#2381;&#2352;&#2340;&#2367;&#2358;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2332;&#2381;&#2334;&#2366;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2325;&#2368;&#2332;&#2367;&#2319;</span><span style=\"font-family: Nirmala UI;\">&#2404;</span></p>\n",
                    options_en: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>15</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>%</mo></math></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>15</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>%</mo></math></span></p>\n", 
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>%</mo></math></span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>%</mo></math></span></p>\n"],
                    options_hi: ["<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>15</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>%</mo></math></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>15</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>%</mo></math></span></p>\n",
                                "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>%</mo></math></span></p>\n", "<p><span style=\"font-family: Cambria Math;\"><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mn>16</mn><mfrac><mn>1</mn><mn>3</mn></mfrac><mo>%</mo></math></span></p>\n"],
                    solution_en: "<p>.<span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Let the marked </span><span style=\"font-family: Cambria Math;\">price =</span><span style=\"font-family: Cambria Math;\"> 100 Rs.</span></p>\r\n<p><span style=\"font-family: Cambria Math;\">Then Selling </span><span style=\"font-family: Cambria Math;\">price =</span><span style=\"font-family: Cambria Math;\"> 60 Rs.</span></p>\r\n<p><math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>a</mi><mi>n</mi><mi>d</mi><mo>&nbsp;</mo><mi>cos</mi><mi>t</mi><mo>&nbsp;</mo><mi>p</mi><mi>r</mi><mi>i</mi><mi>c</mi><mi>e</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>10</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>600</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mi>R</mi><mi>s</mi><mo>.</mo><mspace linebreak=\"newline\"></mspace><mi>M</mi><mi>a</mi><mi>r</mi><mi>k</mi><mi>u</mi><mi>p</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>600</mn><mn>7</mn></mfrac></mrow><mstyle displaystyle=\"true\"><mfrac><mn>600</mn><mn>7</mn></mfrac></mstyle></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>16</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>%</mo></math></p>\n",
                    solution_hi: "<p>.<span style=\"font-family: Cambria Math;\">15.(</span><span style=\"font-family: Cambria Math;\">c)</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2350;&#2366;&#2344;&#2366;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2309;&#2306;&#2325;&#2367;&#2340;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 100 Rs.</span></p>\r\n<p><span style=\"font-family: Nirmala UI;\">&#2340;&#2348;</span><span style=\"font-family: Cambria Math;\"> ,</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2357;&#2367;&#2325;&#2381;&#2352;&#2351;</span><span style=\"font-family: Cambria Math;\"> </span><span style=\"font-family: Nirmala UI;\">&#2350;&#2370;&#2354;&#2381;&#2351;</span><span style=\"font-family: Cambria Math;\"> = 60 Rs.</span></p>\r\n<p><math style=\"font-family:Verdana\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mi>&#2324;&#2352;</mi><mo>&nbsp;</mo><mi>&#2354;&#2366;&#2327;&#2340;</mi><mo>&nbsp;</mo><mi>&#2350;&#2370;&#2354;&#2381;&#2351;</mi><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>60</mn><mo>&nbsp;</mo><mo>&times;</mo><mo>&nbsp;</mo><mfrac><mn>10</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mn>600</mn><mn>7</mn></mfrac><mo>&nbsp;</mo><mi>R</mi><mi>s</mi><mo>.</mo><mspace linebreak=\"newline\"></mspace><mi>&#2350;&#2366;&#2352;&#2381;&#2325;&#2309;&#2346;</mi><mo>&nbsp;</mo><mo>%</mo><mo>&nbsp;</mo><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mfrac><mrow><mn>100</mn><mo>&nbsp;</mo><mo>-</mo><mo>&nbsp;</mo><mfrac><mn>600</mn><mn>7</mn></mfrac></mrow><mstyle displaystyle=\"true\"><mfrac><mn>600</mn><mn>7</mn></mfrac></mstyle></mfrac><mo>&times;</mo><mn>100</mn><mo>&nbsp;</mo><mo>=</mo><mo>&nbsp;</mo><mn>16</mn><mfrac><mn>2</mn><mn>3</mn></mfrac><mo>%</mo></math></p>\n",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>