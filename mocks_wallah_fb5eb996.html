<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. AR Rehman, Gulzar and _____ won the 52<sup>nd</sup> annual Grammy Award for the best motion picture song &lsquo;Jai ho&rsquo;.</p>",
                    question_hi: "<p>1. ए.आर. रहमान, गुलज़ार और _____ ने सर्वश्रेष्ठ मोशन पिक्चर गीत \'जय हो\' के लिए 52 वाँ वार्षिक ग्रैमी पुरस्कार जीता।</p>",
                    options_en: ["<p>Danny Boyle</p>", "<p>Tanvi Shah</p>", 
                                "<p>Dev Patel</p>", "<p>Freida Pinto</p>"],
                    options_hi: ["<p>डैनी बॉयल</p>", "<p>तन्वी शाह</p>",
                                "<p>देव पटेल</p>", "<p>फ्रीडा पिंटो</p>"],
                    solution_en: "<p>1.(b)<strong> Tanvi Shah. </strong>She is the first Indian Woman to win a Grammy Award. Grammy Award is given in the field of Music. It is presented by the Recording Academy of the United States. First Indian who won a grammy award was Pandit Ravi Shankar. A R Rahman won Two Grammy Awards in 2010. The record for the most Grammy Awards won is held by Beyonc&eacute; (USA).</p>",
                    solution_hi: "<p>1.(b) <strong>तन्वी शाह। </strong>वह ग्रैमी पुरस्कार जीतने वाली प्रथम भारतीय महिला हैं। ग्रैमी पुरस्कार संगीत के क्षेत्र में दिया जाता है। यह संयुक्त राज्य अमेरिका की रिकॉर्डिंग अकादमी द्वारा प्रस्तुत किया जाता है। ग्रैमी पुरस्कार जीतने वाले प्रथम भारतीय पंडित रवि शंकर थे। ए. आर. रहमान ने 2010 में दो ग्रैमी पुरस्कार जीते थे। सबसे अधिक ग्रैमी पुरस्कार जीतने का रिकॉर्ड बियॉन्से (USA) के नाम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. Raziya Sultan was the first and only female ruler of the Sultanate, who ascended the throne in:</p>",
                    question_hi: "<p>2. रज़िया सुल्ताना सल्तनत की पहली और एकमात्र महिला शासक थी, जो _____में सिंहासन पर बैठीं ।</p>",
                    options_en: ["<p>1238 AD</p>", "<p>1236 AD</p>", 
                                "<p>1241 AD</p>", "<p>1242 AD</p>"],
                    options_hi: ["<p>1238 ईस्वी</p>", "<p>1236 ईस्वी</p>",
                                "<p>1241 ईस्वी</p>", "<p>1242 ईस्वी</p>"],
                    solution_en: "<p>2.(b)<strong> 1236 AD.</strong> Razia Sultana was the daughter of Iltutmish and served as the Sultan of Delhi from 1236 to 1240. She was married to Malik Altunia, the governor of Bathinda. She was buried in Kaithal, Haryana. She was imprisoned by Malik Ikhtiyar-ud-din Altunia in the Qila Mubarak Fort (Bathinda). The book \"Razia Queen of India\" was written by Rafiq Zakaria about her life.</p>",
                    solution_hi: "<p>2.(b) <strong>1236 ईस्वी।</strong> रज़िया सुल्तान इल्तुतमिश की पुत्री थीं और 1236 से 1240 तक दिल्ली की सुल्तान के रूप में शासन किया। वह मलिक अल्तुनिया, बठिंडा के गवर्नर, से विवाहित थीं। उनकी कब्र कैथल, हरियाणा में है । उन्हें मलिक इख्तियार-उद-दीन अल्तुनिया द्वारा किला मुबारक (बठिंडा) में कैद कर लिया गया था। उनके जीवन पर \"रज़िया क्वीन ऑफ इंडिया\" नामक पुस्तक रफीक जकारिया द्वारा लिखी गई थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. By which National Waterways (NW) of India, are the delta channels of Mahanadi and Brahmani rivers and East Coast Canal connected ?</p>",
                    question_hi: "<p>3. भारत के किस राष्ट्रीय जलमार्ग (NW) द्वारा महानदी और ब्राह्मणी नदी के डेल्टा चैनल और पूर्वी तटीय चैनल जुड़े हुए हैं ?</p>",
                    options_en: ["<p>NW 1</p>", "<p>NW 3</p>", 
                                "<p>NW 2</p>", "<p>NW 5</p>"],
                    options_hi: ["<p>NW 1</p>", "<p>NW 3</p>",
                                "<p>NW 2</p>", "<p>NW 5</p>"],
                    solution_en: "<p>3.(d)<strong> NW 5.</strong> It connects the states of Odisha and West Bengal, stretching 623 km in total, with 91 km in West Bengal and 532 km in Odisha. National Waterway-1 (NW-1) spans the Ganga-Bhagirathi-Hooghly river system from Prayagraj, Uttar Pradesh, to Haldia, West Bengal. National Waterway-2 covers 891 km of the Brahmaputra River between the Bangladesh border near Dhubri and Sadiya in Assam. National Waterway-3, also known as the West Coast Canal, is a 168 km route in Kerala from Kollam to Kottapuram.</p>",
                    solution_hi: "<p>3.(d) <strong>NW 5. </strong>यह ओडिशा और पश्चिम बंगाल राज्यों को जोड़ता है, जिसका विस्तार कुल 623 किलोमीटर तक है, जिसमें पश्चिम बंगाल में 91 किलोमीटर और ओडिशा में 532 किलोमीटर शामिल हैं। राष्ट्रीय जलमार्ग-1 (NW-1) प्रयागराज, उत्तर प्रदेश, हल्दिया से पश्चिम बंगाल तक गंगा-भागीरथी-हुगली नदी प्रणाली को जोड़ता है। राष्ट्रीय जलमार्ग-2 धुबरी के पास बांग्लादेश सीमा और असम में सदिया के बीच ब्रह्मपुत्र नदी के 891 किलोमीटर हिस्से को क्षेत्ररक्षित करता है। राष्ट्रीय जलमार्ग-3, जिसे वेस्ट कोस्ट नहर के रूप में भी जाना जाता है, केरल में कोल्लम से कोट्टापुरम तक 168 किलोमीटर का मार्ग है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. The Working Group under the chairmanship of _____ in the year_____ proposed a new intermediate monetary aggregate to be referred to as NM2.</p>",
                    question_hi: "<p>4. ______की अध्यक्षता में बने कार्य समूह ने वर्ष _____में एक नए मध्यवर्ती मौद्रिक समुच्चय को NM2 के रूप में संदर्भित करने का प्रस्ताव दिया।</p>",
                    options_en: ["<p>Dr. C Rangarajan; 1996</p>", "<p>Dr. YV Reddy; 1998</p>", 
                                "<p>Dr. KV Kamath; 1995</p>", "<p>Dr. PK Mohanty; 1998</p>"],
                    options_hi: ["<p>डॉ. सी. रंगराजन; 1996</p>", "<p>डॉ. वाई.वी. रेड्डी; 1998</p>",
                                "<p>डॉ. के.वी. कामथ; 1995</p>", "<p>डॉ. पी.के. मोहंती; 1998</p>"],
                    solution_en: "<p>4.(b) <strong>Dr. YV Reddy; 1998.</strong> NM2 (Narrow Money 2) measures the money supply in an economy, including currency, demand deposits, and time deposits. It indicates liquidity and helps guide monetary policy and economic regulation. Central banks, such as the RBI, use NM2 to control inflation and manage economic growth, reflecting broader money supply aspects for policy decisions.</p>",
                    solution_hi: "<p>4.(b) <strong>डॉ. वाई.वी. रेड्डी; 1998.</strong> NM2 (नैरो मनी 2) मुद्रा, मांग जमा और सावधि जमा सहित अर्थव्यवस्था में मुद्रा आपूर्ति को मापता है। यह तरलता को इंगित करता है और यह मौद्रिक नीति तथा आर्थिक विनियमन को निर्देशित करने में मदद करता है। RBI जैसे केंद्रीय बैंक मुद्रास्फीति को नियंत्रित करने और आर्थिक विकास का प्रबंधन करने के लिए NM2 का उपयोग करते हैं, जो नीतिगत निर्णयों के लिए व्यापक मुद्रा आपूर्ति पहलुओं को दर्शाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. In which state of India is the Tadoba National Park located ?</p>",
                    question_hi: "<p>5. ताडोबा राष्ट्रीय उद्यान भारत के किस राज्य में स्थित है ?</p>",
                    options_en: ["<p>Maharashtra</p>", "<p>Gujarat</p>", 
                                "<p>Madhya Pradesh</p>", "<p>Kerala</p>"],
                    options_hi: ["<p>महाराष्ट्र</p>", "<p>गुजरात</p>",
                                "<p>मध्य प्रदेश</p>", "<p>केरल</p>"],
                    solution_en: "<p>5.(a) <strong>Maharashtra. </strong>Tadoba National Park is located in the Chandrapur District. In 1955, it was given the designation of a National Park. Other National Parks of Maharashtra are Chandoli National Park, Navegaon National Park, Gugamal National Park, Sanjay Gandhi National Park. Gujarat - Gir National Park and Wildlife Sanctuary (Sasan Gir), Blackbuck National Park. Madhya Pradesh - Bandhavgarh National Park. Kerala - Silent Valley National Park, Anamudi Shola National Park.</p>",
                    solution_hi: "<p>5.(a) <strong>महाराष्ट्र।</strong> ताडोबा राष्ट्रीय उद्यान चंद्रपुर जिले में स्थित है। 1955 में इसे राष्ट्रीय उद्यान का दर्जा प्रदान किया गया था। महाराष्ट्र के अन्य राष्ट्रीय उद्यान चंदौली राष्ट्रीय उद्यान, नवेगांव राष्ट्रीय उद्यान, गुगामल राष्ट्रीय उद्यान, संजय गांधी राष्ट्रीय उद्यान। गुजरात - गिर राष्ट्रीय उद्यान और वन्यजीव अभयारण्य (सासन गिर), ब्लैकबक राष्ट्रीय उद्यान। मध्य प्रदेश - बांधवगढ़ राष्ट्रीय उद्यान। केरल - साइलेंट वैली राष्ट्रीय उद्यान, अनामुडी शोला राष्ट्रीय उद्यान।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "6. Which fundamental duty applies to you when you see your college friends start fighting with the canteen person when he passed a few anti-religious statements?",
                    question_hi: "6. जब आप देखते हैं कि आपके कॉलेज के दोस्त कैंटीन वाले के कुछ धर्म-विरोधी बयान देने पर उससे झगड़ने लगते हैं, तो आप पर कौन-सा मौलिक कर्तव्य लागू होता है?",
                    options_en: [" To promote harmony and spirit of brotherhood", " To value and preserve the rich heritage ", 
                                " To develop the scientific temper, humanism, and the spirit of inquiry ", " To protect and improve our natural environment<br /> "],
                    options_hi: [" सद्भाव और भ्रातृत्व की भावना को बढ़ावा देना", " हमारे समृद्ध विरासत को महत्व देना और उसका परिरक्षण करना  ",
                                " वैज्ञानिक दृष्टिकोण, मानववाद और जांच की भावना को विकसित करना ", " हमारे प्राकृतिक पर्यावरण की रक्षा और सुधार करना"],
                    solution_en: "6.(a) Article 51A in Part IV-A of the Indian Constitution lists eleven Fundamental Duties. Here are a few of them : Article 51A(e) : To promote harmony and the spirit of common brotherhood amongst all the people of India transcending religious, linguistic and regional or sectional diversities; to renounce practices derogatory to the dignity of women. Article 51A(f) : To value and preserve the rich heritage of our composite culture. Article 51A(g) : To protect and improve the natural environment including forests, lakes, rivers and wildlife, and to have compassion for living creatures. Article 51A (h) : To develop the scientific temper, humanism and the spirit of inquiry and reform.",
                    solution_hi: "6.(a) भारतीय संविधान के भाग IV-A में अनुच्छेद 51A में ग्यारह मौलिक कर्तव्य सूचीबद्ध हैं। उनमें से कुछ इस प्रकार हैं : अनुच्छेद 51A(e) : भारत के सभी लोगों के बीच धार्मिक, भाषाई और क्षेत्रीय या वर्गीय विविधताओं से परे सद्भाव और समान भाईचारे की भावना को बढ़ावा देना; महिलाओं की गरिमा के लिए अपमानजनक प्रथाओं का त्याग करना। अनुच्छेद 51A(f) : हमारी समग्र संस्कृति की समृद्ध विरासत को महत्व देना और संरक्षित करना। अनुच्छेद 51A(g) : वनों, झीलों, नदियों और वन्य जीवन सहित प्राकृतिक पर्यावरण की रक्षा और सुधार करना और जीवित प्राणियों के प्रति दया रखना। अनुच्छेद 51A(h) : वैज्ञानिक स्वभाव, मानवतावाद और जांच और सुधार की भावना का विकास करना।",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. When was the All India Trade Union Congress (AITUC) established ?</p>",
                    question_hi: "<p>7. अखिल भारतीय ट्रेड यूनियन कांग्रेस (AITUC) की स्थापना कब हुई थी ?</p>",
                    options_en: ["<p>1926</p>", "<p>1924</p>", 
                                "<p>1928</p>", "<p>1920</p>"],
                    options_hi: ["<p>1926 में</p>", "<p>1924 में</p>",
                                "<p>1928 में</p>", "<p>1920 में</p>"],
                    solution_en: "<p>7.(d) <strong>1920. </strong>All India Trade Union Congress (AITUC) was formed in Bombay. The AITUC was a broad-based organisation involving diverse ideologies. The main ideological groups were the communists led by S.A. Dange and M.N. Roy, the moderates led by M. Joshi and V.V. Giri and the nationalists which involved people like Lala Lajpat Rai and Jawaharlal Nehru. The first President of the All Indian Trade Union Congress (AITUC) on October 31, 1920, was Lala Lajpat Rai.</p>",
                    solution_hi: "<p>7.(d) <strong>1920 में। </strong>अखिल भारतीय ट्रेड यूनियन कांग्रेस (AITUC) का गठन बम्बई में किया गया। AITUC एक व्यापक आधार वाला संगठन था जिसमें विविध विचारधाराएँ शामिल थीं। मुख्य वैचारिक समूह एस ए डांगे और एम एन रॉय के नेतृत्व वाले कम्युनिस्ट, एम जोशी और वी वी गिरि के नेतृत्व वाले नरमपंथी और राष्ट्रवादी थे जिनमें लाला लाजपत राय और जवाहरलाल नेहरू जैसे लोग शामिल थे। 31 अक्टूबर 1920 को अखिल भारतीय ट्रेड यूनियन कांग्रेस (AITUC) के पहले अध्यक्ष लाला लाजपत राय थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. Who founded the Arya Samaj in Mumbai in 1875 ?</p>",
                    question_hi: "<p>8. 1875 में मुंबई में आर्य समाज की स्थापना किसने की थी ?</p>",
                    options_en: ["<p>Swami Dayanand Saraswati</p>", "<p>Debendranath Tagore</p>", 
                                "<p>Swami Vivekanand</p>", "<p>Raja Ram Mohan Roy</p>"],
                    options_hi: ["<p>स्वामी दयानंद सरस्वती</p>", "<p>देवेन्द्रनाथ टैगोर</p>",
                                "<p>स्वामी विवेकानंद</p>", "<p>राजा राम मोहन राय</p>"],
                    solution_en: "<p>8.(a)<strong> Swami Dayanand Saraswati,</strong> an Indian philosopher, is known for his work Satyarth Prakash and the slogan &ldquo;Go Back to the Vedas.&rdquo; He also initiated the Shuddhi Movement. Debendranath Tagore founded the Tattvabodhini Sabha in 1839 and joined the Brahmo Samaj (founded by Raja Rammohan Roy in 1828) in 1842. The Ramakrishna Mission was established by Swami Vivekananda in May 1897 at Belur Math, West Bengal.</p>",
                    solution_hi: "<p>8.(a) <strong>स्वामी दयानंद सरस्वती,</strong> एक भारतीय दार्शनिक, अपनी कृति सत्यार्थ प्रकाश और \"वेदों की ओर लौटो\" नारे के लिए जाने जाते हैं। उन्होंने शुद्धि आंदोलन की भी शुरुआत की। देवेंद्रनाथ टैगोर ने 1839 में तत्त्वबोधिनी सभा की स्थापना की और 1842 में ब्रह्म समाज (जिसकी स्थापना राजा राममोहन रॉय ने 1828 में की थी) में शामिल हो गए। रामकृष्ण मिशन की स्थापना स्वामी विवेकानंद ने मई 1897 में बेलूर मठ, पश्चिम बंगाल में की थी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. By which of the following methods do red algae reproduce ?</p>",
                    question_hi: "<p>9. लाल शैवाल निम्नलिखित में से किस विधि द्वारा प्रजनन करते हैं ?</p>",
                    options_en: [" Grafting ", " Cutting", 
                                " Micropropagation ", " Fragmentation"],
                    options_hi: [" ग्राफ्टिंग ", " कटिंग ",
                                " माइक्रोप्रोपेगेशन", " फ्रेगमेंटेमेंटेशन"],
                    solution_en: "<p>9.(d) <strong>Fragmentation. </strong>Algae reproduce through vegetative, asexual, and sexual methods: Vegetative Reproduction: This occurs by fragmentation, where each fragment develops into a new thallus. Asexual Reproduction: This involves the production of various types of spores, with zoospores being the most common. Sexual Reproduction: in algae involves the fusion of gametes, which can be isogamous (fusion of similar gametes) or anisogamous (fusion of dissimilar gametes), leading to the formation of zygotes that develop into new individuals.</p>",
                    solution_hi: "<p>9.(d) <strong>फ्रेगमेंटेमेंटेशन।</strong> शैवाल वानस्पतिक, अलैंगिक और लैंगिक तरीकों से प्रजनन करते हैं: वानस्पतिक प्रजनन: यह विखंडन द्वारा होता है, जहां प्रत्येक खंड एक नए थैलस में विकसित होता है। अलैंगिक प्रजनन: इसमें विभिन्न प्रकार के बीजाणुओं का उत्पादन शामिल है, जिसमें ज़ूस्पोर्स सबसे सामान्य हैं। लैंगिक प्रजनन : शैवाल में युग्मकों का संलयन शामिल होता है, जो समयुग्मी (समान युग्मकों का संलयन) या विषमयुग्मी (असमान युग्मकों का संलयन) हो सकता है, जिसके परिणामस्वरूप युग्मनज का निर्माण होता है जो नए जीवों में विकसित होते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Who is called the father of Local Self-Government in India ?</p>",
                    question_hi: "<p>10. निम्नलिखित में से किसे भारत में स्थानीय स्वशासन के जनक के रूप में जाना जाता है ?</p>",
                    options_en: ["<p>Lord Cornwallis</p>", "<p>Lord Wellesley</p>", 
                                "<p>Lord Ripon</p>", "<p>Lord William Bentinck</p>"],
                    options_hi: ["<p>लॉर्ड कॉर्नवालिस</p>", "<p>लार्ड वेलेस्ले</p>",
                                "<p>लॉर्ड रिपन</p>", "<p>लॉर्ड विलियम बेंटिक</p>"],
                    solution_en: "<p>10.(c) <strong>Lord Ripon.</strong> During Lord Ripon&rsquo;s tenure (1880-84), key reforms included the repeal of the Vernacular Press Act (1881) and a resolution on local self-government (1882). His period also saw the Ilbert Bill controversy (1883) and the formation of the Hunter Commission on Education (1882).</p>",
                    solution_hi: "<p>10.(c) <strong>लॉर्ड रिपन।</strong> लॉर्ड रिपन के कार्यकाल (1880-84) के दौरान, प्रमुख सुधारों में वर्नाक्युलर प्रेस एक्ट का निरस्त (1882) और स्थानीय स्वशासन पर एक प्रस्ताव (1881) शामिल था। उनके समय में इल्बर्ट बिल विवाद (1883) भी हुआ और शिक्षा पर हंटर आयोग (1882) का गठन किया गया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following is NOT a physical change ?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन-सा भौतिक परिवर्तन नहीं है ?</p>",
                    options_en: ["<p>Heating of iron rod to red hot</p>", "<p>Curdling of milk</p>", 
                                "<p>Evaporation of diesel</p>", "<p>Sublimation of NH<sub>4</sub>Cl</p>"],
                    options_hi: ["<p>लोहे की छड़ को गर्म करके लाल करना</p>", "<p>दूध से दही बनना</p>",
                                "<p>डीजल का वाष्पीकरण</p>", "<p>NH<sub>4</sub>Cl का उर्ध्वपातन</p>"],
                    solution_en: "<p>11.(b) <strong>Curdling of milk. </strong>It is a chemical change because lactic acid bacteria changes the milk permanently to the curd. A change in which a substance undergoes a change in its physical properties is called a physical change. A physical change is generally reversible. In such a change no new substance is formed. Examples include dissolving sugar in water, sublimation of dry ice, crushing paper, melting wax, and boiling water.</p>",
                    solution_hi: "<p>11.(b) <strong>दूध से दही बनना। </strong>यह एक रासायनिक परिवर्तन है क्योंकि लैक्टिक अम्ल बैक्टीरिया दूध को स्थायी रूप से दही में परिवर्तित कर देता है। ऐसा परिवर्तन जिसमें किसी पदार्थ के भौतिक गुणों में परिवर्तन होता है, उसे भौतिक परिवर्तन कहते हैं। भौतिक परिवर्तन सामान्यतः प्रतिवर्ती होता है। ऐसे परिवर्तन में कोई नया पदार्थ नहीं बनता है। उदाहरणों में जल में चीनी घोलना, सूखी बर्फ का उर्ध्वपातन, कागज को कुचलना, मोम को पिघलाना और जल को उबालना शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Who among the following Indian cricketers were named the Wisden&rsquo;s cricketer of the year in the 2022 edition of the Almanack ?</p>",
                    question_hi: "<p>12. निम्नलिखित भारतीय क्रिकेटरों में से किन्&zwj;हें अल्मनैक (Almanack) के 2022 संस्करण में विजडन क्रिकेटर ऑफ द ईयर (Wisden&rsquo;s cricketer of the year) नामित किया गया ?</p>",
                    options_en: ["<p>Surya Kumar Yadav and Axar Patel</p>", "<p>Virat Kohli and Bhuvneshwar Kumar</p>", 
                                "<p>Rohit Sharma and Jasprit Bumrah</p>", "<p>Rohit Sharma and Virat Kohli</p>"],
                    options_hi: ["<p>सूर्य कुमार यादव और अक्षर पटेल</p>", "<p>विराट कोहली और भुवनेश्वर कुमार</p>",
                                "<p>रोहित शर्मा और जसप्रीत बुमराह</p>", "<p>रोहित शर्मा और विराट कोहली</p>"],
                    solution_en: "<p>12.(c) <strong>Rohit Sharma and Jasprit Bumrah.</strong> Wisden Cricketers of the Year : They are selected by the annual publication Wisden Cricketers\' Almanack, primarily based on their influence in the previous English season. 2023 Recipients: Tom Blundell, Ben Foakes, Harmanpreet Kaur, Daryl Mitchell, and Matthew Potts.</p>",
                    solution_hi: "<p>12.(c)<strong> रोहित शर्मा और जसप्रीत बुमराह। </strong>विजडन क्रिकेटर्स ऑफ द ईयर: इनका चयन वार्षिक प्रकाशन विजडन क्रिकेटर्स अल्मनैक द्वारा किया जाता है, जो मुख्य रूप से पिछले इंग्लिश सीज़न में उनके प्रदर्शन के आधार पर होता है। 2023 प्राप्तकर्ता: टॉम ब्लंडेल, बेन फ़ोक्स, हरमनप्रीत कौर, डेरिल मिशेल और मैथ्यू पॉट्स आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. Which of the following is responsible for the red colour of beetroot ?</p>",
                    question_hi: "<p>13. चुकंदर के लाल रंग के होने के लिए निम्नलिखित में से कौन उत्तरदायी है ?</p>",
                    options_en: ["<p>Curcumin</p>", "<p>Betanin</p>", 
                                "<p>Beta carotene</p>", "<p>Lycopene</p>"],
                    options_hi: ["<p>करक्यूमिन (Curcumin)</p>", "<p>बीटानिन (Betanin)</p>",
                                "<p>बीटा कैरोटीन (Beta carotene)</p>", "<p>लाइकोपीन (Lycopene)</p>"],
                    solution_en: "<p>13.(b) <strong>Betanin. </strong>The red color in beetroot is due to betacyanin, a water-soluble pigment located in the cell vacuole, not in chloroplasts. Curcumin : A yellow-orange pigment found in turmeric (Curcuma longa). Beta carotene : A yellow-orange pigment converted to vitamin A in the body, found in carrots and sweet potatoes. Lycopene is the red colored pigment abundantly found in red colored fruits and vegetables such as tomato, papaya, pink grapefruit, pink guava and watermelon.</p>",
                    solution_hi: "<p>13.(b) <strong>बीटानिन (Betanin)।</strong> चुकंदर में लाल रंग बीटासायनिन के कारण होता है, जो कोशिका रिक्तिका में स्थित एक जल-विलेय वर्णक है, जो कि क्लोरोप्लास्ट में नहीं बल्कि कोशिका रिक्तिका में पाया जाता है। करक्यूमिन : हल्दी (करक्यूमा लोंगा) में पाया जाने वाला एक पीला-नारंगी रंगद्रव्य है। बीटा कैरोटीन: शरीर में विटामिन A में परिवर्तित होने वाला एक पीला-नारंगी रंगद्रव्य, जो गाजर और शकरकंद में पाया जाता है। लाइकोपीन लाल रंग का वर्णक है जो टमाटर, पपीता, गुलाबी अंगूर, गुलाबी अमरूद और तरबूज जैसे लाल रंग के फलों और सब्जियों में प्रचुर मात्रा में पाया जाता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. According to the Census of India 2011, which group of Union Territories has the highest density of population ?</p>",
                    question_hi: "<p>14. भारत की जनगणना 2011 के अनुसार, केन्द्र शासित प्रदेशों के किस समूह का जनसंख्या घनत्व सर्वाधिक है ?</p>",
                    options_en: ["<p>Lakshadweep, Daman and Diu and Puducherry</p>", "<p>Delhi, Chandigarh and Daman and Diu</p>", 
                                "<p>Delhi, Chandigarh and Puducherry</p>", "<p>Lakshadweep, Daman and Diu and Chandigarh</p>"],
                    options_hi: ["<p>लक्षद्वीप, दमन और दीव तथा पुदुचेरी</p>", "<p>दिल्ली, चंडीगढ़ तथा दमन और दीव</p>",
                                "<p>दिल्ली, चंडीगढ़ और पुदुचेरी</p>", "<p>लक्षद्वीप, दमन और दीव तथा चंडीगढ़</p>"],
                    solution_en: "<p>14.(c) <strong>Delhi, Chandigarh and Puducherry. </strong>In 2011, the population density of India was 382 persons per square kilometer, with Arunachal Pradesh having the lowest density at 17 persons per square kilometer; Bihar had the highest density at 1,106 persons per square kilometer, followed by West Bengal (1028), Kerala (860), Uttar Pradesh (829), and Haryana (573).</p>",
                    solution_hi: "<p>14.(c)<strong> दिल्ली, चंडीगढ़ और पुदुचेरी। </strong>2011 में, भारत का जनसंख्या घनत्व 382 व्यक्ति प्रति वर्ग किलोमीटर था, जिसमें अरुणाचल प्रदेश का घनत्व सबसे कम 17 व्यक्ति प्रति वर्ग किलोमीटर था; बिहार में सबसे अधिक 1,106 व्यक्ति प्रति वर्ग किलोमीटर घनत्व था, उसके बाद पश्चिम बंगाल (1028), केरल (860), उत्तर प्रदेश (829) और हरियाणा (573) का स्थान था।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. Which Article deals with the election of the President ?</p>",
                    question_hi: "<p>15. कौन-सा अनुच्छेद राष्ट्रपति के निर्वाचन से संबंधित है ?</p>",
                    options_en: ["<p>Article 72</p>", "<p>Article 54</p>", 
                                "<p>Article 74</p>", "<p>Article 66</p>"],
                    options_hi: ["<p>अनुच्छेद 72</p>", "<p>अनुच्छेद 54</p>",
                                "<p>अनुच्छेद 74</p>", "<p>अनुच्छेद 66</p>"],
                    solution_en: "<p>15.(b) <strong>Article 54 -</strong> The President shall be elected by the members of an electoral college consisting of: (a) the elected members of both Houses of Parliament; and (b) the elected members of the Legislative Assemblies of the States. Article 66 - Election of Vice-President. Article 72 : Power of the President to grant pardons, suspend, remit, or commute sentences in certain cases. Article 74 : Establishes the Council of Ministers to aid and advise the President.</p>",
                    solution_hi: "<p>15.(b) <strong>अनुच्छेद 54 -</strong> राष्ट्रपति का चुनाव एक निर्वाचक मंडल के सदस्यों द्वारा किया जाएगा, जिसमें शामिल सदस्य होंगे : (a) संसद के दोनों सदनों के निर्वाचित सदस्य; और (b) राज्यों की विधानसभाओं के निर्वाचित सदस्य। अनुच्छेद 66 - उपराष्ट्रपति का चुनाव। अनुच्छेद 72 : राष्ट्रपति को कुछ मामलों में क्षमा देने, सजा निलंबित करने, माफ करने या कम करने की शक्ति प्रदान करता है। अनुच्छेद 74 : राष्ट्रपति की सहायता और सलाह के लिए मंत्रिपरिषद की स्थापना करता है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Identify an example of plasma as a state of matter</p>",
                    question_hi: "<p>16. पदार्थ की अवस्था के रूप में प्लाज्मा के एक उदाहरण की पहचान कीजिए।</p>",
                    options_en: ["<p>Blood</p>", "<p>Dry ice</p>", 
                                "<p>Freon</p>", "<p>Neon sign bulbs</p>"],
                    options_hi: ["<p>रक्त</p>", "<p>शुष्क बर्फ</p>",
                                "<p>फ्रेऑन</p>", "<p>नियॉन साइन बल्ब</p>"],
                    solution_en: "<p>16.(d) <strong>Neon sign bulbs.</strong> Plasma is a hot, ionized gas made up of roughly equal numbers of positively charged ions and negatively charged electrons. It has distinct characteristics that differentiate it from ordinary neutral gases, making it a \"fourth state of matter\" (solid, liquid, gas, and plasma). Plasmas are strongly influenced by electric and magnetic fields due to their charged particles, unlike neutral gases.</p>",
                    solution_hi: "<p>16.(d)<strong> नियॉन साइन बल्ब। </strong>प्लाज़्मा एक गर्म, आयनीकृत गैस है जो लगभग समान संख्या में धनात्मक आवेशित आयनों और ऋणात्मक आवेशित इलेक्ट्रॉनों से बनी होती है। इसमें विशिष्ट विशेषताएँ होती हैं जो इसे सामान्य उदासीन गैसों से अलग करती हैं, जिससे यह \"पदार्थ की चौथी अवस्था\" बन जाती है (ठोस, द्रव, गैस, और प्लाज़्मा)। प्लाज़्मा अपने आवेशित कणों के कारण विद्युत और चुंबकीय क्षेत्रों से बहुत प्रभावित होते हैं, जो उदासीन गैसों के विपरीत है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Classical Sarod player, Amjad Ali Khan was awarded _______ in 2001 by the Government of India.</p>",
                    question_hi: "<p>17. शास्त्रीय सरोद वादक अमजद अली खान को 2001 में भारत सरकार द्वारा ______से सम्मानित किया गया था।</p>",
                    options_en: ["<p>Bharat Ratna</p>", "<p>Padma Shri</p>", 
                                "<p>Padma Vibhushan</p>", "<p>Padma Bhushan</p>"],
                    options_hi: ["<p>भारत रत्न</p>", "<p>पद्म श्री</p>",
                                "<p>पद्म विभूषण</p>", "<p>पद्म भूषण</p>"],
                    solution_en: "<p>17.(c) <strong>Padma Vibhushan. </strong>Ustad Amjad Ali Khan (Sarod Samrat): He is an Indian classical sarod player, best known for his clear and fast ekhara taans. He was born in Gwalior. Awards: Padma Shri (1975), Padma Bhushan (1991), Sangeet Natak Akademi Award (1989), Sangeet Natak Akademi Fellowship (2011).</p>",
                    solution_hi: "<p>17.(c)<strong> पद्म विभूषण।</strong> उस्ताद अमजद अली खान (सरोद सम्राट) : वह एक भारतीय शास्त्रीय सरोद वादक हैं, जो अपने स्पष्ट और तीव्र एखारा तान के लिए जाने जाते हैं। उनका जन्म ग्वालियर में हुआ था। पुरस्कार : पद्म श्री (1975), पद्म भूषण (1991), संगीत नाटक अकादमी पुरस्कार (1989), संगीत नाटक अकादमी फ़ेलोशिप (2011)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "18. Which of the following statements is correct?",
                    question_hi: "18. निम्नलिखित में से कौन-सा कथन सही है?",
                    options_en: [" Pond water and sea water are considered as fresh water.", " Sea water is freshwater habitat. ", 
                                " Rivers and sea water are considered as fresh water. ", " Rivers, creeks, lakes, ponds, and streams are all freshwater habitats."],
                    options_hi: [" तालाब के पानी और समुद्री जल को ताजा पानी (Fresh Water) माना जाता है। ", " समुद्र का पानी मीठे पानी(Freshwater) का आशय है। ",
                                " नदियों और समुद्र के पानी को ताजा पानी(Fresh Water) माना जाता है। ", " नदियाँ, खाड़ियाँ, झीलें, तालाब और सोते मीठे पानी (Freshwater) के आशय है।"],
                    solution_en: "18.(d) Fresh water ecosystem - Water on land which is continuously cycling and has low salt content is known as fresh water and its study is called limnology. (i) Static or still water (Lentic) e.g. pond, lake, bogs and swamps. (ii) Running water (Lotic) e.g. springs, mountain brooks, streams and rivers.",
                    solution_hi: "18.(d) ताज़ा पानी पारिस्थितिकी तंत्र - भूमि पर पानी जो लगातार चक्रित होता है और जिसमें नमक की मात्रा कम होती है उसे ताज़ा पानी के रूप में जाना जाता है और इसके अध्ययन को लिम्नोलॉजी कहा जाता है। (i) स्थैतिक या शांत पानी (लेंटिक) जैसे तालाब, झील और दलदल। (ii) बहता पानी (लोटिक) जैसे झरने, पहाड़ी झरने और नदियाँ।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Mahendravarman I who wrote Mattavilasa-prahasana is related to which of the following ruling dynasties ?</p>",
                    question_hi: "<p>19. मत्तविलास-प्रहसन के रचयिता महेंद्रवर्मन प्रथम, निम्नलिखित में से किस शासक राजवंश से संबंधित है ?</p>",
                    options_en: ["<p>Maitraka</p>", "<p>Pallava</p>", 
                                "<p>Pushyabhuti</p>", "<p>Chalukya</p>"],
                    options_hi: ["<p>मैत्रक</p>", "<p>पल्लव</p>",
                                "<p>पुष्यभूति</p>", "<p>चालुक्य</p>"],
                    solution_en: "<p>19.(b) <strong>Pallava </strong>dynasty (275 CE to 897 CE) - They ruled a significant portion of the Deccan, also known as Tondaimandalam, which covered parts of present-day Tamil Nadu and Andhra Pradesh. Founder - Simha Vishnu (Avanisimha). Mahendravarman - He defeated the Kalabhras and re-established the Pallava kingdom. Narashimhavarman-I - Son of Mahendravarman.</p>",
                    solution_hi: "<p>19.(b) <strong>पल्लव</strong> वंश (275 ई. से 897 ई.) - उन्होंने दक्कन के एक महत्वपूर्ण हिस्से पर शासन किया, जिसे तोंडईमंडलम के नाम से भी जाना जाता है, जिसमें वर्तमान तमिलनाडु और आंध्र प्रदेश के कुछ हिस्से शामिल थे। संस्थापक - सिंह विष्णु (अवनिसिंह)। महेंद्रवर्मन - उन्होंने कलभ्रों को हराया और पल्लव साम्राज्य को पुनः स्थापित किया था। नरसिंहवर्मन-I - महेंद्रवर्मन का पुत्र।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Who is considered the highest law officer of the state in India ?</p>",
                    question_hi: "<p>20. भारत में राज्य का सर्वोच्च विधि अधिकारी किसे माना जाता है ?</p>",
                    options_en: ["<p>Advocate general</p>", "<p>Attorney general</p>", 
                                "<p>Chief justice of high court</p>", "<p>Auditor general</p>"],
                    options_hi: ["<p>महाधिवक्ता</p>", "<p>महान्यायवादी</p>",
                                "<p>उच्च न्यायालय के मुख्य न्यायाधीश</p>", "<p>महालेखापरीक्षक</p>"],
                    solution_en: "<p>20.(a) <strong>Advocate general.</strong> It is a Constitutional post and its authority is mentioned in article 165 of the Indian Constitution. Appointment: The Governor of each State shall appoint a person qualified to be a High Court Judge as the Advocate-General for the State. Term and Remuneration: The Advocate-General holds office at the pleasure of the Governor and receives remuneration as determined by the Governor.</p>",
                    solution_hi: "<p>20.(a) <strong>महाधिवक्ता। </strong>यह एक संवैधानिक पद है और इसका अधिकार भारतीय संविधान के अनुच्छेद 165 में वर्णित है। नियुक्ति: प्रत्येक राज्य का राज्यपाल उच्च न्यायालय के न्यायाधीश बनने के योग्य व्यक्ति को राज्य के महाधिवक्ता के रूप में नियुक्त करेगा। कार्यकाल और पारिश्रमिक: महाधिवक्ता राज्यपाल की इच्छा पर पद धारण करता है और राज्यपाल द्वारा निर्धारित पारिश्रमिक प्राप्त करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "21. Which of the following Constitution Amendment Bills is related to the provision of modifying the list of Scheduled Caste in Tamil Nadu?",
                    question_hi: "21. निम्नलिखित में से कौन-सा संविधान संशोधन विधेयक तमिलनाडु में अनुसूचित जाति की सूची को संशोधित करने के प्रावधान से संबंधित है? ",
                    options_en: [" The Constitution (Scheduled Tribes) Order (Amendment) Bill, 2019  ", " The Constitution (Scheduled Tribes) Order Amendment Act, 2013", 
                                " The Constitution (Scheduled Caste) Order (Amendment) Bill, 2015 ", " The Constitution (Scheduled Castes) Order (Amendment) Bill, 2021"],
                    options_hi: [" संविधान (अनुसूचित जनजाति) आदेश (संशोधन) विधेयक 2019 ", " संविधान (अनुसूचित जनजाति) आदेश संशोधन अधिनियम 2013  ",
                                " संविधान (अनुसूचित जाति) आदेश (संशोधन) विधेयक 2015 ", " संविधान (अनुसूचित जाति) आदेश (संशोधन) विधेयक 2021"],
                    solution_en: "21.(d) The Bill amends the Constitution (Scheduled Castes) Order, 1950. The Constitution empowers the President to specify the Scheduled Castes (SCs) in various states and union territories.  Further, it permits Parliament to modify this list of notified SCs.  The Statement of Objects and Reasons of the Bill states that the Bill has been introduced to give effect to modifications proposed by the state of Tamil Nadu.",
                    solution_hi: "21.(d) यह विधेयक संविधान (अनुसूचित जातियां) आदेश, 1950 में संशोधन करता है। संविधान राष्ट्रपति को विभिन्न राज्यों एवं केंद्र शासित प्रदेशों में अनुसूचित जातियों (SC) को निर्दिष्ट करने का अधिकार देता है। इसके अलावा, यह संसद को अधिसूचित SC की इस सूची को संशोधित करने की अनुमति देता है। विधेयक के उद्देश्यों और कारणों के कथन में कहा गया है कि तमिलनाडु राज्य द्वारा प्रस्तावित संशोधनों को प्रभावी करने के लिए विधेयक पेश किया गया है।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. What is the primary benefit offered to businesses in Special Economic Zones (SEZs) ?</p>",
                    question_hi: "<p>22. विशेष आर्थिक क्षेत्रों {Special Economic Zones (SEZs)} में व्यवसायों को दिया जाने वाला प्राथमिक लाभ क्या है ?</p>",
                    options_en: ["<p>Tax and duty concessions</p>", "<p>Guaranteed market share</p>", 
                                "<p>Mandatory government contracts</p>", "<p>Unlimited foreign investment</p>"],
                    options_hi: ["<p>कर और शुल्क रियायतें</p>", "<p>गारंटीयुक्त मार्केट शेयर</p>",
                                "<p>अनिवार्य सरकारी अनुबंध</p>", "<p>असीमित विदेशी निवेश</p>"],
                    solution_en: "<p>22.(a) <strong>Tax and duty concessions. </strong>The incentives and facilities offered to the units in SEZs for attracting investments into the SEZs, including foreign investment include:- Duty free import/domestic procurement of goods for development, operation and maintenance of SEZ units. 100% Income Tax exemption on export income for SEZ units under Section 10AA of the Income Tax Act for first 5 years, 50% for next 5 years thereafter and 50% of the ploughed back export profit for next 5 years.</p>",
                    solution_hi: "<p>22.(a) <strong>कर और शुल्क रियायतें। </strong>विदेशी निवेश सहित विशेष आर्थिक क्षेत्र में निवेश को आकर्षित करने के लिए विशेष आर्थिक क्षेत्र में इकाइयों को दिए जाने वाले प्रोत्साहन और सुविधाओं में शामिल हैं:- विशेष आर्थिक क्षेत्र इकाइयों के विकास, संचालन और रखरखाव के लिए वस्तुओं का शुल्क मुक्त आयात/घरेलू खरीद। विशेष आर्थिक क्षेत्र इकाइयों के लिए आयकर अधिनियम की धारा 10AA के तहत निर्यात आय पर पहले 5 वर्षों के लिए 100% आयकर छूट, उसके बाद अगले 5 वर्षों के लिए 50% और अगले 5 वर्षों के लिए वापस किए गए निर्यात लाभ का 50%।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Which of the following countries will host the ICC men&rsquo;s Cricket World Cup in the year 2031 ?</p>",
                    question_hi: "<p>23. निम्नलिखित में से कौन-से देश वर्ष 2031 में आई.सी.सी. (ICC) पुरुष क्रिकेट विश्व कप की मेजबानी करेंगे ?</p>",
                    options_en: ["<p>India, Bangladesh</p>", "<p>India, Sri Lanka</p>", 
                                "<p>Sri Lanka and Pakistan</p>", "<p>Sri Lanka and Bangladesh</p>"],
                    options_hi: ["<p>भारत, बांग्लादेश</p>", "<p>भारत, श्रीलंका</p>",
                                "<p>श्रीलंका और पाकिस्तान</p>", "<p>श्रीलंका और बांग्लादेश</p>"],
                    solution_en: "<p>23.(a) <strong>India, Bangladesh.</strong> ICC (International Cricket Council) - Founded - 15 June 1909, Headquarters - Dubai. Men\'s ICC Cricket World Cup (ODI) 2027 - Host Country (Namibia, South Africa and Zimbabwe). Cricket World Cup, 1983 was held in England, where India won its first World Cup title.</p>",
                    solution_hi: "<p>23.(a) <strong>भारत, बांग्लादेश। </strong>ICC (अंतर्राष्ट्रीय क्रिकेट परिषद) - स्थापना - 15 जून 1909, मुख्यालय - दुबई। पुरुष ICC क्रिकेट विश्व कप (ODI) 2027 - मेज़बान देश (नामीबिया, दक्षिण अफ़्रीका और ज़िम्बाब्वे)। क्रिकेट विश्व कप, 1983 इंग्लैंड में आयोजित किया गया था, जहाँ पर भारत ने अपना प्रथम विश्व कप खिताब जीता था।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. By what name is the festival of Makar Sankranti celebrated in Assam ?</p>",
                    question_hi: "<p>24. असम में मकर संक्रांति का पर्व किस नाम से मनाया जाता है ?</p>",
                    options_en: ["<p>Pedda Padunga</p>", "<p>Thai Pongal</p>", 
                                "<p>Pongal</p>", "<p>Magh Bihu</p>"],
                    options_hi: ["<p>पेड्डा पडुंगा</p>", "<p>थाई पोंगल</p>",
                                "<p>पोंगल</p>", "<p>माघ बिहू</p>"],
                    solution_en: "<p>24.(d)<strong> Magh Bihu.</strong> Makar Sankranti is celebrated in January and marks the transition of the sun into the zodiac sign Capricorn (Makar). This event symbolizes the end of winter and the beginning of longer days, as the sun starts moving northward (known as Uttarayana). The festival is celebrated under various names in different parts of India -Makar Sankranti: Kerala, Odisha, Karnataka, Maharashtra, Goa, and West Bengal. Maghi Saaji: Himachal Pradesh. Sakrat: Haryana and Rajasthan. Thai Pongal: Tamil Nadu (celebrated as a harvest festival). Ghughuti: Uttarakhand. Dahi Chura: Bihar (the traditional dish eaten during this festival). Khichdi Parv: Uttar Pradesh (where khichdi is prepared as part of the celebrations). Uttarayan: Gujarat (popular for the kite-flying tradition).</p>",
                    solution_hi: "<p>24.(d) <strong>माघ बिहू।</strong> मकर संक्रांति जनवरी में मनाई जाती है और यह सूर्य के मकर राशि में प्रवेश का प्रतीक है। यह घटना सर्दियों के अंत और लंबे दिनों की शुरुआत का प्रतीक है, क्योंकि सूर्य उत्तर की ओर बढ़ना शुरू कर देता है (जिसे उत्तरायण कहा जाता है)। यह त्योहार भारत के विभिन्न हिस्सों में विभिन्न नामों से मनाया जाता है - मकर संक्रांति : केरल, ओडिशा, कर्नाटक, महाराष्ट्र, गोवा और पश्चिम बंगाल। माघी साजी : हिमाचल प्रदेश। सकरात : हरियाणा और राजस्थान। थाई पोंगल : तमिलनाडु (फसल उत्सव के रूप में मनाया जाता है)। घुघुती : उत्तराखंड। दही चूड़ा : बिहार (इस त्योहार के दौरान खाया जाने वाला पारंपरिक व्यंजन)। खिचड़ी पर्व : उत्तर प्रदेश (जहां उत्सव के हिस्से के रूप में खिचड़ी तैयार की जाती है)। उत्तरायण : गुजरात (पतंग उड़ाने की परंपरा के लिए लोकप्रिय)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Which of the following terms is related to football ?</p>",
                    question_hi: "<p>25. निम्नलिखित में से कौन-सा शब्द फुटबॉल से संबंधित है ?</p>",
                    options_en: ["<p>Third man</p>", "<p>Bicycle Kick</p>", 
                                "<p>Overs</p>", "<p>LBW</p>"],
                    options_hi: ["<p>थर्ड मैन (Third man)</p>", "<p>बाईसिकल किक (Bicycle Kick)</p>",
                                "<p>ओवर्स (Overs)</p>", "<p>एल.बी.डब्ल्यू. (LBW)</p>"],
                    solution_en: "<p>25.(b) <strong>Bicycle Kick:</strong> This is a type of kick in football where a player, while airborne, kicks the ball overhead using a cycling motion of the legs. Important Terminologies: Football - Direct free kick, Set, Volley, Service line, Header, Free Kick, Penalty Kick, Hat-trick. Cricket- Third man, Overs, LBW (Leg Before Wicket).</p>",
                    solution_hi: "<p>25.(b) <strong>बाईसिकल किक (Bicycle Kick):</strong> यह फुटबॉल में किक का एक प्रकार है जिसमें खिलाड़ी हवा में उड़ते हुए पैरों की साइकिलिंग गति का उपयोग करके गेंद को ऊपर की ओर मारता है। महत्वपूर्ण शब्दावली: फुटबॉल - डायरेक्ट फ्री किक, सेट, वॉली, सर्विस लाइन, हेडर, फ्री किक, पेनल्टी किक, हैट्रिक। क्रिकेट- थर्ड मैन, ओवर, LBW (लेग बिफोर विकेट)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>