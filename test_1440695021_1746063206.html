<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">60:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the option that represents the letters that when sequentially placed from left to right in the blanks below will complete the letter series.<br>_ C _ W _ _ _ C _ W _ _</p>",
                    question_hi: "<p>1. उस विकल्प का चयन कीजिए जो उन अक्षरों को प्रदर्शित करता है जिन्हें नीचे दिए गए रिक्&zwj;त स्&zwj;थानों में बाएं से दाएं क्रमिक रूप से रखने पर अक्षर श्रृंखला पूर्ण हो जाएगी।<br>_ C _ W _ _ _ C _ W _ _</p>",
                    options_en: [
                        "<p>WCCWWWCC</p>",
                        "<p>WWCWWWCW</p>",
                        "<p>CWCWWCCW</p>",
                        "<p>CCWWCCWW</p>"
                    ],
                    options_hi: [
                        "<p>WCCWWWCC</p>",
                        "<p>WWCWWWCW</p>",
                        "<p>CWCWWCCW</p>",
                        "<p>CCWWCCWW</p>"
                    ],
                    solution_en: "<p>1.(b) <span style=\"text-decoration: underline;\"><strong>W</strong></span> C <span style=\"text-decoration: underline;\"><strong>W</strong></span> / W <span style=\"text-decoration: underline;\"><strong>C</strong></span> <span style=\"text-decoration: underline;\"><strong>W</strong></span> / <span style=\"text-decoration: underline;\"><strong>W</strong></span> C<span style=\"text-decoration: underline;\"><strong>W</strong></span> / W<span style=\"text-decoration: underline;\"><strong>C</strong><strong>W</strong></span></p>",
                    solution_hi: "<p>1.(b) <span style=\"text-decoration: underline;\"><strong>W</strong></span> C <span style=\"text-decoration: underline;\"><strong>W</strong></span> / W <span style=\"text-decoration: underline;\"><strong>C</strong></span> <span style=\"text-decoration: underline;\"><strong>W</strong></span> / <span style=\"text-decoration: underline;\"><strong>W</strong></span> C<span style=\"text-decoration: underline;\"><strong>W</strong></span> / W<span style=\"text-decoration: underline;\"><strong>C</strong><strong>W</strong></span></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In this question, three statements are given, followed by two conclusions numbered I and II. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusion(s) logically follows/follow from the statements. <br><strong>Statements:</strong> <br>No medicine is a capsule. <br>Some capsules are tablets. <br>Some tablets are laptops. <br><strong>Conclusions:</strong> <br>I. Some capsules are laptops. <br>II. All tablets can never be medicines.</p>",
                    question_hi: "<p>2. इस प्रश्न में तीन कथन दिए गए हैं, जिसके बाद दो निष्कर्ष I और II दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्&zwj;य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, निर्धारित करें कि कौन सा/से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन:</strong> <br>कोई दवा कैप्&zwj;सूल नहीं है। <br>कुछ कैप्&zwj;सूल टैबलेट हैं। <br>कुछ टैबलेट लैपटॉप हैं। <br><strong>निष्&zwj;कर्ष:</strong> <br>I. कुछ कैप्&zwj;सूल लैपटॉप हैं। <br>II. सभी टैबलेट कभी भी दवा नहीं हो सकते।</p>",
                    options_en: [
                        "<p>Neither Conclusion I nor II follow.</p>",
                        "<p>Both Conclusions I and II follow.</p>",
                        "<p>Only Conclusion II follows.</p>",
                        "<p>Only Conclusion I follows.</p>"
                    ],
                    options_hi: [
                        "<p>न तो निष्कर्ष I अनुसरण करता है और न ही II अनुसरण करता है।</p>",
                        "<p>निष्कर्ष I और II दोनों अनुसरण करते हैं।</p>",
                        "<p>केवल निष्कर्ष II अनुसरण करता है।</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है।</p>"
                    ],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712777634.png\" alt=\"rId4\" width=\"373\" height=\"76\"><br>Only Conclusion II follows.</p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712781571.png\" alt=\"rId5\" width=\"365\" height=\"72\"><br>केवल निष्कर्ष II अनुसरण करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(<strong>NOTE: </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(27, 405, 3)<br>(23, 690, 6)</p>",
                    question_hi: "<p>3. उस समुच्चय का चयन करें जिसमें दी गई संख्याएँ आपस में उसी प्रकार संबंधित हैं, जिस प्रकार प्रश्न में दिए गए समुच्चय की संख्याएँ आपस में संबंधित हैं।<br>(<strong>ध्यान दें:</strong> संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएँ की जानी चाहिए। जैसे, 13 के मामले में - 13 पर की जाने वाली विभिन्न गणितीय संक्रियाएँ जैसे जोड़ना / घटाना / गुणा करना आदि केवल 13 के साथ की जा सकती हैं। लेकिन 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>(27, 405, 3)<br>(23, 690, 6)</p>",
                    options_en: [
                        "<p>(14, 364, 7)</p>",
                        "<p>(22, 616, 7)</p>",
                        "<p>(16, 448, 7)</p>",
                        "<p>(24, 600, 5)</p>"
                    ],
                    options_hi: [
                        "<p>(14, 364, 7)</p>",
                        "<p>(22, 616, 7)</p>",
                        "<p>(16, 448, 7)</p>",
                        "<p>(24, 600, 5)</p>"
                    ],
                    solution_en: "<p>3.(d) <strong>Logic :-</strong> (1st number &times; 3rd number) &times; 5 = 2nd number<br>(27, 405, 3) :- (27 &times; 3) &times; 5 &rArr; (81) &times; 5 = 405<br>(23, 690, 6) :- (23 &times; 6) &times; 5 &rArr; (138) &times; 5 = 690<br>Similarly,<br>(24, 600, 5) :- (24 &times; 5) &times; 5 &rArr; (120) &times; 5 = 600</p>",
                    solution_hi: "<p>3.(d) <strong>तर्क:- </strong>(पहली संख्या &times; तीसरी संख्या) &times; 5 = दूसरी संख्या<br>(27, 405, 3) :- (27 &times; 3) &times; 5 &rArr; (81) &times; 5 = 405<br>(23, 690, 6) :- (23 &times; 6) &times; 5 &rArr; (138) &times; 5 = 690<br>इसी प्रकार,<br>(24, 600, 5) :- (24 &times; 5) &times; 5 &rArr; (120) &times; 5 = 600</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. In a certain code language, \'MUTUAL\' is written as \'12121202113\', \'MARKET\' is written as \'2051118113\', how will \'MODULE\' be written in that language ?</p>",
                    question_hi: "<p>4. एक निश्चित कूट भाषा में, \'MUTUAL\' को \'12121202113\' लिखा जाता है, \'MARKET\' को \'2051118113\' लिखा जाता है, उसी कूट भाषा में \'MODULE\' को कैसे लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>1315421125</p>",
                        "<p>5122141513</p>",
                        "<p>5122141613</p>",
                        "<p>14122361522</p>"
                    ],
                    options_hi: [
                        "<p>1315421125</p>",
                        "<p>5122141513</p>",
                        "<p>5122141613</p>",
                        "<p>14122361522</p>"
                    ],
                    solution_en: "<p>4.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712784002.png\" alt=\"rId6\" width=\"149\" height=\"93\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712785959.png\" alt=\"rId7\" width=\"150\" height=\"93\"></p>\n<p>Similarly,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712786206.png\" alt=\"rId8\" width=\"145\" height=\"91\"></p>",
                    solution_hi: "<p>4.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712784002.png\" alt=\"rId6\" width=\"149\" height=\"93\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712785959.png\" alt=\"rId7\" width=\"150\" height=\"93\"></p>\n<p>इसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712786206.png\" alt=\"rId8\" width=\"145\" height=\"91\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the number from among the given options that can replace the question mark (?) in the following series.<br>1225, 1184, 1147, 1116, 1087, ?</p>",
                    question_hi: "<p>5. दिए गए विकल्पों में से उस संख्या का चयन कीजिए जो निम्नलिखित श्रृंखला में प्रश्न चिह्न (?) के स्&zwj;थान पर आ सकती है।<br>1225, 1184, 1147, 1116, 1087, ?</p>",
                    options_en: [
                        "<p>1054</p>",
                        "<p>1078</p>",
                        "<p>1072</p>",
                        "<p>1064</p>"
                    ],
                    options_hi: [
                        "<p>1054</p>",
                        "<p>1078</p>",
                        "<p>1072</p>",
                        "<p>1064</p>"
                    ],
                    solution_en: "<p>5.(d) <strong>logic:-</strong> subtracting consecutive prime numbers </p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712786962.png\" alt=\"rId10\" width=\"255\" height=\"46\"></p>",
                    solution_hi: "<p>5.(d) <strong>तर्क:- </strong>लगातार अभाज्य संख्याओं को घटाना</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712786962.png\" alt=\"rId10\" width=\"255\" height=\"46\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code language,<br>\'A + B\' means \'A is the mother of B\'<br>\'A - B\' means \'A is the husband of B\'<br>\'A <math display=\"inline\"><mo>&#215;</mo></math> B\' means \'B is the sister of A\'<br>\'A &divide; B\' means \'A is the father of B\'<br>Based on the above, how is B related to E if \'D &divide; E - C + A <math display=\"inline\"><mo>&#215;</mo></math> B\' ?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में,<br>\'A + B\' का अर्थ है\'A, B की माता है\'<br>\'A - B\' का अर्थ है\' A, B का पति है<br>\'A <math display=\"inline\"><mo>&#215;</mo></math> B\' का अर्थ है\'B, A की बहन है\'<br>\'A &divide; B\' का अर्थ है\' A, B का पिता है\'<br>उपर्युक्त के आधार पर, यदि \'\'D &divide; E - C + A <math display=\"inline\"><mo>&#215;</mo></math> B है, तो B, E से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        "<p>Son</p>",
                        "<p>Mother</p>",
                        "<p>Wife</p>",
                        "<p>Daughter</p>"
                    ],
                    options_hi: [
                        "<p>पुत्र</p>",
                        "<p>माता</p>",
                        "<p>पत्नी</p>",
                        "<p>पुत्री</p>"
                    ],
                    solution_en: "<p>6.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712787161.png\" alt=\"rId11\" width=\"183\" height=\"179\"><br>B is the daughter of E.</p>",
                    solution_hi: "<p>6.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712787161.png\" alt=\"rId11\" width=\"183\" height=\"179\"><br>B, E की बेटी है.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group ?<br>(<strong>Note :</strong> The odd letter-cluster is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>7. अंग्रेजी वर्णमाला क्रम पर आधारित, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। किस अक्षर-समूह का संबंध उस समूह से नहीं है ?<br>(<strong>नोट : </strong>असंगत अक्षर-समूह, व्यंजनों/नोंस्वरों की संख्या या इस अक्षर-समूह में उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: [
                        "<p>CGE</p>",
                        "<p>MQO</p>",
                        "<p>GKI</p>",
                        "<p>KON</p>"
                    ],
                    options_hi: [
                        "<p>CGE</p>",
                        "<p>MQO</p>",
                        "<p>GKI</p>",
                        "<p>KON</p>"
                    ],
                    solution_en: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712788844.png\" alt=\"rId12\" width=\"107\" height=\"63\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712790009.png\" alt=\"rId13\" width=\"107\" height=\"62\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712790451.png\" alt=\"rId14\" width=\"106\" height=\"62\"></p>\n<p>But,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712790612.png\" alt=\"rId15\" width=\"106\" height=\"63\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712788844.png\" alt=\"rId12\" width=\"107\" height=\"63\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712790009.png\" alt=\"rId13\" width=\"107\" height=\"62\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712790451.png\" alt=\"rId14\" width=\"106\" height=\"62\"></p>\n<p>लेकिन,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712790612.png\" alt=\"rId15\" width=\"106\" height=\"63\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. How many triangles are there in the given figure ?&nbsp;<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791029.png\" alt=\"rId16\" width=\"124\" height=\"114\"></p>",
                    question_hi: "<p>8. दी गई आकृति में कितने त्रिभुज हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791029.png\" alt=\"rId16\" width=\"124\" height=\"114\"></p>",
                    options_en: [
                        "<p>21</p>",
                        "<p>20</p>",
                        "<p>19</p>",
                        "<p>18</p>"
                    ],
                    options_hi: [
                        "<p>21</p>",
                        "<p>20</p>",
                        "<p>19</p>",
                        "<p>18</p>"
                    ],
                    solution_en: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791160.png\" alt=\"rId17\" width=\"196\" height=\"186\"><br>There are 21 triangles<br>ABD, BCH, DEF, FGH, B&rsquo;FH, HIJ, IJK,HJK, PLN, LPO, LMN, LON, PQO , QOR, RTS,RSU, TUS, USV, WXY WXC&rsquo;, XZA&rsquo; .</p>",
                    solution_hi: "<p>8.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791160.png\" alt=\"rId17\" width=\"196\" height=\"186\"><br>21 त्रिभुज हैं<br>ABD, BCH, DEF, FGH, B&rsquo;FH, HIJ, IJK,HJK, PLN, LPO, LMN, LON, PQO , QOR, RTS,RSU, TUS, USV, WXY WXC&rsquo;, XZA&rsquo; .</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Which letter-cluster will replace the question mark (?) to complete the given series ?<br>ZVRN, VSPM, RPNL, ?, JJJJ</p>",
                    question_hi: "<p>9. निम्नलिखित में से कौन सा अक्षर-समूह दी गई श्रृंखला को पूरा करने के लिए प्रश्न चिह्न (?) को प्रतिस्थापित करेगा ?<br>ZVRN, VSPM, RPNL, ?, JJJJ</p>",
                    options_en: [
                        "<p>RMLN</p>",
                        "<p>RNLL</p>",
                        "<p>RNLK</p>",
                        "<p>NMLK</p>"
                    ],
                    options_hi: [
                        "<p>RMLN</p>",
                        "<p>RNLL</p>",
                        "<p>RNLK</p>",
                        "<p>NMLK</p>"
                    ],
                    solution_en: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791395.png\" alt=\"rId18\" width=\"354\" height=\"103\"></p>",
                    solution_hi: "<p>9.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791395.png\" alt=\"rId18\" width=\"354\" height=\"103\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791498.png\" alt=\"rId19\" width=\"132\" height=\"76\"></p>",
                    question_hi: "<p>10. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति (X) उसके भाग के रूप में निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791498.png\" alt=\"rId19\" width=\"132\" height=\"76\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791609.png\" alt=\"rId20\" width=\"88\" height=\"68\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791739.png\" alt=\"rId21\" width=\"94\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792094.png\" alt=\"rId22\" width=\"101\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792269.png\" alt=\"rId23\" width=\"104\" height=\"80\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791609.png\" alt=\"rId20\" width=\"88\" height=\"68\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712791739.png\" alt=\"rId21\" width=\"94\" height=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792094.png\" alt=\"rId22\" width=\"101\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792269.png\" alt=\"rId23\" width=\"104\" height=\"80\"></p>"
                    ],
                    solution_en: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792388.png\" alt=\"rId24\" width=\"109\" height=\"82\"></p>",
                    solution_hi: "<p>10.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792388.png\" alt=\"rId24\" width=\"109\" height=\"82\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. 30 is related to 41 following certain logic. Following the same logic, 14 is related to 21. To which of the following numbers is 54 related, following the same logic ?&nbsp;<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>11. किसी निश्चित तर्क के अनुसार 30 का संबंध 41 से है। उसी तर्क के अनुसार 14 का संबंध 21 से है। उसी तर्क के अनुसार 54 निम्नलिखित में से किस संख्या से संबंधित है ?<br>(<strong>नोट: </strong>संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>63</p>",
                        "<p>53</p>",
                        "<p>59</p>",
                        "<p>69</p>"
                    ],
                    options_hi: [
                        "<p>63</p>",
                        "<p>53</p>",
                        "<p>59</p>",
                        "<p>69</p>"
                    ],
                    solution_en: "<p>11.(d)<br>(30 : 41) :- 5<sup>2</sup> + 5 : 6<sup>2</sup> + 5 = 30 : 41<br>(14 : 21) :- 3<sup>2</sup> + 5 : 4<sup>2</sup> + 5 <math display=\"inline\"><mo>=</mo></math> 14 : 21<br>Similarly<br>(54 : x) :- 7<sup>2</sup> + 5 : 8<sup>2</sup> + 5 = 54 : 69</p>",
                    solution_hi: "<p>11.(d)<br>(30 : 41) :- 5<sup>2</sup> + 5 : 6<sup>2</sup> + 5 = 30 : 41<br>(14 : 21) :- 3<sup>2</sup> + 5 : 4<sup>2</sup> + 5 <math display=\"inline\"><mo>=</mo></math> 14 : 21<br>इसी प्रकार <br>(54 : x) :- 7<sup>2</sup> + 5 : 8<sup>2</sup> + 5 = 54 : 69</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. Which figure should replace the question mark (?) if the following series were to be continued ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792546.png\" alt=\"rId25\" width=\"270\" height=\"71\"></p>",
                    question_hi: "<p>12. निम्नलिखित श्रृंखला को जारी रखने के लिए प्रश्न चिह्न (?) के स्थान पर कौन-सी आकृति आनी चाहिए ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792546.png\" alt=\"rId25\" width=\"270\" height=\"71\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792705.png\" alt=\"rId26\" width=\"77\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792804.png\" alt=\"rId27\" width=\"77\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792898.png\" alt=\"rId28\" width=\"77\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792984.png\" alt=\"rId29\" width=\"78\" height=\"76\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792705.png\" alt=\"rId26\" width=\"77\" height=\"76\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792804.png\" alt=\"rId27\" width=\"77\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792898.png\" alt=\"rId28\" width=\"78\" height=\"77\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792984.png\" alt=\"rId29\" width=\"78\" height=\"76\"></p>"
                    ],
                    solution_en: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792804.png\" alt=\"rId27\" width=\"77\" height=\"77\"></p>",
                    solution_hi: "<p>12.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712792804.png\" alt=\"rId27\" width=\"77\" height=\"77\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Three of the following numbers are alike in a certain way and one is different. Pick the odd one out.<br>(<strong>NOTE :</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>13. निम्नलिखित संख्&zwj;याओं में से तीन किसी प्रकार से एक समान हैं और एक उनसे असंगत है। उस असंगत का चयन कीजिए।<br>(नोट : गणितीय संक्रियाएं संख्&zwj;याओं को उनके घटक अंकों में तोड़े बिना पूर्ण संख्&zwj;याओं पर की जानी चाहिए। उदाहरण के लिए 13 - 13 पर की जाने वाली संक्रियाएं, जैसे जोड़ना, घटाना, गुणा करना इत्&zwj;यादि, केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और तब 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>31 &ndash; 5 &ndash; 160</p>",
                        "<p>27 &ndash; 9 &ndash; 243</p>",
                        "<p>21 &ndash; 4 &ndash; 89</p>",
                        "<p>13 &ndash; 8 &ndash; 109</p>"
                    ],
                    options_hi: [
                        "<p>31 &ndash; 5 &ndash; 160</p>",
                        "<p>27 &ndash; 9 &ndash; 243</p>",
                        "<p>21 &ndash; 4 &ndash; 89</p>",
                        "<p>13 &ndash; 8 &ndash; 109</p>"
                    ],
                    solution_en: "<p>13.(b) <strong>Logic :-</strong> (1st number &times; 2nd number) + 5 = 3rd number<br>(31 - 5 - 160) :- (31 &times; 5) + 5 &rArr; (155) + 5 = 160<br>(21 - 4 - 89) :- (21 &times; 4) + 5 &rArr; (84) + 5 = 89<br>(13 - 8 - 109) :- (13 &times; 8) + 5 &rArr; (104) + 5 = 109<br>But,<br>(27 - 9 - 243) :- (27 &times; 9) + 5 &rArr; (243) + 5 = 248(Not 243)</p>",
                    solution_hi: "<p>13.(b) <strong>तर्क :- </strong>(पहली संख्या &times; दूसरी संख्या) + 5 = तीसरी संख्या<br>(31 - 5 - 160) :- (31 &times; 5) + 5 &rArr; (155) + 5 = 160<br>(21 - 4 - 89) :- (21 &times; 4) + 5 &rArr; (84) + 5 = 89<br>(13 - 8 - 109) :- (13 &times; 8) + 5 &rArr; (104) + 5 = 109<br>लेकिन,<br>(27 - 9 - 243) :- (27 &times; 9) + 5 &rArr; (243) + 5 = 248 (243 नहीं)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, <br>\'A ! B\' means &lsquo;A is the father of B&rsquo;, <br>\'A &amp; B\' means &lsquo;A is the brother of B&rsquo;, <br>\'A * B\' means &lsquo;A is the wife of B&rsquo; and<br>\'A # B\' means &lsquo;A is the daughter of B&rsquo;. <br>Which of the following means that S is the husband\'s brother\'s wife of M ?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में,<br>\'A ! B\' का अर्थ है कि &lsquo;A, B का पिता है&rsquo;,<br>\'A &amp; B\' का अर्थ है कि &lsquo;A, B का भाई है&rsquo;,<br>\'A * B\' का अर्थ है कि &lsquo;A, B की पत्नी है&rsquo; और<br>\'A # B\' का अर्थ है कि &lsquo;A, B की पुत्री है&rsquo;। <br>निम्नलिखित में से किसका अर्थ यह है कि S, M की देवरानी/जेठानी है ?</p>",
                    options_en: [
                        "<p>S &amp; F * J ! I # M</p>",
                        "<p>S * F &amp; J ! I # M</p>",
                        "<p>S &amp; F ! J * I # M</p>",
                        "<p>S &amp; F # J ! I * M</p>"
                    ],
                    options_hi: [
                        "<p>S &amp; F * J ! I # M</p>",
                        "<p>S * F &amp; J ! I # M</p>",
                        "<p>S &amp; F ! J * I # M</p>",
                        "<p>S &amp; F # J ! I * M</p>"
                    ],
                    solution_en: "<p>14.(b)<br>After checking all the options, only option (b) satisfied.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793097.png\" alt=\"rId30\" width=\"249\" height=\"114\"></p>",
                    solution_hi: "<p>14.(b)<br>सभी विकल्पों की जांच करने के बाद केवल विकल्प (b) ही संतुष्ट करता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793097.png\" alt=\"rId30\" width=\"249\" height=\"114\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793232.png\" alt=\"rId31\" width=\"109\" height=\"100\"></p>",
                    question_hi: "<p>15. दर्पण को MN पर रखे जाने पर दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793232.png\" alt=\"rId31\" width=\"109\" height=\"100\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793343.png\" alt=\"rId32\" width=\"110\" height=\"24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793470.png\" alt=\"rId33\" width=\"109\" height=\"24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793590.png\" alt=\"rId34\" width=\"110\" height=\"24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793690.png\" alt=\"rId35\" width=\"109\" height=\"24\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793343.png\" alt=\"rId32\" width=\"110\" height=\"24\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793470.png\" alt=\"rId33\" width=\"114\" height=\"25\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793590.png\" alt=\"rId34\" width=\"119\" height=\"26\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793690.png\" alt=\"rId35\" width=\"119\" height=\"26\"></p>"
                    ],
                    solution_en: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793590.png\" alt=\"rId34\" width=\"123\" height=\"27\"></p>",
                    solution_hi: "<p>15.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793590.png\" alt=\"rId34\" width=\"123\" height=\"27\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the term from among the given options that can replace the question mark (?) in the following series.<br>MY 84, KW 80, IU 76, GS 72, ?</p>",
                    question_hi: "<p>16. दिए गए विकल्पों में से उस पद का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकता है।<br>MY 84, KW 80, IU 76, GS 72, ?</p>",
                    options_en: [
                        "<p>ER 70</p>",
                        "<p>EQ 68</p>",
                        "<p>FQ 71</p>",
                        "<p>FP 69</p>"
                    ],
                    options_hi: [
                        "<p>ER 70</p>",
                        "<p>EQ 68</p>",
                        "<p>FQ 71</p>",
                        "<p>FP 69</p>"
                    ],
                    solution_en: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793836.png\" alt=\"rId36\" width=\"333\" height=\"85\"></p>",
                    solution_hi: "<p>16.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793836.png\" alt=\"rId36\" width=\"333\" height=\"85\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. Select the correct mirror image of the given figure when the mirror is placed MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793928.png\" alt=\"rId37\" width=\"192\" height=\"54\"></p>",
                    question_hi: "<p>17. नीचे दी गई आकृति के उस सही दर्पण प्रतिबिंब को चुनिए, जो नीचे दर्शाए गए अनुसार दर्पण को MN पर रखने पर बनेगा।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712793928.png\" alt=\"rId37\" width=\"192\" height=\"54\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794022.png\" alt=\"rId38\" width=\"124\" height=\"29\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794093.png\" alt=\"rId39\" width=\"122\" height=\"27\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794196.png\" alt=\"rId40\" width=\"124\" height=\"30\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794290.png\" alt=\"rId41\" width=\"125\" height=\"35\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794022.png\" alt=\"rId38\" width=\"124\" height=\"29\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794093.png\" alt=\"rId39\" width=\"126\" height=\"28\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794196.png\" alt=\"rId40\" width=\"124\" height=\"30\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794290.png\" alt=\"rId41\" width=\"125\" height=\"35\"></p>"
                    ],
                    solution_en: "<p>17.(a) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794022.png\" alt=\"rId38\" width=\"124\" height=\"29\"></p>",
                    solution_hi: "<p>17.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794022.png\" alt=\"rId38\" width=\"124\" height=\"29\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. Which two numbers (not digits) should be interchanged to make the given equation correct ?<br>72 - 6 + (18 &divide; 4) + 17 - (22 &times; 2) = 44</p>",
                    question_hi: "<p>18. दिए गए समीकरण को सही बनाने के लिए किन दो संख्याओं (अंक नहीं) को आपस में बदला जाना चाहिए ?<br>72 - 6 + (18 &divide; 4) + 17 - (22 &times; 2) = 44</p>",
                    options_en: [
                        "<p>2 and 4</p>",
                        "<p>22 and 18</p>",
                        "<p>6 and 22</p>",
                        "<p>6 and 4</p>"
                    ],
                    options_hi: [
                        "<p>2 और 4</p>",
                        "<p>22 और 18</p>",
                        "<p>6 और 22</p>",
                        "<p>6 और 4</p>"
                    ],
                    solution_en: "<p>18.(d)<br><strong>Given: </strong>72 - 6 + (18 &divide; 4) + 17 - (22 &times; 2) = 44<br>As per the given instruction after interchanging the number 6 and 4, we get<br>72 - 4 + (18 &divide; 6) + 17 - (22 &times; 2) = 44<br>72 - 4 + 3 + 17 - 44 = 44<br>92 - 48 = 44 (satisfied)</p>",
                    solution_hi: "<p>18.(d)<br><strong>दिया गया है: </strong>72 - 6 + (18 &divide; 4) + 17 - (22 &times; 2) = 44<br>दिए गए निर्देश के अनुसार संख्या 6 और 4 को आपस में बदलने पर हमें प्राप्त होता है<br>72 - 4 + (18 &divide; 6) + 17 - (22 &times; 2) = 44<br>72 - 4 + 3 + 17 - 44 = 44<br>92 - 48 = 44 (संतुष्ट)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19.&nbsp;<br>BIRTHDAY - IBTRDHYA<br>BLAZER - LBZARE<br>REMOVE - ?<br>Using the same logic as applied for the first two pairs of terms, select the term that can replace the question mark (?) in the third pair.&nbsp;</p>",
                    question_hi: "<p>19.&nbsp;<br>BIRTHDAY - IBTRDHYA<br>BLAZER - LBZARE<br>REMOVE - ?<br>पदों के पहले दो युग्मों के लिए लागू किए गए तर्क का उपयोग करते हुए, उस पद का चयन करें जो तीसरे युग्म में प्रश्न चिह्न (?) को प्रतिस्थापित कर सकता है।&nbsp;</p>",
                    options_en: [
                        "<p>ERMOVE</p>",
                        "<p>EROMVE</p>",
                        "<p>EROMEV</p>",
                        "<p>EVOMER</p>"
                    ],
                    options_hi: [
                        "<p>ERMOVE</p>",
                        "<p>EROMVE</p>",
                        "<p>EROMEV</p>",
                        "<p>EVOMER</p>"
                    ],
                    solution_en: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794491.png\" alt=\"rId42\" width=\"152\" height=\"78\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794655.png\" alt=\"rId43\" width=\"111\" height=\"78\"></p>\n<p>Similarly,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794763.png\" alt=\"rId44\" width=\"123\" height=\"79\"></p>",
                    solution_hi: "<p>19.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794491.png\" alt=\"rId42\" width=\"152\" height=\"78\">&nbsp; ,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794655.png\" alt=\"rId43\" width=\"111\" height=\"78\"></p>\n<p>इसी प्रकार,</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794763.png\" alt=\"rId44\" width=\"123\" height=\"79\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. In a certain code language, &lsquo;complete the task&rsquo; is written as &lsquo;je tc xo&rsquo; and &lsquo;work is complete&rsquo; is coded as &lsquo;xo ig lx&rsquo;. How is &lsquo;complete&rsquo; coded in the given language ?</p>",
                    question_hi: "<p>20. एक निश्चित कूट भाषा में, \'complete the task\' को \'je tc xo\' के रूप में लिखा जाता है और \'work is complete\' को \'xo ig lx\' के रूप में लिखा जाता है। दी गई भाषा में \'complete\' को किस प्रकार लिखा जाएगा ?</p>",
                    options_en: [
                        "<p>xo</p>",
                        "<p>je</p>",
                        "<p>tc</p>",
                        "<p>lx</p>"
                    ],
                    options_hi: [
                        "<p>xo</p>",
                        "<p>je</p>",
                        "<p>tc</p>",
                        "<p>lx</p>"
                    ],
                    solution_en: "<p>20.(a) complete the task :- je tc xo&hellip;&hellip;(i)<br>work is complete :- xo ig lx&hellip;&hellip;.(ii)<br>From (i) and (ii) the code of &lsquo;complete&rsquo; = &lsquo;xo&rsquo;.</p>",
                    solution_hi: "<p>20.(a) complete the task :- je tc xo&hellip;&hellip;(i)<br>work is complete :- xo ig lx&hellip;&hellip;.(ii)<br>(i) और (ii) से \'complete\' का कोड = \'xo\'</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. The position of how many letters will change if each of the letters in the word &lsquo;CORNFLAKES&rsquo; is re-arranged in the English alphabetical order from left to right ?</p>",
                    question_hi: "<p>21. यदि शब्द &lsquo;CORNFLAKES के प्रत्येक अक्षर को अंग्रेजी वर्णमाला के क्रम में बाएँ से दाएँ पुनर्व्यवस्थित किया जाए तो कितने अक्षरों की स्थिति परिवर्तित होगी ?</p>",
                    options_en: [
                        "<p>Nine</p>",
                        "<p>Seven</p>",
                        "<p>Eight</p>",
                        "<p>Ten</p>"
                    ],
                    options_hi: [
                        "<p>नौ</p>",
                        "<p>सात</p>",
                        "<p>आठ</p>",
                        "<p>दस</p>"
                    ],
                    solution_en: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794881.png\" alt=\"rId45\" width=\"216\" height=\"87\"><br>We can see that the position of 8 letters will be changed.</p>",
                    solution_hi: "<p>21.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794881.png\" alt=\"rId45\" width=\"216\" height=\"87\"><br>हम देख सकते हैं कि 8 अक्षरों का स्थान बदल जाएगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Select the option that is related to the fifth letter-cluster in the same way as the second letter-cluster is related to the first letter-cluster and the fourth letter-cluster is related to the third letter-cluster.<br>DOY : PAK :: BQM : NCY :: ILU : ?</p>",
                    question_hi: "<p>22. उस विकल्प का चयन करें जो पाँचवें अक्षर-समूह से उसी प्रकार संबंधित है जिस प्रकार दूसरा अक्षर-समूह पहले अक्षर-समूह से संबंधित है और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है।<br>DOY : PAK :: BQM : NCY :: ILU : ?</p>",
                    options_en: [
                        "<p>TYG</p>",
                        "<p>UYG</p>",
                        "<p>TXG</p>",
                        "<p>UXG</p>"
                    ],
                    options_hi: [
                        "<p>TYG</p>",
                        "<p>UYG</p>",
                        "<p>TXG</p>",
                        "<p>UXG</p>"
                    ],
                    solution_en: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794988.png\" alt=\"rId46\" width=\"91\" height=\"88\">&nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795078.png\" alt=\"rId47\" width=\"90\" height=\"88\"><br>Similarly</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795208.png\" alt=\"rId48\" width=\"89\" height=\"85\"></p>",
                    solution_hi: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712794988.png\" alt=\"rId46\" width=\"91\" height=\"88\">&nbsp; &nbsp;,&nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795078.png\" alt=\"rId47\" width=\"90\" height=\"88\"></p>\n<p>उसी प्रकार</p>\n<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795208.png\" alt=\"rId48\" width=\"89\" height=\"85\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Identify the figure given in the options which when put in place of the question mark (?) will logically complete the series ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795320.png\" alt=\"rId49\" width=\"298\" height=\"72\"></p>",
                    question_hi: "<p>23. विकल्पों में दी गई उस आकृति की पहचान कीजिए जिसे प्रश्न चिह्न (?) के स्थान पर रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795320.png\" alt=\"rId49\" width=\"298\" height=\"72\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795429.png\" alt=\"rId50\" width=\"82\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795520.png\" alt=\"rId51\" width=\"85\" height=\"69\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795636.png\" alt=\"rId52\" width=\"82\" height=\"74\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795731.png\" alt=\"rId53\" width=\"84\" height=\"69\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795429.png\" alt=\"rId50\" width=\"82\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795520.png\" alt=\"rId51\" width=\"87\" height=\"71\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795636.png\" alt=\"rId52\" width=\"83\" height=\"75\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795731.png\" alt=\"rId53\" width=\"84\" height=\"69\"></p>"
                    ],
                    solution_en: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795429.png\" alt=\"rId50\" width=\"83\" height=\"72\"></p>",
                    solution_hi: "<p>23.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795429.png\" alt=\"rId50\" width=\"83\" height=\"72\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Three statements are given followed by three conclusions numbered I, II and III. Assuming the statements to be true, even if they seem to be at variance with commonly known facts, decide which of the conclusions logically follow(s) from the statements.<br><strong>Statements :</strong><br>All cards are postcards.<br>Some postcards are books.<br>All books are novels.<br><strong>Conclusions :</strong><br>I. Some postcards are novels.<br>II. No card is a book.<br>III. All cards are novels.</p>",
                    question_hi: "<p>24. तीन कथन और उसके बाद तीन निष्कर्ष I, II और III दिए गए हैं। कथनों को सत्य मानते हुए, भले ही वे सामान्य रूप से ज्ञात तथ्यों से भिन्न प्रतीत होते हों, तय कीजिए कि कौन-सा/से निष्कर्ष इन कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन :</strong><br>सभी कार्ड, पोस्टकार्ड हैं।<br>कुछ पोस्टकार्ड, किताबें हैं।<br>सभी किताबें, उपन्यास हैं।<br><strong>निष्कर्ष </strong>:<br>I. कुछ पोस्टकार्ड, उपन्यास हैं।<br>II. कोई भी कार्ड, किताब नहीं है।<br>III. सभी कार्ड, उपन्यास हैं।</p>",
                    options_en: [
                        "<p>Only II and III conclusion follow</p>",
                        "<p>Only conclusion III follows</p>",
                        "<p>Only I and II conclusion follow</p>",
                        "<p>Only conclusion I follows</p>"
                    ],
                    options_hi: [
                        "<p>केवल II और III निष्कर्ष अनुसरण करते हैं</p>",
                        "<p>केवल निष्कर्ष III अनुसरण करता है</p>",
                        "<p>केवल I और II निष्कर्ष अनुसरण करते हैं</p>",
                        "<p>केवल निष्कर्ष I अनुसरण करता है</p>"
                    ],
                    solution_en: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712795884.png\" alt=\"rId54\" width=\"258\" height=\"100\"><br>Only conclusion I follows</p>",
                    solution_hi: "<p>24.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712796039.png\" alt=\"rId55\" width=\"241\" height=\"93\"><br>केवल निष्कर्ष I अनुसरण करता है</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Which of the following interchanges of numbers (not digits) would make the given equation correct ?<br>2<sup>57- 33 &divide; 11 &times; 13 - 2</sup> + 3<sup>64 - 56 &divide; 8 &times; 4 - 44</sup> = 35</p>",
                    question_hi: "<p>25. दिए गए समीकरण को सही करने के लिए किन दो संख्याओं (अंक नहीं) को आपस में बदला जाना चाहिए ?<br>2<sup>57- 33 &divide; 11 &times; 13 - 2</sup> + 3<sup>64 - 56 &divide; 8 &times; 4 - 44</sup> = 35</p>",
                    options_en: [
                        "<p>2 and 4</p>",
                        "<p>56 and 57</p>",
                        "<p>57 and 64</p>",
                        "<p>33 and 44</p>"
                    ],
                    options_hi: [
                        "<p>2 और 4</p>",
                        "<p>56 और 57</p>",
                        "<p>57 और 64</p>",
                        "<p>33 और 44</p>"
                    ],
                    solution_en: "<p>25.(d)<strong> Given :- </strong>2<sup>57- 33 &divide; 11 &times; 13 - 2</sup> + 3<sup>64 - 56 &divide; 8 &times; 4 - 44</sup> = 35<br>After going through all the options, option d satisfied. After interchanging 33 and 44 we get,<br>2<sup>57 - 44&nbsp;&divide; 11 &times; 13 - 2</sup> + 3<sup>64 - 56 &divide; 8 &times; 4 - 33</sup> = 35<br>2<sup>57 - 4 &times; 13 - 2</sup> + 3<sup>64 - 28 - 33</sup> = 35<br>2<sup>57 - 52 - 2</sup> + 3<sup>64 - 61</sup> = 35<br>2<sup>3</sup> + 3<sup>3</sup> = 35<br>8 + 27 = 35<br>35 = 35<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>25.(d) <strong>दिया गया :- </strong>2<sup>57- 33 &divide; 11 &times; 13 - 2</sup> + 3<sup>64 - 56 &divide; 8 &times; 4 - 44</sup> = 35<br>सभी विकल्पों की जांच करने पर विकल्प d संतुष्ट करता है। 33 और 44 को आपस में बदलने पर हमें प्राप्त होता है,<br>2<sup>57 - 44&nbsp;&divide; 11 &times; 13 - 2</sup> + 3<sup>64 - 56 &divide; 8 &times; 4 - 33</sup> = 35<br>2<sup>57 - 4 &times; 13 - 2</sup> + 3<sup>64 - 28 - 33</sup> = 35<br>2<sup>57 - 52 - 2</sup> + 3<sup>64 - 61</sup> = 35<br>2<sup>3</sup> + 3<sup>3</sup> = 35<br>8 + 27 = 35<br>35 = 35<br>L.H.S. = R.H.S.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following options is mostly caused by overpopulation ?</p>",
                    question_hi: "<p>26. निम्नलिखित में से कौन-सा आमतौर पर अधिक जनसंख्या के कारण होता है ?</p>",
                    options_en: [
                        "<p>Land and other renewable resources are under pressure</p>",
                        "<p>Adequate natural resource storage</p>",
                        "<p>Reduction in the scarcity of water</p>",
                        "<p>Increase in the area covered by forests</p>"
                    ],
                    options_hi: [
                        "<p>भूमि और अन्य नवीकरणीय संसाधनों पर दबाव</p>",
                        "<p>पर्याप्त प्राकृतिक संसाधन भंडारण</p>",
                        "<p>जल की अप्राप्यता में कमी</p>",
                        "<p>वनों के आच्छादित क्षेत्र में वृद्धि</p>"
                    ],
                    solution_en: "<p>26.(a) As the population grows, there\'s increased demand for living space, agriculture, and resources. This leads to deforestation, uncontrolled urban expansion, and overexploitation of resources like water, soil, and forests.</p>",
                    solution_hi: "<p>26.(a) जैसे-जैसे आबादी बढ़ती है, रहने की जगह, कृषि और संसाधनों की मांग बढ़ती जाती है। इससे वनों की कटाई, अनियंत्रित शहरी विस्तार और जल, मिट्टी और जंगलों जैसे संसाधनों का अत्यधिक दोहन होता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Palladium on barium sulphate (Pd/BaSO<sub>4</sub>) is also known as:</p>",
                    question_hi: "<p>27. बेरियम सल्फेट (Pd/BaSO<sub>4</sub>) पर पैलेडियम को_______के रूप में भी जाना जाता है।</p>",
                    options_en: [
                        "<p>a Hillman reaction catalyst</p>",
                        "<p>a Cannizzaro reaction catalyst</p>",
                        "<p>the Rosenmund catalyst</p>",
                        "<p>an aldol reaction catalyst</p>"
                    ],
                    options_hi: [
                        "<p>हिलमैन अभिक्रिया उत्प्रेरक</p>",
                        "<p>कैनिजारो अभिक्रिया उत्प्रेरक</p>",
                        "<p>रेजेनमुंड उत्प्रेरक</p>",
                        "<p>ऐल्डॉल अभिक्रिया उत्प्रेरक</p>"
                    ],
                    solution_en: "<p>27.(c) <strong>the Rosenmund catalyst. </strong>The Rosenmund reduction is a hydrogenation process in which an acyl chloride is selectively reduced to an aldehyde. The reaction was named after Karl Wilhelm Rosenmund. The Rosenmund catalyst can be prepared by reduction of palladium(II) chloride solution in the presence of BaSO<sub>4</sub>. Catalyst used : Hillman reaction (a nucleophilic catalyst, such as a tertiary amine or phosphine), Cannizzaro reaction (ytterbium triflate).</p>",
                    solution_hi: "<p>27.(c)<strong> रेजेनमुंड उत्प्रेरक।</strong> रेजेनमुंड रिडक्शन एक हाइड्रोजनीकरण प्रक्रिया है जिसमें एसाइल क्लोराइड को एल्डिहाइड में अपचयित किया जाता है। इस अभिक्रिया का नाम कार्ल विल्हेम रेजेनमुंड के नाम पर रखा गया था। रेजेनमुंड उत्प्रेरक को BaSO<sub>4</sub> की उपस्थिति में पैलेडियम (II) क्लोराइड विलयन के अपचयन द्वारा तैयार किया जा सकता है। प्रयुक्त उत्प्रेरक: हिलमैन अभिक्रिया (एक न्यूक्लियोफिलिक उत्प्रेरक, जैसे कि तृतीयक अमीन या फॉस्फीन), कैनिज़ारो अभिक्रिया (यटरबियम ट्राइफ्लेट)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Which organization declared 2025 as the International Year of Glaciers\' Preservation ?</p>",
                    question_hi: "<p>28. किस संगठन ने 2025 को \'हिमनद संरक्षण का अंतर्राष्ट्रीय वर्ष\' घोषित किया है ?</p>",
                    options_en: [
                        "<p>UNESCO</p>",
                        "<p>World Meteorological Organization</p>",
                        "<p>United Nations General Assembly</p>",
                        "<p>International Union for Conservation of Nature</p>"
                    ],
                    options_hi: [
                        "<p>यूनेस्को</p>",
                        "<p>विश्व मौसम विज्ञान संगठन</p>",
                        "<p>संयुक्त राष्ट्र महासभा</p>",
                        "<p>प्रकृति संरक्षण के लिए अंतर्राष्ट्रीय संघ</p>"
                    ],
                    solution_en: "<p>28.(c) The United Nations has declared March 21 as the World Day for Glaciers to highlight the critical role of glaciers and the urgent need for their conservation amid climate change threats.</p>",
                    solution_hi: "<p>28.(c) <strong>संयुक्त राष्ट्र महासभा। </strong>संयुक्त राष्ट्र ने 21 मार्च को \'विश्व हिमनद दिवस\' घोषित किया है, ताकि हिमनदों की महत्वपूर्ण भूमिका और जलवायु परिवर्तन के खतरे के बीच उनके संरक्षण की तत्काल आवश्यकता को उजागर किया जा सके।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which party formed the government in Britain in the year 1945 ?</p>",
                    question_hi: "<p>29. वर्ष 1945 में ब्रिटेन में किस पार्टी की सरकार बनी ?</p>",
                    options_en: [
                        "<p>Democratic Party</p>",
                        "<p>Labour Party</p>",
                        "<p>Socialist Party</p>",
                        "<p>Liberal Party</p>"
                    ],
                    options_hi: [
                        "<p>डेमोक्रेटिक पार्टी</p>",
                        "<p>लेबर पार्टी</p>",
                        "<p>सोशलिस्ट पार्टी</p>",
                        "<p>लिबरल पार्टी</p>"
                    ],
                    solution_en: "<p>29.(b) <strong>Labour Party.</strong> In the 1945 general elections in Britain, the Labour Party, under the leadership of Clement Attlee, achieved a decisive victory, defeating Winston Churchill\'s Conservative Party. Democratic Party is one of the two major political parties, alongside the Republican Party, in the United States.</p>",
                    solution_hi: "<p>29.(b) <strong>लेबर पार्टी।</strong> ब्रिटेन में 1945 के आम चुनावों में, क्लेमेंट एटली के नेतृत्व में लेबर पार्टी ने विंस्टन चर्चिल की कंजर्वेटिव पार्टी को हराकर निर्णायक जीत हासिल की। ​​डेमोक्रेटिक पार्टी, संयुक्त राज्य अमेरिका में रिपब्लिकन पार्टी के साथ-साथ दो प्रमुख राजनीतिक दलों में से एक है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. In which year was the PM Ayushman Bharat Health Infrastructure Mission launched ?</p>",
                    question_hi: "<p>30. पीएम आयुष्मान भारत हेल्थ इंफ्रास्ट्रक्चर मिशन किस वर्ष शुरू किया गया था ?</p>",
                    options_en: [
                        "<p>2020</p>",
                        "<p>2022</p>",
                        "<p>2021</p>",
                        "<p>2023</p>"
                    ],
                    options_hi: [
                        "<p>2020</p>",
                        "<p>2022</p>",
                        "<p>2021</p>",
                        "<p>2023</p>"
                    ],
                    solution_en: "<p>30.(c) <strong>2021. </strong>Prime Minister Shri Narendra Modi inaugurated the PM Ayushman Bharat Health Infrastructure Mission, which aims to be one of the largest initiatives in India focused on improving healthcare infrastructure. Related Schemes and Their Launch Years : National Rural Health Mission (2005), Ayushman Bharat (2018), and National Digital Health Mission (2020).</p>",
                    solution_hi: "<p>30.(c)<strong> 2021. </strong>प्रधानमंत्री श्री नरेन्द्र मोदी ने पीएम आयुष्मान भारत हेल्थ इंफ्रास्ट्रक्चर मिशन का उद्घाटन किया, जिसका उद्देश्य हेल्थकेयर इंफ्रास्ट्रक्चर में सुधार पर केंद्रित भारत की सबसे बड़ी पहलों में से एक बनना है। संबंधित योजनाएँ एवं उनके शुभारंभ वर्ष: राष्ट्रीय ग्रामीण स्वास्थ्य मिशन (2005), आयुष्मान भारत (2018), और राष्ट्रीय डिजिटल स्वास्थ्य मिशन (2020)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. What is the title of Pratap Bhanu Mehta&rsquo;s upcoming anthology of writings, published by Juggernaut ?</p>",
                    question_hi: "<p>31. प्रताप भानु मेहता की आगामी लेख संकलन (anthology), जिसे जगरनॉट द्वारा प्रकाशित किया जाएगा, का शीर्षक क्या है ?</p>",
                    options_en: [
                        "<p>Ideas of India</p>",
                        "<p>The True Land of Promise</p>",
                        "<p>Reflections on Freedom</p>",
                        "<p>India Through My Eyes</p>"
                    ],
                    options_hi: [
                        "<p>आइडियाज़ ऑफ इंडिया</p>",
                        "<p>द ट्रू लैंड ऑफ प्रॉमिस</p>",
                        "<p>रिफ्लेक्शन्स ऑन फ्रीडम</p>",
                        "<p>इंडिया थ्रू माय आइज़</p>"
                    ],
                    solution_en: "<p>31.(b) <strong>The True Land of Promise. </strong>&lsquo;The burden of democracy&rsquo;book is written by him.</p>",
                    solution_hi: "<p>31.(b) <strong>द ट्रू लैंड ऑफ प्रॉमिस।</strong> द बर्डन ऑफ डेमोक्रेसी पुस्तक भी प्रताप भानु मेहता द्वारा लिखी गई है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following categories does Gonyaulax belong to ?</p>",
                    question_hi: "<p>32. निम्नलिखित में से कौन-सी श्रेणी गोनिओलेक्स (Gonyaulax) से संबंधित है ?</p>",
                    options_en: [
                        "<p>Euglenoids</p>",
                        "<p>Chrysophytes</p>",
                        "<p>Protozoans</p>",
                        "<p>Dinoflagellates</p>"
                    ],
                    options_hi: [
                        "<p>यूग्लीनोएड्स (Euglenoids)</p>",
                        "<p>क्राइसोफाइट्स (Chrysophytes)</p>",
                        "<p>प्रोटोजोअन्&zwj;स (Protozoans)</p>",
                        "<p>डाइनोफ्लैजलेट्स (Dinoflagellates)</p>"
                    ],
                    solution_en: "<p>32.(d) <strong>Dinoflagellates. </strong>These organisms are mainly marine, photosynthetic organisms that can appear in various colors such as yellow, green, brown, blue, or red, depending on their pigments. Euglenoids include Euglena, Chrysophytes encompass diatoms and golden algae, while Amoeba represents protozoans.</p>",
                    solution_hi: "<p>32.(d)<strong> डाइनोफ्लैजलेट्स</strong> (Dinoflagellates)। ये जीव मुख्य रूप से समुद्री, प्रकाश संश्लेषक जीव हैं जो अपने रंगद्रव्य के आधार पर पीले, हरे, भूरे, नीले या लाल जैसे विभिन्न रंगों में दिखाई दे सकते हैं। यूग्लेनोइड्स में यूग्लेना शामिल है, क्राइसोफाइट्स में डायटम और गोल्डन शैवाल शामिल हैं, जबकि अमीबा प्रोटोजोआ को दर्शाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. The organisation that Comptroller and Auditor General heads is known as ______.</p>",
                    question_hi: "<p>33. नियंत्रक और महालेखापरीक्षक जिस संगठन का प्रमुख होता है, उसे _____के नाम से जाना जाता है।</p>",
                    options_en: [
                        "<p>Indian Civil Accounts Organisation</p>",
                        "<p>Controller General of Accounts</p>",
                        "<p>Indian Audit and Accounts Department</p>",
                        "<p>Institute of Chartered Accountants of India</p>"
                    ],
                    options_hi: [
                        "<p>भारतीय नागरिक लेखा संगठन</p>",
                        "<p>लेखा महानियंत्रक</p>",
                        "<p>भारतीय लेखापरीक्षा और लेखा विभाग</p>",
                        "<p>इंस्टीट्यूट ऑफ चार्टर्ड एकाउंटेंट्स ऑफ इंडिया</p>"
                    ],
                    solution_en: "<p>33.(c) <strong>Indian Audit and Accounts Department. </strong>The Comptroller and Auditor General (CAG) of India serves as the chief guardian of the public purse. Articles 148 to 151 of the Indian Constitution detail the role and powers of the CAG, which include auditing all receipts and expenditures of both the Government of India and State Governments.</p>",
                    solution_hi: "<p>33.(c) <strong>भारतीय लेखा परीक्षा और लेखा विभाग। </strong>भारत के नियंत्रक और महालेखा परीक्षक (CAG) सार्वजनिक धन के मुख्य संरक्षक के रूप में कार्य करते हैं। भारतीय संविधान के अनुच्छेद 148 से 151 में CAG की भूमिका और शक्तियों का विवरण दिया गया है, जिसमें भारत सरकार और राज्य सरकारों दोनों की सभी प्राप्तियों और व्ययों का लेखा परीक्षण शामिल है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Who won the triple crown in the final round of the MMSC FMSCI Indian National Motorcycle Drag Racing Championship ?</p>",
                    question_hi: "<p>34. MMSC FMSCI भारतीय राष्ट्रीय मोटरसाइकिल ड्रैग रेसिंग चैंपियनशिप के फाइनल राउंड में ट्रिपल क्राउन किसने जीता ?</p>",
                    options_en: [
                        "<p>Hemanth Muddappa</p>",
                        "<p>Raghul Krishnan</p>",
                        "<p>Amit Shelar</p>",
                        "<p>Shreyas Kottary</p>"
                    ],
                    options_hi: [
                        "<p>हेमंत मुद्धप्पा</p>",
                        "<p>रघुल कृष्णन</p>",
                        "<p>अमित शेलार</p>",
                        "<p>श्रेयस कोट्टारी</p>"
                    ],
                    solution_en: "<p>34.(a) <strong>Hemanth Muddappa,</strong> known as the \"Drag King\" in Indian motorsport, secured a triple crown victory in the final round of the championship at the Madras International Circuit, extending his record to 15 National titles.</p>",
                    solution_hi: "<p>34.(a)<strong> हेमंत मुद्धप्पा। </strong>भारतीय मोटरस्पोर्ट्स के \"ड्रैग किंग\" के नाम से प्रसिद्ध हेमंत मुद्धप्पा ने मद्रास अंतर्राष्ट्रीय सर्किट में चैंपियनशिप के फाइनल राउंड में ट्रिपल क्राउन जीतकर अपने राष्ट्रीय खिताबों की संख्या 15 तक बढ़ा दी।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Taxus wallichiana Zucc (Himalayan yew) is a medicinal plant found in which of the following states ?</p>",
                    question_hi: "<p>35. टैक्सस वालिचियाना (Taxus wallichiana) ज़ुक्क (हिमालयी यू) एक औषधीय पौधा है जो निम्नलिखित में से किस राज्य में पाया जाता है ?</p>",
                    options_en: [
                        "<p>Goa</p>",
                        "<p>Jharkhand</p>",
                        "<p>Himachal Pradesh</p>",
                        "<p>Bihar</p>"
                    ],
                    options_hi: [
                        "<p>गोवा</p>",
                        "<p>झारखंड</p>",
                        "<p>हिमाचल प्रदेश</p>",
                        "<p>बिहार</p>"
                    ],
                    solution_en: "<p>35.(c) <strong>Himachal Pradesh.</strong> The Himalayan yew grows in the high-altitude forests of the Western Himalayas, especially in the states of Himachal Pradesh, Uttarakhand, and parts of Jammu &amp; Kashmir. The cool and moist environment of these regions provides the ideal conditions for this plant. This plant has traditionally been used to treat epilepsy, respiratory infections, colds, cough, asthma, and liver disorders.</p>",
                    solution_hi: "<p>35.(c) <strong>हिमाचल प्रदेश।</strong> हिमालयी यू पश्चिमी हिमालय के ऊंचे जंगलों में, खासकर हिमाचल प्रदेश, उत्तराखंड तथा जम्मू और कश्मीर के कुछ हिस्सों में उगता है। इन क्षेत्रों का ठंडा और आर्द्र वातावरण इस पौधे के लिए आदर्श परिस्थितियाँ प्रदान करता है। इस पौधे का उपयोग पारंपरिक रूप से मिर्गी, श्वसन संक्रमण, सर्दी, खांसी, अस्थमा एवं यकृत विकारों के इलाज के लिए किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Who among the following, while praising the amending feature of the Indian Constitution said that &lsquo;This variety in the amending process is wise but is rarely found&rsquo; ?</p>",
                    question_hi: "<p>36. निम्नलिखित में से किसने भारतीय संविधान की संशोधित विशेषता की प्रशंसा करते हुए कहा था कि \'संशोधन प्रक्रिया में यह विविधता बुद्धिमानी है लेकिन बहुत कम पाई जाती है\'n?</p>",
                    options_en: [
                        "<p>Granville Austin</p>",
                        "<p>Ivor Jennings</p>",
                        "<p>K C Wheare</p>",
                        "<p>HM Seervai</p>"
                    ],
                    options_hi: [
                        "<p>ग्रैनविले ऑस्टिन (Granville Austin)</p>",
                        "<p>इवोर जेनिंग्स (Ivor Jennings)</p>",
                        "<p>के. सी. व्हेयर (K C Wheare)</p>",
                        "<p>एचएम सीरवई (HM Seervai)</p>"
                    ],
                    solution_en: "<p>36.(c) <strong>K C Wheare</strong> was an Australian academic who was an expert on the constitutions of the British Commonwealth. He also described the Indian Constitution as Quasi federal. Granville Austin, a well-known scholar of the Indian Constitution, said, &ldquo;The amending process has proved itself as one of the most ably conceived aspects of the constitution&rdquo;. Ivor Jennings said that &lsquo;India is a federation with a strong centralising tendency&rsquo;.</p>",
                    solution_hi: "<p>36.(c)<strong> के. सी. व्हेयर</strong> (K C Wheare) एक ऑस्ट्रेलियाई शिक्षाविद थे जो ब्रिटिश राष्ट्रमंडल के संविधानों के विशेषज्ञ थे। उन्होंने भारतीय संविधान को अर्द्ध-संघीय बताया। भारतीय संविधान के सुप्रसिद्ध विद्वान ग्रैनविले ऑस्टिन ने कहा, \"संशोधन प्रक्रिया ने स्वयं को संविधान के सबसे सुविचारित पहलुओं में से एक साबित किया है\"। इवोर (आइवर) जेनिंग्स ने कहा था कि &lsquo;भारत एक मजबूत केंद्रीकरण प्रवृत्ति वाला संघ है।&rsquo;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Gangu bai Hangal and Prabha Atre were exponents of the _____Gharana.</p>",
                    question_hi: "<p>37. गंगू बाई हंगल और प्रभा अत्रे ______घराने की प्रतिपादक थीं।</p>",
                    options_en: [
                        "<p>Rampur</p>",
                        "<p>Jaipur</p>",
                        "<p>Kirana</p>",
                        "<p>Mewati</p>"
                    ],
                    options_hi: [
                        "<p>रामपुर</p>",
                        "<p>जयपुर</p>",
                        "<p>किराना</p>",
                        "<p>मेवाती</p>"
                    ],
                    solution_en: "<p>37.(c) <strong>Kirana. </strong>Gangu Bai Hangal was an Indian singer of the khayal genre of Hindustani classical music from Karnataka. She received the Padma Shri in 1962, the Padma Bhushan in 1971, and the Padma Vibhushan in 2002. Prabha Atre is another renowned Indian classical vocalist. She received the Padma Shri in 1990, the Padma Bhushan in 2002, and the Padma Vibhushan in 2022 and Sangeet Natak Academy Award in 1991. Abdul Karim Khan was the founder of the Kirana Gharana. Some exponents: Abdul Wahid Khan, Ashique Ali Khan, Roshan Ara Begum.</p>",
                    solution_hi: "<p>37.(c) <strong>किराना।</strong> गंगू बाई हंगल कर्नाटक की हिंदुस्तानी शास्त्रीय संगीत की ख्याल शैली की एक भारतीय गायिका थीं। उन्हें 1962 में पद्म श्री, 1971 में पद्म भूषण और 2002 में पद्म विभूषण से सम्मानित किया गया। प्रभा अत्रे एक अन्य प्रसिद्ध भारतीय शास्त्रीय गायिका हैं। जिन्हें 1990 में पद्म श्री, 2002 में पद्म भूषण और 2022 में पद्म विभूषण से सम्मानित किया गया तथा 1991 में संगीत नाटक अकादमी पुरस्कार प्राप्त हुआ। अब्दुल करीम खान किराना घराने के संस्थापक थे। कुछ प्रमुख प्रतिपादक (कलाकार): अब्दुल वाहिद खान, आशिक अली खान, रोशन आरा बेगम।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. What is the unit of measurement for optical power of the lens ?</p>",
                    question_hi: "<p>38. लेंस की ऑप्टिकल पॉवर (प्रकाशीय शक्ति) के लिए माप की इकाई क्या है ?</p>",
                    options_en: [
                        "<p>Yotta</p>",
                        "<p>Katal</p>",
                        "<p>Radian</p>",
                        "<p>Diopter</p>"
                    ],
                    options_hi: [
                        "<p>योट्टा</p>",
                        "<p>कटल</p>",
                        "<p>रेडियन</p>",
                        "<p>डायोप्टर</p>"
                    ],
                    solution_en: "<p>38.(d) <strong>Diopter. </strong>Base Quantity and SI Units: Length - Metre (m); Mass - Kilogram (kg); Time - Second (s); Electric current - Ampere (A); Thermodynamic temperature - Kelvin (K); Amount of substance - Mole (mol); Luminous intensity - Candela (cd). The prefix \"yotta\" is a metric system unit that represents a factor of 1024. The katal is the SI unit of catalytic activity. A radian is a unit of measuring angles.</p>",
                    solution_hi: "<p>38.(d) <strong>डायोप्टर।</strong> मूल मात्रकों का SI मात्रक: लंबाई - मीटर (m); द्रव्यमान - किलोग्राम (kg); समय - सेकंड (s); विद्युत धारा - एम्पीयर (A); ऊष्मागतिक ताप - केल्विन (K); पदार्थ की मात्रा - मोल (mol); ज्योति तीव्रता - कैंडेला (cd)। उपसर्ग (prefix) \"योट्टा\" एक मीट्रिक प्रणाली मात्रक है जो 1024 के गुणनखंड को दर्शाता है। कैटल उत्प्रेरक क्रियाशीलता की SI मात्रक है। रेडियन कोण मापने की मात्रक है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Who among the following was the last sultan of Delhi sultanate ?</p>",
                    question_hi: "<p>39. निम्नलिखित में से कौन दिल्ली सल्तनत का अंतिम सुल्तान था ?</p>",
                    options_en: [
                        "<p>Firoz Shah Tughlaq</p>",
                        "<p>Sikandar Lodi</p>",
                        "<p>Muhammad Bin Tughlaq</p>",
                        "<p>Ibrahim Lodi</p>"
                    ],
                    options_hi: [
                        "<p>फ़िरोज़ शाह तुग़लक</p>",
                        "<p>सिकंदर लोदी</p>",
                        "<p>मुहम्मद बिन तुगलक</p>",
                        "<p>इब्राहिम लोदी</p>"
                    ],
                    solution_en: "<p>39.(d) <strong>Ibrahim Lodi </strong>was the last Sultan of the Delhi Sultanate (1206&ndash;1526), he became Sultan in 1517 after the death of his father Sikandar Khan Lodi. The Lodi dynasty was founded by Bahlol lodi, reigning for thirty eight years. In 1526, he was defeated and killed in the Battle of Panipat by Babur, giving way to the emergence of the Mughal Empire in India.</p>",
                    solution_hi: "<p>39.(d) <strong>इब्राहिम लोदी </strong>दिल्ली सल्तनत (1206-1526) का अंतिम सुल्तान था, वह अपने पिता सिकंदर खान लोदी की मृत्यु के बाद 1517 में सुल्तान बना। लोदी वंश की स्थापना बहलोल लोदी ने की थी, जिसने अड़तीस वर्षों तक शासन किया। वह 1526 में, हुए पानीपत के युद्ध में बाबर द्वारा पराजित हुआ और मार दिया गया, जिससे भारत में मुगल साम्राज्य का उदय हुआ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. In the context of water bodies, Neap Tides occur :</p>",
                    question_hi: "<p>40. जल निकायों के संदर्भ में, लघु ज्वार भाटा कब आते हैं ?</p>",
                    options_en: [
                        "<p>when the earth, moon and sun line up in a straight line</p>",
                        "<p>when the sun and moon are on the opposite sides</p>",
                        "<p>when there is so much interference by continents</p>",
                        "<p>when the sun and moon are at right angles to each other</p>"
                    ],
                    options_hi: [
                        "<p>जब पृथ्वी, चंद्रमा और सूर्य एक सीधी रेखा में आ जाते हैं</p>",
                        "<p>जब सूर्य और चंद्रमा विपरीत दिशाओं में होते हैं</p>",
                        "<p>जब महाद्वीपों द्वारा बहुत अधिक हस्तक्षेप होता है।</p>",
                        "<p>जब सूर्य और चंद्रमा एक दूसरे के समकोण पर होते हैं</p>"
                    ],
                    solution_en: "<p>40.(d) During neap tide, gravitational and centrifugal forces are divided. The high tides of a neap tide are lower than the average high tide and the low tides of a Neap tide are higher than the average low tide. Spring tides - When the sun, the moon and the earth are in a straight line, the height of the tide will be higher. These are called spring tides, and they occur twice a month.</p>",
                    solution_hi: "<p>40.(d) लघु ज्वार के दौरान, गुरुत्वाकर्षण और अपकेंद्रीय बल विभाजित होते हैं। लघु ज्वार का उच्च ज्वार औसत उच्च ज्वार से कम होता है और लघु ज्वार का निम्न ज्वार औसत निम्न ज्वार से अधिक होता है। वसंत ज्वार - जब सूर्य, चंद्रमा और पृथ्वी एक सीधी रेखा में होते हैं, तो ज्वार की ऊंचाई अधिक होगी। इन्हें वसंत ज्वार कहा जाता है, और ये महीने में दो बार आते हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. Anna Chandy, the first Indian woman to serve as a judge at a high court was appointed in which High Court ?</p>",
                    question_hi: "<p>41. उच्च न्यायालय में न्यायाधीश के रूप में सेवाएं देने वाली पहली भारतीय महिला अन्ना चांडी किस उच्च न्यायालय में नियुक्त थीं ?</p>",
                    options_en: [
                        "<p>West Bengal</p>",
                        "<p>Kerala</p>",
                        "<p>Tamil Nadu</p>",
                        "<p>Andhra Pradesh</p>"
                    ],
                    options_hi: [
                        "<p>पश्चिम बंगाल</p>",
                        "<p>केरल</p>",
                        "<p>तमिलनाडु</p>",
                        "<p>आंध्र प्रदेश</p>"
                    ],
                    solution_en: "<p>41.(b) <strong>Kerala.</strong> She was the first female judge in India, the first woman to become a judge of a High Court. She became the founder and editor of the journal (Shreemati) in which she supported women\'s rights. Justice Leila Seth was the first woman to be Chief Justice of a High Court. Fathima Beevi is the first female judge of the Supreme Court of India.</p>",
                    solution_hi: "<p>41.(b) <strong>केरल।</strong> वह भारत की पहली महिला न्यायाधीश थीं, तथा उच्च न्यायालय की न्यायाधीश बनने वाली पहली महिला थीं। वह पत्रिका (श्रीमती) की संस्थापक और संपादक बनीं, जिसमें उन्होंने महिलाओं के अधिकारों का समर्थन किया। न्यायमूर्ति लीला सेठ उच्च न्यायालय की मुख्य न्यायाधीश बनने वाली पहली महिला थीं। फातिमा बीवी भारत के सर्वोच्च न्यायालय की पहली महिला न्यायाधीश हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. What do we mean by Allen&rsquo;s rule ?</p>",
                    question_hi: "<p>42. एलन के नियम से हमारा क्या तात्पर्य है ?</p>",
                    options_en: [
                        "<p>Mammals from colder climates generally have shorter ears and limbs to minimise heat loss.</p>",
                        "<p>Desert lizards lack the physiological ability that mammals have to deal with the high temperatures of their habitat.</p>",
                        "<p>Some plants have no leaves &ndash; they are reduced to spines &ndash; and the photosynthetic function is taken over by the flattened stems.</p>",
                        "<p>The body compensates low oxygen availability by increasing red blood cell production, decreasing the binding affinity of haemoglobin and by increasing the breathing rate.</p>"
                    ],
                    options_hi: [
                        "<p>ठंडी जलवायु वाले स्तनधारियों के कान और अंग (भुजा/टांग) आमतौर पर छोटे होते हैं ताकि ऊष्मा की हानि न्यूनतम हो।</p>",
                        "<p>रेगिस्तानी छिपकलियों में शारीरिक क्षमता की कमी होती है क्योंकि स्तनधारियों को अपने आवास के उच्च तापमान से निपटना पड़ता है।</p>",
                        "<p>कुछ पौधों में पत्ते नहीं होते हैं - वे कांटों में परिवर्तित हो जाते हैं - और प्रकाश संश्लेषक कार्य चपटे तनों द्वारा होता है।</p>",
                        "<p>शरीर लाल रक्त कोशिकाओं के उत्पादन को बढ़ाकर, हीमोग्लोबिन की बाध्यकारी सादृश्यता को कम करके और सांस लेने की दर को बढ़ाकर कम ऑक्सीजन की उपलब्धता की भरपाई करता है।</p>"
                    ],
                    solution_en: "<p>42.(a) Allen\'s rule is an ecogeographical rule formulated by Joel Asaph Allen in 1877. It states that the body surface-area-to-volume ratio for homeothermic animals varies with the average temperature of the habitat to which they are adapted (i.e. the ratio is low in cold climates and high in hot climates).</p>",
                    solution_hi: "<p>42.(a) एलन का नियम एक पारिस्थितिक-भौगोलिक नियम है जिसे जोएल असाफ एलन ने 1877 में प्रतिपादित किया था। यह बताता है कि होमोथर्मिक जानवरों के लिए शरीर की सतह-क्षेत्रफल-से-आयतन का अनुपात उस आवास के औसत तापमान के साथ बदलता रहता है जिसके लिए वे अनुकूलित होते हैं (यानी यह अनुपात ठंडे मौसम में कम और गर्म मौसम में अधिक होता है)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Which of the following Constitutions inspired the law-making process in India ?</p>",
                    question_hi: "<p>43. निम्नलिखित में से किस संविधान ने भारत में कानून बनाने की प्रक्रिया को प्रेरित किया ?</p>",
                    options_en: [
                        "<p>Irish Constitution</p>",
                        "<p>British Constitution</p>",
                        "<p>US Constitution</p>",
                        "<p>Canadian Constitution</p>"
                    ],
                    options_hi: [
                        "<p>आयरलैंड के संविधान</p>",
                        "<p>ब्रिटेन के संविधान</p>",
                        "<p>अमेरिका के संविधान</p>",
                        "<p>कनाडा के संविधान</p>"
                    ],
                    solution_en: "<p>43.(b) <strong>British Constitution. </strong>Borrowed features of Indian Constitution: British - Parliamentary government, Rule of Law, legislative procedure, single citizenship, cabinet system, prerogative writs. US - Fundamental rights, independence of judiciary, judicial review, impeachment of the President. Canada - Federation with a strong Centre, vesting of residuary powers in the Centre, appointment of state Governors by the Centre. Irish - Directive Principles of State Policy, nomination of members to Rajya Sabha and method of election of President.</p>",
                    solution_hi: "<p>43.(b) <strong>ब्रिटेन के संविधान। </strong>भारतीय संविधान में अन्य देशों से ली गई विशेषताएँ: ब्रिटिश - संसदीय सरकार, विधि का शासन, विधायी प्रक्रिया, एकल नागरिकता, कैबिनेट प्रणाली, विशेषाधिकार रिट। अमेरिका - मौलिक अधिकार, न्यायपालिका की स्वतंत्रता, न्यायिक समीक्षा, राष्ट्रपति पर महाभियोग। कनाडा - एक मजबूत केंद्र वाला संघ, केंद्र में अवशिष्ट शक्तियों का निहित होना, केंद्र द्वारा राज्य के राज्यपालों की नियुक्ति। आयरलैंड- राज्य के नीति निर्देशक सिद्धांत, राज्यसभा के सदस्यों का नामांकन और राष्ट्रपति के चुनाव की विधि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. How many players are there on each side of the Kabaddi Team in the field ?</p>",
                    question_hi: "<p>44. मैदान में कबड्डी टीम के प्रत्येक पाले में कितने खिलाड़ी होते हैं ?</p>",
                    options_en: [
                        "<p>9</p>",
                        "<p>7</p>",
                        "<p>8</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>9</p>",
                        "<p>7</p>",
                        "<p>8</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>44.(b)<strong> 7.</strong> Sports and Number of Players in Each Team : Cricket (11), Hockey (11), Football (11), Basketball (5), Volleyball (6), Water Polo (7), Kho-Kho (9), Rugby (15).</p>",
                    solution_hi: "<p>44.(b) <strong>7.</strong> खेल और प्रत्येक टीम में खिलाड़ियों की संख्या : क्रिकेट (11), हॉकी (11), फुटबॉल (11), बास्केटबॉल (5), वॉलीबॉल (6), वाटर पोलो (7), खो-खो (9), रग्बी (15)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. The Mahalanobis model was the basis for which Five-Year Plan ?</p>",
                    question_hi: "<p>45. महालनोबिस मॉडल किस पंचवर्षीय योजना का आधार था ?</p>",
                    options_en: [
                        "<p>Fourth Five-Year Plan</p>",
                        "<p>Second Five-Year Plan</p>",
                        "<p>Sixth Five-Year Plan</p>",
                        "<p>First Five-Year Plan</p>"
                    ],
                    options_hi: [
                        "<p>चौथी पंचवर्षीय योजना</p>",
                        "<p>दूसरी पंचवर्षीय योजना</p>",
                        "<p>छठी पंचवर्षीय योजना</p>",
                        "<p>पहली पंचवर्षीय योजना</p>"
                    ],
                    solution_en: "<p>45.(b) <strong>Second Five-Year Plan</strong> (1956 - 61): Target Growth rate - 4.5%. Actual Growth rate - 4.3%. The Plan Focussed on rapid industrialization- heavy &amp; basic industries. Advocated huge imports through foreign loans.</p>",
                    solution_hi: "<p>45.(b) <strong>दूसरी पंचवर्षीय योजना</strong> (1956 - 61): लक्ष्य वृद्धि दर - 4.5%, वास्तविक वृद्धि दर - 4.3%। यह योजना तीव्र औद्योगिकीकरण (भारी और बुनियादी उद्योग) पर केंद्रित थी। विदेशी ऋणों के माध्यम से भारी आयात का समर्थन किया गया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Which of the following is an example of public sector industry in India ?</p>",
                    question_hi: "<p>46. निम्नलिखित में से कौन-सा विकल्प भारत में सार्वजनिक क्षेत्र के उद्योग का एक उदाहरण है ?</p>",
                    options_en: [
                        "<p>Bharat Heavy Electricals Limited</p>",
                        "<p>Bajaj Auto</p>",
                        "<p>Dabur</p>",
                        "<p>Tata Iron Steel Company</p>"
                    ],
                    options_hi: [
                        "<p>भारत हेवी इलेक्ट्रिकल्स लिमिटेड</p>",
                        "<p>बजाज ऑटो</p>",
                        "<p>डाबर</p>",
                        "<p>टाटा आयरन स्टील कंपनी</p>"
                    ],
                    solution_en: "<p>46.(a) <strong>Bharat Heavy Electricals Limited. </strong>The public sector is the part of the economy that is owned and managed by the government, including government-controlled enterprises. BHEL is a government-owned company that specializes in manufacturing electrical equipment. It was established in 1964. Other PSUs: Steel Authority of India Ltd (SAIL), ONGC, and OIL etc.</p>",
                    solution_hi: "<p>46.(a) <strong>भारत हेवी इलेक्ट्रिकल्स लिमिटेड।</strong> सार्वजनिक क्षेत्र अर्थव्यवस्था का वह हिस्सा है जिसका स्वामित्व और प्रबंधन सरकार के पास होता है, जिसमें सरकार द्वारा नियंत्रित उद्यम भी शामिल हैं। BHEL एक सरकारी स्वामित्व वाली कंपनी है जो विद्युत के उपकरण बनाने में परिपूर्ण है। इसकी स्थापना 1964 में की गई थी। अन्य सार्वजनिक क्षेत्र के उपक्रम : स्टील अथॉरिटी ऑफ इंडिया लिमिटेड (SAIL), ONGC और OIL आदि।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. What are the five Fs of indirect transmission ?</p>",
                    question_hi: "<p>47. अप्रत्यक्ष संचरण के पांच F कौन-से हैं ?&nbsp;</p>",
                    options_en: [
                        "<p>Flies, fingers, fomites, food and fluid</p>",
                        "<p>Fruit, fingers, flu, food and fluid</p>",
                        "<p>Flies, fingers, friends, food and fruit</p>",
                        "<p>Flies, fingers, fomites, food and fruit</p>"
                    ],
                    options_hi: [
                        "<p>Flies (मक्खियां), fingers (उंगलियां), fomites (फोमाइट), food (भोजन) और fluid (तरल)</p>",
                        "<p>fruit (फल), fingers (उंगलियां), flu (फ्लू), food (भोजन) और fluid (तरल)</p>",
                        "<p>Flies (मक्खियां), fingers (उंगलियां), friends (मित्र), food (भोजन) और fruit (फल)</p>",
                        "<p>Flies (मक्खियां), fingers (उंगलियां), fomites (फोमाइट), food (भोजन) और fruit (फल)</p>"
                    ],
                    solution_en: "<p>47.(a) <strong>Flies, fingers, fomites, food and fluid. </strong>Indirect transmission of communicable diseases occurs through various means, commonly referred to as the \"5Fs\" : flies, fingers, fomites (objects like towels and handkerchiefs that can carry infections), food, and fluids. Some diseases can spread through contaminated water, food, ice, blood, and even body tissues or organs. Examples of such diseases include typhoid, diarrhea, polio, intestinal parasites, and infective hepatitis.</p>",
                    solution_hi: "<p>47.(a) <strong>Flies (मक्खियां), fingers (उंगलियां), fomites (फोमाइट), food (भोजन) और fluid (तरल)। </strong>संक्रामक रोगों का अप्रत्यक्ष संचरण विभिन्न माध्यमों से होता है, जिन्हें आमतौर पर \"5F\" कहा जाता है: मक्खियाँ, उंगलियाँ, फोमाइट्स (तौलिए और रूमाल जैसी वस्तुएँ जो संक्रमण फैला सकती हैं), भोजन और तरल पदार्थ। कुछ बीमारियाँ दूषित जल, भोजन, बर्फ, रक्त और यहाँ तक कि शरीर के ऊतकों या अंगों से भी फैल सकती हैं। ऐसी बीमारियों के उदाहरणों में टाइफाइड, डायरिया, पोलियो, आंतों के परजीवी और संक्रामक हेपेटाइटिस शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Match the points under Column A with those under Column B.<br><strong id=\"docs-internal-guid-612c36b0-7fff-057c-c8fd-5a3ebac6dec3\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdtU0KFS6omfN9mvJuFNwJZ6LvAu87DqxL_0fa2ZDnLF--7Jg1SsjpPjlN2FA8p9UscyCVG1sfiObhZqvbm9hjh6OXxDACpeDFLq5jfutbpIS7AqPG1ZA7v72d20cLtr_HFbgep?key=43mLuonIz8YB9syH1WAZMUHj\" width=\"297\" height=\"136\"></strong></p>",
                    question_hi: "<p>48. कॉलम A में दिए गए बिंदुओं का कॉलम B के बिंदुओं से मिलान कीजिए।<br><strong id=\"docs-internal-guid-96d810f8-7fff-8b9d-f45e-48e10a78f8aa\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfrNLVxLLruDjsJPUudk4bzPR4BAabZ6QYqjjqzQAJIXfG_Sw38UOWQQVHLwgKQIrRY4wdsrfHgjqpkUoeCwviIMfSNWvwGgeJItM44Sq_-tC1yJJblgs9iKOsYsMPpVqX6UWU3?key=43mLuonIz8YB9syH1WAZMUHj\" width=\"449\" height=\"134\"></strong></p>",
                    options_en: [
                        "<p>i-d, ii-c, iii-b, iv-a</p>",
                        "<p>i-b, ii-a, iii-c, iv-d</p>",
                        "<p>i-a, ii-c, iii-b, iv-d</p>",
                        "<p>i-a, ii-b, iii-c, iv-d</p>"
                    ],
                    options_hi: [
                        "<p>i-d, ii-c, iii-b, iv-a</p>",
                        "<p>i-b, ii-a, iii-c, iv-d</p>",
                        "<p>i-a, ii-c, iii-b, iv-d</p>",
                        "<p>i-a, ii-b, iii-c, iv-d</p>"
                    ],
                    solution_en: "<p>48.(a)<strong> i-d, ii-c, iii-b, iv-a. </strong>Annelida (Latin, annulus meaning \"little ring\") includes animals that can live in water (both marine and freshwater) or on land. They may be free-living or, in some cases, parasitic. Examples include Nereis, and Hirudinaria (blood-sucking leech). Arthropoda : The largest phylum in the animal kingdom and includes insects. Examples include Scorpion and Prawn. Echinodermata : These animals have an endoskeleton of calcareous ossicles. Examples include Antedon (Sea lily), and Cucumaria (Sea cucumber). Platyhelminthes : They have a dorso-ventrally flattened body. Examples include Fasciola (Liver fluke).</p>",
                    solution_hi: "<p>48.(a)<strong> i-d, ii-c, iii-b, iv-a. </strong>एनेलिडा (लैटिन में, एनलस का अर्थ है \"छोटी अंगूठी\") में वे जन्तु शामिल हैं जो जल (समुद्री और स्वच्छ जल दोनों) या स्थल दोनों पर रह सकते हैं। वे स्वतंत्र रूप से रहने वाले या, कुछ स्थितियों में, परजीवी हो सकते हैं। उदाहरणों में नेरीस और हिरुडिनेरिया (रक्त चूसने वाली जोंक) शामिल हैं। संधिपाद (आर्थ्रोपोडा): जन्तु जगत में सबसे बड़ा संघ और इसमें कीड़े शामिल हैं। उदाहरणों में बिच्छू और झींगा शामिल हैं। इकाइनोडर्मेटा: इन जंतुओं में कैल्केरियस अस्थियों का एक अंतःकंकाल होता है। उदाहरणों में एंटेडन (समुद्री लिली) और कुकुमेरिया (समुद्री ककड़ी) शामिल हैं। प्लेटिहेल्मिन्थेस: इनका शरीर पृष्ठ-अधरीय रूप से चपटा होता है। उदाहरणों में फैसिओला (लिवर फ्लूक) शामिल हैं।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. In the state legislative assembly, the Money bill can be introduced with the prior permission of ______.</p>",
                    question_hi: "<p>49. राज्य विधान सभा में, धन विधेयक को ______ की पूर्व अनुमति से पेश किया जा सकता है।</p>",
                    options_en: [
                        "<p>Governor</p>",
                        "<p>Auditor General</p>",
                        "<p>Finance Minister</p>",
                        "<p>CAG</p>"
                    ],
                    options_hi: [
                        "<p>राज्यपाल</p>",
                        "<p>महालेखा परीक्षक</p>",
                        "<p>वित्त मंत्री</p>",
                        "<p>भारत के नियन्त्रक एवं महालेखा परीक्षक (CAG)</p>"
                    ],
                    solution_en: "<p>49.(a) <strong>Governor. </strong>Money Bills in State Legislatures : A Money Bill is a special type of financial bill that deals with matters like taxation, borrowing, or expenditure from the state\'s Consolidated Fund. Article 198 of the Constitution of India deals with the special procedure for money bills in state legislatures. Article 148 : Provision for the appointment of the Comptroller and Auditor General of India (CAG) by the President.</p>",
                    solution_hi: "<p>49.(a) <strong>राज्यपाल। </strong>राज्य विधानमंडलों में धन विधेयक: धन विधेयक एक विशेष प्रकार का वित्तीय विधेयक होता है जो राज्य के समेकित कोष से कराधान, उधार या व्यय जैसे मामलों से संबंधित होता है। भारत के संविधान का अनुच्छेद 198 राज्य विधानमंडलों में धन विधेयकों के लिए विशेष प्रक्रिया से संबंधित है। अनुच्छेद 148: राष्ट्रपति द्वारा भारत के नियंत्रक और महालेखा परीक्षक (CAG) की नियुक्ति का प्रावधान करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. What is the name of the Joint Military Exercise between the Indian Army (IA) and the Malaysian Army that commenced in Pahang district, Malaysia, from December 2 to 15, 2024 ?</p>",
                    question_hi: "<p>50. दिसंबर 2024 में भारत- मलेशिया संयुक्त सैन्य अभ्यास का नाम क्या था जो पहांग जिले, मलेशिया में 2 से 15 दिसंबर तक आयोजित हुआ था ?</p>",
                    options_en: [
                        "<p>VINBAX</p>",
                        "<p>IMBEX</p>",
                        "<p>Garuda Shakti</p>",
                        "<p>Harimau Shakti</p>"
                    ],
                    options_hi: [
                        "<p>VINBAX</p>",
                        "<p>IMBEX</p>",
                        "<p>गरुड़ शक्ति</p>",
                        "<p>हरिमाऊ शक्ति</p>"
                    ],
                    solution_en: "<p>50.(d) <strong>Harimau Shakti.</strong> The 4th edition of the India-Malaysia Joint Military Exercise &lsquo;Harimau Shakti&rsquo; commenced at Bentong Camp in Pahang district, Malaysia, on December 2, 2024. The exercise, involving 78 personnel from the MAHAR Regiment and 123 personnel from the Royal Malaysian Regiment, aims to enhance defense cooperation and bilateral ties through cross-training and simulated exercises.</p>",
                    solution_hi: "<p>50.(d) <strong>हरिमाऊ शक्ति।</strong> भारत-मलेशिया संयुक्त सैन्य अभ्यास &lsquo;हरिमाऊ शक्ति&rsquo; का चौथा संस्करण 2 दिसंबर 2024 को पहांग जिले, मलेशिया में बेंटोंग कैम्प में शुरू हुआ। यह अभ्यास दोनों देशों के बीच रक्षा सहयोग और द्विपक्षीय संबंधों को बढ़ावा देने के उद्देश्य से आयोजित किया गया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. Aman has certain number of notes of denomination ₹20 and ₹10 which amount to ₹390. If the numbers of notes of each kind are interchanged, then the new amount is ₹90 less than before. Find the number of notes of ₹20 denomination.</p>",
                    question_hi: "<p>51. अमन के पास निश्चित संख्या में ₹20 और ₹10 मूल्य-वर्ग के नोट हैं, जिनकी राशि ₹390 है। यदि प्रत्येक प्रकार के नोटों की संख्याएँ परस्पर बदल दी जाएँ तो नई राशि पहले की तुलना में ₹90 कम हो जाती है। ₹20 मूल्य-वर्ग के नोटों की संख्या ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>16</p>",
                        "<p>12</p>",
                        "<p>15</p>",
                        "<p>14</p>"
                    ],
                    options_hi: [
                        "<p>16</p>",
                        "<p>12</p>",
                        "<p>15</p>",
                        "<p>14</p>"
                    ],
                    solution_en: "<p>51.(a) Let the number of notes ₹20 and ₹10 be <math display=\"inline\"><mi>x</mi></math> and y respectively,<br>According to the question,<br>20<math display=\"inline\"><mi>x</mi></math> + 10y = ₹ 390 &hellip;&hellip;.(i)<br>10<math display=\"inline\"><mi>x</mi></math> + 20y = ₹ 300 &hellip;&hellip;.(ii)<br>Add eq . (i) and (ii)<br>30<math display=\"inline\"><mi>x</mi></math> + 30y = 690<br><math display=\"inline\"><mi>x</mi></math> + y = 23 <br><math display=\"inline\"><mi>y</mi></math> = 23 - x<br>Put the value of y in eq . (i)<br>20<math display=\"inline\"><mi>x</mi></math> + 230 - 10x = 390<br>10<math display=\"inline\"><mi>x</mi></math> = 160 &rArr; x = 16</p>",
                    solution_hi: "<p>51.(a) माना ₹20 और ₹10 के नोटों की संख्या क्रमशः <math display=\"inline\"><mi>x</mi></math> और y है,<br>प्रश्न के अनुसार,<br>20<math display=\"inline\"><mi>x</mi></math> + 10y = ₹ 390 &hellip;&hellip;.(i)<br>10<math display=\"inline\"><mi>x</mi></math> + 20y = ₹ 300 &hellip;&hellip;.(ii)<br>समीकरण (i) और (ii) को जोड़ने पर,<br>30<math display=\"inline\"><mi>x</mi></math> + 30y = 690<br><math display=\"inline\"><mi>x</mi></math> + y = 23 <br><math display=\"inline\"><mi>y</mi></math> = 23 - x<br>समीकरण (i) में y का मान रखने पर,<br>20<math display=\"inline\"><mi>x</mi></math> + 230 - 10x = 390<br>10<math display=\"inline\"><mi>x</mi></math> = 160 &rArr; x = 16</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. Simplify the following expression:<br>(x + y)(x - y)(x<sup>2</sup> + y<sup>2</sup>)(x<sup>4</sup> + y<sup>4</sup>)(x<sup>8</sup> + y<sup>8</sup>)(x<sup>16</sup> + y<sup>16</sup>)</p>",
                    question_hi: "<p>52. निम्नलिखित व्यंजक को सरल कीजिए: <br>(x + y)(x - y)(x<sup>2</sup> + y<sup>2</sup>)(x<sup>4</sup> + y<sup>4</sup>)(x<sup>8</sup> + y<sup>8</sup>)(x<sup>16</sup> + y<sup>16</sup>)</p>",
                    options_en: [
                        "<p>x<sup>32</sup> - y<sup>32</sup></p>",
                        "<p>x<sup>128</sup> - y<sup>128</sup></p>",
                        "<p>x<sup>64</sup> - y<sup>64</sup></p>",
                        "<p>x<sup>256</sup> - y<sup>28</sup></p>"
                    ],
                    options_hi: [
                        "<p>x<sup>32</sup> - y<sup>32</sup></p>",
                        "<p>x<sup>128</sup> - y<sup>128</sup></p>",
                        "<p>x<sup>64</sup> - y<sup>64</sup></p>",
                        "<p>x<sup>256</sup> - y<sup>28</sup></p>"
                    ],
                    solution_en: "<p>52.(a)<br><strong>Formula used :</strong> <br>a<sup>2</sup> - b<sup>2</sup> = (a - b)(a + b)<br>(x + y)(x - y)(x<sup>2</sup> + y<sup>2</sup>)(x<sup>4</sup> + y<sup>4</sup>)(x<sup>8</sup> + y<sup>8</sup>)(x<sup>16</sup> + y<sup>16</sup>)<br>(x<sup>2</sup> - y<sup>2</sup>)(x<sup>2</sup> + y<sup>2</sup>)(x<sup>4</sup> + y<sup>4</sup>)(x<sup>8</sup> + y<sup>8</sup>)(x<sup>16</sup> + y<sup>16</sup>)<br>(x<sup>4</sup> - y<sup>4</sup>)(x<sup>4</sup> + y<sup>4</sup>)(x<sup>8</sup> + y<sup>8</sup>)(x<sup>16</sup> + y<sup>16</sup>)<br>(x<sup>8</sup> - y<sup>8</sup>)(x<sup>8</sup> + y<sup>8</sup>)(x<sup>16</sup> + y<sup>16</sup>)<br>(x<sup>16</sup> - y<sup>16</sup>)(x<sup>16</sup> + y<sup>16</sup>) = x<sup>32</sup> - y<sup>32</sup></p>",
                    solution_hi: "<p>52.(a)<br><strong>प्रयुक्त सूत्र:</strong> <br>a<sup>2</sup> - b<sup>2</sup> = (a - b)(a + b)<br>(x + y)(x - y)(x<sup>2</sup> + y<sup>2</sup>)(x<sup>4</sup> + y<sup>4</sup>)(x<sup>8</sup> + y<sup>8</sup>)(x<sup>16</sup> + y<sup>16</sup>)<br>(x<sup>2</sup> - y<sup>2</sup>)(x<sup>2</sup> + y<sup>2</sup>)(x<sup>4</sup> + y<sup>4</sup>)(x<sup>8</sup> + y<sup>8</sup>)(x<sup>16</sup> + y<sup>16</sup>)<br>(x<sup>4</sup> - y<sup>4</sup>)(x<sup>4</sup> + y<sup>4</sup>)(x<sup>8</sup> + y<sup>8</sup>)(x<sup>16</sup> + y<sup>16</sup>)<br>(x<sup>8</sup> - y<sup>8</sup>)(x<sup>8</sup> + y<sup>8</sup>)(x<sup>16</sup> + y<sup>16</sup>)<br>(x<sup>16</sup> - y<sup>16</sup>)(x<sup>16</sup> + y<sup>16</sup>) = x<sup>32</sup> - y<sup>32</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. In two successive years, 75 and 50 employees of a company appeared at the departmental examination. Respectively, 84% and 52% of them passed. The average rate of pass percentage is:</p>",
                    question_hi: "<p>53. दो क्रमिक वर्षों में एक कंपनी के 75 और 50 कर्मचारी विभागीय परीक्षा में उपस्थित हुए। उनमें से क्रमशः 84% और 52% उत्तीर्ण हुए। उत्तीर्ण प्रतिशत की औसत दर कितनी है ?</p>",
                    options_en: [
                        "<p>71%</p>",
                        "<p>41%</p>",
                        "<p>71<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>",
                        "<p>41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>71%</p>",
                        "<p>41%</p>",
                        "<p>71<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>",
                        "<p>41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>53.(c)<br>Average rate of pass % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>84</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>75</mn><mo>+</mo><mfrac><mn>52</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>50</mn></mrow><mrow><mn>75</mn><mo>+</mo><mn>50</mn></mrow></mfrac></math> &times; 100<br>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>21</mn><mo>&#215;</mo><mn>3</mn><mo>+</mo><mn>26</mn></mrow><mn>125</mn></mfrac></math> &times; 100&nbsp;</p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>63</mn><mo>+</mo><mn>26</mn></mrow><mn>125</mn></mfrac></math> &times; 100</p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>89</mn><mn>5</mn></mfrac></math> &times; 4 = 71<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>53.(c)<br>उत्तीर्ण होने की औसत दर % = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mfrac><mn>84</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>75</mn><mo>+</mo><mfrac><mn>52</mn><mn>100</mn></mfrac><mo>&#215;</mo><mn>50</mn></mrow><mrow><mn>75</mn><mo>+</mo><mn>50</mn></mrow></mfrac></math> &times; 100</p>\n<p>= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>21</mn><mo>&#215;</mo><mn>3</mn><mo>+</mo><mn>26</mn></mrow><mn>125</mn></mfrac></math> &times; 100&nbsp;</p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>63</mn><mo>+</mo><mn>26</mn></mrow><mn>125</mn></mfrac></math> &times; 100</p>\n<p>=&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>89</mn><mn>5</mn></mfrac></math> &times; 4 = 71<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Half the perimeter of a rectangular garden, whose length is 8 cm more than its width, is 42 cm. Find the area of the rectangular garden.</p>",
                    question_hi: "<p>54. एक आयताकार बगीचे, जिसकी लंबाई इसकी चौड़ाई से 8 cm अधिक है, का आधा परिमाप 42 cm है। आयताकार बगीचे का क्षेत्रफल ज्ञात करें।</p>",
                    options_en: [
                        "<p>524 cm<sup>2</sup></p>",
                        "<p>425 cm<sup>2</sup></p>",
                        "<p>254 cm<sup>2</sup></p>",
                        "<p>542 cm<sup>2</sup></p>"
                    ],
                    options_hi: [
                        "<p>524 cm<sup>2</sup></p>",
                        "<p>425 cm<sup>2</sup></p>",
                        "<p>254 cm<sup>2</sup></p>",
                        "<p>542 cm<sup>2</sup></p>"
                    ],
                    solution_en: "<p>54.(b)<br>Let the width of rectangular garden be x<br>Then, length = (x&nbsp;+ 8) cm<br>Perimeter of garden = 42 &times; 2 = 84 cm<br>2(x&nbsp;+ x + 8) = 84<br>2(2x&nbsp;+ 8) = 84<br>2x&nbsp;+ 8 = 42<br>x + 4 = 21<br><math display=\"inline\"><mi>x</mi></math> = 17<br>So, the area of rectangular garden =17 &times; 25 = 425 cm<sup>2</sup></p>",
                    solution_hi: "<p>54.(b)<br>माना आयताकार बगीचे की चौड़ाई x&nbsp;है<br>तो, लंबाई = (x&nbsp;+ 8) cm<br>बगीचे का परिमाप = 42 &times; 2 = 84 cm<br>2(x&nbsp;+ x + 8) = 84<br>2(2x&nbsp;+ 8) = 84<br>2x&nbsp;+ 8 = 42<br>x + 4 = 21<br><math display=\"inline\"><mi>x</mi></math> = 17<br>अत: आयताकार बगीचे का क्षेत्रफल =17 &times; 25 = 425 cm<sup>2</sup></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. Find the mode of the data 2, 2, 3, 5, 15, 15, 15, 20, 21, 23, 25, 15, 23, 25.</p>",
                    question_hi: "<p>55. आँकड़ों 2, 2, 3, 5, 15, 15, 15, 20, 21, 23, 25, 15, 23, 25 का बहुलक ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>21</p>",
                        "<p>25</p>",
                        "<p>23</p>",
                        "<p>15</p>"
                    ],
                    options_hi: [
                        "<p>21</p>",
                        "<p>25</p>",
                        "<p>23</p>",
                        "<p>15</p>"
                    ],
                    solution_en: "<p>55.(d)<br>2 , 2 , 3 , 5 , 15 , 15 , 15 , 15 , 20 , 21 , 23 , 23 , 25 , 25<br>Mode = highest frequency of number = 15</p>",
                    solution_hi: "<p>55.(d)<br>2 , 2 , 3 , 5 , 15 , 15 , 15 , 15 , 20 , 21 , 23 , 23 , 25 , 25<br>बहुलक= संख्या की उच्चतम आवृत्ति = 15</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>59</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>26</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>] = _____.</p>",
                    question_hi: "<p>56. [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>59</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>26</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>] = _____.</p>",
                    options_en: [
                        "<p>-1</p>",
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>2</p>"
                    ],
                    options_hi: [
                        "<p>-1</p>",
                        "<p>0</p>",
                        "<p>1</p>",
                        "<p>2</p>"
                    ],
                    solution_en: "<p>56.(c) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>59</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>26</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>]&nbsp;</p>\n<p>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>31</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>64</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>]</p>\n<p>= [<math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>31</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>31</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>18</mn><mo>&#176;</mo><mi>&#160;</mi><mo>&#247;</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + tan (30&deg; + 15&deg;)</p>\n<p>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>0</mn><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + tan 45&deg;</p>\n<p>= 0 + 1 = 1</p>",
                    solution_hi: "<p>56.(c) [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>59</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>26</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>]&nbsp;</p>\n<p>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>31</mn><mo>&#176;</mo><mo>-</mo><mi>s</mi><mi>e</mi><mi>c</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>31</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>(</mo><mn>90</mn><mo>&#176;</mo><mo>-</mo><mn>64</mn><mo>&#176;</mo><mo>)</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + [<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>+</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mn>1</mn><mo>-</mo><mi>tan</mi><mo>&#160;</mo><mn>30</mn><mo>&#176;</mo><mo>&#160;</mo><mi>tan</mi><mo>&#160;</mo><mn>15</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math>]</p>\n<p>= [<math display=\"inline\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>31</mn><mo>&#176;</mo><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>e</mi><mi>c</mi><mi>&#160;</mi><mn>31</mn><mo>&#176;</mo></mrow><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>18</mn><mo>&#176;</mo><mi>&#160;</mi><mo>&#247;</mo><mi>&#160;</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + tan (30&deg; + 15&deg;)</p>\n<p>= [<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>0</mn><mrow><mo>(</mo><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#160;</mi><mn>18</mn><mo>&#176;</mo><mo>&#247;</mo><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#160;</mi><mn>72</mn><mo>&#176;</mo><mo>)</mo></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow><mrow><mi>c</mi><mi>o</mi><mi>t</mi><mo>&#160;</mo><mn>64</mn><mo>&#176;</mo></mrow></mfrac></math>] + tan 45&deg;</p>\n<p>= 0 + 1 = 1</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. A person borrowed some money on simple interest. After 4 years, he returned <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> of the money to lender. What was the rate of interest ?</p>",
                    question_hi: "<p>57. एक व्यक्ति ने साधारण ब्याज पर कुछ धन उधार लिया। 4 वर्षों के बाद उसने ऋणदाता को धन का <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> भाग वापस कर दिया। ब्याज की दर कितनी थी ?</p>",
                    options_en: [
                        "<p>20% p.a.</p>",
                        "<p>18% p.a.</p>",
                        "<p>17% p.a.</p>",
                        "<p>19% p.a.</p>"
                    ],
                    options_hi: [
                        "<p>20% प्रतिवर्ष</p>",
                        "<p>18% प्रतिवर्ष</p>",
                        "<p>17% प्रतिवर्ष</p>",
                        "<p>19% प्रतिवर्ष</p>"
                    ],
                    solution_en: "<p>57.(a)<br>Amount = Principal &times; (1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>R</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>&#215;</mo><mi>T</mi><mi>i</mi><mi>m</mi><mi>e</mi></mrow><mn>100</mn></mfrac></math>)<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> P = P (1 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>R</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math>)</p>\n<p>&rArr;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> - 1 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>R</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math></p>\n<p>&rArr;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>R</mi><mi>a</mi><mi>t</mi><mi>e</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math></p>\n<p>&rArr; Rate = 20%</p>",
                    solution_hi: "<p>57.(a)<br>मिश्रधन = मूलधन (1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;</mi><mi>&#2352;</mi><mo>&#215;</mo><mi>&#2360;</mi><mi>&#2350;</mi><mi>&#2351;</mi></mrow><mn>100</mn></mfrac></math>)<br>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> मूलधन = मूलधन (1 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;</mi><mi>&#2352;</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math>)</p>\n<p>&rArr;&nbsp;<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9</mn><mn>5</mn></mfrac></math> - 1 = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;</mi><mi>&#2352;</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math></p>\n<p>&rArr; <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>4</mn><mn>5</mn></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;</mi><mi>&#2352;</mi><mo>&#215;</mo><mn>4</mn></mrow><mn>100</mn></mfrac></math></p>\n<p>&rArr; दर = 20%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Pipe A can fill a cistern in 4 hours and Pipe B can fill the same cistern in 5 hours. Pipe C can empty a full cistern in 3 hours. If all three pipes are opened together, then the time (in minutes) taken to fill the tank is: (round to the nearest minute)</p>",
                    question_hi: "<p>58. पाइप A एक टंकी को 4 घंटे में भर सकता है और पाइप B उसी टंकी को 5 घंटे में भर सकता है। पाइप C पूरी टंकी को 3 घंटे में खाली कर सकता है। यदि सभी तीन पाइप एक साथ खोले जाते हैं, तो टंकी को भरने में कितना समय (मिनट में) लगेगा ? (निकटतम मिनट तक पूर्णांकित)</p>",
                    options_en: [
                        "<p>625</p>",
                        "<p>445</p>",
                        "<p>800</p>",
                        "<p>514</p>"
                    ],
                    options_hi: [
                        "<p>625</p>",
                        "<p>445</p>",
                        "<p>800</p>",
                        "<p>514</p>"
                    ],
                    solution_en: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712796214.png\" alt=\"rId56\" width=\"281\" height=\"132\"><br>Efficiency of all pipe together = 15 + 12 - 20 = 7 unit<br>Time taken to fill tank together = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>7</mn></mfrac></math> &times; 60 minutes = 514 .28 &asymp; 514 minute</p>",
                    solution_hi: "<p>58.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712796606.png\" alt=\"rId57\" width=\"286\" height=\"147\"><br>सभी पाइपों की कुल क्षमता= 15 + 12 - 20 = 7 unit<br>टंकी को एक साथ भरने में लगा समय= <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>7</mn></mfrac></math> &times; 60 मिनट = 514 .28 &asymp; 514 मिनट</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The average of 50 numbers is 23. If each number is multiplied by 13, then the new&nbsp;average is:</p>",
                    question_hi: "<p>59. 50 संख्याओं का औसत 23 है। यदि प्रत्येक संख्या को 13 से गुणा किया जाए, तो नया औसत क्या होगा ?</p>",
                    options_en: [
                        "<p>88</p>",
                        "<p>299</p>",
                        "<p>650</p>",
                        "<p>219</p>"
                    ],
                    options_hi: [
                        "<p>88</p>",
                        "<p>299</p>",
                        "<p>650</p>",
                        "<p>219</p>"
                    ],
                    solution_en: "<p>59.(b)<br>Average of 50 numbers = 23<br>New average when each number is multiplied by 13 = average &times; 13 = 23 &times; 13 = 299</p>",
                    solution_hi: "<p>59.(b)<br>50 संख्याओं का औसत = 23<br>प्रत्येक संख्या को 13 से गुणा करने पर नया औसत = औसत &times; 13 = 23 &times; 13 = 299</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. There are y yellow marbles and b blue marbles in a box. 18% of the yellow marbles were added to 24% of the blue marbles, and the total was 30. Three times the number of yellow marbles exceeds two times the number of blue marbles by 200. What is the ratio of y<sup>2</sup> to b<sup>2 </sup>?</p>",
                    question_hi: "<p>60. एक डिब्बे में y पीले कंचे और b नीले कंचे हैं। 18% पीले कंचों को 24% नीले कंचों में मिलाया गया, और योग 30 था। पीले कंचों की संख्या का तीन गुना, नीले कंचों की संख्या के दो गुने से 200 अधिक है। y&sup2; और b&sup2; का अनुपात क्या है ?</p>",
                    options_en: [
                        "<p>6 ∶ 5</p>",
                        "<p>4 : 1</p>",
                        "<p>2 ∶ 1</p>",
                        "<p>5 ∶ 2</p>"
                    ],
                    options_hi: [
                        "<p>6 ∶ 5</p>",
                        "<p>4 : 1</p>",
                        "<p>2 ∶ 1</p>",
                        "<p>5 ∶ 2</p>"
                    ],
                    solution_en: "<p>60.(b) According to the question,<br><math display=\"inline\"><mi>y</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>100</mn></mfrac></math> + b &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>100</mn></mfrac></math> = 30 </p>\n<p>&rArr; 3y + 4b = 500 &hellip;&hellip;..(i)<br>And , 3<math display=\"inline\"><mi>y</mi></math> = 2b + 200<br>&rArr; 3y - 2b = 200 &hellip;&hellip;..(ii)<br>Subtract equation (i) from equation (ii), we get<br>6b = 300<br>b = 50<br>Put the value of b in equation (i)<br>3y + 200 = 500<br>3y = 300<br>y = 100<br>Required ratio = 100 &times; 100 : 50 &times; 50 = 4 : 1</p>",
                    solution_hi: "<p>60.(b) प्रश्न के अनुसार,<br><math display=\"inline\"><mi>y</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>18</mn><mn>100</mn></mfrac></math> + b &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>24</mn><mn>100</mn></mfrac></math> = 30 </p>\n<p>&rArr; 3y + 4b = 500 &hellip;&hellip;..(i)<br>और , 3<math display=\"inline\"><mi>y</mi></math> = 2b + 200<br>&rArr;&nbsp;3y - 2b = 200 &hellip;&hellip;..(ii)<br>समीकरण (i) को समीकरण (ii) से घटाने पर हमें प्राप्त होता है,<br>6b = 300<br>b = 50<br>b का मान समीकरण (i) में रखने पर <br>3y + 200 = 500<br>3y = 300<br>y = 100<br>आवश्यक अनुपात = 100 &times; 100 : 50 &times; 50 = 4 : 1</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. In a circular race of 750 m, X and Y start from the same point and at the same time with speeds of 9 km/h and 13.5 km/h, respectively. If they are running in the same direction then, when will they meet again for the first time on the track ?</p>",
                    question_hi: "<p>61. 750 m वाली एक वृत्&zwj;ताकार दौड़ में, X और Y एक ही बिंदु से और एक ही समय पर क्रमशः 9 km/h और 13.5 km/h की चाल से दौड़ना शुरू करते हैं। यदि वे एक ही दिशा में दौड़ रहे हैं, तो वे पुन: ट्रैक पर पहली बार कब मिलेंगे ?</p>",
                    options_en: [
                        "<p>750 seconds</p>",
                        "<p>900 seconds</p>",
                        "<p>500 seconds</p>",
                        "<p>600 seconds</p>"
                    ],
                    options_hi: [
                        "<p>750 सेकंड में</p>",
                        "<p>900 सेकंड में</p>",
                        "<p>500 सेकंड में</p>",
                        "<p>600 सेकंड में</p>"
                    ],
                    solution_en: "<p>61.(d)<br>Relative speed when running in same direction =13.5 - 9 = 4.5 km/hr = 4.5 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 1.25 m/sec <br>Time taken to meet again for the first time = <math display=\"inline\"><mfrac><mrow><mn>750</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = 600 sec</p>",
                    solution_hi: "<p>61.(d)<br>समान दिशा में चलने पर सापेक्ष गति =13.5 - 9 = 4.5 किमी/घंटा = 4.5 &times; <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> = 1.25 मीटर/सेकंड <br>पहली बार दोबारा मिलने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>750</mn></mrow><mrow><mn>1</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> = 600 सेकंड</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. The given bar graph shows the foreign direct investment (in million dollars) in Country A from all over the world over the given years<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712796715.png\" alt=\"rId58\" width=\"327\" height=\"292\"> <br>What was the ratio of investment in 2018 to that in 2015 ?</p>",
                    question_hi: "<p>62. दिया गया दंड आलेख दिए गए वर्षों में दुनिया भर से देश A में प्रत्यक्ष विदेशी निवेश (मिलियन डॉलर में) को दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712796832.png\" alt=\"rId59\" width=\"321\" height=\"287\"> <br>मिलियन डॉलर में निवेश 2018 और 2015 में निवेश का अनुपात क्या था ?</p>",
                    options_en: [
                        "<p>13 : 12</p>",
                        "<p>12 : 11</p>",
                        "<p>11 : 12</p>",
                        "<p>13 : 11</p>"
                    ],
                    options_hi: [
                        "<p>13 : 12</p>",
                        "<p>12 : 11</p>",
                        "<p>11 : 12</p>",
                        "<p>13 : 11</p>"
                    ],
                    solution_en: "<p>62.(a)<br>Investment in 2018 = 65<br>Investment in 2015 = 60<br>Required ratio = <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>60</mn><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>12</mn></mfrac></math> or 13 : 12</p>",
                    solution_hi: "<p>62.(a)<br>2018 में निवेश = 65<br>2015 में निवेश = 60<br>आवश्यक अनुपात = <math display=\"inline\"><mfrac><mrow><mn>65</mn></mrow><mrow><mn>60</mn><mi>&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>13</mn><mn>12</mn></mfrac></math> या 13 : 12</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. In an obtuse-angled triangle ABC, the length of its longest side AB is 50 cm and one ofthe other two sides are 42 cm. If the area of the triangle is 294 cm<sup>2</sup>, what is the length (in cm) of its third side ?</p>",
                    question_hi: "<p>63. एक अधिककोण त्रिभुज ABC में, इसकी सबसे लंबी भुजा AB की लंबाई 50 cm है और अन्य दो भुजाओं&nbsp;में से एक भुजा की लंबाई 42 cm है। यदि त्रिभुज का क्षेत्रफल 294 cm<strong id=\"docs-internal-guid-1e8f15c6-7fff-cc85-5672-983670d5c7fa\">&nbsp;</strong> है, तो इसकी तीसरी भुजा की लंबाई (cm में) क्या है ?</p>",
                    options_en: [
                        "<p>12<math display=\"inline\"><msqrt><mn>43</mn></msqrt></math></p>",
                        "<p>8<math display=\"inline\"><msqrt><mn>35</mn></msqrt></math></p>",
                        "<p>15<math display=\"inline\"><msqrt><mn>21</mn></msqrt></math></p>",
                        "<p>2<math display=\"inline\"><msqrt><mn>58</mn></msqrt></math></p>"
                    ],
                    options_hi: [
                        "<p>12<math display=\"inline\"><msqrt><mn>43</mn></msqrt></math></p>",
                        "<p>8<math display=\"inline\"><msqrt><mn>35</mn></msqrt></math></p>",
                        "<p>15<math display=\"inline\"><msqrt><mn>21</mn></msqrt></math></p>",
                        "<p>2<math display=\"inline\"><msqrt><mn>58</mn></msqrt></math></p>"
                    ],
                    solution_en: "<p>63.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712797020.png\" alt=\"rId60\" width=\"202\" height=\"152\"><br>Let the height of &Delta;ABC&nbsp;is AD<br>Area of &Delta;ABC = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; BC &times; AD<br>294 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 42 &times; AD<br>14 = AD &hellip;.. (i)<br>Now in &Delta;ADB<br>AB<sup>2</sup> = AD<sup>2</sup> + BD<sup>2</sup><br>50<sup>2</sup> = 14<sup>2</sup> + BD<sup>2</sup> &hellip; [from (i)]<br>BD<sup>2</sup>&nbsp;= 2500 - 196 = 2304<br>BD = 48 cm<br>DC = BD - BC<br>= 48 - 42<br>= 6 cm<br>In &Delta;ADC<br>AC<sup>2</sup> = AD<sup>2</sup> + DC<sup>2</sup><br>= 14<sup>2</sup>&nbsp;+ 6<sup>2</sup><br>= 196 + 36<br>= 232 cm<br>AC = 2<math display=\"inline\"><msqrt><mn>58</mn></msqrt></math> cm</p>",
                    solution_hi: "<p>63.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712797020.png\" alt=\"rId60\" width=\"202\" height=\"152\"></p>\n<p>माना कि &Delta;ABC&nbsp;&nbsp;की ऊँचाई AD है<br>&Delta;ABC&nbsp; का क्षेत्रफल = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; BC &times; AD<br>294 = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> &times; 42 &times; AD<br>14 = AD &hellip;.. (i)<br>अब &Delta;ADB में&nbsp;<br>AB<sup>2</sup> = AD<sup>2</sup> + BD<sup>2</sup><br>50<sup>2</sup> = 14<sup>2</sup> + BD<sup>2</sup> &hellip; [ (i)]<br>BD<sup>2</sup>&nbsp;= 2500 - 196 = 2304<br>BD = 48 cm<br>DC = BD - BC<br>= 48 - 42<br>= 6 cm<br>&Delta;ADC में <br>AC<sup>2</sup> = AD<sup>2</sup> + DC<sup>2</sup><br>= 14<sup>2</sup>&nbsp;+ 6<sup>2</sup><br>= 196 + 36<br>= 232 cm<br>AC = 2<math display=\"inline\"><msqrt><mn>58</mn></msqrt></math> cm</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. A thief takes off on his bike at a certain speed, after seeing a police car at a distance of 250 m. The police car starts chasing the thief and catches him. If the thief runs 1.5 km before being caught and the speed of the police car is 70 km/h, then what is the speed of thief&rsquo;s bike (in km/h) ?</p>",
                    question_hi: "<p>64. 250 m की दूरी पर एक पुलिस कार को देखकर एक चोर एक निश्चित चाल से अपनी बाइक से भागता है। पुलिस की गाड़ी चोर का पीछा करती है और उसे पकड़ लेती है। यदि चोर पकड़े जाने से पहले 1.5 km भागता है और पुलिस की कार की चाल 70 km/h है, तो चोर की बाइक की चाल (km/h में) कितनी है ?</p>",
                    options_en: [
                        "<p>65</p>",
                        "<p>60</p>",
                        "<p>55</p>",
                        "<p>50</p>"
                    ],
                    options_hi: [
                        "<p>65</p>",
                        "<p>60</p>",
                        "<p>55</p>",
                        "<p>50</p>"
                    ],
                    solution_en: "<p>64.(b)<br>Time taken by police car to cover 1750 m = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1750</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>1000</mn></mfrac></mrow><mn>70</mn></mfrac></math> hr = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>1000</mn></mfrac></math>hr<br>Speed of thief = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mrow><mfrac><mrow><mn>25</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></mrow></mfrac></math> km/h = 60 km/h</p>",
                    solution_hi: "<p>64.(b)<br>पुलिस की गाड़ी को 1750 मीटर तय करने में लगा समय = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1750</mn><mo>&#215;</mo><mfrac><mn>1</mn><mn>1000</mn></mfrac></mrow><mn>70</mn></mfrac></math> घंटे = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>25</mn><mn>1000</mn></mfrac></math>&nbsp;घंटे<br>Speed of thief = <math display=\"inline\"><mfrac><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow><mrow><mfrac><mrow><mn>25</mn></mrow><mrow><mn>1000</mn></mrow></mfrac></mrow></mfrac></math> किमी/घंटा = 60 किमी/घंटा</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Manish, Nakul and Pintoo alone can complete a certain work in 21 days, 28 days and 15 days, respectively. Manish and Pintoo started the work together while Nakul joined them after 5 days and worked with them till the completion of the work. For how many days did Nakul work ?</p>",
                    question_hi: "<p>65. मनीष, नकुल और पिंटू अकेले किसी काम को क्रमशः 21 दिन, 28 दिन और 15 दिन में पूरा कर सकते हैं। मनीष और पिंटू ने एक साथ काम शुरू किया जबकि नकुल ने 5 दिन के बाद उनके साथ काम करना शुरू किया और काम पूरा होने तक उनके साथ काम किया। नकुल ने कितने दिन तक काम किया ?</p>",
                    options_en: [
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>3<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>5<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>65.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712797159.png\" alt=\"rId61\" width=\"236\" height=\"150\"><br>5 days work of Manish and Pintu = 5 <math display=\"inline\"><mo>&#215;</mo></math> (20 + 28) = 240 unit<br>Remaining work = 420 - 240 = 180 unit<br>Time taken by all three to complete remaining work = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>20</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>28</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>63</mn></mfrac></math> = 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days<br>Hence, number of days Nakul worked for = 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> days</p>",
                    solution_hi: "<p>65.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712797288.png\" alt=\"rId62\" width=\"214\" height=\"155\"><br>मनीष और पिंटू का 5 दिन का कार्य = 5 <math display=\"inline\"><mo>&#215;</mo></math> (20 + 28) = 240 इकाई<br>शेष कार्य = 420 - 240 = 180 इकाई<br>शेष कार्य को पूरा करने में तीनों द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>20</mn><mo>+</mo><mn>15</mn><mo>+</mo><mn>28</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>180</mn><mn>63</mn></mfrac></math> = 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math>&nbsp;दिन<br>अतः, नकुल ने जितने दिन काम किया = 2<math display=\"inline\"><mfrac><mrow><mn>6</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> दिन</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. In an election between two candidates, the one who gets 88% of the votes is elected by a majority of 684 votes. What is the total number of votes polled ? (all polled votes are valid)</p>",
                    question_hi: "<p>66. दो उम्मीदवारों के बीच चुनाव में, 88% मत पाने वाला उम्&zwj;मीदवार 684 मतों के बहुमत से जीत जाता है।डाले गए मतों की कुल संख्या कितनी है ? (डाले गए सभी मत वैध हैं)</p>",
                    options_en: [
                        "<p>900</p>",
                        "<p>800</p>",
                        "<p>850</p>",
                        "<p>750</p>"
                    ],
                    options_hi: [
                        "<p>900</p>",
                        "<p>800</p>",
                        "<p>850</p>",
                        "<p>750</p>"
                    ],
                    solution_en: "<p>66.(a) let total number of votes be 100<br>Elected candidate got votes = 88%<br>Not elected candidate got votes = 100 - 88 = 12 %<br>According to the question,<br>(88 - 12)% = 76 % = 684<br>100 % = 684 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>76</mn></mrow></mfrac></math> = 900</p>",
                    solution_hi: "<p>66.(a) माना कि कुल वोटों की संख्या 100 है। <br>निर्वाचित उम्मीदवार को मिले वोट = 88%<br>गैर निर्वाचित उम्मीदवार को मिले वोट = 100 - 88 = 12 %<br>प्रश्न के अनुसार,<br>(88 - 12)% = 76 % = 684<br>100 % = 684 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>76</mn></mrow></mfrac></math> = 900</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. Study the given graph and answer the question that follows.<br>The graph shows the exports from three companies (A, B and C) over the years (in ₹ crore).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712797416.png\" alt=\"rId63\" width=\"473\" height=\"283\"> <br>In which year was the difference between the exports from companies A and B the minimum ?</p>",
                    question_hi: "<p>67. दिए गए ग्राफ का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए। <br>ग्राफ तीन कंपनियों (A, B और C) का कुछ वर्षों में (₹ करोड़ में) निर्यात दर्शाता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712797416.png\" alt=\"rId63\" width=\"473\" height=\"283\"><br>किस वर्ष कंपनी A और B से निर्यात के बीच का अंतर न्यूनतम था?</p>",
                    options_en: [
                        "<p>1994</p>",
                        "<p>1998</p>",
                        "<p>1995</p>",
                        "<p>1997</p>"
                    ],
                    options_hi: [
                        "<p>1994</p>",
                        "<p>1998</p>",
                        "<p>1995</p>",
                        "<p>1997</p>"
                    ],
                    solution_en: "<p>67.(b) <br>By checking options<br>Difference between the exports from company A and B <br>In 1994 :- do not have data<br>In 1998 :- (152 - 95) = 57 crore<br>In 1995 :- (125 - 60) = 65 crore<br>In 1997 :- (145 - 80) = 65 crore<br>In 1998 required difference is minimum</p>",
                    solution_hi: "<p>67.(b) <br>विकल्पों की जाँच करने पर&nbsp;कंपनी A और B के निर्यात के बीच अंतर <br>1994 में :- डेटा नहीं है<br>1998 में :- (152 - 95) = 57 करोड़<br>1995 में :- (125 - 60) = 65 करोड़<br>1997 में :- (145 - 80) = 65 करोड़<br>1998 में आवश्यक अंतर न्यूनतम है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. Three numbers are in the ratio 3 : 9 : 11 and their HCF is 29. Find the numbers.</p>",
                    question_hi: "<p>68. तीन संख्याएं 3 : 9 : 11 के अनुपात में हैं और उनका महत्तम समपवर्तक 29 है। वे तीन संख्याएं ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>84, 240, and 300</p>",
                        "<p>58, 88, and 319</p>",
                        "<p>144, 261, and 320</p>",
                        "<p>87, 261, and 319</p>"
                    ],
                    options_hi: [
                        "<p>84, 240, और 300</p>",
                        "<p>58, 88, और 319</p>",
                        "<p>144, 261, और 320</p>",
                        "<p>87, 261, और 319</p>"
                    ],
                    solution_en: "<p>68.(d)<br>Let the numbers be 3a, 9a and 11a.<br>Then, HCF(3a, 9a, 11a) = a = 29.<br>Therefore, the numbers are 87, 261 and 319.</p>",
                    solution_hi: "<p>68.(d)<br>माना संख्याएँ 3a, 9a और 11a हैं।<br>फिर, HCF(3a, 9a, 11a) = a = 29.<br>इसलिए, संख्याएँ 87, 261 और 319 हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. The value of (2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> + 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math>) &times; 16 &divide; 49 is</p>",
                    question_hi: "<p>69. (2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> + 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math>) &times; 16 &divide; 49 का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mn>1</mn><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mn>2</mn><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><mn>4</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mn>1</mn><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mn>2</mn><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>9</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>69.(b)<br>(2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> + 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math>) &times; 16 &divide; 49<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>4</mn></mfrac></mstyle></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math>) &times; 16 &divide; 49<br>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>+</mo><mn>21</mn><mo>-</mo><mn>2</mn></mrow><mn>12</mn></mfrac></math>) &times; 16 &divide; 49<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mfrac><mn>49</mn><mn>12</mn></mfrac></mfenced></math> &times; 16 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>49</mn></mfrac></mstyle></math> = <math display=\"inline\"><mn>1</mn><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>69.(b)<br>(2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> + 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>3</mn><mn>4</mn></mfrac></mstyle></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math>) &times; 16 &divide; 49<br>(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>2</mn></mfrac></math> +&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>7</mn><mn>4</mn></mfrac></mstyle></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>6</mn></mfrac></mstyle></math>) &times; 16 &divide; 49<br>(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>+</mo><mn>21</mn><mo>-</mo><mn>2</mn></mrow><mn>12</mn></mfrac></math>) &times; 16 &divide; 49<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfenced separators=\"|\"><mfrac><mn>49</mn><mn>12</mn></mfrac></mfenced></math> &times; 16 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mstyle displaystyle=\"false\"><mfrac><mn>1</mn><mn>49</mn></mfrac></mstyle></math> = <math display=\"inline\"><mn>1</mn><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. From the given data, find the ratio of the cost prices of a shirt and a trouser<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712797540.png\" alt=\"rId64\" width=\"215\" height=\"65\"></p>",
                    question_hi: "<p>70. दिए गए आँकड़ों से शर्ट और ट्राउज़र के क्रय मूल्य का अनुपात ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712797666.png\" alt=\"rId65\" width=\"194\" height=\"90\"></p>",
                    options_en: [
                        "<p>2 : 1</p>",
                        "<p>1 : 3</p>",
                        "<p>3 : 4</p>",
                        "<p>5 : 3</p>"
                    ],
                    options_hi: [
                        "<p>2 : 1</p>",
                        "<p>1 : 3</p>",
                        "<p>3 : 4</p>",
                        "<p>5 : 3</p>"
                    ],
                    solution_en: "<p>70.(d) According to the question,<br>CP of the shirt = 1200 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math> = ₹1000<br>CP of the trouser = 660 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>110</mn></mrow></mfrac></math> = ₹600<br>Required ratio = 1000 : 600 = 5 : 3</p>",
                    solution_hi: "<p>70.(d) प्रश्न के अनुसार,<br>शर्ट का C.P. = 1200 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>120</mn></mrow></mfrac></math> = ₹1000<br>ट्राउज़र का C.P. = 660 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>110</mn></mrow></mfrac></math> = ₹600<br>आवश्यक अनुपात = 1000 : 600 = 5 : 3</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. If &theta; is an acute angle and tan&theta; + cot&theta; = 2, then find the value of tan<sup>3</sup>&theta; + cot<sup>3</sup>&theta; + 6tan<sup>3</sup>&theta;cot<sup>2</sup>&theta;.</p>",
                    question_hi: "<p>71. यदि &theta; एक न्यूनकोण है और tan&theta; + cot&theta; = 2 है, तो tan<sup>3</sup>&theta; + cot<sup>3</sup>&theta; + 6tan<sup>3</sup>&theta;cot<sup>2</sup>&theta; का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>8</p>",
                        "<p>10</p>",
                        "<p>12</p>",
                        "<p>6</p>"
                    ],
                    options_hi: [
                        "<p>8</p>",
                        "<p>10</p>",
                        "<p>12</p>",
                        "<p>6</p>"
                    ],
                    solution_en: "<p>71.(a) <br>According to the question,<br>tan&theta; + cot&theta; = 2 <br>Let &theta; = 45&deg; then,<br>1 + 1 = 2 (satisfied)<br>Now, put the value of &theta; = 45&deg; we get,<br>&rArr; tan<sup>3</sup>&theta; + cot<sup>3</sup>&theta; + 6tan<sup>3</sup>&theta;cot<sup>2</sup>&theta;<br>&rArr; 1 + 1 + 6 = 8</p>",
                    solution_hi: "<p>71.(a) <br>प्रश्न के अनुसार,<br>tan&theta; + cot&theta; = 2 <br>माना &theta; = 45&deg; तो,<br>1 + 1 = 2 (संतुष्ट)<br>अब, &theta; = 45&deg; रखने पर हमें प्राप्त होता है,<br>&rArr; tan<sup>3</sup>&theta; + cot<sup>3</sup>&theta; + 6tan<sup>3</sup>&theta;cot<sup>2</sup>&theta;<br>&rArr; 1 + 1 + 6 = 8</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. In measuring the sides of a rectangle, one side is taken 10% in excess, and the other 8% in deficit. The error percent in the area calculated from these measurements is:</p>",
                    question_hi: "<p>72. एक आयत की भुजाओं को मापने में एक भुजा को 10% अधिक और दूसरी को 8% कम माप लिया जाता है। इन मापों से परिकलित क्षेत्रफल में त्रुटि प्रतिशत क्&zwj;या है ?</p>",
                    options_en: [
                        "<p>1.4%</p>",
                        "<p>1.0%</p>",
                        "<p>1.2%</p>",
                        "<p>0.8%</p>"
                    ],
                    options_hi: [
                        "<p>1.4%</p>",
                        "<p>1.0%</p>",
                        "<p>1.2%</p>",
                        "<p>0.8%</p>"
                    ],
                    solution_en: "<p>72.(c)<br>10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math>, 8% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>25</mn></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; Area of rectangle = Length &times; Width<br>Old&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 250&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; &nbsp; 10&nbsp; &nbsp; &times;&nbsp; &nbsp;25 <br>New&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 253&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;=&nbsp; &nbsp; &nbsp; 11&nbsp; &nbsp; &times;&nbsp; &nbsp;23<br>Change in area = <math display=\"inline\"><mfrac><mrow><mn>253</mn><mo>-</mo><mn>250</mn></mrow><mrow><mn>250</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>250</mn></mfrac></math> = 1.2%<br><strong>Alternate method :</strong><br>Net Change in area = (10 - 8 - <math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mi>&#160;</mi><mn>8</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 2 - 0.8 = 1.2%</p>",
                    solution_hi: "<p>72.(c)<br>10% = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>10</mn></mrow></mfrac></math>, 8% = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>25</mn></mfrac></math><br>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; आयत का क्षेत्रफल = लंबाई &times; चौड़ाई <br>पुराना&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 250&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp;10&nbsp; &times;&nbsp; &nbsp;25 <br>नया&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 253&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; =&nbsp; &nbsp; 11&nbsp; &times;&nbsp; &nbsp;23<br>क्षेत्रफल में त्रुटि प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>253</mn><mo>-</mo><mn>250</mn></mrow><mrow><mn>250</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>300</mn><mn>250</mn></mfrac></math> = 1.2%<br><strong>वैकल्पिक विधि :<br></strong>क्षेत्रफल में त्रुटि प्रतिशत = (10 - 8 -&nbsp;<math display=\"inline\"><mfrac><mrow><mn>10</mn><mo>&#215;</mo><mi>&#160;</mi><mn>8</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math>)% = 2 - 0.8 = 1.2%</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Two circles of radius 7 units each, intersect in such a way that the common chord is of length 7 units. What is the common area in square units of the intersection ?</p>",
                    question_hi: "<p>73. प्रत्येक 7 इकाई त्रिज्या वाले दो वृत्त इस प्रकार प्रतिच्छेद करते हैं कि उभयनिष्ठ जीवा की लंबाई 7 इकाई हो। प्रतिच्छेदन का वर्ग इकाइयों में उभयनिष्ठ क्षेत्रफल क्या होगा ?</p>",
                    options_en: [
                        "<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>3</mn></mfrac></math>)</p>",
                        "<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>",
                        "<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>6</mn></mfrac></math>)</p>",
                        "<p>98(<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>"
                    ],
                    options_hi: [
                        "<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>3</mn></mfrac></math>)</p>",
                        "<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>3</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>",
                        "<p>98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>6</mn></mfrac></math>)</p>",
                        "<p>98(<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>"
                    ],
                    solution_en: "<p>73.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712797826.png\" alt=\"rId66\" width=\"256\" height=\"166\"><br>Common area = 2 &times; (area of sector OABO - Area of equilateral <math display=\"inline\"><mi>&#916;</mi></math>OAB)<br>= 2 &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>360</mn></mfrac></math> &pi; &times; (7)<sup>2 </sup>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>&times; (7)<sup>2</sup>)<br>= 2 &times; (7)<sup>2</sup> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&pi; - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)<br>= 98 (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&pi; - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)<br>Hence, required area = 98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>",
                    solution_hi: "<p>73.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712797948.png\" alt=\"rId67\" width=\"245\" height=\"144\"><br>उभयनिष्ठ क्षेत्रफल = 2 &times; (त्रिज्यखंड OABO का क्षेत्रफल - समबाहु OAB का क्षेत्रफल)<br>= 2 &times; (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>360</mn></mfrac></math> &pi; &times; (7)<sup>2 </sup>- <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>&times; (7)<sup>2</sup>)<br>= 2 &times; (7)<sup>2</sup> (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&pi; - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)<br>= 98 (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math>&pi; - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)<br>अत: अभीष्ट क्षेत्रफल = 98 (<math display=\"inline\"><mfrac><mrow><mi>&#960;</mi></mrow><mrow><mn>6</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msqrt><mn>3</mn></msqrt><mn>4</mn></mfrac></math>)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. A merchant has 1000 kg of sugar, part of which he sells at a 10% profit and the rest at a 40% profit. He gains 20% on the whole. The quantity sold at 40% profit is:</p>",
                    question_hi: "<p>74. एक व्यापारी के पास 1000 kg चीनी है, जिसका एक भाग वह 10% लाभ पर और शेष 40% लाभ पर बेचता है। उसे पूरे सौदे में 20% का लाभ होता है। 40% लाभ पर बेची गई मात्रा की गणना करें।</p>",
                    options_en: [
                        "<p>543<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;kg</p>",
                        "<p>383<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;kg</p>",
                        "<p>333<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;kg</p>",
                        "<p>443<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;kg</p>"
                    ],
                    options_hi: [
                        "<p>543<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> kg</p>",
                        "<p>383<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;kg</p>",
                        "<p>333<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;kg</p>",
                        "<p>443<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>&nbsp;kg</p>"
                    ],
                    solution_en: "<p>74.(c) <br>Total quantity of sugar = 1000 kg<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712798081.png\" alt=\"rId68\" width=\"120\" height=\"130\"><br>Sugar sold at 40 % = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 1000 = 333<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> kg</p>",
                    solution_hi: "<p>74.(c) <br>चीनी की कुल मात्रा = 1000 किग्रा<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744712798081.png\" alt=\"rId68\" width=\"120\" height=\"130\"><br>40% लाभ में बेंची गयी चीनी = <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 1000 = 333<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math> किग्रा</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. If an electricity bill is paid before the due date, one gets a reduction of 5% on the amount of the bill. By paying the bill before the due date, a person got a reduction of ₹250. The original amount of his electricity bill (in ₹) was:</p>",
                    question_hi: "<p>75. यदि नियत तारीख से पहले बिजली बिल का भुगतान किया जाता है, तो बिल की राशि पर 5% की छूट दी जाती है। नियत तारीख से पहले बिल का भुगतान करने पर एक व्यक्ति को ₹250 की छूट प्राप्त हुई। उसके बिजली बिल की मूल राशि (₹ में) कितनी थी ?</p>",
                    options_en: [
                        "<p>4000</p>",
                        "<p>4500</p>",
                        "<p>5000</p>",
                        "<p>5500</p>"
                    ],
                    options_hi: [
                        "<p>4000</p>",
                        "<p>4500</p>",
                        "<p>5000</p>",
                        "<p>5500</p>"
                    ],
                    solution_en: "<p>75.(c) <br>If the bill is paid before the due date, the reduction is 5% which is equal to 250.<br>5 % = ₹ 250<br>&rArr; 1 % = ₹ 50<br>&rArr; 100 % = ₹ 5000<br>Hence, original amount of his electricity bill = ₹ 5,000</p>",
                    solution_hi: "<p>75.(c) <br>यदि नियत तिथि से पहले बिल का भुगतान किया जाता है, तो कटौती 5% है जो 250 के बराबर है।<br>5 % = ₹ 250<br>&rArr; 1 % = ₹ 50<br>&rArr; 100% = ₹ 5000/-<br>अतः, उसके बिजली बिल की मूल राशि = ₹ 5,000</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the most appropriate ANTONYM of the given word.<br>Resistance</p>",
                    question_hi: "<p>76. Select the most appropriate ANTONYM of the given word.<br>Resistance</p>",
                    options_en: [
                        "<p>Tenderness</p>",
                        "<p>Tolerance</p>",
                        "<p>Reliance</p>",
                        "<p>Awareness</p>"
                    ],
                    options_hi: [
                        "<p>Tenderness</p>",
                        "<p>Tolerance</p>",
                        "<p>Reliance</p>",
                        "<p>Awareness</p>"
                    ],
                    solution_en: "<p>76.(b) <strong>Tolerance</strong> - the quality of allowing people to do or believe what they want although you do not agree with it.<br><strong>Resistance</strong> - the refusal to accept or comply with something. <br><strong>Tenderness</strong> - gentleness and kindness.<br><strong>Reliance</strong> - the fact of depending on someone or something.<br><strong>Awareness</strong> - the mental state of knowing about something.</p>",
                    solution_hi: "<p>76.(b) <strong>Tolerance</strong> (सहनशीलता) - the quality of allowing people to do or believe what they want although you do not agree with it.<br><strong>Resistance</strong> (प्रतिरोध) - the refusal to accept or comply with something. <br><strong>Tenderness</strong> (दयालुता) - gentleness and kindness.<br><strong>Reliance</strong> (निर्भरता) - the fact of depending on someone or something.<br><strong>Awareness</strong> (जागरूकता) - the mental state of knowing about something.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. The following sentence has been split into segments. Identify the segment that contains an error. If you don&rsquo;t find any error, mark &lsquo;No error&rsquo; as your answer. <br>People admired him, for / he lived by the same principals / that he preached.</p>",
                    question_hi: "<p>77. The following sentence has been split into segments. Identify the segment that contains an error. If you don&rsquo;t find any error, mark &lsquo;No error&rsquo; as your answer. <br>People admired him, for / he lived by the same principals / that he preached.</p>",
                    options_en: [
                        "<p>that he preached</p>",
                        "<p>No error</p>",
                        "<p>he lived by the same principals</p>",
                        "<p>People admired him, for</p>"
                    ],
                    options_hi: [
                        "<p>that he preached</p>",
                        "<p>No error</p>",
                        "<p>he lived by the same principals</p>",
                        "<p>People admired him, for</p>"
                    ],
                    solution_en: "<p>77.(c) he lived by the same principals<br>&lsquo;Principals&rsquo; must be replaced with &lsquo;principles&rsquo; as the given sentence talks about a kind of belief or idea that guides you. Hence, &lsquo;he lived by the same principles&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>77.(c) he lived by the same principals<br>&lsquo;Principals&rsquo; के स्थान पर &lsquo;principles&rsquo; का प्रयोग होगा, क्योंकि दिया गया sentence एक प्रकार के विश्वास या विचार के बारे में बात करता है जो आपका मार्गदर्शन करता है। अतः, &lsquo;he lived by the same principles&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. The genuine beliefs, though not unconsciously acquired by most children; and even if they depart from these beliefs in later life<br>B. Something of them remains deeply implanted, ready to emerge in a time of stress of crisis<br>C. The importance of education in forming character and opinion is very great and well recognised by all<br>D. Education is, as a rule, the strongest force on the side of what exists and again fundamental change</p>",
                    question_hi: "<p>78. Sentences of a paragraph are given below in jumbled order. Arrange the sentences in the correct order to form a meaningful and coherent paragraph.<br>A. The genuine beliefs, though not unconsciously acquired by most children; and even if they depart from these beliefs in later life<br>B. Something of them remains deeply implanted, ready to emerge in a time of stress of crisis<br>C. The importance of education in forming character and opinion is very great and well recognised by all<br>D. Education is, as a rule, the strongest force on the side of what exists and again fundamental change</p>",
                    options_en: [
                        "<p>DCAB</p>",
                        "<p>DABC</p>",
                        "<p>CBAD</p>",
                        "<p>CABD</p>"
                    ],
                    options_hi: [
                        "<p>DCAB</p>",
                        "<p>DABC</p>",
                        "<p>CBAD</p>",
                        "<p>CABD</p>"
                    ],
                    solution_en: "<p>78.(d) CABD<br>Sentence C will be the starting line as it introduces the main idea of the parajumble i.e. &lsquo;importance of education in forming character and opinion&rsquo;. And, Sentence A states that this importance is well recognised by all the genuine beliefs. So, A will follow C. Further, Sentence B states that despite departure from these beliefs, something of them remains deeply implanted &amp; Sentence D states that education is the greatest force and fundamental change. So, D will follow B. Going through the options, option &lsquo;d&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>78.(d) CABD<br>Sentence C प्रारंभिक line होगी क्योंकि यह parajumble के मुख्य विचार &lsquo;importance of education in forming character and opinion&rsquo; का परिचय देता है। और Sentence A बताता है कि इस महत्व (importance) को सभी वास्तविक मान्यताओं(beliefs) द्वारा अच्छी तरह से पहचाना जाता है। इसलिए, C के बाद A आएगा। इसके अलावा, Sentence B बताता है कि इन मान्यताओं से अलग होने के बावजूद, उनमें से कुछ deeply निहित हैं और Sentence D बताता है कि शिक्षा सबसे बड़ी शक्ति और मौलिक परिवर्तन (fundamental change) है। इसलिए, B के बाद D आएगा। अतः options के माध्यम से जाने पर, option &lsquo;d&rsquo; में सही sequence है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>To fall back on</p>",
                    question_hi: "<p>79. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>To fall back on</p>",
                    options_en: [
                        "<p>to oppose something important</p>",
                        "<p>to suffer an injury on the back in an accident</p>",
                        "<p>to fail to do something important in time</p>",
                        "<p>to seek support out of necessity</p>"
                    ],
                    options_hi: [
                        "<p>to oppose something important</p>",
                        "<p>to suffer an injury on the back in an accident</p>",
                        "<p>to fail to do something important in time</p>",
                        "<p>to seek support out of necessity</p>"
                    ],
                    solution_en: "<p>79.(d) To fall back on- to seek support out of necessity. <br>Example- If his startup fails he will have to fall back on his job.</p>",
                    solution_hi: "<p>79.(d) To fall back on - to seek support out of necessity. <br>Eg - अगर उनका स्टार्टअप विफल हो जाता है तो उन्हें अपने काम पर वापस जाना होगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the most appropriate option that can substitute the underlined segment in the given sentence. If there is no need to substitute it, select \'No substitution required\'.<br>The little boy cried with pain <span style=\"text-decoration: underline;\">when he burnt</span> his fingers.</p>",
                    question_hi: "<p>80. Select the most appropriate option that can substitute the underlined segment in the&nbsp;given sentence. If there is no need to substitute it, select \'No substitution required\'.<br>The little boy cried with pain <span style=\"text-decoration: underline;\">when he burnt</span> his fingers.</p>",
                    options_en: [
                        "<p>No substitution required</p>",
                        "<p>while he burn his fingers</p>",
                        "<p>as he burning his fingers</p>",
                        "<p>when he burns his fingers</p>"
                    ],
                    options_hi: [
                        "<p>No substitution required</p>",
                        "<p>while he burn his fingers</p>",
                        "<p>as he burning his fingers</p>",
                        "<p>when he burns his fingers</p>"
                    ],
                    solution_en: "<p>80.(a) <strong>No substitution required</strong><br>The given sentence is grammatically correct.</p>",
                    solution_hi: "<p>80.(a) <strong>No substitution required</strong><br>दिया गया sentence grammatically सही है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the most appropriate option that can substitute the underlined words in the&nbsp;given sentence.<br>It is a general belief that the female ostrich <span style=\"text-decoration: underline;\">guard the nest at night.</span></p>",
                    question_hi: "<p>81. Select the most appropriate option that can substitute the underlined words in the&nbsp;given sentence.<br>It is a general belief that the female ostrich <span style=\"text-decoration: underline;\">guard the nest at night.</span></p>",
                    options_en: [
                        "<p>is guarding the nest at night</p>",
                        "<p>does guard the nest in night</p>",
                        "<p>guards the nest at night</p>",
                        "<p>guarded the nest at night</p>"
                    ],
                    options_hi: [
                        "<p>is guarding the nest at night</p>",
                        "<p>does guard the nest in night</p>",
                        "<p>guards the nest at night</p>",
                        "<p>guarded the nest at night</p>"
                    ],
                    solution_en: "<p>81.(c) <strong>guards the nest at night</strong><br>According to the &ldquo;Subject-Verb Agreement Rule&rdquo;, a singular subject always takes a singular verb. At the end of a singular verb, s/es is used. In the given sentence, &lsquo;female ostrich&rsquo; is a singular subject that will take &lsquo;guards&rsquo; as a singular verb. Hence, &lsquo;guards the nest at night&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>81.(c) <strong>guards the nest at night</strong><br>&ldquo;Subject-Verb Agreement Rule&rdquo; के अनुसार, singular subject के साथ हमेशा singular verb का प्रयोग होता है। Singular verb के अंत में s/es का प्रयोग होता है। दिए गए sentence में, &lsquo;female ostrich&rsquo; एक singular subject है जिसके साथ &lsquo;guards&rsquo; singular verb का प्रयोग होगा। अतः, &lsquo;guards the nest at night&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>I said to Rana, \"Do have a cup of tea.&rdquo;</p>",
                    question_hi: "<p>82. Choose the most appropriate option to change the narration (direct/indirect) of the given sentence:<br>I said to Rana, \"Do have a cup of tea.&rdquo;</p>",
                    options_en: [
                        "<p>I requested to have a cup of tea.</p>",
                        "<p>I requested Rana to have a cup of tea.</p>",
                        "<p>I requested Rana for tea.</p>",
                        "<p>I told Rana to have a cup of tea.</p>"
                    ],
                    options_hi: [
                        "<p>I requested to have a cup of tea.</p>",
                        "<p>I requested Rana to have a cup of tea.</p>",
                        "<p>I requested Rana for tea.</p>",
                        "<p>I told Rana to have a cup of tea.</p>"
                    ],
                    solution_en: "<p>82.(b) I requested Rana to have a cup of tea.<br>(a) I requested to have a cup of tea. (Listener is missing)<br>(c) I requested Rana for tea. (Meaning of sentence changed)<br>(d) I told Rana to have a cup of tea. (The word do here is laying stress which has not been used here)</p>",
                    solution_hi: "<p>82.(b) I requested Rana to have a cup of tea.<br>(a) I requested to have a cup of tea. (Listener गायब है)<br>(c) I requested Rana for tea. (वाक्य का अर्थ बदल गया)<br>(d) I told Rana to have a cup of tea. (Do शब्द जोर (stress) देने के लिए किया जा रहा है जिसका प्रयोग यहाँ नहीं किया गया है)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the option that expresses the given sentence in passive voice.<br>Historians tell us how this country was divided into three.</p>",
                    question_hi: "<p>83. Select the option that expresses the given sentence in passive voice.<br>Historians tell us how this country was divided into three.</p>",
                    options_en: [
                        "<p>We are told by historians how this country has been divided into three.</p>",
                        "<p>We are told by historians how this country divided into three.</p>",
                        "<p>We are told by historians how this country was divided into three.</p>",
                        "<p>We have been told by historians how this country was divided into three.</p>"
                    ],
                    options_hi: [
                        "<p>We are told by historians how this country has been divided into three.</p>",
                        "<p>We are told by historians how this country divided into three.</p>",
                        "<p>We are told by historians how this country was divided into three.</p>",
                        "<p>We have been told by historians how this country was divided into three.</p>"
                    ],
                    solution_en: "<p>83.(c) We are told by historians how this country was divided into three. (Correct)<br>(a) We are told by historians how this country <span style=\"text-decoration: underline;\">has been divided</span> into three. (Incorrect Tense)<br>(b) We are told by historians how this <span style=\"text-decoration: underline;\">country divided</span> into three. (Helping verb is missing)<br>(c) We <span style=\"text-decoration: underline;\">have been told</span> by historians how this country was divided into three. (Incorrect Tense)</p>",
                    solution_hi: "<p>83.(c) We are told by historians how this country was divided into three. (Correct)<br>(a) We are told by historians how this country <span style=\"text-decoration: underline;\">has been divided</span> into three. (गलत Tense)<br>(b) We are told by historians how this <span style=\"text-decoration: underline;\">country divided</span> into three. (Helping verb missing है)<br>(c) We <span style=\"text-decoration: underline;\">have been told</span> by historians how this country was divided into three. (गलत Tense)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the most appropriate synonym of the given word.<br>Titanic</p>",
                    question_hi: "<p>84. Select the most appropriate synonym of the given word.<br>Titanic</p>",
                    options_en: [
                        "<p>Deep</p>",
                        "<p>Huge</p>",
                        "<p>Disastrous</p>",
                        "<p>Attractive</p>"
                    ],
                    options_hi: [
                        "<p>Deep</p>",
                        "<p>Huge</p>",
                        "<p>Disastrous</p>",
                        "<p>Attractive</p>"
                    ],
                    solution_en: "<p>84.(b) <strong>Huge-</strong> very large in size, extent, or amount.<br><strong>Titanic-</strong> extremely powerful, strong, important, or large.<br><strong>Disastrous-</strong> causing great damage.<br><strong>Attractive-</strong> beautiful or nice to look at.</p>",
                    solution_hi: "<p>84.(b) <strong>Huge</strong> (विशाल) - very large in size, extent, or amount.<br><strong>Titanic</strong> (विशाल) - extremely powerful, strong, important, or large.<br><strong>Disastrous</strong> (विनाशकारी) - causing great damage.<br><strong>Attractive</strong> (आकर्षक) - beautiful or nice to look at.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct sequence to form a meaningful sentence.<br>(P) the sun<br>(Q) to our planet, Earth<br>(O) provides essential energy<br>(R) sustaining life and warmth</p>",
                    question_hi: "<p>85. Parts of a sentence are given below in jumbled order. Select the option that arranges the parts in the correct sequence to form a meaningful sentence.<br>(P) the sun<br>(Q) to our planet, Earth<br>(O) provides essential energy<br>(R) sustaining life and warmth</p>",
                    options_en: [
                        "<p>POQR</p>",
                        "<p>QROP</p>",
                        "<p>ROQP</p>",
                        "<p>ORPQ</p>"
                    ],
                    options_hi: [
                        "<p>POQR</p>",
                        "<p>QROP</p>",
                        "<p>ROQP</p>",
                        "<p>ORPQ</p>"
                    ],
                    solution_en: "<p>85.(a) POQR<br>The given sentence starts with Part P as it contains the main subject of the sentence, i.e. The Sun. Part P will be followed by Part O as it contains the main verb(provides) and its object(essential energy). Further, Part Q states that Earth is the planet that gets essential energy and Part R states that the energy sustains life and warmth. Going through the options, option &lsquo;a&rsquo; has the correct sequence.</p>",
                    solution_hi: "<p>85.(a) POQR<br>दिया गया Sentence, Part P से प्रारंभ होता है क्योंकि इसमें sentence का मुख्य विषय, &lsquo;The Sun&rsquo; शामिल है। Part P के बाद Part O आएगा क्योंकि इसमें main verb (provides) और उसका object (essential energy) शामिल है। इसके अलावा, Part Q बताता है कि पृथ्वी वह ग्रह है जिसे आवश्यक ऊर्जा मिलती है और Part R बताता है कि ऊर्जा जीवन एवं ऊष्मा को बनाए रखती है। अतः options के माध्यम से जाने पर, option &lsquo;a&rsquo; में सही sequence है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Find the correctly spelt word.</p>",
                    question_hi: "<p>86. Find the correctly spelt word.</p>",
                    options_en: [
                        "<p>coroborate</p>",
                        "<p>explainatory</p>",
                        "<p>hygeine</p>",
                        "<p>believable</p>"
                    ],
                    options_hi: [
                        "<p>coroborate</p>",
                        "<p>explainatory</p>",
                        "<p>hygeine</p>",
                        "<p>believable</p>"
                    ],
                    solution_en: "<p>86.(d) Believable.<br>Other words- corroborate , explanatory , hygiene</p>",
                    solution_hi: "<p>86.(d) Believable.<br>Other words- corroborate , explanatory , hygiene</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Identify the segment in the sentence, which contains the error.<br>He looked upon me eye to eye for a few moments before he spoke.</p>",
                    question_hi: "<p>87. Identify the segment in the sentence, which contains the error.<br>He looked upon me eye to eye for a few moments before he spoke.</p>",
                    options_en: [
                        "<p>before he spoke</p>",
                        "<p>No error</p>",
                        "<p>eye to eye for a few moments</p>",
                        "<p>he looked upon me</p>"
                    ],
                    options_hi: [
                        "<p>before he spoke</p>",
                        "<p>No error</p>",
                        "<p>eye to eye for a few moments</p>",
                        "<p>he looked upon me</p>"
                    ],
                    solution_en: "<p>87.(d) he looked upon me<br><strong>&lsquo;Looked upon me&rsquo;</strong> is incorrect. Use &ldquo;look at me.&rdquo; Look at something is a phrasal verb which means to look in a specific direction.</p>",
                    solution_hi: "<p>87.(d) he looked upon me<br><strong>&lsquo;Looked upon me&rsquo;</strong> गलत है. &ldquo;look at me.&rdquo; का प्रयोग करें। Look at something एक phrasal verb है जिसका अर्थ है किसी विशिष्ट दिशा में देखना।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate meaning of the idiom (in the context).<br>Fresh out of college, Ram found it difficult to get a job as he was <strong><span style=\"text-decoration: underline;\">wet behind the ears.</span></strong></p>",
                    question_hi: "<p>88. Select the most appropriate meaning of the idiom (in the context).<br>Fresh out of college, Ram found it difficult to get a job as he was <span style=\"text-decoration: underline;\"><strong>wet behind the ears.</strong></span></p>",
                    options_en: [
                        "<p>unsuitable</p>",
                        "<p>inexperienced</p>",
                        "<p>unhealthy</p>",
                        "<p>irresponsible</p>"
                    ],
                    options_hi: [
                        "<p>unsuitable</p>",
                        "<p>inexperienced</p>",
                        "<p>unhealthy</p>",
                        "<p>irresponsible</p>"
                    ],
                    solution_en: "<p>88.(b) inexperienced<br>Idiom <strong>wet behind the ears</strong> means young and without much experience; naive.</p>",
                    solution_hi: "<p>88.(b) inexperienced<br><strong>Wet behind the ears</strong> का अर्थ है- बिना अनुभव के या अनाड़ी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the option that expresses the given sentence in passive voice.<br>The company will launch the new product next month, and they are already promoting it through various marketing channels.</p>",
                    question_hi: "<p>89. Select the option that expresses the given sentence in passive voice.<br>The company will launch the new product next month, and they are already promoting it through various marketing channels.</p>",
                    options_en: [
                        "<p>The new product will be launched by the company next month, and it is already being promoted through various marketing channels.</p>",
                        "<p>The new product were been launched by the company next month, and it already was promoting through various marketing channels.</p>",
                        "<p>The new product was launched by the company next month, and it is already promoted through various marketing channels.</p>",
                        "<p>The new product will have been launched by the company next month, and it was already being promoted through various marketing channels.</p>"
                    ],
                    options_hi: [
                        "<p>The new product will be launched by the company next month, and it is already being promoted through various marketing channels.</p>",
                        "<p>The new product were been launched by the company next month, and it already was promoting through various marketing channels.</p>",
                        "<p>The new product was launched by the company next month, and it is already promoted through various marketing channels.</p>",
                        "<p>The new product will have been launched by the company next month, and it was already being promoted through various marketing channels.</p>"
                    ],
                    solution_en: "<p>89.(a) The new product will be launched by the company next month, and it is already being promoted through various marketing channels. (Correct)<br>(b) The new product <span style=\"text-decoration: underline;\">were been</span> launched by the company next month, and it already was promoting through various marketing channels. (Incorrect Verb)<br>(c) The new product <span style=\"text-decoration: underline;\">was launched</span> by the company next month, and it is already promoted through various marketing channels. (Incorrect Tense)<br>(d) The new product <span style=\"text-decoration: underline;\">will have been</span> launched by the company next month, and it was already being promoted through various marketing channels. (Incorrect Helping Verb)</p>",
                    solution_hi: "<p>89.(a) The new product will be launched by the company next month, and it is already being promoted through various marketing channels. (Correct)<br>(b) The new product <span style=\"text-decoration: underline;\">were been</span> launched by the company next month, and it already was promoting through various marketing channels. (गलत Verb)<br>(c) The new product <span style=\"text-decoration: underline;\">was launched</span> by the company next month, and it is already promoted through various marketing channels. (गलत Tense)<br>(d) The new product <span style=\"text-decoration: underline;\">will have been</span> launched by the company next month, and it was already being promoted through various marketing channels. (गलत Helping Verb)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>Study of the interaction of people with their environment.</p>",
                    question_hi: "<p>90. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>Study of the interaction of people with their environment.</p>",
                    options_en: [
                        "<p>ecology</p>",
                        "<p>Psychology</p>",
                        "<p>philosophy</p>",
                        "<p>geography</p>"
                    ],
                    options_hi: [
                        "<p>ecology</p>",
                        "<p>Psychology</p>",
                        "<p>philosophy</p>",
                        "<p>geography</p>"
                    ],
                    solution_en: "<p>90.(a) ecology<br>Ecology - the branch of biology that deals with the relations of organisms to one another and to their physical surroundings.<br>Psychology - the scientific study of the human mind and its functions, especially those affecting behaviour in a given context.<br>Philosophy - the study of the fundamental nature of knowledge, reality, and existence, especially when considered as an academic discipline.<br>Geography - the study of the physical features of the earth and its atmosphere,</p>",
                    solution_hi: "<p>90.(a) ecology<br>Ecology - जीव विज्ञान की शाखा जो जीवों के एक दूसरे से और उनके भौतिक परिवेश के संबंधों से संबंधित है।<br>Psychology - मानव मन और उसके कार्यों का वैज्ञानिक अध्ययन, विशेष रूप से वे जो किसी संदर्भ में व्यवहार को प्रभावित करते हैं।<br>Philosophy - ज्ञान, वास्तविकता और अस्तित्व की मौलिक प्रकृति का अध्ययन, विशेष रूप से जब एक अकादमिक अनुशासन के रूप में माना जाता है।<br>Geography - पृथ्वी और उसके वातावरण की भौतिक विशेषताओं का अध्ययन।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>Study of insects is called</p>",
                    question_hi: "<p>91. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>Study of insects is called</p>",
                    options_en: [
                        "<p>Ecology</p>",
                        "<p>Embryology</p>",
                        "<p>Entomology</p>",
                        "<p>Biology</p>"
                    ],
                    options_hi: [
                        "<p>Ecology</p>",
                        "<p>Embryology</p>",
                        "<p>Entomology</p>",
                        "<p>Biology</p>"
                    ],
                    solution_en: "<p>91.(c) <strong>Entomology.</strong> <br><strong>Ecology-</strong> the branch of biology that deals with the relations of organisms to one another and to their physical surroundings.<br><strong>Embryology-</strong> the branch of biology and medicine concerned with the study of embryos and their development.<br><strong>Entomology-</strong> the branch of zoology concerned with the study of insects.<br><strong>Biology-</strong> the study of living organisms, divided into many specialized fields that cover their morphology, physiology, anatomy, behaviour, origin, and distribution.</p>",
                    solution_hi: "<p>91.(c) <strong>Entomology.</strong> <br><strong>Ecology-</strong> जीव विज्ञान की शाखा जो जीवों के एक दूसरे से और उनके भौतिक परिवेश के संबंधों से संबंधित है।<br><strong>Embryology</strong> - भ्रूण और उनके विकास के अध्ययन से संबंधित जीव विज्ञान और चिकित्सा की शाखा।<br><strong>Entomology</strong> - कीड़ों के अध्ययन से संबंधित जूलॉजी की शाखा।<br><strong>Biology</strong> - जीवित जीवों का अध्ययन।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the most appropriate homonym to fill in the blank.<br>Sonu eats _______ like a rabbit.</p>",
                    question_hi: "<p>92. Select the most appropriate homonym to fill in the blank.<br>Sonu eats _______ like a rabbit.</p>",
                    options_en: [
                        "<p>carat</p>",
                        "<p>carrot</p>",
                        "<p>caret</p>",
                        "<p>karat</p>"
                    ],
                    options_hi: [
                        "<p>carat</p>",
                        "<p>carrot</p>",
                        "<p>caret</p>",
                        "<p>karat</p>"
                    ],
                    solution_en: "<p>92.(b) <strong>carrot</strong><br>The given passage states that Sonu eats carrot like a rabbit. Hence &lsquo;carrot&rsquo; is the right answer.</p>",
                    solution_hi: "<p>92.(b) <strong>carrot</strong><br>दिए गए passage में बताया गया है कि सोनू खरगोश (rabbit) की तरह गाजर (carrot) खाता है। अतः &lsquo;carrot&rsquo; सही उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Select the most appropriate synonym of the given word.<br>Respect</p>",
                    question_hi: "<p>93. Select the most appropriate synonym of the given word.<br>Respect</p>",
                    options_en: [
                        "<p>Disrespect</p>",
                        "<p>Invalidate</p>",
                        "<p>Honour</p>",
                        "<p>Attend</p>"
                    ],
                    options_hi: [
                        "<p>Disrespect</p>",
                        "<p>Invalidate</p>",
                        "<p>Honour</p>",
                        "<p>Attend</p>"
                    ],
                    solution_en: "<p>93.(c) <strong>Honour</strong> - high respect.<br><strong>Respect</strong> - a feeling of deep admiration for someone or something.<br><strong>Disrespect</strong> - lack of respect.<br><strong>Invalidate</strong> - to show that an idea, a story, an argument, etc. is wrong.<br><strong>Attend</strong> - be present at a place.</p>",
                    solution_hi: "<p>93.(c) <strong>Honour</strong> (सम्मान) - high respect.<br><strong>Respect</strong> (आदर) - a feeling of deep admiration for someone or something.<br><strong>Disrespect</strong> (अनादर) - lack of respect.<br><strong>Invalidate</strong> (अमान्य करना) - to show that an idea, a story, an argument, etc. is wrong.<br><strong>Attend</strong> (उपस्थित होना) - be present at a place.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Select the most appropriate ANTONYM of the underlined word.<br>The rocky terrain made the hike difficult and <span style=\"text-decoration: underline;\">strenuous.</span></p>",
                    question_hi: "<p>94. Select the most appropriate ANTONYM of the underlined word.<br>The rocky terrain made the hike difficult and <span style=\"text-decoration: underline;\">strenuous.</span></p>",
                    options_en: [
                        "<p>Lively</p>",
                        "<p>Demanding</p>",
                        "<p>Easy</p>",
                        "<p>Intense</p>"
                    ],
                    options_hi: [
                        "<p>Lively</p>",
                        "<p>Demanding</p>",
                        "<p>Easy</p>",
                        "<p>Intense</p>"
                    ],
                    solution_en: "<p>94.(c) <strong>Easy-</strong> without difficulty or effort.<br><strong>Strenuous-</strong> using or needing a lot of effort.<br><strong>Lively-</strong> full of energy and interest.<br><strong>Demanding-</strong> needing a lot of your time, attention or effort.<br><strong>Intense-</strong> of extreme force, degree, or strength.</p>",
                    solution_hi: "<p>94.(c) <strong>Easy</strong> (आसान) - without difficulty or effort.<br><strong>Strenuous</strong> (कठिन प्रयास) - using or needing a lot of effort.<br><strong>Lively</strong> (जीवंत/ ओजस्वी) - full of energy and interest.<br><strong>Demanding</strong> (मांग करना) - needing a lot of your time, attention or effort.<br><strong>Intense</strong> (तीव्र) - of extreme force, degree, or strength.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. Select the most appropriate option that can best replace the bracketed word to fill in&nbsp;the blank<br>His _______stature was not a measure of his overall personality. (scrawny)</p>",
                    question_hi: "<p>95. Select the most appropriate option that can best replace the bracketed word to fill in&nbsp;the blank<br>His ________ stature was not a measure of his overall personality. (scrawny)</p>",
                    options_en: [
                        "<p>thin</p>",
                        "<p>sickly</p>",
                        "<p>keen</p>",
                        "<p>muscular</p>"
                    ],
                    options_hi: [
                        "<p>thin</p>",
                        "<p>sickly</p>",
                        "<p>keen</p>",
                        "<p>muscular</p>"
                    ],
                    solution_en: "<p>95.(a) <strong>thin</strong><br>&lsquo;Scrawny&rsquo; means unattractively thin and bony. The given sentence states that his thin stature was not a measure of his overall personality. Hence, &lsquo;thin&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(a) <strong>thin</strong><br>&lsquo;Scrawny&rsquo; का अर्थ है unattractively thin और हड्डीदार (bony)। दिए गए sentence में बताया गया है कि उसका छोटा कद उसके समग्र व्यक्तित्व (overall personality) का माप नहीं था। अतः, &lsquo;thin&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:-&nbsp;</strong><br>Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (96)_____ of summer rains, winter rains and northeast monsoons in Kerala, (97)_____ it one of the very few (98)_____ in India enjoying equatorial rainforest type climate, with no (99) _____ dry season. The well-distributed rainfall pattern of Kanjirapally is the (100)_____ reason for the phenomenon of high yield of latex from rubber plantations in and around the town.&nbsp;<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:-&nbsp;</strong><br>Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (96)_____ of summer rains, winter rains and northeast monsoons in Kerala, (97)_____ it one of the very few (98)_____ in India enjoying equatorial rainforest type climate, with no (99) _____ dry season. The well-distributed rainfall pattern of Kanjirapally is the (100)_____ reason for the phenomenon of high yield of latex from rubber plantations in and around the town.<br>Select the most appropriate option to fill in the blank no 96.</p>",
                    options_en: [
                        "<p>amount</p>",
                        "<p>number</p>",
                        "<p>expanse</p>",
                        "<p>extent</p>"
                    ],
                    options_hi: [
                        "<p>amount</p>",
                        "<p>number</p>",
                        "<p>expanse</p>",
                        "<p>extent</p>"
                    ],
                    solution_en: "<p>96.(a) The amount of something is how much of it there is, a quantity of something. The given passage states that Kanjirapally receives the highest amount of summer rains. Hence, &lsquo;amount&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(a) The amount of something का अर्थ है किसी चीज की मात्रा। दिए गए passage में कहा गया है कि कांजीरापल्ली में सबसे अधिक ग्रीष्मकालीन वर्षा होती है। इसलिए, \' &lsquo;amount&rsquo; \' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:-&nbsp;</strong><br>Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (96)_____ of summer rains, winter rains and northeast monsoons in Kerala, (97)_____ it one of the very few (98)_____ in India enjoying equatorial rainforest type climate, with no (99) _____ dry season. The well-distributed rainfall pattern of Kanjirapally is the (100)_____ reason for the phenomenon of high yield of latex from rubber plantations in and around the town.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:-&nbsp;</strong><br>Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (96)_____ of summer rains, winter rains and northeast monsoons in Kerala, (97)_____ it one of the very few (98)_____ in India enjoying equatorial rainforest type climate, with no (99) _____ dry season. The well-distributed rainfall pattern of Kanjirapally is the (100)_____ reason for the phenomenon of high yield of latex from rubber plantations in and around the town.<br>Select the most appropriate option to fill in the blank no 97.</p>",
                    options_en: [
                        "<p>make</p>",
                        "<p>making</p>",
                        "<p>made</p>",
                        "<p>makes</p>"
                    ],
                    options_hi: [
                        "<p>make</p>",
                        "<p>making</p>",
                        "<p>made</p>",
                        "<p>makes</p>"
                    ],
                    solution_en: "<p>97.(b) Making means to make somebody/something become something. The given passage states that Kanjirapally receives the highest amount of summer rain in Kerala making it one of the very few places in India that enjoys an equatorial rainforest-type climate. Hence, &lsquo;making&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) Making का अर्थ है किसी को कुछ बनाना। दिए गए passage में कहा गया है कि केरल में कांजीरापल्ली में सबसे अधिक ग्रीष्मकालीन वर्षा होती है, जिससे यह भारत के उन कुछ स्थानों में से एक है जहाँ भूमध्यरेखीय वर्षा वन-प्रकार (equatorial rainforest) की जलवायु पायी जाती है । इसलिए, \'Making\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:-&nbsp;</strong><br>Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (96)_____ of summer rains, winter rains and northeast monsoons in Kerala, (97)_____ it one of the very few (98)_____ in India enjoying equatorial rainforest type climate, with no (99) _____ dry season. The well-distributed rainfall pattern of Kanjirapally is the (100)_____ reason for the phenomenon of high yield of latex from rubber plantations in and around the town.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:-&nbsp;</strong><br>Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (96)_____ of summer rains, winter rains and northeast monsoons in Kerala, (97)_____ it one of the very few (98)_____ in India enjoying equatorial rainforest type climate, with no (99) _____ dry season. The well-distributed rainfall pattern of Kanjirapally is the (100)_____ reason for the phenomenon of high yield of latex from rubber plantations in and around the town.<br>Select the most appropriate option to fill in the blank no 98.</p>",
                    options_en: [
                        "<p>space</p>",
                        "<p>places</p>",
                        "<p>spot</p>",
                        "<p>place</p>"
                    ],
                    options_hi: [
                        "<p>space</p>",
                        "<p>places</p>",
                        "<p>spot</p>",
                        "<p>place</p>"
                    ],
                    solution_en: "<p>98.(b) Place means a particular position or area. The given passage states that Kanjirapally makes Kerala one of the very few places in India that enjoys an equatorial rainforest-type climate. Hence, &lsquo;places&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(b) Place का अर्थ है एक विशेष क्षेत्र। दिए गए passage में कहा गया है कि कांजीरापल्ली केरल को भारत के उन कुछ स्थानों में से एक बनाता है जो भूमध्यरेखीय वर्षावन-प्रकार की जलवायु पायी जाती है । इसलिए, \'places&rsquo;\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99.<strong> Cloze Test:-&nbsp;</strong><br>Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (96)_____ of summer rains, winter rains and northeast monsoons in Kerala, (97)_____ it one of the very few (98)_____ in India enjoying equatorial rainforest type climate, with no (99) _____ dry season. The well-distributed rainfall pattern of Kanjirapally is the (100)_____ reason for the phenomenon of high yield of latex from rubber plantations in and around the town.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:-&nbsp;</strong><br>Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (96)_____ of summer rains, winter rains and northeast monsoons in Kerala, (97)_____ it one of the very few (98)_____ in India enjoying equatorial rainforest type climate, with no (99) _____ dry season. The well-distributed rainfall pattern of Kanjirapally is the (100)_____ reason for the phenomenon of high yield of latex from rubber plantations in and around the town.<br>Select the most appropriate option to fill in the blank no 99.</p>",
                    options_en: [
                        "<p>individual</p>",
                        "<p>divergent</p>",
                        "<p>diverse</p>",
                        "<p>distinct</p>"
                    ],
                    options_hi: [
                        "<p>individual</p>",
                        "<p>divergent</p>",
                        "<p>diverse</p>",
                        "<p>distinct</p>"
                    ],
                    solution_en: "<p>99.(d) Distinct means clearly different from something else. The given passage states that Kanjirapally has no different dry season. Hence, &lsquo;distinct&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(d) Distinct का अर्थ है स्पष्ट रूप से किसी चीज से अलग। दिए गए passage में कहा गया है कि कांजीरापल्ली में कोई अलग शुष्क ऋतु नहीं है। इसलिए, Distinct &lsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:-&nbsp;</strong><br>Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (96)_____ of summer rains, winter rains and northeast monsoons in Kerala, (97)_____ it one of the very few (98)_____ in India enjoying equatorial rainforest type climate, with no (99) _____ dry season. The well-distributed rainfall pattern of Kanjirapally is the (100)_____ reason for the phenomenon of high yield of latex from rubber plantations in and around the town.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:-&nbsp;</strong><br>Kanjirapally has the highest number of rainy days in Kerala. It receives the highest (96)_____ of summer rains, winter rains and northeast monsoons in Kerala, (97)_____ it one of the very few (98)_____ in India enjoying equatorial rainforest type climate, with no (99) _____ dry season. The well-distributed rainfall pattern of Kanjirapally is the (100)_____ reason for the phenomenon of high yield of latex from rubber plantations in and around the town.<br>Select the most appropriate option to fill in the blank no 100.</p>",
                    options_en: [
                        "<p>insignificant</p>",
                        "<p>primary</p>",
                        "<p>minor</p>",
                        "<p>initial</p>"
                    ],
                    options_hi: [
                        "<p>insignificant</p>",
                        "<p>primary</p>",
                        "<p>minor</p>",
                        "<p>initial</p>"
                    ],
                    solution_en: "<p>100.(b) Primary means most important or main. The given passage states that the well-distributed rainfall pattern of Kanjirapally is the main reason for the phenomenon of a high yield of latex. Hence, &lsquo;primary&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) Primary का अर्थ है सबसे महत्वपूर्ण या मुख्य। दिए गए passage में कहा गया है कि कंजीरापल्ली का वर्षा चक्र लेटेक्स की उच्च उपज का मुख्य कारण है । इसलिए, \'primary \' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>