<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">60:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 100</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">100</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 60 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. What should come in place of the question mark (?) in the given series based on the English alphabetical order?<br>BXL, DVN, FTP, ?, JPT</p>",
                    question_hi: "<p>1. अंग्रेजी वर्णमाला क्रम के आधार पर दी गई श्रृंखला में प्रश्न चिह्न (?) के स्थान पर क्या आना चाहिए?<br>BXL, DVN, FTP, ?, JPT</p>",
                    options_en: ["<p>GRR</p>", "<p>GRS</p>", 
                                "<p>HRR</p>", "<p>HST</p>"],
                    options_hi: ["<p>GRR</p>", "<p>GRS</p>",
                                "<p>HRR</p>", "<p>HST</p>"],
                    solution_en: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793302323.png\" alt=\"rId4\" width=\"301\" height=\"96\"></p>",
                    solution_hi: "<p>1.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793302323.png\" alt=\"rId4\" width=\"301\" height=\"96\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. In the following question below are given some statements followed by some conclusions based on those statements. Taking the given statements to be true even if they seem to be at variance from commonly known facts. Read all the conclusions and then decide which of the given conclusion(s) logically follows the given statements.<br><strong>Statements</strong> :<br>1. Some table are white.<br>2. No road is white.<br><strong>Conclusions</strong> :<br>I. No white is road.<br>II. No road is table.<br>III. No white is table</p>",
                    question_hi: "<p>2. निम्नलिखित प्रश्न में कुछ कथन और उन कथनों के आधार पर कुछ निष्कर्ष दिए गए हैं। आपको दिए गए कथनों को सत्य मानना है, चाहे वे सामान्यतः ज्ञात तथ्यों से भिन्न प्रतीत होते हों। सभी निष्कर्षों को ध्यानपूर्वक पढ़िए और निश्चय कीजिए कि दिए गए निष्कर्षों में से कौन-सा/कौन-से निष्कर्ष कथनों का तार्किक रूप से अनुसरण करता है/करते हैं।<br><strong>कथन:</strong><br>1. कुछ मेज, सफेद हैं।<br>2. कोई सड़क, सफेद नहीं है।<br><strong>निष्कर्ष:</strong><br>I. कोई सफेद, सड़क नहीं है।<br>II. कोई सड़क, मेज नहीं है।<br>III. कोई सफेद, मेज नहीं है।</p>",
                    options_en: ["<p>All conclusion follows</p>", "<p>Both conclusions II and III follows</p>", 
                                "<p>Neither conclusion follows</p>", "<p>Only conclusion I follows</p>"],
                    options_hi: ["<p>सभी निष्कर्ष अनुसरण करते हैं</p>", "<p>दोनों निष्कर्ष ॥ ओर III अनुसरण करते हैं</p>",
                                "<p>कोई भी निष्कर्ष अनुसरण नहीं करता है</p>", "<p>केवल निष्कर्ष। अनुसरण करता है</p>"],
                    solution_en: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793302594.png\" alt=\"rId5\" width=\"328\" height=\"111\"><br>Clearly, we can see that Only conclusion I follows .</p>",
                    solution_hi: "<p>2.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793302693.png\" alt=\"rId6\" width=\"332\" height=\"109\"><br>स्पष्ट रूप से, हम देख सकते हैं कि केवल निष्कर्ष I अनुसरण करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Six letters K, L, M, N, O and P are written on different faces of a dice. Three positions of this dice are shown in the figure. Find the letter on the face opposite to M.<br><strong id=\"docs-internal-guid-1831c56d-7fff-77bd-27ee-2c7ef8e1db52\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf5mfjEtqaoBvtvPwxUkEeYD-sT9Rvl-iyIYRq41ZdUwThddpxIgqEaHbrpfKGgP2_Wb-2OyA23QK2M8AEQJL8fRpD7Nbm9rOsNjX889pXkgpv3AhVIvLxUae0TNDC49fKIonY9Nw?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"338\" height=\"118\"></strong></p>",
                    question_hi: "<p>3. एक पासे के विभिन्न फलकों पर छ: अक्षर K, L, M, N, O और P लिखे गए हैं। नीचे चित्र में इस पासे की तीन स्थितियाँ दिखाई गई है। अक्षर M के विपरीत फलक पर कौन-सा अक्षर है?<br><strong id=\"docs-internal-guid-1831c56d-7fff-77bd-27ee-2c7ef8e1db52\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf5mfjEtqaoBvtvPwxUkEeYD-sT9Rvl-iyIYRq41ZdUwThddpxIgqEaHbrpfKGgP2_Wb-2OyA23QK2M8AEQJL8fRpD7Nbm9rOsNjX889pXkgpv3AhVIvLxUae0TNDC49fKIonY9Nw?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"338\" height=\"118\"></strong></p>",
                    options_en: ["<p>P</p>", "<p>N</p>", 
                                "<p>K</p>", "<p>L</p>"],
                    options_hi: ["<p>P</p>", "<p>N</p>",
                                "<p>K</p>", "<p>L</p>"],
                    solution_en: "<p>3.(b) From 1st and 2nd dice K and O are common. The opposite face of M is N.</p>",
                    solution_hi: "<p>3.(b) पहले और दूसरे पासे से K और O उभयनिष्ठ हैं। M का विपरीत फलक N है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. If 28 A 49 B 77 C 11 = -14 and 30 A 50 B 88 C 22 = -16, then 85 A 15 B 40 C 8 =?</p>",
                    question_hi: "<p>4. यदि 28 A 49 B 77 C 11 = -14 और 30 A 50 B 88 C 22 = -16, है, तो 85 A 15 B 40 C 8 =?</p>",
                    options_en: ["<p>84</p>", "<p>75</p>", 
                                "<p>70</p>", "<p>80</p>"],
                    options_hi: ["<p>84</p>", "<p>75</p>",
                                "<p>70</p>", "<p>80</p>"],
                    solution_en: "<p>4.(b)&nbsp;After replacing A, B and C with &lsquo;-&rsquo;, &lsquo;+&rsquo;, &lsquo;&divide; &rsquo;respectively . we get ; <br>28 A 49 B 77 C 11 = -14 &rArr; 28 - 49 + 77 &divide; 11 = -14<br>30 A 50 B 88 C 22 = -16 &rArr; 30 - 50 + 88 &divide; 22 = -16<br>85 A 15 B 40 C 8 &rArr; 85 - 15 + 40 &divide; 8 = 75</p>",
                    solution_hi: "<p>4.(b)&nbsp;A, B और C को क्रमशः \'-\', \'+\', \'&divide; \'से बदलने के बाद हमें मिलता है ; <br>28 A 49 B 77 C 11 = -14 &rArr; 28 - 49 + 77 &divide; 11 = -14<br>30 A 50 B 88 C 22 = -16 &rArr; 30 - 50 + 88 &divide; 22 = -16<br>85 A 15 B 40 C 8 &rArr; 85 - 15 + 40 &divide; 8 = 75</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Arrange the given words in alphabetical order:<br>1. Response<br>2. Reporter<br>3. Reaction<br>4. Registration<br>5. Relevant</p>",
                    question_hi: "<p>5. दिए गए शब्दों को अंग्रेज़ी वर्णमाला के क्रम में व्यवस्थित कीजिए।<br>1. Response<br>2. Reporter<br>3. Reaction<br>4. Registration<br>5. Relevant</p>",
                    options_en: ["<p>2, 5, 1, 3, 4</p>", "<p>5, 1, 4, 2, 3</p>", 
                                "<p>3, 4, 5, 2, 1</p>", "<p>4, 5, 1, 2, 3</p>"],
                    options_hi: ["<p>2, 5, 1, 3, 4</p>", "<p>5, 1, 4, 2, 3</p>",
                                "<p>3, 4, 5, 2, 1</p>", "<p>4, 5, 1, 2, 3</p>"],
                    solution_en: "<p>5.(c)&nbsp;The correct order is :<br>Reaction(3) &rarr; Registration(4) &rarr; Relevant(5) &rarr; Reporter(2) &rarr; Response(1)</p>",
                    solution_hi: "<p>5.(c)&nbsp;सही क्रम है :<br>Reaction(3) &rarr; Registration(4) &rarr; Relevant(5) &rarr; Reporter(2) &rarr; Response(1)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. Study the given pattern carefully and select the number from among the given options that can replace the question mark (?) in it<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793302895.png\" alt=\"rId8\" width=\"130\" height=\"73\"></p>",
                    question_hi: "<p>6. दिए गए पैटर्न का सावधानीपूर्वक अध्ययन करें और दिए गए विकल्पों में से संख्या का चयन करें जो इसमें प्रश्न चिह्न (?) को बदल सकते हैं<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793302895.png\" alt=\"rId8\" width=\"130\" height=\"73\"></p>",
                    options_en: ["<p>12</p>", "<p>10</p>", 
                                "<p>4</p>", "<p>13</p>"],
                    options_hi: ["<p>12</p>", "<p>10</p>",
                                "<p>4</p>", "<p>13</p>"],
                    solution_en: "<p>6.(b) <strong>Logic</strong> :- (Second number)&nbsp;+ (First number &divide;&nbsp;2) = Third number<br>(6, 8, 11) :- (8) + (6 &divide;&nbsp;2) &rArr; 8 + 3 = 11<br>(2 ,1 ,2) :- (1) + (2 &divide;&nbsp;2) &rArr; 1 + 1 = 2 <br>Similarly,<br>(6, 7, ? ) :- (7) + (6 &divide;&nbsp;2) &rArr; 7 + 3 = 10</p>",
                    solution_hi: "<p>6.(b) <strong>तर्क</strong> :- (दूसरी संख्या) + (पहली संख्या &divide;&nbsp;2) = तीसरी संख्या<br>(6, 8, 11) :- (8) + (6 &divide;&nbsp;2) &rArr; 8 + 3 = 11<br>(2 ,1, 2) :- (1) + (2 &divide;&nbsp;2) &rArr; 1 + 1 = 2 <br>इसी प्रकार,<br>(6, 7, ? ) :- (7) + (6 &divide;&nbsp;2) &rArr; 7 + 3 = 10</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the figure that will replace the question mark (?) in the following figure series. <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303024.png\" alt=\"rId9\" height=\"85\"></p>",
                    question_hi: "<p>7. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जिसे दी गई आकृति श्रृंखला में प्रश्न चिह्न (?) के स्थान पर रखा जा सकता है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303024.png\" alt=\"rId9\" height=\"85\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303170.png\" alt=\"rId10\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303307.png\" alt=\"rId11\" height=\"85\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303409.png\" alt=\"rId12\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303518.png\" alt=\"rId13\" height=\"85\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303170.png\" alt=\"rId10\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303307.png\" alt=\"rId11\" height=\"85\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303409.png\" alt=\"rId12\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303518.png\" alt=\"rId13\" height=\"85\"></p>"],
                    solution_en: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303409.png\" alt=\"rId12\" height=\"85\"></p>",
                    solution_hi: "<p>7.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303409.png\" alt=\"rId12\" height=\"85\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. If a mirror is placed on the line AB, then which of the option figures will be the correct image of the question figure?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303622.png\" alt=\"rId14\" height=\"121\"></p>",
                    question_hi: "<p>8. यदि एक दर्पण को रेखा AB पर रखा जाए, तो विकल्प आकृतियों में से कौन-सी आकृति प्रश्न आकृति का सही प्रतिबिंब होगी?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303622.png\" alt=\"rId14\" height=\"121\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303788.png\" alt=\"rId15\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303906.png\" alt=\"rId16\" height=\"85\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793304005.png\" alt=\"rId17\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793304097.png\" alt=\"rId18\" height=\"85\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303788.png\" alt=\"rId15\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303906.png\" alt=\"rId16\" height=\"85\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793304005.png\" alt=\"rId17\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793304097.png\" alt=\"rId18\" height=\"85\"></p>"],
                    solution_en: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303906.png\" alt=\"rId16\" height=\"85\"></p>",
                    solution_hi: "<p>8.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793303906.png\" alt=\"rId16\" height=\"85\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. Based on the English alphabetical order, three of the following four letter-clusters are alike in a certain way and thus form a group. Which letter-cluster does not belong to that group?<br>(<strong>Note </strong>: The odd letter-cluster is not based on the number of consonants/vowels or their position in the letter-cluster.)</p>",
                    question_hi: "<p>9.अंग्रेजी वर्णमाला क्रम पर आधारित, निम्नलिखित चार अक्षर-समूहों में से तीन किसी निश्चित तरीके से समान हैं और इस प्रकार एक समूह बनाते हैं। किस अक्षर-समूह का संबंध उस समूह से नहीं है?<br>(<strong>नोट </strong>: असंगत अक्षर-समूह, व्यंजनों/स्वरों की संख्या या इस अक्षर-समूह में उनकी स्थिति पर आधारित नहीं है।)</p>",
                    options_en: ["<p>DGI</p>", "<p>FIK</p>", 
                                "<p>ORT</p>", "<p>JMP</p>"],
                    options_hi: ["<p>DGI</p>", "<p>FIK</p>",
                                "<p>ORT</p>", "<p>JMP</p>"],
                    solution_en: "<p>9.(d)<br><strong id=\"docs-internal-guid-27a4a3a5-7fff-7bb7-2258-df1bdaca1db4\"><strong id=\"docs-internal-guid-301c40b5-7fff-4fc1-a07c-4b6f8efca860\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc86vgGK5Nni5rEJsNfvib9ZiDxkdqZtdOOm1Y58XSdbXiY_0dDOp4sdaPNCWLaHjkKWjmDWg19FNDnb79apEY4BEg42nVYGmzooeaDK-j-EHIR29HE_psO9LZ0zjGVeA9ZRPcKtA?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"129\" height=\"65\">,&nbsp; &nbsp; &nbsp;</strong><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcIxmv22KUrDWS3sF8VGgmXtOz_EhAL15IBM1AReNwfhwroLB9r9UbHZwkp2bBLJ7jG-KVflZfIvljgKWfiHsFDiUEDUPhrjYB5a2HRiWszOi0QpoOIgMxqcUlqxs2wCGpmaBx7dw?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"127\" height=\"65\">,&nbsp; &nbsp; &nbsp;</strong><strong id=\"docs-internal-guid-a121faef-7fff-5e96-06e6-2078afb1ef69\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXct3XrzPpcfSkh-M7pP0lYfFkX4GhuinPxpiD329omHTyajWNwumvgyya31ZrGBkN2zkfk4cpb2gYYt_q2arJVELsWD7J2L2quTvgkOcMvFG0WdoSvZvyXoH9hRm_iOAczFcd-R?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"142\" height=\"65\"></strong><br>But,<br><strong id=\"docs-internal-guid-a85ff415-7fff-7705-53e1-96608d78faeb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXco15ESQUMsRIAti0DF-xI4pOwOcHCJICOStjuJQYI1ge_GRyiqG389l5nZ2K0c-vCGB-ZzM5x08n5akkxcbEVpe8fuVw7MYLLvueQDY4HYaYTdXOCD36OQfe1X_0j7SZv0fG8T?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"128\" height=\"65\"></strong></p>",
                    solution_hi: "<p>9.(d)<br><strong id=\"docs-internal-guid-27a4a3a5-7fff-7bb7-2258-df1bdaca1db4\"><strong id=\"docs-internal-guid-301c40b5-7fff-4fc1-a07c-4b6f8efca860\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc86vgGK5Nni5rEJsNfvib9ZiDxkdqZtdOOm1Y58XSdbXiY_0dDOp4sdaPNCWLaHjkKWjmDWg19FNDnb79apEY4BEg42nVYGmzooeaDK-j-EHIR29HE_psO9LZ0zjGVeA9ZRPcKtA?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"129\" height=\"65\">,&nbsp; &nbsp; &nbsp;</strong><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcIxmv22KUrDWS3sF8VGgmXtOz_EhAL15IBM1AReNwfhwroLB9r9UbHZwkp2bBLJ7jG-KVflZfIvljgKWfiHsFDiUEDUPhrjYB5a2HRiWszOi0QpoOIgMxqcUlqxs2wCGpmaBx7dw?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"127\" height=\"65\">,&nbsp; &nbsp; &nbsp;</strong><strong id=\"docs-internal-guid-a121faef-7fff-5e96-06e6-2078afb1ef69\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXct3XrzPpcfSkh-M7pP0lYfFkX4GhuinPxpiD329omHTyajWNwumvgyya31ZrGBkN2zkfk4cpb2gYYt_q2arJVELsWD7J2L2quTvgkOcMvFG0WdoSvZvyXoH9hRm_iOAczFcd-R?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"142\" height=\"65\"></strong><br>लेकिन,<br><strong id=\"docs-internal-guid-a85ff415-7fff-7705-53e1-96608d78faeb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXco15ESQUMsRIAti0DF-xI4pOwOcHCJICOStjuJQYI1ge_GRyiqG389l5nZ2K0c-vCGB-ZzM5x08n5akkxcbEVpe8fuVw7MYLLvueQDY4HYaYTdXOCD36OQfe1X_0j7SZv0fG8T?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"128\" height=\"65\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. In a code language, \'PIG\' is coded as 29 and \'COW\' is coded as 38. How will \'CAMEL\' be coded in the same language?</p>",
                    question_hi: "<p>10. एक कूट भाषा में, \'PIG\' को 29 के रूप में कूटबद्ध किया जाता है और \'COW\' को 38 के रूप में कूटबद्ध किया जाता है। उसी भाषा में \'CAMEL\' को किस प्रकार कूटबद्ध किया जाएगा?</p>",
                    options_en: ["<p>22</p>", "<p>27</p>", 
                                "<p>29</p>", "<p>34</p>"],
                    options_hi: ["<p>22</p>", "<p>27</p>",
                                "<p>29</p>", "<p>34</p>"],
                    solution_en: "<p>10.(c) <strong>Logic</strong> :- (Sum of the place value of letter) - (Number of letter)<br>PIG :- (16 + 9 + 7) - 3 &rArr; (32) - 3 = 29<br>COW :- (3 + 15 + 23) - 3 &rArr; (41) - 3 = 38<br>Similarly,<br>CAMEL :- (3 + 1 + 13 + 5 + 12) - 5 = (34) - 5 = 29</p>",
                    solution_hi: "<p>10.(c) <strong>तर्क</strong> :- (अक्षर के स्थानीय मान का योग) - (अक्षर की संख्या) <br>PIG :- (16 + 9 + 7) - 3 &rArr;(32) - 3 = 29<br>COW :- (3 + 15 + 23) - 3 &rArr;(41) - 3 = 38<br>इसी प्रकार,<br>CAMEL :- (3 + 1 + 13 + 5 + 12) - 5 &rArr; (34) - 5 = 29</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Which of the following numbers will replace the question mark (?) in the given series?<br>45, 90, 154, ?, 347</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन-सी संख्या दी गई श्रृंखला में प्रश्न चिह्न (?) को प्रतिस्थापित करेगी?<br>45, 90, 154, ?, 347</p>",
                    options_en: ["<p>248</p>", "<p>273</p>", 
                                "<p>239</p>", "<p>256</p>"],
                    options_hi: ["<p>248</p>", "<p>273</p>",
                                "<p>239</p>", "<p>256</p>"],
                    solution_en: "<p>11.(c)<br><strong id=\"docs-internal-guid-20a5d4cd-7fff-68d8-66d9-633fa436fb86\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfyQTBT56UPHD7TSKgKkxHKDTMlAuf7bWewBjR3Cil54FSepOBwTKLaBBXW26CY99dqi5lyVqxBI2XjTt3CWgttLadKjiO_wTXBziC0JpRcZtLn7m7rCG_Kn6j__Fjv7ySWVh_4Yw?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"255\" height=\"127\"></strong></p>",
                    solution_hi: "<p>11.(c)<br><strong id=\"docs-internal-guid-20a5d4cd-7fff-68d8-66d9-633fa436fb86\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfyQTBT56UPHD7TSKgKkxHKDTMlAuf7bWewBjR3Cil54FSepOBwTKLaBBXW26CY99dqi5lyVqxBI2XjTt3CWgttLadKjiO_wTXBziC0JpRcZtLn7m7rCG_Kn6j__Fjv7ySWVh_4Yw?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"255\" height=\"127\"></strong></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "<p>12. At a wedding reception, a man pointed towards the groom and claims the groom to be the brother of the son of his grandfather\'s son. Who is the groom to the man?</p>",
                    question_hi: "<p>12. एक शादी के रिसेप्शन में, एक व्यक्ति ने दूल्हे की ओर इशारा किया और दूल्हे को अपने दादा के पुत्र के पुत्र का भाई बताया। दूल्हा उस आदमी का क्या लगता है?</p>",
                    options_en: ["<p>Brother</p>", "<p>Brother-in-law</p>", 
                                "<p>Uncle</p>", "<p>Father</p>"],
                    options_hi: ["<p>भाई</p>", "<p>साला/जीजा</p>",
                                "<p>चाचा</p>", "<p>पिता</p>"],
                    solution_en: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793304852.png\" alt=\"rId24\" width=\"194\" height=\"180\"><br>Groom is the brother of man.</p>",
                    solution_hi: "<p>12.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793304962.png\" alt=\"rId25\" height=\"180\"><br>दूल्हा उस व्यक्ति का भाई है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. P, Q, R, S, T and U are sitting around a circular table facing the centre. Only one person sits between T and R. Only one person sits between R and U. Only two people sit between U and Q . S sits to the immediate left of T. Who sits second to the left of P?</p>",
                    question_hi: "<p>13. P, Q, R, S, T और U एक वृत्ताकार मेज के चारो ओर केंद्र की ओर मुख करके बैठे हैं। T और R के बीच केवल एक व्यक्ति बैठा है। केवल एक व्यक्ति R और U के बीच बैठा है। U और Q के बीच केवल दो व्यक्ति बैठे हैं। S, T के ठीक बाएं बैठा है। P के बाएं से दूसरे स्थान पर कौन बैठा है?</p>",
                    options_en: ["<p>U</p>", "<p>Q</p>", 
                                "<p>R</p>", "<p>S</p>"],
                    options_hi: ["<p>U</p>", "<p>Q</p>",
                                "<p>R</p>", "<p>S</p>"],
                    solution_en: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793305078.png\" alt=\"rId26\" width=\"199\" height=\"152\"></p>",
                    solution_hi: "<p>13.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793305078.png\" alt=\"rId26\" width=\"199\" height=\"152\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. Select the option figure in which the given figure is embedded as its part (rotation is NOT allowed).<br><strong id=\"docs-internal-guid-d7795a8b-7fff-b56e-e405-b3761bdf6475\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXerqtrCZHuTlIAqX-3AYvETpNk9KXkWHtgd-n85tJtxvkmRWcq-PyopnZmrFMCewFPoIg1dZGk4rd3dSDJQEmPcUh3pkK80l6LWNNARSw2E5UJ0QhfUrOF82wG95GLQmDpm-Tbq3w?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"99\" height=\"85\"></strong></p>",
                    question_hi: "<p>14. विकल्प वाली उस आकृति का चयन कीजिए जिसमें दी गई आकृति,उसके एक भाग के रूप में अंतर्निहित हो (घुमाने की अनुमति नहीं है)।<br><strong id=\"docs-internal-guid-3bf9d228-7fff-d900-aa18-35f9abe999af\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXerqtrCZHuTlIAqX-3AYvETpNk9KXkWHtgd-n85tJtxvkmRWcq-PyopnZmrFMCewFPoIg1dZGk4rd3dSDJQEmPcUh3pkK80l6LWNNARSw2E5UJ0QhfUrOF82wG95GLQmDpm-Tbq3w?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"99\" height=\"85\"></strong></p>",
                    options_en: ["<p><strong id=\"docs-internal-guid-240d7a87-7fff-6d9a-e36f-2ee7f24554ef\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdRlpmN30-rntew08IZHbftaFbQ6jOBhUpUcZ9j8tn1p1bE91Yajih0eqIqgmJTDLxgGqKKw3pGrJWH-gZcDrrPI25r7WaPH0Lnr0MGJ0oQrNHJpIcrxRaRWHrYhCAH_W9rbggLFQ?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"98.22828376957209\" height=\"93.66571443105484\"></strong></p>", "<p><strong id=\"docs-internal-guid-3a89e899-7fff-8ea7-79ea-c1073560b21a\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXffZMsZqaMtVh0gSF8pai12FSk-mcVqIjawt6QjAQq6i3m5A2J5Sp3hYzmNBKHYgsL4krUOPSv_8w2eqFOnEFDAM0OUOFkr-uEIUnLiH9dmAbwpIAsM6joEczDz8Ds6K16T_feW?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"98.41058178294105\" height=\"97.5000349682721\"></strong></p>", 
                                "<p><strong id=\"docs-internal-guid-2e7883c9-7fff-9eed-9a93-31b655cae30d\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfTBFPh0J0vbW712BcFHIydzMZheIeOD59aPQ-AiUVCfK8g3DXbyvqX9f_8QaPxlGsKHbtPLTHukGs1CCyag2j8oFU14u94VY293dRXizxAEykuRkS7iN4ledBvLk_Xxy6DOR6t?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"100.28715577381463\" height=\"99.40102814069013\"></strong></p>", "<p><strong id=\"docs-internal-guid-a6d4fc74-7fff-ae9c-ed1b-b639dafc4c61\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfzUGeGX0UPYpdqnGQB039jXek8xW2UuGoyXtDCN9NlpMQefX0gujC0VGDbG3JcYlIS2f7TzvBKXMmJKuABU2UJTOhlUbXfou-LD0jUMWKWNZeAVJALxZJ-kaP4O0LnnBgpk8OQ?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"99.4045928812145\" height=\"111.62588378583885\"></strong></p>"],
                    options_hi: ["<p><strong id=\"docs-internal-guid-3b9fadbe-7fff-e944-c429-f154fede3a6c\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdRlpmN30-rntew08IZHbftaFbQ6jOBhUpUcZ9j8tn1p1bE91Yajih0eqIqgmJTDLxgGqKKw3pGrJWH-gZcDrrPI25r7WaPH0Lnr0MGJ0oQrNHJpIcrxRaRWHrYhCAH_W9rbggLFQ?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"98.22828376957209\" height=\"93.66571443105484\"></strong></p>", "<p><strong id=\"docs-internal-guid-58f14c68-7fff-f75c-de9c-ef2de5ddf220\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXffZMsZqaMtVh0gSF8pai12FSk-mcVqIjawt6QjAQq6i3m5A2J5Sp3hYzmNBKHYgsL4krUOPSv_8w2eqFOnEFDAM0OUOFkr-uEIUnLiH9dmAbwpIAsM6joEczDz8Ds6K16T_feW?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"98.41058178294105\" height=\"97.5000349682721\"></strong></p>",
                                "<p><strong id=\"docs-internal-guid-026f4c6c-7fff-3391-049a-11090c350768\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfTBFPh0J0vbW712BcFHIydzMZheIeOD59aPQ-AiUVCfK8g3DXbyvqX9f_8QaPxlGsKHbtPLTHukGs1CCyag2j8oFU14u94VY293dRXizxAEykuRkS7iN4ledBvLk_Xxy6DOR6t?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"100.28715577381463\" height=\"99.40102814069013\"></strong></p>", "<p><strong id=\"docs-internal-guid-c20051ed-7fff-44fb-c990-024aaeb8837f\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfzUGeGX0UPYpdqnGQB039jXek8xW2UuGoyXtDCN9NlpMQefX0gujC0VGDbG3JcYlIS2f7TzvBKXMmJKuABU2UJTOhlUbXfou-LD0jUMWKWNZeAVJALxZJ-kaP4O0LnnBgpk8OQ?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"99.4045928812145\" height=\"111.62588378583885\"></strong></p>"],
                    solution_en: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793305763.png\" alt=\"rId32\" height=\"85\"></p>",
                    solution_hi: "<p>14.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793305763.png\" alt=\"rId32\" height=\"85\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. In a certain code language, &lsquo;MOVE&rsquo; is coded as &lsquo;3917&rsquo; and &lsquo;EAST&rsquo; is coded as &lsquo;4692&rsquo;.&nbsp;What is the code for &lsquo;E in the given code language?</p>",
                    question_hi: "<p>15. एक निश्चित कूट भाषा में, \'MOVE\' को \'3917\' के रूप में कूटबद्ध किया जाता है और \'EAST\' को \'4692\' के रूप में कूटबद्ध किया जाता है। दी गई कूट भाषा में \'E\' के लिए कूट क्या है?</p>",
                    options_en: ["<p>9</p>", "<p>4</p>", 
                                "<p>2</p>", "<p>7</p>"],
                    options_hi: ["<p>9</p>", "<p>4</p>",
                                "<p>2</p>", "<p>7</p>"],
                    solution_en: "<p>15.(a)&nbsp;MOVE&rsquo; &rarr;&nbsp;&lsquo;3917 &hellip;&hellip;&hellip; (i) <br>&lsquo;EAST&rsquo; &rarr;&nbsp;&lsquo;4692&rsquo; &hellip;&hellip;&hellip; (ii)<br>From (i) and (ii) &lsquo;E&rsquo; and &lsquo;9&rsquo; is common <br>so, The Code of &lsquo;E&rsquo; is &lsquo;9&rsquo;</p>",
                    solution_hi: "<p>15.(a)&nbsp;MOVE&rsquo; &rarr;&nbsp;&lsquo;3917 &hellip;&hellip;&hellip; (i) <br>&lsquo;EAST&rsquo; &rarr;&nbsp;&lsquo;4692&rsquo; &hellip;&hellip;&hellip; (ii)<br>(i) और (ii) से &lsquo;E&rsquo; और &lsquo;9&rsquo; उभयनिष्ठ है ,इसलिए, \'E\' का कोड \'9\' है</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the pair which follows the same pattern as that followed by the two set of pairs given below. Both pairs follow the same pattern.<br>CJM : FMP<br>AHL : DKO</p>",
                    question_hi: "<p>16. उस युग्म को चुनिए, जो उसी पैटर्न का अनुसरण करता है जो नीचे दिए गए युग्मों के दो समुच्चय द्वारा किया जाता है। दोनों युग्म समान पैटर्न का अनुसरण करते हैं।<br>CJM : FMP<br>AHL : DKO</p>",
                    options_en: ["<p>GHA : AHG</p>", "<p>LTV : TVL</p>", 
                                "<p>KOQ : PLJ</p>", "<p>NRS : QUV</p>"],
                    options_hi: ["<p>GHA : AHG</p>", "<p>LTV : TVL</p>",
                                "<p>KOQ : PLJ</p>", "<p>NRS : QUV</p>"],
                    solution_en: "<p>16.(d)<br><strong id=\"docs-internal-guid-662daa08-7fff-7567-8e1d-8d6e59a3dadb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdm2hHi2seoPeHecJbnotj3ixopQ52xZEo3DQ9YP6zdHTJwlxoilFfnz4t3Jtj4uBnx_qHP3GG07MP-jY-C0zOmjJwvUi9wYBCmjMK17n1t84h8v5ZOSd5ETdmq6SDNAzvzjWb55g?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"267\" height=\"115\"></strong><br>Similarly,<br><strong id=\"docs-internal-guid-b98c4391-7fff-8738-ba7c-a07b235025f9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfTmsbQUPa0n-uTpfZqpoy4CtjX5_LM_Hs0gXYPlc7RbnOeDRGO0gRUUT-FH5h7sPvGbpdeXbcrdbeYUATWssLjlddCCjOFxHrvgOempDZBA2HOfiHpVCFRDpJ_oLpvNFu0jk7LSg?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"117\" height=\"107\"></strong></p>",
                    solution_hi: "<p>16.(d)<br><strong id=\"docs-internal-guid-662daa08-7fff-7567-8e1d-8d6e59a3dadb\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdm2hHi2seoPeHecJbnotj3ixopQ52xZEo3DQ9YP6zdHTJwlxoilFfnz4t3Jtj4uBnx_qHP3GG07MP-jY-C0zOmjJwvUi9wYBCmjMK17n1t84h8v5ZOSd5ETdmq6SDNAzvzjWb55g?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"267\" height=\"115\"></strong><br>इसी प्रकार,<br><strong id=\"docs-internal-guid-b98c4391-7fff-8738-ba7c-a07b235025f9\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfTmsbQUPa0n-uTpfZqpoy4CtjX5_LM_Hs0gXYPlc7RbnOeDRGO0gRUUT-FH5h7sPvGbpdeXbcrdbeYUATWssLjlddCCjOFxHrvgOempDZBA2HOfiHpVCFRDpJ_oLpvNFu0jk7LSg?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"117\" height=\"107\"></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. If \'U\' stands for \'<math display=\"inline\"><mo>&#247;</mo></math>\', \'D\' stands for \'&times;\', \'B\' stands for \'+\' and \'T\' stands for \'-\', what will&nbsp;come in place of the question mark (?) in the following equation?<br>13 T 26 U 13 D 24 B 7 = ?</p>",
                    question_hi: "<p>17. यदि \'U\' का अर्थ \'<math display=\"inline\"><mo>&#247;</mo></math>&rsquo;, \'D\' का अर्थ\' &times;\', \'B\' का अर्थ\'+\' और \'T\' का अर्थ\'-\' है, तो निम्नलिखित समीकरण में प्रश्न चिन्ह (?) के स्थान पर क्या आएगा?<br>13 T 26 U 13 D 24 B 7 = ?</p>",
                    options_en: ["<p>- 87</p>", "<p>- 28</p>", 
                                "<p>- 43</p>", "<p>- 56</p>"],
                    options_hi: ["<p>- 87</p>", "<p>- 28</p>",
                                "<p>- 43</p>", "<p>- 56</p>"],
                    solution_en: "<p>17.(b) <strong>Given</strong> :- 13 T 26 U 13 D 24 B 7<br>As per given instruction after interchanging letters with sign we get<br>13 - 26 &divide;&nbsp;13 &times; 24 + 7<br>13 - 2 &times; 24 + 7<br>13 - 48 + 7 = -28</p>",
                    solution_hi: "<p>17.(b) <strong>दिया गया :-</strong> 13 T 26 U 13 D 24 B 7<br>दिए गए निर्देश के अनुसार अक्षरों को चिह्न से बदलने पर हमें प्राप्त होता है<br>13 - 26 &divide;&nbsp;13 &times; 24 + 7<br>13 - 2 &times; 24 + 7<br>13 - 48 + 7 = -28</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. A piece of paper is folded and punched as shown below in the question figures. From the given option figures, indicate how it will appear when opened ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306117.png\" alt=\"rId35\" height=\"85\"></p>",
                    question_hi: "<p>18. निम्नलिखित प्रश्न में दी गई आकृतियों के अनुसार कागज के एक टुकड़े को मोड़कर उसमें छेद किया जाता है। विकल्पों में दी गई आकृतियों में से बताइए कि खोले जाने पर यह कैसा दिखाई देगा?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306117.png\" alt=\"rId35\" height=\"85\"></p>",
                    options_en: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306302.png\" alt=\"rId36\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306400.png\" alt=\"rId37\" height=\"85\"></p>", 
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306500.png\" alt=\"rId38\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306589.png\" alt=\"rId39\" height=\"85\"></p>"],
                    options_hi: ["<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306302.png\" alt=\"rId36\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306400.png\" alt=\"rId37\" height=\"85\"></p>",
                                "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306500.png\" alt=\"rId38\" height=\"85\"></p>", "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306589.png\" alt=\"rId39\" height=\"85\"></p>"],
                    solution_en: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306400.png\" alt=\"rId37\" height=\"85\"></p>",
                    solution_hi: "<p>18.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306400.png\" alt=\"rId37\" height=\"85\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. \'A &times; B\' means \'A is the husband of B\'<br>\'A + B\' means \'A is brother of B\'<br>\'A &divide; B\' means \'A is sister of B\'<br>\'A - B\' means \'A is the wife of B\'<br>How is X related to A in the expression \'X + Y &divide; Z - A\' ?</p>",
                    question_hi: "<p>19. \'A &times; B\' का अर्थ है \'A, B का पति है\'<br>\'A + B\' का अर्थ है \'A, B का भाई है\'<br>\'A &divide; B\' का अर्थ है \'A, B की बहन है\'<br>\'A - B\' का अर्थ है \'A, B की पत्नी है\'<br>व्यंजक \'X + Y &divide; Z - A\' में X का A से क्या संबंध है?</p>",
                    options_en: ["<p>son</p>", "<p>wife\'s brother</p>", 
                                "<p>sister\'s husband</p>", "<p>brother</p>"],
                    options_hi: ["<p>बेटा</p>", "<p>पत्नी का भाई</p>",
                                "<p>बहन का पति</p>", "<p>भाई</p>"],
                    solution_en: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306699.png\" alt=\"rId40\" width=\"186\" height=\"49\"><br>As we can see, X is the wife&rsquo;s brother of A.</p>",
                    solution_hi: "<p>19.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306699.png\" alt=\"rId40\" width=\"186\" height=\"49\"><br>जैसा कि हम देख सकते हैं, X, A की पत्नी का भाई है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. By interchanging the given two signs which of the following equation will be correct?<br>&times; and &divide;<br>I. 5 + 6 &times; 2 - 4 &divide;&nbsp;1 = 10<br>II. 9 &times; 3 - 4 <math display=\"inline\"><mo>&#247;</mo></math> 2 + 5 = 4</p>",
                    question_hi: "<p>20. नीचे दिए गए दो चिन्हों को आपस में बदलने पर निम्नलिखित में से कौन-सा समीकरण सही होगा?<br>&times; और &divide;<br>I. 5 + 6 &times; 2 - 4 &divide;&nbsp;1 = 10<br>II. 9 &times; 3 - 4 &divide;&nbsp;2 + 5 = 4</p>",
                    options_en: ["<p>Only II</p>", "<p>Both I and II</p>", 
                                "<p>Only I</p>", "<p>Neither I nor II</p>"],
                    options_hi: ["<p>केवल II</p>", "<p>। और ।। दोनों</p>",
                                "<p>केवल ।</p>", "<p>न तो। और न ही ।।</p>"],
                    solution_en: "<p>20.(d)&nbsp;Given: <br>I. 5 + 6 &times; 2 - 4 &divide;&nbsp;1 = 10<br>II. 9 &times; 3 - 4 &divide;&nbsp;2 + 5 = 4<br>After interchanging the &times; and &divide;&nbsp;as per the question, we have:<br>I. 5 + 6 &times; 2 - 4 &divide;&nbsp;1 = 10<br>LHS &rArr; 5 + 6 &divide; 2 - 4 &times; 1 <br><math display=\"inline\"><mo>&#8658;</mo></math> 5 + 3 - 4 = 4 &ne; RHS <br>II. 9 &times; 3 - 4 &divide;&nbsp;2 + 5 = 4<br>LHS &rArr; 9 &divide; 3 - 4 &times; 2 + 5 <br>&rArr; 3 - 8 + 5 = 0 &ne; RHS<br>As we can see, neither I nor II is correct.</p>",
                    solution_hi: "<p>20.(d)&nbsp;दिया गया: <br>I. 5 + 6 &times; 2 - 4 &divide;&nbsp;1 = 10<br>II. 9 &times; 3 - 4 &divide;&nbsp;2 + 5 = 4<br>प्रश्न के अनुसार &times; और &divide;&nbsp;को बदलने के बाद, हमें प्राप्त होता है: <br>I. 5 + 6 &times; 2 - 4 &divide;&nbsp;1 = 10<br>LHS <math display=\"inline\"><mo>&#8658;</mo></math> 5 + 6 &divide; 2 - 4 &times; 1 <br><math display=\"inline\"><mo>&#8658;</mo></math> 5 + 3 - 4 = 4 &ne; RHS <br>II. 9 &times; 3 - 4 &divide;&nbsp;2 + 5 = 4<br>LHS <math display=\"inline\"><mo>&#8658;</mo></math> 9 &divide; 3 - 4 &times; 2 + 5 <br><math display=\"inline\"><mo>&#8658;</mo></math> 3 - 8 + 5 = 0 &ne; RHS<br>जैसा कि हम देख सकते हैं, न तो I और न ही II सही होगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the set in which the numbers are related in the same way as are the numbers of&nbsp;the following sets.<br>(<strong>NOTE </strong>: Operations should be performed on the whole numbers, without breaking&nbsp;down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as&nbsp;adding /subtracting /multiplying etc. to 13 can be performed. Breaking down 13 into 1&nbsp;and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(59, 83, 97)<br>(63, 87, 101)</p>",
                    question_hi: "<p>21. उस समुचय का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए&nbsp;समुचयों की संख्याएं संबंधित हैं।<br>(ध्यान दें : संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की&nbsp;जानी चाहिए। उदाहरण के लिए संख्या 13 - संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा&nbsp;करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर&nbsp;गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>(59, 83, 97)<br>(63, 87, 101)</p>",
                    options_en: ["<p>(67, 91, 115)</p>", "<p>(71, 95, 109)</p>", 
                                "<p>(51, 65, 89)</p>", "<p>(48, 82, 96)</p>"],
                    options_hi: ["<p>(67, 91, 115)</p>", "<p>(71, 95, 109)</p>",
                                "<p>(51, 65, 89)</p>", "<p>(48, 82, 96)</p>"],
                    solution_en: "<p>21.(b) <strong>Logic</strong> :- (1st number + 24) = 2nd number , (2nd number + 14) = 3rd number<br>(59, 83, 97) :- (59 + 24) = 83, (83 + 14) = 97<br>(63, 87, 101) :- (63 + 24) = 87 , (87 + 14) = 101<br>Similarly,<br>(71, 95, 109) :- (71 + 24) = 95, (95 + 14) = 109</p>",
                    solution_hi: "<p>21.(b) <strong>तर्क</strong> :- (पहली संख्या + 24) = दूसरी संख्या, (दूसरी संख्या + 14) = तीसरी संख्या<br>(59, 83, 97) :- (59 + 24) = 83, (83 + 14) = 97<br>(63, 87, 101) :- (63 + 24) = 87 , (87 + 14) = 101<br>इसी प्रकार,<br>(71, 95, 109) :- (71 + 24) = 95, (95 + 14) = 109</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different.<br>(Note : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster)<br>1) FGNOP<br>2) IJQRS<br>3) OPWXY<br>4) RSXYZ</p>",
                    question_hi: "<p>22. चार अक्षर-समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक जैसे हैं और एक भिन्न है। दिए गए विकल्पों में उस भिन्न अक्षर-समूह को चुनिए।<br>(नोट : विषम का चयन व्यंजनों/स्वरों की संख्या या अक्षर समूह में उनकी स्थिति पर आधारित नहीं है)<br>1) FGNOP<br>2) IJQRS<br>3) OPWXY<br>4) RSXYZ</p>",
                    options_en: ["<p>RSXYZ</p>", "<p>IJQRS</p>", 
                                "<p>OPWXY</p>", "<p>FGNOP</p>"],
                    options_hi: ["<p>RSXYZ</p>", "<p>IJQRS</p>",
                                "<p>OPWXY</p>", "<p>FGNOP</p>"],
                    solution_en: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306860.png\" alt=\"rId41\" height=\"50\">,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306979.png\" alt=\"rId42\" height=\"50\">,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793307175.png\" alt=\"rId43\" height=\"50\"><br>But,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793307422.png\" alt=\"rId44\" height=\"50\"></p>",
                    solution_hi: "<p>22.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306860.png\" alt=\"rId41\" height=\"50\">,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793306979.png\" alt=\"rId42\" height=\"50\">,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793307175.png\" alt=\"rId43\" height=\"50\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793307422.png\" alt=\"rId44\" height=\"50\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the correct combination of letters that when sequentially placed in the blanks of the given series will make the series logically complete. <br>_WSVR_QTPS_RNQM_ LOK_</p>",
                    question_hi: "<p>23. विकल्पों में दिए गए अक्षरों के उस सही संयोजन का चयन कीजिए जिसे दी गई शृंखला के रिक्त स्थानों में क्रमिक रूप से रखने पर शृंखला तार्किक रूप से पूरी हो जाएगी।<br>_WSVR_QTPS_RNQM_ LOK_</p>",
                    options_en: ["<p>XUQPM</p>", "<p>XVQLM</p>", 
                                "<p>TVOLN</p>", "<p>TUOPN</p>"],
                    options_hi: ["<p>XUQPM</p>", "<p>XVQLM</p>",
                                "<p>TVOLN</p>", "<p>TUOPN</p>"],
                    solution_en: "<p>23.(d) <strong>Logic</strong> :- All the letter are decreasing by 2.<br><strong><span style=\"text-decoration: underline;\">T</span></strong>WSV/ R<strong><span style=\"text-decoration: underline;\">U</span></strong>QT / PS<strong><span style=\"text-decoration: underline;\">O</span></strong>R / NQM<strong><span style=\"text-decoration: underline;\">P</span></strong> / LOK<strong><span style=\"text-decoration: underline;\">N</span></strong></p>",
                    solution_hi: "<p>23.(d) <strong>तर्क</strong> :- सभी अक्षर 2 से कम हो रहे हैं।<br><strong><span style=\"text-decoration: underline;\">T</span></strong>WSV/ R<strong><span style=\"text-decoration: underline;\">U</span></strong>QT / PS<strong><span style=\"text-decoration: underline;\">O</span></strong>R / NQM<strong><span style=\"text-decoration: underline;\">P</span></strong> / LOK<strong><span style=\"text-decoration: underline;\">N</span></strong></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "24. Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters/number of consonants/vowels in the word)<br />Boiling: Lukewarm :: Freezing:?",
                    question_hi: "24. उस विकल्प का चयन करें, जो तीसरे शब्द से उसी प्रकार संबंधित है, जिस प्रकार दूसरा शब्द, पहले शब्द से संबंधित है। (शब्दों को अर्थपूर्ण हिंदी / अँग्रेजी शब्दों के रूप में माना जाना चाहिए और शब्द में अक्षरों की संख्या/ व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)<br />उबालना : गुनगुना :: हिमतापी : ?",
                    options_en: [" Gas ", " Cold ", 
                                " Water  ", " Ice"],
                    options_hi: [" गैस ", " ठंडा ",
                                " पानी ", " बर्फ"],
                    solution_en: "24.(b)<br />\"Boiling\" to \"Lukewarm\" is a decrease in heat intensity. Similarly, \"Freezing\" to \"Cold\" is a decrease in cold intensity.",
                    solution_hi: "24.(b)<br />\"उबालना\" से \"गुनगुना\" होना ताप की तीव्रता में कमी है। इसी प्रकार, \"हिमतापी \" से \"ठंडा\" होना ठंडे होने की तीव्रता में कमी है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Read the given statements and conclusions carefully. You have to consider the given statements to be true even if they seem to be at variance from commonly known facts. You have to decide which of the given conclusions logically follow/s from the given statements.<br><strong>Statements:</strong><br>Some pins are clips.<br>Some clips are papers.<br>No paper is a book.<br><strong>Conclusions:</strong><br>(I): No book is a pin.<br>(II): At least some pins are papers.</p>",
                    question_hi: "<p>25. दिए गए कथनों और निष्कर्षों को ध्यानपूर्वक पढ़ें। आपको दिए गए कथनों को सत्य मानना है, भले ही वे सर्वज्ञात तथ्यों से भिन्न प्रतीत होते हों। आपको तय करना है कि दिए गए निष्कर्षों में से कौन सा/से निष्कर्ष दिए गए कथनों का तार्किक रूप से अनुसरण करता करते हैं/ हैं।<br><strong>कथन:</strong> <br>कुछ पिन क्लिप हैं।<br>कुछ क्लिप कागज हैं।<br>कोई कागज किताब नहीं है।<br><strong>निष्कर्ष:</strong> <br>(I) : कोई किताब पिन नहीं है।<br>(II) : कम से कम कुछ पिन कागज हैं।</p>",
                    options_en: ["<p>Neither conclusion (I) nor (II) follows.</p>", "<p>Only conclusion (I) follows.</p>", 
                                "<p>Only conclusion (II) follows.</p>", "<p>Both conclusions (I) and (II) follow.</p>"],
                    options_hi: ["<p>न तो निष्कर्ष (I) और न ही (II) अनुसरण करता है।</p>", "<p>केवल निष्कर्ष (I) अनुसरण करता है।</p>",
                                "<p>केवल निष्कर्ष (II) अनुसरण करता है।</p>", "<p>दोनों निष्कर्ष (I) और (II) अनुसरण करते हैं।</p>"],
                    solution_en: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793307624.png\" alt=\"rId45\" width=\"443\" height=\"93\"><br>Hence, neither conclusion I nor II follows.</p>",
                    solution_hi: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793307805.png\" alt=\"rId46\" height=\"93\"><br>अतः, न तो निष्कर्ष I और न ही II अनुसरण करता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which of the following is the Least visible form of land erosion?</p>",
                    question_hi: "<p>26. निम्न में से कौन-सा भू-अपरदन का सबसे कम दिखाई देने वाला रूप है?</p>",
                    options_en: ["<p>Gully erosion</p>", "<p>Ravine formation</p>", 
                                "<p>Sheet erosion</p>", "<p>Landslides</p>"],
                    options_hi: ["<p>गली अपरदन</p>", "<p>रेविन गठन</p>",
                                "<p>शीट अपरदन</p>", "<p>भूस्खलन</p>"],
                    solution_en: "<p>26.(c) <strong>Sheet erosion</strong> is the uniform removal of soil in thin layers, and it occurs when soil particles are carried evenly over the soil surface by rainwater that does not infiltrate into the ground. <strong>Gully erosion</strong> is the removal of soil along drainage lines by surface water runoff. <strong>Ravine</strong> is formed through the process of erosion, and it starts out as the site of a small stream or river. <strong>Landslides</strong> occur when masses of rock, earth, or debris move down a slope.</p>",
                    solution_hi: "<p>26.(c) <strong>शीट अपरदन</strong> पतली परतों में मिट्टी का एक समान निष्कासन है, और यह तब होता है जब मिट्टी के कणों को समान रूप से वर्षा जल द्वारा मिट्टी की सतह पर ले जाया जाता है जो जमीन के अंदर रिसता नहीं है । <strong>गली अपरदन</strong> सतही जल अपवाह द्वारा जल निकासी लाइनों के साथ मिट्टी को हटाना है। कटाव की प्रक्रिया के माध्यम से खड्ड <strong>(Ravine)</strong> का निर्माण होता है, और यह एक छोटी सी धारा या नदी के स्थल के रूप में शुरू होता है। <strong>भूस्खलन</strong> तब होता है जब चट्टान, पृथ्वी या मलबे का ढेर ढलान से नीचे चला जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "<p>27. Who among the following described the Indian Constitution as &lsquo;quasi federal&rsquo;?</p>",
                    question_hi: "<p>27. निम्नलिखित में से किसने भारतीय संविधान को \'अर्ध संघीय\' बताया?</p>",
                    options_en: ["<p>MV Pylee</p>", "<p>Gunnar Myrdal</p>", 
                                "<p>AV Dicey</p>", "<p>KC Wheare</p>"],
                    options_hi: ["<p>एम.वी. पाइली</p>", "<p>गुन्नार मिर्डल</p>",
                                "<p>ए.वी. डाइसी</p>", "<p>के.सी. व्हीयर</p>"],
                    solution_en: "<p>27.(d) The Indian Constitution has been variously described as &lsquo;federal in form but unitary in spirit&rsquo;, &lsquo;quasi-federal&rsquo; by <strong>K.C. Wheare</strong>, &lsquo;bargaining federalism&rsquo; by Morris Jones, &lsquo;co-operative federalism&rsquo; by Granville Austin, &lsquo;federation with a centralizing tendency&rsquo; by Ivor Jennings, etc.</p>",
                    solution_hi: "<p>27.(d) भारतीय संविधान को <strong>के.सी. व्हीयर</strong> द्वारा \'संघीय रूप में लेकिन भावना में एकात्मक\', \'अर्ध-संघीय\' के रूप में वर्णित किया गया है। मॉरिस जोन्स द्वारा \'सौदेबाजी संघवाद\', ग्रानविले ऑस्टिन द्वारा \'सहकारी संघवाद\', आइवर जेनिंग्स द्वारा \'केंद्रीकरण की प्रवृत्ति वाला संघ\' आदि कहा गया है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. What is the dimension of the Hockey field?</p>",
                    question_hi: "<p>28. हॉकी मैदान का आयाम(dimension) क्या है?</p>",
                    options_en: ["<p>91.4 &times; 55 m</p>", "<p>100 &times; 55 m</p>", 
                                "<p>90 &times; 50 m</p>", "<p>100.3 &times; 50 m</p>"],
                    options_hi: ["<p>91.4 &times; 55 मी</p>", "<p>100 &times; 55 मी</p>",
                                "<p>90 &times; 50 मी</p>", "<p>100.3 &times; 50 मी</p>"],
                    solution_en: "<p>28(a) The dimension of the Hockey field is <strong>91.4 &times; 55 m</strong>. There are 11 players on each side. A regular game consists of three 20-minute periods, with a 15-minute intermission after the first and second periods. <strong>Dhyan Chand</strong> is known as the &ldquo;Hockey Wizard&rdquo;.</p>",
                    solution_hi: "<p>28.(a) हॉकी मैदान का आयाम (dimension ) 91.4 &times; 55 मीटर है। प्रत्येक पक्ष में 11 खिलाड़ी होते हैं। एक नियमित गेम में तीन 20 मिनट की अवधि होती है, जिसमें पहली और दूसरी अवधि के बाद 15 मिनट का मध्यांतर होता है। ध्यानचंद को \"हॉकी के जादूगर\" के नाम से जाना जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. Which type of radiation designates a part of the electromagnetic spectrum with frequencies approximately from 300 GHz to 3 kHz, or equivalent, wavelengths approximately from 1 mm to 100 km?</p>",
                    question_hi: "<p>29. निम्नलिखित में से किस प्रकार का विकिरण लगभग 300 GHz से 3 kHz की आवृति वाले या समतुल्य, लगभग 1 mm से 100 km तक की तरंग दैर्ध्य वाले विद्युत् चुंबकीय स्पेक्ट्रम के एक भाग को निर्दिष्ट करता है?</p>",
                    options_en: ["<p>Microwaves</p>", "<p>Infrared waves</p>", 
                                "<p>Radio waves</p>", "<p>Ultraviolet rays</p>"],
                    options_hi: ["<p>माइक्रोवेव</p>", "<p>अवरक्त तरंगें</p>",
                                "<p>रेडियो तरंगें</p>", "<p>पराबैंगनी किरणें</p>"],
                    solution_en: "<p>29.(c) <strong>Radio waves</strong> have the longest wavelengths in the electromagnetic spectrum. Examples - Cellular Networks, RADAR, Radio Astronomy, Satellite Communication etc. <strong>Microwave</strong> is a form of electromagnetic radiation with wavelengths ranging from about one meter to one millimeter corresponding to frequencies between 300 MHz and 300 GHz respectively. <strong>Ultraviolet</strong> is a form of electromagnetic radiation with wavelengths from 10 nm to 400 nm, shorter than that of visible light, but longer than X-rays. <strong>Infrared waves are</strong> electromagnetic radiation with wavelengths longer than those of visible light.</p>",
                    solution_hi: "<p>29.(c) विद्युत चुम्बकीय स्पेक्ट्रम में रेडियो तरंगों की तरंग दैर्ध्य (wavelengths) सबसे लंबी होती है। उदाहरण - सेलुलर नेटवर्क, RADAR, रेडियो खगोल विज्ञान, उपग्रह संचार आदि। माइक्रोवेव विद्युत चुम्बकीय विकिरण का एक रूप है जिसमें तरंग दैर्ध्य लगभग एक meter से एक millimeter तक होता है जो क्रमशः 300 MHz और 300 GHz के बीच आवृत्तियों के अनुरूप होता है। पराबैंगनी, विद्युत चुम्बकीय विकिरण का एक रूप है जिसमें तरंग दैर्ध्य 10 nm से 400 nm तक होता है, जो दृश्य प्रकाश से छोटा होता है, लेकिन एक्स-रे से लंबा होता है। अवरक्त तरंगें विद्युत चुम्बकीय विकिरण हैं जिनकी तरंग दैर्ध्य, दृश्य प्रकाश की तुलना में अधिक लंबी होती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "<p>30. Who among the following scholars is associated with the Tibet-home theory of the Aryans?</p>",
                    question_hi: "<p>30. निम्नलिखित विद्वानों में से कौन आर्यों के तिब्बत-गृह सिद्धांत से जुड़ा है?</p>",
                    options_en: ["<p>Dayanand Saraswati</p>", "<p>Bal Gangadhar Tilak</p>", 
                                "<p>MacDonell</p>", "<p>Max Muller</p>"],
                    options_hi: ["<p>दयानंद सरस्वती</p>", "<p>बाल गंगाधर तिलक</p>",
                                "<p>मैकडोनेल</p>", "<p>मैक्स मुलर</p>"],
                    solution_en: "<p>30.(a) In his book <strong>Satyarth Prakash, Swami Dayanand Saraswati</strong> propounded that the original home of the Aryans was Tibet. <strong>Bal Gangadhar tilak</strong> argued that the North Pole was the original home of Aryans.<strong>The Aryan theory,</strong> propounded by <strong>Max Mueller </strong>in the 19th century and linguist <strong>William Jones,</strong> states that Aryans were a class of fair-skinned , agrarian noblemen who came from Central Asia to inhabit India after the Indus Valley period</p>",
                    solution_hi: "<p>30.(a) <strong>स्वामी दयानन्द सरस्वती </strong>ने अपनी पुस्तक सत्यार्थ प्रकाश में प्रतिपादित किया कि आर्यों का मूल निवास तिब्बत था। इसके पीछे तर्क यह था कि तिब्बत में अत्यधिक ठंड के कारण सूर्य और अग्नि की पूजा की जाती थी और ऋग्वेद में वर्णित वनस्पति और जीव तिब्बत में पाए जाते थे।<strong>बाल गंगाधर तिलक </strong>ने तर्क दिया कि उत्तरी ध्रुव आर्यों का मूल घर था।19वीं शताब्दी में <strong>मैक्स म्यूएलर और</strong> भाषाविद् <strong>विलियम जोन्स</strong> द्वारा प्रतिपादित आर्य सिद्धांत में कहा गया है कि आर्य गोरी चमड़ी वाले, कृषि प्रधानों का एक वर्ग थे जो सिंधु घाटी काल के बाद भारत में रहने के लिए मध्य एशिया से आए थे।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Match column A with column B.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793307929.png\" alt=\"rId47\" width=\"352\" height=\"120\"></p>",
                    question_hi: "<p>31. स्तंभ A को कॉलम B से सुम्मेलित कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793308041.png\" alt=\"rId48\" width=\"316\" height=\"167\"></p>",
                    options_en: ["<p>i-b, ii-a, iii-d, iv-c</p>", "<p>i-b, ii-a, iii-c, iv-d</p>", 
                                "<p>i-b, ii-c, iii-d, iv-a</p>", "<p>i-a, ii-b, iii-c, iv-d</p>"],
                    options_hi: ["<p>i-b, ii-a, iii-d, iv-c</p>", "<p>i-b, ii-a, iii-c, iv-d</p>",
                                "<p>i-b, ii-c, iii-d, iv-a</p>", "<p>i-a, ii-b, iii-c, iv-d</p>"],
                    solution_en: "<p>31.(c) <strong>Peptidoglycan</strong> (Bacterial cell wall), <strong>Pectin</strong> (cell wall of Fruits), <strong>Chitin</strong> (Insect cell wall), and <strong>Cellulose</strong> (Cell wall of plants).</p>",
                    solution_hi: "<p>31.(c) <strong>पेप्टिडोग्लाइकन</strong> (जीवाणु कोशिका भित्ति), <strong>पेक्टिन</strong> (फलों की कोशिका भित्ति), <strong>काइटिन</strong> (कीट कोशिका भित्ति), और <strong>सेल्यूलोज</strong> (पौधों की कोशिका भित्ति)।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Name the gland that controls the functioning of other endocrine glands.</p>",
                    question_hi: "<p>32. उस ग्रंथि का नाम बताएं जो अन्य अंतःस्रावी ग्रंथियों के कार्य को नियंत्रित करती है ।</p>",
                    options_en: ["<p>Pancreas</p>", "<p>Pituitary gland</p>", 
                                "<p>Pineal gland</p>", "<p>Adrenal gland</p>"],
                    options_hi: ["<p>अग्नाशय</p>", "<p>पीयुष ग्रंथि</p>",
                                "<p>पीनियल ग्रंथि</p>", "<p>एड्रेनल ग्रंथि</p>"],
                    solution_en: "<p>32.(b) <strong>Pituitary</strong> gland controls the functioning of other endocrine glands.It is also known as master gland. <strong>Pancreas</strong> - It is part of the digestive system and produces insulin and other important enzymes and hormones that help break down foods. <strong>Pineal gland</strong> - The pineal gland is a small, pea-shaped gland in the brain. It is used to regulate melatonin hormone which is responsible for regulating sleeping patterns. <strong>Adrenal glands</strong> - The adrenal glands are also known as suprarenal glands.They produce a variety of hormones like adrenaline,cortisol and aldosterone.</p>",
                    solution_hi: "<p>32.(b) <strong>पीयुष ग्रंथि</strong> अन्य अंतःस्रावी ग्रंथियों के कामकाज को नियंत्रित करती है। इसे मास्टर ग्रंथि के रूप में भी जाना जाता है। <strong>अग्न्याशय</strong> - यह पाचन तंत्र का हिस्सा है और इंसुलिन और अन्य महत्वपूर्ण एंजाइम और हार्मोन का उत्पादन करता है जो खाद्य पदार्थों को तोड़ने में मदद करते हैं। <strong>पीनियल ग्रंथि</strong> - पीनियल ग्रंथि मस्तिष्क में एक छोटी, मटर के आकार की ग्रंथि होती है। इसका उपयोग मेलाटोनिन हार्मोन को विनियमित करने के लिए किया जाता है जो नींद के पैटर्न को विनियमित करने के लिए जिम्मेदार होता <strong>है।अधिवृक्क ग्रंथि</strong> - अधिवृक्क ग्रंथियों को अधिवृक्क ग्रंथियों के रूप में भी जाना जाता है। वे एड्रेनालाईन, कोर्टिसोल और एल्डोस्टेरोन जैसे विभिन्न प्रकार के हार्मोन का उत्पादन करते हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Which of the following come under the Quaternary sector?</p>",
                    question_hi: "<p>33. निम्नलिखित में से कौन चतुष्कोणीय क्षेत्र के अंतर्गत नहीं आता है?</p>",
                    options_en: ["<p>IT</p>", "<p>Mining</p>", 
                                "<p>Manufacturing</p>", "<p>Fisheries</p>"],
                    options_hi: ["<p>सूचना प्रौद्योगिक</p>", "<p>खनन</p>",
                                "<p>विनिर्माण</p>", "<p>मत्स्य पालन</p>"],
                    solution_en: "<p>33.(a) The three main sectors of the economy are: <strong>Primary sector&ndash;</strong> Deals with the extraction of raw materials &ndash; mining,fishing, and agriculture. <strong>Secondary/manufacturing sector</strong> &ndash; deals with producing finished goods e.g. making toys, cars, food, and clothes. <strong>3. Service / &lsquo;tertiary&rsquo; sector </strong>&ndash; include both production and exchange.Tertiary sector = White collar jobs. <strong>Quaternary Sector</strong> involve research and development e.g. IT.</p>",
                    solution_hi: "<p>33.(a) <strong>अर्थव्यवस्था के तीन मुख्य क्षेत्र</strong> हैं: <strong>प्राथमिक क्षेत्र</strong> - कच्चे माल के निष्कर्षण से संबंधित - खनन, मछली पकड़ने और कृषि। <strong>द्वितीयक/विनिर्माण क्षेत्र </strong>- तैयार माल के उत्पादन से संबंधित है उदा। खिलौने, कार, भोजन और कपड़े बनाना। 3.<strong> सेवा / \'तृतीयक\' क्षेत्र -</strong> उत्पादन और विनिमय दोनों शामिल हैं। तृतीयक क्षेत्र = सफेदपोश नौकरियां। चौथे क्षेत्र में अनुसंधान और विकास शामिल है उदाहरण सूचना प्रौद्योगिकी (IT) ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Which is the largest continental shelf in the world?</p>",
                    question_hi: "<p>34. विश्व का सबसे बड़ा महाद्वीपीय शेल्फ (continental shelf) कौन सा है?</p>",
                    options_en: ["<p>The shelf of India</p>", "<p>The Indian Ocean shelf</p>", 
                                "<p>The shelf in the Pacific Ocean</p>", "<p>The Siberian shelf in the Arctic Ocean</p>"],
                    options_hi: ["<p>भारत की शेल्फ</p>", "<p>हिंद महासागर शेल्फ</p>",
                                "<p>प्रशांत महासागर में शेल्फ</p>", "<p>आर्कटिक महासागर में साइबेरियाई शेल्फ</p>"],
                    solution_en: "<p>34.(d) <strong>The Siberian shelf </strong>in the Arctic Ocean is the largest continental shelf in the world. A continental shelf is the edge of a continent that lies under the ocean. <strong>Example of a Continental Shelf Landform: </strong>Siberian Shelf, Arctic Ocean.</p>",
                    solution_hi: "<p>34.(d) आर्कटिक महासागर में <strong>साइबेरियन शेल्फ</strong> दुनिया का सबसे बड़ा महाद्वीपीय शेल्फ है। महाद्वीपीय शेल्फ एक महाद्वीप का किनारा है जो समुद्र के नीचे स्थित है। <strong>महाद्वीपीय शेल्फ लैंडफॉर्म का उदाहरण :</strong> साइबेरियाई शेल्फ, आर्कटिक महासागर।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. The National Commission for Scheduled Tribes came into existence as a separate commission in which of the following year?</p>",
                    question_hi: "<p>35. राष्ट्रीय अनुसूचित जनजाति आयोग निम्नलिखित में से किस वर्ष एक अलग आयोग के रूप में अस्तित्व में आया?</p>",
                    options_en: ["<p>2000</p>", "<p>2006</p>", 
                                "<p>2004</p>", "<p>2002</p>"],
                    options_hi: ["<p>2000</p>", "<p>2006</p>",
                                "<p>2004</p>", "<p>2002</p>"],
                    solution_en: "<p>35.(c) <strong>National Commission for Scheduled Tribes </strong>came into existence as a separate commission in <strong>2004. Preceding commission- </strong>National Commission for Scheduled Castes and Scheduled Tribes 1978. <strong>Article 334</strong> provides for the reservation of seats for Scheduled Castes and Scheduled Tribes in the Lok Sabha and the State Vidhan Sabhas.</p>",
                    solution_hi: "<p>35.(c) राष्ट्रीय अनुसूचित जनजाति आयोग <strong>2004</strong> में एक अलग आयोग के रूप में अस्तित्व में आया। <strong>पूर्ववर्ती आयोग</strong>- राष्ट्रीय अनुसूचित जाति एवं अनुसूचित जनजाति आयोग 1978। <strong>अनुच्छेद 334</strong> में लोकसभा और राज्य विधानसभाओं में अनुसूचित जाति और अनुसूचित जनजातियों के लिए सीटों के आरक्षण का प्रावधान है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Which compound has the chemical formula CHCl<sub>3</sub> whose melting point is 209 K?</p>",
                    question_hi: "<p>36. किस यौगिक का रासायनिक सूत्र CHCl<sub>3</sub> है जिसका गलनांक 209 K है?</p>",
                    options_en: ["<p>Carbon tetrachloride</p>", "<p>Chlorophenol</p>", 
                                "<p>Chloroform</p>", "<p>Chloroethanol</p>"],
                    options_hi: ["<p>कार्बन टेट्राक्लोराइड</p>", "<p>क्लोरोफेनोल</p>",
                                "<p>क्लोरोफॉर्म</p>", "<p>क्लोरोइथेनॉल</p>"],
                    solution_en: "<p>36.(c) <strong>Chloroform (CHCl<sub>3</sub>)</strong> is a colorless liquid that quickly evaporates into gas. <strong>Carbon Tetrachloride (CCl<sub>4</sub>)</strong> is a clear, colorless, volatile and very stable chlorinated hydrocarbon. <strong>Chlorophenol (C<sub>6</sub>H<sub>5</sub>ClO)</strong> is a chlorinated aromatic compound and has been extensively used as a fungicide. <strong>Chloroethanol (C<sub>2</sub>H<sub>5</sub>ClO)</strong> is primarily used for synthesis of ethylene oxide and a number of synthetic reactions for preparation of dyes, pharmaceuticals, biocides and plasticizers.</p>",
                    solution_hi: "<p>36.(c) <strong>क्लोरोफॉर्म (CHCl<sub>3</sub>)</strong> एक रंगहीन तरल है जो जल्दी से गैस में वाष्पित हो जाता है। <strong>कार्बन टेट्राक्लोराइड (CCl<sub>4</sub>)</strong> एक साफ, रंगहीन, वाष्पशील और बहुत स्थिर क्लोरीनयुक्त हाइड्रोकार्बन है। <strong>क्लोरोफेनोल (C<sub>6</sub>H<sub>5</sub>ClO)</strong> एक क्लोरीनयुक्त सुगंधित यौगिक है और इसे बड़े पैमाने पर कवकनाशी के रूप में उपयोग किया जाता है। <strong>क्लोरोएथेनॉल (C<sub>2</sub>H<sub>5</sub>ClO)</strong> मुख्य रूप से एथिलीन ऑक्साइड के संश्लेषण और रंजक, फार्मास्यूटिकल्स, बायोसाइड्स और प्लास्टिसाइज़र को बनाने के लिए कई सिंथेटिक प्रतिक्रियाओं के लिए उपयोग किया जाता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Which country won both the men\'s and women\'s titles at the inaugural Kho Kho World Cup 2025 ?</p>",
                    question_hi: "<p>37. किस देश ने 2025 की पहली खो-खो विश्व कप में पुरुषों और महिलाओं दोनों के खिताब जीते ?</p>",
                    options_en: ["<p>Nepal</p>", "<p>Bangladesh</p>", 
                                "<p>India</p>", "<p>Sri Lanka</p>"],
                    options_hi: ["<p>नेपाल</p>", "<p>बांग्लादेश</p>",
                                "<p>भारत</p>", "<p>श्रीलंका</p>"],
                    solution_en: "<p>37.(c) <strong>India</strong> clinched both the men\'s and women\'s titles at the inaugural Kho Kho World Cup 2025, held from January 13 to 19 at the Indira Gandhi Arena in New Delhi. In the men\'s final, India secured a 54-36 victory over Nepal, while the women\'s team defeated Nepal 78-40.</p>",
                    solution_hi: "<p>37.(c) <strong>भारत</strong> ।&nbsp;भारत ने नई दिल्ली के इंदिरा गांधी एरिना में 13 से 19 जनवरी तक आयोजित 2025 की पहली खो-खो विश्व कप में पुरुषों और महिलाओं दोनों के खिताब जीते। पुरुषों के फाइनल में, भारत ने नेपाल को 54-36 से हराया, जबकि महिला टीम ने नेपाल को 78-40 से पराजित किया।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Match the following dance forms with their respective states:-<br>(P) Lava&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(i) Maharashtra<br>(Q) Koli&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (ii) Lakshadweep<br>(R) Cheraw&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iii) Mizoram<br>(S) Terah Taali&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(iv) Rajasthan</p>",
                    question_hi: "<p>38. निम्नलिखित नृत्य रूपों को उनके संबंधित राज्यों के साथ मिलाएं: -<br>(P) लावा&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(i) महाराष्ट्र<br>(Q) कोली&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (ii) लक्षद्वीप<br>(R) चेराव&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iii) मिजोरम<br>(S) तेरह ताली&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iv).राजस्थान</p>",
                    options_en: ["<p>P- (i) , Q- (ii) , R- (iii) , S- (iv)</p>", "<p>P- (ii) , Q- (i) , R- (iii) , S- (iv)</p>", 
                                "<p>P- (ii) , Q- (iv) , R- (i) , S- (iii)</p>", "<p>P- (iv) , Q- (i) , R- (ii) , S- (iii)</p>"],
                    options_hi: ["<p>P- (i) , Q- (ii) , R- (iii) , S- (iv)</p>", "<p>P- (ii) , Q- (i) , R- (iii) , S- (iv)</p>",
                                "<p>P- (ii) , Q- (iv) , R- (i) , S- (iii)</p>", "<p>P- (iv) , Q- (i) , R- (ii) , S- (iii)</p>"],
                    solution_en: "<p>38.(b) <strong>Lava</strong> is a popular folk dance of (Lakshadweep) Minicoy Island. <strong>Koli Dance</strong> dances chairs on heads with lighted diyas are performed by women. It is a popular folk dance of Maharashtra. <strong>Cheraw</strong> Dance is a traditional dance form of Mizoram. The folk dance named <strong>\'Terah Taali\'</strong> is traditionally associated with Rajasthan. This folk dance is performed by the Kamada tribes who are traditional snake charmers.</p>",
                    solution_hi: "<p>38(b) <strong>लावा</strong> मिनीकॉय द्वीप का एक लोकप्रिय लोक नृत्य है। <strong>कोली नृत्य</strong> सिर पर दीयों के साथ कुर्सियों पर नृत्य करता है जो महिलाओं द्वारा किया जाता है। यह महाराष्ट्र का एक लोकप्रिय लोक नृत्य है। <strong>चेराव</strong> नृत्य मिजोरम का पारंपरिक नृत्य है। <strong>\'तेरह ताली\'</strong> नाम का लोक नृत्य पारंपरिक रूप से <strong>राजस्थान</strong> से जुड़ा हुआ है। यह लोक नृत्य कामदा जनजाति द्वारा किया जाता है जो पारंपरिक सपेरे हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. In which year was the Battle of Saragarhi fought?</p>",
                    question_hi: "<p>39. सारागढ़ी की लड़ाई किस वर्ष लड़ी गयी थी?</p>",
                    options_en: ["<p>1867</p>", "<p>1897</p>", 
                                "<p>1854</p>", "<p>1878</p>"],
                    options_hi: ["<p>1867</p>", "<p>1897</p>",
                                "<p>1854</p>", "<p>1878</p>"],
                    solution_en: "<p>39.(b) The Battle of Saragarhi was fought on 12 September 1897 between the British Indian Empire and the Afghan tribesmen. It occurred in the North-West Frontier Province. During the battle of Saragarhi,The British post was defended by 21 Sikh soldiers of 36th Sikhs regiment of British Indian Army against soldiers of Afghan tribes,who were more than 8 to 10 thousand in numbers.</p>",
                    solution_hi: "<p>39.(b) सारागढ़ी की लड़ाई 12 सितंबर 1897 ई को ब्रिटिश भारतीय साम्राज्य और अफगान आदिवासियों के बीच लड़ी गई थी। यह उत्तर-पश्चिम सीमांत प्रांत में हुआ।सारागढ़ी की लड़ाई के दौरान, ब्रिटिश भारतीय सेना की 36वीं सिख रेजिमेंट के 21 सिख सैनिकों ने अफगान जनजातियों के सैनिकों के खिलाफ ब्रिटिश चौकी का बचाव किया, जिनकी संख्या 8 से 10 हजार से अधिक थी।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Which state has inked a pact with WWF India to develop the State Biodiversity Strategy and Action Plan?</p>",
                    question_hi: "<p>40. किस राज्य ने राज्य जैव विविधता रणनीति और कार्य योजना विकसित करने के लिए WWF इंडिया के साथ समझौता किया है?</p>",
                    options_en: ["<p>Nagaland</p>", "<p>Manipur</p>", 
                                "<p>Assam</p>", "<p>Arunachal Pradesh</p>"],
                    options_hi: ["<p>नागालैंड</p>", "<p>मणिपुर</p>",
                                "<p>असम</p>", "<p>अरुणाचल प्रदेश</p>"],
                    solution_en: "<p>40.(d) <strong>Arunachal Pradesh.</strong>&nbsp; Arunachal Pradesh inked a pact with WWF India to develop the State Biodiversity Strategy and Action Plan (SBSAP). The SBSAP will align with the post-2020 Global Biodiversity Framework and Pakke Declaration. Arunachal Pradesh Chief minister&rarr; Pema Khandu, Governor&rarr;BD Mishra.</p>",
                    solution_hi: "<p>40.(d) <strong>अरुणाचल प्रदेश ।&nbsp; </strong>अरुणाचल प्रदेश ने राज्य जैव विविधता रणनीति और कार्य योजना (SBSAP) विकसित करने के लिए WWF इंडिया के साथ एक समझौता किया।यह योजना 2020 के बाद के वैश्विक जैव विविधता फ्रेमवर्क और पक्के घोषणा (Pakke Declaration) के साथ संरेखित होगा। अरुणाचल प्रदेश के मुख्यमंत्री- पेमा खांडू, राज्यपाल- बीडी मिश्रा ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. In which period was the legendary Victoria Terminus Station (currently Chhtarpati Shivaji Maharaj Terminus), Mumbai built?</p>",
                    question_hi: "<p>41. प्रसिद्ध विक्टोरिया टर्मिनस स्टेशन ( वर्तमान में छत्रपति शिवाजी महाराज टर्मिनस ), मुंबई का निर्माण किस अवधि में किया गया था ?</p>",
                    options_en: ["<p>1878 to 1888</p>", "<p>1843 to 1853</p>", 
                                "<p>1933 to 1943</p>", "<p>1911 to 1921</p>"],
                    options_hi: ["<p>1878 से 1888</p>", "<p>1843 से 1853</p>",
                                "<p>1933 से 1943</p>", "<p>1911 से 1921</p>"],
                    solution_en: "<p>41.(a) The Chhatrapati Shivaji station, formerly known as Victoria Terminus, began to be built in 1878 and completed in 1888. It is designed by the British architect <strong>F.W. Stevens.</strong></p>",
                    solution_hi: "<p>41.(a) छत्रपति शिवाजी स्टेशन, जिसे पहले विक्टोरिया टर्मिनस के नाम से जाना जाता था, का निर्माण 1878 में शुरू हुआ और 1888 में पूरा हुआ। इसे ब्रिटिश वास्तुकार <strong>एफ.डब्ल्यू. स्टीवंस</strong> द्वारा डिजाइन किया गया है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Who among the following was the founder of the Indian Association for the Cultivation of Science?</p>",
                    question_hi: "<p>42. निम्नलिखित में से कौन इंडियन एसोसिएशन फॉर द कल्टीवेशन ऑफ साइंस के संस्थापक थे?</p>",
                    options_en: ["<p>Ashutosh Mukhopadhyay</p>", "<p>Mahendra Lal Sircar</p>", 
                                "<p>Prafulla Chandra Roy</p>", "<p>Jagadish Chandra Bose</p>"],
                    options_hi: ["<p>आशुतोष मुखोपाध्याय</p>", "<p>महेंद्र लाल सरकार</p>",
                                "<p>प्रफुल्ल चंद्र रॉय</p>", "<p>जगदीश चंद्र बोस</p>"],
                    solution_en: "<p>42.(b) <strong>Mahendra Lal Sircar</strong> was the founder of the Indian Association for the Cultivation of Science. <strong>Ashutosh Mukhopadhyay</strong> founded the Calcutta Mathematical Society in 1908. <strong>Prafulla Chandra Roy</strong> Is known as the father of Indian Chemistry. A <strong>crescograph</strong> is a device for measuring the growth in plants was discovered by <strong>Jagadish Chandra Bose.</strong></p>",
                    solution_hi: "<p>42.(b) <strong>महेंद्र लाल सरकार</strong> इंडियन एसोसिएशन फॉर द कल्टीवेशन ऑफ साइंस के संस्थापक थे। <strong>आशुतोष मुखोपाध्याय</strong> ने 1908 में कलकत्ता मैथमैटिकल सोसाइटी की स्थापना की। <strong>प्रफुल्ल चंद्र रॉय</strong> भारतीय रसायन विज्ञान के पिता के रूप में जाना जाता है। <strong>क्रेस्कोग्राफ</strong> एक उपकरण है जो पौधों में वृद्धि को मापने के लिए <strong>जगदीश चंद्र बोस</strong> द्वारा खोजा गया था।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. Select the correctly matched pair of names of the company and its founder.</p>",
                    question_hi: "<p>43. कंपनी के नाम तथा इसके संस्थापक के सुमेलित युग्म का चयन कीजिए।</p>",
                    options_en: ["<p>ITC &ndash; GD Birla</p>", "<p>Wipro &ndash; Mohammed Hashim Premji</p>", 
                                "<p>Infosys &ndash; Azim Premji</p>", "<p>Reliance &ndash; Mukesh Ambani</p>"],
                    options_hi: ["<p>आईटीसी - जीडी बिड़ला</p>", "<p>विप्रो - मोहम्मद हाशिम प्रेमजी</p>",
                                "<p>इनफ़ोसिस - अजीम प्रेमजी</p>", "<p>रिलायंस - मुकेश अम्बानी</p>"],
                    solution_en: "<p>43.(b) ITC was initially a British owned company registered in Calcutta. <strong>Mohammed Hashim Premji</strong> is the founder of Wipro. Infosys was founded by <strong>N.R Narayana Murthyy,</strong> Nandan Nilekani, S.D. Shibulal, Kris Gopalakrishnan, Ashok Arora, N.S. Raghavan and K.Dinesh. <strong>Dhirubhai Ambani</strong> was the founder of <strong>Reliance.</strong></p>",
                    solution_hi: "<p>43.(b) <strong>आईटीसी</strong> शुरू में कलकत्ता में पंजीकृत एक ब्रिटिश स्वामित्व वाली कंपनी थी। <strong>मोहम्मद हाशिम प्रेमजी विप्रो</strong> के संस्थापक हैं। <strong>इंफोसिस</strong> की स्थापना <strong>एन.आर. नारायण मूर्ति,</strong> नंदन नीलेकणी, एस.डी. शिबूलाल, क्रिस गोपालकृष्णन, अशोक अरोड़ा, एन.एस. राघवन और के. दिनेश। <strong>धीरूभाई अंबानी</strong> रिलायंस के संस्थापक थे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. ______ is one of the languages in which the denomination is printed on the reverse of a contemporary Indian currency note.</p>",
                    question_hi: "<p>44. ______ उन भाषाओं में से एक है जिसमें एक समकालीन भारतीय मुद्रा नोट के पीछे मूल्यवर्ग मुद्रित होता है।</p>",
                    options_en: ["<p>Burmese</p>", "<p>Sinhala</p>", 
                                "<p>Nepali</p>", "<p>Dzongkha</p>"],
                    options_hi: ["<p>बर्मीज़</p>", "<p>सिंहला</p>",
                                "<p>नेपाली</p>", "<p>ज़ोंगखा</p>"],
                    solution_en: "<p>44.(c) <strong>Nepali</strong> is one of the languages in which the denomination is printed on the reverse of a contemporary Indian currency note. <strong>Sinhala,</strong> is an Indo-Aryan language primarily spoken by the Sinhalese people of Sri Lanka. <strong>Dzongkha</strong> is a Sino-Tibetan language spoken in Bhutan. <strong>Burmese</strong> is a Sino-Tibetan language spoken in Myanmar.</p>",
                    solution_hi: "<p>44.(c) <strong>नेपाली</strong> उन भाषाओं में से एक है जो भारतीय मुद्रा नोट पर मुद्रित की जाती है। <strong>सिंहली,</strong> एक इंडो-आर्यन भाषा है जो मुख्य रूप से श्रीलंका के सिंहली लोगों द्वारा बोली जाती है। <strong>ज़ोंगखा</strong> भूटान में बोली जाने वाली एक चीन-तिब्बती भाषा है। <strong>बर्मी</strong> म्यांमार में बोली जाने वाली एक चीनी-तिब्बती भाषा है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. The term &lsquo;Cherry Picking&rsquo; is used in which sport?</p>",
                    question_hi: "<p>45. \'चेरी पिकिंग&rsquo; शब्द किस खेल में प्रयोग किया जाता है?</p>",
                    options_en: ["<p>Table Tennis</p>", "<p>Swimming</p>", 
                                "<p>Basketball</p>", "<p>Cricket</p>"],
                    options_hi: ["<p>टेबल टेनिस</p>", "<p>तैराकी</p>",
                                "<p>बास्केटबॉल</p>", "<p>क्रिकेट</p>"],
                    solution_en: "<p>45.(c) The term Cherry picking is used in basketball. Other terms used in Basketball: Slam dunk, Full-court press, Alley-oop, Jump shot, Playmaker, Point guard, Hoopster, Sixth man, etc.</p>",
                    solution_hi: "<p>45.(c) बास्केटबॉल में चेरी पिकिंग शब्द का प्रयोग किया जाता है। बास्केटबॉल में इस्तेमाल होने वाले अन्य शब्द: स्लैम डंक, फुल-कोर्ट प्रेस, एले-ऊप, जंप शॉट, प्लेमेकर, पॉइंट गार्ड, हूपस्टर, सिक्स्थ मैन आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. Who was honored with the Sri Chandrasekarendra Saraswathi National Eminence Award for Public Leadership in December 2024 ?</p>",
                    question_hi: "<p>46. दिसंबर 2024 में, किसे सार्वजनिक नेतृत्व के लिए श्री चंद्रशेखरेन्द्र सरस्वती राष्ट्रीय सम्मान से सम्मानित किया गया?</p>",
                    options_en: ["<p>Narendra Modi</p>", "<p>S. Jaishankar</p>", 
                                "<p>Venkaiah Naidu</p>", "<p>Amit Shah</p>"],
                    options_hi: ["<p>नरेंद्र मोदी</p>", "<p>एस. जयशंकर</p>",
                                "<p>वेंकैया नायडू</p>", "<p>अमित शाह</p>"],
                    solution_en: "<p>46.(b) <strong>S. Jaishankar.</strong> Subrahmanyam Jaishankar is currently serving as the External Affairs Minister of India, a position he has held since May 30, 2019. He is also a Member of Parliament (Rajya Sabha), representing the state of Gujarat.</p>",
                    solution_hi: "<p>46.(b) <strong>एस. जयशंकर </strong>। सुब्रह्मण्यम जयशंकर वर्तमान में भारत के विदेश मंत्री के रूप&nbsp;में सेवा दे रहे हैं, यह पद उन्होंने 30 मई 2019 से संभाला है। वे गुजरात राज्य से राज्यसभा के सदस्य भी हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Match the following Books with their authors:-<br>(P) Lajja -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (i) Bill Gates<br>(Q) The Road Ahead&rsquo;-&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(ii) Taslima Nasrin<br>(R) Set theBoy Free -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iii) Johnny Marr<br>(S) In The -Afternoon of Time&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iv) Harivansh Rai Bachchan</p>",
                    question_hi: "<p>47. निम्नलिखित पुस्तकों को उनके लेखकों के साथ सुमेलित करें:-<br>(P) लज्जा&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(i) बिल गेट्स<br>(Q)\'द रोड अहेड\'&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (ii) तसलीमा नसरीन<br>(R) सेट द बॉय फ्री&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; (iii) जॉनी मार्<br>(S) \'समय की दोपहर में&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;(iv) हरिवंश राय बच्चन\'</p>",
                    options_en: ["<p>P- (i) , Q- (ii) , R- (iii) , S- (iv)</p>", "<p>P- (ii) , Q- (i) , R- (iii) , S- (iv)</p>", 
                                "<p>P- (ii) , Q- (iv) , R- (i) , S- (iii)</p>", "<p>P- (iv) , Q- (i) , R- (ii) , S- (iii)</p>"],
                    options_hi: ["<p>P- (i) , Q- (ii) , R- (iii) , S- (iv)</p>", "<p>P- (ii) , Q- (i) , R- (iii) , S- (iv)</p>",
                                "<p>P- (ii) , Q- (iv) , R- (i) , S- (iii)</p>", "<p>P- (iv) , Q- (i) , R- (ii) , S- (iii)</p>"],
                    solution_en: "<p>47.(b) <strong>&ldquo;Lajja&rdquo; book is authored by Taslima Nasrin.</strong> Some of her famous books are - &lsquo;My Girlhood&rsquo;, &lsquo;French Lover&rsquo;, &lsquo;Revenge&rsquo;, &lsquo;Selected Columns&rsquo; etc.&lsquo;<strong>The Road Ahead&rsquo; </strong>is autobiography of Bill Gates.<strong>Johnny Marr</strong> is the author of &lsquo;Set the Boy Free&rsquo;&lsquo;<strong>In The Afternoon of Time: An Autobiography&rsquo; written by Harivansh Rai Bachchan</strong>. Poems by Harivansh Rai Bachhan- &lsquo;Tera Haar&rsquo;, &lsquo;Madhushala&rsquo;, &lsquo;Madhubala&rsquo;, &lsquo;Madhukalash&rsquo;, etc.</p>",
                    solution_hi: "<p>47.(b) विवादास्पद उपन्यास \'लज्जा (शर्म)\' की लेखिका <strong>तसलीमा नसरीन</strong> हैं। उनकी कुछ प्रसिद्ध पुस्तकें हैं - \'माई गर्लहुड\', \'फ्रेंच लवर\', \'रिवेंज\',\'सेलेक्टेड कॉलम्स\' आदि।\'<strong>द रोड अहेड\'</strong> बिल गेट्स की आत्मकथा है। <strong>जॉनी मार</strong> \'सेट द बॉय फ्री\' के लेखक हैं।&nbsp;\'इन द आफ्टरनून ऑफ टाइम: एन ऑटोबायोग्राफी\' <strong>हरिवंश राय बच्चन</strong> द्वारा लिखी गई थी। हरिवंश राय बच्चन की कविताएँ- \'तेरा हार\', \'मधुशाला\', \'मधुबाला\', \'मधुकलश\', आदि।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Which Indian city won the Global Award for Sustainable Development in Cities in 2024 ?</p>",
                    question_hi: "<p>48. 2024 में कौन सा भारतीय शहर ग्लोबल अवार्ड फॉर सस्टेनेबल डेवलपमेंट इन सिटीज़ जीता?</p>",
                    options_en: ["<p>Bengaluru</p>", "<p>Mumbai</p>", 
                                "<p>Thiruvananthapuram</p>", "<p>Delhi</p>"],
                    options_hi: ["<p>बेंगलुरु</p>", "<p>मुंबई</p>",
                                "<p>तिरुवनंतपुरम</p>", "<p>दिल्ली</p>"],
                    solution_en: "<p>48.(c) <strong>Thiruvananthapuram.</strong> The Thiruvananthapuram Municipal Corporation has been honored with the Global Award for Sustainable Development in Cities, an initiative by UN Habitat and the Shanghai Municipality. This award recognizes cities and municipalities worldwide for their outstanding achievements in implementing the New Urban Agenda for sustainable urban development.</p>",
                    solution_hi: "<p>48.(c) <strong>तिरुवनंतपुरम।</strong> तिरुवनंतपुरम नगर निगम को ग्लोबल अवार्ड फॉर सस्टेनेबल डेवलपमेंट इन सिटीज़ से सम्मानित किया गया, जो कि यूएन हैबिटैट और शंघाई नगरपालिका द्वारा एक पहल है। यह पुरस्कार विश्व भर के शहरों और नगरपालिकाओं को उनके नव शहरी एजेंडा को लागू करने में उत्कृष्ट उपलब्धियों के लिए मान्यता प्रदान करता है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which Schedule of the Indian Constitution divides the power between the Centre and the state in terms of the union, state and concurrent list?</p>",
                    question_hi: "<p>49. भारतीय संविधान की कौन सी अनुसूची संघ, राज्य और समवर्ती सूची के संदर्भ में केंद्र और राज्य के बीच शक्तियों को विभाजित करती है?</p>",
                    options_en: ["<p>Fifth Schedule</p>", "<p>Ninth Schedule</p>", 
                                "<p>Third Schedule</p>", "<p>Seventh Schedule</p>"],
                    options_hi: ["<p>पांचवीं अनुसूची</p>", "<p>नौवीं अनुसूची</p>",
                                "<p>तीसरी अनुसूची</p>", "<p>सातवीं अनुसूची</p>"],
                    solution_en: "<p>49.(d) <strong>The seventh Schedule</strong> deals with the three legislative lists: Union, State, and Concurrent. <strong>Article 246:</strong> Subject-matter of laws made by Parliament and by the Legislatures of States, deals with this schedule. The <strong>fifth Schedule</strong> contains provisions as to the Administration and Control of Scheduled Areas and Scheduled Tribes.The <strong>Ninth Schedule</strong> contains provisions as to validation of certain Acts and Regulations.The <strong>third Schedule</strong> contains the Forms of Oaths or Affirmations.</p>",
                    solution_hi: "<p>49.(d) <strong>सातवीं अनुसूची</strong> तीन विधायी सूचियों&nbsp; से संबंधित है: संघ, राज्य और समवर्ती। <strong>अनुच्छेद 246:</strong> संसद और राज्यों के विधान मंडलों द्वारा बनाए गए कानूनों की विषय-वस्तु इस अनुसूची से संबंधित है। <strong>पांचवीं अनुसूची</strong> में अनुसूचित क्षेत्रों और अनुसूचित जनजातियों के प्रशासन और नियंत्रण के प्रावधान हैं। <strong>नौवीं अनुसूची</strong> में कुछ अधिनियमों और विनियमों के सत्यापन के प्रावधान हैं। <strong>तीसरी अनुसूची</strong> में शपथ या प्रतिज्ञान के रूप हैं।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. The ancient city of Champa is believed to be the capital of ______ Mahajanapada.</p>",
                    question_hi: "<p>50. चंपा का प्राचीन शहर ______ महाजनपद की राजधानी माना जाता है।</p>",
                    options_en: ["<p>Anga</p>", "<p>Kashi</p>", 
                                "<p>Matsya</p>", "<p>Vajji</p>"],
                    options_hi: ["<p>अंग</p>", "<p>काशी</p>",
                                "<p>मत्स्य:</p>", "<p>वज्जि</p>"],
                    solution_en: "<p>50.(a) <strong>16 Mahajanpadas are</strong> Anga (Champa), Magadha (Girivraja/ Rajagriha), Kashi(Kasi), Vatsa (Kausambi), Kosala {Shravasti (northern), Kushavati (southern)}, Shurasena (Mathura), Panchala (Ahichchatra and Kampilya), Kuru (Indraprastha), Matsya (Viratanagar), Chedi (Sotavati), Avanti (Ujjain or Mahishmati), Gandhara (Taxila), Kamboja (Pooch), Asmaka (Potali), Vajji (Vaishali), Malla( kusinara).</p>",
                    solution_hi: "<p>50.(a) <strong>16 महाजनपद हैं </strong>- अंग (चंपा) मगध (गिरिव्रज / राजगृह), काशी (कासी ),वत्स (कौशाम्बी), कोसल {श्रावस्ती (उत्तरी), कुशावती (दक्षिणी)}, शूरसेन (मथुरा), पांचाल (अहिच्छत्र और काम्पिल्य), कुरु (इंद्रप्रस्थ), मत्स्य (विराटनगर), छेदी (सोतावती), अवंती (उज्जैन या महिष्मती), गांधार (तक्षशिला) ),कम्बोज (पूच), अस्माका (पोटाली), वज्जी (वैशाली),मल्ल (कुशीनारा)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. If 3c2933k is divisible by both 5 and 11, where c and k are single digit natural numbers,&nbsp;then c + k = ______.</p>",
                    question_hi: "<p>51. यदि 3c2933k, 5 और 11 दोनों से विभाज्य है जहां c और k एक अंक वाली प्राकृतिक संख्याएं हैं, तो c + k =_________ है।</p>",
                    options_en: ["<p>6</p>", "<p>8</p>", 
                                "<p>5</p>", "<p>7</p>"],
                    options_hi: ["<p>6</p>", "<p>8</p>",
                                "<p>5</p>", "<p>7</p>"],
                    solution_en: "<p>51.(a)&nbsp;Divisibility of 11: The difference of the sum of digits at odd position and sum of digits at even position in a number is 0 or divisible by 11.<br>Now, (3 + 2 + 3 + k) - (c + 9 + 3) = 0<br>&rArr; (8 + k) = (c + 12) &hellip;&hellip;(i)<br>Divisibility of 5: If the last digit of the number should be 0 or 5 then that number will be divisible by 5.<br>Value of k = 5 (given we take only natural no.)<br>Put value of k in equation (i), we get<br>&rArr; (8 + 5) = (c + 12) <br>&rArr; c = 13 - 12 = 1<br>So,<br>c + k = 1 + 5 = 6</p>",
                    solution_hi: "<p>51.(a)&nbsp;11 की विभाज्यता: किसी संख्या में विषम स्थान पर अंकों के योग और सम स्थान पर अंकों के योग का अंतर 0 हो या 11 से विभाज्य होना चाहिए <br>अब, (3 + 2 + 3 + k) - (c + 9 + 3) = 0<br>&rArr; (8 + k) = (c + 12) &hellip;&hellip;(i)<br>5 से विभाज्यता: यदि किसी संख्या का अंतिम अंक 0 या 5 हो तो वह संख्या 5 से विभाज्य होगी।<br>K का मान = 5 (दिया है, हम केवल प्राकृतिक संख्या लेंगे)<br>समीकरण (i) में k का मान रखने पर, हमें मिलता है<br>&rArr; (8 + 5) = (c + 12) <br>&rArr; c = 13 - 12 = 1<br>इसलिए,<br>c + k = 1 + 5 = 6</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. In an election between two candidates A and B, A got 33% of total votes casted and&nbsp;still lost by 55,522 votes. Find the total number of votes got by B.</p>",
                    question_hi: "<p>52. दो उम्मीदवारों A और B के बीच हुए एक चुनाव में A को डाले गए कुल मतों के 33% मत मिले और फिर भी वह 55,522 मतों से चुनाव हार गया। B को कुल कितने मत मिले?</p>",
                    options_en: ["<p>1,09,411</p>", "<p>1,04,911</p>", 
                                "<p>1,09,141</p>", "<p>1,09,114</p>"],
                    options_hi: ["<p>1,09,411</p>", "<p>1,04,911</p>",
                                "<p>1,09,141</p>", "<p>1,09,114</p>"],
                    solution_en: "<p>52.(a)&nbsp;Votes got by B = (100 - 33)% = 67%<br>Difference = (67 - 33)% = 34%<br>34% = 55522<br>(Vote got by B) 67% = <math display=\"inline\"><mfrac><mrow><mn>55522</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> &times; 67 = 109411</p>",
                    solution_hi: "<p>52.(a)&nbsp;B को मिले वोट = (100 - 33)% = 67%<br>अंतर = (67 - 33)% = 34%<br>34% = 55522<br>(B को मिले वोट) 67% = <math display=\"inline\"><mfrac><mrow><mn>55522</mn></mrow><mrow><mn>34</mn></mrow></mfrac></math> &times; 67 = 109411</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. Simplify the following expression. <br>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>of 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &divide;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math> + (2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> &divide; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> of 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math> - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>)</p>",
                    question_hi: "<p>53. 3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>of 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &divide;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math> + (2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> &divide; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> of 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math> - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>", 
                                "<p>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>", "<p>1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>"],
                    options_hi: ["<p>4<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>", "<p>2<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>",
                                "<p>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>", "<p>1<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>"],
                    solution_en: "<p>53.(d)<br>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>of 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &divide;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math> + (2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> &divide; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> of 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math> - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>)</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math> of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>7</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math>)</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math>)</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math> = 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>",
                    solution_hi: "<p>53.(d)<br>3<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math>of 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>4</mn></mfrac></math> &divide;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math> + (2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>5</mn></mfrac></math> &divide; 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>5</mn></mfrac></math> of 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>7</mn></mfrac></math> - 2<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>3</mn></mfrac></math>)</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>4</mn></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>3</mn></mfrac></math> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>5</mn></mfrac></math> &divide; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>5</mn></mfrac></math> of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>10</mn><mn>7</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math>)</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> + (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>11</mn><mn>10</mn></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>3</mn></mfrac></math>)</p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>5</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>37</mn><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>35</mn><mn>30</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>7</mn><mn>6</mn></mfrac></math> = 1<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>6</mn></mfrac></math></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. The distance between the centres of the two circles, with radii 3 cm and 2 cm, respectively, is 13 cm. The length (in cm) of a transverse common tangent is:</p>",
                    question_hi: "<p>54. क्रमशः 3 cm और 2 cm त्रिज्याओं वाले दो वृत्तों के केंद्रों के बीच की दूरी 13 cm है। एक अनुप्रस्थ उभयनिष्ठ स्पर्शरखा की लंबाई (cm में) क्या होगी ?</p>",
                    options_en: ["<p>18 cm</p>", "<p>12 cm</p>", 
                                "<p>9 cm</p>", "<p>16 cm</p>"],
                    options_hi: ["<p>18 cm</p>", "<p>12 cm</p>",
                                "<p>9 cm</p>", "<p>16 cm</p>"],
                    solution_en: "<p>54.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793308227.png\" alt=\"rId49\" width=\"279\" height=\"151\"><br>Length of transverse tangent (l) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>13</mn><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>169</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>25</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math> = 12 cm</p>",
                    solution_hi: "<p>54.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793308227.png\" alt=\"rId49\" width=\"279\" height=\"151\"><br>अनुप्रस्थ स्पर्शरेखा की लंबाई (l) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mi mathvariant=\"normal\">d</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><msub><mi mathvariant=\"normal\">r</mi><mn>1</mn></msub><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msub><mi mathvariant=\"normal\">r</mi><mn>2</mn></msub><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>13</mn><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mrow><mo>(</mo><mn>3</mn><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>)</mo></mrow><mn>2</mn></msup></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>169</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>25</mn></msqrt></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>144</mn></msqrt></math> = 12 cm</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. The Least Common Multiple of two prime numbers x and y, (x &lt; y) is 141. Then what is the value of (y - 5x)?</p>",
                    question_hi: "<p>55. दो अभाज्य संख्याओं &times; और y, (x &lt; y) का लघुत्तम समापवर्त्य 141 है। तो (y - 5x) का मान कितना है?</p>",
                    options_en: ["<p>32</p>", "<p>30</p>", 
                                "<p>38</p>", "<p>40</p>"],
                    options_hi: ["<p>32</p>", "<p>30</p>",
                                "<p>38</p>", "<p>40</p>"],
                    solution_en: "<p>55.(a)&nbsp;LCM of (x, y) = 47 &times; 3<br>y = 47 and x&nbsp;= 3<br>y - 5x&nbsp;= 47 - 5 &times; 3 = 32</p>",
                    solution_hi: "<p>55.(a)&nbsp;(x, y) का लघुत्तम समापवर्त्य = 47 &times; 3<br>y = 47 और x&nbsp;= 3<br>y - 5x&nbsp;= 47 - 5 &times; 3 = 32</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. (cot &theta; + tan &theta;)(cosec &theta; &minus; sin &theta;)(cos &theta; &minus; sec &theta;) = ________.</p>",
                    question_hi: "<p>56. (cot &theta; + tan &theta;)(cosec &theta; &minus; sin &theta;)(cos &theta; &minus; sec &theta;) = ________.</p>",
                    options_en: ["<p>&minus;1</p>", "<p>2</p>", 
                                "<p>0</p>", "<p>1</p>"],
                    options_hi: ["<p>&minus;1</p>", "<p>2</p>",
                                "<p>0</p>", "<p>1</p>"],
                    solution_en: "<p>56.(a)&nbsp;On putting &theta; = 45&deg; then we get,<br>= (cot &theta; + tan &theta;)(cosec &theta; &minus; sin &theta;)(cos &theta; &minus; sec &theta;)<br>= (1 + 1)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> &minus; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math> &minus; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>)<br>= (2)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math>)(-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math>)&nbsp;= -1</p>",
                    solution_hi: "<p>56.(a)&nbsp;&theta; = 45&deg; रखने पर हमें प्राप्त होता है,<br>= (cot &theta; + tan &theta;)(cosec &theta; &minus; sin &theta;)(cos &theta; &minus; sec &theta;)<br>= (1 + 1)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math> &minus; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math>)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math> &minus; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>2</mn></msqrt></math>)<br>= (2)(<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math>)(-<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><msqrt><mn>2</mn></msqrt></mfrac></math>) = -1</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. A shopkeeper claims that he sells rock salt at ₹23 per kg, which costs him ₹25 per kg. But he gives 800 g instead of 1,000 g. What is his percentage profit or loss?</p>",
                    question_hi: "<p>57. एक दुकानदार दावा करता है कि वह सेंधा नमक ₹23 प्रति kg की दर से बेचता है, जिसकी कीमत उसे ₹25 प्रति kg पड़ती है। लेकिन वह 1,000 g की जगह 800 g ही देता है। उसका लाभ या हानि प्रतिशत क्या है?</p>",
                    options_en: ["<p>Profit of 8%</p>", "<p>Loss of 3%</p>", 
                                "<p>Loss of 5%</p>", "<p>Profit of 15%</p>"],
                    options_hi: ["<p>8% का लाभ</p>", "<p>3% की हानि</p>",
                                "<p>5% की हानि</p>", "<p>15% का लाभ</p>"],
                    solution_en: "<p>57.(d)<br>Ratio - &nbsp; &nbsp; &nbsp; CP &nbsp; : &nbsp; SP<br>Cost&nbsp; -&nbsp; &nbsp; &nbsp; &nbsp; 25 &nbsp; :&nbsp; &nbsp; 23<br>Weight - &nbsp; 800g&nbsp; :&nbsp; 1000g<br>&mdash;----------------------------------<br>Final -&nbsp; &nbsp; &nbsp; &nbsp; 200&nbsp; :&nbsp; 230<br><strong id=\"docs-internal-guid-d09ddcb8-7fff-f390-39a1-cc5c8037d6aa\"></strong>So,<br>Required profit% =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>230</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>200</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math> &times; 100 = 15 %</p>",
                    solution_hi: "<p>57.(d)<br>अनुपात -&nbsp; क्रय मूल्य&nbsp; : &nbsp; विक्रय मूल्य<br>लागत &nbsp; -&nbsp; &nbsp; &nbsp; 25 &nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; 23<br>भार &nbsp; &nbsp; - &nbsp; &nbsp; 800g &nbsp; &nbsp; : &nbsp; 1000g<br>&mdash;----------------------------------<br>अंतिम&nbsp; -&nbsp; &nbsp; &nbsp; &nbsp; 200&nbsp; &nbsp; : &nbsp; &nbsp; 230<br><strong id=\"docs-internal-guid-d0ee369c-7fff-b5f1-2060-d7271a20b0f8\"></strong>इसलिए,<br>अभीष्ट लाभ% =&nbsp;<math display=\"inline\"><mfrac><mrow><mn>230</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>200</mn></mrow><mrow><mn>200</mn></mrow></mfrac></math> &times; 100 = 15 %</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. A borrows ₹89,500 for 3 years at 7% p.a. simple interest. B borrows the same amount at simple interest of 9% for the first year, 8% for the second year, and 7% for the third year. What is the total simple interest (in ₹) paid by A and B?</p>",
                    question_hi: "<p>58. A ने 3 वर्षों के लिए 7% वार्षिक साधारण ब्याज पर ₹89,500 की धनराशि उधार ली। B ने साधारण ब्याज पर समान धनराशि पहले वर्ष के लिए 9%, दूसरे वर्ष के लिए 8% और तीसरे वर्ष के लिए 7% की दर पर उधार ली। A और B द्वारा भुगतान किए गए कुल साधारण ब्याज (₹ में) की गणना कीजिए।</p>",
                    options_en: ["<p>45,027</p>", "<p>47,025</p>", 
                                "<p>42,075</p>", "<p>40,275</p>"],
                    options_hi: ["<p>45,027</p>", "<p>47,025</p>",
                                "<p>42,075</p>", "<p>40,275</p>"],
                    solution_en: "<p>58.(d)&nbsp;S.I. for A = 89500 &times; 21% = ₹18,795<br>S.I. for B = 89500 &times; (9 + 8 + 7)% = ₹21,480<br>Hence, total S.I. paid by A and B = 18,795 + 21,480 = ₹40,275</p>",
                    solution_hi: "<p>58.(d)&nbsp;A के लिए साधारण ब्याज = 89500 &times; 21% = ₹18,795<br>B के लिए साधारण ब्याज = 89500 &times; (9 + 8 + 7)% = ₹21,480<br>इसलिए, A और B द्वारा भुगतान किया गया कुल साधारण ब्याज = 18,795 + 21,480 = ₹40,275</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. The batting average of a cricket player for 40 innings is 60 runs. His highest score in an innings exceeds his lowest score by 110 runs. If these two innings are excluded, the average score of the remaining 38 innings is 58 runs. Find his highest score.</p>",
                    question_hi: "<p>59. एक क्रिकेट खिलाड़ी का 40 पारियों में बल्लेबाजी का औसत 60 रन है। एक पारी में उसका उच्चतम स्कोर उसके न्यूनतम स्कोर से 110 रन अधिक है। यदि इन दोनों पारियों को हटा दिया जाए तो शेष 38 पारियों का औसत स्कोर 58 है। उसका उच्चतम स्कोर ज्ञात कीजिए।</p>",
                    options_en: ["<p>290</p>", "<p>153</p>", 
                                "<p>220</p>", "<p>310</p>"],
                    options_hi: ["<p>290</p>", "<p>153</p>",
                                "<p>220</p>", "<p>310</p>"],
                    solution_en: "<p>59.(b)&nbsp;Let the highest score be x<br>&rArr; His lowest score = x - 110<br>According to question,<br>Total runs scored by player = 40 &times; 60 = 2400<br>Total runs scored in the remaining 38 matches = 38 &times; 58 = 2204<br>Now,<br>Highest score + lowest score = 2400 - 2204<br>&rArr; x + x - 110 = 196<br>&rArr; 2x = 306<br>&rArr; x = 153<br>Hence, the highest score will be 153</p>",
                    solution_hi: "<p>59.(b) माना , उच्चतम स्कोर = x<br>&rArr; उसका न्यूनतम स्कोर = x - 110<br>प्रश्न के अनुसार,<br>खिलाड़ी द्वारा बनाए गए कुल रन = 40 &times; 60 = 2400<br>शेष 38 मैचों में बनाए गए कुल रन = 38 &times; 58 = 2204<br>उच्चतम स्कोर + न्यूनतम स्कोर = 2400 - 2204<br>&rArr; x + x - 110 = 196<br>&rArr; 2x = 306<br>&rArr; x = 153<br>अतः, उच्चतम स्कोर = 153</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Study the given bar-graph and answer the question that follows.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793308338.png\" alt=\"rId50\" width=\"294\" height=\"217\"> <br>How many students obtained more than the average marks of the class?</p>",
                    question_hi: "<p>60. दिए गए दंड आलेख का अध्ययन कीजिए और नीचे दिए गए प्रश्न का उत्तर दीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793308498.png\" alt=\"rId51\" width=\"323\" height=\"231\"><br>कितने छात्रों ने कक्षा के औसत अंक से अधिक अंक प्राप्त किए?</p>",
                    options_en: ["<p>8</p>", "<p>6</p>", 
                                "<p>10</p>", "<p>11</p>"],
                    options_hi: ["<p>8</p>", "<p>6</p>",
                                "<p>10</p>", "<p>11</p>"],
                    solution_en: "<p>60.(c) Average marks of the class<br>=&nbsp;<math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>125</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>130</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>135</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>140</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>8</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>145</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>150</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>160</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>3390</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 141.25 <br>Number of students whose marks is more than average = 5 + 4 + 1 = 10</p>",
                    solution_hi: "<p>60.(c) कक्षा के औसत अंक<br>=&nbsp;<math display=\"inline\"><mfrac><mrow><mo>(</mo><mn>125</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>130</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>135</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>2</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>140</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>8</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>145</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>5</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>150</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>4</mn><mo>)</mo><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mo>(</mo><mn>160</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>1</mn><mo>)</mo></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>5</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>4</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>1</mn></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><mn>3390</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 141.25 <br>उन विद्यार्थियों की संख्या जिनके अंक औसत अंक से अधिक हैं = 5 + 4 + 1 = 10</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. The marked price of a crockery was Rs 15400/- and was offering a discount of 12%. Harsh bargained with the shopkeeper to get a successive discount and brought the crockery for Rs. 10164/- What was the second discount offered by the shopkeeper?</p>",
                    question_hi: "<p>61. एक क्रॉकरी का अंकित मूल्य 15400/- रुपए था और उस पर 12% की छूट दी जा रही थी। हर्ष ने दूसरी छूट पाने के लिए दुकानदार से मोलभाव किया और उसने क्रॉकरी को 10164/- रुपए में खरीदा, दुकानदार द्वारा दी गई दूसरी छूट कितनी थी ?</p>",
                    options_en: ["<p>30%</p>", "<p>25%</p>", 
                                "<p>15%</p>", "<p>10%</p>"],
                    options_hi: ["<p>30%</p>", "<p>25%</p>",
                                "<p>15%</p>", "<p>10%</p>"],
                    solution_en: "<p>61.(b)&nbsp;Let the another discount be x%.<br>According to question,<br>&rArr;<strong id=\"docs-internal-guid-fec5fcac-7fff-369c-5b6d-a98c0423975a\"> </strong>10164 = 15400 &times; <math display=\"inline\"><mfrac><mrow><mn>88</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math><br>&rArr; <strong id=\"docs-internal-guid-fec5fcac-7fff-369c-5b6d-a98c0423975a\"></strong>(100 - x) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10164</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>100</mn></mrow><mrow><mn>154</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>88</mn></mrow></mfrac></math><br>&rArr; x&nbsp;= 100 - 75 = 25%</p>",
                    solution_hi: "<p>61.(b)&nbsp;माना कि दूसरी छूट x% है।<br>प्रश्न के अनुसार,<br>&rArr;<strong id=\"docs-internal-guid-fec5fcac-7fff-369c-5b6d-a98c0423975a\"> </strong>10164 = 15400 &times; <math display=\"inline\"><mfrac><mrow><mn>88</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>(</mo><mn>100</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi mathvariant=\"normal\">x</mi><mo>)</mo></mrow><mn>100</mn></mfrac></math><br>&rArr; <strong id=\"docs-internal-guid-fec5fcac-7fff-369c-5b6d-a98c0423975a\"></strong>(100 - x) = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>10164</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>100</mn></mrow><mrow><mn>154</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>88</mn></mrow></mfrac></math><br>&rArr; x = 100 - 75 = 25%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A thief is noticed by a policeman from a distance of 500 m. The thief starts running and the policeman chases him. The thief and the policeman run at the speed of 12 km/h and 13 km/h, respectively. What is the distance between them after 12 minutes?</p>",
                    question_hi: "<p>62. एक पुलिसमैन ने एक चोर को 500 m की दूरी से देखा। चोर भागने लगता है और पुलिसमैन उसका पीछा करता है। चोर और पुलिसमैन क्रमशः 12 km/h और 13 km/h की चाल से दौड़ते हैं। 12 मिनट के बाद दोनों के बीच की दूरी कितनी होगी?</p>",
                    options_en: ["<p>150 m</p>", "<p>100 m</p>", 
                                "<p>300 m</p>", "<p>200 m</p>"],
                    options_hi: ["<p>150 m</p>", "<p>100 m</p>",
                                "<p>300 m</p>", "<p>200 m</p>"],
                    solution_en: "<p>62.(c)&nbsp;Distance = 500m<br>Speed of thief = 12 km/hr<br>Speed of policeman = 13 km/hr<br>Relative speed = (13 - 12) km/hr = 1 km/hr =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>18</mn></mfrac></math> m /sec.<br>distance between them after 12 minutes = 500 - <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> &times; 12 &times; 60<br>= 500 - 200 = 300 m.</p>",
                    solution_hi: "<p>62.(c)&nbsp;दूरी = 500m<br>चोर की गति = 12 km/hr<br>पुलिसमैन की गति = 13 km/hr<br>सापेक्ष गति = (13 - 12) km/hr = 1 km/hr = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> m /sec.<br>12 मिनट बाद उनके बीच की दूरी = 500 - <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>18</mn></mrow></mfrac></math> &times; 12 &times; 60<br>= 500 - 200 = 300 m.</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. If cosec x = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math>, then the value of <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#160;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>27</mn><mo>&#160;</mo><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>125</mn><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> is:</p>",
                    question_hi: "<p>63. यदि cosec x = <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>5</mn><mo>&#160;</mo><msup><mi>sin</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>8</mn><mo>&#160;</mo><msup><mi>cot</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow><mrow><mn>27</mn><mo>&#160;</mo><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>125</mn><mo>&#160;</mo><msup><mi>cos</mi><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math> का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>91</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>", 
                                "<p><math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>87</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>99</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math></p>"],
                    options_hi: ["<p><math display=\"inline\"><mfrac><mrow><mn>33</mn></mrow><mrow><mn>91</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                                "<p><math display=\"inline\"><mfrac><mrow><mn>55</mn></mrow><mrow><mn>87</mn></mrow></mfrac></math></p>", "<p><math display=\"inline\"><mfrac><mrow><mn>99</mn></mrow><mrow><mn>47</mn></mrow></mfrac></math></p>"],
                    solution_en: "<p>63.(b) Given, cosec x = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>hypotenuse</mi><mi>perpendicular</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>By using pythagorean triplet (4, 3, 5) , base = 3<br>Now,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>&#160;&#160;</mo><mo>+</mo><mo>&#160;&#160;</mo><msup><mrow><mn>8</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cot</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow><mrow><msup><mrow><mn>27</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mn>125</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>27</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi>&#160;</mi><mn>125</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></mrow><mrow><mn>27</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>16</mn></mrow><mrow><mn>9</mn></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>125</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>9</mn></mrow><mrow><mn>25</mn></mrow></mfrac></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>16</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow><mrow><mn>48</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>45</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>32</mn><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>45</mn></mrow><mrow><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                    solution_hi: "<p>63.(b) दिया गया है, cosec x = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2352;&#2381;&#2339;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow><mrow><mi>&#2354;&#2306;&#2348;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>5</mn><mn>4</mn></mfrac></math><br>पायथागॉरियन त्रिक (4, 3, 5) का उपयोग करके , आधार = 3<br>अब,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mrow><mn>5</mn><mi mathvariant=\"normal\">&#160;</mi><mi>sin</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mo>&#160;&#160;</mo><mo>+</mo><mo>&#160;&#160;</mo><msup><mrow><mn>8</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cot</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow><mrow><msup><mrow><mn>27</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mrow><mn>125</mn><mi mathvariant=\"normal\">&#160;</mi><mi>cos</mi></mrow><mn>2</mn></msup><mi mathvariant=\"normal\">x</mi></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>4</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mn>27</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>4</mn></mrow><mrow><mn>3</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup><mo>-</mo><mi>&#160;</mi><mn>125</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mo>(</mo><mfrac><mrow><mn>3</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mn>5</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>16</mn></mrow><mrow><mn>25</mn></mrow></mfrac><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>&#160;</mi><mn>8</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>9</mn></mrow><mrow><mn>16</mn></mrow></mfrac></mrow><mrow><mn>27</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>16</mn></mrow><mrow><mn>9</mn></mrow></mfrac><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>125</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>9</mn></mrow><mrow><mn>25</mn></mrow></mfrac></mrow></mfrac></math></p>\n<p>=&nbsp;<math display=\"inline\"><mfrac><mrow><mfrac><mrow><mn>16</mn></mrow><mrow><mn>5</mn></mrow></mfrac><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mfrac><mrow><mn>9</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow><mrow><mn>48</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>45</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>32</mn><mi>&#160;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>45</mn></mrow><mrow><mn>3</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow></mfrac></math> = <math display=\"inline\"><mfrac><mrow><mn>77</mn></mrow><mrow><mn>30</mn></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. The area of a square is 16x<sup>2</sup>&nbsp;+ 40x + 25 square units. Find the perimeter of the square.</p>",
                    question_hi: "<p>64. किसी वर्ग का क्षेत्रफल 16x<sup>2</sup>&nbsp;+ 40x + 25 वर्ग इकाई है। उस वर्ग का परिमाप ज्ञात कीजिए।</p>",
                    options_en: ["<p>3(10x&nbsp;+ 8)</p>", "<p>3(8x&nbsp;+ 10)</p>", 
                                "<p>2(10x&nbsp;+ 8)</p>", "<p>2(8x&nbsp;+ 10)</p>"],
                    options_hi: ["<p>3(10x&nbsp;+ 8)</p>", "<p>3(8x&nbsp;+ 10)</p>",
                                "<p>2(10x&nbsp;+ 8)</p>", "<p>2(8x&nbsp;+ 10)</p>"],
                    solution_en: "<p>64.(d)&nbsp;Area of square = (side)<sup>2</sup><br>= 16x<sup>2</sup>&nbsp;+ 40x + 25<br>= (4x + 5)<sup>2</sup><br>&rArr; Side of square = (4x + 5)<br>&rArr; Perimeter of square = 4 &times; (4x + 5)<br>= 2 (8x&nbsp;+ 10)</p>",
                    solution_hi: "<p>64.(d)&nbsp;वर्ग का क्षेत्रफल = (भुजा)<sup>2</sup><br>= 16x<sup>2</sup>&nbsp;+ 40x + 25<br>= (4x + 5)<sup>2</sup><br>&rArr; वर्ग की भुजा = (4x + 5)<br>&rArr; वर्ग का परिमाप = 4 &times; (4x + 5)<br>= 2 (8x&nbsp;+ 10)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. b is the sum of two quantities, one of which varies inversely as the square of a<sup>2</sup>, and the other of which varies directly as a. Identify the relationship between a and b if b = 49 when a = 3 or 5.</p>",
                    question_hi: "<p>65. b दो राशियों का योग है, जिनमें से एक a<sup>2</sup> के वर्ग के व्युत्क्रमानुपाती है और दूसरी a के अनुक्रमानुपाती है। यदि a = 3 या 5 होने पर b = 49 हो, तो a और b के बीच संबंध की पहचान कीजिए ।</p>",
                    options_en: ["<p>a = 3b</p>", "<p>a = 3b<sup>2</sup> &minus; 7/a</p>", 
                                "<p>b = 8a + 225/a<sup>2</sup></p>", "<p>b = 2a</p>"],
                    options_hi: ["<p>a = 3b</p>", "<p>a = 3b<sup>2</sup> &minus; 7/a</p>",
                                "<p>b = 8a + 225/a<sup>2</sup></p>", "<p>b = 2a</p>"],
                    solution_en: "<p>65.(c) In this type question solving by the help of options,<br>On checking all options one by one, only option (c) satisfies. <br>&rArr; b = 8a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>225</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac></math> <br>LHS = b = 49<br>RHS = 8a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>225</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac></math>&nbsp;= 8 &times; 5 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>225</mn><mn>25</mn></mfrac></math> = 40 + 9 = 49<br>Hence, LSH = RHS</p>",
                    solution_hi: "<p>65.(c) इस प्रकार के प्रश्नों को विकल्पों की सहायता से हल करते है।<br>सभी विकल्पों की एक-एक करके जांच करने पर केवल विकल्प (c) ही संतुष्ट करता है।<br>&rArr; b = 8a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>225</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac></math> <br>LHS = b = 49<br>RHS = 8a + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>225</mn><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup></mfrac></math>&nbsp;= 8 &times; 5 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>225</mn><mn>25</mn></mfrac></math> = 40 + 9 = 49<br>अतः , LSH = RHS</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If (a + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math> then what is the value of (a<sup>6</sup> + a<sup>-6</sup>)?</p>",
                    question_hi: "<p>66. यदि (a + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>&nbsp;है, तो (a<sup>6</sup> + a<sup>-6</sup>) का मान ज्ञात कीजिए।</p>",
                    options_en: ["<p>3048190</p>", "<p>3048542</p>", 
                                "<p>3048132</p>", "<p>3048625</p>"],
                    options_hi: ["<p>3048190</p>", "<p>3048542</p>",
                                "<p>3048132</p>", "<p>3048625</p>"],
                    solution_en: "<p>66.(a) (a + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>a<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = (7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>)<sup>2</sup> - 2 = 49 &times; 3 - 2 =145<br>(a<sup>6</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math>) = 145<sup>3</sup> - 3 &times; 145 = 3048625 - 435 = 3048190</p>",
                    solution_hi: "<p>66.(a) (a + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mi mathvariant=\"normal\">a</mi></mfrac></math>) = 7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math><br>a<sup>2</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>2</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math> = (7<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>3</mn></msqrt></math>)<sup>2</sup> - 2 = 49 &times; 3 - 2 =145<br>(a<sup>6</sup> + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><msup><mi mathvariant=\"normal\">a</mi><mn>6</mn></msup><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac></math>) = 145<sup>3</sup> - 3 &times; 145 = 3048625 - 435 = 3048190</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The radius and height of a cylinder are in the ratio 6 : 7 and its volume is 792 cm<sup>3</sup>. Calculate its curved surface area in cm<sup>2</sup></p>",
                    question_hi: "<p>67. एक बेलन की त्रिज्या और ऊंचाई का अनुपात 6 : 7 है और इसका आयतन 792 cm<sup>3</sup> है। इसके वक्र पृष्ठीय क्षेत्रफल की गणना cm<sup>2</sup> में कीजिए।</p>",
                    options_en: ["<p>262</p>", "<p>490</p>", 
                                "<p>264</p>", "<p>226</p>"],
                    options_hi: ["<p>262</p>", "<p>490</p>",
                                "<p>264</p>", "<p>226</p>"],
                    solution_en: "<p>67.(c)&nbsp;Volume of the cylinder = &pi;r<sup>2</sup>h<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; (6x)<sup>2</sup>(7x) = 792<br>x<sup>3</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>792</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>7</mn></mrow><mrow><mn>22</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>36</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>7</mn></mrow></mfrac></math> = 1 so x = 1<br>So, the radius and height of the cylinder will be 6 and 7<br>Now, according to the question,<br>CSA of the cylinder = 2&pi;rh<br>= 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 6 &times; 7&nbsp;= 264 cm<sup>2</sup></p>",
                    solution_hi: "<p>67.(c)&nbsp;बेलन का आयतन = &pi;r<sup>2</sup>h<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; (6x)<sup>2</sup>(7x) = 792<br>x<sup>3</sup> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>792</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>7</mn></mrow><mrow><mn>22</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>36</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>7</mn></mrow></mfrac></math> = 1 इसलिए x = 1<br>तो, बेलन की त्रिज्या और ऊंचाई 6 और 7 होगी<br>अब, प्रश्न के अनुसार,<br>बेलन का वक्र पृष्ठीय क्षेत्रफल = 2&pi;rh = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; 6 &times; 7&nbsp;= 264 cm<sup>2</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. R can finish a work in 6 days if he works 10 hours a day. S can finish it in 5 days if he works 8 hours a day. If both start a work and work daily for 6 hours, the work will be finished in _____ days.</p>",
                    question_hi: "<p>68. यदि R प्रतिदिन 10 घंटे काम करता है तो वह एक काम 6 दिनों में पूरा कर सकता है। यदि S प्रतिदिन 8 घंटे काम करता है तो वह इसे 5 दिनों में पूरा कर सकता है। यदि दोनों एक साथ मिलकर काम शुरू करते हैं और प्रतिदिन 6 घंटे काम करते हैं, तो काम कितने दिनों में पूरा हो जाएगा?</p>",
                    options_en: ["<p>5</p>", "<p>4</p>", 
                                "<p>3</p>", "<p>6</p>"],
                    options_hi: ["<p>5</p>", "<p>4</p>",
                                "<p>3</p>", "<p>6</p>"],
                    solution_en: "<p>68.(b)&nbsp;According to the question,<br>R &times; 10 &times; 6 = S &times; 8 &times; 5<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">R</mi><mi mathvariant=\"normal\">S</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>60</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>Total work = 2 &times; 10 &times; 6 = 120 units<br>Required days = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mo>(</mo><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> = 4 days</p>",
                    solution_hi: "<p>68.(b)&nbsp;प्रश्न के अनुसार,<br>R &times; 10 &times; 6 = S &times; 8 &times; 5<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">R</mi><mi mathvariant=\"normal\">S</mi></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>60</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mn>3</mn></mfrac></math><br>कुल कार्य = 2 &times; 10 &times; 6 = 120 इकाई<br>आवश्यक दिन = <math display=\"inline\"><mfrac><mrow><mn>120</mn></mrow><mrow><mo>(</mo><mn>2</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>3</mn><mo>)</mo><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow></mfrac></math> = 4 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. A person walks along a rectangular field, whose dimensions are 6 km long and 3 km wide. For the first 1 hour, he walks at a speed of 2 km/hr. After 1 hour, he increases his speed to 4 km/hr. Next 1 hour, his speed is decreased to 2 km/hr. For the next 1 hour, he increases his speed to 4 km/hr. Find his average speed (in km/hr).</p>",
                    question_hi: "<p>69. एक व्यक्ति एक आयताकार मैदान के अनुदिश चलता है, जिसकी लंबाई 6 km और चौड़ाई 3 km है। पहले 1 घंटे के लिए, वह 2 km/hr की चाल से चलता है। 1 घंटे के बाद, वह अपनी चाल बढ़ाकर 4 km/hr कर देता है। अगले 1 घंटे में उसकी चाल घटकर 2 km/hr हो जाती है। अगले 1 घंटे के लिए, वह अपनी चाल बढ़ाकर 4 km/hr कर देता है। उसकी औसत चाल (km/hr में) ज्ञात कीजिए।</p>",
                    options_en: ["<p>5</p>", "<p>4</p>", 
                                "<p>3</p>", "<p>2</p>"],
                    options_hi: ["<p>5</p>", "<p>4</p>",
                                "<p>3</p>", "<p>2</p>"],
                    solution_en: "<p>69.(c) In 1st hour, distance covered by person at the speed 2km/h = 2 km<br>In 2nd hour , distance covered by person at the speed of 4 km/h = 4 km<br>In 3rd hour ,distance covered by person at the speed of 2km/h = 2 km<br>In 4th hour , distance covered by person at the speed of 4km/h = 4 km<br>Total distance covered = 2 + 4 + 2 + 4 = 12 km<br>Total time taken = 1 + 1 + 1 + 1 = 4 hour <br>Average speed = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>distance</mi></mrow><mrow><mi>total</mi><mi mathvariant=\"normal\">&#160;</mi><mi>time</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>4</mn></mfrac></math> = 3 km/h</p>",
                    solution_hi: "<p>69.(c) पहले घंटे में, व्यक्ति द्वारा 2 किमी/घंटा की गति से तय की गई दूरी = 2 km<br>दूसरे घंटे में व्यक्ति द्वारा 4 किमी/घंटा की गति से तय की गई दूरी = 4 km<br>तीसरे घंटे में व्यक्ति द्वारा 2 किमी/घंटा की गति से तय की गई दूरी = 2 km<br>चौथे घंटे में व्यक्ति द्वारा 4 किमी/घंटा की गति से तय की गई दूरी = 4 km<br>तय की गई कुल दूरी = 2 + 4 + 2 + 4 = 12 km<br>कुल लिया गया समय = 1 + 1 + 1 + 1 = 4 घंटा<br>औसत गति = <math style=\"font-family:Verdana\" display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2342;&#2370;&#2352;&#2368;</mi></mrow><mrow><mi>&#2325;&#2369;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2360;&#2350;&#2351;</mi></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>12</mn><mn>4</mn></mfrac></math> = 3 km/h</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Find the area (in cm<sup>2</sup>) of the sector of a circle of radius 21 cm with a central angle of 60&deg;. [Use &pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>]</p>",
                    question_hi: "<p>70. 60&deg; केंद्रीय कोण वाले 21 cm त्रिज्या के एक वृत्त के त्रिज्यखंड का क्षेत्रफल (cm<sup>2</sup> में) ज्ञात कीजिए। [&pi; = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math>&nbsp;लीजिए।]</p>",
                    options_en: ["<p>231</p>", "<p>289</p>", 
                                "<p>302</p>", "<p>245</p>"],
                    options_hi: ["<p>231</p>", "<p>289</p>",
                                "<p>302</p>", "<p>245</p>"],
                    solution_en: "<p>70.(a)&nbsp;Area of sector = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mn>360</mn></mfrac></math> &times; &pi;r<sup>2</sup><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>360</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 21 &times; 21 = 231 cm<sup>2</sup></p>",
                    solution_hi: "<p>70.(a)&nbsp;त्रिज्यखंड का क्षेत्रफल = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">&#952;</mi><mn>360</mn></mfrac></math> &times; &pi;r<sup>2</sup><br>=&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>360</mn></mfrac></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>22</mn><mn>7</mn></mfrac></math> &times; 21 &times; 21 = 231 cm<sup>2</sup></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. Study the given bar-graph and answer the question that follows.<br>The bar-graph shows the production (in lakh) of kitchen appliances manufactured by three companies A, B, and C over a period of six years from 2012 to 2017.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793308722.png\" alt=\"rId52\" height=\"250\"> <br>80% of the combined average production of companies B and C is what percentage (to the nearest integer) more/less than 90% of the average production of company A. for the given period of 2012 to 2017?</p>",
                    question_hi: "<p>71. दिए गए बार ग्राफ का अध्ययन करें और नीचे दिए गए प्रश्न का उत्तर दें।<br>बार ग्राफ में 2012 से 2017 तक छः वर्षों की अवधि में तीन कंपनियों A, B और C द्वारा निर्मित रसोई उपकरणों के उत्पादन (लाख में) को दर्शाया गया है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793308866.png\" alt=\"rId53\" height=\"250\"> <br>वर्ष 2012 से 2017 की दी गई अवधि में कंपनी B और C के संयुक्त औसत उत्पादन का 80%, कंपनी A के औसत उत्पादन के 90% से कितना प्रतिशत (निकटतम पूर्णांक तक) अधिक / कम है?</p>",
                    options_en: ["<p>21% more</p>", "<p>19% more</p>", 
                                "<p>19% less</p>", "<p>21% less</p>"],
                    options_hi: ["<p>21% अधिक</p>", "<p>19% अधिक</p>",
                                "<p>19% कम</p>", "<p>21% कम</p>"],
                    solution_en: "<p>71.(c)&nbsp;Production of company B = 130 + 80 + 70 + 90 + 180 + 80 = 630<br>Production of company C = 70 + 90 + 120 + 80 + 110 + 140 = 610<br>Average production of company B and C = <math display=\"inline\"><mfrac><mrow><mn>630</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>610</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 103.33<br>80% of average production of company B and C = 103.33 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 82.66<br>Average production of company A = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>90</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>160</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>70</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>110</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 113.33<br>90% of average production of company A = 113.33 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 102<br>required% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>102</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>82</mn><mo>.</mo><mn>66</mn></mrow><mn>102</mn></mfrac></math> &times; 100 = 18.95 (approx 19% less)</p>",
                    solution_hi: "<p>71.(c)&nbsp;कंपनी B का उत्पादन = 130 + 80 + 70 + 90 + 180 + 80 = 630<br>कंपनी C का उत्पादन = 70 + 90 + 120 + 80 + 110 + 140 = 610<br>कंपनी B और C का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>630</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>610</mn></mrow><mrow><mn>12</mn></mrow></mfrac></math> = 103.33<br>कंपनी B और C के औसत उत्पादन का 80% = 103.33 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 82.66<br>कंपनी A का औसत उत्पादन = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>90</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>160</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>70</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>110</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math> = 113.33<br>कंपनी A के औसत उत्पादन का 90% = 113.33 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 102<br>आवश्यक% = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>102</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>82</mn><mo>.</mo><mn>66</mn></mrow><mn>102</mn></mfrac></math> &times; 100 = 18.95 (लगभग 19% कम)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If interest is being compound annually, then what sum will amount to Rs. 9464 in 2 years at the rate of 30 percent per annum at compound interest?</p>",
                    question_hi: "<p>72. यदि ब्याज वार्षिक रूप से संयोजित हो रहा है, तो 30 प्रतिशत वार्षिक चक्रवृद्धि ब्याज की दर से कितनी धनराशि 2 वर्ष में 9464 रुपए हो जाएगी?</p>",
                    options_en: ["<p>Rs. 5600</p>", "<p>Rs. 5400</p>", 
                                "<p>Rs. 6800</p>", "<p>Rs. 5750</p>"],
                    options_hi: ["<p>5600 रुपए</p>", "<p>5400 रुपए</p>",
                                "<p>6800 रुपए</p>", "<p>5750 रुपए</p>"],
                    solution_en: "<p>72.(a)&nbsp;Let sum be 100%<br>Effective CI for 2 yrs at 30% rate = (30 + 30 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>30</mn></mrow><mn>100</mn></mfrac></math>)% = 69%<br>169% ---------------- ₹9464<br>100% ---------------- <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>9464</mn><mn>169</mn></mfrac></math> &times; 100 = ₹5600</p>",
                    solution_hi: "<p>72.(a)&nbsp;माना धनराशि 100% है<br>30% दर पर 2 वर्षों के लिए प्रभावी चक्रवृद्धि ब्याज = (30 + 30 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>30</mn><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mn>30</mn></mrow><mn>100</mn></mfrac></math>)% = 69%<br>169% ---------------- ₹9464<br>100% ---------------- <math display=\"inline\"><mfrac><mrow><mn>9464</mn></mrow><mrow><mn>169</mn></mrow></mfrac></math> &times; 100 = ₹5600</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. The salary of an employee is reduced by 80% and then the reduced salary is increased by 80%. The percentage loss in the salary of the employee is:</p>",
                    question_hi: "<p>73. एक कर्मचारी के वेतन में 80% की कमी की जाती है और फिर घटाए गए वेतन में 80% की वृद्धि की जाती है। कर्मचारी के वेतन में प्रतिशत हानि (कमी) की गणना करें।</p>",
                    options_en: ["<p>54%</p>", "<p>58%</p>", 
                                "<p>68%</p>", "<p>64%</p>"],
                    options_hi: ["<p>54%</p>", "<p>58%</p>",
                                "<p>68%</p>", "<p>64%</p>"],
                    solution_en: "<p>73.(d) Let starting salary = ₹100<br>When reduced by 80% , salary = ₹ 20<br>Again reduced salary is increased by 80% , Salary = 20 &times; <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 36<br>Percentage loss = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>36</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 64%</p>",
                    solution_hi: "<p>73.(d) माना प्रारंभिक वेतन = ₹100<br>80% कम होने पर , वेतन = ₹ 20<br>पुनः कम वेतन में 80% की वृद्धि हुई, वेतन = 20 &times; <math display=\"inline\"><mfrac><mrow><mn>180</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = ₹ 36<br>प्रतिशत हानि = <math display=\"inline\"><mfrac><mrow><mn>100</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>36</mn><mi>&#160;</mi></mrow><mrow><mn>100</mn></mrow></mfrac></math> &times; 100 = 64%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. The ratio of the speeds of a boat while going upstream and going downstream is 2 : 3&nbsp;and the sum of these two speeds is 15 km/h. What is the speed of the stream?</p>",
                    question_hi: "<p>74. एक नाव की धारा के प्रतिकूल और धारा के अनुकूल जाने की चाल का अनुपात 2 : 3 है और इन दोनों चालों का योग 15 km/h है। धारा की चाल क्या है?</p>",
                    options_en: ["<p>3.5 km/h</p>", "<p>1.5 km/h</p>", 
                                "<p>3 km/h</p>", "<p>2.5 km/h</p>"],
                    options_hi: ["<p>3.5 km/h</p>", "<p>1.5 km/h</p>",
                                "<p>3 km/h</p>", "<p>2.5 km/h</p>"],
                    solution_en: "<p>74.(b)&nbsp;Let Speed of upstream and downstream be 2x&nbsp;and 3x respectively,<br>According to the question,<br>(2x&nbsp;+ 3x) = 15<br>5x&nbsp;= 15 &rArr; x = 3 km/h<br>Then, speed of upstream and downstream be 6km/h and 9 km/h respectively,<br>Now, speed of current = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> = 1.5 km/h</p>",
                    solution_hi: "<p>74.(b)&nbsp;माना धारा के प्रतिकूल और धारा के अनुकूल गति क्रमशः 2x&nbsp;और 3x है,<br>प्रश्न के अनुसार,<br>(2x&nbsp;+ 3x) = 15<br>5x&nbsp;= 15 &rArr; x = 3 km/h<br>तो, धारा के प्रतिकूल और धारा के अनुकूल गति क्रमशः 6 किमी/घंटा और 9 किमी/घंटा है,<br>अब, धारा की गति = <math display=\"inline\"><mfrac><mrow><mn>9</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>3</mn><mn>2</mn></mfrac></math> = 1.5 km/h</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. Three pipes P, Q and R can fill a cistern in 40 minutes, 80 minutes and 120 minutes, respectively. Initially, all the pipes are opened. After how much time (in minutes) should the pipes Q and R be turned off so that the cistern will be completely filled in just half an hour?</p>",
                    question_hi: "<p>75. तीन पाइप P, Q और R एक टंकी को क्रमशः 40 मिनट, 80 मिनट और 120 मिनट में भर सकते हैं। शुरुआत में, सभी पाइप खोल दिए जाते हैं। कितने समय (मिनटों में) के बाद, पाइप Q और R को बंद कर देना चाहिए ताकि टंकी केवल आधे घंटे में पूरी भर जाए?</p>",
                    options_en: ["<p>14</p>", "<p>10</p>", 
                                "<p>16</p>", "<p>12</p>"],
                    options_hi: ["<p>14</p>", "<p>10</p>",
                                "<p>16</p>", "<p>12</p>"],
                    solution_en: "<p>75.(d)<br><strong id=\"docs-internal-guid-ca016349-7fff-843a-14c1-d1699e7e2227\"><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXc770zX56KZT6w4YoQi4OU7zTEtoso89TS7GcoqOpHMnE_SEZ1bCNUszpVdnQ5XPqzVuYI26EQg0jQOqA935MDMC1jwvuanqp8CFwxeS1wO5rmW5WXMhClqd0KIfMK90VuMTj7d9Q?key=R9kPZRrYWrMKnOZART2DEx93\" width=\"264\" height=\"181\"></strong><br>Let pipes Q and R be turned off for t minutes<br>According to question <br>&rArr; (6 + 3+2)&times; t + 6 &times; (30 - t) = 240<br>&rArr; 11t + 180 - 6t = 240<br>&rArr; 5t = 60 &rArr; t = 12</p>",
                    solution_hi: "<p>75.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744793309110.png\" alt=\"rId55\" height=\"181\"><br>माना , पाइप Q और R को t मिनट के लिए बंद कर दिया गया है<br>प्रश्न के अनुसार <br>&rArr; (6 + 3+2)&times; t + 6 &times; (30 - t) = 240<br>&rArr; 11t + 180 - 6t = 240<br>&rArr; 5t = 60&nbsp;&rArr; t = 12</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Select the sentence that has a grammatical error.</p>",
                    question_hi: "<p>76. Select the sentence that has a grammatical error.</p>",
                    options_en: ["<p>I will be driving into town later on.</p>", "<p>I might drive into town later on.</p>", 
                                "<p>I could be driving into town later on.</p>", "<p>I will be drive into town later on.</p>"],
                    options_hi: ["<p>I will be driving into town later on.</p>", "<p>I might drive into town later on.</p>",
                                "<p>I could be driving into town later on.</p>", "<p>I will be drive into town later on.</p>"],
                    solution_en: "<p>76.(d) I will be drive into town later on.<br>&lsquo;Will be + V<sub>ing</sub>&rsquo; is the correct grammatical structure for the sentence which is in the future continuous tense. &lsquo;Drive&rsquo; must be replaced with \'driving\'. Hence, the sentence given in option (d) has an error.</p>",
                    solution_hi: "<p>76.(d) I will be drive into town later on.<br>&lsquo;Will be + V<sub>ing</sub>&rsquo; future continuous tense में sentence के लिए सही grammatical structure है। &lsquo;Drive&rsquo; के स्थान पर \'driving\' का प्रयोग होगा। इसलिए, option (d) में दिए गए sentence में error है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who is trained to travel in a spacecraft.</p>",
                    question_hi: "<p>77. Select the option that can be used as a one-word substitute for the given group of words.<br>A person who is trained to travel in a spacecraft.</p>",
                    options_en: ["<p>Astronaut</p>", "<p>Anthropologist</p>", 
                                "<p>Alchemist</p>", "<p>Curator</p>"],
                    options_hi: ["<p>Astronaut</p>", "<p>Anthropologist</p>",
                                "<p>Alchemist</p>", "<p>Curator</p>"],
                    solution_en: "<p>77.(a) <strong>Astronaut-</strong> a person who is trained to travel in a spacecraft.<br><strong>Anthropologist-</strong> a scientist who studies the origins, development, and behaviors of humans.<br><strong>Alchemist-</strong> a person who transforms or creates something through a seemingly magical process.<br><strong>Curator-</strong> the person in charge of a museum, art collection, etc.</p>",
                    solution_hi: "<p>77.(a) <strong>Astronaut</strong> (अंतरिक्ष यात्री)- a person who is trained to travel in a spacecraft.<br><strong>Anthropologist</strong> (मानवविज्ञानी)- a scientist who studies the origins, development, and behaviors of humans.<br><strong>Alchemist</strong> (कीमियागर)- a person who transforms or creates something through a seemingly magical process.<br><strong>Curator</strong> (संग्रहाध्यक्ष)- the person in charge of a museum, art collection, etc.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Choose the most appropriate option to change the narration (direct / indirect) of the given sentence.<br>&ldquo;They are late&rdquo; She has already told us.</p>",
                    question_hi: "<p>78. Choose the most appropriate option to change the narration (direct / indirect) of the given sentence.<br>&ldquo;They are late&rdquo; She has already told us.</p>",
                    options_en: ["<p>She has already told us that they are late.</p>", "<p>She told us that they are late already.</p>", 
                                "<p>She told us that they are already late.</p>", "<p>She has already been telling us that they are late.</p>"],
                    options_hi: ["<p>She has already told us that they are late.</p>", "<p>She told us that they are late already.</p>",
                                "<p>She told us that they are already late.</p>", "<p>She has already been telling us that they are late.</p>"],
                    solution_en: "<p>78.(a) She already told us that they were late. <br>(b). She told us that they <strong>are late</strong> already.(Incorrect change of tense)<br>(c). She told us that they <strong>are already late.</strong> (Incorrect change of tense)<br>(d.) She <strong>has already been telling us</strong> that they are late.(Incorrect change of tense)</p>",
                    solution_hi: "<p>78.(a) She already told us that they were late. <br>(b). She told us that they <strong>are late</strong> already.(Tense का गलत प्रयोग )<br>(c). She told us that they <strong>are already late</strong>.(Tense का गलत प्रयोग ) <br>(d.) She <strong>has already been telling us</strong> that they are late.(Tense का गलत प्रयोग )</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Select the most appropriate ANTONYM of the underlined word in the following&nbsp;sentence.<br>My most <span style=\"text-decoration: underline;\">vivid</span> recollection of that summer is the ocean.</p>",
                    question_hi: "<p>79. Select the most appropriate ANTONYM of the underlined word in the following<br>sentence.<br>My most <span style=\"text-decoration: underline;\">vivid</span> recollection of that summer is the ocean.</p>",
                    options_en: ["<p>Eloquent</p>", "<p>Vague</p>", 
                                "<p>Pictorial</p>", "<p>Lucid</p>"],
                    options_hi: ["<p>Eloquent</p>", "<p>Vague</p>",
                                "<p>Pictorial</p>", "<p>Lucid</p>"],
                    solution_en: "<p>79.(b) <strong>Vague-</strong> not clearly expressed.<br><strong>Vivid-</strong> producing very clear, powerful, and detailed images in the mind.<br><strong>Eloquent-</strong> fluent or persuasive in speaking or writing.<br><strong>Pictorial-</strong> expressed in pictures.<br><strong>Lucid-</strong> clear and easy to understand.</p>",
                    solution_hi: "<p>79.(b) <strong>Vague</strong> (अस्पष्ट) - not clearly expressed.<br><strong>Vivid</strong> (सुस्पष्ट) - producing very clear, powerful, and detailed images in the mind.<br><strong>Eloquent</strong> (सुवक्ता) - fluent or persuasive in speaking or writing.<br><strong>Pictorial</strong> (चित्रात्मक) - expressed in pictures.<br><strong>Lucid</strong> (सुस्पष्ट) - clear and easy to understand.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. The sharp increase in our consumption of fossil fuels has led to their depletion at an alarming rate.<br>B. Unchecked burning of fossil fuel is like an unchecked dripping tap which will eventually run dry.<br>C. The toxic pollutants released from burning these fuels are also a cause for concern.<br>D. This has led to the tapping of various non-conventional sources of energy that are cleaner alternatives to fossil fuels.</p>",
                    question_hi: "<p>80. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>A. The sharp increase in our consumption of fossil fuels has led to their depletion at an alarming rate.<br>B. Unchecked burning of fossil fuel is like an unchecked dripping tap which will eventually run dry.<br>C. The toxic pollutants released from burning these fuels are also a cause for concern.<br>D. This has led to the tapping of various non-conventional sources of energy that are cleaner alternatives to fossil fuels.</p>",
                    options_en: ["<p>CDBA</p>", "<p>CBAD</p>", 
                                "<p>ACBD</p>", "<p>DCBA</p>"],
                    options_hi: ["<p>CDBA</p>", "<p>CBAD</p>",
                                "<p>ACBD</p>", "<p>DCBA</p>"],
                    solution_en: "<p>80.(c) ACBD<br>Sentence A will be the starting line as it contains the main idea of the parajumble i.e. depletion of fossil fuels. However, Sentence C states that the toxic pollutants released from burning these fuels are also a cause for concern. So, C will follow A. Further, Sentence B states that unchecked burning of fossil fuel is like an unchecked dripping tap that will eventually run dry and sentence D states that this has led to the tapping of various non-conventional sources of energy that are cleaner alternatives to fossil fuels. So, D will follow B. Going through the options, option c has the correct sequence.</p>",
                    solution_hi: "<p>80.(c) ACBD<br>वाक्य A शुरुआती line होगी क्योंकि इसमें parajumble का मुख्य विचार है अर्थात् fossil fuels की कमी। हालांकि, वाक्य C बताता है कि इन ईंधनों को जलाने से निकलने वाले जहरीले प्रदूषक भी चिंता का कारण हैं। तो A के बाद C होगा। इसके बाद, वाक्य B बताता है कि जीवाश्म ईंधन का अनियंत्रित जलना एक अनियंत्रित dripping tap की तरह है जो सूख जाएगा और वाक्य D बताता है कि इसी वजह से ऊर्जा के विभिन्न non-conventional sources का प्रयोग शुरू हुआ है जो कि ईंधन के स्वच्छ विकल्प हैं। तो B के बाद D होगा। सभी विकल्पों को देखते हुए, Option C का sequence सही है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Select the INCORRECTLY spelt word.</p>",
                    question_hi: "<p>81. Select the INCORRECTLY spelt word.</p>",
                    options_en: ["<p>Demolish</p>", "<p>Destroy</p>", 
                                "<p>Develop</p>", "<p>Betrey</p>"],
                    options_hi: ["<p>Demolish</p>", "<p>Destroy</p>",
                                "<p>Develop</p>", "<p>Betrey</p>"],
                    solution_en: "<p>81.(d) betrey<br>&lsquo;Betray&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>81.(d) betrey<br>&lsquo;Betray&rsquo; सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Select the most appropriate meaning of the underlined idiom in the following sentence. <br>I can&rsquo;t begin to talk on this situation right now; I am still <span style=\"text-decoration: underline;\">wrapping my head around it</span>.</p>",
                    question_hi: "<p>82. Select the most appropriate meaning of the underlined idiom in the following sentence. <br>I can&rsquo;t begin to talk on this situation right now; I am still <span style=\"text-decoration: underline;\">wrapping my head around it</span>.</p>",
                    options_en: ["<p>Understanding something complicated</p>", "<p>Comparing two things that can&rsquo;t be compared</p>", 
                                "<p>Revealing a secret nobody knows</p>", "<p>Ignoring something completely</p>"],
                    options_hi: ["<p>Understanding something complicated</p>", "<p>Comparing two things that can&rsquo;t be compared</p>",
                                "<p>Revealing a secret nobody knows</p>", "<p>Ignoring something completely</p>"],
                    solution_en: "<p>82.(a) <strong>Wrapping my head around it-</strong> understanding something complicated.</p>",
                    solution_hi: "<p>82.(a) <strong>Wrapping my head around it-</strong> understanding something complicated./किसी जटिल चीज़ को समझना।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>It is a fact that the same judgement can often <span style=\"text-decoration: underline;\">be taken by more than</span> one way.</p>",
                    question_hi: "<p>83. Select the most appropriate option to substitute the underlined segment in the given sentence.<br>It is a fact that the same judgement can often <span style=\"text-decoration: underline;\">be taken by more than</span> one way.</p>",
                    options_en: ["<p>be taken in more than</p>", "<p>be taken on more than</p>", 
                                "<p>be taken of more than</p>", "<p>be taken for more than</p>"],
                    options_hi: ["<p>be taken in more than</p>", "<p>be taken on more than</p>",
                                "<p>be taken of more than</p>", "<p>be taken for more than</p>"],
                    solution_en: "<p>83.(a) be taken in more than<br>The phrasal verb &lsquo;take in&rsquo; means to understand something that you read or hear. The given sentence states that it is a fact that the same judgement can be taken in more than one way. Hence, &lsquo;be taken in more than&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>83.(a) be taken in more than<br>phrasal verb &lsquo;take in&rsquo; का अर्थ है कुछ ऐसा समझना जो आप read या hear हैं। दिया गया sentence बताता है कि यह एक fact है कि एक ही निर्णय (judgement) को एक से अधिक तरीकों से लिया जा सकता है। इसलिए, &lsquo;be taken in more than&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Select the option that can be used as a one-word substitute for the given group of words.<br>Cannot be corrected</p>",
                    question_hi: "<p>84. Select the option that can be used as a one-word substitute for the given group of words.<br>Cannot be corrected</p>",
                    options_en: ["<p>Illegible</p>", "<p>Incorrigible</p>", 
                                "<p>Incredible</p>", "<p>Ineligible</p>"],
                    options_hi: ["<p>Illegible</p>", "<p>Incorrigible</p>",
                                "<p>Incredible</p>", "<p>Ineligible</p>"],
                    solution_en: "<p>84.(b) <strong>Incorrigible-</strong> cannot be corrected.<br><strong>Illegible-</strong> not clear enough to be read.<br><strong>Incredible-</strong> impossible to believe.<br><strong>Ineligible-</strong> not qualified for an office or position.</p>",
                    solution_hi: "<p>84.(b) <strong>Incorrigible</strong> (असुधार्य)- cannot be corrected.<br><strong>Illegible</strong> (अपठनीय)- not clear enough to be read.<br><strong>Incredible</strong> (अविश्वसनीय)- impossible to believe.<br><strong>Ineligible</strong> (अयोग्य)- not qualified for an office or position.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Did he plan an excursion to mountains?</p>",
                    question_hi: "<p>85. Choose the most appropriate option to change the voice (active / passive) form of the given sentence.<br>Did he plan an excursion to mountains?</p>",
                    options_en: ["<p>Was an excursion to mountains planned by him?</p>", "<p>had an excursion to mountains planned by him?</p>", 
                                "<p>Is an excursion to mountains planned by him?</p>", "<p>has an excursion to mountains planned by him?</p>"],
                    options_hi: ["<p>Was an excursion to mountains planned by him?</p>", "<p>had an excursion to mountains planned by him?</p>",
                                "<p>Is an excursion to mountains planned by him?</p>", "<p>has an excursion to mountains planned by him?</p>"],
                    solution_en: "<p>85.(a) Was an excursion to mountains planned by him?<br>(b). <strong>Had</strong> an excursion to mountains planned by him? (Tense has changed)<br>(c). <strong>Is an</strong> excursion to mountains planned by him? (Tense has changed)<br>(d). <strong>Has</strong> an excursion to mountains planned by him? (Tense has changed)</p>",
                    solution_hi: "<p>85.(a) Was an excursion to mountains planned by him?<br>(b) <strong>Had</strong> an excursion to mountains planned by him? (Tense बदल गया)<br>(c) <strong>Is an</strong> excursion to mountains planned by him? (Tense बदल गया)<br>(d) <strong>Has</strong> an excursion to mountains planned by him? (Tense बदल गया)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate synonym of the given word.<br>Fertile</p>",
                    question_hi: "<p>86. Select the most appropriate synonym of the given word.<br>Fertile</p>",
                    options_en: ["<p>Hectic</p>", "<p>Destructive</p>", 
                                "<p>Amusing</p>", "<p>Productive</p>"],
                    options_hi: ["<p>Hectic</p>", "<p>Destructive</p>",
                                "<p>Amusing</p>", "<p>Productive</p>"],
                    solution_en: "<p>86.(d) <strong>Productive-</strong> able to produce large amounts of goods, crops, or other commodities.<br><strong>Fertile-</strong> capable of producing abundant growth.<br><strong>Hectic-</strong> full of intense activity.<br><strong>Destructive-</strong> causing great damage or harm.<br><strong>Amusing-</strong> entertaining or funny.</p>",
                    solution_hi: "<p>86.(d) <strong>Productive</strong> (उत्पादक) - able to produce large amounts of goods, crops, or other commodities.<br><strong>Fertile</strong> (उपजाऊ) - capable of producing abundant growth.<br><strong>Hectic</strong> (व्यस्ततापूर्ण) - full of intense activity.<br><strong>Destructive</strong> (विनाशकारी) - causing great damage or harm.<br><strong>Amusing</strong> (मनोरंजक) - entertaining or funny.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of the given idiom.<br>A bolt from the blue</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of the given idiom.<br>A bolt from the blue</p>",
                    options_en: ["<p>A sudden and unexpected event</p>", "<p>Raining heavily</p>", 
                                "<p>Thunder and rain in a strom</p>", "<p>A spontaneous but expected event</p>"],
                    options_hi: ["<p>A sudden and unexpected event</p>", "<p>Raining heavily</p>",
                                "<p>Thunder and rain in a strom</p>", "<p>A spontaneous but expected event</p>"],
                    solution_en: "<p>87.(a) <strong>A bolt from the blue- </strong>a sudden and unexpected event.<br>E.g.- The sudden announcement of the company\'s closure was a bolt from the blue for all the employees.</p>",
                    solution_hi: "<p>87.(a) <strong>A bolt from the blue-</strong> a sudden and unexpected event./आकस्मिक एवं अप्रत्याशित घटना। <br>E.g.- The sudden announcement of the company\'s closure was a bolt from the blue for all the employees.</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Select the most appropriate option that collocates with &lsquo;wish&rsquo; to fill in the blank. <br>The genie asked Aladdin to ________ a wish.</p>",
                    question_hi: "<p>88. Select the most appropriate option that collocates with &lsquo;wish&rsquo; to fill in the blank. <br>The genie asked Aladdin to ________ a wish.</p>",
                    options_en: ["<p>make</p>", "<p>take</p>", 
                                "<p>ask</p>", "<p>tell</p>"],
                    options_hi: ["<p>make</p>", "<p>take</p>",
                                "<p>ask</p>", "<p>tell</p>"],
                    solution_en: "<p>88.(a) Make<br>&lsquo;Make&rsquo; is the correct verb to use with &lsquo;wish&rsquo;. The phrase &lsquo;make a wish&rsquo; means to wish for something. The given sentence states that the genie asked Aladdin to make a wish. Hence, &lsquo;make&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>88.(a) Make<br>&lsquo;Wish&rsquo; के साथ प्रयोग करने के लिए &lsquo;Make&rsquo; सही verb है। Phrase &lsquo;make a wish&rsquo; का अर्थ है किसी चीज़ की इच्छा करना। दिए गए sentence में कहा गया है कि जिन्न (genie) ने अलादीन (Aladdin) से इच्छा करने के लिए कहा। अतः, &lsquo;make&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. Select the most appropriate synonym of the given word.<br>Desire</p>",
                    question_hi: "<p>89. Select the most appropriate synonym of the given word.<br>Desire</p>",
                    options_en: ["<p>Disgust</p>", "<p>Aversion</p>", 
                                "<p>Loathing</p>", "<p>Longing</p>"],
                    options_hi: ["<p>Disgust</p>", "<p>Aversion</p>",
                                "<p>Loathing</p>", "<p>Longing</p>"],
                    solution_en: "<p>89.(d) <strong>Longing-</strong> a strong, persistent desire for something.<br><strong>Desire-</strong> a strong feeling of wanting something.<br><strong>Disgust-</strong> a strong feeling of dislike or revulsion.<br><strong>Aversion-</strong> a strong dislike or unwillingness.<br><strong>Loathing-</strong> intense hatred or disgust.</p>",
                    solution_hi: "<p>89.(d) <strong>Longing</strong> (लालसा) - a strong, persistent desire for something.<br><strong>Desire</strong> (इच्छा) - a strong feeling of wanting something.<br><strong>Disgust</strong> (घृणित) - a strong feeling of dislike or revulsion.<br><strong>Aversion</strong> (विरूचि) - a strong dislike or unwillingness.<br><strong>Loathing</strong> (घृणा) - intense hatred or disgust.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>Mr. Stein is a very cold and <span style=\"text-decoration: underline;\">hard</span> man.</p>",
                    question_hi: "<p>90. Select the most appropriate ANTONYM of the underlined word in the given sentence.<br>Mr. Stein is a very cold and <span style=\"text-decoration: underline;\">hard</span> man.</p>",
                    options_en: ["<p>Relaxed</p>", "<p>Tolerant</p>", 
                                "<p>Clement</p>", "<p>Charitable</p>"],
                    options_hi: ["<p>Relaxed</p>", "<p>Tolerant</p>",
                                "<p>Clement</p>", "<p>Charitable</p>"],
                    solution_en: "<p>90.(d) <strong>Charitable-</strong> generous in giving to those in need.<br><strong>Hard-</strong> firm and solid in texture.<br><strong>Relaxed-</strong> free from tension or anxiety.<br><strong>Tolerant-</strong> willing to accept others\' beliefs or behaviors.<br><strong>Clement-</strong> mild or merciful.</p>",
                    solution_hi: "<p>90.(d) <strong>Charitable</strong> (दानशील) - generous in giving to those in need.<br><strong>Hard</strong> (कठोर) - firm and solid in texture.<br><strong>Relaxed</strong> (तनाव मुक्त) - free from tension or anxiety.<br><strong>Tolerant</strong> (सहनशील) - willing to accept others\' beliefs or behaviors.<br><strong>Clement</strong> (दयालु) - mild or merciful.</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. <strong>Cloze Test :-</strong> <br>Throughout the ages, birds have been a source of wonder to all who have (91) ______ their soaring flight or listened to (92)______ sweet songs. As a group, birds are (93) _______. They are the only animals creatures covered (94) ____feathers. This evolutionary development (95) ______ birds from all other animals.<br>Select the most appropriate option to fill in the blank no 91.</p>",
                    question_hi: "<p>91.<strong> Cloze Test :-</strong> <br>Throughout the ages, birds have been a source of wonder to all who have (91) ______ their soaring flight or listened to (92)______ sweet songs. As a group, birds are (93) _______. They are the only animals creatures covered (94) ____feathers. This evolutionary development (95) ______ birds from all other animals.<br>Select the most appropriate option to fill in the blank no 91.</p>",
                    options_en: ["<p>claimed</p>", "<p>verified</p>", 
                                "<p>observed</p>", "<p>supported</p>"],
                    options_hi: ["<p>claimed</p>", "<p>verified</p>",
                                "<p>observed</p>", "<p>supported</p>"],
                    solution_en: "<p>91.(c) <strong>Observed</strong> - notice (something) <br><strong>Claimed</strong> - to say that something is true, without having any proof<br><strong>Verified</strong> - to check or state that something is true<br><strong>Supported</strong> - to help somebody<br>So , option (c) is fit to the context of the sentence.</p>",
                    solution_hi: "<p>91.(c) <strong>Observed</strong> - निरीक्षण करना<br><strong>Claimed</strong> - किसी बात का बिना प्रमाणों के दावा करना <br><strong>Verified</strong> - जाँचना या बताना कि कुछ सत्य है<br><strong>Supported</strong> -किसी की मदद करना<br>इसलिए, विकल्प (c) वाक्य के संदर्भ में उपयुक्त है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. <strong>Cloze Test :-</strong> <br>Throughout the ages, birds have been a source of wonder to all who have (91) ______ their soaring flight or listened to (92)______ sweet songs. As a group, birds are (93) _______. They are the only animals creatures covered (94) ____feathers. This evolutionary development (95) ______ birds from all other animals.<br>Select the most appropriate option to fill in the blank no 92.</p>",
                    question_hi: "<p>92. <strong>Cloze Test :-</strong> <br>Throughout the ages, birds have been a source of wonder to all who have (91) ______ their soaring flight or listened to (92)______ sweet songs. As a group, birds are (93) _______. They are the only animals creatures covered (94) ____feathers. This evolutionary development (95) ______ birds from all other animals.<br>Select the most appropriate option to fill in the blank no 92.</p>",
                    options_en: ["<p>them</p>", "<p>its</p>", 
                                "<p>their</p>", "<p>they</p>"],
                    options_hi: ["<p>them</p>", "<p>its</p>",
                                "<p>their</p>", "<p>they</p>"],
                    solution_en: "<p>92.(c) their.<br>The possessive pronoun &ldquo;their&rdquo; should be used with &ldquo;birds&rdquo; (plural)</p>",
                    solution_hi: "<p>92.(c) their. <br>&ldquo;Birds&rdquo; (plural) का प्रयोग The possessive pronoun &ldquo;their&rdquo; के साथ करना चाहिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. <strong>Cloze Test :-</strong> <br>Throughout the ages, birds have been a source of wonder to all who have (91) ______ their soaring flight or listened to (92)______ sweet songs. As a group, birds are (93) _______. They are the only animals creatures covered (94) ____feathers. This evolutionary development (95) ______ birds from all other animals.<br>Select the most appropriate option to fill in the blank no 93.</p>",
                    question_hi: "<p>93. <strong>Cloze Test :-</strong> <br>Throughout the ages, birds have been a source of wonder to all who have (91) ______ their soaring flight or listened to (92)______ sweet songs. As a group, birds are (93) _______. They are the only animals creatures covered (94) ____feathers. This evolutionary development (95) ______ birds from all other animals.<br>Select the most appropriate option to fill in the blank no 93.</p>",
                    options_en: ["<p>mundane</p>", "<p>unique</p>", 
                                "<p>indifferent</p>", "<p>ordinary</p>"],
                    options_hi: ["<p>mundane</p>", "<p>unique</p>",
                                "<p>indifferent</p>", "<p>ordinary</p>"],
                    solution_en: "<p>93.(b) <strong>Unique</strong> - being the only one of its kind; unlike anything else<br><strong>Mundane</strong> - lacking interest or excitement; dull.<br><strong>Indifferent</strong> - having no particular interest or sympathy; unconcerned.<br><strong>Ordinary</strong> - with no special or distinctive features; normal.<br>Option (b) is the answer</p>",
                    solution_hi: "<p>93.(b) <strong>Unique</strong> - किसी और चीज के&nbsp;विपरीत खुद की तरह का होना; <br><strong>Mundane</strong> (सांसारिक) - रुचि या उत्तेजना की कमी; सुस्त।<br><strong>Indifferent</strong> - कोई विशेष रुचि या सहानुभूति न होना।<br><strong>Ordinary</strong> - बिना किसी विशेष या विशिष्ट लक्षणों के; सामान्य।<br>विकल्प (b) उत्तर है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. <strong>Cloze Test :-</strong> <br>Throughout the ages, birds have been a source of wonder to all who have (91) ______ their soaring flight or listened to (92)______ sweet songs. As a group, birds are (93) _______. They are the only animals creatures covered (94) ____feathers. This evolutionary development (95) ______ birds from all other animals.<br>Select the most appropriate option to fill in the blank no 94.</p>",
                    question_hi: "<p>94. <strong>Cloze Test :-</strong> <br>Throughout the ages, birds have been a source of wonder to all who have (91) ______ their soaring flight or listened to (92)______ sweet songs. As a group, birds are (93) _______. They are the only animals creatures covered (94) ____feathers. This evolutionary development (95) ______ birds from all other animals.<br>Select the most appropriate option to fill in the blank no 94.</p>",
                    options_en: ["<p>on</p>", "<p>at</p>", 
                                "<p>with</p>", "<p>from</p>"],
                    options_hi: ["<p>on</p>", "<p>at</p>",
                                "<p>with</p>", "<p>from</p>"],
                    solution_en: "<p>94.(c) with,&nbsp;<br>The preposition &ldquo;with&rdquo; should be used with &ldquo;cover&rdquo;</p>",
                    solution_hi: "<p>94.(c) with<br>&ldquo;Cover&rdquo; के साथ preposition &ldquo;with&rdquo; का प्रयोग किया जाना चाहिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. <strong>Cloze Test:-</strong> <br>Throughout the ages, birds have been a source of wonder to all who have (91) ______ their soaring flight or listened to (92)______ sweet songs. As a group, birds are (93) _______. They are the only animals creatures covered (94) ____feathers. This evolutionary development (95) ______ birds from all other animals.<br>Select the most appropriate option to fill in the blank no 95.</p>",
                    question_hi: "<p>95.<strong> Cloze Test :-</strong> <br>Throughout the ages, birds have been a source of wonder to all who have (91) ______ their soaring flight or listened to (92)______ sweet songs. As a group, birds are (93) _______. They are the only animals creatures covered (94) ____feathers. This evolutionary development (95) ______ birds from all other animals.<br>Select the most appropriate option to fill in the blank no 95.</p>",
                    options_en: ["<p>is separated</p>", "<p>has been separated</p>", 
                                "<p>separates</p>", "<p>was separated</p>"],
                    options_hi: ["<p>is separated</p>", "<p>has been separated</p>",
                                "<p>separates</p>", "<p>was separated</p>"],
                    solution_en: "<p>95.(c) Separates , &ldquo;Separates&rdquo; should be used for the &ldquo;simple present tense&rdquo; and &ldquo;singular object.&rdquo;</p>",
                    solution_hi: "<p>95.(c) Separates ,\"Separates\" का उपयोग \"simple present tense\" और \"singular object\" के लिए किया जाना चाहिए।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Comprehension</strong> :-<br>While some herbal remedies may have proven health benefits, not all herbs are safe. It&rsquo;s important to research and consult with a healthcare professional before using any herbal concoction. While we may use some herbs for minor ailments, such as a cold or headache, others may treat more serious conditions, such as cancer or heart disease. Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives, so it&rsquo;s important to read labels and research before using these concoctions. While herbal remedies may have some health benefits, they should not be used as a substitute for traditional medicine.<br>Identify the tone of the passage.</p>",
                    question_hi: "<p>96. <strong>Comprehension</strong> :-<br>While some herbal remedies may have proven health benefits, not all herbs are safe. It&rsquo;s important to research and consult with a healthcare professional before using any herbal concoction. While we may use some herbs for minor ailments, such as a cold or headache, others may treat more serious conditions, such as cancer or heart disease. Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives, so it&rsquo;s important to read labels and research before using these concoctions. While herbal remedies may have some health benefits, they should not be used as a substitute for traditional medicine.<br>Identify the tone of the passage.</p>",
                    options_en: ["<p>Populist</p>", "<p>Pedestrian</p>", 
                                "<p>Sarcastic</p>", "<p>Cautionary</p>"],
                    options_hi: ["<p>Populist</p>", "<p>Pedestrian</p>",
                                "<p>Sarcastic</p>", "<p>Cautionary</p>"],
                    solution_en: "<p>96.(d) Cautionary<br>In a cautionary tone, the author warns the readers about potential risks or dangers and encourages them to be careful or avoid certain actions.</p>",
                    solution_hi: "<p>96.(d) Cautionary<br>Cautionary tone में author, readers को संभावित जोखिमों या खतरों के बारे में चेतावनी देता है तथा उन्हें सावधान (careful) रहने या कुछ कार्यों से बचने के लिए प्रोत्साहित (encourage) करता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Comprehension</strong> :-<br>While some herbal remedies may have proven health benefits, not all herbs are safe. It&rsquo;s important to research and consult with a healthcare professional before using any herbal concoction. While we may use some herbs for minor ailments, such as a cold or headache, others may treat more serious conditions, such as cancer or heart disease. Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives, so it&rsquo;s important to read labels and research before using these concoctions. While herbal remedies may have some health benefits, they should not be used as a substitute for traditional medicine.<br>Select the most appropriate title for the passage.</p>",
                    question_hi: "<p>97. <strong>Comprehension</strong> :-<br>While some herbal remedies may have proven health benefits, not all herbs are safe. It&rsquo;s important to research and consult with a healthcare professional before using any herbal concoction. While we may use some herbs for minor ailments, such as a cold or headache, others may treat more serious conditions, such as cancer or heart disease. Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives, so it&rsquo;s important to read labels and research before using these concoctions. While herbal remedies may have some health benefits, they should not be used as a substitute for traditional medicine.<br>Select the most appropriate title for the passage.</p>",
                    options_en: ["<p>The Benefits of Herbal Concoctions</p>", "<p>Herbal Remedies</p>", 
                                "<p>Risks with Herbal Remedies</p>", "<p>Herbal Remedies: A Way of Life</p>"],
                    options_hi: ["<p>The Benefits of Herbal Concoctions</p>", "<p>Herbal Remedies</p>",
                                "<p>Risks with Herbal Remedies</p>", "<p>Herbal Remedies: A Way of Life</p>"],
                    solution_en: "<p>97.(c) Risks with Herbal Remedies<br>It can be inferred from the passage that the most appropriate title for the passage is &lsquo;Risks with Herbal Remedies&rsquo;.</p>",
                    solution_hi: "<p>97.(c) Risks with Herbal Remedies<br>दिया गए passage से यह अनुमान लगाया जा सकता है कि passage के लिए सबसे उपयुक्त title &lsquo;Risks with Herbal Remedies&rsquo; है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Comprehension</strong> :-<br>While some herbal remedies may have proven health benefits, not all herbs are safe. It&rsquo;s important to research and consult with a healthcare professional before using any herbal concoction. While we may use some herbs for minor ailments, such as a cold or headache, others may treat more serious conditions, such as cancer or heart disease. Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives, so it&rsquo;s important to read labels and research before using these concoctions. While herbal remedies may have some health benefits, they should not be used as a substitute for traditional medicine.<br>Identify the statement that holds true according to the passage.</p>",
                    question_hi: "<p>98. <strong>Comprehension</strong> :-<br>While some herbal remedies may have proven health benefits, not all herbs are safe. It&rsquo;s important to research and consult with a healthcare professional before using any herbal concoction. While we may use some herbs for minor ailments, such as a cold or headache, others may treat more serious conditions, such as cancer or heart disease. Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives, so it&rsquo;s important to read labels and research before using these concoctions. While herbal remedies may have some health benefits, they should not be used as a substitute for traditional medicine.<br>Identify the statement that holds true according to the passage.</p>",
                    options_en: ["<p>Herbal concoctions should not be used for curing minor illnesses.</p>", "<p>Herbal remedies can sometimes contain artificial ingredients.</p>", 
                                "<p>Herbal remedies can be trusted blindly.</p>", "<p>Herbal remedies have no proven health benefits.</p>"],
                    options_hi: ["<p>Herbal concoctions should not be used for curing minor illnesses.</p>", "<p>Herbal remedies can sometimes contain artificial ingredients.</p>",
                                "<p>Herbal remedies can be trusted blindly.</p>", "<p>Herbal remedies have no proven health benefits.</p>"],
                    solution_en: "<p>98.(b) Herbal remedies can sometimes contain artificial ingredients.<br>(Line/s from the passage- Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives)</p>",
                    solution_hi: "<p>98.(b) Herbal remedies can sometimes contain artificial ingredients.<br>(Passage से ली गई line/s- Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives/सभी हर्बल मिश्रण प्राकृतिक और जैविक नहीं होते। कुछ में सिंथेटिक तत्व या संरक्षक (preservatives) हो सकते हैं।)</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Comprehension</strong> :-<br>While some herbal remedies may have proven health benefits, not all herbs are safe. It&rsquo;s important to research and consult with a healthcare professional before using any herbal concoction. While we may use some herbs for minor ailments, such as a cold or headache, others may treat more serious conditions, such as cancer or heart disease. Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives, so it&rsquo;s important to read labels and research before using these concoctions. While herbal remedies may have some health benefits, they should not be used as a substitute for traditional medicine.<br>Select the option that best describes the central theme of the passage.</p>",
                    question_hi: "<p>99. <strong>Comprehension</strong> :-<br>While some herbal remedies may have proven health benefits, not all herbs are safe. It&rsquo;s important to research and consult with a healthcare professional before using any herbal concoction. While we may use some herbs for minor ailments, such as a cold or headache, others may treat more serious conditions, such as cancer or heart disease. Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives, so it&rsquo;s important to read labels and research before using these concoctions. While herbal remedies may have some health benefits, they should not be used as a substitute for traditional medicine.<br>Select the option that best describes the central theme of the passage.</p>",
                    options_en: ["<p>While herbal remedies may have some benefits, these should be used cautiously and in consultation with health professionals.</p>", "<p>Herbal remedies can cause cancer and heart disease.</p>", 
                                "<p>Herbal remedies are essential for fighting all sorts of modern diseases.</p>", "<p>Herbal remedies can successfully replace all other forms of medication.</p>"],
                    options_hi: ["<p>While herbal remedies may have some benefits, these should be used cautiously and in consultation with health professionals.</p>", "<p>Herbal remedies can cause cancer and heart disease.</p>",
                                "<p>Herbal remedies are essential for fighting all sorts of modern diseases.</p>", "<p>Herbal remedies can successfully replace all other forms of medication.</p>"],
                    solution_en: "<p>99.(a) While herbal remedies may have some benefits, these should be used cautiously&nbsp;and in consultation with health professionals.<br>It can be inferred from the passage that the most appropriate theme for the passage is given in option (a).</p>",
                    solution_hi: "<p>99.(a) While herbal remedies may have some benefits, these should be used cautiously&nbsp;and in consultation with health professionals.<br>दिए गए Passage से यह अनुमान लगाया जा सकता है कि passage के लिए सबसे उपयुक्त theme option (a) में दी गई है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Comprehension</strong> :-<br>While some herbal remedies may have proven health benefits, not all herbs are safe. It&rsquo;s important to research and consult with a healthcare professional before using any herbal concoction. While we may use some herbs for minor ailments, such as a cold or headache, others may treat more serious conditions, such as cancer or heart disease. Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives, so it&rsquo;s important to read labels and research before using these concoctions. While herbal remedies may have some health benefits, they should not be used as a substitute for traditional medicine.<br>Select the most appropriate ANTONYM of the given word in the context of the passage.<br>Substitute</p>",
                    question_hi: "<p>100. <strong>Comprehension</strong> :-<br>While some herbal remedies may have proven health benefits, not all herbs are safe. It&rsquo;s important to research and consult with a healthcare professional before using any herbal concoction. While we may use some herbs for minor ailments, such as a cold or headache, others may treat more serious conditions, such as cancer or heart disease. Not all herbal concoctions are natural and organic. Some may contain synthetic ingredients or preservatives, so it&rsquo;s important to read labels and research before using these concoctions. While herbal remedies may have some health benefits, they should not be used as a substitute for traditional medicine.<br>Select the most appropriate ANTONYM of the given word in the context of the passage.<br>Substitute</p>",
                    options_en: ["<p>Perdurable</p>", "<p>Equivalent</p>", 
                                "<p>Rival</p>", "<p>Surrogate</p>"],
                    options_hi: ["<p>Perdurable</p>", "<p>Equivalent</p>",
                                "<p>Rival</p>", "<p>Surrogate</p>"],
                    solution_en: "<p>100.(b) <strong>Equivalent-</strong> equal in value, amount, function, or meaning.<br><strong>Substitute-</strong> a person or thing acting in place of another.<br><strong>Perdurable-</strong> enduring continuously; long-lasting.<br><strong>Rival-</strong> a competitor or opponent.<br><strong>Surrogate-</strong> one appointed to act in place of another.</p>",
                    solution_hi: "<p>100.(b) <strong>Equivalent</strong> (समतुल्य)- equal in value, amount, function, or meaning.<br><strong>Substitute</strong> (प्रतिस्थापन)- a person or thing acting in place of another.<br><strong>Perdurable</strong> (चिरस्थायी)- enduring continuously; long-lasting.<br><strong>Rival</strong> (प्रतिद्वंद्वी)- a competitor or opponent.<br><strong>Surrogate</strong> (प्रतिनिधि)- one appointed to act in place of another.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>