<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Test Interface</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        .question-nav{position:fixed;top:70px;right:0;height:calc(100vh - 70px);width:320px;transition:all .3s;z-index:100;display:flex;flex-direction:column}
        .question-content{margin-right:0;transition:all .3s}
        .question-box{width:40px;height:40px;margin:5px;cursor:pointer}
        .attempted{background-color:#3498db!important;color:white}
        .correct{background-color:#2ecc71!important;color:white}
        .incorrect{background-color:#e74c3c!important;color:white}
        .current{border:3px solid #f39c12!important}
        #question-boxes{overflow-y:auto;max-height:calc(100vh - 150px);padding-bottom:15px}
        @media (max-width:768px){.question-nav{width:100%;right:-100%}.question-content{margin-right:0}}
    </style>
</head>
<body>
    <div class="container-fluid p-0">
        <!-- Header with controls -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-graduation-cap me-2"></i>Smart Test
                </a>
                <!-- Section selector -->
                <div class="navbar-nav me-auto section-tabs">
                    <select class="form-select" id="section-selector" onchange="switchSection(this.value)">
                        <option value="all">All Sections</option>
                    </select>
                </div>
                <div class="d-flex align-items-center">
                    <!-- Timer -->
                    <div class="bg-light text-primary rounded px-3 py-2 me-2 fw-bold" id="timer">
                        <i class="far fa-clock me-2"></i><span id="timer-display">100:00</span>
                    </div>
                    <!-- Submit button -->
                    <button class="btn btn-success me-2" id="submit-btn" onclick="submitTest()">
                        <i class="fas fa-check me-1"></i> Submit
                    </button>
                    <!-- Language toggle -->
                    <button class="btn btn-light me-2" id="lang-btn" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i>
                    </button>
                    <!-- Menu toggle for mobile -->
                    <button class="btn btn-light d-lg-none" id="menu-toggle" onclick="toggleMenu()">
                        <i class="fas fa-th"></i>
                    </button>
                </div>
            </div>
        </nav>
        <div class="row m-0">
            <!-- Main content area -->
            <div class="col p-0 question-content" id="question-content">
                <div class="container py-4">
                    <!-- Question navigation -->
                    <div class="d-flex justify-content-between mb-4">
                        <button class="btn btn-outline-primary" onclick="prevQuestion()">
                            <i class="fas fa-chevron-left me-1"></i> Previous
                        </button>
                        <div class="fw-bold" id="question-counter">Question 1 of 100</div>
                        <button class="btn btn-outline-primary" onclick="nextQuestion()">
                            Next <i class="fas fa-chevron-right ms-1"></i>
                        </button>
                    </div>
                    <!-- Questions will be displayed here -->
                    <div id="questions-container"></div>
                </div>
            </div>
            <!-- Question navigation sidebar -->
            <div class="bg-light question-nav" id="question-nav">
                <div class="p-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="m-0">Questions</h5>
                        <div>
                            <span class="badge bg-secondary me-1" id="attempted-count">0</span>
                            <span class="badge bg-secondary" id="total-count">100</span>
                            <button class="btn btn-sm btn-outline-secondary ms-2 d-lg-none" onclick="toggleMenu()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="d-flex flex-wrap" id="question-boxes"></div>
                </div>
            </div>
        </div>
        <!-- Results modal -->
        <div class="modal fade" id="results-modal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">Test Results</h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row text-center">
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-primary bg-opacity-10 rounded">
                                    <h3 id="score-value">0</h3>
                                    <div class="small text-muted">Score</div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="p-3 bg-success bg-opacity-10 rounded">
                                    <h3 id="correct-value">0</h3>
                                    <div class="small text-muted">Correct</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-danger bg-opacity-10 rounded">
                                    <h3 id="incorrect-value">0</h3>
                                    <div class="small text-muted">Incorrect</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="p-3 bg-secondary bg-opacity-10 rounded">
                                    <h3 id="unattempted-value">0</h3>
                                    <div class="small text-muted">Unattempted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" onclick="reviewTest()">Review Test</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 100 * 60;
        let resultsModal;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            resultsModal = new bootstrap.Modal(document.getElementById('results-modal'));
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 768) {
                document.getElementById('question-content').style.marginRight = '320px';
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["18"] = {
                name: "General Awareness",
                start: 24,
                end: 48
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["17"] = {
                name: "Quantitative Aptitude",
                start: 49,
                end: 73
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="17">Quantitative Aptitude</option>`;
            sections["7"] = {
                name: "English",
                start: 74,
                end: 98
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="7">English</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 99,
                end: 99
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. 35 is related to 1125 following a certain logic. Following the same logic, 45 is related to 1925. To which of the following is 85 related, following the same logic?<br><strong>(NOTE:</strong> Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13-Operations on 13 such as adding / subtracting / multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>1. एक निश्चित तर्क का अनुसरण करते हुए 35, 1125 से संबंधित है। उसी तर्क का अनुसरण करते हुए 45, 1925 से संबंधित है। समान तर्क का अनुसरण करते हुए 85 निम्नलिखित में से किस से संबंधित है? <br><strong>(नोट:</strong> संख्याओं को उनके घटक अंकों में तोड़े बिना संक्रियाएँ केवल पूर्ण संख्याओं पर की जानी चाहिए। उदाहरण के लिए 13 को लीजिए - 13 पर की जाने वाली संक्रियाएँ, जैसे 13 में जोड़ना / घटाना / गुणा करना इत्यादि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)</p>",
                    options_en: [
                        "<p>7255</p>",
                        "<p>7125</p>",
                        "<p>7325</p>",
                        "<p>7135</p>"
                    ],
                    options_hi: [
                        "<p>7255</p>",
                        "<p>7125</p>",
                        "<p>7325</p>",
                        "<p>7135</p>"
                    ],
                    solution_en: "<p>1.(b) <strong>Logic</strong> :- (1st number)<sup>2</sup> - 100 = 2nd number<br>(35, 1125) :- (35)<sup>2</sup> - 100 &rArr; 1225 - 100 = 1125<br>(45 , 1925) :- (45)<sup>2</sup> - 100 &rArr; 2025 - 100 = 1925<br>Similarly,<br>(85, ?) :- (85)<sup>2</sup> - 100 &rArr; 7225 - 100 = 7125</p>",
                    solution_hi: "<p>1.(b) <strong>तर्क:-</strong> (पहली संख्या)<sup>2</sup> - 100 = दूसरी संख्या<br>(35, 1125) :- (35)<sup>2</sup> - 100 &rArr; 1225 - 100 = 1125<br>(45 , 1925) :- (45)<sup>2</sup> - 100 &rArr; 2025 - 100 = 1925<br>इसी प्रकार,<br>(85, ?) :- (85)<sup>2</sup> - 100 &rArr; 7225 - 100 = 7125</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Pointing to a photograph, Rani said, \"She is the daughter of the only son of my grandfather.\" How is the girl in the photograph related to Rani ?</p>",
                    question_hi: "<p>2. एक तस्वीर की ओर इशारा करते हुए रानी ने कहा \"वह मेरे दादा के इकलौते बेटे की बेटी है।\" तस्वीर में दिख रही लड़की रानी से किस प्रकार संबंधित है ?</p>",
                    options_en: [
                        "<p>Brother</p>",
                        "<p>Cousin sister</p>",
                        "<p>Sister</p>",
                        "<p>Daughter</p>"
                    ],
                    options_hi: [
                        "<p>भाई</p>",
                        "<p>चचेरी बहन / ममेरी बहन / मौसेरी बहन / फुफेरी बहन</p>",
                        "<p>बहन</p>",
                        "<p>बेटी</p>"
                    ],
                    solution_en: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607698586.png\" alt=\"rId4\" width=\"186\" height=\"146\"><br>Hence, &lsquo;Girl&rsquo; in the photograph is the sister of &lsquo;Rani&rsquo;.</p>",
                    solution_hi: "<p>2.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607698723.png\" alt=\"rId5\" width=\"200\"><br>अतः, तस्वीर में \'लड़की\' \'रानी\' की बहन है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "<p>3. Select the figure from among the given options that can replace the question mark (?) in the following series.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607698922.png\" alt=\"rId6\" width=\"300\"></p>",
                    question_hi: "<p>3. दिए गए विकल्पों में से उस आकृति का चयन कीजिए जो निम्नलिखित श्रृंखला में प्रश्नवाचक चिन्ह (?) को प्रतिस्थापित कर सकती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607698922.png\" alt=\"rId6\" width=\"300\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699040.png\" alt=\"rId7\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699171.png\" alt=\"rId8\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699324.png\" alt=\"rId9\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699441.png\" alt=\"rId10\" width=\"70\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699040.png\" alt=\"rId7\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699171.png\" alt=\"rId8\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699324.png\" alt=\"rId9\" width=\"70\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699441.png\" alt=\"rId10\" width=\"70\"></p>"
                    ],
                    solution_en: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699324.png\" alt=\"rId9\" width=\"70\"></p>",
                    solution_hi: "<p>3.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699324.png\" alt=\"rId9\" width=\"70\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Four letter-clusters have been given, out of which three are alike in some manner and one is different. Select the one that is different.<br><strong>Note</strong> : The odd one out is not based on the number of consonants/vowels or their position in the letter cluster</p>",
                    question_hi: "<p>4. चार अक्षर समूह दिए गए हैं, जिनमें से तीन किसी न किसी रूप में एक समान हैं। उस असमान विकल्प को चुनिए। <br><strong>नोट</strong> : अक्षर समूह में, असमान विकल्प व्यंजनों/स्वरों की संख्या या उनकी स्थिति पर आधारित नहीं है।</p>",
                    options_en: [
                        "<p>ONM</p>",
                        "<p>BCD</p>",
                        "<p>RST</p>",
                        "<p>JKL</p>"
                    ],
                    options_hi: [
                        "<p>ONM</p>",
                        "<p>BCD</p>",
                        "<p>RST</p>",
                        "<p>JKL</p>"
                    ],
                    solution_en: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699596.png\" alt=\"rId11\" width=\"150\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699775.png\" alt=\"rId12\" width=\"150\">&nbsp; &nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699923.png\" alt=\"rId13\" width=\"150\"><br>But<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700029.png\" alt=\"rId14\" width=\"150\"></p>",
                    solution_hi: "<p>4.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699596.png\" alt=\"rId11\" width=\"150\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699775.png\" alt=\"rId12\" width=\"150\">&nbsp; &nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607699923.png\" alt=\"rId13\" width=\"150\"><br>लेकिन,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700029.png\" alt=\"rId14\" width=\"150\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Three statements are followed by conclusions numbered I, II. You have to consider these statements to be true, even if they seem to be at variance with commonly known facts. Decide which of the given logically follow/s from the given statement.<br><strong>Statements:</strong><br>All water is earth.<br>All earth is fire.<br>Some fire is air.<br><strong>Conclusion</strong> (I): Some air is earth.<br><strong>Conclusion</strong> (II): Some fire is water.</p>",
                    question_hi: "<p>5. तीन कथनों के बाद।,॥ क्रमांकित निष्कर्ष दिए गए हैं। आपको इन कथनों को सत्य मानना होगा, चाहे वे समान्यतः ज्ञात तथ्यों से अलग प्रतीत होते हों। आपको निश्चय करना है कि कौन-सा/कौन-से निष्कर्ष तार्किक रूप से दिए गए कथन/कथनों के अनुसार है/ हैं।<br><strong>कथन:</strong><br>सभी जल, पृथ्वी है।<br>सभी पृथ्वी, आग है।<br>कुछ आग, हवा है।<br><strong>निष्कर्ष (I)</strong>: कुछ हवा, पृथ्वी हैं।<br><strong>निष्कर्ष (II):</strong> कुछ आग, जल है।</p>",
                    options_en: [
                        "<p>Neither conclusion (I) nor (II) follows.</p>",
                        "<p>Only conclusion (II) follows.</p>",
                        "<p>Only conclusion (I) follows.</p>",
                        "<p>Both conclusions (I) and (II) follow.</p>"
                    ],
                    options_hi: [
                        "<p>न तो निष्कर्ष (I) और न ही (II) कथनों के अनुसार है।</p>",
                        "<p>केवल निष्कर्ष (II) कथनों के अनुसार है।</p>",
                        "<p>केवल निष्कर्ष (I) कथनों के अनुसार है।</p>",
                        "<p>दोनों निष्कर्ष (I) और (II) कथनों के अनुसार हैं।</p>"
                    ],
                    solution_en: "<p>5.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700137.png\" alt=\"rId15\" width=\"356\" height=\"121\"><br>Only Conclusion II follows.</p>",
                    solution_hi: "<p>5.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700238.png\" alt=\"rId16\" width=\"343\" height=\"113\"><br>केवल निष्कर्ष II कथनों के अनुसार है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "<p>6. In a certain code, PROVIDE is written as QSPWJEF and PLATE is written as QMBUF. What is the code for POND ?</p>",
                    question_hi: "<p>6. एक निश्चित कूट भाषा में PROVIDE को QSPWJEF और PLATE को QMBUF लिखा जाता है। POND के लिए कूट क्&zwj;या है?</p>",
                    options_en: [
                        "<p>QPOE</p>",
                        "<p>RQPF</p>",
                        "<p>PNOD</p>",
                        "<p>ONMC</p>"
                    ],
                    options_hi: [
                        "<p>QPOE</p>",
                        "<p>RQPF</p>",
                        "<p>PNOD</p>",
                        "<p>ONMC</p>"
                    ],
                    solution_en: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700359.png\" alt=\"rId17\" width=\"180\" height=\"72\"> &nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700484.png\" alt=\"rId18\" width=\"128\" height=\"71\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700579.png\" alt=\"rId19\" width=\"108\" height=\"73\"></p>",
                    solution_hi: "<p>6.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700359.png\" alt=\"rId17\" width=\"180\" height=\"72\">&nbsp; &nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700484.png\" alt=\"rId18\" width=\"128\" height=\"71\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700579.png\" alt=\"rId19\" width=\"108\" height=\"73\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the term from among the given options that can replace the question mark (?) in the following series.<br>PJ 16, SM 25, VP 36, YS 49, ?</p>",
                    question_hi: "<p>7. दिए गए विकल्पों में से उस पद का चयन कीजिए, जो निम्नलिखित शृंखला में प्रश्न-चिह्न (?) को प्रतिस्थापित कर सकता है।<br>PJ 16, SM 25, VP 36, YS 49, ?</p>",
                    options_en: [
                        "<p>CW 81</p>",
                        "<p>BV 64</p>",
                        "<p>DW 64</p>",
                        "<p>CV 36</p>"
                    ],
                    options_hi: [
                        "<p>CW 81</p>",
                        "<p>BV 64</p>",
                        "<p>DW 64</p>",
                        "<p>CV 36</p>"
                    ],
                    solution_en: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700680.png\" alt=\"rId20\" width=\"423\" height=\"145\"></p>",
                    solution_hi: "<p>7.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700680.png\" alt=\"rId20\" width=\"423\" height=\"145\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. What will come in the place of question mark (?) in the following equation, if \'+\' and \'-\' are interchanged and \'&times;\' and \'&divide;\' are interchanged?<br>99 &divide;&nbsp;9 - 999 &times; 9 + 99 = ?</p>",
                    question_hi: "<p>8. निम्नलिखित समीकरण में यदि \'+\' और \'-\' को आपस में बदल दिया जाए तथा \'&times;\' और&nbsp; \'&divide;\' को आपस में बदल दिया जाए, तो प्रश्न चिह्न (?) के स्थान पर क्या आएगा?<br>99&nbsp; &divide; 9 - 999 &times; 9 + 99 = ?</p>",
                    options_en: [
                        "<p>901</p>",
                        "<p>902</p>",
                        "<p>903</p>",
                        "<p>904</p>"
                    ],
                    options_hi: [
                        "<p>901</p>",
                        "<p>902</p>",
                        "<p>903</p>",
                        "<p>904</p>"
                    ],
                    solution_en: "<p>8.(c) <strong>Given</strong>:- 99 &divide; 9 - 999 &times; 9 + 99 <br>On interchanging signs as per instructions we get,<br>99 &times;&nbsp;9 + 999 &divide; 9 - 99 <br>= 891 + 111 -&nbsp;99 = 903</p>",
                    solution_hi: "<p>8.(c) <strong>दिया गया:-</strong> 99 &divide;&nbsp;9 - 999 &times; 9 + 99 <br>हमें प्राप्त निर्देशों के अनुसार संकेतों को बदलने पर,<br>99 &times;&nbsp;9 + 999 &divide; 9 - 99 <br>= 891 + 111 - 99 = 903</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "<p>9. What will be the day of the week on 08 June 2030 ?</p>",
                    question_hi: "<p>9. 08 जून, 2030 को सप्ताह का कौन-सा दिन होगा ?</p>",
                    options_en: [
                        "<p>Saturday</p>",
                        "<p>Friday</p>",
                        "<p>Sunday</p>",
                        "<p>Monday</p>"
                    ],
                    options_hi: [
                        "<p>शनिवार</p>",
                        "<p>शुक्रवार</p>",
                        "<p>रविवार</p>",
                        "<p>सोमवार</p>"
                    ],
                    solution_en: "<p>9.(a) <br>Formula for calculating day of any date<br>0 = sunday, 1 = monday.... 6 = saturday<br>A = century code (4,2,0,6 for remainder 1,2,3,0) where remainder = dividing by first two digit of the year by 4<br>B = last two digits of year<br>C = number of leap years fallen in that century before that date.<br>D = month code (respective code of the months are 0, 3, 3, 6, 1, 4, 6, 2, 5, 0, 3, 5)<br>E = date<br>Day = remainder of (A + B + C + D + E) when divided by 7.<br>For 8 June 2030,<br>A = 6 B = 30 C = 7 D = 4 E = 8<br>According to formula, <br>Remainder of (A + B + C + D + E)/7 = 55/7 <br>is 6 &rArr; Hence, 6 stands for Saturday.</p>",
                    solution_hi: "<p>9.(a)<br>किसी भी तिथि का दिन निकालने का सूत्र,<br>0 = रविवार , 1 = सोमवार .... 6 = शनिवार <br>A = शताब्दी कोड ( 1, 2, 3, 0 शेष के लिए 4, 2, 0, 6 ); जहां शेष = वर्ष के पहले दो अंकों को 4 से विभाजित करना,<br>B = वर्ष के अंतिम दो अंक<br>C = उस शताब्दी में उस तिथि से पहले आये हुए लीप वर्षों की संख्या।<br>D = माह कोड (महीनों के संबंधित कोड 0, 3, 3, 6, 1, 4, 6, 2, 5, 0, 3, 5)<br>E = दिन <br>दिन = (A + B + C + D + E) को 7 से विभाजित करने पर शेषफल <br>8 जून 2030 के लिए,<br>A = 6 B = 30 C = 7 D = 4 E = 8<br>सूत्र के अनुसार,<br>(A + B + C + D + E) / 7 = 55/7 का शेषफल 6 है। <br>अत: 6 शनिवार का कोड है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. How many rectangles are there in the given figure ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700815.png\" alt=\"rId21\" width=\"244\" height=\"88\"></p>",
                    question_hi: "<p>10. दी गई आकृति में कितने आयत हैं ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700815.png\" alt=\"rId21\" width=\"244\" height=\"88\"></p>",
                    options_en: [
                        "<p>11</p>",
                        "<p>9</p>",
                        "<p>8</p>",
                        "<p>10</p>"
                    ],
                    options_hi: [
                        "<p>11</p>",
                        "<p>9</p>",
                        "<p>8</p>",
                        "<p>10</p>"
                    ],
                    solution_en: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700939.png\" alt=\"rId22\" width=\"400\"><br>There are 10 rectangle<br>ABDC, IHGF, NOED , OPFE, NPFD, JKLM, MLON, JKON, MLED, JKED</p>",
                    solution_hi: "<p>10.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607700939.png\" alt=\"rId22\" width=\"400\"><br>10 आयत हैं<br>ABDC, IHGF, NOED , OPFE, NPFD, JKLM, MLON, JKON, MLED, JKED</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the option figure in which the given figure (X) is embedded as its part (rotation is NOT allowed).<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701093.png\" alt=\"rId23\" width=\"100\" height=\"103\"></p>",
                    question_hi: "<p>11. उस विकल्प आकृति का चयन कीजिए जिसमें दी गई आकृति (X) उसके भाग के रूप में सन्निहित है (घूर्णन की अनुमति नहीं है)।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701093.png\" alt=\"rId23\" width=\"100\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701225.png\" alt=\"rId24\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701318.png\" alt=\"rId25\" width=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701408.png\" alt=\"rId26\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701505.png\" alt=\"rId27\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701225.png\" alt=\"rId24\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701318.png\" alt=\"rId25\" width=\"100\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701408.png\" alt=\"rId26\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701505.png\" alt=\"rId27\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701711.png\" alt=\"rId28\" width=\"90\"></p>",
                    solution_hi: "<p>11.(c)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701711.png\" alt=\"rId28\" width=\"90\"></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "12. Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters/number of consonants/vowels in the word)<br />Sugar : Diabetes :: Fat : _______",
                    question_hi: "12. उस विकल्प का चयन करें, जो तीसरे शब्द से उसी प्रकार संबंधित है, जैसे दूसरा शब्द पहले शब्द से संबंधित है। (शब्दों को हिन्दी के अर्थपूर्ण शब्दों के रूप में माना जाना चाहिए और ये शब्द, अक्षरों की संख्या/व्यंजनों/स्वरों की<br />संख्या के आधार पर एक-दूसरे से संबंधित नहीं होने चाहिए)<br />चीनी : मधुमेह :: वसा : _______",
                    options_en: [
                        " Insomnia",
                        " Obesity",
                        " Thyroid",
                        " Lethargy"
                    ],
                    options_hi: [
                        " अनिद्रा",
                        " मोटापा",
                        " थाइरोइड",
                        " तन्द्रा"
                    ],
                    solution_en: "12.(b) As diabetes is caused by Sugar similarly Obesity is caused by Fat.",
                    solution_hi: "12.(b) जिस प्रकार मधुमेह चीनी के कारण होता है उसी प्रकार मोटापा वसा के कारण होता है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "<p>13. Select the option that represents the letters that, when sequentially placed from left to right in the blanks, will complete the letter series.<br>_ R _ _ A R _ O _ _ G O A _ G O</p>",
                    question_hi: "<p>13. उस विकल्प का चयन करें, जो उन अक्षरों का प्रतिनिधित्व करता है, जो रिक्त स्थानों में क्रमिक रूप से बाएँ से दाएँ रखे जाने पर दी गई अक्षर शृंखला को पूरा करेंगे। <br>_ R _ _ A R _ O _ _ G O A _ G O</p>",
                    options_en: [
                        "<p>A G O G R A A</p>",
                        "<p>A G O G A R R</p>",
                        "<p>A A G O G R R</p>",
                        "<p>A G G O A R R</p>"
                    ],
                    options_hi: [
                        "<p>A G O G R A A</p>",
                        "<p>A G O G A R R</p>",
                        "<p>A A G O G R R</p>",
                        "<p>A G G O A R R</p>"
                    ],
                    solution_en: "<p>13.(b)<br><span style=\"text-decoration: underline;\"><strong>A</strong></span> R <span style=\"text-decoration: underline;\"><strong>G O</strong></span> / A R <span style=\"text-decoration: underline;\"><strong>G</strong></span> O / <span style=\"text-decoration: underline;\"><strong>A R</strong></span> G O / A <span style=\"text-decoration: underline;\"><strong>R</strong></span> G O</p>",
                    solution_hi: "<p>13.(b)<br><span style=\"text-decoration: underline;\"><strong>A</strong></span> R <span style=\"text-decoration: underline;\"><strong>G O</strong></span> / A R <span style=\"text-decoration: underline;\"><strong>G</strong></span> O / <span style=\"text-decoration: underline;\"><strong>A R</strong></span> G O / A <span style=\"text-decoration: underline;\"><strong>R</strong></span> G O</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. In a certain code language, <br>\'X # Y\' means \'X is the wife of Y\',<br>\'X $ Y\' means \'X is the daughter of Y\',<br>\'X @ Y\' means \'X is the brother of Y\',<br>\'X &amp; Y\' means \'X is the father of Y\',<br>\'X ✕ Y\' means \'X is the sister of Y\'.<br>If \'I @ H $ F # D &amp; L # K @ O\', then how is I related to L?</p>",
                    question_hi: "<p>14. एक निश्चित कूट भाषा में, <br>\'X # Y\' का अर्थ है कि \'X, Y की पत्नी है\',<br>\'X $ Y\' का अर्थ है कि \'X, Y की पुत्री है\',<br>\'X @Y\' का अर्थ है कि \'X, Y का भाई है,<br>\'X &amp; Y\' का अर्थ है कि \'X, Y का पिता है\',<br>\'X ✕ Y\' का अर्थ है कि \'X,Y की बहन है।\'.<br>यदि \' I @ H $ F # D &amp; L # K @ O है\', तो। का L से क्या संबंध है?</p>",
                    options_en: [
                        "<p>Father\'s brother</p>",
                        "<p>Brother</p>",
                        "<p>Sister</p>",
                        "<p>Brother\'s son</p>"
                    ],
                    options_hi: [
                        "<p>चाचा</p>",
                        "<p>भाई</p>",
                        "<p>बहन</p>",
                        "<p>भांजा/भतीजा</p>"
                    ],
                    solution_en: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701843.png\" alt=\"rId29\" width=\"271\" height=\"101\"><br>I is the brother of L.</p>",
                    solution_hi: "<p>14.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701843.png\" alt=\"rId29\" width=\"271\" height=\"101\"><br>I , L का भाई है</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "6",
                    question_en: "<p>15. Which two numbers (not individual digits) should be interchanged to make the given equation correct ?<br>56 + (11 <math display=\"inline\"><mo>&#215;</mo></math> 2) - (52 &divide; 4) + 16 = 76</p>",
                    question_hi: "<p>15. दिए गए समीकरण को संतुलित करने के लिए किन दो संख्याओं (अकेले अंकों को नहीं) को आपस में बदलना होगा ?<br>56 + (11 <math display=\"inline\"><mo>&#215;</mo></math> 2) - (52 &divide; 4) + 16 = 76</p>",
                    options_en: [
                        "<p>16 and 11</p>",
                        "<p>56 and 52</p>",
                        "<p>2 and 4</p>",
                        "<p>52 and 16</p>"
                    ],
                    options_hi: [
                        "<p>16 और 11</p>",
                        "<p>56 और 52</p>",
                        "<p>2 और 4</p>",
                        "<p>52 और 16</p>"
                    ],
                    solution_en: "<p>15.(b) <strong>Given</strong> :- 56 + (11 &times; 2) - (52 &divide;&nbsp;4) + 16 = 76<br>After going through all the options, option (b) satisfies. After interchanging 56 and 52 we get<br>52 + (11 &times; 2) - (56 &divide; 4) + 16 <br>52 + 22 - 14 + 16<br>74 + 2 = 76<br>L.H.S. = R.H.S.</p>",
                    solution_hi: "<p>15.(b) <strong>दिया गया :-</strong>&nbsp; &nbsp;56 + (11 &times; 2) - (52 &divide; 4) + 16 = 76<br>सभी विकल्पों को देखने के बाद विकल्प (b) संतुष्ट करता है। 56 और 52 को आपस में बदलने पर हमें प्राप्त होता है<br>52 + (11 &times; 2) - (56 &divide; 4) + 16&nbsp;<br>52 + 22 - 14 + 16<br>74 + 2 = 76<br>L.H.S. = R.H.S.</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "6",
                    question_en: "<p>16. Select the option that is related to the fifth letter cluster in the same way as the second letter cluster is related to the first letter cluster and the fourth letter cluster is related to the third letter cluster,<br>ROYAL : ALZRO :: ABOUT : UTPAB :: POLES : ?</p>",
                    question_hi: "<p>16. उस विकल्प का चयन करें, जो पाँचवें अक्षर-समूह से उसी तरह संबंधित है, जैसे दूसरा अक्षर-समूह पहले अक्षर-समूह से और चौथा अक्षर-समूह तीसरे अक्षर-समूह से संबंधित है। <br>ROYAL : ALZRO :: ABOUT : UTPAB :: POLES : ?</p>",
                    options_en: [
                        "<p>ESNPQ</p>",
                        "<p>FRMQN</p>",
                        "<p>FSNPR</p>",
                        "<p>ESMPO</p>"
                    ],
                    options_hi: [
                        "<p>ESNPQ</p>",
                        "<p>FRMQN</p>",
                        "<p>FSNPR</p>",
                        "<p>ESMPO</p>"
                    ],
                    solution_en: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701974.png\" alt=\"rId30\" width=\"150\">&nbsp; &nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607702118.png\" alt=\"rId31\" width=\"150\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607702250.png\" alt=\"rId32\" width=\"150\"></p>",
                    solution_hi: "<p>16.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607701974.png\" alt=\"rId30\" width=\"150\">&nbsp; &nbsp; &nbsp; &nbsp; ,&nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607702118.png\" alt=\"rId31\" width=\"150\"><br>इसी प्रकार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607702250.png\" alt=\"rId32\" width=\"150\"><br><br></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "6",
                    question_en: "<p>17. What should come in place of &lsquo;?&rsquo; in the given series?<br>5040, 6064, 6576, 6832, 6960, ?</p>",
                    question_hi: "<p>17. दी गई शृंखला में \'?\' के स्थान पर क्या आना चाहिए?<br>5040, 6064, 6576, 6832, 6960, ?</p>",
                    options_en: [
                        "<p>7472</p>",
                        "<p>7024</p>",
                        "<p>7088</p>",
                        "<p>7216</p>"
                    ],
                    options_hi: [
                        "<p>7472</p>",
                        "<p>7024</p>",
                        "<p>7088</p>",
                        "<p>7216</p>"
                    ],
                    solution_en: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607702362.png\" alt=\"rId33\" width=\"397\" height=\"97\"></p>",
                    solution_hi: "<p>17.(b)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607702362.png\" alt=\"rId33\" width=\"417\" height=\"102\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "6",
                    question_en: "<p>18. In a certain code language, &lsquo;I play football&rsquo; is coded as &lsquo;ku ch ta&rsquo; and &lsquo;he play cricket&rsquo; is coded as &lsquo;al bi ta&rsquo;. How is &lsquo;play&rsquo; coded in the given language?</p>",
                    question_hi: "<p>18. एक निश्चित कूट भाषा में, \'I play football\' को \'ku ch ta\' के रूप में कूटबद्ध किया जाता है और \'he play cricket\' को \'al bi ta\' के रूप में कूटबद्ध किया जाता है। दी गई भाषा में \'play\' को किस प्रकार कूटबद्ध किया गया है?</p>",
                    options_en: [
                        "<p>ku</p>",
                        "<p>al</p>",
                        "<p>bi</p>",
                        "<p>ta</p>"
                    ],
                    options_hi: [
                        "<p>ku</p>",
                        "<p>al</p>",
                        "<p>bi</p>",
                        "<p>ta</p>"
                    ],
                    solution_en: "<p>18.(d) I play football :- ku ch ta&hellip;..(i)<br>he play cricket :- al bi ta&hellip;&hellip;(ii)<br>From (i) and (ii) &lsquo;play&rsquo; and &lsquo;ta&rsquo; are common. The code of &lsquo;play&rsquo; = &lsquo;ta&rsquo;.</p>",
                    solution_hi: "<p>18.(d) I play football :- ku ch ta&hellip;..(i)<br>he play cricket :- al bi ta&hellip;&hellip;(ii)<br>(i) और (ii) से &lsquo;play&rsquo; और &lsquo;ta&rsquo; उभयनिष्ठ हैं। &lsquo;play&rsquo; का कोड = &lsquo;ta&rsquo; है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "6",
                    question_en: "<p>19. In a certain code language, if HOUSE is coded as 67 and GROUND is coded as 83, then how will KITCHEN be coded?</p>",
                    question_hi: "<p>19.एक निश्चित कूट भाषा में, यदि \'HOUSE\' को \'67\' लिखा जाता है तथा \'GROUND\' को \'83\' लिखा जाता है, तो उसी कूट भाषा में \'KITCHEN\' को कैसे लिखा जाएगा?</p>",
                    options_en: [
                        "<p>119</p>",
                        "<p>110</p>",
                        "<p>130</p>",
                        "<p>122</p>"
                    ],
                    options_hi: [
                        "<p>119</p>",
                        "<p>110</p>",
                        "<p>130</p>",
                        "<p>122</p>"
                    ],
                    solution_en: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607702538.png\" alt=\"rId34\" width=\"214\" height=\"125\">&nbsp; &nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; &nbsp; &nbsp; <img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607702670.png\" alt=\"rId35\" width=\"256\" height=\"127\"><br>Similarly,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607702825.png\" alt=\"rId36\" width=\"304\" height=\"126\"></p>",
                    solution_hi: "<p>19.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703002.png\" alt=\"rId37\" width=\"207\" height=\"122\">&nbsp; &nbsp; &nbsp;,&nbsp; &nbsp; &nbsp; &nbsp;<img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703187.png\" alt=\"rId38\" width=\"238\" height=\"119\"><br>इसी प्रकार<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703331.png\" alt=\"rId39\" width=\"310\" height=\"129\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "6",
                    question_en: "<p>20. Six Roman numbers I, II, III, IV, V and VI are written on different faces of a dice. Two positions of this dice are shown in the figure below. Find the Roman number on the face opposite to &lsquo;VI&rsquo;.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703458.png\" alt=\"rId40\" width=\"167\" height=\"100\"></p>",
                    question_hi: "<p>20. एक पासे के विभिन्न फलकों पर छह रोमन संख्याएँ I, II, III, IV, V और VI अंकित की गई हैं। नीचे चित्र में इस पासे की दो स्थितियों को दिखाया गया है। संख्या \'VI\' के विपरीत फलक पर आने वाली रोमन संख्या ज्ञात कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703458.png\" alt=\"rId40\" width=\"167\" height=\"100\"></p>",
                    options_en: [
                        "<p>V</p>",
                        "<p>II</p>",
                        "<p>III</p>",
                        "<p>IV</p>"
                    ],
                    options_hi: [
                        "<p>V</p>",
                        "<p>II</p>",
                        "<p>III</p>",
                        "<p>IV</p>"
                    ],
                    solution_en: "<p>20.(b) in both dice two roman numbers( III,V) are common.<br>So, the opposite face of &lsquo;VI&rsquo; is &lsquo;II&rsquo;.</p>",
                    solution_hi: "<p>20.(b) दोनों पासों में दो रोमन संख्याएँ (III,V) उभयनिष्ठ हैं।<br>अतः, \'VI\' का विपरीत फलक \'II\' है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "6",
                    question_en: "<p>21. Select the option that represents the correct order of the given words as they would appear in an English dictionary.<br>1. Discover<br>2. Disperse<br>3. Disjointed<br>4. Discard<br>5. Disposable<br>6. Discriminate</p>",
                    question_hi: "<p>21. उस विकल्प का चयन कीजिए जिसमें निम्न शब्दों का वह क्रम दिया गया है, जिस क्रम में वे अंग्रेजी शब्दकोश में आते है। <br>1. Discover<br>2. Disperse<br>3. Disjointed<br>4. Discard<br>5. Disposable<br>6. Discriminate</p>",
                    options_en: [
                        "<p>4, 1, 6, 2, 3, 5</p>",
                        "<p>4, 1, 6, 5, 3, 2</p>",
                        "<p>4, 6, 1, 3, 2, 5</p>",
                        "<p>4, 1, 6, 3, 2, 5</p>"
                    ],
                    options_hi: [
                        "<p>4, 1, 6, 2, 3, 5</p>",
                        "<p>4, 1, 6, 5, 3, 2</p>",
                        "<p>4, 6, 1, 3, 2, 5</p>",
                        "<p>4, 1, 6, 3, 2, 5</p>"
                    ],
                    solution_en: "<p>21.(d) The correct order is <br>Discard(4) &rarr; Discover(1) &rarr; Discriminate(6) &rarr; Disjointed(3) &rarr; Disperse(2) &rarr; Disposable(5)</p>",
                    solution_hi: "<p>21.(d) सही क्रम है<br>Discard(4) &rarr; Discover(1) &rarr; Discriminate(6) &rarr; Disjointed(3) &rarr; Disperse(2) &rarr; Disposable(5)</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "6",
                    question_en: "<p>22. The sequence of folding a paper and the manner in which the folded paper is cut is shown in the following figures. How would this paper look when unfolded?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703637.png\" alt=\"rId41\" width=\"240\" height=\"127\"></p>",
                    question_hi: "<p>22. निम्नलिखित आकृतियों में एक कागज़ को मोड़ने का क्रम और मुड़े हुए कागज़ को काटने का तरीका दर्शाया गया है। खोले जाने पर यह कागज़ कैसा दिखाई देगा ?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703637.png\" alt=\"rId41\" width=\"240\" height=\"127\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703744.png\" alt=\"rId42\" width=\"90\" height=\"138\"></p>",
                        "<p><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703861.png\" alt=\"rId43\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703962.png\" alt=\"rId44\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704059.png\" alt=\"rId45\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703744.png\" alt=\"rId42\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703861.png\" alt=\"rId43\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607703962.png\" alt=\"rId44\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704059.png\" alt=\"rId45\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>22.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704154.png\" alt=\"rId46\" width=\"90\"></p>",
                    solution_hi: "<p>22.(d) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704154.png\" alt=\"rId46\" width=\"90\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "6",
                    question_en: "<p>23. Select the correct mirror image of the given figure when the mirror is placed at MN as shown below.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704296.png\" alt=\"rId47\" width=\"118\" height=\"153\"></p>",
                    question_hi: "<p>23. जब दर्पण को नीचे दर्शाए गए अनुसार MN पर रखा जाता है तो दी गई आकृति के सही दर्पण प्रतिबिंब का चयन कीजिए।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704296.png\" alt=\"rId47\" width=\"118\" height=\"153\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704394.png\" alt=\"rId48\" width=\"120\" height=\"23\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704542.png\" alt=\"rId49\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704683.png\" alt=\"rId50\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704855.png\" alt=\"rId51\" width=\"120\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704394.png\" alt=\"rId48\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704542.png\" alt=\"rId49\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704683.png\" alt=\"rId50\" width=\"120\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704855.png\" alt=\"rId51\" width=\"120\"></p>"
                    ],
                    solution_en: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704855.png\" alt=\"rId51\" width=\"120\"></p>",
                    solution_hi: "<p>23.(d)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704855.png\" alt=\"rId51\" width=\"120\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "6",
                    question_en: "<p>24. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br><strong>(NOTE</strong> : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /subtracting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)<br>(21, 34, 76)<br>(39, 26, 104)</p>",
                    question_hi: "<p>24. उस समुच्य का चयन कीजिए जिसमें संख्याएं ठीक उसी प्रकार संबंधित हैं जिस प्रकार दिए गए समुच्यों की संख्याएं संबंधित हैं। <br><strong>(निर्देश</strong> :) संख्याओं को उनके घटक अंकों में अलग-अलग किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13- संख्या 13 पर संक्रियाएं जैसे 13 को जोड़ना/घटाना/गुणा करना आदि किया जा सकता है। 13 को 1 और 3 में अलग-अलग करने की और फिर 1 और 3 पर गणितीय संख्याएं करने की अनुमति नहीं है।)<br>(21, 34, 76)<br>(39, 26, 104)</p>",
                    options_en: [
                        "<p>(28, 23, 79)</p>",
                        "<p>(34, 25, 102)</p>",
                        "<p>(25, 31, 108)</p>",
                        "<p>(37, 43, 127)</p>"
                    ],
                    options_hi: [
                        "<p>(28, 23, 79)</p>",
                        "<p>(34, 25, 102)</p>",
                        "<p>(25, 31, 108)</p>",
                        "<p>(37, 43, 127)</p>"
                    ],
                    solution_en: "<p>24.(a) <strong>Logic</strong> :- (3rd number - 2nd number) &divide;&nbsp;2 = 1st number<br>(21 ,34, 76) :- (76 - 34) &divide; 2 &rArr; (42) &divide; 2 = 21<br>(39, 26, 104) :- (104 - 26) &divide; 2 &rArr; (78) &divide; 2 = 39<br>Similarly,<br>(28, 23, 79) :- (79 -23) &divide; 2 &rArr; (56) &divide; 2 = 28</p>",
                    solution_hi: "<p>24.(a) <strong>तर्क</strong> :- (तीसरी संख्या - दूसरी संख्या) &divide; 2 = पहली संख्या<br>(21 ,34, 76) :- (76 - 34) &divide; 2 &rArr; (42) &divide; 2 = 21<br>(39, 26, 104) :- (104 - 26) &divide; 2 &rArr; (78) &divide; 2 = 39<br>इसी प्रकार,<br>(28, 23, 79) :- (79 -23) &divide; 2 &rArr; (56) &divide; 2 = 28</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "18",
                    question_en: "<p>25. Which figure should replace the question mark (?) if the following figure series were to be continued?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704959.png\" alt=\"rId52\" width=\"400\" height=\"91\"></p>",
                    question_hi: "<p>25. यदि निम्नलिखित आकृति श्रृंखला को जारी रखना हो तो कौन-सी आकृति प्रश्न चिन्ह (?) के स्थान पर आनी चाहिए?<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607704959.png\" alt=\"rId52\" width=\"400\" height=\"91\"></p>",
                    options_en: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705059.png\" alt=\"rId53\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705220.png\" alt=\"rId54\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705317.png\" alt=\"rId55\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705419.png\" alt=\"rId56\" width=\"90\"></p>"
                    ],
                    options_hi: [
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705059.png\" alt=\"rId53\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705220.png\" alt=\"rId54\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705317.png\" alt=\"rId55\" width=\"90\"></p>",
                        "<p><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705419.png\" alt=\"rId56\" width=\"90\"></p>"
                    ],
                    solution_en: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705059.png\" alt=\"rId53\" width=\"82\" height=\"77\"></p>",
                    solution_hi: "<p>25.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705059.png\" alt=\"rId53\" width=\"82\" height=\"77\"></p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "26",
                    section: "18",
                    question_en: "<p>26. Which mobile app was launched in September 2024 at the Indian Council of Agricultural Research-Central Institute of Freshwater Aquaculture (ICAR - CIFA) to address the needs of the ornamental fisheries sector?</p>",
                    question_hi: "<p>26. सजावटी मत्स्य पालन क्षेत्र की जरूरतों को पूरा करने के लिए भारतीय कृषि अनुसंधान परिषद-केंद्रीय मीठे पानी की जलीय कृषि संस्थान (ICAR - CIFA) में सितंबर 2024 में कौन सा मोबाइल ऐप लॉन्च किया गया था ?</p>",
                    options_en: [
                        "<p>Saa₹thi (SAARTHI) 2.0</p>",
                        "<p>NERACE</p>",
                        "<p>Lokpath</p>",
                        "<p>Rangeen Machhli</p>"
                    ],
                    options_hi: [
                        "<p>साथी (SAARTHI) 2.0</p>",
                        "<p>NERACE</p>",
                        "<p>लोकपथ</p>",
                        "<p>रंगीन मछली</p>"
                    ],
                    solution_en: "<p>26.(d) <strong>Rangeen Machhli.</strong> On 12th September 2024, Union Minister Rajiv Ranjan Singh alias Lalan Singh, launched the Rangeen Machhli mobile app at Indian Council of Agricultural Research-Central Institute of Freshwater Aquaculture (ICAR-CIFA), Bhubaneswar, Odisha.</p>",
                    solution_hi: "<p>26.(d) <strong>रंगीन मछली।</strong> 12 सितंबर 2024 को केंद्रीय मंत्री राजीव रंजन सिंह उर्फ ​​ललन सिंह ने भारतीय कृषि अनुसंधान परिषद-केंद्रीय मीठे पानी की जलीय कृषि संस्थान (ICAR-CIFA), भुवनेश्वर, ओडिशा में रंगीन मछली मोबाइल ऐप लॉन्च किया।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "27",
                    section: "18",
                    question_en: "27. Jaita is the main dance form of which state?",
                    question_hi: "27. जैता किस राज्य का एक प्रमुख नृत्य रूप है?",
                    options_en: [
                        " Karnataka ",
                        " Rajasthan",
                        " Madhya Pradesh ",
                        " Uttar Pradesh"
                    ],
                    options_hi: [
                        " कर्नाटक",
                        " राजस्थान",
                        " मध्य प्रदेश",
                        " उत्तर प्रदेश"
                    ],
                    solution_en: "<p>27.(d) <strong>Uttar Pradesh.</strong> Jaita is a traditional folk dance performed by the Jat community, especially in the Bundelkhand region, during weddings and festivals. Other folk dances of Uttar Pradesh include: Nautanki, Raslila, Kajri. State with their folk dances: Rajasthan - Ghoomar, Suisini, kalbeliya, Chakri, Ganagor, Jhulan Leela. Madhya Pradesh - Phulpati, Jawara, Selalarki, Selabhadoni, Matki. Karnataka - Yakshagana, Lambi, Huttari, Kunitha, Suggi, Karga.</p>",
                    solution_hi: "<p>27.(d) <strong>उत्तर</strong> प्रदेश। जैता एक पारंपरिक लोक नृत्य है जो जाट समुदाय द्वारा, विशेष रूप से बुन्देलखण्ड क्षेत्र में, शादियों और त्योहारों के दौरान किया जाता है। उत्तर प्रदेश के अन्य लोक नृत्यों में शामिल हैं: नौटंकी, रासलीला, कजरी। राज्य एवं संबंधित लोकनृत्य: राजस्थान - घूमर, सुइसिनी, कालबेलिया, चकरी, गणगौर, झूलन लीला। मध्य प्रदेश - फूलपाती, जावरा, सेलालार्की, सेलाभदोनी, मटकी। कर्नाटक - यक्षगान, लाम्बी, हुट्टारी, कुनिथा, सुग्गी, करगा।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "28",
                    section: "18",
                    question_en: "<p>28. Who was the designer of India&rsquo;s national flag?</p>",
                    question_hi: "<p>28. भारत के राष्ट्रीय ध्वज का डिजाइनर कौन था?</p>",
                    options_en: [
                        "<p>BN Rau</p>",
                        "<p>KM Munshi</p>",
                        "<p>Pingali Venkayya</p>",
                        "<p>Prem Behari Narain Raizada</p>"
                    ],
                    options_hi: [
                        "<p>बीएन राऊ</p>",
                        "<p>केएम मुंशी</p>",
                        "<p>पिंगली वेंकैया</p>",
                        "<p>प्रेमबिहारी नारायण रायजादा</p>"
                    ],
                    solution_en: "<p>28.(c) <strong>Pingali</strong> <strong>Venkayya</strong>: He presented the original design of the Indian flag with red and green colors to Mahatma Gandhi in 1921. The National Flag was adopted by the Constituent Assembly of India on 22 July 1947. On August 22, 1907, Madam Bhikaji Cama became the first person to hoist the Indian flag on foreign soil in Stuttgart in Germany. B. N. Rau : Appointed Constitutional Adviser in 1946. K. M. Munshi : Described the Preamble as the \"political horoscope\" of the Constitution. He was also the first Chairman of the Order of Business Committee. Prem Behari Narain Raizada (Saxena): Hand-wrote the original Indian Constitution.</p>",
                    solution_hi: "<p>28.(c) <strong>पिंगली वेंकैया:</strong> उन्होंने 1921 में महात्मा गांधी को लाल और हरे रंग के साथ भारतीय ध्वज का मूल डिज़ाइन प्रस्तुत किया। 22 जुलाई 1947 को भारत की संविधान सभा द्वारा राष्ट्रीय ध्वज को अपनाया गया था। 22 अगस्त, 1907 को मैडम भीकाजी कामा जर्मनी के स्टटगार्ट में विदेशी धरती पर भारतीय ध्वज फहराने वाली प्रथम भारतीय थी। बी. एन. राव : 1946 में संवैधानिक सलाहकार नियुक्त किए गए। के. एम. मुंशी : प्रस्तावना को संविधान की \"राजनीतिक कुंडली\" के रूप में वर्णित किया। वे ऑर्डर ऑफ़ बिज़नेस कमेटी के प्रथम अध्यक्ष भी थे। प्रेम बिहारी नारायण रायज़ादा (सक्सेना): मूल भारतीय संविधान को हाथ से लिखा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "29",
                    section: "18",
                    question_en: "<p>29. What is the defense allocation in the Union Budget FY2025-26?</p>",
                    question_hi: "<p>29. संघीय बजट FY2025-26 में रक्षा आवंटन कितना है?</p>",
                    options_en: [
                        "<p>₹5.5 lakh crore</p>",
                        "<p>₹6.8 lakh crore</p>",
                        "<p>₹7.2 lakh crore</p>",
                        "<p>₹6.2 lakh crore</p>"
                    ],
                    options_hi: [
                        "<p>₹5.5 लाख करोड़</p>",
                        "<p>₹6.8 लाख करोड़</p>",
                        "<p>₹7.2 लाख करोड़</p>",
                        "<p>₹6.2 लाख करोड़</p>"
                    ],
                    solution_en: "<p>29.(b) <strong>₹6.8 lakh crore.</strong> The budget emphasizes modernization, domestic procurement, and operational readiness, with ₹1.8 lakh crore allocated for new weapons and equipment.</p>",
                    solution_hi: "<p>29.(b) <strong>₹6.8 लाख करोड़।</strong> यह बजट आधुनिकीकरण, स्वदेशी खरीद और संचालन तत्परता को प्राथमिकता देता है, जिसमें ₹1.8 लाख करोड़ नए हथियारों और उपकरणों की खरीद के लिए आवंटित किए गए हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "30",
                    section: "18",
                    question_en: "30. ________ is the science and art of cultivating fruits, vegetables, flowers and ornamental plants.",
                    question_hi: "<p>30. _________ फलों, सब्जियों, फूलों और सजावटी पौधों की खेती का विज्ञान और कला है।</p>",
                    options_en: [
                        " Viniculture",
                        " Floriculture",
                        " Sericulture ",
                        " Horticulture"
                    ],
                    options_hi: [
                        " विनीकल्चर",
                        " फ्लोरीकल्चर",
                        " सेरीकल्चर",
                        " हार्टीकल्चर"
                    ],
                    solution_en: "<p>30.(d) <strong>Horticulture.</strong> Olericulture : Branch of horticulture that deals with the production, processing, and marketing of vegetables. Viniculture: The cultivation of grapes, specifically for wine production. Floriculture includes cultivation and production of all types of ornamentals, viz., croton, cacti, orchids, grasses and bamboos. Sericulture: The cultivation of silkworms for silk production.</p>",
                    solution_hi: "<p>30.(d) <strong>हार्टीकल्चर।</strong> ओलेरीकल्चर: बागवानी की वह शाखा जो सब्ज़ियों के उत्पादन, प्रसंस्करण और विपणन से संबंधित है। विनीकल्चर: अंगूर की खेती, विशेष रूप से वाइन उत्पादन के लिए। फ्लोरीकल्चर में सभी प्रकार के सजावटी पौधों, जैसे क्रोटन, कैक्टि, ऑर्किड, घास और बांस की खेती और उत्पादन शामिल है। सेरीकल्चर: रेशम उत्पादन के लिए रेशम के कीटों की खेती।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "31",
                    section: "18",
                    question_en: "<p>31. Which Article of the Indian Constitution mentions that any person to become the President of India has to be a citizen of India?</p>",
                    question_hi: "<p>31. भारतीय संविधान के किस अनुच्छेद में उल्लेख है कि भारत का राष्ट्रपति बनने के लिए किसी भी व्यक्ति को भारत का नागरिक होना आवश्यक है?</p>",
                    options_en: [
                        "<p>Article 58</p>",
                        "<p>Article 57</p>",
                        "<p>Article 60</p>",
                        "<p>Article 59</p>"
                    ],
                    options_hi: [
                        "<p>अनुच्छेद 58</p>",
                        "<p>अनुच्छेद 57</p>",
                        "<p>अनुच्छेद 60</p>",
                        "<p>अनुच्छेद 59</p>"
                    ],
                    solution_en: "<p>31.(a) <strong>Article 58</strong> of the Indian Constitution states the qualifications for a person to be eligible for election as President of India, which includes being a citizen of India. Other articles: Article 57 - Eligibility for re-election. Article 59 - Conditions of the President\'s office. Article 60 - Oath or affirmation by the President.</p>",
                    solution_hi: "<p>31.(a) <strong>अनुच्छेद 58</strong> भारतीय संविधान में भारत के राष्ट्रपति के रूप में निर्वाचित होने के लिए किसी व्यक्ति की योग्यताओं का उल्लेख है, जिसमें भारत का नागरिक होना भी शामिल है। अन्य अनुच्छेद: अनुच्छेद 57 - पुनः निर्वाचन के लिए पात्रता। अनुच्छेद 59 - राष्ट्रपति के पद के लिए शर्तें। अनुच्छेद 60 - राष्ट्रपति द्वारा शपथ या प्रतिज्ञान।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "32",
                    section: "18",
                    question_en: "<p>32. Which of the following items has a high fibre content?</p>",
                    question_hi: "<p>32. निम्नलिखित में से किस वस्तु में फाइबर की उच्च मात्रा पाई जाती है?</p>",
                    options_en: [
                        "<p>Eggs</p>",
                        "<p>Fish</p>",
                        "<p>Whole grain</p>",
                        "<p>Milk</p>"
                    ],
                    options_hi: [
                        "<p>अंडा</p>",
                        "<p>मछली</p>",
                        "<p>साबुत अनाज</p>",
                        "<p>दूध</p>"
                    ],
                    solution_en: "<p>32.(c) <strong>Whole grains</strong> are high in dietary fiber, which is beneficial for digestive health. They also provide various vitamins and minerals. Other foods and their minerals: Eggs: Protein-rich, but fiber-free. Fish: Rich in protein and omega-3 fatty acids. Milk: Rich in calcium and protein.</p>",
                    solution_hi: "<p>32.(c) <strong>साबुत अनाज</strong> में आहार फाइबर की मात्रा अधिक होती है, जो पाचन स्वास्थ्य के लिए फायदेमंद है। वे विभिन्न विटामिन और खनिज भी प्रदान करते हैं। अन्य खाद्य पदार्थ एवं उनके खनिज: अंडे - प्रोटीन से भरपूर, लेकिन फाइबर रहित। मछली: प्रोटीन और ओमेगा-3 फैटी अम्ल से भरपूर। दूध: कैल्शियम और प्रोटीन की प्रचूर मात्रा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "33",
                    section: "18",
                    question_en: "<p>33. Select the correct statement about climax community.</p>",
                    question_hi: "<p>33. चरमोत्कर्ष समुदाय के बारे में सही कथन का चयन कीजिए।</p>",
                    options_en: [
                        "<p>It is an ecological community in which populations of plants or animals, which are very unstable and exist for very few times.</p>",
                        "<p>It is the very first community of the ecosystem.</p>",
                        "<p>It consists of only plant population that makes a new ecological system.</p>",
                        "<p>It is an ecological community in which populations of plants or animals remain stable and exist in balance with each other and their environment.</p>"
                    ],
                    options_hi: [
                        "<p>यह एक पारिस्थितिक समुदाय है, जिसमें पौधों या जानवरों की संख्या, जो बहुत अस्थिर हैं और बहुत कम समय के लिए मौजूद होती हैं।</p>",
                        "<p>यह पारिस्थितिकी तंत्र का एक बहुत ही पहला समुदाय है।</p>",
                        "<p>इसमें केवल पौधों की संख्या शामिल होती है, जो एक नई पारिस्थितिक प्रणाली बनाती है।</p>",
                        "<p>यह एक पारिस्थितिक समुदाय है, जिसमें पौधों या जानवरों की संख्या स्थिर रहती है और एक-दूसरे और उनके पर्यावरण के साथ संतुलन में रहती है।</p>"
                    ],
                    solution_en: "<p>33.(d) A climax community is the &ldquo;endpoint&rdquo; of succession within the context of a particular climate and geography. The climax community remains stable as long as the environment remains unchanged.</p>",
                    solution_hi: "<p>33.(d) चरमोत्कर्ष समुदाय किसी विशेष जलवायु और भूगोल के संदर्भ में अनुक्रमण का \"अंतिम बिंदु\" होता है। चरमोत्कर्ष समुदाय तब तक स्थिर रहता है जब तक पर्यावरण अपरिवर्तित रहता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "34",
                    section: "18",
                    question_en: "<p>34. Geologically, which of the following physiographic divisions of India is supposed to be one of the most stable land blocks?</p>",
                    question_hi: "<p>34. भूगर्भीय रूप से, भारत का निम्नलिखित में से कौन सा भौगोलिक विभाजन सबसे स्थिर भूमि ब्लॉकों में से एक माना जाता है?</p>",
                    options_en: [
                        "<p>The Northern Plains</p>",
                        "<p>The Himalayas</p>",
                        "<p>The Peninsular Plateau</p>",
                        "<p>The Indian Desert</p>"
                    ],
                    options_hi: [
                        "<p>उत्तरी मैदान</p>",
                        "<p>हिमालय</p>",
                        "<p>प्रायद्वीपीय पठार</p>",
                        "<p>भारतीय रेगिस्तान</p>"
                    ],
                    solution_en: "<p>34.(c) <strong>The Peninsular plateau</strong> is a tableland composed of the old crystalline, igneous and metamorphic rocks. This plateau consists of two broad divisions, namely, the Central Highlands and the Deccan Plateau. The part of the Peninsular plateau lying to the north of the Narmada river. The Indian desert lies towards the western margins of the Aravali Hills. It is an undulating sandy plain covered with sand dunes. The northern plain is formed of alluvial soil. The Himalayas, geologically young and structurally fold mountains stretch over the northern borders of India.</p>",
                    solution_hi: "<p>34.(c) <strong>प्रायद्वीपीय पठार</strong> एक पठार है जो पुराने क्रिस्टलीय, आग्नेय और रूपांतरित चट्टानों से मिलकर बना है। इस पठार में दो व्यापक विभाजन हैं, अर्थात्, मध्य हाइलैंड्स और दक्कन पठार। प्रायद्वीपीय पठार का वह भाग जो नर्मदा नदी के उत्तर में स्थित है। भारतीय रेगिस्तान अरावली पहाड़ियों के पश्चिमी छोर की ओर स्थित है। यह रेत के टीलों से ढका एक तरंगित रेतीला मैदान है। उत्तरी मैदान जलोढ़ मिट्टी से बना है। भूवैज्ञानिक दृष्टि से तरुण और संरचनात्मक दृष्टि से वलित पर्वत हिमालय भारत की उत्तरी सीमा तक फैला हुआ है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "35",
                    section: "18",
                    question_en: "<p>35. Which of the following is NOT among the commercial sources of energy?</p>",
                    question_hi: "<p>35. निम्नलिखित में से कौन-सा विकल्प ऊर्जा के वाणिज्यिक स्रोतों में से एक नहीं है?</p>",
                    options_en: [
                        "<p>Petroleum</p>",
                        "<p>Electricity</p>",
                        "<p>Fuel wood</p>",
                        "<p>Hydropower</p>"
                    ],
                    options_hi: [
                        "<p>पेट्रोलियम</p>",
                        "<p>बिजली</p>",
                        "<p>ईंधन की लकड़ी</p>",
                        "<p>जलविद्युत</p>"
                    ],
                    solution_en: "<p>35.(c) <strong>Fuel wood.</strong> It is a non-commercial or traditional source of energy, primarily used for domestic purposes such as cooking and heating. Commercial sources of energy are typically: Fossil fuels (petroleum, coal, natural gas), Electricity (produced from a variety of energy sources), Hydropower (electricity generated from water resources).</p>",
                    solution_hi: "<p>35.(c) <strong>ईंधन</strong> <strong>की लकड़ी</strong>। यह ऊर्जा का एक गैर-वाणिज्यिक या पारंपरिक स्रोत है, जिसका उपयोग मुख्य रूप से खाना पकाने और गर्म करने जैसे घरेलू उद्देश्यों के लिए किया जाता है। ऊर्जा के वाणिज्यिक स्रोत सामान्यतः हैं - जीवाश्म ईंधन (पेट्रोलियम, कोयला, प्राकृतिक गैस), बिजली (विभिन्न ऊर्जा स्रोतों से उत्पादित), जलविद्युत (जल संसाधनों से उत्पन्न बिजली) आदि।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "36",
                    section: "18",
                    question_en: "<p>36. Atmospheric temperature does NOT depend on _________.</p>",
                    question_hi: "<p>36. तापमान ___________पर निर्भर नहीं करता है।</p>",
                    options_en: [
                        "<p>salinity of oceans</p>",
                        "<p>distance from sea</p>",
                        "<p>latitude</p>",
                        "<p>altitude</p>"
                    ],
                    options_hi: [
                        "<p>महासागरों की लवणता</p>",
                        "<p>समुद्र से दूरी</p>",
                        "<p>अक्षांश</p>",
                        "<p>उन्&zwj;नतांश</p>"
                    ],
                    solution_en: "<p>36.(a) <strong>salinity of oceans</strong>. Atmospheric temperature primarily depends on several factors: latitude, which influences the variation in solar radiation due to the Earth\'s tilt; altitude, where temperature decreases with increasing elevation; and distance from the sea, which creates differences between coastal and continental temperatures.</p>",
                    solution_hi: "<p>36.(a) <strong>महासागरों की लवणता।</strong> वायुमंडलीय तापमान मुख्य रूप से कई कारकों पर निर्भर करता है: अक्षांश, जो पृथ्वी के झुकाव के कारण सौर विकिरण में भिन्नता को प्रभावित करता है; ऊँचाई, जहाँ ऊँचाई बढ़ने के साथ तापमान घटता है; और समुद्र से दूरी, जो तटीय और महाद्वीपीय तापमान के बीच अंतर उत्पन्न करती है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "37",
                    section: "18",
                    question_en: "<p>37. Who among the following was honored with the title of Khan-e-Khana during the reign of Akbar?</p>",
                    question_hi: "<p>37. निम्नलिखित में से किसे अकबर के शासनकाल में खान-ए-खाना की उपाधि से सम्मानित किया गया था?</p>",
                    options_en: [
                        "<p>Baz Bahadur</p>",
                        "<p>Abul Fazal</p>",
                        "<p>Todarmal</p>",
                        "<p>Bairam Khan</p>"
                    ],
                    options_hi: [
                        "<p>बाज़ बहादुर</p>",
                        "<p>अबुल फज़ल</p>",
                        "<p>टोडरमल</p>",
                        "<p>बैरम खाँ</p>"
                    ],
                    solution_en: "<p>37.(d) <strong>Bairam Khan.</strong> He was a renowned military commander and nobleman who served as Akbar\'s regent and advisor. Akbar was the third Mughal emperor, reigning from 1556 to 1605. His court was known for the \"nine gems\" or Navratnas, which included Birbal, Tansen, Abul Fazl, Faizi, Todar Mal, Raja Man Singh, Abdul Rahim Khan-I-Khana, Fakir Aziao-Din, and Mullah Do Piaza.</p>",
                    solution_hi: "<p>37.(d) <strong>बैरम खाँ। </strong>वह एक प्रसिद्ध सैन्य कमांडर और कुलीन व्यक्ति थे, जिन्होंने अकबर के संरक्षक और सलाहकार के रूप में कार्य किया था। अकबर तीसरे मुगल सम्राट थे, जिन्होंने 1556 से 1605 तक शासन किया था। उनका दरबार \"नौ रत्नों\" या नवरत्नों के लिए जाना जाता था, जिसमें बीरबल, तानसेन, अबुल फ़ज़ल, फ़ैज़ी, टोडर मल, राजा मान सिंह, अब्दुल रहीम खान-ए-खाना, फ़कीर अज़ियाओ-दीन और मुल्ला दो पियाज़ा शामिल थे।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "38",
                    section: "18",
                    question_en: "<p>38. Rani Karnaa was awarded the Sangeet Natak Akademi (1996) for her contribution to _____ .</p>",
                    question_hi: "<p>38. रानी कर्णा को _________ में उनके योग दान के लिए संगीत नाटक अकादमी (1996) से सम्मानित किया गया था।</p>",
                    options_en: [
                        "<p>Odissi</p>",
                        "<p>Kathak</p>",
                        "<p>Kathakali</p>",
                        "<p>Kuchipudi</p>"
                    ],
                    options_hi: [
                        "<p>ओडिसी</p>",
                        "<p>कथक</p>",
                        "<p>कथकली</p>",
                        "<p>कुचिपुड़ी</p>"
                    ],
                    solution_en: "<p>38.(b) <strong>Kathak.</strong> Rani Karnaa also received the Padma Shri Award in 2014. She was trained by Pandit Narayan Prasad and Sunder Prasad of the Jaipur gharana and Pandit Shambhu Maharaj and Birju Maharaj of Lucknow gharanas. Some renowned Kathak dancers include Pandit Birju Maharaj, Lachu Maharaj, and Shovana Narayan.</p>",
                    solution_hi: "<p>38.(b) <strong>कथक।</strong> रानी कर्णा को 2014 में पद्म श्री पुरस्कार से सम्मानित किया गया था। उन्हें जयपुर घराने के पंडित नारायण प्रसाद और सुंदर प्रसाद तथा लखनऊ घराने के पंडित शंभू महाराज और बिरजू महाराज द्वारा प्रशिक्षित किया गया था। कुछ प्रसिद्ध कथक नर्तकों में पंडित बिरजू महाराज, लच्छू महाराज और शोवना नारायण शामिल हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "39",
                    section: "18",
                    question_en: "<p>39. Koderma Gaya Hazaribagh belt is known for the production of which of the following minerals?</p>",
                    question_hi: "<p>39. कोडरमा गया हज़ारीबाग बेल्ट निम्नलिखित में से किस खनिज के उत्पादन के लिए जाना जाता है?</p>",
                    options_en: [
                        "<p>Bauxite</p>",
                        "<p>Mica</p>",
                        "<p>Coal</p>",
                        "<p>Copper</p>"
                    ],
                    options_hi: [
                        "<p>बॉक्साइट</p>",
                        "<p>अभ्रक</p>",
                        "<p>कोयला</p>",
                        "<p>तांबा</p>"
                    ],
                    solution_en: "<p>39.(b) <strong>Mica</strong> deposits are found on the northern edge of the Chota Nagpur Plateau, with the Koderma-Gaya-Hazaribagh belt in Jharkhand leading production. Major mica-producing regions include Gaya, Hazaribagh, Ajmer, Beawar, Bhilwara, Udaipur, Dungarpur, Banswara, and Nellore. The main mica-producing states are Jharkhand, Rajasthan, and Andhra Pradesh.</p>",
                    solution_hi: "<p>39.(b) <strong>अभ्रक</strong> के भंडार छोटा नागपुर पठार के उत्तरी किनारे पर पाए जाते हैं, जिसमें झारखंड में कोडरमा-गया-हजारीबाग क्षेत्र सबसे ज़्यादा उत्पादन करता है। प्रमुख अभ्रक उत्पादक क्षेत्रों में गया, हजारीबाग, अजमेर, ब्यावर, भीलवाड़ा, उदयपुर, डूंगरपुर, बांसवाड़ा और नेल्लोर शामिल हैं। झारखंड, राजस्थान और आंध्र प्रदेश मुख्य अभ्रक उत्पादक राज्य हैं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "40",
                    section: "18",
                    question_en: "<p>40. Who is credited with inventing the reflecting telescope?</p>",
                    question_hi: "<p>40. परावर्तक दूरबीन के आविष्कार का श्रेय किसे दिया जाता है?</p>",
                    options_en: [
                        "<p>Isaac Newton</p>",
                        "<p>Johannes Kepler</p>",
                        "<p>Christiaan Huygens</p>",
                        "<p>Galileo Galilei</p>"
                    ],
                    options_hi: [
                        "<p>आइजैक न्यूटन</p>",
                        "<p>जोहान्स केप्लर</p>",
                        "<p>क्रिश्चियन हाइगेन्स</p>",
                        "<p>गैलीलियो गैलीली</p>"
                    ],
                    solution_en: "<p>40.(a) <strong>Isaac Newton.</strong> In 1668, he devised a reflecting telescope. Instead of a lens, it used a single curved main mirror, together with a smaller flat mirror. Scientists and Discoveries: Johannes Kepler - The three laws of planetary motion; Christiaan Huygens - The Wave theory of light.</p>",
                    solution_hi: "<p>40.(a) <strong>आइजैक न्यूटन।</strong> 1668 में, उन्होंने एक परावर्तक दूरबीन का आविष्कार किया था। एक लेंस के जगह, इसमें एक छोटे से समतल दर्पण के साथ एकल गोलिये मुख्य दर्पण का उपयोग किया गया था। वैज्ञानिक और आविष्कार: योहानेस केप्लर - ग्रहीय गति के तीन नियम; क्रिश्चियन हाइगेंस - प्रकाश का तरंग सिद्धांत।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "41",
                    section: "18",
                    question_en: "<p>41. What is the growth rate of the population of India as per census 2011?</p>",
                    question_hi: "<p>41. 2011 की जनगणना के अनुसार, भारत की जनसंख्या वृद्धि दर कितनी है ?</p>",
                    options_en: [
                        "<p>0.2164</p>",
                        "<p>0.1894</p>",
                        "<p>0.1764</p>",
                        "<p>0.2154</p>"
                    ],
                    options_hi: [
                        "<p>0.2164</p>",
                        "<p>0.1894</p>",
                        "<p>0.1764</p>",
                        "<p>0.2154</p>"
                    ],
                    solution_en: "<p>41.(c) <strong>0.1764</strong>. Population growth refers to the change in the number of people in a specific area over time, expressed as a percentage. The first census was initiated by British Viceroy Lord Mayo in 1872. The first synchronized census occurred in 1881. The 2011 Census, the 15th overall and 7th since Independence, had the slogan &ldquo;Our Census, Our Future.&rdquo;</p>",
                    solution_hi: "<p>41.(c) <strong>0.1764. </strong>जनसंख्या वृद्धि का अर्थ है समय के साथ किसी खास क्षेत्र में लोगों की संख्या में होने वाला परिवर्तन, जिसे प्रतिशत के रूप में व्यक्त किया जाता है। प्रथम जनगणना की शुरुआत 1872 ईस्वी में ब्रिटिश वायसराय लॉर्ड मेयो ने की थी। प्रथम समकालिक जनगणना 1881 में हुई थी। 2011 की जनगणना, जो कुल मिलाकर 15वीं तथा स्वतंत्रता के बाद 7वीं थी, का नारा था &ldquo;हमारी जनगणना, हमारा भविष्य।&rdquo;</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "42",
                    section: "18",
                    question_en: "<p>42. Which of the following was discovered by GN Ramachandran?</p>",
                    question_hi: "<p>42. निम्नलिखित में से किसकी खोज जी. एन. रामचंद्रन ने की थी?</p>",
                    options_en: [
                        "<p>Fluid mosaic model of a cell</p>",
                        "<p>Plasma membrane</p>",
                        "<p>Golgi bodies</p>",
                        "<p>Triple helical structure of collagen</p>"
                    ],
                    options_hi: [
                        "<p>कोशिका का तरल मोज़ैक मॉडल</p>",
                        "<p>जीवद्रव्यझिल्ली</p>",
                        "<p>गॉल्जीकाय</p>",
                        "<p>कोलेजन की तिहरी कुंडलित संरचना</p>"
                    ],
                    solution_en: "<p>42.(d) <strong>Triple helical structure of collagen.</strong> G.N. Ramachandran, an Indian physicist, is renowned for his work that resulted in the creation of the Ramachandran plot, which aids in understanding peptide structure. The collagen triple helix, a right-handed super-coiled structure, consists of three parallel &alpha;-chains, each adopting a polyproline II helical conformation.</p>",
                    solution_hi: "<p>42.(d) <strong>कोलेजन की तिहरी कुंडलित संरचना ।</strong> जी.एन. रामचंद्रन, एक भारतीय भौतिक वैज्ञानिक है, जो अपने कार्य के लिए प्रसिद्ध हैं जिसके परिणामस्वरूप रामचंद्रन प्लॉट का निर्माण हुआ, जो पेप्टाइड संरचना को समझने में सहायता करता है। कोलेजन की तिहरी कुंडलित, एक दाएं हाथ की सुपर-कॉइल संरचना, तीन समानांतर &alpha;-चेन से मिलकर बनी है, जिनमें से प्रत्येक पॉलीप्रोलाइन II कुंडलित संरचना को अपनाती है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "43",
                    section: "18",
                    question_en: "<p>43. The Vienna Convention is related to ____________.</p>",
                    question_hi: "<p>43. विएना सम्मेलन ___________ से संबंधित है।</p>",
                    options_en: [
                        "<p>protection of wild life</p>",
                        "<p>protection of ozone layer</p>",
                        "<p>Disposing of harmful electronic waste</p>",
                        "<p>sustainable agriculture</p>"
                    ],
                    options_hi: [
                        "<p>वन्य जीवों के संरक्षण</p>",
                        "<p>ओजोन परत के संरक्षण</p>",
                        "<p>हानिकारक इलेक्ट्रॉनिक अपशिष्ट के निपटान</p>",
                        "<p>स्थायी कृषि</p>"
                    ],
                    solution_en: "<p>43.(b) <strong>protection of ozone layer</strong>. The Vienna Convention multilateral environmental agreement was signed in 1985, came into effect in 1988, and achieved universal ratification by 2009. The primary goal of the Vienna Convention is to safeguard human health and the environment from the harmful effects caused by ozone layer depletion.</p>",
                    solution_hi: "<p>43.(b) <strong>ओजोन परत के संरक्षण।</strong> विएना सम्मेलन बहुपक्षीय पर्यावरण समझौते पर 1985 में हस्ताक्षर किए गए थे, जो 1988 में लागू हुआ और 2009 तक सार्वभौमिक अनुसमर्थन प्राप्त हुआ। विएना सम्मेलन का प्राथमिक लक्ष्य ओजोन परत क्षरण के कारण होने वाले हानिकारक प्रभावों से मानव स्वास्थ्य और पर्यावरण की रक्षा करना है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "44",
                    section: "18",
                    question_en: "<p>44. Which of the following is NOT considered a method of conservation of natural resources?</p>",
                    question_hi: "<p>44. निम्नलिखित में से किसे प्राकृतिक संसाधनों के संरक्षण का तरीका नहीं माना जाता है?</p>",
                    options_en: [
                        "<p>Extraction</p>",
                        "<p>Afforestation</p>",
                        "<p>Recycling</p>",
                        "<p>Terrace farming</p>"
                    ],
                    options_hi: [
                        "<p>निष्कर्षण</p>",
                        "<p>वनीकरण</p>",
                        "<p>पुनर्चक्रण</p>",
                        "<p>टेरेस फार्मिंग</p>"
                    ],
                    solution_en: "<p>44.(a) <strong>Extraction</strong> - A separation method that transfers a compound from one phase or solvent to another to isolate it from impurities or unreacted starting materials. Method of conservation of Natural Resources : Reforestation, Rainwater Harvesting, Treatment of Industrial Effluents, In-Situ conservation of Biodiversity, Ex-Situ conservation of Biodiversity, Sustainable development, Crop rotation, Contour ploughing, etc.</p>",
                    solution_hi: "<p>44.(a) <strong>निष्कर्षण</strong> - एक पृथक्करण विधि जो किसी यौगिक को एक चरण या विलायक से दूसरे चरण में स्थानांतरित करती है ताकि उसे अशुद्धियों या अप्रतिक्रियाशील प्रारंभिक पदार्थों से अलग किया जा सके। प्राकृतिक संसाधनों के संरक्षण की विधि: वनरोपण, वर्षा जल संचयन, औद्योगिक अपशिष्टों का उपचार, जैव विविधता का इन-सीटू संरक्षण, जैव विविधता का एक्स-सीटू संरक्षण, सतत विकास, फसल चक्रण, समोच्च जुताई, आदि है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "45",
                    section: "18",
                    question_en: "<p>45. Select the INCORRECT combination of dance and its respective state of origin.</p>",
                    question_hi: "<p>45. नृत्य और उसके उत्पत्ति के संबंधित राज्य के गलत संयोजन का चयन कीजिए।</p>",
                    options_en: [
                        "<p>Sattriya &ndash; Himachal Pradesh</p>",
                        "<p>Bharatanatyam &ndash; Tamil Nadu</p>",
                        "<p>Kuchipudi &ndash; Andhra Pradesh</p>",
                        "<p>Kathakali &ndash; Kerala</p>"
                    ],
                    options_hi: [
                        "<p>सत्त्रिया - हिमाचल प्रदेश</p>",
                        "<p>भरतनाट्यम - तमिलनाडु</p>",
                        "<p>कुचिपुड़ी - आंध्र प्रदेश</p>",
                        "<p>कथकली - केरल</p>"
                    ],
                    solution_en: "<p>45.(a)<strong> Sattriya &ndash; Himachal Pradesh</strong>. Sattriya is a classical Indian dance form that originated in the Vaishnava monasteries of Assam. Classical dances and their state of Origin : Bharatnatyam (Tamil Nadu) &middot; Kathak (North India) &middot; Kathakali (Kerala) &middot; Kuchipudi (Andhra Pradesh), Mohiniyattam (Kerala), Odissi (Odisha), Sattriya (Assam).</p>",
                    solution_hi: "<p>45.(a) <strong>सत्त्रिया</strong> - <strong>हिमाचल</strong> <strong>प्रदेश</strong>। सत्त्रिया एक शास्त्रीय भारतीय नृत्य शैली है जिसकी उत्पत्ति असम के वैष्णव मठों में हुई थी। शास्त्रीय नृत्य और उनकी उत्पत्ति वाले राज्य: भरतनाट्यम (तमिलनाडु), कथक (उत्तर भारत), कथकली (केरल), कुचिपुड़ी (आंध्र प्रदेश), मोहिनीअट्टम (केरल), ओडिसी (ओडिशा), सत्त्रिया (असम)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "46",
                    section: "18",
                    question_en: "<p>46. In 1908 Khudiram Bose along with _________ was involved in throwing a bomb at a carriage believing it to be occupied by Kingsford, the then sitting judge of Muzaffarpur.</p>",
                    question_hi: "<p>46. 1908 में खुदीराम बोस, ______________के साथ एक गाड़ी पर, यह मानकर कि गाड़ी मुजफ्फरपुर के तत्कालीन जज किंग्सफोर्ड की है, बम फेंकने में शामिल थे।</p>",
                    options_en: [
                        "<p>Rajguru</p>",
                        "<p>Sukhdev</p>",
                        "<p>Prafulla Chaki</p>",
                        "<p>Bhagat Singh</p>"
                    ],
                    options_hi: [
                        "<p>राजगुरु</p>",
                        "<p>सुखदेव</p>",
                        "<p>प्रफुल्ल चाकी</p>",
                        "<p>भगत सिंह</p>"
                    ],
                    solution_en: "<p>46.(c) <strong>Prafulla</strong> <strong>Chaki</strong> was an Indian revolutionary associated with the Jugantar group of revolutionaries. Khudiram Bose was among the youngest martyrs of India\'s freedom struggle against the British. At just 15, he had joined Anushilan Samiti and took part in several revolutionary activities against the British Raj. He was executed on August 11, 1908, in Muzaffarpur jail in Bihar. </p>",
                    solution_hi: "<p>46.(c) <strong>प्रफुल्ल चाकी</strong> जुगंतर क्रांतिकारियों के समूह से जुड़े एक भारतीय क्रांतिकारी थे। खुदीराम बोस अंग्रेजों के खिलाफ भारत के स्वतंत्रता संग्राम के सबसे कम आयु के शहीदों में से एक थे। महज 15 वर्ष की आयु में वे अनुशीलन समिति में शामिल हो गए थे और ब्रिटिश राज के खिलाफ कई क्रांतिकारी गतिविधियों में भाग लिया था। उन्हें 11 अगस्त, 1908 को बिहार के मुजफ्फरपुर जेल में फांसी दे दी गई थी।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "47",
                    section: "18",
                    question_en: "<p>47. Industries of strategic and national importance are usually placed in the ________ sector.</p>",
                    question_hi: "<p>47. रणनीतिक और राष्ट्रीय महत्व के उद्योगों को आमतौर पर ________क्षेत्र में रखा जाता है।</p>",
                    options_en: [
                        "<p>Public</p>",
                        "<p>Co-operative</p>",
                        "<p>Private</p>",
                        "<p>Joint</p>"
                    ],
                    options_hi: [
                        "<p>सार्वजनिक</p>",
                        "<p>सहकारी</p>",
                        "<p>निजी</p>",
                        "<p>संयुक्त</p>"
                    ],
                    solution_en: "<p>47.(a) <strong>Public</strong> sector enterprises are government/state controlled companies or corporations funded by governments. The private sector is the part of the economy that is not state-controlled and is run by individuals and companies for profit. Co-operative sector industries are operated and owned by the suppliers or producers of raw materials, workers or both. A Joint sector is a business or industry that is jointly owned, controlled, and managed by the government and private entrepreneurs.</p>",
                    solution_hi: "<p>47.(a) <strong>सार्वजनिक</strong> क्षेत्र के उद्यम सरकार/राज्य द्वारा नियंत्रित कंपनियाँ या निगम हैं जिन्हें सरकार द्वारा वित्तपोषित किया जाता है। निजी क्षेत्र अर्थव्यवस्था का वह भाग है जो राज्य द्वारा नियंत्रित नहीं है और इसे व्यक्तियों और कंपनियों द्वारा लाभ के लिए चलाया जाता है। सहकारी क्षेत्र के उद्योगों का संचालन और स्वामित्व कच्चे माल, श्रमिकों या दोनों के आपूर्तिकर्ताओं या उत्पादकों द्वारा किया जाता है। संयुक्त क्षेत्र एक व्यवसाय या उद्योग है जिसका स्वामित्व, नियंत्रण और प्रबंधन सरकार और निजी उद्यमियों द्वारा संयुक्त रूप से किया जाता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "48",
                    section: "18",
                    question_en: "<p>48. Under which of the following Mughal emperors were the Marathas a major challenge to the sovereignty of the Mughals?</p>",
                    question_hi: "<p>48. निम्नलिखित में से किस मुगल सम्राट के शासनकाल के दौरान मराठा मुगलों की संप्रभुता के लिए एक बड़ी चुनौती थे?</p>",
                    options_en: [
                        "<p>Humayun</p>",
                        "<p>Aurangzeb</p>",
                        "<p>Jahangir</p>",
                        "<p>Babur</p>"
                    ],
                    options_hi: [
                        "<p>हुमायूँ</p>",
                        "<p>औरंगज़ेब</p>",
                        "<p>जहाँगीर</p>",
                        "<p>बाबर</p>"
                    ],
                    solution_en: "<p>48.(b) <strong>Aurangzeb</strong> (Reign : 1658-1707) - He was the sixth Mughal Emperor and also known as Zinda Pir (living saint), Fakir, and Darbesh. He gave the French permission to build a factory in Surat in 1667. He banned singing, dancing and playing musical instruments, and Kalma writings on coins. He reimposed Jizya and Pilgrim tax in 1679 and ended the celebration of Navroz.</p>",
                    solution_hi: "<p>48.(b) <strong>औरंगजेब</strong> (शासनकाल : 1658-1707) - वह छठा मुगल बादशाह था और उसे जिंदा पीर (जीवित संत), फकीर और दरबेश के नाम से भी जाना जाता था। उसने 1667 में सूरत में एक कारखाना बनाने के लिए फ्रांसीसियों को अनुमति दी थी। उसने गायन, नृत्य और संगीत वाद्ययंत्र बजाने तथा सिक्कों पर कलमा लिखने पर प्रतिबंध लगा दिया था। उसने 1679 में जजिया और तीर्थयात्री कर फिर से लागू कर दिया और नवरोज का उत्सव मनाना बंद कर दिया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "49",
                    section: "18",
                    question_en: "<p>49. Which of the following is NOT a poverty alleviation programme in India?</p>",
                    question_hi: "<p>49. निम्नलिखित में से कौन-सा भारत में गरीबी उन्मूलन कार्यक्रम (poverty alleviation programme) नहीं है?</p>",
                    options_en: [
                        "<p>Rural Housing - Indira Awaas Yojana (IAY)</p>",
                        "<p>Namami Gange</p>",
                        "<p>Rural Employment Generation Programme (REGP)</p>",
                        "<p>Sampoorna Grameen Rozgar Yojana (SGRY)</p>"
                    ],
                    options_hi: [
                        "<p>ग्रामीण आवास- इंदिरा आवास योजना (IAY)</p>",
                        "<p>नमामि गंगे</p>",
                        "<p>ग्रामीण रोजगार सृजन कार्यक्रम (REGP)</p>",
                        "<p>संपूर्ण ग्रामीण रोजगार योजना (SGRY)</p>"
                    ],
                    solution_en: "<p>49.(b) <strong>Namami Gange.</strong> India has implemented several poverty alleviation programs aimed at reducing poverty and improving living standards. Poverty alleviation programme in India: Mahatma Gandhi National Rural Employment Guarantee Act (2005), Pradhan Mantri Awas Yojana (2015), National Rural Livelihood Mission (2011), Public Distribution System (PDS), Pradhan Mantri Garib Kalyan Yojana (2016).</p>",
                    solution_hi: "<p>49.(b) <strong>नमामि गंगे।</strong> भारत ने गरीबी कम करने और जीवन स्तर में सुधार लाने के उद्देश्य से कई गरीबी उन्मूलन कार्यक्रम लागू किए हैं। भारत में गरीबी उन्मूलन कार्यक्रम: महात्मा गांधी राष्ट्रीय ग्रामीण रोजगार गारंटी अधिनियम (2005), प्रधानमंत्री आवास योजना (2015), राष्ट्रीय ग्रामीण आजीविका मिशन (2011), सार्वजनिक वितरण प्रणाली (PDS), प्रधानमंत्री गरीब कल्याण योजना (2016)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "50",
                    section: "17",
                    question_en: "<p>50. Which state will host the 39th National Games in 2027 ?</p>",
                    question_hi: "<p>50. 2027 में 39वें राष्ट्रीय खेलों की मेजबानी कौन सा राज्य करेगा ?</p>",
                    options_en: [
                        "<p>Assam</p>",
                        "<p>Meghalaya</p>",
                        "<p>Karnataka</p>",
                        "<p>Odisha</p>"
                    ],
                    options_hi: [
                        "<p>असम</p>",
                        "<p>मेघालय</p>",
                        "<p>कर्नाटक</p>",
                        "<p>ओडिशा</p>"
                    ],
                    solution_en: "<p>50.(b) <strong>Meghalaya.</strong> The Indian Olympic Association confirmed Meghalaya as the host for the 39th National Games in February/March 2027. Meghalaya will host the event for the first time, aiming to boost sports infrastructure in the Northeast. The 2023 National Games were held in Goa, while Uttarakhand will host the 2025 edition.</p>",
                    solution_hi: "<p>50.(b) <strong>मेघालय।</strong> भारतीय ओलंपिक संघ ने फरवरी/मार्च 2027 में होने वाले 39वें राष्ट्रीय खेलों के लिए मेघालय को मेजबान घोषित किया। मेघालय पहली बार इस आयोजन की मेजबानी करेगा, जिससे पूर्वोत्तर भारत में खेल अधोसंरचना को बढ़ावा मिलेगा। 2023 राष्ट्रीय खेल गोवा में आयोजित हुए थे, जबकि 2025 संस्करण उत्तराखंड में होगा |</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "51",
                    section: "17",
                    question_en: "<p>51. What is the Highest Common Factor of <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> &times; 3<sup>5</sup> and 2<sup>4</sup> &times; 3<sup>6 </sup>?</p>",
                    question_hi: "<p>51. <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> &times; 3<sup>5</sup> और 2<sup>4</sup> &times; 3<sup>6</sup> का महत्तम समापवर्तक क्या है?</p>",
                    options_en: [
                        "<p>2 <math display=\"inline\"><mo>&#215;</mo></math> 3<sup>2</sup></p>",
                        "<p><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> &times; 3<sup>3</sup></p>",
                        "<p><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> &times; 3<sup>5</sup></p>",
                        "<p><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></math> &times; 3<sup>2</sup></p>"
                    ],
                    options_hi: [
                        "<p>2 <math display=\"inline\"><mo>&#215;</mo></math> 3<sup>2</sup></p>",
                        "<p><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> &times; 3<sup>3</sup></p>",
                        "<p><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> &times; 3<sup>5</sup></p>",
                        "<p><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>2</mn></mrow></msup></math> &times; 3<sup>2</sup></p>"
                    ],
                    solution_en: "<p>51.(c)<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> &times; 3<sup>5</sup> and 2<sup>4</sup> &times; 3<sup>6</sup><br>HCF = <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> &times; 3<sup>5</sup></p>",
                    solution_hi: "<p>51.(c)<br><math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> &times; 3<sup>5</sup> और 2<sup>4</sup> &times; 3<sup>6</sup><br>महत्तम समापवर्तक (HCF )= <math display=\"inline\"><msup><mrow><mn>2</mn></mrow><mrow><mn>3</mn></mrow></msup></math> &times; 3<sup>5</sup></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "52",
                    section: "17",
                    question_en: "<p>52. For what value of K does the following system have an infinite number of solutions? <br>x + 2Ky - 8 = 0 <br>2x - y - 16 = 0</p>",
                    question_hi: "<p>52. K के किस मान के लिए निम्नलिखित निकाय के अनंत हल हैं?<br>x + 2Ky - 8 = 0 <br>2x - y - 16 = 0</p>",
                    options_en: [
                        "<p>4</p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>2&nbsp;</p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>4</p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>2</p>",
                        "<p>- <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>52.(b) x + 2Ky - 8 = 0 &hellip;. (i) <br>2x - y - 16 = 0 &hellip;.(ii)<br>Condition for infinitely many solution , <math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn><msub><mrow></mrow><mrow></mrow></msub></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>c</mi><mn>1</mn></msub><msub><mi>c</mi><mn>2</mn></msub></mfrac></math><br>By comparing, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi>a</mi><mn>1</mn></msub><msub><mi>a</mi><mn>2</mn></msub></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">k</mi></mrow><mrow><mo>-</mo><mn>1</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> k =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math></p>",
                    solution_hi: "<p>52.(b) x + 2Ky - 8 = 0 &hellip;. (i) <br>2x - y - 16 = 0 &hellip;.(ii)<br>अनंत परिणामों के लिए शर्त ,<math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn><msub><mrow></mrow><mrow></mrow></msub></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></math><br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn><msub><mrow></mrow><mrow></mrow></msub></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac></math>&nbsp; तुलना करने पर<br><br><math display=\"inline\"><mo>&#8658;</mo></math> <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">k</mi></mrow><mrow><mo>-</mo><mn>1</mn></mrow></mfrac></math><br><math display=\"inline\"><mo>&#8658;</mo></math> k =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mo>-</mo><mo>&#160;</mo><mn>1</mn></mrow><mn>4</mn></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "53",
                    section: "17",
                    question_en: "<p>53. The third proportional of <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi></mrow></msup></mrow><mrow><msup><mrow><mi>y</mi><mi>&#160;</mi></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></msup></mrow></mfrac></math> &times; Z<sup>3.5</sup> and x<sup>0.25</sup> &times;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">y</mi><mrow><mn>0</mn><mo>.</mo><mn>25</mn></mrow></msup><mrow><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mrow><mo>-</mo><mn>0</mn><mo>.</mo><mn>25</mn></mrow></msup></mrow></mfrac></math>&nbsp; is ______.</p>",
                    question_hi: "<p>53. <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn><mo>.</mo><mn>5</mn><mi>&#160;</mi></mrow></msup></mrow><mrow><msup><mrow><mi>y</mi><mi>&#160;</mi></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></msup></mrow></mfrac></math> &times; Z<sup>3.5</sup> और x<sup>0.25</sup> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">y</mi><mrow><mn>0</mn><mo>.</mo><mn>25</mn></mrow></msup><mrow><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mrow><mo>-</mo><mn>0</mn><mo>.</mo><mn>25</mn></mrow></msup></mrow></mfrac></math> का तृतीयानुपाती ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>Y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><msup><mrow><mi>X</mi></mrow><mrow><mn>3</mn></mrow></msup><msup><mrow><mi>Z</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>Y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>X</mi></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mi>Z</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>Y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>X</mi></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mi>Z</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>Y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>X</mi></mrow><mrow><mn>3</mn></mrow></msup><msup><mrow><mi>Z</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>Y</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow><mrow><msup><mrow><mi>X</mi></mrow><mrow><mn>3</mn></mrow></msup><msup><mrow><mi>Z</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>Y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>X</mi></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mi>Z</mi></mrow><mrow><mn>3</mn></mrow></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>Y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>X</mi></mrow><mrow><mn>2</mn></mrow></msup><msup><mrow><mi>Z</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><msup><mrow><mi>Y</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><msup><mrow><mi>X</mi></mrow><mrow><mn>3</mn></mrow></msup><msup><mrow><mi>Z</mi></mrow><mrow><mn>2</mn></mrow></msup></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>53.(b)</p>\n<p>Third proportional =&nbsp;<math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi></mrow><mrow><mn>0</mn><mo>.</mo><mn>25</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><msup><mrow><mi>y</mi></mrow><mrow><mn>0</mn><mo>.</mo><mn>25</mn></mrow></msup></mrow><mrow><msup><mrow><mi>z</mi></mrow><mrow><mo>-</mo><mn>0</mn><mo>.</mo><mn>25</mn></mrow></msup></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><msup><mrow><mi mathvariant=\"bold-italic\">x</mi></mrow><mrow><mn>2</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi></mrow></msup></mrow><mrow><msup><mrow><mi mathvariant=\"bold-italic\">y</mi><mi mathvariant=\"bold-italic\">&#160;</mi></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></msup></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">z</mi></mrow><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><msup><mi>y</mi><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></msup><msup><mi>z</mi><mstyle displaystyle=\"true\"><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></mstyle></msup></mfrac></mstyle><mo>&#160;</mo></mrow><mstyle displaystyle=\"true\"><mfrac><msup><mi>x</mi><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>2</mn></mfrac></mstyle></msup><msup><mi>y</mi><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle></msup></mfrac><mo>&#215;</mo><mo>&#160;</mo><msup><mi>z</mi><mfrac><mn>7</mn><mn>2</mn></mfrac></msup></mstyle></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mi>z</mi></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></msup></mrow><mrow><msup><mrow><mi mathvariant=\"bold-italic\">x</mi></mrow><mrow><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi></mrow></msup><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">y</mi><mi mathvariant=\"bold-italic\">&#160;</mi></mrow><mrow><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></msup><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">z</mi></mrow><mrow><mfrac><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">&#160;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">y</mi><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow></msup><mrow><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>2</mn></mfrac></mstyle><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow></msup><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow></msup></mrow></mfrac></math>&nbsp; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">y</mi><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow></msup><mrow><msup><mi mathvariant=\"normal\">x</mi><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow></msup></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">Y</mi><mn>2</mn></msup><mrow><msup><mi mathvariant=\"normal\">X</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">Z</mi><mn>3</mn></msup></mrow></mfrac></math></p>",
                    solution_hi: "<p>53.(b)</p>\n<p>तृतीयानुपाती=&nbsp;<math display=\"inline\"><mfrac><mrow><msup><mrow><mo>(</mo><mi>x</mi></mrow><mrow><mn>0</mn><mo>.</mo><mn>25</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><msup><mrow><mi>y</mi></mrow><mrow><mn>0</mn><mo>.</mo><mn>25</mn></mrow></msup></mrow><mrow><msup><mrow><mi>z</mi></mrow><mrow><mo>-</mo><mn>0</mn><mo>.</mo><mn>25</mn></mrow></msup></mrow></mfrac><msup><mrow><mo>)</mo></mrow><mrow><mn>2</mn></mrow></msup></mrow><mrow><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><msup><mrow><mi mathvariant=\"bold-italic\">x</mi></mrow><mrow><mn>2</mn><mo>.</mo><mn>5</mn><mi mathvariant=\"bold-italic\">&#160;</mi></mrow></msup></mrow><mrow><msup><mrow><mi mathvariant=\"bold-italic\">y</mi><mi mathvariant=\"bold-italic\">&#160;</mi></mrow><mrow><mn>1</mn><mo>.</mo><mn>5</mn></mrow></msup></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">z</mi></mrow><mrow><mn>3</mn><mo>.</mo><mn>5</mn></mrow></msup></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi>x</mi><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><msup><mi>y</mi><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle></msup><msup><mi>z</mi><mstyle displaystyle=\"true\"><mfrac><mrow><mo>-</mo><mn>1</mn></mrow><mn>2</mn></mfrac></mstyle></msup></mfrac></mstyle><mo>&#160;</mo></mrow><mrow><mstyle displaystyle=\"true\"><mfrac><msup><mi>x</mi><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>2</mn></mfrac></mstyle></msup><msup><mi>y</mi><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle></msup></mfrac></mstyle><mo>&#215;</mo><mo>&#160;</mo><msup><mi>z</mi><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle></msup></mrow></mfrac></math><br>= <math display=\"inline\"><mfrac><mrow><msup><mrow><mi>x</mi></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mi>y</mi></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><msup><mrow><mi>z</mi></mrow><mrow><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></msup></mrow><mrow><msup><mrow><mi mathvariant=\"bold-italic\">x</mi></mrow><mrow><mfrac><mrow><mn>5</mn></mrow><mrow><mn>2</mn></mrow></mfrac><mi mathvariant=\"bold-italic\">&#160;</mi></mrow></msup><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">y</mi><mi mathvariant=\"bold-italic\">&#160;</mi></mrow><mrow><mo>-</mo><mi mathvariant=\"bold-italic\">&#160;</mi><mfrac><mrow><mn>3</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></msup><mo>&#215;</mo><mi mathvariant=\"bold-italic\">&#160;</mi><msup><mrow><mi mathvariant=\"bold-italic\">z</mi></mrow><mrow><mfrac><mrow><mn>7</mn></mrow><mrow><mn>2</mn></mrow></mfrac></mrow></msup><mi mathvariant=\"bold-italic\">&#160;</mi><mi mathvariant=\"bold-italic\">&#160;</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">y</mi><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>3</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow></msup><mrow><msup><mi mathvariant=\"normal\">x</mi><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>5</mn><mn>2</mn></mfrac></mstyle><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow></msup><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>7</mn><mn>2</mn></mfrac></mstyle><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mn>1</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow></msup></mrow></mfrac></math>&nbsp; =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">y</mi><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow></msup><mrow><msup><mi mathvariant=\"normal\">x</mi><mstyle displaystyle=\"true\"><mfrac><mn>4</mn><mn>2</mn></mfrac></mstyle></msup><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">z</mi><mrow><mo>(</mo><mstyle displaystyle=\"true\"><mfrac><mn>6</mn><mn>2</mn></mfrac></mstyle><mo>)</mo></mrow></msup></mrow></mfrac></math> = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">Y</mi><mn>2</mn></msup><mrow><msup><mi mathvariant=\"normal\">X</mi><mn>2</mn></msup><msup><mi mathvariant=\"normal\">Z</mi><mn>3</mn></msup></mrow></mfrac></math></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "54",
                    section: "17",
                    question_en: "<p>54. Amita can build a room in the same amount of time that Bina and Sita working together can build. If Amita and Bina together could do it in 25 days and Sita alone in 35 days, then how many days are required for Bina alone to do the same work ?</p>",
                    question_hi: "<p>54. अमिता उतने ही समय में एक कमरा बना सकती है, जितने समय में बीना और सीता एकसाथ मिलकर उसे बना सकती हैं। यदि अमिता और बीना एकसाथ मिलकर इसे 25 दिन में कर सकती हैं और सीता अकेले 35 दिन में कर सकती हैं, तो बीना को अकेले उसी कार्य को करने में कितने दिन लगेंगे ?</p>",
                    options_en: [
                        "<p>152 Days</p>",
                        "<p>175 Days</p>",
                        "<p>165 Days</p>",
                        "<p>180 Days</p>"
                    ],
                    options_hi: [
                        "<p>152 दिन</p>",
                        "<p>175 दिन</p>",
                        "<p>165 दिन</p>",
                        "<p>180 दिन</p>"
                    ],
                    solution_en: "<p>54.(b)<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math> Amita : Bina + Sita<br>Time <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; :&nbsp; &nbsp; 1<br>Effi. <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; :&nbsp; &nbsp; 1<br>Total Effi. = 2 units &hellip; (i)<br>Now, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705591.png\" alt=\"rId57\" width=\"209\" height=\"147\"><br>Effi. of Amita + Bina + Sita = 12 unit &hellip; (ii)<br>From (i) and (ii)<br>Effi. of Bina and Sita = 6 unit<br>Effi. of Bina = 6 - 5 = 1 unit<br>Time taken by Bina = <math display=\"inline\"><mfrac><mrow><mn>175</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 175 days</p>",
                    solution_hi: "<p>54.(b)<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> अमिता :&nbsp; बीना + सीता<br>समय <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; 1&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;1<br>दक्षता <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;1&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;1<br>कुल दक्षता = 2 इकाई &hellip; (i)<br>अब, <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607705776.png\" alt=\"rId58\" width=\"235\" height=\"173\"><br>अमिता + बीना + सीता की दक्षता = 12 इकाई&hellip; (ii)<br>(i) और (ii) से<br>बीना और सीता की दक्षता = 6 इकाई<br>बीना की दक्षता = 6 - 5 = 1 इकाई<br>बीना द्वारा लिया गया समय = <math display=\"inline\"><mfrac><mrow><mn>175</mn></mrow><mrow><mn>1</mn></mrow></mfrac></math> = 175 दिन</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "55",
                    section: "17",
                    question_en: "<p>55. A certain sum of money is lent out at simple interest. If that money becomes ₹24,000 in 2 years and ₹32,000 in 4 years, the annual rate of interest is:</p>",
                    question_hi: "<p>55. साधारण ब्याज पर एक निश्चित धनराशि ऋण पर दी जाती है। यदि वह धनराशि 2 वर्षों में ₹24,000 और 4 वर्षों में ₹32,000 हो जाती है, तो वार्षिक ब्याज दर क्या है?</p>",
                    options_en: [
                        "<p>20%</p>",
                        "<p>25%</p>",
                        "<p>16%</p>",
                        "<p>30%</p>"
                    ],
                    options_hi: [
                        "<p>20%</p>",
                        "<p>25%</p>",
                        "<p>16%</p>",
                        "<p>30%</p>"
                    ],
                    solution_en: "<p>55.(b)<br>Simple interest for 1 year = <math display=\"inline\"><mfrac><mrow><mn>32000</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>24000</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8000</mn><mn>2</mn></mfrac></math> = ₹4000<br>Principal = 24000 - 2 &times; 4000 = ₹16000<br>Hence, rate = <math display=\"inline\"><mfrac><mrow><mn>4000</mn></mrow><mrow><mn>16000</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    solution_hi: "<p>55.(b)<br>1 वर्ष का साधारण ब्याज = <math display=\"inline\"><mfrac><mrow><mn>32000</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mn>24000</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>8000</mn><mn>2</mn></mfrac></math> = ₹4000<br>मूलधन = 24000 - 2 &times; 4000 = ₹16000<br>अत: दर = <math display=\"inline\"><mfrac><mrow><mn>4000</mn></mrow><mrow><mn>16000</mn></mrow></mfrac></math> &times; 100 = 25%</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "56",
                    section: "17",
                    question_en: "<p>56. In a class, there are 60 students. The average weight of the entire class is 42 kg. If there are 36 boys in the class and the average weight of all the boys is 50 kg, then what is the average weight (in kg) of all the girls?</p>",
                    question_hi: "<p>56. एक कक्षा में 60 विद्यार्थी हैं। संपूर्ण कक्षा का औसत वजन 42 kg है। यदि कक्षा में 36 लड़के हैं और सभी लड़कों का औसत वजन 50 kg है, तो सभी लड़कियों का औसत वजन (kg में) कितना है?</p>",
                    options_en: [
                        "<p>30</p>",
                        "<p>32</p>",
                        "<p>36</p>",
                        "<p>34</p>"
                    ],
                    options_hi: [
                        "<p>30</p>",
                        "<p>32</p>",
                        "<p>36</p>",
                        "<p>34</p>"
                    ],
                    solution_en: "<p>56.(a)<br>Total weight of 60 students = 60 <math display=\"inline\"><mo>&#215;</mo></math> 42 = 2520<br>Total weight of boys = 36 <math display=\"inline\"><mo>&#215;</mo></math> 50 = 1800<br>Total weight of girls = 2520 - 1800 = 720<br>Average weight of girls = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 30</p>",
                    solution_hi: "<p>56.(a)<br>60 विद्यार्थियों का कुल भार = 60 <math display=\"inline\"><mo>&#215;</mo></math> 42 = 2520<br>लड़कों का कुल वजन = 36 <math display=\"inline\"><mo>&#215;</mo></math> 50 = 1800<br>लड़कियों का कुल वजन = 2520 - 1800 = 720<br>लड़कियों का औसत वजन = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>24</mn></mrow></mfrac></math> = 30</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "57",
                    section: "17",
                    question_en: "<p>57. A reduction of 7.5% in the cost price of a commodity enables a shopkeeper to purchase 15 kg more than what he previously purchased for a sum of ₹7,400. In order to make a profit of 32.5% on the pre-reduction cost price of the commodity, at what price (in ₹) per kg should the commodity be sold ?</p>",
                    question_hi: "<p>57. किसी वस्तु के लागत मूल्य में 7.5% की कमी होने पर एक दुकानदार ₹7,400 की राशि से पहले की खरीदी जाने वाली वस्तु की तुलना में 15 kg अधिक वस्तु खरीद सकता है। वस्तु के कटौती से पूर्व के क्रय मूल्य पर 32.5% का लाभ प्राप्त करने के लिए, वस्तु को किस मूल्य (₹ में) प्रति kg पर बेचना होगा?</p>",
                    options_en: [
                        "<p>54</p>",
                        "<p>53</p>",
                        "<p>52</p>",
                        "<p>51</p>"
                    ],
                    options_hi: [
                        "<p>54</p>",
                        "<p>53</p>",
                        "<p>52</p>",
                        "<p>51</p>"
                    ],
                    solution_en: "<p>57.(b)<br>Expenditure = Price &times; Consumption<br>Ratio <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; Original&nbsp; :&nbsp; Final<br>Price <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; 40&nbsp; &nbsp; &nbsp; &nbsp;:&nbsp; &nbsp;37<br>Consumption <math display=\"inline\"><mo>&#8594;</mo></math> 37&nbsp; &nbsp;:&nbsp; &nbsp; 40<br>---------------------------------------------------<br>Expenditure <math display=\"inline\"><mo>&#8594;</mo></math> 1480 : 1480<br>Increase in quantity (3 units) = 15 kg.<br>Original consumption (37 units) = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 37 = 185 kg.<br>The original price of sugar per kg = <math display=\"inline\"><mfrac><mrow><mn>7400</mn></mrow><mrow><mn>185</mn></mrow></mfrac></math> = 40 Rs.<br>Selling price after 32.5% profit = 40 <math display=\"inline\"><mo>&#215;</mo></math>&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>132</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = 53</p>",
                    solution_hi: "<p>57.(b)<br>व्यय =&nbsp; &nbsp; &nbsp; &nbsp;कीमत &times; खपत<br>अनुपात <math display=\"inline\"><mo>&#8594;</mo></math> मूल : अंतिम<br>कीमत <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; 40 : 37<br>खपत <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp; &nbsp;37 : 40<br>------------------------------------------<br>व्यय <math display=\"inline\"><mo>&#8594;</mo></math>&nbsp; &nbsp;1480 : 1480<br>मात्रा में वृद्धि (3 इकाई) = 15 किग्रा.<br>मूल खपत (37 इकाई) = <math display=\"inline\"><mfrac><mrow><mn>15</mn></mrow><mrow><mn>3</mn></mrow></mfrac></math> &times; 37 = 185 kg.<br>प्रति किलो चीनी की मूल कीमत = <math display=\"inline\"><mfrac><mrow><mn>7400</mn></mrow><mrow><mn>185</mn></mrow></mfrac></math> = 40 Rs.<br>32.5% लाभ के बाद विक्रय मूल्य = 40 &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>132</mn><mo>.</mo><mn>5</mn></mrow><mn>100</mn></mfrac></math> = 53</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "58",
                    section: "17",
                    question_en: "<p>58. Two towns P and Q are 275 km apart. A motorcycle rider starts from P towards Q at 10:00 a.m. at the speed of 25 km/h. Another rider starts from Q towards P at 12 noon on the same day at the speed of 20 km/h. At what time will they cross each other?</p>",
                    question_hi: "<p>58. दो शहर P और Q परस्पर 275 km दूर हैं। एक मोटरसाइकिल सवार 25 km/h की चाल से सुबह 10:00 बजे (10:00 a.m.) P से Q की ओर चलना शुरू करता है। एक अन्य सवार Q से P की ओर उसी दिन दोपहर 12 बजे (12 noon) 20 km/h की चाल से चलना शुरू करता है। वे किस समय एक दूसरे को पार करेंगे?</p>",
                    options_en: [
                        "<p>3:00 p.m.</p>",
                        "<p>5:00 p.m.</p>",
                        "<p>1:15 p.m.</p>",
                        "<p>4:30 p.m.</p>"
                    ],
                    options_hi: [
                        "<p>3:00 p.m.</p>",
                        "<p>5:00 p.m.</p>",
                        "<p>1:15 p.m.</p>",
                        "<p>4:30 p.m.</p>"
                    ],
                    solution_en: "<p>58.(b)<br>Distance travelled by P in 2 hour = 50 km<br>Remaining distance = 275 - 50 = 225<br>Time taken to meet = <math display=\"inline\"><mfrac><mrow><mn>225</mn></mrow><mrow><mn>25</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>20</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>225</mn><mn>45</mn></mfrac></math> = 5 hours<br>Hence, they will meet at 5:00 (12 noon + 5 hrs) pm.</p>",
                    solution_hi: "<p>58.(b)<br>P द्वारा 2 घंटे में तय की गई दूरी = 50 km<br>शेष दूरी = 275 - 50 = 225<br>मिलने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>225</mn></mrow><mrow><mn>25</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>20</mn></mrow></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>225</mn><mn>45</mn></mfrac></math> = 5 घंटे<br>इसलिए, वे शाम 5:00 (दोपहर 12 बजे + 5 घंटे) बजे मिलेंगे।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "59",
                    section: "17",
                    question_en: "<p>59. Which of the following numbers is divisible by 11?</p>",
                    question_hi: "<p>59. निम्नलिखित में से कौन सी संख्या 11 से विभाज्य है?</p>",
                    options_en: [
                        "<p>88,65,987</p>",
                        "<p>55,78,961</p>",
                        "<p>88,65,747</p>",
                        "<p>45,12,458</p>"
                    ],
                    options_hi: [
                        "<p>88,65,987</p>",
                        "<p>55,78,961</p>",
                        "<p>88,65,747</p>",
                        "<p>45,12,458</p>"
                    ],
                    solution_en: "<p>59.(c)<br><strong>For divisibility by 11</strong>, the difference between the sum of the digits at odd places and the sum of the digits at even places of the number is either 0 or divisible by 11.<br>Option (a) 88,65,987 <br>(8 + 6 + 9 + 7) - (8 + 5 + 8) = 30 - 21 = 9 (not divisible by 11)<br>Option (b) 55,78,961<br>(5 + 7 + 9 + 1) - (5 + 8 + 6) =22 - 19 = 3 (not divisible by 11)<br>Option (c) 88,65,747 <br>(8 + 6 + 7 + 7) - (8 + 5 + 4) = 28 - 17 = 11 (divisible by 11)<br>Option (d) 45,12,458<br>(4 + 1 + 4 + 8) - (5 + 2 + 5) = 17 - 12 = 5 (not divisible by 11)</p>",
                    solution_hi: "<p>59.(c)<br><strong>11 से विभाज्यता के लिए,</strong> संख्या के विषम स्थानों के अंकों के योग और सम स्थानों के अंकों के योग के बीच का अंतर या तो 0 है या 11 से विभाज्य संख्या है।<br>विकल्प (a) 88,65,987 <br>(8 + 6 + 9 + 7) - (8 + 5 + 8) = 30 - 21 = 9 (11 से विभाज्य नहीं)<br>विकल्प (b) 55,78,961<br>(5 + 7 + 9 + 1) - (5 + 8 + 6) =22 - 19 = 3 (11 से विभाज्य नहीं)<br>विकल्प (c) 88,65,747 <br>(8 + 6 + 7 + 7) - (8 + 5 + 4) = 28 - 17 = 11 (11 से विभाज्य है।)<br>विकल्प (d) 45,12,458<br>(4 + 1 + 4 + 8) - (5 + 2 + 5) = 17 - 12 = 5 (11 से विभाज्य नहीं)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "60",
                    section: "17",
                    question_en: "<p>60. Simplify the expression:<br>15 &times; 15 &times; 15 + 3 &times; 15 &times; 12 &times; 12 - 3 &times; 15 &times; 15 &times; 12 - 12 &times; 12 &times; 12</p>",
                    question_hi: "<p>60. निम्नलिखित व्यंजक को सरल कीजिए:<br>15 &times; 15 &times; 15 + 3 &times; 15 &times; 12 &times; 12 - 3 &times; 15 &times; 15 &times; 12 - 12 &times; 12 &times; 12</p>",
                    options_en: [
                        "<p>36</p>",
                        "<p>9</p>",
                        "<p>18</p>",
                        "<p>27</p>"
                    ],
                    options_hi: [
                        "<p>36</p>",
                        "<p>9</p>",
                        "<p>18</p>",
                        "<p>27</p>"
                    ],
                    solution_en: "<p>60.(d)<br><strong>Concept used : </strong><br>(a - b<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> = a<sup>3</sup> - b<sup>3</sup> - 3a<sup>2</sup>b + 3ab<sup>2</sup><br>15 &times; 15 &times; 15 + 3 &times; 15 &times; 12 &times; 12 - 3 &times; 15 &times; 15 &times; 12 - 12 &times; 12 &times; 12<br><math display=\"inline\"><mn>1</mn><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 3 &times; 15 &times; 12<sup>2</sup> - 3 &times; 15<sup>2 </sup>&times; 12 - 12<sup>3<br></sup>= (15 - 12<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> = 3<sup>3</sup> = 27</p>",
                    solution_hi: "<p>60.(d)<br><strong>प्रयुक्त अवधारणा :</strong><br>(a - b<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> = a<sup>3</sup> - b<sup>3</sup> - 3a<sup>2</sup>b + 3ab<sup>2</sup><br>15 &times; 15 &times; 15 + 3 &times; 15 &times; 12 &times; 12 - 3 &times; 15 &times; 15 &times; 12 - 12 &times; 12 &times; 12<br><math display=\"inline\"><mn>1</mn><msup><mrow><mn>5</mn></mrow><mrow><mn>3</mn></mrow></msup></math> + 3 &times; 15 &times; 12<sup>2</sup> - 3 &times; 15<sup>2 </sup>&times; 12 - 12<sup>3<br></sup>= (15 - 12<math display=\"inline\"><msup><mrow><mo>)</mo></mrow><mrow><mn>3</mn></mrow></msup></math> = 3<sup>3</sup> = 27</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "61",
                    section: "17",
                    question_en: "<p>61. What is the value of cos(x + y) - sin(x - y) + tan(2z)?</p>",
                    question_hi: "<p>61. cos(x + y) - sin(x - y) + tan(2z) का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>cos x. cos y - sin x. sin y - sin x. cos y - cos x. sin y + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">z</mi></mrow></mfrac></math></p>",
                        "<p>sin x. sin y + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">z</mi></mrow></mfrac></math></p>",
                        "<p>cos x.cos y - sin x. sin y - sin x.cos y + cos x. sin y + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">z</mi></mrow></mfrac></math></p>",
                        "<p>cos x.cos y - sin x. sin y - cos x. sin y + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">z</mi></mrow></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>cos x. cos y - sin x. sin y - sin x. cos y - cos x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>",
                        "<p>sin x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>",
                        "<p>cos x.cos y - sin x. sin y - sin x.cos y + cos x. sin y + <math display=\"inline\"><mfrac><mrow><mn>2</mn><mi>&#160;</mi><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>",
                        "<p>cos x.cos y - sin x. sin y - cos x. sin y + <math display=\"inline\"><mfrac><mrow><mi>t</mi><mi>a</mi><mi>n</mi><mi>&#160;</mi><mi>z</mi></mrow><mrow><mn>1</mn><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>t</mi><mi>a</mi><msup><mrow><mi>n</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>z</mi></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>61.(c)<br>As we know <br>{cos(x + y) = cos x.cos y - sin x. sin y }<br>{sin(x - y) = sin x.cos y - cos x. sin y}<br>{tan(2z) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">z</mi></mrow></mfrac></math> }<br>So, cos(x + y) - sin(x - y) + tan(2z)<br>= cos x.cos y - sin x. sin y - (sin x.cos y - cos x. sin y) + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">z</mi></mrow></mfrac></math><br>= cos x.cos y - sin x. sin y - sin x.cos y + cos x. sin y + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">z</mi></mrow></mfrac></math></p>",
                    solution_hi: "<p>61.(c)<br>जैसा कि हम जानते हैं <br>{cos(x + y) = cos x.cos y - sin x. sin y }<br>{ sin(x - y) = sin x.cos y - cos x. sin y}<br>{tan(2z) = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">z</mi></mrow></mfrac></math> }<br>तो , cos(x + y) - sin(x - y) + tan(2z)<br>= cos x.cos y - sin x. sin y - (sin x.cos y - cos x. sin y) + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">z</mi></mrow></mfrac></math><br>= cos x.cos y - sin x. sin y - sin x.cos y + cos x. sin y + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi mathvariant=\"normal\">&#160;</mi><mi>tan</mi><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">z</mi></mrow><mrow><mn>1</mn><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><msup><mi>tan</mi><mn>2</mn></msup><mi mathvariant=\"normal\">z</mi></mrow></mfrac></math></p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "62",
                    section: "17",
                    question_en: "<p>62. A cylinder has a curved surface area equal to 350% of the curved surface area of another cylinder. If their radii are in the ratio 3 : 1, then the volume of the smaller cylinder is approximately _____% of the larger cylinder (the smaller cylinder has its radius and height smaller when compared to the larger).</p>",
                    question_hi: "<p>62. एक बेलन का वक्र पृष्ठीय क्षेत्रफल दूसरे बेलन के वक्र पृष्ठीय क्षेत्रफल के 350% के बराबर है। यदि उनकी त्रिज्याएँ 3 : 1 के अनुपात में हैं, तो छोटे आकार वाले बेलन का आयतन, बड़े आकार वाले बेलन का लगभग _____% है (बड़े आकार वाले बेलन की तुलना में छोटे आकार वाले बेलन की त्रिज्या और ऊँचाई कम है)।</p>",
                    options_en: [
                        "<p>10.16</p>",
                        "<p>8.86</p>",
                        "<p>7.28</p>",
                        "<p>9.52</p>"
                    ],
                    options_hi: [
                        "<p>10.16</p>",
                        "<p>8.86</p>",
                        "<p>7.28</p>",
                        "<p>9.52</p>"
                    ],
                    solution_en: "<p>62.(d)<br>According to the question,<br>CSA of the cylinder = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>h</mi></math><br>2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>h</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>100</mn></mfrac></math> = 2&pi;RH<br>h : H = 6 : 7 (where radii ratio are, 3 : 1)<br>Volume of the cylinder = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math> <br>Small cylinder volume = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mfenced separators=\"|\"><mrow><mn>1</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></math><br>Big cylinder volume = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mfenced separators=\"|\"><mrow><mn>3</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi></math>7<br>required% = <math display=\"inline\"><mfrac><mrow><mi>&#960;</mi><msup><mrow><mfenced separators=\"|\"><mrow><mn>1</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mi>&#160;</mi><mi>&#960;</mi><msup><mrow><mfenced separators=\"|\"><mrow><mn>3</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7</mn></mrow></mfrac></math> &times; 100 = 9.52%</p>",
                    solution_hi: "<p>62.(d)<br>प्रश्न के अनुसार,<br>वेलन का वक्रपृष्ठीय क्षेत्रफल = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>h</mi></math><br>2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi><mi>h</mi></math> &times; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>350</mn><mn>100</mn></mfrac></math> = 2&pi;RH<br>h : H = 6 : 7 (त्रिज्याएँ , 3 : 1 के अनुपात मे है।)<br>वेलन का आयतन = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mi>r</mi></mrow><mrow><mn>2</mn></mrow></msup><mi>h</mi></math> <br>छोटे वेलन का आयतन = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mfenced separators=\"|\"><mrow><mn>1</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></math><br>बडे़ वेलन का आयतन = <math display=\"inline\"><mi>&#960;</mi><msup><mrow><mfenced separators=\"|\"><mrow><mn>3</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi></math>7<br>आवश्यक% = <math display=\"inline\"><mfrac><mrow><mi>&#960;</mi><msup><mrow><mfenced separators=\"|\"><mrow><mn>1</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>6</mn></mrow><mrow><mi>&#160;</mi><mi>&#960;</mi><msup><mrow><mfenced separators=\"|\"><mrow><mn>3</mn></mrow></mfenced></mrow><mrow><mn>2</mn></mrow></msup><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>7</mn></mrow></mfrac></math> &times; 100 = 9.52%</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "63",
                    section: "17",
                    question_en: "<p>63. For which of the following values of a and b do the given equations, have NO solution? <br>x - ay = 2 - a (1 - a)x + 6y = a + b</p>",
                    question_hi: "<p>63. निम्नलिखित में से a और b के किन मानों के लिए दिए गए समीकरणों में कोई हल नहीं है? <br>x - ay = 2 - a <br>(1 - a)x + 6y = a + b</p>",
                    options_en: [
                        "<p>a = -3,b &ne; 1</p>",
                        "<p>a = 3,b &ne;-1</p>",
                        "<p>a = -3,b &ne; -1</p>",
                        "<p>a = 3,b &ne; 1</p>"
                    ],
                    options_hi: [
                        "<p>a = -3,b &ne; 1</p>",
                        "<p>a = 3,b &ne;-1</p>",
                        "<p>a = -3,b &ne; -1</p>",
                        "<p>a = 3,b &ne; 1</p>"
                    ],
                    solution_en: "<p>63.(b) <br><math display=\"inline\"><mi>x</mi></math> - ay = 2 - a<br><math display=\"inline\"><mi>x</mi></math> - ay - (2 - a) = 0 &hellip; (i)<br>(1 - <math display=\"inline\"><mi>a</mi></math>)x + 6y - (a + b) &hellip; (ii)<br>For No solution<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac></math> &ne;&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607706045.png\" alt=\"rId59\" width=\"449\" height=\"281\"></p>",
                    solution_hi: "<p>63.(b) <br><math display=\"inline\"><mi>x</mi></math> - ay = 2 - a<br><math display=\"inline\"><mi>x</mi></math> - ay - (2 - a) = 0 &hellip; (i)<br>(1 - <math display=\"inline\"><mi>a</mi></math>)x + 6y - (a+b) &hellip; (ii)<br>कोई हल नहीं के लिए<br><math display=\"inline\"><mfrac><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>1</mn></mrow></msub></mrow><mrow><msub><mrow><mi>a</mi></mrow><mrow><mn>2</mn></mrow></msub></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">b</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">b</mi><mn>2</mn></msub></mfrac></math> &ne; <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msub><mi mathvariant=\"normal\">c</mi><mn>1</mn></msub><msub><mi mathvariant=\"normal\">c</mi><mn>2</mn></msub></mfrac></math><br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607706147.png\" alt=\"rId60\"></p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "64",
                    section: "17",
                    question_en: "<p>64. CD is a tangent to a circle of circumference of 88 cm with its centre at O. OC cuts the circle at P while OD cuts circle at V such that &ang;COD = 90&deg;. Length of PC is 6 cm. What is half of CD (in cm) if OD exceeds OC by 1 cm?</p>",
                    question_hi: "<p>64. CD, 88 सेमी परिधि वाले एक वृत्त की स्पर्शरेखा है जिसका केंद्र O है। OC वृत्त को P पर काटता है जबकि OD वृत्त को V पर इस प्रकार काटता है कि &ang;COD = 90&deg; है। PC की लंबाई 6 सेमी है। यदि OD, OC से 1 सेमी अधिक है तो CD का आधा (सेमी में) क्या है?</p>",
                    options_en: [
                        "<p>14.5</p>",
                        "<p>15.5</p>",
                        "<p>16.5</p>",
                        "<p>31</p>"
                    ],
                    options_hi: [
                        "<p>14.5</p>",
                        "<p>15.5</p>",
                        "<p>16.5</p>",
                        "<p>31</p>"
                    ],
                    solution_en: "<p>64.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607706277.png\" alt=\"rId61\" width=\"249\" height=\"163\"><br>Circumference of the circle = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi></math><br>88 = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; r<br>r = 14 cm<br>Length of PO and OV = 14 cm<br>So the length of CO = 6 + 14 = 20 cm<br>And the length of OD = 20 + 1 = 21<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>CD</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = (CO)<sup>2</sup> + (OD)<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>CD</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = (20)<sup>2</sup> + (21)<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>CD</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = 400 + 441 = 841<br>CD = 29 cm<br>Half of the CD = <math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 14.5 cm</p>",
                    solution_hi: "<p>64.(a)<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607706277.png\" alt=\"rId61\" width=\"249\" height=\"163\"><br>वृत्त की परिधि = 2<math display=\"inline\"><mi>&#960;</mi><mi>r</mi></math><br>88 = 2 &times; <math display=\"inline\"><mfrac><mrow><mn>22</mn></mrow><mrow><mn>7</mn></mrow></mfrac></math> &times; r<br>r = 14 सेमी<br>PO और OV की लंबाई = 14 सेमी<br>अतः CO की लंबाई = 6 + 14 = 20 सेमी<br>और OD की लंबाई = 20 + 1 = 21<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>CD</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = (CO)<sup>2</sup> + (OD)<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>CD</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = (20)<sup>2</sup> + (21)<sup>2</sup><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msup><mrow><mo>(</mo><mi>CD</mi><mo>)</mo></mrow><mn>2</mn></msup></math> = 400 + 441 = 841<br>CD = 29 सेमी<br>CD का आधा भाग = <math display=\"inline\"><mfrac><mrow><mn>29</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 14.5 सेमी</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "65",
                    section: "17",
                    question_en: "<p>65. Naman bought a few apples for ₹720 from a shop. He negotiated the price and the shopkeeper reduced it by ₹2 per apple. Due to this Naman could buy four more apples than what he had bought earlier. How many apples did he originally buy?</p>",
                    question_hi: "<p>65. नमन, किसी दुकान से ₹720 में कुछ सेब खरीदता है। वह कीमत पर मोल-भाव करता है और दुकानदार कीमत में प्रति सेब ₹2 की कमी कर देता है। इसके कारण नमन, पहले खरीदे गए सेब से चार अधिक सेब खरीद लेता है। उसने पहले कितने सेब खरीदे थे?</p>",
                    options_en: [
                        "<p>48</p>",
                        "<p>44</p>",
                        "<p>36</p>",
                        "<p>40</p>"
                    ],
                    options_hi: [
                        "<p>48</p>",
                        "<p>44</p>",
                        "<p>36</p>",
                        "<p>40</p>"
                    ],
                    solution_en: "<p>65.(c)<br>Let the initial price of apples be ₹x<br>According to question<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>720</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>2</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>720</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = 4<br>720(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math>) = 4<br>x(x - 2) = 360<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> - 2x - 360 = 0<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> - 20x +18x - 360 = 0<br>x(x - 20) + 18(x - 20) = 0<br>(x - 20)(x + 18) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 20<br>the no of apples originally bought = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 36</p>",
                    solution_hi: "<p>65.(c) <br>माना सेब का प्रारंभिक मूल्य ₹x है,<br>प्रश्च के अनुसार,<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>720</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>2</mn></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>720</mn><mi mathvariant=\"normal\">x</mi></mfrac></math> = 4<br>720(<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>2</mn><mrow><mi mathvariant=\"normal\">x</mi><mo>(</mo><mi mathvariant=\"normal\">x</mi><mo>-</mo><mn>2</mn><mo>)</mo></mrow></mfrac></math>) = 4<br>x(x - 2) = 360<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> - 2x - 360 = 0<br><math display=\"inline\"><msup><mrow><mi>x</mi></mrow><mrow><mn>2</mn></mrow></msup></math> - 20x +18x - 360 = 0<br>x(x - 20) + 18(x - 20) = 0<br>(x - 20)(x + 18) = 0<br><math display=\"inline\"><mo>&#8658;</mo></math> x = 20<br>तो, मूल रूप से खरीदे गए सेबों की संख्या = <math display=\"inline\"><mfrac><mrow><mn>720</mn></mrow><mrow><mn>20</mn></mrow></mfrac></math> = 36</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "66",
                    section: "17",
                    question_en: "<p>66. If cot<math display=\"inline\"><mi>&#945;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math>, then find the value of&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>Qsin&#945;</mi></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3.</p>",
                    question_hi: "<p>66. यदि cot<math display=\"inline\"><mi>&#945;</mi></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math> है, तो <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mi>Qsin&#945;</mi></mrow></mfrac></math> -&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>+</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3 का मान ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>0</p>",
                        "<p>3</p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mrow><msup><mi mathvariant=\"bold\">P</mi><mn>2</mn></msup><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><msup><mi mathvariant=\"bold\">Q</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></math></p>"
                    ],
                    options_hi: [
                        "<p>0</p>",
                        "<p>3</p>",
                        "<p><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mrow><msup><mi mathvariant=\"bold\">P</mi><mn>2</mn></msup><mi mathvariant=\"bold\">&#160;</mi><mo>+</mo><mi mathvariant=\"bold\">&#160;</mi><msup><mi mathvariant=\"bold\">Q</mi><mn>2</mn></msup></mrow></mfrac></math></p>",
                        "<p><math display=\"inline\"><mfrac><mrow><mi mathvariant=\"bold-italic\">P</mi></mrow><mrow><mi mathvariant=\"bold-italic\">Q</mi></mrow></mfrac></math></p>"
                    ],
                    solution_en: "<p>66.(b)<br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>Qsin&#945;</mi></mrow></mfrac></math><br>Divide numerator and denominator by sin<math display=\"inline\"><mi>&#945;</mi></math><br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mi>cos&#945;</mi><mrow><mi>sin&#945;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">Q</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mi>sin&#945;</mi><mi>sin&#945;</mi></mfrac></mrow><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mi>cos&#945;</mi><mrow><mi>sin&#945;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">Q</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mi>sin&#945;</mi><mi>sin&#945;</mi></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#160;</mo><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">P</mi></mrow><mi mathvariant=\"normal\">Q</mi></mfrac></mstyle><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi mathvariant=\"normal\">Q</mi><mo>&#160;</mo></mrow><mrow><mi mathvariant=\"normal\">P</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></mstyle><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">Q</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math><br>So, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>Qsin&#945;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3&nbsp; = 3</p>",
                    solution_hi: "<p>66.(b)<br><math display=\"inline\"><mfrac><mrow><mi>P</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#945;</mi><mi>&#160;</mi><mo>-</mo><mi>&#160;</mi><mi>Q</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#945;</mi></mrow><mrow><mi>P</mi><mi>c</mi><mi>o</mi><mi>s</mi><mi>&#945;</mi><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mi>Q</mi><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#945;</mi></mrow></mfrac></math><br>अंश और हर को <math display=\"inline\"><mi>s</mi><mi>i</mi><mi>n</mi><mi>&#945;</mi></math> से विभाजित करने पर <br><math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mi>cos&#945;</mi><mrow><mi>sin&#945;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">Q</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mi>sin&#945;</mi><mi>sin&#945;</mi></mfrac></mrow><mrow><mi mathvariant=\"normal\">P</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mi>cos&#945;</mi><mrow><mi>sin&#945;</mi><mi mathvariant=\"normal\">&#160;</mi></mrow></mfrac><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi mathvariant=\"normal\">Q</mi><mo>&#215;</mo><mi mathvariant=\"normal\">&#160;</mi><mfrac><mi>sin&#945;</mi><mi>sin&#945;</mi></mfrac></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi mathvariant=\"normal\">P</mi><mo>&#160;</mo><mo>&#215;</mo><mstyle displaystyle=\"true\"><mfrac><mrow><mo>&#160;</mo><mi mathvariant=\"normal\">P</mi></mrow><mi mathvariant=\"normal\">Q</mi></mfrac></mstyle><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mi mathvariant=\"normal\">Q</mi><mo>&#160;</mo></mrow><mrow><mi mathvariant=\"normal\">P</mi><mo>&#160;</mo><mo>&#215;</mo><mo>&#160;</mo><mstyle displaystyle=\"true\"><mfrac><mi mathvariant=\"normal\">P</mi><mi mathvariant=\"normal\">Q</mi></mfrac></mstyle><mo>+</mo><mo>&#160;</mo><mi mathvariant=\"normal\">Q</mi></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math><br>तो, <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>Pcos&#945;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>Qsin&#945;</mi></mrow><mrow><mi>Pcos&#945;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>+</mo><mi mathvariant=\"normal\">&#160;</mi><mi>Qsin&#945;</mi></mrow></mfrac></math> - <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> -<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow><mrow><msup><mi mathvariant=\"normal\">P</mi><mn>2</mn></msup><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><msup><mi mathvariant=\"normal\">Q</mi><mn>2</mn></msup></mrow></mfrac></math> + 3&nbsp; = 3</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "67",
                    section: "17",
                    question_en: "<p>67. The population of a city was 5000000 in 2020. The population grows 7.5% annually. The population in 2022 is ______.</p>",
                    question_hi: "<p>67. 2020 में एक शहर की जनसंख्या 5000000 थी। जनसंख्या में वार्षिक रूप से 7.5% की वृद्धि होती है। 2022 में शहर की जनसंख्या _________ है।</p>",
                    options_en: [
                        "<p>5558875</p>",
                        "<p>5887125</p>",
                        "<p>5875215</p>",
                        "<p>5778125</p>"
                    ],
                    options_hi: [
                        "<p>5558875</p>",
                        "<p>5887125</p>",
                        "<p>5875215</p>",
                        "<p>5778125</p>"
                    ],
                    solution_en: "<p>67.(d)<br>7.5% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math><br>Required population = 5000000 &times; <math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = 5778125</p>",
                    solution_hi: "<p>67.(d)<br>7.5% = <math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math><br>आवश्यक जनसंख्या = 5000000 &times; <math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> &times; <math display=\"inline\"><mfrac><mrow><mn>43</mn></mrow><mrow><mn>40</mn></mrow></mfrac></math> = 5778125</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "68",
                    section: "17",
                    question_en: "<p>68. What is the discount that Rohan should offer on the remaining ₹ 8,000 of a laptop priced at ₹ 48,000, given that, he has already given a 12% discount on the first ₹ 28,000 and 8% discount on the next ₹ 12,000 to match the discount amount of 9.5% given on the total price?</p>",
                    question_hi: "<p>68. रोहन को ₹48,000 के मूल्य वाले लैपटॉप के शेष ₹8,000 पर कितनी छूट देनी होगी, यदि वह पहले ₹28,000 के भुगतान पर 12% की छूट और अगले ₹12,000 के भुगतान पर 8% की छूट पहले ही दे चुका है ताकि लैपटॉप के कुल मूल्य पर दी गई 9.5% की छूट की बराबरी की जा सके?</p>",
                    options_en: [
                        "<p>₹ 402</p>",
                        "<p>₹ 204</p>",
                        "<p>₹ 420</p>",
                        "<p>₹ 240</p>"
                    ],
                    options_hi: [
                        "<p>₹ 402</p>",
                        "<p>₹ 420</p>",
                        "<p>₹ 204</p>",
                        "<p>₹ 240</p>"
                    ],
                    solution_en: "<p>68.(d)<br>Overall discount given = 48000 &times; 9.5% = ₹4560<br>Discount on first ₹28000 = 28000 &times; 12% = ₹3360<br>Discount on next ₹12000 = 12000 &times; 8% = ₹960<br>Hence, required discount = 4560 - (3360 + 960) = ₹240</p>",
                    solution_hi: "<p>68.(d)<br>दी गई कुल छूट = 48000 &times; 9.5% = ₹4560<br>पहले ₹28000 पर छूट = 28000 &times; 12% = ₹3360<br>अगले ₹12000 पर छूट = 12000 &times; 8% = ₹960<br>अतः, आवश्यक छूट = 4560 - (3360 + 960) = ₹240</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "69",
                    section: "17",
                    question_en: "<p>69. PQ is parallel to SR in a trapezium PQRS. It is given that PQ &gt; SR and the diagonals PR and QS intersect at O. If PO = 3x&nbsp;&minus; 15, OQ = x + 9, OR = x &minus; 5 and OS = 5 and x has two values x<sub>1 </sub>and x<sub>2</sub>, then the value of (x<sub>1</sub><sup>2</sup> - x<sub>2</sub><sup>2</sup>) is:</p>",
                    question_hi: "<p>69. एक समलंब चतुभुज PQRS में PQ, SR के समानांतर है। यह दिया गया है कि PQ &gt; SR है तथा विकर्ण PR और QS, O पर प्रतिच्छेद करते हैं। यदि PO = 3x&nbsp;- 15, OQ = x + 9, OR = x - 5 और OS = 5 है तथा x के दो मान x<sub>1</sub> और x<sub>2</sub> हैं, तो (x<sub>1</sub><sup>2</sup> - x<sub>2</sub><sup>2</sup>) का मान क्या है?</p>",
                    options_en: [
                        "<p>15</p>",
                        "<p>11</p>",
                        "<p>13</p>",
                        "<p>19</p>"
                    ],
                    options_hi: [
                        "<p>15</p>",
                        "<p>11</p>",
                        "<p>13</p>",
                        "<p>19</p>"
                    ],
                    solution_en: "<p>69.(b)<br>According to the question,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607706526.png\" alt=\"rId63\" width=\"320\" height=\"209\"><br><strong>Concept used :</strong> - <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PO</mi><mi>OR</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>QO</mi><mi>OS</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi><mo>-</mo><mn>15</mn></mrow><mrow><mi>x</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn></mrow><mn>5</mn></mfrac></math><br>15<math display=\"inline\"><mi>x</mi></math> - 75 = x<sup>2</sup> + 9x - 5x - 45<br><math display=\"inline\"><mi>x</mi></math><sup>2</sup> - 11x + 30 = 0<br><math display=\"inline\"><mi>x</mi></math><sup>2</sup> - 6x - 5x + 30 = 0 <br><math display=\"inline\"><mi>x</mi></math>(x - 6) - 5(x - 6) = 0<br>(<math display=\"inline\"><mi>x</mi></math> - 5)(x - 6)<br><math display=\"inline\"><mi>x</mi></math> = 5, x = 6<br>Now, the value of (x<sub>1</sub><sup>2</sup> - x<sub>2</sub><sup>2</sup>) = (6<sup>2</sup> - 5<sup>2</sup>) = 36 - 25 = 11</p>",
                    solution_hi: "<p>69.(b)<br>प्रश्न के अनुसार,<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607706526.png\" alt=\"rId63\" width=\"320\" height=\"209\"><br><strong>प्रयुक्त अवधारणा : -</strong> <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>PO</mi><mi>OR</mi></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mi>QO</mi><mi>OS</mi></mfrac></math><br><math display=\"inline\"><mfrac><mrow><mn>3</mn><mi>x</mi><mo>-</mo><mn>15</mn></mrow><mrow><mi>x</mi><mo>-</mo><mn>5</mn></mrow></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>x</mi><mo>&#160;</mo><mo>+</mo><mo>&#160;</mo><mn>9</mn></mrow><mn>5</mn></mfrac></math><br>15<math display=\"inline\"><mi>x</mi></math> - 75 = x<sup>2</sup> + 9x - 5x - 45<br><math display=\"inline\"><mi>x</mi></math><sup>2</sup> - 11x + 30 = 0<br><math display=\"inline\"><mi>x</mi></math><sup>2</sup> - 6x - 5x + 30 = 0 <br><math display=\"inline\"><mi>x</mi></math>(x - 6) - 5(x - 6) = 0<br>(<math display=\"inline\"><mi>x</mi></math> - 5)(x - 6)<br><math display=\"inline\"><mi>x</mi></math> = 5, x = 6<br>अब, (x<sub>1</sub><sup>2</sup> - x<sub>2</sub><sup>2</sup>) का मान = (6<sup>2</sup> - 5<sup>2</sup>) = 36 - 25 = 11</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "70",
                    section: "17",
                    question_en: "<p>70. Read the given information and answer the question that follows.<br>The following table gives the percentage of marks obtained by seven students in six different subjects in an examination.<br>The number in the brackets give the maximum marks in each subject.<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607706632.png\" alt=\"rId64\" width=\"480\" height=\"184\"> <br>If someone secured all the highest scores that have been obtained by some student or the other in the six subjects as given in the table above, what would be the exact overall percentage score obtained by that student?</p>",
                    question_hi: "<p>70. दी गई जानकारी को ध्यानपूर्वक पढ़ें और आगे दिए गए प्रश्न का उत्तर दें।<br>नीचे दी गई तालिका में एक परीक्षा के छह अलग-अलग विषयों में सात विद्यार्थियों द्वारा प्राप्त अंकों का प्रतिशत दर्शाया गया है।<br>कोष्ठक में दी गई संख्या प्रत्येक विषय के अधिकतम अंक को दर्शाती है।<br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607706766.png\" alt=\"rId65\" width=\"495\" height=\"246\"> <br>यदि किसी ने उपरोक्त तालिका में दिए गए छह विषयों में किसी भी विद्यार्थी या अन्य को प्राप्त अंकों की तुलना में उच्चतम अंक प्राप्त किए हैं, तो उस विद्यार्थी को प्राप्त कुल प्रतिशत अंक ज्ञात करें।</p>",
                    options_en: [
                        "<p>91 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>",
                        "<p>95.16 %</p>",
                        "<p>91%</p>",
                        "<p>90 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>"
                    ],
                    options_hi: [
                        "<p>91 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>",
                        "<p>95.16 %</p>",
                        "<p>91<math display=\"inline\"><mi>%</mi></math></p>",
                        "<p>90 <math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>"
                    ],
                    solution_en: "<p>70.(a)<br>Highest marks in maths = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 150<br>Highest marks in chemistry = 130 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 104<br>Highest marks in Physics = 120 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 108<br>Highest marks in Geography = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 95<br>Highest marks in History = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 54<br>Highest marks in Computer science = 40 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 36<br>Required percentage = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>104</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>108</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>95</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>54</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>36</mn></mrow><mrow><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>130</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>120</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>40</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>547</mn></mrow><mrow><mn>600</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>547</mn><mn>6</mn></mfrac></math> = 91 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>",
                    solution_hi: "<p>70.(a)<br>गणित में उच्चतम अंक = 150 &times; <math display=\"inline\"><mfrac><mrow><mn>100</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 150<br>रसायन विज्ञान में उच्चतम अंक = 130 &times; <math display=\"inline\"><mfrac><mrow><mn>80</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 104<br>भौतिकी में उच्चतम अंक = 120 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 108<br>भूगोल में उच्चतम अंक = 100 &times; <math display=\"inline\"><mfrac><mrow><mn>95</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 95<br>इतिहास में उच्चतम अंक = 60 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 54<br>कंप्यूटर विज्ञान में उच्चतम अंक = 40 &times; <math display=\"inline\"><mfrac><mrow><mn>90</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 36<br>आवश्यक प्रतिशत = <math display=\"inline\"><mfrac><mrow><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>104</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>108</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>95</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>54</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>36</mn></mrow><mrow><mn>150</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>130</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>120</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>100</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>60</mn><mi>&#160;</mi><mo>+</mo><mi>&#160;</mi><mn>40</mn><mi>&#160;</mi></mrow></mfrac></math> &times; 100 <br>= <math display=\"inline\"><mfrac><mrow><mn>547</mn></mrow><mrow><mn>600</mn></mrow></mfrac></math> &times; 100 = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>547</mn><mn>6</mn></mfrac></math> = 91 <math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>6</mn></mrow></mfrac></math>%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "71",
                    section: "17",
                    question_en: "<p>71. In a 5-km race, A beats B by 750 metres and C by 1260 metres. By how many metres does B beat C in the same race?</p>",
                    question_hi: "<p>71. 5 km की एक दौड़ में, A, B को 750 मीटर और C को 1260 मीटर से हराता है। उसी दौड़ में B, C को कितने मीटर से हराता है?</p>",
                    options_en: [
                        "<p>700 metres</p>",
                        "<p>600 metres</p>",
                        "<p>500 metres</p>",
                        "<p>400 metres</p>"
                    ],
                    options_hi: [
                        "<p>700 मीटर</p>",
                        "<p>600 मीटर</p>",
                        "<p>500 मीटर</p>",
                        "<p>400 मीटर</p>"
                    ],
                    solution_en: "<p>71.(b)<br><strong>Ratio -&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; A&nbsp; :&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; B&nbsp; :&nbsp; &nbsp; &nbsp;C</strong><br><strong>Distance</strong> - 5km : 4.25km : 3.74km<br>Now, total distance covered by C in the same race till B finish the race <br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> &times; 3.74 = 4.4 km<br>Hence, distance by which B beats C = 5 - 4.4 = 0.6km = 600 meter.</p>",
                    solution_hi: "<p>71.(b)<br><strong>अनुपात -&nbsp; &nbsp;A&nbsp; &nbsp; :&nbsp; &nbsp; &nbsp; B&nbsp; &nbsp; &nbsp; :&nbsp; &nbsp; &nbsp;C</strong><br>दूरी -&nbsp; &nbsp; &nbsp; 5 km&nbsp; : 4.25km&nbsp; &nbsp;: 3.74km<br>अब, B द्वारा दौड़ समाप्त करने तक उसी दौड़ में C द्वारा तय की गई कुल दूरी <br><math display=\"inline\"><mfrac><mrow><mn>5</mn></mrow><mrow><mn>4</mn><mo>.</mo><mn>25</mn></mrow></mfrac></math> &times; 3.74 = 4.4 km<br>इसलिए, वह दूरी जिससे B, C को हराता है = 5 - 4.4 = 0.6km = 600 मीटर</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "72",
                    section: "17",
                    question_en: "<p>72. If price of an article increases successively by 15% and 10%, respectively, then what is the percentage equivalent to a single price increase of that article?</p>",
                    question_hi: "<p>72. यदि किसी वस्तु के मूल्य में क्रमागत रूप से 15% और 10% की वृद्धि होती है, तो उस वस्तु की एकल मूल्य वृद्धि के समतुल्&zwj;य प्रतिशत क्या है?</p>",
                    options_en: [
                        "<p>26.5%</p>",
                        "<p>25%</p>",
                        "<p>23.5%</p>",
                        "<p>12.5%</p>"
                    ],
                    options_hi: [
                        "<p>26.5%</p>",
                        "<p>25%</p>",
                        "<p>23.5%</p>",
                        "<p>12.5%</p>"
                    ],
                    solution_en: "<p>72.(a)<br>increase % = 15 + 10 + <math display=\"inline\"><mfrac><mrow><mn>15</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 26.5%</p>",
                    solution_hi: "<p>72.(a)<br>वृद्धि% = 15 + 10 + <math display=\"inline\"><mfrac><mrow><mn>15</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mn>10</mn></mrow><mrow><mn>100</mn></mrow></mfrac></math> = 26.5%</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "73",
                    section: "17",
                    question_en: "<p>73. Three partners decided to start a business. They decided to donate 10% of the profit to a charity organisation and then the remaining profit would be shared among first, second and third partner in the ratio 1 : 2 : 2. The total profit received was ₹1,80,000. Find the share of the first partner.</p>",
                    question_hi: "<p>73. तीन साझेदारों ने एक व्यवसाय आरंभ करने का निर्णय लिया। उन्होंने लाभ का 10% एक धर्मार्थ संगठन को दान करने और फिर शेष लाभ को पहले, दूसरे और तीसरे साझेदार के बीच 1 : 2 : 2 के अनुपात में साझा करने का फैसला किया। प्राप्त कुल लाभ ₹1,80,000 था। पहले साझेदार का हिस्सा ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>₹32,400</p>",
                        "<p>₹31,800</p>",
                        "<p>₹33,200</p>",
                        "<p>₹29,600</p>"
                    ],
                    options_hi: [
                        "<p>₹32,400</p>",
                        "<p>₹31,800</p>",
                        "<p>₹33,200</p>",
                        "<p>₹29,600</p>"
                    ],
                    solution_en: "<p>73.(a)<br>Total profit distributed among partners in business = 1,80,000 &times; 90% = ₹1,62,000&nbsp;<br>Share of the first partner = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mn>2</mn></mrow></mfrac></math>) &times; 1,62,000 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>,</mo><mn>62</mn><mo>,</mo><mn>000</mn></mrow><mn>5</mn></mfrac></math>&nbsp; =&nbsp; ₹32,400</p>",
                    solution_hi: "<p>73.(a)<br>व्यवसाय में साझेदारों के बीच वितरित कुल लाभ = 1,80,000 &times; 90% = ₹1,62,000 <br>पहले साझेदार का हिस्सा = (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>1</mn><mrow><mn>1</mn><mo>+</mo><mo>&#160;</mo><mn>2</mn><mo>&#160;</mo><mo>+</mo><mn>2</mn></mrow></mfrac></math>) &times; 1,62,000 =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mo>,</mo><mn>62</mn><mo>,</mo><mn>000</mn></mrow><mn>5</mn></mfrac></math>&nbsp; =&nbsp; ₹32,400</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "74",
                    section: "17",
                    question_en: "<p>74. An inlet pipe can fill a water storage tank in 11 hours and an outlet pipe can empty the completely filled tank in 15 hours .If both pipes opened simultaneously. The time taken to fill the empty tank ( in hrs) is :</p>",
                    question_hi: "<p>74. एक इनलेट पाइप किसी जल भंडारण टंकी को 11 घंटे में भर सकता है और एक आउटलेट पाइप पूरी तरह से भरी हुई टंकी को 15 घंटे में खाली कर सकता है। यदि दोनों पाइप को एक साथ खोल दिया जाए, तो खाली टंकी को भरने में लगने वाला समय (घंटे में) ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>45<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>49<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>40</p>"
                    ],
                    options_hi: [
                        "<p>45<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math></p>",
                        "<p>41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>49<math display=\"inline\"><mfrac><mrow><mn>3</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math></p>",
                        "<p>40</p>"
                    ],
                    solution_en: "<p>74.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607706869.png\" alt=\"rId66\" width=\"269\" height=\"163\"><br>Efficiency of both pipes in 1 hour = 15 - 11 = 4 unitTime taken to fill the tank = <math display=\"inline\"><mfrac><mrow><mn>165</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> hours</p>",
                    solution_hi: "<p>74.(b) <br><img src=\"https://d2jnyx7x72uicf.cloudfront.net/image_1744607706996.png\" alt=\"rId67\"><br>1 घंटे में दोनों पाइपों की क्षमता = 15 - 11 = 4 इकाई टंकी को भरने में लगा समय = <math display=\"inline\"><mfrac><mrow><mn>165</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> = 41<math display=\"inline\"><mfrac><mrow><mn>1</mn></mrow><mrow><mn>4</mn></mrow></mfrac></math> घंटे</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "75",
                    section: "7",
                    question_en: "<p>75. A boatman rows 2 km in 10 mins, along the stream and 12 km in 2 hours against the stream. What is the speed of the stream ?</p>",
                    question_hi: "<p>75. एक नाविक धारा के अनुकूल 10 मिनट में 2 km और धारा के प्रतिकूल 2 घंटे में 12 km नाव चलाता है। धारा की चाल ज्ञात कीजिए।</p>",
                    options_en: [
                        "<p>3 km/h</p>",
                        "<p>2.5 km/h</p>",
                        "<p>4 km/h</p>",
                        "<p>3.5 km/h</p>"
                    ],
                    options_hi: [
                        "<p>3 km/h</p>",
                        "<p>2.5 km/h</p>",
                        "<p>4 km/h</p>",
                        "<p>3.5 km/h</p>"
                    ],
                    solution_en: "<p>75.(a)<br>Downstream speed = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>60</mn></mrow></mfrac></mrow></mfrac></math> km/h = 12 km/h<br>Upstream speed = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> km/h = 6 km/h<br>Speed of stream = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>downstream</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>upstream</mi></mrow><mn>2</mn></mfrac></math> =&nbsp;<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>6</mn></mrow><mn>2</mn></mfrac></math> = 3 km/h</p>",
                    solution_hi: "<p>75.(a)<br>धारा के अनुकूल गति = <math display=\"inline\"><mfrac><mrow><mn>2</mn></mrow><mrow><mn>10</mn><mi>&#160;</mi><mo>&#215;</mo><mi>&#160;</mi><mfrac><mrow><mn>1</mn></mrow><mrow><mn>60</mn></mrow></mfrac></mrow></mfrac></math> किमी/घंटा = 12 किमी/घंटा<br>धारा के प्रतिकूल गति = <math display=\"inline\"><mfrac><mrow><mn>12</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> किमी/घंटा = 6 किमी/घंटा<br>धारा की गति = <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2343;&#2366;&#2352;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2375;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2309;&#2344;&#2369;&#2325;&#2370;&#2354;</mi><mi mathvariant=\"normal\">&#160;</mi><mo>-</mo><mi mathvariant=\"normal\">&#160;</mi><mi>&#2343;&#2366;&#2352;&#2366;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2325;&#2375;</mi><mi mathvariant=\"normal\">&#160;</mi><mi>&#2346;&#2381;&#2352;&#2340;&#2367;&#2325;&#2370;&#2354;</mi></mrow><mn>2</mn></mfrac></math> = <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>12</mn><mo>&#160;</mo><mo>-</mo><mo>&#160;</mo><mn>6</mn></mrow><mn>2</mn></mfrac></math> = 3 किमी/घंटा</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "76",
                    section: "7",
                    question_en: "<p>76. Identify the segment in the sentence, which contains the grammatical error. <br>He is in bad mood as he could not clear the exam.</p>",
                    question_hi: "<p>76. Identify the segment in the sentence, which contains the grammatical error. <br>He is in bad mood as he could not clear the exam.</p>",
                    options_en: [
                        "<p>He is in</p>",
                        "<p>bad mood as</p>",
                        "<p>could not</p>",
                        "<p>clear the exam</p>"
                    ],
                    options_hi: [
                        "<p>He is in</p>",
                        "<p>bad mood as</p>",
                        "<p>could not</p>",
                        "<p>clear the exam</p>"
                    ],
                    solution_en: "<p>76.(b) Bad mood as<br>There is an article error in the sentence. We will put article &lsquo;a&rsquo; before &lsquo;bad mood&rsquo; as it is a phrase &lsquo;in a bad mood/situation&rsquo;. Hence, &lsquo;a bad mood as&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>76.(b) Bad mood as<br>Sentence में article की त्रुटि है। हम article \'a\' को \'bad mood\' से पहले लगाएंगे क्योंकि यहां \'bad mood/situation\' एक phrase है। इसलिए, \'a bad mood as\' सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "77",
                    section: "7",
                    question_en: "<p>77. Select the most appropriate Synonym of the given word.<br>Brat</p>",
                    question_hi: "<p>77. Select the most appropriate Synonym of the given word.<br>Brat</p>",
                    options_en: [
                        "<p>Spoilt child</p>",
                        "<p>Rich person</p>",
                        "<p>Plump lady</p>",
                        "<p>Wise man</p>"
                    ],
                    options_hi: [
                        "<p>Spoilt child</p>",
                        "<p>Rich person</p>",
                        "<p>Plump lady</p>",
                        "<p>Wise man</p>"
                    ],
                    solution_en: "<p>77.(a) Spoilt child <br><strong>Brat-</strong> an ill-mannered annoying child or Spoilt child.<br>Spoilt child - behaves badly if they do not get what he wants.<br>Plump lady - having a full rounded shape and slightly fat. <br>Wise man- having or showing wisdom, good sense, or good judgment.</p>",
                    solution_hi: "<p>77.(a) Spoilt child <br><strong>Brat-</strong> an ill- बिगड़ैल बच्चे।<br>Spoilt child - बिगड़ैल बच्चे।<br>Plump lady - मोटी महिला।<br>Wise man - ज्ञानी व्यक्ति ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "78",
                    section: "7",
                    question_en: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The injured man had been shot <span style=\"text-decoration: underline;\"><strong>from his back</strong></span>.</p>",
                    question_hi: "<p>78. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>The injured man had been shot <span style=\"text-decoration: underline;\"><strong>from his back</strong></span>.</p>",
                    options_en: [
                        "<p>in the back</p>",
                        "<p>to the back</p>",
                        "<p>by his back</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>in the back</p>",
                        "<p>to the back</p>",
                        "<p>by his back</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>78.(a) In the back <br>There is a prepositional error in the given sentence. The preposition &lsquo;from&rsquo; must be replaced with &lsquo;in&rsquo; because &lsquo;shot in the back&rsquo; is the correct phrase. Hence, &lsquo;in the back&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>78.(a) In the back <br>दिए गए वाक्य में prepositional error है। Preposition &lsquo;from&rsquo; को &lsquo;in&rsquo; से बदला जाना चाहिए क्योंकि &lsquo;shot in the back&rsquo; सही phrase है। इसलिए, &lsquo;in the back&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "79",
                    section: "7",
                    question_en: "<p>79. Find the correctly spelt word from the given options.</p>",
                    question_hi: "<p>79. Find the correctly spelt word from the given options.</p>",
                    options_en: [
                        "<p>diesel</p>",
                        "<p>deizel</p>",
                        "<p>diesal</p>",
                        "<p>deisel</p>"
                    ],
                    options_hi: [
                        "<p>diesel</p>",
                        "<p>deizel</p>",
                        "<p>diesal</p>",
                        "<p>deisel</p>"
                    ],
                    solution_en: "<p>79. (a) &lsquo;Diesel&rsquo; is the correct spelling.</p>",
                    solution_hi: "<p>79. (a) &lsquo;Diesel&rsquo; सही spelling हैं ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "80",
                    section: "7",
                    question_en: "<p>80. Select the option that correctly expresses the given sentence in passive voice.<br>The shareholders will elect him a director.</p>",
                    question_hi: "<p>80. Select the option that correctly expresses the given sentence in passive voice.<br>The shareholders will elect him a director.</p>",
                    options_en: [
                        "<p>He will be elected as director by the shareholders.</p>",
                        "<p>The shareholders have elected him a director.</p>",
                        "<p>The shareholders elected him a director.</p>",
                        "<p>He will be elected a director.</p>"
                    ],
                    options_hi: [
                        "<p>He will be elected as director by the shareholders.</p>",
                        "<p>The shareholders have elected him a director.</p>",
                        "<p>The shareholders elected him a director.</p>",
                        "<p>He will be elected a director.</p>"
                    ],
                    solution_en: "<p>80.(a) He will be elected as director by the shareholders. (Correct)<br>(b) The shareholders have elected him a director. (Incorrect Sentence Structure)<br>(c) The shareholders elected him a director. (Incorrect Sentence Structure)<br>(d) He will be elected a director. (Sentence is incomplete)</p>",
                    solution_hi: "<p>80.(a) He will be elected as director by the shareholders. (Correct)<br>(b) The shareholders have elected him a director. (गलत Sentence Structure)<br>(c) The shareholders elected him a director. (गलत Sentence Structure)<br>(d) He will be elected a director. (Sentence, incomplete है)</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "81",
                    section: "7",
                    question_en: "<p>81. Identify the segment in the sentence, which contains the grammatical error. <br>Whom do you think will win the next match and take away the trophy ?</p>",
                    question_hi: "<p>81. Identify the segment in the sentence, which contains the grammatical error. <br>Whom do you think will win the next match and take away the trophy ?</p>",
                    options_en: [
                        "<p>Whom do</p>",
                        "<p>you think will</p>",
                        "<p>and take away the trophy ?</p>",
                        "<p>win the next match</p>"
                    ],
                    options_hi: [
                        "<p>Whom do</p>",
                        "<p>you think will</p>",
                        "<p>and take away the trophy ?</p>",
                        "<p>win the next match</p>"
                    ],
                    solution_en: "<p>81.(a) Whom do<br>We will replace &ldquo;Whom&rdquo;( should be used to refer to the object of a verb or preposition) to &ldquo;Who&rdquo; (should be used to refer to the subject of a sentence.) The sentence contains error in option(b).</p>",
                    solution_hi: "<p>81.(a) Whom do<br>हम \"Whom\"(किसी verb या preposition के object को संदर्भित करने के लिए इस्तेमाल किया जाना चाहिए) को \"Who\"(sentence के subject को संदर्भित करने के लिए इस्तेमाल किया जाना चाहिए) से बदल देंगे। sentence में option (b) में त्रुटि है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "82",
                    section: "7",
                    question_en: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) He seemed at first to resent my invasion of his privacy.<br>(B) But when he found that I did not have a catapult ,he became friendly.<br>(C) My first friend was a small grey squirrel.<br>(D) When I started bringing him pieces of cake and biscuit he grew quite bold and was soon taking morsels from hand.</p>",
                    question_hi: "<p>82. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) He seemed at first to resent my invasion of his privacy.<br>(B) But when he found that I did not have a catapult ,he became friendly.<br>(C) My first friend was a small grey squirrel.<br>(D) When I started bringing him pieces of cake and biscuit he grew quite bold and was soon taking morsels from hand.</p>",
                    options_en: [
                        "<p>CBAD</p>",
                        "<p>ACBD</p>",
                        "<p>DBCA</p>",
                        "<p>CABD</p>"
                    ],
                    options_hi: [
                        "<p>CBAD</p>",
                        "<p>ACBD</p>",
                        "<p>DBCA</p>",
                        "<p>CABD</p>"
                    ],
                    solution_en: "<p>82.(d) <strong>CABD</strong><br>Sentence C will be the starting line as it contains the main idea of the parajumble i.e.a small grey squirrel was the narrator&rsquo;s friend . Sentence A states that first he thought that it seemed displeased as it thought he invaded its privacy . So, A will follow C. Further, Sentence B states that later it became friendly and Sentence D states that after this he started giving it pieces of cakes and biscuits. So, D will follow B . Going through the options, option (d) has the correct sequence.</p>",
                    solution_hi: "<p>82.(d) <strong>CABD</strong><br>Sentence C प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार है यानी एक छोटी ग्रे गिलहरी narrator की मित्र थी। Sentence A बताता है कि पहले उसने सोचा कि वह नाराज है। क्योंकि उसने उसकी privacy में बाधा डाली थी। तो, C के बाद A आएगा। आगे, sentence B कहता है कि बाद में दोस्ताना हो गया और sentence D कहता है कि इसके बाद उसने केक और बिस्कुट के टुकड़े देना शुरू कर दिया। तो B के बाद D आएगा। Options को देखते हुए, option (d) में सही क्रम है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "83",
                    section: "7",
                    question_en: "<p>83. Choose the word opposite in meaning to the given word.<br>Miniature</p>",
                    question_hi: "<p>83. Choose the word opposite in meaning to the given word.<br>Miniature</p>",
                    options_en: [
                        "<p>large</p>",
                        "<p>small</p>",
                        "<p>heavy</p>",
                        "<p>least</p>"
                    ],
                    options_hi: [
                        "<p>large</p>",
                        "<p>small</p>",
                        "<p>heavy</p>",
                        "<p>least</p>"
                    ],
                    solution_en: "<p>83.(a) Large<br>Miniature- very small of its kind.<br>Large- considerable or relatively great size, extent, or capacity.<br>small- a size that is less than normal or usual.<br>heavy- to have great weight; difficult to lift or move.<br>least- smallest in amount, extent, or significance.</p>",
                    solution_hi: "<p>83.(a) Large<br>Miniature- अपनी तरह का बहुत छोटा।<br>Large- बड़ा आकार।<br>small- छोटा।<br>heavy- बहुत वजन होना।<br>least- राशि, सीमा या महत्व में सबसे छोटा।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "84",
                    section: "7",
                    question_en: "<p>84. Identify the word that is misspelt.</p>",
                    question_hi: "<p>84. Identify the word that is misspelt.</p>",
                    options_en: [
                        "<p>loquacious</p>",
                        "<p>animosity</p>",
                        "<p>supporters</p>",
                        "<p>palpible</p>"
                    ],
                    options_hi: [
                        "<p>loquacious</p>",
                        "<p>animosity</p>",
                        "<p>supporters</p>",
                        "<p>palpible</p>"
                    ],
                    solution_en: "<p>84.(d) <strong>&lsquo;Palpable&rsquo;</strong> is the correct spelling.</p>",
                    solution_hi: "<p>84.(d) <strong>\'Palpable\'</strong> सही spelling है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "85",
                    section: "7",
                    question_en: "<p>85. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Mr. A.R. Sahni, member of the Planning Commission said yesterday that <span style=\"text-decoration: underline;\">five lakh jobs will be created</span> by the year 2023</p>",
                    question_hi: "<p>85. Identify the best way to improve the underlined part of the given sentence. If there is no improvement required, select &lsquo;no improvement&rsquo;.<br>Mr. A.R. Sahni, member of the Planning Commission said yesterday that <span style=\"text-decoration: underline;\">five lakh jobs will be created</span> by the year 2023</p>",
                    options_en: [
                        "<p>five lakh jobs will create</p>",
                        "<p>five lakh jobs will be creates</p>",
                        "<p>five lakhs jobs will be create</p>",
                        "<p>No improvement</p>"
                    ],
                    options_hi: [
                        "<p>five lakh jobs will create</p>",
                        "<p>five lakh jobs will be creates</p>",
                        "<p>five lakhs jobs will be create</p>",
                        "<p>No improvement</p>"
                    ],
                    solution_en: "<p>85.(d) No improvement. The given sentence is grammatically correct.</p>",
                    solution_hi: "<p>85.(d) No improvement. दिया गया sentence grammatically correct है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "86",
                    section: "7",
                    question_en: "<p>86. Select the most appropriate meaning of the underlined idiom.<br>Despite multiple obstacles, she continued with her endeavour, demonstrating that she has <span style=\"text-decoration: underline;\">nerves of steel</span>.</p>",
                    question_hi: "<p>86. Select the most appropriate meaning of the underlined idiom.<br>Despite multiple obstacles, she continued with her endeavour, demonstrating that she has <span style=\"text-decoration: underline;\">nerves of steel</span>.</p>",
                    options_en: [
                        "<p>The capacity to maintain composure when stressed</p>",
                        "<p>Extraordinary beauty</p>",
                        "<p>An absence of fear or anxiety</p>",
                        "<p>Relentless determination</p>"
                    ],
                    options_hi: [
                        "<p>The capacity to maintain composure when stressed</p>",
                        "<p>Extraordinary beauty</p>",
                        "<p>An absence of fear or anxiety</p>",
                        "<p>Relentless determination</p>"
                    ],
                    solution_en: "<p>86.(c) <strong>nerves of steel</strong> - an absence of fear or anxiety</p>",
                    solution_hi: "<p>86.(c) <strong>nerves of steel</strong> - an absence of fear or anxiety/भय या चिंता का अभाव</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "87",
                    section: "7",
                    question_en: "<p>87. Select the most appropriate meaning of idiom in the sentence.<br>Shyam Prasad was <span style=\"text-decoration: underline;\"><strong>a chip off old block</strong></span>.</p>",
                    question_hi: "<p>87. Select the most appropriate meaning of idiom in the sentence.<br>Shyam Prasad was <span style=\"text-decoration: underline;\"><strong>a chip off old block</strong></span>.</p>",
                    options_en: [
                        "<p>a common boy of the locality</p>",
                        "<p>an experienced old man</p>",
                        "<p>a being of the old generation</p>",
                        "<p>someone similar in character to one&rsquo;s father</p>"
                    ],
                    options_hi: [
                        "<p>a common boy of the locality</p>",
                        "<p>an experienced old man</p>",
                        "<p>a being of the old generation</p>",
                        "<p>someone similar in character to one&rsquo;s father</p>"
                    ],
                    solution_en: "<p>87.(d) someone similar in character to one&rsquo;s father</p>",
                    solution_hi: "<p>87.(d) someone similar in character to one&rsquo;s father /जिसका चरित्र उसके पिता के चरित्र के समान हो ।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "88",
                    section: "7",
                    question_en: "<p>88. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>a little gush of gratitude</p>",
                    question_hi: "<p>88. Choose the best alternative which best expresses the meaning of the idiom/ phrase given below. <br>a little gush of gratitude</p>",
                    options_en: [
                        "<p>gradual recovery</p>",
                        "<p>friendly feeling</p>",
                        "<p>excessive love</p>",
                        "<p>excessive enthusiasm</p>"
                    ],
                    options_hi: [
                        "<p>gradual recovery</p>",
                        "<p>friendly feeling</p>",
                        "<p>excessive love</p>",
                        "<p>excessive enthusiasm</p>"
                    ],
                    solution_en: "<p>88.(b) A little gush of gratitude- friendly feeling. <br>She really helped me to set up in the new city, so I owe her a little gush of gratitude.</p>",
                    solution_hi: "<p>88.(b) A little gush of gratitude- friendly feeling./ मैत्री भावना<br>She really helped me get set up in the new city, so I owe her a little gush of gratitude./<br>उसने वास्तव में मुझे नए शहर में स्थापित होने में मदद की, इसलिए मैं उसका आभार व्यक्त करता हूं।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "89",
                    section: "7",
                    question_en: "<p>89. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>One who has obstinate and narrow religious views</p>",
                    question_hi: "<p>89. In the following questions, out of the four alternatives, choose the one which can be substituted for the given words/sentence.<br>One who has obstinate and narrow religious views</p>",
                    options_en: [
                        "<p>Theosophist</p>",
                        "<p>Bigot</p>",
                        "<p>Philosopher</p>",
                        "<p>Theologian</p>"
                    ],
                    options_hi: [
                        "<p>Theosophist</p>",
                        "<p>Bigot</p>",
                        "<p>Philosopher</p>",
                        "<p>Theologian</p>"
                    ],
                    solution_en: "<p>89.(b) <strong>Bigot-</strong> a person who is intolerant towards those holding different opinions.<br><strong>Theosophist-</strong> a person who engages or is an expert in theosophy.<br><strong>Philosopher-</strong> a person engaged or learned in philosophy, especially as an academic discipline.<br><strong>Theologian-</strong> a person who engages or is an expert in theology.</p>",
                    solution_hi: "<p>89.(b) <strong>Bigot-</strong> एक व्यक्ति जो अलग राय रखने वालों के प्रति असहिष्णु है।<br><strong>Theosophist-</strong> रहस्यमय अंतर्दृष्टि के आधार पर भगवान और दुनिया के बारे में शिक्षण।<br><strong>Philosopher-</strong> दर्शनशास्त्र में लगा हुआ या सीखा हुआ व्यक्ति।<br><strong>Theologian- </strong>एक व्यक्ति जो धर्मशास्त्र में संलग्न या विशेषज्ञ है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "90",
                    section: "7",
                    question_en: "<p>90. Select the option that expresses the following sentence in active voice.<br>The letter will be delivered by the postman tomorrow.</p>",
                    question_hi: "<p>90. Select the option that expresses the following sentence in active voice.<br>The letter will be delivered by the postman tomorrow.</p>",
                    options_en: [
                        "<p>The postman delivered the letter tomorrow</p>",
                        "<p>The postman will have delivered the letter tomorrow.</p>",
                        "<p>The postman will deliver the letter tomorrow.</p>",
                        "<p>The postman will be delivering the letter tomorrow.</p>"
                    ],
                    options_hi: [
                        "<p>The postman delivered the letter tomorrow</p>",
                        "<p>The postman will have delivered the letter tomorrow.</p>",
                        "<p>The postman will deliver the letter tomorrow.</p>",
                        "<p>The postman will be delivering the letter tomorrow.</p>"
                    ],
                    solution_en: "<p>90.(c) The postman will deliver the letter tomorrow. (Correct)<br>(a) The postman <span style=\"text-decoration: underline;\">delivered</span> the letter tomorrow. (Incorrect Tense)<br>(b) The postman <span style=\"text-decoration: underline;\">will have delivered</span> the letter tomorrow. (Incorrect Verb)<br>(d) The postman <span style=\"text-decoration: underline;\">will be delivering</span> the letter tomorrow. (Incorrect Verb)</p>",
                    solution_hi: "<p>90.(c) The postman will deliver the letter tomorrow. (Correct)<br>(a) The postman <span style=\"text-decoration: underline;\">delivered</span> the letter tomorrow. (गलत Tense)<br>(b) The postman <span style=\"text-decoration: underline;\">will have delivered</span> the letter tomorrow. (गलत Verb)<br>(d) The postman <span style=\"text-decoration: underline;\">will be delivering</span> the letter tomorrow. (गलत Verb)</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "91",
                    section: "7",
                    question_en: "<p>91. Select the most appropriate Synonym of the given word<br>Prodigal</p>",
                    question_hi: "<p>91. Select the most appropriate Synonym of the given word<br>Prodigal</p>",
                    options_en: [
                        "<p>talented</p>",
                        "<p>wasteful</p>",
                        "<p>intelligent</p>",
                        "<p>modest</p>"
                    ],
                    options_hi: [
                        "<p>talented</p>",
                        "<p>wasteful</p>",
                        "<p>intelligent</p>",
                        "<p>modest</p>"
                    ],
                    solution_en: "<p>91.(b) <strong>wasteful</strong><br><strong>Prodigal-</strong> spending money freely and rather wastefully.<br><strong>Wasteful-</strong> using more of something than necessary, causing waste.<br><strong>Talented-</strong> having talent or special ability, gifted.<br><strong>Intelligent-</strong> having or showing the ability to understand, learn and think, clever.<br><strong>Modest-</strong> not talking too much about your own abilities, good qualities, etc.</p>",
                    solution_hi: "<p>91.(b) <strong>wasteful</strong><br><strong>Prodigal-</strong> खर्चीला<br><strong>Wasteful-</strong> अनावश्यक<br><strong>Talented-</strong> प्रतिभाशाली <br><strong>In</strong>t<strong>elligent-</strong> बुद्धिमान<br><strong>Modest-</strong> विनम्र</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "92",
                    section: "7",
                    question_en: "<p>92. Select the correct collocation to complete the following sentence. <br>She suppressed a __________ urge to yawn.</p>",
                    question_hi: "<p>92. Select the correct collocation to complete the following sentence. <br>She suppressed a __________ urge to yawn.</p>",
                    options_en: [
                        "<p>stout</p>",
                        "<p>strong</p>",
                        "<p>hard</p>",
                        "<p>tough</p>"
                    ],
                    options_hi: [
                        "<p>stout</p>",
                        "<p>strong</p>",
                        "<p>hard</p>",
                        "<p>tough</p>"
                    ],
                    solution_en: "<p>92.(b) strong<br>The given sentence states that she suppressed a strong urge to yawn. Hence, &lsquo;strong&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>92.(b) strong<br>दिए गए sentence में कहा गया है कि उसने जम्हाई या उबासी लेने (yawn) की तीव्र इच्छा को दबा दिया। अतः, &lsquo;strong&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "93",
                    section: "7",
                    question_en: "<p>93. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Reports indicate that thousands of farmers in Andhra Pradesh, Karnataka and Maharashtra have killed themselves, often by drinking pesticide.<br>(B) What drives farmers, to this extreme step?<br>(C) The investigation of journalist P. Sainath shows that farmers&rsquo; recent distress is due to a fusion of environmental and economic factors.<br>(D) Agrarian conditions have become more volatile as farmers are exposed to the fluctuations of the world market.</p>",
                    question_hi: "<p>93. Given below are four jumbled sentences. Pick the option that gives their correct order.<br>(A) Reports indicate that thousands of farmers in Andhra Pradesh, Karnataka and Maharashtra have killed themselves, often by drinking pesticide.<br>(B) What drives farmers, to this extreme step?<br>(C) The investigation of journalist P. Sainath shows that farmers&rsquo; recent distress is due to a fusion of environmental and economic factors.<br>(D) Agrarian conditions have become more volatile as farmers are exposed to the fluctuations of the world market.</p>",
                    options_en: [
                        "<p>ACBD</p>",
                        "<p>ABCD</p>",
                        "<p>ACDB</p>",
                        "<p>ADBC</p>"
                    ],
                    options_hi: [
                        "<p>ACBD</p>",
                        "<p>ABCD</p>",
                        "<p>ACDB</p>",
                        "<p>ADBC</p>"
                    ],
                    solution_en: "<p>93.(b) ABCD<br>Sentence A will be the starting line as it contains the main idea of the parajumble i.e.thousands of farmers in many states attempted suicide by drinking pesticide. Sentence B asks why the farmers take this step . So, B will follow A. Further, Sentence C states that the investigation showed the main reason of farmers distress is environmental and economic factors and Sentence D states about agrarian conditions . So, D will follow C. Going through the options, option (b) ABCD has the correct sequence.</p>",
                    solution_hi: "<p>93.(b) ABCD<br>Sentence A प्रारंभिक line होगी क्योंकि इसमें parajumble का मुख्य विचार है यानी कई राज्यों में हजारों किसानों ने कीटनाशक पीकर आत्महत्या का प्रयास किया। Sentence B पूछता है कि किसान यह कदम क्यों उठाते हैं। तो, A के बाद B आएगा। आगे, sentence C बताता है कि जांच से पता चलता है कि किसानों के संकट का मुख्य कारण पर्यावरणीय और आर्थिक कारक हैं और sentence D कृषि स्थितियों के बारे में बताता है। इसलिए,C के बाद D आएगा। Options को देखते हुए, option (b) ABCD में सही क्रम है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "94",
                    section: "7",
                    question_en: "<p>94. Choose the word opposite in the meaning to the word given.<br>Feebleness</p>",
                    question_hi: "<p>94. Choose the word opposite in the meaning to the word given.<br>Feebleness</p>",
                    options_en: [
                        "<p>soundness</p>",
                        "<p>infirm</p>",
                        "<p>imbecility</p>",
                        "<p>strength</p>"
                    ],
                    options_hi: [
                        "<p>soundness</p>",
                        "<p>infirm</p>",
                        "<p>imbecility</p>",
                        "<p>strength</p>"
                    ],
                    solution_en: "<p>94.(a) <strong>soundness</strong><br><strong>Feebleness</strong> - Lacking bodily strength; weak<br><strong>Soundness</strong> - free from injury, damage, defect, disease, etc. ; in good condition; healthy; robust:<br><strong>Infirm</strong> - not physically or mentally strong, especially through age or illness.<br><strong>Imbecility</strong> - an instance or point of weakness; feebleness; incapability.<br><strong>Strength</strong> - the quality or state of being physically strong.</p>",
                    solution_hi: "<p>94.(a) <strong>soundness</strong><br><strong>Feebleness</strong> - कमज़ोरी<br><strong>Soundness</strong> - दृढ़ता<br><strong>Infirm</strong> - बीमार <br><strong>Imbecility</strong> - मूर्खता<br><strong>Strength</strong> - ताकत</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "95",
                    section: "7",
                    question_en: "<p>95. It was difficult to_____ Gopal to watch a horror movie.</p>",
                    question_hi: "<p>95. It was difficult to_____ Gopal to watch a horror movie.</p>",
                    options_en: [
                        "<p>confer</p>",
                        "<p>persuade</p>",
                        "<p>dissuade</p>",
                        "<p>consult</p>"
                    ],
                    options_hi: [
                        "<p>confer</p>",
                        "<p>persuade</p>",
                        "<p>dissuade</p>",
                        "<p>consult</p>"
                    ],
                    solution_en: "<p>95.(b) persuade. <br>Persuade means to make somebody believe something. The given sentence states that it was difficult to make Gopal watch a horror movie. Hence, &lsquo;persuade&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>95.(b) persuade. <br>Persuade का अर्थ है किसी को किसी बात पर विश्वास दिलाना। दिए गए वाक्य में कहा गया है कि गोपाल को एक डरावनी फिल्म दिखाना मुश्किल था। इसलिए, &lsquo;persuade&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "96",
                    section: "7",
                    question_en: "<p>96. <strong>Cloze Test:</strong><br>It was an unusual dark night. In the (96)________ of an enjoyable dream, Jim thought he heard his dog (97)________ loudly. He groaned and glanced (98)________ his clock sleepily. Twelve o\'clock the green fluorescent (99)________ of his clock read. Suddenly, Jim (100)________ hushed voices outside his house. He immediately became alert and jumped out of bed. As his parents had gone on a holiday, Jim was alone at home. He decided to look who was outside.<br>Select the correct option to fill blank 96.</p>",
                    question_hi: "<p>96. <strong>Cloze Test:</strong><br>It was an unusual dark night. In the (96)________ of an enjoyable dream, Jim thought he heard his dog (97)________ loudly. He groaned and glanced (98)________ his clock sleepily. Twelve o\'clock the green fluorescent (99)________ of his clock read. Suddenly, Jim (100)________ hushed voices outside his house. He immediately became alert and jumped out of bed. As his parents had gone on a holiday, Jim was alone at home. He decided to look who was outside.<br>Select the correct option to fill blank 96.</p>",
                    options_en: [
                        "<p>middle</p>",
                        "<p>end</p>",
                        "<p>between</p>",
                        "<p>side</p>"
                    ],
                    options_hi: [
                        "<p>middle</p>",
                        "<p>end</p>",
                        "<p>between</p>",
                        "<p>side</p>"
                    ],
                    solution_en: "<p>96.(a) middle<br>The phrase &lsquo;in the middle of&rsquo; means in the process of doing something. The given passage states that in the middle of an enjoyable dream, Jim thought he heard his dog barking loudly. Hence, &lsquo;middle&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>96.(a) middle<br>Phrase &lsquo;in the middle of&rsquo; का अर्थ है कुछ करने की प्रक्रिया (process) में। दिए गए passage में बताया गया है कि एक सुखद सपने (enjoyable dream) के बीच में, Jim को लगा कि उसने अपने कुत्ते (dog) को जोर से भौंकते हुए सुना है। इसलिए, &lsquo;middle&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "97",
                    section: "7",
                    question_en: "<p>97. <strong>Cloze Test:</strong><br>It was an unusual dark night. In the (96)________ of an enjoyable dream, Jim thought he heard his dog (97)________ loudly. He groaned and glanced (98)________ his clock sleepily. Twelve o\'clock the green fluorescent (99)________ of his clock read. Suddenly, Jim (100)________ hushed voices outside his house. He immediately became alert and jumped out of bed. As his parents had gone on a holiday, Jim was alone at home. He decided to look who was outside.<br>Select the correct option to fill blank 97.</p>",
                    question_hi: "<p>97. <strong>Cloze Test:</strong><br>It was an unusual dark night. In the (96)________ of an enjoyable dream, Jim thought he heard his dog (97)________ loudly. He groaned and glanced (98)________ his clock sleepily. Twelve o\'clock the green fluorescent (99)________ of his clock read. Suddenly, Jim (100)________ hushed voices outside his house. He immediately became alert and jumped out of bed. As his parents had gone on a holiday, Jim was alone at home. He decided to look who was outside.<br>Select the correct option to fill blank 97.</p>",
                    options_en: [
                        "<p>jumping</p>",
                        "<p>barking</p>",
                        "<p>shouting</p>",
                        "<p>dancing</p>"
                    ],
                    options_hi: [
                        "<p>jumping</p>",
                        "<p>barking</p>",
                        "<p>shouting</p>",
                        "<p>dancing</p>"
                    ],
                    solution_en: "<p>97.(b) barking<br>&lsquo;Barking&rsquo; is the loud sound made by dogs. The given passage states that in the middle of an enjoyable dream, Jim thought he heard his dog barking loudly. Hence, &lsquo;barking&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>97.(b) barking<br>&lsquo;Barking&rsquo; कुत्तों (dogs) द्वारा की जाने वाली तेज़ आवाज़ (loud sound) है। दिए गए passage में बताया गया है कि एक सुखद सपने के बीच में, Jim को लगा कि उसने अपने कुत्ते को ज़ोर से भौंकते हुए सुना है। इसलिए, &lsquo;barking&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "98",
                    section: "7",
                    question_en: "<p>98. <strong>Cloze Test:</strong><br>It was an unusual dark night. In the (96)________ of an enjoyable dream, Jim thought he heard his dog (97)________ loudly. He groaned and glanced (98)________ his clock sleepily. Twelve o\'clock the green fluorescent (99)________ of his clock read. Suddenly, Jim (100)________ hushed voices outside his house. He immediately became alert and jumped out of bed. As his parents had gone on a holiday, Jim was alone at home. He decided to look who was outside.<br>Select the correct option to fill blank 98.</p>",
                    question_hi: "<p>98. <strong>Cloze Test:</strong><br>It was an unusual dark night. In the (96)________ of an enjoyable dream, Jim thought he heard his dog (97)________ loudly. He groaned and glanced (98)________ his clock sleepily. Twelve o\'clock the green fluorescent (99)________ of his clock read. Suddenly, Jim (100)________ hushed voices outside his house. He immediately became alert and jumped out of bed. As his parents had gone on a holiday, Jim was alone at home. He decided to look who was outside.<br>Select the correct option to fill blank 98.</p>",
                    options_en: [
                        "<p>on</p>",
                        "<p>upon</p>",
                        "<p>at</p>",
                        "<p>in</p>"
                    ],
                    options_hi: [
                        "<p>on</p>",
                        "<p>upon</p>",
                        "<p>at</p>",
                        "<p>in</p>"
                    ],
                    solution_en: "<p>98.(c) at<br>&lsquo;At&rsquo; is a fixed preposition used after the verb &lsquo;glance&rsquo;. &lsquo;Glance at&rsquo; means to take a quick look at something. Hence, &lsquo;at&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>98.(c) at<br>&lsquo;At&rsquo; एक fixed preposition है जो verb &lsquo;glance&rsquo; के बाद प्रयोग होगा। &lsquo;Glance at&rsquo; का अर्थ है किसी चीज़ पर एक सरसरी नज़र डालना। इसलिए, &lsquo;at&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "99",
                    section: "7",
                    question_en: "<p>99. <strong>Cloze Test:</strong><br>It was an unusual dark night. In the (96)________ of an enjoyable dream, Jim thought he heard his dog (97)________ loudly. He groaned and glanced (98)________ his clock sleepily. Twelve o\'clock the green fluorescent (99)________ of his clock read. Suddenly, Jim (100)________ hushed voices outside his house. He immediately became alert and jumped out of bed. As his parents had gone on a holiday, Jim was alone at home. He decided to look who was outside.<br>Select the correct option to fill blank 99.</p>",
                    question_hi: "<p>99. <strong>Cloze Test:</strong><br>It was an unusual dark night. In the (96)________ of an enjoyable dream, Jim thought he heard his dog (97)________ loudly. He groaned and glanced (98)________ his clock sleepily. Twelve o\'clock the green fluorescent (99)________ of his clock read. Suddenly, Jim (100)________ hushed voices outside his house. He immediately became alert and jumped out of bed. As his parents had gone on a holiday, Jim was alone at home. He decided to look who was outside.<br>Select the correct option to fill blank 99.</p>",
                    options_en: [
                        "<p>light</p>",
                        "<p>hands</p>",
                        "<p>points</p>",
                        "<p>dial</p>"
                    ],
                    options_hi: [
                        "<p>light</p>",
                        "<p>hands</p>",
                        "<p>points</p>",
                        "<p>dial</p>"
                    ],
                    solution_en: "<p>99.(b) hands<br>&lsquo;Hand&rsquo; means a clock&rsquo;s pointer that indicates time. Hence, &lsquo;hands&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>99.(b) hands<br>&lsquo;Hand&rsquo; का अर्थ घड़ी (clock) का वह संकेतक (pointer) है जो समय बताता है। इसलिए, &lsquo;hands&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "100",
                    section: "misc",
                    question_en: "<p>100. <strong>Cloze Test:</strong><br>It was an unusual dark night. In the (96)________ of an enjoyable dream, Jim thought he heard his dog (97)________ loudly. He groaned and glanced (98)________ his clock sleepily. Twelve o\'clock the green fluorescent (99)________ of his clock read. Suddenly, Jim (100)________ hushed voices outside his house. He immediately became alert and jumped out of bed. As his parents had gone on a holiday, Jim was alone at home. He decided to look who was outside.<br>Select the correct option to fill blank 100.</p>",
                    question_hi: "<p>100. <strong>Cloze Test:</strong><br>It was an unusual dark night. In the (96)________ of an enjoyable dream, Jim thought he heard his dog (97)________ loudly. He groaned and glanced (98)________ his clock sleepily. Twelve o\'clock the green fluorescent (99)________ of his clock read. Suddenly, Jim (100)________ hushed voices outside his house. He immediately became alert and jumped out of bed. As his parents had gone on a holiday, Jim was alone at home. He decided to look who was outside.<br>Select the correct option to fill blank 100.</p>",
                    options_en: [
                        "<p>listened</p>",
                        "<p>heard</p>",
                        "<p>thought</p>",
                        "<p>saw</p>"
                    ],
                    options_hi: [
                        "<p>listened</p>",
                        "<p>heard</p>",
                        "<p>thought</p>",
                        "<p>saw</p>"
                    ],
                    solution_en: "<p>100.(b) heard<br>&lsquo;Heard&rsquo; is the past form of &lsquo;hear&rsquo;. The given passage states that Jim heard hushed voices outside his house. Hence, &lsquo;heard&rsquo; is the most appropriate answer.</p>",
                    solution_hi: "<p>100.(b) heard<br>&lsquo;Heard&rsquo; &lsquo;hear&rsquo; का past form है। दिए गए passage में बताया गया है कि Jim ने अपने घर के बाहर धीमी आवाज़ें सुनीं। इसलिए, &lsquo;heard&rsquo; सबसे उपयुक्त उत्तर है।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'question-box d-flex align-items-center justify-content-center border rounded';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.question-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            const html = `
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <div class="question-text mb-4">
                            <div class="en">${question.question_en}</div>
                            <div class="hi" style="display:none">${question.question_hi}</div>
                        </div>
                        <div class="options">
                            ${['a', 'b', 'c', 'd'].map((opt, i) => `
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="q${index}" 
                                        id="opt-${index}-${opt}" value="${opt}" 
                                        ${answers[question.id] === opt ? 'checked' : ''}
                                        onclick="selectAnswer('${question.id}', '${opt}')">
                                    <label class="form-check-label w-100 py-2 px-3 rounded hover-bg-light" for="opt-${index}-${opt}">
                                        <div class="en">${question.options_en[i]}</div>
                                        <div class="hi" style="display:none">${question.options_hi[i]}</div>
                                    </label>
                                </div>
                            `).join('')}
                        </div>
                        ${submitted ? `
                            <div class="alert ${answers[question.id] === question.correct ? 'alert-success' : 'alert-danger'} mt-3">
                                ${answers[question.id] ? (answers[question.id] === question.correct ? 'Correct!' : 'Incorrect!') : 'Not attempted'}
                                <div class="mt-2">
                                    <strong>Solution:</strong>
                                    <div class="en">${question.solution_en}</div>
                                    <div class="hi" style="display:none">${question.solution_hi}</div>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // If in mobile view, close the menu
            if (window.innerWidth < 768) {
                toggleMenu(false);
            }
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                document.getElementById(`box-${index}`).classList.add('attempted');
            }
        }
        // Navigate to previous question
        function prevQuestion() {
            if (currentQuestion > 0) {
                showQuestion(currentQuestion - 1);
            }
        }
        // Navigate to next question
        function nextQuestion() {
            if (currentQuestion < questions.length - 1) {
                showQuestion(currentQuestion + 1);
            }
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) {
                showQuestion(section.start);
            }
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update button text
            document.getElementById('lang-btn').innerHTML = currentLang === 'en' ? 
                '<i class="fas fa-language"></i> हिंदी' : 
                '<i class="fas fa-language"></i> English';
        }
        // Toggle menu for mobile
        function toggleMenu(show) {
            const nav = document.getElementById('question-nav');
            const content = document.getElementById('question-content');
            if (show === undefined) {
                show = nav.style.right === '-100%' || nav.style.right === '';
            }
            if (show) {
                nav.style.right = '0';
                if (window.innerWidth >= 768) {
                    content.style.marginRight = '320px';
                }
            } else {
                nav.style.right = window.innerWidth < 768 ? '-100%' : '0';
                content.style.marginRight = window.innerWidth < 768 ? '0' : '320px';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
            document.getElementById('total-count').textContent = questions.length;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.remove('attempted');
                    box.classList.add('bg-secondary', 'text-white');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.remove('attempted');
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.remove('attempted');
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            resultsModal.show();
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check-circle me-1"></i> Submitted';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            resultsModal.hide();
            showQuestion(0);
        }
    </script>
</body>
</html>