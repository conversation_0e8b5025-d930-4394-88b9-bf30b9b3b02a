<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">10:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 15</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">15</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 10 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["6"] = {
                name: "Reasoning",
                start: 0,
                end: 13
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="6">Reasoning</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 14,
                end: 14
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "6",
                    question_en: "<p>1. Select the set in which the numbers are related in the same way as are the numbers of the following set.<br>7 &mdash; 14 &mdash; 23<br>4 &mdash; 11 &mdash; 20<br>NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>1. उस समुच्चय का चयन कीजिए जिसमें संख्याएँ उसी प्रकार से संबंधित हैं जिस प्रकार निम्नलिखित समुच्चय की संख्याएँ संबंधित हैं।<br>7 &mdash; 14 &mdash; 23<br>4 &mdash; 11 &mdash; 20<br>नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 को लें - 13 पर संक्रियाएं जैसे 13 में जोड़ना/घटाना/गुणा करना आदि की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>7 &mdash; 15 &mdash; 22</p>", "<p>15 &mdash; 30 &mdash; 20</p>", 
                                "<p>5 &mdash; 12 &mdash; 21</p>", "<p>9 &mdash; 12 &mdash; 22</p>"],
                    options_hi: ["<p>7 &mdash; 15 &mdash; 22</p>", "<p>15 &mdash; 30 &mdash; 20</p>",
                                "<p>5 &mdash; 12 &mdash; 21</p>", "<p>9 &mdash; 12 &mdash; 22</p>"],
                    solution_en: "<p>1.(c) <strong>Logic:-</strong> (1st number + 3rd number)<math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 = 2nd number<br>(7 - 14- 23) :- (30)<math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 &rArr; (15) - 1 = 14<br>(4 - 11 - 20) :- (24)<math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 &rArr; (12) - 1 = 11<br>Similarly,<br>(5 - 12 - 21) :- (26) <math display=\"inline\"><mo>&#247;</mo></math> 2 -1 &rArr; (13) - 1 = 12</p>",
                    solution_hi: "<p>1.(c) <strong>तर्क:- </strong>(पहली संख्या + तीसरी संख्या) <math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 = दूसरी संख्या<br>(7 - 14- 23) :- (30)<math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 &rArr; (15) - 1 = 14<br>(4 - 11 - 20) :- (24)<math display=\"inline\"><mo>&#247;</mo></math> 2 - 1 &rArr; (12) - 1 = 11<br>इसी प्रकार,<br>(5 - 12 - 21) :- (26) <math display=\"inline\"><mo>&#247;</mo></math> 2 -1 &rArr; (13) - 1 = 12</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "6",
                    question_en: "<p>2. Select the set in which the numbers are related in the same way as are the numbers of the following sets.<br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)<br>(27, 405, 3)<br>(23, 690, 6)</p>",
                    question_hi: "<p>2. उस समुच्चय का चयन करें जिसमें दी गई संख्याएँ आपस में उसी प्रकार संबंधित हैं, जिस प्रकार प्रश्न में दिए गए समुच्चय की संख्याएँ आपस में संबंधित हैं।<br>(ध्यान दें: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर गणितीय संक्रियाएँ की जानी चाहिए। जैसे, 13 के मामले में - 13 पर की जाने वाली विभिन्न गणितीय संक्रियाएँ जैसे जोड़ना / घटाना / गुणा करना आदि केवल 13 के साथ की जा सकती हैं। लेकिन 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है।)<br>(27, 405, 3)<br>(23, 690, 6)</p>",
                    options_en: ["<p>(14, 364, 7)</p>", "<p>(22, 616, 7)</p>", 
                                "<p>(16, 448, 7)</p>", "<p>(24, 600, 5)</p>"],
                    options_hi: ["<p>(14, 364, 7)</p>", "<p>(22, 616, 7)</p>",
                                "<p>(16, 448, 7)</p>", "<p>(24, 600, 5)</p>"],
                    solution_en: "<p>2.(d) <strong>Logic:-</strong> (1st number &times; 3rd number)&times;5 = 2nd number<br>(27, 405, 3) :- (27 &times; 3)&times; 5 &rArr; (81)&times; 5 = 405<br>(23, 690, 6) :- (23 &times; 6)&times;5 &rArr; (138)&times;5 = 690<br>Similarly,<br>(24, 600, 5) :- (24 &times; 5)&times;5 &rArr; (120)&times; 5 = 600</p>",
                    solution_hi: "<p>2.(d) <strong>तर्क:- </strong>(पहली संख्या &times; तीसरी संख्या)&times;5 = दूसरी संख्या<br>(27, 405, 3) :- (27 &times; 3)&times; 5 &rArr; (81)&times; 5 = 405<br>(23, 690, 6) :- (23 &times; 6)&times;5 &rArr; (138)&times;5 = 690<br>इसी प्रकार,<br>(24, 600, 5) :- (24 &times; 5)&times;5 &rArr; (120)&times; 5 = 600</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "6",
                    question_en: "3.  Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must not be related to each other based on the number of letters/number of consonants/vowels in the word)<br />Frivolous : Big :: Deride : ?",
                    question_hi: "3. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जैसे दूसरा शब्द पहले शब्द से संबंधित है। (शब्दों को अर्थपूर्ण रूप में माना जाना चाहिए और शब्द आपस में अक्षरों की संख्या/व्यंजन/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)<br />तुच्छ : प्रासंगिक :: उपहास : ?",
                    options_en: [" Jeer", " Detest", 
                                " Taunt", " Encourage"],
                    options_hi: [" मज़ाक ", " घृणा करना  ",
                                "  तिरस्कार", " प्रोत्साहित करना"],
                    solution_en: "3.(d) As “Frivolous” and “Big” are antonym of each other similarly, “Deride” and “Encourage” are antonym of each other.",
                    solution_hi: "3.(d) जैसे “तुच्छ” और “प्रासंगिक” एक दूसरे के विपरीतार्थक शब्द हैं, उसी प्रकार “उपहास” करना और “प्रोत्साहित” करना एक दूसरे के विपरीतार्थक शब्द हैं।",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "6",
                    question_en: "<p>4. Select the triad in which the numbers are related to each other in the same way as the numbers in the following triads. <br>16 - 40 - 100<br>8 - 20 - 50<br>(NOTE: Operation should be performed on the whole numbers, Without breaking down the numbers into its constituent digits. E.g. 13- Operation on 13 such as adding/multiplying itc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>4. उस त्रिक का चयन करें जिसमें दी गई संख्याएं एक - दूसरे से उसी प्रकार संबंधित हैं, जिस प्रकार निम्&zwj;नलिखित त्रिकों की संख्याएं एक - दूसरे से संबंधित हैं। <br>16 - 40 - 100<br>8 - 20 - 50<br>नोट: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>12-30-120</p>", "<p>18-50-110</p>", 
                                "<p>22-74-185</p>", "<p>24-60-150</p>"],
                    options_hi: ["<p>12-30-120</p>", "<p>18-50-110</p>",
                                "<p>22-74-185</p>", "<p>24-60-150</p>"],
                    solution_en: "<p>4.(d)<strong> Logic: </strong>1st number &times; 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>1</mn><mi>s</mi><mi>t</mi><mi>&#160;</mi><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi></mrow><mn>2</mn></mfrac></math> = 2nd number, 2nd number &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mn>2</mn><mi>n</mi><mi>d</mi><mo>&#160;</mo><mi>n</mi><mi>u</mi><mi>m</mi><mi>b</mi><mi>e</mi><mi>r</mi></mrow><mn>3</mn></mfrac></math> = 3rd number<br>16 - 40 - 100 :- 16 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 40, 40 &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math> = 100<br>8 - 20 - 50 :- 8 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 20, 20 &times; 2 +<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>2</mn></mfrac></math> = 50<br>Similarly<br>24-60-150 :- 24 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 60, 60 &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>2</mn></mfrac></math> = 150</p>",
                    solution_hi: "<p>4.(d) <strong>तर्क: </strong>पहला नंबर &times; 2 + <math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2346;&#2361;&#2354;&#2366;</mi><mi>&#160;</mi><mo>&#160;</mo><mi>&#2344;&#2306;&#2348;&#2352;</mi></mrow><mn>2</mn></mfrac></math> = दूसरा नंबर, दूसरा नंबर &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mrow><mi>&#2342;&#2370;&#2360;&#2352;&#2366;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2344;&#2306;&#2348;&#2352;</mi></mrow><mn>3</mn></mfrac></math>&nbsp;= तीसरा नंबर<br>16 - 40 - 100 :- 16 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>16</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 40, 40 &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>40</mn><mn>2</mn></mfrac></math> = 100<br>8 - 20 - 50 :- 8 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>8</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 20, 20 &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>20</mn><mn>2</mn></mfrac></math> = 50<br>उसी प्रकार<br>24-60-150 :- 24 &times; 2 + <math display=\"inline\"><mfrac><mrow><mn>24</mn></mrow><mrow><mn>2</mn></mrow></mfrac></math> = 60, 60 &times; 2 + <math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mfrac><mn>60</mn><mn>2</mn></mfrac></math> = 150</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "6",
                    question_en: "<p>5. Select the triad in which the numbers are related to each other in the same way as are the numbers of the given triads. <br>(148, 126, 104), (98, 76, 54) <br>(NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>5. उस त्रिक का चयन कीजिए, जिसमें संख्याएँ एक-दूसरे से उसी तरह संबंधित हैं, जैसे दिए गए त्रिकों की संख्याएँ हैं। <br>(148, 126, 104), (98, 76, 54) <br>(ध्यान दीजिए: संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 - 13 पर संक्रिया जैसे जोड़ना / हटाना / गुणा करना आदि 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>(157, 135, 113)</p>", "<p>(149, 137, 115)</p>", 
                                "<p>(128, 104, 82)</p>", "<p>(134, 102, 90)</p>"],
                    options_hi: ["<p>(157, 135, 113)</p>", "<p>(149, 137, 115)</p>",
                                "<p>(128, 104, 82)</p>", "<p>(134, 102, 90)</p>"],
                    solution_en: "<p>5.(a)<strong> Logic : </strong>1st number - 2nd number = 2nd number - third number = 22<br>(148, 126, 104) :- 148 - 126 = 126 - 104 = 22<br>(98, 76, 54) :- 98 - 76 = 76 - 54 = 22<br>Similarly<br>(157, 135, 113) :- 157 - 135 = 135 - 113 = 22</p>",
                    solution_hi: "<p>5.(a) <strong>तर्क : </strong>पहली संख्या - दूसरी संख्या = दूसरी संख्या - तीसरी संख्या = 22<br>(148, 126, 104) :- 148 - 126 = 126 - 104 = 22<br>(98, 76, 54) :- 98 - 76 = 76 - 54 = 22<br>इसी प्रकार, <br>(157, 135, 113) :- 157 - 135 = 135 - 113 = 22</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "6",
                    question_en: "6. ‘Vacant’ is related to ‘Empty’ in the same way as ‘Crowd’ is related to ‘________’. ",
                    question_hi: "6. \'रिक्त\' का संबंध \'खाली\' से उसी प्रकार है, जैसे \'भीड़\' का संबंध \'________\' से है।",
                    options_en: [" Throes ", " Loner  ", 
                                " Throng ", " Disband "],
                    options_hi: [" कष्ट ", " अकेला  ",
                                " जमघट", " उखड़ना"],
                    solution_en: "6.(c) ‘Vacant’ and ‘Empty’ are synonyms, Similarly, ‘Crowd’ and ‘Throng’ are synonyms.",
                    solution_hi: "6.(c)<br />‘रिक्त’ और ‘खाली’ पर्यायवाची शब्द हैं, इसी प्रकार, ‘भीड़’ और’जमघट’ पर्यायवाची शब्द हैं.",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "6",
                    question_en: "<p>7. Select the option that is related to the fifth term in the same way as the second term is related to the first term and the fourth term is related to the third term. <br>READER : ERZWRE :: SERIAL : ESIRLA :: JINGLE : ?</p>",
                    question_hi: "<p>7. उस विकल्प का चयन करें जो पांचवें पद से उसी प्रकार संबंधित है जिस प्रकार दूसरा पद, पहले पद से संबंधित है और चौथा पद, तीसरे पद से संबंधित है।<br>READER : ERZWRE :: SERIAL : ESIRLA :: JINGLE : ?</p>",
                    options_en: ["<p>IJGNEL</p>", "<p>JIMTEL</p>", 
                                "<p>JIGNEL</p>", "<p>IJMTEL</p>"],
                    options_hi: ["<p>IJGNEL</p>", "<p>JIMTEL</p>",
                                "<p>JIGNEL</p>", "<p>IJMTEL</p>"],
                    solution_en: "<p>7.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdKO4OoCKAvJuRqtPZF7x3qfbkXdUvsopTU_vH9cTBsxEFQbYVvhG5sf2i-t7-CqzDMk0FdOwnkazmnKCVfvBZ6BxnH9odtWD_AhJt3yQrhS5IRKsTFfNzp8FnbuVw41qc7dwqAYA?key=Ao6tJXFQ1LArZfgoUMvrUXZ9\" width=\"183\" height=\"102\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXf1pNfOxYgUf1_a_fGesZwL7dUz0M78tGyuBqDGvPw6IDgjIOF7d6skQryjtqraMnJygw_Mg98mLri3Vnt0Qfcoz1VZ9QcaJKejwDK5lMam9iBHk-O5XzcJtq9vuL8HBSgE_cMKlg?key=Ao6tJXFQ1LArZfgoUMvrUXZ9\" width=\"182\" height=\"102\"><br>Similarly<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcJA_t0YfHSl3I49A7dtwYwI3gH_3GRkN5kqwShiPaYY90q86A-p0QMeagMyVIGGLx4Vs2pHqzjBTa3k87BHZG5HPoGIj3yB4ZH0HNYjh4baEwChWsd3qKuHtNqiGQ7wUy7hzHcCg?key=Ao6tJXFQ1LArZfgoUMvrUXZ9\" width=\"191\" height=\"108\"></p>",
                    solution_hi: "<p>7.(d)<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXcsvj62gXpXjKjPZg0d__tDio4iSf0sLVg2WWI6jxnXEI1k0UUAppQeJ78itsRuZK5I7Q4ZHwWvvY-dY8RDWVECE6vgJniMwo5eW3dEXal5QVmAzS35FUwUuTf2NA_yetisx-wv?key=Ao6tJXFQ1LArZfgoUMvrUXZ9\" width=\"204\" height=\"113\"><br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXfvEOG6Ogvr3UBkApzqBFS0ts5tvUqn128q3v0P9Phwc7JAnweMQrOTsxIUMo9BDxjSjOvOZUhCKISSzS9xhMsTvnBBhfHyHSGJXygU7VOxHLmTKGq62xEEHEsPsLmL68sS-x1qXw?key=Ao6tJXFQ1LArZfgoUMvrUXZ9\" width=\"209\" height=\"118\"><br>उसी प्रकार<br><img src=\"https://lh7-rt.googleusercontent.com/docsz/AD_4nXdLHVBEAn9o1ouC0MaSBe9SRYl9HslJ9JJsdfNRDY_Gte2icb7AbbMNonxHcF6ANzlys9rlZ7EIzowhZ2ocu9faAgt75RuNdQN9PiGP3Ya46O4igWzTTNAUJ2WAN_Uo9TlVV4PX?key=Ao6tJXFQ1LArZfgoUMvrUXZ9\" width=\"211\" height=\"120\"></p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "6",
                    question_en: "<p>8. 16 is related to 30 following a certain logic. Following the same logic, 81 is related to 110. To which of the following is 49 related, following the same logic? (NOTE : Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc., to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.)</p>",
                    question_hi: "<p>8. एक निश्चित तर्क के अनुसार 16 का संबंध 30 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 81 का संबंध 110 से है। उसी तर्क का अनुसरण करते हुए, 49 का संबंध निम्नलिखित में से किससे है? <br>नोट: संख्याओं को उनके घटक अंकों में विभक्त किए बिना पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- - 13 पर संक्रियाएं, जैसे कि जोड़ना/घटाना/गुणा आदि 13 में की जा सकती हैं। 13 को 1 और 3 में विभक्त करना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।</p>",
                    options_en: ["<p>64</p>", "<p>60</p>", 
                                "<p>72</p>", "<p>90</p>"],
                    options_hi: ["<p>64</p>", "<p>60</p>",
                                "<p>72</p>", "<p>90</p>"],
                    solution_en: "<p>8.(c)<strong> Logic:</strong> (<math display=\"inline\"><msqrt><msup><mrow><mn>1</mn></mrow><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><msup><mn>1</mn><mrow><mi>s</mi><mi>t</mi></mrow></msup><mi>n</mi><mi>o</mi><mo>.</mo></msqrt></math>+2) = 2<sup>nd</sup>no.<br>(16 : 30) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math>+ 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math> + 2) &rArr; 5 &times; 6 = 30<br>(81 : 110) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math> + 2) &rArr; 10 &times; 11 = 110<br>Similarly<br>(49 : <math display=\"inline\"><mi>x</mi></math>) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math> + 2) &rArr; 8 &times; 9 = 72</p>",
                    solution_hi: "<p>8.(c) तर्क : (<math display=\"inline\" xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2346;</mi><mi>&#2361;</mi><mi>&#2354;&#2368;</mi><mi>&#160;</mi><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi><mi>&#160;</mi></msqrt></math> + 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mi>&#2346;&#2361;&#2354;&#2368;</mi><mo>&#160;</mo><mo>&#160;</mo><mi>&#2360;&#2306;&#2326;&#2381;&#2351;&#2366;</mi></msqrt></math> +2) = दूसरी संख्या <br>(16 : 30) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math>+ 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>16</mn></msqrt></math>+ 2) &rArr; 5 &times; 6 = 30<br>(81 : 110) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math>+ 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>81</mn></msqrt></math>+ 2) &rArr; 10 &times; 11 = 110<br>उसी प्रकार<br>(49 : <math display=\"inline\"><mi>x</mi></math>) :- (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math>+ 1) &times; (<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><msqrt><mn>49</mn></msqrt></math>+ 2) &rArr; 8 &times; 9 = 72</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "6",
                    question_en: "9. The following contains two pairs of words which are related to each other in a certain way. Three of the following four word-pairs are alike as these have the same relationship and thus form a group. Which word-pair does NOT belong to that group? <br />(The words must be considered as meaningful English words and must not be grouped based on the number of letters/number of consonants/vowels in the word)",
                    question_hi: "9. यहाँ दो शब्दों के युग्म दिए गए हैं जो एक निश्चित तरीके से एक दूसरे से संबंधित हैं। निम्नलिखित चार शब्द-युग्मों में से तीन शब्द-युग्म एक समान हैं क्योंकि इनके बीच में समान संबंध है और इस प्रकार एक समूह बनाते हैं। निम्नलिखित में से कौन-सा शब्द-युग्म इस समूह से संबंधित नहीं है? <br />(शब्दों को सार्थक हिंदी शब्दों के रूप में माना जाना चाहिए और शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर समूहीकृत नहीं किया जाना चाहिए)",
                    options_en: [" Joy - Peace ", " Misery - Happiness ", 
                                " Problem - Concern ", " Worry - Fear "],
                    options_hi: [" आनंद - शांति ", " कष्ट - ख़ुशी ",
                                " समस्या - चिंता", " चिंता - भय"],
                    solution_en: "9.(b)<br />(Joy - Peace, Problem - Concern, Worry - Fear) represent words that are closely related or synonymous in meaning. However, Misery and Happiness are opposites.",
                    solution_hi: "9.(b)<br />(आनंद - शांति, समस्या - चिंता, चिंता - भय) उन शब्दों का प्रतिनिधित्व करते हैं जो अर्थ में निकट रूप से संबंधित या पर्यायवाची हैं। हालाँकि, कष्ट और खुशी विपरीत हैं।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "6",
                    question_en: "<p>10. Two sets of numbers are given below. In each set of numbers, certain mathematical operation(s) on the first number result(s) in the second number. Similarly, certain mathematical operation(s) on the second number result(s) in the third number and so on. Which of the given options follows the same set of operations as in the given sets? <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into their constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/subtracting/multiplying to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>9 &ndash; 27 &ndash; 31 &ndash; 36; 10 &ndash; 30 &ndash; 34 &ndash; 39</p>",
                    question_hi: "<p>10. नीचे संख्याओं के दो समुच्चय दिए गए हैं। संख्याओं के प्रत्येक समुच्चय में, पहली संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर दूसरी संख्या प्राप्त होती है। इसी प्रकार, दूसरी संख्या पर कुछ निश्चित गणितीय संक्रिया करने पर तीसरी संख्या प्राप्त होती है और इसी तरह आगे भी होती है। दिए गए विकल्पों में से कौन-सा विकल्प दिए गए समुच्चयों की तरह संक्रियाओं के समान समुच्चय का अनुसरण करता है? <br>(नोट: संख्याओं को उनके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 लीजिए - 13 पर की जाने वाली संक्रियाएँ जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि केवल 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना, तथा 1 और 3 पर गणितीय संक्रिया करने की अनुमति नहीं है।)<br>9 &ndash; 27 &ndash; 31 &ndash; 36; 10 &ndash; 30 &ndash; 34 &ndash; 39</p>",
                    options_en: ["<p>8 &ndash; 24 &ndash; 28 &ndash; 35</p>", "<p>15 &ndash; 45 &ndash; 50 &ndash; 55</p>", 
                                "<p>11 &ndash; 33 &ndash; 38 &ndash; 44</p>", "<p>5 &ndash; 15 &ndash; 19 &ndash; 24</p>"],
                    options_hi: ["<p>8 &ndash; 24 &ndash; 28 &ndash; 35</p>", "<p>15 &ndash; 45 &ndash; 50 &ndash; 55</p>",
                                "<p>11 &ndash; 33 &ndash; 38 &ndash; 44</p>", "<p>5 &ndash; 15 &ndash; 19 &ndash; 24</p>"],
                    solution_en: "<p>10.(d) <strong>Logic:</strong> 1st number &times; 3 = 2nd number, 2nd number + 4 = 3rd number, <br>3rd number + 5 = 4th number.<br>9 &ndash; 27 &ndash; 31 &ndash; 36 :- 9 &times; 3 = 27 , 27 + 4 = 31, 31 +5 = 36<br>10 &ndash; 30 &ndash; 34 &ndash; 39 :- 10 &times; 3 = 30, 30 + 4 = 34, 34 + 5= 39<br>Similarly<br>5 &ndash; 15 &ndash; 19 &ndash; 24 :- 5 &times; 3 = 15, 15 + 4= 19, 19 + 5 = 24</p>",
                    solution_hi: "<p>10.(d)<strong> तर्क:</strong> पहली संख्या &times; 3 = दूसरी संख्या, दूसरी संख्या + 4 = तीसरी संख्या, <br>तीसरी संख्या + 5 = चौथी संख्या.<br>9 &ndash; 27 &ndash; 31 &ndash; 36 :- 9 &times; 3 = 27 , 27 + 4 = 31, 31 +5 = 36<br>10 &ndash; 30 &ndash; 34 &ndash; 39 :- 10 &times; 3 = 30, 30 + 4 = 34, 34 + 5= 39<br>इसी प्रकार <br>5 &ndash; 15 &ndash; 19 &ndash; 24 :- 5 &times; 3 = 15, 15 + 4= 19, 19 + 5 = 24</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "6",
                    question_en: "<p>11. Select the option in which the numbers share the same relationship as that shared by the given pair of numbers. (NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding/deleting/multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed.) <br>63 : 441 <br>51 : 357</p>",
                    question_hi: "<p>11. उस विकल्&zwj;प का चयन कीजिए जिसमें संख्&zwj;याओं के मध्&zwj;य वही संबंध है जो नीचे दिए गए युग्&zwj;म की संख्&zwj;याओं के मध्&zwj;य है। <br>(ध्यान दें: संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए 13 को लें- 13 पर की जाने वाली संक्रियाएं जैसे जोड़ना/ घटाना/ गुणा करना आदि केवल 13 पर की जा सकती हैं। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है।)<br>63 : 441 <br>51 : 357</p>",
                    options_en: ["<p>68 : 340</p>", "<p>54 : 324</p>", 
                                "<p>39 : 273</p>", "<p>89 : 96</p>"],
                    options_hi: ["<p>68 : 340</p>", "<p>54 : 324</p>",
                                "<p>39 : 273</p>", "<p>89 : 96</p>"],
                    solution_en: "<p>11.(c) <strong>Logic:</strong> 1st number &times; 7 = 2nd number<br>63 : 441 :- 63 &times; 7 = 441 <br>51 : 357 :- 51 &times; 7 = 357<br>Similarly<br>39 : 273 :- 39 &times; 7 = 273</p>",
                    solution_hi: "<p>11.(c) <strong>तर्क:</strong> पहली संख्या &times; 7 = दूसरी संख्या<br>63 : 441 :- 63 &times; 7 = 441 <br>51 : 357 :- 51 &times; 7 = 357<br>इसी प्रकार<br>39 : 273 :- 39 &times; 7 = 273</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "6",
                    question_en: "12 Select the word-pair that best represents a similar relationship to the one expressed in the pair of words given below. <br />(The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters/number of consonants/vowels in the word) <br />Kaziranga : Rhinoceros",
                    question_hi: "12. उस शब्द-युग्म का चयन करें, जो नीचे दिए गए शब्दों के युग्म में व्यक्त किए गए संबंध के समान संबंधों का सबसे बेहतर ढंग से प्रतिनिधित्व करता है।<br />(शब्दों को अर्थपूर्ण हिंदी शब्द माना जाना चाहिए और शब्द में अक्षरों की संख्या/व्यंजनों/स्वरों की संख्या के आधार पर एक दूसरे से संबंधित नहीं होना चाहिए)<br />Kaziranga : Rhinoceros (काजीरंगा : गैंडा)",
                    options_en: [" Gahirmatha : Lions  ", " Periyar : Elephants  ", 
                                " Ranthambore : Peacocks ", " Gir : Leopards"],
                    options_hi: [" Gahirmatha : Lions (गहिरमाथा : सिंह)", " Periyar : Elephants (पेरियार : हाथी) ",
                                " Ranthambore : Peacocks (रणथंभौर : मोर) ", " Gir : Leopards (गिर : तेंदुआ)"],
                    solution_en: "12.(b) As kaziranga is famous for Rhinoceros, just as Periyar is famous for Elephants.",
                    solution_hi: "12.(b) जैसे काजीरंगा गैंडे के लिए प्रसिद्ध है, वैसे ही पेरियार हाथियों के लिए प्रसिद्ध है।",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "6",
                    question_en: "13. Select the option that is related to the third word in the same way as the second word is related to the first word. (The words must be considered as meaningful English words and must NOT be related to each other based on the number of letters/number of consonants/vowels in the word) <br />Magician : Magic :: Dancer : ?",
                    question_hi: "13. उस विकल्प का चयन करें जो तीसरे शब्द से उसी प्रकार संबंधित है जिस प्रकार दूसरा शब्द पहले शब्द से संबंधित है। (शब्दों को अर्थपूर्ण अंग्रेजी/ हिन्दी शब्दों के रूप में माना जाना चाहिए और इन्हें शब्द में अक्षरों की संख्या/व्यंजनों की संख्या/स्वरों की संख्या के आधार पर एक दूसरे से संबद्ध नहीं किया जाना चाहिए।) <br />जादूगर : जादू :: नर्तक : ?",
                    options_en: [" Dance", " Theatre ", 
                                " Movie", " Song<br /> "],
                    options_hi: [" नृत्य", " थिएटर ",
                                " चलचित्र ", " गीत"],
                    solution_en: "13.(a) Just as a magician performs magic, a dancer performs dance. ",
                    solution_hi: "13.(a) जैसे जादूगर जादू दिखाता है वैसे ही नर्तक नृत्य करता है ।   ",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "6",
                    question_en: "<p>14. 13 is related to 117 following a certain logic. Following the same logic, 23 is related to 207. To which of the following is 51 related, following the same logic ? <br>(NOTE: Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g. 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>14. एक निश्चित तर्क का अनुसरण करते हुए 13, 117 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 23, 207 से संबंधित है। उसी तर्क का अनुसरण करते हुए, 51 निम्नलिखित में से किससे संबंधित है? <br>(नोट: संख्याओं को उसके घटक अंकों में तोड़े बिना, पूर्ण संख्याओं पर संक्रियाएं की जानी चाहिए। उदाहरण के लिए संख्या 13 को लें - 13 पर संक्रियाएं, जैसे कि 13 में जोड़ना/घटाना/गुणा करना आदि, की जा सकती हैं। 13 को 1 और 3 में तोड़ने और फिर 1 और 3 पर गणितीय संक्रियाएं करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>350</p>", "<p>450</p>", 
                                "<p>359</p>", "<p>459</p>"],
                    options_hi: ["<p>350</p>", "<p>450</p>",
                                "<p>359</p>", "<p>459</p>"],
                    solution_en: "<p>14.(d) <strong>Logic:</strong> 1st number &times; 9 = 2nd number<br>(13 : 117) :- 13 &times; 9 = 117<br>(23 : 207) : - 23 &times; 9 = 207<br>Similarly<br>(51 : <math display=\"inline\"><mi>x</mi></math>) :- 51 &times; 9 = 459</p>",
                    solution_hi: "<p>14.(d) <strong>तर्क:</strong> पहली संख्या &times; 9 = दूसरी संख्या<br>(13 : 117) :- 13 &times; 9 = 117<br>(23 : 207) : - 23 &times; 9 = 207<br>उसी प्रकार<br>(51 : <math display=\"inline\"><mi>x</mi></math>) :- 51 &times; 9 = 459</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "misc",
                    question_en: "<p>15. Select the set in which the numbers are related in the same way as are the numbers of the given sets.<br>(429, 514), (688, 773)<br>(<strong>NOTE : </strong>Operations should be performed on the whole numbers, without breaking down the numbers into its constituent digits. E.g., 13 &ndash; Operations on 13 such as adding /deleting /multiplying etc. to 13 can be performed. Breaking down 13 into 1 and 3 and then performing mathematical operations on 1 and 3 is not allowed)</p>",
                    question_hi: "<p>15. उस समुच्चय का चयन कीजिए, जिसमें संख्याएँ एक-दूसरे से उसी तरह संबंधित हैं, जैसे दिए गए समुच्चय की संख्याएँ हैं।<br>(429, 514), (688, 773)<br>(<strong>ध्यान दीजिए :</strong> संख्याओं को उनके घटक अंकों में विभाजित किए बिना, पूर्ण संख्याओं पर संक्रियाएँ की जानी चाहिए। उदाहरण के लिए 13 - 13 पर संक्रिया जैसे जोड़ना / हटाना / गुणा करना आदि 13 पर किया जा सकता है। 13 को 1 और 3 में तोड़ना और फिर 1 और 3 पर गणितीय संक्रियाएँ करने की अनुमति नहीं है)</p>",
                    options_en: ["<p>(396, 491)</p>", "<p>(546, 631)</p>", 
                                "<p>(319, 399)</p>", "<p>(407, 512)</p>"],
                    options_hi: ["<p>(396, 491)</p>", "<p>(546, 631)</p>",
                                "<p>(319, 399)</p>", "<p>(407, 512)</p>"],
                    solution_en: "<p>15.(b) <strong>Logic:</strong> (1st number + 85) = 2nd number<br>(429, 514) :- 429 +85 = 514<br>(688, 773) :- 688 + 85 = 773<br>Similarly<br>(546, 631) :- 546 +85 = 631</p>",
                    solution_hi: "<p>15.(b) <strong>तर्क:</strong> (पहली संख्या + 85) = दूसरी संख्या<br>(429, 514) :- 429 +85 = 514<br>(688, 773) :- 688 + 85 = 773<br>उसी प्रकार<br>(546, 631) :- 546 +85 = 631</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>