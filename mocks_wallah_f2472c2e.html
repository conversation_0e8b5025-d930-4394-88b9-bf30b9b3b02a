<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test App</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <style>
        :root {
            --primary: #6c5ce7;
            --primary-light: #a29bfe;
            --primary-dark: #5649c0;
            --secondary: #00cec9;
            --accent: #fd79a8;
            --success: #00b894;
            --warning: #fdcb6e;
            --danger: #d63031;
            --light: #f8f9fa;
            --dark: #2d3436;
            --gray: #636e72;
            --light-gray: #dfe6e9;
            --card-bg: #ffffff;
            --body-bg: #f5f6fa;
            --header-bg: linear-gradient(135deg, var(--primary), var(--primary-dark));
            --footer-bg: #ffffff;
            --box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            --box-shadow-sm: 0 2px 10px rgba(0,0,0,0.05);
            --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        }
        * {
            -webkit-tap-highlight-color: transparent;
        }
        body {
            background-color: var(--body-bg);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            color: var(--dark);
            line-height: 1.6;
            padding-bottom: 80px; /* Space for fixed navigation */
            overflow-x: hidden;
        }
        /* Header Styles */
        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: var(--header-bg);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            z-index: 1000;
            box-shadow: var(--box-shadow-sm);
        }
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
        }
        .header-brand {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            text-decoration: none;
        }
        .header-brand i {
            margin-right: 8px;
            font-size: 1.2rem;
        }
        .header-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .control-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }
        .control-btn:hover {
            background: rgba(255,255,255,0.2);
        }
        .control-btn.active {
            background: rgba(255,255,255,0.3);
        }
        .timer-display {
            background: rgba(0,0,0,0.2);
            border-radius: 18px;
            padding: 4px 12px;
            font-size: 0.85rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        .timer-display i {
            margin-right: 6px;
        }
        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding: 16px;
            min-height: calc(100vh - 140px);
        }
        .question-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: var(--box-shadow-sm);
            border: none;
            transition: var(--transition);
        }
        .question-counter {
            font-size: 0.9rem;
            color: var(--gray);
            text-align: center;
            margin-bottom: 16px;
            font-weight: 500;
        }
        .question-text {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 20px;
            color: var(--dark);
        }
        .option-item {
            margin-bottom: 12px;
            position: relative;
        }
        .option-input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
            .question-text {
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
    }
    .question-text img,
    .option-label img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 8px 0;
        border-radius: 8px;
    }
    .option-label {
        overflow: hidden;
    }
    .question-card {
        overflow: hidden;
    }
        .option-label {
            display: block;
            padding: 14px 16px;
            background: var(--card-bg);
            border: 1px solid var(--light-gray);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }
        .option-label:hover {
            border-color: var(--primary-light);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .option-input:checked + .option-label {
            border-color: var(--primary);
            background: rgba(108, 92, 231, 0.05);
            box-shadow: 0 0 0 1px var(--primary);
        }
        .option-input:checked + .option-label::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--primary);
        }
        /* When reviewing answers */
        .review-mode .option-input:checked + .option-label.correct {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-input:checked + .option-label.correct::after {
            background: var(--success);
        }
        .review-mode .option-input:checked + .option-label.incorrect {
            border-color: var(--danger);
            background: rgba(214, 48, 49, 0.05);
            box-shadow: 0 0 0 1px var(--danger);
        }
        .MathJax, .mjx-chtml {
            overflow-x: auto;
            max-width: 100%;
            display: inline-block;
        }
        .review-mode .option-input:checked + .option-label.incorrect::after {
            background: var(--danger);
        }
        .review-mode .option-label.correct-answer {
            border-color: var(--success);
            background: rgba(0, 184, 148, 0.05);
            box-shadow: 0 0 0 1px var(--success);
        }
        .review-mode .option-label.correct-answer::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 3px;
            height: 100%;
            background: var(--success);
        }
        .solution-container {
            margin-top: 20px;
            padding: 16px;
            background: rgba(108, 92, 231, 0.05);
            border-radius: 12px;
            border-left: 3px solid var(--primary);
        }
        .solution-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary);
        }
        /* Question Navigation */
        .question-nav {
            position: fixed;
            top: 60px;
            right: 0;
            bottom: 80px;
            width: 100%;
            max-width: 320px;
            background: var(--card-bg);
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
            transform: translateX(100%);
            transition: var(--transition);
            z-index: 900;
            overflow-y: auto;
            padding: 16px;
        }
        .question-nav.show {
            transform: translateX(0);
        }
        .nav-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        .nav-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0;
        }
        .question-stats {
            display: flex;
            gap: 8px;
        }
        .stat-badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        .section-selector {
            width: 100%;
            margin-bottom: 16px;
            border-radius: 12px;
            padding: 10px 16px;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            font-size: 0.9rem;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 10px center;
            background-size: 16px;
        }
        .question-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
            gap: 8px;
        }
        .q-box {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            border: 1px solid var(--light-gray);
            background: var(--card-bg);
            transition: var(--transition);
            position: relative;
        }
        .q-box:hover {
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-sm);
        }
        .q-box.attempted {
            background-color: var(--primary);
            color: white;
            border-color: var(--primary);
        }
        .q-box.correct {
            background-color: var(--success);
            color: white;
            border-color: var(--success);
        }
        .q-box.incorrect {
            background-color: var(--danger);
            color: white;
            border-color: var(--danger);
        }
        .q-box.current {
            border: 2px solid var(--accent) !important;
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        .q-box.unattempted {
            background-color: var(--gray);
            color: white;
            border-color: var(--gray);
        }
        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: var(--footer-bg);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 16px;
            z-index: 800;
        }
        .nav-btn {
            height: 48px;
            border-radius: 12px;
            border: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 20px;
            transition: var(--transition);
            gap: 8px;
        }
        .nav-btn i {
            font-size: 1rem;
        }
        .nav-btn-primary {
            background: var(--primary);
            color: white;
        }
        .nav-btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
        }
        .nav-btn-outline {
            background: transparent;
            color: var(--primary);
            border: 1px solid var(--primary);
        }
        .nav-btn-outline:hover {
            background: rgba(108, 92, 231, 0.1);
            transform: translateY(-2px);
        }
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }
        /* Overlay when nav is open */
        .nav-overlay {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 80px;
            background: rgba(0,0,0,0.5);
            z-index: 800;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
        }
        .nav-overlay.show {
            opacity: 1;
            pointer-events: all;
        }
        /* Results Modal */
        .results-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1100;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition);
            padding: 16px;
        }
        .results-modal.show {
            opacity: 1;
            pointer-events: all;
        }
        .results-card {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 500px;
            overflow: hidden;
            transform: translateY(20px);
            transition: var(--transition);
        }
        .results-modal.show .results-card {
            transform: translateY(0);
        }
        .results-header {
            background: var(--header-bg);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .results-title {
            margin: 0;
            font-weight: 600;
        }
        .results-body {
            padding: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 20px;
        }
        .stat-card {
            padding: 16px;
            border-radius: 12px;
            text-align: center;
            color: white;
            box-shadow: var(--box-shadow-sm);
        }
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.8rem;
            opacity: 0.9;
        }
        .results-footer {
            padding: 16px 20px;
            display: flex;
            justify-content: center;
            border-top: 1px solid var(--light-gray);
        }
        /* Responsive Adjustments */
        @media (min-width: 992px) {
            .question-nav {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 320px;
            }
            .nav-overlay {
                display: none;
            }
            .bottom-nav {
                display: none;
            }
        }
        @media (max-width: 576px) {
            .question-grid {
                grid-template-columns: repeat(auto-fill, minmax(36px, 1fr));
                gap: 6px;
            }
            .q-box {
                width: 36px;
                height: 36px;
                font-size: 0.9rem;
            }
            .option-label {
                padding: 12px 14px;
            }
            .question-text {
                font-size: 1rem;
            }
            .header-brand {
                font-size: 1rem;
            }
            .control-btn {
                width: 32px;
                height: 32px;
            }
            .timer-display {
                font-size: 0.8rem;
                padding: 4px 10px;
            }
            .nav-btn {
                height: 44px;
                padding: 0 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="header-controls">
                <!-- Timer -->
                <div class="timer-display">
                    <i class="far fa-clock"></i>
                    <span id="timer-display">8:00</span>
                </div>
                <!-- Language toggle -->
                <button class="control-btn" id="lang-btn" onclick="toggleLanguage()">
                    <span id="lang-text">En</span>
                </button>
                <!-- Submit button -->
                <button class="control-btn" id="submit-btn" onclick="confirmSubmit()">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <!-- Questions toggle for mobile -->
                <button class="control-btn nav-toggle-btn" id="nav-toggle" onclick="toggleNav()">
                    <i class="fas fa-th"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- Main content -->
    <main class="main-content" id="main-content">
        <div class="question-counter" id="question-counter">Question 1 of 25</div>
        <!-- Questions will be displayed here -->
        <div id="questions-container"></div>
    </main>
    <!-- Overlay for mobile nav -->
    <div class="nav-overlay" id="nav-overlay" onclick="toggleNav(false)"></div>
    <!-- Question navigation sidebar -->
    <aside class="question-nav" id="question-nav">
        <div class="nav-header">
            <h3 class="nav-title">Questions</h3>
            <div class="question-stats">
                <span class="stat-badge bg-primary" id="attempted-count">0</span>
                <span class="stat-badge bg-gray" id="total-count">25</span>
                <button class="control-btn d-lg-none" onclick="toggleNav(false)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <!-- Section selector -->
        <select class="section-selector" id="section-selector" onchange="switchSection(this.value)">
            <option value="all">All Sections</option>
        </select>
        <!-- Question grid -->
        <div class="question-grid" id="question-boxes"></div>
    </aside>
    <!-- Bottom navigation for mobile -->
    <nav class="bottom-nav d-lg-none">
        <button class="nav-btn nav-btn-outline" onclick="prevQuestion()">
            <i class="fas fa-chevron-left"></i>
            <span>Previous</span>
        </button>
        <button class="nav-btn nav-btn-outline" onclick="nextQuestion()">
            <span>Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </nav>
    <!-- Results modal -->
    <div class="results-modal" id="results-modal">
        <div class="results-card">
            <div class="results-header">
                <h3 class="results-title">Test Results</h3>
            </div>
            <div class="results-body">
                <div class="stats-grid">
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--primary), var(--primary-dark))">
                        <div class="stat-value" id="score-value">0</div>
                        <div class="stat-label">Score</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--success), #1b9aaa)">
                        <div class="stat-value" id="correct-value">0</div>
                        <div class="stat-label">Correct</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--danger), var(--accent))">
                        <div class="stat-value" id="incorrect-value">0</div>
                        <div class="stat-label">Incorrect</div>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(135deg, var(--gray), var(--dark))">
                        <div class="stat-value" id="unattempted-value">0</div>
                        <div class="stat-label">Unattempted</div>
                    </div>
                </div>
            </div>
            <div class="results-footer">
                <button class="nav-btn nav-btn-primary" onclick="reviewTest()">
                    <i class="fas fa-search me-1"></i>
                    <span>Review Test</span>
                </button>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Questions data
        const questions = [];
        const sections = {};
        let currentLang = "en";
        let currentQuestion = 0;
        let answers = {};
        let submitted = false;
        let totalTime = 8 * 60;
        // Initialize the test
        window.onload = function() {
            initializeTest();
            startTimer();
            // If on desktop, show the question navigation by default
            if (window.innerWidth >= 992) {
                document.getElementById('question-nav').classList.add('show');
                document.getElementById('main-content').classList.add('with-nav');
            }
        };
        // Initialize test data
        function initializeTest() {
            // Add sections and questions
            sections["18"] = {
                name: "General Awareness",
                start: 0,
                end: 23
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="18">General Awareness</option>`;
            sections["misc"] = {
                name: "Miscellaneous",
                start: 24,
                end: 24
            };
            document.getElementById('section-selector').innerHTML += 
                `<option value="misc">Miscellaneous</option>`;
                questions.push({
                    id: "1",
                    section: "18",
                    question_en: "<p>1. According to Koeppen\'s Scheme of classification of climate, which of the following&nbsp;characteristics is associated with \'Group E\'?</p>",
                    question_hi: "<p>1. कोपेन जलवायु वर्गीकरण प्रणाली (Koeppen\'s Scheme of classification of climate) के अनुसार,&nbsp;निम्नलिखित में से कौन-सा अभिलक्षण \'E\' समूह से संबंधित है?</p>",
                    options_en: ["<p>The average temperature of the coldest month is &ndash;3&deg;C or below</p>", "<p>The average temperature for all months is below 10&deg;C</p>", 
                                "<p>The average temperature of the coldest month is higher than &ndash;3&deg;C but below 18&deg;C</p>", "<p>The average temperature of the coldest month is 18&deg;C or higher</p>"],
                    options_hi: ["<p>सबसे ठंडे महीने का औसत तापमान -3&deg;C या उससे कम होता है।</p>", "<p>सभी महीनों का औसत तापमान 10&deg;C से कम होता है।</p>",
                                "<p>सबसे ठंडे महीने का औसत तापमान -3&deg;C से अधिक लेकिन 18&deg;C से कम होता है।</p>", "<p>सबसे ठंडे महीने का औसत तापमान 18&deg;C या उससे अधिक होता है।</p>"],
                    solution_en: "<p>1.(b) <strong>Climatic Groups According to Koeppen:</strong> A - Tropical: Average temperature of the coldest month is 18&deg; C or higher. B - Dry Climates: Potential evaporation exceeds precipitation. C - Warm Temperate: The average temperature of the coldest month of the (Mid-latitude) climates is higher than minus 3&deg;C but below 18&deg;C. D - Cold Snow Forest Climates: The average temperature of the coldest month is minus 3&deg; C or below. H - High Land: Cold due to elevation.</p>",
                    solution_hi: "<p>1.(b) <strong>कोपेन के अनुसार जलवायु समूह:</strong> A - उष्णकटिबंधीय: सबसे ठंडे महीने का औसत तापमान 18 डिग्री सेल्सियस या उससे अधिक है। B - शुष्क जलवायु: संभावित वाष्पीकरण वर्षा से अधिक है। C - गर्म शीतोष्ण: (मध्य अक्षांश) जलवायु के सबसे ठंडे महीने का औसत तापमान शून्य से 3 डिग्री सेल्सियस से अधिक लेकिन 18 डिग्री सेल्सियस से कम है। D - शीत बर्फ वन जलवायु: सबसे ठंडे महीने का औसत तापमान शून्य से 3 डिग्री सेल्सियस या उससे कम होता है। H - उच्च भूमि: ऊँचाई के कारण ठंडी ।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "2",
                    section: "18",
                    question_en: "<p>2. The Assam government observed the birthday of ________ on 31 March 2023, as&nbsp;Students Day in the state.</p>",
                    question_hi: "<p>2. असम सरकार ने 31 मार्च 2023 को ________ के जन्मदिन को राज्य में छात्र दिवस के रूप में मनाया।</p>",
                    options_en: ["<p>Bodofa Upendra Nath Brahma</p>", "<p>Prafulla Kumar Mahanta</p>", 
                                "<p>Biraj Kumar Sarma</p>", "<p>Bhrigu Kumar Phukan</p>"],
                    options_hi: ["<p>बोडोफा उपेन्द्र नाथ ब्रह्मा (Bodofa Upendra Nath Brahma)</p>", "<p>प्रफुल्ल कुमार महंत (Prafulla Kumar Mahanta)</p>",
                                "<p>बिराज कुमार शर्मा (Biraj Kumar Sarma)</p>", "<p>भृगुकुमार फूकन (Bhrigu Kumar Phukan)</p>"],
                    solution_en: "<p>2.(a) <strong>Bodofa Upendra Nath Brahma :</strong> He was an Indian Bodo social activist and the former president of All Bodo Students\' Union. Famous Personalities of Assam : Kanaklata Barua, Hema Bharali, Radha Govinda Baruah, Akhil Gogoi, Maniram Dewan, Sati Sadhani, Kali Charan Brahma, Dipankar Bhattacharjee, Shiva Thapa, Ranjan Gogoi.</p>",
                    solution_hi: "<p>2.(a) <strong>बोडोफा उपेन्द्र नाथ ब्रह्मा </strong>एक भारतीय बोडो सामाजिक कार्यकर्ता और ऑल बोडो स्टूडेंट्स यूनियन के पूर्व अध्यक्ष थे। असम की प्रसिद्ध हस्तियाँ: कनकलता बरुआ, हेमा भराली, राधा गोविंदा बरुआ, अखिल गोगोई, मनीराम दीवान, सती सधानी, काली चरण ब्रह्मा, दीपांकर भट्टाचार्जी, शिव थापा, रंजन गोगोई।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "3",
                    section: "18",
                    question_en: "<p>3. To create a new MS Word document, which of the following steps is correct?</p>",
                    question_hi: "<p>3. एक नया MS वर्ड दस्तावेज़ बनाने के लिए, इनमें से कौन-सा चरण सही है?</p>",
                    options_en: ["<p>File Tab &rarr; Start</p>", "<p>File Tab &rarr; Recent</p>", 
                                "<p>File Tab &rarr; Open</p>", "<p>File Tab &rarr; New</p>"],
                    options_hi: ["<p>File Tab &rarr; Start</p>", "<p>File Tab &rarr; Recent</p>",
                                "<p>File Tab &rarr; Open</p>", "<p>File Tab &rarr; New</p>"],
                    solution_en: "<p>3.(d) <strong>File Tab &rarr; New. </strong>Microsoft Word is a word processor program developed by Microsoft. It was first released on October 25, 1983, under the name Multi-Tool Word for Xenix systems. Some Microsoft Word keyboard shortcuts: Ctrl + O - Opens a document, Ctrl + W - Closes a document, Ctrl + B - Applies or removes bold formatting, Ctrl + I - Applies or removes italic formatting, Ctrl + U - Applies or removes underline formatting.</p>",
                    solution_hi: "<p>3.(d) <strong>File Tab &rarr; New. </strong>माइक्रोसॉफ्ट वर्ड (MS वर्ड), माइक्रोसॉफ्ट द्वारा विकसित एक वर्ड प्रोसेसर प्रोग्राम है। इसे पहली बार 25 अक्टूबर, 1983 को ज़ेनिक्स सिस्टम के लिए मल्टी-टूल वर्ड नाम से रिलीज़ किया गया था। माइक्रोसॉफ्ट वर्ड के कीबोर्ड शॉर्टकट: Ctrl + O - डाक्यूमेंट्स खोलता है, Ctrl + W - डाक्यूमेंट्स बंद करता है, Ctrl + B - बोल्ड फ़ॉर्मेटिंग लागू करता है या हटाता है, Ctrl + I - इटैलिक फ़ॉर्मेटिंग लागू करता है या हटाता है, Ctrl + U - अंडरलाइन फ़ॉर्मेटिंग लागू करता है या हटाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "4",
                    section: "18",
                    question_en: "<p>4. Khelo India Winter Games 2023 was held at _______.</p>",
                    question_hi: "<p>4. खेलो इंडिया शीतकालीन खेल ( Khelo India Winter Games ) 2023 _______ में आयोजित किए गए थे।</p>",
                    options_en: ["<p>Leh</p>", "<p>Himachal Pradesh</p>", 
                                "<p>Jammu and Kashmir</p>", "<p>Uttarakhand</p>"],
                    options_hi: ["<p>लेह</p>", "<p>हिमाचल प्रदेश</p>",
                                "<p>जम्मू-कश्मीर</p>", "<p>उत्तराखंड</p>"],
                    solution_en: "<p>4.(c) <strong>Jammu and Kashmir. </strong>Khelo India Winter Games (KIWG) are the national level multidisciplinary grassroot winter games of India. Events include skiing, alpine skiing, nordic skiing, snow rugby, Ice stock sport, snow baseball, mountaineering, snowshoe running, ice hockey, figure skating and speed skating. Jammu and Kashmir topped the KIWG 2023 medal table with 26 golds, 25 silvers and 25 bronze. Maharashtra finished second with 13 gold medals, eight silvers and six bronze.</p>",
                    solution_hi: "<p>4.(c) <strong>जम्मू और कश्मीर।</strong> खेलो इंडिया शीतकालीन गेम्स (KIWG) भारत के राष्ट्रीय स्तर के बहु-विषयक जमीनी स्तर के शीतकालीन खेल हैं। इन खेलों में स्कीइंग, अल्पाइन स्कीइंग, नॉर्डिक स्कीइंग, स्नो रग्बी, आइस स्टॉक स्पोर्ट, स्नो बेसबॉल, पर्वतारोहण, स्नोशू रनिंग, आइस हॉकी, फिगर स्केटिंग और स्पीड स्केटिंग शामिल हैं। जम्मू और कश्मीर ने 26 स्वर्ण, 25 रजत और 25 कांस्य के साथ KIWG 2023 पदक तालिका में शीर्ष स्थान हासिल किया। महाराष्ट्र 13 स्वर्ण पदक, आठ रजत और छह कांस्य के साथ दूसरे स्थान पर रहा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "5",
                    section: "18",
                    question_en: "<p>5. Application software includes a variety of programs that are designed to meet the information processing needs of __________.</p>",
                    question_hi: "<p>5. एप्लिकेशन सॉफ़्टवेयर में विभिन्न प्रकार के प्रोग्राम शामिल होते हैं जो __________ की सूचना प्रसंस्करण आवश्यकताओं को पूरा करने के लिए डिज़ाइन किए गए हैं।</p>",
                    options_en: ["<p>customers</p>", "<p>programmers</p>", 
                                "<p>team leaders</p>", "<p>end users</p>"],
                    options_hi: ["<p>ग्राहकों (customers)</p>", "<p>प्रोग्रामरों (programmers)</p>",
                                "<p>टीम लीडरों (team leaders)</p>", "<p>अंतिम उपयोगकर्ताओं (end users)</p>"],
                    solution_en: "<p>5.(d)<strong> End users.</strong> Application software is a term used for software created for a specific purpose. Examples of application software include: Word Processing Software, Graphics Software, Spreadsheet Software, Presentation Software, Web Browsers.</p>",
                    solution_hi: "<p>5.(d) <strong>अंतिम उपयोगकर्ताओं।</strong> एप्लीकेशन सॉफ्टवेयर एक ऐसा शब्द है जिसका उपयोग किसी विशिष्ट उद्देश्य के लिए बनाए गए सॉफ्टवेयर के लिए किया जाता है। एप्लीकेशन सॉफ्टवेयर के उदाहरणों में शामिल हैं: वर्ड प्रोसेसिंग सॉफ्टवेयर, ग्राफिक्स सॉफ्टवेयर, स्प्रेडशीट सॉफ्टवेयर, प्रेजेंटेशन सॉफ्टवेयर, वेब ब्राउज़र।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "6",
                    section: "18",
                    question_en: "<p>6. As of March 2020, which of the following solar power plants is the biggest plant in the world, with a total installed capacity of 2,245 MW?</p>",
                    question_hi: "<p>6. मार्च 2020 तक, निम्नलिखित में से कौन-सा सौर ऊर्जा संयंत्र दुनिया का सबसे बड़ा संयंत्र है, जिसकी कुल स्थापित क्षमता (total installed capacity) 2,245 मेगावाट है?</p>",
                    options_en: ["<p>Kamuthi solar power project</p>", "<p>Bhadla solar park</p>", 
                                "<p>Sakri photovoltaic solar energy project</p>", "<p>Dhirubhai Ambani solar park</p>"],
                    options_hi: ["<p>कामुथी सौर ऊर्जा परियोजना</p>", "<p>भड़ला सोलर पार्क</p>",
                                "<p>सकरी फोटोवोल्टिक सौर ऊर्जा परियोजना</p>", "<p>धीरूभाई अंबानी सोलर पार्क</p>"],
                    solution_en: "<p>6.(b) <strong>Bhadla solar park:</strong> It is a solar power plant located in the Thar Desert of Rajasthan, India. It covers an area of 56 square kilometers. As of 2024, it is the largest solar park in the world by capacity. Kamuthi Solar Power Project - Tamil Nadu. Sakri photovoltaic solar energy project - Maharashtra. Dhirubhai Ambani solar park - Rajasthan.</p>",
                    solution_hi: "<p>6.(b)<strong> भड़ला सोलर पार्क : </strong>यह भारत के राजस्थान के थार रेगिस्तान में स्थित एक सौर ऊर्जा संयंत्र है। यह 56 वर्ग किलोमीटर के क्षेत्र में फैला हुआ है। 2024 तक, यह क्षमता के हिसाब से विश्व का सबसे बड़ा सौर पार्क है। कामुथी सौर ऊर्जा परियोजना - तमिलनाडु। सकरी फोटोवोल्टिक सौर ऊर्जा परियोजना - महाराष्ट्र। धीरूभाई अंबानी सौर पार्क - राजस्थान।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "7",
                    section: "18",
                    question_en: "<p>7. Which of the following committees was associated with Industrial Licensing Policy Inquiry?</p>",
                    question_hi: "<p>7. निम्नलिखित में से कौन-सी समिति औद्योगिक लाइसेंसिंग नीति जांच से जुड़ी थी?</p>",
                    options_en: ["<p>Hazari Committee</p>", "<p>Sen Committee</p>", 
                                "<p>Gadgil Committee</p>", "<p>Dutt Committee</p>"],
                    options_hi: ["<p>हजारी समिति</p>", "<p>सेन समिति</p>",
                                "<p>गाडगिल समिति</p>", "<p>दत्त समिति</p>"],
                    solution_en: "<p>7.(d) <strong>Dutt Committee. </strong>The Hazari Committee was a committee in India that studied the industrial licensing procedure under the Industries (Development and Regulation) Act, 1951. Abhijit Sen Committee (2002) - Long Term Food Policy. The Gadgil commission was formed to study the impact of population pressure, climate change and development activities on the Western Ghats.</p>",
                    solution_hi: "<p>7.(d) <strong>दत्त समिति। </strong>हजारी समिति भारत में एक ऐसी समिति थी जिसने उद्योग (विकास और विनियमन) अधिनियम, 1951 के तहत औद्योगिक लाइसेंसिंग प्रक्रिया का अध्ययन किया था। अभिजीत सेन समिति (2002) - दीर्घकालिक खाद्य नीति। पश्चिमी घाट पर जनसंख्या दाब, जलवायु परिवर्तन और विकास गतिविधियों के प्रभाव का अध्ययन करने के लिए गाडगिल आयोग का गठन किया गया था।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "8",
                    section: "18",
                    question_en: "<p>8. At which place did Raja Ram Mohan Roy form a reform association called Brahmo&nbsp;Sabha in the year 1828?</p>",
                    question_hi: "<p>8. वर्ष 1828 में राजा राम मोहन राय ने किस स्थान पर ब्रह्म सभा नामक एक सुधार संघ का गठन किया&nbsp;था?</p>",
                    options_en: ["<p>Pune</p>", "<p>Bombay</p>", 
                                "<p>Calcutta</p>", "<p>Madras</p>"],
                    options_hi: ["<p>पुणे</p>", "<p>बंबई</p>",
                                "<p>कलकत्ता</p>", "<p>मद्रास</p>"],
                    solution_en: "<p>8.(c) <strong>Calcutta</strong>. Raja Ram Mohan Roy (Father of Indian Renaissance) was given the title of Raja by Mughal emperor Akbar II. Brahmo Samaj: Aim - worship of the eternal God. It split into two in 1866 (Brahmo Samaj of India led by Keshub Chandra Sen and Adi Brahmo Samaj led by Debendranath Tagore), Prominent Leaders: Debendranath Tagore, Keshub Chandra Sen, Pt. Sivnath Shastri, and Rabindranath Tagore}</p>",
                    solution_hi: "<p>8.(c) <strong>कलकत्ता</strong>। राजा राम मोहन राय (भारतीय पुनर्जागरण के जनक) को मुगल सम्राट अकबर द्वितीय ने राजा की उपाधि दी थी। ब्रह्म समाज: उद्देश्य - शाश्वत ईश्वर की पूजा। यह 1866 में दो भागों (भारत के ब्रह्म समाज का नेतृत्व केशव चंद्र सेन और आदि ब्रह्म समाज का नेतृत्व देवेन्द्रनाथ टैगोर ने किया) में विभाजित हो गया , प्रमुख नेता: देबेंद्रनाथ टैगोर, केशव चंद्र सेन, पंडित शिवनाथ शास्त्री और रवींद्रनाथ टैगोर।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "9",
                    section: "18",
                    question_en: "<p>9. Which amongst the following Sultans shifted his capital from Delhi to Daulatabad?</p>",
                    question_hi: "<p>9. निलिखित में से किस सुल्तान ने अपनी राजधानी दिल्ली से दौलताबाद स्थानांतरित की थी?</p>",
                    options_en: ["<p>Feroz Shah Tughlaq</p>", "<p>Ibrahim Lodhi</p>", 
                                "<p>Sikandar Lodhi</p>", "<p>Muhammad Bin Tughlaq</p>"],
                    options_hi: ["<p>फिरोज़ शाह तुगलक</p>", "<p>इब्राहिम लोदी</p>",
                                "<p>सिकंदर लोदी</p>", "<p>मुहम्मद बिन तुगलक</p>"],
                    solution_en: "<p>9.(d)<strong> Muhammad Bin Tughlaq:</strong> He shifted his capital from Delhi to the more centrally located Devagiri in Maharashtra, which was renamed Daulatabad. His Five Disastrous Projects are: Taxation in the Doab, Transfer of Capital, Khurasan Expedition, Qarachil Expedition, Introduction of Token Currency.</p>",
                    solution_hi: "<p>9.(d) <strong>मुहम्मद बिन तुगलक </strong>ने अपनी राजधानी दिल्ली से महाराष्ट्र के अधिक केंद्र में स्थित देवगिरी में स्थानांतरित कर दी, जिसका नाम बदलकर दौलताबाद कर दिया गया। उसकी पाँच विनाशकारी परियोजनाएँ: दोआब में कराधान, राजधानी का हस्तांतरण, खुरासान अभियान, कराचिल अभियान, टोकन मुद्रा का प्रचलन।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "10",
                    section: "18",
                    question_en: "<p>10. Price control and rationing are direct control measures to check ________.</p>",
                    question_hi: "<p>10. मूल्य नियंत्रण और राशनिंग (rationing) ________ की जाँच के लिए प्रत्यक्ष नियंत्रण उपाय हैं।</p>",
                    options_en: ["<p>deflation</p>", "<p>reflation</p>", 
                                "<p>inflation</p>", "<p>disinflation</p>"],
                    options_hi: ["<p>अपस्फीति</p>", "<p>प्रत्यवस्फीति</p>",
                                "<p>मुद्रास्फ़ीति</p>", "<p>विस्फीति</p>"],
                    solution_en: "<p>10.(c) <strong>Inflation</strong>: It is a decrease in the purchasing power of money, reflected in a general increase in the prices of goods and services in an economy. Lack of financial discipline by the government can lead to excess expenditure and inflation.</p>",
                    solution_hi: "<p>10.(c) <strong>मुद्रास्फीति</strong>: यह मुद्रा की क्रय शक्ति में कमी है, जो अर्थव्यवस्था में वस्तुओं और सेवाओं की कीमतों में सामान्य वृद्धि में परिलक्षित होती है। सरकार द्वारा वित्तीय अनुशासन की कमी से अत्यधिक व्यय और मुद्रास्फीति हो सकती है।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "11",
                    section: "18",
                    question_en: "<p>11. Which of the following is NOT related to the &lsquo;Trinity of Carnatic Music&rsquo;?</p>",
                    question_hi: "<p>11. निम्नलिखित में से कौन, \'कर्नाटिक संगीत की त्रिमूर्ति\' से संबंधित नहीं है?</p>",
                    options_en: ["<p>Purandara Das</p>", "<p>Syama Shastri</p>", 
                                "<p>Muthuswami Dikshitar</p>", "<p>Tyagaraja</p>"],
                    options_hi: ["<p>पुरंदरदास</p>", "<p>श्यामा शास्त्री</p>",
                                "<p>मुथुस्वामी दीक्षितार</p>", "<p>त्यागराज</p>"],
                    solution_en: "<p>11.(a) <strong>Purandara Das</strong> (&lsquo;&rsquo;father of Carnatic music&rsquo;&rsquo;) was a saint from Karnataka who contributed to the evolution of Carnatic music in the 18th century. The Trinity of Carnatic Music, also known as the Three Jewels of Carnatic Music, refers to the outstanding trio of composer-musicians of Carnatic music in the 18th century - Tyagaraja, Muthuswami Dikshitar, and Syama Sastri.</p>",
                    solution_hi: "<p>11.(a) <strong>पुरंदर दास</strong> (\'\'कर्नाटक संगीत के जनक\'\') कर्नाटक के एक संत थे जिन्होंने 18वीं शताब्दी में कर्नाटक संगीत के विकास में योगदान दिया। कर्नाटक संगीत की त्रिमूर्ति, जिसे कर्नाटक संगीत के तीन रत्नों के रूप में भी जाना जाता है, 18वीं शताब्दी में कर्नाटक संगीत के संगीतकार-संगीतकारों की उत्कृष्ट तिकड़ी को संदर्भित करता है - त्यागराज, मुथुस्वामी दीक्षितार और श्यामा शास्त्री।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "12",
                    section: "18",
                    question_en: "<p>12. Which sport is NOT played in mixed doubles?</p>",
                    question_hi: "<p>12. मिश्रित युगल (mixed doubles) में कौन-सा खेल नहीं खेला जाता है?</p>",
                    options_en: ["<p>Badminton</p>", "<p>Table tennis</p>", 
                                "<p>Tennis</p>", "<p>Handball</p>"],
                    options_hi: ["<p>बैडमिंटन</p>", "<p>टेबल टेनिस</p>",
                                "<p>टेनिस</p>", "<p>हैंडबॉल</p>"],
                    solution_en: "<p>12.(d) <strong>Handball</strong>. Mixed doubles is played between two teams of two players, with one male and one female on each side. This variation of competition is prominent in curling and racket sports, such as tennis, table tennis, and badminton (where it is known as mixed doubles), as well as card games like contract bridge. In figure skating, the corresponding event is called pairs skating.</p>",
                    solution_hi: "<p>12.(d) <strong>हैंडबॉल</strong>। मिश्रित युगल दो खिलाड़ियों की दो टीमों के बीच खेला जाता है, जिसमें प्रत्येक पक्ष में एक पुरुष और एक महिला होती है। प्रतियोगिता का यह रूप कर्लिंग और रैकेट खेलों में प्रमुख है, जैसे टेनिस, टेबल टेनिस और बैडमिंटन (जहाँ इसे मिश्रित युगल के रूप में जाना जाता है), इसके साथ ही कॉन्ट्रैक्ट ब्रिज जैसे कार्ड गेम भी प्रमुख है। फिगर स्केटिंग में, इसी इवेंट को पेयर स्केटिंग कहा जाता है।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "13",
                    section: "18",
                    question_en: "<p>13. As per the Census-2011, the total absolute increase in population during the decade is&nbsp;________.</p>",
                    question_hi: "<p>13. जनगणना-2011 के अनुसार, दशक के दौरान जनसंख्या में कुल पूर्ण वृद्धि ________ है।</p>",
                    options_en: ["<p>18.19 crore</p>", "<p>16.41 crore</p>", 
                                "<p>17.21 crore</p>", "<p>15.23 crore</p>"],
                    options_hi: ["<p>18.19 करोड़</p>", "<p>16.41 करोड़</p>",
                                "<p>17.21 करोड़</p>", "<p>15.23 करोड़</p>"],
                    solution_en: "<p>13.(a) <strong>18.19 crore.</strong> The population density in India in 2011 was 382 people per square kilometer. Bihar has the highest population density (1106 people per square kilometer), followed by Bengal (1028) and Kerala (860). The state with the lowest population density is Arunachal Pradesh (17 persons/sq.km) and the Union Territory with the lowest density population is Andaman &amp; Nicobar Islands.</p>",
                    solution_hi: "<p>13.(a) <strong>18.19 करोड़।</strong> 2011 में भारत में जनसंख्या घनत्व 382 व्यक्ति प्रति वर्ग किलोमीटर था। बिहार में सबसे अधिक जनसंख्या घनत्व (1106 व्यक्ति प्रति वर्ग किलोमीटर) है, उसके बाद बंगाल (1028) और केरल (860) का स्थान है। सबसे कम जनसंख्या घनत्व वाला राज्य अरुणाचल प्रदेश (17 व्यक्ति/वर्ग किलोमीटर) है और सबसे कम जनसंख्या घनत्व वाला केंद्र शासित प्रदेश अंडमान और निकोबार द्वीप समूह है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "14",
                    section: "18",
                    question_en: "<p>14. How many Fundamental Rights were initially provided in the Constitution of India?</p>",
                    question_hi: "<p>14. आरंभ में भारत के संविधान में कितने मौलिक अधिकार प्रदान किए गए थे?</p>",
                    options_en: ["<p>Seven</p>", "<p>Eight</p>", 
                                "<p>Nine</p>", "<p>Six</p>"],
                    options_hi: ["<p>सात</p>", "<p>आठ</p>",
                                "<p>नौ</p>", "<p>छह</p>"],
                    solution_en: "<p>14.(a) <strong>Seven</strong>. Initially, the Constitution of India provided seven Fundamental Rights: Right to equality, Right to freedom, Right against exploitation, Right to freedom of religion, Cultural and Educational rights, Right to constitutional remedies and Right to property. The 44th Amendment of 1978 removed the right to property from the list of fundamental rights, making it a legal right under Article 300A. Currently there are 6 Fundamental Rights in the Indian Constitution.</p>",
                    solution_hi: "<p>14.(a) <strong>सात</strong>। प्रारंभ में, मौलिक अधिकार की संख्या सात थी : समानता का अधिकार, स्वतंत्रता का अधिकार, शोषण के विरुद्ध अधिकार, धार्मिक स्वतंत्रता का अधिकार, सांस्कृतिक और शैक्षिक अधिकार, संवैधानिक उपचार का अधिकार और संपत्ति का अधिकार। 1978 के 44वें संशोधन ने मौलिक अधिकारों की सूची से &lsquo;संपत्ति के अधिकार&rsquo; को हटा दिया, जिससे यह अनुच्छेद 300A के तहत एक कानूनी अधिकार बन गया। वर्तमान में भारतीय संविधान में 6 मौलिक अधिकार है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "15",
                    section: "18",
                    question_en: "<p>15. As of March, 2024, who is the Executive Chairman of the National Legal Services Authority of India?</p>",
                    question_hi: "<p>15. मार्च, 2024 तक की स्थिति के अनुसार, भारतीय राष्ट्रीय कानूनी सेवा प्राधिकरण (National Legal<br>Services Authority) के कार्यकारी अध्यक्ष कौन हैं?</p>",
                    options_en: ["<p>Justice Ranjan Gogoi</p>", "<p>Justice Sanjiv Khanna</p>", 
                                "<p>Justice Sharad Bobde</p>", "<p>Justice UU Lalit</p>"],
                    options_hi: ["<p>न्यायमूर्ति रंजन गोगोई</p>", "<p>न्यायमूर्ति संजीव खन्ना</p>",
                                "<p>न्यायमूर्ति शरद बोबड़े</p>", "<p>न्यायमूर्ति यू. यू. ललित</p>"],
                    solution_en: "<p>15.(b)<strong> Justice Sanjiv Khanna. </strong>The National Legal Services Authority (NALSA) is a statutory body of India established on 9 November 1995 under the Legal Services Authorities Act 1987. Headquarters - New Delhi. Motto - Access to Justice for All. Ranjan Gogoi served as the 46th Chief Justice of India from 2018 to 2019. Sharad Arvind Bobde served as the 47th Chief Justice of India. Uday Umesh Lalit served as the 49th Chief Justice of India.</p>",
                    solution_hi: "<p>15.(b) <strong>न्यायमूर्ति संजीव खन्ना।</strong> राष्ट्रीय विधिक सेवा प्राधिकरण (NALSA), भारत का एक वैधानिक निकाय है जिसकी स्थापना 9 नवंबर 1995 को विधिक सेवा प्राधिकरण अधिनियम 1987 के तहत की गई थी। मुख्यालय - नई दिल्ली। आदर्श वाक्य - न्याय सबके लिए (Access to Justice for All)। रंजन गोगोई ने 2018 से 2019 तक भारत के 46वें मुख्य न्यायाधीश के रूप में कार्य किया। शरद अरविंद बोबडे ने भारत के 47वें मुख्य न्यायाधीश के रूप में कार्य किया। उदय उमेश ललित ने भारत के 49वें मुख्य न्यायाधीश के रूप में कार्य किया।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "16",
                    section: "18",
                    question_en: "<p>16. Select the natural habitat of the Asiatic Lion from the following.</p>",
                    question_hi: "<p>16. निम्नलिखित में से एशियाई शेर के प्राकृतिक आवास का चयन कीजिए।</p>",
                    options_en: ["<p>Gir forest</p>", "<p>Sundarbans</p>", 
                                "<p>Kanha National Park</p>", "<p>Saranda forest</p>"],
                    options_hi: ["<p>गिर वन</p>", "<p>सुंदरवन</p>",
                                "<p>कान्हा राष्ट्रीय उद्यान</p>", "<p>सारंडा वन</p>"],
                    solution_en: "<p>16.(a) <strong>Gir forest</strong>. Asiatic lions are an endangered species and their availability is restricted only to the Gir National Park situated in Gujarat. India is the only country in the world to have both tigers and lions. Tigers are found in the forests of Madhya Pradesh, the Sundarbans of West Bengal. Number of Lions in India - 674. The first tiger reserve in India was established in 1973 (Project Tiger).</p>",
                    solution_hi: "<p>16.(a)<strong> गिर वन। </strong>एशियाई शेर एक लुप्तप्राय प्रजाति हैं और उनकी उपलब्धता केवल गुजरात में स्थित गिर राष्ट्रीय उद्यान तक ही सीमित है। भारत विश्व का एकमात्र ऐसा देश है जहाँ बाघ और शेर दोनों पाए जाते हैं। बाघ मध्य प्रदेश के जंगलों, पश्चिम बंगाल के सुंदरवन में पाए जाते हैं। भारत में शेरों की संख्या 674 है। भारत में पहला बाघ अभयारण्य 1973 (प्रोजेक्ट टाइगर) में स्थापित किया गया था ।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "17",
                    section: "18",
                    question_en: "<p>17. Select olfactory indicator from the given options.</p>",
                    question_hi: "<p>17. दिए गए विकल्पों में से घ्राण संकेतक (olfactory indicator) का चयन करें।</p>",
                    options_en: ["<p>Turmeric</p>", "<p>Petunia</p>", 
                                "<p>Clove oil</p>", "<p>Cabbage juice</p>"],
                    options_hi: ["<p>हल्दी (Turmeric)</p>", "<p>पेटूनिया(Petunia)</p>",
                                "<p>लौंग का तेल (Clove oil)</p>", "<p>गोभी का रस (Cabbage juice)</p>"],
                    solution_en: "<p>17.(c) <strong>Clove oil. </strong>An olfactory indicator is a substance that smells different when mixed with an acidic or basic solution. Olfactory indicators are used in laboratories to determine if a solution is acidic or basic, a process called olfactory titration. Other olfactory indicators: Onion, Vanilla.</p>",
                    solution_hi: "<p>17.(c) <strong>लौंग का तेल। </strong>घ्राण संकेतक एक ऐसा पदार्थ है जो अम्लीय या क्षारीय विलयन के साथ मिश्रित होने पर अलग गंध देता है। प्रयोगशालाओं में घ्राण सूचक का उपयोग यह निर्धारित करने के लिए किया जाता है कि कोई विलयन अम्लीय है या क्षारीय, इस प्रक्रिया को घ्राण अनुमापन कहा जाता है। अन्य घ्राण सूचक: प्याज, वेनिला।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "18",
                    section: "18",
                    question_en: "<p>18. Which of the following is NOT one of the classical languages of India?</p>",
                    question_hi: "<p>18. निम्नलिखित में से कौन-सी भारत की एक शास्त्रीय भाषा नहीं है?</p>",
                    options_en: ["<p>Odiya</p>", "<p>Malayalam</p>", 
                                "<p>Kannada</p>", "<p>Magahi</p>"],
                    options_hi: ["<p>उड़िया</p>", "<p>मलयालम</p>",
                                "<p>कन्नड़</p>", "<p>मगही</p>"],
                    solution_en: "<p>18.(d) <strong>Magahi </strong>(Magadhi) is an Indo-Aryan language spoken in Bihar, Jharkhand and West Bengal states of eastern India, and in the Terai of Nepal. All the Classical Languages are listed in the Eighth Schedule of the Constitution. Six languages that enjoy the &lsquo;Classical&rsquo; status in India: Tamil (declared in 2004), Sanskrit (2005), Kannada (2008), Telugu (2008), Malayalam (2013), and Odia (2014).</p>",
                    solution_hi: "<p>18.(d) <strong>मगही </strong>(मागधी) एक इंडो-आर्यन भाषा है जो पूर्वी भारत के बिहार, झारखंड और पश्चिम बंगाल राज्यों और नेपाल के तराई क्षेत्रों में बोली जाती है। सभी शास्त्रीय भाषाएँ संविधान की आठवीं अनुसूची में सूचीबद्ध हैं। भारत में छह भाषाएँ जिन्हें \'शास्त्रीय\' दर्जा प्राप्त है: तमिल (2004 में घोषित), संस्कृत (2005), कन्नड़ (2008), तेलुगु (2008), मलयालम (2013) और ओडिया (2014)।</p>",
                    correct: "d",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "19",
                    section: "18",
                    question_en: "<p>19. Which statement about Classical Carnatic Music is correct?</p>",
                    question_hi: "<p>19. शास्त्रीय कर्नाटक संगीत के बारे में कौन सा कथन सही है?</p>",
                    options_en: ["<p>The compositions are either in Telugu, Kannada, Tamil or Sanskrit.</p>", "<p>The compositions are either in Marathi, Odia, Konkani or Bengali.</p>", 
                                "<p>The compositions are either in Telugu, Malayalam, Hindi or Sanskrit.</p>", "<p>The compositions are either in Malayalam, Marathi, Tamil or Bengali.</p>"],
                    options_hi: ["<p>रचनाएँ या तो तेलुगु, कन्नड़, तमिल या संस्कृत में हैं।</p>", "<p>रचनाएँ या तो मराठी, ओडिया, कोंकणी या बंगाली में हैं।</p>",
                                "<p>रचनाएँ या तो तेलुगु, मलयालम, हिंदी या संस्कृत में हैं।</p>", "<p>रचनाएँ या तो मलयालम, मराठी, तमिल या बंगाली में हैं।</p>"],
                    solution_en: "<p>19.(a) Carnatic music in the South Indian languages, is a system of music commonly associated with South India, including the modern Indian states of Andhra Pradesh, Karnataka, Kerala, Tamil Nadu and portions of east and south Telangana and southern Odisha. Some Carnatic Singers: Dr. Nithyashree Mahadevan, Sudha Raghunathan, K J Yesudas, P Unni Krishnan, M Balamuralikrishna, Semmangudi Srinivasa Iyer, Bombay Jayashri, Aruna Sairam.</p>",
                    solution_hi: "<p>19.(a) दक्षिण भारतीय भाषाओं में कर्नाटक संगीत, संगीत की एक ऐसी प्रणाली है जो आमतौर पर दक्षिण भारत से जुड़ी है, जिसमें आधुनिक भारतीय राज्य आंध्र प्रदेश, कर्नाटक, केरल, तमिलनाडु और पूर्वी और दक्षिणी तेलंगाना और दक्षिणी ओडिशा के कुछ हिस्से शामिल हैं। कर्नाटक संगीत के कुछ गायक: डॉ. नित्याश्री महादेवन, सुधा रघुनाथन, के.जे.येसुदास, पी.उन्नी कृष्णन, एम.बालामुरलीकृष्ण, सेम्मनगुडी श्रीनिवास अय्यर, बॉम्बे जयश्री, अरुणा साईराम।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "20",
                    section: "18",
                    question_en: "<p>20. Lal Thanhawla holds the record for the longest-serving Chief Minister of ________, occupying the position for five terms.</p>",
                    question_hi: "<p>20. सबसे लंबे समय तक________ के मुख्यमंत्री पद पर बने रहने का रिकॉर्ड लाल थानहावला (Lal Thanhawla) के नाम है, जो पाँच बार इस पद पर रह चुके हैं।</p>",
                    options_en: ["<p>Meghalaya</p>", "<p>Manipur</p>", 
                                "<p>Mizoram</p>", "<p>Sikkim</p>"],
                    options_hi: ["<p>मेघालय</p>", "<p>मणिपुर</p>",
                                "<p>मिजोरम</p>", "<p>सिक्किम</p>"],
                    solution_en: "<p>20.(c) <strong>Mizoram</strong>. Mizoram : Formation - 20 February 1987; 1st Governor - Hiteswar Saikia; First Chief Minister - C. Chhunga.</p>",
                    solution_hi: "<p>20.(c) <strong>मिजोरम</strong>। मिजोरम : गठन - 20 फरवरी 1987; प्रथम राज्यपाल - हितेश्वर सैकिया; प्रथम मुख्यमंत्री - सी. छुंगा।</p>",
                    correct: "c",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "21",
                    section: "18",
                    question_en: "<p>21. Which of the following dances is listed in the UNESCO List of Intangible Cultural&nbsp;Heritage of India since 2010 ?</p>",
                    question_hi: "<p>21. निम्नलिखित में से कौन-सा नृत्य 2010 से भारत की अमूर्त सांस्कृतिक विरासत की यूनेस्को (UNESCO)&nbsp;सूची में सूचीबद्ध है?</p>",
                    options_en: ["<p>Garba</p>", "<p>Kalbelia</p>", 
                                "<p>Ghumar</p>", "<p>Matki</p>"],
                    options_hi: ["<p>गरबा</p>", "<p>कालबेलिया</p>",
                                "<p>घूमर</p>", "<p>मटकी</p>"],
                    solution_en: "<p>21.(b) <strong>Kalbelia </strong>: It has other names like \'Sapera Dance\' or \'Snake Charmer Dance\'. This dance is particularly performed by a Rajasthani tribe called \'Kalbelia\'. Other folk dances and states: Garba (Gujarat), Ghoomar or ghumar (Rajasthan), Matki dance (Madhya Pradesh).</p>",
                    solution_hi: "<p>21.(b) <strong>कालबेलिया</strong>: इसके अन्य नाम \'सपेरा नृत्य\' या \'स्नेक चार्मर नृत्य\' है। यह नृत्य विशेष रूप से \'कालबेलिया\' नामक राजस्थानी जनजाति द्वारा किया जाता है। अन्य लोक नृत्य और राज्य: गरबा (गुजरात), घूमर (राजस्थान), मटकी नृत्य (मध्य प्रदेश)।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "22",
                    section: "18",
                    question_en: "<p>22. _________ of the Constitution of India has provisions for legal enforcement of then fundamental rights.</p>",
                    question_hi: "<p>22. भारत के संविधान के _________ में मौलिक अधिकारों के कानूनी प्रवर्तन के प्रावधान है।</p>",
                    options_en: ["<p>Article 32</p>", "<p>Article 19</p>", 
                                "<p>Article 29</p>", "<p>Article 28</p>"],
                    options_hi: ["<p>अनुच्छेद 32</p>", "<p>अनुच्छेद 19</p>",
                                "<p>अनुच्छेद 29</p>", "<p>अनुच्छेद 28</p>"],
                    solution_en: "<p>22.(a) <strong>Article 32. </strong>It grants every individual the right to move the Supreme Court for the enforcement of their fundamental rights. The five types of writs that can be issued in India are: Habeas corpus, Mandamus, Certiorari, Prohibition, Quo-warranto. Article 226 of the Constitution of India gives High Courts the power to issue writs</p>",
                    solution_hi: "<p>22.(a) <strong>अनुच्छेद 32</strong>. यह प्रत्येक व्यक्ति को अपने मौलिक अधिकारों के प्रवर्तन के लिए सर्वोच्च न्यायालय में जाने का अधिकार देता है। भारत में जारी किए जा सकने वाले पाँच प्रकार के रिट हैं: बंदी प्रत्यक्षीकरण, परमादेश, उत्प्रेषण, प्रतिषेध, अधिकार-पृच्छा। भारतीय संविधान का अनुच्छेद 226 उच्च न्यायालयों को रिट जारी करने की शक्ति देता है।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "23",
                    section: "18",
                    question_en: "<p>23. Which of the following is NOT compulsory equipment for a football referee?</p>",
                    question_hi: "<p>23. निलिखित में से कौन-सा फुटबॉल रेफरी के लिए अनिवार्य उपकरण नहीं है?</p>",
                    options_en: ["<p>White flag</p>", "<p>Whistle(s)</p>", 
                                "<p>Watch(es)</p>", "<p>Red and yellow cards</p>"],
                    options_hi: ["<p>सफेद झंडा</p>", "<p>सीटी/सीटियां</p>",
                                "<p>घड़ी/घड़ियां</p>", "<p>लाल और पीले कार्ड</p>"],
                    solution_en: "<p>23.(a) <strong>White flag. </strong>A Red Card is used by the officials to remove a player from the match. A yellow card in football is a disciplinary warning that a referee shows to a player who breaks a rule. Modern football originated in Britain in the 19th century. Some famous footballers include: Lionel Messi, Cristiano Ronaldo, Diego Maradona, Zinedine Zidane, Pele, Johan Cruyff, Franz Beckenbauer.</p>",
                    solution_hi: "<p>23.(a) <strong>सफेद झंडा।</strong> लाल कार्ड का इस्तेमाल अधिकारी किसी खिलाड़ी को मैच से बाहर निकालने के लिए करते हैं। फुटबॉल में पीला कार्ड एक अनुशासनात्मक चेतावनी है जो रेफरी किसी ऐसे खिलाड़ी को दिखाता है जो नियम तोड़ता है। आधुनिक फुटबॉल की शुरुआत 19वीं सदी में ब्रिटेन में हुई थी। कुछ प्रसिद्ध फुटबॉल खिलाड़ी : लियोनेल मेस्सी, क्रिस्टियानो रोनाल्डो, डिएगो माराडोना, ज़िनेदिन ज़िदान, पेले, जोहान क्रूफ़, फ़्रांज़ बेकनबाउर।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "24",
                    section: "18",
                    question_en: "<p>24. Doljatra is a festival of which state from followings?</p>",
                    question_hi: "<p>24. डोलजात्रा (Doljatra) निम्नलिखित में से किस राज्य का एक त्योहार है?</p>",
                    options_en: ["<p>Uttar Pradesh</p>", "<p>Assam</p>", 
                                "<p>Rajasthan</p>", "<p>Haryana</p>"],
                    options_hi: ["<p>उत्तर प्रदेश</p>", "<p>असम</p>",
                                "<p>राजस्थान</p>", "<p>हरियाणा</p>"],
                    solution_en: "<p>24.(b) <strong>Assam</strong>. Other festivals: Assam - Bihu, Me-Dum-Me-Phi, Baishagu, Ambubachi Mela, Ali-Ai-Ligang, Assam Tea Festival, Bare Saharia Bhaona, Majuli Festival, Dehing Patkai Festival.</p>",
                    solution_hi: "<p>24.(b) <strong>असम</strong>. अन्य त्योहार: असम - बिहू, मी-दम-मी-फी, बैशागु, अंबुबाची मेला, अली-ऐ-लिगांग, असम चाय महोत्सव, बरे सहरिया भाओना, माजुली महोत्सव, देहिंग पटकाई महोत्सव।</p>",
                    correct: "b",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
                questions.push({
                    id: "25",
                    section: "misc",
                    question_en: "<p>25. Which of the following is the correct relationship?</p>",
                    question_hi: "<p>25. निम्नलिखित में से कौन सा संबंध सही है?</p>",
                    options_en: ["<p>F = ma</p>", "<p>F = m/a</p>", 
                                "<p>F = m2a</p>", "<p>F = a/m</p>"],
                    options_hi: ["<p>F = ma</p>", "<p>F = m/a</p>",
                                "<p>F = m2a</p>", "<p>F = a/m</p>"],
                    solution_en: "<p>25.(a)<strong> F = ma. </strong>Newton\'s second law is often stated as F = ma, which means the force (F) acting on an object is equal to the mass (m) of the object times its acceleration (a). This means the more mass an object has, the more force you need to accelerate it, and the greater the force, the greater the object\'s acceleration. Force(F) = Mass(m) &times; Acceleration(a). SI unit of Force = Newtons (N).</p>",
                    solution_hi: "<p>25.(a)<strong> F = ma</strong>. न्यूटन के दूसरे नियम को प्रायः F = ma के रूप में व्यक्त किया जाता है, जिसका अर्थ है कि किसी वस्तु पर कार्य करने वाला बल (F) वस्तु के द्रव्यमान (m) और उसके त्वरण (a) के गुणनफल के बराबर होता है। इसका अर्थ है कि किसी वस्तु का द्रव्यमान जितना अधिक होगा, उसे गति देने के लिए उतना ही अधिक बल लगाना होगा, और जितना अधिक बल होगा, वस्तु का त्वरण उतना ही अधिक होगा। बल (F) = द्रव्यमान (m) &times; त्वरण (a)। बल का SI मात्रक = न्यूटन (N)।</p>",
                    correct: "a",
                    pos_marks: 2.0,
                    neg_marks: 0.5
                });
            // Generate question boxes
            const boxesContainer = document.getElementById('question-boxes');
            questions.forEach((q, index) => {
                const box = document.createElement('div');
                box.className = 'q-box';
                box.textContent = index + 1;
                box.onclick = () => showQuestion(index);
                box.id = `box-${index}`;
                boxesContainer.appendChild(box);
            });
            // Show first question
            showQuestion(0);
            updateProgress();
        }
        // Show a specific question
        function formatContent(text) {
            if (!text) return '';
            return text.replace(/<img/g, '<img style="max-width:100%;height:auto;"');
        }
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) return;
            currentQuestion = index;
            const question = questions[index];
            // Update question counter
            document.getElementById('question-counter').textContent = `Question ${index + 1} of ${questions.length}`;
            // Update question boxes
            document.querySelectorAll('.q-box').forEach(box => box.classList.remove('current'));
            document.getElementById(`box-${index}`).classList.add('current');
            // Generate question HTML
            let html = `
                <div class="question-card">
                    <div class="question-text">
                        <div class="en">${formatContent(question.question_en)}</div>
                        <div class="hi" style="display:none">${formatContent(question.question_hi)} </div>
                    </div>
                    <div class="options" id="options-container">`;
            // Add options
            ['a', 'b', 'c', 'd'].forEach((opt, i) => {
                let optionClass = '';
                let isCorrectAnswer = opt === question.correct;
                let isUserAnswer = answers[question.id] === opt;
                // When reviewing, add appropriate classes
                if (submitted) {
                    if (isCorrectAnswer) {
                        optionClass += ' correct-answer';
                    } else if (isUserAnswer && !isCorrectAnswer) {
                        optionClass += ' incorrect';
                    }
                }
                html += `
                    <div class="option-item">
                        <input class="option-input" type="radio" name="q${index}" 
                            id="opt-${index}-${opt}" value="${opt}" 
                            ${isUserAnswer ? 'checked' : ''}
                            onclick="selectAnswer('${question.id}', '${opt}')">
                       <label class="option-label${optionClass}" for="opt-${index}-${opt}">
                            <div class="en">${question.options_en[i]}</div>
                            <div class="hi" style="display:none">${question.options_hi[i]}</div>
                        </label>
                    </div>`;
            });
            // Add solution if submitted
            if (submitted) {
                const isCorrect = answers[question.id] === question.correct;
                const resultText = answers[question.id] ? (isCorrect ? 'Correct!' : 'Incorrect!') : 'Not attempted';
                html += `
                    <div class="solution-container">
                        <div class="solution-title">${resultText}</div>
                        <div class="solution-text">
                            <div class="en">${question.solution_en}</div>
                            <div class="hi" style="display:none">${question.solution_hi}</div>
                        </div>
                    </div>`;
            }
            html += `</div></div>`;
            document.getElementById('questions-container').innerHTML = html;
            // Apply language settings
            applyLanguage();
            // If in mobile view, close the menu
            if (window.innerWidth < 992) {
                toggleNav(false);
            }
            // Add review mode class if submitted
            if (submitted) {
                document.getElementById('options-container').classList.add('review-mode');
            }
        }
        // Apply current language
        function applyLanguage() {
            document.querySelectorAll(`.${currentLang}`).forEach(el => el.style.display = 'block');
            document.querySelectorAll(`.${currentLang === 'en' ? 'hi' : 'en'}`).forEach(el => el.style.display = 'none');
            // Update language button text
            document.getElementById('lang-text').textContent = currentLang === 'en' ? 'Hi' : 'En';
        }
        // Select an answer
        function selectAnswer(qId, option) {
            if (submitted) return;
            answers[qId] = option;
            updateProgress();
            // Mark question as attempted in the navigation
            const index = questions.findIndex(q => q.id === qId);
            if (index >= 0) {
                const box = document.getElementById(`box-${index}`);
                box.classList.add('attempted');
                box.classList.remove('unattempted');
            }
        }
        // Navigate to previous/next question
        function prevQuestion() { 
            if (currentQuestion > 0) showQuestion(currentQuestion - 1); 
        }
        function nextQuestion() { 
            if (currentQuestion < questions.length - 1) showQuestion(currentQuestion + 1); 
        }
        // Switch section
        function switchSection(sectionId) {
            if (sectionId === 'all') {
                showQuestion(0);
                return;
            }
            const section = sections[sectionId];
            if (section) showQuestion(section.start);
        }
        // Toggle language
        function toggleLanguage() {
            currentLang = currentLang === 'en' ? 'hi' : 'en';
            applyLanguage();
        }
        // Toggle navigation sidebar
        function toggleNav(show) {
            const nav = document.getElementById('question-nav');
            const overlay = document.getElementById('nav-overlay');
            if (show === undefined) {
                show = !nav.classList.contains('show');
            }
            if (show) {
                nav.classList.add('show');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            } else {
                nav.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }
        }
        // Update progress
        function updateProgress() {
            const attempted = Object.keys(answers).length;
            document.getElementById('attempted-count').textContent = attempted;
        }
        // Start timer
        function startTimer() {
            const timerDisplay = document.getElementById('timer-display');
            let timeLeft = totalTime;
            const timerInterval = setInterval(() => {
                if (timeLeft <= 0 || submitted) {
                    clearInterval(timerInterval);
                    if (!submitted) submitTest();
                    return;
                }
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                timerDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                timeLeft--;
            }, 1000);
        }
        // Confirm before submitting
        function confirmSubmit() {
            if (submitted) return;
            const attempted = Object.keys(answers).length;
            const remaining = questions.length - attempted;
            // Create a more modern confirmation dialog
            const confirmHtml = `
                <div style="text-align: center; padding: 20px;">
                    <h4 style="margin-bottom: 16px; color: var(--dark);">Confirm Submission</h4>
                    <p style="margin-bottom: 24px; color: var(--gray);">
                        You have attempted ${attempted} of ${questions.length} questions.<br>
                        ${remaining} questions are unattempted.
                    </p>
                    <div style="display: flex; justify-content: center; gap: 12px;">
                        <button onclick="this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; border: 1px solid var(--light-gray); background: white; border-radius: 8px; cursor: pointer;">
                            Cancel
                        </button>
                        <button onclick="submitTest(); this.closest('.modal').querySelector('.cancel-btn').click()" 
                            style="padding: 8px 20px; background: var(--primary); color: white; border: none; border-radius: 8px; cursor: pointer;">
                            Submit Test
                        </button>
                    </div>
                </div>`;
            // Create modal element
            const modal = document.createElement('div');
            modal.className = 'results-modal show';
            modal.innerHTML = `
                <div class="results-card" style="max-width: 400px;">
                    <button class="cancel-btn" style="position: absolute; top: 12px; right: 12px; background: none; border: none; font-size: 1.2rem; cursor: pointer; color: var(--gray);" onclick="this.closest('.results-modal').remove()">
                        ×
                    </button>
                    ${confirmHtml}
                </div>`;
            document.body.appendChild(modal);
        }
        // Submit test
        function submitTest() {
            if (submitted) return;
            submitted = true;
            let score = 0;
            let correctCount = 0;
            let incorrectCount = 0;
            let unattemptedCount = 0;
            questions.forEach((q, index) => {
                const userAns = answers[q.id];
                const box = document.getElementById(`box-${index}`);
                box.classList.remove('attempted');
                if (!userAns) {
                    unattemptedCount++;
                    box.classList.add('unattempted');
                } else if (userAns === q.correct) {
                    correctCount++;
                    score += q.pos_marks;
                    box.classList.add('correct');
                } else {
                    incorrectCount++;
                    score -= q.neg_marks;
                    box.classList.add('incorrect');
                }
            });
            // Update result values
            document.getElementById('score-value').textContent = score.toFixed(2);
            document.getElementById('correct-value').textContent = correctCount;
            document.getElementById('incorrect-value').textContent = incorrectCount;
            document.getElementById('unattempted-value').textContent = unattemptedCount;
            // Show the results modal
            document.getElementById('results-modal').classList.add('show');
            // Disable submit button
            const submitBtn = document.getElementById('submit-btn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-check"></i>';
            submitBtn.style.backgroundColor = 'var(--success)';
            // Refresh current question view to show solution
            showQuestion(currentQuestion);
        }
        // Review test
        function reviewTest() {
            document.getElementById('results-modal').classList.remove('show');
            showQuestion(0);
        }
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === document.getElementById('results-modal')) {
                document.getElementById('results-modal').classList.remove('show');
            }
        });
        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') prevQuestion();
            if (e.key === 'ArrowRight') nextQuestion();
        });
    </script>
</body>
</html>